{"ast": null, "code": "\"use strict\";\n\nmodule.exports = function (data, encoding) {\n  if (typeof data === \"number\") {\n    return Buffer.alloc(data);\n  }\n  return Buffer.from(data, encoding);\n};\nmodule.exports.test = function (b) {\n  return Buffer.isBuffer(b);\n};", "map": {"version": 3, "names": ["module", "exports", "data", "encoding", "<PERSON><PERSON><PERSON>", "alloc", "from", "test", "b", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/pizzip@3.1.7/node_modules/pizzip/js/nodeBuffer.js"], "sourcesContent": ["\"use strict\";\n\nmodule.exports = function (data, encoding) {\n  if (typeof data === \"number\") {\n    return Buffer.alloc(data);\n  }\n  return Buffer.from(data, encoding);\n};\nmodule.exports.test = function (b) {\n  return Buffer.isBuffer(b);\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,UAAUC,IAAI,EAAEC,QAAQ,EAAE;EACzC,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAOE,MAAM,CAACC,KAAK,CAACH,IAAI,CAAC;EAC3B;EACA,OAAOE,MAAM,CAACE,IAAI,CAACJ,IAAI,EAAEC,QAAQ,CAAC;AACpC,CAAC;AACDH,MAAM,CAACC,OAAO,CAACM,IAAI,GAAG,UAAUC,CAAC,EAAE;EACjC,OAAOJ,MAAM,CAACK,QAAQ,CAACD,CAAC,CAAC;AAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}