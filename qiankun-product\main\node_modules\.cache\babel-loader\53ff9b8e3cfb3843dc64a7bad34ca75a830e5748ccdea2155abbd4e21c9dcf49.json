{"ast": null, "code": "import '../../../../utils/index.mjs';\nimport { datePickerSharedProps, selectionModeWithDefault } from './shared.mjs';\nimport { buildProps } from '../../../../utils/vue/props/runtime.mjs';\nvar date = datePickerSharedProps.date,\n  disabledDate = datePickerSharedProps.disabledDate,\n  parsedValue = datePickerSharedProps.parsedValue;\nvar basicYearTableProps = buildProps({\n  date,\n  disabledDate,\n  parsedValue,\n  selectionMode: selectionModeWithDefault(\"year\")\n});\nexport { basicYearTableProps };", "map": {"version": 3, "names": ["date", "datePickerSharedProps", "disabledDate", "parsedValue", "basicYearTableProps", "buildProps", "selectionMode", "selectionModeWithDefault"], "sources": ["../../../../../../../packages/components/date-picker/src/props/basic-year-table.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport { datePickerSharedProps, selectionModeWithDefault } from './shared'\n\nimport type { ExtractPropTypes } from 'vue'\n\nconst { date, disabledDate, parsedValue } = datePickerSharedProps\n\nexport const basicYearTableProps = buildProps({\n  date,\n  disabledDate,\n  parsedValue,\n  selectionMode: selectionModeWithDefault('year'),\n} as const)\n\nexport type BasicYearTableProps = ExtractPropTypes<typeof basicYearTableProps>\n"], "mappings": ";;;AAEA,IAAQA,IAAI,GAAgCC,qBAAqB,CAAzDD,IAAI;EAAEE,YAAY,GAAkBD,qBAAqB,CAAnDC,YAAY;EAAEC,WAAW,GAAKF,qBAAqB,CAArCE,WAAW;AAC3B,IAACC,mBAAmB,GAAGC,UAAU,CAAC;EAC5CL,IAAI;EACJE,YAAY;EACZC,WAAW;EACXG,aAAa,EAAEC,wBAAwB,CAAC,MAAM;AAChD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}