{"ast": null, "code": "'use strict';\n\n/* eslint-disable no-bitwise */\nvar decodeCache = {};\nfunction getDecodeCache(exclude) {\n  var i,\n    ch,\n    cache = decodeCache[exclude];\n  if (cache) {\n    return cache;\n  }\n  cache = decodeCache[exclude] = [];\n  for (i = 0; i < 128; i++) {\n    ch = String.fromCharCode(i);\n    cache.push(ch);\n  }\n  for (i = 0; i < exclude.length; i++) {\n    ch = exclude.charCodeAt(i);\n    cache[ch] = '%' + ('0' + ch.toString(16).toUpperCase()).slice(-2);\n  }\n  return cache;\n}\n\n// Decode percent-encoded string.\n//\nfunction decode(string, exclude) {\n  var cache;\n  if (typeof exclude !== 'string') {\n    exclude = decode.defaultChars;\n  }\n  cache = getDecodeCache(exclude);\n  return string.replace(/(%[a-f0-9]{2})+/gi, function (seq) {\n    var i,\n      l,\n      b1,\n      b2,\n      b3,\n      b4,\n      chr,\n      result = '';\n    for (i = 0, l = seq.length; i < l; i += 3) {\n      b1 = parseInt(seq.slice(i + 1, i + 3), 16);\n      if (b1 < 0x80) {\n        result += cache[b1];\n        continue;\n      }\n      if ((b1 & 0xE0) === 0xC0 && i + 3 < l) {\n        // 110xxxxx 10xxxxxx\n        b2 = parseInt(seq.slice(i + 4, i + 6), 16);\n        if ((b2 & 0xC0) === 0x80) {\n          chr = b1 << 6 & 0x7C0 | b2 & 0x3F;\n          if (chr < 0x80) {\n            result += \"\\uFFFD\\uFFFD\";\n          } else {\n            result += String.fromCharCode(chr);\n          }\n          i += 3;\n          continue;\n        }\n      }\n      if ((b1 & 0xF0) === 0xE0 && i + 6 < l) {\n        // 1110xxxx 10xxxxxx 10xxxxxx\n        b2 = parseInt(seq.slice(i + 4, i + 6), 16);\n        b3 = parseInt(seq.slice(i + 7, i + 9), 16);\n        if ((b2 & 0xC0) === 0x80 && (b3 & 0xC0) === 0x80) {\n          chr = b1 << 12 & 0xF000 | b2 << 6 & 0xFC0 | b3 & 0x3F;\n          if (chr < 0x800 || chr >= 0xD800 && chr <= 0xDFFF) {\n            result += \"\\uFFFD\\uFFFD\\uFFFD\";\n          } else {\n            result += String.fromCharCode(chr);\n          }\n          i += 6;\n          continue;\n        }\n      }\n      if ((b1 & 0xF8) === 0xF0 && i + 9 < l) {\n        // 111110xx 10xxxxxx 10xxxxxx 10xxxxxx\n        b2 = parseInt(seq.slice(i + 4, i + 6), 16);\n        b3 = parseInt(seq.slice(i + 7, i + 9), 16);\n        b4 = parseInt(seq.slice(i + 10, i + 12), 16);\n        if ((b2 & 0xC0) === 0x80 && (b3 & 0xC0) === 0x80 && (b4 & 0xC0) === 0x80) {\n          chr = b1 << 18 & 0x1C0000 | b2 << 12 & 0x3F000 | b3 << 6 & 0xFC0 | b4 & 0x3F;\n          if (chr < 0x10000 || chr > 0x10FFFF) {\n            result += \"\\uFFFD\\uFFFD\\uFFFD\\uFFFD\";\n          } else {\n            chr -= 0x10000;\n            result += String.fromCharCode(0xD800 + (chr >> 10), 0xDC00 + (chr & 0x3FF));\n          }\n          i += 9;\n          continue;\n        }\n      }\n      result += \"\\uFFFD\";\n    }\n    return result;\n  });\n}\ndecode.defaultChars = ';/?:@&=+$,#';\ndecode.componentChars = '';\nmodule.exports = decode;", "map": {"version": 3, "names": ["decodeCache", "getDecodeCache", "exclude", "i", "ch", "cache", "String", "fromCharCode", "push", "length", "charCodeAt", "toString", "toUpperCase", "slice", "decode", "string", "defaultChars", "replace", "seq", "l", "b1", "b2", "b3", "b4", "chr", "result", "parseInt", "componentChars", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/mdurl@1.0.1/node_modules/mdurl/decode.js"], "sourcesContent": ["\n'use strict';\n\n\n/* eslint-disable no-bitwise */\n\nvar decodeCache = {};\n\nfunction getDecodeCache(exclude) {\n  var i, ch, cache = decodeCache[exclude];\n  if (cache) { return cache; }\n\n  cache = decodeCache[exclude] = [];\n\n  for (i = 0; i < 128; i++) {\n    ch = String.fromCharCode(i);\n    cache.push(ch);\n  }\n\n  for (i = 0; i < exclude.length; i++) {\n    ch = exclude.charCodeAt(i);\n    cache[ch] = '%' + ('0' + ch.toString(16).toUpperCase()).slice(-2);\n  }\n\n  return cache;\n}\n\n\n// Decode percent-encoded string.\n//\nfunction decode(string, exclude) {\n  var cache;\n\n  if (typeof exclude !== 'string') {\n    exclude = decode.defaultChars;\n  }\n\n  cache = getDecodeCache(exclude);\n\n  return string.replace(/(%[a-f0-9]{2})+/gi, function(seq) {\n    var i, l, b1, b2, b3, b4, chr,\n        result = '';\n\n    for (i = 0, l = seq.length; i < l; i += 3) {\n      b1 = parseInt(seq.slice(i + 1, i + 3), 16);\n\n      if (b1 < 0x80) {\n        result += cache[b1];\n        continue;\n      }\n\n      if ((b1 & 0xE0) === 0xC0 && (i + 3 < l)) {\n        // 110xxxxx 10xxxxxx\n        b2 = parseInt(seq.slice(i + 4, i + 6), 16);\n\n        if ((b2 & 0xC0) === 0x80) {\n          chr = ((b1 << 6) & 0x7C0) | (b2 & 0x3F);\n\n          if (chr < 0x80) {\n            result += '\\ufffd\\ufffd';\n          } else {\n            result += String.fromCharCode(chr);\n          }\n\n          i += 3;\n          continue;\n        }\n      }\n\n      if ((b1 & 0xF0) === 0xE0 && (i + 6 < l)) {\n        // 1110xxxx 10xxxxxx 10xxxxxx\n        b2 = parseInt(seq.slice(i + 4, i + 6), 16);\n        b3 = parseInt(seq.slice(i + 7, i + 9), 16);\n\n        if ((b2 & 0xC0) === 0x80 && (b3 & 0xC0) === 0x80) {\n          chr = ((b1 << 12) & 0xF000) | ((b2 << 6) & 0xFC0) | (b3 & 0x3F);\n\n          if (chr < 0x800 || (chr >= 0xD800 && chr <= 0xDFFF)) {\n            result += '\\ufffd\\ufffd\\ufffd';\n          } else {\n            result += String.fromCharCode(chr);\n          }\n\n          i += 6;\n          continue;\n        }\n      }\n\n      if ((b1 & 0xF8) === 0xF0 && (i + 9 < l)) {\n        // 111110xx 10xxxxxx 10xxxxxx 10xxxxxx\n        b2 = parseInt(seq.slice(i + 4, i + 6), 16);\n        b3 = parseInt(seq.slice(i + 7, i + 9), 16);\n        b4 = parseInt(seq.slice(i + 10, i + 12), 16);\n\n        if ((b2 & 0xC0) === 0x80 && (b3 & 0xC0) === 0x80 && (b4 & 0xC0) === 0x80) {\n          chr = ((b1 << 18) & 0x1C0000) | ((b2 << 12) & 0x3F000) | ((b3 << 6) & 0xFC0) | (b4 & 0x3F);\n\n          if (chr < 0x10000 || chr > 0x10FFFF) {\n            result += '\\ufffd\\ufffd\\ufffd\\ufffd';\n          } else {\n            chr -= 0x10000;\n            result += String.fromCharCode(0xD800 + (chr >> 10), 0xDC00 + (chr & 0x3FF));\n          }\n\n          i += 9;\n          continue;\n        }\n      }\n\n      result += '\\ufffd';\n    }\n\n    return result;\n  });\n}\n\n\ndecode.defaultChars   = ';/?:@&=+$,#';\ndecode.componentChars = '';\n\n\nmodule.exports = decode;\n"], "mappings": "AACA,YAAY;;AAGZ;AAEA,IAAIA,WAAW,GAAG,CAAC,CAAC;AAEpB,SAASC,cAAcA,CAACC,OAAO,EAAE;EAC/B,IAAIC,CAAC;IAAEC,EAAE;IAAEC,KAAK,GAAGL,WAAW,CAACE,OAAO,CAAC;EACvC,IAAIG,KAAK,EAAE;IAAE,OAAOA,KAAK;EAAE;EAE3BA,KAAK,GAAGL,WAAW,CAACE,OAAO,CAAC,GAAG,EAAE;EAEjC,KAAKC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;IACxBC,EAAE,GAAGE,MAAM,CAACC,YAAY,CAACJ,CAAC,CAAC;IAC3BE,KAAK,CAACG,IAAI,CAACJ,EAAE,CAAC;EAChB;EAEA,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,OAAO,CAACO,MAAM,EAAEN,CAAC,EAAE,EAAE;IACnCC,EAAE,GAAGF,OAAO,CAACQ,UAAU,CAACP,CAAC,CAAC;IAC1BE,KAAK,CAACD,EAAE,CAAC,GAAG,GAAG,GAAG,CAAC,GAAG,GAAGA,EAAE,CAACO,QAAQ,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;EACnE;EAEA,OAAOR,KAAK;AACd;;AAGA;AACA;AACA,SAASS,MAAMA,CAACC,MAAM,EAAEb,OAAO,EAAE;EAC/B,IAAIG,KAAK;EAET,IAAI,OAAOH,OAAO,KAAK,QAAQ,EAAE;IAC/BA,OAAO,GAAGY,MAAM,CAACE,YAAY;EAC/B;EAEAX,KAAK,GAAGJ,cAAc,CAACC,OAAO,CAAC;EAE/B,OAAOa,MAAM,CAACE,OAAO,CAAC,mBAAmB,EAAE,UAASC,GAAG,EAAE;IACvD,IAAIf,CAAC;MAAEgB,CAAC;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,EAAE;MAAEC,GAAG;MACzBC,MAAM,GAAG,EAAE;IAEf,KAAKtB,CAAC,GAAG,CAAC,EAAEgB,CAAC,GAAGD,GAAG,CAACT,MAAM,EAAEN,CAAC,GAAGgB,CAAC,EAAEhB,CAAC,IAAI,CAAC,EAAE;MACzCiB,EAAE,GAAGM,QAAQ,CAACR,GAAG,CAACL,KAAK,CAACV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;MAE1C,IAAIiB,EAAE,GAAG,IAAI,EAAE;QACbK,MAAM,IAAIpB,KAAK,CAACe,EAAE,CAAC;QACnB;MACF;MAEA,IAAI,CAACA,EAAE,GAAG,IAAI,MAAM,IAAI,IAAKjB,CAAC,GAAG,CAAC,GAAGgB,CAAE,EAAE;QACvC;QACAE,EAAE,GAAGK,QAAQ,CAACR,GAAG,CAACL,KAAK,CAACV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;QAE1C,IAAI,CAACkB,EAAE,GAAG,IAAI,MAAM,IAAI,EAAE;UACxBG,GAAG,GAAKJ,EAAE,IAAI,CAAC,GAAI,KAAK,GAAKC,EAAE,GAAG,IAAK;UAEvC,IAAIG,GAAG,GAAG,IAAI,EAAE;YACdC,MAAM,IAAI,cAAc;UAC1B,CAAC,MAAM;YACLA,MAAM,IAAInB,MAAM,CAACC,YAAY,CAACiB,GAAG,CAAC;UACpC;UAEArB,CAAC,IAAI,CAAC;UACN;QACF;MACF;MAEA,IAAI,CAACiB,EAAE,GAAG,IAAI,MAAM,IAAI,IAAKjB,CAAC,GAAG,CAAC,GAAGgB,CAAE,EAAE;QACvC;QACAE,EAAE,GAAGK,QAAQ,CAACR,GAAG,CAACL,KAAK,CAACV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;QAC1CmB,EAAE,GAAGI,QAAQ,CAACR,GAAG,CAACL,KAAK,CAACV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;QAE1C,IAAI,CAACkB,EAAE,GAAG,IAAI,MAAM,IAAI,IAAI,CAACC,EAAE,GAAG,IAAI,MAAM,IAAI,EAAE;UAChDE,GAAG,GAAKJ,EAAE,IAAI,EAAE,GAAI,MAAM,GAAMC,EAAE,IAAI,CAAC,GAAI,KAAM,GAAIC,EAAE,GAAG,IAAK;UAE/D,IAAIE,GAAG,GAAG,KAAK,IAAKA,GAAG,IAAI,MAAM,IAAIA,GAAG,IAAI,MAAO,EAAE;YACnDC,MAAM,IAAI,oBAAoB;UAChC,CAAC,MAAM;YACLA,MAAM,IAAInB,MAAM,CAACC,YAAY,CAACiB,GAAG,CAAC;UACpC;UAEArB,CAAC,IAAI,CAAC;UACN;QACF;MACF;MAEA,IAAI,CAACiB,EAAE,GAAG,IAAI,MAAM,IAAI,IAAKjB,CAAC,GAAG,CAAC,GAAGgB,CAAE,EAAE;QACvC;QACAE,EAAE,GAAGK,QAAQ,CAACR,GAAG,CAACL,KAAK,CAACV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;QAC1CmB,EAAE,GAAGI,QAAQ,CAACR,GAAG,CAACL,KAAK,CAACV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;QAC1CoB,EAAE,GAAGG,QAAQ,CAACR,GAAG,CAACL,KAAK,CAACV,CAAC,GAAG,EAAE,EAAEA,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC;QAE5C,IAAI,CAACkB,EAAE,GAAG,IAAI,MAAM,IAAI,IAAI,CAACC,EAAE,GAAG,IAAI,MAAM,IAAI,IAAI,CAACC,EAAE,GAAG,IAAI,MAAM,IAAI,EAAE;UACxEC,GAAG,GAAKJ,EAAE,IAAI,EAAE,GAAI,QAAQ,GAAMC,EAAE,IAAI,EAAE,GAAI,OAAQ,GAAKC,EAAE,IAAI,CAAC,GAAI,KAAM,GAAIC,EAAE,GAAG,IAAK;UAE1F,IAAIC,GAAG,GAAG,OAAO,IAAIA,GAAG,GAAG,QAAQ,EAAE;YACnCC,MAAM,IAAI,0BAA0B;UACtC,CAAC,MAAM;YACLD,GAAG,IAAI,OAAO;YACdC,MAAM,IAAInB,MAAM,CAACC,YAAY,CAAC,MAAM,IAAIiB,GAAG,IAAI,EAAE,CAAC,EAAE,MAAM,IAAIA,GAAG,GAAG,KAAK,CAAC,CAAC;UAC7E;UAEArB,CAAC,IAAI,CAAC;UACN;QACF;MACF;MAEAsB,MAAM,IAAI,QAAQ;IACpB;IAEA,OAAOA,MAAM;EACf,CAAC,CAAC;AACJ;AAGAX,MAAM,CAACE,YAAY,GAAK,aAAa;AACrCF,MAAM,CAACa,cAAc,GAAG,EAAE;AAG1BC,MAAM,CAACC,OAAO,GAAGf,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}