{"ast": null, "code": "// fences (``` lang, ~~~ lang)\n\n'use strict';\n\nmodule.exports = function fence(state, startLine, endLine, silent) {\n  var marker,\n    len,\n    params,\n    nextLine,\n    mem,\n    token,\n    markup,\n    haveEndMarker = false,\n    pos = state.bMarks[startLine] + state.tShift[startLine],\n    max = state.eMarks[startLine];\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) {\n    return false;\n  }\n  if (pos + 3 > max) {\n    return false;\n  }\n  marker = state.src.charCodeAt(pos);\n  if (marker !== 0x7E /* ~ */ && marker !== 0x60 /* ` */) {\n    return false;\n  }\n\n  // scan marker length\n  mem = pos;\n  pos = state.skipChars(pos, marker);\n  len = pos - mem;\n  if (len < 3) {\n    return false;\n  }\n  markup = state.src.slice(mem, pos);\n  params = state.src.slice(pos, max);\n  if (marker === 0x60 /* ` */) {\n    if (params.indexOf(String.fromCharCode(marker)) >= 0) {\n      return false;\n    }\n  }\n\n  // Since start is found, we can report success here in validation mode\n  if (silent) {\n    return true;\n  }\n\n  // search end of block\n  nextLine = startLine;\n  for (;;) {\n    nextLine++;\n    if (nextLine >= endLine) {\n      // unclosed block should be autoclosed by end of document.\n      // also block seems to be autoclosed by end of parent\n      break;\n    }\n    pos = mem = state.bMarks[nextLine] + state.tShift[nextLine];\n    max = state.eMarks[nextLine];\n    if (pos < max && state.sCount[nextLine] < state.blkIndent) {\n      // non-empty line with negative indent should stop the list:\n      // - ```\n      //  test\n      break;\n    }\n    if (state.src.charCodeAt(pos) !== marker) {\n      continue;\n    }\n    if (state.sCount[nextLine] - state.blkIndent >= 4) {\n      // closing fence should be indented less than 4 spaces\n      continue;\n    }\n    pos = state.skipChars(pos, marker);\n\n    // closing code fence must be at least as long as the opening one\n    if (pos - mem < len) {\n      continue;\n    }\n\n    // make sure tail has spaces only\n    pos = state.skipSpaces(pos);\n    if (pos < max) {\n      continue;\n    }\n    haveEndMarker = true;\n    // found!\n    break;\n  }\n\n  // If a fence has heading spaces, they should be removed from its inner block\n  len = state.sCount[startLine];\n  state.line = nextLine + (haveEndMarker ? 1 : 0);\n  token = state.push('fence', 'code', 0);\n  token.info = params;\n  token.content = state.getLines(startLine + 1, nextLine, len, true);\n  token.markup = markup;\n  token.map = [startLine, state.line];\n  return true;\n};", "map": {"version": 3, "names": ["module", "exports", "fence", "state", "startLine", "endLine", "silent", "marker", "len", "params", "nextLine", "mem", "token", "markup", "have<PERSON>nd<PERSON><PERSON><PERSON>", "pos", "bMarks", "tShift", "max", "eMarks", "sCount", "blkIndent", "src", "charCodeAt", "<PERSON><PERSON><PERSON><PERSON>", "slice", "indexOf", "String", "fromCharCode", "skipSpaces", "line", "push", "info", "content", "getLines", "map"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_block/fence.js"], "sourcesContent": ["// fences (``` lang, ~~~ lang)\n\n'use strict';\n\n\nmodule.exports = function fence(state, startLine, endLine, silent) {\n  var marker, len, params, nextLine, mem, token, markup,\n      haveEndMarker = false,\n      pos = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine];\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false; }\n\n  if (pos + 3 > max) { return false; }\n\n  marker = state.src.charCodeAt(pos);\n\n  if (marker !== 0x7E/* ~ */ && marker !== 0x60 /* ` */) {\n    return false;\n  }\n\n  // scan marker length\n  mem = pos;\n  pos = state.skipChars(pos, marker);\n\n  len = pos - mem;\n\n  if (len < 3) { return false; }\n\n  markup = state.src.slice(mem, pos);\n  params = state.src.slice(pos, max);\n\n  if (marker === 0x60 /* ` */) {\n    if (params.indexOf(String.fromCharCode(marker)) >= 0) {\n      return false;\n    }\n  }\n\n  // Since start is found, we can report success here in validation mode\n  if (silent) { return true; }\n\n  // search end of block\n  nextLine = startLine;\n\n  for (;;) {\n    nextLine++;\n    if (nextLine >= endLine) {\n      // unclosed block should be autoclosed by end of document.\n      // also block seems to be autoclosed by end of parent\n      break;\n    }\n\n    pos = mem = state.bMarks[nextLine] + state.tShift[nextLine];\n    max = state.eMarks[nextLine];\n\n    if (pos < max && state.sCount[nextLine] < state.blkIndent) {\n      // non-empty line with negative indent should stop the list:\n      // - ```\n      //  test\n      break;\n    }\n\n    if (state.src.charCodeAt(pos) !== marker) { continue; }\n\n    if (state.sCount[nextLine] - state.blkIndent >= 4) {\n      // closing fence should be indented less than 4 spaces\n      continue;\n    }\n\n    pos = state.skipChars(pos, marker);\n\n    // closing code fence must be at least as long as the opening one\n    if (pos - mem < len) { continue; }\n\n    // make sure tail has spaces only\n    pos = state.skipSpaces(pos);\n\n    if (pos < max) { continue; }\n\n    haveEndMarker = true;\n    // found!\n    break;\n  }\n\n  // If a fence has heading spaces, they should be removed from its inner block\n  len = state.sCount[startLine];\n\n  state.line = nextLine + (haveEndMarker ? 1 : 0);\n\n  token         = state.push('fence', 'code', 0);\n  token.info    = params;\n  token.content = state.getLines(startLine + 1, nextLine, len, true);\n  token.markup  = markup;\n  token.map     = [ startLine, state.line ];\n\n  return true;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAGZA,MAAM,CAACC,OAAO,GAAG,SAASC,KAAKA,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAE;EACjE,IAAIC,MAAM;IAAEC,GAAG;IAAEC,MAAM;IAAEC,QAAQ;IAAEC,GAAG;IAAEC,KAAK;IAAEC,MAAM;IACjDC,aAAa,GAAG,KAAK;IACrBC,GAAG,GAAGZ,KAAK,CAACa,MAAM,CAACZ,SAAS,CAAC,GAAGD,KAAK,CAACc,MAAM,CAACb,SAAS,CAAC;IACvDc,GAAG,GAAGf,KAAK,CAACgB,MAAM,CAACf,SAAS,CAAC;;EAEjC;EACA,IAAID,KAAK,CAACiB,MAAM,CAAChB,SAAS,CAAC,GAAGD,KAAK,CAACkB,SAAS,IAAI,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAEpE,IAAIN,GAAG,GAAG,CAAC,GAAGG,GAAG,EAAE;IAAE,OAAO,KAAK;EAAE;EAEnCX,MAAM,GAAGJ,KAAK,CAACmB,GAAG,CAACC,UAAU,CAACR,GAAG,CAAC;EAElC,IAAIR,MAAM,KAAK,IAAI,YAAWA,MAAM,KAAK,IAAI,CAAC,SAAS;IACrD,OAAO,KAAK;EACd;;EAEA;EACAI,GAAG,GAAGI,GAAG;EACTA,GAAG,GAAGZ,KAAK,CAACqB,SAAS,CAACT,GAAG,EAAER,MAAM,CAAC;EAElCC,GAAG,GAAGO,GAAG,GAAGJ,GAAG;EAEf,IAAIH,GAAG,GAAG,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAE7BK,MAAM,GAAGV,KAAK,CAACmB,GAAG,CAACG,KAAK,CAACd,GAAG,EAAEI,GAAG,CAAC;EAClCN,MAAM,GAAGN,KAAK,CAACmB,GAAG,CAACG,KAAK,CAACV,GAAG,EAAEG,GAAG,CAAC;EAElC,IAAIX,MAAM,KAAK,IAAI,CAAC,SAAS;IAC3B,IAAIE,MAAM,CAACiB,OAAO,CAACC,MAAM,CAACC,YAAY,CAACrB,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE;MACpD,OAAO,KAAK;IACd;EACF;;EAEA;EACA,IAAID,MAAM,EAAE;IAAE,OAAO,IAAI;EAAE;;EAE3B;EACAI,QAAQ,GAAGN,SAAS;EAEpB,SAAS;IACPM,QAAQ,EAAE;IACV,IAAIA,QAAQ,IAAIL,OAAO,EAAE;MACvB;MACA;MACA;IACF;IAEAU,GAAG,GAAGJ,GAAG,GAAGR,KAAK,CAACa,MAAM,CAACN,QAAQ,CAAC,GAAGP,KAAK,CAACc,MAAM,CAACP,QAAQ,CAAC;IAC3DQ,GAAG,GAAGf,KAAK,CAACgB,MAAM,CAACT,QAAQ,CAAC;IAE5B,IAAIK,GAAG,GAAGG,GAAG,IAAIf,KAAK,CAACiB,MAAM,CAACV,QAAQ,CAAC,GAAGP,KAAK,CAACkB,SAAS,EAAE;MACzD;MACA;MACA;MACA;IACF;IAEA,IAAIlB,KAAK,CAACmB,GAAG,CAACC,UAAU,CAACR,GAAG,CAAC,KAAKR,MAAM,EAAE;MAAE;IAAU;IAEtD,IAAIJ,KAAK,CAACiB,MAAM,CAACV,QAAQ,CAAC,GAAGP,KAAK,CAACkB,SAAS,IAAI,CAAC,EAAE;MACjD;MACA;IACF;IAEAN,GAAG,GAAGZ,KAAK,CAACqB,SAAS,CAACT,GAAG,EAAER,MAAM,CAAC;;IAElC;IACA,IAAIQ,GAAG,GAAGJ,GAAG,GAAGH,GAAG,EAAE;MAAE;IAAU;;IAEjC;IACAO,GAAG,GAAGZ,KAAK,CAAC0B,UAAU,CAACd,GAAG,CAAC;IAE3B,IAAIA,GAAG,GAAGG,GAAG,EAAE;MAAE;IAAU;IAE3BJ,aAAa,GAAG,IAAI;IACpB;IACA;EACF;;EAEA;EACAN,GAAG,GAAGL,KAAK,CAACiB,MAAM,CAAChB,SAAS,CAAC;EAE7BD,KAAK,CAAC2B,IAAI,GAAGpB,QAAQ,IAAII,aAAa,GAAG,CAAC,GAAG,CAAC,CAAC;EAE/CF,KAAK,GAAWT,KAAK,CAAC4B,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;EAC9CnB,KAAK,CAACoB,IAAI,GAAMvB,MAAM;EACtBG,KAAK,CAACqB,OAAO,GAAG9B,KAAK,CAAC+B,QAAQ,CAAC9B,SAAS,GAAG,CAAC,EAAEM,QAAQ,EAAEF,GAAG,EAAE,IAAI,CAAC;EAClEI,KAAK,CAACC,MAAM,GAAIA,MAAM;EACtBD,KAAK,CAACuB,GAAG,GAAO,CAAE/B,SAAS,EAAED,KAAK,CAAC2B,IAAI,CAAE;EAEzC,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}