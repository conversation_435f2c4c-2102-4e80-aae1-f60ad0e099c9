{"ast": null, "code": "// Markdown-it plugin to render GitHub-style task lists; see\n//\n// https://github.com/blog/1375-task-lists-in-gfm-issues-pulls-comments\n// https://github.com/blog/1825-task-lists-in-all-markdown-documents\n\nvar disableCheckboxes = true;\nvar useLabelWrapper = false;\nvar useLabelAfter = false;\nmodule.exports = function (md, options) {\n  if (options) {\n    disableCheckboxes = !options.enabled;\n    useLabelWrapper = !!options.label;\n    useLabelAfter = !!options.labelAfter;\n  }\n  md.core.ruler.after('inline', 'github-task-lists', function (state) {\n    var tokens = state.tokens;\n    for (var i = 2; i < tokens.length; i++) {\n      if (isTodoItem(tokens, i)) {\n        todoify(tokens[i], state.Token);\n        attrSet(tokens[i - 2], 'class', 'task-list-item' + (!disableCheckboxes ? ' enabled' : ''));\n        attrSet(tokens[parentToken(tokens, i - 2)], 'class', 'contains-task-list');\n      }\n    }\n  });\n};\nfunction attrSet(token, name, value) {\n  var index = token.attrIndex(name);\n  var attr = [name, value];\n  if (index < 0) {\n    token.attrPush(attr);\n  } else {\n    token.attrs[index] = attr;\n  }\n}\nfunction parentToken(tokens, index) {\n  var targetLevel = tokens[index].level - 1;\n  for (var i = index - 1; i >= 0; i--) {\n    if (tokens[i].level === targetLevel) {\n      return i;\n    }\n  }\n  return -1;\n}\nfunction isTodoItem(tokens, index) {\n  return isInline(tokens[index]) && isParagraph(tokens[index - 1]) && isListItem(tokens[index - 2]) && startsWithTodoMarkdown(tokens[index]);\n}\nfunction todoify(token, TokenConstructor) {\n  token.children.unshift(makeCheckbox(token, TokenConstructor));\n  token.children[1].content = token.children[1].content.slice(3);\n  token.content = token.content.slice(3);\n  if (useLabelWrapper) {\n    if (useLabelAfter) {\n      token.children.pop();\n\n      // Use large random number as id property of the checkbox.\n      var id = 'task-item-' + Math.ceil(Math.random() * (10000 * 1000) - 1000);\n      token.children[0].content = token.children[0].content.slice(0, -1) + ' id=\"' + id + '\">';\n      token.children.push(afterLabel(token.content, id, TokenConstructor));\n    } else {\n      token.children.unshift(beginLabel(TokenConstructor));\n      token.children.push(endLabel(TokenConstructor));\n    }\n  }\n}\nfunction makeCheckbox(token, TokenConstructor) {\n  var checkbox = new TokenConstructor('html_inline', '', 0);\n  var disabledAttr = disableCheckboxes ? ' disabled=\"\" ' : '';\n  if (token.content.indexOf('[ ] ') === 0) {\n    checkbox.content = '<input class=\"task-list-item-checkbox\"' + disabledAttr + 'type=\"checkbox\">';\n  } else if (token.content.indexOf('[x] ') === 0 || token.content.indexOf('[X] ') === 0) {\n    checkbox.content = '<input class=\"task-list-item-checkbox\" checked=\"\"' + disabledAttr + 'type=\"checkbox\">';\n  }\n  return checkbox;\n}\n\n// these next two functions are kind of hacky; probably should really be a\n// true block-level token with .tag=='label'\nfunction beginLabel(TokenConstructor) {\n  var token = new TokenConstructor('html_inline', '', 0);\n  token.content = '<label>';\n  return token;\n}\nfunction endLabel(TokenConstructor) {\n  var token = new TokenConstructor('html_inline', '', 0);\n  token.content = '</label>';\n  return token;\n}\nfunction afterLabel(content, id, TokenConstructor) {\n  var token = new TokenConstructor('html_inline', '', 0);\n  token.content = '<label class=\"task-list-item-label\" for=\"' + id + '\">' + content + '</label>';\n  token.attrs = [{\n    for: id\n  }];\n  return token;\n}\nfunction isInline(token) {\n  return token.type === 'inline';\n}\nfunction isParagraph(token) {\n  return token.type === 'paragraph_open';\n}\nfunction isListItem(token) {\n  return token.type === 'list_item_open';\n}\nfunction startsWithTodoMarkdown(token) {\n  // leading whitespace in a list item is already trimmed off by markdown-it\n  return token.content.indexOf('[ ] ') === 0 || token.content.indexOf('[x] ') === 0 || token.content.indexOf('[X] ') === 0;\n}", "map": {"version": 3, "names": ["disableCheckboxes", "useLabelWrapper", "useLabelAfter", "module", "exports", "md", "options", "enabled", "label", "labelAfter", "core", "ruler", "after", "state", "tokens", "i", "length", "isTodoItem", "todoify", "Token", "attrSet", "parentToken", "token", "name", "value", "index", "attrIndex", "attr", "attrPush", "attrs", "targetLevel", "level", "isInline", "isParagraph", "isListItem", "startsWithTodoMarkdown", "TokenConstructor", "children", "unshift", "makeCheckbox", "content", "slice", "pop", "id", "Math", "ceil", "random", "push", "<PERSON><PERSON><PERSON><PERSON>", "beginLabel", "endLabel", "checkbox", "disabledAttr", "indexOf", "for", "type"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it-task-lists@2.1.1/node_modules/markdown-it-task-lists/index.js"], "sourcesContent": ["// Markdown-it plugin to render GitHub-style task lists; see\n//\n// https://github.com/blog/1375-task-lists-in-gfm-issues-pulls-comments\n// https://github.com/blog/1825-task-lists-in-all-markdown-documents\n\nvar disableCheckboxes = true;\nvar useLabelWrapper = false;\nvar useLabelAfter = false;\n\nmodule.exports = function(md, options) {\n\tif (options) {\n\t\tdisableCheckboxes = !options.enabled;\n\t\tuseLabelWrapper = !!options.label;\n\t\tuseLabelAfter = !!options.labelAfter;\n\t}\n\n\tmd.core.ruler.after('inline', 'github-task-lists', function(state) {\n\t\tvar tokens = state.tokens;\n\t\tfor (var i = 2; i < tokens.length; i++) {\n\t\t\tif (isTodoItem(tokens, i)) {\n\t\t\t\ttodoify(tokens[i], state.Token);\n\t\t\t\tattrSet(tokens[i-2], 'class', 'task-list-item' + (!disableCheckboxes ? ' enabled' : ''));\n\t\t\t\tattrSet(tokens[parentToken(tokens, i-2)], 'class', 'contains-task-list');\n\t\t\t}\n\t\t}\n\t});\n};\n\nfunction attrSet(token, name, value) {\n\tvar index = token.attrIndex(name);\n\tvar attr = [name, value];\n\n\tif (index < 0) {\n\t\ttoken.attrPush(attr);\n\t} else {\n\t\ttoken.attrs[index] = attr;\n\t}\n}\n\nfunction parentToken(tokens, index) {\n\tvar targetLevel = tokens[index].level - 1;\n\tfor (var i = index - 1; i >= 0; i--) {\n\t\tif (tokens[i].level === targetLevel) {\n\t\t\treturn i;\n\t\t}\n\t}\n\treturn -1;\n}\n\nfunction isTodoItem(tokens, index) {\n\treturn isInline(tokens[index]) &&\n\t       isParagraph(tokens[index - 1]) &&\n\t       isListItem(tokens[index - 2]) &&\n\t       startsWithTodoMarkdown(tokens[index]);\n}\n\nfunction todoify(token, TokenConstructor) {\n\ttoken.children.unshift(makeCheckbox(token, TokenConstructor));\n\ttoken.children[1].content = token.children[1].content.slice(3);\n\ttoken.content = token.content.slice(3);\n\n\tif (useLabelWrapper) {\n\t\tif (useLabelAfter) {\n\t\t\ttoken.children.pop();\n\n\t\t\t// Use large random number as id property of the checkbox.\n\t\t\tvar id = 'task-item-' + Math.ceil(Math.random() * (10000 * 1000) - 1000);\n\t\t\ttoken.children[0].content = token.children[0].content.slice(0, -1) + ' id=\"' + id + '\">';\n\t\t\ttoken.children.push(afterLabel(token.content, id, TokenConstructor));\n\t\t} else {\n\t\t\ttoken.children.unshift(beginLabel(TokenConstructor));\n\t\t\ttoken.children.push(endLabel(TokenConstructor));\n\t\t}\n\t}\n}\n\nfunction makeCheckbox(token, TokenConstructor) {\n\tvar checkbox = new TokenConstructor('html_inline', '', 0);\n\tvar disabledAttr = disableCheckboxes ? ' disabled=\"\" ' : '';\n\tif (token.content.indexOf('[ ] ') === 0) {\n\t\tcheckbox.content = '<input class=\"task-list-item-checkbox\"' + disabledAttr + 'type=\"checkbox\">';\n\t} else if (token.content.indexOf('[x] ') === 0 || token.content.indexOf('[X] ') === 0) {\n\t\tcheckbox.content = '<input class=\"task-list-item-checkbox\" checked=\"\"' + disabledAttr + 'type=\"checkbox\">';\n\t}\n\treturn checkbox;\n}\n\n// these next two functions are kind of hacky; probably should really be a\n// true block-level token with .tag=='label'\nfunction beginLabel(TokenConstructor) {\n\tvar token = new TokenConstructor('html_inline', '', 0);\n\ttoken.content = '<label>';\n\treturn token;\n}\n\nfunction endLabel(TokenConstructor) {\n\tvar token = new TokenConstructor('html_inline', '', 0);\n\ttoken.content = '</label>';\n\treturn token;\n}\n\nfunction afterLabel(content, id, TokenConstructor) {\n\tvar token = new TokenConstructor('html_inline', '', 0);\n\ttoken.content = '<label class=\"task-list-item-label\" for=\"' + id + '\">' + content + '</label>';\n\ttoken.attrs = [{for: id}];\n\treturn token;\n}\n\nfunction isInline(token) { return token.type === 'inline'; }\nfunction isParagraph(token) { return token.type === 'paragraph_open'; }\nfunction isListItem(token) { return token.type === 'list_item_open'; }\n\nfunction startsWithTodoMarkdown(token) {\n\t// leading whitespace in a list item is already trimmed off by markdown-it\n\treturn token.content.indexOf('[ ] ') === 0 || token.content.indexOf('[x] ') === 0 || token.content.indexOf('[X] ') === 0;\n}\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,IAAIA,iBAAiB,GAAG,IAAI;AAC5B,IAAIC,eAAe,GAAG,KAAK;AAC3B,IAAIC,aAAa,GAAG,KAAK;AAEzBC,MAAM,CAACC,OAAO,GAAG,UAASC,EAAE,EAAEC,OAAO,EAAE;EACtC,IAAIA,OAAO,EAAE;IACZN,iBAAiB,GAAG,CAACM,OAAO,CAACC,OAAO;IACpCN,eAAe,GAAG,CAAC,CAACK,OAAO,CAACE,KAAK;IACjCN,aAAa,GAAG,CAAC,CAACI,OAAO,CAACG,UAAU;EACrC;EAEAJ,EAAE,CAACK,IAAI,CAACC,KAAK,CAACC,KAAK,CAAC,QAAQ,EAAE,mBAAmB,EAAE,UAASC,KAAK,EAAE;IAClE,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IACzB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,MAAM,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MACvC,IAAIE,UAAU,CAACH,MAAM,EAAEC,CAAC,CAAC,EAAE;QAC1BG,OAAO,CAACJ,MAAM,CAACC,CAAC,CAAC,EAAEF,KAAK,CAACM,KAAK,CAAC;QAC/BC,OAAO,CAACN,MAAM,CAACC,CAAC,GAAC,CAAC,CAAC,EAAE,OAAO,EAAE,gBAAgB,IAAI,CAACf,iBAAiB,GAAG,UAAU,GAAG,EAAE,CAAC,CAAC;QACxFoB,OAAO,CAACN,MAAM,CAACO,WAAW,CAACP,MAAM,EAAEC,CAAC,GAAC,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,oBAAoB,CAAC;MACzE;IACD;EACD,CAAC,CAAC;AACH,CAAC;AAED,SAASK,OAAOA,CAACE,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAE;EACpC,IAAIC,KAAK,GAAGH,KAAK,CAACI,SAAS,CAACH,IAAI,CAAC;EACjC,IAAII,IAAI,GAAG,CAACJ,IAAI,EAAEC,KAAK,CAAC;EAExB,IAAIC,KAAK,GAAG,CAAC,EAAE;IACdH,KAAK,CAACM,QAAQ,CAACD,IAAI,CAAC;EACrB,CAAC,MAAM;IACNL,KAAK,CAACO,KAAK,CAACJ,KAAK,CAAC,GAAGE,IAAI;EAC1B;AACD;AAEA,SAASN,WAAWA,CAACP,MAAM,EAAEW,KAAK,EAAE;EACnC,IAAIK,WAAW,GAAGhB,MAAM,CAACW,KAAK,CAAC,CAACM,KAAK,GAAG,CAAC;EACzC,KAAK,IAAIhB,CAAC,GAAGU,KAAK,GAAG,CAAC,EAAEV,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACpC,IAAID,MAAM,CAACC,CAAC,CAAC,CAACgB,KAAK,KAAKD,WAAW,EAAE;MACpC,OAAOf,CAAC;IACT;EACD;EACA,OAAO,CAAC,CAAC;AACV;AAEA,SAASE,UAAUA,CAACH,MAAM,EAAEW,KAAK,EAAE;EAClC,OAAOO,QAAQ,CAAClB,MAAM,CAACW,KAAK,CAAC,CAAC,IACvBQ,WAAW,CAACnB,MAAM,CAACW,KAAK,GAAG,CAAC,CAAC,CAAC,IAC9BS,UAAU,CAACpB,MAAM,CAACW,KAAK,GAAG,CAAC,CAAC,CAAC,IAC7BU,sBAAsB,CAACrB,MAAM,CAACW,KAAK,CAAC,CAAC;AAC7C;AAEA,SAASP,OAAOA,CAACI,KAAK,EAAEc,gBAAgB,EAAE;EACzCd,KAAK,CAACe,QAAQ,CAACC,OAAO,CAACC,YAAY,CAACjB,KAAK,EAAEc,gBAAgB,CAAC,CAAC;EAC7Dd,KAAK,CAACe,QAAQ,CAAC,CAAC,CAAC,CAACG,OAAO,GAAGlB,KAAK,CAACe,QAAQ,CAAC,CAAC,CAAC,CAACG,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC;EAC9DnB,KAAK,CAACkB,OAAO,GAAGlB,KAAK,CAACkB,OAAO,CAACC,KAAK,CAAC,CAAC,CAAC;EAEtC,IAAIxC,eAAe,EAAE;IACpB,IAAIC,aAAa,EAAE;MAClBoB,KAAK,CAACe,QAAQ,CAACK,GAAG,CAAC,CAAC;;MAEpB;MACA,IAAIC,EAAE,GAAG,YAAY,GAAGC,IAAI,CAACC,IAAI,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC;MACxExB,KAAK,CAACe,QAAQ,CAAC,CAAC,CAAC,CAACG,OAAO,GAAGlB,KAAK,CAACe,QAAQ,CAAC,CAAC,CAAC,CAACG,OAAO,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,OAAO,GAAGE,EAAE,GAAG,IAAI;MACxFrB,KAAK,CAACe,QAAQ,CAACU,IAAI,CAACC,UAAU,CAAC1B,KAAK,CAACkB,OAAO,EAAEG,EAAE,EAAEP,gBAAgB,CAAC,CAAC;IACrE,CAAC,MAAM;MACNd,KAAK,CAACe,QAAQ,CAACC,OAAO,CAACW,UAAU,CAACb,gBAAgB,CAAC,CAAC;MACpDd,KAAK,CAACe,QAAQ,CAACU,IAAI,CAACG,QAAQ,CAACd,gBAAgB,CAAC,CAAC;IAChD;EACD;AACD;AAEA,SAASG,YAAYA,CAACjB,KAAK,EAAEc,gBAAgB,EAAE;EAC9C,IAAIe,QAAQ,GAAG,IAAIf,gBAAgB,CAAC,aAAa,EAAE,EAAE,EAAE,CAAC,CAAC;EACzD,IAAIgB,YAAY,GAAGpD,iBAAiB,GAAG,eAAe,GAAG,EAAE;EAC3D,IAAIsB,KAAK,CAACkB,OAAO,CAACa,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;IACxCF,QAAQ,CAACX,OAAO,GAAG,wCAAwC,GAAGY,YAAY,GAAG,kBAAkB;EAChG,CAAC,MAAM,IAAI9B,KAAK,CAACkB,OAAO,CAACa,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI/B,KAAK,CAACkB,OAAO,CAACa,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;IACtFF,QAAQ,CAACX,OAAO,GAAG,mDAAmD,GAAGY,YAAY,GAAG,kBAAkB;EAC3G;EACA,OAAOD,QAAQ;AAChB;;AAEA;AACA;AACA,SAASF,UAAUA,CAACb,gBAAgB,EAAE;EACrC,IAAId,KAAK,GAAG,IAAIc,gBAAgB,CAAC,aAAa,EAAE,EAAE,EAAE,CAAC,CAAC;EACtDd,KAAK,CAACkB,OAAO,GAAG,SAAS;EACzB,OAAOlB,KAAK;AACb;AAEA,SAAS4B,QAAQA,CAACd,gBAAgB,EAAE;EACnC,IAAId,KAAK,GAAG,IAAIc,gBAAgB,CAAC,aAAa,EAAE,EAAE,EAAE,CAAC,CAAC;EACtDd,KAAK,CAACkB,OAAO,GAAG,UAAU;EAC1B,OAAOlB,KAAK;AACb;AAEA,SAAS0B,UAAUA,CAACR,OAAO,EAAEG,EAAE,EAAEP,gBAAgB,EAAE;EAClD,IAAId,KAAK,GAAG,IAAIc,gBAAgB,CAAC,aAAa,EAAE,EAAE,EAAE,CAAC,CAAC;EACtDd,KAAK,CAACkB,OAAO,GAAG,2CAA2C,GAAGG,EAAE,GAAG,IAAI,GAAGH,OAAO,GAAG,UAAU;EAC9FlB,KAAK,CAACO,KAAK,GAAG,CAAC;IAACyB,GAAG,EAAEX;EAAE,CAAC,CAAC;EACzB,OAAOrB,KAAK;AACb;AAEA,SAASU,QAAQA,CAACV,KAAK,EAAE;EAAE,OAAOA,KAAK,CAACiC,IAAI,KAAK,QAAQ;AAAE;AAC3D,SAAStB,WAAWA,CAACX,KAAK,EAAE;EAAE,OAAOA,KAAK,CAACiC,IAAI,KAAK,gBAAgB;AAAE;AACtE,SAASrB,UAAUA,CAACZ,KAAK,EAAE;EAAE,OAAOA,KAAK,CAACiC,IAAI,KAAK,gBAAgB;AAAE;AAErE,SAASpB,sBAAsBA,CAACb,KAAK,EAAE;EACtC;EACA,OAAOA,KAAK,CAACkB,OAAO,CAACa,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI/B,KAAK,CAACkB,OAAO,CAACa,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI/B,KAAK,CAACkB,OAAO,CAACa,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC;AACzH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}