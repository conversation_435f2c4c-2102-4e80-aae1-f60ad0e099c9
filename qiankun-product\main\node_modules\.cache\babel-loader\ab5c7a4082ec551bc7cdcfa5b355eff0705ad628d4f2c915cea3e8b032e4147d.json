{"ast": null, "code": "// Process html tags\n\n'use strict';\n\nvar HTML_TAG_RE = require('../common/html_re').HTML_TAG_RE;\nfunction isLetter(ch) {\n  /*eslint no-bitwise:0*/\n  var lc = ch | 0x20; // to lower case\n  return lc >= 0x61 /* a */ && lc <= 0x7a /* z */;\n}\nmodule.exports = function html_inline(state, silent) {\n  var ch,\n    match,\n    max,\n    token,\n    pos = state.pos;\n  if (!state.md.options.html) {\n    return false;\n  }\n\n  // Check start\n  max = state.posMax;\n  if (state.src.charCodeAt(pos) !== 0x3C /* < */ || pos + 2 >= max) {\n    return false;\n  }\n\n  // Quick fail on second char\n  ch = state.src.charCodeAt(pos + 1);\n  if (ch !== 0x21 /* ! */ && ch !== 0x3F /* ? */ && ch !== 0x2F /* / */ && !isLetter(ch)) {\n    return false;\n  }\n  match = state.src.slice(pos).match(HTML_TAG_RE);\n  if (!match) {\n    return false;\n  }\n  if (!silent) {\n    token = state.push('html_inline', '', 0);\n    token.content = state.src.slice(pos, pos + match[0].length);\n  }\n  state.pos += match[0].length;\n  return true;\n};", "map": {"version": 3, "names": ["HTML_TAG_RE", "require", "isLetter", "ch", "lc", "module", "exports", "html_inline", "state", "silent", "match", "max", "token", "pos", "md", "options", "html", "posMax", "src", "charCodeAt", "slice", "push", "content", "length"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_inline/html_inline.js"], "sourcesContent": ["// Process html tags\n\n'use strict';\n\n\nvar HTML_TAG_RE = require('../common/html_re').HTML_TAG_RE;\n\n\nfunction isLetter(ch) {\n  /*eslint no-bitwise:0*/\n  var lc = ch | 0x20; // to lower case\n  return (lc >= 0x61/* a */) && (lc <= 0x7a/* z */);\n}\n\n\nmodule.exports = function html_inline(state, silent) {\n  var ch, match, max, token,\n      pos = state.pos;\n\n  if (!state.md.options.html) { return false; }\n\n  // Check start\n  max = state.posMax;\n  if (state.src.charCodeAt(pos) !== 0x3C/* < */ ||\n      pos + 2 >= max) {\n    return false;\n  }\n\n  // Quick fail on second char\n  ch = state.src.charCodeAt(pos + 1);\n  if (ch !== 0x21/* ! */ &&\n      ch !== 0x3F/* ? */ &&\n      ch !== 0x2F/* / */ &&\n      !isLetter(ch)) {\n    return false;\n  }\n\n  match = state.src.slice(pos).match(HTML_TAG_RE);\n  if (!match) { return false; }\n\n  if (!silent) {\n    token         = state.push('html_inline', '', 0);\n    token.content = state.src.slice(pos, pos + match[0].length);\n  }\n  state.pos += match[0].length;\n  return true;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAGZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,mBAAmB,CAAC,CAACD,WAAW;AAG1D,SAASE,QAAQA,CAACC,EAAE,EAAE;EACpB;EACA,IAAIC,EAAE,GAAGD,EAAE,GAAG,IAAI,CAAC,CAAC;EACpB,OAAQC,EAAE,IAAI,IAAI,YAAaA,EAAE,IAAI,IAAI,QAAQ;AACnD;AAGAC,MAAM,CAACC,OAAO,GAAG,SAASC,WAAWA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACnD,IAAIN,EAAE;IAAEO,KAAK;IAAEC,GAAG;IAAEC,KAAK;IACrBC,GAAG,GAAGL,KAAK,CAACK,GAAG;EAEnB,IAAI,CAACL,KAAK,CAACM,EAAE,CAACC,OAAO,CAACC,IAAI,EAAE;IAAE,OAAO,KAAK;EAAE;;EAE5C;EACAL,GAAG,GAAGH,KAAK,CAACS,MAAM;EAClB,IAAIT,KAAK,CAACU,GAAG,CAACC,UAAU,CAACN,GAAG,CAAC,KAAK,IAAI,YAClCA,GAAG,GAAG,CAAC,IAAIF,GAAG,EAAE;IAClB,OAAO,KAAK;EACd;;EAEA;EACAR,EAAE,GAAGK,KAAK,CAACU,GAAG,CAACC,UAAU,CAACN,GAAG,GAAG,CAAC,CAAC;EAClC,IAAIV,EAAE,KAAK,IAAI,YACXA,EAAE,KAAK,IAAI,YACXA,EAAE,KAAK,IAAI,YACX,CAACD,QAAQ,CAACC,EAAE,CAAC,EAAE;IACjB,OAAO,KAAK;EACd;EAEAO,KAAK,GAAGF,KAAK,CAACU,GAAG,CAACE,KAAK,CAACP,GAAG,CAAC,CAACH,KAAK,CAACV,WAAW,CAAC;EAC/C,IAAI,CAACU,KAAK,EAAE;IAAE,OAAO,KAAK;EAAE;EAE5B,IAAI,CAACD,MAAM,EAAE;IACXG,KAAK,GAAWJ,KAAK,CAACa,IAAI,CAAC,aAAa,EAAE,EAAE,EAAE,CAAC,CAAC;IAChDT,KAAK,CAACU,OAAO,GAAGd,KAAK,CAACU,GAAG,CAACE,KAAK,CAACP,GAAG,EAAEA,GAAG,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACa,MAAM,CAAC;EAC7D;EACAf,KAAK,CAACK,GAAG,IAAIH,KAAK,CAAC,CAAC,CAAC,CAACa,MAAM;EAC5B,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}