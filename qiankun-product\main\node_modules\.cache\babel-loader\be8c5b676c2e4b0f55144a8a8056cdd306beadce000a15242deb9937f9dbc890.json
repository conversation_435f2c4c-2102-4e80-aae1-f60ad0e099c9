{"ast": null, "code": "/**\n * JS Implementation of MurmurHash3 (r136) (as of May 20, 2011)\n *\n * Modifed to support non-ascii string by encoding to utf-8\n *\n * <AUTHOR> href=\"mailto:<EMAIL>\"><PERSON></a>\n * @see http://github.com/garycourt/murmurhash-js\n * <AUTHOR> href=\"mailto:<EMAIL>\">Austin Appleby</a>\n * @see http://sites.google.com/site/murmurhash/\n *\n * @param {string} str string to hash\n * @param {number} seed Positive integer only\n * @return {number} 32-bit positive integer hash\n */\n\nfunction murmurhash3_32_gc(str, seed) {\n  var key, remainder, bytes, h1, h1b, c1, c1b, c2, c2b, k1, i;\n  key = new TextEncoder().encode(str);\n  remainder = key.length & 3; // key.length % 4\n  bytes = key.length - remainder;\n  h1 = seed;\n  c1 = 0xcc9e2d51;\n  c2 = 0x1b873593;\n  i = 0;\n  while (i < bytes) {\n    k1 = key[i] & 0xff | (key[++i] & 0xff) << 8 | (key[++i] & 0xff) << 16 | (key[++i] & 0xff) << 24;\n    ++i;\n    k1 = (k1 & 0xffff) * c1 + (((k1 >>> 16) * c1 & 0xffff) << 16) & 0xffffffff;\n    k1 = k1 << 15 | k1 >>> 17;\n    k1 = (k1 & 0xffff) * c2 + (((k1 >>> 16) * c2 & 0xffff) << 16) & 0xffffffff;\n    h1 ^= k1;\n    h1 = h1 << 13 | h1 >>> 19;\n    h1b = (h1 & 0xffff) * 5 + (((h1 >>> 16) * 5 & 0xffff) << 16) & 0xffffffff;\n    h1 = (h1b & 0xffff) + 0x6b64 + (((h1b >>> 16) + 0xe654 & 0xffff) << 16);\n  }\n  k1 = 0;\n  switch (remainder) {\n    case 3:\n      k1 ^= (key[i + 2] & 0xff) << 16;\n    case 2:\n      k1 ^= (key[i + 1] & 0xff) << 8;\n    case 1:\n      k1 ^= key[i] & 0xff;\n      k1 = (k1 & 0xffff) * c1 + (((k1 >>> 16) * c1 & 0xffff) << 16) & 0xffffffff;\n      k1 = k1 << 15 | k1 >>> 17;\n      k1 = (k1 & 0xffff) * c2 + (((k1 >>> 16) * c2 & 0xffff) << 16) & 0xffffffff;\n      h1 ^= k1;\n  }\n  h1 ^= key.length;\n  h1 ^= h1 >>> 16;\n  h1 = (h1 & 0xffff) * 0x85ebca6b + (((h1 >>> 16) * 0x85ebca6b & 0xffff) << 16) & 0xffffffff;\n  h1 ^= h1 >>> 13;\n  h1 = (h1 & 0xffff) * 0xc2b2ae35 + (((h1 >>> 16) * 0xc2b2ae35 & 0xffff) << 16) & 0xffffffff;\n  h1 ^= h1 >>> 16;\n  return h1 >>> 0;\n}\nexport default murmurhash3_32_gc;", "map": {"version": 3, "names": ["murmurhash3_32_gc", "str", "seed", "key", "remainder", "bytes", "h1", "h1b", "c1", "c1b", "c2", "c2b", "k1", "i", "TextEncoder", "encode", "length"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/@datatraccorporation+markdown-it-mermaid@0.5.0/node_modules/@datatraccorporation/markdown-it-mermaid/src/murmurhash3_gc.js"], "sourcesContent": ["/**\n * JS Implementation of MurmurHash3 (r136) (as of May 20, 2011)\n *\n * Modifed to support non-ascii string by encoding to utf-8\n *\n * <AUTHOR> href=\"mailto:<EMAIL>\"><PERSON></a>\n * @see http://github.com/garycourt/murmurhash-js\n * <AUTHOR> href=\"mailto:<EMAIL>\">Austin Appleby</a>\n * @see http://sites.google.com/site/murmurhash/\n *\n * @param {string} str string to hash\n * @param {number} seed Positive integer only\n * @return {number} 32-bit positive integer hash\n */\n\nfunction murmurhash3_32_gc(str, seed) {\n\tvar key, remainder, bytes, h1, h1b, c1, c1b, c2, c2b, k1, i;\n\n\tkey = new TextEncoder().encode(str);\n\n\tremainder = key.length & 3; // key.length % 4\n\tbytes = key.length - remainder;\n\th1 = seed;\n\tc1 = 0xcc9e2d51;\n\tc2 = 0x1b873593;\n\ti = 0;\n\n\twhile (i < bytes) {\n\t\tk1 =\n\t\t  ((key[i] & 0xff)) |\n\t\t  ((key[++i] & 0xff) << 8) |\n\t\t  ((key[++i] & 0xff) << 16) |\n\t\t  ((key[++i] & 0xff) << 24);\n\t\t++i;\n\n\t\tk1 = ((((k1 & 0xffff) * c1) + ((((k1 >>> 16) * c1) & 0xffff) << 16))) & 0xffffffff;\n\t\tk1 = (k1 << 15) | (k1 >>> 17);\n\t\tk1 = ((((k1 & 0xffff) * c2) + ((((k1 >>> 16) * c2) & 0xffff) << 16))) & 0xffffffff;\n\n\t\th1 ^= k1;\n        h1 = (h1 << 13) | (h1 >>> 19);\n\t\th1b = ((((h1 & 0xffff) * 5) + ((((h1 >>> 16) * 5) & 0xffff) << 16))) & 0xffffffff;\n\t\th1 = (((h1b & 0xffff) + 0x6b64) + ((((h1b >>> 16) + 0xe654) & 0xffff) << 16));\n\t}\n\n\tk1 = 0;\n\n\tswitch (remainder) {\n\t\tcase 3: k1 ^= (key[i + 2] & 0xff) << 16;\n\t\tcase 2: k1 ^= (key[i + 1] & 0xff) << 8;\n\t\tcase 1: k1 ^= (key[i] & 0xff);\n\n\t\tk1 = (((k1 & 0xffff) * c1) + ((((k1 >>> 16) * c1) & 0xffff) << 16)) & 0xffffffff;\n\t\tk1 = (k1 << 15) | (k1 >>> 17);\n\t\tk1 = (((k1 & 0xffff) * c2) + ((((k1 >>> 16) * c2) & 0xffff) << 16)) & 0xffffffff;\n\t\th1 ^= k1;\n\t}\n\n\th1 ^= key.length;\n\n\th1 ^= h1 >>> 16;\n\th1 = (((h1 & 0xffff) * 0x85ebca6b) + ((((h1 >>> 16) * 0x85ebca6b) & 0xffff) << 16)) & 0xffffffff;\n\th1 ^= h1 >>> 13;\n\th1 = ((((h1 & 0xffff) * 0xc2b2ae35) + ((((h1 >>> 16) * 0xc2b2ae35) & 0xffff) << 16))) & 0xffffffff;\n\th1 ^= h1 >>> 16;\n\n\treturn h1 >>> 0;\n}\n\nexport default murmurhash3_32_gc;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,iBAAiBA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACrC,IAAIC,GAAG,EAAEC,SAAS,EAAEC,KAAK,EAAEC,EAAE,EAAEC,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,EAAE,EAAEC,CAAC;EAE3DV,GAAG,GAAG,IAAIW,WAAW,CAAC,CAAC,CAACC,MAAM,CAACd,GAAG,CAAC;EAEnCG,SAAS,GAAGD,GAAG,CAACa,MAAM,GAAG,CAAC,CAAC,CAAC;EAC5BX,KAAK,GAAGF,GAAG,CAACa,MAAM,GAAGZ,SAAS;EAC9BE,EAAE,GAAGJ,IAAI;EACTM,EAAE,GAAG,UAAU;EACfE,EAAE,GAAG,UAAU;EACfG,CAAC,GAAG,CAAC;EAEL,OAAOA,CAAC,GAAGR,KAAK,EAAE;IACjBO,EAAE,GACET,GAAG,CAACU,CAAC,CAAC,GAAG,IAAI,GACd,CAACV,GAAG,CAAC,EAAEU,CAAC,CAAC,GAAG,IAAI,KAAK,CAAE,GACvB,CAACV,GAAG,CAAC,EAAEU,CAAC,CAAC,GAAG,IAAI,KAAK,EAAG,GACxB,CAACV,GAAG,CAAC,EAAEU,CAAC,CAAC,GAAG,IAAI,KAAK,EAAG;IAC3B,EAAEA,CAAC;IAEHD,EAAE,GAAM,CAACA,EAAE,GAAG,MAAM,IAAIJ,EAAE,IAAK,CAAE,CAACI,EAAE,KAAK,EAAE,IAAIJ,EAAE,GAAI,MAAM,KAAK,EAAE,CAAC,GAAK,UAAU;IAClFI,EAAE,GAAIA,EAAE,IAAI,EAAE,GAAKA,EAAE,KAAK,EAAG;IAC7BA,EAAE,GAAM,CAACA,EAAE,GAAG,MAAM,IAAIF,EAAE,IAAK,CAAE,CAACE,EAAE,KAAK,EAAE,IAAIF,EAAE,GAAI,MAAM,KAAK,EAAE,CAAC,GAAK,UAAU;IAElFJ,EAAE,IAAIM,EAAE;IACFN,EAAE,GAAIA,EAAE,IAAI,EAAE,GAAKA,EAAE,KAAK,EAAG;IACnCC,GAAG,GAAM,CAACD,EAAE,GAAG,MAAM,IAAI,CAAC,IAAK,CAAE,CAACA,EAAE,KAAK,EAAE,IAAI,CAAC,GAAI,MAAM,KAAK,EAAE,CAAC,GAAK,UAAU;IACjFA,EAAE,GAAK,CAACC,GAAG,GAAG,MAAM,IAAI,MAAM,IAAK,CAAE,CAACA,GAAG,KAAK,EAAE,IAAI,MAAM,GAAI,MAAM,KAAK,EAAE,CAAE;EAC9E;EAEAK,EAAE,GAAG,CAAC;EAEN,QAAQR,SAAS;IAChB,KAAK,CAAC;MAAEQ,EAAE,IAAI,CAACT,GAAG,CAACU,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,EAAE;IACvC,KAAK,CAAC;MAAED,EAAE,IAAI,CAACT,GAAG,CAACU,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,KAAK,CAAC;IACtC,KAAK,CAAC;MAAED,EAAE,IAAKT,GAAG,CAACU,CAAC,CAAC,GAAG,IAAK;MAE7BD,EAAE,GAAK,CAACA,EAAE,GAAG,MAAM,IAAIJ,EAAE,IAAK,CAAE,CAACI,EAAE,KAAK,EAAE,IAAIJ,EAAE,GAAI,MAAM,KAAK,EAAE,CAAC,GAAI,UAAU;MAChFI,EAAE,GAAIA,EAAE,IAAI,EAAE,GAAKA,EAAE,KAAK,EAAG;MAC7BA,EAAE,GAAK,CAACA,EAAE,GAAG,MAAM,IAAIF,EAAE,IAAK,CAAE,CAACE,EAAE,KAAK,EAAE,IAAIF,EAAE,GAAI,MAAM,KAAK,EAAE,CAAC,GAAI,UAAU;MAChFJ,EAAE,IAAIM,EAAE;EACT;EAEAN,EAAE,IAAIH,GAAG,CAACa,MAAM;EAEhBV,EAAE,IAAIA,EAAE,KAAK,EAAE;EACfA,EAAE,GAAK,CAACA,EAAE,GAAG,MAAM,IAAI,UAAU,IAAK,CAAE,CAACA,EAAE,KAAK,EAAE,IAAI,UAAU,GAAI,MAAM,KAAK,EAAE,CAAC,GAAI,UAAU;EAChGA,EAAE,IAAIA,EAAE,KAAK,EAAE;EACfA,EAAE,GAAM,CAACA,EAAE,GAAG,MAAM,IAAI,UAAU,IAAK,CAAE,CAACA,EAAE,KAAK,EAAE,IAAI,UAAU,GAAI,MAAM,KAAK,EAAE,CAAC,GAAK,UAAU;EAClGA,EAAE,IAAIA,EAAE,KAAK,EAAE;EAEf,OAAOA,EAAE,KAAK,CAAC;AAChB;AAEA,eAAeN,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}