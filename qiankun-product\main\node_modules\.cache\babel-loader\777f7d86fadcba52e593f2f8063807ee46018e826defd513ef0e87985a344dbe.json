{"ast": null, "code": "import '../../utils/index.mjs';\nimport Badge from './src/badge2.mjs';\nexport { badgeProps } from './src/badge.mjs';\nimport { withInstall } from '../../utils/vue/install.mjs';\nvar ElBadge = withInstall(Badge);\nexport { ElBadge, ElBadge as default };", "map": {"version": 3, "names": ["ElBadge", "withInstall", "Badge"], "sources": ["../../../../../packages/components/badge/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\n\nimport Badge from './src/badge.vue'\n\nexport const ElBadge = withInstall(Badge)\nexport default ElBadge\n\nexport * from './src/badge'\nexport type { BadgeInstance } from './src/instance'\n"], "mappings": ";;;;AAEY,IAACA,OAAO,GAAGC,WAAW,CAACC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}