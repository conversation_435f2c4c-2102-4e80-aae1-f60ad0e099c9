{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, withCtx as _withCtx, vShow as _vShow, withDirectives as _withDirectives, createElementBlock as _createElementBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_popover = _resolveComponent(\"el-popover\");\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", {\n    class: \"GlobalFloatingWindow forbidSelect\",\n    ref: \"elContainer\",\n    onMousedown: $setup.handleMousedown,\n    onMouseup: $setup.handleMouseup\n  }, [_createVNode(_component_el_popover, {\n    visible: $setup.elShow,\n    disabled: $setup.disabled,\n    placement: \"left\",\n    \"popper-class\": \"GlobalFloatingWindowPopover\"\n  }, {\n    reference: _withCtx(function () {\n      return [_createElementVNode(\"div\", {\n        class: _normalizeClass(['GlobalFloatingWindowBody', $setup.isActiveType ? 'is-left ' : 'is-right', {\n          'is-active': $setup.isActive\n        }]),\n        onClick: $setup.handleClick\n      }, [_createVNode(_component_el_image, {\n        src: $setup.IntelligentAssistant,\n        loading: \"lazy\",\n        fit: \"cover\",\n        draggable: \"false\"\n      }, null, 8 /* PROPS */, [\"src\"])], 2 /* CLASS */)];\n    }),\n    default: _withCtx(function () {\n      return [!$setup.disabled ? (_openBlock(), _createBlock($setup[\"GlobalAiChat\"], {\n        key: 0,\n        modelValue: $setup.elShow,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.elShow = $event;\n        })\n      }, null, 8 /* PROPS */, [\"modelValue\"])) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"visible\", \"disabled\"])], 544 /* NEED_HYDRATION, NEED_PATCH */)), [[_vShow, $setup.AiChatElShow && (!$setup.disabled || !$setup.elShow)]]);\n}", "map": {"version": 3, "names": ["_createElementBlock", "class", "ref", "onMousedown", "$setup", "handleMousedown", "onMouseup", "handleMouseup", "_createVNode", "_component_el_popover", "visible", "elShow", "disabled", "placement", "reference", "_withCtx", "_createElementVNode", "_normalizeClass", "isActiveType", "isActive", "onClick", "handleClick", "_component_el_image", "src", "IntelligentAssistant", "loading", "fit", "draggable", "default", "_createBlock", "key", "modelValue", "_cache", "$event", "_createCommentVNode", "_", "AiChatElShow"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LayoutContainer\\components\\GlobalFloatingWindow.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalFloatingWindow forbidSelect\" ref=\"elContainer\" @mousedown=\"handleMousedown\" @mouseup=\"handleMouseup\"\r\n    v-show=\"AiChatElShow && (!disabled || !elShow)\">\r\n    <el-popover :visible=\"elShow\" :disabled=\"disabled\" placement=\"left\" popper-class=\"GlobalFloatingWindowPopover\">\r\n      <template #reference>\r\n        <div :class=\"['GlobalFloatingWindowBody', isActiveType ? 'is-left ' : 'is-right', { 'is-active': isActive }]\"\r\n          @click=\"handleClick\">\r\n          <el-image :src=\"IntelligentAssistant\" loading=\"lazy\" fit=\"cover\" draggable=\"false\" />\r\n        </div>\r\n      </template>\r\n      <GlobalAiChat v-model=\"elShow\" v-if=\"!disabled\"></GlobalAiChat>\r\n    </el-popover>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalFloatingWindow' }\r\n</script>\r\n<script setup>\r\nimport { ref, computed, onMounted, onUnmounted } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { IntelligentAssistant } from 'common/js/system_var.js'\r\nimport GlobalAiChat from '../../GlobalAiChat/GlobalAiChat'\r\nconst store = useStore()\r\nconst props = defineProps({\r\n  modelValue: { type: Boolean, default: false },\r\n  disabled: { type: Boolean, default: false }\r\n})\r\nconst emit = defineEmits(['update:modelValue'])\r\nconst AiChatElShow = computed(() => store.state.AiChatElShow)\r\nconst elShow = computed({\r\n  get () { return props.modelValue },\r\n  set (value) { emit('update:modelValue', value) }\r\n})\r\nconst disabled = computed(() => props.disabled)\r\nconst elContainer = ref()\r\nconst isDragging = ref(false)\r\nconst positionX = ref(window.innerWidth - 100)\r\nconst positionY = ref(window.innerHeight - 268)\r\nconst startPosX = ref(0)\r\nconst startPosY = ref(0)\r\n// const elShow = ref(false)\r\nconst isActive = ref(false)\r\nconst isActiveType = ref(false)\r\nconst handleClick = () => {\r\n  if (!isDragging.value && !isActive.value) elShow.value = !elShow.value\r\n  isActive.value = false\r\n}\r\nconst handleMousedown = (event) => {\r\n  isDragging.value = true\r\n  startPosX.value = event.clientX - positionX.value\r\n  startPosY.value = event.clientY - positionY.value\r\n  document.addEventListener('mousemove', handleMousemove)\r\n  document.addEventListener('mouseup', handleMouseup)\r\n}\r\nconst handleMouseup = () => {\r\n  if (isDragging.value) {\r\n    isDragging.value = false\r\n    document.removeEventListener('mousemove', handleMousemove)\r\n    document.removeEventListener('mouseup', handleMouseup)\r\n    handleAutoSnap()\r\n  }\r\n}\r\nconst handleMousemove = (event) => {\r\n  if (isDragging.value) {\r\n    elShow.value = false\r\n    isActive.value = true\r\n    const newX = event.clientX - startPosX.value\r\n    const newY = event.clientY - startPosY.value\r\n    const windowWidth = window.innerWidth\r\n    const windowHeight = window.innerHeight\r\n    const elWidth = elContainer.value.offsetWidth\r\n    const elHeight = elContainer.value.offsetHeight\r\n    positionX.value = Math.max(0, Math.min(windowWidth - elWidth, newX))\r\n    positionY.value = Math.max(0, Math.min(windowHeight - elHeight, newY))\r\n    elContainer.value.style.top = positionY.value + 'px'\r\n    elContainer.value.style.left = positionX.value + 'px'\r\n  }\r\n}\r\n// 自动吸附到最近的侧边\r\nconst handleAutoSnap = () => {\r\n  const windowWidth = window.innerWidth\r\n  const elWidth = elContainer.value.offsetWidth\r\n  if (positionX.value + elWidth / 2 < windowWidth / 2) {\r\n    positionX.value = 0\r\n    isActiveType.value = true\r\n  } else {\r\n    isActiveType.value = false\r\n    positionX.value = windowWidth - elWidth\r\n  }\r\n  elContainer.value.style.top = positionY.value + 'px'\r\n  elContainer.value.style.left = positionX.value + 'px'\r\n}\r\nonMounted(() => {\r\n  positionX.value = window.innerWidth - elContainer.value.offsetWidth\r\n  elContainer.value.style.top = positionY.value + 'px'\r\n  elContainer.value.style.left = positionX.value + 'px'\r\n  window.addEventListener('resize', handleAutoSnap)\r\n})\r\nonUnmounted(() => {\r\n  window.removeEventListener('resize', handleAutoSnap)\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.GlobalFloatingWindow {\r\n  width: 66px;\r\n  height: 52px;\r\n  position: fixed;\r\n  cursor: pointer;\r\n  z-index: 99;\r\n\r\n  .GlobalFloatingWindowBody {\r\n    width: 66px;\r\n    height: 52px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background: #fff;\r\n    box-shadow: var(--zy-el-box-shadow-light);\r\n    overflow: hidden;\r\n\r\n    &.is-left {\r\n      padding-right: 6px;\r\n      border-radius: 0 26px 26px 0;\r\n    }\r\n\r\n    &.is-right {\r\n      padding-left: 6px;\r\n      border-radius: 26px 0 0 26px;\r\n    }\r\n\r\n    &.is-active {\r\n      padding: 0;\r\n      border-radius: 28px;\r\n    }\r\n\r\n    .zy-el-image {\r\n      width: 44px;\r\n      height: 44px;\r\n    }\r\n  }\r\n}\r\n\r\n.GlobalFloatingWindowPopover {\r\n  width: 580px !important;\r\n  height: 80% !important;\r\n  padding: 0 !important;\r\n}\r\n</style>"], "mappings": ";;;;wCACEA,mBAAA,CAWM;IAXDC,KAAK,EAAC,mCAAmC;IAACC,GAAG,EAAC,aAAa;IAAEC,WAAS,EAAEC,MAAA,CAAAC,eAAe;IAAGC,SAAO,EAAEF,MAAA,CAAAG;MAEtGC,YAAA,CAQaC,qBAAA;IARAC,OAAO,EAAEN,MAAA,CAAAO,MAAM;IAAGC,QAAQ,EAAER,MAAA,CAAAQ,QAAQ;IAAEC,SAAS,EAAC,MAAM;IAAC,cAAY,EAAC;;IACpEC,SAAS,EAAAC,QAAA,CAClB;MAAA,OAGM,CAHNC,mBAAA,CAGM;QAHAf,KAAK,EALnBgB,eAAA,8BAKkDb,MAAA,CAAAc,YAAY;UAAA,aAA2Cd,MAAA,CAAAe;QAAQ;QACtGC,OAAK,EAAEhB,MAAA,CAAAiB;UACRb,YAAA,CAAqFc,mBAAA;QAA1EC,GAAG,EAAEnB,MAAA,CAAAoB,oBAAoB;QAAEC,OAAO,EAAC,MAAM;QAACC,GAAG,EAAC,OAAO;QAACC,SAAS,EAAC;;;IAPrFC,OAAA,EAAAb,QAAA,CAWiF;MAAA,OAC/B,C,CAFNX,MAAA,CAAAQ,QAAQ,I,cAA9CiB,YAAA,CAA+DzB,MAAA;QAVrE0B,GAAA;QAAAC,UAAA,EAU6B3B,MAAA,CAAAO,MAAM;QAVnC,uBAAAqB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAU6B7B,MAAA,CAAAO,MAAM,GAAAsB,MAAA;QAAA;iDAVnCC,mBAAA,e;;IAAAC,CAAA;iGAEY/B,MAAA,CAAAgC,YAAY,MAAMhC,MAAA,CAAAQ,QAAQ,KAAKR,MAAA,CAAAO,MAAM,G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}