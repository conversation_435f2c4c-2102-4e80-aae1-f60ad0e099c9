{"ast": null, "code": "'use strict';\n\nvar emoji_html = require('./lib/render');\nvar emoji_replace = require('./lib/replace');\nvar normalize_opts = require('./lib/normalize_opts');\nmodule.exports = function emoji_plugin(md, options) {\n  var defaults = {\n    defs: {},\n    shortcuts: {},\n    enabled: []\n  };\n  var opts = normalize_opts(md.utils.assign({}, defaults, options || {}));\n  md.renderer.rules.emoji = emoji_html;\n  md.core.ruler.after('linkify', 'emoji', emoji_replace(md, opts.defs, opts.shortcuts, opts.scanRE, opts.replaceRE));\n};", "map": {"version": 3, "names": ["emoji_html", "require", "emoji_replace", "normalize_opts", "module", "exports", "emoji_plugin", "md", "options", "defaults", "defs", "shortcuts", "enabled", "opts", "utils", "assign", "renderer", "rules", "emoji", "core", "ruler", "after", "scanRE", "replaceRE"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it-emoji@2.0.2/node_modules/markdown-it-emoji/bare.js"], "sourcesContent": ["'use strict';\n\n\nvar emoji_html        = require('./lib/render');\nvar emoji_replace     = require('./lib/replace');\nvar normalize_opts    = require('./lib/normalize_opts');\n\n\nmodule.exports = function emoji_plugin(md, options) {\n  var defaults = {\n    defs: {},\n    shortcuts: {},\n    enabled: []\n  };\n\n  var opts = normalize_opts(md.utils.assign({}, defaults, options || {}));\n\n  md.renderer.rules.emoji = emoji_html;\n\n  md.core.ruler.after(\n    'linkify',\n    'emoji',\n    emoji_replace(md, opts.defs, opts.shortcuts, opts.scanRE, opts.replaceRE)\n  );\n};\n"], "mappings": "AAAA,YAAY;;AAGZ,IAAIA,UAAU,GAAUC,OAAO,CAAC,cAAc,CAAC;AAC/C,IAAIC,aAAa,GAAOD,OAAO,CAAC,eAAe,CAAC;AAChD,IAAIE,cAAc,GAAMF,OAAO,CAAC,sBAAsB,CAAC;AAGvDG,MAAM,CAACC,OAAO,GAAG,SAASC,YAAYA,CAACC,EAAE,EAAEC,OAAO,EAAE;EAClD,IAAIC,QAAQ,GAAG;IACbC,IAAI,EAAE,CAAC,CAAC;IACRC,SAAS,EAAE,CAAC,CAAC;IACbC,OAAO,EAAE;EACX,CAAC;EAED,IAAIC,IAAI,GAAGV,cAAc,CAACI,EAAE,CAACO,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,QAAQ,EAAED,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC;EAEvED,EAAE,CAACS,QAAQ,CAACC,KAAK,CAACC,KAAK,GAAGlB,UAAU;EAEpCO,EAAE,CAACY,IAAI,CAACC,KAAK,CAACC,KAAK,CACjB,SAAS,EACT,OAAO,EACPnB,aAAa,CAACK,EAAE,EAAEM,IAAI,CAACH,IAAI,EAAEG,IAAI,CAACF,SAAS,EAAEE,IAAI,CAACS,MAAM,EAAET,IAAI,CAACU,SAAS,CAC1E,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}