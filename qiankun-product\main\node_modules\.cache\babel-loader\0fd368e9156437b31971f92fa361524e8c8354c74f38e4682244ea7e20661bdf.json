{"ast": null, "code": "function _assertThisInitialized(e) {\n  if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  return e;\n}\nexport { _assertThisInitialized as default };", "map": {"version": 3, "names": ["_assertThisInitialized", "e", "ReferenceError", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/@babel+runtime@7.26.0/node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js"], "sourcesContent": ["function _assertThisInitialized(e) {\n  if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  return e;\n}\nexport { _assertThisInitialized as default };"], "mappings": "AAAA,SAASA,sBAAsBA,CAACC,CAAC,EAAE;EACjC,IAAI,KAAK,CAAC,KAAKA,CAAC,EAAE,MAAM,IAAIC,cAAc,CAAC,2DAA2D,CAAC;EACvG,OAAOD,CAAC;AACV;AACA,SAASD,sBAAsB,IAAIG,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}