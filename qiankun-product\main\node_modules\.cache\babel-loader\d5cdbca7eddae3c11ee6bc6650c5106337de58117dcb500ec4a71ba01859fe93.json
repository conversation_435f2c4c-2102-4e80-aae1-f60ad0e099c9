{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, resolveDirective as _resolveDirective, openBlock as _openBlock, createElementBlock as _createElementBlock, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = [\"lement-loading-text\"];\nvar _hoisted_2 = {\n  class: \"VersionComparisonFileInfoHead\"\n};\nvar _hoisted_3 = {\n  class: \"VersionComparisonFileInfoHeadText\"\n};\nvar _hoisted_4 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", {\n    class: \"VersionComparisonAiFileInfo\",\n    \"lement-loading-text\": $setup.loadingText\n  }, [_createElementVNode(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, _toDisplayString($setup.props.name), 1 /* TEXT */)]), _createVNode(_component_el_scrollbar, {\n    class: \"VersionComparisonFileInfoScrollbar\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", {\n        class: \"VersionComparisonFileInfoWord\",\n        innerHTML: $setup.props.comparisonHtml\n      }, null, 8 /* PROPS */, _hoisted_4)];\n    }),\n    _: 1 /* STABLE */\n  })], 8 /* PROPS */, _hoisted_1)), [[_directive_loading, $setup.loading]]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "$setup", "loadingText", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_toDisplayString", "props", "name", "_createVNode", "_component_el_scrollbar", "default", "_withCtx", "innerHTML", "comparisonHtml", "_hoisted_4", "_", "_hoisted_1", "loading"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\Intelligentize\\VersionComparison\\VersionComparisonAiFileInfo.vue"], "sourcesContent": ["<template>\r\n  <div class=\"VersionComparisonAiFileInfo\"\r\n       v-loading=\"loading\"\r\n       :lement-loading-text=\"loadingText\">\r\n    <div class=\"VersionComparisonFileInfoHead\">\r\n      <div class=\"VersionComparisonFileInfoHeadText\">{{ props.name }}</div>\r\n    </div>\r\n    <el-scrollbar class=\"VersionComparisonFileInfoScrollbar\">\r\n      <div class=\"VersionComparisonFileInfoWord\"\r\n           v-html=\"props.comparisonHtml\"></div>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'VersionComparisonAiFileInfo' }\r\n</script>\r\n<script setup>\r\nimport { ref} from 'vue'\r\nconst props = defineProps({ id: { type: String, default: '' }, name: { type: String, default: '' }, comparisonHtml: { type: String, default: '' } })\r\nconst emit = defineEmits(['loadCallback'])\r\nconst loading = ref(false)\r\nconst loadingText = ref('')\r\n</script>\r\n<style lang=\"scss\">\r\n.VersionComparisonAiFileInfo {\r\n  width: 100%;\r\n  // height: calc(50% - 10px);\r\n  // margin-bottom: 20px;\r\n  border: 1px solid var(--zy-el-border-color-lighter);\r\n  border-radius: 8px;\r\n  box-shadow: var(--zy-el-box-shadow);\r\n\r\n  .VersionComparisonFileInfoHead {\r\n    width: 100%;\r\n    height: 50px;\r\n    color: #fff;\r\n    background-image: linear-gradient(72deg, var(--zy-el-color-primary), var(--zy-el-color-primary-light-9));\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n    padding: 4px 20px;\r\n    border-top-left-radius: 8px;\r\n    border-top-right-radius: 8px;\r\n\r\n\r\n    .VersionComparisonFileInfoHeadText {\r\n      font-weight: bold;\r\n      font-size: var(--zy-name-font-size);\r\n    }\r\n  }\r\n\r\n  .VersionComparisonFileInfoScrollbar {\r\n    width: 100%;\r\n    height: calc(100% - 60px);\r\n\r\n    .VersionComparisonFileInfoWord {\r\n      padding: 0 20px;\r\n      div p span {\r\n        font-size: 16pt !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";iBAAA;;EAISA,KAAK,EAAC;AAA+B;;EACnCA,KAAK,EAAC;AAAmC;iBALpD;;;;wCACEC,mBAAA,CAUM;IAVDD,KAAK,EAAC,6BAA6B;IAElC,qBAAmB,EAAEE,MAAA,CAAAC;MACzBC,mBAAA,CAEM,OAFNC,UAEM,GADJD,mBAAA,CAAqE,OAArEE,UAAqE,EAAAC,gBAAA,CAAnBL,MAAA,CAAAM,KAAK,CAACC,IAAI,iB,GAE9DC,YAAA,CAGeC,uBAAA;IAHDX,KAAK,EAAC;EAAoC;IAP5DY,OAAA,EAAAC,QAAA,CAQM;MAAA,OACyC,CADzCT,mBAAA,CACyC;QADpCJ,KAAK,EAAC,+BAA+B;QACrCc,SAA6B,EAArBZ,MAAA,CAAAM,KAAK,CAACO;8BATzBC,UAAA,E;;IAAAC,CAAA;sBAAAC,UAAA,K,qBAEkBhB,MAAA,CAAAiB,OAAO,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}