{"ast": null, "code": "import LazyWrapper from './_LazyWrapper.js';\nimport getData from './_getData.js';\nimport getFuncName from './_getFuncName.js';\nimport lodash from './wrapperLodash.js';\n\n/**\n * Checks if `func` has a lazy counterpart.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` has a lazy counterpart,\n *  else `false`.\n */\nfunction isLaziable(func) {\n  var funcName = getFuncName(func),\n    other = lodash[funcName];\n  if (typeof other != 'function' || !(funcName in LazyWrapper.prototype)) {\n    return false;\n  }\n  if (func === other) {\n    return true;\n  }\n  var data = getData(other);\n  return !!data && func === data[0];\n}\nexport default isLaziable;", "map": {"version": 3, "names": ["LazyWrapper", "getData", "getFuncName", "lodash", "isLaziable", "func", "funcName", "other", "prototype", "data"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_isLaziable.js"], "sourcesContent": ["import LazyWrapper from './_LazyWrapper.js';\nimport getData from './_getData.js';\nimport getFuncName from './_getFuncName.js';\nimport lodash from './wrapperLodash.js';\n\n/**\n * Checks if `func` has a lazy counterpart.\n *\n * @private\n * @param {Function} func The function to check.\n * @returns {boolean} Returns `true` if `func` has a lazy counterpart,\n *  else `false`.\n */\nfunction isLaziable(func) {\n  var funcName = getFuncName(func),\n      other = lodash[funcName];\n\n  if (typeof other != 'function' || !(funcName in LazyWrapper.prototype)) {\n    return false;\n  }\n  if (func === other) {\n    return true;\n  }\n  var data = getData(other);\n  return !!data && func === data[0];\n}\n\nexport default isLaziable;\n"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,OAAO,MAAM,eAAe;AACnC,OAAOC,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,MAAM,MAAM,oBAAoB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,IAAI,EAAE;EACxB,IAAIC,QAAQ,GAAGJ,WAAW,CAACG,IAAI,CAAC;IAC5BE,KAAK,GAAGJ,MAAM,CAACG,QAAQ,CAAC;EAE5B,IAAI,OAAOC,KAAK,IAAI,UAAU,IAAI,EAAED,QAAQ,IAAIN,WAAW,CAACQ,SAAS,CAAC,EAAE;IACtE,OAAO,KAAK;EACd;EACA,IAAIH,IAAI,KAAKE,KAAK,EAAE;IAClB,OAAO,IAAI;EACb;EACA,IAAIE,IAAI,GAAGR,OAAO,CAACM,KAAK,CAAC;EACzB,OAAO,CAAC,CAACE,IAAI,IAAIJ,IAAI,KAAKI,IAAI,CAAC,CAAC,CAAC;AACnC;AAEA,eAAeL,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}