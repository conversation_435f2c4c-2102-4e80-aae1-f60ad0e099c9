{"ast": null, "code": "'use strict';\n\n/**\n * Ponyfill for `Array.prototype.find` which is only available in ES6 runtimes.\n *\n * Works with anything that has a `length` property and index access properties, including NodeList.\n *\n * @template {unknown} T\n * @param {Array<T> | ({length:number, [number]: T})} list\n * @param {function (item: T, index: number, list:Array<T> | ({length:number, [number]: T})):boolean} predicate\n * @param {Partial<Pick<ArrayConstructor['prototype'], 'find'>>?} ac `Array.prototype` by default,\n * \t\t\t\tallows injecting a custom implementation in tests\n * @returns {T | undefined}\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/find\n * @see https://tc39.es/ecma262/multipage/indexed-collections.html#sec-array.prototype.find\n */\nfunction find(list, predicate, ac) {\n  if (ac === undefined) {\n    ac = Array.prototype;\n  }\n  if (list && typeof ac.find === 'function') {\n    return ac.find.call(list, predicate);\n  }\n  for (var i = 0; i < list.length; i++) {\n    if (Object.prototype.hasOwnProperty.call(list, i)) {\n      var item = list[i];\n      if (predicate.call(undefined, item, i, list)) {\n        return item;\n      }\n    }\n  }\n}\n\n/**\n * \"Shallow freezes\" an object to render it immutable.\n * Uses `Object.freeze` if available,\n * otherwise the immutability is only in the type.\n *\n * Is used to create \"enum like\" objects.\n *\n * @template T\n * @param {T} object the object to freeze\n * @param {Pick<ObjectConstructor, 'freeze'> = Object} oc `Object` by default,\n * \t\t\t\tallows to inject custom object constructor for tests\n * @returns {Readonly<T>}\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/freeze\n */\nfunction freeze(object, oc) {\n  if (oc === undefined) {\n    oc = Object;\n  }\n  return oc && typeof oc.freeze === 'function' ? oc.freeze(object) : object;\n}\n\n/**\n * Since we can not rely on `Object.assign` we provide a simplified version\n * that is sufficient for our needs.\n *\n * @param {Object} target\n * @param {Object | null | undefined} source\n *\n * @returns {Object} target\n * @throws TypeError if target is not an object\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\n * @see https://tc39.es/ecma262/multipage/fundamental-objects.html#sec-object.assign\n */\nfunction assign(target, source) {\n  if (target === null || typeof target !== 'object') {\n    throw new TypeError('target is not an object');\n  }\n  for (var key in source) {\n    if (Object.prototype.hasOwnProperty.call(source, key)) {\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\n\n/**\n * All mime types that are allowed as input to `DOMParser.parseFromString`\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser/parseFromString#Argument02 MDN\n * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#domparsersupportedtype WHATWG HTML Spec\n * @see DOMParser.prototype.parseFromString\n */\nvar MIME_TYPE = freeze({\n  /**\n   * `text/html`, the only mime type that triggers treating an XML document as HTML.\n   *\n   * @see DOMParser.SupportedType.isHTML\n   * @see https://www.iana.org/assignments/media-types/text/html IANA MimeType registration\n   * @see https://en.wikipedia.org/wiki/HTML Wikipedia\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser/parseFromString MDN\n   * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#dom-domparser-parsefromstring WHATWG HTML Spec\n   */\n  HTML: 'text/html',\n  /**\n   * Helper method to check a mime type if it indicates an HTML document\n   *\n   * @param {string} [value]\n   * @returns {boolean}\n   *\n   * @see https://www.iana.org/assignments/media-types/text/html IANA MimeType registration\n   * @see https://en.wikipedia.org/wiki/HTML Wikipedia\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser/parseFromString MDN\n   * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#dom-domparser-parsefromstring \t */\n  isHTML: function isHTML(value) {\n    return value === MIME_TYPE.HTML;\n  },\n  /**\n   * `application/xml`, the standard mime type for XML documents.\n   *\n   * @see https://www.iana.org/assignments/media-types/application/xml IANA MimeType registration\n   * @see https://tools.ietf.org/html/rfc7303#section-9.1 RFC 7303\n   * @see https://en.wikipedia.org/wiki/XML_and_MIME Wikipedia\n   */\n  XML_APPLICATION: 'application/xml',\n  /**\n   * `text/html`, an alias for `application/xml`.\n   *\n   * @see https://tools.ietf.org/html/rfc7303#section-9.2 RFC 7303\n   * @see https://www.iana.org/assignments/media-types/text/xml IANA MimeType registration\n   * @see https://en.wikipedia.org/wiki/XML_and_MIME Wikipedia\n   */\n  XML_TEXT: 'text/xml',\n  /**\n   * `application/xhtml+xml`, indicates an XML document that has the default HTML namespace,\n   * but is parsed as an XML document.\n   *\n   * @see https://www.iana.org/assignments/media-types/application/xhtml+xml IANA MimeType registration\n   * @see https://dom.spec.whatwg.org/#dom-domimplementation-createdocument WHATWG DOM Spec\n   * @see https://en.wikipedia.org/wiki/XHTML Wikipedia\n   */\n  XML_XHTML_APPLICATION: 'application/xhtml+xml',\n  /**\n   * `image/svg+xml`,\n   *\n   * @see https://www.iana.org/assignments/media-types/image/svg+xml IANA MimeType registration\n   * @see https://www.w3.org/TR/SVG11/ W3C SVG 1.1\n   * @see https://en.wikipedia.org/wiki/Scalable_Vector_Graphics Wikipedia\n   */\n  XML_SVG_IMAGE: 'image/svg+xml'\n});\n\n/**\n * Namespaces that are used in this code base.\n *\n * @see http://www.w3.org/TR/REC-xml-names\n */\nvar NAMESPACE = freeze({\n  /**\n   * The XHTML namespace.\n   *\n   * @see http://www.w3.org/1999/xhtml\n   */\n  HTML: 'http://www.w3.org/1999/xhtml',\n  /**\n   * Checks if `uri` equals `NAMESPACE.HTML`.\n   *\n   * @param {string} [uri]\n   *\n   * @see NAMESPACE.HTML\n   */\n  isHTML: function isHTML(uri) {\n    return uri === NAMESPACE.HTML;\n  },\n  /**\n   * The SVG namespace.\n   *\n   * @see http://www.w3.org/2000/svg\n   */\n  SVG: 'http://www.w3.org/2000/svg',\n  /**\n   * The `xml:` namespace.\n   *\n   * @see http://www.w3.org/XML/1998/namespace\n   */\n  XML: 'http://www.w3.org/XML/1998/namespace',\n  /**\n   * The `xmlns:` namespace\n   *\n   * @see https://www.w3.org/2000/xmlns/\n   */\n  XMLNS: 'http://www.w3.org/2000/xmlns/'\n});\nexports.assign = assign;\nexports.find = find;\nexports.freeze = freeze;\nexports.MIME_TYPE = MIME_TYPE;\nexports.NAMESPACE = NAMESPACE;", "map": {"version": 3, "names": ["find", "list", "predicate", "ac", "undefined", "Array", "prototype", "call", "i", "length", "Object", "hasOwnProperty", "item", "freeze", "object", "oc", "assign", "target", "source", "TypeError", "key", "MIME_TYPE", "HTML", "isHTML", "value", "XML_APPLICATION", "XML_TEXT", "XML_XHTML_APPLICATION", "XML_SVG_IMAGE", "NAMESPACE", "uri", "SVG", "XML", "XMLNS", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/@xmldom+xmldom@0.8.10/node_modules/@xmldom/xmldom/lib/conventions.js"], "sourcesContent": ["'use strict'\n\n/**\n * Ponyfill for `Array.prototype.find` which is only available in ES6 runtimes.\n *\n * Works with anything that has a `length` property and index access properties, including NodeList.\n *\n * @template {unknown} T\n * @param {Array<T> | ({length:number, [number]: T})} list\n * @param {function (item: T, index: number, list:Array<T> | ({length:number, [number]: T})):boolean} predicate\n * @param {Partial<Pick<ArrayConstructor['prototype'], 'find'>>?} ac `Array.prototype` by default,\n * \t\t\t\tallows injecting a custom implementation in tests\n * @returns {T | undefined}\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/find\n * @see https://tc39.es/ecma262/multipage/indexed-collections.html#sec-array.prototype.find\n */\nfunction find(list, predicate, ac) {\n\tif (ac === undefined) {\n\t\tac = Array.prototype;\n\t}\n\tif (list && typeof ac.find === 'function') {\n\t\treturn ac.find.call(list, predicate);\n\t}\n\tfor (var i = 0; i < list.length; i++) {\n\t\tif (Object.prototype.hasOwnProperty.call(list, i)) {\n\t\t\tvar item = list[i];\n\t\t\tif (predicate.call(undefined, item, i, list)) {\n\t\t\t\treturn item;\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * \"Shallow freezes\" an object to render it immutable.\n * Uses `Object.freeze` if available,\n * otherwise the immutability is only in the type.\n *\n * Is used to create \"enum like\" objects.\n *\n * @template T\n * @param {T} object the object to freeze\n * @param {Pick<ObjectConstructor, 'freeze'> = Object} oc `Object` by default,\n * \t\t\t\tallows to inject custom object constructor for tests\n * @returns {Readonly<T>}\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/freeze\n */\nfunction freeze(object, oc) {\n\tif (oc === undefined) {\n\t\toc = Object\n\t}\n\treturn oc && typeof oc.freeze === 'function' ? oc.freeze(object) : object\n}\n\n/**\n * Since we can not rely on `Object.assign` we provide a simplified version\n * that is sufficient for our needs.\n *\n * @param {Object} target\n * @param {Object | null | undefined} source\n *\n * @returns {Object} target\n * @throws TypeError if target is not an object\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/assign\n * @see https://tc39.es/ecma262/multipage/fundamental-objects.html#sec-object.assign\n */\nfunction assign(target, source) {\n\tif (target === null || typeof target !== 'object') {\n\t\tthrow new TypeError('target is not an object')\n\t}\n\tfor (var key in source) {\n\t\tif (Object.prototype.hasOwnProperty.call(source, key)) {\n\t\t\ttarget[key] = source[key]\n\t\t}\n\t}\n\treturn target\n}\n\n/**\n * All mime types that are allowed as input to `DOMParser.parseFromString`\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser/parseFromString#Argument02 MDN\n * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#domparsersupportedtype WHATWG HTML Spec\n * @see DOMParser.prototype.parseFromString\n */\nvar MIME_TYPE = freeze({\n\t/**\n\t * `text/html`, the only mime type that triggers treating an XML document as HTML.\n\t *\n\t * @see DOMParser.SupportedType.isHTML\n\t * @see https://www.iana.org/assignments/media-types/text/html IANA MimeType registration\n\t * @see https://en.wikipedia.org/wiki/HTML Wikipedia\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser/parseFromString MDN\n\t * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#dom-domparser-parsefromstring WHATWG HTML Spec\n\t */\n\tHTML: 'text/html',\n\n\t/**\n\t * Helper method to check a mime type if it indicates an HTML document\n\t *\n\t * @param {string} [value]\n\t * @returns {boolean}\n\t *\n\t * @see https://www.iana.org/assignments/media-types/text/html IANA MimeType registration\n\t * @see https://en.wikipedia.org/wiki/HTML Wikipedia\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser/parseFromString MDN\n\t * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#dom-domparser-parsefromstring \t */\n\tisHTML: function (value) {\n\t\treturn value === MIME_TYPE.HTML\n\t},\n\n\t/**\n\t * `application/xml`, the standard mime type for XML documents.\n\t *\n\t * @see https://www.iana.org/assignments/media-types/application/xml IANA MimeType registration\n\t * @see https://tools.ietf.org/html/rfc7303#section-9.1 RFC 7303\n\t * @see https://en.wikipedia.org/wiki/XML_and_MIME Wikipedia\n\t */\n\tXML_APPLICATION: 'application/xml',\n\n\t/**\n\t * `text/html`, an alias for `application/xml`.\n\t *\n\t * @see https://tools.ietf.org/html/rfc7303#section-9.2 RFC 7303\n\t * @see https://www.iana.org/assignments/media-types/text/xml IANA MimeType registration\n\t * @see https://en.wikipedia.org/wiki/XML_and_MIME Wikipedia\n\t */\n\tXML_TEXT: 'text/xml',\n\n\t/**\n\t * `application/xhtml+xml`, indicates an XML document that has the default HTML namespace,\n\t * but is parsed as an XML document.\n\t *\n\t * @see https://www.iana.org/assignments/media-types/application/xhtml+xml IANA MimeType registration\n\t * @see https://dom.spec.whatwg.org/#dom-domimplementation-createdocument WHATWG DOM Spec\n\t * @see https://en.wikipedia.org/wiki/XHTML Wikipedia\n\t */\n\tXML_XHTML_APPLICATION: 'application/xhtml+xml',\n\n\t/**\n\t * `image/svg+xml`,\n\t *\n\t * @see https://www.iana.org/assignments/media-types/image/svg+xml IANA MimeType registration\n\t * @see https://www.w3.org/TR/SVG11/ W3C SVG 1.1\n\t * @see https://en.wikipedia.org/wiki/Scalable_Vector_Graphics Wikipedia\n\t */\n\tXML_SVG_IMAGE: 'image/svg+xml',\n})\n\n/**\n * Namespaces that are used in this code base.\n *\n * @see http://www.w3.org/TR/REC-xml-names\n */\nvar NAMESPACE = freeze({\n\t/**\n\t * The XHTML namespace.\n\t *\n\t * @see http://www.w3.org/1999/xhtml\n\t */\n\tHTML: 'http://www.w3.org/1999/xhtml',\n\n\t/**\n\t * Checks if `uri` equals `NAMESPACE.HTML`.\n\t *\n\t * @param {string} [uri]\n\t *\n\t * @see NAMESPACE.HTML\n\t */\n\tisHTML: function (uri) {\n\t\treturn uri === NAMESPACE.HTML\n\t},\n\n\t/**\n\t * The SVG namespace.\n\t *\n\t * @see http://www.w3.org/2000/svg\n\t */\n\tSVG: 'http://www.w3.org/2000/svg',\n\n\t/**\n\t * The `xml:` namespace.\n\t *\n\t * @see http://www.w3.org/XML/1998/namespace\n\t */\n\tXML: 'http://www.w3.org/XML/1998/namespace',\n\n\t/**\n\t * The `xmlns:` namespace\n\t *\n\t * @see https://www.w3.org/2000/xmlns/\n\t */\n\tXMLNS: 'http://www.w3.org/2000/xmlns/',\n})\n\nexports.assign = assign;\nexports.find = find;\nexports.freeze = freeze;\nexports.MIME_TYPE = MIME_TYPE;\nexports.NAMESPACE = NAMESPACE;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,IAAIA,CAACC,IAAI,EAAEC,SAAS,EAAEC,EAAE,EAAE;EAClC,IAAIA,EAAE,KAAKC,SAAS,EAAE;IACrBD,EAAE,GAAGE,KAAK,CAACC,SAAS;EACrB;EACA,IAAIL,IAAI,IAAI,OAAOE,EAAE,CAACH,IAAI,KAAK,UAAU,EAAE;IAC1C,OAAOG,EAAE,CAACH,IAAI,CAACO,IAAI,CAACN,IAAI,EAAEC,SAAS,CAAC;EACrC;EACA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,IAAI,CAACQ,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC,IAAIE,MAAM,CAACJ,SAAS,CAACK,cAAc,CAACJ,IAAI,CAACN,IAAI,EAAEO,CAAC,CAAC,EAAE;MAClD,IAAII,IAAI,GAAGX,IAAI,CAACO,CAAC,CAAC;MAClB,IAAIN,SAAS,CAACK,IAAI,CAACH,SAAS,EAAEQ,IAAI,EAAEJ,CAAC,EAAEP,IAAI,CAAC,EAAE;QAC7C,OAAOW,IAAI;MACZ;IACD;EACD;AACD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,MAAM,EAAEC,EAAE,EAAE;EAC3B,IAAIA,EAAE,KAAKX,SAAS,EAAE;IACrBW,EAAE,GAAGL,MAAM;EACZ;EACA,OAAOK,EAAE,IAAI,OAAOA,EAAE,CAACF,MAAM,KAAK,UAAU,GAAGE,EAAE,CAACF,MAAM,CAACC,MAAM,CAAC,GAAGA,MAAM;AAC1E;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,MAAMA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAC/B,IAAID,MAAM,KAAK,IAAI,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAClD,MAAM,IAAIE,SAAS,CAAC,yBAAyB,CAAC;EAC/C;EACA,KAAK,IAAIC,GAAG,IAAIF,MAAM,EAAE;IACvB,IAAIR,MAAM,CAACJ,SAAS,CAACK,cAAc,CAACJ,IAAI,CAACW,MAAM,EAAEE,GAAG,CAAC,EAAE;MACtDH,MAAM,CAACG,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;IAC1B;EACD;EACA,OAAOH,MAAM;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAII,SAAS,GAAGR,MAAM,CAAC;EACtB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACCS,IAAI,EAAE,WAAW;EAEjB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACCC,MAAM,EAAE,SAARA,MAAMA,CAAYC,KAAK,EAAE;IACxB,OAAOA,KAAK,KAAKH,SAAS,CAACC,IAAI;EAChC,CAAC;EAED;AACD;AACA;AACA;AACA;AACA;AACA;EACCG,eAAe,EAAE,iBAAiB;EAElC;AACD;AACA;AACA;AACA;AACA;AACA;EACCC,QAAQ,EAAE,UAAU;EAEpB;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACCC,qBAAqB,EAAE,uBAAuB;EAE9C;AACD;AACA;AACA;AACA;AACA;AACA;EACCC,aAAa,EAAE;AAChB,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA,IAAIC,SAAS,GAAGhB,MAAM,CAAC;EACtB;AACD;AACA;AACA;AACA;EACCS,IAAI,EAAE,8BAA8B;EAEpC;AACD;AACA;AACA;AACA;AACA;AACA;EACCC,MAAM,EAAE,SAARA,MAAMA,CAAYO,GAAG,EAAE;IACtB,OAAOA,GAAG,KAAKD,SAAS,CAACP,IAAI;EAC9B,CAAC;EAED;AACD;AACA;AACA;AACA;EACCS,GAAG,EAAE,4BAA4B;EAEjC;AACD;AACA;AACA;AACA;EACCC,GAAG,EAAE,sCAAsC;EAE3C;AACD;AACA;AACA;AACA;EACCC,KAAK,EAAE;AACR,CAAC,CAAC;AAEFC,OAAO,CAAClB,MAAM,GAAGA,MAAM;AACvBkB,OAAO,CAAClC,IAAI,GAAGA,IAAI;AACnBkC,OAAO,CAACrB,MAAM,GAAGA,MAAM;AACvBqB,OAAO,CAACb,SAAS,GAAGA,SAAS;AAC7Ba,OAAO,CAACL,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}