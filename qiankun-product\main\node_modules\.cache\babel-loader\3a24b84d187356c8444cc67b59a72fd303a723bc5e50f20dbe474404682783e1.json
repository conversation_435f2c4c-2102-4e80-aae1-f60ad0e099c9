{"ast": null, "code": "// Emojies & shortcuts replacement logic.\n//\n// Note: In theory, it could be faster to parse :smile: in inline chain and\n// leave only shortcuts here. But, who care...\n//\n\n'use strict';\n\nmodule.exports = function create_rule(md, emojies, shortcuts, scanRE, replaceRE) {\n  var arrayReplaceAt = md.utils.arrayReplaceAt,\n    ucm = md.utils.lib.ucmicro,\n    ZPCc = new RegExp([ucm.Z.source, ucm.P.source, ucm.Cc.source].join('|'));\n  function splitTextToken(text, level, Token) {\n    var token,\n      last_pos = 0,\n      nodes = [];\n    text.replace(replaceRE, function (match, offset, src) {\n      var emoji_name;\n      // Validate emoji name\n      if (shortcuts.hasOwnProperty(match)) {\n        // replace shortcut with full name\n        emoji_name = shortcuts[match];\n\n        // Don't allow letters before any shortcut (as in no \":/\" in http://)\n        if (offset > 0 && !ZPCc.test(src[offset - 1])) {\n          return;\n        }\n\n        // Don't allow letters after any shortcut\n        if (offset + match.length < src.length && !ZPCc.test(src[offset + match.length])) {\n          return;\n        }\n      } else {\n        emoji_name = match.slice(1, -1);\n      }\n\n      // Add new tokens to pending list\n      if (offset > last_pos) {\n        token = new Token('text', '', 0);\n        token.content = text.slice(last_pos, offset);\n        nodes.push(token);\n      }\n      token = new Token('emoji', '', 0);\n      token.markup = emoji_name;\n      token.content = emojies[emoji_name];\n      nodes.push(token);\n      last_pos = offset + match.length;\n    });\n    if (last_pos < text.length) {\n      token = new Token('text', '', 0);\n      token.content = text.slice(last_pos);\n      nodes.push(token);\n    }\n    return nodes;\n  }\n  return function emoji_replace(state) {\n    var i,\n      j,\n      l,\n      tokens,\n      token,\n      blockTokens = state.tokens,\n      autolinkLevel = 0;\n    for (j = 0, l = blockTokens.length; j < l; j++) {\n      if (blockTokens[j].type !== 'inline') {\n        continue;\n      }\n      tokens = blockTokens[j].children;\n\n      // We scan from the end, to keep position when new tags added.\n      // Use reversed logic in links start/end match\n      for (i = tokens.length - 1; i >= 0; i--) {\n        token = tokens[i];\n        if (token.type === 'link_open' || token.type === 'link_close') {\n          if (token.info === 'auto') {\n            autolinkLevel -= token.nesting;\n          }\n        }\n        if (token.type === 'text' && autolinkLevel === 0 && scanRE.test(token.content)) {\n          // replace current node\n          blockTokens[j].children = tokens = arrayReplaceAt(tokens, i, splitTextToken(token.content, token.level, state.Token));\n        }\n      }\n    }\n  };\n};", "map": {"version": 3, "names": ["module", "exports", "create_rule", "md", "emojies", "shortcuts", "scanRE", "replaceRE", "arrayReplaceAt", "utils", "ucm", "lib", "ucmicro", "ZPCc", "RegExp", "Z", "source", "P", "Cc", "join", "splitTextToken", "text", "level", "Token", "token", "last_pos", "nodes", "replace", "match", "offset", "src", "emoji_name", "hasOwnProperty", "test", "length", "slice", "content", "push", "markup", "emoji_replace", "state", "i", "j", "l", "tokens", "blockTokens", "autolinkLevel", "type", "children", "info", "nesting"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it-emoji@2.0.2/node_modules/markdown-it-emoji/lib/replace.js"], "sourcesContent": ["// Emojies & shortcuts replacement logic.\n//\n// Note: In theory, it could be faster to parse :smile: in inline chain and\n// leave only shortcuts here. But, who care...\n//\n\n'use strict';\n\n\nmodule.exports = function create_rule(md, emojies, shortcuts, scanRE, replaceRE) {\n  var arrayReplaceAt = md.utils.arrayReplaceAt,\n      ucm = md.utils.lib.ucmicro,\n      ZPCc = new RegExp([ ucm.Z.source, ucm.P.source, ucm.Cc.source ].join('|'));\n\n  function splitTextToken(text, level, Token) {\n    var token, last_pos = 0, nodes = [];\n\n    text.replace(replaceRE, function (match, offset, src) {\n      var emoji_name;\n      // Validate emoji name\n      if (shortcuts.hasOwnProperty(match)) {\n        // replace shortcut with full name\n        emoji_name = shortcuts[match];\n\n        // Don't allow letters before any shortcut (as in no \":/\" in http://)\n        if (offset > 0 && !ZPCc.test(src[offset - 1])) {\n          return;\n        }\n\n        // Don't allow letters after any shortcut\n        if (offset + match.length < src.length && !ZPCc.test(src[offset + match.length])) {\n          return;\n        }\n      } else {\n        emoji_name = match.slice(1, -1);\n      }\n\n      // Add new tokens to pending list\n      if (offset > last_pos) {\n        token         = new Token('text', '', 0);\n        token.content = text.slice(last_pos, offset);\n        nodes.push(token);\n      }\n\n      token         = new Token('emoji', '', 0);\n      token.markup  = emoji_name;\n      token.content = emojies[emoji_name];\n      nodes.push(token);\n\n      last_pos = offset + match.length;\n    });\n\n    if (last_pos < text.length) {\n      token         = new Token('text', '', 0);\n      token.content = text.slice(last_pos);\n      nodes.push(token);\n    }\n\n    return nodes;\n  }\n\n  return function emoji_replace(state) {\n    var i, j, l, tokens, token,\n        blockTokens = state.tokens,\n        autolinkLevel = 0;\n\n    for (j = 0, l = blockTokens.length; j < l; j++) {\n      if (blockTokens[j].type !== 'inline') { continue; }\n      tokens = blockTokens[j].children;\n\n      // We scan from the end, to keep position when new tags added.\n      // Use reversed logic in links start/end match\n      for (i = tokens.length - 1; i >= 0; i--) {\n        token = tokens[i];\n\n        if (token.type === 'link_open' || token.type === 'link_close') {\n          if (token.info === 'auto') { autolinkLevel -= token.nesting; }\n        }\n\n        if (token.type === 'text' && autolinkLevel === 0 && scanRE.test(token.content)) {\n          // replace current node\n          blockTokens[j].children = tokens = arrayReplaceAt(\n            tokens, i, splitTextToken(token.content, token.level, state.Token)\n          );\n        }\n      }\n    }\n  };\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAGZA,MAAM,CAACC,OAAO,GAAG,SAASC,WAAWA,CAACC,EAAE,EAAEC,OAAO,EAAEC,SAAS,EAAEC,MAAM,EAAEC,SAAS,EAAE;EAC/E,IAAIC,cAAc,GAAGL,EAAE,CAACM,KAAK,CAACD,cAAc;IACxCE,GAAG,GAAGP,EAAE,CAACM,KAAK,CAACE,GAAG,CAACC,OAAO;IAC1BC,IAAI,GAAG,IAAIC,MAAM,CAAC,CAAEJ,GAAG,CAACK,CAAC,CAACC,MAAM,EAAEN,GAAG,CAACO,CAAC,CAACD,MAAM,EAAEN,GAAG,CAACQ,EAAE,CAACF,MAAM,CAAE,CAACG,IAAI,CAAC,GAAG,CAAC,CAAC;EAE9E,SAASC,cAAcA,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC1C,IAAIC,KAAK;MAAEC,QAAQ,GAAG,CAAC;MAAEC,KAAK,GAAG,EAAE;IAEnCL,IAAI,CAACM,OAAO,CAACpB,SAAS,EAAE,UAAUqB,KAAK,EAAEC,MAAM,EAAEC,GAAG,EAAE;MACpD,IAAIC,UAAU;MACd;MACA,IAAI1B,SAAS,CAAC2B,cAAc,CAACJ,KAAK,CAAC,EAAE;QACnC;QACAG,UAAU,GAAG1B,SAAS,CAACuB,KAAK,CAAC;;QAE7B;QACA,IAAIC,MAAM,GAAG,CAAC,IAAI,CAAChB,IAAI,CAACoB,IAAI,CAACH,GAAG,CAACD,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;UAC7C;QACF;;QAEA;QACA,IAAIA,MAAM,GAAGD,KAAK,CAACM,MAAM,GAAGJ,GAAG,CAACI,MAAM,IAAI,CAACrB,IAAI,CAACoB,IAAI,CAACH,GAAG,CAACD,MAAM,GAAGD,KAAK,CAACM,MAAM,CAAC,CAAC,EAAE;UAChF;QACF;MACF,CAAC,MAAM;QACLH,UAAU,GAAGH,KAAK,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MACjC;;MAEA;MACA,IAAIN,MAAM,GAAGJ,QAAQ,EAAE;QACrBD,KAAK,GAAW,IAAID,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;QACxCC,KAAK,CAACY,OAAO,GAAGf,IAAI,CAACc,KAAK,CAACV,QAAQ,EAAEI,MAAM,CAAC;QAC5CH,KAAK,CAACW,IAAI,CAACb,KAAK,CAAC;MACnB;MAEAA,KAAK,GAAW,IAAID,KAAK,CAAC,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;MACzCC,KAAK,CAACc,MAAM,GAAIP,UAAU;MAC1BP,KAAK,CAACY,OAAO,GAAGhC,OAAO,CAAC2B,UAAU,CAAC;MACnCL,KAAK,CAACW,IAAI,CAACb,KAAK,CAAC;MAEjBC,QAAQ,GAAGI,MAAM,GAAGD,KAAK,CAACM,MAAM;IAClC,CAAC,CAAC;IAEF,IAAIT,QAAQ,GAAGJ,IAAI,CAACa,MAAM,EAAE;MAC1BV,KAAK,GAAW,IAAID,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;MACxCC,KAAK,CAACY,OAAO,GAAGf,IAAI,CAACc,KAAK,CAACV,QAAQ,CAAC;MACpCC,KAAK,CAACW,IAAI,CAACb,KAAK,CAAC;IACnB;IAEA,OAAOE,KAAK;EACd;EAEA,OAAO,SAASa,aAAaA,CAACC,KAAK,EAAE;IACnC,IAAIC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,MAAM;MAAEpB,KAAK;MACtBqB,WAAW,GAAGL,KAAK,CAACI,MAAM;MAC1BE,aAAa,GAAG,CAAC;IAErB,KAAKJ,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGE,WAAW,CAACX,MAAM,EAAEQ,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAC9C,IAAIG,WAAW,CAACH,CAAC,CAAC,CAACK,IAAI,KAAK,QAAQ,EAAE;QAAE;MAAU;MAClDH,MAAM,GAAGC,WAAW,CAACH,CAAC,CAAC,CAACM,QAAQ;;MAEhC;MACA;MACA,KAAKP,CAAC,GAAGG,MAAM,CAACV,MAAM,GAAG,CAAC,EAAEO,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACvCjB,KAAK,GAAGoB,MAAM,CAACH,CAAC,CAAC;QAEjB,IAAIjB,KAAK,CAACuB,IAAI,KAAK,WAAW,IAAIvB,KAAK,CAACuB,IAAI,KAAK,YAAY,EAAE;UAC7D,IAAIvB,KAAK,CAACyB,IAAI,KAAK,MAAM,EAAE;YAAEH,aAAa,IAAItB,KAAK,CAAC0B,OAAO;UAAE;QAC/D;QAEA,IAAI1B,KAAK,CAACuB,IAAI,KAAK,MAAM,IAAID,aAAa,KAAK,CAAC,IAAIxC,MAAM,CAAC2B,IAAI,CAACT,KAAK,CAACY,OAAO,CAAC,EAAE;UAC9E;UACAS,WAAW,CAACH,CAAC,CAAC,CAACM,QAAQ,GAAGJ,MAAM,GAAGpC,cAAc,CAC/CoC,MAAM,EAAEH,CAAC,EAAErB,cAAc,CAACI,KAAK,CAACY,OAAO,EAAEZ,KAAK,CAACF,KAAK,EAAEkB,KAAK,CAACjB,KAAK,CACnE,CAAC;QACH;MACF;IACF;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}