{"ast": null, "code": "import Mermaid from 'mermaid';\nimport Murmur from './murmurhash3_gc.js';\nvar htmlEntities = function htmlEntities(str) {\n  return String(str).replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');\n};\nvar MermaidChart = function MermaidChart(code) {\n  var title = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '';\n  try {\n    var needsUniqueId = \"render\" + Murmur(code, 42).toString();\n    Mermaid.mermaidAPI.render(needsUniqueId, code, function (sc) {\n      code = sc;\n    });\n    if (title && String(title).length) {\n      title = `<div class=\"mermaid-title\">${htmlEntities(title)}</div>`;\n    }\n    return `<div class=\"mermaid\">${title}${code}</div>`;\n  } catch (err) {\n    return `<pre>${htmlEntities(err.name)}: ${htmlEntities(err.message)}</pre>`;\n  }\n};\nvar _MermaidPlugIn = function MermaidPlugIn(md, opts) {\n  Mermaid.initialize(Object.assign(_MermaidPlugIn.default, opts));\n  var defaultRenderer = md.renderer.rules.fence.bind(md.renderer.rules);\n  md.renderer.rules.fence = function (tokens, idx, opts, env, self) {\n    var token = tokens[idx];\n    var code = token.content.trim();\n    if (token.info.startsWith('mermaid')) {\n      var title;\n      var spc = token.info.indexOf(' ', 7);\n      if (spc > 0) {\n        title = token.info.slice(spc + 1);\n      }\n      return MermaidChart(code, title);\n    }\n    return defaultRenderer(tokens, idx, opts, env, self);\n  };\n};\n_MermaidPlugIn.default = {\n  startOnLoad: false,\n  securityLevel: 'true',\n  theme: \"default\",\n  flowchart: {\n    htmlLabels: false,\n    useMaxWidth: true\n  }\n};\nexport default _MermaidPlugIn;", "map": {"version": 3, "names": ["Mermaid", "<PERSON><PERSON><PERSON>", "htmlEntities", "str", "String", "replace", "Me<PERSON><PERSON><PERSON>", "code", "title", "arguments", "length", "undefined", "needsUniqueId", "toString", "mermaidAPI", "render", "sc", "err", "name", "message", "MermaidPlugIn", "md", "opts", "initialize", "Object", "assign", "default", "defaultRenderer", "renderer", "rules", "fence", "bind", "tokens", "idx", "env", "self", "token", "content", "trim", "info", "startsWith", "spc", "indexOf", "slice", "startOnLoad", "securityLevel", "theme", "flowchart", "htmlLabels", "useMaxWidth"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/@datatraccorporation+markdown-it-mermaid@0.5.0/node_modules/@datatraccorporation/markdown-it-mermaid/src/index.js"], "sourcesContent": ["import Mermaid from 'mermaid';\nimport Murmur from './murmurhash3_gc.js';\n\n\nconst htmlEntities = (str) =>\n    String(str).replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');\n\n\nconst MermaidChart = (code, title='') => {\n  try {\n    var needsUniqueId = \"render\" + Murmur(code, 42).toString();\n    Mermaid.mermaidAPI.render(needsUniqueId, code, sc => {code=sc});\n    if (title && String(title).length) {\n        title = `<div class=\"mermaid-title\">${htmlEntities(title)}</div>`;\n    }\n    return `<div class=\"mermaid\">${title}${code}</div>`;\n  } catch (err) {\n    return `<pre>${htmlEntities(err.name)}: ${htmlEntities(err.message)}</pre>`;\n  }\n}\n\n\nconst MermaidPlugIn = (md, opts) => {\n  Mermaid.initialize(Object.assign(MermaidPlugIn.default, opts));\n\n  const defaultRenderer = md.renderer.rules.fence.bind(md.renderer.rules);\n\n  md.renderer.rules.fence = (tokens, idx, opts, env, self) => {\n    const token = tokens[idx];\n    const code = token.content.trim();\n    if (token.info.startsWith('mermaid')) {\n      let title;\n      const spc = token.info.indexOf(' ', 7);\n      if (spc > 0) {\n          title = token.info.slice(spc + 1);\n      }\n      return MermaidChart(code, title);\n    }\n    return defaultRenderer(tokens, idx, opts, env, self);\n  }\n}\n\n\nMermaidPlugIn.default = {\n  startOnLoad: false,\n  securityLevel: 'true',\n    theme: \"default\",\n    flowchart:{\n      htmlLabels: false,\n      useMaxWidth: true,\n    }\n};\n\nexport default MermaidPlugIn;\n"], "mappings": "AAAA,OAAOA,OAAO,MAAM,SAAS;AAC7B,OAAOC,MAAM,MAAM,qBAAqB;AAGxC,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,GAAG;EAAA,OACrBC,MAAM,CAACD,GAAG,CAAC,CAACE,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;AAAA;AAGlF,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,IAAI,EAAe;EAAA,IAAbC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAC,EAAE;EAClC,IAAI;IACF,IAAIG,aAAa,GAAG,QAAQ,GAAGX,MAAM,CAACM,IAAI,EAAE,EAAE,CAAC,CAACM,QAAQ,CAAC,CAAC;IAC1Db,OAAO,CAACc,UAAU,CAACC,MAAM,CAACH,aAAa,EAAEL,IAAI,EAAE,UAAAS,EAAE,EAAI;MAACT,IAAI,GAACS,EAAE;IAAA,CAAC,CAAC;IAC/D,IAAIR,KAAK,IAAIJ,MAAM,CAACI,KAAK,CAAC,CAACE,MAAM,EAAE;MAC/BF,KAAK,GAAG,8BAA8BN,YAAY,CAACM,KAAK,CAAC,QAAQ;IACrE;IACA,OAAO,wBAAwBA,KAAK,GAAGD,IAAI,QAAQ;EACrD,CAAC,CAAC,OAAOU,GAAG,EAAE;IACZ,OAAO,QAAQf,YAAY,CAACe,GAAG,CAACC,IAAI,CAAC,KAAKhB,YAAY,CAACe,GAAG,CAACE,OAAO,CAAC,QAAQ;EAC7E;AACF,CAAC;AAGD,IAAMC,cAAa,GAAG,SAAhBA,aAAaA,CAAIC,EAAE,EAAEC,IAAI,EAAK;EAClCtB,OAAO,CAACuB,UAAU,CAACC,MAAM,CAACC,MAAM,CAACL,cAAa,CAACM,OAAO,EAAEJ,IAAI,CAAC,CAAC;EAE9D,IAAMK,eAAe,GAAGN,EAAE,CAACO,QAAQ,CAACC,KAAK,CAACC,KAAK,CAACC,IAAI,CAACV,EAAE,CAACO,QAAQ,CAACC,KAAK,CAAC;EAEvER,EAAE,CAACO,QAAQ,CAACC,KAAK,CAACC,KAAK,GAAG,UAACE,MAAM,EAAEC,GAAG,EAAEX,IAAI,EAAEY,GAAG,EAAEC,IAAI,EAAK;IAC1D,IAAMC,KAAK,GAAGJ,MAAM,CAACC,GAAG,CAAC;IACzB,IAAM1B,IAAI,GAAG6B,KAAK,CAACC,OAAO,CAACC,IAAI,CAAC,CAAC;IACjC,IAAIF,KAAK,CAACG,IAAI,CAACC,UAAU,CAAC,SAAS,CAAC,EAAE;MACpC,IAAIhC,KAAK;MACT,IAAMiC,GAAG,GAAGL,KAAK,CAACG,IAAI,CAACG,OAAO,CAAC,GAAG,EAAE,CAAC,CAAC;MACtC,IAAID,GAAG,GAAG,CAAC,EAAE;QACTjC,KAAK,GAAG4B,KAAK,CAACG,IAAI,CAACI,KAAK,CAACF,GAAG,GAAG,CAAC,CAAC;MACrC;MACA,OAAOnC,YAAY,CAACC,IAAI,EAAEC,KAAK,CAAC;IAClC;IACA,OAAOmB,eAAe,CAACK,MAAM,EAAEC,GAAG,EAAEX,IAAI,EAAEY,GAAG,EAAEC,IAAI,CAAC;EACtD,CAAC;AACH,CAAC;AAGDf,cAAa,CAACM,OAAO,GAAG;EACtBkB,WAAW,EAAE,KAAK;EAClBC,aAAa,EAAE,MAAM;EACnBC,KAAK,EAAE,SAAS;EAChBC,SAAS,EAAC;IACRC,UAAU,EAAE,KAAK;IACjBC,WAAW,EAAE;EACf;AACJ,CAAC;AAED,eAAe7B,cAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}