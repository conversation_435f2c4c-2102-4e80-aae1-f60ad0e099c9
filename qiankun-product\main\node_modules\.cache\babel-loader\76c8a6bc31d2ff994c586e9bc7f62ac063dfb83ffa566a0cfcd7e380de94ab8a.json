{"ast": null, "code": "function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { warn } from 'vue';\nimport { fromPairs } from 'lodash-unified';\nimport '../../types.mjs';\nimport '../../objects.mjs';\nimport { isObject, hasOwn } from '@vue/shared';\nvar epPropKey = \"__epPropKey\";\nvar definePropType = function definePropType(val) {\n  return val;\n};\nvar isEpProp = function isEpProp(val) {\n  return isObject(val) && !!val[epPropKey];\n};\nvar buildProp = function buildProp(prop, key) {\n  if (!isObject(prop) || isEpProp(prop)) return prop;\n  var values = prop.values,\n    required = prop.required,\n    defaultValue = prop.default,\n    type = prop.type,\n    validator = prop.validator;\n  var _validator = values || validator ? function (val) {\n    var valid = false;\n    var allowedValues = [];\n    if (values) {\n      allowedValues = Array.from(values);\n      if (hasOwn(prop, \"default\")) {\n        allowedValues.push(defaultValue);\n      }\n      valid || (valid = allowedValues.includes(val));\n    }\n    if (validator) valid || (valid = validator(val));\n    if (!valid && allowedValues.length > 0) {\n      var allowValuesText = _toConsumableArray(new Set(allowedValues)).map(function (value) {\n        return JSON.stringify(value);\n      }).join(\", \");\n      warn(`Invalid prop: validation failed${key ? ` for prop \"${key}\"` : \"\"}. Expected one of [${allowValuesText}], got value ${JSON.stringify(val)}.`);\n    }\n    return valid;\n  } : void 0;\n  var epProp = {\n    type,\n    required: !!required,\n    validator: _validator,\n    [epPropKey]: true\n  };\n  if (hasOwn(prop, \"default\")) epProp.default = defaultValue;\n  return epProp;\n};\nvar buildProps = function buildProps(props) {\n  return fromPairs(Object.entries(props).map(function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      key = _ref2[0],\n      option = _ref2[1];\n    return [key, buildProp(option, key)];\n  }));\n};\nexport { buildProp, buildProps, definePropType, epPropKey, isEpProp };", "map": {"version": 3, "names": ["epPropKey", "definePropType", "val", "isEpProp", "isObject", "buildProp", "prop", "key", "values", "required", "defaultValue", "default", "type", "validator", "_validator", "valid", "<PERSON><PERSON><PERSON><PERSON>", "Array", "from", "hasOwn", "push", "includes", "length", "allowValuesText", "_toConsumableArray", "Set", "map", "value", "JSON", "stringify", "join", "warn", "epProp", "buildProps", "props", "fromPairs", "Object", "entries", "_ref", "_ref2", "_slicedToArray", "option"], "sources": ["../../../../../../packages/utils/vue/props/runtime.ts"], "sourcesContent": ["import { warn } from 'vue'\nimport { fromPairs } from 'lodash-unified'\nimport { isObject } from '../../types'\nimport { hasOwn } from '../../objects'\n\nimport type { PropType } from 'vue'\nimport type {\n  EpProp,\n  EpPropConvert,\n  EpPropFinalized,\n  EpPropInput,\n  EpPropMergeType,\n  IfEpProp,\n  IfNativePropType,\n  NativePropType,\n} from './types'\n\nexport const epPropKey = '__epPropKey'\n\nexport const definePropType = <T>(val: any): PropType<T> => val\n\nexport const isEpProp = (val: unknown): val is EpProp<any, any, any> =>\n  isObject(val) && !!(val as any)[epPropKey]\n\n/**\n * @description Build prop. It can better optimize prop types\n * @description 生成 prop，能更好地优化类型\n * @example\n  // limited options\n  // the type will be PropType<'light' | 'dark'>\n  buildProp({\n    type: String,\n    values: ['light', 'dark'],\n  } as const)\n  * @example\n  // limited options and other types\n  // the type will be PropType<'small' | 'large' | number>\n  buildProp({\n    type: [String, Number],\n    values: ['small', 'large'],\n    validator: (val: unknown): val is number => typeof val === 'number',\n  } as const)\n  @link see more: https://github.com/element-plus/element-plus/pull/3341\n */\nexport const buildProp = <\n  Type = never,\n  Value = never,\n  Validator = never,\n  Default extends EpPropMergeType<Type, Value, Validator> = never,\n  Required extends boolean = false\n>(\n  prop: EpPropInput<Type, Value, Validator, Default, Required>,\n  key?: string\n): EpPropFinalized<Type, Value, Validator, Default, Required> => {\n  // filter native prop type and nested prop, e.g `null`, `undefined` (from `buildProps`)\n  if (!isObject(prop) || isEpProp(prop)) return prop as any\n\n  const { values, required, default: defaultValue, type, validator } = prop\n\n  const _validator =\n    values || validator\n      ? (val: unknown) => {\n          let valid = false\n          let allowedValues: unknown[] = []\n\n          if (values) {\n            allowedValues = Array.from(values)\n            if (hasOwn(prop, 'default')) {\n              allowedValues.push(defaultValue)\n            }\n            valid ||= allowedValues.includes(val)\n          }\n          if (validator) valid ||= validator(val)\n\n          if (!valid && allowedValues.length > 0) {\n            const allowValuesText = [...new Set(allowedValues)]\n              .map((value) => JSON.stringify(value))\n              .join(', ')\n            warn(\n              `Invalid prop: validation failed${\n                key ? ` for prop \"${key}\"` : ''\n              }. Expected one of [${allowValuesText}], got value ${JSON.stringify(\n                val\n              )}.`\n            )\n          }\n          return valid\n        }\n      : undefined\n\n  const epProp: any = {\n    type,\n    required: !!required,\n    validator: _validator,\n    [epPropKey]: true,\n  }\n  if (hasOwn(prop, 'default')) epProp.default = defaultValue\n  return epProp\n}\n\nexport const buildProps = <\n  Props extends Record<\n    string,\n    | { [epPropKey]: true }\n    | NativePropType\n    | EpPropInput<any, any, any, any, any>\n  >\n>(\n  props: Props\n): {\n  [K in keyof Props]: IfEpProp<\n    Props[K],\n    Props[K],\n    IfNativePropType<Props[K], Props[K], EpPropConvert<Props[K]>>\n  >\n} =>\n  fromPairs(\n    Object.entries(props).map(([key, option]) => [\n      key,\n      buildProp(option as any, key),\n    ])\n  ) as any\n"], "mappings": ";;;;;;;;;;;;;;;AAIY,IAACA,SAAS,GAAG;AACb,IAACC,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,GAAG;EAAA,OAAKA,GAAA;AAAA;AAC3B,IAACC,QAAQ,GAAG,SAAXA,QAAQA,CAAID,GAAG;EAAA,OAAKE,QAAQ,CAACF,GAAG,CAAC,IAAI,CAAC,CAACA,GAAG,CAACF,SAAS;AAAA;AACrD,IAACK,SAAS,GAAG,SAAZA,SAASA,CAAIC,IAAI,EAAEC,GAAG,EAAK;EACtC,IAAI,CAACH,QAAQ,CAACE,IAAI,CAAC,IAAIH,QAAQ,CAACG,IAAI,CAAC,EACnC,OAAOA,IAAI;EACb,IAAQE,MAAM,GAAuDF,IAAI,CAAjEE,MAAM;IAAEC,QAAQ,GAA6CH,IAAI,CAAzDG,QAAQ;IAAWC,YAAY,GAAsBJ,IAAI,CAA/CK,OAAO;IAAgBC,IAAI,GAAgBN,IAAI,CAAxBM,IAAI;IAAEC,SAAS,GAAKP,IAAI,CAAlBO,SAAS;EAChE,IAAMC,UAAU,GAAGN,MAAM,IAAIK,SAAS,GAAG,UAACX,GAAG,EAAK;IAChD,IAAIa,KAAK,GAAG,KAAK;IACjB,IAAIC,aAAa,GAAG,EAAE;IACtB,IAAIR,MAAM,EAAE;MACVQ,aAAa,GAAGC,KAAK,CAACC,IAAI,CAACV,MAAM,CAAC;MAClC,IAAIW,MAAM,CAACb,IAAI,EAAE,SAAS,CAAC,EAAE;QAC3BU,aAAa,CAACI,IAAI,CAACV,YAAY,CAAC;MACxC;MACMK,KAAK,KAAKA,KAAK,GAAGC,aAAa,CAACK,QAAQ,CAACnB,GAAG,CAAC,CAAC;IACpD;IACI,IAAIW,SAAS,EACXE,KAAK,KAAKA,KAAK,GAAGF,SAAS,CAACX,GAAG,CAAC,CAAC;IACnC,IAAI,CAACa,KAAK,IAAIC,aAAa,CAACM,MAAM,GAAG,CAAC,EAAE;MACtC,IAAMC,eAAe,GAAGC,kBAAA,CAAI,IAAIC,GAAG,CAACT,aAAa,CAAC,EAAEU,GAAG,CAAC,UAACC,KAAK;QAAA,OAAKC,IAAI,CAACC,SAAS,CAACF,KAAK,CAAC;MAAA,EAAC,CAACG,IAAI,CAAC,IAAI,CAAC;MACpGC,IAAI,CAAC,kCAAkCxB,GAAG,GAAG,cAAcA,GAAG,GAAG,GAAG,EAAE,sBAAsBgB,eAAe,gBAAgBK,IAAI,CAACC,SAAS,CAAC3B,GAAG,CAAC,GAAG,CAAC;IACxJ;IACI,OAAOa,KAAK;EAChB,CAAG,GAAG,KAAK,CAAC;EACV,IAAMiB,MAAM,GAAG;IACbpB,IAAI;IACJH,QAAQ,EAAE,CAAC,CAACA,QAAQ;IACpBI,SAAS,EAAEC,UAAU;IACrB,CAACd,SAAS,GAAG;EACjB,CAAG;EACD,IAAImB,MAAM,CAACb,IAAI,EAAE,SAAS,CAAC,EACzB0B,MAAM,CAACrB,OAAO,GAAGD,YAAY;EAC/B,OAAOsB,MAAM;AACf;AACY,IAACC,UAAU,GAAG,SAAbA,UAAUA,CAAIC,KAAK;EAAA,OAAKC,SAAS,CAACC,MAAM,CAACC,OAAO,CAACH,KAAK,CAAC,CAACR,GAAG,CAAC,UAAAY,IAAA;IAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAF,IAAA;MAAE/B,GAAG,GAAAgC,KAAA;MAAEE,MAAM,GAAAF,KAAA;IAAA,OAAM,CAC1FhC,GAAG,EACHF,SAAS,CAACoC,MAAM,EAAElC,GAAG,CAAC,CACvB;EAAA,EAAC;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}