{"ast": null, "code": "// heading (#, ##, ...)\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\nmodule.exports = function heading(state, startLine, endLine, silent) {\n  var ch,\n    level,\n    tmp,\n    token,\n    pos = state.bMarks[startLine] + state.tShift[startLine],\n    max = state.eMarks[startLine];\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) {\n    return false;\n  }\n  ch = state.src.charCodeAt(pos);\n  if (ch !== 0x23 /* # */ || pos >= max) {\n    return false;\n  }\n\n  // count heading level\n  level = 1;\n  ch = state.src.charCodeAt(++pos);\n  while (ch === 0x23 /* # */ && pos < max && level <= 6) {\n    level++;\n    ch = state.src.charCodeAt(++pos);\n  }\n  if (level > 6 || pos < max && !isSpace(ch)) {\n    return false;\n  }\n  if (silent) {\n    return true;\n  }\n\n  // Let's cut tails like '    ###  ' from the end of string\n\n  max = state.skipSpacesBack(max, pos);\n  tmp = state.skipCharsBack(max, 0x23, pos); // #\n  if (tmp > pos && isSpace(state.src.charCodeAt(tmp - 1))) {\n    max = tmp;\n  }\n  state.line = startLine + 1;\n  token = state.push('heading_open', 'h' + String(level), 1);\n  token.markup = '########'.slice(0, level);\n  token.map = [startLine, state.line];\n  token = state.push('inline', '', 0);\n  token.content = state.src.slice(pos, max).trim();\n  token.map = [startLine, state.line];\n  token.children = [];\n  token = state.push('heading_close', 'h' + String(level), -1);\n  token.markup = '########'.slice(0, level);\n  return true;\n};", "map": {"version": 3, "names": ["isSpace", "require", "module", "exports", "heading", "state", "startLine", "endLine", "silent", "ch", "level", "tmp", "token", "pos", "bMarks", "tShift", "max", "eMarks", "sCount", "blkIndent", "src", "charCodeAt", "skipSpacesBack", "skip<PERSON><PERSON><PERSON><PERSON><PERSON>", "line", "push", "String", "markup", "slice", "map", "content", "trim", "children"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_block/heading.js"], "sourcesContent": ["// heading (#, ##, ...)\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\n\n\nmodule.exports = function heading(state, startLine, endLine, silent) {\n  var ch, level, tmp, token,\n      pos = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine];\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false; }\n\n  ch  = state.src.charCodeAt(pos);\n\n  if (ch !== 0x23/* # */ || pos >= max) { return false; }\n\n  // count heading level\n  level = 1;\n  ch = state.src.charCodeAt(++pos);\n  while (ch === 0x23/* # */ && pos < max && level <= 6) {\n    level++;\n    ch = state.src.charCodeAt(++pos);\n  }\n\n  if (level > 6 || (pos < max && !isSpace(ch))) { return false; }\n\n  if (silent) { return true; }\n\n  // Let's cut tails like '    ###  ' from the end of string\n\n  max = state.skipSpacesBack(max, pos);\n  tmp = state.skipCharsBack(max, 0x23, pos); // #\n  if (tmp > pos && isSpace(state.src.charCodeAt(tmp - 1))) {\n    max = tmp;\n  }\n\n  state.line = startLine + 1;\n\n  token        = state.push('heading_open', 'h' + String(level), 1);\n  token.markup = '########'.slice(0, level);\n  token.map    = [ startLine, state.line ];\n\n  token          = state.push('inline', '', 0);\n  token.content  = state.src.slice(pos, max).trim();\n  token.map      = [ startLine, state.line ];\n  token.children = [];\n\n  token        = state.push('heading_close', 'h' + String(level), -1);\n  token.markup = '########'.slice(0, level);\n\n  return true;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,iBAAiB,CAAC,CAACD,OAAO;AAGhDE,MAAM,CAACC,OAAO,GAAG,SAASC,OAAOA,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAE;EACnE,IAAIC,EAAE;IAAEC,KAAK;IAAEC,GAAG;IAAEC,KAAK;IACrBC,GAAG,GAAGR,KAAK,CAACS,MAAM,CAACR,SAAS,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,SAAS,CAAC;IACvDU,GAAG,GAAGX,KAAK,CAACY,MAAM,CAACX,SAAS,CAAC;;EAEjC;EACA,IAAID,KAAK,CAACa,MAAM,CAACZ,SAAS,CAAC,GAAGD,KAAK,CAACc,SAAS,IAAI,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAEpEV,EAAE,GAAIJ,KAAK,CAACe,GAAG,CAACC,UAAU,CAACR,GAAG,CAAC;EAE/B,IAAIJ,EAAE,KAAK,IAAI,YAAWI,GAAG,IAAIG,GAAG,EAAE;IAAE,OAAO,KAAK;EAAE;;EAEtD;EACAN,KAAK,GAAG,CAAC;EACTD,EAAE,GAAGJ,KAAK,CAACe,GAAG,CAACC,UAAU,CAAC,EAAER,GAAG,CAAC;EAChC,OAAOJ,EAAE,KAAK,IAAI,YAAWI,GAAG,GAAGG,GAAG,IAAIN,KAAK,IAAI,CAAC,EAAE;IACpDA,KAAK,EAAE;IACPD,EAAE,GAAGJ,KAAK,CAACe,GAAG,CAACC,UAAU,CAAC,EAAER,GAAG,CAAC;EAClC;EAEA,IAAIH,KAAK,GAAG,CAAC,IAAKG,GAAG,GAAGG,GAAG,IAAI,CAAChB,OAAO,CAACS,EAAE,CAAE,EAAE;IAAE,OAAO,KAAK;EAAE;EAE9D,IAAID,MAAM,EAAE;IAAE,OAAO,IAAI;EAAE;;EAE3B;;EAEAQ,GAAG,GAAGX,KAAK,CAACiB,cAAc,CAACN,GAAG,EAAEH,GAAG,CAAC;EACpCF,GAAG,GAAGN,KAAK,CAACkB,aAAa,CAACP,GAAG,EAAE,IAAI,EAAEH,GAAG,CAAC,CAAC,CAAC;EAC3C,IAAIF,GAAG,GAAGE,GAAG,IAAIb,OAAO,CAACK,KAAK,CAACe,GAAG,CAACC,UAAU,CAACV,GAAG,GAAG,CAAC,CAAC,CAAC,EAAE;IACvDK,GAAG,GAAGL,GAAG;EACX;EAEAN,KAAK,CAACmB,IAAI,GAAGlB,SAAS,GAAG,CAAC;EAE1BM,KAAK,GAAUP,KAAK,CAACoB,IAAI,CAAC,cAAc,EAAE,GAAG,GAAGC,MAAM,CAAChB,KAAK,CAAC,EAAE,CAAC,CAAC;EACjEE,KAAK,CAACe,MAAM,GAAG,UAAU,CAACC,KAAK,CAAC,CAAC,EAAElB,KAAK,CAAC;EACzCE,KAAK,CAACiB,GAAG,GAAM,CAAEvB,SAAS,EAAED,KAAK,CAACmB,IAAI,CAAE;EAExCZ,KAAK,GAAYP,KAAK,CAACoB,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;EAC5Cb,KAAK,CAACkB,OAAO,GAAIzB,KAAK,CAACe,GAAG,CAACQ,KAAK,CAACf,GAAG,EAAEG,GAAG,CAAC,CAACe,IAAI,CAAC,CAAC;EACjDnB,KAAK,CAACiB,GAAG,GAAQ,CAAEvB,SAAS,EAAED,KAAK,CAACmB,IAAI,CAAE;EAC1CZ,KAAK,CAACoB,QAAQ,GAAG,EAAE;EAEnBpB,KAAK,GAAUP,KAAK,CAACoB,IAAI,CAAC,eAAe,EAAE,GAAG,GAAGC,MAAM,CAAChB,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;EACnEE,KAAK,CAACe,MAAM,GAAG,UAAU,CAACC,KAAK,CAAC,CAAC,EAAElB,KAAK,CAAC;EAEzC,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}