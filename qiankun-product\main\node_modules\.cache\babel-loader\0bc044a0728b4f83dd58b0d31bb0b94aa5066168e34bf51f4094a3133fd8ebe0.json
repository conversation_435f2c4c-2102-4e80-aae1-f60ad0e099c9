{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\n/**\n * <AUTHOR>\n * @since 2020-10-13\n */\nimport { isBoundedFunction, isCallable, nativeDocument, nativeGlobal } from '../../../utils';\nimport { getCurrentRunningApp } from '../../common';\nimport { calcAppCount, getAppWrapperHeadElement, isAllAppsUnmounted, isHijackingTag, patchHTMLDynamicAppendPrototypeFunctions, rebuildCSSRules, recordStyledComponentsCSSRules, styleElementRefNodeNo, styleElementTargetSymbol } from './common';\nvar elementAttachedSymbol = Symbol('attachedApp');\n// Get native global window with a sandbox disgusted way, thus we could share it between qiankun instances🤪\nObject.defineProperty(nativeGlobal, '__proxyAttachContainerConfigMap__', {\n  enumerable: false,\n  writable: true\n});\nObject.defineProperty(nativeGlobal, '__currentLockingSandbox__', {\n  enumerable: false,\n  writable: true,\n  configurable: true\n});\nvar rawHeadAppendChild = HTMLHeadElement.prototype.appendChild;\nvar rawHeadInsertBefore = HTMLHeadElement.prototype.insertBefore;\n// Share proxyAttachContainerConfigMap between multiple qiankun instance, thus they could access the same record\nnativeGlobal.__proxyAttachContainerConfigMap__ = nativeGlobal.__proxyAttachContainerConfigMap__ || new WeakMap();\nvar proxyAttachContainerConfigMap = nativeGlobal.__proxyAttachContainerConfigMap__;\nvar elementAttachContainerConfigMap = new WeakMap();\nvar docCreatePatchedMap = new WeakMap();\nvar patchMap = new WeakMap();\nfunction patchDocument(cfg) {\n  var sandbox = cfg.sandbox,\n    speedy = cfg.speedy;\n  var attachElementToProxy = function attachElementToProxy(element, proxy) {\n    var proxyContainerConfig = proxyAttachContainerConfigMap.get(proxy);\n    if (proxyContainerConfig) {\n      elementAttachContainerConfigMap.set(element, proxyContainerConfig);\n    }\n  };\n  if (speedy) {\n    var modifications = {};\n    var proxyDocument = new Proxy(document, {\n      /**\n       * Read and write must be paired, otherwise the write operation will leak to the global\n       */\n      set: function set(target, p, value) {\n        switch (p) {\n          case 'createElement':\n            {\n              modifications.createElement = value;\n              break;\n            }\n          case 'querySelector':\n            {\n              modifications.querySelector = value;\n              break;\n            }\n          default:\n            target[p] = value;\n            break;\n        }\n        return true;\n      },\n      get: function get(target, p, receiver) {\n        switch (p) {\n          case 'createElement':\n            {\n              // Must store the original createElement function to avoid error in nested sandbox\n              var targetCreateElement = modifications.createElement || target.createElement;\n              return function createElement() {\n                if (!nativeGlobal.__currentLockingSandbox__) {\n                  nativeGlobal.__currentLockingSandbox__ = sandbox.name;\n                }\n                for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n                  args[_key] = arguments[_key];\n                }\n                var element = targetCreateElement.call.apply(targetCreateElement, [target].concat(args));\n                // only record the element which is created by the current sandbox, thus we can avoid the element created by nested sandboxes\n                if (nativeGlobal.__currentLockingSandbox__ === sandbox.name) {\n                  attachElementToProxy(element, sandbox.proxy);\n                  delete nativeGlobal.__currentLockingSandbox__;\n                }\n                return element;\n              };\n            }\n          case 'querySelector':\n            {\n              var targetQuerySelector = modifications.querySelector || target.querySelector;\n              return function querySelector() {\n                for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n                  args[_key2] = arguments[_key2];\n                }\n                var selector = args[0];\n                switch (selector) {\n                  case 'head':\n                    {\n                      var containerConfig = proxyAttachContainerConfigMap.get(sandbox.proxy);\n                      if (containerConfig) {\n                        var qiankunHead = getAppWrapperHeadElement(containerConfig.appWrapperGetter());\n                        qiankunHead.appendChild = HTMLHeadElement.prototype.appendChild;\n                        qiankunHead.insertBefore = HTMLHeadElement.prototype.insertBefore;\n                        qiankunHead.removeChild = HTMLHeadElement.prototype.removeChild;\n                        return qiankunHead;\n                      }\n                      break;\n                    }\n                }\n                return targetQuerySelector.call.apply(targetQuerySelector, [target].concat(args));\n              };\n            }\n          default:\n            break;\n        }\n        var value = target[p];\n        // must rebind the function to the target otherwise it will cause illegal invocation error\n        if (isCallable(value) && !isBoundedFunction(value)) {\n          return function proxyFunction() {\n            for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n              args[_key3] = arguments[_key3];\n            }\n            return value.call.apply(value, [target].concat(_toConsumableArray(args.map(function (arg) {\n              return arg === receiver ? target : arg;\n            }))));\n          };\n        }\n        return value;\n      }\n    });\n    sandbox.patchDocument(proxyDocument);\n    // patch MutationObserver.prototype.observe to avoid type error\n    // https://github.com/umijs/qiankun/issues/2406\n    var nativeMutationObserverObserveFn = MutationObserver.prototype.observe;\n    if (!patchMap.has(nativeMutationObserverObserveFn)) {\n      var observe = function observe(target, options) {\n        var realTarget = target instanceof Document ? nativeDocument : target;\n        return nativeMutationObserverObserveFn.call(this, realTarget, options);\n      };\n      MutationObserver.prototype.observe = observe;\n      patchMap.set(nativeMutationObserverObserveFn, observe);\n    }\n    // patch Node.prototype.compareDocumentPosition to avoid type error\n    var prevCompareDocumentPosition = Node.prototype.compareDocumentPosition;\n    if (!patchMap.has(prevCompareDocumentPosition)) {\n      Node.prototype.compareDocumentPosition = function compareDocumentPosition(node) {\n        var realNode = node instanceof Document ? nativeDocument : node;\n        return prevCompareDocumentPosition.call(this, realNode);\n      };\n      patchMap.set(prevCompareDocumentPosition, Node.prototype.compareDocumentPosition);\n    }\n    // patch parentNode getter to avoid document === html.parentNode\n    // https://github.com/umijs/qiankun/issues/2408#issuecomment-1446229105\n    var parentNodeDescriptor = Object.getOwnPropertyDescriptor(Node.prototype, 'parentNode');\n    if (parentNodeDescriptor && !patchMap.has(parentNodeDescriptor)) {\n      var parentNodeGetter = parentNodeDescriptor.get,\n        configurable = parentNodeDescriptor.configurable;\n      if (parentNodeGetter && configurable) {\n        var patchedParentNodeDescriptor = _objectSpread(_objectSpread({}, parentNodeDescriptor), {}, {\n          get: function get() {\n            var parentNode = parentNodeGetter.call(this);\n            if (parentNode instanceof Document) {\n              var _getCurrentRunningApp;\n              var proxy = (_getCurrentRunningApp = getCurrentRunningApp()) === null || _getCurrentRunningApp === void 0 ? void 0 : _getCurrentRunningApp.window;\n              if (proxy) {\n                return proxy.document;\n              }\n            }\n            return parentNode;\n          }\n        });\n        Object.defineProperty(Node.prototype, 'parentNode', patchedParentNodeDescriptor);\n        patchMap.set(parentNodeDescriptor, patchedParentNodeDescriptor);\n      }\n    }\n    return function () {\n      MutationObserver.prototype.observe = nativeMutationObserverObserveFn;\n      patchMap.delete(nativeMutationObserverObserveFn);\n      Node.prototype.compareDocumentPosition = prevCompareDocumentPosition;\n      patchMap.delete(prevCompareDocumentPosition);\n      if (parentNodeDescriptor) {\n        Object.defineProperty(Node.prototype, 'parentNode', parentNodeDescriptor);\n        patchMap.delete(parentNodeDescriptor);\n      }\n    };\n  }\n  var docCreateElementFnBeforeOverwrite = docCreatePatchedMap.get(document.createElement);\n  if (!docCreateElementFnBeforeOverwrite) {\n    var rawDocumentCreateElement = document.createElement;\n    Document.prototype.createElement = function createElement(tagName, options) {\n      var element = rawDocumentCreateElement.call(this, tagName, options);\n      if (isHijackingTag(tagName)) {\n        var _ref = getCurrentRunningApp() || {},\n          currentRunningSandboxProxy = _ref.window;\n        if (currentRunningSandboxProxy) {\n          attachElementToProxy(element, currentRunningSandboxProxy);\n        }\n      }\n      return element;\n    };\n    // It means it have been overwritten while createElement is an own property of document\n    if (document.hasOwnProperty('createElement')) {\n      document.createElement = Document.prototype.createElement;\n    }\n    docCreatePatchedMap.set(Document.prototype.createElement, rawDocumentCreateElement);\n  }\n  return function unpatch() {\n    if (docCreateElementFnBeforeOverwrite) {\n      Document.prototype.createElement = docCreateElementFnBeforeOverwrite;\n      document.createElement = docCreateElementFnBeforeOverwrite;\n    }\n  };\n}\nexport function patchStrictSandbox(appName, appWrapperGetter, sandbox) {\n  var mounting = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true;\n  var scopedCSS = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n  var excludeAssetFilter = arguments.length > 5 ? arguments[5] : undefined;\n  var speedySandbox = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : false;\n  var proxy = sandbox.proxy;\n  var containerConfig = proxyAttachContainerConfigMap.get(proxy);\n  if (!containerConfig) {\n    containerConfig = {\n      appName: appName,\n      proxy: proxy,\n      appWrapperGetter: appWrapperGetter,\n      dynamicStyleSheetElements: [],\n      strictGlobal: true,\n      speedySandbox: speedySandbox,\n      excludeAssetFilter: excludeAssetFilter,\n      scopedCSS: scopedCSS\n    };\n    proxyAttachContainerConfigMap.set(proxy, containerConfig);\n  }\n  // all dynamic style sheets are stored in proxy container\n  var _containerConfig = containerConfig,\n    dynamicStyleSheetElements = _containerConfig.dynamicStyleSheetElements;\n  var unpatchDynamicAppendPrototypeFunctions = patchHTMLDynamicAppendPrototypeFunctions(function (element) {\n    return elementAttachContainerConfigMap.has(element);\n  }, function (element) {\n    return elementAttachContainerConfigMap.get(element);\n  });\n  var unpatchDocument = patchDocument({\n    sandbox: sandbox,\n    speedy: speedySandbox\n  });\n  if (!mounting) calcAppCount(appName, 'increase', 'bootstrapping');\n  if (mounting) calcAppCount(appName, 'increase', 'mounting');\n  return function free() {\n    if (!mounting) calcAppCount(appName, 'decrease', 'bootstrapping');\n    if (mounting) calcAppCount(appName, 'decrease', 'mounting');\n    // release the overwritten prototype after all the micro apps unmounted\n    if (isAllAppsUnmounted()) {\n      unpatchDynamicAppendPrototypeFunctions();\n      unpatchDocument();\n    }\n    recordStyledComponentsCSSRules(dynamicStyleSheetElements);\n    // As now the sub app content all wrapped with a special id container,\n    // the dynamic style sheet would be removed automatically while unmoutting\n    return function rebuild() {\n      rebuildCSSRules(dynamicStyleSheetElements, function (stylesheetElement) {\n        var appWrapper = appWrapperGetter();\n        if (!appWrapper.contains(stylesheetElement)) {\n          var mountDom = stylesheetElement[styleElementTargetSymbol] === 'head' ? getAppWrapperHeadElement(appWrapper) : appWrapper;\n          var refNo = stylesheetElement[styleElementRefNodeNo];\n          if (typeof refNo === 'number' && refNo !== -1) {\n            // the reference node may be dynamic script comment which is not rebuilt while remounting thus reference node no longer exists\n            var refNode = mountDom.childNodes[refNo] || null;\n            rawHeadInsertBefore.call(mountDom, stylesheetElement, refNode);\n            return true;\n          } else {\n            rawHeadAppendChild.call(mountDom, stylesheetElement);\n            return true;\n          }\n        }\n        return false;\n      });\n    };\n  };\n}", "map": {"version": 3, "names": ["_objectSpread", "_toConsumableArray", "isBoundedFunction", "isCallable", "nativeDocument", "nativeGlobal", "getCurrentRunningApp", "calcAppCount", "getAppWrapperHeadElement", "isAllAppsUnmounted", "isHijackingTag", "patchHTMLDynamicAppendPrototypeFunctions", "rebuildCSSRules", "recordStyledComponentsCSSRules", "styleElementRefNodeNo", "styleElementTargetSymbol", "elementAttachedSymbol", "Symbol", "Object", "defineProperty", "enumerable", "writable", "configurable", "rawHeadAppendChild", "HTMLHeadElement", "prototype", "append<PERSON><PERSON><PERSON>", "rawHeadInsertBefore", "insertBefore", "__proxyAttachContainerConfigMap__", "WeakMap", "proxyAttachContainerConfigMap", "elementAttachContainerConfigMap", "docCreatePatchedMap", "patchMap", "patchDocument", "cfg", "sandbox", "speedy", "attachElementToProxy", "element", "proxy", "proxyContainerConfig", "get", "set", "modifications", "proxyDocument", "Proxy", "document", "target", "p", "value", "createElement", "querySelector", "receiver", "targetCreateElement", "__currentLockingSandbox__", "name", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "targetQuerySelector", "_len2", "_key2", "selector", "containerConfig", "qiankunHead", "appWrapperGetter", "<PERSON><PERSON><PERSON><PERSON>", "proxyFunction", "_len3", "_key3", "map", "arg", "nativeMutationObserverObserveFn", "MutationObserver", "observe", "has", "options", "realTarget", "Document", "prevCompareDocumentPosition", "Node", "compareDocumentPosition", "node", "realNode", "parentNodeDescriptor", "getOwnPropertyDescriptor", "parentNodeGetter", "patchedParentNodeDescriptor", "parentNode", "_getCurrentRunningApp", "window", "delete", "docCreateElementFnBeforeOverwrite", "rawDocumentCreateElement", "tagName", "_ref", "currentRunningSandboxProxy", "hasOwnProperty", "unpatch", "patchStrictSandbox", "appName", "mounting", "undefined", "scopedCSS", "excludeAssetFilter", "speedySandbox", "dynamicStyleSheetElements", "strictGlobal", "_containerConfig", "unpatchDynamicAppendPrototypeFunctions", "unpatchDocument", "free", "rebuild", "stylesheetElement", "appWrapper", "contains", "mountDom", "refNo", "refNode", "childNodes"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/qiankun@2.10.16/node_modules/qiankun/es/sandbox/patchers/dynamicAppend/forStrictSandbox.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\n/**\n * <AUTHOR>\n * @since 2020-10-13\n */\nimport { isBoundedFunction, isCallable, nativeDocument, nativeGlobal } from '../../../utils';\nimport { getCurrentRunningApp } from '../../common';\nimport { calcAppCount, getAppWrapperHeadElement, isAllAppsUnmounted, isHijackingTag, patchHTMLDynamicAppendPrototypeFunctions, rebuildCSSRules, recordStyledComponentsCSSRules, styleElementRefNodeNo, styleElementTargetSymbol } from './common';\nvar elementAttachedSymbol = Symbol('attachedApp');\n// Get native global window with a sandbox disgusted way, thus we could share it between qiankun instances🤪\nObject.defineProperty(nativeGlobal, '__proxyAttachContainerConfigMap__', {\n  enumerable: false,\n  writable: true\n});\nObject.defineProperty(nativeGlobal, '__currentLockingSandbox__', {\n  enumerable: false,\n  writable: true,\n  configurable: true\n});\nvar rawHeadAppendChild = HTMLHeadElement.prototype.appendChild;\nvar rawHeadInsertBefore = HTMLHeadElement.prototype.insertBefore;\n// Share proxyAttachContainerConfigMap between multiple qiankun instance, thus they could access the same record\nnativeGlobal.__proxyAttachContainerConfigMap__ = nativeGlobal.__proxyAttachContainerConfigMap__ || new WeakMap();\nvar proxyAttachContainerConfigMap = nativeGlobal.__proxyAttachContainerConfigMap__;\nvar elementAttachContainerConfigMap = new WeakMap();\nvar docCreatePatchedMap = new WeakMap();\nvar patchMap = new WeakMap();\nfunction patchDocument(cfg) {\n  var sandbox = cfg.sandbox,\n    speedy = cfg.speedy;\n  var attachElementToProxy = function attachElementToProxy(element, proxy) {\n    var proxyContainerConfig = proxyAttachContainerConfigMap.get(proxy);\n    if (proxyContainerConfig) {\n      elementAttachContainerConfigMap.set(element, proxyContainerConfig);\n    }\n  };\n  if (speedy) {\n    var modifications = {};\n    var proxyDocument = new Proxy(document, {\n      /**\n       * Read and write must be paired, otherwise the write operation will leak to the global\n       */\n      set: function set(target, p, value) {\n        switch (p) {\n          case 'createElement':\n            {\n              modifications.createElement = value;\n              break;\n            }\n          case 'querySelector':\n            {\n              modifications.querySelector = value;\n              break;\n            }\n          default:\n            target[p] = value;\n            break;\n        }\n        return true;\n      },\n      get: function get(target, p, receiver) {\n        switch (p) {\n          case 'createElement':\n            {\n              // Must store the original createElement function to avoid error in nested sandbox\n              var targetCreateElement = modifications.createElement || target.createElement;\n              return function createElement() {\n                if (!nativeGlobal.__currentLockingSandbox__) {\n                  nativeGlobal.__currentLockingSandbox__ = sandbox.name;\n                }\n                for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n                  args[_key] = arguments[_key];\n                }\n                var element = targetCreateElement.call.apply(targetCreateElement, [target].concat(args));\n                // only record the element which is created by the current sandbox, thus we can avoid the element created by nested sandboxes\n                if (nativeGlobal.__currentLockingSandbox__ === sandbox.name) {\n                  attachElementToProxy(element, sandbox.proxy);\n                  delete nativeGlobal.__currentLockingSandbox__;\n                }\n                return element;\n              };\n            }\n          case 'querySelector':\n            {\n              var targetQuerySelector = modifications.querySelector || target.querySelector;\n              return function querySelector() {\n                for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n                  args[_key2] = arguments[_key2];\n                }\n                var selector = args[0];\n                switch (selector) {\n                  case 'head':\n                    {\n                      var containerConfig = proxyAttachContainerConfigMap.get(sandbox.proxy);\n                      if (containerConfig) {\n                        var qiankunHead = getAppWrapperHeadElement(containerConfig.appWrapperGetter());\n                        qiankunHead.appendChild = HTMLHeadElement.prototype.appendChild;\n                        qiankunHead.insertBefore = HTMLHeadElement.prototype.insertBefore;\n                        qiankunHead.removeChild = HTMLHeadElement.prototype.removeChild;\n                        return qiankunHead;\n                      }\n                      break;\n                    }\n                }\n                return targetQuerySelector.call.apply(targetQuerySelector, [target].concat(args));\n              };\n            }\n          default:\n            break;\n        }\n        var value = target[p];\n        // must rebind the function to the target otherwise it will cause illegal invocation error\n        if (isCallable(value) && !isBoundedFunction(value)) {\n          return function proxyFunction() {\n            for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n              args[_key3] = arguments[_key3];\n            }\n            return value.call.apply(value, [target].concat(_toConsumableArray(args.map(function (arg) {\n              return arg === receiver ? target : arg;\n            }))));\n          };\n        }\n        return value;\n      }\n    });\n    sandbox.patchDocument(proxyDocument);\n    // patch MutationObserver.prototype.observe to avoid type error\n    // https://github.com/umijs/qiankun/issues/2406\n    var nativeMutationObserverObserveFn = MutationObserver.prototype.observe;\n    if (!patchMap.has(nativeMutationObserverObserveFn)) {\n      var observe = function observe(target, options) {\n        var realTarget = target instanceof Document ? nativeDocument : target;\n        return nativeMutationObserverObserveFn.call(this, realTarget, options);\n      };\n      MutationObserver.prototype.observe = observe;\n      patchMap.set(nativeMutationObserverObserveFn, observe);\n    }\n    // patch Node.prototype.compareDocumentPosition to avoid type error\n    var prevCompareDocumentPosition = Node.prototype.compareDocumentPosition;\n    if (!patchMap.has(prevCompareDocumentPosition)) {\n      Node.prototype.compareDocumentPosition = function compareDocumentPosition(node) {\n        var realNode = node instanceof Document ? nativeDocument : node;\n        return prevCompareDocumentPosition.call(this, realNode);\n      };\n      patchMap.set(prevCompareDocumentPosition, Node.prototype.compareDocumentPosition);\n    }\n    // patch parentNode getter to avoid document === html.parentNode\n    // https://github.com/umijs/qiankun/issues/2408#issuecomment-1446229105\n    var parentNodeDescriptor = Object.getOwnPropertyDescriptor(Node.prototype, 'parentNode');\n    if (parentNodeDescriptor && !patchMap.has(parentNodeDescriptor)) {\n      var parentNodeGetter = parentNodeDescriptor.get,\n        configurable = parentNodeDescriptor.configurable;\n      if (parentNodeGetter && configurable) {\n        var patchedParentNodeDescriptor = _objectSpread(_objectSpread({}, parentNodeDescriptor), {}, {\n          get: function get() {\n            var parentNode = parentNodeGetter.call(this);\n            if (parentNode instanceof Document) {\n              var _getCurrentRunningApp;\n              var proxy = (_getCurrentRunningApp = getCurrentRunningApp()) === null || _getCurrentRunningApp === void 0 ? void 0 : _getCurrentRunningApp.window;\n              if (proxy) {\n                return proxy.document;\n              }\n            }\n            return parentNode;\n          }\n        });\n        Object.defineProperty(Node.prototype, 'parentNode', patchedParentNodeDescriptor);\n        patchMap.set(parentNodeDescriptor, patchedParentNodeDescriptor);\n      }\n    }\n    return function () {\n      MutationObserver.prototype.observe = nativeMutationObserverObserveFn;\n      patchMap.delete(nativeMutationObserverObserveFn);\n      Node.prototype.compareDocumentPosition = prevCompareDocumentPosition;\n      patchMap.delete(prevCompareDocumentPosition);\n      if (parentNodeDescriptor) {\n        Object.defineProperty(Node.prototype, 'parentNode', parentNodeDescriptor);\n        patchMap.delete(parentNodeDescriptor);\n      }\n    };\n  }\n  var docCreateElementFnBeforeOverwrite = docCreatePatchedMap.get(document.createElement);\n  if (!docCreateElementFnBeforeOverwrite) {\n    var rawDocumentCreateElement = document.createElement;\n    Document.prototype.createElement = function createElement(tagName, options) {\n      var element = rawDocumentCreateElement.call(this, tagName, options);\n      if (isHijackingTag(tagName)) {\n        var _ref = getCurrentRunningApp() || {},\n          currentRunningSandboxProxy = _ref.window;\n        if (currentRunningSandboxProxy) {\n          attachElementToProxy(element, currentRunningSandboxProxy);\n        }\n      }\n      return element;\n    };\n    // It means it have been overwritten while createElement is an own property of document\n    if (document.hasOwnProperty('createElement')) {\n      document.createElement = Document.prototype.createElement;\n    }\n    docCreatePatchedMap.set(Document.prototype.createElement, rawDocumentCreateElement);\n  }\n  return function unpatch() {\n    if (docCreateElementFnBeforeOverwrite) {\n      Document.prototype.createElement = docCreateElementFnBeforeOverwrite;\n      document.createElement = docCreateElementFnBeforeOverwrite;\n    }\n  };\n}\nexport function patchStrictSandbox(appName, appWrapperGetter, sandbox) {\n  var mounting = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true;\n  var scopedCSS = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n  var excludeAssetFilter = arguments.length > 5 ? arguments[5] : undefined;\n  var speedySandbox = arguments.length > 6 && arguments[6] !== undefined ? arguments[6] : false;\n  var proxy = sandbox.proxy;\n  var containerConfig = proxyAttachContainerConfigMap.get(proxy);\n  if (!containerConfig) {\n    containerConfig = {\n      appName: appName,\n      proxy: proxy,\n      appWrapperGetter: appWrapperGetter,\n      dynamicStyleSheetElements: [],\n      strictGlobal: true,\n      speedySandbox: speedySandbox,\n      excludeAssetFilter: excludeAssetFilter,\n      scopedCSS: scopedCSS\n    };\n    proxyAttachContainerConfigMap.set(proxy, containerConfig);\n  }\n  // all dynamic style sheets are stored in proxy container\n  var _containerConfig = containerConfig,\n    dynamicStyleSheetElements = _containerConfig.dynamicStyleSheetElements;\n  var unpatchDynamicAppendPrototypeFunctions = patchHTMLDynamicAppendPrototypeFunctions(function (element) {\n    return elementAttachContainerConfigMap.has(element);\n  }, function (element) {\n    return elementAttachContainerConfigMap.get(element);\n  });\n  var unpatchDocument = patchDocument({\n    sandbox: sandbox,\n    speedy: speedySandbox\n  });\n  if (!mounting) calcAppCount(appName, 'increase', 'bootstrapping');\n  if (mounting) calcAppCount(appName, 'increase', 'mounting');\n  return function free() {\n    if (!mounting) calcAppCount(appName, 'decrease', 'bootstrapping');\n    if (mounting) calcAppCount(appName, 'decrease', 'mounting');\n    // release the overwritten prototype after all the micro apps unmounted\n    if (isAllAppsUnmounted()) {\n      unpatchDynamicAppendPrototypeFunctions();\n      unpatchDocument();\n    }\n    recordStyledComponentsCSSRules(dynamicStyleSheetElements);\n    // As now the sub app content all wrapped with a special id container,\n    // the dynamic style sheet would be removed automatically while unmoutting\n    return function rebuild() {\n      rebuildCSSRules(dynamicStyleSheetElements, function (stylesheetElement) {\n        var appWrapper = appWrapperGetter();\n        if (!appWrapper.contains(stylesheetElement)) {\n          var mountDom = stylesheetElement[styleElementTargetSymbol] === 'head' ? getAppWrapperHeadElement(appWrapper) : appWrapper;\n          var refNo = stylesheetElement[styleElementRefNodeNo];\n          if (typeof refNo === 'number' && refNo !== -1) {\n            // the reference node may be dynamic script comment which is not rebuilt while remounting thus reference node no longer exists\n            var refNode = mountDom.childNodes[refNo] || null;\n            rawHeadInsertBefore.call(mountDom, stylesheetElement, refNode);\n            return true;\n          } else {\n            rawHeadAppendChild.call(mountDom, stylesheetElement);\n            return true;\n          }\n        }\n        return false;\n      });\n    };\n  };\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E;AACA;AACA;AACA;AACA,SAASC,iBAAiB,EAAEC,UAAU,EAAEC,cAAc,EAAEC,YAAY,QAAQ,gBAAgB;AAC5F,SAASC,oBAAoB,QAAQ,cAAc;AACnD,SAASC,YAAY,EAAEC,wBAAwB,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,wCAAwC,EAAEC,eAAe,EAAEC,8BAA8B,EAAEC,qBAAqB,EAAEC,wBAAwB,QAAQ,UAAU;AACjP,IAAIC,qBAAqB,GAAGC,MAAM,CAAC,aAAa,CAAC;AACjD;AACAC,MAAM,CAACC,cAAc,CAACd,YAAY,EAAE,mCAAmC,EAAE;EACvEe,UAAU,EAAE,KAAK;EACjBC,QAAQ,EAAE;AACZ,CAAC,CAAC;AACFH,MAAM,CAACC,cAAc,CAACd,YAAY,EAAE,2BAA2B,EAAE;EAC/De,UAAU,EAAE,KAAK;EACjBC,QAAQ,EAAE,IAAI;EACdC,YAAY,EAAE;AAChB,CAAC,CAAC;AACF,IAAIC,kBAAkB,GAAGC,eAAe,CAACC,SAAS,CAACC,WAAW;AAC9D,IAAIC,mBAAmB,GAAGH,eAAe,CAACC,SAAS,CAACG,YAAY;AAChE;AACAvB,YAAY,CAACwB,iCAAiC,GAAGxB,YAAY,CAACwB,iCAAiC,IAAI,IAAIC,OAAO,CAAC,CAAC;AAChH,IAAIC,6BAA6B,GAAG1B,YAAY,CAACwB,iCAAiC;AAClF,IAAIG,+BAA+B,GAAG,IAAIF,OAAO,CAAC,CAAC;AACnD,IAAIG,mBAAmB,GAAG,IAAIH,OAAO,CAAC,CAAC;AACvC,IAAII,QAAQ,GAAG,IAAIJ,OAAO,CAAC,CAAC;AAC5B,SAASK,aAAaA,CAACC,GAAG,EAAE;EAC1B,IAAIC,OAAO,GAAGD,GAAG,CAACC,OAAO;IACvBC,MAAM,GAAGF,GAAG,CAACE,MAAM;EACrB,IAAIC,oBAAoB,GAAG,SAASA,oBAAoBA,CAACC,OAAO,EAAEC,KAAK,EAAE;IACvE,IAAIC,oBAAoB,GAAGX,6BAA6B,CAACY,GAAG,CAACF,KAAK,CAAC;IACnE,IAAIC,oBAAoB,EAAE;MACxBV,+BAA+B,CAACY,GAAG,CAACJ,OAAO,EAAEE,oBAAoB,CAAC;IACpE;EACF,CAAC;EACD,IAAIJ,MAAM,EAAE;IACV,IAAIO,aAAa,GAAG,CAAC,CAAC;IACtB,IAAIC,aAAa,GAAG,IAAIC,KAAK,CAACC,QAAQ,EAAE;MACtC;AACN;AACA;MACMJ,GAAG,EAAE,SAASA,GAAGA,CAACK,MAAM,EAAEC,CAAC,EAAEC,KAAK,EAAE;QAClC,QAAQD,CAAC;UACP,KAAK,eAAe;YAClB;cACEL,aAAa,CAACO,aAAa,GAAGD,KAAK;cACnC;YACF;UACF,KAAK,eAAe;YAClB;cACEN,aAAa,CAACQ,aAAa,GAAGF,KAAK;cACnC;YACF;UACF;YACEF,MAAM,CAACC,CAAC,CAAC,GAAGC,KAAK;YACjB;QACJ;QACA,OAAO,IAAI;MACb,CAAC;MACDR,GAAG,EAAE,SAASA,GAAGA,CAACM,MAAM,EAAEC,CAAC,EAAEI,QAAQ,EAAE;QACrC,QAAQJ,CAAC;UACP,KAAK,eAAe;YAClB;cACE;cACA,IAAIK,mBAAmB,GAAGV,aAAa,CAACO,aAAa,IAAIH,MAAM,CAACG,aAAa;cAC7E,OAAO,SAASA,aAAaA,CAAA,EAAG;gBAC9B,IAAI,CAAC/C,YAAY,CAACmD,yBAAyB,EAAE;kBAC3CnD,YAAY,CAACmD,yBAAyB,GAAGnB,OAAO,CAACoB,IAAI;gBACvD;gBACA,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;kBACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;gBAC9B;gBACA,IAAIvB,OAAO,GAAGe,mBAAmB,CAACS,IAAI,CAACC,KAAK,CAACV,mBAAmB,EAAE,CAACN,MAAM,CAAC,CAACiB,MAAM,CAACL,IAAI,CAAC,CAAC;gBACxF;gBACA,IAAIxD,YAAY,CAACmD,yBAAyB,KAAKnB,OAAO,CAACoB,IAAI,EAAE;kBAC3DlB,oBAAoB,CAACC,OAAO,EAAEH,OAAO,CAACI,KAAK,CAAC;kBAC5C,OAAOpC,YAAY,CAACmD,yBAAyB;gBAC/C;gBACA,OAAOhB,OAAO;cAChB,CAAC;YACH;UACF,KAAK,eAAe;YAClB;cACE,IAAI2B,mBAAmB,GAAGtB,aAAa,CAACQ,aAAa,IAAIJ,MAAM,CAACI,aAAa;cAC7E,OAAO,SAASA,aAAaA,CAAA,EAAG;gBAC9B,KAAK,IAAIe,KAAK,GAAGT,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACM,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;kBAC7FR,IAAI,CAACQ,KAAK,CAAC,GAAGV,SAAS,CAACU,KAAK,CAAC;gBAChC;gBACA,IAAIC,QAAQ,GAAGT,IAAI,CAAC,CAAC,CAAC;gBACtB,QAAQS,QAAQ;kBACd,KAAK,MAAM;oBACT;sBACE,IAAIC,eAAe,GAAGxC,6BAA6B,CAACY,GAAG,CAACN,OAAO,CAACI,KAAK,CAAC;sBACtE,IAAI8B,eAAe,EAAE;wBACnB,IAAIC,WAAW,GAAGhE,wBAAwB,CAAC+D,eAAe,CAACE,gBAAgB,CAAC,CAAC,CAAC;wBAC9ED,WAAW,CAAC9C,WAAW,GAAGF,eAAe,CAACC,SAAS,CAACC,WAAW;wBAC/D8C,WAAW,CAAC5C,YAAY,GAAGJ,eAAe,CAACC,SAAS,CAACG,YAAY;wBACjE4C,WAAW,CAACE,WAAW,GAAGlD,eAAe,CAACC,SAAS,CAACiD,WAAW;wBAC/D,OAAOF,WAAW;sBACpB;sBACA;oBACF;gBACJ;gBACA,OAAOL,mBAAmB,CAACH,IAAI,CAACC,KAAK,CAACE,mBAAmB,EAAE,CAAClB,MAAM,CAAC,CAACiB,MAAM,CAACL,IAAI,CAAC,CAAC;cACnF,CAAC;YACH;UACF;YACE;QACJ;QACA,IAAIV,KAAK,GAAGF,MAAM,CAACC,CAAC,CAAC;QACrB;QACA,IAAI/C,UAAU,CAACgD,KAAK,CAAC,IAAI,CAACjD,iBAAiB,CAACiD,KAAK,CAAC,EAAE;UAClD,OAAO,SAASwB,aAAaA,CAAA,EAAG;YAC9B,KAAK,IAAIC,KAAK,GAAGjB,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACc,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;cAC7FhB,IAAI,CAACgB,KAAK,CAAC,GAAGlB,SAAS,CAACkB,KAAK,CAAC;YAChC;YACA,OAAO1B,KAAK,CAACa,IAAI,CAACC,KAAK,CAACd,KAAK,EAAE,CAACF,MAAM,CAAC,CAACiB,MAAM,CAACjE,kBAAkB,CAAC4D,IAAI,CAACiB,GAAG,CAAC,UAAUC,GAAG,EAAE;cACxF,OAAOA,GAAG,KAAKzB,QAAQ,GAAGL,MAAM,GAAG8B,GAAG;YACxC,CAAC,CAAC,CAAC,CAAC,CAAC;UACP,CAAC;QACH;QACA,OAAO5B,KAAK;MACd;IACF,CAAC,CAAC;IACFd,OAAO,CAACF,aAAa,CAACW,aAAa,CAAC;IACpC;IACA;IACA,IAAIkC,+BAA+B,GAAGC,gBAAgB,CAACxD,SAAS,CAACyD,OAAO;IACxE,IAAI,CAAChD,QAAQ,CAACiD,GAAG,CAACH,+BAA+B,CAAC,EAAE;MAClD,IAAIE,OAAO,GAAG,SAASA,OAAOA,CAACjC,MAAM,EAAEmC,OAAO,EAAE;QAC9C,IAAIC,UAAU,GAAGpC,MAAM,YAAYqC,QAAQ,GAAGlF,cAAc,GAAG6C,MAAM;QACrE,OAAO+B,+BAA+B,CAAChB,IAAI,CAAC,IAAI,EAAEqB,UAAU,EAAED,OAAO,CAAC;MACxE,CAAC;MACDH,gBAAgB,CAACxD,SAAS,CAACyD,OAAO,GAAGA,OAAO;MAC5ChD,QAAQ,CAACU,GAAG,CAACoC,+BAA+B,EAAEE,OAAO,CAAC;IACxD;IACA;IACA,IAAIK,2BAA2B,GAAGC,IAAI,CAAC/D,SAAS,CAACgE,uBAAuB;IACxE,IAAI,CAACvD,QAAQ,CAACiD,GAAG,CAACI,2BAA2B,CAAC,EAAE;MAC9CC,IAAI,CAAC/D,SAAS,CAACgE,uBAAuB,GAAG,SAASA,uBAAuBA,CAACC,IAAI,EAAE;QAC9E,IAAIC,QAAQ,GAAGD,IAAI,YAAYJ,QAAQ,GAAGlF,cAAc,GAAGsF,IAAI;QAC/D,OAAOH,2BAA2B,CAACvB,IAAI,CAAC,IAAI,EAAE2B,QAAQ,CAAC;MACzD,CAAC;MACDzD,QAAQ,CAACU,GAAG,CAAC2C,2BAA2B,EAAEC,IAAI,CAAC/D,SAAS,CAACgE,uBAAuB,CAAC;IACnF;IACA;IACA;IACA,IAAIG,oBAAoB,GAAG1E,MAAM,CAAC2E,wBAAwB,CAACL,IAAI,CAAC/D,SAAS,EAAE,YAAY,CAAC;IACxF,IAAImE,oBAAoB,IAAI,CAAC1D,QAAQ,CAACiD,GAAG,CAACS,oBAAoB,CAAC,EAAE;MAC/D,IAAIE,gBAAgB,GAAGF,oBAAoB,CAACjD,GAAG;QAC7CrB,YAAY,GAAGsE,oBAAoB,CAACtE,YAAY;MAClD,IAAIwE,gBAAgB,IAAIxE,YAAY,EAAE;QACpC,IAAIyE,2BAA2B,GAAG/F,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4F,oBAAoB,CAAC,EAAE,CAAC,CAAC,EAAE;UAC3FjD,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;YAClB,IAAIqD,UAAU,GAAGF,gBAAgB,CAAC9B,IAAI,CAAC,IAAI,CAAC;YAC5C,IAAIgC,UAAU,YAAYV,QAAQ,EAAE;cAClC,IAAIW,qBAAqB;cACzB,IAAIxD,KAAK,GAAG,CAACwD,qBAAqB,GAAG3F,oBAAoB,CAAC,CAAC,MAAM,IAAI,IAAI2F,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACC,MAAM;cACjJ,IAAIzD,KAAK,EAAE;gBACT,OAAOA,KAAK,CAACO,QAAQ;cACvB;YACF;YACA,OAAOgD,UAAU;UACnB;QACF,CAAC,CAAC;QACF9E,MAAM,CAACC,cAAc,CAACqE,IAAI,CAAC/D,SAAS,EAAE,YAAY,EAAEsE,2BAA2B,CAAC;QAChF7D,QAAQ,CAACU,GAAG,CAACgD,oBAAoB,EAAEG,2BAA2B,CAAC;MACjE;IACF;IACA,OAAO,YAAY;MACjBd,gBAAgB,CAACxD,SAAS,CAACyD,OAAO,GAAGF,+BAA+B;MACpE9C,QAAQ,CAACiE,MAAM,CAACnB,+BAA+B,CAAC;MAChDQ,IAAI,CAAC/D,SAAS,CAACgE,uBAAuB,GAAGF,2BAA2B;MACpErD,QAAQ,CAACiE,MAAM,CAACZ,2BAA2B,CAAC;MAC5C,IAAIK,oBAAoB,EAAE;QACxB1E,MAAM,CAACC,cAAc,CAACqE,IAAI,CAAC/D,SAAS,EAAE,YAAY,EAAEmE,oBAAoB,CAAC;QACzE1D,QAAQ,CAACiE,MAAM,CAACP,oBAAoB,CAAC;MACvC;IACF,CAAC;EACH;EACA,IAAIQ,iCAAiC,GAAGnE,mBAAmB,CAACU,GAAG,CAACK,QAAQ,CAACI,aAAa,CAAC;EACvF,IAAI,CAACgD,iCAAiC,EAAE;IACtC,IAAIC,wBAAwB,GAAGrD,QAAQ,CAACI,aAAa;IACrDkC,QAAQ,CAAC7D,SAAS,CAAC2B,aAAa,GAAG,SAASA,aAAaA,CAACkD,OAAO,EAAElB,OAAO,EAAE;MAC1E,IAAI5C,OAAO,GAAG6D,wBAAwB,CAACrC,IAAI,CAAC,IAAI,EAAEsC,OAAO,EAAElB,OAAO,CAAC;MACnE,IAAI1E,cAAc,CAAC4F,OAAO,CAAC,EAAE;QAC3B,IAAIC,IAAI,GAAGjG,oBAAoB,CAAC,CAAC,IAAI,CAAC,CAAC;UACrCkG,0BAA0B,GAAGD,IAAI,CAACL,MAAM;QAC1C,IAAIM,0BAA0B,EAAE;UAC9BjE,oBAAoB,CAACC,OAAO,EAAEgE,0BAA0B,CAAC;QAC3D;MACF;MACA,OAAOhE,OAAO;IAChB,CAAC;IACD;IACA,IAAIQ,QAAQ,CAACyD,cAAc,CAAC,eAAe,CAAC,EAAE;MAC5CzD,QAAQ,CAACI,aAAa,GAAGkC,QAAQ,CAAC7D,SAAS,CAAC2B,aAAa;IAC3D;IACAnB,mBAAmB,CAACW,GAAG,CAAC0C,QAAQ,CAAC7D,SAAS,CAAC2B,aAAa,EAAEiD,wBAAwB,CAAC;EACrF;EACA,OAAO,SAASK,OAAOA,CAAA,EAAG;IACxB,IAAIN,iCAAiC,EAAE;MACrCd,QAAQ,CAAC7D,SAAS,CAAC2B,aAAa,GAAGgD,iCAAiC;MACpEpD,QAAQ,CAACI,aAAa,GAAGgD,iCAAiC;IAC5D;EACF,CAAC;AACH;AACA,OAAO,SAASO,kBAAkBA,CAACC,OAAO,EAAEnC,gBAAgB,EAAEpC,OAAO,EAAE;EACrE,IAAIwE,QAAQ,GAAGlD,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKmD,SAAS,GAAGnD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACvF,IAAIoD,SAAS,GAAGpD,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKmD,SAAS,GAAGnD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACzF,IAAIqD,kBAAkB,GAAGrD,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGmD,SAAS;EACxE,IAAIG,aAAa,GAAGtD,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKmD,SAAS,GAAGnD,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAC7F,IAAIlB,KAAK,GAAGJ,OAAO,CAACI,KAAK;EACzB,IAAI8B,eAAe,GAAGxC,6BAA6B,CAACY,GAAG,CAACF,KAAK,CAAC;EAC9D,IAAI,CAAC8B,eAAe,EAAE;IACpBA,eAAe,GAAG;MAChBqC,OAAO,EAAEA,OAAO;MAChBnE,KAAK,EAAEA,KAAK;MACZgC,gBAAgB,EAAEA,gBAAgB;MAClCyC,yBAAyB,EAAE,EAAE;MAC7BC,YAAY,EAAE,IAAI;MAClBF,aAAa,EAAEA,aAAa;MAC5BD,kBAAkB,EAAEA,kBAAkB;MACtCD,SAAS,EAAEA;IACb,CAAC;IACDhF,6BAA6B,CAACa,GAAG,CAACH,KAAK,EAAE8B,eAAe,CAAC;EAC3D;EACA;EACA,IAAI6C,gBAAgB,GAAG7C,eAAe;IACpC2C,yBAAyB,GAAGE,gBAAgB,CAACF,yBAAyB;EACxE,IAAIG,sCAAsC,GAAG1G,wCAAwC,CAAC,UAAU6B,OAAO,EAAE;IACvG,OAAOR,+BAA+B,CAACmD,GAAG,CAAC3C,OAAO,CAAC;EACrD,CAAC,EAAE,UAAUA,OAAO,EAAE;IACpB,OAAOR,+BAA+B,CAACW,GAAG,CAACH,OAAO,CAAC;EACrD,CAAC,CAAC;EACF,IAAI8E,eAAe,GAAGnF,aAAa,CAAC;IAClCE,OAAO,EAAEA,OAAO;IAChBC,MAAM,EAAE2E;EACV,CAAC,CAAC;EACF,IAAI,CAACJ,QAAQ,EAAEtG,YAAY,CAACqG,OAAO,EAAE,UAAU,EAAE,eAAe,CAAC;EACjE,IAAIC,QAAQ,EAAEtG,YAAY,CAACqG,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC;EAC3D,OAAO,SAASW,IAAIA,CAAA,EAAG;IACrB,IAAI,CAACV,QAAQ,EAAEtG,YAAY,CAACqG,OAAO,EAAE,UAAU,EAAE,eAAe,CAAC;IACjE,IAAIC,QAAQ,EAAEtG,YAAY,CAACqG,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC;IAC3D;IACA,IAAInG,kBAAkB,CAAC,CAAC,EAAE;MACxB4G,sCAAsC,CAAC,CAAC;MACxCC,eAAe,CAAC,CAAC;IACnB;IACAzG,8BAA8B,CAACqG,yBAAyB,CAAC;IACzD;IACA;IACA,OAAO,SAASM,OAAOA,CAAA,EAAG;MACxB5G,eAAe,CAACsG,yBAAyB,EAAE,UAAUO,iBAAiB,EAAE;QACtE,IAAIC,UAAU,GAAGjD,gBAAgB,CAAC,CAAC;QACnC,IAAI,CAACiD,UAAU,CAACC,QAAQ,CAACF,iBAAiB,CAAC,EAAE;UAC3C,IAAIG,QAAQ,GAAGH,iBAAiB,CAAC1G,wBAAwB,CAAC,KAAK,MAAM,GAAGP,wBAAwB,CAACkH,UAAU,CAAC,GAAGA,UAAU;UACzH,IAAIG,KAAK,GAAGJ,iBAAiB,CAAC3G,qBAAqB,CAAC;UACpD,IAAI,OAAO+G,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;YAC7C;YACA,IAAIC,OAAO,GAAGF,QAAQ,CAACG,UAAU,CAACF,KAAK,CAAC,IAAI,IAAI;YAChDlG,mBAAmB,CAACqC,IAAI,CAAC4D,QAAQ,EAAEH,iBAAiB,EAAEK,OAAO,CAAC;YAC9D,OAAO,IAAI;UACb,CAAC,MAAM;YACLvG,kBAAkB,CAACyC,IAAI,CAAC4D,QAAQ,EAAEH,iBAAiB,CAAC;YACpD,OAAO,IAAI;UACb;QACF;QACA,OAAO,KAAK;MACd,CAAC,CAAC;IACJ,CAAC;EACH,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}