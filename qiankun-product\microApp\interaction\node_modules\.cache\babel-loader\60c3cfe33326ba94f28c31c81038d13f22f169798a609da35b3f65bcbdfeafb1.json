{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, onBeforeUnmount, watch, onMounted, computed, nextTick } from 'vue';\nimport { format } from 'common/js/time.js';\nimport { Close } from '@element-plus/icons-vue';\nimport Hls from 'hls.js';\n// 使用 hls.js 播放 HLS 流\n\nvar __default__ = {\n  name: 'LiveBroadcastDetails'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    },\n    id: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['callback', 'update:modelValue'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var emit = __emit;\n    var details = ref({});\n    var logoSrc = ref('https://xazx.cszysoft.com:8131/lzt/pageImg/open/logo?areaId=610100');\n    var activeTab = ref('details');\n    var imgUrl = computed(function () {\n      return details.value.coverImg ? api.fileURL(details.value.coverImg) : '';\n    });\n    var countdownText = ref('');\n    var countdownTimeOnly = ref('');\n\n    // 视频播放器相关\n    var videoPlayer = ref(null);\n    var player = ref(null);\n    var hls = ref(null);\n    var isPlayerInitialized = ref(false);\n\n    // 播放器控制状态\n    var isPlaying = ref(false);\n    var volume = ref(1);\n    var volumeIcon = computed(function () {\n      if (volume.value === 0) return '🔇';\n      if (volume.value < 0.5) return '🔈';\n      return '🔊';\n    });\n    var progressPercent = ref(0);\n    var timeDisplay = ref('00:00 / 加载中');\n    var isFullscreen = ref(false);\n    var qualityLevels = ref([]);\n    var selectedQuality = ref('');\n    var statusMessage = ref('HLS视频流播放器 | 准备连接视频源...');\n    var errorMessage = ref('');\n    onMounted(function () {\n      // 在 onMounted 中，如果组件已经显示且有 id，则加载数据\n      if (props.modelValue && props.id) {\n        getInfo();\n      }\n    });\n\n    // 监听 modelValue 变化 - 当组件从隐藏变为显示时加载数据\n    watch(function () {\n      return props.modelValue;\n    }, function (newVal, oldVal) {\n      if (newVal && props.id && !oldVal) {\n        // 只有从 false 变为 true 且有 id 时才加载数据\n        getInfo();\n      }\n    });\n\n    // 监听 id 变化 - 当 id 改变且组件显示时加载数据\n    watch(function () {\n      return props.id;\n    }, function (newVal, oldVal) {\n      if (newVal && props.modelValue && newVal !== oldVal) {\n        getInfo();\n      }\n    });\n    var getInfo = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.videoConnectionInfo({\n                detailId: props.id\n              });\n            case 2:\n              res = _context.sent;\n              data = res.data;\n              details.value = data;\n              applyStatusFromProps();\n            case 6:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function getInfo() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n\n    // 监听状态变化\n    watch(function () {\n      return details.value.meetingStatus;\n    }, function (newStatus, oldStatus) {\n      if (newStatus === '进行中' && oldStatus !== '进行中') {\n        // 从未开始变为进行中，初始化播放器\n        nextTick(function () {\n          initVideoPlayer();\n        });\n      } else if (newStatus !== '进行中' && oldStatus === '进行中') {\n        // 从进行中变为其他状态，销毁播放器\n        destroyVideoPlayer();\n      }\n    });\n    var tickCountdown = function tickCountdown() {\n      if (!details.value.startTime) return;\n      var start = new Date(details.value.startTime).getTime();\n      var now = Date.now();\n      var diff = Math.max(0, start - now);\n      var sec = Math.floor(diff / 1000) % 60;\n      var min = Math.floor(diff / (1000 * 60)) % 60;\n      var hour = Math.floor(diff / (1000 * 60 * 60)) % 24;\n      var day = Math.floor(diff / (1000 * 60 * 60 * 24));\n      var timeText = '';\n      if (day > 0) {\n        // 大于1天：显示 X天X时X分\n        timeText = `${day} 天 ${hour} 时 ${min} 分`;\n      } else if (hour > 0) {\n        // 大于1小时但小于1天：显示 X时X分X秒\n        timeText = `${hour} 时 ${min} 分 ${sec} 秒`;\n      } else if (min > 0) {\n        // 大于1分钟但小于1小时：显示 X分X秒\n        timeText = `${min} 分 ${sec} 秒`;\n      } else {\n        // 小于1分钟：显示 X秒\n        timeText = `${sec} 秒`;\n      }\n\n      // 设置完整的倒计时文本（保留原有逻辑）\n      countdownText.value = `距离 ${format(details.value.startTime, 'MM/DD HH:mm')} 直播开始还有 ${timeText}`;\n      // 设置只包含时间的文本（用于两行显示）\n      countdownTimeOnly.value = timeText;\n    };\n    var timer = null;\n    var applyStatusFromProps = function applyStatusFromProps() {\n      if (timer) clearInterval(timer);\n      if (details.value.meetingStatus === '未开始') {\n        tickCountdown();\n        timer = setInterval(tickCountdown, 1000);\n      } else if (details.value.meetingStatus === '进行中') {\n        // 初始化播放器\n        nextTick(function () {\n          initVideoPlayer();\n        });\n      }\n    };\n\n    // 初始化视频播放器\n    var initVideoPlayer = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var video, hlsUrl, isAccessible;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              if (!(isPlayerInitialized.value || !videoContainer.value)) {\n                _context2.next = 2;\n                break;\n              }\n              return _context2.abrupt(\"return\");\n            case 2:\n              console.log('初始化视频播放器');\n              console.log('推流地址:', details.value.liveUrl);\n\n              // 销毁现有播放器\n              destroyVideoPlayer();\n\n              // 创建原生 video 元素\n              video = document.createElement('video');\n              video.className = 'live-video-player';\n              video.controls = true;\n              video.autoplay = true;\n              video.preload = 'auto';\n              video.playsInline = true;\n              video.webkitPlaysinline = true;\n              video.style.width = '100%';\n              video.style.height = '100%';\n              video.style.objectFit = 'contain';\n\n              // 添加到容器\n              videoContainer.value.appendChild(video);\n              player.value = video;\n              isPlayerInitialized.value = true;\n\n              // 设置直播流地址（使用HLS格式）\n              if (!details.value.liveUrl) {\n                _context2.next = 32;\n                break;\n              }\n              hlsUrl = getHlsUrl(details.value.liveUrl);\n              console.log('解析后的HLS地址:', hlsUrl);\n              if (!hlsUrl) {\n                _context2.next = 30;\n                break;\n              }\n              _context2.next = 24;\n              return testHlsUrl(hlsUrl);\n            case 24:\n              isAccessible = _context2.sent;\n              if (isAccessible) {\n                _context2.next = 29;\n                break;\n              }\n              console.error('HLS地址无法访问，可能是CORS问题或地址无效');\n              alert('推流地址无法访问，请检查网络连接或联系管理员');\n              return _context2.abrupt(\"return\");\n            case 29:\n              // 检查浏览器是否支持HLS\n              if (Hls.isSupported()) {\n                // 使用 hls.js 播放\n                hls.value = new Hls({\n                  debug: false,\n                  enableWorker: true,\n                  lowLatencyMode: true,\n                  backBufferLength: 90\n                });\n                hls.value.loadSource(hlsUrl);\n                hls.value.attachMedia(video);\n                hls.value.on(Hls.Events.MANIFEST_PARSED, function () {\n                  console.log('HLS清单解析完成，开始播放');\n                  video.play().catch(function (e) {\n                    console.error('自动播放失败:', e);\n                  });\n                });\n                hls.value.on(Hls.Events.ERROR, function (event, data) {\n                  console.error('HLS错误:', data);\n                  if (data.fatal) {\n                    switch (data.type) {\n                      case Hls.ErrorTypes.NETWORK_ERROR:\n                        console.error('网络错误，尝试恢复...');\n                        hls.value.startLoad();\n                        break;\n                      case Hls.ErrorTypes.MEDIA_ERROR:\n                        console.error('媒体错误，尝试恢复...');\n                        hls.value.recoverMediaError();\n                        break;\n                      default:\n                        console.error('致命错误，无法恢复');\n                        break;\n                    }\n                  }\n                });\n              } else if (video.canPlayType('application/vnd.apple.mpegurl')) {\n                // 原生支持HLS（Safari）\n                video.src = hlsUrl;\n                video.addEventListener('loadedmetadata', function () {\n                  console.log('原生HLS支持，开始播放');\n                  video.play().catch(function (e) {\n                    console.error('自动播放失败:', e);\n                  });\n                });\n              } else {\n                console.error('浏览器不支持HLS播放');\n                alert('您的浏览器不支持HLS播放，请使用Chrome、Firefox或Safari');\n              }\n            case 30:\n              _context2.next = 33;\n              break;\n            case 32:\n              console.warn('没有推流地址');\n            case 33:\n              // 添加错误处理\n              video.addEventListener('error', function (error) {\n                console.error('视频播放错误:', error);\n              });\n\n              // 添加加载事件\n              video.addEventListener('loadeddata', function () {\n                console.log('视频数据加载完成');\n              });\n\n              // 添加自定义控制按钮\n              addCustomControls(video);\n            case 36:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function initVideoPlayer() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n\n    // 销毁视频播放器\n    var destroyVideoPlayer = function destroyVideoPlayer() {\n      // 销毁 hls.js 实例\n      if (hls.value) {\n        try {\n          hls.value.destroy();\n        } catch (error) {\n          console.error('销毁HLS实例错误:', error);\n        }\n        hls.value = null;\n      }\n      if (player.value) {\n        try {\n          player.value.pause();\n          player.value.src = '';\n          player.value.load();\n          if (videoContainer.value) {\n            videoContainer.value.innerHTML = '';\n          }\n        } catch (error) {\n          console.error('销毁播放器错误:', error);\n        }\n        player.value = null;\n        isPlayerInitialized.value = false;\n      }\n    };\n\n    // 从推流地址中获取HLS地址\n    var getHlsUrl = function getHlsUrl(liveUrl) {\n      console.log('原始推流地址:', liveUrl);\n\n      // 如果liveUrl是JSON格式，解析出HLS地址\n      try {\n        var urlData = JSON.parse(liveUrl);\n        console.log('解析的JSON数据:', urlData);\n        var hlsUrl = urlData.hls || liveUrl;\n        console.log('提取的HLS地址:', hlsUrl);\n        return hlsUrl;\n      } catch (error) {\n        console.log('不是JSON格式，直接使用原地址');\n        // 如果不是JSON格式，直接返回原地址\n        return liveUrl;\n      }\n    };\n\n    // 测试HLS地址是否可访问\n    var testHlsUrl = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3(url) {\n        var response;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.prev = 0;\n              console.log('测试HLS地址可访问性:', url);\n              _context3.next = 4;\n              return fetch(url, {\n                method: 'HEAD',\n                mode: 'cors'\n              });\n            case 4:\n              response = _context3.sent;\n              console.log('HLS地址响应状态:', response.status);\n              return _context3.abrupt(\"return\", response.ok);\n            case 9:\n              _context3.prev = 9;\n              _context3.t0 = _context3[\"catch\"](0);\n              console.error('HLS地址测试失败:', _context3.t0);\n              return _context3.abrupt(\"return\", false);\n            case 13:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3, null, [[0, 9]]);\n      }));\n      return function testHlsUrl(_x) {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n\n    // 示例推流地址解析\n    // 如果您的推流地址是字符串格式，可以这样处理：\n    // const sampleUrl = {\n    //   \"flv\": \"http://prdpulllive.xylink.com/prdnemo/9681c99c9088f6520198def4ad835a39.flv?auth_key=...\",\n    //   \"hls\": \"http://prdpulllive.xylink.com/prdnemo/9681c99c9088f6520198def4ad835a39.m3u8?auth_key=...\",\n    //   \"rtmp\": \"rtmp://prdpulllive.xylink.com/prdnemo/9681c99c9088f6520198def4ad835a39?auth_key=...\"\n    // }\n\n    // 添加自定义控制按钮\n    var addCustomControls = function addCustomControls(video) {\n      // 创建自定义控制栏\n      var controlsContainer = document.createElement('div');\n      controlsContainer.className = 'custom-video-controls';\n      controlsContainer.style.cssText = `\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    background: rgba(0, 0, 0, 0.7);\n    padding: 10px;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    color: white;\n    font-size: 14px;\n  `;\n\n      // 左侧控制按钮\n      var leftControls = document.createElement('div');\n      leftControls.style.display = 'flex';\n      leftControls.style.alignItems = 'center';\n      leftControls.style.gap = '15px';\n\n      // 播放/暂停按钮\n      var playBtn = document.createElement('button');\n      playBtn.innerHTML = '▶';\n      playBtn.style.cssText = 'background: none; border: none; color: white; cursor: pointer; font-size: 16px;';\n      playBtn.onclick = function () {\n        if (video.paused) {\n          video.play();\n          playBtn.innerHTML = '⏸';\n        } else {\n          video.pause();\n          playBtn.innerHTML = '▶';\n        }\n      };\n\n      // 刷新按钮\n      var refreshBtn = document.createElement('button');\n      refreshBtn.innerHTML = '🔄';\n      refreshBtn.style.cssText = 'background: none; border: none; color: white; cursor: pointer; font-size: 16px;';\n      refreshBtn.onclick = function () {\n        video.currentTime = 0;\n        video.play();\n      };\n      leftControls.appendChild(playBtn);\n      leftControls.appendChild(refreshBtn);\n\n      // 右侧控制按钮\n      var rightControls = document.createElement('div');\n      rightControls.style.display = 'flex';\n      rightControls.style.alignItems = 'center';\n      rightControls.style.gap = '15px';\n\n      // 高清按钮\n      var hdBtn = document.createElement('button');\n      hdBtn.innerHTML = '高清';\n      hdBtn.style.cssText = 'background: none; border: none; color: white; cursor: pointer; font-size: 14px;';\n      hdBtn.onclick = function () {\n        console.log('高清设置');\n      };\n\n      // 声音按钮\n      var volumeBtn = document.createElement('button');\n      volumeBtn.innerHTML = '🔊';\n      volumeBtn.style.cssText = 'background: none; border: none; color: white; cursor: pointer; font-size: 16px;';\n      volumeBtn.onclick = function () {\n        video.muted = !video.muted;\n        volumeBtn.innerHTML = video.muted ? '🔇' : '🔊';\n      };\n\n      // 弹幕按钮\n      var danmakuBtn = document.createElement('button');\n      danmakuBtn.innerHTML = '弹幕';\n      danmakuBtn.style.cssText = 'background: none; border: none; color: white; cursor: pointer; font-size: 14px;';\n      danmakuBtn.onclick = function () {\n        console.log('弹幕功能');\n      };\n\n      // 全屏按钮\n      var fullscreenBtn = document.createElement('button');\n      fullscreenBtn.innerHTML = '⛶';\n      fullscreenBtn.style.cssText = 'background: none; border: none; color: white; cursor: pointer; font-size: 16px;';\n      fullscreenBtn.onclick = function () {\n        if (document.fullscreenElement) {\n          document.exitFullscreen();\n        } else {\n          videoContainer.value.requestFullscreen();\n        }\n      };\n      rightControls.appendChild(hdBtn);\n      rightControls.appendChild(volumeBtn);\n      rightControls.appendChild(danmakuBtn);\n      rightControls.appendChild(fullscreenBtn);\n      controlsContainer.appendChild(leftControls);\n      controlsContainer.appendChild(rightControls);\n\n      // 添加到视频容器\n      videoContainer.value.appendChild(controlsContainer);\n\n      // 隐藏原生控制栏\n      video.controls = false;\n    };\n    onBeforeUnmount(function () {\n      if (timer) clearInterval(timer);\n      destroyVideoPlayer();\n    });\n    var handleClose = function handleClose() {\n      emit('update:modelValue', false);\n      emit('callback');\n    };\n    var __returned__ = {\n      props,\n      emit,\n      details,\n      logoSrc,\n      activeTab,\n      imgUrl,\n      countdownText,\n      countdownTimeOnly,\n      videoPlayer,\n      player,\n      hls,\n      isPlayerInitialized,\n      isPlaying,\n      volume,\n      volumeIcon,\n      progressPercent,\n      timeDisplay,\n      isFullscreen,\n      qualityLevels,\n      selectedQuality,\n      statusMessage,\n      errorMessage,\n      getInfo,\n      tickCountdown,\n      get timer() {\n        return timer;\n      },\n      set timer(v) {\n        timer = v;\n      },\n      applyStatusFromProps,\n      initVideoPlayer,\n      destroyVideoPlayer,\n      getHlsUrl,\n      testHlsUrl,\n      addCustomControls,\n      handleClose,\n      get api() {\n        return api;\n      },\n      ref,\n      onBeforeUnmount,\n      watch,\n      onMounted,\n      computed,\n      nextTick,\n      get format() {\n        return format;\n      },\n      get Close() {\n        return Close;\n      },\n      get Hls() {\n        return Hls;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "onBeforeUnmount", "watch", "onMounted", "computed", "nextTick", "format", "Close", "Hls", "__default__", "props", "__props", "emit", "__emit", "details", "logoSrc", "activeTab", "imgUrl", "coverImg", "fileURL", "countdownText", "countdownTimeOnly", "videoPlayer", "player", "hls", "isPlayerInitialized", "isPlaying", "volume", "volumeIcon", "progressPercent", "timeDisplay", "isFullscreen", "qualityLevels", "selectedQuality", "statusMessage", "errorMessage", "modelValue", "id", "getInfo", "newVal", "oldVal", "_ref2", "_callee", "res", "data", "_callee$", "_context", "videoConnectionInfo", "detailId", "applyStatusFromProps", "meetingStatus", "newStatus", "oldStatus", "initVideoPlayer", "destroyVideoPlayer", "tickCountdown", "startTime", "start", "Date", "getTime", "now", "diff", "Math", "max", "sec", "floor", "min", "hour", "day", "timeText", "timer", "clearInterval", "setInterval", "_ref3", "_callee2", "video", "hlsUrl", "isAccessible", "_callee2$", "_context2", "videoContainer", "console", "log", "liveUrl", "document", "createElement", "className", "controls", "autoplay", "preload", "playsInline", "webkitPlaysinline", "style", "width", "height", "objectFit", "append<PERSON><PERSON><PERSON>", "getHlsUrl", "testHlsUrl", "error", "alert", "isSupported", "debug", "enableWorker", "lowLatencyMode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadSource", "attachMedia", "on", "Events", "MANIFEST_PARSED", "play", "ERROR", "event", "fatal", "ErrorTypes", "NETWORK_ERROR", "startLoad", "MEDIA_ERROR", "recoverMediaError", "canPlayType", "src", "addEventListener", "warn", "addCustomControls", "destroy", "pause", "load", "innerHTML", "urlData", "JSON", "parse", "_ref4", "_callee3", "url", "response", "_callee3$", "_context3", "fetch", "mode", "status", "ok", "t0", "_x", "controlsContainer", "cssText", "leftControls", "display", "alignItems", "gap", "playBtn", "onclick", "paused", "refreshBtn", "currentTime", "rightControls", "hdBtn", "volumeBtn", "muted", "danmakuBtn", "fullscreenBtn", "fullscreenElement", "exitFullscreen", "requestFullscreen", "handleClose"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/interaction/src/views/LiveManagement/LiveBroadcastDetails.vue"], "sourcesContent": ["<template>\r\n  <transition name=\"details-fade\">\r\n    <div v-show=\"modelValue\" class=\"LiveBroadcastDetails\">\r\n      <div class=\"LiveBroadcastDetailsHeader\">\r\n        <img v-if=\"logoSrc\" class=\"LiveBroadcastDetailsEmblem\" :src=\"logoSrc\" alt=\"emblem\" />\r\n        <div class=\"LiveBroadcastDetailsHeadInfo\">\r\n          <div class=\"LiveBroadcastDetailsTitle\">{{ details.theme }}</div>\r\n          <div class=\"LiveBroadcastDetailsTime\">直播时间：{{ format(details.startTime) }} 到 {{\r\n            format(details.endTime) }}</div>\r\n        </div>\r\n        <el-icon class=\"LiveBroadcastDetailsClose\" @click=\"handleClose\">\r\n          <Close />\r\n        </el-icon>\r\n      </div>\r\n      <div class=\"LiveBroadcastDetailsBody\">\r\n        <div class=\"LiveBroadcastDetailsCanvas\">\r\n          <!-- 未开始：显示封面图 + 倒计时 -->\r\n          <div v-if=\"details.meetingStatus === '未开始'\" class=\"LiveBroadcastDetailsPoster\">\r\n            <img :src=\"imgUrl\" alt=\"海报/播放画面区域\" style=\"width: 100%;height: 100%;object-fit: contain;\">\r\n            <div class=\"LiveBroadcastDetailsCountdown\">\r\n              <div class=\"countdown-line1\">距离 {{ format(details.startTime, 'MM/DD HH:mm') }} 直播开始还有</div>\r\n              <div class=\"countdown-line2\">{{ countdownTimeOnly }}</div>\r\n            </div>\r\n          </div>\r\n          <!-- 进行中：直播播放器 -->\r\n          <div v-else-if=\"details.meetingStatus === '进行中'\" class=\"LiveBroadcastDetailsLiveOverlay\">\r\n            <div class=\"player-container\">\r\n              <video ref=\"videoPlayer\" id=\"video-player\" controls></video>\r\n              <div class=\"controls\">\r\n                <button class=\"control-btn\" @click=\"togglePlayPause\">{{ isPlaying ? '⏸ 暂停' : '▶ 播放' }}</button>\r\n                <div class=\"volume-control\">\r\n                  <span class=\"volume-icon\">{{ volumeIcon }}</span>\r\n                  <input type=\"range\" min=\"0\" max=\"1\" step=\"0.05\" v-model=\"volume\" @input=\"updateVolume\"\r\n                    class=\"volume-slider\">\r\n                </div>\r\n                <div class=\"progress-container\" @click=\"seekVideo\">\r\n                  <div class=\"progress-bar\" :style=\"{ width: progressPercent + '%' }\"></div>\r\n                </div>\r\n                <span class=\"time-display\">{{ timeDisplay }}</span>\r\n                <select class=\"quality-selector\" v-model=\"selectedQuality\" @change=\"changeQuality\"\r\n                  :disabled=\"!qualityLevels.length\">\r\n                  <option value=\"\">选择清晰度</option>\r\n                  <option v-for=\"(level, index) in qualityLevels\" :key=\"index\" :value=\"index\">\r\n                    {{ level.height }}p ({{ Math.round(level.bitrate / 1000) }}kbps)\r\n                  </option>\r\n                </select>\r\n                <button class=\"control-btn fullscreen-btn\" @click=\"toggleFullscreen\">\r\n                  {{ isFullscreen ? '⮿ 退出全屏' : '⛶ 全屏' }}\r\n                </button>\r\n              </div>\r\n            </div>\r\n            <div class=\"status-panel\">\r\n              <div class=\"status-message\">{{ statusMessage }}</div>\r\n              <div class=\"error-message\" v-if=\"errorMessage\" :style=\"{ display: errorMessage ? 'block' : 'none' }\">\r\n                {{ errorMessage }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <!-- 已结束：遮罩 + 回放按钮 -->\r\n          <div v-else-if=\"details.meetingStatus === '已结束'\" class=\"LiveBroadcastDetailsEnded\">\r\n            <div class=\"LiveBroadcastDetailsEndedWrap\">\r\n              <div class=\"endedTitle\">直播已结束</div>\r\n              <el-button type=\"primary\" class=\"replayBtn\" v-if=\"details.isReplay == 1\">观看回放</el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"LiveBroadcastDetailsSidebar\">\r\n          <div class=\"LiveBroadcastDetailsTabs\">\r\n            <div :class=\"['LiveBroadcastDetailsTab', { active: activeTab === 'details' }]\"\r\n              @click=\"activeTab = 'details'\">\r\n              直播详情</div>\r\n            <div :class=\"['LiveBroadcastDetailsTab', { active: activeTab === 'interact' }]\"\r\n              @click=\"activeTab = 'interact'\">\r\n              互动</div>\r\n          </div>\r\n          <div class=\"LiveBroadcastDetailsTabPane\" v-if=\"activeTab === 'details'\">\r\n            <div class=\"detailsTitle\">{{ details?.theme || '-' }}</div>\r\n            <div class=\"detailsTime\">时间：{{ format(details?.startTime) }} - {{ format(details?.endTime) }}</div>\r\n            <div class=\"detailsDesc\">{{ details.liveDescribes || '暂无简介' }}</div>\r\n          </div>\r\n          <div class=\"LiveBroadcastDetailsTabPane\" v-else>\r\n            <div class=\"LiveBroadcastDetailsPanelTitle\">互动</div>\r\n            <div class=\"LiveBroadcastDetailsPanelText\">暂无互动内容</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </transition>\r\n\r\n</template>\r\n<script>\r\nexport default { name: 'LiveBroadcastDetails' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onBeforeUnmount, watch, onMounted, computed, nextTick } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { Close } from '@element-plus/icons-vue'\r\nimport Hls from 'hls.js'\r\n// 使用 hls.js 播放 HLS 流\r\nconst props = defineProps({\r\n  modelValue: { type: Boolean, default: false },\r\n  id: { type: String, default: '' }\r\n})\r\nconst emit = defineEmits(['callback', 'update:modelValue'])\r\nconst details = ref({})\r\nconst logoSrc = ref('https://xazx.cszysoft.com:8131/lzt/pageImg/open/logo?areaId=610100')\r\n\r\nconst activeTab = ref('details')\r\nconst imgUrl = computed(() => details.value.coverImg ? api.fileURL(details.value.coverImg) : '')\r\nconst countdownText = ref('')\r\nconst countdownTimeOnly = ref('')\r\n\r\n// 视频播放器相关\r\nconst videoPlayer = ref(null)\r\nconst player = ref(null)\r\nconst hls = ref(null)\r\nconst isPlayerInitialized = ref(false)\r\n\r\n// 播放器控制状态\r\nconst isPlaying = ref(false)\r\nconst volume = ref(1)\r\nconst volumeIcon = computed(() => {\r\n  if (volume.value === 0) return '🔇'\r\n  if (volume.value < 0.5) return '🔈'\r\n  return '🔊'\r\n})\r\nconst progressPercent = ref(0)\r\nconst timeDisplay = ref('00:00 / 加载中')\r\nconst isFullscreen = ref(false)\r\nconst qualityLevels = ref([])\r\nconst selectedQuality = ref('')\r\nconst statusMessage = ref('HLS视频流播放器 | 准备连接视频源...')\r\nconst errorMessage = ref('')\r\n\r\nonMounted(() => {\r\n  // 在 onMounted 中，如果组件已经显示且有 id，则加载数据\r\n  if (props.modelValue && props.id) {\r\n    getInfo()\r\n  }\r\n})\r\n\r\n// 监听 modelValue 变化 - 当组件从隐藏变为显示时加载数据\r\nwatch(() => props.modelValue, (newVal, oldVal) => {\r\n  if (newVal && props.id && !oldVal) {\r\n    // 只有从 false 变为 true 且有 id 时才加载数据\r\n    getInfo()\r\n  }\r\n})\r\n\r\n// 监听 id 变化 - 当 id 改变且组件显示时加载数据\r\nwatch(() => props.id, (newVal, oldVal) => {\r\n  if (newVal && props.modelValue && newVal !== oldVal) {\r\n    getInfo()\r\n  }\r\n})\r\n\r\nconst getInfo = async () => {\r\n  const res = await api.videoConnectionInfo({ detailId: props.id })\r\n  var { data } = res\r\n  details.value = data\r\n  applyStatusFromProps()\r\n}\r\n\r\n// 监听状态变化\r\nwatch(() => details.value.meetingStatus, (newStatus, oldStatus) => {\r\n  if (newStatus === '进行中' && oldStatus !== '进行中') {\r\n    // 从未开始变为进行中，初始化播放器\r\n    nextTick(() => {\r\n      initVideoPlayer()\r\n    })\r\n  } else if (newStatus !== '进行中' && oldStatus === '进行中') {\r\n    // 从进行中变为其他状态，销毁播放器\r\n    destroyVideoPlayer()\r\n  }\r\n})\r\n\r\nconst tickCountdown = () => {\r\n  if (!details.value.startTime) return\r\n  const start = new Date(details.value.startTime).getTime()\r\n  const now = Date.now()\r\n  let diff = Math.max(0, start - now)\r\n\r\n  const sec = Math.floor(diff / 1000) % 60\r\n  const min = Math.floor(diff / (1000 * 60)) % 60\r\n  const hour = Math.floor(diff / (1000 * 60 * 60)) % 24\r\n  const day = Math.floor(diff / (1000 * 60 * 60 * 24))\r\n\r\n  let timeText = ''\r\n\r\n  if (day > 0) {\r\n    // 大于1天：显示 X天X时X分\r\n    timeText = `${day} 天 ${hour} 时 ${min} 分`\r\n  } else if (hour > 0) {\r\n    // 大于1小时但小于1天：显示 X时X分X秒\r\n    timeText = `${hour} 时 ${min} 分 ${sec} 秒`\r\n  } else if (min > 0) {\r\n    // 大于1分钟但小于1小时：显示 X分X秒\r\n    timeText = `${min} 分 ${sec} 秒`\r\n  } else {\r\n    // 小于1分钟：显示 X秒\r\n    timeText = `${sec} 秒`\r\n  }\r\n\r\n  // 设置完整的倒计时文本（保留原有逻辑）\r\n  countdownText.value = `距离 ${format(details.value.startTime, 'MM/DD HH:mm')} 直播开始还有 ${timeText}`\r\n  // 设置只包含时间的文本（用于两行显示）\r\n  countdownTimeOnly.value = timeText\r\n}\r\n\r\nlet timer = null\r\nconst applyStatusFromProps = () => {\r\n  if (timer) clearInterval(timer)\r\n  if (details.value.meetingStatus === '未开始') {\r\n    tickCountdown()\r\n    timer = setInterval(tickCountdown, 1000)\r\n  } else if (details.value.meetingStatus === '进行中') {\r\n    // 初始化播放器\r\n    nextTick(() => {\r\n      initVideoPlayer()\r\n    })\r\n  }\r\n}\r\n\r\n// 初始化视频播放器\r\nconst initVideoPlayer = async () => {\r\n  if (isPlayerInitialized.value || !videoContainer.value) return\r\n\r\n  console.log('初始化视频播放器')\r\n  console.log('推流地址:', details.value.liveUrl)\r\n\r\n  // 销毁现有播放器\r\n  destroyVideoPlayer()\r\n\r\n  // 创建原生 video 元素\r\n  const video = document.createElement('video')\r\n  video.className = 'live-video-player'\r\n  video.controls = true\r\n  video.autoplay = true\r\n  video.preload = 'auto'\r\n  video.playsInline = true\r\n  video.webkitPlaysinline = true\r\n  video.style.width = '100%'\r\n  video.style.height = '100%'\r\n  video.style.objectFit = 'contain'\r\n\r\n  // 添加到容器\r\n  videoContainer.value.appendChild(video)\r\n  player.value = video\r\n  isPlayerInitialized.value = true\r\n\r\n  // 设置直播流地址（使用HLS格式）\r\n  if (details.value.liveUrl) {\r\n    const hlsUrl = getHlsUrl(details.value.liveUrl)\r\n    console.log('解析后的HLS地址:', hlsUrl)\r\n\r\n    if (hlsUrl) {\r\n      // 测试HLS地址可访问性\r\n      const isAccessible = await testHlsUrl(hlsUrl)\r\n      if (!isAccessible) {\r\n        console.error('HLS地址无法访问，可能是CORS问题或地址无效')\r\n        alert('推流地址无法访问，请检查网络连接或联系管理员')\r\n        return\r\n      }\r\n\r\n      // 检查浏览器是否支持HLS\r\n      if (Hls.isSupported()) {\r\n        // 使用 hls.js 播放\r\n        hls.value = new Hls({\r\n          debug: false,\r\n          enableWorker: true,\r\n          lowLatencyMode: true,\r\n          backBufferLength: 90\r\n        })\r\n\r\n        hls.value.loadSource(hlsUrl)\r\n        hls.value.attachMedia(video)\r\n\r\n        hls.value.on(Hls.Events.MANIFEST_PARSED, () => {\r\n          console.log('HLS清单解析完成，开始播放')\r\n          video.play().catch(e => {\r\n            console.error('自动播放失败:', e)\r\n          })\r\n        })\r\n\r\n        hls.value.on(Hls.Events.ERROR, (event, data) => {\r\n          console.error('HLS错误:', data)\r\n          if (data.fatal) {\r\n            switch (data.type) {\r\n              case Hls.ErrorTypes.NETWORK_ERROR:\r\n                console.error('网络错误，尝试恢复...')\r\n                hls.value.startLoad()\r\n                break\r\n              case Hls.ErrorTypes.MEDIA_ERROR:\r\n                console.error('媒体错误，尝试恢复...')\r\n                hls.value.recoverMediaError()\r\n                break\r\n              default:\r\n                console.error('致命错误，无法恢复')\r\n                break\r\n            }\r\n          }\r\n        })\r\n      } else if (video.canPlayType('application/vnd.apple.mpegurl')) {\r\n        // 原生支持HLS（Safari）\r\n        video.src = hlsUrl\r\n        video.addEventListener('loadedmetadata', () => {\r\n          console.log('原生HLS支持，开始播放')\r\n          video.play().catch(e => {\r\n            console.error('自动播放失败:', e)\r\n          })\r\n        })\r\n      } else {\r\n        console.error('浏览器不支持HLS播放')\r\n        alert('您的浏览器不支持HLS播放，请使用Chrome、Firefox或Safari')\r\n      }\r\n    }\r\n  } else {\r\n    console.warn('没有推流地址')\r\n  }\r\n\r\n  // 添加错误处理\r\n  video.addEventListener('error', (error) => {\r\n    console.error('视频播放错误:', error)\r\n  })\r\n\r\n  // 添加加载事件\r\n  video.addEventListener('loadeddata', () => {\r\n    console.log('视频数据加载完成')\r\n  })\r\n\r\n  // 添加自定义控制按钮\r\n  addCustomControls(video)\r\n}\r\n\r\n// 销毁视频播放器\r\nconst destroyVideoPlayer = () => {\r\n  // 销毁 hls.js 实例\r\n  if (hls.value) {\r\n    try {\r\n      hls.value.destroy()\r\n    } catch (error) {\r\n      console.error('销毁HLS实例错误:', error)\r\n    }\r\n    hls.value = null\r\n  }\r\n\r\n  if (player.value) {\r\n    try {\r\n      player.value.pause()\r\n      player.value.src = ''\r\n      player.value.load()\r\n      if (videoContainer.value) {\r\n        videoContainer.value.innerHTML = ''\r\n      }\r\n    } catch (error) {\r\n      console.error('销毁播放器错误:', error)\r\n    }\r\n    player.value = null\r\n    isPlayerInitialized.value = false\r\n  }\r\n}\r\n\r\n// 从推流地址中获取HLS地址\r\nconst getHlsUrl = (liveUrl) => {\r\n  console.log('原始推流地址:', liveUrl)\r\n\r\n  // 如果liveUrl是JSON格式，解析出HLS地址\r\n  try {\r\n    const urlData = JSON.parse(liveUrl)\r\n    console.log('解析的JSON数据:', urlData)\r\n    const hlsUrl = urlData.hls || liveUrl\r\n    console.log('提取的HLS地址:', hlsUrl)\r\n    return hlsUrl\r\n  } catch (error) {\r\n    console.log('不是JSON格式，直接使用原地址')\r\n    // 如果不是JSON格式，直接返回原地址\r\n    return liveUrl\r\n  }\r\n}\r\n\r\n// 测试HLS地址是否可访问\r\nconst testHlsUrl = async (url) => {\r\n  try {\r\n    console.log('测试HLS地址可访问性:', url)\r\n    const response = await fetch(url, {\r\n      method: 'HEAD',\r\n      mode: 'cors'\r\n    })\r\n    console.log('HLS地址响应状态:', response.status)\r\n    return response.ok\r\n  } catch (error) {\r\n    console.error('HLS地址测试失败:', error)\r\n    return false\r\n  }\r\n}\r\n\r\n// 示例推流地址解析\r\n// 如果您的推流地址是字符串格式，可以这样处理：\r\n// const sampleUrl = {\r\n//   \"flv\": \"http://prdpulllive.xylink.com/prdnemo/9681c99c9088f6520198def4ad835a39.flv?auth_key=...\",\r\n//   \"hls\": \"http://prdpulllive.xylink.com/prdnemo/9681c99c9088f6520198def4ad835a39.m3u8?auth_key=...\",\r\n//   \"rtmp\": \"rtmp://prdpulllive.xylink.com/prdnemo/9681c99c9088f6520198def4ad835a39?auth_key=...\"\r\n// }\r\n\r\n// 添加自定义控制按钮\r\nconst addCustomControls = (video) => {\r\n  // 创建自定义控制栏\r\n  const controlsContainer = document.createElement('div')\r\n  controlsContainer.className = 'custom-video-controls'\r\n  controlsContainer.style.cssText = `\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    background: rgba(0, 0, 0, 0.7);\r\n    padding: 10px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    color: white;\r\n    font-size: 14px;\r\n  `\r\n\r\n  // 左侧控制按钮\r\n  const leftControls = document.createElement('div')\r\n  leftControls.style.display = 'flex'\r\n  leftControls.style.alignItems = 'center'\r\n  leftControls.style.gap = '15px'\r\n\r\n  // 播放/暂停按钮\r\n  const playBtn = document.createElement('button')\r\n  playBtn.innerHTML = '▶'\r\n  playBtn.style.cssText = 'background: none; border: none; color: white; cursor: pointer; font-size: 16px;'\r\n  playBtn.onclick = () => {\r\n    if (video.paused) {\r\n      video.play()\r\n      playBtn.innerHTML = '⏸'\r\n    } else {\r\n      video.pause()\r\n      playBtn.innerHTML = '▶'\r\n    }\r\n  }\r\n\r\n  // 刷新按钮\r\n  const refreshBtn = document.createElement('button')\r\n  refreshBtn.innerHTML = '🔄'\r\n  refreshBtn.style.cssText = 'background: none; border: none; color: white; cursor: pointer; font-size: 16px;'\r\n  refreshBtn.onclick = () => {\r\n    video.currentTime = 0\r\n    video.play()\r\n  }\r\n\r\n  leftControls.appendChild(playBtn)\r\n  leftControls.appendChild(refreshBtn)\r\n\r\n  // 右侧控制按钮\r\n  const rightControls = document.createElement('div')\r\n  rightControls.style.display = 'flex'\r\n  rightControls.style.alignItems = 'center'\r\n  rightControls.style.gap = '15px'\r\n\r\n  // 高清按钮\r\n  const hdBtn = document.createElement('button')\r\n  hdBtn.innerHTML = '高清'\r\n  hdBtn.style.cssText = 'background: none; border: none; color: white; cursor: pointer; font-size: 14px;'\r\n  hdBtn.onclick = () => {\r\n    console.log('高清设置')\r\n  }\r\n\r\n  // 声音按钮\r\n  const volumeBtn = document.createElement('button')\r\n  volumeBtn.innerHTML = '🔊'\r\n  volumeBtn.style.cssText = 'background: none; border: none; color: white; cursor: pointer; font-size: 16px;'\r\n  volumeBtn.onclick = () => {\r\n    video.muted = !video.muted\r\n    volumeBtn.innerHTML = video.muted ? '🔇' : '🔊'\r\n  }\r\n\r\n  // 弹幕按钮\r\n  const danmakuBtn = document.createElement('button')\r\n  danmakuBtn.innerHTML = '弹幕'\r\n  danmakuBtn.style.cssText = 'background: none; border: none; color: white; cursor: pointer; font-size: 14px;'\r\n  danmakuBtn.onclick = () => {\r\n    console.log('弹幕功能')\r\n  }\r\n\r\n  // 全屏按钮\r\n  const fullscreenBtn = document.createElement('button')\r\n  fullscreenBtn.innerHTML = '⛶'\r\n  fullscreenBtn.style.cssText = 'background: none; border: none; color: white; cursor: pointer; font-size: 16px;'\r\n  fullscreenBtn.onclick = () => {\r\n    if (document.fullscreenElement) {\r\n      document.exitFullscreen()\r\n    } else {\r\n      videoContainer.value.requestFullscreen()\r\n    }\r\n  }\r\n\r\n  rightControls.appendChild(hdBtn)\r\n  rightControls.appendChild(volumeBtn)\r\n  rightControls.appendChild(danmakuBtn)\r\n  rightControls.appendChild(fullscreenBtn)\r\n\r\n  controlsContainer.appendChild(leftControls)\r\n  controlsContainer.appendChild(rightControls)\r\n\r\n  // 添加到视频容器\r\n  videoContainer.value.appendChild(controlsContainer)\r\n\r\n  // 隐藏原生控制栏\r\n  video.controls = false\r\n}\r\nonBeforeUnmount(() => {\r\n  if (timer) clearInterval(timer)\r\n  destroyVideoPlayer()\r\n})\r\nconst handleClose = () => {\r\n  emit('update:modelValue', false)\r\n  emit('callback')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.LiveBroadcastDetails {\r\n  position: fixed;\r\n  inset: 0;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  background: #0F0F0F;\r\n  display: flex;\r\n  flex-direction: column;\r\n  z-index: 9999;\r\n\r\n  .LiveBroadcastDetailsHeader {\r\n    position: relative;\r\n    height: 80px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 16px;\r\n    color: #fff;\r\n    background: #2B2B2B;\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.08);\r\n\r\n    .LiveBroadcastDetailsEmblem {\r\n      width: 58px;\r\n      height: 58px;\r\n      margin-right: 14px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsHeadInfo {\r\n      display: flex;\r\n      flex-direction: column;\r\n    }\r\n\r\n    .LiveBroadcastDetailsTitle {\r\n      font-family: Microsoft YaHei, Microsoft YaHei;\r\n      font-weight: bold;\r\n      font-size: 20px;\r\n      color: #FFFFFF;\r\n      margin-bottom: 12px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsTime {\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #D9D9D9;\r\n    }\r\n\r\n    .LiveBroadcastDetailsClose {\r\n      position: absolute;\r\n      right: 16px;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      color: #fff;\r\n      cursor: pointer;\r\n      font-size: 18px;\r\n      opacity: .85;\r\n    }\r\n  }\r\n\r\n  .LiveBroadcastDetailsBody {\r\n    flex: 1;\r\n    display: grid;\r\n    grid-template-columns: 1fr 360px;\r\n    gap: 16px;\r\n    padding: 16px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .LiveBroadcastDetailsCanvas {\r\n    position: relative;\r\n    height: 100%;\r\n    background: #111;\r\n    overflow: hidden;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .LiveBroadcastDetailsPoster {\r\n      width: 100%;\r\n      height: 100%;\r\n      background: #000;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      color: #d23a2e;\r\n      font-weight: bold;\r\n      font-size: 22px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsCountdown {\r\n      position: absolute;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      height: 95px;\r\n      background: rgba(0, 0, 0, 0.8);\r\n      color: #fff;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n\r\n      .countdown-line1 {\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        margin-bottom: 8px;\r\n        opacity: 0.9;\r\n      }\r\n\r\n      .countdown-line2 {\r\n        font-size: 24px;\r\n        font-weight: 600;\r\n        letter-spacing: 2px;\r\n      }\r\n    }\r\n\r\n    .LiveBroadcastDetailsLiveOverlay {\r\n      position: absolute;\r\n      left: 0;\r\n      right: 0;\r\n      top: 0;\r\n      bottom: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n\r\n      .video-container {\r\n        width: 100%;\r\n        height: 100%;\r\n        position: relative;\r\n\r\n        .live-video-player {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: contain;\r\n        }\r\n\r\n        .custom-video-controls {\r\n          position: absolute;\r\n          bottom: 0;\r\n          left: 0;\r\n          right: 0;\r\n          background: rgba(0, 0, 0, 0.7);\r\n          padding: 10px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: space-between;\r\n          color: white;\r\n          font-size: 14px;\r\n\r\n          button {\r\n            background: none;\r\n            border: none;\r\n            color: white;\r\n            cursor: pointer;\r\n            font-size: 14px;\r\n            padding: 5px;\r\n            border-radius: 3px;\r\n            transition: background-color 0.2s;\r\n\r\n            &:hover {\r\n              background: rgba(255, 255, 255, 0.1);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .LiveBroadcastDetailsEnded {\r\n      position: absolute;\r\n      inset: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      background: rgba(0, 0, 0, .45);\r\n\r\n      .LiveBroadcastDetailsEndedWrap {\r\n        text-align: center;\r\n\r\n        .endedTitle {\r\n          color: #fff;\r\n          font-size: 18px;\r\n          margin-bottom: 12px;\r\n        }\r\n\r\n        .replayBtn {\r\n          background: linear-gradient(90deg, #5bc0ff, #5f7cff);\r\n          border: none;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .LiveBroadcastDetailsSidebar {\r\n    height: 100%;\r\n    background: #191919;\r\n    border-left: 1px solid rgba(255, 255, 255, 0.05);\r\n    color: #e8e8e8;\r\n    // padding: 14px 16px 16px 16px;\r\n    overflow: auto;\r\n\r\n    .LiveBroadcastDetailsTabs {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-around;\r\n      gap: 40px;\r\n      margin-bottom: 10px;\r\n      background: #2B2B2B;\r\n      padding: 14px 16px;\r\n\r\n      .LiveBroadcastDetailsTab {\r\n        cursor: pointer;\r\n        color: #999999;\r\n        position: relative;\r\n        font-size: 16px;\r\n        line-height: 1;\r\n        transition: color .2s ease;\r\n        font-weight: 500;\r\n\r\n        &.active {\r\n          color: #ffffff;\r\n          font-weight: 700;\r\n        }\r\n\r\n        &.active::after {\r\n          content: '';\r\n          position: absolute;\r\n          left: 0;\r\n          right: 0;\r\n          bottom: -8px;\r\n          height: 3px;\r\n          background: #54BDFF;\r\n          border-radius: 3px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LiveBroadcastDetailsTabPane {\r\n      padding: 12px 15px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsPanelTitle {\r\n      font-weight: bold;\r\n      margin-bottom: 10px;\r\n      font-size: 15px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsPanelText {\r\n      font-size: 13px;\r\n      line-height: 1.8;\r\n      color: #cfcfcf;\r\n    }\r\n\r\n    /* 详情tab内样式 */\r\n    .detailsTitle {\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #FFFFFF;\r\n      margin-bottom: 14px;\r\n    }\r\n\r\n    .detailsTime {\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #666666;\r\n      margin-bottom: 12px;\r\n    }\r\n\r\n    .detailsDesc {\r\n      font-size: 13px;\r\n      color: #d9d9d9;\r\n      line-height: 1.8;\r\n    }\r\n  }\r\n}\r\n\r\n.details-fade-enter-active,\r\n.details-fade-leave-active {\r\n  transition: opacity .2s ease;\r\n}\r\n\r\n.details-fade-enter-from,\r\n.details-fade-leave-to {\r\n  opacity: 0;\r\n}\r\n</style>\r\n"], "mappings": "+CA+FA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,eAAe,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,KAAK;AAChF,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,KAAK,QAAQ,yBAAyB;AAC/C,OAAOC,GAAG,MAAM,QAAQ;AACxB;;AARA,IAAAC,WAAA,GAAe;EAAErC,IAAI,EAAE;AAAuB,CAAC;;;;;;;;;;;;;;;;;IAS/C,IAAMsC,KAAK,GAAGC,OAGZ;IACF,IAAMC,IAAI,GAAGC,MAA8C;IAC3D,IAAMC,OAAO,GAAGd,GAAG,CAAC,CAAC,CAAC,CAAC;IACvB,IAAMe,OAAO,GAAGf,GAAG,CAAC,oEAAoE,CAAC;IAEzF,IAAMgB,SAAS,GAAGhB,GAAG,CAAC,SAAS,CAAC;IAChC,IAAMiB,MAAM,GAAGb,QAAQ,CAAC;MAAA,OAAMU,OAAO,CAACnH,KAAK,CAACuH,QAAQ,GAAGnB,GAAG,CAACoB,OAAO,CAACL,OAAO,CAACnH,KAAK,CAACuH,QAAQ,CAAC,GAAG,EAAE;IAAA,EAAC;IAChG,IAAME,aAAa,GAAGpB,GAAG,CAAC,EAAE,CAAC;IAC7B,IAAMqB,iBAAiB,GAAGrB,GAAG,CAAC,EAAE,CAAC;;IAEjC;IACA,IAAMsB,WAAW,GAAGtB,GAAG,CAAC,IAAI,CAAC;IAC7B,IAAMuB,MAAM,GAAGvB,GAAG,CAAC,IAAI,CAAC;IACxB,IAAMwB,GAAG,GAAGxB,GAAG,CAAC,IAAI,CAAC;IACrB,IAAMyB,mBAAmB,GAAGzB,GAAG,CAAC,KAAK,CAAC;;IAEtC;IACA,IAAM0B,SAAS,GAAG1B,GAAG,CAAC,KAAK,CAAC;IAC5B,IAAM2B,MAAM,GAAG3B,GAAG,CAAC,CAAC,CAAC;IACrB,IAAM4B,UAAU,GAAGxB,QAAQ,CAAC,YAAM;MAChC,IAAIuB,MAAM,CAAChI,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;MACnC,IAAIgI,MAAM,CAAChI,KAAK,GAAG,GAAG,EAAE,OAAO,IAAI;MACnC,OAAO,IAAI;IACb,CAAC,CAAC;IACF,IAAMkI,eAAe,GAAG7B,GAAG,CAAC,CAAC,CAAC;IAC9B,IAAM8B,WAAW,GAAG9B,GAAG,CAAC,aAAa,CAAC;IACtC,IAAM+B,YAAY,GAAG/B,GAAG,CAAC,KAAK,CAAC;IAC/B,IAAMgC,aAAa,GAAGhC,GAAG,CAAC,EAAE,CAAC;IAC7B,IAAMiC,eAAe,GAAGjC,GAAG,CAAC,EAAE,CAAC;IAC/B,IAAMkC,aAAa,GAAGlC,GAAG,CAAC,wBAAwB,CAAC;IACnD,IAAMmC,YAAY,GAAGnC,GAAG,CAAC,EAAE,CAAC;IAE5BG,SAAS,CAAC,YAAM;MACd;MACA,IAAIO,KAAK,CAAC0B,UAAU,IAAI1B,KAAK,CAAC2B,EAAE,EAAE;QAChCC,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;;IAEF;IACApC,KAAK,CAAC;MAAA,OAAMQ,KAAK,CAAC0B,UAAU;IAAA,GAAE,UAACG,MAAM,EAAEC,MAAM,EAAK;MAChD,IAAID,MAAM,IAAI7B,KAAK,CAAC2B,EAAE,IAAI,CAACG,MAAM,EAAE;QACjC;QACAF,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;;IAEF;IACApC,KAAK,CAAC;MAAA,OAAMQ,KAAK,CAAC2B,EAAE;IAAA,GAAE,UAACE,MAAM,EAAEC,MAAM,EAAK;MACxC,IAAID,MAAM,IAAI7B,KAAK,CAAC0B,UAAU,IAAIG,MAAM,KAAKC,MAAM,EAAE;QACnDF,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;IAEF,IAAMA,OAAO;MAAA,IAAAG,KAAA,GAAA/C,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAqE,QAAA;QAAA,IAAAC,GAAA,EAAAC,IAAA;QAAA,OAAA3J,mBAAA,GAAAuB,IAAA,UAAAqI,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAhE,IAAA,GAAAgE,QAAA,CAAA3F,IAAA;YAAA;cAAA2F,QAAA,CAAA3F,IAAA;cAAA,OACI4C,GAAG,CAACgD,mBAAmB,CAAC;gBAAEC,QAAQ,EAAEtC,KAAK,CAAC2B;cAAG,CAAC,CAAC;YAAA;cAA3DM,GAAG,GAAAG,QAAA,CAAAlG,IAAA;cACHgG,IAAI,GAAKD,GAAG,CAAZC,IAAI;cACV9B,OAAO,CAACnH,KAAK,GAAGiJ,IAAI;cACpBK,oBAAoB,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAH,QAAA,CAAA7D,IAAA;UAAA;QAAA,GAAAyD,OAAA;MAAA,CACvB;MAAA,gBALKJ,OAAOA,CAAA;QAAA,OAAAG,KAAA,CAAA7C,KAAA,OAAAD,SAAA;MAAA;IAAA,GAKZ;;IAED;IACAO,KAAK,CAAC;MAAA,OAAMY,OAAO,CAACnH,KAAK,CAACuJ,aAAa;IAAA,GAAE,UAACC,SAAS,EAAEC,SAAS,EAAK;MACjE,IAAID,SAAS,KAAK,KAAK,IAAIC,SAAS,KAAK,KAAK,EAAE;QAC9C;QACA/C,QAAQ,CAAC,YAAM;UACbgD,eAAe,CAAC,CAAC;QACnB,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIF,SAAS,KAAK,KAAK,IAAIC,SAAS,KAAK,KAAK,EAAE;QACrD;QACAE,kBAAkB,CAAC,CAAC;MACtB;IACF,CAAC,CAAC;IAEF,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1B,IAAI,CAACzC,OAAO,CAACnH,KAAK,CAAC6J,SAAS,EAAE;MAC9B,IAAMC,KAAK,GAAG,IAAIC,IAAI,CAAC5C,OAAO,CAACnH,KAAK,CAAC6J,SAAS,CAAC,CAACG,OAAO,CAAC,CAAC;MACzD,IAAMC,GAAG,GAAGF,IAAI,CAACE,GAAG,CAAC,CAAC;MACtB,IAAIC,IAAI,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,KAAK,GAAGG,GAAG,CAAC;MAEnC,IAAMI,GAAG,GAAGF,IAAI,CAACG,KAAK,CAACJ,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE;MACxC,IAAMK,GAAG,GAAGJ,IAAI,CAACG,KAAK,CAACJ,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;MAC/C,IAAMM,IAAI,GAAGL,IAAI,CAACG,KAAK,CAACJ,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;MACrD,IAAMO,GAAG,GAAGN,IAAI,CAACG,KAAK,CAACJ,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MAEpD,IAAIQ,QAAQ,GAAG,EAAE;MAEjB,IAAID,GAAG,GAAG,CAAC,EAAE;QACX;QACAC,QAAQ,GAAG,GAAGD,GAAG,MAAMD,IAAI,MAAMD,GAAG,IAAI;MAC1C,CAAC,MAAM,IAAIC,IAAI,GAAG,CAAC,EAAE;QACnB;QACAE,QAAQ,GAAG,GAAGF,IAAI,MAAMD,GAAG,MAAMF,GAAG,IAAI;MAC1C,CAAC,MAAM,IAAIE,GAAG,GAAG,CAAC,EAAE;QAClB;QACAG,QAAQ,GAAG,GAAGH,GAAG,MAAMF,GAAG,IAAI;MAChC,CAAC,MAAM;QACL;QACAK,QAAQ,GAAG,GAAGL,GAAG,IAAI;MACvB;;MAEA;MACA5C,aAAa,CAACzH,KAAK,GAAG,MAAM2G,MAAM,CAACQ,OAAO,CAACnH,KAAK,CAAC6J,SAAS,EAAE,aAAa,CAAC,WAAWa,QAAQ,EAAE;MAC/F;MACAhD,iBAAiB,CAAC1H,KAAK,GAAG0K,QAAQ;IACpC,CAAC;IAED,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAMrB,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAS;MACjC,IAAIqB,KAAK,EAAEC,aAAa,CAACD,KAAK,CAAC;MAC/B,IAAIxD,OAAO,CAACnH,KAAK,CAACuJ,aAAa,KAAK,KAAK,EAAE;QACzCK,aAAa,CAAC,CAAC;QACfe,KAAK,GAAGE,WAAW,CAACjB,aAAa,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM,IAAIzC,OAAO,CAACnH,KAAK,CAACuJ,aAAa,KAAK,KAAK,EAAE;QAChD;QACA7C,QAAQ,CAAC,YAAM;UACbgD,eAAe,CAAC,CAAC;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;;IAED;IACA,IAAMA,eAAe;MAAA,IAAAoB,KAAA,GAAA/E,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAqG,SAAA;QAAA,IAAAC,KAAA,EAAAC,MAAA,EAAAC,YAAA;QAAA,OAAA5L,mBAAA,GAAAuB,IAAA,UAAAsK,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAjG,IAAA,GAAAiG,SAAA,CAAA5H,IAAA;YAAA;cAAA,MAClBsE,mBAAmB,CAAC9H,KAAK,IAAI,CAACqL,cAAc,CAACrL,KAAK;gBAAAoL,SAAA,CAAA5H,IAAA;gBAAA;cAAA;cAAA,OAAA4H,SAAA,CAAAhI,MAAA;YAAA;cAEtDkI,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;cACvBD,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEpE,OAAO,CAACnH,KAAK,CAACwL,OAAO,CAAC;;cAE3C;cACA7B,kBAAkB,CAAC,CAAC;;cAEpB;cACMqB,KAAK,GAAGS,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;cAC7CV,KAAK,CAACW,SAAS,GAAG,mBAAmB;cACrCX,KAAK,CAACY,QAAQ,GAAG,IAAI;cACrBZ,KAAK,CAACa,QAAQ,GAAG,IAAI;cACrBb,KAAK,CAACc,OAAO,GAAG,MAAM;cACtBd,KAAK,CAACe,WAAW,GAAG,IAAI;cACxBf,KAAK,CAACgB,iBAAiB,GAAG,IAAI;cAC9BhB,KAAK,CAACiB,KAAK,CAACC,KAAK,GAAG,MAAM;cAC1BlB,KAAK,CAACiB,KAAK,CAACE,MAAM,GAAG,MAAM;cAC3BnB,KAAK,CAACiB,KAAK,CAACG,SAAS,GAAG,SAAS;;cAEjC;cACAf,cAAc,CAACrL,KAAK,CAACqM,WAAW,CAACrB,KAAK,CAAC;cACvCpD,MAAM,CAAC5H,KAAK,GAAGgL,KAAK;cACpBlD,mBAAmB,CAAC9H,KAAK,GAAG,IAAI;;cAEhC;cAAA,KACImH,OAAO,CAACnH,KAAK,CAACwL,OAAO;gBAAAJ,SAAA,CAAA5H,IAAA;gBAAA;cAAA;cACjByH,MAAM,GAAGqB,SAAS,CAACnF,OAAO,CAACnH,KAAK,CAACwL,OAAO,CAAC;cAC/CF,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEN,MAAM,CAAC;cAAA,KAE7BA,MAAM;gBAAAG,SAAA,CAAA5H,IAAA;gBAAA;cAAA;cAAA4H,SAAA,CAAA5H,IAAA;cAAA,OAEmB+I,UAAU,CAACtB,MAAM,CAAC;YAAA;cAAvCC,YAAY,GAAAE,SAAA,CAAAnI,IAAA;cAAA,IACbiI,YAAY;gBAAAE,SAAA,CAAA5H,IAAA;gBAAA;cAAA;cACf8H,OAAO,CAACkB,KAAK,CAAC,0BAA0B,CAAC;cACzCC,KAAK,CAAC,wBAAwB,CAAC;cAAA,OAAArB,SAAA,CAAAhI,MAAA;YAAA;cAIjC;cACA,IAAIyD,GAAG,CAAC6F,WAAW,CAAC,CAAC,EAAE;gBACrB;gBACA7E,GAAG,CAAC7H,KAAK,GAAG,IAAI6G,GAAG,CAAC;kBAClB8F,KAAK,EAAE,KAAK;kBACZC,YAAY,EAAE,IAAI;kBAClBC,cAAc,EAAE,IAAI;kBACpBC,gBAAgB,EAAE;gBACpB,CAAC,CAAC;gBAEFjF,GAAG,CAAC7H,KAAK,CAAC+M,UAAU,CAAC9B,MAAM,CAAC;gBAC5BpD,GAAG,CAAC7H,KAAK,CAACgN,WAAW,CAAChC,KAAK,CAAC;gBAE5BnD,GAAG,CAAC7H,KAAK,CAACiN,EAAE,CAACpG,GAAG,CAACqG,MAAM,CAACC,eAAe,EAAE,YAAM;kBAC7C7B,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;kBAC7BP,KAAK,CAACoC,IAAI,CAAC,CAAC,CAACzH,KAAK,CAAC,UAAApG,CAAC,EAAI;oBACtB+L,OAAO,CAACkB,KAAK,CAAC,SAAS,EAAEjN,CAAC,CAAC;kBAC7B,CAAC,CAAC;gBACJ,CAAC,CAAC;gBAEFsI,GAAG,CAAC7H,KAAK,CAACiN,EAAE,CAACpG,GAAG,CAACqG,MAAM,CAACG,KAAK,EAAE,UAACC,KAAK,EAAErE,IAAI,EAAK;kBAC9CqC,OAAO,CAACkB,KAAK,CAAC,QAAQ,EAAEvD,IAAI,CAAC;kBAC7B,IAAIA,IAAI,CAACsE,KAAK,EAAE;oBACd,QAAQtE,IAAI,CAAC9H,IAAI;sBACf,KAAK0F,GAAG,CAAC2G,UAAU,CAACC,aAAa;wBAC/BnC,OAAO,CAACkB,KAAK,CAAC,cAAc,CAAC;wBAC7B3E,GAAG,CAAC7H,KAAK,CAAC0N,SAAS,CAAC,CAAC;wBACrB;sBACF,KAAK7G,GAAG,CAAC2G,UAAU,CAACG,WAAW;wBAC7BrC,OAAO,CAACkB,KAAK,CAAC,cAAc,CAAC;wBAC7B3E,GAAG,CAAC7H,KAAK,CAAC4N,iBAAiB,CAAC,CAAC;wBAC7B;sBACF;wBACEtC,OAAO,CAACkB,KAAK,CAAC,WAAW,CAAC;wBAC1B;oBACJ;kBACF;gBACF,CAAC,CAAC;cACJ,CAAC,MAAM,IAAIxB,KAAK,CAAC6C,WAAW,CAAC,+BAA+B,CAAC,EAAE;gBAC7D;gBACA7C,KAAK,CAAC8C,GAAG,GAAG7C,MAAM;gBAClBD,KAAK,CAAC+C,gBAAgB,CAAC,gBAAgB,EAAE,YAAM;kBAC7CzC,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;kBAC3BP,KAAK,CAACoC,IAAI,CAAC,CAAC,CAACzH,KAAK,CAAC,UAAApG,CAAC,EAAI;oBACtB+L,OAAO,CAACkB,KAAK,CAAC,SAAS,EAAEjN,CAAC,CAAC;kBAC7B,CAAC,CAAC;gBACJ,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL+L,OAAO,CAACkB,KAAK,CAAC,aAAa,CAAC;gBAC5BC,KAAK,CAAC,wCAAwC,CAAC;cACjD;YAAC;cAAArB,SAAA,CAAA5H,IAAA;cAAA;YAAA;cAGH8H,OAAO,CAAC0C,IAAI,CAAC,QAAQ,CAAC;YAAA;cAGxB;cACAhD,KAAK,CAAC+C,gBAAgB,CAAC,OAAO,EAAE,UAACvB,KAAK,EAAK;gBACzClB,OAAO,CAACkB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;cACjC,CAAC,CAAC;;cAEF;cACAxB,KAAK,CAAC+C,gBAAgB,CAAC,YAAY,EAAE,YAAM;gBACzCzC,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;cACzB,CAAC,CAAC;;cAEF;cACA0C,iBAAiB,CAACjD,KAAK,CAAC;YAAA;YAAA;cAAA,OAAAI,SAAA,CAAA9F,IAAA;UAAA;QAAA,GAAAyF,QAAA;MAAA,CACzB;MAAA,gBA5GKrB,eAAeA,CAAA;QAAA,OAAAoB,KAAA,CAAA7E,KAAA,OAAAD,SAAA;MAAA;IAAA,GA4GpB;;IAED;IACA,IAAM2D,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/B;MACA,IAAI9B,GAAG,CAAC7H,KAAK,EAAE;QACb,IAAI;UACF6H,GAAG,CAAC7H,KAAK,CAACkO,OAAO,CAAC,CAAC;QACrB,CAAC,CAAC,OAAO1B,KAAK,EAAE;UACdlB,OAAO,CAACkB,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;QACpC;QACA3E,GAAG,CAAC7H,KAAK,GAAG,IAAI;MAClB;MAEA,IAAI4H,MAAM,CAAC5H,KAAK,EAAE;QAChB,IAAI;UACF4H,MAAM,CAAC5H,KAAK,CAACmO,KAAK,CAAC,CAAC;UACpBvG,MAAM,CAAC5H,KAAK,CAAC8N,GAAG,GAAG,EAAE;UACrBlG,MAAM,CAAC5H,KAAK,CAACoO,IAAI,CAAC,CAAC;UACnB,IAAI/C,cAAc,CAACrL,KAAK,EAAE;YACxBqL,cAAc,CAACrL,KAAK,CAACqO,SAAS,GAAG,EAAE;UACrC;QACF,CAAC,CAAC,OAAO7B,KAAK,EAAE;UACdlB,OAAO,CAACkB,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;QAClC;QACA5E,MAAM,CAAC5H,KAAK,GAAG,IAAI;QACnB8H,mBAAmB,CAAC9H,KAAK,GAAG,KAAK;MACnC;IACF,CAAC;;IAED;IACA,IAAMsM,SAAS,GAAG,SAAZA,SAASA,CAAId,OAAO,EAAK;MAC7BF,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEC,OAAO,CAAC;;MAE/B;MACA,IAAI;QACF,IAAM8C,OAAO,GAAGC,IAAI,CAACC,KAAK,CAAChD,OAAO,CAAC;QACnCF,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE+C,OAAO,CAAC;QAClC,IAAMrD,MAAM,GAAGqD,OAAO,CAACzG,GAAG,IAAI2D,OAAO;QACrCF,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEN,MAAM,CAAC;QAChC,OAAOA,MAAM;MACf,CAAC,CAAC,OAAOuB,KAAK,EAAE;QACdlB,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;QAC/B;QACA,OAAOC,OAAO;MAChB;IACF,CAAC;;IAED;IACA,IAAMe,UAAU;MAAA,IAAAkC,KAAA,GAAA1I,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAgK,SAAOC,GAAG;QAAA,IAAAC,QAAA;QAAA,OAAAtP,mBAAA,GAAAuB,IAAA,UAAAgO,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA3J,IAAA,GAAA2J,SAAA,CAAAtL,IAAA;YAAA;cAAAsL,SAAA,CAAA3J,IAAA;cAEzBmG,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEoD,GAAG,CAAC;cAAAG,SAAA,CAAAtL,IAAA;cAAA,OACTuL,KAAK,CAACJ,GAAG,EAAE;gBAChC7L,MAAM,EAAE,MAAM;gBACdkM,IAAI,EAAE;cACR,CAAC,CAAC;YAAA;cAHIJ,QAAQ,GAAAE,SAAA,CAAA7L,IAAA;cAIdqI,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEqD,QAAQ,CAACK,MAAM,CAAC;cAAA,OAAAH,SAAA,CAAA1L,MAAA,WACnCwL,QAAQ,CAACM,EAAE;YAAA;cAAAJ,SAAA,CAAA3J,IAAA;cAAA2J,SAAA,CAAAK,EAAA,GAAAL,SAAA;cAElBxD,OAAO,CAACkB,KAAK,CAAC,YAAY,EAAAsC,SAAA,CAAAK,EAAO,CAAC;cAAA,OAAAL,SAAA,CAAA1L,MAAA,WAC3B,KAAK;YAAA;YAAA;cAAA,OAAA0L,SAAA,CAAAxJ,IAAA;UAAA;QAAA,GAAAoJ,QAAA;MAAA,CAEf;MAAA,gBAbKnC,UAAUA,CAAA6C,EAAA;QAAA,OAAAX,KAAA,CAAAxI,KAAA,OAAAD,SAAA;MAAA;IAAA,GAaf;;IAED;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA,IAAMiI,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIjD,KAAK,EAAK;MACnC;MACA,IAAMqE,iBAAiB,GAAG5D,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACvD2D,iBAAiB,CAAC1D,SAAS,GAAG,uBAAuB;MACrD0D,iBAAiB,CAACpD,KAAK,CAACqD,OAAO,GAAG;AACpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;;MAED;MACA,IAAMC,YAAY,GAAG9D,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MAClD6D,YAAY,CAACtD,KAAK,CAACuD,OAAO,GAAG,MAAM;MACnCD,YAAY,CAACtD,KAAK,CAACwD,UAAU,GAAG,QAAQ;MACxCF,YAAY,CAACtD,KAAK,CAACyD,GAAG,GAAG,MAAM;;MAE/B;MACA,IAAMC,OAAO,GAAGlE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAChDiE,OAAO,CAACtB,SAAS,GAAG,GAAG;MACvBsB,OAAO,CAAC1D,KAAK,CAACqD,OAAO,GAAG,iFAAiF;MACzGK,OAAO,CAACC,OAAO,GAAG,YAAM;QACtB,IAAI5E,KAAK,CAAC6E,MAAM,EAAE;UAChB7E,KAAK,CAACoC,IAAI,CAAC,CAAC;UACZuC,OAAO,CAACtB,SAAS,GAAG,GAAG;QACzB,CAAC,MAAM;UACLrD,KAAK,CAACmD,KAAK,CAAC,CAAC;UACbwB,OAAO,CAACtB,SAAS,GAAG,GAAG;QACzB;MACF,CAAC;;MAED;MACA,IAAMyB,UAAU,GAAGrE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MACnDoE,UAAU,CAACzB,SAAS,GAAG,IAAI;MAC3ByB,UAAU,CAAC7D,KAAK,CAACqD,OAAO,GAAG,iFAAiF;MAC5GQ,UAAU,CAACF,OAAO,GAAG,YAAM;QACzB5E,KAAK,CAAC+E,WAAW,GAAG,CAAC;QACrB/E,KAAK,CAACoC,IAAI,CAAC,CAAC;MACd,CAAC;MAEDmC,YAAY,CAAClD,WAAW,CAACsD,OAAO,CAAC;MACjCJ,YAAY,CAAClD,WAAW,CAACyD,UAAU,CAAC;;MAEpC;MACA,IAAME,aAAa,GAAGvE,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;MACnDsE,aAAa,CAAC/D,KAAK,CAACuD,OAAO,GAAG,MAAM;MACpCQ,aAAa,CAAC/D,KAAK,CAACwD,UAAU,GAAG,QAAQ;MACzCO,aAAa,CAAC/D,KAAK,CAACyD,GAAG,GAAG,MAAM;;MAEhC;MACA,IAAMO,KAAK,GAAGxE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC9CuE,KAAK,CAAC5B,SAAS,GAAG,IAAI;MACtB4B,KAAK,CAAChE,KAAK,CAACqD,OAAO,GAAG,iFAAiF;MACvGW,KAAK,CAACL,OAAO,GAAG,YAAM;QACpBtE,OAAO,CAACC,GAAG,CAAC,MAAM,CAAC;MACrB,CAAC;;MAED;MACA,IAAM2E,SAAS,GAAGzE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAClDwE,SAAS,CAAC7B,SAAS,GAAG,IAAI;MAC1B6B,SAAS,CAACjE,KAAK,CAACqD,OAAO,GAAG,iFAAiF;MAC3GY,SAAS,CAACN,OAAO,GAAG,YAAM;QACxB5E,KAAK,CAACmF,KAAK,GAAG,CAACnF,KAAK,CAACmF,KAAK;QAC1BD,SAAS,CAAC7B,SAAS,GAAGrD,KAAK,CAACmF,KAAK,GAAG,IAAI,GAAG,IAAI;MACjD,CAAC;;MAED;MACA,IAAMC,UAAU,GAAG3E,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MACnD0E,UAAU,CAAC/B,SAAS,GAAG,IAAI;MAC3B+B,UAAU,CAACnE,KAAK,CAACqD,OAAO,GAAG,iFAAiF;MAC5Gc,UAAU,CAACR,OAAO,GAAG,YAAM;QACzBtE,OAAO,CAACC,GAAG,CAAC,MAAM,CAAC;MACrB,CAAC;;MAED;MACA,IAAM8E,aAAa,GAAG5E,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MACtD2E,aAAa,CAAChC,SAAS,GAAG,GAAG;MAC7BgC,aAAa,CAACpE,KAAK,CAACqD,OAAO,GAAG,iFAAiF;MAC/Ge,aAAa,CAACT,OAAO,GAAG,YAAM;QAC5B,IAAInE,QAAQ,CAAC6E,iBAAiB,EAAE;UAC9B7E,QAAQ,CAAC8E,cAAc,CAAC,CAAC;QAC3B,CAAC,MAAM;UACLlF,cAAc,CAACrL,KAAK,CAACwQ,iBAAiB,CAAC,CAAC;QAC1C;MACF,CAAC;MAEDR,aAAa,CAAC3D,WAAW,CAAC4D,KAAK,CAAC;MAChCD,aAAa,CAAC3D,WAAW,CAAC6D,SAAS,CAAC;MACpCF,aAAa,CAAC3D,WAAW,CAAC+D,UAAU,CAAC;MACrCJ,aAAa,CAAC3D,WAAW,CAACgE,aAAa,CAAC;MAExChB,iBAAiB,CAAChD,WAAW,CAACkD,YAAY,CAAC;MAC3CF,iBAAiB,CAAChD,WAAW,CAAC2D,aAAa,CAAC;;MAE5C;MACA3E,cAAc,CAACrL,KAAK,CAACqM,WAAW,CAACgD,iBAAiB,CAAC;;MAEnD;MACArE,KAAK,CAACY,QAAQ,GAAG,KAAK;IACxB,CAAC;IACDtF,eAAe,CAAC,YAAM;MACpB,IAAIqE,KAAK,EAAEC,aAAa,CAACD,KAAK,CAAC;MAC/BhB,kBAAkB,CAAC,CAAC;IACtB,CAAC,CAAC;IACF,IAAM8G,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBxJ,IAAI,CAAC,mBAAmB,EAAE,KAAK,CAAC;MAChCA,IAAI,CAAC,UAAU,CAAC;IAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}