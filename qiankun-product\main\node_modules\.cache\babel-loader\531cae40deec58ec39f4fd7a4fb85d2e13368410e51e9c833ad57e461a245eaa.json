{"ast": null, "code": "module.exports = function (a, b) {\n  var fn = function fn() {};\n  fn.prototype = b.prototype;\n  a.prototype = new fn();\n  a.prototype.constructor = a;\n};", "map": {"version": 3, "names": ["module", "exports", "a", "b", "fn", "prototype", "constructor"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/component-inherit@0.0.3/node_modules/component-inherit/index.js"], "sourcesContent": ["\nmodule.exports = function(a, b){\n  var fn = function(){};\n  fn.prototype = b.prototype;\n  a.prototype = new fn;\n  a.prototype.constructor = a;\n};"], "mappings": "AACAA,MAAM,CAACC,OAAO,GAAG,UAASC,CAAC,EAAEC,CAAC,EAAC;EAC7B,IAAIC,EAAE,GAAG,SAALA,EAAEA,CAAA,EAAa,CAAC,CAAC;EACrBA,EAAE,CAACC,SAAS,GAAGF,CAAC,CAACE,SAAS;EAC1BH,CAAC,CAACG,SAAS,GAAG,IAAID,EAAE,CAAD,CAAC;EACpBF,CAAC,CAACG,SAAS,CAACC,WAAW,GAAGJ,CAAC;AAC7B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}