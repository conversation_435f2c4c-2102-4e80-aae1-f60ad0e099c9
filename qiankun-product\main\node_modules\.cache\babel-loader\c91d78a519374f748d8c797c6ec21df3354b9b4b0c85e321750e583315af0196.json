{"ast": null, "code": "\"use strict\";\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nvar wrapper = require(\"../module-wrapper.js\");\nvar _require = require(\"../doc-utils.js\"),\n  isTextStart = _require.isTextStart,\n  isTextEnd = _require.isTextEnd,\n  endsWith = _require.endsWith,\n  startsWith = _require.startsWith;\nvar wTpreserve = '<w:t xml:space=\"preserve\">';\nvar wTpreservelen = wTpreserve.length;\nvar wtEnd = \"</w:t>\";\nvar wtEndlen = wtEnd.length;\nfunction isWtStart(part) {\n  return isTextStart(part) && part.tag === \"w:t\";\n}\nfunction addXMLPreserve(chunk, index) {\n  var tag = chunk[index].value;\n  if (chunk[index + 1].value === \"</w:t>\") {\n    return tag;\n  }\n  if (tag.indexOf('xml:space=\"preserve\"') !== -1) {\n    return tag;\n  }\n  return tag.substr(0, tag.length - 1) + ' xml:space=\"preserve\">';\n}\nfunction isInsideLoop(meta, chunk) {\n  return meta && meta.basePart && chunk.length > 1;\n}\nvar SpacePreserve = /*#__PURE__*/function () {\n  function SpacePreserve() {\n    _classCallCheck(this, SpacePreserve);\n    this.name = \"SpacePreserveModule\";\n  }\n  return _createClass(SpacePreserve, [{\n    key: \"postparse\",\n    value: function postparse(postparsed, meta) {\n      var chunk = [],\n        inTextTag = false,\n        endLindex = 0,\n        lastTextTag = 0;\n      function isStartingPlaceHolder(part, chunk) {\n        return part.type === \"placeholder\" && chunk.length > 1;\n      }\n      var result = postparsed.reduce(function (postparsed, part) {\n        if (isWtStart(part)) {\n          inTextTag = true;\n          lastTextTag = chunk.length;\n        }\n        if (!inTextTag) {\n          postparsed.push(part);\n          return postparsed;\n        }\n        chunk.push(part);\n        if (isInsideLoop(meta, chunk)) {\n          endLindex = meta.basePart.endLindex;\n          chunk[0].value = addXMLPreserve(chunk, 0);\n        }\n        if (isStartingPlaceHolder(part, chunk)) {\n          chunk[lastTextTag].value = addXMLPreserve(chunk, lastTextTag);\n          endLindex = part.endLindex;\n        }\n        if (isTextEnd(part) && part.lIndex > endLindex) {\n          if (endLindex !== 0) {\n            chunk[lastTextTag].value = addXMLPreserve(chunk, lastTextTag);\n          }\n          Array.prototype.push.apply(postparsed, chunk);\n          chunk = [];\n          inTextTag = false;\n          endLindex = 0;\n          lastTextTag = 0;\n        }\n        return postparsed;\n      }, []);\n      Array.prototype.push.apply(result, chunk);\n      return result;\n    }\n  }, {\n    key: \"postrender\",\n    value: function postrender(parts) {\n      var lastNonEmpty = \"\";\n      var lastNonEmptyIndex = 0;\n      for (var i = 0, len = parts.length; i < len; i++) {\n        var index = i;\n        var p = parts[i];\n        if (p === \"\") {\n          continue;\n        }\n        if (endsWith(lastNonEmpty, wTpreserve) && startsWith(p, wtEnd)) {\n          parts[lastNonEmptyIndex] = lastNonEmpty.substr(0, lastNonEmpty.length - wTpreservelen) + \"<w:t/>\";\n          p = p.substr(wtEndlen);\n        }\n        lastNonEmpty = p;\n        lastNonEmptyIndex = index;\n        parts[i] = p;\n      }\n      return parts;\n    }\n  }]);\n}();\nmodule.exports = function () {\n  return wrapper(new SpacePreserve());\n};", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_classCallCheck", "a", "n", "TypeError", "_defineProperties", "e", "r", "t", "length", "enumerable", "configurable", "writable", "Object", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "i", "_toPrimitive", "toPrimitive", "call", "String", "Number", "wrapper", "require", "_require", "isTextStart", "isTextEnd", "endsWith", "startsWith", "wTpreserve", "wTpreservelen", "wtEnd", "wtEndlen", "isWtStart", "part", "tag", "addXMLPreserve", "chunk", "index", "value", "indexOf", "substr", "isInsideLoop", "meta", "basePart", "SpacePreserve", "name", "postparse", "postparsed", "inTextTag", "endLindex", "lastTextTag", "isStartingPlaceHolder", "type", "result", "reduce", "push", "lIndex", "Array", "apply", "postrender", "parts", "lastNonEmpty", "lastNonEmptyIndex", "len", "p", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/docxtemplater@3.52.0/node_modules/docxtemplater/js/modules/space-preserve.js"], "sourcesContent": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nvar wrapper = require(\"../module-wrapper.js\");\nvar _require = require(\"../doc-utils.js\"),\n  isTextStart = _require.isTextStart,\n  isTextEnd = _require.isTextEnd,\n  endsWith = _require.endsWith,\n  startsWith = _require.startsWith;\nvar wTpreserve = '<w:t xml:space=\"preserve\">';\nvar wTpreservelen = wTpreserve.length;\nvar wtEnd = \"</w:t>\";\nvar wtEndlen = wtEnd.length;\nfunction isWtStart(part) {\n  return isTextStart(part) && part.tag === \"w:t\";\n}\nfunction addXMLPreserve(chunk, index) {\n  var tag = chunk[index].value;\n  if (chunk[index + 1].value === \"</w:t>\") {\n    return tag;\n  }\n  if (tag.indexOf('xml:space=\"preserve\"') !== -1) {\n    return tag;\n  }\n  return tag.substr(0, tag.length - 1) + ' xml:space=\"preserve\">';\n}\nfunction isInsideLoop(meta, chunk) {\n  return meta && meta.basePart && chunk.length > 1;\n}\nvar SpacePreserve = /*#__PURE__*/function () {\n  function SpacePreserve() {\n    _classCallCheck(this, SpacePreserve);\n    this.name = \"SpacePreserveModule\";\n  }\n  return _createClass(SpacePreserve, [{\n    key: \"postparse\",\n    value: function postparse(postparsed, meta) {\n      var chunk = [],\n        inTextTag = false,\n        endLindex = 0,\n        lastTextTag = 0;\n      function isStartingPlaceHolder(part, chunk) {\n        return part.type === \"placeholder\" && chunk.length > 1;\n      }\n      var result = postparsed.reduce(function (postparsed, part) {\n        if (isWtStart(part)) {\n          inTextTag = true;\n          lastTextTag = chunk.length;\n        }\n        if (!inTextTag) {\n          postparsed.push(part);\n          return postparsed;\n        }\n        chunk.push(part);\n        if (isInsideLoop(meta, chunk)) {\n          endLindex = meta.basePart.endLindex;\n          chunk[0].value = addXMLPreserve(chunk, 0);\n        }\n        if (isStartingPlaceHolder(part, chunk)) {\n          chunk[lastTextTag].value = addXMLPreserve(chunk, lastTextTag);\n          endLindex = part.endLindex;\n        }\n        if (isTextEnd(part) && part.lIndex > endLindex) {\n          if (endLindex !== 0) {\n            chunk[lastTextTag].value = addXMLPreserve(chunk, lastTextTag);\n          }\n          Array.prototype.push.apply(postparsed, chunk);\n          chunk = [];\n          inTextTag = false;\n          endLindex = 0;\n          lastTextTag = 0;\n        }\n        return postparsed;\n      }, []);\n      Array.prototype.push.apply(result, chunk);\n      return result;\n    }\n  }, {\n    key: \"postrender\",\n    value: function postrender(parts) {\n      var lastNonEmpty = \"\";\n      var lastNonEmptyIndex = 0;\n      for (var i = 0, len = parts.length; i < len; i++) {\n        var index = i;\n        var p = parts[i];\n        if (p === \"\") {\n          continue;\n        }\n        if (endsWith(lastNonEmpty, wTpreserve) && startsWith(p, wtEnd)) {\n          parts[lastNonEmptyIndex] = lastNonEmpty.substr(0, lastNonEmpty.length - wTpreservelen) + \"<w:t/>\";\n          p = p.substr(wtEndlen);\n        }\n        lastNonEmpty = p;\n        lastNonEmptyIndex = index;\n        parts[i] = p;\n      }\n      return parts;\n    }\n  }]);\n}();\nmodule.exports = function () {\n  return wrapper(new SpacePreserve());\n};"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,EAAED,CAAC,YAAYC,CAAC,CAAC,EAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;AAAE;AAClH,SAASC,iBAAiBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIZ,CAAC,GAAGW,CAAC,CAACC,CAAC,CAAC;IAAEZ,CAAC,CAACc,UAAU,GAAGd,CAAC,CAACc,UAAU,IAAI,CAAC,CAAC,EAAEd,CAAC,CAACe,YAAY,GAAG,CAAC,CAAC,EAAE,OAAO,IAAIf,CAAC,KAAKA,CAAC,CAACgB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAEC,MAAM,CAACC,cAAc,CAACR,CAAC,EAAES,cAAc,CAACnB,CAAC,CAACoB,GAAG,CAAC,EAAEpB,CAAC,CAAC;EAAE;AAAE;AACvO,SAASqB,YAAYA,CAACX,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAOD,CAAC,IAAIF,iBAAiB,CAACC,CAAC,CAACN,SAAS,EAAEO,CAAC,CAAC,EAAEC,CAAC,IAAIH,iBAAiB,CAACC,CAAC,EAAEE,CAAC,CAAC,EAAEK,MAAM,CAACC,cAAc,CAACR,CAAC,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,EAAEN,CAAC;AAAE;AAC1K,SAASS,cAAcA,CAACP,CAAC,EAAE;EAAE,IAAIU,CAAC,GAAGC,YAAY,CAACX,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAIb,OAAO,CAACuB,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASC,YAAYA,CAACX,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIZ,OAAO,CAACa,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACX,MAAM,CAACuB,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKd,CAAC,EAAE;IAAE,IAAIY,CAAC,GAAGZ,CAAC,CAACe,IAAI,CAACb,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIZ,OAAO,CAACuB,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAId,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKG,CAAC,GAAGe,MAAM,GAAGC,MAAM,EAAEf,CAAC,CAAC;AAAE;AAC3T,IAAIgB,OAAO,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC7C,IAAIC,QAAQ,GAAGD,OAAO,CAAC,iBAAiB,CAAC;EACvCE,WAAW,GAAGD,QAAQ,CAACC,WAAW;EAClCC,SAAS,GAAGF,QAAQ,CAACE,SAAS;EAC9BC,QAAQ,GAAGH,QAAQ,CAACG,QAAQ;EAC5BC,UAAU,GAAGJ,QAAQ,CAACI,UAAU;AAClC,IAAIC,UAAU,GAAG,4BAA4B;AAC7C,IAAIC,aAAa,GAAGD,UAAU,CAACtB,MAAM;AACrC,IAAIwB,KAAK,GAAG,QAAQ;AACpB,IAAIC,QAAQ,GAAGD,KAAK,CAACxB,MAAM;AAC3B,SAAS0B,SAASA,CAACC,IAAI,EAAE;EACvB,OAAOT,WAAW,CAACS,IAAI,CAAC,IAAIA,IAAI,CAACC,GAAG,KAAK,KAAK;AAChD;AACA,SAASC,cAAcA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACpC,IAAIH,GAAG,GAAGE,KAAK,CAACC,KAAK,CAAC,CAACC,KAAK;EAC5B,IAAIF,KAAK,CAACC,KAAK,GAAG,CAAC,CAAC,CAACC,KAAK,KAAK,QAAQ,EAAE;IACvC,OAAOJ,GAAG;EACZ;EACA,IAAIA,GAAG,CAACK,OAAO,CAAC,sBAAsB,CAAC,KAAK,CAAC,CAAC,EAAE;IAC9C,OAAOL,GAAG;EACZ;EACA,OAAOA,GAAG,CAACM,MAAM,CAAC,CAAC,EAAEN,GAAG,CAAC5B,MAAM,GAAG,CAAC,CAAC,GAAG,wBAAwB;AACjE;AACA,SAASmC,YAAYA,CAACC,IAAI,EAAEN,KAAK,EAAE;EACjC,OAAOM,IAAI,IAAIA,IAAI,CAACC,QAAQ,IAAIP,KAAK,CAAC9B,MAAM,GAAG,CAAC;AAClD;AACA,IAAIsC,aAAa,GAAG,aAAa,YAAY;EAC3C,SAASA,aAAaA,CAAA,EAAG;IACvB9C,eAAe,CAAC,IAAI,EAAE8C,aAAa,CAAC;IACpC,IAAI,CAACC,IAAI,GAAG,qBAAqB;EACnC;EACA,OAAO/B,YAAY,CAAC8B,aAAa,EAAE,CAAC;IAClC/B,GAAG,EAAE,WAAW;IAChByB,KAAK,EAAE,SAASQ,SAASA,CAACC,UAAU,EAAEL,IAAI,EAAE;MAC1C,IAAIN,KAAK,GAAG,EAAE;QACZY,SAAS,GAAG,KAAK;QACjBC,SAAS,GAAG,CAAC;QACbC,WAAW,GAAG,CAAC;MACjB,SAASC,qBAAqBA,CAAClB,IAAI,EAAEG,KAAK,EAAE;QAC1C,OAAOH,IAAI,CAACmB,IAAI,KAAK,aAAa,IAAIhB,KAAK,CAAC9B,MAAM,GAAG,CAAC;MACxD;MACA,IAAI+C,MAAM,GAAGN,UAAU,CAACO,MAAM,CAAC,UAAUP,UAAU,EAAEd,IAAI,EAAE;QACzD,IAAID,SAAS,CAACC,IAAI,CAAC,EAAE;UACnBe,SAAS,GAAG,IAAI;UAChBE,WAAW,GAAGd,KAAK,CAAC9B,MAAM;QAC5B;QACA,IAAI,CAAC0C,SAAS,EAAE;UACdD,UAAU,CAACQ,IAAI,CAACtB,IAAI,CAAC;UACrB,OAAOc,UAAU;QACnB;QACAX,KAAK,CAACmB,IAAI,CAACtB,IAAI,CAAC;QAChB,IAAIQ,YAAY,CAACC,IAAI,EAAEN,KAAK,CAAC,EAAE;UAC7Ba,SAAS,GAAGP,IAAI,CAACC,QAAQ,CAACM,SAAS;UACnCb,KAAK,CAAC,CAAC,CAAC,CAACE,KAAK,GAAGH,cAAc,CAACC,KAAK,EAAE,CAAC,CAAC;QAC3C;QACA,IAAIe,qBAAqB,CAAClB,IAAI,EAAEG,KAAK,CAAC,EAAE;UACtCA,KAAK,CAACc,WAAW,CAAC,CAACZ,KAAK,GAAGH,cAAc,CAACC,KAAK,EAAEc,WAAW,CAAC;UAC7DD,SAAS,GAAGhB,IAAI,CAACgB,SAAS;QAC5B;QACA,IAAIxB,SAAS,CAACQ,IAAI,CAAC,IAAIA,IAAI,CAACuB,MAAM,GAAGP,SAAS,EAAE;UAC9C,IAAIA,SAAS,KAAK,CAAC,EAAE;YACnBb,KAAK,CAACc,WAAW,CAAC,CAACZ,KAAK,GAAGH,cAAc,CAACC,KAAK,EAAEc,WAAW,CAAC;UAC/D;UACAO,KAAK,CAAC5D,SAAS,CAAC0D,IAAI,CAACG,KAAK,CAACX,UAAU,EAAEX,KAAK,CAAC;UAC7CA,KAAK,GAAG,EAAE;UACVY,SAAS,GAAG,KAAK;UACjBC,SAAS,GAAG,CAAC;UACbC,WAAW,GAAG,CAAC;QACjB;QACA,OAAOH,UAAU;MACnB,CAAC,EAAE,EAAE,CAAC;MACNU,KAAK,CAAC5D,SAAS,CAAC0D,IAAI,CAACG,KAAK,CAACL,MAAM,EAAEjB,KAAK,CAAC;MACzC,OAAOiB,MAAM;IACf;EACF,CAAC,EAAE;IACDxC,GAAG,EAAE,YAAY;IACjByB,KAAK,EAAE,SAASqB,UAAUA,CAACC,KAAK,EAAE;MAChC,IAAIC,YAAY,GAAG,EAAE;MACrB,IAAIC,iBAAiB,GAAG,CAAC;MACzB,KAAK,IAAI/C,CAAC,GAAG,CAAC,EAAEgD,GAAG,GAAGH,KAAK,CAACtD,MAAM,EAAES,CAAC,GAAGgD,GAAG,EAAEhD,CAAC,EAAE,EAAE;QAChD,IAAIsB,KAAK,GAAGtB,CAAC;QACb,IAAIiD,CAAC,GAAGJ,KAAK,CAAC7C,CAAC,CAAC;QAChB,IAAIiD,CAAC,KAAK,EAAE,EAAE;UACZ;QACF;QACA,IAAItC,QAAQ,CAACmC,YAAY,EAAEjC,UAAU,CAAC,IAAID,UAAU,CAACqC,CAAC,EAAElC,KAAK,CAAC,EAAE;UAC9D8B,KAAK,CAACE,iBAAiB,CAAC,GAAGD,YAAY,CAACrB,MAAM,CAAC,CAAC,EAAEqB,YAAY,CAACvD,MAAM,GAAGuB,aAAa,CAAC,GAAG,QAAQ;UACjGmC,CAAC,GAAGA,CAAC,CAACxB,MAAM,CAACT,QAAQ,CAAC;QACxB;QACA8B,YAAY,GAAGG,CAAC;QAChBF,iBAAiB,GAAGzB,KAAK;QACzBuB,KAAK,CAAC7C,CAAC,CAAC,GAAGiD,CAAC;MACd;MACA,OAAOJ,KAAK;IACd;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACHK,MAAM,CAACC,OAAO,GAAG,YAAY;EAC3B,OAAO7C,OAAO,CAAC,IAAIuB,aAAa,CAAC,CAAC,CAAC;AACrC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}