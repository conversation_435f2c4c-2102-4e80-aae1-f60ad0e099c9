{"ast": null, "code": "'use strict';\n\nvar encodeCache = {};\n\n// Create a lookup array where anything but characters in `chars` string\n// and alphanumeric chars is percent-encoded.\n//\nfunction getEncodeCache(exclude) {\n  var i,\n    ch,\n    cache = encodeCache[exclude];\n  if (cache) {\n    return cache;\n  }\n  cache = encodeCache[exclude] = [];\n  for (i = 0; i < 128; i++) {\n    ch = String.fromCharCode(i);\n    if (/^[0-9a-z]$/i.test(ch)) {\n      // always allow unencoded alphanumeric characters\n      cache.push(ch);\n    } else {\n      cache.push('%' + ('0' + i.toString(16).toUpperCase()).slice(-2));\n    }\n  }\n  for (i = 0; i < exclude.length; i++) {\n    cache[exclude.charCodeAt(i)] = exclude[i];\n  }\n  return cache;\n}\n\n// Encode unsafe characters with percent-encoding, skipping already\n// encoded sequences.\n//\n//  - string       - string to encode\n//  - exclude      - list of characters to ignore (in addition to a-zA-Z0-9)\n//  - keepEscaped  - don't encode '%' in a correct escape sequence (default: true)\n//\nfunction encode(string, exclude, keepEscaped) {\n  var i,\n    l,\n    code,\n    nextCode,\n    cache,\n    result = '';\n  if (typeof exclude !== 'string') {\n    // encode(string, keepEscaped)\n    keepEscaped = exclude;\n    exclude = encode.defaultChars;\n  }\n  if (typeof keepEscaped === 'undefined') {\n    keepEscaped = true;\n  }\n  cache = getEncodeCache(exclude);\n  for (i = 0, l = string.length; i < l; i++) {\n    code = string.charCodeAt(i);\n    if (keepEscaped && code === 0x25 /* % */ && i + 2 < l) {\n      if (/^[0-9a-f]{2}$/i.test(string.slice(i + 1, i + 3))) {\n        result += string.slice(i, i + 3);\n        i += 2;\n        continue;\n      }\n    }\n    if (code < 128) {\n      result += cache[code];\n      continue;\n    }\n    if (code >= 0xD800 && code <= 0xDFFF) {\n      if (code >= 0xD800 && code <= 0xDBFF && i + 1 < l) {\n        nextCode = string.charCodeAt(i + 1);\n        if (nextCode >= 0xDC00 && nextCode <= 0xDFFF) {\n          result += encodeURIComponent(string[i] + string[i + 1]);\n          i++;\n          continue;\n        }\n      }\n      result += '%EF%BF%BD';\n      continue;\n    }\n    result += encodeURIComponent(string[i]);\n  }\n  return result;\n}\nencode.defaultChars = \";/?:@&=+$,-_.!~*'()#\";\nencode.componentChars = \"-_.!~*'()\";\nmodule.exports = encode;", "map": {"version": 3, "names": ["encodeCache", "getEncodeCache", "exclude", "i", "ch", "cache", "String", "fromCharCode", "test", "push", "toString", "toUpperCase", "slice", "length", "charCodeAt", "encode", "string", "keepEscaped", "l", "code", "nextCode", "result", "defaultChars", "encodeURIComponent", "componentChars", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/mdurl@1.0.1/node_modules/mdurl/encode.js"], "sourcesContent": ["\n'use strict';\n\n\nvar encodeCache = {};\n\n\n// Create a lookup array where anything but characters in `chars` string\n// and alphanumeric chars is percent-encoded.\n//\nfunction getEncodeCache(exclude) {\n  var i, ch, cache = encodeCache[exclude];\n  if (cache) { return cache; }\n\n  cache = encodeCache[exclude] = [];\n\n  for (i = 0; i < 128; i++) {\n    ch = String.fromCharCode(i);\n\n    if (/^[0-9a-z]$/i.test(ch)) {\n      // always allow unencoded alphanumeric characters\n      cache.push(ch);\n    } else {\n      cache.push('%' + ('0' + i.toString(16).toUpperCase()).slice(-2));\n    }\n  }\n\n  for (i = 0; i < exclude.length; i++) {\n    cache[exclude.charCodeAt(i)] = exclude[i];\n  }\n\n  return cache;\n}\n\n\n// Encode unsafe characters with percent-encoding, skipping already\n// encoded sequences.\n//\n//  - string       - string to encode\n//  - exclude      - list of characters to ignore (in addition to a-zA-Z0-9)\n//  - keepEscaped  - don't encode '%' in a correct escape sequence (default: true)\n//\nfunction encode(string, exclude, keepEscaped) {\n  var i, l, code, nextCode, cache,\n      result = '';\n\n  if (typeof exclude !== 'string') {\n    // encode(string, keepEscaped)\n    keepEscaped  = exclude;\n    exclude = encode.defaultChars;\n  }\n\n  if (typeof keepEscaped === 'undefined') {\n    keepEscaped = true;\n  }\n\n  cache = getEncodeCache(exclude);\n\n  for (i = 0, l = string.length; i < l; i++) {\n    code = string.charCodeAt(i);\n\n    if (keepEscaped && code === 0x25 /* % */ && i + 2 < l) {\n      if (/^[0-9a-f]{2}$/i.test(string.slice(i + 1, i + 3))) {\n        result += string.slice(i, i + 3);\n        i += 2;\n        continue;\n      }\n    }\n\n    if (code < 128) {\n      result += cache[code];\n      continue;\n    }\n\n    if (code >= 0xD800 && code <= 0xDFFF) {\n      if (code >= 0xD800 && code <= 0xDBFF && i + 1 < l) {\n        nextCode = string.charCodeAt(i + 1);\n        if (nextCode >= 0xDC00 && nextCode <= 0xDFFF) {\n          result += encodeURIComponent(string[i] + string[i + 1]);\n          i++;\n          continue;\n        }\n      }\n      result += '%EF%BF%BD';\n      continue;\n    }\n\n    result += encodeURIComponent(string[i]);\n  }\n\n  return result;\n}\n\nencode.defaultChars   = \";/?:@&=+$,-_.!~*'()#\";\nencode.componentChars = \"-_.!~*'()\";\n\n\nmodule.exports = encode;\n"], "mappings": "AACA,YAAY;;AAGZ,IAAIA,WAAW,GAAG,CAAC,CAAC;;AAGpB;AACA;AACA;AACA,SAASC,cAAcA,CAACC,OAAO,EAAE;EAC/B,IAAIC,CAAC;IAAEC,EAAE;IAAEC,KAAK,GAAGL,WAAW,CAACE,OAAO,CAAC;EACvC,IAAIG,KAAK,EAAE;IAAE,OAAOA,KAAK;EAAE;EAE3BA,KAAK,GAAGL,WAAW,CAACE,OAAO,CAAC,GAAG,EAAE;EAEjC,KAAKC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;IACxBC,EAAE,GAAGE,MAAM,CAACC,YAAY,CAACJ,CAAC,CAAC;IAE3B,IAAI,aAAa,CAACK,IAAI,CAACJ,EAAE,CAAC,EAAE;MAC1B;MACAC,KAAK,CAACI,IAAI,CAACL,EAAE,CAAC;IAChB,CAAC,MAAM;MACLC,KAAK,CAACI,IAAI,CAAC,GAAG,GAAG,CAAC,GAAG,GAAGN,CAAC,CAACO,QAAQ,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;IAClE;EACF;EAEA,KAAKT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,OAAO,CAACW,MAAM,EAAEV,CAAC,EAAE,EAAE;IACnCE,KAAK,CAACH,OAAO,CAACY,UAAU,CAACX,CAAC,CAAC,CAAC,GAAGD,OAAO,CAACC,CAAC,CAAC;EAC3C;EAEA,OAAOE,KAAK;AACd;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,MAAMA,CAACC,MAAM,EAAEd,OAAO,EAAEe,WAAW,EAAE;EAC5C,IAAId,CAAC;IAAEe,CAAC;IAAEC,IAAI;IAAEC,QAAQ;IAAEf,KAAK;IAC3BgB,MAAM,GAAG,EAAE;EAEf,IAAI,OAAOnB,OAAO,KAAK,QAAQ,EAAE;IAC/B;IACAe,WAAW,GAAIf,OAAO;IACtBA,OAAO,GAAGa,MAAM,CAACO,YAAY;EAC/B;EAEA,IAAI,OAAOL,WAAW,KAAK,WAAW,EAAE;IACtCA,WAAW,GAAG,IAAI;EACpB;EAEAZ,KAAK,GAAGJ,cAAc,CAACC,OAAO,CAAC;EAE/B,KAAKC,CAAC,GAAG,CAAC,EAAEe,CAAC,GAAGF,MAAM,CAACH,MAAM,EAAEV,CAAC,GAAGe,CAAC,EAAEf,CAAC,EAAE,EAAE;IACzCgB,IAAI,GAAGH,MAAM,CAACF,UAAU,CAACX,CAAC,CAAC;IAE3B,IAAIc,WAAW,IAAIE,IAAI,KAAK,IAAI,CAAC,WAAWhB,CAAC,GAAG,CAAC,GAAGe,CAAC,EAAE;MACrD,IAAI,gBAAgB,CAACV,IAAI,CAACQ,MAAM,CAACJ,KAAK,CAACT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;QACrDkB,MAAM,IAAIL,MAAM,CAACJ,KAAK,CAACT,CAAC,EAAEA,CAAC,GAAG,CAAC,CAAC;QAChCA,CAAC,IAAI,CAAC;QACN;MACF;IACF;IAEA,IAAIgB,IAAI,GAAG,GAAG,EAAE;MACdE,MAAM,IAAIhB,KAAK,CAACc,IAAI,CAAC;MACrB;IACF;IAEA,IAAIA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,EAAE;MACpC,IAAIA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,IAAIhB,CAAC,GAAG,CAAC,GAAGe,CAAC,EAAE;QACjDE,QAAQ,GAAGJ,MAAM,CAACF,UAAU,CAACX,CAAC,GAAG,CAAC,CAAC;QACnC,IAAIiB,QAAQ,IAAI,MAAM,IAAIA,QAAQ,IAAI,MAAM,EAAE;UAC5CC,MAAM,IAAIE,kBAAkB,CAACP,MAAM,CAACb,CAAC,CAAC,GAAGa,MAAM,CAACb,CAAC,GAAG,CAAC,CAAC,CAAC;UACvDA,CAAC,EAAE;UACH;QACF;MACF;MACAkB,MAAM,IAAI,WAAW;MACrB;IACF;IAEAA,MAAM,IAAIE,kBAAkB,CAACP,MAAM,CAACb,CAAC,CAAC,CAAC;EACzC;EAEA,OAAOkB,MAAM;AACf;AAEAN,MAAM,CAACO,YAAY,GAAK,sBAAsB;AAC9CP,MAAM,CAACS,cAAc,GAAG,WAAW;AAGnCC,MAAM,CAACC,OAAO,GAAGX,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}