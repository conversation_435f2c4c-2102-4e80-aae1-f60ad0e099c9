{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: \"global-markdown\",\n    ref: \"elRef\",\n    onClick: $setup.handleClick\n  }, null, 512 /* NEED_PATCH */);\n}", "map": {"version": 3, "names": ["_createElementBlock", "class", "ref", "onClick", "$setup", "handleClick"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\common\\components\\global-markdown\\global-markdown.vue"], "sourcesContent": ["<template>\r\n  <div class=\"global-markdown\" ref=\"elRef\" @click=\"handleClick\"></div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalMarkdown' }\r\n</script>\r\n<script setup>\r\nimport { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'\r\nimport markdownIt from './markdown-it'\r\nimport { deepCloneAndUpdate, buildCodeBlock } from './code-block.js'\r\n\r\nconst props = defineProps({\r\n  modelValue: { type: String, default: '' },\r\n  content: { type: String, default: '' },\r\n  onLinkClick: { type: Function, default: null }\r\n})\r\n\r\nconst emit = defineEmits(['update:modelValue', 'update'])\r\nconst elRef = ref()\r\nconst htmlValue = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(value) {\r\n    emit('update:modelValue', value)\r\n  }\r\n})\r\nconst htmlData = ref('')\r\nlet isRendering = false\r\nconst renderQueue = ref([])\r\nlet renderTimeout = null\r\n\r\n/** 检查元素是否有效 */\r\nconst isValidElement = () => {\r\n  return elRef.value && elRef.value.isConnected\r\n}\r\n\r\n/** Step 4. 渲染markdown的 HTML Element. */\r\nconst renderMarkdown = (data) => {\r\n  try {\r\n    if (!isValidElement()) return\r\n\r\n    const tmpDiv = document.createElement('div')\r\n    tmpDiv.innerHTML = markdownIt.render(data)\r\n    buildCodeBlock(tmpDiv)\r\n    deepCloneAndUpdate(elRef.value, tmpDiv)\r\n  } catch (error) {\r\n    console.error('Error rendering markdown:', error)\r\n  }\r\n}\r\n\r\n/** Step 3. 处理异步渲染 */\r\nconst processRenderQueue = () => {\r\n  if (!isValidElement() || renderQueue.value.length === 0) {\r\n    isRendering = false\r\n    htmlValue.value = htmlData.value\r\n    return\r\n  }\r\n\r\n  const data = renderQueue.value.shift()\r\n  renderMarkdown(data)\r\n\r\n  // 使用 requestAnimationFrame 替代 setTimeout 以获得更好的性能\r\n  renderTimeout = requestAnimationFrame(() => {\r\n    processRenderQueue()\r\n  })\r\n}\r\n\r\n/** Step 2. 异步队列控制渲染 */\r\nconst enqueueRender = (data) => {\r\n  if (!isValidElement()) return\r\n\r\n  htmlData.value += data\r\n  renderQueue.value.push(htmlData.value)\r\n\r\n  if (!isRendering) {\r\n    isRendering = true\r\n    processRenderQueue()\r\n  }\r\n}\r\n\r\n/** Step 1. 清空内容 */\r\nconst clearContent = () => {\r\n  htmlData.value = ''\r\n  htmlValue.value = ''\r\n  elRef.value.innerHTML = ''\r\n}\r\n\r\n// 监听内容变化\r\nwatch(\r\n  () => props.content,\r\n  () => {\r\n    nextTick(() => {\r\n      if (!isValidElement()) return\r\n\r\n      elRef.value.innerHTML = ''\r\n      htmlData.value = props.content\r\n      htmlValue.value = props.content\r\n\r\n      if (props.content) {\r\n        emit('update')\r\n        renderMarkdown(props.content)\r\n      }\r\n    })\r\n  },\r\n  { immediate: true }\r\n)\r\n\r\nconst handleClick = (e) => {\r\n  const target = e.target\r\n  if (target.tagName.toLowerCase() === 'a') {\r\n    if (props.onLinkClick) {\r\n      e.preventDefault()\r\n      e.stopPropagation()\r\n      props.onLinkClick({\r\n        href: target.href,\r\n        text: target.textContent,\r\n        event: e\r\n      })\r\n    }\r\n  }\r\n}\r\n\r\n// 组件挂载时的初始化\r\nonMounted(() => {\r\n  if (props.content) {\r\n    emit('update')\r\n    renderMarkdown(props.content)\r\n  }\r\n})\r\n\r\n// 组件卸载时的清理\r\nonUnmounted(() => {\r\n  if (renderTimeout) {\r\n    cancelAnimationFrame(renderTimeout)\r\n  }\r\n  renderQueue.value = []\r\n  isRendering = false\r\n})\r\n\r\ndefineExpose({ elRef, enqueueRender, clearContent })\r\n</script>\r\n<style lang=\"scss\">\r\n@import './index.scss';\r\n\r\n.global-markdown {\r\n  width: 100%;\r\n  background: #fff;\r\n  line-height: var(--zy-line-height);\r\n  font-size: var(--zy-text-font-size);\r\n\r\n  img {\r\n    display: block;\r\n    margin: 15px auto 15px;\r\n    border-radius: 6px;\r\n    width: 100%;\r\n    cursor: pointer;\r\n    cursor: zoom-in;\r\n    box-shadow: 0 1px 15px rgba(27, 31, 35, 0.15), 0 0 1px rgba(106, 115, 125, 0.35);\r\n  }\r\n\r\n  h1 code,\r\n  h2 code,\r\n  h3 code,\r\n  h4 code,\r\n  h5 code,\r\n  h6 code,\r\n  p > code,\r\n  li > code,\r\n  table code {\r\n    color: #c7254e;\r\n    font-family: consolas !important;\r\n    vertical-align: middle;\r\n    margin: 0 3px;\r\n    background-color: #f9f2f4 !important;\r\n    line-height: var(--zy-line-height);\r\n    font-size: var(--zy-text-font-size);\r\n    padding: 0.2em 0.3em !important;\r\n    border-radius: 3px !important;\r\n    border: 1px solid #f9f2f4 !important;\r\n  }\r\n\r\n  p {\r\n    color: var(--text-color);\r\n    line-height: var(--zy-line-height);\r\n    font-size: var(--zy-text-font-size);\r\n  }\r\n\r\n  h1,\r\n  h2,\r\n  h3,\r\n  h4,\r\n  h5,\r\n  h6 {\r\n    overflow: hidden;\r\n    -webkit-line-clamp: 4;\r\n    text-overflow: ellipsis;\r\n    display: -webkit-box;\r\n    -webkit-box-orient: vertical;\r\n    color: #1f2d3d;\r\n    transition: all 0.2s ease-out;\r\n  }\r\n\r\n  h4,\r\n  h5,\r\n  h6 {\r\n    font-size: 16px;\r\n  }\r\n\r\n  h1 {\r\n    font-size: 26px;\r\n    margin: 10px 0;\r\n  }\r\n\r\n  h2 {\r\n    font-size: 22px;\r\n  }\r\n\r\n  h3 {\r\n    font-size: 18px;\r\n  }\r\n\r\n  /* 代码样式 */\r\n  pre {\r\n    white-space: pre;\r\n    position: relative;\r\n    border-radius: 7px;\r\n    color: #bababa;\r\n    background-color: #282c34;\r\n    font-size: 14px;\r\n    padding: 0;\r\n\r\n    code {\r\n      border: none;\r\n      border-radius: 7px;\r\n      font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace !important;\r\n      line-height: 21px;\r\n    }\r\n  }\r\n\r\n  kbd {\r\n    background-color: #f7f7f7;\r\n    color: #222325;\r\n    border-radius: 0.25rem;\r\n    border: 1px solid #cbcccd;\r\n    box-shadow: 0 2px 0 1px #cbcccd;\r\n    cursor: default;\r\n    font-family: Arial, sans-serif;\r\n    font-size: 0.75em;\r\n    line-height: 1;\r\n    min-width: 0.75rem;\r\n    padding: 2px 5px;\r\n    position: relative;\r\n    top: -1px;\r\n\r\n    &:hover {\r\n      box-shadow: 0 1px 0 0.5px #cbcccd;\r\n      top: 1px;\r\n    }\r\n  }\r\n\r\n  a {\r\n    color: #2d8cf0;\r\n    text-decoration: none;\r\n    transition: all 0.3s ease;\r\n    position: relative;\r\n\r\n    &::after {\r\n      content: '';\r\n      display: block;\r\n      width: 0;\r\n      height: 1px;\r\n      position: absolute;\r\n      left: 0;\r\n      bottom: -2px;\r\n      background: #2d8cf0;\r\n      transition: all 0.3s ease-in-out;\r\n    }\r\n\r\n    &:hover::after {\r\n      width: 100%;\r\n    }\r\n  }\r\n\r\n  hr {\r\n    position: relative;\r\n    margin: 20px 0;\r\n    border: 2px dashed #bfe4fb;\r\n    width: 100%;\r\n    box-sizing: content-box;\r\n    height: 0;\r\n    overflow: visible;\r\n    box-sizing: border-box;\r\n  }\r\n\r\n  hr::before {\r\n    position: absolute;\r\n    top: -11px;\r\n    left: 2%;\r\n    z-index: 1;\r\n    color: #bfe4fb;\r\n    content: '✂';\r\n    font-size: 21px;\r\n    line-height: 1;\r\n    -webkit-transition: all 1s ease-in-out;\r\n    -moz-transition: all 1s ease-in-out;\r\n    -o-transition: all 1s ease-in-out;\r\n    -ms-transition: all 1s ease-in-out;\r\n    transition: all 1s ease-in-out;\r\n  }\r\n\r\n  hr:hover::before {\r\n    left: calc(98% - 20px);\r\n  }\r\n\r\n  table {\r\n    font-size: 15px;\r\n    width: 100%;\r\n    margin: 15px 0px;\r\n    display: block;\r\n    overflow-x: auto;\r\n    border: none;\r\n    border-collapse: collapse;\r\n    border-spacing: 0;\r\n\r\n    &::-webkit-scrollbar {\r\n      height: 4px !important;\r\n    }\r\n\r\n    th {\r\n      background: #bfe4fb;\r\n      border: 1px solid #a6d6f5;\r\n      white-space: nowrap;\r\n      font-weight: 400;\r\n      padding: 6px 15px;\r\n      min-width: 100px;\r\n    }\r\n\r\n    td {\r\n      border: 1px solid #a6d6f5;\r\n      padding: 6px 15px;\r\n      min-width: 100px;\r\n    }\r\n  }\r\n\r\n  ul,\r\n  ol {\r\n    padding-left: 2em;\r\n\r\n    li {\r\n      margin: 4px 0px;\r\n    }\r\n  }\r\n\r\n  ul li {\r\n    list-style: circle;\r\n\r\n    &::marker {\r\n      transition: all 0.4s;\r\n      /* color: #49b1f5; */\r\n      color: var(--theme-color);\r\n      font-weight: 600;\r\n      font-size: 1.05em;\r\n    }\r\n\r\n    &:hover::marker {\r\n      color: #ff7242;\r\n    }\r\n  }\r\n\r\n  blockquote {\r\n    border: none;\r\n    margin: 15px 0px;\r\n    color: inherit;\r\n    border-radius: 4px;\r\n    padding: 1px 15px;\r\n    border-left: 4px solid var(--theme-color);\r\n    background-color: #f8f8f8;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;uBACEA,mBAAA,CAAoE;IAA/DC,KAAK,EAAC,iBAAiB;IAACC,GAAG,EAAC,OAAO;IAAEC,OAAK,EAAEC,MAAA,CAAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}