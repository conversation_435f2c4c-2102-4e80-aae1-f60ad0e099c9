{"ast": null, "code": "import { defineAsyncComponent } from 'vue';\nimport { useRoute, useRouter } from 'vue-router';\nimport { qiankun, LayoutViewUnitedFront, ChatMethod, AiChatMethod, refreshIcon } from './LayoutViewUnitedFront.js';\nimport { systemLogo, systemName, whetherAiChat, systemNameAreaPrefix, layoutNameBg, layoutChildBg, layoutChildNameBg } from 'common/js/system_var.js';\nimport { ArrowRight, ArrowDown } from '@element-plus/icons-vue';\nvar __default__ = {\n  name: 'LayoutViewUnitedFront'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var HelpDocument = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/HelpDocument');\n    });\n    var EditPassWord = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/EditPassWord');\n    });\n    var LayoutBoxMessage = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/LayoutBoxMessage');\n    });\n    var LayoutPersonalDoList = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/LayoutPersonalDoList');\n    });\n    var GlobalRegionSelect = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/GlobalRegionSelect');\n    });\n    var GlobalChatFloating = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/GlobalChatFloating');\n    });\n    var GlobalFloatingWindow = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/GlobalFloatingWindow');\n    });\n    var GlobalAiControls = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/GlobalAiControls');\n    });\n    var GlobalAiChat = defineAsyncComponent(function () {\n      return import('../GlobalAiChat/GlobalAiChat');\n    });\n    var suggestPop = defineAsyncComponent(function () {\n      return import('./component/suggestPop');\n    });\n    var qusetionAnswering = defineAsyncComponent(function () {\n      return import('./component/question-answering.vue');\n    });\n    var SubAppViewport = {\n      name: 'SubAppViewport',\n      props: ['name'],\n      template: `<div :id=\"name\" class=\"subApp-viewport\"></div>`\n    };\n    var _qiankun = qiankun(useRoute()),\n      isMain = _qiankun.isMain;\n    var _LayoutViewUnitedFron = LayoutViewUnitedFront(useRoute(), useRouter()),\n      user = _LayoutViewUnitedFron.user,\n      area = _LayoutViewUnitedFron.area,\n      role = _LayoutViewUnitedFron.role,\n      left = _LayoutViewUnitedFron.left,\n      width = _LayoutViewUnitedFron.width,\n      LayoutViewBox = _LayoutViewUnitedFron.LayoutViewBox,\n      LayoutViewInfo = _LayoutViewUnitedFron.LayoutViewInfo,\n      helpShow = _LayoutViewUnitedFron.helpShow,\n      handleCommand = _LayoutViewUnitedFron.handleCommand,\n      editPassWordShow = _LayoutViewUnitedFron.editPassWordShow,\n      verifyEditPassWord = _LayoutViewUnitedFron.verifyEditPassWord,\n      verifyEditPassWordShow = _LayoutViewUnitedFron.verifyEditPassWordShow,\n      editPassWordCallback = _LayoutViewUnitedFron.editPassWordCallback,\n      regionId = _LayoutViewUnitedFron.regionId,\n      regionName = _LayoutViewUnitedFron.regionName,\n      regionSelect = _LayoutViewUnitedFron.regionSelect,\n      isRegionSelectShow = _LayoutViewUnitedFron.isRegionSelectShow,\n      isOrganizationSelectShow = _LayoutViewUnitedFron.isOrganizationSelectShow,\n      isView = _LayoutViewUnitedFron.isView,\n      isChildView = _LayoutViewUnitedFron.isChildView,\n      tabMenu = _LayoutViewUnitedFron.tabMenu,\n      tabMenuData = _LayoutViewUnitedFron.tabMenuData,\n      handleClick = _LayoutViewUnitedFron.handleClick,\n      menuId = _LayoutViewUnitedFron.menuId,\n      menuData = _LayoutViewUnitedFron.menuData,\n      menuClick = _LayoutViewUnitedFron.menuClick,\n      handleBreadcrumb = _LayoutViewUnitedFron.handleBreadcrumb,\n      WorkBenchObj = _LayoutViewUnitedFron.WorkBenchObj,\n      childData = _LayoutViewUnitedFron.childData,\n      WorkBenchReturn = _LayoutViewUnitedFron.WorkBenchReturn,\n      isRefresh = _LayoutViewUnitedFron.isRefresh,\n      keepAliveRoute = _LayoutViewUnitedFron.keepAliveRoute,\n      tabData = _LayoutViewUnitedFron.tabData,\n      tabClick = _LayoutViewUnitedFron.tabClick,\n      handleRefresh = _LayoutViewUnitedFron.handleRefresh,\n      handleClose = _LayoutViewUnitedFron.handleClose,\n      handleCloseOther = _LayoutViewUnitedFron.handleCloseOther,\n      isMicroApp = _LayoutViewUnitedFron.isMicroApp,\n      MicroApp = _LayoutViewUnitedFron.MicroApp,\n      suggestPopShow = _LayoutViewUnitedFron.suggestPopShow,\n      currentOrganization = _LayoutViewUnitedFron.currentOrganization,\n      organizationList = _LayoutViewUnitedFron.organizationList,\n      handleOrganizationChange = _LayoutViewUnitedFron.handleOrganizationChange;\n    var _ChatMethod = ChatMethod(),\n      rongCloudToken = _ChatMethod.rongCloudToken;\n    var _AiChatMethod = AiChatMethod(),\n      AiChatTargetWidth = _AiChatMethod.AiChatTargetWidth,\n      AiChatViewType = _AiChatMethod.AiChatViewType,\n      AiChatWindowShow = _AiChatMethod.AiChatWindowShow;\n    var __returned__ = {\n      HelpDocument,\n      EditPassWord,\n      LayoutBoxMessage,\n      LayoutPersonalDoList,\n      GlobalRegionSelect,\n      GlobalChatFloating,\n      GlobalFloatingWindow,\n      GlobalAiControls,\n      GlobalAiChat,\n      suggestPop,\n      qusetionAnswering,\n      SubAppViewport,\n      isMain,\n      user,\n      area,\n      role,\n      left,\n      width,\n      LayoutViewBox,\n      LayoutViewInfo,\n      helpShow,\n      handleCommand,\n      editPassWordShow,\n      verifyEditPassWord,\n      verifyEditPassWordShow,\n      editPassWordCallback,\n      regionId,\n      regionName,\n      regionSelect,\n      isRegionSelectShow,\n      isOrganizationSelectShow,\n      isView,\n      isChildView,\n      tabMenu,\n      tabMenuData,\n      handleClick,\n      menuId,\n      menuData,\n      menuClick,\n      handleBreadcrumb,\n      WorkBenchObj,\n      childData,\n      WorkBenchReturn,\n      isRefresh,\n      keepAliveRoute,\n      tabData,\n      tabClick,\n      handleRefresh,\n      handleClose,\n      handleCloseOther,\n      isMicroApp,\n      MicroApp,\n      suggestPopShow,\n      currentOrganization,\n      organizationList,\n      handleOrganizationChange,\n      rongCloudToken,\n      AiChatTargetWidth,\n      AiChatViewType,\n      AiChatWindowShow,\n      defineAsyncComponent,\n      get useRoute() {\n        return useRoute;\n      },\n      get useRouter() {\n        return useRouter;\n      },\n      get qiankun() {\n        return qiankun;\n      },\n      get LayoutViewUnitedFront() {\n        return LayoutViewUnitedFront;\n      },\n      get ChatMethod() {\n        return ChatMethod;\n      },\n      get AiChatMethod() {\n        return AiChatMethod;\n      },\n      get refreshIcon() {\n        return refreshIcon;\n      },\n      get systemLogo() {\n        return systemLogo;\n      },\n      get systemName() {\n        return systemName;\n      },\n      get whetherAiChat() {\n        return whetherAiChat;\n      },\n      get systemNameAreaPrefix() {\n        return systemNameAreaPrefix;\n      },\n      get layoutNameBg() {\n        return layoutNameBg;\n      },\n      get layoutChildBg() {\n        return layoutChildBg;\n      },\n      get layoutChildNameBg() {\n        return layoutChildNameBg;\n      },\n      get ArrowRight() {\n        return ArrowRight;\n      },\n      get ArrowDown() {\n        return ArrowDown;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["defineAsyncComponent", "useRoute", "useRouter", "qiankun", "LayoutViewUnitedFront", "ChatMethod", "AiChatMethod", "refreshIcon", "systemLogo", "systemName", "whetherAiChat", "systemNameAreaPrefix", "layoutNameBg", "layoutChildBg", "layoutChildNameBg", "ArrowRight", "ArrowDown", "__default__", "name", "HelpDocument", "EditPassWord", "LayoutBoxMessage", "LayoutPersonalDoList", "GlobalRegionSelect", "GlobalChatFloating", "GlobalFloatingWindow", "GlobalAiControls", "GlobalAiChat", "suggestPop", "qusetionAnswering", "SubAppViewport", "props", "template", "_qiankun", "is<PERSON><PERSON>", "_LayoutViewUnitedFron", "user", "area", "role", "left", "width", "LayoutViewBox", "LayoutViewInfo", "helpShow", "handleCommand", "editPassWordShow", "verifyEditPassWord", "verifyEditPassWordShow", "editPassWordCallback", "regionId", "regionName", "regionSelect", "isRegionSelectShow", "isOrganizationSelectShow", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabMenu", "tabMenuData", "handleClick", "menuId", "menuData", "menuClick", "handleBreadcrumb", "WorkBenchObj", "childData", "WorkBenchReturn", "isRefresh", "keepAliveRoute", "tabData", "tabClick", "handleRefresh", "handleClose", "handleCloseOther", "isMicroApp", "MicroApp", "suggestPopShow", "currentOrganization", "organizationList", "handleOrganizationChange", "_ChatMethod", "rongCloudToken", "_AiChatMethod", "AiChatTargetWidth", "AiChatViewType", "AiChatWindowShow"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/LayoutViewUnitedFront/LayoutViewUnitedFront.vue"], "sourcesContent": ["<template>\r\n  <el-container class=\"LayoutViewUnitedFront\">\r\n    <el-header class=\"LayoutViewHeader\">\r\n      <div class=\"LayoutViewBox\" ref=\"LayoutViewBox\" @click=\"WorkBenchReturn\"\r\n        :style=\"`background: url('${layoutNameBg}') no-repeat;background-size: auto 100%;background-position: right;`\">\r\n        <div class=\"LayoutViewLogo\">\r\n          <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n        </div>\r\n        <div class=\"LayoutViewName\">{{ systemNameAreaPrefix === 'true' ? regionName : '' }}{{ systemName }}</div>\r\n      </div>\r\n      <div class=\"LayoutViewChildView\" :style=\"`left:${left - 88\r\n        }px;background: url('${layoutChildNameBg}') no-repeat;background-size: auto 100%;background-position: right;`\"\r\n        v-if=\"isChildView\">\r\n        <div class=\"LayoutViewChildViewIcon\"></div>\r\n        {{ childData.name }}\r\n      </div>\r\n      <div class=\"LayoutViewHeaderMenu\" :class=\"{ isLayoutViewHeaderMenu: isChildView }\" :style=\"`${width}padding-left:${left}px;background: url('${isChildView ? layoutChildBg : ''\r\n        }') no-repeat;background-size: 287px 65px;background-position: right bottom;`\">\r\n        <el-tabs v-model=\"tabMenu\" @tab-change=\"handleClick\">\r\n          <el-tab-pane v-for=\"item in tabMenuData\" :key=\"item.id\" :name=\"item.id\">\r\n            <template #label>\r\n              <div class=\"LayoutViewHeaderMenuItem forbidSelect\">\r\n                <el-image :src=\"item.icon\" fit=\"cover\" />\r\n                <span>{{ item.name }}</span>\r\n              </div>\r\n            </template>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n      <div class=\"LayoutViewInfo\" ref=\"LayoutViewInfo\">\r\n        <div class=\"WorkBenchReturn\" @click=\"WorkBenchReturn\" v-if=\"isChildView\">\r\n          <span></span>\r\n          {{ WorkBenchObj.name }}\r\n        </div>\r\n        <div class=\"LayoutViewOrganization\" v-show=\"!isChildView\">\r\n          <el-dropdown @command=\"handleOrganizationChange\" trigger=\"click\">\r\n            <div class=\"LayoutViewOrganizationTrigger\">\r\n              <span class=\"LayoutViewOrganizationText\">{{ currentOrganization?.orgName || '请选择组织' }}</span>\r\n              <el-icon class=\"LayoutViewOrganizationIcon\">\r\n                <ArrowDown />\r\n              </el-icon>\r\n            </div>\r\n            <template #dropdown>\r\n              <el-dropdown-menu>\r\n                <el-dropdown-item v-for=\"item in organizationList\" :key=\"item.id || item.orgId\" :command=\"item\"\r\n                  :class=\"{ 'is-active': (currentOrganization?.id || currentOrganization?.orgId) === (item.id || item.orgId) }\">\r\n                  {{ item.orgName }}\r\n                </el-dropdown-item>\r\n              </el-dropdown-menu>\r\n            </template>\r\n          </el-dropdown>\r\n        </div>\r\n        <xyl-region v-model=\"regionId\" :data=\"area\" @select=\"regionSelect\" v-show=\"!isChildView\"\r\n          :props=\"{ label: 'name', children: 'children' }\"></xyl-region>\r\n        <el-tooltip placement=\"top\" effect=\"light\" :offset=\"6\" :disabled=\"!role.length\">\r\n          <template #content>\r\n            <div class=\"LayoutViewRoleItem\" v-for=\"(item, index) in role\" :key=\"index\">{{ item }}</div>\r\n          </template>\r\n          <div class=\"LayoutViewUser\">\r\n            <el-image :src=\"user.image\" fit=\"cover\" />\r\n            <span class=\"forbidSelect\">{{ user.userName }}</span>\r\n          </div>\r\n        </el-tooltip>\r\n        <LayoutPersonalDoList></LayoutPersonalDoList>\r\n        <LayoutBoxMessage></LayoutBoxMessage>\r\n        <div class=\"LayoutViewRefresh\" v-html=\"refreshIcon\" @click=\"handleCommand('refresh')\" title=\"重新加载平台\"></div>\r\n        <el-dropdown @command=\"handleCommand\">\r\n          <div class=\"LayoutOperation\"></div>\r\n          <template #dropdown>\r\n            <el-dropdown-menu>\r\n              <el-dropdown-item command=\"task\">系统任务管理器</el-dropdown-item>\r\n              <!-- <el-dropdown-item command=\"refresh\">重新加载平台</el-dropdown-item> -->\r\n              <!-- <el-dropdown-item command=\"locale\">简繁切换</el-dropdown-item>\r\n              <el-dropdown-item command=\"help\">帮助文档</el-dropdown-item> -->\r\n              <el-dropdown-item command=\"edit_password\">修改密码</el-dropdown-item>\r\n              <el-dropdown-item command=\"exit\">安全退出</el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </template>\r\n        </el-dropdown>\r\n      </div>\r\n    </el-header>\r\n    <el-container class=\"LayoutViewContainer\">\r\n      <el-aside class=\"LayoutViewAside\" v-show=\"isView\">\r\n        <xyl-menu v-model=\"menuId\" :menuData=\"menuData\" @select=\"menuClick\"></xyl-menu>\r\n      </el-aside>\r\n      <el-main class=\"LayoutViewMain\"\r\n        :class=\"{ LayoutViewMainView: !isView, LayoutViewMainBreadcrumb: !isView && tabData.length > 1 }\">\r\n        <xyl-tab v-model=\"menuId\" @tab-click=\"tabClick\" @refresh=\"handleRefresh\" @close=\"handleClose\"\r\n          @closeOther=\"handleCloseOther\" v-show=\"isView\">\r\n          <xyl-tab-item v-for=\"item in tabData\" :key=\"item.id\" :value=\"item.id\">{{ item.name }}</xyl-tab-item>\r\n        </xyl-tab>\r\n        <div class=\"LayoutViewBreadcrumb\" v-if=\"!isView && tabData.length > 1\">\r\n          <el-breadcrumb :separator-icon=\"ArrowRight\">\r\n            <el-breadcrumb-item v-for=\"(item, index) in tabData\" :key=\"`key-${item.id}`\"\r\n              @click=\"handleBreadcrumb(item, index)\">\r\n              {{ item.name }}\r\n            </el-breadcrumb-item>\r\n          </el-breadcrumb>\r\n        </div>\r\n        <div class=\"LayoutViewBody\">\r\n          <router-view v-slot=\"{ Component }\">\r\n            <keep-alive :include=\"keepAliveRoute\">\r\n              <component v-if=\"isMain && isRefresh\" :key=\"$route.fullPath\" :is=\"Component\"></component>\r\n            </keep-alive>\r\n          </router-view>\r\n          <SubAppViewport v-for=\"item in MicroApp\" :key=\"item\" v-show=\"!isMain && isMicroApp === item\" :name=\"item\">\r\n          </SubAppViewport>\r\n        </div>\r\n      </el-main>\r\n      <el-aside class=\"LayoutViewFloatingWindow\" v-if=\"whetherAiChat\">\r\n        <transition name=\"width-animation\">\r\n          <div class=\"LayoutViewFloatingWindowBody\" :style=\"{ '--ai-chat-target-width': AiChatTargetWidth }\"\r\n            v-if=\"AiChatViewType\" v-show=\"AiChatWindowShow\">\r\n            <GlobalAiChat v-model=\"AiChatWindowShow\"></GlobalAiChat>\r\n          </div>\r\n        </transition>\r\n      </el-aside>\r\n    </el-container>\r\n    <xyl-popup-window v-model=\"helpShow\" name=\"帮助文档\">\r\n      <HelpDocument></HelpDocument>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"editPassWordShow\" name=\"修改密码\">\r\n      <EditPassWord :type=\"verifyEditPassWord\" @callback=\"editPassWordCallback\"></EditPassWord>\r\n    </xyl-popup-window>\r\n    <div class=\"ConstraintEditPassWord\" v-if=\"verifyEditPassWordShow\">\r\n      <EditPassWord :type=\"verifyEditPassWord\" @callback=\"editPassWordCallback\"></EditPassWord>\r\n    </div>\r\n    <GlobalRegionSelect v-if=\"isRegionSelectShow\" @callback=\"regionSelect\"></GlobalRegionSelect>\r\n  </el-container>\r\n  <qusetionAnswering></qusetionAnswering>\r\n  <GlobalChatFloating v-if=\"rongCloudToken\"></GlobalChatFloating>\r\n  <GlobalFloatingWindow v-model=\"AiChatWindowShow\" :disabled=\"AiChatViewType\" v-if=\"whetherAiChat\" />\r\n  <GlobalAiControls v-if=\"whetherAiChat\" />\r\n  <suggestPop v-if=\"isMain && suggestPopShow\" />\r\n</template>\r\n<script>\r\nexport default { name: 'LayoutViewUnitedFront' }\r\n</script>\r\n<script setup>\r\nimport { defineAsyncComponent } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { qiankun, LayoutViewUnitedFront, ChatMethod, AiChatMethod, refreshIcon } from './LayoutViewUnitedFront.js'\r\nimport {\r\n  systemLogo,\r\n  systemName,\r\n  whetherAiChat,\r\n  systemNameAreaPrefix,\r\n  layoutNameBg,\r\n  layoutChildBg,\r\n  layoutChildNameBg\r\n} from 'common/js/system_var.js'\r\nimport { ArrowRight, ArrowDown } from '@element-plus/icons-vue'\r\nconst HelpDocument = defineAsyncComponent(() => import('../LayoutContainer/components/HelpDocument'))\r\nconst EditPassWord = defineAsyncComponent(() => import('../LayoutContainer/components/EditPassWord'))\r\nconst LayoutBoxMessage = defineAsyncComponent(() => import('../LayoutContainer/components/LayoutBoxMessage'))\r\nconst LayoutPersonalDoList = defineAsyncComponent(() => import('../LayoutContainer/components/LayoutPersonalDoList'))\r\nconst GlobalRegionSelect = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalRegionSelect'))\r\nconst GlobalChatFloating = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalChatFloating'))\r\nconst GlobalFloatingWindow = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalFloatingWindow'))\r\nconst GlobalAiControls = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalAiControls'))\r\nconst GlobalAiChat = defineAsyncComponent(() => import('../GlobalAiChat/GlobalAiChat'))\r\nconst suggestPop = defineAsyncComponent(() => import('./component/suggestPop'))\r\nconst qusetionAnswering = defineAsyncComponent(() => import('./component/question-answering.vue'))\r\nconst SubAppViewport = {\r\n  name: 'SubAppViewport',\r\n  props: ['name'],\r\n  template: `<div :id=\"name\" class=\"subApp-viewport\"></div>`\r\n}\r\nconst { isMain } = qiankun(useRoute())\r\nconst {\r\n  user,\r\n  area,\r\n  role,\r\n  left,\r\n  width,\r\n  LayoutViewBox,\r\n  LayoutViewInfo,\r\n  helpShow,\r\n  handleCommand,\r\n  editPassWordShow,\r\n  verifyEditPassWord,\r\n  verifyEditPassWordShow,\r\n  editPassWordCallback,\r\n  regionId,\r\n  regionName,\r\n  regionSelect,\r\n  isRegionSelectShow,\r\n  isOrganizationSelectShow,\r\n  isView,\r\n  isChildView,\r\n  tabMenu,\r\n  tabMenuData,\r\n  handleClick,\r\n  menuId,\r\n  menuData,\r\n  menuClick,\r\n  handleBreadcrumb,\r\n  WorkBenchObj,\r\n  childData,\r\n  WorkBenchReturn,\r\n  isRefresh,\r\n  keepAliveRoute,\r\n  tabData,\r\n  tabClick,\r\n  handleRefresh,\r\n  handleClose,\r\n  handleCloseOther,\r\n  isMicroApp,\r\n  MicroApp,\r\n  suggestPopShow,\r\n  currentOrganization,\r\n  organizationList,\r\n  handleOrganizationChange\r\n} = LayoutViewUnitedFront(useRoute(), useRouter())\r\n\r\nconst { rongCloudToken } = ChatMethod()\r\nconst { AiChatTargetWidth, AiChatViewType, AiChatWindowShow } = AiChatMethod()\r\n</script>\r\n<style lang=\"scss\">\r\n.LayoutViewUnitedFront {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .LayoutViewHeader {\r\n    height: 62px;\r\n    background-color: var(--zy-el-color-primary);\r\n    position: relative;\r\n    display: flex;\r\n    justify-content: space-between;\r\n\r\n    .LayoutViewBox {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      z-index: 9;\r\n      height: 77px;\r\n      display: flex;\r\n      align-items: center;\r\n      // pointer-events: none;\r\n      padding: 0 88px 15px 20px;\r\n      cursor: pointer;\r\n\r\n      .LayoutViewLogo {\r\n        width: 52px;\r\n\r\n        .zy-el-image {\r\n          width: 100%;\r\n          display: block;\r\n        }\r\n      }\r\n\r\n      .LayoutViewName {\r\n        font-size: var(--zy-system-font-size);\r\n        line-height: var(--zy-line-height);\r\n        font-weight: bold;\r\n        color: var(--zy-el-color-primary);\r\n        padding-left: 12px;\r\n      }\r\n    }\r\n\r\n    .LayoutViewChildView {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      height: 76px;\r\n      display: flex;\r\n      padding: 0 88px;\r\n      padding-bottom: 14px;\r\n      z-index: 3;\r\n      pointer-events: none;\r\n      display: flex;\r\n      align-items: center;\r\n      font-size: var(--zy-navigation-font-size);\r\n      font-weight: bold;\r\n      color: #fff;\r\n\r\n      .LayoutViewChildViewIcon {\r\n        width: 52px;\r\n        height: calc(var(--zy-navigation-font-size) * var(--zy-line-height));\r\n        background: url('../img/layout_view_child_view_icon.png') no-repeat;\r\n        background-size: 21px 11px;\r\n        background-position: center center;\r\n      }\r\n    }\r\n\r\n    .LayoutViewHeaderMenu {\r\n      height: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      overflow: hidden;\r\n\r\n      .zy-el-tabs {\r\n        width: 100%;\r\n\r\n        .zy-el-tabs__header {\r\n          margin: 0;\r\n\r\n          .zy-el-tabs__nav-next,\r\n          .zy-el-tabs__nav-prev {\r\n            color: #fff;\r\n            font-size: var(--zy-navigation-font-size);\r\n            line-height: 48px;\r\n\r\n            .zy-el-icon {\r\n              color: #fff;\r\n            }\r\n          }\r\n\r\n          .zy-el-tabs__nav-wrap {\r\n            &::after {\r\n              background-color: transparent;\r\n            }\r\n\r\n            .zy-el-tabs__item {\r\n              height: 43px;\r\n              line-height: 43px;\r\n              font-size: var(--zy-navigation-font-size);\r\n\r\n              .LayoutViewHeaderMenuItem {\r\n                display: flex;\r\n                align-items: center;\r\n                vertical-align: middle;\r\n\r\n                .zy-el-image {\r\n                  width: 20px;\r\n                  height: 20px;\r\n                  margin-right: 6px;\r\n                }\r\n\r\n                &>span {\r\n                  color: #fff;\r\n                }\r\n              }\r\n            }\r\n\r\n            .is-active {\r\n              font-weight: bold;\r\n            }\r\n\r\n            .zy-el-tabs__active-bar {\r\n              height: 3px;\r\n              background-color: #fff;\r\n            }\r\n          }\r\n        }\r\n\r\n        .zy-el-tabs__content {\r\n          display: none;\r\n        }\r\n      }\r\n    }\r\n\r\n    .isLayoutViewHeaderMenu {\r\n      .zy-el-tabs {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    .LayoutViewInfo {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .WorkBenchReturn {\r\n        height: 36px;\r\n        padding: 0 12px;\r\n        border: 1px solid #ffffff;\r\n        border-radius: var(--el-border-radius-base);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-size: var(--zy-name-font-size);\r\n        line-height: var(--zy-line-height);\r\n        color: #ffffff;\r\n        cursor: pointer;\r\n        margin-right: 22px;\r\n\r\n        span {\r\n          width: 22px;\r\n          height: 22px;\r\n          display: inline-block;\r\n          background: url('../img/work_bench_return.png') no-repeat;\r\n          background-size: 100% 100%;\r\n          margin-right: 6px;\r\n        }\r\n      }\r\n\r\n      .LayoutViewUser {\r\n        display: flex;\r\n        align-items: center;\r\n        cursor: pointer;\r\n\r\n        .zy-el-image {\r\n          height: 38px;\r\n          width: 38px;\r\n          border-radius: 50%;\r\n          overflow: hidden;\r\n        }\r\n\r\n        span {\r\n          color: #fff;\r\n          margin-left: 8px;\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: var(--zy-line-height);\r\n        }\r\n      }\r\n\r\n      .zy-el-badge {\r\n        margin-left: 20px;\r\n      }\r\n\r\n      .zy-el-dropdown {\r\n        margin-left: 18px;\r\n      }\r\n\r\n      .LayoutViewRefresh {\r\n        width: 30px;\r\n        height: 30px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        margin-left: 18px;\r\n        cursor: pointer;\r\n      }\r\n\r\n      .LayoutPersonalDoList {\r\n        width: 26px;\r\n        height: 26px;\r\n        cursor: pointer;\r\n        background: url('../img/layout_personal_do_list.png') no-repeat;\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .LayoutBoxMessage {\r\n        width: 26px;\r\n        height: 26px;\r\n        cursor: pointer;\r\n        background: url('../img/layout_box_message.png') no-repeat;\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .LayoutOperation {\r\n        width: 26px;\r\n        height: 26px;\r\n        cursor: pointer;\r\n        background: url('../img/layout_operation.png') no-repeat;\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .LayoutViewOrganization {\r\n        margin-right: 22px;\r\n\r\n        .LayoutViewOrganizationTrigger {\r\n          height: 36px;\r\n          padding: 0 12px;\r\n          border: 1px solid #ffffff;\r\n          border-radius: var(--el-border-radius-base);\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: space-between;\r\n          cursor: pointer;\r\n          min-width: 120px;\r\n\r\n          &:hover {\r\n            background-color: rgba(255, 255, 255, 0.1);\r\n          }\r\n\r\n          .LayoutViewOrganizationText {\r\n            color: #ffffff;\r\n            font-size: var(--zy-name-font-size);\r\n            line-height: var(--zy-line-height);\r\n            white-space: nowrap;\r\n            overflow: hidden;\r\n            text-overflow: ellipsis;\r\n            max-width: 100px;\r\n          }\r\n\r\n          .LayoutViewOrganizationIcon {\r\n            color: #ffffff;\r\n            font-size: 12px;\r\n            margin-left: 8px;\r\n            transition: transform 0.3s;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .LayoutViewContainer {\r\n    width: 100%;\r\n    height: calc(100% - 62px);\r\n    background: var(--zy-el-color-info-light-9);\r\n\r\n    .LayoutViewFloatingWindow {\r\n      width: auto;\r\n      height: 100%;\r\n\r\n      .LayoutViewFloatingWindowBody {\r\n        width: var(--ai-chat-target-width);\r\n        height: 100%;\r\n        background: #fff;\r\n        box-sizing: border-box;\r\n        transform-origin: left center;\r\n        border-left: 1px solid var(--zy-el-border-color-lighter);\r\n      }\r\n\r\n      /* 进入动画 */\r\n      .width-animation-enter-active {\r\n        animation: widen 0.2s ease-in-out forwards;\r\n      }\r\n\r\n      /* 离开动画 */\r\n      .width-animation-leave-active {\r\n        animation: narrow 0.2s ease-in-out forwards;\r\n      }\r\n\r\n      /* 定义进入动画 */\r\n      @keyframes widen {\r\n        from {\r\n          width: 0;\r\n        }\r\n\r\n        to {\r\n          width: var(--ai-chat-target-width);\r\n        }\r\n      }\r\n\r\n      /* 定义离开动画 */\r\n      @keyframes narrow {\r\n        from {\r\n          width: var(--ai-chat-target-width);\r\n        }\r\n\r\n        to {\r\n          width: 0;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LayoutViewAside {\r\n      width: auto;\r\n    }\r\n\r\n    .LayoutViewMain {\r\n      height: 100%;\r\n      padding: var(--zy-distance-three) var(--zy-distance-three) 0 0;\r\n\r\n      .LayoutViewBreadcrumb {\r\n        width: 100%;\r\n        height: calc((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px);\r\n        display: flex;\r\n        align-items: center;\r\n        background-color: #fff;\r\n        padding: 0 var(--zy-distance-two);\r\n        border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n        .zy-el-breadcrumb {\r\n          font-size: var(--zy-name-font-size);\r\n\r\n          .zy-el-breadcrumb__inner {\r\n            cursor: pointer;\r\n            font-weight: bold;\r\n            color: var(--zy-el-color-primary);\r\n          }\r\n\r\n          .zy-el-breadcrumb__item {\r\n            &:last-child {\r\n              .zy-el-breadcrumb__inner {\r\n                cursor: text;\r\n                font-weight: normal;\r\n                color: var(--zy-el-text-color-regular);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .LayoutViewBody {\r\n        width: 100%;\r\n        height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px));\r\n        background-color: #fff;\r\n\r\n        .subApp-viewport {\r\n          width: 100%;\r\n          height: 100%;\r\n\r\n          >div {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .LayoutViewMainView {\r\n      width: 100%;\r\n      padding: 0;\r\n      background: #f8f8f8;\r\n\r\n      .LayoutViewBody {\r\n        height: 100%;\r\n      }\r\n    }\r\n\r\n    .LayoutViewMainBreadcrumb {\r\n      width: 100%;\r\n\r\n      .LayoutViewBody {\r\n        position: relative;\r\n        height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px));\r\n      }\r\n    }\r\n  }\r\n\r\n  .ConstraintEditPassWord {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    z-index: 999;\r\n    background-color: #fff;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .EditPassWord {\r\n      box-shadow: 0px 2px 40px rgba(0, 0, 0, 0.1);\r\n    }\r\n  }\r\n}\r\n\r\n.LayoutViewRoleItem {\r\n  font-size: var(--zy-text-font-size);\r\n  line-height: var(--zy-line-height);\r\n}\r\n</style>\r\n"], "mappings": "AA2IA,SAASA,oBAAoB,QAAQ,KAAK;AAC1C,SAASC,QAAQ,EAAEC,SAAS,QAAQ,YAAY;AAChD,SAASC,OAAO,EAAEC,qBAAqB,EAAEC,UAAU,EAAEC,YAAY,EAAEC,WAAW,QAAQ,4BAA4B;AAClH,SACEC,UAAU,EACVC,UAAU,EACVC,aAAa,EACbC,oBAAoB,EACpBC,YAAY,EACZC,aAAa,EACbC,iBAAiB,QACZ,yBAAyB;AAChC,SAASC,UAAU,EAAEC,SAAS,QAAQ,yBAAyB;AAf/D,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAwB,CAAC;;;;;IAgBhD,IAAMC,YAAY,GAAGnB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,4CAA4C,CAAC;IAAA,EAAC;IACrG,IAAMoB,YAAY,GAAGpB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,4CAA4C,CAAC;IAAA,EAAC;IACrG,IAAMqB,gBAAgB,GAAGrB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,gDAAgD,CAAC;IAAA,EAAC;IAC7G,IAAMsB,oBAAoB,GAAGtB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,oDAAoD,CAAC;IAAA,EAAC;IACrH,IAAMuB,kBAAkB,GAAGvB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,kDAAkD,CAAC;IAAA,EAAC;IACjH,IAAMwB,kBAAkB,GAAGxB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,kDAAkD,CAAC;IAAA,EAAC;IACjH,IAAMyB,oBAAoB,GAAGzB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,oDAAoD,CAAC;IAAA,EAAC;IACrH,IAAM0B,gBAAgB,GAAG1B,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,gDAAgD,CAAC;IAAA,EAAC;IAC7G,IAAM2B,YAAY,GAAG3B,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,8BAA8B,CAAC;IAAA,EAAC;IACvF,IAAM4B,UAAU,GAAG5B,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,wBAAwB,CAAC;IAAA,EAAC;IAC/E,IAAM6B,iBAAiB,GAAG7B,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,oCAAoC,CAAC;IAAA,EAAC;IAClG,IAAM8B,cAAc,GAAG;MACrBZ,IAAI,EAAE,gBAAgB;MACtBa,KAAK,EAAE,CAAC,MAAM,CAAC;MACfC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAAC,QAAA,GAAmB9B,OAAO,CAACF,QAAQ,CAAC,CAAC,CAAC;MAA9BiC,MAAM,GAAAD,QAAA,CAANC,MAAM;IACd,IAAAC,qBAAA,GA4CI/B,qBAAqB,CAACH,QAAQ,CAAC,CAAC,EAAEC,SAAS,CAAC,CAAC,CAAC;MA3ChDkC,IAAI,GAAAD,qBAAA,CAAJC,IAAI;MACJC,IAAI,GAAAF,qBAAA,CAAJE,IAAI;MACJC,IAAI,GAAAH,qBAAA,CAAJG,IAAI;MACJC,IAAI,GAAAJ,qBAAA,CAAJI,IAAI;MACJC,KAAK,GAAAL,qBAAA,CAALK,KAAK;MACLC,aAAa,GAAAN,qBAAA,CAAbM,aAAa;MACbC,cAAc,GAAAP,qBAAA,CAAdO,cAAc;MACdC,QAAQ,GAAAR,qBAAA,CAARQ,QAAQ;MACRC,aAAa,GAAAT,qBAAA,CAAbS,aAAa;MACbC,gBAAgB,GAAAV,qBAAA,CAAhBU,gBAAgB;MAChBC,kBAAkB,GAAAX,qBAAA,CAAlBW,kBAAkB;MAClBC,sBAAsB,GAAAZ,qBAAA,CAAtBY,sBAAsB;MACtBC,oBAAoB,GAAAb,qBAAA,CAApBa,oBAAoB;MACpBC,QAAQ,GAAAd,qBAAA,CAARc,QAAQ;MACRC,UAAU,GAAAf,qBAAA,CAAVe,UAAU;MACVC,YAAY,GAAAhB,qBAAA,CAAZgB,YAAY;MACZC,kBAAkB,GAAAjB,qBAAA,CAAlBiB,kBAAkB;MAClBC,wBAAwB,GAAAlB,qBAAA,CAAxBkB,wBAAwB;MACxBC,MAAM,GAAAnB,qBAAA,CAANmB,MAAM;MACNC,WAAW,GAAApB,qBAAA,CAAXoB,WAAW;MACXC,OAAO,GAAArB,qBAAA,CAAPqB,OAAO;MACPC,WAAW,GAAAtB,qBAAA,CAAXsB,WAAW;MACXC,WAAW,GAAAvB,qBAAA,CAAXuB,WAAW;MACXC,MAAM,GAAAxB,qBAAA,CAANwB,MAAM;MACNC,QAAQ,GAAAzB,qBAAA,CAARyB,QAAQ;MACRC,SAAS,GAAA1B,qBAAA,CAAT0B,SAAS;MACTC,gBAAgB,GAAA3B,qBAAA,CAAhB2B,gBAAgB;MAChBC,YAAY,GAAA5B,qBAAA,CAAZ4B,YAAY;MACZC,SAAS,GAAA7B,qBAAA,CAAT6B,SAAS;MACTC,eAAe,GAAA9B,qBAAA,CAAf8B,eAAe;MACfC,SAAS,GAAA/B,qBAAA,CAAT+B,SAAS;MACTC,cAAc,GAAAhC,qBAAA,CAAdgC,cAAc;MACdC,OAAO,GAAAjC,qBAAA,CAAPiC,OAAO;MACPC,QAAQ,GAAAlC,qBAAA,CAARkC,QAAQ;MACRC,aAAa,GAAAnC,qBAAA,CAAbmC,aAAa;MACbC,WAAW,GAAApC,qBAAA,CAAXoC,WAAW;MACXC,gBAAgB,GAAArC,qBAAA,CAAhBqC,gBAAgB;MAChBC,UAAU,GAAAtC,qBAAA,CAAVsC,UAAU;MACVC,QAAQ,GAAAvC,qBAAA,CAARuC,QAAQ;MACRC,cAAc,GAAAxC,qBAAA,CAAdwC,cAAc;MACdC,mBAAmB,GAAAzC,qBAAA,CAAnByC,mBAAmB;MACnBC,gBAAgB,GAAA1C,qBAAA,CAAhB0C,gBAAgB;MAChBC,wBAAwB,GAAA3C,qBAAA,CAAxB2C,wBAAwB;IAG1B,IAAAC,WAAA,GAA2B1E,UAAU,CAAC,CAAC;MAA/B2E,cAAc,GAAAD,WAAA,CAAdC,cAAc;IACtB,IAAAC,aAAA,GAAgE3E,YAAY,CAAC,CAAC;MAAtE4E,iBAAiB,GAAAD,aAAA,CAAjBC,iBAAiB;MAAEC,cAAc,GAAAF,aAAA,CAAdE,cAAc;MAAEC,gBAAgB,GAAAH,aAAA,CAAhBG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}