{"ast": null, "code": "\"use strict\";\n\nfunction CompressedObject() {\n  this.compressedSize = 0;\n  this.uncompressedSize = 0;\n  this.crc32 = 0;\n  this.compressionMethod = null;\n  this.compressedContent = null;\n}\nCompressedObject.prototype = {\n  /**\n   * Return the decompressed content in an unspecified format.\n   * The format will depend on the decompressor.\n   * @return {Object} the decompressed content.\n   */\n  getContent: function getContent() {\n    return null; // see implementation\n  },\n  /**\n   * Return the compressed content in an unspecified format.\n   * The format will depend on the compressed conten source.\n   * @return {Object} the compressed content.\n   */\n  getCompressedContent: function getCompressedContent() {\n    return null; // see implementation\n  }\n};\nmodule.exports = CompressedObject;", "map": {"version": 3, "names": ["CompressedObject", "compressedSize", "uncompressedSize", "crc32", "compressionMethod", "compressedContent", "prototype", "get<PERSON>ontent", "getCompressedContent", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/pizzip@3.1.7/node_modules/pizzip/js/compressedObject.js"], "sourcesContent": ["\"use strict\";\n\nfunction CompressedObject() {\n  this.compressedSize = 0;\n  this.uncompressedSize = 0;\n  this.crc32 = 0;\n  this.compressionMethod = null;\n  this.compressedContent = null;\n}\nCompressedObject.prototype = {\n  /**\n   * Return the decompressed content in an unspecified format.\n   * The format will depend on the decompressor.\n   * @return {Object} the decompressed content.\n   */\n  getContent: function getContent() {\n    return null; // see implementation\n  },\n  /**\n   * Return the compressed content in an unspecified format.\n   * The format will depend on the compressed conten source.\n   * @return {Object} the compressed content.\n   */\n  getCompressedContent: function getCompressedContent() {\n    return null; // see implementation\n  }\n};\nmodule.exports = CompressedObject;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,gBAAgBA,CAAA,EAAG;EAC1B,IAAI,CAACC,cAAc,GAAG,CAAC;EACvB,IAAI,CAACC,gBAAgB,GAAG,CAAC;EACzB,IAAI,CAACC,KAAK,GAAG,CAAC;EACd,IAAI,CAACC,iBAAiB,GAAG,IAAI;EAC7B,IAAI,CAACC,iBAAiB,GAAG,IAAI;AAC/B;AACAL,gBAAgB,CAACM,SAAS,GAAG;EAC3B;AACF;AACA;AACA;AACA;EACEC,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;IAChC,OAAO,IAAI,CAAC,CAAC;EACf,CAAC;EACD;AACF;AACA;AACA;AACA;EACEC,oBAAoB,EAAE,SAASA,oBAAoBA,CAAA,EAAG;IACpD,OAAO,IAAI,CAAC,CAAC;EACf;AACF,CAAC;AACDC,MAAM,CAACC,OAAO,GAAGV,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}