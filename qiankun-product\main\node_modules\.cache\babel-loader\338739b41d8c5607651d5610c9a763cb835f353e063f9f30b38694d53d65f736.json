{"ast": null, "code": "// Convert input options to more useable format\n// and compile search regexp\n\n'use strict';\n\nfunction quoteRE(str) {\n  return str.replace(/[.?*+^$[\\]\\\\(){}|-]/g, '\\\\$&');\n}\nmodule.exports = function normalize_opts(options) {\n  var emojies = options.defs,\n    shortcuts;\n\n  // Filter emojies by whitelist, if needed\n  if (options.enabled.length) {\n    emojies = Object.keys(emojies).reduce(function (acc, key) {\n      if (options.enabled.indexOf(key) >= 0) {\n        acc[key] = emojies[key];\n      }\n      return acc;\n    }, {});\n  }\n\n  // Flatten shortcuts to simple object: { alias: emoji_name }\n  shortcuts = Object.keys(options.shortcuts).reduce(function (acc, key) {\n    // Skip aliases for filtered emojies, to reduce regexp\n    if (!emojies[key]) {\n      return acc;\n    }\n    if (Array.isArray(options.shortcuts[key])) {\n      options.shortcuts[key].forEach(function (alias) {\n        acc[alias] = key;\n      });\n      return acc;\n    }\n    acc[options.shortcuts[key]] = key;\n    return acc;\n  }, {});\n  var keys = Object.keys(emojies),\n    names;\n\n  // If no definitions are given, return empty regex to avoid replacements with 'undefined'.\n  if (keys.length === 0) {\n    names = '^$';\n  } else {\n    // Compile regexp\n    names = keys.map(function (name) {\n      return ':' + name + ':';\n    }).concat(Object.keys(shortcuts)).sort().reverse().map(function (name) {\n      return quoteRE(name);\n    }).join('|');\n  }\n  var scanRE = RegExp(names);\n  var replaceRE = RegExp(names, 'g');\n  return {\n    defs: emojies,\n    shortcuts: shortcuts,\n    scanRE: scanRE,\n    replaceRE: replaceRE\n  };\n};", "map": {"version": 3, "names": ["quoteRE", "str", "replace", "module", "exports", "normalize_opts", "options", "emojies", "defs", "shortcuts", "enabled", "length", "Object", "keys", "reduce", "acc", "key", "indexOf", "Array", "isArray", "for<PERSON>ach", "alias", "names", "map", "name", "concat", "sort", "reverse", "join", "scanRE", "RegExp", "replaceRE"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it-emoji@2.0.2/node_modules/markdown-it-emoji/lib/normalize_opts.js"], "sourcesContent": ["// Convert input options to more useable format\n// and compile search regexp\n\n'use strict';\n\n\nfunction quoteRE(str) {\n  return str.replace(/[.?*+^$[\\]\\\\(){}|-]/g, '\\\\$&');\n}\n\n\nmodule.exports = function normalize_opts(options) {\n  var emojies = options.defs,\n      shortcuts;\n\n  // Filter emojies by whitelist, if needed\n  if (options.enabled.length) {\n    emojies = Object.keys(emojies).reduce(function (acc, key) {\n      if (options.enabled.indexOf(key) >= 0) {\n        acc[key] = emojies[key];\n      }\n      return acc;\n    }, {});\n  }\n\n  // Flatten shortcuts to simple object: { alias: emoji_name }\n  shortcuts = Object.keys(options.shortcuts).reduce(function (acc, key) {\n    // Skip aliases for filtered emojies, to reduce regexp\n    if (!emojies[key]) { return acc; }\n\n    if (Array.isArray(options.shortcuts[key])) {\n      options.shortcuts[key].forEach(function (alias) {\n        acc[alias] = key;\n      });\n      return acc;\n    }\n\n    acc[options.shortcuts[key]] = key;\n    return acc;\n  }, {});\n\n  var keys = Object.keys(emojies),\n      names;\n\n  // If no definitions are given, return empty regex to avoid replacements with 'undefined'.\n  if (keys.length === 0) {\n    names = '^$';\n  } else {\n    // Compile regexp\n    names = keys\n      .map(function (name) { return ':' + name + ':'; })\n      .concat(Object.keys(shortcuts))\n      .sort()\n      .reverse()\n      .map(function (name) { return quoteRE(name); })\n      .join('|');\n  }\n  var scanRE = RegExp(names);\n  var replaceRE = RegExp(names, 'g');\n\n  return {\n    defs: emojies,\n    shortcuts: shortcuts,\n    scanRE: scanRE,\n    replaceRE: replaceRE\n  };\n};\n"], "mappings": "AAAA;AACA;;AAEA,YAAY;;AAGZ,SAASA,OAAOA,CAACC,GAAG,EAAE;EACpB,OAAOA,GAAG,CAACC,OAAO,CAAC,sBAAsB,EAAE,MAAM,CAAC;AACpD;AAGAC,MAAM,CAACC,OAAO,GAAG,SAASC,cAAcA,CAACC,OAAO,EAAE;EAChD,IAAIC,OAAO,GAAGD,OAAO,CAACE,IAAI;IACtBC,SAAS;;EAEb;EACA,IAAIH,OAAO,CAACI,OAAO,CAACC,MAAM,EAAE;IAC1BJ,OAAO,GAAGK,MAAM,CAACC,IAAI,CAACN,OAAO,CAAC,CAACO,MAAM,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;MACxD,IAAIV,OAAO,CAACI,OAAO,CAACO,OAAO,CAACD,GAAG,CAAC,IAAI,CAAC,EAAE;QACrCD,GAAG,CAACC,GAAG,CAAC,GAAGT,OAAO,CAACS,GAAG,CAAC;MACzB;MACA,OAAOD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EACR;;EAEA;EACAN,SAAS,GAAGG,MAAM,CAACC,IAAI,CAACP,OAAO,CAACG,SAAS,CAAC,CAACK,MAAM,CAAC,UAAUC,GAAG,EAAEC,GAAG,EAAE;IACpE;IACA,IAAI,CAACT,OAAO,CAACS,GAAG,CAAC,EAAE;MAAE,OAAOD,GAAG;IAAE;IAEjC,IAAIG,KAAK,CAACC,OAAO,CAACb,OAAO,CAACG,SAAS,CAACO,GAAG,CAAC,CAAC,EAAE;MACzCV,OAAO,CAACG,SAAS,CAACO,GAAG,CAAC,CAACI,OAAO,CAAC,UAAUC,KAAK,EAAE;QAC9CN,GAAG,CAACM,KAAK,CAAC,GAAGL,GAAG;MAClB,CAAC,CAAC;MACF,OAAOD,GAAG;IACZ;IAEAA,GAAG,CAACT,OAAO,CAACG,SAAS,CAACO,GAAG,CAAC,CAAC,GAAGA,GAAG;IACjC,OAAOD,GAAG;EACZ,CAAC,EAAE,CAAC,CAAC,CAAC;EAEN,IAAIF,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACN,OAAO,CAAC;IAC3Be,KAAK;;EAET;EACA,IAAIT,IAAI,CAACF,MAAM,KAAK,CAAC,EAAE;IACrBW,KAAK,GAAG,IAAI;EACd,CAAC,MAAM;IACL;IACAA,KAAK,GAAGT,IAAI,CACTU,GAAG,CAAC,UAAUC,IAAI,EAAE;MAAE,OAAO,GAAG,GAAGA,IAAI,GAAG,GAAG;IAAE,CAAC,CAAC,CACjDC,MAAM,CAACb,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAAC,CAC9BiB,IAAI,CAAC,CAAC,CACNC,OAAO,CAAC,CAAC,CACTJ,GAAG,CAAC,UAAUC,IAAI,EAAE;MAAE,OAAOxB,OAAO,CAACwB,IAAI,CAAC;IAAE,CAAC,CAAC,CAC9CI,IAAI,CAAC,GAAG,CAAC;EACd;EACA,IAAIC,MAAM,GAAGC,MAAM,CAACR,KAAK,CAAC;EAC1B,IAAIS,SAAS,GAAGD,MAAM,CAACR,KAAK,EAAE,GAAG,CAAC;EAElC,OAAO;IACLd,IAAI,EAAED,OAAO;IACbE,SAAS,EAAEA,SAAS;IACpBoB,MAAM,EAAEA,MAAM;IACdE,SAAS,EAAEA;EACb,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}