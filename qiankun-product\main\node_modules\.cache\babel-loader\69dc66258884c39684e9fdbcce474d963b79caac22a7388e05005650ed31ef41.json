{"ast": null, "code": "// Horizontal rule\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\nmodule.exports = function hr(state, startLine, endLine, silent) {\n  var marker,\n    cnt,\n    ch,\n    token,\n    pos = state.bMarks[startLine] + state.tShift[startLine],\n    max = state.eMarks[startLine];\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) {\n    return false;\n  }\n  marker = state.src.charCodeAt(pos++);\n\n  // Check hr marker\n  if (marker !== 0x2A /* * */ && marker !== 0x2D /* - */ && marker !== 0x5F /* _ */) {\n    return false;\n  }\n\n  // markers can be mixed with spaces, but there should be at least 3 of them\n\n  cnt = 1;\n  while (pos < max) {\n    ch = state.src.charCodeAt(pos++);\n    if (ch !== marker && !isSpace(ch)) {\n      return false;\n    }\n    if (ch === marker) {\n      cnt++;\n    }\n  }\n  if (cnt < 3) {\n    return false;\n  }\n  if (silent) {\n    return true;\n  }\n  state.line = startLine + 1;\n  token = state.push('hr', 'hr', 0);\n  token.map = [startLine, state.line];\n  token.markup = Array(cnt + 1).join(String.fromCharCode(marker));\n  return true;\n};", "map": {"version": 3, "names": ["isSpace", "require", "module", "exports", "hr", "state", "startLine", "endLine", "silent", "marker", "cnt", "ch", "token", "pos", "bMarks", "tShift", "max", "eMarks", "sCount", "blkIndent", "src", "charCodeAt", "line", "push", "map", "markup", "Array", "join", "String", "fromCharCode"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_block/hr.js"], "sourcesContent": ["// Horizontal rule\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\n\n\nmodule.exports = function hr(state, startLine, endLine, silent) {\n  var marker, cnt, ch, token,\n      pos = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine];\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false; }\n\n  marker = state.src.charCodeAt(pos++);\n\n  // Check hr marker\n  if (marker !== 0x2A/* * */ &&\n      marker !== 0x2D/* - */ &&\n      marker !== 0x5F/* _ */) {\n    return false;\n  }\n\n  // markers can be mixed with spaces, but there should be at least 3 of them\n\n  cnt = 1;\n  while (pos < max) {\n    ch = state.src.charCodeAt(pos++);\n    if (ch !== marker && !isSpace(ch)) { return false; }\n    if (ch === marker) { cnt++; }\n  }\n\n  if (cnt < 3) { return false; }\n\n  if (silent) { return true; }\n\n  state.line = startLine + 1;\n\n  token        = state.push('hr', 'hr', 0);\n  token.map    = [ startLine, state.line ];\n  token.markup = Array(cnt + 1).join(String.fromCharCode(marker));\n\n  return true;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,iBAAiB,CAAC,CAACD,OAAO;AAGhDE,MAAM,CAACC,OAAO,GAAG,SAASC,EAAEA,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAE;EAC9D,IAAIC,MAAM;IAAEC,GAAG;IAAEC,EAAE;IAAEC,KAAK;IACtBC,GAAG,GAAGR,KAAK,CAACS,MAAM,CAACR,SAAS,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,SAAS,CAAC;IACvDU,GAAG,GAAGX,KAAK,CAACY,MAAM,CAACX,SAAS,CAAC;;EAEjC;EACA,IAAID,KAAK,CAACa,MAAM,CAACZ,SAAS,CAAC,GAAGD,KAAK,CAACc,SAAS,IAAI,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAEpEV,MAAM,GAAGJ,KAAK,CAACe,GAAG,CAACC,UAAU,CAACR,GAAG,EAAE,CAAC;;EAEpC;EACA,IAAIJ,MAAM,KAAK,IAAI,YACfA,MAAM,KAAK,IAAI,YACfA,MAAM,KAAK,IAAI,UAAS;IAC1B,OAAO,KAAK;EACd;;EAEA;;EAEAC,GAAG,GAAG,CAAC;EACP,OAAOG,GAAG,GAAGG,GAAG,EAAE;IAChBL,EAAE,GAAGN,KAAK,CAACe,GAAG,CAACC,UAAU,CAACR,GAAG,EAAE,CAAC;IAChC,IAAIF,EAAE,KAAKF,MAAM,IAAI,CAACT,OAAO,CAACW,EAAE,CAAC,EAAE;MAAE,OAAO,KAAK;IAAE;IACnD,IAAIA,EAAE,KAAKF,MAAM,EAAE;MAAEC,GAAG,EAAE;IAAE;EAC9B;EAEA,IAAIA,GAAG,GAAG,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAE7B,IAAIF,MAAM,EAAE;IAAE,OAAO,IAAI;EAAE;EAE3BH,KAAK,CAACiB,IAAI,GAAGhB,SAAS,GAAG,CAAC;EAE1BM,KAAK,GAAUP,KAAK,CAACkB,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;EACxCX,KAAK,CAACY,GAAG,GAAM,CAAElB,SAAS,EAAED,KAAK,CAACiB,IAAI,CAAE;EACxCV,KAAK,CAACa,MAAM,GAAGC,KAAK,CAAChB,GAAG,GAAG,CAAC,CAAC,CAACiB,IAAI,CAACC,MAAM,CAACC,YAAY,CAACpB,MAAM,CAAC,CAAC;EAE/D,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}