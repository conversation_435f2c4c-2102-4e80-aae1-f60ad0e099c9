{"ast": null, "code": "\"use strict\";\n\nvar USE_TYPEDARRAY = typeof Uint8Array !== \"undefined\" && typeof Uint16Array !== \"undefined\" && typeof Uint32Array !== \"undefined\";\nvar pako = require(\"pako/dist/pako.es5.min.js\");\nexports.uncompressInputType = USE_TYPEDARRAY ? \"uint8array\" : \"array\";\nexports.compressInputType = USE_TYPEDARRAY ? \"uint8array\" : \"array\";\nexports.magic = \"\\x08\\x00\";\nexports.compress = function (input, compressionOptions) {\n  return pako.deflateRaw(input, {\n    level: compressionOptions.level || -1 // default compression\n  });\n};\nexports.uncompress = function (input) {\n  return pako.inflateRaw(input);\n};", "map": {"version": 3, "names": ["USE_TYPEDARRAY", "Uint8Array", "Uint16Array", "Uint32Array", "pako", "require", "exports", "uncompressInputType", "compressInputType", "magic", "compress", "input", "compressionOptions", "deflateRaw", "level", "uncompress", "inflateRaw"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/pizzip@3.1.7/node_modules/pizzip/js/flate.js"], "sourcesContent": ["\"use strict\";\n\nvar USE_TYPEDARRAY = typeof Uint8Array !== \"undefined\" && typeof Uint16Array !== \"undefined\" && typeof Uint32Array !== \"undefined\";\nvar pako = require(\"pako/dist/pako.es5.min.js\");\nexports.uncompressInputType = USE_TYPEDARRAY ? \"uint8array\" : \"array\";\nexports.compressInputType = USE_TYPEDARRAY ? \"uint8array\" : \"array\";\nexports.magic = \"\\x08\\x00\";\nexports.compress = function (input, compressionOptions) {\n  return pako.deflateRaw(input, {\n    level: compressionOptions.level || -1 // default compression\n  });\n};\nexports.uncompress = function (input) {\n  return pako.inflateRaw(input);\n};"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,cAAc,GAAG,OAAOC,UAAU,KAAK,WAAW,IAAI,OAAOC,WAAW,KAAK,WAAW,IAAI,OAAOC,WAAW,KAAK,WAAW;AAClI,IAAIC,IAAI,GAAGC,OAAO,CAAC,2BAA2B,CAAC;AAC/CC,OAAO,CAACC,mBAAmB,GAAGP,cAAc,GAAG,YAAY,GAAG,OAAO;AACrEM,OAAO,CAACE,iBAAiB,GAAGR,cAAc,GAAG,YAAY,GAAG,OAAO;AACnEM,OAAO,CAACG,KAAK,GAAG,UAAU;AAC1BH,OAAO,CAACI,QAAQ,GAAG,UAAUC,KAAK,EAAEC,kBAAkB,EAAE;EACtD,OAAOR,IAAI,CAACS,UAAU,CAACF,KAAK,EAAE;IAC5BG,KAAK,EAAEF,kBAAkB,CAACE,KAAK,IAAI,CAAC,CAAC,CAAC;EACxC,CAAC,CAAC;AACJ,CAAC;AACDR,OAAO,CAACS,UAAU,GAAG,UAAUJ,KAAK,EAAE;EACpC,OAAOP,IAAI,CAACY,UAAU,CAACL,KAAK,CAAC;AAC/B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}