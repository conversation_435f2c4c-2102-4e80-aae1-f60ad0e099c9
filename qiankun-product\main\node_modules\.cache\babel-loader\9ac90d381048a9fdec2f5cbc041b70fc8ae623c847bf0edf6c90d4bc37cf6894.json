{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _regeneratorRuntime from \"@babel/runtime/regenerator\";\nvar rawPublicPath = window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__;\nexport default function getAddOn(global) {\n  var publicPath = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '/';\n  var hasMountedOnce = false;\n  return {\n    beforeLoad: function beforeLoad() {\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              // eslint-disable-next-line no-param-reassign\n              global.__INJECTED_PUBLIC_PATH_BY_QIANKUN__ = publicPath;\n            case 1:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }))();\n    },\n    beforeMount: function beforeMount() {\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              if (hasMountedOnce) {\n                // eslint-disable-next-line no-param-reassign\n                global.__INJECTED_PUBLIC_PATH_BY_QIANKUN__ = publicPath;\n              }\n            case 1:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }))();\n    },\n    beforeUnmount: function beforeUnmount() {\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              if (rawPublicPath === undefined) {\n                // eslint-disable-next-line no-param-reassign\n                delete global.__INJECTED_PUBLIC_PATH_BY_QIANKUN__;\n              } else {\n                // eslint-disable-next-line no-param-reassign\n                global.__INJECTED_PUBLIC_PATH_BY_QIANKUN__ = rawPublicPath;\n              }\n              hasMountedOnce = true;\n            case 2:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }))();\n    }\n  };\n}", "map": {"version": 3, "names": ["_asyncToGenerator", "_regeneratorRuntime", "rawPublicPath", "window", "__INJECTED_PUBLIC_PATH_BY_QIANKUN__", "getAddOn", "global", "publicPath", "arguments", "length", "undefined", "hasMountedOnce", "beforeLoad", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "stop", "beforeMount", "_callee2", "_callee2$", "_context2", "beforeUnmount", "_callee3", "_callee3$", "_context3"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/qiankun@2.10.16/node_modules/qiankun/es/addons/runtimePublicPath.js"], "sourcesContent": ["import _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _regeneratorRuntime from \"@babel/runtime/regenerator\";\nvar rawPublicPath = window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__;\nexport default function getAddOn(global) {\n  var publicPath = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : '/';\n  var hasMountedOnce = false;\n  return {\n    beforeLoad: function beforeLoad() {\n      return _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              // eslint-disable-next-line no-param-reassign\n              global.__INJECTED_PUBLIC_PATH_BY_QIANKUN__ = publicPath;\n            case 1:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }))();\n    },\n    beforeMount: function beforeMount() {\n      return _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              if (hasMountedOnce) {\n                // eslint-disable-next-line no-param-reassign\n                global.__INJECTED_PUBLIC_PATH_BY_QIANKUN__ = publicPath;\n              }\n            case 1:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }))();\n    },\n    beforeUnmount: function beforeUnmount() {\n      return _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              if (rawPublicPath === undefined) {\n                // eslint-disable-next-line no-param-reassign\n                delete global.__INJECTED_PUBLIC_PATH_BY_QIANKUN__;\n              } else {\n                // eslint-disable-next-line no-param-reassign\n                global.__INJECTED_PUBLIC_PATH_BY_QIANKUN__ = rawPublicPath;\n              }\n              hasMountedOnce = true;\n            case 2:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }))();\n    }\n  };\n}"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,mBAAmB,MAAM,4BAA4B;AAC5D,IAAIC,aAAa,GAAGC,MAAM,CAACC,mCAAmC;AAC9D,eAAe,SAASC,QAAQA,CAACC,MAAM,EAAE;EACvC,IAAIC,UAAU,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,GAAG;EACxF,IAAIG,cAAc,GAAG,KAAK;EAC1B,OAAO;IACLC,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;MAChC,OAAOZ,iBAAiB,CAAE,aAAaC,mBAAmB,CAACY,IAAI,CAAC,SAASC,OAAOA,CAAA,EAAG;QACjF,OAAOb,mBAAmB,CAACc,IAAI,CAAC,SAASC,QAAQA,CAACC,QAAQ,EAAE;UAC1D,OAAO,CAAC,EAAE,QAAQA,QAAQ,CAACC,IAAI,GAAGD,QAAQ,CAACE,IAAI;YAC7C,KAAK,CAAC;cACJ;cACAb,MAAM,CAACF,mCAAmC,GAAGG,UAAU;YACzD,KAAK,CAAC;YACN,KAAK,KAAK;cACR,OAAOU,QAAQ,CAACG,IAAI,CAAC,CAAC;UAC1B;QACF,CAAC,EAAEN,OAAO,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC;IACDO,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;MAClC,OAAOrB,iBAAiB,CAAE,aAAaC,mBAAmB,CAACY,IAAI,CAAC,SAASS,QAAQA,CAAA,EAAG;QAClF,OAAOrB,mBAAmB,CAACc,IAAI,CAAC,SAASQ,SAASA,CAACC,SAAS,EAAE;UAC5D,OAAO,CAAC,EAAE,QAAQA,SAAS,CAACN,IAAI,GAAGM,SAAS,CAACL,IAAI;YAC/C,KAAK,CAAC;cACJ,IAAIR,cAAc,EAAE;gBAClB;gBACAL,MAAM,CAACF,mCAAmC,GAAGG,UAAU;cACzD;YACF,KAAK,CAAC;YACN,KAAK,KAAK;cACR,OAAOiB,SAAS,CAACJ,IAAI,CAAC,CAAC;UAC3B;QACF,CAAC,EAAEE,QAAQ,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC;IACDG,aAAa,EAAE,SAASA,aAAaA,CAAA,EAAG;MACtC,OAAOzB,iBAAiB,CAAE,aAAaC,mBAAmB,CAACY,IAAI,CAAC,SAASa,QAAQA,CAAA,EAAG;QAClF,OAAOzB,mBAAmB,CAACc,IAAI,CAAC,SAASY,SAASA,CAACC,SAAS,EAAE;UAC5D,OAAO,CAAC,EAAE,QAAQA,SAAS,CAACV,IAAI,GAAGU,SAAS,CAACT,IAAI;YAC/C,KAAK,CAAC;cACJ,IAAIjB,aAAa,KAAKQ,SAAS,EAAE;gBAC/B;gBACA,OAAOJ,MAAM,CAACF,mCAAmC;cACnD,CAAC,MAAM;gBACL;gBACAE,MAAM,CAACF,mCAAmC,GAAGF,aAAa;cAC5D;cACAS,cAAc,GAAG,IAAI;YACvB,KAAK,CAAC;YACN,KAAK,KAAK;cACR,OAAOiB,SAAS,CAACR,IAAI,CAAC,CAAC;UAC3B;QACF,CAAC,EAAEM,QAAQ,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC;IACP;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}