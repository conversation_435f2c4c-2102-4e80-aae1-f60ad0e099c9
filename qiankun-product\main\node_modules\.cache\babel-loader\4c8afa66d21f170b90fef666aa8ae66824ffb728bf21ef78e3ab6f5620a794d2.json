{"ast": null, "code": "\"use strict\";\n\nvar DataReader = require(\"./dataReader.js\");\nfunction ArrayReader(data) {\n  if (data) {\n    this.data = data;\n    this.length = this.data.length;\n    this.index = 0;\n    this.zero = 0;\n    for (var i = 0; i < this.data.length; i++) {\n      data[i] &= data[i];\n    }\n  }\n}\nArrayReader.prototype = new DataReader();\n/**\n * @see DataReader.byteAt\n */\nArrayReader.prototype.byteAt = function (i) {\n  return this.data[this.zero + i];\n};\n/**\n * @see DataReader.lastIndexOfSignature\n */\nArrayReader.prototype.lastIndexOfSignature = function (sig) {\n  var sig0 = sig.charCodeAt(0),\n    sig1 = sig.charCodeAt(1),\n    sig2 = sig.charCodeAt(2),\n    sig3 = sig.charCodeAt(3);\n  for (var i = this.length - 4; i >= 0; --i) {\n    if (this.data[i] === sig0 && this.data[i + 1] === sig1 && this.data[i + 2] === sig2 && this.data[i + 3] === sig3) {\n      return i - this.zero;\n    }\n  }\n  return -1;\n};\n/**\n * @see DataReader.readData\n */\nArrayReader.prototype.readData = function (size) {\n  this.checkOffset(size);\n  if (size === 0) {\n    return [];\n  }\n  var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n  this.index += size;\n  return result;\n};\nmodule.exports = ArrayReader;", "map": {"version": 3, "names": ["DataReader", "require", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "data", "length", "index", "zero", "i", "prototype", "byteAt", "lastIndexOfSignature", "sig", "sig0", "charCodeAt", "sig1", "sig2", "sig3", "readData", "size", "checkOffset", "result", "slice", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/pizzip@3.1.7/node_modules/pizzip/js/arrayReader.js"], "sourcesContent": ["\"use strict\";\n\nvar DataReader = require(\"./dataReader.js\");\nfunction ArrayReader(data) {\n  if (data) {\n    this.data = data;\n    this.length = this.data.length;\n    this.index = 0;\n    this.zero = 0;\n    for (var i = 0; i < this.data.length; i++) {\n      data[i] &= data[i];\n    }\n  }\n}\nArrayReader.prototype = new DataReader();\n/**\n * @see DataReader.byteAt\n */\nArrayReader.prototype.byteAt = function (i) {\n  return this.data[this.zero + i];\n};\n/**\n * @see DataReader.lastIndexOfSignature\n */\nArrayReader.prototype.lastIndexOfSignature = function (sig) {\n  var sig0 = sig.charCodeAt(0),\n    sig1 = sig.charCodeAt(1),\n    sig2 = sig.charCodeAt(2),\n    sig3 = sig.charCodeAt(3);\n  for (var i = this.length - 4; i >= 0; --i) {\n    if (this.data[i] === sig0 && this.data[i + 1] === sig1 && this.data[i + 2] === sig2 && this.data[i + 3] === sig3) {\n      return i - this.zero;\n    }\n  }\n  return -1;\n};\n/**\n * @see DataReader.readData\n */\nArrayReader.prototype.readData = function (size) {\n  this.checkOffset(size);\n  if (size === 0) {\n    return [];\n  }\n  var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n  this.index += size;\n  return result;\n};\nmodule.exports = ArrayReader;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAC3C,SAASC,WAAWA,CAACC,IAAI,EAAE;EACzB,IAAIA,IAAI,EAAE;IACR,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAG,IAAI,CAACD,IAAI,CAACC,MAAM;IAC9B,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACJ,IAAI,CAACC,MAAM,EAAEG,CAAC,EAAE,EAAE;MACzCJ,IAAI,CAACI,CAAC,CAAC,IAAIJ,IAAI,CAACI,CAAC,CAAC;IACpB;EACF;AACF;AACAL,WAAW,CAACM,SAAS,GAAG,IAAIR,UAAU,CAAC,CAAC;AACxC;AACA;AACA;AACAE,WAAW,CAACM,SAAS,CAACC,MAAM,GAAG,UAAUF,CAAC,EAAE;EAC1C,OAAO,IAAI,CAACJ,IAAI,CAAC,IAAI,CAACG,IAAI,GAAGC,CAAC,CAAC;AACjC,CAAC;AACD;AACA;AACA;AACAL,WAAW,CAACM,SAAS,CAACE,oBAAoB,GAAG,UAAUC,GAAG,EAAE;EAC1D,IAAIC,IAAI,GAAGD,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC;IAC1BC,IAAI,GAAGH,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC;IACxBE,IAAI,GAAGJ,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC;IACxBG,IAAI,GAAGL,GAAG,CAACE,UAAU,CAAC,CAAC,CAAC;EAC1B,KAAK,IAAIN,CAAC,GAAG,IAAI,CAACH,MAAM,GAAG,CAAC,EAAEG,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;IACzC,IAAI,IAAI,CAACJ,IAAI,CAACI,CAAC,CAAC,KAAKK,IAAI,IAAI,IAAI,CAACT,IAAI,CAACI,CAAC,GAAG,CAAC,CAAC,KAAKO,IAAI,IAAI,IAAI,CAACX,IAAI,CAACI,CAAC,GAAG,CAAC,CAAC,KAAKQ,IAAI,IAAI,IAAI,CAACZ,IAAI,CAACI,CAAC,GAAG,CAAC,CAAC,KAAKS,IAAI,EAAE;MAChH,OAAOT,CAAC,GAAG,IAAI,CAACD,IAAI;IACtB;EACF;EACA,OAAO,CAAC,CAAC;AACX,CAAC;AACD;AACA;AACA;AACAJ,WAAW,CAACM,SAAS,CAACS,QAAQ,GAAG,UAAUC,IAAI,EAAE;EAC/C,IAAI,CAACC,WAAW,CAACD,IAAI,CAAC;EACtB,IAAIA,IAAI,KAAK,CAAC,EAAE;IACd,OAAO,EAAE;EACX;EACA,IAAIE,MAAM,GAAG,IAAI,CAACjB,IAAI,CAACkB,KAAK,CAAC,IAAI,CAACf,IAAI,GAAG,IAAI,CAACD,KAAK,EAAE,IAAI,CAACC,IAAI,GAAG,IAAI,CAACD,KAAK,GAAGa,IAAI,CAAC;EACnF,IAAI,CAACb,KAAK,IAAIa,IAAI;EAClB,OAAOE,MAAM;AACf,CAAC;AACDE,MAAM,CAACC,OAAO,GAAGrB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}