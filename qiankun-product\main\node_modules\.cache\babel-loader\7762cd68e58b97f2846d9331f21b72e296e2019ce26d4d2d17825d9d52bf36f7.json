{"ast": null, "code": "export var firstTime = function firstTime(to, next, callback) {\n  if (to.query.token) {\n    sessionStorage.setItem('token', to.query.token);\n    sessionStorage.setItem('query', JSON.stringify(to.query));\n  }\n  if (to.query.client_id && to.query.redirect_uri) {\n    sessionStorage.setItem('client_id', to.query.client_id);\n    sessionStorage.setItem('redirect_uri', to.query.redirect_uri);\n  }\n  callback();\n};", "map": {"version": 3, "names": ["firstTime", "to", "next", "callback", "query", "token", "sessionStorage", "setItem", "JSON", "stringify", "client_id", "redirect_uri"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/config/entry.js"], "sourcesContent": ["export const firstTime = (to, next, callback) => {\r\n  if (to.query.token) {\r\n    sessionStorage.setItem('token', to.query.token)\r\n    sessionStorage.setItem('query', JSON.stringify(to.query))\r\n  }\r\n  if (to.query.client_id && to.query.redirect_uri) {\r\n    sessionStorage.setItem('client_id', to.query.client_id)\r\n    sessionStorage.setItem('redirect_uri', to.query.redirect_uri)\r\n  }\r\n  callback()\r\n}\r\n"], "mappings": "AAAA,OAAO,IAAMA,SAAS,GAAG,SAAZA,SAASA,CAAIC,EAAE,EAAEC,IAAI,EAAEC,QAAQ,EAAK;EAC/C,IAAIF,EAAE,CAACG,KAAK,CAACC,KAAK,EAAE;IAClBC,cAAc,CAACC,OAAO,CAAC,OAAO,EAAEN,EAAE,CAACG,KAAK,CAACC,KAAK,CAAC;IAC/CC,cAAc,CAACC,OAAO,CAAC,OAAO,EAAEC,IAAI,CAACC,SAAS,CAACR,EAAE,CAACG,KAAK,CAAC,CAAC;EAC3D;EACA,IAAIH,EAAE,CAACG,KAAK,CAACM,SAAS,IAAIT,EAAE,CAACG,KAAK,CAACO,YAAY,EAAE;IAC/CL,cAAc,CAACC,OAAO,CAAC,WAAW,EAAEN,EAAE,CAACG,KAAK,CAACM,SAAS,CAAC;IACvDJ,cAAc,CAACC,OAAO,CAAC,cAAc,EAAEN,EAAE,CAACG,KAAK,CAACO,YAAY,CAAC;EAC/D;EACAR,QAAQ,CAAC,CAAC;AACZ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}