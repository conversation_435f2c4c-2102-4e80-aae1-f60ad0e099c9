{"ast": null, "code": "import { resolveComponent as _resolveComponent, with<PERSON><PERSON><PERSON> as _withKeys, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"LiveManagement\"\n};\nvar _hoisted_2 = {\n  class: \"globalTable\"\n};\nvar _hoisted_3 = {\n  class: \"globalPagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_xyl_search_button = _resolveComponent(\"xyl-search-button\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_el_link = _resolveComponent(\"el-link\");\n  var _component_xyl_global_table_button = _resolveComponent(\"xyl-global-table-button\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(_Fragment, null, [_createElementVNode(\"div\", _hoisted_1, [_createVNode(_component_xyl_search_button, {\n    onQueryClick: $setup.handleQuery,\n    onResetClick: $setup.handleReset,\n    onHandleButton: $setup.handleButton,\n    buttonList: $setup.buttonList\n  }, {\n    search: _withCtx(function () {\n      return [_createVNode(_component_el_input, {\n        modelValue: $setup.keyword,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.keyword = $event;\n        }),\n        placeholder: \"请输入关键词\",\n        onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onQueryClick\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_table, {\n    ref: \"tableRef\",\n    \"row-key\": \"id\",\n    data: $setup.tableData,\n    onSelect: $setup.handleTableSelect,\n    onSelectAll: $setup.handleTableSelect\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        \"reserve-selection\": \"\",\n        width: \"60\",\n        fixed: \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"直播标题\",\n        \"min-width\": \"200\",\n        \"show-overflow-tooltip\": \"\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createVNode(_component_el_link, {\n            type: \"primary\",\n            onClick: function onClick($event) {\n              return $setup.handleDetails(scope.row);\n            }\n          }, {\n            default: _withCtx(function () {\n              return [_createTextVNode(_toDisplayString(scope.row.theme), 1 /* TEXT */)];\n            }),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"直播计划开始时间\",\n        width: \"200\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createTextVNode(_toDisplayString($setup.format(scope.row.startTime)), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"直播计划结束时间\",\n        width: \"200\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createTextVNode(_toDisplayString($setup.format(scope.row.endTime)), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"直播状态\",\n        width: \"140\",\n        prop: \"meetingStatus\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"直播互动\",\n        width: \"140\",\n        prop: \"\"\n      }), _createVNode(_component_xyl_global_table_button, {\n        data: $setup.tableButtonList,\n        onButtonClick: $setup.handleCommand,\n        elWhetherShow: $setup.handleElWhetherShow\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelect\", \"onSelectAll\"])]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.pageNo,\n    \"onUpdate:currentPage\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.pageNo = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": $setup.pageSizes,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleQuery,\n    onCurrentChange: $setup.handleQuery,\n    total: $setup.totals,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"page-sizes\", \"onSizeChange\", \"onCurrentChange\", \"total\"])])]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.show,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.show = $event;\n    }),\n    name: $setup.id ? '编辑' : '新增'\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"LiveManagementNew\"], {\n        id: $setup.id,\n        onCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"name\"])], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_Fragment", "_createElementVNode", "_hoisted_1", "_createVNode", "_component_xyl_search_button", "onQueryClick", "$setup", "handleQuery", "onResetClick", "handleReset", "onHandleButton", "handleButton", "buttonList", "search", "_withCtx", "_component_el_input", "modelValue", "keyword", "_cache", "$event", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "clearable", "_", "_hoisted_2", "_component_el_table", "ref", "data", "tableData", "onSelect", "handleTableSelect", "onSelectAll", "default", "_component_el_table_column", "type", "width", "fixed", "label", "scope", "_component_el_link", "onClick", "handleDetails", "row", "_createTextVNode", "_toDisplayString", "theme", "format", "startTime", "endTime", "prop", "_component_xyl_global_table_button", "tableButtonList", "onButtonClick", "handleCommand", "elWhetherShow", "handleElWhetherShow", "_hoisted_3", "_component_el_pagination", "currentPage", "pageNo", "pageSize", "pageSizes", "layout", "onSizeChange", "onCurrentChange", "total", "totals", "background", "_component_xyl_popup_window", "show", "name", "id", "onCallback", "callback"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\LiveManagement.vue"], "sourcesContent": ["<template>\r\n  <div class=\"LiveManagement\">\r\n    <xyl-search-button @queryClick=\"handleQuery\" @resetClick=\"handleReset\" @handleButton=\"handleButton\"\r\n      :buttonList=\"buttonList\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\" row-key=\"id\" :data=\"tableData\" @select=\"handleTableSelect\"\r\n        @select-all=\"handleTableSelect\">\r\n        <el-table-column type=\"selection\" reserve-selection width=\"60\" fixed />\r\n        <el-table-column label=\"直播标题\" min-width=\"200\" show-overflow-tooltip>\r\n          <template #default=\"scope\">\r\n            <el-link type=\"primary\" @click=\"handleDetails(scope.row)\">{{ scope.row.theme }}</el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"直播计划开始时间\" width=\"200\">\r\n          <template #default=\"scope\">{{ format(scope.row.startTime) }}</template>\r\n        </el-table-column>\r\n        <el-table-column label=\"直播计划结束时间\" width=\"200\">\r\n          <template #default=\"scope\">{{ format(scope.row.endTime) }}</template>\r\n        </el-table-column>\r\n        <el-table-column label=\"直播状态\" width=\"140\" prop=\"meetingStatus\" />\r\n        <el-table-column label=\"直播互动\" width=\"140\" prop=\"\" />\r\n        <xyl-global-table-button :data=\"tableButtonList\" @buttonClick=\"handleCommand\"\r\n          :elWhetherShow=\"handleElWhetherShow\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\" v-model:page-size=\"pageSize\" :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\" @size-change=\"handleQuery\" @current-change=\"handleQuery\"\r\n        :total=\"totals\" background />\r\n    </div>\r\n  </div>\r\n  <xyl-popup-window v-model=\"show\" :name=\"id ? '编辑' : '新增'\">\r\n    <LiveManagementNew :id=\"id\" @callback=\"callback\"></LiveManagementNew>\r\n  </xyl-popup-window>\r\n</template>\r\n<script>\r\nexport default { name: 'LiveManagement' }\r\n</script>\r\n<script setup>\r\nimport { ref, onActivated } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport LiveManagementNew from './LiveManagementNew'\r\nconst buttonList = [\r\n  { id: 'del', name: '删除', type: 'primary', has: 'del' }\r\n]\r\nconst tableButtonList = [\r\n  // { id: 'liveConsole', name: '直播控制台', width: 100, has: 'liveConsole', whetherShow: true },\r\n  { id: 'edit', name: '编辑', width: 100, has: 'edit', whetherShow: true }\r\n]\r\nconst id = ref()\r\nconst show = ref(false)\r\nconst {\r\n  keyword,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableData,\r\n  handleQuery,\r\n  handleTableSelect,\r\n  handleDel,\r\n  tableRefReset\r\n} = GlobalTable({ tableApi: 'videoConnectionList', delApi: 'videoConnectionDelLive', tableDataObj: { query: { isLive: 1 } } })\r\nonActivated(() => {\r\n  handleQuery()\r\n})\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'del':\r\n      handleDel('直播')\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleCommand = (row, isType) => {\r\n  switch (isType) {\r\n    case 'liveConsole':\r\n      console.log('点击了直播控制台')\r\n      break\r\n    case 'edit':\r\n      console.log('点击了编辑')\r\n      handleEdit(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\nconst handleEdit = (row) => {\r\n  id.value = row.id\r\n  show.value = true\r\n}\r\nconst handleDetails = (item) => {\r\n  qiankunMicro.setGlobalState({ openRoute: { name: '视频会议详情', path: '/interaction/VideoMeetinDetails', query: { id: item.id } } })\r\n}\r\nconst handleElWhetherShow = (row, isType) => {\r\n  console.log('row===>', row)\r\n  console.log('isType===>', isType)\r\n  if (isType === 'xx') {\r\n    return true\r\n  }\r\n  return true\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  handleQuery()\r\n  show.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.LiveManagement {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAgB;;EAOpBA,KAAK,EAAC;AAAa;;EAqBnBA,KAAK,EAAC;AAAkB;;;;;;;;;;uBA7BjCC,mBAAA,CAAAC,SAAA,SACEC,mBAAA,CAiCM,OAjCNC,UAiCM,GAhCJC,YAAA,CAKoBC,4BAAA;IALAC,YAAU,EAAEC,MAAA,CAAAC,WAAW;IAAGC,YAAU,EAAEF,MAAA,CAAAG,WAAW;IAAGC,cAAY,EAAEJ,MAAA,CAAAK,YAAY;IAC/FC,UAAU,EAAEN,MAAA,CAAAM;;IACFC,MAAM,EAAAC,QAAA,CACf;MAAA,OAAwF,CAAxFX,YAAA,CAAwFY,mBAAA;QALhGC,UAAA,EAK2BV,MAAA,CAAAW,OAAO;QALlC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAK2Bb,MAAA,CAAAW,OAAO,GAAAE,MAAA;QAAA;QAAEC,WAAW,EAAC,QAAQ;QAAEC,OAAK,EAL/DC,SAAA,CAKuEhB,MAAA,CAAAC,WAAW;QAAEgB,SAAS,EAAT;;;IALpFC,CAAA;uCAQIvB,mBAAA,CAoBM,OApBNwB,UAoBM,GAnBJtB,YAAA,CAkBWuB,mBAAA;IAlBDC,GAAG,EAAC,UAAU;IAAC,SAAO,EAAC,IAAI;IAAEC,IAAI,EAAEtB,MAAA,CAAAuB,SAAS;IAAGC,QAAM,EAAExB,MAAA,CAAAyB,iBAAiB;IAC/EC,WAAU,EAAE1B,MAAA,CAAAyB;;IAVrBE,OAAA,EAAAnB,QAAA,CAWQ;MAAA,OAAuE,CAAvEX,YAAA,CAAuE+B,0BAAA;QAAtDC,IAAI,EAAC,WAAW;QAAC,mBAAiB,EAAjB,EAAiB;QAACC,KAAK,EAAC,IAAI;QAACC,KAAK,EAAL;UAC/DlC,YAAA,CAIkB+B,0BAAA;QAJDI,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC,KAAK;QAAC,uBAAqB,EAArB;;QACjCL,OAAO,EAAAnB,QAAA,CAChB,UAAyFyB,KADlE;UAAA,QACvBpC,YAAA,CAAyFqC,kBAAA;YAAhFL,IAAI,EAAC,SAAS;YAAEM,OAAK,WAALA,OAAKA,CAAAtB,MAAA;cAAA,OAAEb,MAAA,CAAAoC,aAAa,CAACH,KAAK,CAACI,GAAG;YAAA;;YAdnEV,OAAA,EAAAnB,QAAA,CAcsE;cAAA,OAAqB,CAd3F8B,gBAAA,CAAAC,gBAAA,CAcyEN,KAAK,CAACI,GAAG,CAACG,KAAK,iB;;YAdxFtB,CAAA;;;QAAAA,CAAA;UAiBQrB,YAAA,CAEkB+B,0BAAA;QAFDI,KAAK,EAAC,UAAU;QAACF,KAAK,EAAC;;QAC3BH,OAAO,EAAAnB,QAAA,CAAS,UAAiCyB,KAAnC;UAAA,QAlBnCK,gBAAA,CAAAC,gBAAA,CAkBwCvC,MAAA,CAAAyC,MAAM,CAACR,KAAK,CAACI,GAAG,CAACK,SAAS,kB;;QAlBlExB,CAAA;UAoBQrB,YAAA,CAEkB+B,0BAAA;QAFDI,KAAK,EAAC,UAAU;QAACF,KAAK,EAAC;;QAC3BH,OAAO,EAAAnB,QAAA,CAAS,UAA+ByB,KAAjC;UAAA,QArBnCK,gBAAA,CAAAC,gBAAA,CAqBwCvC,MAAA,CAAAyC,MAAM,CAACR,KAAK,CAACI,GAAG,CAACM,OAAO,kB;;QArBhEzB,CAAA;UAuBQrB,YAAA,CAAiE+B,0BAAA;QAAhDI,KAAK,EAAC,MAAM;QAACF,KAAK,EAAC,KAAK;QAACc,IAAI,EAAC;UAC/C/C,YAAA,CAAoD+B,0BAAA;QAAnCI,KAAK,EAAC,MAAM;QAACF,KAAK,EAAC,KAAK;QAACc,IAAI,EAAC;UAC/C/C,YAAA,CACiEgD,kCAAA;QADvCvB,IAAI,EAAEtB,MAAA,CAAA8C,eAAe;QAAGC,aAAW,EAAE/C,MAAA,CAAAgD,aAAa;QACzEC,aAAa,EAAEjD,MAAA,CAAAkD;;;IA1B1BhC,CAAA;4DA6BIvB,mBAAA,CAIM,OAJNwD,UAIM,GAHJtD,YAAA,CAE+BuD,wBAAA;IAFRC,WAAW,EAAErD,MAAA,CAAAsD,MAAM;IA9BhD,wBAAA1C,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA8B0Cb,MAAA,CAAAsD,MAAM,GAAAzC,MAAA;IAAA;IAAU,WAAS,EAAEb,MAAA,CAAAuD,QAAQ;IA9B7E,qBAAA3C,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA8BqEb,MAAA,CAAAuD,QAAQ,GAAA1C,MAAA;IAAA;IAAG,YAAU,EAAEb,MAAA,CAAAwD,SAAS;IAC7FC,MAAM,EAAC,yCAAyC;IAAEC,YAAW,EAAE1D,MAAA,CAAAC,WAAW;IAAG0D,eAAc,EAAE3D,MAAA,CAAAC,WAAW;IACvG2D,KAAK,EAAE5D,MAAA,CAAA6D,MAAM;IAAEC,UAAU,EAAV;uHAGtBjE,YAAA,CAEmBkE,2BAAA;IArCrBrD,UAAA,EAmC6BV,MAAA,CAAAgE,IAAI;IAnCjC,uBAAApD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAmC6Bb,MAAA,CAAAgE,IAAI,GAAAnD,MAAA;IAAA;IAAGoD,IAAI,EAAEjE,MAAA,CAAAkE,EAAE;;IAnC5CvC,OAAA,EAAAnB,QAAA,CAoCI;MAAA,OAAqE,CAArEX,YAAA,CAAqEG,MAAA;QAAjDkE,EAAE,EAAElE,MAAA,CAAAkE,EAAE;QAAGC,UAAQ,EAAEnE,MAAA,CAAAoE;;;IApC3ClD,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}