{"ast": null, "code": "\"use strict\";\n\nvar StringReader = require(\"./stringReader.js\");\nvar utils = require(\"./utils.js\");\nvar CompressedObject = require(\"./compressedObject.js\");\nvar pizzipProto = require(\"./object.js\");\nvar support = require(\"./support.js\");\nvar MADE_BY_DOS = 0x00;\nvar MADE_BY_UNIX = 0x03;\n\n// class ZipEntry {{{\n/**\n * An entry in the zip file.\n * @constructor\n * @param {Object} options Options of the current file.\n * @param {Object} loadOptions Options for loading the stream.\n */\nfunction ZipEntry(options, loadOptions) {\n  this.options = options;\n  this.loadOptions = loadOptions;\n}\nZipEntry.prototype = {\n  /**\n   * say if the file is encrypted.\n   * @return {boolean} true if the file is encrypted, false otherwise.\n   */\n  isEncrypted: function isEncrypted() {\n    // bit 1 is set\n    return (this.bitFlag & 0x0001) === 0x0001;\n  },\n  /**\n   * say if the file has utf-8 filename/comment.\n   * @return {boolean} true if the filename/comment is in utf-8, false otherwise.\n   */\n  useUTF8: function useUTF8() {\n    // bit 11 is set\n    return (this.bitFlag & 0x0800) === 0x0800;\n  },\n  /**\n   * Prepare the function used to generate the compressed content from this ZipFile.\n   * @param {DataReader} reader the reader to use.\n   * @param {number} from the offset from where we should read the data.\n   * @param {number} length the length of the data to read.\n   * @return {Function} the callback to get the compressed content (the type depends of the DataReader class).\n   */\n  prepareCompressedContent: function prepareCompressedContent(reader, from, length) {\n    return function () {\n      var previousIndex = reader.index;\n      reader.setIndex(from);\n      var compressedFileData = reader.readData(length);\n      reader.setIndex(previousIndex);\n      return compressedFileData;\n    };\n  },\n  /**\n   * Prepare the function used to generate the uncompressed content from this ZipFile.\n   * @param {DataReader} reader the reader to use.\n   * @param {number} from the offset from where we should read the data.\n   * @param {number} length the length of the data to read.\n   * @param {PizZip.compression} compression the compression used on this file.\n   * @param {number} uncompressedSize the uncompressed size to expect.\n   * @return {Function} the callback to get the uncompressed content (the type depends of the DataReader class).\n   */\n  prepareContent: function prepareContent(reader, from, length, compression, uncompressedSize) {\n    return function () {\n      var compressedFileData = utils.transformTo(compression.uncompressInputType, this.getCompressedContent());\n      var uncompressedFileData = compression.uncompress(compressedFileData);\n      if (uncompressedFileData.length !== uncompressedSize) {\n        throw new Error(\"Bug : uncompressed data size mismatch\");\n      }\n      return uncompressedFileData;\n    };\n  },\n  /**\n   * Read the local part of a zip file and add the info in this object.\n   * @param {DataReader} reader the reader to use.\n   */\n  readLocalPart: function readLocalPart(reader) {\n    // we already know everything from the central dir !\n    // If the central dir data are false, we are doomed.\n    // On the bright side, the local part is scary  : zip64, data descriptors, both, etc.\n    // The less data we get here, the more reliable this should be.\n    // Let's skip the whole header and dash to the data !\n    reader.skip(22);\n    // in some zip created on windows, the filename stored in the central dir contains \\ instead of /.\n    // Strangely, the filename here is OK.\n    // I would love to treat these zip files as corrupted (see http://www.info-zip.org/FAQ.html#backslashes\n    // or APPNOTE#********, \"All slashes MUST be forward slashes '/'\") but there are a lot of bad zip generators...\n    // Search \"unzip mismatching \"local\" filename continuing with \"central\" filename version\" on\n    // the internet.\n    //\n    // I think I see the logic here : the central directory is used to display\n    // content and the local directory is used to extract the files. Mixing / and \\\n    // may be used to display \\ to windows users and use / when extracting the files.\n    // Unfortunately, this lead also to some issues : http://seclists.org/fulldisclosure/2009/Sep/394\n    this.fileNameLength = reader.readInt(2);\n    var localExtraFieldsLength = reader.readInt(2); // can't be sure this will be the same as the central dir\n    this.fileName = reader.readData(this.fileNameLength);\n    reader.skip(localExtraFieldsLength);\n    if (this.compressedSize === -1 || this.uncompressedSize === -1) {\n      throw new Error(\"Bug or corrupted zip : didn't get enough informations from the central directory \" + \"(compressedSize == -1 || uncompressedSize == -1)\");\n    }\n    var compression = utils.findCompression(this.compressionMethod);\n    if (compression === null) {\n      // no compression found\n      throw new Error(\"Corrupted zip : compression \" + utils.pretty(this.compressionMethod) + \" unknown (inner file : \" + utils.transformTo(\"string\", this.fileName) + \")\");\n    }\n    this.decompressed = new CompressedObject();\n    this.decompressed.compressedSize = this.compressedSize;\n    this.decompressed.uncompressedSize = this.uncompressedSize;\n    this.decompressed.crc32 = this.crc32;\n    this.decompressed.compressionMethod = this.compressionMethod;\n    this.decompressed.getCompressedContent = this.prepareCompressedContent(reader, reader.index, this.compressedSize, compression);\n    this.decompressed.getContent = this.prepareContent(reader, reader.index, this.compressedSize, compression, this.uncompressedSize);\n\n    // we need to compute the crc32...\n    if (this.loadOptions.checkCRC32) {\n      this.decompressed = utils.transformTo(\"string\", this.decompressed.getContent());\n      if (pizzipProto.crc32(this.decompressed) !== this.crc32) {\n        throw new Error(\"Corrupted zip : CRC32 mismatch\");\n      }\n    }\n  },\n  /**\n   * Read the central part of a zip file and add the info in this object.\n   * @param {DataReader} reader the reader to use.\n   */\n  readCentralPart: function readCentralPart(reader) {\n    this.versionMadeBy = reader.readInt(2);\n    this.versionNeeded = reader.readInt(2);\n    this.bitFlag = reader.readInt(2);\n    this.compressionMethod = reader.readString(2);\n    this.date = reader.readDate();\n    this.crc32 = reader.readInt(4);\n    this.compressedSize = reader.readInt(4);\n    this.uncompressedSize = reader.readInt(4);\n    this.fileNameLength = reader.readInt(2);\n    this.extraFieldsLength = reader.readInt(2);\n    this.fileCommentLength = reader.readInt(2);\n    this.diskNumberStart = reader.readInt(2);\n    this.internalFileAttributes = reader.readInt(2);\n    this.externalFileAttributes = reader.readInt(4);\n    this.localHeaderOffset = reader.readInt(4);\n    if (this.isEncrypted()) {\n      throw new Error(\"Encrypted zip are not supported\");\n    }\n    this.fileName = reader.readData(this.fileNameLength);\n    this.readExtraFields(reader);\n    this.parseZIP64ExtraField(reader);\n    this.fileComment = reader.readData(this.fileCommentLength);\n  },\n  /**\n   * Parse the external file attributes and get the unix/dos permissions.\n   */\n  processAttributes: function processAttributes() {\n    this.unixPermissions = null;\n    this.dosPermissions = null;\n    var madeBy = this.versionMadeBy >> 8;\n\n    // Check if we have the DOS directory flag set.\n    // We look for it in the DOS and UNIX permissions\n    // but some unknown platform could set it as a compatibility flag.\n    this.dir = !!(this.externalFileAttributes & 0x0010);\n    if (madeBy === MADE_BY_DOS) {\n      // first 6 bits (0 to 5)\n      this.dosPermissions = this.externalFileAttributes & 0x3f;\n    }\n    if (madeBy === MADE_BY_UNIX) {\n      this.unixPermissions = this.externalFileAttributes >> 16 & 0xffff;\n      // the octal permissions are in (this.unixPermissions & 0x01FF).toString(8);\n    }\n\n    // fail safe : if the name ends with a / it probably means a folder\n    if (!this.dir && this.fileNameStr.slice(-1) === \"/\") {\n      this.dir = true;\n    }\n  },\n  /**\n   * Parse the ZIP64 extra field and merge the info in the current ZipEntry.\n   */\n  parseZIP64ExtraField: function parseZIP64ExtraField() {\n    if (!this.extraFields[0x0001]) {\n      return;\n    }\n\n    // should be something, preparing the extra reader\n    var extraReader = new StringReader(this.extraFields[0x0001].value);\n\n    // I really hope that these 64bits integer can fit in 32 bits integer, because js\n    // won't let us have more.\n    if (this.uncompressedSize === utils.MAX_VALUE_32BITS) {\n      this.uncompressedSize = extraReader.readInt(8);\n    }\n    if (this.compressedSize === utils.MAX_VALUE_32BITS) {\n      this.compressedSize = extraReader.readInt(8);\n    }\n    if (this.localHeaderOffset === utils.MAX_VALUE_32BITS) {\n      this.localHeaderOffset = extraReader.readInt(8);\n    }\n    if (this.diskNumberStart === utils.MAX_VALUE_32BITS) {\n      this.diskNumberStart = extraReader.readInt(4);\n    }\n  },\n  /**\n   * Read the central part of a zip file and add the info in this object.\n   * @param {DataReader} reader the reader to use.\n   */\n  readExtraFields: function readExtraFields(reader) {\n    var start = reader.index;\n    var extraFieldId, extraFieldLength, extraFieldValue;\n    this.extraFields = this.extraFields || {};\n    while (reader.index < start + this.extraFieldsLength) {\n      extraFieldId = reader.readInt(2);\n      extraFieldLength = reader.readInt(2);\n      extraFieldValue = reader.readString(extraFieldLength);\n      this.extraFields[extraFieldId] = {\n        id: extraFieldId,\n        length: extraFieldLength,\n        value: extraFieldValue\n      };\n    }\n  },\n  /**\n   * Apply an UTF8 transformation if needed.\n   */\n  handleUTF8: function handleUTF8() {\n    var decodeParamType = support.uint8array ? \"uint8array\" : \"array\";\n    if (this.useUTF8()) {\n      this.fileNameStr = pizzipProto.utf8decode(this.fileName);\n      this.fileCommentStr = pizzipProto.utf8decode(this.fileComment);\n    } else {\n      var upath = this.findExtraFieldUnicodePath();\n      if (upath !== null) {\n        this.fileNameStr = upath;\n      } else {\n        var fileNameByteArray = utils.transformTo(decodeParamType, this.fileName);\n        this.fileNameStr = this.loadOptions.decodeFileName(fileNameByteArray);\n      }\n      var ucomment = this.findExtraFieldUnicodeComment();\n      if (ucomment !== null) {\n        this.fileCommentStr = ucomment;\n      } else {\n        var commentByteArray = utils.transformTo(decodeParamType, this.fileComment);\n        this.fileCommentStr = this.loadOptions.decodeFileName(commentByteArray);\n      }\n    }\n  },\n  /**\n   * Find the unicode path declared in the extra field, if any.\n   * @return {String} the unicode path, null otherwise.\n   */\n  findExtraFieldUnicodePath: function findExtraFieldUnicodePath() {\n    var upathField = this.extraFields[0x7075];\n    if (upathField) {\n      var extraReader = new StringReader(upathField.value);\n\n      // wrong version\n      if (extraReader.readInt(1) !== 1) {\n        return null;\n      }\n\n      // the crc of the filename changed, this field is out of date.\n      if (pizzipProto.crc32(this.fileName) !== extraReader.readInt(4)) {\n        return null;\n      }\n      return pizzipProto.utf8decode(extraReader.readString(upathField.length - 5));\n    }\n    return null;\n  },\n  /**\n   * Find the unicode comment declared in the extra field, if any.\n   * @return {String} the unicode comment, null otherwise.\n   */\n  findExtraFieldUnicodeComment: function findExtraFieldUnicodeComment() {\n    var ucommentField = this.extraFields[0x6375];\n    if (ucommentField) {\n      var extraReader = new StringReader(ucommentField.value);\n\n      // wrong version\n      if (extraReader.readInt(1) !== 1) {\n        return null;\n      }\n\n      // the crc of the comment changed, this field is out of date.\n      if (pizzipProto.crc32(this.fileComment) !== extraReader.readInt(4)) {\n        return null;\n      }\n      return pizzipProto.utf8decode(extraReader.readString(ucommentField.length - 5));\n    }\n    return null;\n  }\n};\nmodule.exports = ZipEntry;", "map": {"version": 3, "names": ["StringReader", "require", "utils", "CompressedObject", "pizzipProto", "support", "MADE_BY_DOS", "MADE_BY_UNIX", "ZipEntry", "options", "loadOptions", "prototype", "isEncrypted", "bitFlag", "useUTF8", "prepareCompressedContent", "reader", "from", "length", "previousIndex", "index", "setIndex", "compressedFileData", "readData", "prepareContent", "compression", "uncompressedSize", "transformTo", "uncompressInputType", "getCompressedContent", "uncompressedFileData", "uncompress", "Error", "readLocalPart", "skip", "fileNameLength", "readInt", "localExtraFieldsLength", "fileName", "compressedSize", "findCompression", "compressionMethod", "pretty", "decompressed", "crc32", "get<PERSON>ontent", "checkCRC32", "readCentralPart", "versionMadeBy", "versionNeeded", "readString", "date", "readDate", "extraFields<PERSON><PERSON>th", "fileCommentLength", "diskNumberStart", "internalFileAttributes", "externalFileAttributes", "localHeaderOffset", "readExtraFields", "parseZIP64ExtraField", "fileComment", "processAttributes", "unixPermissions", "dosPermissions", "<PERSON><PERSON><PERSON>", "dir", "fileNameStr", "slice", "extraFields", "extraReader", "value", "MAX_VALUE_32BITS", "start", "extraFieldId", "extraField<PERSON>ength", "extraFieldValue", "id", "handleUTF8", "decodeParamType", "uint8array", "utf8decode", "fileCommentStr", "upath", "findExtraFieldUnicodePath", "fileNameByteArray", "decodeFileName", "ucomment", "findExtraFieldUnicodeComment", "commentByteArray", "upathField", "ucommentField", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/pizzip@3.1.7/node_modules/pizzip/js/zipEntry.js"], "sourcesContent": ["\"use strict\";\n\nvar StringReader = require(\"./stringReader.js\");\nvar utils = require(\"./utils.js\");\nvar CompressedObject = require(\"./compressedObject.js\");\nvar pizzipProto = require(\"./object.js\");\nvar support = require(\"./support.js\");\nvar MADE_BY_DOS = 0x00;\nvar MADE_BY_UNIX = 0x03;\n\n// class ZipEntry {{{\n/**\n * An entry in the zip file.\n * @constructor\n * @param {Object} options Options of the current file.\n * @param {Object} loadOptions Options for loading the stream.\n */\nfunction ZipEntry(options, loadOptions) {\n  this.options = options;\n  this.loadOptions = loadOptions;\n}\nZipEntry.prototype = {\n  /**\n   * say if the file is encrypted.\n   * @return {boolean} true if the file is encrypted, false otherwise.\n   */\n  isEncrypted: function isEncrypted() {\n    // bit 1 is set\n    return (this.bitFlag & 0x0001) === 0x0001;\n  },\n  /**\n   * say if the file has utf-8 filename/comment.\n   * @return {boolean} true if the filename/comment is in utf-8, false otherwise.\n   */\n  useUTF8: function useUTF8() {\n    // bit 11 is set\n    return (this.bitFlag & 0x0800) === 0x0800;\n  },\n  /**\n   * Prepare the function used to generate the compressed content from this ZipFile.\n   * @param {DataReader} reader the reader to use.\n   * @param {number} from the offset from where we should read the data.\n   * @param {number} length the length of the data to read.\n   * @return {Function} the callback to get the compressed content (the type depends of the DataReader class).\n   */\n  prepareCompressedContent: function prepareCompressedContent(reader, from, length) {\n    return function () {\n      var previousIndex = reader.index;\n      reader.setIndex(from);\n      var compressedFileData = reader.readData(length);\n      reader.setIndex(previousIndex);\n      return compressedFileData;\n    };\n  },\n  /**\n   * Prepare the function used to generate the uncompressed content from this ZipFile.\n   * @param {DataReader} reader the reader to use.\n   * @param {number} from the offset from where we should read the data.\n   * @param {number} length the length of the data to read.\n   * @param {PizZip.compression} compression the compression used on this file.\n   * @param {number} uncompressedSize the uncompressed size to expect.\n   * @return {Function} the callback to get the uncompressed content (the type depends of the DataReader class).\n   */\n  prepareContent: function prepareContent(reader, from, length, compression, uncompressedSize) {\n    return function () {\n      var compressedFileData = utils.transformTo(compression.uncompressInputType, this.getCompressedContent());\n      var uncompressedFileData = compression.uncompress(compressedFileData);\n      if (uncompressedFileData.length !== uncompressedSize) {\n        throw new Error(\"Bug : uncompressed data size mismatch\");\n      }\n      return uncompressedFileData;\n    };\n  },\n  /**\n   * Read the local part of a zip file and add the info in this object.\n   * @param {DataReader} reader the reader to use.\n   */\n  readLocalPart: function readLocalPart(reader) {\n    // we already know everything from the central dir !\n    // If the central dir data are false, we are doomed.\n    // On the bright side, the local part is scary  : zip64, data descriptors, both, etc.\n    // The less data we get here, the more reliable this should be.\n    // Let's skip the whole header and dash to the data !\n    reader.skip(22);\n    // in some zip created on windows, the filename stored in the central dir contains \\ instead of /.\n    // Strangely, the filename here is OK.\n    // I would love to treat these zip files as corrupted (see http://www.info-zip.org/FAQ.html#backslashes\n    // or APPNOTE#********, \"All slashes MUST be forward slashes '/'\") but there are a lot of bad zip generators...\n    // Search \"unzip mismatching \"local\" filename continuing with \"central\" filename version\" on\n    // the internet.\n    //\n    // I think I see the logic here : the central directory is used to display\n    // content and the local directory is used to extract the files. Mixing / and \\\n    // may be used to display \\ to windows users and use / when extracting the files.\n    // Unfortunately, this lead also to some issues : http://seclists.org/fulldisclosure/2009/Sep/394\n    this.fileNameLength = reader.readInt(2);\n    var localExtraFieldsLength = reader.readInt(2); // can't be sure this will be the same as the central dir\n    this.fileName = reader.readData(this.fileNameLength);\n    reader.skip(localExtraFieldsLength);\n    if (this.compressedSize === -1 || this.uncompressedSize === -1) {\n      throw new Error(\"Bug or corrupted zip : didn't get enough informations from the central directory \" + \"(compressedSize == -1 || uncompressedSize == -1)\");\n    }\n    var compression = utils.findCompression(this.compressionMethod);\n    if (compression === null) {\n      // no compression found\n      throw new Error(\"Corrupted zip : compression \" + utils.pretty(this.compressionMethod) + \" unknown (inner file : \" + utils.transformTo(\"string\", this.fileName) + \")\");\n    }\n    this.decompressed = new CompressedObject();\n    this.decompressed.compressedSize = this.compressedSize;\n    this.decompressed.uncompressedSize = this.uncompressedSize;\n    this.decompressed.crc32 = this.crc32;\n    this.decompressed.compressionMethod = this.compressionMethod;\n    this.decompressed.getCompressedContent = this.prepareCompressedContent(reader, reader.index, this.compressedSize, compression);\n    this.decompressed.getContent = this.prepareContent(reader, reader.index, this.compressedSize, compression, this.uncompressedSize);\n\n    // we need to compute the crc32...\n    if (this.loadOptions.checkCRC32) {\n      this.decompressed = utils.transformTo(\"string\", this.decompressed.getContent());\n      if (pizzipProto.crc32(this.decompressed) !== this.crc32) {\n        throw new Error(\"Corrupted zip : CRC32 mismatch\");\n      }\n    }\n  },\n  /**\n   * Read the central part of a zip file and add the info in this object.\n   * @param {DataReader} reader the reader to use.\n   */\n  readCentralPart: function readCentralPart(reader) {\n    this.versionMadeBy = reader.readInt(2);\n    this.versionNeeded = reader.readInt(2);\n    this.bitFlag = reader.readInt(2);\n    this.compressionMethod = reader.readString(2);\n    this.date = reader.readDate();\n    this.crc32 = reader.readInt(4);\n    this.compressedSize = reader.readInt(4);\n    this.uncompressedSize = reader.readInt(4);\n    this.fileNameLength = reader.readInt(2);\n    this.extraFieldsLength = reader.readInt(2);\n    this.fileCommentLength = reader.readInt(2);\n    this.diskNumberStart = reader.readInt(2);\n    this.internalFileAttributes = reader.readInt(2);\n    this.externalFileAttributes = reader.readInt(4);\n    this.localHeaderOffset = reader.readInt(4);\n    if (this.isEncrypted()) {\n      throw new Error(\"Encrypted zip are not supported\");\n    }\n    this.fileName = reader.readData(this.fileNameLength);\n    this.readExtraFields(reader);\n    this.parseZIP64ExtraField(reader);\n    this.fileComment = reader.readData(this.fileCommentLength);\n  },\n  /**\n   * Parse the external file attributes and get the unix/dos permissions.\n   */\n  processAttributes: function processAttributes() {\n    this.unixPermissions = null;\n    this.dosPermissions = null;\n    var madeBy = this.versionMadeBy >> 8;\n\n    // Check if we have the DOS directory flag set.\n    // We look for it in the DOS and UNIX permissions\n    // but some unknown platform could set it as a compatibility flag.\n    this.dir = !!(this.externalFileAttributes & 0x0010);\n    if (madeBy === MADE_BY_DOS) {\n      // first 6 bits (0 to 5)\n      this.dosPermissions = this.externalFileAttributes & 0x3f;\n    }\n    if (madeBy === MADE_BY_UNIX) {\n      this.unixPermissions = this.externalFileAttributes >> 16 & 0xffff;\n      // the octal permissions are in (this.unixPermissions & 0x01FF).toString(8);\n    }\n\n    // fail safe : if the name ends with a / it probably means a folder\n    if (!this.dir && this.fileNameStr.slice(-1) === \"/\") {\n      this.dir = true;\n    }\n  },\n  /**\n   * Parse the ZIP64 extra field and merge the info in the current ZipEntry.\n   */\n  parseZIP64ExtraField: function parseZIP64ExtraField() {\n    if (!this.extraFields[0x0001]) {\n      return;\n    }\n\n    // should be something, preparing the extra reader\n    var extraReader = new StringReader(this.extraFields[0x0001].value);\n\n    // I really hope that these 64bits integer can fit in 32 bits integer, because js\n    // won't let us have more.\n    if (this.uncompressedSize === utils.MAX_VALUE_32BITS) {\n      this.uncompressedSize = extraReader.readInt(8);\n    }\n    if (this.compressedSize === utils.MAX_VALUE_32BITS) {\n      this.compressedSize = extraReader.readInt(8);\n    }\n    if (this.localHeaderOffset === utils.MAX_VALUE_32BITS) {\n      this.localHeaderOffset = extraReader.readInt(8);\n    }\n    if (this.diskNumberStart === utils.MAX_VALUE_32BITS) {\n      this.diskNumberStart = extraReader.readInt(4);\n    }\n  },\n  /**\n   * Read the central part of a zip file and add the info in this object.\n   * @param {DataReader} reader the reader to use.\n   */\n  readExtraFields: function readExtraFields(reader) {\n    var start = reader.index;\n    var extraFieldId, extraFieldLength, extraFieldValue;\n    this.extraFields = this.extraFields || {};\n    while (reader.index < start + this.extraFieldsLength) {\n      extraFieldId = reader.readInt(2);\n      extraFieldLength = reader.readInt(2);\n      extraFieldValue = reader.readString(extraFieldLength);\n      this.extraFields[extraFieldId] = {\n        id: extraFieldId,\n        length: extraFieldLength,\n        value: extraFieldValue\n      };\n    }\n  },\n  /**\n   * Apply an UTF8 transformation if needed.\n   */\n  handleUTF8: function handleUTF8() {\n    var decodeParamType = support.uint8array ? \"uint8array\" : \"array\";\n    if (this.useUTF8()) {\n      this.fileNameStr = pizzipProto.utf8decode(this.fileName);\n      this.fileCommentStr = pizzipProto.utf8decode(this.fileComment);\n    } else {\n      var upath = this.findExtraFieldUnicodePath();\n      if (upath !== null) {\n        this.fileNameStr = upath;\n      } else {\n        var fileNameByteArray = utils.transformTo(decodeParamType, this.fileName);\n        this.fileNameStr = this.loadOptions.decodeFileName(fileNameByteArray);\n      }\n      var ucomment = this.findExtraFieldUnicodeComment();\n      if (ucomment !== null) {\n        this.fileCommentStr = ucomment;\n      } else {\n        var commentByteArray = utils.transformTo(decodeParamType, this.fileComment);\n        this.fileCommentStr = this.loadOptions.decodeFileName(commentByteArray);\n      }\n    }\n  },\n  /**\n   * Find the unicode path declared in the extra field, if any.\n   * @return {String} the unicode path, null otherwise.\n   */\n  findExtraFieldUnicodePath: function findExtraFieldUnicodePath() {\n    var upathField = this.extraFields[0x7075];\n    if (upathField) {\n      var extraReader = new StringReader(upathField.value);\n\n      // wrong version\n      if (extraReader.readInt(1) !== 1) {\n        return null;\n      }\n\n      // the crc of the filename changed, this field is out of date.\n      if (pizzipProto.crc32(this.fileName) !== extraReader.readInt(4)) {\n        return null;\n      }\n      return pizzipProto.utf8decode(extraReader.readString(upathField.length - 5));\n    }\n    return null;\n  },\n  /**\n   * Find the unicode comment declared in the extra field, if any.\n   * @return {String} the unicode comment, null otherwise.\n   */\n  findExtraFieldUnicodeComment: function findExtraFieldUnicodeComment() {\n    var ucommentField = this.extraFields[0x6375];\n    if (ucommentField) {\n      var extraReader = new StringReader(ucommentField.value);\n\n      // wrong version\n      if (extraReader.readInt(1) !== 1) {\n        return null;\n      }\n\n      // the crc of the comment changed, this field is out of date.\n      if (pizzipProto.crc32(this.fileComment) !== extraReader.readInt(4)) {\n        return null;\n      }\n      return pizzipProto.utf8decode(extraReader.readString(ucommentField.length - 5));\n    }\n    return null;\n  }\n};\nmodule.exports = ZipEntry;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,mBAAmB,CAAC;AAC/C,IAAIC,KAAK,GAAGD,OAAO,CAAC,YAAY,CAAC;AACjC,IAAIE,gBAAgB,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AACvD,IAAIG,WAAW,GAAGH,OAAO,CAAC,aAAa,CAAC;AACxC,IAAII,OAAO,GAAGJ,OAAO,CAAC,cAAc,CAAC;AACrC,IAAIK,WAAW,GAAG,IAAI;AACtB,IAAIC,YAAY,GAAG,IAAI;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,OAAO,EAAEC,WAAW,EAAE;EACtC,IAAI,CAACD,OAAO,GAAGA,OAAO;EACtB,IAAI,CAACC,WAAW,GAAGA,WAAW;AAChC;AACAF,QAAQ,CAACG,SAAS,GAAG;EACnB;AACF;AACA;AACA;EACEC,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;IAClC;IACA,OAAO,CAAC,IAAI,CAACC,OAAO,GAAG,MAAM,MAAM,MAAM;EAC3C,CAAC;EACD;AACF;AACA;AACA;EACEC,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;IAC1B;IACA,OAAO,CAAC,IAAI,CAACD,OAAO,GAAG,MAAM,MAAM,MAAM;EAC3C,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;EACEE,wBAAwB,EAAE,SAASA,wBAAwBA,CAACC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAE;IAChF,OAAO,YAAY;MACjB,IAAIC,aAAa,GAAGH,MAAM,CAACI,KAAK;MAChCJ,MAAM,CAACK,QAAQ,CAACJ,IAAI,CAAC;MACrB,IAAIK,kBAAkB,GAAGN,MAAM,CAACO,QAAQ,CAACL,MAAM,CAAC;MAChDF,MAAM,CAACK,QAAQ,CAACF,aAAa,CAAC;MAC9B,OAAOG,kBAAkB;IAC3B,CAAC;EACH,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEE,cAAc,EAAE,SAASA,cAAcA,CAACR,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAEO,WAAW,EAAEC,gBAAgB,EAAE;IAC3F,OAAO,YAAY;MACjB,IAAIJ,kBAAkB,GAAGpB,KAAK,CAACyB,WAAW,CAACF,WAAW,CAACG,mBAAmB,EAAE,IAAI,CAACC,oBAAoB,CAAC,CAAC,CAAC;MACxG,IAAIC,oBAAoB,GAAGL,WAAW,CAACM,UAAU,CAACT,kBAAkB,CAAC;MACrE,IAAIQ,oBAAoB,CAACZ,MAAM,KAAKQ,gBAAgB,EAAE;QACpD,MAAM,IAAIM,KAAK,CAAC,uCAAuC,CAAC;MAC1D;MACA,OAAOF,oBAAoB;IAC7B,CAAC;EACH,CAAC;EACD;AACF;AACA;AACA;EACEG,aAAa,EAAE,SAASA,aAAaA,CAACjB,MAAM,EAAE;IAC5C;IACA;IACA;IACA;IACA;IACAA,MAAM,CAACkB,IAAI,CAAC,EAAE,CAAC;IACf;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,cAAc,GAAGnB,MAAM,CAACoB,OAAO,CAAC,CAAC,CAAC;IACvC,IAAIC,sBAAsB,GAAGrB,MAAM,CAACoB,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAChD,IAAI,CAACE,QAAQ,GAAGtB,MAAM,CAACO,QAAQ,CAAC,IAAI,CAACY,cAAc,CAAC;IACpDnB,MAAM,CAACkB,IAAI,CAACG,sBAAsB,CAAC;IACnC,IAAI,IAAI,CAACE,cAAc,KAAK,CAAC,CAAC,IAAI,IAAI,CAACb,gBAAgB,KAAK,CAAC,CAAC,EAAE;MAC9D,MAAM,IAAIM,KAAK,CAAC,mFAAmF,GAAG,kDAAkD,CAAC;IAC3J;IACA,IAAIP,WAAW,GAAGvB,KAAK,CAACsC,eAAe,CAAC,IAAI,CAACC,iBAAiB,CAAC;IAC/D,IAAIhB,WAAW,KAAK,IAAI,EAAE;MACxB;MACA,MAAM,IAAIO,KAAK,CAAC,8BAA8B,GAAG9B,KAAK,CAACwC,MAAM,CAAC,IAAI,CAACD,iBAAiB,CAAC,GAAG,yBAAyB,GAAGvC,KAAK,CAACyB,WAAW,CAAC,QAAQ,EAAE,IAAI,CAACW,QAAQ,CAAC,GAAG,GAAG,CAAC;IACvK;IACA,IAAI,CAACK,YAAY,GAAG,IAAIxC,gBAAgB,CAAC,CAAC;IAC1C,IAAI,CAACwC,YAAY,CAACJ,cAAc,GAAG,IAAI,CAACA,cAAc;IACtD,IAAI,CAACI,YAAY,CAACjB,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC1D,IAAI,CAACiB,YAAY,CAACC,KAAK,GAAG,IAAI,CAACA,KAAK;IACpC,IAAI,CAACD,YAAY,CAACF,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;IAC5D,IAAI,CAACE,YAAY,CAACd,oBAAoB,GAAG,IAAI,CAACd,wBAAwB,CAACC,MAAM,EAAEA,MAAM,CAACI,KAAK,EAAE,IAAI,CAACmB,cAAc,EAAEd,WAAW,CAAC;IAC9H,IAAI,CAACkB,YAAY,CAACE,UAAU,GAAG,IAAI,CAACrB,cAAc,CAACR,MAAM,EAAEA,MAAM,CAACI,KAAK,EAAE,IAAI,CAACmB,cAAc,EAAEd,WAAW,EAAE,IAAI,CAACC,gBAAgB,CAAC;;IAEjI;IACA,IAAI,IAAI,CAAChB,WAAW,CAACoC,UAAU,EAAE;MAC/B,IAAI,CAACH,YAAY,GAAGzC,KAAK,CAACyB,WAAW,CAAC,QAAQ,EAAE,IAAI,CAACgB,YAAY,CAACE,UAAU,CAAC,CAAC,CAAC;MAC/E,IAAIzC,WAAW,CAACwC,KAAK,CAAC,IAAI,CAACD,YAAY,CAAC,KAAK,IAAI,CAACC,KAAK,EAAE;QACvD,MAAM,IAAIZ,KAAK,CAAC,gCAAgC,CAAC;MACnD;IACF;EACF,CAAC;EACD;AACF;AACA;AACA;EACEe,eAAe,EAAE,SAASA,eAAeA,CAAC/B,MAAM,EAAE;IAChD,IAAI,CAACgC,aAAa,GAAGhC,MAAM,CAACoB,OAAO,CAAC,CAAC,CAAC;IACtC,IAAI,CAACa,aAAa,GAAGjC,MAAM,CAACoB,OAAO,CAAC,CAAC,CAAC;IACtC,IAAI,CAACvB,OAAO,GAAGG,MAAM,CAACoB,OAAO,CAAC,CAAC,CAAC;IAChC,IAAI,CAACK,iBAAiB,GAAGzB,MAAM,CAACkC,UAAU,CAAC,CAAC,CAAC;IAC7C,IAAI,CAACC,IAAI,GAAGnC,MAAM,CAACoC,QAAQ,CAAC,CAAC;IAC7B,IAAI,CAACR,KAAK,GAAG5B,MAAM,CAACoB,OAAO,CAAC,CAAC,CAAC;IAC9B,IAAI,CAACG,cAAc,GAAGvB,MAAM,CAACoB,OAAO,CAAC,CAAC,CAAC;IACvC,IAAI,CAACV,gBAAgB,GAAGV,MAAM,CAACoB,OAAO,CAAC,CAAC,CAAC;IACzC,IAAI,CAACD,cAAc,GAAGnB,MAAM,CAACoB,OAAO,CAAC,CAAC,CAAC;IACvC,IAAI,CAACiB,iBAAiB,GAAGrC,MAAM,CAACoB,OAAO,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACkB,iBAAiB,GAAGtC,MAAM,CAACoB,OAAO,CAAC,CAAC,CAAC;IAC1C,IAAI,CAACmB,eAAe,GAAGvC,MAAM,CAACoB,OAAO,CAAC,CAAC,CAAC;IACxC,IAAI,CAACoB,sBAAsB,GAAGxC,MAAM,CAACoB,OAAO,CAAC,CAAC,CAAC;IAC/C,IAAI,CAACqB,sBAAsB,GAAGzC,MAAM,CAACoB,OAAO,CAAC,CAAC,CAAC;IAC/C,IAAI,CAACsB,iBAAiB,GAAG1C,MAAM,CAACoB,OAAO,CAAC,CAAC,CAAC;IAC1C,IAAI,IAAI,CAACxB,WAAW,CAAC,CAAC,EAAE;MACtB,MAAM,IAAIoB,KAAK,CAAC,iCAAiC,CAAC;IACpD;IACA,IAAI,CAACM,QAAQ,GAAGtB,MAAM,CAACO,QAAQ,CAAC,IAAI,CAACY,cAAc,CAAC;IACpD,IAAI,CAACwB,eAAe,CAAC3C,MAAM,CAAC;IAC5B,IAAI,CAAC4C,oBAAoB,CAAC5C,MAAM,CAAC;IACjC,IAAI,CAAC6C,WAAW,GAAG7C,MAAM,CAACO,QAAQ,CAAC,IAAI,CAAC+B,iBAAiB,CAAC;EAC5D,CAAC;EACD;AACF;AACA;EACEQ,iBAAiB,EAAE,SAASA,iBAAiBA,CAAA,EAAG;IAC9C,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B,IAAIC,MAAM,GAAG,IAAI,CAACjB,aAAa,IAAI,CAAC;;IAEpC;IACA;IACA;IACA,IAAI,CAACkB,GAAG,GAAG,CAAC,EAAE,IAAI,CAACT,sBAAsB,GAAG,MAAM,CAAC;IACnD,IAAIQ,MAAM,KAAK3D,WAAW,EAAE;MAC1B;MACA,IAAI,CAAC0D,cAAc,GAAG,IAAI,CAACP,sBAAsB,GAAG,IAAI;IAC1D;IACA,IAAIQ,MAAM,KAAK1D,YAAY,EAAE;MAC3B,IAAI,CAACwD,eAAe,GAAG,IAAI,CAACN,sBAAsB,IAAI,EAAE,GAAG,MAAM;MACjE;IACF;;IAEA;IACA,IAAI,CAAC,IAAI,CAACS,GAAG,IAAI,IAAI,CAACC,WAAW,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACnD,IAAI,CAACF,GAAG,GAAG,IAAI;IACjB;EACF,CAAC;EACD;AACF;AACA;EACEN,oBAAoB,EAAE,SAASA,oBAAoBA,CAAA,EAAG;IACpD,IAAI,CAAC,IAAI,CAACS,WAAW,CAAC,MAAM,CAAC,EAAE;MAC7B;IACF;;IAEA;IACA,IAAIC,WAAW,GAAG,IAAItE,YAAY,CAAC,IAAI,CAACqE,WAAW,CAAC,MAAM,CAAC,CAACE,KAAK,CAAC;;IAElE;IACA;IACA,IAAI,IAAI,CAAC7C,gBAAgB,KAAKxB,KAAK,CAACsE,gBAAgB,EAAE;MACpD,IAAI,CAAC9C,gBAAgB,GAAG4C,WAAW,CAAClC,OAAO,CAAC,CAAC,CAAC;IAChD;IACA,IAAI,IAAI,CAACG,cAAc,KAAKrC,KAAK,CAACsE,gBAAgB,EAAE;MAClD,IAAI,CAACjC,cAAc,GAAG+B,WAAW,CAAClC,OAAO,CAAC,CAAC,CAAC;IAC9C;IACA,IAAI,IAAI,CAACsB,iBAAiB,KAAKxD,KAAK,CAACsE,gBAAgB,EAAE;MACrD,IAAI,CAACd,iBAAiB,GAAGY,WAAW,CAAClC,OAAO,CAAC,CAAC,CAAC;IACjD;IACA,IAAI,IAAI,CAACmB,eAAe,KAAKrD,KAAK,CAACsE,gBAAgB,EAAE;MACnD,IAAI,CAACjB,eAAe,GAAGe,WAAW,CAAClC,OAAO,CAAC,CAAC,CAAC;IAC/C;EACF,CAAC;EACD;AACF;AACA;AACA;EACEuB,eAAe,EAAE,SAASA,eAAeA,CAAC3C,MAAM,EAAE;IAChD,IAAIyD,KAAK,GAAGzD,MAAM,CAACI,KAAK;IACxB,IAAIsD,YAAY,EAAEC,gBAAgB,EAAEC,eAAe;IACnD,IAAI,CAACP,WAAW,GAAG,IAAI,CAACA,WAAW,IAAI,CAAC,CAAC;IACzC,OAAOrD,MAAM,CAACI,KAAK,GAAGqD,KAAK,GAAG,IAAI,CAACpB,iBAAiB,EAAE;MACpDqB,YAAY,GAAG1D,MAAM,CAACoB,OAAO,CAAC,CAAC,CAAC;MAChCuC,gBAAgB,GAAG3D,MAAM,CAACoB,OAAO,CAAC,CAAC,CAAC;MACpCwC,eAAe,GAAG5D,MAAM,CAACkC,UAAU,CAACyB,gBAAgB,CAAC;MACrD,IAAI,CAACN,WAAW,CAACK,YAAY,CAAC,GAAG;QAC/BG,EAAE,EAAEH,YAAY;QAChBxD,MAAM,EAAEyD,gBAAgB;QACxBJ,KAAK,EAAEK;MACT,CAAC;IACH;EACF,CAAC;EACD;AACF;AACA;EACEE,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;IAChC,IAAIC,eAAe,GAAG1E,OAAO,CAAC2E,UAAU,GAAG,YAAY,GAAG,OAAO;IACjE,IAAI,IAAI,CAAClE,OAAO,CAAC,CAAC,EAAE;MAClB,IAAI,CAACqD,WAAW,GAAG/D,WAAW,CAAC6E,UAAU,CAAC,IAAI,CAAC3C,QAAQ,CAAC;MACxD,IAAI,CAAC4C,cAAc,GAAG9E,WAAW,CAAC6E,UAAU,CAAC,IAAI,CAACpB,WAAW,CAAC;IAChE,CAAC,MAAM;MACL,IAAIsB,KAAK,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;MAC5C,IAAID,KAAK,KAAK,IAAI,EAAE;QAClB,IAAI,CAAChB,WAAW,GAAGgB,KAAK;MAC1B,CAAC,MAAM;QACL,IAAIE,iBAAiB,GAAGnF,KAAK,CAACyB,WAAW,CAACoD,eAAe,EAAE,IAAI,CAACzC,QAAQ,CAAC;QACzE,IAAI,CAAC6B,WAAW,GAAG,IAAI,CAACzD,WAAW,CAAC4E,cAAc,CAACD,iBAAiB,CAAC;MACvE;MACA,IAAIE,QAAQ,GAAG,IAAI,CAACC,4BAA4B,CAAC,CAAC;MAClD,IAAID,QAAQ,KAAK,IAAI,EAAE;QACrB,IAAI,CAACL,cAAc,GAAGK,QAAQ;MAChC,CAAC,MAAM;QACL,IAAIE,gBAAgB,GAAGvF,KAAK,CAACyB,WAAW,CAACoD,eAAe,EAAE,IAAI,CAAClB,WAAW,CAAC;QAC3E,IAAI,CAACqB,cAAc,GAAG,IAAI,CAACxE,WAAW,CAAC4E,cAAc,CAACG,gBAAgB,CAAC;MACzE;IACF;EACF,CAAC;EACD;AACF;AACA;AACA;EACEL,yBAAyB,EAAE,SAASA,yBAAyBA,CAAA,EAAG;IAC9D,IAAIM,UAAU,GAAG,IAAI,CAACrB,WAAW,CAAC,MAAM,CAAC;IACzC,IAAIqB,UAAU,EAAE;MACd,IAAIpB,WAAW,GAAG,IAAItE,YAAY,CAAC0F,UAAU,CAACnB,KAAK,CAAC;;MAEpD;MACA,IAAID,WAAW,CAAClC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QAChC,OAAO,IAAI;MACb;;MAEA;MACA,IAAIhC,WAAW,CAACwC,KAAK,CAAC,IAAI,CAACN,QAAQ,CAAC,KAAKgC,WAAW,CAAClC,OAAO,CAAC,CAAC,CAAC,EAAE;QAC/D,OAAO,IAAI;MACb;MACA,OAAOhC,WAAW,CAAC6E,UAAU,CAACX,WAAW,CAACpB,UAAU,CAACwC,UAAU,CAACxE,MAAM,GAAG,CAAC,CAAC,CAAC;IAC9E;IACA,OAAO,IAAI;EACb,CAAC;EACD;AACF;AACA;AACA;EACEsE,4BAA4B,EAAE,SAASA,4BAA4BA,CAAA,EAAG;IACpE,IAAIG,aAAa,GAAG,IAAI,CAACtB,WAAW,CAAC,MAAM,CAAC;IAC5C,IAAIsB,aAAa,EAAE;MACjB,IAAIrB,WAAW,GAAG,IAAItE,YAAY,CAAC2F,aAAa,CAACpB,KAAK,CAAC;;MAEvD;MACA,IAAID,WAAW,CAAClC,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QAChC,OAAO,IAAI;MACb;;MAEA;MACA,IAAIhC,WAAW,CAACwC,KAAK,CAAC,IAAI,CAACiB,WAAW,CAAC,KAAKS,WAAW,CAAClC,OAAO,CAAC,CAAC,CAAC,EAAE;QAClE,OAAO,IAAI;MACb;MACA,OAAOhC,WAAW,CAAC6E,UAAU,CAACX,WAAW,CAACpB,UAAU,CAACyC,aAAa,CAACzE,MAAM,GAAG,CAAC,CAAC,CAAC;IACjF;IACA,OAAO,IAAI;EACb;AACF,CAAC;AACD0E,MAAM,CAACC,OAAO,GAAGrF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}