{"ast": null, "code": "import { createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString } from \"vue\";\nvar _hoisted_1 = {\n  class: \"business-select-region\"\n};\nvar _hoisted_2 = {\n  class: \"business-select-region-button\"\n};\nvar _hoisted_3 = {\n  class: \"usiness-select-region-text row1\"\n};\nvar _hoisted_4 = [\"onClick\"];\nvar _hoisted_5 = {\n  class: \"business-select-region-pagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_empty = _resolveComponent(\"el-empty\");\n  var _component_Delete = _resolveComponent(\"Delete\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_select_region = _resolveComponent(\"select-region\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: _cache[0] || (_cache[0] = function ($event) {\n      return $setup.show = !$setup.show;\n    })\n  }, {\n    default: _withCtx(function () {\n      return _cache[5] || (_cache[5] = [_createTextVNode(\"选择地区\")]);\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_input, {\n    modelValue: $setup.keyWord,\n    \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.keyWord = $event;\n    }),\n    placeholder: \"请输入地区名称\",\n    clearable: \"\"\n  }, null, 8 /* PROPS */, [\"modelValue\"])]), _createVNode(_component_el_scrollbar, {\n    class: \"usiness-select-region-body\"\n  }, {\n    default: _withCtx(function () {\n      return [!$setup.regionData.length ? (_openBlock(), _createBlock(_component_el_empty, {\n        key: 0,\n        \"image-size\": 120,\n        description: \"暂未选中地区\"\n      })) : _createCommentVNode(\"v-if\", true), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.regionData, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"usiness-select-region-user\",\n          key: item.id\n        }, [_createElementVNode(\"div\", _hoisted_3, _toDisplayString(item.name), 1 /* TEXT */), _createElementVNode(\"div\", {\n          class: \"usiness-select-region-del\",\n          onClick: function onClick($event) {\n            return $setup.userDel(item);\n          }\n        }, [_createVNode(_component_el_icon, null, {\n          default: _withCtx(function () {\n            return [_createVNode(_component_Delete)];\n          }),\n          _: 1 /* STABLE */\n        })], 8 /* PROPS */, _hoisted_4)]);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.pageNo,\n    \"onUpdate:currentPage\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.pageNo = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": [10, 20, 50, 100, 200],\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleQuery,\n    onCurrentChange: $setup.handleQuery,\n    total: $setup.totals,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"total\"])]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.show,\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n      return $setup.show = $event;\n    }),\n    name: \"选择地区\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_select_region, {\n        regionData: $setup.regionDataArr,\n        levelType: $setup.props.levelType,\n        params: $setup.props.params,\n        urlParams: $setup.props.urlParams,\n        onCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"regionData\", \"levelType\", \"params\", \"urlParams\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_button", "type", "onClick", "_cache", "$event", "$setup", "show", "default", "_withCtx", "_createTextVNode", "_", "_component_el_input", "modelValue", "key<PERSON>ord", "placeholder", "clearable", "_component_el_scrollbar", "regionData", "length", "_createBlock", "_component_el_empty", "key", "description", "_createCommentVNode", "_Fragment", "_renderList", "item", "id", "_hoisted_3", "_toDisplayString", "name", "userDel", "_component_el_icon", "_component_Delete", "_hoisted_4", "_hoisted_5", "_component_el_pagination", "currentPage", "pageNo", "pageSize", "layout", "onSizeChange", "handleQuery", "onCurrentChange", "total", "totals", "background", "_component_xyl_popup_window", "_component_select_region", "regionDataArr", "levelType", "props", "params", "urlParams", "onCallback", "callback"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\common\\components\\business-select-region\\business-select-region.vue"], "sourcesContent": ["<template>\r\n  <div class=\"business-select-region\">\r\n    <div class=\"business-select-region-button\">\r\n      <el-button type=\"primary\" @click=\"show = !show\">选择地区</el-button>\r\n      <el-input v-model=\"keyWord\" placeholder=\"请输入地区名称\" clearable />\r\n    </div>\r\n    <el-scrollbar class=\"usiness-select-region-body\">\r\n      <el-empty :image-size=\"120\" description=\"暂未选中地区\" v-if=\"!regionData.length\" />\r\n      <div class=\"usiness-select-region-user\" v-for=\"item in regionData\" :key=\"item.id\">\r\n        <div class=\"usiness-select-region-text row1\">{{ item.name }}</div>\r\n        <div class=\"usiness-select-region-del\" @click=\"userDel(item)\">\r\n          <el-icon>\r\n            <Delete />\r\n          </el-icon>\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n    <div class=\"business-select-region-pagination\">\r\n      <el-pagination\r\n        v-model:currentPage=\"pageNo\"\r\n        v-model:page-size=\"pageSize\"\r\n        :page-sizes=\"[10, 20, 50, 100, 200]\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\"\r\n        @size-change=\"handleQuery\"\r\n        @current-change=\"handleQuery\"\r\n        :total=\"totals\"\r\n        background />\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\" name=\"选择地区\">\r\n      <select-region\r\n        :regionData=\"regionDataArr\"\r\n        :levelType=\"props.levelType\"\r\n        :params=\"props.params\"\r\n        :urlParams=\"props.urlParams\"\r\n        @callback=\"callback\"></select-region>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'BusinessSelectRegion' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, watch } from 'vue'\r\nconst props = defineProps({\r\n  modelValue: { type: Array, default: () => [] },\r\n  levelType: { type: String, default: 'all' },\r\n  params: { type: Object, default: () => ({}) },\r\n  urlParams: { type: Object, default: () => ({}) }\r\n})\r\nconst emit = defineEmits(['update:modelValue', 'callback'])\r\nconst keyWord = ref('')\r\nconst pageNo = ref(1)\r\nconst pageSize = ref(10)\r\nconst show = ref(false)\r\nconst userId = ref(props.modelValue)\r\nconst regionData = ref([])\r\nconst regionDataArr = ref([])\r\nconst regionDataArrFilter = computed(() => {\r\n  return regionDataArr.value.filter((item) => item.name.includes(keyWord.value))\r\n})\r\nconst totals = computed(() => regionDataArrFilter.value.length)\r\nconst getAreaTree = async () => {\r\n  const { data } = await api.businessAreaTree({ levelType: props.levelType, isFilterUnUsing: 1 })\r\n  selectedMethods(data)\r\n  handleQuery()\r\n  emit('callback', regionDataArr.value)\r\n}\r\n// 首次进来默认选中\r\nconst selectedMethods = (data) => {\r\n  data.forEach((item) => {\r\n    if (props.modelValue.includes(item.id)) {\r\n      regionDataArr.value.push(item)\r\n    }\r\n    if (item.children && item.children.length > 0) {\r\n      selectedMethods(item.children)\r\n    }\r\n  })\r\n}\r\nconst handleQuery = () => {\r\n  regionData.value = regionDataArrFilter.value.slice(pageSize.value * (pageNo.value - 1), pageSize.value * pageNo.value)\r\n}\r\nconst callback = (data) => {\r\n  show.value = false\r\n  if (data) {\r\n    regionDataArr.value = data\r\n    emit(\r\n      'update:modelValue',\r\n      data.map((v) => v.id)\r\n    )\r\n    emit('callback', regionDataArr.value)\r\n    handleQuery()\r\n  }\r\n}\r\nconst userDel = (row) => {\r\n  regionDataArr.value = regionDataArr.value.filter((item) => item.id !== row.id)\r\n  emit(\r\n    'update:modelValue',\r\n    regionDataArr.value.map((v) => v.id)\r\n  )\r\n  emit('callback', regionDataArr.value)\r\n  handleQuery()\r\n}\r\nwatch(\r\n  () => keyWord.value,\r\n  () => {\r\n    pageNo.value = 1\r\n    handleQuery()\r\n  },\r\n  { immediate: true }\r\n)\r\nwatch(\r\n  () => props.modelValue,\r\n  () => {\r\n    userId.value = props.modelValue\r\n    if (props.modelValue.length !== regionDataArr.value.length) {\r\n      getAreaTree()\r\n    }\r\n  },\r\n  { immediate: true }\r\n)\r\n</script>\r\n<style lang=\"scss\">\r\n.business-select-region {\r\n  width: 100%;\r\n  height: 380px;\r\n  border: 1px solid var(--zy-el-border-color-lighter);\r\n  border-radius: var(--el-border-radius-base);\r\n\r\n  .business-select-region-button {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    line-height: normal;\r\n    padding: var(--zy-distance-five) var(--zy-distance-two);\r\n    border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .zy-el-button {\r\n      height: var(--zy-height-secondary);\r\n    }\r\n    .zy-el-input {\r\n      width: 220px;\r\n      height: var(--zy-height-routine);\r\n    }\r\n  }\r\n\r\n  .usiness-select-region-body {\r\n    width: 100%;\r\n    height: 288px;\r\n\r\n    .usiness-select-region-user {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      padding: var(--zy-distance-four) 0;\r\n      border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n      .usiness-select-region-text {\r\n        padding-left: var(--zy-distance-two);\r\n        line-height: var(--zy-line-height);\r\n        font-size: var(--zy-text-font-size);\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .row1 {\r\n        flex: 1;\r\n      }\r\n\r\n      .row2 {\r\n        flex: 2;\r\n      }\r\n\r\n      .row3 {\r\n        flex: 3;\r\n      }\r\n\r\n      .row4 {\r\n        flex: 4;\r\n      }\r\n\r\n      .usiness-select-region-del {\r\n        width: 60px;\r\n        display: flex;\r\n        align-items: center;\r\n        cursor: pointer;\r\n        padding-left: var(--zy-distance-two);\r\n      }\r\n    }\r\n  }\r\n\r\n  .business-select-region-pagination {\r\n    width: 100%;\r\n    height: 42px;\r\n    line-height: normal;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: flex-end;\r\n    border-top: 1px solid var(--zy-el-border-color-lighter);\r\n    padding-right: var(--zy-distance-two);\r\n\r\n    .zy-el-pagination {\r\n      --zy-height: var(--zy-height-routine);\r\n      --zy-el-component-size: var(--zy-height);\r\n\r\n      .zy-el-select {\r\n        width: 128px !important;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAwB;;EAC5BA,KAAK,EAAC;AAA+B;;EAOjCA,KAAK,EAAC;AAAiC;iBATpD;;EAiBSA,KAAK,EAAC;AAAmC;;;;;;;;;;;uBAhBhDC,mBAAA,CAmCM,OAnCNC,UAmCM,GAlCJC,mBAAA,CAGM,OAHNC,UAGM,GAFJC,YAAA,CAAgEC,oBAAA;IAArDC,IAAI,EAAC,SAAS;IAAEC,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAEC,MAAA,CAAAC,IAAI,IAAID,MAAA,CAAAC,IAAI;IAAA;;IAHpDC,OAAA,EAAAC,QAAA,CAGsD;MAAA,OAAIL,MAAA,QAAAA,MAAA,OAH1DM,gBAAA,CAGsD,MAAI,E;;IAH1DC,CAAA;MAIMX,YAAA,CAA8DY,mBAAA;IAJpEC,UAAA,EAIyBP,MAAA,CAAAQ,OAAO;IAJhC,uBAAAV,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAIyBC,MAAA,CAAAQ,OAAO,GAAAT,MAAA;IAAA;IAAEU,WAAW,EAAC,SAAS;IAACC,SAAS,EAAT;6CAEpDhB,YAAA,CAUeiB,uBAAA;IAVDtB,KAAK,EAAC;EAA4B;IANpDa,OAAA,EAAAC,QAAA,CAOe;MAAA,OAER,C,CAFuDH,MAAA,CAAAY,UAAU,CAACC,MAAM,I,cAAzEC,YAAA,CAA6EC,mBAAA;QAPnFC,GAAA;QAOiB,YAAU,EAAE,GAAG;QAAEC,WAAW,EAAC;YAP9CC,mBAAA,iB,kBAQM5B,mBAAA,CAOM6B,SAAA,QAfZC,WAAA,CAQ6DpB,MAAA,CAAAY,UAAU,EARvE,UAQqDS,IAAI;6BAAnD/B,mBAAA,CAOM;UAPDD,KAAK,EAAC,4BAA4B;UAA6B2B,GAAG,EAAEK,IAAI,CAACC;YAC5E9B,mBAAA,CAAkE,OAAlE+B,UAAkE,EAAAC,gBAAA,CAAlBH,IAAI,CAACI,IAAI,kBACzDjC,mBAAA,CAIM;UAJDH,KAAK,EAAC,2BAA2B;UAAEQ,OAAK,WAALA,OAAKA,CAAAE,MAAA;YAAA,OAAEC,MAAA,CAAA0B,OAAO,CAACL,IAAI;UAAA;YACzD3B,YAAA,CAEUiC,kBAAA;UAbpBzB,OAAA,EAAAC,QAAA,CAYY;YAAA,OAAU,CAAVT,YAAA,CAAUkC,iBAAA,E;;UAZtBvB,CAAA;4BAAAwB,UAAA,E;;;IAAAxB,CAAA;MAiBIb,mBAAA,CAUM,OAVNsC,UAUM,GATJpC,YAAA,CAQeqC,wBAAA;IAPLC,WAAW,EAAEhC,MAAA,CAAAiC,MAAM;IAnBnC,wBAAAnC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAmB6BC,MAAA,CAAAiC,MAAM,GAAAlC,MAAA;IAAA;IACnB,WAAS,EAAEC,MAAA,CAAAkC,QAAQ;IApBnC,qBAAApC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAoB2BC,MAAA,CAAAkC,QAAQ,GAAAnC,MAAA;IAAA;IAC1B,YAAU,EAAE,sBAAsB;IACnCoC,MAAM,EAAC,yCAAyC;IAC/CC,YAAW,EAAEpC,MAAA,CAAAqC,WAAW;IACxBC,eAAc,EAAEtC,MAAA,CAAAqC,WAAW;IAC3BE,KAAK,EAAEvC,MAAA,CAAAwC,MAAM;IACdC,UAAU,EAAV;oEAEJ/C,YAAA,CAOmBgD,2BAAA;IAnCvBnC,UAAA,EA4B+BP,MAAA,CAAAC,IAAI;IA5BnC,uBAAAH,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA4B+BC,MAAA,CAAAC,IAAI,GAAAF,MAAA;IAAA;IAAE0B,IAAI,EAAC;;IA5B1CvB,OAAA,EAAAC,QAAA,CA6BM;MAAA,OAKuC,CALvCT,YAAA,CAKuCiD,wBAAA;QAJpC/B,UAAU,EAAEZ,MAAA,CAAA4C,aAAa;QACzBC,SAAS,EAAE7C,MAAA,CAAA8C,KAAK,CAACD,SAAS;QAC1BE,MAAM,EAAE/C,MAAA,CAAA8C,KAAK,CAACC,MAAM;QACpBC,SAAS,EAAEhD,MAAA,CAAA8C,KAAK,CAACE,SAAS;QAC1BC,UAAQ,EAAEjD,MAAA,CAAAkD;;;IAlCnB7C,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}