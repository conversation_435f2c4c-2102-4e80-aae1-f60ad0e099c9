{"ast": null, "code": "// Process definition lists\n//\n'use strict';\n\nmodule.exports = function deflist_plugin(md) {\n  var isSpace = md.utils.isSpace;\n\n  // Search `[:~][\\n ]`, returns next pos after marker on success\n  // or -1 on fail.\n  function skipMarker(state, line) {\n    var pos,\n      marker,\n      start = state.bMarks[line] + state.tShift[line],\n      max = state.eMarks[line];\n    if (start >= max) {\n      return -1;\n    }\n\n    // Check bullet\n    marker = state.src.charCodeAt(start++);\n    if (marker !== 0x7E /* ~ */ && marker !== 0x3A /* : */) {\n      return -1;\n    }\n    pos = state.skipSpaces(start);\n\n    // require space after \":\"\n    if (start === pos) {\n      return -1;\n    }\n\n    // no empty definitions, e.g. \"  : \"\n    if (pos >= max) {\n      return -1;\n    }\n    return start;\n  }\n  function markTightParagraphs(state, idx) {\n    var i,\n      l,\n      level = state.level + 2;\n    for (i = idx + 2, l = state.tokens.length - 2; i < l; i++) {\n      if (state.tokens[i].level === level && state.tokens[i].type === 'paragraph_open') {\n        state.tokens[i + 2].hidden = true;\n        state.tokens[i].hidden = true;\n        i += 2;\n      }\n    }\n  }\n  function deflist(state, startLine, endLine, silent) {\n    var ch, contentStart, ddLine, dtLine, itemLines, listLines, listTokIdx, max, nextLine, offset, oldDDIndent, oldIndent, oldParentType, oldSCount, oldTShift, oldTight, pos, prevEmptyEnd, tight, token;\n    if (silent) {\n      // quirk: validation mode validates a dd block only, not a whole deflist\n      if (state.ddIndent < 0) {\n        return false;\n      }\n      return skipMarker(state, startLine) >= 0;\n    }\n    nextLine = startLine + 1;\n    if (nextLine >= endLine) {\n      return false;\n    }\n    if (state.isEmpty(nextLine)) {\n      nextLine++;\n      if (nextLine >= endLine) {\n        return false;\n      }\n    }\n    if (state.sCount[nextLine] < state.blkIndent) {\n      return false;\n    }\n    contentStart = skipMarker(state, nextLine);\n    if (contentStart < 0) {\n      return false;\n    }\n\n    // Start list\n    listTokIdx = state.tokens.length;\n    tight = true;\n    token = state.push('dl_open', 'dl', 1);\n    token.map = listLines = [startLine, 0];\n\n    //\n    // Iterate list items\n    //\n\n    dtLine = startLine;\n    ddLine = nextLine;\n\n    // One definition list can contain multiple DTs,\n    // and one DT can be followed by multiple DDs.\n    //\n    // Thus, there is two loops here, and label is\n    // needed to break out of the second one\n    //\n    /*eslint no-labels:0,block-scoped-var:0*/\n    OUTER: for (;;) {\n      prevEmptyEnd = false;\n      token = state.push('dt_open', 'dt', 1);\n      token.map = [dtLine, dtLine];\n      token = state.push('inline', '', 0);\n      token.map = [dtLine, dtLine];\n      token.content = state.getLines(dtLine, dtLine + 1, state.blkIndent, false).trim();\n      token.children = [];\n      token = state.push('dt_close', 'dt', -1);\n      for (;;) {\n        token = state.push('dd_open', 'dd', 1);\n        token.map = itemLines = [nextLine, 0];\n        pos = contentStart;\n        max = state.eMarks[ddLine];\n        offset = state.sCount[ddLine] + contentStart - (state.bMarks[ddLine] + state.tShift[ddLine]);\n        while (pos < max) {\n          ch = state.src.charCodeAt(pos);\n          if (isSpace(ch)) {\n            if (ch === 0x09) {\n              offset += 4 - offset % 4;\n            } else {\n              offset++;\n            }\n          } else {\n            break;\n          }\n          pos++;\n        }\n        contentStart = pos;\n        oldTight = state.tight;\n        oldDDIndent = state.ddIndent;\n        oldIndent = state.blkIndent;\n        oldTShift = state.tShift[ddLine];\n        oldSCount = state.sCount[ddLine];\n        oldParentType = state.parentType;\n        state.blkIndent = state.ddIndent = state.sCount[ddLine] + 2;\n        state.tShift[ddLine] = contentStart - state.bMarks[ddLine];\n        state.sCount[ddLine] = offset;\n        state.tight = true;\n        state.parentType = 'deflist';\n        state.md.block.tokenize(state, ddLine, endLine, true);\n\n        // If any of list item is tight, mark list as tight\n        if (!state.tight || prevEmptyEnd) {\n          tight = false;\n        }\n        // Item become loose if finish with empty line,\n        // but we should filter last element, because it means list finish\n        prevEmptyEnd = state.line - ddLine > 1 && state.isEmpty(state.line - 1);\n        state.tShift[ddLine] = oldTShift;\n        state.sCount[ddLine] = oldSCount;\n        state.tight = oldTight;\n        state.parentType = oldParentType;\n        state.blkIndent = oldIndent;\n        state.ddIndent = oldDDIndent;\n        token = state.push('dd_close', 'dd', -1);\n        itemLines[1] = nextLine = state.line;\n        if (nextLine >= endLine) {\n          break OUTER;\n        }\n        if (state.sCount[nextLine] < state.blkIndent) {\n          break OUTER;\n        }\n        contentStart = skipMarker(state, nextLine);\n        if (contentStart < 0) {\n          break;\n        }\n        ddLine = nextLine;\n\n        // go to the next loop iteration:\n        // insert DD tag and repeat checking\n      }\n      if (nextLine >= endLine) {\n        break;\n      }\n      dtLine = nextLine;\n      if (state.isEmpty(dtLine)) {\n        break;\n      }\n      if (state.sCount[dtLine] < state.blkIndent) {\n        break;\n      }\n      ddLine = dtLine + 1;\n      if (ddLine >= endLine) {\n        break;\n      }\n      if (state.isEmpty(ddLine)) {\n        ddLine++;\n      }\n      if (ddLine >= endLine) {\n        break;\n      }\n      if (state.sCount[ddLine] < state.blkIndent) {\n        break;\n      }\n      contentStart = skipMarker(state, ddLine);\n      if (contentStart < 0) {\n        break;\n      }\n\n      // go to the next loop iteration:\n      // insert DT and DD tags and repeat checking\n    }\n\n    // Finilize list\n    token = state.push('dl_close', 'dl', -1);\n    listLines[1] = nextLine;\n    state.line = nextLine;\n\n    // mark paragraphs tight if needed\n    if (tight) {\n      markTightParagraphs(state, listTokIdx);\n    }\n    return true;\n  }\n  md.block.ruler.before('paragraph', 'deflist', deflist, {\n    alt: ['paragraph', 'reference', 'blockquote']\n  });\n};", "map": {"version": 3, "names": ["module", "exports", "deflist_plugin", "md", "isSpace", "utils", "<PERSON><PERSON><PERSON><PERSON>", "state", "line", "pos", "marker", "start", "bMarks", "tShift", "max", "eMarks", "src", "charCodeAt", "skipSpaces", "markTightParagraphs", "idx", "i", "l", "level", "tokens", "length", "type", "hidden", "deflist", "startLine", "endLine", "silent", "ch", "contentStart", "ddLine", "dtLine", "itemLines", "listLines", "listTokIdx", "nextLine", "offset", "oldDDIndent", "oldIndent", "oldParentType", "oldSCount", "oldTShift", "oldTight", "prevEmptyEnd", "tight", "token", "ddIndent", "isEmpty", "sCount", "blkIndent", "push", "map", "OUTER", "content", "getLines", "trim", "children", "parentType", "block", "tokenize", "ruler", "before", "alt"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it-deflist@2.1.0/node_modules/markdown-it-deflist/index.js"], "sourcesContent": ["// Process definition lists\n//\n'use strict';\n\n\nmodule.exports = function deflist_plugin(md) {\n  var isSpace = md.utils.isSpace;\n\n  // Search `[:~][\\n ]`, returns next pos after marker on success\n  // or -1 on fail.\n  function skipMarker(state, line) {\n    var pos, marker,\n        start = state.bMarks[line] + state.tShift[line],\n        max = state.eMarks[line];\n\n    if (start >= max) { return -1; }\n\n    // Check bullet\n    marker = state.src.charCodeAt(start++);\n    if (marker !== 0x7E/* ~ */ && marker !== 0x3A/* : */) { return -1; }\n\n    pos = state.skipSpaces(start);\n\n    // require space after \":\"\n    if (start === pos) { return -1; }\n\n    // no empty definitions, e.g. \"  : \"\n    if (pos >= max) { return -1; }\n\n    return start;\n  }\n\n  function markTightParagraphs(state, idx) {\n    var i, l,\n        level = state.level + 2;\n\n    for (i = idx + 2, l = state.tokens.length - 2; i < l; i++) {\n      if (state.tokens[i].level === level && state.tokens[i].type === 'paragraph_open') {\n        state.tokens[i + 2].hidden = true;\n        state.tokens[i].hidden = true;\n        i += 2;\n      }\n    }\n  }\n\n  function deflist(state, startLine, endLine, silent) {\n    var ch,\n        contentStart,\n        ddLine,\n        dtLine,\n        itemLines,\n        listLines,\n        listTokIdx,\n        max,\n        nextLine,\n        offset,\n        oldDDIndent,\n        oldIndent,\n        oldParentType,\n        oldSCount,\n        oldTShift,\n        oldTight,\n        pos,\n        prevEmptyEnd,\n        tight,\n        token;\n\n    if (silent) {\n      // quirk: validation mode validates a dd block only, not a whole deflist\n      if (state.ddIndent < 0) { return false; }\n      return skipMarker(state, startLine) >= 0;\n    }\n\n    nextLine = startLine + 1;\n    if (nextLine >= endLine) { return false; }\n\n    if (state.isEmpty(nextLine)) {\n      nextLine++;\n      if (nextLine >= endLine) { return false; }\n    }\n\n    if (state.sCount[nextLine] < state.blkIndent) { return false; }\n    contentStart = skipMarker(state, nextLine);\n    if (contentStart < 0) { return false; }\n\n    // Start list\n    listTokIdx = state.tokens.length;\n    tight = true;\n\n    token     = state.push('dl_open', 'dl', 1);\n    token.map = listLines = [ startLine, 0 ];\n\n    //\n    // Iterate list items\n    //\n\n    dtLine = startLine;\n    ddLine = nextLine;\n\n    // One definition list can contain multiple DTs,\n    // and one DT can be followed by multiple DDs.\n    //\n    // Thus, there is two loops here, and label is\n    // needed to break out of the second one\n    //\n    /*eslint no-labels:0,block-scoped-var:0*/\n    OUTER:\n    for (;;) {\n      prevEmptyEnd = false;\n\n      token          = state.push('dt_open', 'dt', 1);\n      token.map      = [ dtLine, dtLine ];\n\n      token          = state.push('inline', '', 0);\n      token.map      = [ dtLine, dtLine ];\n      token.content  = state.getLines(dtLine, dtLine + 1, state.blkIndent, false).trim();\n      token.children = [];\n\n      token          = state.push('dt_close', 'dt', -1);\n\n      for (;;) {\n        token     = state.push('dd_open', 'dd', 1);\n        token.map = itemLines = [ nextLine, 0 ];\n\n        pos = contentStart;\n        max = state.eMarks[ddLine];\n        offset = state.sCount[ddLine] + contentStart - (state.bMarks[ddLine] + state.tShift[ddLine]);\n\n        while (pos < max) {\n          ch = state.src.charCodeAt(pos);\n\n          if (isSpace(ch)) {\n            if (ch === 0x09) {\n              offset += 4 - offset % 4;\n            } else {\n              offset++;\n            }\n          } else {\n            break;\n          }\n\n          pos++;\n        }\n\n        contentStart = pos;\n\n        oldTight = state.tight;\n        oldDDIndent = state.ddIndent;\n        oldIndent = state.blkIndent;\n        oldTShift = state.tShift[ddLine];\n        oldSCount = state.sCount[ddLine];\n        oldParentType = state.parentType;\n        state.blkIndent = state.ddIndent = state.sCount[ddLine] + 2;\n        state.tShift[ddLine] = contentStart - state.bMarks[ddLine];\n        state.sCount[ddLine] = offset;\n        state.tight = true;\n        state.parentType = 'deflist';\n\n        state.md.block.tokenize(state, ddLine, endLine, true);\n\n        // If any of list item is tight, mark list as tight\n        if (!state.tight || prevEmptyEnd) {\n          tight = false;\n        }\n        // Item become loose if finish with empty line,\n        // but we should filter last element, because it means list finish\n        prevEmptyEnd = (state.line - ddLine) > 1 && state.isEmpty(state.line - 1);\n\n        state.tShift[ddLine] = oldTShift;\n        state.sCount[ddLine] = oldSCount;\n        state.tight = oldTight;\n        state.parentType = oldParentType;\n        state.blkIndent = oldIndent;\n        state.ddIndent = oldDDIndent;\n\n        token = state.push('dd_close', 'dd', -1);\n\n        itemLines[1] = nextLine = state.line;\n\n        if (nextLine >= endLine) { break OUTER; }\n\n        if (state.sCount[nextLine] < state.blkIndent) { break OUTER; }\n        contentStart = skipMarker(state, nextLine);\n        if (contentStart < 0) { break; }\n\n        ddLine = nextLine;\n\n        // go to the next loop iteration:\n        // insert DD tag and repeat checking\n      }\n\n      if (nextLine >= endLine) { break; }\n      dtLine = nextLine;\n\n      if (state.isEmpty(dtLine)) { break; }\n      if (state.sCount[dtLine] < state.blkIndent) { break; }\n\n      ddLine = dtLine + 1;\n      if (ddLine >= endLine) { break; }\n      if (state.isEmpty(ddLine)) { ddLine++; }\n      if (ddLine >= endLine) { break; }\n\n      if (state.sCount[ddLine] < state.blkIndent) { break; }\n      contentStart = skipMarker(state, ddLine);\n      if (contentStart < 0) { break; }\n\n      // go to the next loop iteration:\n      // insert DT and DD tags and repeat checking\n    }\n\n    // Finilize list\n    token = state.push('dl_close', 'dl', -1);\n\n    listLines[1] = nextLine;\n\n    state.line = nextLine;\n\n    // mark paragraphs tight if needed\n    if (tight) {\n      markTightParagraphs(state, listTokIdx);\n    }\n\n    return true;\n  }\n\n\n  md.block.ruler.before('paragraph', 'deflist', deflist, { alt: [ 'paragraph', 'reference', 'blockquote' ] });\n};\n"], "mappings": "AAAA;AACA;AACA,YAAY;;AAGZA,MAAM,CAACC,OAAO,GAAG,SAASC,cAAcA,CAACC,EAAE,EAAE;EAC3C,IAAIC,OAAO,GAAGD,EAAE,CAACE,KAAK,CAACD,OAAO;;EAE9B;EACA;EACA,SAASE,UAAUA,CAACC,KAAK,EAAEC,IAAI,EAAE;IAC/B,IAAIC,GAAG;MAAEC,MAAM;MACXC,KAAK,GAAGJ,KAAK,CAACK,MAAM,CAACJ,IAAI,CAAC,GAAGD,KAAK,CAACM,MAAM,CAACL,IAAI,CAAC;MAC/CM,GAAG,GAAGP,KAAK,CAACQ,MAAM,CAACP,IAAI,CAAC;IAE5B,IAAIG,KAAK,IAAIG,GAAG,EAAE;MAAE,OAAO,CAAC,CAAC;IAAE;;IAE/B;IACAJ,MAAM,GAAGH,KAAK,CAACS,GAAG,CAACC,UAAU,CAACN,KAAK,EAAE,CAAC;IACtC,IAAID,MAAM,KAAK,IAAI,YAAWA,MAAM,KAAK,IAAI,UAAS;MAAE,OAAO,CAAC,CAAC;IAAE;IAEnED,GAAG,GAAGF,KAAK,CAACW,UAAU,CAACP,KAAK,CAAC;;IAE7B;IACA,IAAIA,KAAK,KAAKF,GAAG,EAAE;MAAE,OAAO,CAAC,CAAC;IAAE;;IAEhC;IACA,IAAIA,GAAG,IAAIK,GAAG,EAAE;MAAE,OAAO,CAAC,CAAC;IAAE;IAE7B,OAAOH,KAAK;EACd;EAEA,SAASQ,mBAAmBA,CAACZ,KAAK,EAAEa,GAAG,EAAE;IACvC,IAAIC,CAAC;MAAEC,CAAC;MACJC,KAAK,GAAGhB,KAAK,CAACgB,KAAK,GAAG,CAAC;IAE3B,KAAKF,CAAC,GAAGD,GAAG,GAAG,CAAC,EAAEE,CAAC,GAAGf,KAAK,CAACiB,MAAM,CAACC,MAAM,GAAG,CAAC,EAAEJ,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACzD,IAAId,KAAK,CAACiB,MAAM,CAACH,CAAC,CAAC,CAACE,KAAK,KAAKA,KAAK,IAAIhB,KAAK,CAACiB,MAAM,CAACH,CAAC,CAAC,CAACK,IAAI,KAAK,gBAAgB,EAAE;QAChFnB,KAAK,CAACiB,MAAM,CAACH,CAAC,GAAG,CAAC,CAAC,CAACM,MAAM,GAAG,IAAI;QACjCpB,KAAK,CAACiB,MAAM,CAACH,CAAC,CAAC,CAACM,MAAM,GAAG,IAAI;QAC7BN,CAAC,IAAI,CAAC;MACR;IACF;EACF;EAEA,SAASO,OAAOA,CAACrB,KAAK,EAAEsB,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAE;IAClD,IAAIC,EAAE,EACFC,YAAY,EACZC,MAAM,EACNC,MAAM,EACNC,SAAS,EACTC,SAAS,EACTC,UAAU,EACVxB,GAAG,EACHyB,QAAQ,EACRC,MAAM,EACNC,WAAW,EACXC,SAAS,EACTC,aAAa,EACbC,SAAS,EACTC,SAAS,EACTC,QAAQ,EACRrC,GAAG,EACHsC,YAAY,EACZC,KAAK,EACLC,KAAK;IAET,IAAIlB,MAAM,EAAE;MACV;MACA,IAAIxB,KAAK,CAAC2C,QAAQ,GAAG,CAAC,EAAE;QAAE,OAAO,KAAK;MAAE;MACxC,OAAO5C,UAAU,CAACC,KAAK,EAAEsB,SAAS,CAAC,IAAI,CAAC;IAC1C;IAEAU,QAAQ,GAAGV,SAAS,GAAG,CAAC;IACxB,IAAIU,QAAQ,IAAIT,OAAO,EAAE;MAAE,OAAO,KAAK;IAAE;IAEzC,IAAIvB,KAAK,CAAC4C,OAAO,CAACZ,QAAQ,CAAC,EAAE;MAC3BA,QAAQ,EAAE;MACV,IAAIA,QAAQ,IAAIT,OAAO,EAAE;QAAE,OAAO,KAAK;MAAE;IAC3C;IAEA,IAAIvB,KAAK,CAAC6C,MAAM,CAACb,QAAQ,CAAC,GAAGhC,KAAK,CAAC8C,SAAS,EAAE;MAAE,OAAO,KAAK;IAAE;IAC9DpB,YAAY,GAAG3B,UAAU,CAACC,KAAK,EAAEgC,QAAQ,CAAC;IAC1C,IAAIN,YAAY,GAAG,CAAC,EAAE;MAAE,OAAO,KAAK;IAAE;;IAEtC;IACAK,UAAU,GAAG/B,KAAK,CAACiB,MAAM,CAACC,MAAM;IAChCuB,KAAK,GAAG,IAAI;IAEZC,KAAK,GAAO1C,KAAK,CAAC+C,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1CL,KAAK,CAACM,GAAG,GAAGlB,SAAS,GAAG,CAAER,SAAS,EAAE,CAAC,CAAE;;IAExC;IACA;IACA;;IAEAM,MAAM,GAAGN,SAAS;IAClBK,MAAM,GAAGK,QAAQ;;IAEjB;IACA;IACA;IACA;IACA;IACA;IACA;IACAiB,KAAK,EACL,SAAS;MACPT,YAAY,GAAG,KAAK;MAEpBE,KAAK,GAAY1C,KAAK,CAAC+C,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;MAC/CL,KAAK,CAACM,GAAG,GAAQ,CAAEpB,MAAM,EAAEA,MAAM,CAAE;MAEnCc,KAAK,GAAY1C,KAAK,CAAC+C,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;MAC5CL,KAAK,CAACM,GAAG,GAAQ,CAAEpB,MAAM,EAAEA,MAAM,CAAE;MACnCc,KAAK,CAACQ,OAAO,GAAIlD,KAAK,CAACmD,QAAQ,CAACvB,MAAM,EAAEA,MAAM,GAAG,CAAC,EAAE5B,KAAK,CAAC8C,SAAS,EAAE,KAAK,CAAC,CAACM,IAAI,CAAC,CAAC;MAClFV,KAAK,CAACW,QAAQ,GAAG,EAAE;MAEnBX,KAAK,GAAY1C,KAAK,CAAC+C,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;MAEjD,SAAS;QACPL,KAAK,GAAO1C,KAAK,CAAC+C,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC1CL,KAAK,CAACM,GAAG,GAAGnB,SAAS,GAAG,CAAEG,QAAQ,EAAE,CAAC,CAAE;QAEvC9B,GAAG,GAAGwB,YAAY;QAClBnB,GAAG,GAAGP,KAAK,CAACQ,MAAM,CAACmB,MAAM,CAAC;QAC1BM,MAAM,GAAGjC,KAAK,CAAC6C,MAAM,CAAClB,MAAM,CAAC,GAAGD,YAAY,IAAI1B,KAAK,CAACK,MAAM,CAACsB,MAAM,CAAC,GAAG3B,KAAK,CAACM,MAAM,CAACqB,MAAM,CAAC,CAAC;QAE5F,OAAOzB,GAAG,GAAGK,GAAG,EAAE;UAChBkB,EAAE,GAAGzB,KAAK,CAACS,GAAG,CAACC,UAAU,CAACR,GAAG,CAAC;UAE9B,IAAIL,OAAO,CAAC4B,EAAE,CAAC,EAAE;YACf,IAAIA,EAAE,KAAK,IAAI,EAAE;cACfQ,MAAM,IAAI,CAAC,GAAGA,MAAM,GAAG,CAAC;YAC1B,CAAC,MAAM;cACLA,MAAM,EAAE;YACV;UACF,CAAC,MAAM;YACL;UACF;UAEA/B,GAAG,EAAE;QACP;QAEAwB,YAAY,GAAGxB,GAAG;QAElBqC,QAAQ,GAAGvC,KAAK,CAACyC,KAAK;QACtBP,WAAW,GAAGlC,KAAK,CAAC2C,QAAQ;QAC5BR,SAAS,GAAGnC,KAAK,CAAC8C,SAAS;QAC3BR,SAAS,GAAGtC,KAAK,CAACM,MAAM,CAACqB,MAAM,CAAC;QAChCU,SAAS,GAAGrC,KAAK,CAAC6C,MAAM,CAAClB,MAAM,CAAC;QAChCS,aAAa,GAAGpC,KAAK,CAACsD,UAAU;QAChCtD,KAAK,CAAC8C,SAAS,GAAG9C,KAAK,CAAC2C,QAAQ,GAAG3C,KAAK,CAAC6C,MAAM,CAAClB,MAAM,CAAC,GAAG,CAAC;QAC3D3B,KAAK,CAACM,MAAM,CAACqB,MAAM,CAAC,GAAGD,YAAY,GAAG1B,KAAK,CAACK,MAAM,CAACsB,MAAM,CAAC;QAC1D3B,KAAK,CAAC6C,MAAM,CAAClB,MAAM,CAAC,GAAGM,MAAM;QAC7BjC,KAAK,CAACyC,KAAK,GAAG,IAAI;QAClBzC,KAAK,CAACsD,UAAU,GAAG,SAAS;QAE5BtD,KAAK,CAACJ,EAAE,CAAC2D,KAAK,CAACC,QAAQ,CAACxD,KAAK,EAAE2B,MAAM,EAAEJ,OAAO,EAAE,IAAI,CAAC;;QAErD;QACA,IAAI,CAACvB,KAAK,CAACyC,KAAK,IAAID,YAAY,EAAE;UAChCC,KAAK,GAAG,KAAK;QACf;QACA;QACA;QACAD,YAAY,GAAIxC,KAAK,CAACC,IAAI,GAAG0B,MAAM,GAAI,CAAC,IAAI3B,KAAK,CAAC4C,OAAO,CAAC5C,KAAK,CAACC,IAAI,GAAG,CAAC,CAAC;QAEzED,KAAK,CAACM,MAAM,CAACqB,MAAM,CAAC,GAAGW,SAAS;QAChCtC,KAAK,CAAC6C,MAAM,CAAClB,MAAM,CAAC,GAAGU,SAAS;QAChCrC,KAAK,CAACyC,KAAK,GAAGF,QAAQ;QACtBvC,KAAK,CAACsD,UAAU,GAAGlB,aAAa;QAChCpC,KAAK,CAAC8C,SAAS,GAAGX,SAAS;QAC3BnC,KAAK,CAAC2C,QAAQ,GAAGT,WAAW;QAE5BQ,KAAK,GAAG1C,KAAK,CAAC+C,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAExClB,SAAS,CAAC,CAAC,CAAC,GAAGG,QAAQ,GAAGhC,KAAK,CAACC,IAAI;QAEpC,IAAI+B,QAAQ,IAAIT,OAAO,EAAE;UAAE,MAAM0B,KAAK;QAAE;QAExC,IAAIjD,KAAK,CAAC6C,MAAM,CAACb,QAAQ,CAAC,GAAGhC,KAAK,CAAC8C,SAAS,EAAE;UAAE,MAAMG,KAAK;QAAE;QAC7DvB,YAAY,GAAG3B,UAAU,CAACC,KAAK,EAAEgC,QAAQ,CAAC;QAC1C,IAAIN,YAAY,GAAG,CAAC,EAAE;UAAE;QAAO;QAE/BC,MAAM,GAAGK,QAAQ;;QAEjB;QACA;MACF;MAEA,IAAIA,QAAQ,IAAIT,OAAO,EAAE;QAAE;MAAO;MAClCK,MAAM,GAAGI,QAAQ;MAEjB,IAAIhC,KAAK,CAAC4C,OAAO,CAAChB,MAAM,CAAC,EAAE;QAAE;MAAO;MACpC,IAAI5B,KAAK,CAAC6C,MAAM,CAACjB,MAAM,CAAC,GAAG5B,KAAK,CAAC8C,SAAS,EAAE;QAAE;MAAO;MAErDnB,MAAM,GAAGC,MAAM,GAAG,CAAC;MACnB,IAAID,MAAM,IAAIJ,OAAO,EAAE;QAAE;MAAO;MAChC,IAAIvB,KAAK,CAAC4C,OAAO,CAACjB,MAAM,CAAC,EAAE;QAAEA,MAAM,EAAE;MAAE;MACvC,IAAIA,MAAM,IAAIJ,OAAO,EAAE;QAAE;MAAO;MAEhC,IAAIvB,KAAK,CAAC6C,MAAM,CAAClB,MAAM,CAAC,GAAG3B,KAAK,CAAC8C,SAAS,EAAE;QAAE;MAAO;MACrDpB,YAAY,GAAG3B,UAAU,CAACC,KAAK,EAAE2B,MAAM,CAAC;MACxC,IAAID,YAAY,GAAG,CAAC,EAAE;QAAE;MAAO;;MAE/B;MACA;IACF;;IAEA;IACAgB,KAAK,GAAG1C,KAAK,CAAC+C,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAExCjB,SAAS,CAAC,CAAC,CAAC,GAAGE,QAAQ;IAEvBhC,KAAK,CAACC,IAAI,GAAG+B,QAAQ;;IAErB;IACA,IAAIS,KAAK,EAAE;MACT7B,mBAAmB,CAACZ,KAAK,EAAE+B,UAAU,CAAC;IACxC;IAEA,OAAO,IAAI;EACb;EAGAnC,EAAE,CAAC2D,KAAK,CAACE,KAAK,CAACC,MAAM,CAAC,WAAW,EAAE,SAAS,EAAErC,OAAO,EAAE;IAAEsC,GAAG,EAAE,CAAE,WAAW,EAAE,WAAW,EAAE,YAAY;EAAG,CAAC,CAAC;AAC7G,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}