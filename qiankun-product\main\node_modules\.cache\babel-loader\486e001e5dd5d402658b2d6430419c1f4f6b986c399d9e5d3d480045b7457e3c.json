{"ast": null, "code": "import MarkdownIt from 'markdown-it';\nimport emoji from 'markdown-it-emoji';\nimport deflist from 'markdown-it-deflist';\nimport abbr from 'markdown-it-abbr';\nimport footnote from 'markdown-it-footnote';\nimport ins from 'markdown-it-ins';\nimport mark from 'markdown-it-mark';\nimport taskLists from 'markdown-it-task-lists';\nimport container from 'markdown-it-container';\nimport toc from 'markdown-it-toc-done-right';\nimport mermaid from '@DatatracCorporation/markdown-it-mermaid';\nvar config = {\n  html: true,\n  xhtmlOut: true,\n  breaks: true,\n  langPrefix: 'lang-',\n  linkify: false,\n  typographer: true,\n  quotes: '“”‘’'\n};\nvar markdownIt = new MarkdownIt(config);\nmarkdownIt.use(emoji).use(deflist).use(abbr).use(footnote).use(ins).use(mark).use(taskLists).use(container).use(container, 'hljs-left').use(container, 'hljs-center').use(container, 'hljs-right').use(toc).use(mermaid);\nexport default markdownIt;", "map": {"version": 3, "names": ["MarkdownIt", "emoji", "deflist", "abbr", "footnote", "ins", "mark", "taskLists", "container", "toc", "mermaid", "config", "html", "xhtmlOut", "breaks", "langPrefix", "linkify", "typographer", "quotes", "markdownIt", "use"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/components/global-markdown/markdown-it.js"], "sourcesContent": ["import MarkdownIt from 'markdown-it'\r\nimport emoji from 'markdown-it-emoji'\r\nimport deflist from 'markdown-it-deflist'\r\nimport abbr from 'markdown-it-abbr'\r\nimport footnote from 'markdown-it-footnote'\r\nimport ins from 'markdown-it-ins'\r\nimport mark from 'markdown-it-mark'\r\nimport taskLists from 'markdown-it-task-lists'\r\nimport container from 'markdown-it-container'\r\nimport toc from 'markdown-it-toc-done-right'\r\nimport mermaid from '@DatatracCorporation/markdown-it-mermaid'\r\n\r\nvar config = {\r\n  html: true,\r\n  xhtmlOut: true,\r\n  breaks: true,\r\n  langPrefix: 'lang-',\r\n  linkify: false,\r\n  typographer: true,\r\n  quotes: '“”‘’'\r\n}\r\nlet markdownIt = new MarkdownIt(config)\r\n\r\nmarkdownIt\r\n  .use(emoji)\r\n  .use(deflist)\r\n  .use(abbr)\r\n  .use(footnote)\r\n  .use(ins)\r\n  .use(mark)\r\n  .use(taskLists)\r\n  .use(container)\r\n  .use(container, 'hljs-left')\r\n  .use(container, 'hljs-center')\r\n  .use(container, 'hljs-right')\r\n  .use(toc)\r\n  .use(mermaid)\r\n\r\nexport default markdownIt"], "mappings": "AAAA,OAAOA,UAAU,MAAM,aAAa;AACpC,OAAOC,KAAK,MAAM,mBAAmB;AACrC,OAAOC,OAAO,MAAM,qBAAqB;AACzC,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,GAAG,MAAM,iBAAiB;AACjC,OAAOC,IAAI,MAAM,kBAAkB;AACnC,OAAOC,SAAS,MAAM,wBAAwB;AAC9C,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,GAAG,MAAM,4BAA4B;AAC5C,OAAOC,OAAO,MAAM,0CAA0C;AAE9D,IAAIC,MAAM,GAAG;EACXC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,IAAI;EACZC,UAAU,EAAE,OAAO;EACnBC,OAAO,EAAE,KAAK;EACdC,WAAW,EAAE,IAAI;EACjBC,MAAM,EAAE;AACV,CAAC;AACD,IAAIC,UAAU,GAAG,IAAInB,UAAU,CAACW,MAAM,CAAC;AAEvCQ,UAAU,CACPC,GAAG,CAACnB,KAAK,CAAC,CACVmB,GAAG,CAAClB,OAAO,CAAC,CACZkB,GAAG,CAACjB,IAAI,CAAC,CACTiB,GAAG,CAAChB,QAAQ,CAAC,CACbgB,GAAG,CAACf,GAAG,CAAC,CACRe,GAAG,CAACd,IAAI,CAAC,CACTc,GAAG,CAACb,SAAS,CAAC,CACda,GAAG,CAACZ,SAAS,CAAC,CACdY,GAAG,CAACZ,SAAS,EAAE,WAAW,CAAC,CAC3BY,GAAG,CAACZ,SAAS,EAAE,aAAa,CAAC,CAC7BY,GAAG,CAACZ,SAAS,EAAE,YAAY,CAAC,CAC5BY,GAAG,CAACX,GAAG,CAAC,CACRW,GAAG,CAACV,OAAO,CAAC;AAEf,eAAeS,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}