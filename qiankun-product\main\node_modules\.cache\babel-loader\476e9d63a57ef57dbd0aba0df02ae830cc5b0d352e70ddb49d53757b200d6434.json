{"ast": null, "code": "'use strict';\n\nvar emojies_defs = require('./lib/data/full.json');\nvar emojies_shortcuts = require('./lib/data/shortcuts');\nvar bare_emoji_plugin = require('./bare');\nmodule.exports = function emoji_plugin(md, options) {\n  var defaults = {\n    defs: emojies_defs,\n    shortcuts: emojies_shortcuts,\n    enabled: []\n  };\n  var opts = md.utils.assign({}, defaults, options || {});\n  bare_emoji_plugin(md, opts);\n};", "map": {"version": 3, "names": ["emojies_defs", "require", "emojies_shortcuts", "bare_emoji_plugin", "module", "exports", "emoji_plugin", "md", "options", "defaults", "defs", "shortcuts", "enabled", "opts", "utils", "assign"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it-emoji@2.0.2/node_modules/markdown-it-emoji/index.js"], "sourcesContent": ["'use strict';\n\n\nvar emojies_defs      = require('./lib/data/full.json');\nvar emojies_shortcuts = require('./lib/data/shortcuts');\nvar bare_emoji_plugin = require('./bare');\n\n\nmodule.exports = function emoji_plugin(md, options) {\n  var defaults = {\n    defs: emojies_defs,\n    shortcuts: emojies_shortcuts,\n    enabled: []\n  };\n\n  var opts = md.utils.assign({}, defaults, options || {});\n\n  bare_emoji_plugin(md, opts);\n};\n"], "mappings": "AAAA,YAAY;;AAGZ,IAAIA,YAAY,GAAQC,OAAO,CAAC,sBAAsB,CAAC;AACvD,IAAIC,iBAAiB,GAAGD,OAAO,CAAC,sBAAsB,CAAC;AACvD,IAAIE,iBAAiB,GAAGF,OAAO,CAAC,QAAQ,CAAC;AAGzCG,MAAM,CAACC,OAAO,GAAG,SAASC,YAAYA,CAACC,EAAE,EAAEC,OAAO,EAAE;EAClD,IAAIC,QAAQ,GAAG;IACbC,IAAI,EAAEV,YAAY;IAClBW,SAAS,EAAET,iBAAiB;IAC5BU,OAAO,EAAE;EACX,CAAC;EAED,IAAIC,IAAI,GAAGN,EAAE,CAACO,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEN,QAAQ,EAAED,OAAO,IAAI,CAAC,CAAC,CAAC;EAEvDL,iBAAiB,CAACI,EAAE,EAAEM,IAAI,CAAC;AAC7B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}