{"ast": null, "code": "import getPrototypeOf from \"./getPrototypeOf.js\";\nimport setPrototypeOf from \"./setPrototypeOf.js\";\nimport isNativeFunction from \"./isNativeFunction.js\";\nimport construct from \"./construct.js\";\nfunction _wrapNativeSuper(t) {\n  var r = \"function\" == typeof Map ? new Map() : void 0;\n  return _wrapNativeSuper = function _wrapNativeSuper(t) {\n    if (null === t || !isNativeFunction(t)) return t;\n    if (\"function\" != typeof t) throw new TypeError(\"Super expression must either be null or a function\");\n    if (void 0 !== r) {\n      if (r.has(t)) return r.get(t);\n      r.set(t, Wrapper);\n    }\n    function Wrapper() {\n      return construct(t, arguments, getPrototypeOf(this).constructor);\n    }\n    return Wrapper.prototype = Object.create(t.prototype, {\n      constructor: {\n        value: Wrapper,\n        enumerable: !1,\n        writable: !0,\n        configurable: !0\n      }\n    }), setPrototypeOf(Wrapper, t);\n  }, _wrapNativeSuper(t);\n}\nexport { _wrapNativeSuper as default };", "map": {"version": 3, "names": ["getPrototypeOf", "setPrototypeOf", "isNativeFunction", "construct", "_wrapNativeSuper", "t", "r", "Map", "TypeError", "has", "get", "set", "Wrapper", "arguments", "constructor", "prototype", "Object", "create", "value", "enumerable", "writable", "configurable", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/@babel+runtime@7.26.0/node_modules/@babel/runtime/helpers/esm/wrapNativeSuper.js"], "sourcesContent": ["import getPrototypeOf from \"./getPrototypeOf.js\";\nimport setPrototypeOf from \"./setPrototypeOf.js\";\nimport isNativeFunction from \"./isNativeFunction.js\";\nimport construct from \"./construct.js\";\nfunction _wrapNativeSuper(t) {\n  var r = \"function\" == typeof Map ? new Map() : void 0;\n  return _wrapNativeSuper = function _wrapNativeSuper(t) {\n    if (null === t || !isNativeFunction(t)) return t;\n    if (\"function\" != typeof t) throw new TypeError(\"Super expression must either be null or a function\");\n    if (void 0 !== r) {\n      if (r.has(t)) return r.get(t);\n      r.set(t, Wrapper);\n    }\n    function Wrapper() {\n      return construct(t, arguments, getPrototypeOf(this).constructor);\n    }\n    return Wrapper.prototype = Object.create(t.prototype, {\n      constructor: {\n        value: Wrapper,\n        enumerable: !1,\n        writable: !0,\n        configurable: !0\n      }\n    }), setPrototypeOf(Wrapper, t);\n  }, _wrapNativeSuper(t);\n}\nexport { _wrapNativeSuper as default };"], "mappings": "AAAA,OAAOA,cAAc,MAAM,qBAAqB;AAChD,OAAOC,cAAc,MAAM,qBAAqB;AAChD,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,OAAOC,SAAS,MAAM,gBAAgB;AACtC,SAASC,gBAAgBA,CAACC,CAAC,EAAE;EAC3B,IAAIC,CAAC,GAAG,UAAU,IAAI,OAAOC,GAAG,GAAG,IAAIA,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;EACrD,OAAOH,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,CAAC,EAAE;IACrD,IAAI,IAAI,KAAKA,CAAC,IAAI,CAACH,gBAAgB,CAACG,CAAC,CAAC,EAAE,OAAOA,CAAC;IAChD,IAAI,UAAU,IAAI,OAAOA,CAAC,EAAE,MAAM,IAAIG,SAAS,CAAC,oDAAoD,CAAC;IACrG,IAAI,KAAK,CAAC,KAAKF,CAAC,EAAE;MAChB,IAAIA,CAAC,CAACG,GAAG,CAACJ,CAAC,CAAC,EAAE,OAAOC,CAAC,CAACI,GAAG,CAACL,CAAC,CAAC;MAC7BC,CAAC,CAACK,GAAG,CAACN,CAAC,EAAEO,OAAO,CAAC;IACnB;IACA,SAASA,OAAOA,CAAA,EAAG;MACjB,OAAOT,SAAS,CAACE,CAAC,EAAEQ,SAAS,EAAEb,cAAc,CAAC,IAAI,CAAC,CAACc,WAAW,CAAC;IAClE;IACA,OAAOF,OAAO,CAACG,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACZ,CAAC,CAACU,SAAS,EAAE;MACpDD,WAAW,EAAE;QACXI,KAAK,EAAEN,OAAO;QACdO,UAAU,EAAE,CAAC,CAAC;QACdC,QAAQ,EAAE,CAAC,CAAC;QACZC,YAAY,EAAE,CAAC;MACjB;IACF,CAAC,CAAC,EAAEpB,cAAc,CAACW,OAAO,EAAEP,CAAC,CAAC;EAChC,CAAC,EAAED,gBAAgB,CAACC,CAAC,CAAC;AACxB;AACA,SAASD,gBAAgB,IAAIkB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}