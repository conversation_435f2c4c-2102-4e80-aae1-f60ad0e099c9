{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"preview-pic\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image_viewer = _resolveComponent(\"el-image-viewer\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_image_viewer, {\n    \"url-list\": $setup.fileUrl\n  }, null, 8 /* PROPS */, [\"url-list\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_image_viewer", "$setup", "fileUrl"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\main\\src\\components\\global-file-preview\\components\\preview-pic.vue"], "sourcesContent": ["<template>\r\n  <div class=\"preview-pic\">\r\n    <el-image-viewer :url-list=\"fileUrl\"></el-image-viewer>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'PreviewPic' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { computed } from 'vue'\r\nconst props = defineProps({ id: { type: String, default: '' }, type: { type: String, default: '' } })\r\nconst fileUrl = computed(() => [`${api.fileURL(props.id + '.' + props.type)}`])\r\n</script>\r\n<style lang=\"scss\">\r\n.preview-pic {\r\n  width: 100%;\r\n  height: 100%;\r\n  position: relative;\r\n\r\n  .zy-el-image-viewer__wrapper {\r\n    position: absolute;\r\n    overflow: hidden;\r\n\r\n    .zy-el-image-viewer__mask {\r\n      background-color: transparent;\r\n    }\r\n\r\n    .zy-el-image-viewer__close {\r\n      display: none;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAa;;;uBAAxBC,mBAAA,CAEM,OAFNC,UAEM,GADJC,YAAA,CAAuDC,0BAAA;IAArC,UAAQ,EAAEC,MAAA,CAAAC;EAAO,sC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}