{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock, createTextVNode as _createTextVNode, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = {\n  key: 0,\n  class: \"content\"\n};\nvar _hoisted_2 = {\n  class: \"suggestPopContentHeader\"\n};\nvar _hoisted_3 = {\n  key: 0,\n  class: \"suggestPopContentChooseRole\"\n};\nvar _hoisted_4 = {\n  class: \"suggestPopContentBody\"\n};\nvar _hoisted_5 = {\n  class: \"mb20\"\n};\nvar _hoisted_6 = {\n  class: \"hasColorBox\"\n};\nvar _hoisted_7 = {\n  class: \"nocolorSpan\"\n};\nvar _hoisted_8 = {\n  class: \"nocolorSpan\"\n};\nvar _hoisted_9 = {\n  class: \"nocolorSpan\"\n};\nvar _hoisted_10 = {\n  class: \"mb20\"\n};\nvar _hoisted_11 = {\n  class: \"mb20\"\n};\nvar _hoisted_12 = {\n  class: \"hasColorBox\"\n};\nvar _hoisted_13 = {\n  class: \"nocolorSpan\"\n};\nvar _hoisted_14 = {\n  class: \"nocolorSpan\"\n};\nvar _hoisted_15 = {\n  class: \"nocolorSpan\"\n};\nvar _hoisted_16 = {\n  class: \"mb20\"\n};\nvar _hoisted_17 = {\n  class: \"hasColorBox\"\n};\nvar _hoisted_18 = {\n  class: \"nocolorSpan\"\n};\nvar _hoisted_19 = {\n  class: \"nocolorSpan\"\n};\nvar _hoisted_20 = {\n  class: \"nocolorSpan\"\n};\nvar _hoisted_21 = {\n  class: \"nocolorSpan\"\n};\nvar _hoisted_22 = {\n  class: \"mb20\"\n};\nvar _hoisted_23 = {\n  class: \"nocolorSpan\"\n};\nvar _hoisted_24 = {\n  class: \"nocolorSpan\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _$setup$tableData$mee, _$setup$tableData$usu, _$setup$tableData$imp, _$setup$tableData$aud, _$setup$tableData$pre, _$setup$tableData$pre2, _$setup$tableData$han, _$setup$tableData$red, _$setup$tableData$yel, _$setup$tableData$gre, _$setup$tableData$adj, _$setup$tableData$del, _$setup$tableData$ans, _$setup$tableData$sat, _$setup$tableData$fin, _$setup$tableData$mee2, _$setup$tableData$usu2, _$setup$tableData$imp2, _$setup$tableData$aud2, _$setup$tableData$pre3, _$setup$tableData$pre4, _$setup$tableData$han2, _$setup$tableData$red2, _$setup$tableData$yel2, _$setup$tableData$gre2, _$setup$tableData$adj2, _$setup$tableData$del2, _$setup$tableData$ans2, _$setup$tableData$sat2, _$setup$tableData$fin2, _$setup$tableData$han3, _$setup$tableData$imp3, _$setup$tableData$red3, _$setup$tableData$yel3, _$setup$tableData$gre3, _$setup$tableData$adj3, _$setup$tableData$del3, _$setup$tableData$ans3, _$setup$tableData$fin3, _$setup$tableData$mem, _$setup$tableData$tea, _$setup$tableData$del4, _$setup$tableData$nor, _$setup$tableData$imp4, _$setup$tableData$nee, _$setup$tableData$bac, _$setup$tableData$dra, _$setup$tableData$sat3;\n  var _component_RefreshRight = _resolveComponent(\"RefreshRight\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_Close = _resolveComponent(\"Close\");\n  var _component_More = _resolveComponent(\"More\");\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", {\n    class: _normalizeClass([\"suggestPop\", {\n      show: $props.isVisible || $setup.show\n    }])\n  }, [_createElementVNode(\"div\", {\n    class: _normalizeClass([\"suggestPopHead\", {\n      showHead: $setup.show\n    }])\n  }, [$setup.show ? (_openBlock(), _createBlock(_component_el_icon, {\n    key: 0\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_RefreshRight, {\n        onClick: $setup.RefreshRightclick\n      })];\n    }),\n    _: 1 /* STABLE */\n  })) : _createCommentVNode(\"v-if\", true), $setup.show ? (_openBlock(), _createBlock(_component_el_icon, {\n    key: 1,\n    onClick: $setup.closePop\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_Close)];\n    }),\n    _: 1 /* STABLE */\n  })) : (_openBlock(), _createBlock(_component_el_icon, {\n    key: 2,\n    onClick: $setup.closePop\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_More)];\n    }),\n    _: 1 /* STABLE */\n  }))], 2 /* CLASS */), $setup.delayedShow ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, _toDisplayString($setup.user.userName) + \"，\" + _toDisplayString($setup.getgreetings()), 1 /* TEXT */), $setup.roles.length > 1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_cache[37] || (_cache[37] = _createElementVNode(\"div\", null, \"选择您的身份以查看更多待办\", -1 /* HOISTED */)), _createVNode(_component_el_select, {\n    modelValue: $setup.role,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.role = $event;\n    }),\n    size: \"small\",\n    style: {\n      \"width\": \"120px\"\n    },\n    onChange: $setup.getCompositeData\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.rulesoptions, function (item) {\n        return _openBlock(), _createBlock(_component_el_option, {\n          key: item.value,\n          label: item.label,\n          value: item.value\n        }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", null, _toDisplayString($setup.tableData.termYear), 1 /* TEXT */), $setup.role == 'npc_contact_committee' ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 0\n  }, [_createElementVNode(\"div\", null, [_createElementVNode(\"span\", {\n    onClick: _cache[1] || (_cache[1] = function ($event) {\n      return $setup.goSuggestList('AllSuggest', 'meetList');\n    })\n  }, _toDisplayString(((_$setup$tableData$mee = $setup.tableData.meetList) === null || _$setup$tableData$mee === void 0 ? void 0 : _$setup$tableData$mee.amount) || 0), 1 /* TEXT */), _cache[38] || (_cache[38] = _createTextVNode(\" 件大会建议， \")), _createElementVNode(\"span\", {\n    onClick: _cache[2] || (_cache[2] = function ($event) {\n      return $setup.goSuggestList('AllSuggest', 'usualList');\n    })\n  }, _toDisplayString(((_$setup$tableData$usu = $setup.tableData.usualList) === null || _$setup$tableData$usu === void 0 ? void 0 : _$setup$tableData$usu.amount) || 0), 1 /* TEXT */), _cache[39] || (_cache[39] = _createTextVNode(\" 件闭会建议， \")), _createElementVNode(\"span\", {\n    onClick: _cache[3] || (_cache[3] = function ($event) {\n      return $setup.goSuggestList('SuggestControls?tableId=id_sgsn_suggestion_main&moduleName=重点建议', 'importantList');\n    })\n  }, _toDisplayString(((_$setup$tableData$imp = $setup.tableData.importantList) === null || _$setup$tableData$imp === void 0 ? void 0 : _$setup$tableData$imp.amount) || 0), 1 /* TEXT */), _cache[40] || (_cache[40] = _createTextVNode(\" 件重点督办建议 \"))]), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"span\", {\n    onClick: _cache[4] || (_cache[4] = function ($event) {\n      return $setup.goSuggestList('SuggestReview?tableId=id_sgsn_suggestion_prepareVerify&moduleName=待审查建议', 'auditList');\n    })\n  }, _toDisplayString(((_$setup$tableData$aud = $setup.tableData.auditList) === null || _$setup$tableData$aud === void 0 ? void 0 : _$setup$tableData$aud.amount) || 0), 1 /* TEXT */), _cache[42] || (_cache[42] = _createTextVNode(\" 件待审查， \")), $setup.suggestionEnablePreAssign ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 0\n  }, [_createElementVNode(\"span\", {\n    onClick: _cache[5] || (_cache[5] = function ($event) {\n      return $setup.goSuggestList('SuggestAdvanceAssign?tableId=id_sgsn_suggestion_preAssignSuggestion&moduleName=预交办', 'preAssignList');\n    })\n  }, _toDisplayString(((_$setup$tableData$pre = $setup.tableData.preAssignList) === null || _$setup$tableData$pre === void 0 ? void 0 : _$setup$tableData$pre.amount) || 0), 1 /* TEXT */), _cache[41] || (_cache[41] = _createTextVNode(\" 件预交办， \"))], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"span\", {\n    onClick: _cache[6] || (_cache[6] = function ($event) {\n      return $setup.goSuggestList('SuggestAssign?tableId=id_sgsn_suggestion_prepareSubmitHandle&moduleName=人大交办中', 'prepareSubmitHandleList');\n    })\n  }, _toDisplayString(((_$setup$tableData$pre2 = $setup.tableData.prepareSubmitHandleList) === null || _$setup$tableData$pre2 === void 0 ? void 0 : _$setup$tableData$pre2.amount) || 0), 1 /* TEXT */), _cache[43] || (_cache[43] = _createTextVNode(\" 件人大交办中 \"))]), _createElementVNode(\"div\", _hoisted_6, [_createElementVNode(\"span\", {\n    onClick: _cache[7] || (_cache[7] = function ($event) {\n      return $setup.goSuggestList('SuggestTransact', 'handleList');\n    })\n  }, _toDisplayString(((_$setup$tableData$han = $setup.tableData.handleList) === null || _$setup$tableData$han === void 0 ? void 0 : _$setup$tableData$han.amount) || 0), 1 /* TEXT */), _cache[44] || (_cache[44] = _createTextVNode(\" 件办理中，其中 \")), _cache[45] || (_cache[45] = _createElementVNode(\"span\", {\n    class: \"red\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_7, _toDisplayString(((_$setup$tableData$red = $setup.tableData.redAnswerDate) === null || _$setup$tableData$red === void 0 ? void 0 : _$setup$tableData$red.amount) || 0), 1 /* TEXT */), _cache[46] || (_cache[46] = _createTextVNode(\" 件， \")), _cache[47] || (_cache[47] = _createElementVNode(\"span\", {\n    class: \"yellow\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_8, _toDisplayString(((_$setup$tableData$yel = $setup.tableData.yellowAnswerDate) === null || _$setup$tableData$yel === void 0 ? void 0 : _$setup$tableData$yel.amount) || 0), 1 /* TEXT */), _cache[48] || (_cache[48] = _createTextVNode(\" 件， \")), _cache[49] || (_cache[49] = _createElementVNode(\"span\", {\n    class: \"green\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_9, _toDisplayString(((_$setup$tableData$gre = $setup.tableData.greenAnswerDate) === null || _$setup$tableData$gre === void 0 ? void 0 : _$setup$tableData$gre.amount) || 0), 1 /* TEXT */), _cache[50] || (_cache[50] = _createTextVNode(\" 件 \"))]), _createElementVNode(\"div\", _hoisted_10, [_createElementVNode(\"span\", {\n    onClick: _cache[8] || (_cache[8] = function ($event) {\n      return $setup.goSuggestList('SuggestApplyForAdjust', 'adjustList');\n    })\n  }, _toDisplayString(((_$setup$tableData$adj = $setup.tableData.adjustList) === null || _$setup$tableData$adj === void 0 ? void 0 : _$setup$tableData$adj.amount) || 0), 1 /* TEXT */), _cache[51] || (_cache[51] = _createTextVNode(\" 件调整申请待审核， \")), _createElementVNode(\"span\", {\n    onClick: _cache[9] || (_cache[9] = function ($event) {\n      return $setup.goSuggestList('SuggestApplyForPostpone', 'delayList');\n    })\n  }, _toDisplayString(((_$setup$tableData$del = $setup.tableData.delayList) === null || _$setup$tableData$del === void 0 ? void 0 : _$setup$tableData$del.amount) || 0), 1 /* TEXT */), _cache[52] || (_cache[52] = _createTextVNode(\" 件延期申请待审核 \"))]), _createElementVNode(\"div\", null, [_createElementVNode(\"span\", {\n    onClick: _cache[10] || (_cache[10] = function ($event) {\n      return $setup.goSuggestList('SuggestReply', 'answerList');\n    })\n  }, _toDisplayString(((_$setup$tableData$ans = $setup.tableData.answerList) === null || _$setup$tableData$ans === void 0 ? void 0 : _$setup$tableData$ans.amount) || 0), 1 /* TEXT */), _cache[53] || (_cache[53] = _createTextVNode(\" 件已答复 \"))]), _createElementVNode(\"div\", null, [_createElementVNode(\"span\", {\n    onClick: _cache[11] || (_cache[11] = function ($event) {\n      return $setup.goSuggestList('AllSuggest', 'satisfactionList');\n    })\n  }, _toDisplayString(((_$setup$tableData$sat = $setup.tableData.satisfactionList) === null || _$setup$tableData$sat === void 0 ? void 0 : _$setup$tableData$sat.amount) || 0), 1 /* TEXT */), _cache[54] || (_cache[54] = _createTextVNode(\" 件已答复待代表满意度测评 \"))]), _createElementVNode(\"div\", null, [_createElementVNode(\"span\", {\n    onClick: _cache[12] || (_cache[12] = function ($event) {\n      return $setup.goSuggestList('SuggestConclude', 'finishList');\n    })\n  }, _toDisplayString(((_$setup$tableData$fin = $setup.tableData.finishList) === null || _$setup$tableData$fin === void 0 ? void 0 : _$setup$tableData$fin.amount) || 0), 1 /* TEXT */), _cache[55] || (_cache[55] = _createTextVNode(\" 件已办结 \"))])], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), $setup.role === 'proposal_committee' ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 1\n  }, [_createElementVNode(\"div\", null, [_createElementVNode(\"span\", {\n    onClick: _cache[13] || (_cache[13] = function ($event) {\n      return $setup.goSuggestList('AllSuggest', 'meetList');\n    })\n  }, _toDisplayString(((_$setup$tableData$mee2 = $setup.tableData.meetList) === null || _$setup$tableData$mee2 === void 0 ? void 0 : _$setup$tableData$mee2.amount) || 0), 1 /* TEXT */), _cache[56] || (_cache[56] = _createTextVNode(\" 件大会提案， \")), _createElementVNode(\"span\", {\n    onClick: _cache[14] || (_cache[14] = function ($event) {\n      return $setup.goSuggestList('AllSuggest', 'usualList');\n    })\n  }, _toDisplayString(((_$setup$tableData$usu2 = $setup.tableData.usualList) === null || _$setup$tableData$usu2 === void 0 ? void 0 : _$setup$tableData$usu2.amount) || 0), 1 /* TEXT */), _cache[57] || (_cache[57] = _createTextVNode(\" 件闭会提案， \")), _createElementVNode(\"span\", {\n    onClick: _cache[15] || (_cache[15] = function ($event) {\n      return $setup.goSuggestList('SuggestControls?tableId=id_prop_proposal_main&moduleName=重点提案', 'importantList');\n    })\n  }, _toDisplayString(((_$setup$tableData$imp2 = $setup.tableData.importantList) === null || _$setup$tableData$imp2 === void 0 ? void 0 : _$setup$tableData$imp2.amount) || 0), 1 /* TEXT */), _cache[58] || (_cache[58] = _createTextVNode(\" 件重点督办提案 \"))]), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"span\", {\n    onClick: _cache[16] || (_cache[16] = function ($event) {\n      return $setup.goSuggestList('SuggestReview?tableId=id_prop_proposal_prepareVerify&moduleName=待审查提案', 'auditList');\n    })\n  }, _toDisplayString(((_$setup$tableData$aud2 = $setup.tableData.auditList) === null || _$setup$tableData$aud2 === void 0 ? void 0 : _$setup$tableData$aud2.amount) || 0), 1 /* TEXT */), _cache[60] || (_cache[60] = _createTextVNode(\" 件待审查， \")), $setup.suggestionEnablePreAssign ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 0\n  }, [_createElementVNode(\"span\", {\n    onClick: _cache[17] || (_cache[17] = function ($event) {\n      return $setup.goSuggestList('SuggestAdvanceAssign?tableId=id_prop_proposal_preAssignPropoasl&moduleName=预交办', 'preAssignList');\n    })\n  }, _toDisplayString(((_$setup$tableData$pre3 = $setup.tableData.preAssignList) === null || _$setup$tableData$pre3 === void 0 ? void 0 : _$setup$tableData$pre3.amount) || 0), 1 /* TEXT */), _cache[59] || (_cache[59] = _createTextVNode(\" 件预交办， \"))], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"span\", {\n    onClick: _cache[18] || (_cache[18] = function ($event) {\n      return $setup.goSuggestList('SuggestAssign?tableId=id_prop_proposal_prepareSubmitHandle&moduleName=政协交办中', 'prepareSubmitHandleList');\n    })\n  }, _toDisplayString(((_$setup$tableData$pre4 = $setup.tableData.prepareSubmitHandleList) === null || _$setup$tableData$pre4 === void 0 ? void 0 : _$setup$tableData$pre4.amount) || 0), 1 /* TEXT */), _cache[61] || (_cache[61] = _createTextVNode(\" 件政协交办中 \"))]), _createElementVNode(\"div\", _hoisted_12, [_createElementVNode(\"span\", {\n    onClick: _cache[19] || (_cache[19] = function ($event) {\n      return $setup.goSuggestList('SuggestTransact', 'handleList');\n    })\n  }, _toDisplayString(((_$setup$tableData$han2 = $setup.tableData.handleList) === null || _$setup$tableData$han2 === void 0 ? void 0 : _$setup$tableData$han2.amount) || 0), 1 /* TEXT */), _cache[62] || (_cache[62] = _createTextVNode(\" 件办理中，其中 \")), _cache[63] || (_cache[63] = _createElementVNode(\"span\", {\n    class: \"red\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_13, _toDisplayString(((_$setup$tableData$red2 = $setup.tableData.redAnswerDate) === null || _$setup$tableData$red2 === void 0 ? void 0 : _$setup$tableData$red2.amount) || 0), 1 /* TEXT */), _cache[64] || (_cache[64] = _createTextVNode(\" 件， \")), _cache[65] || (_cache[65] = _createElementVNode(\"span\", {\n    class: \"yellow\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_14, _toDisplayString(((_$setup$tableData$yel2 = $setup.tableData.yellowAnswerDate) === null || _$setup$tableData$yel2 === void 0 ? void 0 : _$setup$tableData$yel2.amount) || 0), 1 /* TEXT */), _cache[66] || (_cache[66] = _createTextVNode(\" 件， \")), _cache[67] || (_cache[67] = _createElementVNode(\"span\", {\n    class: \"green\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_15, _toDisplayString(((_$setup$tableData$gre2 = $setup.tableData.greenAnswerDate) === null || _$setup$tableData$gre2 === void 0 ? void 0 : _$setup$tableData$gre2.amount) || 0), 1 /* TEXT */), _cache[68] || (_cache[68] = _createTextVNode(\" 件 \"))]), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"span\", {\n    onClick: _cache[20] || (_cache[20] = function ($event) {\n      return $setup.goSuggestList('SuggestApplyForAdjust', 'adjustList');\n    })\n  }, _toDisplayString(((_$setup$tableData$adj2 = $setup.tableData.adjustList) === null || _$setup$tableData$adj2 === void 0 ? void 0 : _$setup$tableData$adj2.amount) || 0), 1 /* TEXT */), _cache[69] || (_cache[69] = _createTextVNode(\" 件调整申请待审核， \")), _createElementVNode(\"span\", {\n    onClick: _cache[21] || (_cache[21] = function ($event) {\n      return $setup.goSuggestList('SuggestApplyForPostpone', 'delayList');\n    })\n  }, _toDisplayString(((_$setup$tableData$del2 = $setup.tableData.delayList) === null || _$setup$tableData$del2 === void 0 ? void 0 : _$setup$tableData$del2.amount) || 0), 1 /* TEXT */), _cache[70] || (_cache[70] = _createTextVNode(\" 件延期申请待审核 \"))]), _createElementVNode(\"div\", null, [_createElementVNode(\"span\", {\n    onClick: _cache[22] || (_cache[22] = function ($event) {\n      return $setup.goSuggestList('SuggestReply', 'answerList');\n    })\n  }, _toDisplayString(((_$setup$tableData$ans2 = $setup.tableData.answerList) === null || _$setup$tableData$ans2 === void 0 ? void 0 : _$setup$tableData$ans2.amount) || 0), 1 /* TEXT */), _cache[71] || (_cache[71] = _createTextVNode(\" 件已答复 \"))]), _createElementVNode(\"div\", null, [_createElementVNode(\"span\", {\n    onClick: _cache[23] || (_cache[23] = function ($event) {\n      return $setup.goSuggestList('SuggestSatisfaction', 'satisfactionList');\n    })\n  }, _toDisplayString(((_$setup$tableData$sat2 = $setup.tableData.satisfactionList) === null || _$setup$tableData$sat2 === void 0 ? void 0 : _$setup$tableData$sat2.amount) || 0), 1 /* TEXT */), _cache[72] || (_cache[72] = _createTextVNode(\" 件已答复待委员满意度测评 \"))]), _createElementVNode(\"div\", null, [_createElementVNode(\"span\", {\n    onClick: _cache[24] || (_cache[24] = function ($event) {\n      return $setup.goSuggestList('SuggestConclude', 'finishList');\n    })\n  }, _toDisplayString(((_$setup$tableData$fin2 = $setup.tableData.finishList) === null || _$setup$tableData$fin2 === void 0 ? void 0 : _$setup$tableData$fin2.amount) || 0), 1 /* TEXT */), _cache[73] || (_cache[73] = _createTextVNode(\" 件已办结 \"))])], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), $setup.role == 'suggestion_office_user' ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 2\n  }, [_createElementVNode(\"div\", null, [_cache[74] || (_cache[74] = _createTextVNode(\" 共 \")), _createElementVNode(\"span\", {\n    onClick: _cache[25] || (_cache[25] = function ($event) {\n      return $setup.goSuggestList('UnitSuggestTransact', 'handleList');\n    })\n  }, _toDisplayString(((_$setup$tableData$han3 = $setup.tableData.handleList) === null || _$setup$tableData$han3 === void 0 ? void 0 : _$setup$tableData$han3.amount) || 0), 1 /* TEXT */), _cache[75] || (_cache[75] = _createTextVNode(\" 件办理中， \")), _createElementVNode(\"span\", {\n    onClick: _cache[26] || (_cache[26] = function ($event) {\n      return $setup.goSuggestList($setup.systemPlatform == 'CPPCC' ? 'SuggestControls?tableId=id_prop_proposal_main&moduleName=重点提案' : 'SuggestControls?tableId=id_sgsn_suggestion_main&moduleName=重点建议', 'importantList');\n    })\n  }, _toDisplayString(((_$setup$tableData$imp3 = $setup.tableData.importantList) === null || _$setup$tableData$imp3 === void 0 ? void 0 : _$setup$tableData$imp3.amount) || 0), 1 /* TEXT */), _createTextVNode(\" 件重点督办\" + _toDisplayString($setup.systemPlatform == 'CPPCC' ? '提案' : '建议'), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_17, [_cache[76] || (_cache[76] = _createTextVNode(\" 其中 \")), _cache[77] || (_cache[77] = _createElementVNode(\"span\", {\n    class: \"red\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_18, _toDisplayString(((_$setup$tableData$red3 = $setup.tableData.redAnswerDate) === null || _$setup$tableData$red3 === void 0 ? void 0 : _$setup$tableData$red3.amount) || 0), 1 /* TEXT */), _cache[78] || (_cache[78] = _createTextVNode(\" 件， \")), _cache[79] || (_cache[79] = _createElementVNode(\"span\", {\n    class: \"yellow\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_19, _toDisplayString(((_$setup$tableData$yel3 = $setup.tableData.yellowAnswerDate) === null || _$setup$tableData$yel3 === void 0 ? void 0 : _$setup$tableData$yel3.amount) || 0), 1 /* TEXT */), _cache[80] || (_cache[80] = _createTextVNode(\" 件， \")), _cache[81] || (_cache[81] = _createElementVNode(\"span\", {\n    class: \"green\"\n  }, null, -1 /* HOISTED */)), _createElementVNode(\"span\", _hoisted_20, _toDisplayString(((_$setup$tableData$gre3 = $setup.tableData.greenAnswerDate) === null || _$setup$tableData$gre3 === void 0 ? void 0 : _$setup$tableData$gre3.amount) || 0), 1 /* TEXT */), _cache[82] || (_cache[82] = _createTextVNode(\" 件 \"))]), _createElementVNode(\"div\", null, [_createElementVNode(\"span\", _hoisted_21, _toDisplayString(((_$setup$tableData$adj3 = $setup.tableData.adjustList) === null || _$setup$tableData$adj3 === void 0 ? void 0 : _$setup$tableData$adj3.amount) || 0), 1 /* TEXT */), _cache[83] || (_cache[83] = _createTextVNode(\" 件调整申请待审核， \"))]), _createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"span\", _hoisted_23, _toDisplayString(((_$setup$tableData$del3 = $setup.tableData.delayList) === null || _$setup$tableData$del3 === void 0 ? void 0 : _$setup$tableData$del3.amount) || 0), 1 /* TEXT */), _cache[84] || (_cache[84] = _createTextVNode(\" 件申请延期待审核， \"))]), _createElementVNode(\"div\", null, [_createElementVNode(\"span\", {\n    onClick: _cache[27] || (_cache[27] = function ($event) {\n      return $setup.goSuggestList('UnitSuggestReply', 'answerList');\n    })\n  }, _toDisplayString(((_$setup$tableData$ans3 = $setup.tableData.answerList) === null || _$setup$tableData$ans3 === void 0 ? void 0 : _$setup$tableData$ans3.amount) || 0), 1 /* TEXT */), _cache[85] || (_cache[85] = _createTextVNode(\" 件已答复 \"))]), _createCommentVNode(\" <div>\\r\\n            <span @click=\\\"goSuggestList('satisfactionList')\\\">{{ tableData.satisfactionList?.amount || 0 }}</span>\\r\\n            件已答复待代表满意度测评\\r\\n          </div> \"), _createElementVNode(\"div\", null, [_createElementVNode(\"span\", {\n    onClick: _cache[28] || (_cache[28] = function ($event) {\n      return $setup.goSuggestList('UnitSuggestConclude', 'finishList');\n    })\n  }, _toDisplayString(((_$setup$tableData$fin3 = $setup.tableData.finishList) === null || _$setup$tableData$fin3 === void 0 ? void 0 : _$setup$tableData$fin3.amount) || 0), 1 /* TEXT */), _cache[86] || (_cache[86] = _createTextVNode(\" 件已办结 \"))])], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), $setup.role == 'delegation_manager' ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 3\n  }, [_createElementVNode(\"div\", null, [_cache[87] || (_cache[87] = _createTextVNode(\" 本代表团 \")), _createElementVNode(\"span\", {\n    onClick: _cache[29] || (_cache[29] = function ($event) {\n      return $setup.goSuggestList('AllSuggest', 'memberList');\n    })\n  }, _toDisplayString(((_$setup$tableData$mem = $setup.tableData.memberList) === null || _$setup$tableData$mem === void 0 ? void 0 : _$setup$tableData$mem.amount) || 0), 1 /* TEXT */), _cache[88] || (_cache[88] = _createTextVNode(\" 件代表建议， \")), _createElementVNode(\"span\", {\n    onClick: _cache[30] || (_cache[30] = function ($event) {\n      return $setup.goSuggestList('AllSuggest', 'teamList');\n    })\n  }, _toDisplayString(((_$setup$tableData$tea = $setup.tableData.teamList) === null || _$setup$tableData$tea === void 0 ? void 0 : _$setup$tableData$tea.amount) || 0), 1 /* TEXT */), _cache[89] || (_cache[89] = _createTextVNode(\" 件全团建议 \"))]), _createElementVNode(\"div\", null, [_createElementVNode(\"span\", {\n    onClick: _cache[31] || (_cache[31] = function ($event) {\n      return $setup.goSuggestList('SuggestReview?tableId=id_sgsn_suggestion_base_delegation_verify&moduleName=代表团审查&nextNode=prepareVerify', 'delegationAuditList');\n    })\n  }, _toDisplayString(((_$setup$tableData$del4 = $setup.tableData.delegationAuditList) === null || _$setup$tableData$del4 === void 0 ? void 0 : _$setup$tableData$del4.amount) || 0), 1 /* TEXT */), _cache[90] || (_cache[90] = _createTextVNode(\" 件待代表团审查建议 \"))])], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true), $setup.role == 'npc_member' || $setup.role == 'cppcc_member' ? (_openBlock(), _createElementBlock(_Fragment, {\n    key: 4\n  }, [_createElementVNode(\"div\", null, [_cache[91] || (_cache[91] = _createTextVNode(\" 您已提交 \")), _createElementVNode(\"span\", {\n    onClick: _cache[32] || (_cache[32] = function ($event) {\n      return $setup.goSuggestList('MyLedSuggest', 'normalList');\n    })\n  }, _toDisplayString(((_$setup$tableData$nor = $setup.tableData.normalList) === null || _$setup$tableData$nor === void 0 ? void 0 : _$setup$tableData$nor.amount) || 0), 1 /* TEXT */), _createTextVNode(\" 件\" + _toDisplayString($setup.systemPlatform == 'CPPCC' ? '提案' : '建议') + \"， \", 1 /* TEXT */), _createElementVNode(\"span\", _hoisted_24, _toDisplayString(((_$setup$tableData$imp4 = $setup.tableData.importantList) === null || _$setup$tableData$imp4 === void 0 ? void 0 : _$setup$tableData$imp4.amount) || 0), 1 /* TEXT */), _createTextVNode(\" 件形成重点督办\" + _toDisplayString($setup.systemPlatform == 'CPPCC' ? '提案' : '建议'), 1 /* TEXT */)]), _createElementVNode(\"div\", null, [_createElementVNode(\"span\", {\n    onClick: _cache[33] || (_cache[33] = function ($event) {\n      return $setup.goSuggestList('MyJointSuggest', 'needJoinList');\n    })\n  }, _toDisplayString(((_$setup$tableData$nee = $setup.tableData.needJoinList) === null || _$setup$tableData$nee === void 0 ? void 0 : _$setup$tableData$nee.amount) || 0), 1 /* TEXT */), _createTextVNode(\" 件需要确认是否\" + _toDisplayString($setup.systemPlatform == 'CPPCC' ? '联名' : '附议') + \"， \", 1 /* TEXT */), _createElementVNode(\"span\", {\n    onClick: _cache[34] || (_cache[34] = function ($event) {\n      return $setup.goSuggestList('MyLedSuggest', 'backList');\n    })\n  }, _toDisplayString(((_$setup$tableData$bac = $setup.tableData.backList) === null || _$setup$tableData$bac === void 0 ? void 0 : _$setup$tableData$bac.amount) || 0), 1 /* TEXT */), _cache[92] || (_cache[92] = _createTextVNode(\" 件被退回， \")), _createElementVNode(\"span\", {\n    onClick: _cache[35] || (_cache[35] = function ($event) {\n      return $setup.goSuggestList($setup.systemPlatform == 'CPPCC' ? 'SuggestDraftBox?nextNode=prepareVerify' : 'SuggestDraftBox?nextNode=prepareVerify', 'draftsList');\n    })\n  }, _toDisplayString(((_$setup$tableData$dra = $setup.tableData.draftsList) === null || _$setup$tableData$dra === void 0 ? void 0 : _$setup$tableData$dra.amount) || 0), 1 /* TEXT */), _cache[93] || (_cache[93] = _createTextVNode(\" 件在草稿箱 \"))]), _createElementVNode(\"div\", null, [_createElementVNode(\"span\", {\n    onClick: _cache[36] || (_cache[36] = function ($event) {\n      return $setup.goSuggestList('MyLedSuggest', 'satisfactionList');\n    })\n  }, _toDisplayString(((_$setup$tableData$sat3 = $setup.tableData.satisfactionList) === null || _$setup$tableData$sat3 === void 0 ? void 0 : _$setup$tableData$sat3.amount) || 0), 1 /* TEXT */), _cache[94] || (_cache[94] = _createTextVNode(\" 件待满意度测评 \"))])], 64 /* STABLE_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)])])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */)), [[_directive_loading, $setup.loading]]);\n}", "map": {"version": 3, "names": ["key", "class", "_createElementBlock", "_normalizeClass", "show", "$props", "isVisible", "$setup", "_createElementVNode", "showHead", "_createBlock", "_component_el_icon", "default", "_withCtx", "_createVNode", "_component_RefreshRight", "onClick", "RefreshRightclick", "_", "_createCommentVNode", "closePop", "_component_Close", "_component_More", "delayedShow", "_hoisted_1", "_hoisted_2", "_toDisplayString", "user", "userName", "getgreetings", "roles", "length", "_hoisted_3", "_component_el_select", "modelValue", "role", "_cache", "$event", "size", "style", "onChange", "getCompositeData", "_Fragment", "_renderList", "rulesoptions", "item", "_component_el_option", "value", "label", "_hoisted_4", "tableData", "termYear", "goSuggestList", "_$setup$tableData$mee", "meetList", "amount", "_createTextVNode", "_$setup$tableData$usu", "usualList", "_$setup$tableData$imp", "importantList", "_hoisted_5", "_$setup$tableData$aud", "auditList", "suggestionEnablePreAssign", "_$setup$tableData$pre", "preAssignList", "_$setup$tableData$pre2", "prepareSubmitHandleList", "_hoisted_6", "_$setup$tableData$han", "handleList", "_hoisted_7", "_$setup$tableData$red", "redAnswerDate", "_hoisted_8", "_$setup$tableData$yel", "yellowAnswerDate", "_hoisted_9", "_$setup$tableData$gre", "greenAnswerDate", "_hoisted_10", "_$setup$tableData$adj", "adjustList", "_$setup$tableData$del", "delayList", "_$setup$tableData$ans", "answerList", "_$setup$tableData$sat", "satisfactionList", "_$setup$tableData$fin", "finishList", "_$setup$tableData$mee2", "_$setup$tableData$usu2", "_$setup$tableData$imp2", "_hoisted_11", "_$setup$tableData$aud2", "_$setup$tableData$pre3", "_$setup$tableData$pre4", "_hoisted_12", "_$setup$tableData$han2", "_hoisted_13", "_$setup$tableData$red2", "_hoisted_14", "_$setup$tableData$yel2", "_hoisted_15", "_$setup$tableData$gre2", "_hoisted_16", "_$setup$tableData$adj2", "_$setup$tableData$del2", "_$setup$tableData$ans2", "_$setup$tableData$sat2", "_$setup$tableData$fin2", "_$setup$tableData$han3", "systemPlatform", "_$setup$tableData$imp3", "_hoisted_17", "_hoisted_18", "_$setup$tableData$red3", "_hoisted_19", "_$setup$tableData$yel3", "_hoisted_20", "_$setup$tableData$gre3", "_hoisted_21", "_$setup$tableData$adj3", "_hoisted_22", "_hoisted_23", "_$setup$tableData$del3", "_$setup$tableData$ans3", "_$setup$tableData$fin3", "_$setup$tableData$mem", "memberList", "_$setup$tableData$tea", "teamList", "_$setup$tableData$del4", "delegationAuditList", "_$setup$tableData$nor", "normalList", "_hoisted_24", "_$setup$tableData$imp4", "_$setup$tableData$nee", "needJoinList", "_$setup$tableData$bac", "backList", "_$setup$tableData$dra", "draftsList", "_$setup$tableData$sat3", "loading"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LayoutView\\component\\suggestPop.vue"], "sourcesContent": ["<template>\r\n  <div class=\"suggestPop\" :class=\"{ show: isVisible || show }\" v-loading=\"loading\">\r\n    <div class=\"suggestPopHead\" :class=\"{ showHead: show }\">\r\n      <el-icon v-if=\"show\">\r\n        <RefreshRight @click=\"RefreshRightclick\" />\r\n      </el-icon>\r\n      <el-icon v-if=\"show\" @click=\"closePop\">\r\n        <Close />\r\n      </el-icon>\r\n      <el-icon @click=\"closePop\" v-else>\r\n        <More />\r\n      </el-icon>\r\n    </div>\r\n    <div class=\"content\" v-if=\"delayedShow\">\r\n      <div class=\"suggestPopContentHeader\">{{ user.userName }}，{{ getgreetings() }}</div>\r\n      <div class=\"suggestPopContentChooseRole\" v-if=\"roles.length > 1\">\r\n        <div>选择您的身份以查看更多待办</div>\r\n        <el-select v-model=\"role\" size=\"small\" style=\"width: 120px\" @change=\"getCompositeData\">\r\n          <el-option v-for=\"item in rulesoptions\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\" />\r\n        </el-select>\r\n      </div>\r\n      <div class=\"suggestPopContentBody\">\r\n        <div>{{ tableData.termYear }}</div>\r\n        <template v-if=\"role == 'npc_contact_committee'\">\r\n          <div>\r\n            <span @click=\"goSuggestList('AllSuggest', 'meetList')\">{{ tableData.meetList?.amount || 0 }}</span>\r\n            件大会建议，\r\n            <span @click=\"goSuggestList('AllSuggest', 'usualList')\">{{ tableData.usualList?.amount || 0 }}</span>\r\n            件闭会建议，\r\n            <span\r\n              @click=\"goSuggestList('SuggestControls?tableId=id_sgsn_suggestion_main&moduleName=重点建议', 'importantList')\">\r\n              {{ tableData.importantList?.amount || 0 }}\r\n            </span>\r\n            件重点督办建议\r\n          </div>\r\n          <div class=\"mb20\">\r\n            <span\r\n              @click=\"goSuggestList('SuggestReview?tableId=id_sgsn_suggestion_prepareVerify&moduleName=待审查建议', 'auditList')\">\r\n              {{ tableData.auditList?.amount || 0 }}\r\n            </span>\r\n            件待审查，\r\n            <template v-if=\"suggestionEnablePreAssign\">\r\n              <span @click=\"\r\n                goSuggestList('SuggestAdvanceAssign?tableId=id_sgsn_suggestion_preAssignSuggestion&moduleName=预交办', 'preAssignList')\r\n                \">\r\n                {{ tableData.preAssignList?.amount || 0 }}\r\n              </span>\r\n              件预交办，\r\n            </template>\r\n            <span @click=\"\r\n              goSuggestList('SuggestAssign?tableId=id_sgsn_suggestion_prepareSubmitHandle&moduleName=人大交办中', 'prepareSubmitHandleList')\r\n              \">\r\n              {{ tableData.prepareSubmitHandleList?.amount || 0 }}\r\n            </span>\r\n            件人大交办中\r\n          </div>\r\n          <div class=\"hasColorBox\">\r\n            <span @click=\"goSuggestList('SuggestTransact', 'handleList')\">{{ tableData.handleList?.amount || 0 }}</span>\r\n            件办理中，其中\r\n            <span class=\"red\"></span>\r\n            <span class=\"nocolorSpan\">{{ tableData.redAnswerDate?.amount || 0 }}</span>\r\n            件，\r\n            <span class=\"yellow\"></span>\r\n            <span class=\"nocolorSpan\">{{ tableData.yellowAnswerDate?.amount || 0 }}</span>\r\n            件，\r\n            <span class=\"green\"></span>\r\n            <span class=\"nocolorSpan\">{{ tableData.greenAnswerDate?.amount || 0 }}</span>\r\n            件\r\n          </div>\r\n          <div class=\"mb20\">\r\n            <span @click=\"goSuggestList('SuggestApplyForAdjust', 'adjustList')\">{{ tableData.adjustList?.amount || 0\r\n              }}</span>\r\n            件调整申请待审核，\r\n            <span @click=\"goSuggestList('SuggestApplyForPostpone', 'delayList')\">{{ tableData.delayList?.amount || 0\r\n              }}</span>\r\n            件延期申请待审核\r\n          </div>\r\n          <div>\r\n            <span @click=\"goSuggestList('SuggestReply', 'answerList')\">{{ tableData.answerList?.amount || 0 }}</span>\r\n            件已答复\r\n          </div>\r\n          <div>\r\n            <span @click=\"goSuggestList('AllSuggest', 'satisfactionList')\">{{ tableData.satisfactionList?.amount || 0\r\n              }}</span>\r\n            件已答复待代表满意度测评\r\n          </div>\r\n          <div>\r\n            <span @click=\"goSuggestList('SuggestConclude', 'finishList')\">{{ tableData.finishList?.amount || 0 }}</span>\r\n            件已办结\r\n          </div>\r\n        </template>\r\n        <template v-if=\"role === 'proposal_committee'\">\r\n          <div>\r\n            <span @click=\"goSuggestList('AllSuggest', 'meetList')\">{{ tableData.meetList?.amount || 0 }}</span>\r\n            件大会提案，\r\n            <span @click=\"goSuggestList('AllSuggest', 'usualList')\">{{ tableData.usualList?.amount || 0 }}</span>\r\n            件闭会提案，\r\n            <span\r\n              @click=\"goSuggestList('SuggestControls?tableId=id_prop_proposal_main&moduleName=重点提案', 'importantList')\">\r\n              {{ tableData.importantList?.amount || 0 }}\r\n            </span>\r\n            件重点督办提案\r\n          </div>\r\n          <div class=\"mb20\">\r\n            <span\r\n              @click=\"goSuggestList('SuggestReview?tableId=id_prop_proposal_prepareVerify&moduleName=待审查提案', 'auditList')\">\r\n              {{ tableData.auditList?.amount || 0 }}\r\n            </span>\r\n            件待审查，\r\n            <template v-if=\"suggestionEnablePreAssign\">\r\n              <span @click=\"\r\n                goSuggestList('SuggestAdvanceAssign?tableId=id_prop_proposal_preAssignPropoasl&moduleName=预交办', 'preAssignList')\r\n                \">\r\n                {{ tableData.preAssignList?.amount || 0 }}\r\n              </span>\r\n              件预交办，\r\n            </template>\r\n            <span @click=\"\r\n              goSuggestList('SuggestAssign?tableId=id_prop_proposal_prepareSubmitHandle&moduleName=政协交办中', 'prepareSubmitHandleList')\r\n              \">\r\n              {{ tableData.prepareSubmitHandleList?.amount || 0 }}\r\n            </span>\r\n            件政协交办中\r\n          </div>\r\n          <div class=\"hasColorBox\">\r\n            <span @click=\"goSuggestList('SuggestTransact', 'handleList')\">{{ tableData.handleList?.amount || 0 }}</span>\r\n            件办理中，其中\r\n            <span class=\"red\"></span>\r\n            <span class=\"nocolorSpan\">{{ tableData.redAnswerDate?.amount || 0 }}</span>\r\n            件，\r\n            <span class=\"yellow\"></span>\r\n            <span class=\"nocolorSpan\">{{ tableData.yellowAnswerDate?.amount || 0 }}</span>\r\n            件，\r\n            <span class=\"green\"></span>\r\n            <span class=\"nocolorSpan\">{{ tableData.greenAnswerDate?.amount || 0 }}</span>\r\n            件\r\n          </div>\r\n          <div class=\"mb20\">\r\n            <span @click=\"goSuggestList('SuggestApplyForAdjust', 'adjustList')\">{{ tableData.adjustList?.amount || 0\r\n              }}</span>\r\n            件调整申请待审核，\r\n            <span @click=\"goSuggestList('SuggestApplyForPostpone', 'delayList')\">{{ tableData.delayList?.amount || 0\r\n              }}</span>\r\n            件延期申请待审核\r\n          </div>\r\n          <div>\r\n            <span @click=\"goSuggestList('SuggestReply', 'answerList')\">{{ tableData.answerList?.amount || 0 }}</span>\r\n            件已答复\r\n          </div>\r\n          <div>\r\n            <span @click=\"goSuggestList('SuggestSatisfaction', 'satisfactionList')\">{{\r\n              tableData.satisfactionList?.amount || 0\r\n              }}</span>\r\n            件已答复待委员满意度测评\r\n          </div>\r\n          <div>\r\n            <span @click=\"goSuggestList('SuggestConclude', 'finishList')\">{{ tableData.finishList?.amount || 0 }}</span>\r\n            件已办结\r\n          </div>\r\n        </template>\r\n        <template v-if=\"role == 'suggestion_office_user'\">\r\n          <div>\r\n            共\r\n            <span @click=\"goSuggestList('UnitSuggestTransact', 'handleList')\">{{ tableData.handleList?.amount || 0\r\n              }}</span>\r\n            件办理中，\r\n            <span @click=\"\r\n              goSuggestList(\r\n                systemPlatform == 'CPPCC'\r\n                  ? 'SuggestControls?tableId=id_prop_proposal_main&moduleName=重点提案'\r\n                  : 'SuggestControls?tableId=id_sgsn_suggestion_main&moduleName=重点建议',\r\n                'importantList'\r\n              )\r\n              \">\r\n              {{ tableData.importantList?.amount || 0 }}\r\n            </span>\r\n            件重点督办{{ systemPlatform == 'CPPCC' ? '提案' : '建议' }}\r\n          </div>\r\n          <div class=\"hasColorBox\">\r\n            其中\r\n            <span class=\"red\"></span>\r\n            <span class=\"nocolorSpan\">{{ tableData.redAnswerDate?.amount || 0 }}</span>\r\n            件，\r\n            <span class=\"yellow\"></span>\r\n            <span class=\"nocolorSpan\">{{ tableData.yellowAnswerDate?.amount || 0 }}</span>\r\n            件，\r\n            <span class=\"green\"></span>\r\n            <span class=\"nocolorSpan\">{{ tableData.greenAnswerDate?.amount || 0 }}</span>\r\n            件\r\n          </div>\r\n          <div>\r\n            <span class=\"nocolorSpan\">{{ tableData.adjustList?.amount || 0 }}</span>\r\n            件调整申请待审核，\r\n          </div>\r\n          <div class=\"mb20\">\r\n            <span class=\"nocolorSpan\">{{ tableData.delayList?.amount || 0 }}</span>\r\n            件申请延期待审核，\r\n          </div>\r\n          <div>\r\n            <span @click=\"goSuggestList('UnitSuggestReply', 'answerList')\">{{ tableData.answerList?.amount || 0\r\n              }}</span>\r\n            件已答复\r\n          </div>\r\n          <!-- <div>\r\n            <span @click=\"goSuggestList('satisfactionList')\">{{ tableData.satisfactionList?.amount || 0 }}</span>\r\n            件已答复待代表满意度测评\r\n          </div> -->\r\n          <div>\r\n            <span @click=\"goSuggestList('UnitSuggestConclude', 'finishList')\">{{ tableData.finishList?.amount || 0\r\n              }}</span>\r\n            件已办结\r\n          </div>\r\n        </template>\r\n        <template v-if=\"role == 'delegation_manager'\">\r\n          <div>\r\n            本代表团\r\n            <span @click=\"goSuggestList('AllSuggest', 'memberList')\">{{ tableData.memberList?.amount || 0 }}</span>\r\n            件代表建议，\r\n            <span @click=\"goSuggestList('AllSuggest', 'teamList')\">{{ tableData.teamList?.amount || 0 }}</span>\r\n            件全团建议\r\n          </div>\r\n          <div>\r\n            <span @click=\"\r\n              goSuggestList(\r\n                'SuggestReview?tableId=id_sgsn_suggestion_base_delegation_verify&moduleName=代表团审查&nextNode=prepareVerify',\r\n                'delegationAuditList'\r\n              )\r\n              \">\r\n              {{ tableData.delegationAuditList?.amount || 0 }}\r\n            </span>\r\n            件待代表团审查建议\r\n          </div>\r\n        </template>\r\n        <template v-if=\"role == 'npc_member' || role == 'cppcc_member'\">\r\n          <div>\r\n            您已提交\r\n            <span @click=\"goSuggestList('MyLedSuggest', 'normalList')\">\r\n              {{ tableData.normalList?.amount || 0 }}\r\n            </span>\r\n            件{{ systemPlatform == 'CPPCC' ? '提案' : '建议' }}，\r\n            <span class=\"nocolorSpan\">{{ tableData.importantList?.amount || 0 }}</span>\r\n            件形成重点督办{{ systemPlatform == 'CPPCC' ? '提案' : '建议' }}\r\n          </div>\r\n          <div>\r\n            <span @click=\"goSuggestList('MyJointSuggest', 'needJoinList')\">\r\n              {{ tableData.needJoinList?.amount || 0 }}\r\n            </span>\r\n            件需要确认是否{{ systemPlatform == 'CPPCC' ? '联名' : '附议' }}，\r\n            <span @click=\"goSuggestList('MyLedSuggest', 'backList')\">\r\n              {{ tableData.backList?.amount || 0 }}\r\n            </span>\r\n            件被退回，\r\n            <span @click=\"\r\n              goSuggestList(\r\n                systemPlatform == 'CPPCC'\r\n                  ? 'SuggestDraftBox?nextNode=prepareVerify'\r\n                  : 'SuggestDraftBox?nextNode=prepareVerify',\r\n                'draftsList'\r\n              )\r\n              \">\r\n              {{ tableData.draftsList?.amount || 0 }}\r\n            </span>\r\n            件在草稿箱\r\n          </div>\r\n          <div>\r\n            <span @click=\"goSuggestList('MyLedSuggest', 'satisfactionList')\">\r\n              {{ tableData.satisfactionList?.amount || 0 }}\r\n            </span>\r\n            件待满意度测评\r\n          </div>\r\n        </template>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'suggestPop'\r\n}\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, computed, inject } from 'vue'\r\nimport { openConfig } from 'common/js/system_var.js'\r\nconst systemPlatform = computed(() => openConfig.value?.systemPlatform || '')\r\nconst props = defineProps({\r\n  isVisible: { type: Boolean, default: false },\r\n  routePth: { type: String, default: '' }\r\n})\r\n\r\nconst openPage = inject('openPage')\r\nconst getgreetings = () => {\r\n  const hour = new Date().getHours()\r\n  if (hour < 12) {\r\n    return '早上好'\r\n  } else if (hour < 18) {\r\n    return '下午好'\r\n  } else {\r\n    return '晚上好'\r\n  }\r\n}\r\n// npc_contact_committee  npc_member  delegation_manager  suggestion_office_user\r\n\r\nconst show = ref(false)\r\nconst delayedShow = ref(false)\r\nconst closePop = () => {\r\n  if (delayedShow.value) {\r\n    delayedShow.value = !delayedShow.value\r\n  } else {\r\n    setTimeout(() => {\r\n      delayedShow.value = !delayedShow.value\r\n    }, 300)\r\n  }\r\n  show.value = !show.value\r\n}\r\n\r\nconst user = ref({})\r\nconst canChooseRoles = ref(\r\n  systemPlatform.value == 'CPPCC'\r\n    ? ['proposal_committee', 'suggestion_office_user', 'cppcc_member']\r\n    : ['npc_contact_committee', 'suggestion_office_user', 'delegation_manager', 'npc_member']\r\n)\r\n\r\nconst role = ref('')\r\nconst rulesoptions = ref([])\r\nconst roles = ref([])\r\nconst suggestionEnablePreAssign = ref(false) // 是否开启预交办\r\nconst getLoginHintConfig = async () => {\r\n  const { data } = await api.globalReadOpenConfig({\r\n    codes: ['suggestion_enable_pre_assign', 'proposal_enable_pre_assign']\r\n  })\r\n  if (data.suggestion_enable_pre_assign && systemPlatform.value != 'CPPCC') {\r\n    suggestionEnablePreAssign.value = data.suggestion_enable_pre_assign == 'true'\r\n  }\r\n  if (data.proposal_enable_pre_assign && systemPlatform.value == 'CPPCC') {\r\n    suggestionEnablePreAssign.value = data.proposal_enable_pre_assign == 'true'\r\n  }\r\n}\r\nonMounted(() => {\r\n  getLoginHintConfig()\r\n  user.value = JSON.parse(sessionStorage.getItem('user'))\r\n  roles.value = user.value.specialRoleKeys.filter((item) => canChooseRoles.value.includes(item))\r\n  if (roles.value.includes('npc_contact_committee'))\r\n    rulesoptions.value.push({ value: 'npc_contact_committee', label: '联工委', param: 'remind_admin' })\r\n  if (roles.value.includes('proposal_committee'))\r\n    rulesoptions.value.push({ value: 'proposal_committee', label: '提案委', param: 'remind_admin' })\r\n  if (roles.value.includes('suggestion_office_user'))\r\n    rulesoptions.value.push({ value: 'suggestion_office_user', label: '办理单位', param: 'remind_office' })\r\n  if (roles.value.includes('delegation_manager'))\r\n    rulesoptions.value.push({ value: 'delegation_manager', label: '代表团管理员', param: 'remind_delegation' })\r\n  if (roles.value.includes('npc_member'))\r\n    rulesoptions.value.push({ value: 'npc_member', label: '人大代表', param: 'remind_npc_member' })\r\n  if (roles.value.includes('cppcc_member'))\r\n    rulesoptions.value.push({ value: 'cppcc_member', label: '政协委员', param: 'remind_member' })\r\n  role.value = rulesoptions.value[0].value\r\n  getCompositeData()\r\n  setTimeout(() => {\r\n    show.value = true\r\n    setTimeout(() => {\r\n      delayedShow.value = true\r\n    }, 100)\r\n  }, 300)\r\n})\r\nconst tableData = ref({})\r\nconst loading = ref(true)\r\nconst getCompositeData = async () => {\r\n  const url = systemPlatform.value === 'CPPCC' ? '/proposalStatistics/composite' : '/suggestionStatistics/composite'\r\n  const res = await api.globalJson(url, {\r\n    countView: rulesoptions.value.filter((v) => v.value === role.value)[0].param\r\n  })\r\n  tableData.value = res.data.tableData.length ? res.data.tableData[0] : {}\r\n  loading.value = false\r\n}\r\nconst RefreshRightclick = () => {\r\n  loading.value = true\r\n  getCompositeData()\r\n}\r\nconst goSuggestList = (type, key) => {\r\n  const count = tableData.value[key]?.amount || 0;\r\n  if (count === 0) {\r\n    ElMessage({\r\n      type: 'info',\r\n      message: `暂无${key === 'draftsList' ? '草稿' : '相关'}数据`\r\n    });\r\n    return;\r\n  }\r\n  const suggestIds = tableData.value[key]?.suggestionIds || tableData.value[key]?.proposalIds || []\r\n  console.log(suggestIds)\r\n\r\n  if (suggestIds.length) {\r\n    sessionStorage.setItem('suggestIds', JSON.stringify(suggestIds))\r\n  } else {\r\n    sessionStorage.removeItem('suggestIds')\r\n  }\r\n  const url = systemPlatform.value == 'CPPCC' ? 'proposal' : 'suggest'\r\n  // if (type) return\r\n  openPage({ key: 'routePath', value: `/${url}/` + type })\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.suggestPop {\r\n  position: absolute;\r\n  right: 16px;\r\n  bottom: 0;\r\n  width: 50px;\r\n  background: #fff;\r\n  z-index: 999;\r\n  transform: translateY(18);\r\n  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);\r\n  box-shadow: 0 -2px 12px rgba(0, 0, 0, 0.1);\r\n  border-radius: 8px 8px 0 0;\r\n  height: 18px;\r\n\r\n  &.show {\r\n    height: auto;\r\n    width: 500px;\r\n    height: 400px;\r\n    transform: translateY(0);\r\n  }\r\n\r\n  .suggestPopHead {\r\n    height: 18px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: flex-end;\r\n    padding: 0 16px;\r\n    background: var(--zy-el-color-primary);\r\n    color: #fff;\r\n    font-size: 20px;\r\n\r\n    .zy-el-icon {\r\n      margin-left: 10px;\r\n      cursor: pointer;\r\n    }\r\n\r\n    &.showHead {\r\n      height: 36px;\r\n    }\r\n  }\r\n\r\n  .content {\r\n    height: calc(100% - 36px);\r\n    padding: 16px;\r\n    overflow-y: auto;\r\n\r\n    .suggestPopContentHeader {\r\n      border-bottom: 1px solid #e5e5e5;\r\n      padding-bottom: 16px;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    .suggestPopContentChooseRole {\r\n      display: flex;\r\n      justify-content: flex-end;\r\n      align-items: center;\r\n      font-size: 14px;\r\n      margin-bottom: 20px;\r\n\r\n      .zy-el-select {\r\n        margin-left: 16px;\r\n      }\r\n    }\r\n\r\n    .suggestPopContentBody {\r\n      padding-left: 10px;\r\n      line-height: 26px;\r\n\r\n      span {\r\n        color: var(--zy-el-color-primary);\r\n        cursor: pointer;\r\n      }\r\n\r\n      .nocolorSpan {\r\n        color: var(--zy-el-text-color-primary);\r\n      }\r\n\r\n      .hasColorBox {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .red {\r\n          width: 20px;\r\n          height: 20px;\r\n          background: #f56c6c;\r\n          border-radius: 50%;\r\n          display: inline-block;\r\n        }\r\n\r\n        .yellow {\r\n          width: 20px;\r\n          height: 20px;\r\n          background: rgb(246, 185, 47);\r\n          border-radius: 50%;\r\n          display: inline-block;\r\n        }\r\n\r\n        .green {\r\n          background: rgb(51, 203, 116);\r\n          width: 20px;\r\n          height: 20px;\r\n          border-radius: 50%;\r\n          display: inline-block;\r\n        }\r\n      }\r\n\r\n      .mb20 {\r\n        margin-bottom: 14px;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAAAA,GAAA;EAaSC,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAyB;;EAd1CD,GAAA;EAeWC,KAAK,EAAC;;;EAMNA,KAAK,EAAC;AAAuB;;EAczBA,KAAK,EAAC;AAAM;;EAqBZA,KAAK,EAAC;AAAa;;EAIhBA,KAAK,EAAC;AAAa;;EAGnBA,KAAK,EAAC;AAAa;;EAGnBA,KAAK,EAAC;AAAa;;EAGtBA,KAAK,EAAC;AAAM;;EAkCZA,KAAK,EAAC;AAAM;;EAqBZA,KAAK,EAAC;AAAa;;EAIhBA,KAAK,EAAC;AAAa;;EAGnBA,KAAK,EAAC;AAAa;;EAGnBA,KAAK,EAAC;AAAa;;EAGtBA,KAAK,EAAC;AAAM;;EAyCZA,KAAK,EAAC;AAAa;;EAGhBA,KAAK,EAAC;AAAa;;EAGnBA,KAAK,EAAC;AAAa;;EAGnBA,KAAK,EAAC;AAAa;;EAInBA,KAAK,EAAC;AAAa;;EAGtBA,KAAK,EAAC;AAAM;;EACTA,KAAK,EAAC;AAAa;;EA6CnBA,KAAK,EAAC;AAAa;;;;;;;;;;wCA/OnCC,mBAAA,CAgRM;IAhRDD,KAAK,EADZE,eAAA,EACa,YAAY;MAAAC,IAAA,EAAiBC,MAAA,CAAAC,SAAS,IAAIC,MAAA,CAAAH;IAAI;MACvDI,mBAAA,CAUM;IAVDP,KAAK,EAFdE,eAAA,EAEe,gBAAgB;MAAAM,QAAA,EAAqBF,MAAA,CAAAH;IAAI;MACnCG,MAAA,CAAAH,IAAI,I,cAAnBM,YAAA,CAEUC,kBAAA;IALhBX,GAAA;EAAA;IAAAY,OAAA,EAAAC,QAAA,CAIQ;MAAA,OAA2C,CAA3CC,YAAA,CAA2CC,uBAAA;QAA5BC,OAAK,EAAET,MAAA,CAAAU;MAAiB,G;;IAJ/CC,CAAA;QAAAC,mBAAA,gBAMqBZ,MAAA,CAAAH,IAAI,I,cAAnBM,YAAA,CAEUC,kBAAA;IARhBX,GAAA;IAM4BgB,OAAK,EAAET,MAAA,CAAAa;;IANnCR,OAAA,EAAAC,QAAA,CAOQ;MAAA,OAAS,CAATC,YAAA,CAASO,gBAAA,E;;IAPjBH,CAAA;uBASMR,YAAA,CAEUC,kBAAA;IAXhBX,GAAA;IASgBgB,OAAK,EAAET,MAAA,CAAAa;;IATvBR,OAAA,EAAAC,QAAA,CAUQ;MAAA,OAAQ,CAARC,YAAA,CAAQQ,eAAA,E;;IAVhBJ,CAAA;wBAa+BX,MAAA,CAAAgB,WAAW,I,cAAtCrB,mBAAA,CAmQM,OAnQNsB,UAmQM,GAlQJhB,mBAAA,CAAmF,OAAnFiB,UAAmF,EAAAC,gBAAA,CAA3CnB,MAAA,CAAAoB,IAAI,CAACC,QAAQ,IAAG,GAAC,GAAAF,gBAAA,CAAGnB,MAAA,CAAAsB,YAAY,oBACzBtB,MAAA,CAAAuB,KAAK,CAACC,MAAM,Q,cAA3D7B,mBAAA,CAKM,OALN8B,UAKM,G,4BAJJxB,mBAAA,CAAwB,aAAnB,eAAa,sBAClBM,YAAA,CAEYmB,oBAAA;IAnBpBC,UAAA,EAiB4B3B,MAAA,CAAA4B,IAAI;IAjBhC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAiB4B9B,MAAA,CAAA4B,IAAI,GAAAE,MAAA;IAAA;IAAEC,IAAI,EAAC,OAAO;IAACC,KAAoB,EAApB;MAAA;IAAA,CAAoB;IAAEC,QAAM,EAAEjC,MAAA,CAAAkC;;IAjB7E7B,OAAA,EAAAC,QAAA,CAkBqB;MAAA,OAA4B,E,kBAAvCX,mBAAA,CAAoGwC,SAAA,QAlB9GC,WAAA,CAkBoCpC,MAAA,CAAAqC,YAAY,EAlBhD,UAkB4BC,IAAI;6BAAtBnC,YAAA,CAAoGoC,oBAAA;UAA3D9C,GAAG,EAAE6C,IAAI,CAACE,KAAK;UAAGC,KAAK,EAAEH,IAAI,CAACG,KAAK;UAAGD,KAAK,EAAEF,IAAI,CAACE;;;;IAlBrG7B,CAAA;yCAAAC,mBAAA,gBAqBMX,mBAAA,CA0PM,OA1PNyC,UA0PM,GAzPJzC,mBAAA,CAAmC,aAAAkB,gBAAA,CAA3BnB,MAAA,CAAA2C,SAAS,CAACC,QAAQ,kBACV5C,MAAA,CAAA4B,IAAI,+B,cAApBjC,mBAAA,CAmEWwC,SAAA;IA1FnB1C,GAAA;EAAA,IAwBUQ,mBAAA,CAUM,cATJA,mBAAA,CAAmG;IAA5FQ,OAAK,EAAAoB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAA+B,EAAAC,qBAAA,GAAA9C,MAAA,CAAA2C,SAAS,CAACI,QAAQ,cAAAD,qBAAA,uBAAlBA,qBAAA,CAAoBE,MAAM,wB,4BAzBhGC,gBAAA,CAyB+G,UAEnG,IAAAhD,mBAAA,CAAqG;IAA9FQ,OAAK,EAAAoB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAAgC,EAAAK,qBAAA,GAAAlD,MAAA,CAAA2C,SAAS,CAACQ,SAAS,cAAAD,qBAAA,uBAAnBA,qBAAA,CAAqBF,MAAM,wB,4BA3BlGC,gBAAA,CA2BiH,UAErG,IAAAhD,mBAAA,CAGO;IAFJQ,OAAK,EAAAoB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAClB,EAAAO,qBAAA,GAAApD,MAAA,CAAA2C,SAAS,CAACU,aAAa,cAAAD,qBAAA,uBAAvBA,qBAAA,CAAyBJ,MAAM,wB,4BA/BhDC,gBAAA,CAgCmB,WAET,G,GACAhD,mBAAA,CAoBM,OApBNqD,UAoBM,GAnBJrD,mBAAA,CAGO;IAFJQ,OAAK,EAAAoB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAClB,EAAAU,qBAAA,GAAAvD,MAAA,CAAA2C,SAAS,CAACa,SAAS,cAAAD,qBAAA,uBAAnBA,qBAAA,CAAqBP,MAAM,wB,4BAtC5CC,gBAAA,CAuCmB,SAEP,IAAgBjD,MAAA,CAAAyD,yBAAyB,I,cAAzC9D,mBAAA,CAOWwC,SAAA;IAhDvB1C,GAAA;EAAA,IA0CcQ,mBAAA,CAIO;IAJAQ,OAAK,EAAAoB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAoB9B,MAAA,CAAA6C,aAAa;IAAA,C;sBAGxC,EAAAa,qBAAA,GAAA1D,MAAA,CAAA2C,SAAS,CAACgB,aAAa,cAAAD,qBAAA,uBAAvBA,qBAAA,CAAyBV,MAAM,wB,4BA7ClDC,gBAAA,CA8CqB,SAET,G,+BAhDZrC,mBAAA,gBAiDYX,mBAAA,CAIO;IAJAQ,OAAK,EAAAoB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAkB9B,MAAA,CAAA6C,aAAa;IAAA,C;sBAGtC,EAAAe,sBAAA,GAAA5D,MAAA,CAAA2C,SAAS,CAACkB,uBAAuB,cAAAD,sBAAA,uBAAjCA,sBAAA,CAAmCZ,MAAM,wB,4BApD1DC,gBAAA,CAqDmB,UAET,G,GACAhD,mBAAA,CAYM,OAZN6D,UAYM,GAXJ7D,mBAAA,CAA4G;IAArGQ,OAAK,EAAAoB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAAsC,EAAAkB,qBAAA,GAAA/D,MAAA,CAAA2C,SAAS,CAACqB,UAAU,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBf,MAAM,wB,4BAzDzGC,gBAAA,CAyDwH,WAE5G,I,4BAAAhD,mBAAA,CAAyB;IAAnBP,KAAK,EAAC;EAAK,6BACjBO,mBAAA,CAA2E,QAA3EgE,UAA2E,EAAA9C,gBAAA,CAA9C,EAAA+C,qBAAA,GAAAlE,MAAA,CAAA2C,SAAS,CAACwB,aAAa,cAAAD,qBAAA,uBAAvBA,qBAAA,CAAyBlB,MAAM,wB,4BA5DxEC,gBAAA,CA4DuF,MAE3E,I,4BAAAhD,mBAAA,CAA4B;IAAtBP,KAAK,EAAC;EAAQ,6BACpBO,mBAAA,CAA8E,QAA9EmE,UAA8E,EAAAjD,gBAAA,CAAjD,EAAAkD,qBAAA,GAAArE,MAAA,CAAA2C,SAAS,CAAC2B,gBAAgB,cAAAD,qBAAA,uBAA1BA,qBAAA,CAA4BrB,MAAM,wB,4BA/D3EC,gBAAA,CA+D0F,MAE9E,I,4BAAAhD,mBAAA,CAA2B;IAArBP,KAAK,EAAC;EAAO,6BACnBO,mBAAA,CAA6E,QAA7EsE,UAA6E,EAAApD,gBAAA,CAAhD,EAAAqD,qBAAA,GAAAxE,MAAA,CAAA2C,SAAS,CAAC8B,eAAe,cAAAD,qBAAA,uBAAzBA,qBAAA,CAA2BxB,MAAM,wB,4BAlE1EC,gBAAA,CAkEyF,KAE/E,G,GACAhD,mBAAA,CAOM,OAPNyE,WAOM,GANJzE,mBAAA,CACW;IADJQ,OAAK,EAAAoB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAA4C,EAAA8B,qBAAA,GAAA3E,MAAA,CAAA2C,SAAS,CAACiC,UAAU,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsB3B,MAAM,wB,4BAtE/GC,gBAAA,CAuEuB,aAEX,IAAAhD,mBAAA,CACW;IADJQ,OAAK,EAAAoB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAA6C,EAAAgC,qBAAA,GAAA7E,MAAA,CAAA2C,SAAS,CAACmC,SAAS,cAAAD,qBAAA,uBAAnBA,qBAAA,CAAqB7B,MAAM,wB,4BAzE/GC,gBAAA,CA0EuB,YAEb,G,GACAhD,mBAAA,CAGM,cAFJA,mBAAA,CAAyG;IAAlGQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAAmC,EAAAkC,qBAAA,GAAA/E,MAAA,CAAA2C,SAAS,CAACqC,UAAU,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsB/B,MAAM,wB,4BA9EtGC,gBAAA,CA8EqH,QAE3G,G,GACAhD,mBAAA,CAIM,cAHJA,mBAAA,CACW;IADJQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAAuC,EAAAoC,qBAAA,GAAAjF,MAAA,CAAA2C,SAAS,CAACuC,gBAAgB,cAAAD,qBAAA,uBAA1BA,qBAAA,CAA4BjC,MAAM,wB,4BAlFhHC,gBAAA,CAmFuB,gBAEb,G,GACAhD,mBAAA,CAGM,cAFJA,mBAAA,CAA4G;IAArGQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAAsC,EAAAsC,qBAAA,GAAAnF,MAAA,CAAA2C,SAAS,CAACyC,UAAU,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBnC,MAAM,wB,4BAvFzGC,gBAAA,CAuFwH,QAE9G,G,iCAzFVrC,mBAAA,gBA2FwBZ,MAAA,CAAA4B,IAAI,6B,cAApBjC,mBAAA,CAoEWwC,SAAA;IA/JnB1C,GAAA;EAAA,IA4FUQ,mBAAA,CAUM,cATJA,mBAAA,CAAmG;IAA5FQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAA+B,EAAAwC,sBAAA,GAAArF,MAAA,CAAA2C,SAAS,CAACI,QAAQ,cAAAsC,sBAAA,uBAAlBA,sBAAA,CAAoBrC,MAAM,wB,4BA7FhGC,gBAAA,CA6F+G,UAEnG,IAAAhD,mBAAA,CAAqG;IAA9FQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAAgC,EAAAyC,sBAAA,GAAAtF,MAAA,CAAA2C,SAAS,CAACQ,SAAS,cAAAmC,sBAAA,uBAAnBA,sBAAA,CAAqBtC,MAAM,wB,4BA/FlGC,gBAAA,CA+FiH,UAErG,IAAAhD,mBAAA,CAGO;IAFJQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAClB,EAAA0C,sBAAA,GAAAvF,MAAA,CAAA2C,SAAS,CAACU,aAAa,cAAAkC,sBAAA,uBAAvBA,sBAAA,CAAyBvC,MAAM,wB,4BAnGhDC,gBAAA,CAoGmB,WAET,G,GACAhD,mBAAA,CAoBM,OApBNuF,WAoBM,GAnBJvF,mBAAA,CAGO;IAFJQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAClB,EAAA4C,sBAAA,GAAAzF,MAAA,CAAA2C,SAAS,CAACa,SAAS,cAAAiC,sBAAA,uBAAnBA,sBAAA,CAAqBzC,MAAM,wB,4BA1G5CC,gBAAA,CA2GmB,SAEP,IAAgBjD,MAAA,CAAAyD,yBAAyB,I,cAAzC9D,mBAAA,CAOWwC,SAAA;IApHvB1C,GAAA;EAAA,IA8GcQ,mBAAA,CAIO;IAJAQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAoB9B,MAAA,CAAA6C,aAAa;IAAA,C;sBAGxC,EAAA6C,sBAAA,GAAA1F,MAAA,CAAA2C,SAAS,CAACgB,aAAa,cAAA+B,sBAAA,uBAAvBA,sBAAA,CAAyB1C,MAAM,wB,4BAjHlDC,gBAAA,CAkHqB,SAET,G,+BApHZrC,mBAAA,gBAqHYX,mBAAA,CAIO;IAJAQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAkB9B,MAAA,CAAA6C,aAAa;IAAA,C;sBAGtC,EAAA8C,sBAAA,GAAA3F,MAAA,CAAA2C,SAAS,CAACkB,uBAAuB,cAAA8B,sBAAA,uBAAjCA,sBAAA,CAAmC3C,MAAM,wB,4BAxH1DC,gBAAA,CAyHmB,UAET,G,GACAhD,mBAAA,CAYM,OAZN2F,WAYM,GAXJ3F,mBAAA,CAA4G;IAArGQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAAsC,EAAAgD,sBAAA,GAAA7F,MAAA,CAAA2C,SAAS,CAACqB,UAAU,cAAA6B,sBAAA,uBAApBA,sBAAA,CAAsB7C,MAAM,wB,4BA7HzGC,gBAAA,CA6HwH,WAE5G,I,4BAAAhD,mBAAA,CAAyB;IAAnBP,KAAK,EAAC;EAAK,6BACjBO,mBAAA,CAA2E,QAA3E6F,WAA2E,EAAA3E,gBAAA,CAA9C,EAAA4E,sBAAA,GAAA/F,MAAA,CAAA2C,SAAS,CAACwB,aAAa,cAAA4B,sBAAA,uBAAvBA,sBAAA,CAAyB/C,MAAM,wB,4BAhIxEC,gBAAA,CAgIuF,MAE3E,I,4BAAAhD,mBAAA,CAA4B;IAAtBP,KAAK,EAAC;EAAQ,6BACpBO,mBAAA,CAA8E,QAA9E+F,WAA8E,EAAA7E,gBAAA,CAAjD,EAAA8E,sBAAA,GAAAjG,MAAA,CAAA2C,SAAS,CAAC2B,gBAAgB,cAAA2B,sBAAA,uBAA1BA,sBAAA,CAA4BjD,MAAM,wB,4BAnI3EC,gBAAA,CAmI0F,MAE9E,I,4BAAAhD,mBAAA,CAA2B;IAArBP,KAAK,EAAC;EAAO,6BACnBO,mBAAA,CAA6E,QAA7EiG,WAA6E,EAAA/E,gBAAA,CAAhD,EAAAgF,sBAAA,GAAAnG,MAAA,CAAA2C,SAAS,CAAC8B,eAAe,cAAA0B,sBAAA,uBAAzBA,sBAAA,CAA2BnD,MAAM,wB,4BAtI1EC,gBAAA,CAsIyF,KAE/E,G,GACAhD,mBAAA,CAOM,OAPNmG,WAOM,GANJnG,mBAAA,CACW;IADJQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAA4C,EAAAwD,sBAAA,GAAArG,MAAA,CAAA2C,SAAS,CAACiC,UAAU,cAAAyB,sBAAA,uBAApBA,sBAAA,CAAsBrD,MAAM,wB,4BA1I/GC,gBAAA,CA2IuB,aAEX,IAAAhD,mBAAA,CACW;IADJQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAA6C,EAAAyD,sBAAA,GAAAtG,MAAA,CAAA2C,SAAS,CAACmC,SAAS,cAAAwB,sBAAA,uBAAnBA,sBAAA,CAAqBtD,MAAM,wB,4BA7I/GC,gBAAA,CA8IuB,YAEb,G,GACAhD,mBAAA,CAGM,cAFJA,mBAAA,CAAyG;IAAlGQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAAmC,EAAA0D,sBAAA,GAAAvG,MAAA,CAAA2C,SAAS,CAACqC,UAAU,cAAAuB,sBAAA,uBAApBA,sBAAA,CAAsBvD,MAAM,wB,4BAlJtGC,gBAAA,CAkJqH,QAE3G,G,GACAhD,mBAAA,CAKM,cAJJA,mBAAA,CAEW;IAFJQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBACzB,EAAA2D,sBAAA,GAAAxG,MAAA,CAAA2C,SAAS,CAACuC,gBAAgB,cAAAsB,sBAAA,uBAA1BA,sBAAA,CAA4BxD,MAAM,wB,4BAvJhDC,gBAAA,CAwJuB,gBAEb,G,GACAhD,mBAAA,CAGM,cAFJA,mBAAA,CAA4G;IAArGQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAAsC,EAAA4D,sBAAA,GAAAzG,MAAA,CAAA2C,SAAS,CAACyC,UAAU,cAAAqB,sBAAA,uBAApBA,sBAAA,CAAsBzD,MAAM,wB,4BA5JzGC,gBAAA,CA4JwH,QAE9G,G,iCA9JVrC,mBAAA,gBAgKwBZ,MAAA,CAAA4B,IAAI,gC,cAApBjC,mBAAA,CAoDWwC,SAAA;IApNnB1C,GAAA;EAAA,IAiKUQ,mBAAA,CAgBM,c,4BAjLhBgD,gBAAA,CAiKe,KAEH,IAAAhD,mBAAA,CACW;IADJQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAA0C,EAAA6D,sBAAA,GAAA1G,MAAA,CAAA2C,SAAS,CAACqB,UAAU,cAAA0C,sBAAA,uBAApBA,sBAAA,CAAsB1D,MAAM,wB,4BAnK7GC,gBAAA,CAoKuB,SAEX,IAAAhD,mBAAA,CASO;IATAQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAkB9B,MAAA,CAAA6C,aAAa,CAAmB7C,MAAA,CAAA2G,cAAc,c;;sBAQvE,EAAAC,sBAAA,GAAA5G,MAAA,CAAA2C,SAAS,CAACU,aAAa,cAAAuD,sBAAA,uBAAvBA,sBAAA,CAAyB5D,MAAM,wBA9KhDC,gBAAA,CA+KmB,QACF,GAAA9B,gBAAA,CAAGnB,MAAA,CAAA2G,cAAc,0C,GAExB1G,mBAAA,CAWM,OAXN4G,WAWM,G,4BA7LhB5D,gBAAA,CAkLmC,MAEvB,I,4BAAAhD,mBAAA,CAAyB;IAAnBP,KAAK,EAAC;EAAK,6BACjBO,mBAAA,CAA2E,QAA3E6G,WAA2E,EAAA3F,gBAAA,CAA9C,EAAA4F,sBAAA,GAAA/G,MAAA,CAAA2C,SAAS,CAACwB,aAAa,cAAA4C,sBAAA,uBAAvBA,sBAAA,CAAyB/D,MAAM,wB,4BArLxEC,gBAAA,CAqLuF,MAE3E,I,4BAAAhD,mBAAA,CAA4B;IAAtBP,KAAK,EAAC;EAAQ,6BACpBO,mBAAA,CAA8E,QAA9E+G,WAA8E,EAAA7F,gBAAA,CAAjD,EAAA8F,sBAAA,GAAAjH,MAAA,CAAA2C,SAAS,CAAC2B,gBAAgB,cAAA2C,sBAAA,uBAA1BA,sBAAA,CAA4BjE,MAAM,wB,4BAxL3EC,gBAAA,CAwL0F,MAE9E,I,4BAAAhD,mBAAA,CAA2B;IAArBP,KAAK,EAAC;EAAO,6BACnBO,mBAAA,CAA6E,QAA7EiH,WAA6E,EAAA/F,gBAAA,CAAhD,EAAAgG,sBAAA,GAAAnH,MAAA,CAAA2C,SAAS,CAAC8B,eAAe,cAAA0C,sBAAA,uBAAzBA,sBAAA,CAA2BnE,MAAM,wB,4BA3L1EC,gBAAA,CA2LyF,KAE/E,G,GACAhD,mBAAA,CAGM,cAFJA,mBAAA,CAAwE,QAAxEmH,WAAwE,EAAAjG,gBAAA,CAA3C,EAAAkG,sBAAA,GAAArH,MAAA,CAAA2C,SAAS,CAACiC,UAAU,cAAAyC,sBAAA,uBAApBA,sBAAA,CAAsBrE,MAAM,wB,4BA/LrEC,gBAAA,CA+LoF,aAE1E,G,GACAhD,mBAAA,CAGM,OAHNqH,WAGM,GAFJrH,mBAAA,CAAuE,QAAvEsH,WAAuE,EAAApG,gBAAA,CAA1C,EAAAqG,sBAAA,GAAAxH,MAAA,CAAA2C,SAAS,CAACmC,SAAS,cAAA0C,sBAAA,uBAAnBA,sBAAA,CAAqBxE,MAAM,wB,4BAnMpEC,gBAAA,CAmMmF,aAEzE,G,GACAhD,mBAAA,CAIM,cAHJA,mBAAA,CACW;IADJQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAAuC,EAAA4E,sBAAA,GAAAzH,MAAA,CAAA2C,SAAS,CAACqC,UAAU,cAAAyC,sBAAA,uBAApBA,sBAAA,CAAsBzE,MAAM,wB,4BAvM1GC,gBAAA,CAwMuB,QAEb,G,GACArC,mBAAA,kLAGU,EACVX,mBAAA,CAIM,cAHJA,mBAAA,CACW;IADJQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAA0C,EAAA6E,sBAAA,GAAA1H,MAAA,CAAA2C,SAAS,CAACyC,UAAU,cAAAsC,sBAAA,uBAApBA,sBAAA,CAAsB1E,MAAM,wB,4BAhN7GC,gBAAA,CAiNuB,QAEb,G,iCAnNVrC,mBAAA,gBAqNwBZ,MAAA,CAAA4B,IAAI,4B,cAApBjC,mBAAA,CAmBWwC,SAAA;IAxOnB1C,GAAA;EAAA,IAsNUQ,mBAAA,CAMM,c,4BA5NhBgD,gBAAA,CAsNe,QAEH,IAAAhD,mBAAA,CAAuG;IAAhGQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAAiC,EAAA8E,qBAAA,GAAA3H,MAAA,CAAA2C,SAAS,CAACiF,UAAU,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsB3E,MAAM,wB,4BAxNpGC,gBAAA,CAwNmH,UAEvG,IAAAhD,mBAAA,CAAmG;IAA5FQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBAA+B,EAAAgF,qBAAA,GAAA7H,MAAA,CAAA2C,SAAS,CAACmF,QAAQ,cAAAD,qBAAA,uBAAlBA,qBAAA,CAAoB7E,MAAM,wB,4BA1NhGC,gBAAA,CA0N+G,SAErG,G,GACAhD,mBAAA,CAUM,cATJA,mBAAA,CAOO;IAPAQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAkB9B,MAAA,CAAA6C,aAAa,C;;sBAMtC,EAAAkF,sBAAA,GAAA/H,MAAA,CAAA2C,SAAS,CAACqF,mBAAmB,cAAAD,sBAAA,uBAA7BA,sBAAA,CAA+B/E,MAAM,wB,4BApOtDC,gBAAA,CAqOmB,aAET,G,iCAvOVrC,mBAAA,gBAyOwBZ,MAAA,CAAA4B,IAAI,oBAAoB5B,MAAA,CAAA4B,IAAI,sB,cAA5CjC,mBAAA,CAqCWwC,SAAA;IA9QnB1C,GAAA;EAAA,IA0OUQ,mBAAA,CAQM,c,4BAlPhBgD,gBAAA,CA0Oe,QAEH,IAAAhD,mBAAA,CAEO;IAFAQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBACtB,EAAAoF,qBAAA,GAAAjI,MAAA,CAAA2C,SAAS,CAACuF,UAAU,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBjF,MAAM,wBA7O7CC,gBAAA,CA8OmB,IACN,GAAA9B,gBAAA,CAAGnB,MAAA,CAAA2G,cAAc,6BAA4B,IAC9C,iBAAA1G,mBAAA,CAA2E,QAA3EkI,WAA2E,EAAAhH,gBAAA,CAA9C,EAAAiH,sBAAA,GAAApI,MAAA,CAAA2C,SAAS,CAACU,aAAa,cAAA+E,sBAAA,uBAAvBA,sBAAA,CAAyBpF,MAAM,wBAhPxEC,gBAAA,CAgPuF,UACpE,GAAA9B,gBAAA,CAAGnB,MAAA,CAAA2G,cAAc,0C,GAE1B1G,mBAAA,CAoBM,cAnBJA,mBAAA,CAEO;IAFAQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBACtB,EAAAwF,qBAAA,GAAArI,MAAA,CAAA2C,SAAS,CAAC2F,YAAY,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBrF,MAAM,wBArP/CC,gBAAA,CAsPmB,UACA,GAAA9B,gBAAA,CAAGnB,MAAA,CAAA2G,cAAc,6BAA4B,IACpD,iBAAA1G,mBAAA,CAEO;IAFAQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBACtB,EAAA0F,qBAAA,GAAAvI,MAAA,CAAA2C,SAAS,CAAC6F,QAAQ,cAAAD,qBAAA,uBAAlBA,qBAAA,CAAoBvF,MAAM,wB,4BAzP3CC,gBAAA,CA0PmB,SAEP,IAAAhD,mBAAA,CASO;IATAQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAkB9B,MAAA,CAAA6C,aAAa,CAAmB7C,MAAA,CAAA2G,cAAc,c;;sBAQvE,EAAA8B,qBAAA,GAAAzI,MAAA,CAAA2C,SAAS,CAAC+F,UAAU,cAAAD,qBAAA,uBAApBA,qBAAA,CAAsBzF,MAAM,wB,4BApQ7CC,gBAAA,CAqQmB,SAET,G,GACAhD,mBAAA,CAKM,cAJJA,mBAAA,CAEO;IAFAQ,OAAK,EAAAoB,MAAA,SAAAA,MAAA,iBAAAC,MAAA;MAAA,OAAE9B,MAAA,CAAA6C,aAAa;IAAA;sBACtB,EAAA8F,sBAAA,GAAA3I,MAAA,CAAA2C,SAAS,CAACuC,gBAAgB,cAAAyD,sBAAA,uBAA1BA,sBAAA,CAA4B3F,MAAM,wB,4BA1QnDC,gBAAA,CA2QmB,WAET,G,iCA7QVrC,mBAAA,e,OAAAA,mBAAA,e,yCAC0EZ,MAAA,CAAA4I,OAAO,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}