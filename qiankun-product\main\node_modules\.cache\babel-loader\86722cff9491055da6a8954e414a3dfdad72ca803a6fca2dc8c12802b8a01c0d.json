{"ast": null, "code": "\"use strict\";\n\nvar utils = require(\"./utils.js\");\n\n/**\n * An object to write any content to an Uint8Array.\n * @constructor\n * @param {number} length The length of the array.\n */\nfunction Uint8ArrayWriter(length) {\n  this.data = new Uint8Array(length);\n  this.index = 0;\n}\nUint8ArrayWriter.prototype = {\n  /**\n   * Append any content to the current array.\n   * @param {Object} input the content to add.\n   */\n  append: function append(input) {\n    if (input.length !== 0) {\n      // with an empty Uint8Array, Opera fails with a \"Offset larger than array size\"\n      input = utils.transformTo(\"uint8array\", input);\n      this.data.set(input, this.index);\n      this.index += input.length;\n    }\n  },\n  /**\n   * Finalize the construction an return the result.\n   * @return {Uint8Array} the generated array.\n   */\n  finalize: function finalize() {\n    return this.data;\n  }\n};\nmodule.exports = Uint8ArrayWriter;", "map": {"version": 3, "names": ["utils", "require", "Uint8ArrayWriter", "length", "data", "Uint8Array", "index", "prototype", "append", "input", "transformTo", "set", "finalize", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/pizzip@3.1.7/node_modules/pizzip/js/uint8ArrayWriter.js"], "sourcesContent": ["\"use strict\";\n\nvar utils = require(\"./utils.js\");\n\n/**\n * An object to write any content to an Uint8Array.\n * @constructor\n * @param {number} length The length of the array.\n */\nfunction Uint8ArrayWriter(length) {\n  this.data = new Uint8Array(length);\n  this.index = 0;\n}\nUint8ArrayWriter.prototype = {\n  /**\n   * Append any content to the current array.\n   * @param {Object} input the content to add.\n   */\n  append: function append(input) {\n    if (input.length !== 0) {\n      // with an empty Uint8Array, Opera fails with a \"Offset larger than array size\"\n      input = utils.transformTo(\"uint8array\", input);\n      this.data.set(input, this.index);\n      this.index += input.length;\n    }\n  },\n  /**\n   * Finalize the construction an return the result.\n   * @return {Uint8Array} the generated array.\n   */\n  finalize: function finalize() {\n    return this.data;\n  }\n};\nmodule.exports = Uint8ArrayWriter;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;;AAEjC;AACA;AACA;AACA;AACA;AACA,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EAChC,IAAI,CAACC,IAAI,GAAG,IAAIC,UAAU,CAACF,MAAM,CAAC;EAClC,IAAI,CAACG,KAAK,GAAG,CAAC;AAChB;AACAJ,gBAAgB,CAACK,SAAS,GAAG;EAC3B;AACF;AACA;AACA;EACEC,MAAM,EAAE,SAASA,MAAMA,CAACC,KAAK,EAAE;IAC7B,IAAIA,KAAK,CAACN,MAAM,KAAK,CAAC,EAAE;MACtB;MACAM,KAAK,GAAGT,KAAK,CAACU,WAAW,CAAC,YAAY,EAAED,KAAK,CAAC;MAC9C,IAAI,CAACL,IAAI,CAACO,GAAG,CAACF,KAAK,EAAE,IAAI,CAACH,KAAK,CAAC;MAChC,IAAI,CAACA,KAAK,IAAIG,KAAK,CAACN,MAAM;IAC5B;EACF,CAAC;EACD;AACF;AACA;AACA;EACES,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;IAC5B,OAAO,IAAI,CAACR,IAAI;EAClB;AACF,CAAC;AACDS,MAAM,CAACC,OAAO,GAAGZ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}