<template>
  <transition name="details-fade">
    <div v-show="modelValue" class="LiveBroadcastDetails">
      <div class="LiveBroadcastDetailsHeader">
        <img v-if="logoSrc" class="LiveBroadcastDetailsEmblem" :src="logoSrc" alt="emblem" />
        <div class="LiveBroadcastDetailsHeadInfo">
          <div class="LiveBroadcastDetailsTitle">{{ details.theme }}</div>
          <div class="LiveBroadcastDetailsTime">直播时间：{{ format(details.startTime) }} 到 {{
            format(details.endTime) }}</div>
        </div>
        <el-icon class="LiveBroadcastDetailsClose" @click="handleClose">
          <Close />
        </el-icon>
      </div>
      <div class="LiveBroadcastDetailsBody">
        <div class="LiveBroadcastDetailsCanvas">
          <!-- 未开始：显示封面图 + 倒计时 -->
          <div v-if="details.meetingStatus === '未开始'" class="LiveBroadcastDetailsPoster">
            <img :src="imgUrl" alt="海报/播放画面区域" style="width: 100%;height: 100%;object-fit: contain;">
            <div class="LiveBroadcastDetailsCountdown">
              <div class="countdown-line1">距离 {{ format(details.startTime, 'MM/DD HH:mm') }} 直播开始还有</div>
              <div class="countdown-line2">{{ countdownTimeOnly }}</div>
            </div>
          </div>
          <!-- 进行中：直播播放器 -->
          <div v-else-if="details.meetingStatus === '进行中'" class="LiveBroadcastDetailsLiveOverlay">
            <div class="player-container">
              <video ref="videoPlayer" id="video-player" controls></video>
              <div class="controls">
                <button class="control-btn" @click="togglePlayPause">{{ isPlaying ? '⏸ 暂停' : '▶ 播放' }}</button>
                <div class="volume-control">
                  <span class="volume-icon">{{ volumeIcon }}</span>
                  <input type="range" min="0" max="1" step="0.05" v-model="volume" @input="updateVolume"
                    class="volume-slider">
                </div>
                <div class="progress-container" @click="seekVideo">
                  <div class="progress-bar" :style="{ width: progressPercent + '%' }"></div>
                </div>
                <span class="time-display">{{ timeDisplay }}</span>
                <select class="quality-selector" v-model="selectedQuality" @change="changeQuality"
                  :disabled="!qualityLevels.length">
                  <option value="">选择清晰度</option>
                  <option v-for="(level, index) in qualityLevels" :key="index" :value="index">
                    {{ level.height }}p ({{ Math.round(level.bitrate / 1000) }}kbps)
                  </option>
                </select>
                <button class="control-btn fullscreen-btn" @click="toggleFullscreen">
                  {{ isFullscreen ? '⮿ 退出全屏' : '⛶ 全屏' }}
                </button>
              </div>
            </div>
            <div class="status-panel">
              <div class="status-message">{{ statusMessage }}</div>
              <div class="error-message" v-if="errorMessage" :style="{ display: errorMessage ? 'block' : 'none' }">
                {{ errorMessage }}
              </div>
            </div>
          </div>
          <!-- 已结束：遮罩 + 回放按钮 -->
          <div v-else-if="details.meetingStatus === '已结束'" class="LiveBroadcastDetailsEnded">
            <div class="LiveBroadcastDetailsEndedWrap">
              <div class="endedTitle">直播已结束</div>
              <el-button type="primary" class="replayBtn" v-if="details.isReplay == 1">观看回放</el-button>
            </div>
          </div>
        </div>
        <div class="LiveBroadcastDetailsSidebar">
          <div class="LiveBroadcastDetailsTabs">
            <div :class="['LiveBroadcastDetailsTab', { active: activeTab === 'details' }]"
              @click="activeTab = 'details'">
              直播详情</div>
            <div :class="['LiveBroadcastDetailsTab', { active: activeTab === 'interact' }]"
              @click="activeTab = 'interact'">
              互动</div>
          </div>
          <div class="LiveBroadcastDetailsTabPane" v-if="activeTab === 'details'">
            <div class="detailsTitle">{{ details?.theme || '-' }}</div>
            <div class="detailsTime">时间：{{ format(details?.startTime) }} - {{ format(details?.endTime) }}</div>
            <div class="detailsDesc">{{ details.liveDescribes || '暂无简介' }}</div>
          </div>
          <div class="LiveBroadcastDetailsTabPane" v-else>
            <div class="LiveBroadcastDetailsPanelTitle">互动</div>
            <div class="LiveBroadcastDetailsPanelText">暂无互动内容</div>
          </div>
        </div>
      </div>
    </div>
  </transition>

</template>
<script>
export default { name: 'LiveBroadcastDetails' }
</script>
<script setup>
import api from '@/api'
import { ref, onBeforeUnmount, watch, onMounted, computed, nextTick } from 'vue'
import { format } from 'common/js/time.js'
import { Close } from '@element-plus/icons-vue'
// import Hls from 'hls.js'
// 临时使用CDN方式加载hls.js，您可以在index.html中添加：
// <script src="https://cdn.jsdelivr.net/npm/hls.js@latest"></script>
const Hls = window.Hls
// 使用 hls.js 播放 HLS 流
const props = defineProps({
modelValue: { type: Boolean, default: false },
id: { type: String, default: '' }
})
const emit = defineEmits(['callback', 'update:modelValue'])
const details = ref({})
const logoSrc = ref('https://xazx.cszysoft.com:8131/lzt/pageImg/open/logo?areaId=610100')

const activeTab = ref('details')
const imgUrl = computed(() => details.value.coverImg ? api.fileURL(details.value.coverImg) : '')
const countdownText = ref('')
const countdownTimeOnly = ref('')

// 视频播放器相关
const videoPlayer = ref(null)
const player = ref(null)
const hls = ref(null)
const isPlayerInitialized = ref(false)

// 播放器控制状态
const isPlaying = ref(false)
const volume = ref(1)
const volumeIcon = computed(() => {
if (volume.value === 0) return '🔇'
if (volume.value < 0.5) return '🔈' return '🔊' }) const progressPercent=ref(0) const timeDisplay=ref('00:00 / 加载中')
  const isFullscreen=ref(false) const qualityLevels=ref([]) const selectedQuality=ref('') const
  statusMessage=ref('HLS视频流播放器 | 准备连接视频源...') const errorMessage=ref('') // 监听 modelValue 变化 - 当组件从隐藏变为显示时加载数据
  watch(()=> props.modelValue, (newVal, oldVal) => {
  if (newVal && props.id && !oldVal) {
  // 只有从 false 变为 true 且有 id 时才加载数据
  getInfo()
  }
  })
}

  // 监听 id 变化 - 当 id 改变且组件显示时加载数据
  watch(() => props.id, (newVal, oldVal) => {
  if (newVal && props.modelValue && newVal !== oldVal) {
  getInfo()
  }
  })

  const getInfo = async () => {
  const res = await api.videoConnectionInfo({ detailId: props.id })
  var { data } = res
  details.value = data
  applyStatusFromProps()
  }

  // 监听状态变化
  watch(() => details.value.meetingStatus, (newStatus, oldStatus) => {
  if (newStatus === '进行中' && oldStatus !== '进行中') {
  // 从未开始变为进行中，初始化播放器
  nextTick(() => {
  initVideoPlayer()
  })
  } else if (newStatus !== '进行中' && oldStatus === '进行中') {
  // 从进行中变为其他状态，销毁播放器
  destroyVideoPlayer()
  }
  })

  const tickCountdown = () => {
  if (!details.value.startTime) return
  const start = new Date(details.value.startTime).getTime()
  const now = Date.now()
  let diff = Math.max(0, start - now)

  const sec = Math.floor(diff / 1000) % 60
  const min = Math.floor(diff / (1000 * 60)) % 60
  const hour = Math.floor(diff / (1000 * 60 * 60)) % 24
  const day = Math.floor(diff / (1000 * 60 * 60 * 24))

  let timeText = ''

  if (day > 0) {
  // 大于1天：显示 X天X时X分
  timeText = `${day} 天 ${hour} 时 ${min} 分`
  } else if (hour > 0) {
  // 大于1小时但小于1天：显示 X时X分X秒
  timeText = `${hour} 时 ${min} 分 ${sec} 秒`
  } else if (min > 0) {
  // 大于1分钟但小于1小时：显示 X分X秒
  timeText = `${min} 分 ${sec} 秒`
  } else {
  // 小于1分钟：显示 X秒
  timeText = `${sec} 秒`
  }

  // 设置完整的倒计时文本（保留原有逻辑）
  countdownText.value = `距离 ${format(details.value.startTime, 'MM/DD HH:mm')} 直播开始还有 ${timeText}`
  // 设置只包含时间的文本（用于两行显示）
  countdownTimeOnly.value = timeText
  }

  let timer = null
  const applyStatusFromProps = () => {
  if (timer) clearInterval(timer)
  if (details.value.meetingStatus === '未开始') {
  tickCountdown()
  timer = setInterval(tickCountdown, 1000)
  } else if (details.value.meetingStatus === '进行中') {
  // 初始化播放器
  nextTick(() => {
  initVideoPlayer()
  })
  }
  }

  // 初始化视频播放器
  const initVideoPlayer = async () => {
  if (isPlayerInitialized.value || !videoPlayer.value) return

  console.log('初始化视频播放器')

  // 销毁现有播放器
  destroyVideoPlayer()

  const video = videoPlayer.value
  player.value = video
  isPlayerInitialized.value = true

  // 重置状态
  errorMessage.value = ''
  statusMessage.value = '正在连接视频源...'
  qualityLevels.value = []
  selectedQuality.value = ''

  // HLS视频流地址 - 使用您提供的地址
  const hlsUrl =
  'http://prdpulllive.xylink.com/prdnemo/9681c99c9088f6520198def4ad835a39.m3u8?auth_key=0cd581927cc20104121e61dae8be17eb-1756182609-c60224498c3e4842a0034bb0575b00fb-'

  // 检查浏览器是否原生支持HLS
  if (video.canPlayType('application/vnd.apple.mpegurl')) {
  // 原生支持HLS
  video.src = hlsUrl
  statusMessage.value = '浏览器原生支持HLS，正在加载...'
  setupVideoEvents()
  } else if (Hls.isSupported()) {
  // 使用HLS.js库
  hls.value = new Hls({
  maxBufferLength: 30,
  maxMaxBufferLength: 60,
  startLevel: -1, // 自动选择适合的初始清晰度
  maxBufferHole: 0.5,
  highLatencyMode: false
  })

  // 加载视频流
  hls.value.loadSource(hlsUrl)
  hls.value.attachMedia(video)

  statusMessage.value = '使用HLS.js加载视频流...'

  // HLS事件监听
  hls.value.on(Hls.Events.MANIFEST_PARSED, function () {
  statusMessage.value = '视频流准备就绪，点击播放按钮开始'

  // 获取可用的清晰度级别
  qualityLevels.value = hls.value.levels || []
  })

  // 错误处理
  hls.value.on(Hls.Events.ERROR, function (_, data) {
  console.error('HLS错误:', data)
  let errorText = ''

  switch (data.type) {
  case Hls.ErrorTypes.NETWORK_ERROR:
  errorText = '网络错误，正在尝试重新连接...'
  hls.value.startLoad() // 尝试重新加载
  break
  case Hls.ErrorTypes.MEDIA_ERROR:
  errorText = '媒体播放错误'
  hls.value.recoverMediaError() // 尝试恢复媒体错误
  break
  default:
  errorText = `播放错误: ${data.details}`
  // 无法恢复的错误，尝试重新初始化
  setTimeout(initVideoPlayer, 3000)
  break
  }

  errorMessage.value = errorText
  })

  setupVideoEvents()
  } else {
  // 不支持HLS
  errorMessage.value = '您的浏览器不支持HLS视频流播放'
  statusMessage.value = 'HLS视频流播放器 | 不支持的浏览器'
  }
  }

  // 设置视频事件监听
  const setupVideoEvents = () => {
  const video = player.value
  if (!video) return

  // 视频可以播放时
  video.addEventListener('canplay', function () {
  statusMessage.value = '视频准备就绪，点击播放按钮开始'
  })

  // 播放事件
  video.addEventListener('play', function () {
  isPlaying.value = true
  statusMessage.value = '正在播放HLS视频流'
  })

  // 暂停事件
  video.addEventListener('pause', function () {
  isPlaying.value = false
  statusMessage.value = 'HLS视频流已暂停'
  })

  // 时间更新事件
  video.addEventListener('timeupdate', updateProgress)

  // 视频结束事件
  video.addEventListener('ended', function () {
  isPlaying.value = false
  statusMessage.value = '视频播放已结束'
  })

  // 音量变化事件
  video.addEventListener('volumechange', function () {
  volume.value = video.volume
  })
  }

  // 更新进度条和时间显示
  const updateProgress = () => {
  const video = player.value
  if (video && video.duration) {
  progressPercent.value = (video.currentTime / video.duration) * 100

  // 更新时间显示
  const currentTime = formatTime(video.currentTime)
  const duration = formatTime(video.duration)
  timeDisplay.value = `${currentTime} / ${duration}`
  }
  }

  // 格式化时间（秒 -> MM:SS）
  const formatTime = (seconds) => {
  if (isNaN(seconds)) return '00:00'
  const minutes = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  // 销毁视频播放器
  const destroyVideoPlayer = () => {
  // 销毁 hls.js 实例
  if (hls.value) {
  try {
  hls.value.destroy()
  } catch (error) {
  console.error('销毁HLS实例错误:', error)
  }
  hls.value = null
  }

  if (player.value) {
  try {
  player.value.pause()
  player.value.src = ''
  player.value.load()
  } catch (error) {
  console.error('销毁播放器错误:', error)
  }
  player.value = null
  isPlayerInitialized.value = false
  }
  }

  // 播放/暂停控制
  const togglePlayPause = () => {
  const video = player.value
  if (!video) return

  if (video.paused) {
  video.play().catch(error => {
  console.error('播放失败:', error)
  errorMessage.value = '播放失败，请检查视频源或浏览器设置'
  })
  } else {
  video.pause()
  }
  }

  // 音量控制
  const updateVolume = () => {
  const video = player.value
  if (video) {
  video.volume = volume.value
  }
  }

  // 进度条点击跳转
  const seekVideo = (event) => {
  const video = player.value
  if (!video || !video.duration) return

  const rect = event.currentTarget.getBoundingClientRect()
  const pos = (event.clientX - rect.left) / rect.width
  const seekTime = pos * video.duration
  video.currentTime = seekTime
  }

  // 全屏控制
  const toggleFullscreen = () => {
  const container = document.querySelector('.player-container')
  if (!container) return

  if (!document.fullscreenElement) {
  container.requestFullscreen().catch(err => {
  console.error('全屏请求失败:', err)
  errorMessage.value = '全屏请求失败: ' + err.message
  })
  } else {
  document.exitFullscreen()
  }
  }

  // 清晰度切换
  const changeQuality = () => {
  if (hls.value && selectedQuality.value !== '') {
  const level = parseInt(selectedQuality.value)
  hls.value.currentLevel = level
  const levelInfo = qualityLevels.value[level]
  if (levelInfo) {
  statusMessage.value = `已切换至 ${levelInfo.height}p 清晰度`
  }
  }
  }

  // 监听全屏状态变化
  const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement
  }

  // 在组件挂载时添加全屏监听
  onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  // 在 onMounted 中，如果组件已经显示且有 id，则加载数据
  if (props.modelValue && props.id) {
  getInfo()
  }
  })
  onBeforeUnmount(() => {
  if (timer) clearInterval(timer)
  destroyVideoPlayer()
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  })
  const handleClose = () => {
  emit('update:modelValue', false)
  emit('callback')
  }
  </script>
  <style lang="scss">
  .LiveBroadcastDetails {
    position: fixed;
    inset: 0;
    width: 100vw;
    height: 100vh;
    background: #0F0F0F;
    display: flex;
    flex-direction: column;
    z-index: 9999;

    .LiveBroadcastDetailsHeader {
      position: relative;
      height: 80px;
      display: flex;
      align-items: center;
      padding: 0 16px;
      color: #fff;
      background: #2B2B2B;
      border-bottom: 1px solid rgba(255, 255, 255, 0.08);

      .LiveBroadcastDetailsEmblem {
        width: 58px;
        height: 58px;
        margin-right: 14px;
      }

      .LiveBroadcastDetailsHeadInfo {
        display: flex;
        flex-direction: column;
      }

      .LiveBroadcastDetailsTitle {
        font-family: Microsoft YaHei, Microsoft YaHei;
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        margin-bottom: 12px;
      }

      .LiveBroadcastDetailsTime {
        font-weight: 400;
        font-size: 14px;
        color: #D9D9D9;
      }

      .LiveBroadcastDetailsClose {
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        color: #fff;
        cursor: pointer;
        font-size: 18px;
        opacity: .85;
      }
    }

    .LiveBroadcastDetailsBody {
      flex: 1;
      display: grid;
      grid-template-columns: 1fr 360px;
      gap: 16px;
      padding: 16px;
      overflow: hidden;
    }

    .LiveBroadcastDetailsCanvas {
      position: relative;
      height: 100%;
      background: #111;
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;

      .LiveBroadcastDetailsPoster {
        width: 100%;
        height: 100%;
        background: #000;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #d23a2e;
        font-weight: bold;
        font-size: 22px;
      }

      .LiveBroadcastDetailsCountdown {
        position: absolute;
        left: 0;
        right: 0;
        bottom: 0;
        height: 95px;
        background: rgba(0, 0, 0, 0.8);
        color: #fff;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);

        .countdown-line1 {
          font-size: 14px;
          font-weight: 400;
          margin-bottom: 8px;
          opacity: 0.9;
        }

        .countdown-line2 {
          font-size: 24px;
          font-weight: 600;
          letter-spacing: 2px;
        }
      }

      .LiveBroadcastDetailsLiveOverlay {
        position: absolute;
        left: 0;
        right: 0;
        top: 0;
        bottom: 0;
        width: 100%;
        height: 100%;

        .player-container {
          position: relative;
          width: 100%;
          height: 100%;
          background: #000;

          #video-player {
            width: 100%;
            height: 100%;
            object-fit: contain;
          }

          .controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(0, 0, 0, 0.8);
            padding: 12px 16px;
            display: flex;
            align-items: center;
            gap: 15px;
            color: white;
            font-size: 14px;

            .control-btn {
              background: rgba(255, 255, 255, 0.1);
              border: none;
              color: white;
              cursor: pointer;
              padding: 8px 12px;
              border-radius: 4px;
              font-size: 14px;
              transition: background-color 0.2s;

              &:hover {
                background: rgba(255, 255, 255, 0.2);
              }

              &.fullscreen-btn {
                margin-left: auto;
              }
            }

            .volume-control {
              display: flex;
              align-items: center;
              gap: 8px;

              .volume-icon {
                font-size: 16px;
                cursor: pointer;
              }

              .volume-slider {
                width: 80px;
                height: 4px;
                background: rgba(255, 255, 255, 0.3);
                border-radius: 2px;
                outline: none;
                cursor: pointer;

                &::-webkit-slider-thumb {
                  appearance: none;
                  width: 14px;
                  height: 14px;
                  background: #fff;
                  border-radius: 50%;
                  cursor: pointer;
                }

                &::-moz-range-thumb {
                  width: 14px;
                  height: 14px;
                  background: #fff;
                  border-radius: 50%;
                  cursor: pointer;
                  border: none;
                }
              }
            }

            .progress-container {
              flex: 1;
              height: 6px;
              background: rgba(255, 255, 255, 0.3);
              border-radius: 3px;
              cursor: pointer;
              position: relative;

              .progress-bar {
                height: 100%;
                background: #54BDFF;
                border-radius: 3px;
                transition: width 0.1s;
              }
            }

            .time-display {
              font-size: 13px;
              color: rgba(255, 255, 255, 0.9);
              min-width: 100px;
              text-align: center;
            }

            .quality-selector {
              background: rgba(255, 255, 255, 0.1);
              border: 1px solid rgba(255, 255, 255, 0.3);
              color: white;
              padding: 6px 10px;
              border-radius: 4px;
              font-size: 13px;
              cursor: pointer;

              &:disabled {
                opacity: 0.5;
                cursor: not-allowed;
              }

              option {
                background: #333;
                color: white;
              }
            }
          }
        }

        .status-panel {
          position: absolute;
          top: 16px;
          left: 16px;
          right: 16px;
          z-index: 10;

          .status-message {
            background: rgba(0, 0, 0, 0.7);
            color: #54BDFF;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            margin-bottom: 8px;
          }

          .error-message {
            background: rgba(220, 53, 69, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
          }
        }
      }

      .LiveBroadcastDetailsEnded {
        position: absolute;
        inset: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(0, 0, 0, .45);

        .LiveBroadcastDetailsEndedWrap {
          text-align: center;

          .endedTitle {
            color: #fff;
            font-size: 18px;
            margin-bottom: 12px;
          }

          .replayBtn {
            background: linear-gradient(90deg, #5bc0ff, #5f7cff);
            border: none;
          }
        }
      }
    }

    .LiveBroadcastDetailsSidebar {
      height: 100%;
      background: #191919;
      border-left: 1px solid rgba(255, 255, 255, 0.05);
      color: #e8e8e8;
      // padding: 14px 16px 16px 16px;
      overflow: auto;

      .LiveBroadcastDetailsTabs {
        display: flex;
        align-items: center;
        justify-content: space-around;
        gap: 40px;
        margin-bottom: 10px;
        background: #2B2B2B;
        padding: 14px 16px;

        .LiveBroadcastDetailsTab {
          cursor: pointer;
          color: #999999;
          position: relative;
          font-size: 16px;
          line-height: 1;
          transition: color .2s ease;
          font-weight: 500;

          &.active {
            color: #ffffff;
            font-weight: 700;
          }

          &.active::after {
            content: '';
            position: absolute;
            left: 0;
            right: 0;
            bottom: -8px;
            height: 3px;
            background: #54BDFF;
            border-radius: 3px;
          }
        }
      }

      .LiveBroadcastDetailsTabPane {
        padding: 12px 15px;
      }

      .LiveBroadcastDetailsPanelTitle {
        font-weight: bold;
        margin-bottom: 10px;
        font-size: 15px;
      }

      .LiveBroadcastDetailsPanelText {
        font-size: 13px;
        line-height: 1.8;
        color: #cfcfcf;
      }

      /* 详情tab内样式 */
      .detailsTitle {
        font-weight: 400;
        font-size: 18px;
        color: #FFFFFF;
        margin-bottom: 14px;
      }

      .detailsTime {
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        margin-bottom: 12px;
      }

      .detailsDesc {
        font-size: 13px;
        color: #d9d9d9;
        line-height: 1.8;
      }
    }
  }

  .details-fade-enter-active,
  .details-fade-leave-active {
    transition: opacity .2s ease;
  }

  .details-fade-enter-from,
  .details-fade-leave-to {
    opacity: 0;
  }
</style>