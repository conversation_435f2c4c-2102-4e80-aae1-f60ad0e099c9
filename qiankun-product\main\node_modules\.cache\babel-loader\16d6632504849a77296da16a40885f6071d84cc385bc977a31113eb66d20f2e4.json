{"ast": null, "code": "'use strict';\n\nexports.Any = require('./properties/Any/regex');\nexports.Cc = require('./categories/Cc/regex');\nexports.Cf = require('./categories/Cf/regex');\nexports.P = require('./categories/P/regex');\nexports.Z = require('./categories/Z/regex');", "map": {"version": 3, "names": ["exports", "Any", "require", "Cc", "Cf", "P", "Z"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/uc.micro@1.0.6/node_modules/uc.micro/index.js"], "sourcesContent": ["'use strict';\n\nexports.Any = require('./properties/Any/regex');\nexports.Cc  = require('./categories/Cc/regex');\nexports.Cf  = require('./categories/Cf/regex');\nexports.P   = require('./categories/P/regex');\nexports.Z   = require('./categories/Z/regex');\n"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,GAAG,GAAGC,OAAO,CAAC,wBAAwB,CAAC;AAC/CF,OAAO,CAACG,EAAE,GAAID,OAAO,CAAC,uBAAuB,CAAC;AAC9CF,OAAO,CAACI,EAAE,GAAIF,OAAO,CAAC,uBAAuB,CAAC;AAC9CF,OAAO,CAACK,CAAC,GAAKH,OAAO,CAAC,sBAAsB,CAAC;AAC7CF,OAAO,CAACM,CAAC,GAAKJ,OAAO,CAAC,sBAAsB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}