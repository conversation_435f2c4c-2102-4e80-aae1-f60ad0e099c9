{"ast": null, "code": "import { ref, onMounted } from 'vue';\nvar __default__ = {\n  name: 'Unauthorized'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    name: {\n      type: String,\n      default: ''\n    },\n    cancelCallback: {\n      type: Function\n    }\n  },\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var props = __props;\n    var visible = ref(false);\n    onMounted(function () {\n      visible.value = true;\n    });\n    var handleCancel = function handleCancel() {\n      visible.value = false;\n      setTimeout(function () {\n        props.cancelCallback();\n      }, 300);\n    };\n    var __returned__ = {\n      props,\n      visible,\n      handleCancel,\n      ref,\n      onMounted\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onMounted", "__default__", "name", "props", "__props", "visible", "value", "handleCancel", "setTimeout", "cancelCallback"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/components/unauthorized/unauthorized.vue"], "sourcesContent": ["<template>\r\n  <div class=\"unauthorized-wrapper\">\r\n    <transition name=\"unauthorized-fade\">\r\n      <div class=\"unauthorized\" v-show=\"visible\">\r\n        <div class=\"unauthorized-icon\"></div>\r\n        <div class=\"unauthorized-title\">抱歉，“{{ props.name }}”地区暂未获得软件正版授权</div>\r\n        <div class=\"unauthorized-text\">\r\n          唯一指定官方授权：\r\n          <div class=\"official-logo\"></div>\r\n        </div>\r\n        <div class=\"unauthorized-close\" @click=\"handleCancel\">\r\n          <svg viewBox=\"0 0 1024 1024\">\r\n            <path\r\n              fill=\"currentColor\"\r\n              d=\"M764.288 214.592 512 466.88 259.712 214.592a31.936 31.936 0 0 0-45.12 45.12L466.752 512 214.528 764.224a31.936 31.936 0 1 0 45.12 45.184L512 557.184l252.288 252.288a31.936 31.936 0 0 0 45.12-45.12L557.12 512.064l252.288-252.352a31.936 31.936 0 1 0-45.12-45.184z\"></path>\r\n          </svg>\r\n        </div>\r\n      </div>\r\n    </transition>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nexport default { name: 'Unauthorized' }\r\n</script>\r\n<script setup>\r\nimport { ref, onMounted } from 'vue'\r\nconst props = defineProps({\r\n  name: { type: String, default: '' },\r\n  cancelCallback: { type: Function }\r\n})\r\n\r\nconst visible = ref(false)\r\n\r\nonMounted(() => {\r\n  visible.value = true\r\n})\r\n\r\nconst handleCancel = () => {\r\n  visible.value = false\r\n  setTimeout(() => {\r\n    props.cancelCallback()\r\n  }, 300)\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.unauthorized-wrapper {\r\n  position: fixed;\r\n  top: 0;\r\n  bottom: 0;\r\n  right: 0;\r\n  left: 0;\r\n  backdrop-filter: blur(2px);\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n\r\n  .unauthorized {\r\n    min-width: 520px;\r\n    padding: var(--zy-distance-one);\r\n    vertical-align: middle;\r\n    background-color: #fff;\r\n    border-radius: var(--el-border-radius-base);\r\n    box-shadow: var(--zy-el-box-shadow);\r\n    backface-visibility: hidden;\r\n    position: relative;\r\n\r\n    .unauthorized-icon {\r\n      width: 120px;\r\n      height: 120px;\r\n      background: url('./img/unauthorized_icon.png') no-repeat;\r\n      background-size: 100% 100%;\r\n      margin: auto;\r\n    }\r\n\r\n    .unauthorized-title {\r\n      text-align: center;\r\n      font-weight: bold;\r\n      padding: var(--zy-distance-three) 0;\r\n      font-size: 18px;\r\n      line-height: var(--zy-line-height);\r\n    }\r\n\r\n    .unauthorized-text {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      font-size: 16px;\r\n      line-height: var(--zy-line-height);\r\n\r\n      .official-logo {\r\n        width: 103px;\r\n        height: 28px;\r\n        background: url('./img/official_logo.png') no-repeat;\r\n        background-size: 103px 25px;\r\n        background-position: center bottom;\r\n      }\r\n    }\r\n\r\n    .unauthorized-close {\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n      width: 52px;\r\n      height: 52px;\r\n      cursor: pointer;\r\n      text-align: center;\r\n      line-height: 52px;\r\n      font-size: var(--zy-navigation-font-size);\r\n\r\n      svg {\r\n        width: 1em;\r\n        height: 1em;\r\n      }\r\n    }\r\n  }\r\n\r\n  .unauthorized-fade-enter-active {\r\n    -webkit-animation: unauthorized-fade-in 0.3s;\r\n    animation: unauthorized-fade-in 0.3s;\r\n  }\r\n\r\n  .unauthorized-fade-leave-active {\r\n    -webkit-animation: unauthorized-fade-out 0.3s;\r\n    animation: unauthorized-fade-out 0.3s;\r\n  }\r\n\r\n  @keyframes unauthorized-fade-in {\r\n    0% {\r\n      transform: translate3d(0, -20px, 0);\r\n      opacity: 0;\r\n    }\r\n\r\n    100% {\r\n      transform: translate3d(0, 0, 0);\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  @keyframes unauthorized-fade-out {\r\n    0% {\r\n      transform: translate3d(0, 0, 0);\r\n      opacity: 1;\r\n    }\r\n\r\n    100% {\r\n      transform: translate3d(0, -20px, 0);\r\n      opacity: 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AA0BA,SAASA,GAAG,EAAEC,SAAS,QAAQ,KAAK;AAHpC,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAe,CAAC;;;;;;;;;;;;;;IAIvC,IAAMC,KAAK,GAAGC,OAGZ;IAEF,IAAMC,OAAO,GAAGN,GAAG,CAAC,KAAK,CAAC;IAE1BC,SAAS,CAAC,YAAM;MACdK,OAAO,CAACC,KAAK,GAAG,IAAI;IACtB,CAAC,CAAC;IAEF,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzBF,OAAO,CAACC,KAAK,GAAG,KAAK;MACrBE,UAAU,CAAC,YAAM;QACfL,KAAK,CAACM,cAAc,CAAC,CAAC;MACxB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}