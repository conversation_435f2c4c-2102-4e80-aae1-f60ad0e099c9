{"ast": null, "code": "\"use strict\";\n\nfunction getResolvedId(part, options) {\n  if (part.lIndex == null) {\n    return null;\n  }\n  var path = options.scopeManager.scopePathItem;\n  if (part.parentPart) {\n    path = path.slice(0, path.length - 1);\n  }\n  var res = options.filePath + \"@\" + part.lIndex.toString() + \"-\" + path.join(\"-\");\n  return res;\n}\nmodule.exports = getResolvedId;", "map": {"version": 3, "names": ["getResolvedId", "part", "options", "lIndex", "path", "scopeManager", "scopePathItem", "parentPart", "slice", "length", "res", "filePath", "toString", "join", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/docxtemplater@3.52.0/node_modules/docxtemplater/js/get-resolved-id.js"], "sourcesContent": ["\"use strict\";\n\nfunction getResolvedId(part, options) {\n  if (part.lIndex == null) {\n    return null;\n  }\n  var path = options.scopeManager.scopePathItem;\n  if (part.parentPart) {\n    path = path.slice(0, path.length - 1);\n  }\n  var res = options.filePath + \"@\" + part.lIndex.toString() + \"-\" + path.join(\"-\");\n  return res;\n}\nmodule.exports = getResolvedId;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,aAAaA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACpC,IAAID,IAAI,CAACE,MAAM,IAAI,IAAI,EAAE;IACvB,OAAO,IAAI;EACb;EACA,IAAIC,IAAI,GAAGF,OAAO,CAACG,YAAY,CAACC,aAAa;EAC7C,IAAIL,IAAI,CAACM,UAAU,EAAE;IACnBH,IAAI,GAAGA,IAAI,CAACI,KAAK,CAAC,CAAC,EAAEJ,IAAI,CAACK,MAAM,GAAG,CAAC,CAAC;EACvC;EACA,IAAIC,GAAG,GAAGR,OAAO,CAACS,QAAQ,GAAG,GAAG,GAAGV,IAAI,CAACE,MAAM,CAACS,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAGR,IAAI,CAACS,IAAI,CAAC,GAAG,CAAC;EAChF,OAAOH,GAAG;AACZ;AACAI,MAAM,CAACC,OAAO,GAAGf,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}