{"ast": null, "code": "function _isNativeFunction(t) {\n  try {\n    return -1 !== Function.toString.call(t).indexOf(\"[native code]\");\n  } catch (n) {\n    return \"function\" == typeof t;\n  }\n}\nexport { _isNativeFunction as default };", "map": {"version": 3, "names": ["_isNativeFunction", "t", "Function", "toString", "call", "indexOf", "n", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/@babel+runtime@7.26.0/node_modules/@babel/runtime/helpers/esm/isNativeFunction.js"], "sourcesContent": ["function _isNativeFunction(t) {\n  try {\n    return -1 !== Function.toString.call(t).indexOf(\"[native code]\");\n  } catch (n) {\n    return \"function\" == typeof t;\n  }\n}\nexport { _isNativeFunction as default };"], "mappings": "AAAA,SAASA,iBAAiBA,CAACC,CAAC,EAAE;EAC5B,IAAI;IACF,OAAO,CAAC,CAAC,KAAKC,QAAQ,CAACC,QAAQ,CAACC,IAAI,CAACH,CAAC,CAAC,CAACI,OAAO,CAAC,eAAe,CAAC;EAClE,CAAC,CAAC,OAAOC,CAAC,EAAE;IACV,OAAO,UAAU,IAAI,OAAOL,CAAC;EAC/B;AACF;AACA,SAASD,iBAAiB,IAAIO,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}