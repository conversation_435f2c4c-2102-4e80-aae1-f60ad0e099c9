{"ast": null, "code": "// Process footnotes\n//\n'use strict';\n\n////////////////////////////////////////////////////////////////////////////////\n// Renderer partials\nfunction render_footnote_anchor_name(tokens, idx, options, env /*, slf*/) {\n  var n = Number(tokens[idx].meta.id + 1).toString();\n  var prefix = '';\n  if (typeof env.docId === 'string') {\n    prefix = '-' + env.docId + '-';\n  }\n  return prefix + n;\n}\nfunction render_footnote_caption(tokens, idx /*, options, env, slf*/) {\n  var n = Number(tokens[idx].meta.id + 1).toString();\n  if (tokens[idx].meta.subId > 0) {\n    n += ':' + tokens[idx].meta.subId;\n  }\n  return '[' + n + ']';\n}\nfunction render_footnote_ref(tokens, idx, options, env, slf) {\n  var id = slf.rules.footnote_anchor_name(tokens, idx, options, env, slf);\n  var caption = slf.rules.footnote_caption(tokens, idx, options, env, slf);\n  var refid = id;\n  if (tokens[idx].meta.subId > 0) {\n    refid += ':' + tokens[idx].meta.subId;\n  }\n  return '<sup class=\"footnote-ref\"><a href=\"#fn' + id + '\" id=\"fnref' + refid + '\">' + caption + '</a></sup>';\n}\nfunction render_footnote_block_open(tokens, idx, options) {\n  return (options.xhtmlOut ? '<hr class=\"footnotes-sep\" />\\n' : '<hr class=\"footnotes-sep\">\\n') + '<section class=\"footnotes\">\\n' + '<ol class=\"footnotes-list\">\\n';\n}\nfunction render_footnote_block_close() {\n  return '</ol>\\n</section>\\n';\n}\nfunction render_footnote_open(tokens, idx, options, env, slf) {\n  var id = slf.rules.footnote_anchor_name(tokens, idx, options, env, slf);\n  if (tokens[idx].meta.subId > 0) {\n    id += ':' + tokens[idx].meta.subId;\n  }\n  return '<li id=\"fn' + id + '\" class=\"footnote-item\">';\n}\nfunction render_footnote_close() {\n  return '</li>\\n';\n}\nfunction render_footnote_anchor(tokens, idx, options, env, slf) {\n  var id = slf.rules.footnote_anchor_name(tokens, idx, options, env, slf);\n  if (tokens[idx].meta.subId > 0) {\n    id += ':' + tokens[idx].meta.subId;\n  }\n\n  /* ↩ with escape code to prevent display as Apple Emoji on iOS */\n  return ' <a href=\"#fnref' + id + \"\\\" class=\\\"footnote-backref\\\">\\u21A9\\uFE0E</a>\";\n}\nmodule.exports = function footnote_plugin(md) {\n  var parseLinkLabel = md.helpers.parseLinkLabel,\n    isSpace = md.utils.isSpace;\n  md.renderer.rules.footnote_ref = render_footnote_ref;\n  md.renderer.rules.footnote_block_open = render_footnote_block_open;\n  md.renderer.rules.footnote_block_close = render_footnote_block_close;\n  md.renderer.rules.footnote_open = render_footnote_open;\n  md.renderer.rules.footnote_close = render_footnote_close;\n  md.renderer.rules.footnote_anchor = render_footnote_anchor;\n\n  // helpers (only used in other rules, no tokens are attached to those)\n  md.renderer.rules.footnote_caption = render_footnote_caption;\n  md.renderer.rules.footnote_anchor_name = render_footnote_anchor_name;\n\n  // Process footnote block definition\n  function footnote_def(state, startLine, endLine, silent) {\n    var oldBMark,\n      oldTShift,\n      oldSCount,\n      oldParentType,\n      pos,\n      label,\n      token,\n      initial,\n      offset,\n      ch,\n      posAfterColon,\n      start = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine];\n\n    // line should be at least 5 chars - \"[^x]:\"\n    if (start + 4 > max) {\n      return false;\n    }\n    if (state.src.charCodeAt(start) !== 0x5B /* [ */) {\n      return false;\n    }\n    if (state.src.charCodeAt(start + 1) !== 0x5E /* ^ */) {\n      return false;\n    }\n    for (pos = start + 2; pos < max; pos++) {\n      if (state.src.charCodeAt(pos) === 0x20) {\n        return false;\n      }\n      if (state.src.charCodeAt(pos) === 0x5D /* ] */) {\n        break;\n      }\n    }\n    if (pos === start + 2) {\n      return false;\n    } // no empty footnote labels\n    if (pos + 1 >= max || state.src.charCodeAt(++pos) !== 0x3A /* : */) {\n      return false;\n    }\n    if (silent) {\n      return true;\n    }\n    pos++;\n    if (!state.env.footnotes) {\n      state.env.footnotes = {};\n    }\n    if (!state.env.footnotes.refs) {\n      state.env.footnotes.refs = {};\n    }\n    label = state.src.slice(start + 2, pos - 2);\n    state.env.footnotes.refs[':' + label] = -1;\n    token = new state.Token('footnote_reference_open', '', 1);\n    token.meta = {\n      label: label\n    };\n    token.level = state.level++;\n    state.tokens.push(token);\n    oldBMark = state.bMarks[startLine];\n    oldTShift = state.tShift[startLine];\n    oldSCount = state.sCount[startLine];\n    oldParentType = state.parentType;\n    posAfterColon = pos;\n    initial = offset = state.sCount[startLine] + pos - (state.bMarks[startLine] + state.tShift[startLine]);\n    while (pos < max) {\n      ch = state.src.charCodeAt(pos);\n      if (isSpace(ch)) {\n        if (ch === 0x09) {\n          offset += 4 - offset % 4;\n        } else {\n          offset++;\n        }\n      } else {\n        break;\n      }\n      pos++;\n    }\n    state.tShift[startLine] = pos - posAfterColon;\n    state.sCount[startLine] = offset - initial;\n    state.bMarks[startLine] = posAfterColon;\n    state.blkIndent += 4;\n    state.parentType = 'footnote';\n    if (state.sCount[startLine] < state.blkIndent) {\n      state.sCount[startLine] += state.blkIndent;\n    }\n    state.md.block.tokenize(state, startLine, endLine, true);\n    state.parentType = oldParentType;\n    state.blkIndent -= 4;\n    state.tShift[startLine] = oldTShift;\n    state.sCount[startLine] = oldSCount;\n    state.bMarks[startLine] = oldBMark;\n    token = new state.Token('footnote_reference_close', '', -1);\n    token.level = --state.level;\n    state.tokens.push(token);\n    return true;\n  }\n\n  // Process inline footnotes (^[...])\n  function footnote_inline(state, silent) {\n    var labelStart,\n      labelEnd,\n      footnoteId,\n      token,\n      tokens,\n      max = state.posMax,\n      start = state.pos;\n    if (start + 2 >= max) {\n      return false;\n    }\n    if (state.src.charCodeAt(start) !== 0x5E /* ^ */) {\n      return false;\n    }\n    if (state.src.charCodeAt(start + 1) !== 0x5B /* [ */) {\n      return false;\n    }\n    labelStart = start + 2;\n    labelEnd = parseLinkLabel(state, start + 1);\n\n    // parser failed to find ']', so it's not a valid note\n    if (labelEnd < 0) {\n      return false;\n    }\n\n    // We found the end of the link, and know for a fact it's a valid link;\n    // so all that's left to do is to call tokenizer.\n    //\n    if (!silent) {\n      if (!state.env.footnotes) {\n        state.env.footnotes = {};\n      }\n      if (!state.env.footnotes.list) {\n        state.env.footnotes.list = [];\n      }\n      footnoteId = state.env.footnotes.list.length;\n      state.md.inline.parse(state.src.slice(labelStart, labelEnd), state.md, state.env, tokens = []);\n      token = state.push('footnote_ref', '', 0);\n      token.meta = {\n        id: footnoteId\n      };\n      state.env.footnotes.list[footnoteId] = {\n        content: state.src.slice(labelStart, labelEnd),\n        tokens: tokens\n      };\n    }\n    state.pos = labelEnd + 1;\n    state.posMax = max;\n    return true;\n  }\n\n  // Process footnote references ([^...])\n  function footnote_ref(state, silent) {\n    var label,\n      pos,\n      footnoteId,\n      footnoteSubId,\n      token,\n      max = state.posMax,\n      start = state.pos;\n\n    // should be at least 4 chars - \"[^x]\"\n    if (start + 3 > max) {\n      return false;\n    }\n    if (!state.env.footnotes || !state.env.footnotes.refs) {\n      return false;\n    }\n    if (state.src.charCodeAt(start) !== 0x5B /* [ */) {\n      return false;\n    }\n    if (state.src.charCodeAt(start + 1) !== 0x5E /* ^ */) {\n      return false;\n    }\n    for (pos = start + 2; pos < max; pos++) {\n      if (state.src.charCodeAt(pos) === 0x20) {\n        return false;\n      }\n      if (state.src.charCodeAt(pos) === 0x0A) {\n        return false;\n      }\n      if (state.src.charCodeAt(pos) === 0x5D /* ] */) {\n        break;\n      }\n    }\n    if (pos === start + 2) {\n      return false;\n    } // no empty footnote labels\n    if (pos >= max) {\n      return false;\n    }\n    pos++;\n    label = state.src.slice(start + 2, pos - 1);\n    if (typeof state.env.footnotes.refs[':' + label] === 'undefined') {\n      return false;\n    }\n    if (!silent) {\n      if (!state.env.footnotes.list) {\n        state.env.footnotes.list = [];\n      }\n      if (state.env.footnotes.refs[':' + label] < 0) {\n        footnoteId = state.env.footnotes.list.length;\n        state.env.footnotes.list[footnoteId] = {\n          label: label,\n          count: 0\n        };\n        state.env.footnotes.refs[':' + label] = footnoteId;\n      } else {\n        footnoteId = state.env.footnotes.refs[':' + label];\n      }\n      footnoteSubId = state.env.footnotes.list[footnoteId].count;\n      state.env.footnotes.list[footnoteId].count++;\n      token = state.push('footnote_ref', '', 0);\n      token.meta = {\n        id: footnoteId,\n        subId: footnoteSubId,\n        label: label\n      };\n    }\n    state.pos = pos;\n    state.posMax = max;\n    return true;\n  }\n\n  // Glue footnote tokens to end of token stream\n  function footnote_tail(state) {\n    var i,\n      l,\n      j,\n      t,\n      lastParagraph,\n      list,\n      token,\n      tokens,\n      current,\n      currentLabel,\n      insideRef = false,\n      refTokens = {};\n    if (!state.env.footnotes) {\n      return;\n    }\n    state.tokens = state.tokens.filter(function (tok) {\n      if (tok.type === 'footnote_reference_open') {\n        insideRef = true;\n        current = [];\n        currentLabel = tok.meta.label;\n        return false;\n      }\n      if (tok.type === 'footnote_reference_close') {\n        insideRef = false;\n        // prepend ':' to avoid conflict with Object.prototype members\n        refTokens[':' + currentLabel] = current;\n        return false;\n      }\n      if (insideRef) {\n        current.push(tok);\n      }\n      return !insideRef;\n    });\n    if (!state.env.footnotes.list) {\n      return;\n    }\n    list = state.env.footnotes.list;\n    token = new state.Token('footnote_block_open', '', 1);\n    state.tokens.push(token);\n    for (i = 0, l = list.length; i < l; i++) {\n      token = new state.Token('footnote_open', '', 1);\n      token.meta = {\n        id: i,\n        label: list[i].label\n      };\n      state.tokens.push(token);\n      if (list[i].tokens) {\n        tokens = [];\n        token = new state.Token('paragraph_open', 'p', 1);\n        token.block = true;\n        tokens.push(token);\n        token = new state.Token('inline', '', 0);\n        token.children = list[i].tokens;\n        token.content = list[i].content;\n        tokens.push(token);\n        token = new state.Token('paragraph_close', 'p', -1);\n        token.block = true;\n        tokens.push(token);\n      } else if (list[i].label) {\n        tokens = refTokens[':' + list[i].label];\n      }\n      if (tokens) state.tokens = state.tokens.concat(tokens);\n      if (state.tokens[state.tokens.length - 1].type === 'paragraph_close') {\n        lastParagraph = state.tokens.pop();\n      } else {\n        lastParagraph = null;\n      }\n      t = list[i].count > 0 ? list[i].count : 1;\n      for (j = 0; j < t; j++) {\n        token = new state.Token('footnote_anchor', '', 0);\n        token.meta = {\n          id: i,\n          subId: j,\n          label: list[i].label\n        };\n        state.tokens.push(token);\n      }\n      if (lastParagraph) {\n        state.tokens.push(lastParagraph);\n      }\n      token = new state.Token('footnote_close', '', -1);\n      state.tokens.push(token);\n    }\n    token = new state.Token('footnote_block_close', '', -1);\n    state.tokens.push(token);\n  }\n  md.block.ruler.before('reference', 'footnote_def', footnote_def, {\n    alt: ['paragraph', 'reference']\n  });\n  md.inline.ruler.after('image', 'footnote_inline', footnote_inline);\n  md.inline.ruler.after('footnote_inline', 'footnote_ref', footnote_ref);\n  md.core.ruler.after('inline', 'footnote_tail', footnote_tail);\n};", "map": {"version": 3, "names": ["render_footnote_anchor_name", "tokens", "idx", "options", "env", "n", "Number", "meta", "id", "toString", "prefix", "docId", "render_footnote_caption", "subId", "render_footnote_ref", "slf", "rules", "footnote_anchor_name", "caption", "footnote_caption", "refid", "render_footnote_block_open", "xhtmlOut", "render_footnote_block_close", "render_footnote_open", "render_footnote_close", "render_footnote_anchor", "module", "exports", "footnote_plugin", "md", "parseLinkLabel", "helpers", "isSpace", "utils", "renderer", "footnote_ref", "footnote_block_open", "footnote_block_close", "footnote_open", "footnote_close", "footnote_anchor", "footnote_def", "state", "startLine", "endLine", "silent", "oldBMark", "oldTShift", "oldSCount", "oldParentType", "pos", "label", "token", "initial", "offset", "ch", "posAfterColon", "start", "bMarks", "tShift", "max", "eMarks", "src", "charCodeAt", "footnotes", "refs", "slice", "Token", "level", "push", "sCount", "parentType", "blkIndent", "block", "tokenize", "footnote_inline", "labelStart", "labelEnd", "footnoteId", "posMax", "list", "length", "inline", "parse", "content", "footnoteSubId", "count", "footnote_tail", "i", "l", "j", "t", "lastParagraph", "current", "current<PERSON><PERSON><PERSON>", "insideRef", "refTokens", "filter", "tok", "type", "children", "concat", "pop", "ruler", "before", "alt", "after", "core"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it-footnote@3.0.3/node_modules/markdown-it-footnote/index.js"], "sourcesContent": ["// Process footnotes\n//\n'use strict';\n\n////////////////////////////////////////////////////////////////////////////////\n// Renderer partials\n\nfunction render_footnote_anchor_name(tokens, idx, options, env/*, slf*/) {\n  var n = Number(tokens[idx].meta.id + 1).toString();\n  var prefix = '';\n\n  if (typeof env.docId === 'string') {\n    prefix = '-' + env.docId + '-';\n  }\n\n  return prefix + n;\n}\n\nfunction render_footnote_caption(tokens, idx/*, options, env, slf*/) {\n  var n = Number(tokens[idx].meta.id + 1).toString();\n\n  if (tokens[idx].meta.subId > 0) {\n    n += ':' + tokens[idx].meta.subId;\n  }\n\n  return '[' + n + ']';\n}\n\nfunction render_footnote_ref(tokens, idx, options, env, slf) {\n  var id      = slf.rules.footnote_anchor_name(tokens, idx, options, env, slf);\n  var caption = slf.rules.footnote_caption(tokens, idx, options, env, slf);\n  var refid   = id;\n\n  if (tokens[idx].meta.subId > 0) {\n    refid += ':' + tokens[idx].meta.subId;\n  }\n\n  return '<sup class=\"footnote-ref\"><a href=\"#fn' + id + '\" id=\"fnref' + refid + '\">' + caption + '</a></sup>';\n}\n\nfunction render_footnote_block_open(tokens, idx, options) {\n  return (options.xhtmlOut ? '<hr class=\"footnotes-sep\" />\\n' : '<hr class=\"footnotes-sep\">\\n') +\n         '<section class=\"footnotes\">\\n' +\n         '<ol class=\"footnotes-list\">\\n';\n}\n\nfunction render_footnote_block_close() {\n  return '</ol>\\n</section>\\n';\n}\n\nfunction render_footnote_open(tokens, idx, options, env, slf) {\n  var id = slf.rules.footnote_anchor_name(tokens, idx, options, env, slf);\n\n  if (tokens[idx].meta.subId > 0) {\n    id += ':' + tokens[idx].meta.subId;\n  }\n\n  return '<li id=\"fn' + id + '\" class=\"footnote-item\">';\n}\n\nfunction render_footnote_close() {\n  return '</li>\\n';\n}\n\nfunction render_footnote_anchor(tokens, idx, options, env, slf) {\n  var id = slf.rules.footnote_anchor_name(tokens, idx, options, env, slf);\n\n  if (tokens[idx].meta.subId > 0) {\n    id += ':' + tokens[idx].meta.subId;\n  }\n\n  /* ↩ with escape code to prevent display as Apple Emoji on iOS */\n  return ' <a href=\"#fnref' + id + '\" class=\"footnote-backref\">\\u21a9\\uFE0E</a>';\n}\n\n\nmodule.exports = function footnote_plugin(md) {\n  var parseLinkLabel = md.helpers.parseLinkLabel,\n      isSpace = md.utils.isSpace;\n\n  md.renderer.rules.footnote_ref          = render_footnote_ref;\n  md.renderer.rules.footnote_block_open   = render_footnote_block_open;\n  md.renderer.rules.footnote_block_close  = render_footnote_block_close;\n  md.renderer.rules.footnote_open         = render_footnote_open;\n  md.renderer.rules.footnote_close        = render_footnote_close;\n  md.renderer.rules.footnote_anchor       = render_footnote_anchor;\n\n  // helpers (only used in other rules, no tokens are attached to those)\n  md.renderer.rules.footnote_caption      = render_footnote_caption;\n  md.renderer.rules.footnote_anchor_name  = render_footnote_anchor_name;\n\n  // Process footnote block definition\n  function footnote_def(state, startLine, endLine, silent) {\n    var oldBMark, oldTShift, oldSCount, oldParentType, pos, label, token,\n        initial, offset, ch, posAfterColon,\n        start = state.bMarks[startLine] + state.tShift[startLine],\n        max = state.eMarks[startLine];\n\n    // line should be at least 5 chars - \"[^x]:\"\n    if (start + 4 > max) { return false; }\n\n    if (state.src.charCodeAt(start) !== 0x5B/* [ */) { return false; }\n    if (state.src.charCodeAt(start + 1) !== 0x5E/* ^ */) { return false; }\n\n    for (pos = start + 2; pos < max; pos++) {\n      if (state.src.charCodeAt(pos) === 0x20) { return false; }\n      if (state.src.charCodeAt(pos) === 0x5D /* ] */) {\n        break;\n      }\n    }\n\n    if (pos === start + 2) { return false; } // no empty footnote labels\n    if (pos + 1 >= max || state.src.charCodeAt(++pos) !== 0x3A /* : */) { return false; }\n    if (silent) { return true; }\n    pos++;\n\n    if (!state.env.footnotes) { state.env.footnotes = {}; }\n    if (!state.env.footnotes.refs) { state.env.footnotes.refs = {}; }\n    label = state.src.slice(start + 2, pos - 2);\n    state.env.footnotes.refs[':' + label] = -1;\n\n    token       = new state.Token('footnote_reference_open', '', 1);\n    token.meta  = { label: label };\n    token.level = state.level++;\n    state.tokens.push(token);\n\n    oldBMark = state.bMarks[startLine];\n    oldTShift = state.tShift[startLine];\n    oldSCount = state.sCount[startLine];\n    oldParentType = state.parentType;\n\n    posAfterColon = pos;\n    initial = offset = state.sCount[startLine] + pos - (state.bMarks[startLine] + state.tShift[startLine]);\n\n    while (pos < max) {\n      ch = state.src.charCodeAt(pos);\n\n      if (isSpace(ch)) {\n        if (ch === 0x09) {\n          offset += 4 - offset % 4;\n        } else {\n          offset++;\n        }\n      } else {\n        break;\n      }\n\n      pos++;\n    }\n\n    state.tShift[startLine] = pos - posAfterColon;\n    state.sCount[startLine] = offset - initial;\n\n    state.bMarks[startLine] = posAfterColon;\n    state.blkIndent += 4;\n    state.parentType = 'footnote';\n\n    if (state.sCount[startLine] < state.blkIndent) {\n      state.sCount[startLine] += state.blkIndent;\n    }\n\n    state.md.block.tokenize(state, startLine, endLine, true);\n\n    state.parentType = oldParentType;\n    state.blkIndent -= 4;\n    state.tShift[startLine] = oldTShift;\n    state.sCount[startLine] = oldSCount;\n    state.bMarks[startLine] = oldBMark;\n\n    token       = new state.Token('footnote_reference_close', '', -1);\n    token.level = --state.level;\n    state.tokens.push(token);\n\n    return true;\n  }\n\n  // Process inline footnotes (^[...])\n  function footnote_inline(state, silent) {\n    var labelStart,\n        labelEnd,\n        footnoteId,\n        token,\n        tokens,\n        max = state.posMax,\n        start = state.pos;\n\n    if (start + 2 >= max) { return false; }\n    if (state.src.charCodeAt(start) !== 0x5E/* ^ */) { return false; }\n    if (state.src.charCodeAt(start + 1) !== 0x5B/* [ */) { return false; }\n\n    labelStart = start + 2;\n    labelEnd = parseLinkLabel(state, start + 1);\n\n    // parser failed to find ']', so it's not a valid note\n    if (labelEnd < 0) { return false; }\n\n    // We found the end of the link, and know for a fact it's a valid link;\n    // so all that's left to do is to call tokenizer.\n    //\n    if (!silent) {\n      if (!state.env.footnotes) { state.env.footnotes = {}; }\n      if (!state.env.footnotes.list) { state.env.footnotes.list = []; }\n      footnoteId = state.env.footnotes.list.length;\n\n      state.md.inline.parse(\n        state.src.slice(labelStart, labelEnd),\n        state.md,\n        state.env,\n        tokens = []\n      );\n\n      token      = state.push('footnote_ref', '', 0);\n      token.meta = { id: footnoteId };\n\n      state.env.footnotes.list[footnoteId] = {\n        content: state.src.slice(labelStart, labelEnd),\n        tokens: tokens\n      };\n    }\n\n    state.pos = labelEnd + 1;\n    state.posMax = max;\n    return true;\n  }\n\n  // Process footnote references ([^...])\n  function footnote_ref(state, silent) {\n    var label,\n        pos,\n        footnoteId,\n        footnoteSubId,\n        token,\n        max = state.posMax,\n        start = state.pos;\n\n    // should be at least 4 chars - \"[^x]\"\n    if (start + 3 > max) { return false; }\n\n    if (!state.env.footnotes || !state.env.footnotes.refs) { return false; }\n    if (state.src.charCodeAt(start) !== 0x5B/* [ */) { return false; }\n    if (state.src.charCodeAt(start + 1) !== 0x5E/* ^ */) { return false; }\n\n    for (pos = start + 2; pos < max; pos++) {\n      if (state.src.charCodeAt(pos) === 0x20) { return false; }\n      if (state.src.charCodeAt(pos) === 0x0A) { return false; }\n      if (state.src.charCodeAt(pos) === 0x5D /* ] */) {\n        break;\n      }\n    }\n\n    if (pos === start + 2) { return false; } // no empty footnote labels\n    if (pos >= max) { return false; }\n    pos++;\n\n    label = state.src.slice(start + 2, pos - 1);\n    if (typeof state.env.footnotes.refs[':' + label] === 'undefined') { return false; }\n\n    if (!silent) {\n      if (!state.env.footnotes.list) { state.env.footnotes.list = []; }\n\n      if (state.env.footnotes.refs[':' + label] < 0) {\n        footnoteId = state.env.footnotes.list.length;\n        state.env.footnotes.list[footnoteId] = { label: label, count: 0 };\n        state.env.footnotes.refs[':' + label] = footnoteId;\n      } else {\n        footnoteId = state.env.footnotes.refs[':' + label];\n      }\n\n      footnoteSubId = state.env.footnotes.list[footnoteId].count;\n      state.env.footnotes.list[footnoteId].count++;\n\n      token      = state.push('footnote_ref', '', 0);\n      token.meta = { id: footnoteId, subId: footnoteSubId, label: label };\n    }\n\n    state.pos = pos;\n    state.posMax = max;\n    return true;\n  }\n\n  // Glue footnote tokens to end of token stream\n  function footnote_tail(state) {\n    var i, l, j, t, lastParagraph, list, token, tokens, current, currentLabel,\n        insideRef = false,\n        refTokens = {};\n\n    if (!state.env.footnotes) { return; }\n\n    state.tokens = state.tokens.filter(function (tok) {\n      if (tok.type === 'footnote_reference_open') {\n        insideRef = true;\n        current = [];\n        currentLabel = tok.meta.label;\n        return false;\n      }\n      if (tok.type === 'footnote_reference_close') {\n        insideRef = false;\n        // prepend ':' to avoid conflict with Object.prototype members\n        refTokens[':' + currentLabel] = current;\n        return false;\n      }\n      if (insideRef) { current.push(tok); }\n      return !insideRef;\n    });\n\n    if (!state.env.footnotes.list) { return; }\n    list = state.env.footnotes.list;\n\n    token = new state.Token('footnote_block_open', '', 1);\n    state.tokens.push(token);\n\n    for (i = 0, l = list.length; i < l; i++) {\n      token      = new state.Token('footnote_open', '', 1);\n      token.meta = { id: i, label: list[i].label };\n      state.tokens.push(token);\n\n      if (list[i].tokens) {\n        tokens = [];\n\n        token          = new state.Token('paragraph_open', 'p', 1);\n        token.block    = true;\n        tokens.push(token);\n\n        token          = new state.Token('inline', '', 0);\n        token.children = list[i].tokens;\n        token.content  = list[i].content;\n        tokens.push(token);\n\n        token          = new state.Token('paragraph_close', 'p', -1);\n        token.block    = true;\n        tokens.push(token);\n\n      } else if (list[i].label) {\n        tokens = refTokens[':' + list[i].label];\n      }\n\n      if (tokens) state.tokens = state.tokens.concat(tokens);\n      if (state.tokens[state.tokens.length - 1].type === 'paragraph_close') {\n        lastParagraph = state.tokens.pop();\n      } else {\n        lastParagraph = null;\n      }\n\n      t = list[i].count > 0 ? list[i].count : 1;\n      for (j = 0; j < t; j++) {\n        token      = new state.Token('footnote_anchor', '', 0);\n        token.meta = { id: i, subId: j, label: list[i].label };\n        state.tokens.push(token);\n      }\n\n      if (lastParagraph) {\n        state.tokens.push(lastParagraph);\n      }\n\n      token = new state.Token('footnote_close', '', -1);\n      state.tokens.push(token);\n    }\n\n    token = new state.Token('footnote_block_close', '', -1);\n    state.tokens.push(token);\n  }\n\n  md.block.ruler.before('reference', 'footnote_def', footnote_def, { alt: [ 'paragraph', 'reference' ] });\n  md.inline.ruler.after('image', 'footnote_inline', footnote_inline);\n  md.inline.ruler.after('footnote_inline', 'footnote_ref', footnote_ref);\n  md.core.ruler.after('inline', 'footnote_tail', footnote_tail);\n};\n"], "mappings": "AAAA;AACA;AACA,YAAY;;AAEZ;AACA;AAEA,SAASA,2BAA2BA,CAACC,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,GAAG,YAAW;EACvE,IAAIC,CAAC,GAAGC,MAAM,CAACL,MAAM,CAACC,GAAG,CAAC,CAACK,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;EAClD,IAAIC,MAAM,GAAG,EAAE;EAEf,IAAI,OAAON,GAAG,CAACO,KAAK,KAAK,QAAQ,EAAE;IACjCD,MAAM,GAAG,GAAG,GAAGN,GAAG,CAACO,KAAK,GAAG,GAAG;EAChC;EAEA,OAAOD,MAAM,GAAGL,CAAC;AACnB;AAEA,SAASO,uBAAuBA,CAACX,MAAM,EAAEC,GAAG,0BAAyB;EACnE,IAAIG,CAAC,GAAGC,MAAM,CAACL,MAAM,CAACC,GAAG,CAAC,CAACK,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;EAElD,IAAIR,MAAM,CAACC,GAAG,CAAC,CAACK,IAAI,CAACM,KAAK,GAAG,CAAC,EAAE;IAC9BR,CAAC,IAAI,GAAG,GAAGJ,MAAM,CAACC,GAAG,CAAC,CAACK,IAAI,CAACM,KAAK;EACnC;EAEA,OAAO,GAAG,GAAGR,CAAC,GAAG,GAAG;AACtB;AAEA,SAASS,mBAAmBA,CAACb,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,GAAG,EAAEW,GAAG,EAAE;EAC3D,IAAIP,EAAE,GAAQO,GAAG,CAACC,KAAK,CAACC,oBAAoB,CAAChB,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,GAAG,EAAEW,GAAG,CAAC;EAC5E,IAAIG,OAAO,GAAGH,GAAG,CAACC,KAAK,CAACG,gBAAgB,CAAClB,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,GAAG,EAAEW,GAAG,CAAC;EACxE,IAAIK,KAAK,GAAKZ,EAAE;EAEhB,IAAIP,MAAM,CAACC,GAAG,CAAC,CAACK,IAAI,CAACM,KAAK,GAAG,CAAC,EAAE;IAC9BO,KAAK,IAAI,GAAG,GAAGnB,MAAM,CAACC,GAAG,CAAC,CAACK,IAAI,CAACM,KAAK;EACvC;EAEA,OAAO,wCAAwC,GAAGL,EAAE,GAAG,aAAa,GAAGY,KAAK,GAAG,IAAI,GAAGF,OAAO,GAAG,YAAY;AAC9G;AAEA,SAASG,0BAA0BA,CAACpB,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAE;EACxD,OAAO,CAACA,OAAO,CAACmB,QAAQ,GAAG,gCAAgC,GAAG,8BAA8B,IACrF,+BAA+B,GAC/B,+BAA+B;AACxC;AAEA,SAASC,2BAA2BA,CAAA,EAAG;EACrC,OAAO,qBAAqB;AAC9B;AAEA,SAASC,oBAAoBA,CAACvB,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,GAAG,EAAEW,GAAG,EAAE;EAC5D,IAAIP,EAAE,GAAGO,GAAG,CAACC,KAAK,CAACC,oBAAoB,CAAChB,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,GAAG,EAAEW,GAAG,CAAC;EAEvE,IAAId,MAAM,CAACC,GAAG,CAAC,CAACK,IAAI,CAACM,KAAK,GAAG,CAAC,EAAE;IAC9BL,EAAE,IAAI,GAAG,GAAGP,MAAM,CAACC,GAAG,CAAC,CAACK,IAAI,CAACM,KAAK;EACpC;EAEA,OAAO,YAAY,GAAGL,EAAE,GAAG,0BAA0B;AACvD;AAEA,SAASiB,qBAAqBA,CAAA,EAAG;EAC/B,OAAO,SAAS;AAClB;AAEA,SAASC,sBAAsBA,CAACzB,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,GAAG,EAAEW,GAAG,EAAE;EAC9D,IAAIP,EAAE,GAAGO,GAAG,CAACC,KAAK,CAACC,oBAAoB,CAAChB,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,GAAG,EAAEW,GAAG,CAAC;EAEvE,IAAId,MAAM,CAACC,GAAG,CAAC,CAACK,IAAI,CAACM,KAAK,GAAG,CAAC,EAAE;IAC9BL,EAAE,IAAI,GAAG,GAAGP,MAAM,CAACC,GAAG,CAAC,CAACK,IAAI,CAACM,KAAK;EACpC;;EAEA;EACA,OAAO,kBAAkB,GAAGL,EAAE,GAAG,gDAA6C;AAChF;AAGAmB,MAAM,CAACC,OAAO,GAAG,SAASC,eAAeA,CAACC,EAAE,EAAE;EAC5C,IAAIC,cAAc,GAAGD,EAAE,CAACE,OAAO,CAACD,cAAc;IAC1CE,OAAO,GAAGH,EAAE,CAACI,KAAK,CAACD,OAAO;EAE9BH,EAAE,CAACK,QAAQ,CAACnB,KAAK,CAACoB,YAAY,GAAYtB,mBAAmB;EAC7DgB,EAAE,CAACK,QAAQ,CAACnB,KAAK,CAACqB,mBAAmB,GAAKhB,0BAA0B;EACpES,EAAE,CAACK,QAAQ,CAACnB,KAAK,CAACsB,oBAAoB,GAAIf,2BAA2B;EACrEO,EAAE,CAACK,QAAQ,CAACnB,KAAK,CAACuB,aAAa,GAAWf,oBAAoB;EAC9DM,EAAE,CAACK,QAAQ,CAACnB,KAAK,CAACwB,cAAc,GAAUf,qBAAqB;EAC/DK,EAAE,CAACK,QAAQ,CAACnB,KAAK,CAACyB,eAAe,GAASf,sBAAsB;;EAEhE;EACAI,EAAE,CAACK,QAAQ,CAACnB,KAAK,CAACG,gBAAgB,GAAQP,uBAAuB;EACjEkB,EAAE,CAACK,QAAQ,CAACnB,KAAK,CAACC,oBAAoB,GAAIjB,2BAA2B;;EAErE;EACA,SAAS0C,YAAYA,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAE;IACvD,IAAIC,QAAQ;MAAEC,SAAS;MAAEC,SAAS;MAAEC,aAAa;MAAEC,GAAG;MAAEC,KAAK;MAAEC,KAAK;MAChEC,OAAO;MAAEC,MAAM;MAAEC,EAAE;MAAEC,aAAa;MAClCC,KAAK,GAAGf,KAAK,CAACgB,MAAM,CAACf,SAAS,CAAC,GAAGD,KAAK,CAACiB,MAAM,CAAChB,SAAS,CAAC;MACzDiB,GAAG,GAAGlB,KAAK,CAACmB,MAAM,CAAClB,SAAS,CAAC;;IAEjC;IACA,IAAIc,KAAK,GAAG,CAAC,GAAGG,GAAG,EAAE;MAAE,OAAO,KAAK;IAAE;IAErC,IAAIlB,KAAK,CAACoB,GAAG,CAACC,UAAU,CAACN,KAAK,CAAC,KAAK,IAAI,UAAS;MAAE,OAAO,KAAK;IAAE;IACjE,IAAIf,KAAK,CAACoB,GAAG,CAACC,UAAU,CAACN,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,UAAS;MAAE,OAAO,KAAK;IAAE;IAErE,KAAKP,GAAG,GAAGO,KAAK,GAAG,CAAC,EAAEP,GAAG,GAAGU,GAAG,EAAEV,GAAG,EAAE,EAAE;MACtC,IAAIR,KAAK,CAACoB,GAAG,CAACC,UAAU,CAACb,GAAG,CAAC,KAAK,IAAI,EAAE;QAAE,OAAO,KAAK;MAAE;MACxD,IAAIR,KAAK,CAACoB,GAAG,CAACC,UAAU,CAACb,GAAG,CAAC,KAAK,IAAI,CAAC,SAAS;QAC9C;MACF;IACF;IAEA,IAAIA,GAAG,KAAKO,KAAK,GAAG,CAAC,EAAE;MAAE,OAAO,KAAK;IAAE,CAAC,CAAC;IACzC,IAAIP,GAAG,GAAG,CAAC,IAAIU,GAAG,IAAIlB,KAAK,CAACoB,GAAG,CAACC,UAAU,CAAC,EAAEb,GAAG,CAAC,KAAK,IAAI,CAAC,SAAS;MAAE,OAAO,KAAK;IAAE;IACpF,IAAIL,MAAM,EAAE;MAAE,OAAO,IAAI;IAAE;IAC3BK,GAAG,EAAE;IAEL,IAAI,CAACR,KAAK,CAACvC,GAAG,CAAC6D,SAAS,EAAE;MAAEtB,KAAK,CAACvC,GAAG,CAAC6D,SAAS,GAAG,CAAC,CAAC;IAAE;IACtD,IAAI,CAACtB,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACC,IAAI,EAAE;MAAEvB,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACC,IAAI,GAAG,CAAC,CAAC;IAAE;IAChEd,KAAK,GAAGT,KAAK,CAACoB,GAAG,CAACI,KAAK,CAACT,KAAK,GAAG,CAAC,EAAEP,GAAG,GAAG,CAAC,CAAC;IAC3CR,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACC,IAAI,CAAC,GAAG,GAAGd,KAAK,CAAC,GAAG,CAAC,CAAC;IAE1CC,KAAK,GAAS,IAAIV,KAAK,CAACyB,KAAK,CAAC,yBAAyB,EAAE,EAAE,EAAE,CAAC,CAAC;IAC/Df,KAAK,CAAC9C,IAAI,GAAI;MAAE6C,KAAK,EAAEA;IAAM,CAAC;IAC9BC,KAAK,CAACgB,KAAK,GAAG1B,KAAK,CAAC0B,KAAK,EAAE;IAC3B1B,KAAK,CAAC1C,MAAM,CAACqE,IAAI,CAACjB,KAAK,CAAC;IAExBN,QAAQ,GAAGJ,KAAK,CAACgB,MAAM,CAACf,SAAS,CAAC;IAClCI,SAAS,GAAGL,KAAK,CAACiB,MAAM,CAAChB,SAAS,CAAC;IACnCK,SAAS,GAAGN,KAAK,CAAC4B,MAAM,CAAC3B,SAAS,CAAC;IACnCM,aAAa,GAAGP,KAAK,CAAC6B,UAAU;IAEhCf,aAAa,GAAGN,GAAG;IACnBG,OAAO,GAAGC,MAAM,GAAGZ,KAAK,CAAC4B,MAAM,CAAC3B,SAAS,CAAC,GAAGO,GAAG,IAAIR,KAAK,CAACgB,MAAM,CAACf,SAAS,CAAC,GAAGD,KAAK,CAACiB,MAAM,CAAChB,SAAS,CAAC,CAAC;IAEtG,OAAOO,GAAG,GAAGU,GAAG,EAAE;MAChBL,EAAE,GAAGb,KAAK,CAACoB,GAAG,CAACC,UAAU,CAACb,GAAG,CAAC;MAE9B,IAAIlB,OAAO,CAACuB,EAAE,CAAC,EAAE;QACf,IAAIA,EAAE,KAAK,IAAI,EAAE;UACfD,MAAM,IAAI,CAAC,GAAGA,MAAM,GAAG,CAAC;QAC1B,CAAC,MAAM;UACLA,MAAM,EAAE;QACV;MACF,CAAC,MAAM;QACL;MACF;MAEAJ,GAAG,EAAE;IACP;IAEAR,KAAK,CAACiB,MAAM,CAAChB,SAAS,CAAC,GAAGO,GAAG,GAAGM,aAAa;IAC7Cd,KAAK,CAAC4B,MAAM,CAAC3B,SAAS,CAAC,GAAGW,MAAM,GAAGD,OAAO;IAE1CX,KAAK,CAACgB,MAAM,CAACf,SAAS,CAAC,GAAGa,aAAa;IACvCd,KAAK,CAAC8B,SAAS,IAAI,CAAC;IACpB9B,KAAK,CAAC6B,UAAU,GAAG,UAAU;IAE7B,IAAI7B,KAAK,CAAC4B,MAAM,CAAC3B,SAAS,CAAC,GAAGD,KAAK,CAAC8B,SAAS,EAAE;MAC7C9B,KAAK,CAAC4B,MAAM,CAAC3B,SAAS,CAAC,IAAID,KAAK,CAAC8B,SAAS;IAC5C;IAEA9B,KAAK,CAACb,EAAE,CAAC4C,KAAK,CAACC,QAAQ,CAAChC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAE,IAAI,CAAC;IAExDF,KAAK,CAAC6B,UAAU,GAAGtB,aAAa;IAChCP,KAAK,CAAC8B,SAAS,IAAI,CAAC;IACpB9B,KAAK,CAACiB,MAAM,CAAChB,SAAS,CAAC,GAAGI,SAAS;IACnCL,KAAK,CAAC4B,MAAM,CAAC3B,SAAS,CAAC,GAAGK,SAAS;IACnCN,KAAK,CAACgB,MAAM,CAACf,SAAS,CAAC,GAAGG,QAAQ;IAElCM,KAAK,GAAS,IAAIV,KAAK,CAACyB,KAAK,CAAC,0BAA0B,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IACjEf,KAAK,CAACgB,KAAK,GAAG,EAAE1B,KAAK,CAAC0B,KAAK;IAC3B1B,KAAK,CAAC1C,MAAM,CAACqE,IAAI,CAACjB,KAAK,CAAC;IAExB,OAAO,IAAI;EACb;;EAEA;EACA,SAASuB,eAAeA,CAACjC,KAAK,EAAEG,MAAM,EAAE;IACtC,IAAI+B,UAAU;MACVC,QAAQ;MACRC,UAAU;MACV1B,KAAK;MACLpD,MAAM;MACN4D,GAAG,GAAGlB,KAAK,CAACqC,MAAM;MAClBtB,KAAK,GAAGf,KAAK,CAACQ,GAAG;IAErB,IAAIO,KAAK,GAAG,CAAC,IAAIG,GAAG,EAAE;MAAE,OAAO,KAAK;IAAE;IACtC,IAAIlB,KAAK,CAACoB,GAAG,CAACC,UAAU,CAACN,KAAK,CAAC,KAAK,IAAI,UAAS;MAAE,OAAO,KAAK;IAAE;IACjE,IAAIf,KAAK,CAACoB,GAAG,CAACC,UAAU,CAACN,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,UAAS;MAAE,OAAO,KAAK;IAAE;IAErEmB,UAAU,GAAGnB,KAAK,GAAG,CAAC;IACtBoB,QAAQ,GAAG/C,cAAc,CAACY,KAAK,EAAEe,KAAK,GAAG,CAAC,CAAC;;IAE3C;IACA,IAAIoB,QAAQ,GAAG,CAAC,EAAE;MAAE,OAAO,KAAK;IAAE;;IAElC;IACA;IACA;IACA,IAAI,CAAChC,MAAM,EAAE;MACX,IAAI,CAACH,KAAK,CAACvC,GAAG,CAAC6D,SAAS,EAAE;QAAEtB,KAAK,CAACvC,GAAG,CAAC6D,SAAS,GAAG,CAAC,CAAC;MAAE;MACtD,IAAI,CAACtB,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACgB,IAAI,EAAE;QAAEtC,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACgB,IAAI,GAAG,EAAE;MAAE;MAChEF,UAAU,GAAGpC,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACgB,IAAI,CAACC,MAAM;MAE5CvC,KAAK,CAACb,EAAE,CAACqD,MAAM,CAACC,KAAK,CACnBzC,KAAK,CAACoB,GAAG,CAACI,KAAK,CAACU,UAAU,EAAEC,QAAQ,CAAC,EACrCnC,KAAK,CAACb,EAAE,EACRa,KAAK,CAACvC,GAAG,EACTH,MAAM,GAAG,EACX,CAAC;MAEDoD,KAAK,GAAQV,KAAK,CAAC2B,IAAI,CAAC,cAAc,EAAE,EAAE,EAAE,CAAC,CAAC;MAC9CjB,KAAK,CAAC9C,IAAI,GAAG;QAAEC,EAAE,EAAEuE;MAAW,CAAC;MAE/BpC,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACgB,IAAI,CAACF,UAAU,CAAC,GAAG;QACrCM,OAAO,EAAE1C,KAAK,CAACoB,GAAG,CAACI,KAAK,CAACU,UAAU,EAAEC,QAAQ,CAAC;QAC9C7E,MAAM,EAAEA;MACV,CAAC;IACH;IAEA0C,KAAK,CAACQ,GAAG,GAAG2B,QAAQ,GAAG,CAAC;IACxBnC,KAAK,CAACqC,MAAM,GAAGnB,GAAG;IAClB,OAAO,IAAI;EACb;;EAEA;EACA,SAASzB,YAAYA,CAACO,KAAK,EAAEG,MAAM,EAAE;IACnC,IAAIM,KAAK;MACLD,GAAG;MACH4B,UAAU;MACVO,aAAa;MACbjC,KAAK;MACLQ,GAAG,GAAGlB,KAAK,CAACqC,MAAM;MAClBtB,KAAK,GAAGf,KAAK,CAACQ,GAAG;;IAErB;IACA,IAAIO,KAAK,GAAG,CAAC,GAAGG,GAAG,EAAE;MAAE,OAAO,KAAK;IAAE;IAErC,IAAI,CAAClB,KAAK,CAACvC,GAAG,CAAC6D,SAAS,IAAI,CAACtB,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACC,IAAI,EAAE;MAAE,OAAO,KAAK;IAAE;IACvE,IAAIvB,KAAK,CAACoB,GAAG,CAACC,UAAU,CAACN,KAAK,CAAC,KAAK,IAAI,UAAS;MAAE,OAAO,KAAK;IAAE;IACjE,IAAIf,KAAK,CAACoB,GAAG,CAACC,UAAU,CAACN,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,UAAS;MAAE,OAAO,KAAK;IAAE;IAErE,KAAKP,GAAG,GAAGO,KAAK,GAAG,CAAC,EAAEP,GAAG,GAAGU,GAAG,EAAEV,GAAG,EAAE,EAAE;MACtC,IAAIR,KAAK,CAACoB,GAAG,CAACC,UAAU,CAACb,GAAG,CAAC,KAAK,IAAI,EAAE;QAAE,OAAO,KAAK;MAAE;MACxD,IAAIR,KAAK,CAACoB,GAAG,CAACC,UAAU,CAACb,GAAG,CAAC,KAAK,IAAI,EAAE;QAAE,OAAO,KAAK;MAAE;MACxD,IAAIR,KAAK,CAACoB,GAAG,CAACC,UAAU,CAACb,GAAG,CAAC,KAAK,IAAI,CAAC,SAAS;QAC9C;MACF;IACF;IAEA,IAAIA,GAAG,KAAKO,KAAK,GAAG,CAAC,EAAE;MAAE,OAAO,KAAK;IAAE,CAAC,CAAC;IACzC,IAAIP,GAAG,IAAIU,GAAG,EAAE;MAAE,OAAO,KAAK;IAAE;IAChCV,GAAG,EAAE;IAELC,KAAK,GAAGT,KAAK,CAACoB,GAAG,CAACI,KAAK,CAACT,KAAK,GAAG,CAAC,EAAEP,GAAG,GAAG,CAAC,CAAC;IAC3C,IAAI,OAAOR,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACC,IAAI,CAAC,GAAG,GAAGd,KAAK,CAAC,KAAK,WAAW,EAAE;MAAE,OAAO,KAAK;IAAE;IAElF,IAAI,CAACN,MAAM,EAAE;MACX,IAAI,CAACH,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACgB,IAAI,EAAE;QAAEtC,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACgB,IAAI,GAAG,EAAE;MAAE;MAEhE,IAAItC,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACC,IAAI,CAAC,GAAG,GAAGd,KAAK,CAAC,GAAG,CAAC,EAAE;QAC7C2B,UAAU,GAAGpC,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACgB,IAAI,CAACC,MAAM;QAC5CvC,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACgB,IAAI,CAACF,UAAU,CAAC,GAAG;UAAE3B,KAAK,EAAEA,KAAK;UAAEmC,KAAK,EAAE;QAAE,CAAC;QACjE5C,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACC,IAAI,CAAC,GAAG,GAAGd,KAAK,CAAC,GAAG2B,UAAU;MACpD,CAAC,MAAM;QACLA,UAAU,GAAGpC,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACC,IAAI,CAAC,GAAG,GAAGd,KAAK,CAAC;MACpD;MAEAkC,aAAa,GAAG3C,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACgB,IAAI,CAACF,UAAU,CAAC,CAACQ,KAAK;MAC1D5C,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACgB,IAAI,CAACF,UAAU,CAAC,CAACQ,KAAK,EAAE;MAE5ClC,KAAK,GAAQV,KAAK,CAAC2B,IAAI,CAAC,cAAc,EAAE,EAAE,EAAE,CAAC,CAAC;MAC9CjB,KAAK,CAAC9C,IAAI,GAAG;QAAEC,EAAE,EAAEuE,UAAU;QAAElE,KAAK,EAAEyE,aAAa;QAAElC,KAAK,EAAEA;MAAM,CAAC;IACrE;IAEAT,KAAK,CAACQ,GAAG,GAAGA,GAAG;IACfR,KAAK,CAACqC,MAAM,GAAGnB,GAAG;IAClB,OAAO,IAAI;EACb;;EAEA;EACA,SAAS2B,aAAaA,CAAC7C,KAAK,EAAE;IAC5B,IAAI8C,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,aAAa;MAAEZ,IAAI;MAAE5B,KAAK;MAAEpD,MAAM;MAAE6F,OAAO;MAAEC,YAAY;MACrEC,SAAS,GAAG,KAAK;MACjBC,SAAS,GAAG,CAAC,CAAC;IAElB,IAAI,CAACtD,KAAK,CAACvC,GAAG,CAAC6D,SAAS,EAAE;MAAE;IAAQ;IAEpCtB,KAAK,CAAC1C,MAAM,GAAG0C,KAAK,CAAC1C,MAAM,CAACiG,MAAM,CAAC,UAAUC,GAAG,EAAE;MAChD,IAAIA,GAAG,CAACC,IAAI,KAAK,yBAAyB,EAAE;QAC1CJ,SAAS,GAAG,IAAI;QAChBF,OAAO,GAAG,EAAE;QACZC,YAAY,GAAGI,GAAG,CAAC5F,IAAI,CAAC6C,KAAK;QAC7B,OAAO,KAAK;MACd;MACA,IAAI+C,GAAG,CAACC,IAAI,KAAK,0BAA0B,EAAE;QAC3CJ,SAAS,GAAG,KAAK;QACjB;QACAC,SAAS,CAAC,GAAG,GAAGF,YAAY,CAAC,GAAGD,OAAO;QACvC,OAAO,KAAK;MACd;MACA,IAAIE,SAAS,EAAE;QAAEF,OAAO,CAACxB,IAAI,CAAC6B,GAAG,CAAC;MAAE;MACpC,OAAO,CAACH,SAAS;IACnB,CAAC,CAAC;IAEF,IAAI,CAACrD,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACgB,IAAI,EAAE;MAAE;IAAQ;IACzCA,IAAI,GAAGtC,KAAK,CAACvC,GAAG,CAAC6D,SAAS,CAACgB,IAAI;IAE/B5B,KAAK,GAAG,IAAIV,KAAK,CAACyB,KAAK,CAAC,qBAAqB,EAAE,EAAE,EAAE,CAAC,CAAC;IACrDzB,KAAK,CAAC1C,MAAM,CAACqE,IAAI,CAACjB,KAAK,CAAC;IAExB,KAAKoC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGT,IAAI,CAACC,MAAM,EAAEO,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MACvCpC,KAAK,GAAQ,IAAIV,KAAK,CAACyB,KAAK,CAAC,eAAe,EAAE,EAAE,EAAE,CAAC,CAAC;MACpDf,KAAK,CAAC9C,IAAI,GAAG;QAAEC,EAAE,EAAEiF,CAAC;QAAErC,KAAK,EAAE6B,IAAI,CAACQ,CAAC,CAAC,CAACrC;MAAM,CAAC;MAC5CT,KAAK,CAAC1C,MAAM,CAACqE,IAAI,CAACjB,KAAK,CAAC;MAExB,IAAI4B,IAAI,CAACQ,CAAC,CAAC,CAACxF,MAAM,EAAE;QAClBA,MAAM,GAAG,EAAE;QAEXoD,KAAK,GAAY,IAAIV,KAAK,CAACyB,KAAK,CAAC,gBAAgB,EAAE,GAAG,EAAE,CAAC,CAAC;QAC1Df,KAAK,CAACqB,KAAK,GAAM,IAAI;QACrBzE,MAAM,CAACqE,IAAI,CAACjB,KAAK,CAAC;QAElBA,KAAK,GAAY,IAAIV,KAAK,CAACyB,KAAK,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;QACjDf,KAAK,CAACgD,QAAQ,GAAGpB,IAAI,CAACQ,CAAC,CAAC,CAACxF,MAAM;QAC/BoD,KAAK,CAACgC,OAAO,GAAIJ,IAAI,CAACQ,CAAC,CAAC,CAACJ,OAAO;QAChCpF,MAAM,CAACqE,IAAI,CAACjB,KAAK,CAAC;QAElBA,KAAK,GAAY,IAAIV,KAAK,CAACyB,KAAK,CAAC,iBAAiB,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAC5Df,KAAK,CAACqB,KAAK,GAAM,IAAI;QACrBzE,MAAM,CAACqE,IAAI,CAACjB,KAAK,CAAC;MAEpB,CAAC,MAAM,IAAI4B,IAAI,CAACQ,CAAC,CAAC,CAACrC,KAAK,EAAE;QACxBnD,MAAM,GAAGgG,SAAS,CAAC,GAAG,GAAGhB,IAAI,CAACQ,CAAC,CAAC,CAACrC,KAAK,CAAC;MACzC;MAEA,IAAInD,MAAM,EAAE0C,KAAK,CAAC1C,MAAM,GAAG0C,KAAK,CAAC1C,MAAM,CAACqG,MAAM,CAACrG,MAAM,CAAC;MACtD,IAAI0C,KAAK,CAAC1C,MAAM,CAAC0C,KAAK,CAAC1C,MAAM,CAACiF,MAAM,GAAG,CAAC,CAAC,CAACkB,IAAI,KAAK,iBAAiB,EAAE;QACpEP,aAAa,GAAGlD,KAAK,CAAC1C,MAAM,CAACsG,GAAG,CAAC,CAAC;MACpC,CAAC,MAAM;QACLV,aAAa,GAAG,IAAI;MACtB;MAEAD,CAAC,GAAGX,IAAI,CAACQ,CAAC,CAAC,CAACF,KAAK,GAAG,CAAC,GAAGN,IAAI,CAACQ,CAAC,CAAC,CAACF,KAAK,GAAG,CAAC;MACzC,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;QACtBtC,KAAK,GAAQ,IAAIV,KAAK,CAACyB,KAAK,CAAC,iBAAiB,EAAE,EAAE,EAAE,CAAC,CAAC;QACtDf,KAAK,CAAC9C,IAAI,GAAG;UAAEC,EAAE,EAAEiF,CAAC;UAAE5E,KAAK,EAAE8E,CAAC;UAAEvC,KAAK,EAAE6B,IAAI,CAACQ,CAAC,CAAC,CAACrC;QAAM,CAAC;QACtDT,KAAK,CAAC1C,MAAM,CAACqE,IAAI,CAACjB,KAAK,CAAC;MAC1B;MAEA,IAAIwC,aAAa,EAAE;QACjBlD,KAAK,CAAC1C,MAAM,CAACqE,IAAI,CAACuB,aAAa,CAAC;MAClC;MAEAxC,KAAK,GAAG,IAAIV,KAAK,CAACyB,KAAK,CAAC,gBAAgB,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;MACjDzB,KAAK,CAAC1C,MAAM,CAACqE,IAAI,CAACjB,KAAK,CAAC;IAC1B;IAEAA,KAAK,GAAG,IAAIV,KAAK,CAACyB,KAAK,CAAC,sBAAsB,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;IACvDzB,KAAK,CAAC1C,MAAM,CAACqE,IAAI,CAACjB,KAAK,CAAC;EAC1B;EAEAvB,EAAE,CAAC4C,KAAK,CAAC8B,KAAK,CAACC,MAAM,CAAC,WAAW,EAAE,cAAc,EAAE/D,YAAY,EAAE;IAAEgE,GAAG,EAAE,CAAE,WAAW,EAAE,WAAW;EAAG,CAAC,CAAC;EACvG5E,EAAE,CAACqD,MAAM,CAACqB,KAAK,CAACG,KAAK,CAAC,OAAO,EAAE,iBAAiB,EAAE/B,eAAe,CAAC;EAClE9C,EAAE,CAACqD,MAAM,CAACqB,KAAK,CAACG,KAAK,CAAC,iBAAiB,EAAE,cAAc,EAAEvE,YAAY,CAAC;EACtEN,EAAE,CAAC8E,IAAI,CAACJ,KAAK,CAACG,KAAK,CAAC,QAAQ,EAAE,eAAe,EAAEnB,aAAa,CAAC;AAC/D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}