{"ast": null, "code": "var toInteger = require('./toInteger');\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * Creates a function that invokes `func`, with the `this` binding and arguments\n * of the created function, while it's called less than `n` times. Subsequent\n * calls to the created function return the result of the last `func` invocation.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Function\n * @param {number} n The number of calls at which `func` is no longer invoked.\n * @param {Function} func The function to restrict.\n * @returns {Function} Returns the new restricted function.\n * @example\n *\n * jQuery(element).on('click', _.before(5, addContactToList));\n * // => Allows adding up to 4 contacts to the list.\n */\nfunction before(n, func) {\n  var result;\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  n = toInteger(n);\n  return function () {\n    if (--n > 0) {\n      result = func.apply(this, arguments);\n    }\n    if (n <= 1) {\n      func = undefined;\n    }\n    return result;\n  };\n}\nmodule.exports = before;", "map": {"version": 3, "names": ["toInteger", "require", "FUNC_ERROR_TEXT", "before", "n", "func", "result", "TypeError", "apply", "arguments", "undefined", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/before.js"], "sourcesContent": ["var toInteger = require('./toInteger');\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * Creates a function that invokes `func`, with the `this` binding and arguments\n * of the created function, while it's called less than `n` times. Subsequent\n * calls to the created function return the result of the last `func` invocation.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category Function\n * @param {number} n The number of calls at which `func` is no longer invoked.\n * @param {Function} func The function to restrict.\n * @returns {Function} Returns the new restricted function.\n * @example\n *\n * jQuery(element).on('click', _.before(5, addContactToList));\n * // => Allows adding up to 4 contacts to the list.\n */\nfunction before(n, func) {\n  var result;\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  n = toInteger(n);\n  return function() {\n    if (--n > 0) {\n      result = func.apply(this, arguments);\n    }\n    if (n <= 1) {\n      func = undefined;\n    }\n    return result;\n  };\n}\n\nmodule.exports = before;\n"], "mappings": "AAAA,IAAIA,SAAS,GAAGC,OAAO,CAAC,aAAa,CAAC;;AAEtC;AACA,IAAIC,eAAe,GAAG,qBAAqB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,CAAC,EAAEC,IAAI,EAAE;EACvB,IAAIC,MAAM;EACV,IAAI,OAAOD,IAAI,IAAI,UAAU,EAAE;IAC7B,MAAM,IAAIE,SAAS,CAACL,eAAe,CAAC;EACtC;EACAE,CAAC,GAAGJ,SAAS,CAACI,CAAC,CAAC;EAChB,OAAO,YAAW;IAChB,IAAI,EAAEA,CAAC,GAAG,CAAC,EAAE;MACXE,MAAM,GAAGD,IAAI,CAACG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;IACtC;IACA,IAAIL,CAAC,IAAI,CAAC,EAAE;MACVC,IAAI,GAAGK,SAAS;IAClB;IACA,OAAOJ,MAAM;EACf,CAAC;AACH;AAEAK,MAAM,CAACC,OAAO,GAAGT,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}