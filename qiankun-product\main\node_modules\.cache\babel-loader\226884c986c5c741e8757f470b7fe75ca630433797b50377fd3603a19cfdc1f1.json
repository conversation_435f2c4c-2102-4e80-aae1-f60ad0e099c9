{"ast": null, "code": "/* global attachEvent */\n\n/**\n * Module requirements.\n */\n\nvar XMLHttpRequest = require('./xmlhttprequest');\nvar Polling = require('./polling');\nvar Emitter = require('component-emitter');\nvar inherit = require('component-inherit');\nvar debug = require('debug')('engine.io-client:polling-xhr');\nvar globalThis = require('../globalThis');\n\n/**\n * Module exports.\n */\n\nmodule.exports = XHR;\nmodule.exports.Request = Request;\n\n/**\n * Empty function\n */\n\nfunction empty() {}\n\n/**\n * XHR Polling constructor.\n *\n * @param {Object} opts\n * @api public\n */\n\nfunction XHR(opts) {\n  Polling.call(this, opts);\n  this.requestTimeout = opts.requestTimeout;\n  this.extraHeaders = opts.extraHeaders;\n  if (typeof location !== 'undefined') {\n    var isSSL = 'https:' === location.protocol;\n    var port = location.port;\n\n    // some user agents have empty `location.port`\n    if (!port) {\n      port = isSSL ? 443 : 80;\n    }\n    this.xd = typeof location !== 'undefined' && opts.hostname !== location.hostname || port !== opts.port;\n    this.xs = opts.secure !== isSSL;\n  }\n}\n\n/**\n * Inherits from Polling.\n */\n\ninherit(XHR, Polling);\n\n/**\n * XHR supports binary\n */\n\nXHR.prototype.supportsBinary = true;\n\n/**\n * Creates a request.\n *\n * @param {String} method\n * @api private\n */\n\nXHR.prototype.request = function (opts) {\n  opts = opts || {};\n  opts.uri = this.uri();\n  opts.xd = this.xd;\n  opts.xs = this.xs;\n  opts.agent = this.agent || false;\n  opts.supportsBinary = this.supportsBinary;\n  opts.enablesXDR = this.enablesXDR;\n  opts.withCredentials = this.withCredentials;\n\n  // SSL options for Node.js client\n  opts.pfx = this.pfx;\n  opts.key = this.key;\n  opts.passphrase = this.passphrase;\n  opts.cert = this.cert;\n  opts.ca = this.ca;\n  opts.ciphers = this.ciphers;\n  opts.rejectUnauthorized = this.rejectUnauthorized;\n  opts.requestTimeout = this.requestTimeout;\n\n  // other options for Node.js client\n  opts.extraHeaders = this.extraHeaders;\n  return new Request(opts);\n};\n\n/**\n * Sends data.\n *\n * @param {String} data to send.\n * @param {Function} called upon flush.\n * @api private\n */\n\nXHR.prototype.doWrite = function (data, fn) {\n  var isBinary = typeof data !== 'string' && data !== undefined;\n  var req = this.request({\n    method: 'POST',\n    data: data,\n    isBinary: isBinary\n  });\n  var self = this;\n  req.on('success', fn);\n  req.on('error', function (err) {\n    self.onError('xhr post error', err);\n  });\n  this.sendXhr = req;\n};\n\n/**\n * Starts a poll cycle.\n *\n * @api private\n */\n\nXHR.prototype.doPoll = function () {\n  debug('xhr poll');\n  var req = this.request();\n  var self = this;\n  req.on('data', function (data) {\n    self.onData(data);\n  });\n  req.on('error', function (err) {\n    self.onError('xhr poll error', err);\n  });\n  this.pollXhr = req;\n};\n\n/**\n * Request constructor\n *\n * @param {Object} options\n * @api public\n */\n\nfunction Request(opts) {\n  this.method = opts.method || 'GET';\n  this.uri = opts.uri;\n  this.xd = !!opts.xd;\n  this.xs = !!opts.xs;\n  this.async = false !== opts.async;\n  this.data = undefined !== opts.data ? opts.data : null;\n  this.agent = opts.agent;\n  this.isBinary = opts.isBinary;\n  this.supportsBinary = opts.supportsBinary;\n  this.enablesXDR = opts.enablesXDR;\n  this.withCredentials = opts.withCredentials;\n  this.requestTimeout = opts.requestTimeout;\n\n  // SSL options for Node.js client\n  this.pfx = opts.pfx;\n  this.key = opts.key;\n  this.passphrase = opts.passphrase;\n  this.cert = opts.cert;\n  this.ca = opts.ca;\n  this.ciphers = opts.ciphers;\n  this.rejectUnauthorized = opts.rejectUnauthorized;\n\n  // other options for Node.js client\n  this.extraHeaders = opts.extraHeaders;\n  this.create();\n}\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Request.prototype);\n\n/**\n * Creates the XHR object and sends the request.\n *\n * @api private\n */\n\nRequest.prototype.create = function () {\n  var opts = {\n    agent: this.agent,\n    xdomain: this.xd,\n    xscheme: this.xs,\n    enablesXDR: this.enablesXDR\n  };\n\n  // SSL options for Node.js client\n  opts.pfx = this.pfx;\n  opts.key = this.key;\n  opts.passphrase = this.passphrase;\n  opts.cert = this.cert;\n  opts.ca = this.ca;\n  opts.ciphers = this.ciphers;\n  opts.rejectUnauthorized = this.rejectUnauthorized;\n  var xhr = this.xhr = new XMLHttpRequest(opts);\n  var self = this;\n  try {\n    debug('xhr open %s: %s', this.method, this.uri);\n    xhr.open(this.method, this.uri, this.async);\n    try {\n      if (this.extraHeaders) {\n        xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n        for (var i in this.extraHeaders) {\n          if (this.extraHeaders.hasOwnProperty(i)) {\n            xhr.setRequestHeader(i, this.extraHeaders[i]);\n          }\n        }\n      }\n    } catch (e) {}\n    if ('POST' === this.method) {\n      try {\n        if (this.isBinary) {\n          xhr.setRequestHeader('Content-type', 'application/octet-stream');\n        } else {\n          xhr.setRequestHeader('Content-type', 'text/plain;charset=UTF-8');\n        }\n      } catch (e) {}\n    }\n    try {\n      xhr.setRequestHeader('Accept', '*/*');\n    } catch (e) {}\n\n    // ie6 check\n    if ('withCredentials' in xhr) {\n      xhr.withCredentials = this.withCredentials;\n    }\n    if (this.requestTimeout) {\n      xhr.timeout = this.requestTimeout;\n    }\n    if (this.hasXDR()) {\n      xhr.onload = function () {\n        self.onLoad();\n      };\n      xhr.onerror = function () {\n        self.onError(xhr.responseText);\n      };\n    } else {\n      xhr.onreadystatechange = function () {\n        if (xhr.readyState === 2) {\n          try {\n            var contentType = xhr.getResponseHeader('Content-Type');\n            if (self.supportsBinary && contentType === 'application/octet-stream' || contentType === 'application/octet-stream; charset=UTF-8') {\n              xhr.responseType = 'arraybuffer';\n            }\n          } catch (e) {}\n        }\n        if (4 !== xhr.readyState) return;\n        if (200 === xhr.status || 1223 === xhr.status) {\n          self.onLoad();\n        } else {\n          // make sure the `error` event handler that's user-set\n          // does not throw in the same tick and gets caught here\n          setTimeout(function () {\n            self.onError(typeof xhr.status === 'number' ? xhr.status : 0);\n          }, 0);\n        }\n      };\n    }\n    debug('xhr data %s', this.data);\n    xhr.send(this.data);\n  } catch (e) {\n    // Need to defer since .create() is called directly fhrom the constructor\n    // and thus the 'error' event can only be only bound *after* this exception\n    // occurs.  Therefore, also, we cannot throw here at all.\n    setTimeout(function () {\n      self.onError(e);\n    }, 0);\n    return;\n  }\n  if (typeof document !== 'undefined') {\n    this.index = Request.requestsCount++;\n    Request.requests[this.index] = this;\n  }\n};\n\n/**\n * Called upon successful response.\n *\n * @api private\n */\n\nRequest.prototype.onSuccess = function () {\n  this.emit('success');\n  this.cleanup();\n};\n\n/**\n * Called if we have data.\n *\n * @api private\n */\n\nRequest.prototype.onData = function (data) {\n  this.emit('data', data);\n  this.onSuccess();\n};\n\n/**\n * Called upon error.\n *\n * @api private\n */\n\nRequest.prototype.onError = function (err) {\n  this.emit('error', err);\n  this.cleanup(true);\n};\n\n/**\n * Cleans up house.\n *\n * @api private\n */\n\nRequest.prototype.cleanup = function (fromError) {\n  if ('undefined' === typeof this.xhr || null === this.xhr) {\n    return;\n  }\n  // xmlhttprequest\n  if (this.hasXDR()) {\n    this.xhr.onload = this.xhr.onerror = empty;\n  } else {\n    this.xhr.onreadystatechange = empty;\n  }\n  if (fromError) {\n    try {\n      this.xhr.abort();\n    } catch (e) {}\n  }\n  if (typeof document !== 'undefined') {\n    delete Request.requests[this.index];\n  }\n  this.xhr = null;\n};\n\n/**\n * Called upon load.\n *\n * @api private\n */\n\nRequest.prototype.onLoad = function () {\n  var data;\n  try {\n    var contentType;\n    try {\n      contentType = this.xhr.getResponseHeader('Content-Type');\n    } catch (e) {}\n    if (contentType === 'application/octet-stream' || contentType === 'application/octet-stream; charset=UTF-8') {\n      data = this.xhr.response || this.xhr.responseText;\n    } else {\n      data = this.xhr.responseText;\n    }\n  } catch (e) {\n    this.onError(e);\n  }\n  if (null != data) {\n    this.onData(data);\n  }\n};\n\n/**\n * Check if it has XDomainRequest.\n *\n * @api private\n */\n\nRequest.prototype.hasXDR = function () {\n  return typeof XDomainRequest !== 'undefined' && !this.xs && this.enablesXDR;\n};\n\n/**\n * Aborts the request.\n *\n * @api public\n */\n\nRequest.prototype.abort = function () {\n  this.cleanup();\n};\n\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\n\nRequest.requestsCount = 0;\nRequest.requests = {};\nif (typeof document !== 'undefined') {\n  if (typeof attachEvent === 'function') {\n    attachEvent('onunload', unloadHandler);\n  } else if (typeof addEventListener === 'function') {\n    var terminationEvent = 'onpagehide' in globalThis ? 'pagehide' : 'unload';\n    addEventListener(terminationEvent, unloadHandler, false);\n  }\n}\nfunction unloadHandler() {\n  for (var i in Request.requests) {\n    if (Request.requests.hasOwnProperty(i)) {\n      Request.requests[i].abort();\n    }\n  }\n}", "map": {"version": 3, "names": ["XMLHttpRequest", "require", "Polling", "Emitter", "inherit", "debug", "globalThis", "module", "exports", "XHR", "Request", "empty", "opts", "call", "requestTimeout", "extraHeaders", "location", "isSSL", "protocol", "port", "xd", "hostname", "xs", "secure", "prototype", "supportsBinary", "request", "uri", "agent", "enablesXDR", "withCredentials", "pfx", "key", "passphrase", "cert", "ca", "ciphers", "rejectUnauthorized", "doWrite", "data", "fn", "isBinary", "undefined", "req", "method", "self", "on", "err", "onError", "sendXhr", "doPoll", "onData", "pollXhr", "async", "create", "xdomain", "xscheme", "xhr", "open", "setDisableHeaderCheck", "i", "hasOwnProperty", "setRequestHeader", "e", "timeout", "hasXDR", "onload", "onLoad", "onerror", "responseText", "onreadystatechange", "readyState", "contentType", "getResponseHeader", "responseType", "status", "setTimeout", "send", "document", "index", "requestsCount", "requests", "onSuccess", "emit", "cleanup", "fromError", "abort", "response", "XDomainRequest", "attachEvent", "unload<PERSON><PERSON><PERSON>", "addEventListener", "terminationEvent"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/engine.io-client@3.5.4/node_modules/engine.io-client/lib/transports/polling-xhr.js"], "sourcesContent": ["/* global attachEvent */\n\n/**\n * Module requirements.\n */\n\nvar XMLHttpRequest = require('./xmlhttprequest');\nvar Polling = require('./polling');\nvar Emitter = require('component-emitter');\nvar inherit = require('component-inherit');\nvar debug = require('debug')('engine.io-client:polling-xhr');\nvar globalThis = require('../globalThis');\n\n/**\n * Module exports.\n */\n\nmodule.exports = XHR;\nmodule.exports.Request = Request;\n\n/**\n * Empty function\n */\n\nfunction empty () {}\n\n/**\n * XHR Polling constructor.\n *\n * @param {Object} opts\n * @api public\n */\n\nfunction XHR (opts) {\n  Polling.call(this, opts);\n  this.requestTimeout = opts.requestTimeout;\n  this.extraHeaders = opts.extraHeaders;\n\n  if (typeof location !== 'undefined') {\n    var isSSL = 'https:' === location.protocol;\n    var port = location.port;\n\n    // some user agents have empty `location.port`\n    if (!port) {\n      port = isSSL ? 443 : 80;\n    }\n\n    this.xd = (typeof location !== 'undefined' && opts.hostname !== location.hostname) ||\n      port !== opts.port;\n    this.xs = opts.secure !== isSSL;\n  }\n}\n\n/**\n * Inherits from Polling.\n */\n\ninherit(XHR, Polling);\n\n/**\n * XHR supports binary\n */\n\nXHR.prototype.supportsBinary = true;\n\n/**\n * Creates a request.\n *\n * @param {String} method\n * @api private\n */\n\nXHR.prototype.request = function (opts) {\n  opts = opts || {};\n  opts.uri = this.uri();\n  opts.xd = this.xd;\n  opts.xs = this.xs;\n  opts.agent = this.agent || false;\n  opts.supportsBinary = this.supportsBinary;\n  opts.enablesXDR = this.enablesXDR;\n  opts.withCredentials = this.withCredentials;\n\n  // SSL options for Node.js client\n  opts.pfx = this.pfx;\n  opts.key = this.key;\n  opts.passphrase = this.passphrase;\n  opts.cert = this.cert;\n  opts.ca = this.ca;\n  opts.ciphers = this.ciphers;\n  opts.rejectUnauthorized = this.rejectUnauthorized;\n  opts.requestTimeout = this.requestTimeout;\n\n  // other options for Node.js client\n  opts.extraHeaders = this.extraHeaders;\n\n  return new Request(opts);\n};\n\n/**\n * Sends data.\n *\n * @param {String} data to send.\n * @param {Function} called upon flush.\n * @api private\n */\n\nXHR.prototype.doWrite = function (data, fn) {\n  var isBinary = typeof data !== 'string' && data !== undefined;\n  var req = this.request({ method: 'POST', data: data, isBinary: isBinary });\n  var self = this;\n  req.on('success', fn);\n  req.on('error', function (err) {\n    self.onError('xhr post error', err);\n  });\n  this.sendXhr = req;\n};\n\n/**\n * Starts a poll cycle.\n *\n * @api private\n */\n\nXHR.prototype.doPoll = function () {\n  debug('xhr poll');\n  var req = this.request();\n  var self = this;\n  req.on('data', function (data) {\n    self.onData(data);\n  });\n  req.on('error', function (err) {\n    self.onError('xhr poll error', err);\n  });\n  this.pollXhr = req;\n};\n\n/**\n * Request constructor\n *\n * @param {Object} options\n * @api public\n */\n\nfunction Request (opts) {\n  this.method = opts.method || 'GET';\n  this.uri = opts.uri;\n  this.xd = !!opts.xd;\n  this.xs = !!opts.xs;\n  this.async = false !== opts.async;\n  this.data = undefined !== opts.data ? opts.data : null;\n  this.agent = opts.agent;\n  this.isBinary = opts.isBinary;\n  this.supportsBinary = opts.supportsBinary;\n  this.enablesXDR = opts.enablesXDR;\n  this.withCredentials = opts.withCredentials;\n  this.requestTimeout = opts.requestTimeout;\n\n  // SSL options for Node.js client\n  this.pfx = opts.pfx;\n  this.key = opts.key;\n  this.passphrase = opts.passphrase;\n  this.cert = opts.cert;\n  this.ca = opts.ca;\n  this.ciphers = opts.ciphers;\n  this.rejectUnauthorized = opts.rejectUnauthorized;\n\n  // other options for Node.js client\n  this.extraHeaders = opts.extraHeaders;\n\n  this.create();\n}\n\n/**\n * Mix in `Emitter`.\n */\n\nEmitter(Request.prototype);\n\n/**\n * Creates the XHR object and sends the request.\n *\n * @api private\n */\n\nRequest.prototype.create = function () {\n  var opts = { agent: this.agent, xdomain: this.xd, xscheme: this.xs, enablesXDR: this.enablesXDR };\n\n  // SSL options for Node.js client\n  opts.pfx = this.pfx;\n  opts.key = this.key;\n  opts.passphrase = this.passphrase;\n  opts.cert = this.cert;\n  opts.ca = this.ca;\n  opts.ciphers = this.ciphers;\n  opts.rejectUnauthorized = this.rejectUnauthorized;\n\n  var xhr = this.xhr = new XMLHttpRequest(opts);\n  var self = this;\n\n  try {\n    debug('xhr open %s: %s', this.method, this.uri);\n    xhr.open(this.method, this.uri, this.async);\n    try {\n      if (this.extraHeaders) {\n        xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n        for (var i in this.extraHeaders) {\n          if (this.extraHeaders.hasOwnProperty(i)) {\n            xhr.setRequestHeader(i, this.extraHeaders[i]);\n          }\n        }\n      }\n    } catch (e) {}\n\n    if ('POST' === this.method) {\n      try {\n        if (this.isBinary) {\n          xhr.setRequestHeader('Content-type', 'application/octet-stream');\n        } else {\n          xhr.setRequestHeader('Content-type', 'text/plain;charset=UTF-8');\n        }\n      } catch (e) {}\n    }\n\n    try {\n      xhr.setRequestHeader('Accept', '*/*');\n    } catch (e) {}\n\n    // ie6 check\n    if ('withCredentials' in xhr) {\n      xhr.withCredentials = this.withCredentials;\n    }\n\n    if (this.requestTimeout) {\n      xhr.timeout = this.requestTimeout;\n    }\n\n    if (this.hasXDR()) {\n      xhr.onload = function () {\n        self.onLoad();\n      };\n      xhr.onerror = function () {\n        self.onError(xhr.responseText);\n      };\n    } else {\n      xhr.onreadystatechange = function () {\n        if (xhr.readyState === 2) {\n          try {\n            var contentType = xhr.getResponseHeader('Content-Type');\n            if (self.supportsBinary && contentType === 'application/octet-stream' || contentType === 'application/octet-stream; charset=UTF-8') {\n              xhr.responseType = 'arraybuffer';\n            }\n          } catch (e) {}\n        }\n        if (4 !== xhr.readyState) return;\n        if (200 === xhr.status || 1223 === xhr.status) {\n          self.onLoad();\n        } else {\n          // make sure the `error` event handler that's user-set\n          // does not throw in the same tick and gets caught here\n          setTimeout(function () {\n            self.onError(typeof xhr.status === 'number' ? xhr.status : 0);\n          }, 0);\n        }\n      };\n    }\n\n    debug('xhr data %s', this.data);\n    xhr.send(this.data);\n  } catch (e) {\n    // Need to defer since .create() is called directly fhrom the constructor\n    // and thus the 'error' event can only be only bound *after* this exception\n    // occurs.  Therefore, also, we cannot throw here at all.\n    setTimeout(function () {\n      self.onError(e);\n    }, 0);\n    return;\n  }\n\n  if (typeof document !== 'undefined') {\n    this.index = Request.requestsCount++;\n    Request.requests[this.index] = this;\n  }\n};\n\n/**\n * Called upon successful response.\n *\n * @api private\n */\n\nRequest.prototype.onSuccess = function () {\n  this.emit('success');\n  this.cleanup();\n};\n\n/**\n * Called if we have data.\n *\n * @api private\n */\n\nRequest.prototype.onData = function (data) {\n  this.emit('data', data);\n  this.onSuccess();\n};\n\n/**\n * Called upon error.\n *\n * @api private\n */\n\nRequest.prototype.onError = function (err) {\n  this.emit('error', err);\n  this.cleanup(true);\n};\n\n/**\n * Cleans up house.\n *\n * @api private\n */\n\nRequest.prototype.cleanup = function (fromError) {\n  if ('undefined' === typeof this.xhr || null === this.xhr) {\n    return;\n  }\n  // xmlhttprequest\n  if (this.hasXDR()) {\n    this.xhr.onload = this.xhr.onerror = empty;\n  } else {\n    this.xhr.onreadystatechange = empty;\n  }\n\n  if (fromError) {\n    try {\n      this.xhr.abort();\n    } catch (e) {}\n  }\n\n  if (typeof document !== 'undefined') {\n    delete Request.requests[this.index];\n  }\n\n  this.xhr = null;\n};\n\n/**\n * Called upon load.\n *\n * @api private\n */\n\nRequest.prototype.onLoad = function () {\n  var data;\n  try {\n    var contentType;\n    try {\n      contentType = this.xhr.getResponseHeader('Content-Type');\n    } catch (e) {}\n    if (contentType === 'application/octet-stream' || contentType === 'application/octet-stream; charset=UTF-8') {\n      data = this.xhr.response || this.xhr.responseText;\n    } else {\n      data = this.xhr.responseText;\n    }\n  } catch (e) {\n    this.onError(e);\n  }\n  if (null != data) {\n    this.onData(data);\n  }\n};\n\n/**\n * Check if it has XDomainRequest.\n *\n * @api private\n */\n\nRequest.prototype.hasXDR = function () {\n  return typeof XDomainRequest !== 'undefined' && !this.xs && this.enablesXDR;\n};\n\n/**\n * Aborts the request.\n *\n * @api public\n */\n\nRequest.prototype.abort = function () {\n  this.cleanup();\n};\n\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\n\nRequest.requestsCount = 0;\nRequest.requests = {};\n\nif (typeof document !== 'undefined') {\n  if (typeof attachEvent === 'function') {\n    attachEvent('onunload', unloadHandler);\n  } else if (typeof addEventListener === 'function') {\n    var terminationEvent = 'onpagehide' in globalThis ? 'pagehide' : 'unload';\n    addEventListener(terminationEvent, unloadHandler, false);\n  }\n}\n\nfunction unloadHandler () {\n  for (var i in Request.requests) {\n    if (Request.requests.hasOwnProperty(i)) {\n      Request.requests[i].abort();\n    }\n  }\n}\n"], "mappings": "AAAA;;AAEA;AACA;AACA;;AAEA,IAAIA,cAAc,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAChD,IAAIC,OAAO,GAAGD,OAAO,CAAC,WAAW,CAAC;AAClC,IAAIE,OAAO,GAAGF,OAAO,CAAC,mBAAmB,CAAC;AAC1C,IAAIG,OAAO,GAAGH,OAAO,CAAC,mBAAmB,CAAC;AAC1C,IAAII,KAAK,GAAGJ,OAAO,CAAC,OAAO,CAAC,CAAC,8BAA8B,CAAC;AAC5D,IAAIK,UAAU,GAAGL,OAAO,CAAC,eAAe,CAAC;;AAEzC;AACA;AACA;;AAEAM,MAAM,CAACC,OAAO,GAAGC,GAAG;AACpBF,MAAM,CAACC,OAAO,CAACE,OAAO,GAAGA,OAAO;;AAEhC;AACA;AACA;;AAEA,SAASC,KAAKA,CAAA,EAAI,CAAC;;AAEnB;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASF,GAAGA,CAAEG,IAAI,EAAE;EAClBV,OAAO,CAACW,IAAI,CAAC,IAAI,EAAED,IAAI,CAAC;EACxB,IAAI,CAACE,cAAc,GAAGF,IAAI,CAACE,cAAc;EACzC,IAAI,CAACC,YAAY,GAAGH,IAAI,CAACG,YAAY;EAErC,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;IACnC,IAAIC,KAAK,GAAG,QAAQ,KAAKD,QAAQ,CAACE,QAAQ;IAC1C,IAAIC,IAAI,GAAGH,QAAQ,CAACG,IAAI;;IAExB;IACA,IAAI,CAACA,IAAI,EAAE;MACTA,IAAI,GAAGF,KAAK,GAAG,GAAG,GAAG,EAAE;IACzB;IAEA,IAAI,CAACG,EAAE,GAAI,OAAOJ,QAAQ,KAAK,WAAW,IAAIJ,IAAI,CAACS,QAAQ,KAAKL,QAAQ,CAACK,QAAQ,IAC/EF,IAAI,KAAKP,IAAI,CAACO,IAAI;IACpB,IAAI,CAACG,EAAE,GAAGV,IAAI,CAACW,MAAM,KAAKN,KAAK;EACjC;AACF;;AAEA;AACA;AACA;;AAEAb,OAAO,CAACK,GAAG,EAAEP,OAAO,CAAC;;AAErB;AACA;AACA;;AAEAO,GAAG,CAACe,SAAS,CAACC,cAAc,GAAG,IAAI;;AAEnC;AACA;AACA;AACA;AACA;AACA;;AAEAhB,GAAG,CAACe,SAAS,CAACE,OAAO,GAAG,UAAUd,IAAI,EAAE;EACtCA,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;EACjBA,IAAI,CAACe,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC;EACrBf,IAAI,CAACQ,EAAE,GAAG,IAAI,CAACA,EAAE;EACjBR,IAAI,CAACU,EAAE,GAAG,IAAI,CAACA,EAAE;EACjBV,IAAI,CAACgB,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,KAAK;EAChChB,IAAI,CAACa,cAAc,GAAG,IAAI,CAACA,cAAc;EACzCb,IAAI,CAACiB,UAAU,GAAG,IAAI,CAACA,UAAU;EACjCjB,IAAI,CAACkB,eAAe,GAAG,IAAI,CAACA,eAAe;;EAE3C;EACAlB,IAAI,CAACmB,GAAG,GAAG,IAAI,CAACA,GAAG;EACnBnB,IAAI,CAACoB,GAAG,GAAG,IAAI,CAACA,GAAG;EACnBpB,IAAI,CAACqB,UAAU,GAAG,IAAI,CAACA,UAAU;EACjCrB,IAAI,CAACsB,IAAI,GAAG,IAAI,CAACA,IAAI;EACrBtB,IAAI,CAACuB,EAAE,GAAG,IAAI,CAACA,EAAE;EACjBvB,IAAI,CAACwB,OAAO,GAAG,IAAI,CAACA,OAAO;EAC3BxB,IAAI,CAACyB,kBAAkB,GAAG,IAAI,CAACA,kBAAkB;EACjDzB,IAAI,CAACE,cAAc,GAAG,IAAI,CAACA,cAAc;;EAEzC;EACAF,IAAI,CAACG,YAAY,GAAG,IAAI,CAACA,YAAY;EAErC,OAAO,IAAIL,OAAO,CAACE,IAAI,CAAC;AAC1B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAH,GAAG,CAACe,SAAS,CAACc,OAAO,GAAG,UAAUC,IAAI,EAAEC,EAAE,EAAE;EAC1C,IAAIC,QAAQ,GAAG,OAAOF,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAKG,SAAS;EAC7D,IAAIC,GAAG,GAAG,IAAI,CAACjB,OAAO,CAAC;IAAEkB,MAAM,EAAE,MAAM;IAAEL,IAAI,EAAEA,IAAI;IAAEE,QAAQ,EAAEA;EAAS,CAAC,CAAC;EAC1E,IAAII,IAAI,GAAG,IAAI;EACfF,GAAG,CAACG,EAAE,CAAC,SAAS,EAAEN,EAAE,CAAC;EACrBG,GAAG,CAACG,EAAE,CAAC,OAAO,EAAE,UAAUC,GAAG,EAAE;IAC7BF,IAAI,CAACG,OAAO,CAAC,gBAAgB,EAAED,GAAG,CAAC;EACrC,CAAC,CAAC;EACF,IAAI,CAACE,OAAO,GAAGN,GAAG;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEAlC,GAAG,CAACe,SAAS,CAAC0B,MAAM,GAAG,YAAY;EACjC7C,KAAK,CAAC,UAAU,CAAC;EACjB,IAAIsC,GAAG,GAAG,IAAI,CAACjB,OAAO,CAAC,CAAC;EACxB,IAAImB,IAAI,GAAG,IAAI;EACfF,GAAG,CAACG,EAAE,CAAC,MAAM,EAAE,UAAUP,IAAI,EAAE;IAC7BM,IAAI,CAACM,MAAM,CAACZ,IAAI,CAAC;EACnB,CAAC,CAAC;EACFI,GAAG,CAACG,EAAE,CAAC,OAAO,EAAE,UAAUC,GAAG,EAAE;IAC7BF,IAAI,CAACG,OAAO,CAAC,gBAAgB,EAAED,GAAG,CAAC;EACrC,CAAC,CAAC;EACF,IAAI,CAACK,OAAO,GAAGT,GAAG;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASjC,OAAOA,CAAEE,IAAI,EAAE;EACtB,IAAI,CAACgC,MAAM,GAAGhC,IAAI,CAACgC,MAAM,IAAI,KAAK;EAClC,IAAI,CAACjB,GAAG,GAAGf,IAAI,CAACe,GAAG;EACnB,IAAI,CAACP,EAAE,GAAG,CAAC,CAACR,IAAI,CAACQ,EAAE;EACnB,IAAI,CAACE,EAAE,GAAG,CAAC,CAACV,IAAI,CAACU,EAAE;EACnB,IAAI,CAAC+B,KAAK,GAAG,KAAK,KAAKzC,IAAI,CAACyC,KAAK;EACjC,IAAI,CAACd,IAAI,GAAGG,SAAS,KAAK9B,IAAI,CAAC2B,IAAI,GAAG3B,IAAI,CAAC2B,IAAI,GAAG,IAAI;EACtD,IAAI,CAACX,KAAK,GAAGhB,IAAI,CAACgB,KAAK;EACvB,IAAI,CAACa,QAAQ,GAAG7B,IAAI,CAAC6B,QAAQ;EAC7B,IAAI,CAAChB,cAAc,GAAGb,IAAI,CAACa,cAAc;EACzC,IAAI,CAACI,UAAU,GAAGjB,IAAI,CAACiB,UAAU;EACjC,IAAI,CAACC,eAAe,GAAGlB,IAAI,CAACkB,eAAe;EAC3C,IAAI,CAAChB,cAAc,GAAGF,IAAI,CAACE,cAAc;;EAEzC;EACA,IAAI,CAACiB,GAAG,GAAGnB,IAAI,CAACmB,GAAG;EACnB,IAAI,CAACC,GAAG,GAAGpB,IAAI,CAACoB,GAAG;EACnB,IAAI,CAACC,UAAU,GAAGrB,IAAI,CAACqB,UAAU;EACjC,IAAI,CAACC,IAAI,GAAGtB,IAAI,CAACsB,IAAI;EACrB,IAAI,CAACC,EAAE,GAAGvB,IAAI,CAACuB,EAAE;EACjB,IAAI,CAACC,OAAO,GAAGxB,IAAI,CAACwB,OAAO;EAC3B,IAAI,CAACC,kBAAkB,GAAGzB,IAAI,CAACyB,kBAAkB;;EAEjD;EACA,IAAI,CAACtB,YAAY,GAAGH,IAAI,CAACG,YAAY;EAErC,IAAI,CAACuC,MAAM,CAAC,CAAC;AACf;;AAEA;AACA;AACA;;AAEAnD,OAAO,CAACO,OAAO,CAACc,SAAS,CAAC;;AAE1B;AACA;AACA;AACA;AACA;;AAEAd,OAAO,CAACc,SAAS,CAAC8B,MAAM,GAAG,YAAY;EACrC,IAAI1C,IAAI,GAAG;IAAEgB,KAAK,EAAE,IAAI,CAACA,KAAK;IAAE2B,OAAO,EAAE,IAAI,CAACnC,EAAE;IAAEoC,OAAO,EAAE,IAAI,CAAClC,EAAE;IAAEO,UAAU,EAAE,IAAI,CAACA;EAAW,CAAC;;EAEjG;EACAjB,IAAI,CAACmB,GAAG,GAAG,IAAI,CAACA,GAAG;EACnBnB,IAAI,CAACoB,GAAG,GAAG,IAAI,CAACA,GAAG;EACnBpB,IAAI,CAACqB,UAAU,GAAG,IAAI,CAACA,UAAU;EACjCrB,IAAI,CAACsB,IAAI,GAAG,IAAI,CAACA,IAAI;EACrBtB,IAAI,CAACuB,EAAE,GAAG,IAAI,CAACA,EAAE;EACjBvB,IAAI,CAACwB,OAAO,GAAG,IAAI,CAACA,OAAO;EAC3BxB,IAAI,CAACyB,kBAAkB,GAAG,IAAI,CAACA,kBAAkB;EAEjD,IAAIoB,GAAG,GAAG,IAAI,CAACA,GAAG,GAAG,IAAIzD,cAAc,CAACY,IAAI,CAAC;EAC7C,IAAIiC,IAAI,GAAG,IAAI;EAEf,IAAI;IACFxC,KAAK,CAAC,iBAAiB,EAAE,IAAI,CAACuC,MAAM,EAAE,IAAI,CAACjB,GAAG,CAAC;IAC/C8B,GAAG,CAACC,IAAI,CAAC,IAAI,CAACd,MAAM,EAAE,IAAI,CAACjB,GAAG,EAAE,IAAI,CAAC0B,KAAK,CAAC;IAC3C,IAAI;MACF,IAAI,IAAI,CAACtC,YAAY,EAAE;QACrB0C,GAAG,CAACE,qBAAqB,IAAIF,GAAG,CAACE,qBAAqB,CAAC,IAAI,CAAC;QAC5D,KAAK,IAAIC,CAAC,IAAI,IAAI,CAAC7C,YAAY,EAAE;UAC/B,IAAI,IAAI,CAACA,YAAY,CAAC8C,cAAc,CAACD,CAAC,CAAC,EAAE;YACvCH,GAAG,CAACK,gBAAgB,CAACF,CAAC,EAAE,IAAI,CAAC7C,YAAY,CAAC6C,CAAC,CAAC,CAAC;UAC/C;QACF;MACF;IACF,CAAC,CAAC,OAAOG,CAAC,EAAE,CAAC;IAEb,IAAI,MAAM,KAAK,IAAI,CAACnB,MAAM,EAAE;MAC1B,IAAI;QACF,IAAI,IAAI,CAACH,QAAQ,EAAE;UACjBgB,GAAG,CAACK,gBAAgB,CAAC,cAAc,EAAE,0BAA0B,CAAC;QAClE,CAAC,MAAM;UACLL,GAAG,CAACK,gBAAgB,CAAC,cAAc,EAAE,0BAA0B,CAAC;QAClE;MACF,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAC;IACf;IAEA,IAAI;MACFN,GAAG,CAACK,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC;IACvC,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAC;;IAEb;IACA,IAAI,iBAAiB,IAAIN,GAAG,EAAE;MAC5BA,GAAG,CAAC3B,eAAe,GAAG,IAAI,CAACA,eAAe;IAC5C;IAEA,IAAI,IAAI,CAAChB,cAAc,EAAE;MACvB2C,GAAG,CAACO,OAAO,GAAG,IAAI,CAAClD,cAAc;IACnC;IAEA,IAAI,IAAI,CAACmD,MAAM,CAAC,CAAC,EAAE;MACjBR,GAAG,CAACS,MAAM,GAAG,YAAY;QACvBrB,IAAI,CAACsB,MAAM,CAAC,CAAC;MACf,CAAC;MACDV,GAAG,CAACW,OAAO,GAAG,YAAY;QACxBvB,IAAI,CAACG,OAAO,CAACS,GAAG,CAACY,YAAY,CAAC;MAChC,CAAC;IACH,CAAC,MAAM;MACLZ,GAAG,CAACa,kBAAkB,GAAG,YAAY;QACnC,IAAIb,GAAG,CAACc,UAAU,KAAK,CAAC,EAAE;UACxB,IAAI;YACF,IAAIC,WAAW,GAAGf,GAAG,CAACgB,iBAAiB,CAAC,cAAc,CAAC;YACvD,IAAI5B,IAAI,CAACpB,cAAc,IAAI+C,WAAW,KAAK,0BAA0B,IAAIA,WAAW,KAAK,yCAAyC,EAAE;cAClIf,GAAG,CAACiB,YAAY,GAAG,aAAa;YAClC;UACF,CAAC,CAAC,OAAOX,CAAC,EAAE,CAAC;QACf;QACA,IAAI,CAAC,KAAKN,GAAG,CAACc,UAAU,EAAE;QAC1B,IAAI,GAAG,KAAKd,GAAG,CAACkB,MAAM,IAAI,IAAI,KAAKlB,GAAG,CAACkB,MAAM,EAAE;UAC7C9B,IAAI,CAACsB,MAAM,CAAC,CAAC;QACf,CAAC,MAAM;UACL;UACA;UACAS,UAAU,CAAC,YAAY;YACrB/B,IAAI,CAACG,OAAO,CAAC,OAAOS,GAAG,CAACkB,MAAM,KAAK,QAAQ,GAAGlB,GAAG,CAACkB,MAAM,GAAG,CAAC,CAAC;UAC/D,CAAC,EAAE,CAAC,CAAC;QACP;MACF,CAAC;IACH;IAEAtE,KAAK,CAAC,aAAa,EAAE,IAAI,CAACkC,IAAI,CAAC;IAC/BkB,GAAG,CAACoB,IAAI,CAAC,IAAI,CAACtC,IAAI,CAAC;EACrB,CAAC,CAAC,OAAOwB,CAAC,EAAE;IACV;IACA;IACA;IACAa,UAAU,CAAC,YAAY;MACrB/B,IAAI,CAACG,OAAO,CAACe,CAAC,CAAC;IACjB,CAAC,EAAE,CAAC,CAAC;IACL;EACF;EAEA,IAAI,OAAOe,QAAQ,KAAK,WAAW,EAAE;IACnC,IAAI,CAACC,KAAK,GAAGrE,OAAO,CAACsE,aAAa,EAAE;IACpCtE,OAAO,CAACuE,QAAQ,CAAC,IAAI,CAACF,KAAK,CAAC,GAAG,IAAI;EACrC;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEArE,OAAO,CAACc,SAAS,CAAC0D,SAAS,GAAG,YAAY;EACxC,IAAI,CAACC,IAAI,CAAC,SAAS,CAAC;EACpB,IAAI,CAACC,OAAO,CAAC,CAAC;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA1E,OAAO,CAACc,SAAS,CAAC2B,MAAM,GAAG,UAAUZ,IAAI,EAAE;EACzC,IAAI,CAAC4C,IAAI,CAAC,MAAM,EAAE5C,IAAI,CAAC;EACvB,IAAI,CAAC2C,SAAS,CAAC,CAAC;AAClB,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEAxE,OAAO,CAACc,SAAS,CAACwB,OAAO,GAAG,UAAUD,GAAG,EAAE;EACzC,IAAI,CAACoC,IAAI,CAAC,OAAO,EAAEpC,GAAG,CAAC;EACvB,IAAI,CAACqC,OAAO,CAAC,IAAI,CAAC;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA1E,OAAO,CAACc,SAAS,CAAC4D,OAAO,GAAG,UAAUC,SAAS,EAAE;EAC/C,IAAI,WAAW,KAAK,OAAO,IAAI,CAAC5B,GAAG,IAAI,IAAI,KAAK,IAAI,CAACA,GAAG,EAAE;IACxD;EACF;EACA;EACA,IAAI,IAAI,CAACQ,MAAM,CAAC,CAAC,EAAE;IACjB,IAAI,CAACR,GAAG,CAACS,MAAM,GAAG,IAAI,CAACT,GAAG,CAACW,OAAO,GAAGzD,KAAK;EAC5C,CAAC,MAAM;IACL,IAAI,CAAC8C,GAAG,CAACa,kBAAkB,GAAG3D,KAAK;EACrC;EAEA,IAAI0E,SAAS,EAAE;IACb,IAAI;MACF,IAAI,CAAC5B,GAAG,CAAC6B,KAAK,CAAC,CAAC;IAClB,CAAC,CAAC,OAAOvB,CAAC,EAAE,CAAC;EACf;EAEA,IAAI,OAAOe,QAAQ,KAAK,WAAW,EAAE;IACnC,OAAOpE,OAAO,CAACuE,QAAQ,CAAC,IAAI,CAACF,KAAK,CAAC;EACrC;EAEA,IAAI,CAACtB,GAAG,GAAG,IAAI;AACjB,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA/C,OAAO,CAACc,SAAS,CAAC2C,MAAM,GAAG,YAAY;EACrC,IAAI5B,IAAI;EACR,IAAI;IACF,IAAIiC,WAAW;IACf,IAAI;MACFA,WAAW,GAAG,IAAI,CAACf,GAAG,CAACgB,iBAAiB,CAAC,cAAc,CAAC;IAC1D,CAAC,CAAC,OAAOV,CAAC,EAAE,CAAC;IACb,IAAIS,WAAW,KAAK,0BAA0B,IAAIA,WAAW,KAAK,yCAAyC,EAAE;MAC3GjC,IAAI,GAAG,IAAI,CAACkB,GAAG,CAAC8B,QAAQ,IAAI,IAAI,CAAC9B,GAAG,CAACY,YAAY;IACnD,CAAC,MAAM;MACL9B,IAAI,GAAG,IAAI,CAACkB,GAAG,CAACY,YAAY;IAC9B;EACF,CAAC,CAAC,OAAON,CAAC,EAAE;IACV,IAAI,CAACf,OAAO,CAACe,CAAC,CAAC;EACjB;EACA,IAAI,IAAI,IAAIxB,IAAI,EAAE;IAChB,IAAI,CAACY,MAAM,CAACZ,IAAI,CAAC;EACnB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA7B,OAAO,CAACc,SAAS,CAACyC,MAAM,GAAG,YAAY;EACrC,OAAO,OAAOuB,cAAc,KAAK,WAAW,IAAI,CAAC,IAAI,CAAClE,EAAE,IAAI,IAAI,CAACO,UAAU;AAC7E,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEAnB,OAAO,CAACc,SAAS,CAAC8D,KAAK,GAAG,YAAY;EACpC,IAAI,CAACF,OAAO,CAAC,CAAC;AAChB,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA1E,OAAO,CAACsE,aAAa,GAAG,CAAC;AACzBtE,OAAO,CAACuE,QAAQ,GAAG,CAAC,CAAC;AAErB,IAAI,OAAOH,QAAQ,KAAK,WAAW,EAAE;EACnC,IAAI,OAAOW,WAAW,KAAK,UAAU,EAAE;IACrCA,WAAW,CAAC,UAAU,EAAEC,aAAa,CAAC;EACxC,CAAC,MAAM,IAAI,OAAOC,gBAAgB,KAAK,UAAU,EAAE;IACjD,IAAIC,gBAAgB,GAAG,YAAY,IAAItF,UAAU,GAAG,UAAU,GAAG,QAAQ;IACzEqF,gBAAgB,CAACC,gBAAgB,EAAEF,aAAa,EAAE,KAAK,CAAC;EAC1D;AACF;AAEA,SAASA,aAAaA,CAAA,EAAI;EACxB,KAAK,IAAI9B,CAAC,IAAIlD,OAAO,CAACuE,QAAQ,EAAE;IAC9B,IAAIvE,OAAO,CAACuE,QAAQ,CAACpB,cAAc,CAACD,CAAC,CAAC,EAAE;MACtClD,OAAO,CAACuE,QAAQ,CAACrB,CAAC,CAAC,CAAC0B,KAAK,CAAC,CAAC;IAC7B;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}