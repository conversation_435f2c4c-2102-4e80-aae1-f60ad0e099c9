{"ast": null, "code": "var conventions = require(\"./conventions\");\nvar dom = require('./dom');\nvar entities = require('./entities');\nvar sax = require('./sax');\nvar DOMImplementation = dom.DOMImplementation;\nvar NAMESPACE = conventions.NAMESPACE;\nvar ParseError = sax.ParseError;\nvar XMLReader = sax.XMLReader;\n\n/**\n * Normalizes line ending according to https://www.w3.org/TR/xml11/#sec-line-ends:\n *\n * > XML parsed entities are often stored in computer files which,\n * > for editing convenience, are organized into lines.\n * > These lines are typically separated by some combination\n * > of the characters CARRIAGE RETURN (#xD) and LINE FEED (#xA).\n * >\n * > To simplify the tasks of applications, the XML processor must behave\n * > as if it normalized all line breaks in external parsed entities (including the document entity)\n * > on input, before parsing, by translating all of the following to a single #xA character:\n * >\n * > 1. the two-character sequence #xD #xA\n * > 2. the two-character sequence #xD #x85\n * > 3. the single character #x85\n * > 4. the single character #x2028\n * > 5. any #xD character that is not immediately followed by #xA or #x85.\n *\n * @param {string} input\n * @returns {string}\n */\nfunction normalizeLineEndings(input) {\n  return input.replace(/\\r[\\n\\u0085]/g, '\\n').replace(/[\\r\\u0085\\u2028]/g, '\\n');\n}\n\n/**\n * @typedef Locator\n * @property {number} [columnNumber]\n * @property {number} [lineNumber]\n */\n\n/**\n * @typedef DOMParserOptions\n * @property {DOMHandler} [domBuilder]\n * @property {Function} [errorHandler]\n * @property {(string) => string} [normalizeLineEndings] used to replace line endings before parsing\n * \t\t\t\t\t\tdefaults to `normalizeLineEndings`\n * @property {Locator} [locator]\n * @property {Record<string, string>} [xmlns]\n *\n * @see normalizeLineEndings\n */\n\n/**\n * The DOMParser interface provides the ability to parse XML or HTML source code\n * from a string into a DOM `Document`.\n *\n * _xmldom is different from the spec in that it allows an `options` parameter,\n * to override the default behavior._\n *\n * @param {DOMParserOptions} [options]\n * @constructor\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser\n * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#dom-parsing-and-serialization\n */\nfunction DOMParser(options) {\n  this.options = options || {\n    locator: {}\n  };\n}\nDOMParser.prototype.parseFromString = function (source, mimeType) {\n  var options = this.options;\n  var sax = new XMLReader();\n  var domBuilder = options.domBuilder || new DOMHandler(); //contentHandler and LexicalHandler\n  var errorHandler = options.errorHandler;\n  var locator = options.locator;\n  var defaultNSMap = options.xmlns || {};\n  var isHTML = /\\/x?html?$/.test(mimeType); //mimeType.toLowerCase().indexOf('html') > -1;\n  var entityMap = isHTML ? entities.HTML_ENTITIES : entities.XML_ENTITIES;\n  if (locator) {\n    domBuilder.setDocumentLocator(locator);\n  }\n  sax.errorHandler = buildErrorHandler(errorHandler, domBuilder, locator);\n  sax.domBuilder = options.domBuilder || domBuilder;\n  if (isHTML) {\n    defaultNSMap[''] = NAMESPACE.HTML;\n  }\n  defaultNSMap.xml = defaultNSMap.xml || NAMESPACE.XML;\n  var normalize = options.normalizeLineEndings || normalizeLineEndings;\n  if (source && typeof source === 'string') {\n    sax.parse(normalize(source), defaultNSMap, entityMap);\n  } else {\n    sax.errorHandler.error('invalid doc source');\n  }\n  return domBuilder.doc;\n};\nfunction buildErrorHandler(errorImpl, domBuilder, locator) {\n  if (!errorImpl) {\n    if (domBuilder instanceof DOMHandler) {\n      return domBuilder;\n    }\n    errorImpl = domBuilder;\n  }\n  var errorHandler = {};\n  var isCallback = errorImpl instanceof Function;\n  locator = locator || {};\n  function build(key) {\n    var fn = errorImpl[key];\n    if (!fn && isCallback) {\n      fn = errorImpl.length == 2 ? function (msg) {\n        errorImpl(key, msg);\n      } : errorImpl;\n    }\n    errorHandler[key] = fn && function (msg) {\n      fn('[xmldom ' + key + ']\\t' + msg + _locator(locator));\n    } || function () {};\n  }\n  build('warning');\n  build('error');\n  build('fatalError');\n  return errorHandler;\n}\n\n//console.log('#\\n\\n\\n\\n\\n\\n\\n####')\n/**\n * +ContentHandler+ErrorHandler\n * +LexicalHandler+EntityResolver2\n * -DeclHandler-DTDHandler\n *\n * DefaultHandler:EntityResolver, DTDHandler, ContentHandler, ErrorHandler\n * DefaultHandler2:DefaultHandler,LexicalHandler, DeclHandler, EntityResolver2\n * @link http://www.saxproject.org/apidoc/org/xml/sax/helpers/DefaultHandler.html\n */\nfunction DOMHandler() {\n  this.cdata = false;\n}\nfunction position(locator, node) {\n  node.lineNumber = locator.lineNumber;\n  node.columnNumber = locator.columnNumber;\n}\n/**\n * @see org.xml.sax.ContentHandler#startDocument\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ContentHandler.html\n */\nDOMHandler.prototype = {\n  startDocument: function startDocument() {\n    this.doc = new DOMImplementation().createDocument(null, null, null);\n    if (this.locator) {\n      this.doc.documentURI = this.locator.systemId;\n    }\n  },\n  startElement: function startElement(namespaceURI, localName, qName, attrs) {\n    var doc = this.doc;\n    var el = doc.createElementNS(namespaceURI, qName || localName);\n    var len = attrs.length;\n    appendElement(this, el);\n    this.currentElement = el;\n    this.locator && position(this.locator, el);\n    for (var i = 0; i < len; i++) {\n      var namespaceURI = attrs.getURI(i);\n      var value = attrs.getValue(i);\n      var qName = attrs.getQName(i);\n      var attr = doc.createAttributeNS(namespaceURI, qName);\n      this.locator && position(attrs.getLocator(i), attr);\n      attr.value = attr.nodeValue = value;\n      el.setAttributeNode(attr);\n    }\n  },\n  endElement: function endElement(namespaceURI, localName, qName) {\n    var current = this.currentElement;\n    var tagName = current.tagName;\n    this.currentElement = current.parentNode;\n  },\n  startPrefixMapping: function startPrefixMapping(prefix, uri) {},\n  endPrefixMapping: function endPrefixMapping(prefix) {},\n  processingInstruction: function processingInstruction(target, data) {\n    var ins = this.doc.createProcessingInstruction(target, data);\n    this.locator && position(this.locator, ins);\n    appendElement(this, ins);\n  },\n  ignorableWhitespace: function ignorableWhitespace(ch, start, length) {},\n  characters: function characters(chars, start, length) {\n    chars = _toString.apply(this, arguments);\n    //console.log(chars)\n    if (chars) {\n      if (this.cdata) {\n        var charNode = this.doc.createCDATASection(chars);\n      } else {\n        var charNode = this.doc.createTextNode(chars);\n      }\n      if (this.currentElement) {\n        this.currentElement.appendChild(charNode);\n      } else if (/^\\s*$/.test(chars)) {\n        this.doc.appendChild(charNode);\n        //process xml\n      }\n      this.locator && position(this.locator, charNode);\n    }\n  },\n  skippedEntity: function skippedEntity(name) {},\n  endDocument: function endDocument() {\n    this.doc.normalize();\n  },\n  setDocumentLocator: function setDocumentLocator(locator) {\n    if (this.locator = locator) {\n      // && !('lineNumber' in locator)){\n      locator.lineNumber = 0;\n    }\n  },\n  //LexicalHandler\n  comment: function comment(chars, start, length) {\n    chars = _toString.apply(this, arguments);\n    var comm = this.doc.createComment(chars);\n    this.locator && position(this.locator, comm);\n    appendElement(this, comm);\n  },\n  startCDATA: function startCDATA() {\n    //used in characters() methods\n    this.cdata = true;\n  },\n  endCDATA: function endCDATA() {\n    this.cdata = false;\n  },\n  startDTD: function startDTD(name, publicId, systemId) {\n    var impl = this.doc.implementation;\n    if (impl && impl.createDocumentType) {\n      var dt = impl.createDocumentType(name, publicId, systemId);\n      this.locator && position(this.locator, dt);\n      appendElement(this, dt);\n      this.doc.doctype = dt;\n    }\n  },\n  /**\n   * @see org.xml.sax.ErrorHandler\n   * @link http://www.saxproject.org/apidoc/org/xml/sax/ErrorHandler.html\n   */\n  warning: function warning(error) {\n    console.warn('[xmldom warning]\\t' + error, _locator(this.locator));\n  },\n  error: function error(_error) {\n    console.error('[xmldom error]\\t' + _error, _locator(this.locator));\n  },\n  fatalError: function fatalError(error) {\n    throw new ParseError(error, this.locator);\n  }\n};\nfunction _locator(l) {\n  if (l) {\n    return '\\n@' + (l.systemId || '') + '#[line:' + l.lineNumber + ',col:' + l.columnNumber + ']';\n  }\n}\nfunction _toString(chars, start, length) {\n  if (typeof chars == 'string') {\n    return chars.substr(start, length);\n  } else {\n    //java sax connect width xmldom on rhino(what about: \"? && !(chars instanceof String)\")\n    if (chars.length >= start + length || start) {\n      return new java.lang.String(chars, start, length) + '';\n    }\n    return chars;\n  }\n}\n\n/*\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ext/LexicalHandler.html\n * used method of org.xml.sax.ext.LexicalHandler:\n *  #comment(chars, start, length)\n *  #startCDATA()\n *  #endCDATA()\n *  #startDTD(name, publicId, systemId)\n *\n *\n * IGNORED method of org.xml.sax.ext.LexicalHandler:\n *  #endDTD()\n *  #startEntity(name)\n *  #endEntity(name)\n *\n *\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ext/DeclHandler.html\n * IGNORED method of org.xml.sax.ext.DeclHandler\n * \t#attributeDecl(eName, aName, type, mode, value)\n *  #elementDecl(name, model)\n *  #externalEntityDecl(name, publicId, systemId)\n *  #internalEntityDecl(name, value)\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ext/EntityResolver2.html\n * IGNORED method of org.xml.sax.EntityResolver2\n *  #resolveEntity(String name,String publicId,String baseURI,String systemId)\n *  #resolveEntity(publicId, systemId)\n *  #getExternalSubset(name, baseURI)\n * @link http://www.saxproject.org/apidoc/org/xml/sax/DTDHandler.html\n * IGNORED method of org.xml.sax.DTDHandler\n *  #notationDecl(name, publicId, systemId) {};\n *  #unparsedEntityDecl(name, publicId, systemId, notationName) {};\n */\n\"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl\".replace(/\\w+/g, function (key) {\n  DOMHandler.prototype[key] = function () {\n    return null;\n  };\n});\n\n/* Private static helpers treated below as private instance methods, so don't need to add these to the public API; we might use a Relator to also get rid of non-standard public properties */\nfunction appendElement(hander, node) {\n  if (!hander.currentElement) {\n    hander.doc.appendChild(node);\n  } else {\n    hander.currentElement.appendChild(node);\n  }\n} //appendChild and setAttributeNS are preformance key\n\nexports.__DOMHandler = DOMHandler;\nexports.normalizeLineEndings = normalizeLineEndings;\nexports.DOMParser = DOMParser;", "map": {"version": 3, "names": ["conventions", "require", "dom", "entities", "sax", "DOMImplementation", "NAMESPACE", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "XMLReader", "normalizeLineEndings", "input", "replace", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options", "locator", "prototype", "parseFromString", "source", "mimeType", "domBuilder", "DOMHandler", "<PERSON><PERSON><PERSON><PERSON>", "defaultNSMap", "xmlns", "isHTML", "test", "entityMap", "HTML_ENTITIES", "XML_ENTITIES", "setDocumentLocator", "buildErrorHandler", "HTML", "xml", "XML", "normalize", "parse", "error", "doc", "errorImpl", "is<PERSON><PERSON>back", "Function", "build", "key", "fn", "length", "msg", "_locator", "cdata", "position", "node", "lineNumber", "columnNumber", "startDocument", "createDocument", "documentURI", "systemId", "startElement", "namespaceURI", "localName", "qName", "attrs", "el", "createElementNS", "len", "appendElement", "currentElement", "i", "getURI", "value", "getValue", "getQName", "attr", "createAttributeNS", "getLocator", "nodeValue", "setAttributeNode", "endElement", "current", "tagName", "parentNode", "startPrefixMapping", "prefix", "uri", "endPrefixMapping", "processingInstruction", "target", "data", "ins", "createProcessingInstruction", "ignorableWhitespace", "ch", "start", "characters", "chars", "_toString", "apply", "arguments", "charNode", "createCDATASection", "createTextNode", "append<PERSON><PERSON><PERSON>", "skippedEntity", "name", "endDocument", "comment", "comm", "createComment", "startCDATA", "endCDATA", "startDTD", "publicId", "impl", "implementation", "createDocumentType", "dt", "doctype", "warning", "console", "warn", "fatalError", "l", "substr", "java", "lang", "String", "hander", "exports", "__<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/@xmldom+xmldom@0.8.10/node_modules/@xmldom/xmldom/lib/dom-parser.js"], "sourcesContent": ["var conventions = require(\"./conventions\");\nvar dom = require('./dom')\nvar entities = require('./entities');\nvar sax = require('./sax');\n\nvar DOMImplementation = dom.DOMImplementation;\n\nvar NAMESPACE = conventions.NAMESPACE;\n\nvar ParseError = sax.ParseError;\nvar XMLReader = sax.XMLReader;\n\n/**\n * Normalizes line ending according to https://www.w3.org/TR/xml11/#sec-line-ends:\n *\n * > XML parsed entities are often stored in computer files which,\n * > for editing convenience, are organized into lines.\n * > These lines are typically separated by some combination\n * > of the characters CARRIAGE RETURN (#xD) and LINE FEED (#xA).\n * >\n * > To simplify the tasks of applications, the XML processor must behave\n * > as if it normalized all line breaks in external parsed entities (including the document entity)\n * > on input, before parsing, by translating all of the following to a single #xA character:\n * >\n * > 1. the two-character sequence #xD #xA\n * > 2. the two-character sequence #xD #x85\n * > 3. the single character #x85\n * > 4. the single character #x2028\n * > 5. any #xD character that is not immediately followed by #xA or #x85.\n *\n * @param {string} input\n * @returns {string}\n */\nfunction normalizeLineEndings(input) {\n\treturn input\n\t\t.replace(/\\r[\\n\\u0085]/g, '\\n')\n\t\t.replace(/[\\r\\u0085\\u2028]/g, '\\n')\n}\n\n/**\n * @typedef Locator\n * @property {number} [columnNumber]\n * @property {number} [lineNumber]\n */\n\n/**\n * @typedef DOMParserOptions\n * @property {DOMHandler} [domBuilder]\n * @property {Function} [errorHandler]\n * @property {(string) => string} [normalizeLineEndings] used to replace line endings before parsing\n * \t\t\t\t\t\tdefaults to `normalizeLineEndings`\n * @property {Locator} [locator]\n * @property {Record<string, string>} [xmlns]\n *\n * @see normalizeLineEndings\n */\n\n/**\n * The DOMParser interface provides the ability to parse XML or HTML source code\n * from a string into a DOM `Document`.\n *\n * _xmldom is different from the spec in that it allows an `options` parameter,\n * to override the default behavior._\n *\n * @param {DOMParserOptions} [options]\n * @constructor\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMParser\n * @see https://html.spec.whatwg.org/multipage/dynamic-markup-insertion.html#dom-parsing-and-serialization\n */\nfunction DOMParser(options){\n\tthis.options = options ||{locator:{}};\n}\n\nDOMParser.prototype.parseFromString = function(source,mimeType){\n\tvar options = this.options;\n\tvar sax =  new XMLReader();\n\tvar domBuilder = options.domBuilder || new DOMHandler();//contentHandler and LexicalHandler\n\tvar errorHandler = options.errorHandler;\n\tvar locator = options.locator;\n\tvar defaultNSMap = options.xmlns||{};\n\tvar isHTML = /\\/x?html?$/.test(mimeType);//mimeType.toLowerCase().indexOf('html') > -1;\n  \tvar entityMap = isHTML ? entities.HTML_ENTITIES : entities.XML_ENTITIES;\n\tif(locator){\n\t\tdomBuilder.setDocumentLocator(locator)\n\t}\n\n\tsax.errorHandler = buildErrorHandler(errorHandler,domBuilder,locator);\n\tsax.domBuilder = options.domBuilder || domBuilder;\n\tif(isHTML){\n\t\tdefaultNSMap[''] = NAMESPACE.HTML;\n\t}\n\tdefaultNSMap.xml = defaultNSMap.xml || NAMESPACE.XML;\n\tvar normalize = options.normalizeLineEndings || normalizeLineEndings;\n\tif (source && typeof source === 'string') {\n\t\tsax.parse(\n\t\t\tnormalize(source),\n\t\t\tdefaultNSMap,\n\t\t\tentityMap\n\t\t)\n\t} else {\n\t\tsax.errorHandler.error('invalid doc source')\n\t}\n\treturn domBuilder.doc;\n}\nfunction buildErrorHandler(errorImpl,domBuilder,locator){\n\tif(!errorImpl){\n\t\tif(domBuilder instanceof DOMHandler){\n\t\t\treturn domBuilder;\n\t\t}\n\t\terrorImpl = domBuilder ;\n\t}\n\tvar errorHandler = {}\n\tvar isCallback = errorImpl instanceof Function;\n\tlocator = locator||{}\n\tfunction build(key){\n\t\tvar fn = errorImpl[key];\n\t\tif(!fn && isCallback){\n\t\t\tfn = errorImpl.length == 2?function(msg){errorImpl(key,msg)}:errorImpl;\n\t\t}\n\t\terrorHandler[key] = fn && function(msg){\n\t\t\tfn('[xmldom '+key+']\\t'+msg+_locator(locator));\n\t\t}||function(){};\n\t}\n\tbuild('warning');\n\tbuild('error');\n\tbuild('fatalError');\n\treturn errorHandler;\n}\n\n//console.log('#\\n\\n\\n\\n\\n\\n\\n####')\n/**\n * +ContentHandler+ErrorHandler\n * +LexicalHandler+EntityResolver2\n * -DeclHandler-DTDHandler\n *\n * DefaultHandler:EntityResolver, DTDHandler, ContentHandler, ErrorHandler\n * DefaultHandler2:DefaultHandler,LexicalHandler, DeclHandler, EntityResolver2\n * @link http://www.saxproject.org/apidoc/org/xml/sax/helpers/DefaultHandler.html\n */\nfunction DOMHandler() {\n    this.cdata = false;\n}\nfunction position(locator,node){\n\tnode.lineNumber = locator.lineNumber;\n\tnode.columnNumber = locator.columnNumber;\n}\n/**\n * @see org.xml.sax.ContentHandler#startDocument\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ContentHandler.html\n */\nDOMHandler.prototype = {\n\tstartDocument : function() {\n    \tthis.doc = new DOMImplementation().createDocument(null, null, null);\n    \tif (this.locator) {\n        \tthis.doc.documentURI = this.locator.systemId;\n    \t}\n\t},\n\tstartElement:function(namespaceURI, localName, qName, attrs) {\n\t\tvar doc = this.doc;\n\t    var el = doc.createElementNS(namespaceURI, qName||localName);\n\t    var len = attrs.length;\n\t    appendElement(this, el);\n\t    this.currentElement = el;\n\n\t\tthis.locator && position(this.locator,el)\n\t    for (var i = 0 ; i < len; i++) {\n\t        var namespaceURI = attrs.getURI(i);\n\t        var value = attrs.getValue(i);\n\t        var qName = attrs.getQName(i);\n\t\t\tvar attr = doc.createAttributeNS(namespaceURI, qName);\n\t\t\tthis.locator &&position(attrs.getLocator(i),attr);\n\t\t\tattr.value = attr.nodeValue = value;\n\t\t\tel.setAttributeNode(attr)\n\t    }\n\t},\n\tendElement:function(namespaceURI, localName, qName) {\n\t\tvar current = this.currentElement\n\t\tvar tagName = current.tagName;\n\t\tthis.currentElement = current.parentNode;\n\t},\n\tstartPrefixMapping:function(prefix, uri) {\n\t},\n\tendPrefixMapping:function(prefix) {\n\t},\n\tprocessingInstruction:function(target, data) {\n\t    var ins = this.doc.createProcessingInstruction(target, data);\n\t    this.locator && position(this.locator,ins)\n\t    appendElement(this, ins);\n\t},\n\tignorableWhitespace:function(ch, start, length) {\n\t},\n\tcharacters:function(chars, start, length) {\n\t\tchars = _toString.apply(this,arguments)\n\t\t//console.log(chars)\n\t\tif(chars){\n\t\t\tif (this.cdata) {\n\t\t\t\tvar charNode = this.doc.createCDATASection(chars);\n\t\t\t} else {\n\t\t\t\tvar charNode = this.doc.createTextNode(chars);\n\t\t\t}\n\t\t\tif(this.currentElement){\n\t\t\t\tthis.currentElement.appendChild(charNode);\n\t\t\t}else if(/^\\s*$/.test(chars)){\n\t\t\t\tthis.doc.appendChild(charNode);\n\t\t\t\t//process xml\n\t\t\t}\n\t\t\tthis.locator && position(this.locator,charNode)\n\t\t}\n\t},\n\tskippedEntity:function(name) {\n\t},\n\tendDocument:function() {\n\t\tthis.doc.normalize();\n\t},\n\tsetDocumentLocator:function (locator) {\n\t    if(this.locator = locator){// && !('lineNumber' in locator)){\n\t    \tlocator.lineNumber = 0;\n\t    }\n\t},\n\t//LexicalHandler\n\tcomment:function(chars, start, length) {\n\t\tchars = _toString.apply(this,arguments)\n\t    var comm = this.doc.createComment(chars);\n\t    this.locator && position(this.locator,comm)\n\t    appendElement(this, comm);\n\t},\n\n\tstartCDATA:function() {\n\t    //used in characters() methods\n\t    this.cdata = true;\n\t},\n\tendCDATA:function() {\n\t    this.cdata = false;\n\t},\n\n\tstartDTD:function(name, publicId, systemId) {\n\t\tvar impl = this.doc.implementation;\n\t    if (impl && impl.createDocumentType) {\n\t        var dt = impl.createDocumentType(name, publicId, systemId);\n\t        this.locator && position(this.locator,dt)\n\t        appendElement(this, dt);\n\t\t\t\t\tthis.doc.doctype = dt;\n\t    }\n\t},\n\t/**\n\t * @see org.xml.sax.ErrorHandler\n\t * @link http://www.saxproject.org/apidoc/org/xml/sax/ErrorHandler.html\n\t */\n\twarning:function(error) {\n\t\tconsole.warn('[xmldom warning]\\t'+error,_locator(this.locator));\n\t},\n\terror:function(error) {\n\t\tconsole.error('[xmldom error]\\t'+error,_locator(this.locator));\n\t},\n\tfatalError:function(error) {\n\t\tthrow new ParseError(error, this.locator);\n\t}\n}\nfunction _locator(l){\n\tif(l){\n\t\treturn '\\n@'+(l.systemId ||'')+'#[line:'+l.lineNumber+',col:'+l.columnNumber+']'\n\t}\n}\nfunction _toString(chars,start,length){\n\tif(typeof chars == 'string'){\n\t\treturn chars.substr(start,length)\n\t}else{//java sax connect width xmldom on rhino(what about: \"? && !(chars instanceof String)\")\n\t\tif(chars.length >= start+length || start){\n\t\t\treturn new java.lang.String(chars,start,length)+'';\n\t\t}\n\t\treturn chars;\n\t}\n}\n\n/*\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ext/LexicalHandler.html\n * used method of org.xml.sax.ext.LexicalHandler:\n *  #comment(chars, start, length)\n *  #startCDATA()\n *  #endCDATA()\n *  #startDTD(name, publicId, systemId)\n *\n *\n * IGNORED method of org.xml.sax.ext.LexicalHandler:\n *  #endDTD()\n *  #startEntity(name)\n *  #endEntity(name)\n *\n *\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ext/DeclHandler.html\n * IGNORED method of org.xml.sax.ext.DeclHandler\n * \t#attributeDecl(eName, aName, type, mode, value)\n *  #elementDecl(name, model)\n *  #externalEntityDecl(name, publicId, systemId)\n *  #internalEntityDecl(name, value)\n * @link http://www.saxproject.org/apidoc/org/xml/sax/ext/EntityResolver2.html\n * IGNORED method of org.xml.sax.EntityResolver2\n *  #resolveEntity(String name,String publicId,String baseURI,String systemId)\n *  #resolveEntity(publicId, systemId)\n *  #getExternalSubset(name, baseURI)\n * @link http://www.saxproject.org/apidoc/org/xml/sax/DTDHandler.html\n * IGNORED method of org.xml.sax.DTDHandler\n *  #notationDecl(name, publicId, systemId) {};\n *  #unparsedEntityDecl(name, publicId, systemId, notationName) {};\n */\n\"endDTD,startEntity,endEntity,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,resolveEntity,getExternalSubset,notationDecl,unparsedEntityDecl\".replace(/\\w+/g,function(key){\n\tDOMHandler.prototype[key] = function(){return null}\n})\n\n/* Private static helpers treated below as private instance methods, so don't need to add these to the public API; we might use a Relator to also get rid of non-standard public properties */\nfunction appendElement (hander,node) {\n    if (!hander.currentElement) {\n        hander.doc.appendChild(node);\n    } else {\n        hander.currentElement.appendChild(node);\n    }\n}//appendChild and setAttributeNS are preformance key\n\nexports.__DOMHandler = DOMHandler;\nexports.normalizeLineEndings = normalizeLineEndings;\nexports.DOMParser = DOMParser;\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,eAAe,CAAC;AAC1C,IAAIC,GAAG,GAAGD,OAAO,CAAC,OAAO,CAAC;AAC1B,IAAIE,QAAQ,GAAGF,OAAO,CAAC,YAAY,CAAC;AACpC,IAAIG,GAAG,GAAGH,OAAO,CAAC,OAAO,CAAC;AAE1B,IAAII,iBAAiB,GAAGH,GAAG,CAACG,iBAAiB;AAE7C,IAAIC,SAAS,GAAGN,WAAW,CAACM,SAAS;AAErC,IAAIC,UAAU,GAAGH,GAAG,CAACG,UAAU;AAC/B,IAAIC,SAAS,GAAGJ,GAAG,CAACI,SAAS;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACC,KAAK,EAAE;EACpC,OAAOA,KAAK,CACVC,OAAO,CAAC,eAAe,EAAE,IAAI,CAAC,CAC9BA,OAAO,CAAC,mBAAmB,EAAE,IAAI,CAAC;AACrC;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,OAAO,EAAC;EAC1B,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAG;IAACC,OAAO,EAAC,CAAC;EAAC,CAAC;AACtC;AAEAF,SAAS,CAACG,SAAS,CAACC,eAAe,GAAG,UAASC,MAAM,EAACC,QAAQ,EAAC;EAC9D,IAAIL,OAAO,GAAG,IAAI,CAACA,OAAO;EAC1B,IAAIT,GAAG,GAAI,IAAII,SAAS,CAAC,CAAC;EAC1B,IAAIW,UAAU,GAAGN,OAAO,CAACM,UAAU,IAAI,IAAIC,UAAU,CAAC,CAAC,CAAC;EACxD,IAAIC,YAAY,GAAGR,OAAO,CAACQ,YAAY;EACvC,IAAIP,OAAO,GAAGD,OAAO,CAACC,OAAO;EAC7B,IAAIQ,YAAY,GAAGT,OAAO,CAACU,KAAK,IAAE,CAAC,CAAC;EACpC,IAAIC,MAAM,GAAG,YAAY,CAACC,IAAI,CAACP,QAAQ,CAAC,CAAC;EACvC,IAAIQ,SAAS,GAAGF,MAAM,GAAGrB,QAAQ,CAACwB,aAAa,GAAGxB,QAAQ,CAACyB,YAAY;EACzE,IAAGd,OAAO,EAAC;IACVK,UAAU,CAACU,kBAAkB,CAACf,OAAO,CAAC;EACvC;EAEAV,GAAG,CAACiB,YAAY,GAAGS,iBAAiB,CAACT,YAAY,EAACF,UAAU,EAACL,OAAO,CAAC;EACrEV,GAAG,CAACe,UAAU,GAAGN,OAAO,CAACM,UAAU,IAAIA,UAAU;EACjD,IAAGK,MAAM,EAAC;IACTF,YAAY,CAAC,EAAE,CAAC,GAAGhB,SAAS,CAACyB,IAAI;EAClC;EACAT,YAAY,CAACU,GAAG,GAAGV,YAAY,CAACU,GAAG,IAAI1B,SAAS,CAAC2B,GAAG;EACpD,IAAIC,SAAS,GAAGrB,OAAO,CAACJ,oBAAoB,IAAIA,oBAAoB;EACpE,IAAIQ,MAAM,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IACzCb,GAAG,CAAC+B,KAAK,CACRD,SAAS,CAACjB,MAAM,CAAC,EACjBK,YAAY,EACZI,SACD,CAAC;EACF,CAAC,MAAM;IACNtB,GAAG,CAACiB,YAAY,CAACe,KAAK,CAAC,oBAAoB,CAAC;EAC7C;EACA,OAAOjB,UAAU,CAACkB,GAAG;AACtB,CAAC;AACD,SAASP,iBAAiBA,CAACQ,SAAS,EAACnB,UAAU,EAACL,OAAO,EAAC;EACvD,IAAG,CAACwB,SAAS,EAAC;IACb,IAAGnB,UAAU,YAAYC,UAAU,EAAC;MACnC,OAAOD,UAAU;IAClB;IACAmB,SAAS,GAAGnB,UAAU;EACvB;EACA,IAAIE,YAAY,GAAG,CAAC,CAAC;EACrB,IAAIkB,UAAU,GAAGD,SAAS,YAAYE,QAAQ;EAC9C1B,OAAO,GAAGA,OAAO,IAAE,CAAC,CAAC;EACrB,SAAS2B,KAAKA,CAACC,GAAG,EAAC;IAClB,IAAIC,EAAE,GAAGL,SAAS,CAACI,GAAG,CAAC;IACvB,IAAG,CAACC,EAAE,IAAIJ,UAAU,EAAC;MACpBI,EAAE,GAAGL,SAAS,CAACM,MAAM,IAAI,CAAC,GAAC,UAASC,GAAG,EAAC;QAACP,SAAS,CAACI,GAAG,EAACG,GAAG,CAAC;MAAA,CAAC,GAACP,SAAS;IACvE;IACAjB,YAAY,CAACqB,GAAG,CAAC,GAAGC,EAAE,IAAI,UAASE,GAAG,EAAC;MACtCF,EAAE,CAAC,UAAU,GAACD,GAAG,GAAC,KAAK,GAACG,GAAG,GAACC,QAAQ,CAAChC,OAAO,CAAC,CAAC;IAC/C,CAAC,IAAE,YAAU,CAAC,CAAC;EAChB;EACA2B,KAAK,CAAC,SAAS,CAAC;EAChBA,KAAK,CAAC,OAAO,CAAC;EACdA,KAAK,CAAC,YAAY,CAAC;EACnB,OAAOpB,YAAY;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASD,UAAUA,CAAA,EAAG;EAClB,IAAI,CAAC2B,KAAK,GAAG,KAAK;AACtB;AACA,SAASC,QAAQA,CAAClC,OAAO,EAACmC,IAAI,EAAC;EAC9BA,IAAI,CAACC,UAAU,GAAGpC,OAAO,CAACoC,UAAU;EACpCD,IAAI,CAACE,YAAY,GAAGrC,OAAO,CAACqC,YAAY;AACzC;AACA;AACA;AACA;AACA;AACA/B,UAAU,CAACL,SAAS,GAAG;EACtBqC,aAAa,EAAG,SAAhBA,aAAaA,CAAA,EAAc;IACvB,IAAI,CAACf,GAAG,GAAG,IAAIhC,iBAAiB,CAAC,CAAC,CAACgD,cAAc,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IACnE,IAAI,IAAI,CAACvC,OAAO,EAAE;MACd,IAAI,CAACuB,GAAG,CAACiB,WAAW,GAAG,IAAI,CAACxC,OAAO,CAACyC,QAAQ;IAChD;EACJ,CAAC;EACDC,YAAY,EAAC,SAAbA,YAAYA,CAAUC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC5D,IAAIvB,GAAG,GAAG,IAAI,CAACA,GAAG;IACf,IAAIwB,EAAE,GAAGxB,GAAG,CAACyB,eAAe,CAACL,YAAY,EAAEE,KAAK,IAAED,SAAS,CAAC;IAC5D,IAAIK,GAAG,GAAGH,KAAK,CAAChB,MAAM;IACtBoB,aAAa,CAAC,IAAI,EAAEH,EAAE,CAAC;IACvB,IAAI,CAACI,cAAc,GAAGJ,EAAE;IAE3B,IAAI,CAAC/C,OAAO,IAAIkC,QAAQ,CAAC,IAAI,CAAClC,OAAO,EAAC+C,EAAE,CAAC;IACtC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAGA,CAAC,GAAGH,GAAG,EAAEG,CAAC,EAAE,EAAE;MAC3B,IAAIT,YAAY,GAAGG,KAAK,CAACO,MAAM,CAACD,CAAC,CAAC;MAClC,IAAIE,KAAK,GAAGR,KAAK,CAACS,QAAQ,CAACH,CAAC,CAAC;MAC7B,IAAIP,KAAK,GAAGC,KAAK,CAACU,QAAQ,CAACJ,CAAC,CAAC;MACnC,IAAIK,IAAI,GAAGlC,GAAG,CAACmC,iBAAiB,CAACf,YAAY,EAAEE,KAAK,CAAC;MACrD,IAAI,CAAC7C,OAAO,IAAGkC,QAAQ,CAACY,KAAK,CAACa,UAAU,CAACP,CAAC,CAAC,EAACK,IAAI,CAAC;MACjDA,IAAI,CAACH,KAAK,GAAGG,IAAI,CAACG,SAAS,GAAGN,KAAK;MACnCP,EAAE,CAACc,gBAAgB,CAACJ,IAAI,CAAC;IACvB;EACJ,CAAC;EACDK,UAAU,EAAC,SAAXA,UAAUA,CAAUnB,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAE;IACnD,IAAIkB,OAAO,GAAG,IAAI,CAACZ,cAAc;IACjC,IAAIa,OAAO,GAAGD,OAAO,CAACC,OAAO;IAC7B,IAAI,CAACb,cAAc,GAAGY,OAAO,CAACE,UAAU;EACzC,CAAC;EACDC,kBAAkB,EAAC,SAAnBA,kBAAkBA,CAAUC,MAAM,EAAEC,GAAG,EAAE,CACzC,CAAC;EACDC,gBAAgB,EAAC,SAAjBA,gBAAgBA,CAAUF,MAAM,EAAE,CAClC,CAAC;EACDG,qBAAqB,EAAC,SAAtBA,qBAAqBA,CAAUC,MAAM,EAAEC,IAAI,EAAE;IACzC,IAAIC,GAAG,GAAG,IAAI,CAAClD,GAAG,CAACmD,2BAA2B,CAACH,MAAM,EAAEC,IAAI,CAAC;IAC5D,IAAI,CAACxE,OAAO,IAAIkC,QAAQ,CAAC,IAAI,CAAClC,OAAO,EAACyE,GAAG,CAAC;IAC1CvB,aAAa,CAAC,IAAI,EAAEuB,GAAG,CAAC;EAC5B,CAAC;EACDE,mBAAmB,EAAC,SAApBA,mBAAmBA,CAAUC,EAAE,EAAEC,KAAK,EAAE/C,MAAM,EAAE,CAChD,CAAC;EACDgD,UAAU,EAAC,SAAXA,UAAUA,CAAUC,KAAK,EAAEF,KAAK,EAAE/C,MAAM,EAAE;IACzCiD,KAAK,GAAGC,SAAS,CAACC,KAAK,CAAC,IAAI,EAACC,SAAS,CAAC;IACvC;IACA,IAAGH,KAAK,EAAC;MACR,IAAI,IAAI,CAAC9C,KAAK,EAAE;QACf,IAAIkD,QAAQ,GAAG,IAAI,CAAC5D,GAAG,CAAC6D,kBAAkB,CAACL,KAAK,CAAC;MAClD,CAAC,MAAM;QACN,IAAII,QAAQ,GAAG,IAAI,CAAC5D,GAAG,CAAC8D,cAAc,CAACN,KAAK,CAAC;MAC9C;MACA,IAAG,IAAI,CAAC5B,cAAc,EAAC;QACtB,IAAI,CAACA,cAAc,CAACmC,WAAW,CAACH,QAAQ,CAAC;MAC1C,CAAC,MAAK,IAAG,OAAO,CAACxE,IAAI,CAACoE,KAAK,CAAC,EAAC;QAC5B,IAAI,CAACxD,GAAG,CAAC+D,WAAW,CAACH,QAAQ,CAAC;QAC9B;MACD;MACA,IAAI,CAACnF,OAAO,IAAIkC,QAAQ,CAAC,IAAI,CAAClC,OAAO,EAACmF,QAAQ,CAAC;IAChD;EACD,CAAC;EACDI,aAAa,EAAC,SAAdA,aAAaA,CAAUC,IAAI,EAAE,CAC7B,CAAC;EACDC,WAAW,EAAC,SAAZA,WAAWA,CAAA,EAAY;IACtB,IAAI,CAAClE,GAAG,CAACH,SAAS,CAAC,CAAC;EACrB,CAAC;EACDL,kBAAkB,EAAC,SAAnBA,kBAAkBA,CAAWf,OAAO,EAAE;IAClC,IAAG,IAAI,CAACA,OAAO,GAAGA,OAAO,EAAC;MAAC;MAC1BA,OAAO,CAACoC,UAAU,GAAG,CAAC;IACvB;EACJ,CAAC;EACD;EACAsD,OAAO,EAAC,SAARA,OAAOA,CAAUX,KAAK,EAAEF,KAAK,EAAE/C,MAAM,EAAE;IACtCiD,KAAK,GAAGC,SAAS,CAACC,KAAK,CAAC,IAAI,EAACC,SAAS,CAAC;IACpC,IAAIS,IAAI,GAAG,IAAI,CAACpE,GAAG,CAACqE,aAAa,CAACb,KAAK,CAAC;IACxC,IAAI,CAAC/E,OAAO,IAAIkC,QAAQ,CAAC,IAAI,CAAClC,OAAO,EAAC2F,IAAI,CAAC;IAC3CzC,aAAa,CAAC,IAAI,EAAEyC,IAAI,CAAC;EAC7B,CAAC;EAEDE,UAAU,EAAC,SAAXA,UAAUA,CAAA,EAAY;IAClB;IACA,IAAI,CAAC5D,KAAK,GAAG,IAAI;EACrB,CAAC;EACD6D,QAAQ,EAAC,SAATA,QAAQA,CAAA,EAAY;IAChB,IAAI,CAAC7D,KAAK,GAAG,KAAK;EACtB,CAAC;EAED8D,QAAQ,EAAC,SAATA,QAAQA,CAAUP,IAAI,EAAEQ,QAAQ,EAAEvD,QAAQ,EAAE;IAC3C,IAAIwD,IAAI,GAAG,IAAI,CAAC1E,GAAG,CAAC2E,cAAc;IAC/B,IAAID,IAAI,IAAIA,IAAI,CAACE,kBAAkB,EAAE;MACjC,IAAIC,EAAE,GAAGH,IAAI,CAACE,kBAAkB,CAACX,IAAI,EAAEQ,QAAQ,EAAEvD,QAAQ,CAAC;MAC1D,IAAI,CAACzC,OAAO,IAAIkC,QAAQ,CAAC,IAAI,CAAClC,OAAO,EAACoG,EAAE,CAAC;MACzClD,aAAa,CAAC,IAAI,EAAEkD,EAAE,CAAC;MAC3B,IAAI,CAAC7E,GAAG,CAAC8E,OAAO,GAAGD,EAAE;IACrB;EACJ,CAAC;EACD;AACD;AACA;AACA;EACCE,OAAO,EAAC,SAARA,OAAOA,CAAUhF,KAAK,EAAE;IACvBiF,OAAO,CAACC,IAAI,CAAC,oBAAoB,GAAClF,KAAK,EAACU,QAAQ,CAAC,IAAI,CAAChC,OAAO,CAAC,CAAC;EAChE,CAAC;EACDsB,KAAK,EAAC,SAANA,KAAKA,CAAUA,MAAK,EAAE;IACrBiF,OAAO,CAACjF,KAAK,CAAC,kBAAkB,GAACA,MAAK,EAACU,QAAQ,CAAC,IAAI,CAAChC,OAAO,CAAC,CAAC;EAC/D,CAAC;EACDyG,UAAU,EAAC,SAAXA,UAAUA,CAAUnF,KAAK,EAAE;IAC1B,MAAM,IAAI7B,UAAU,CAAC6B,KAAK,EAAE,IAAI,CAACtB,OAAO,CAAC;EAC1C;AACD,CAAC;AACD,SAASgC,QAAQA,CAAC0E,CAAC,EAAC;EACnB,IAAGA,CAAC,EAAC;IACJ,OAAO,KAAK,IAAEA,CAAC,CAACjE,QAAQ,IAAG,EAAE,CAAC,GAAC,SAAS,GAACiE,CAAC,CAACtE,UAAU,GAAC,OAAO,GAACsE,CAAC,CAACrE,YAAY,GAAC,GAAG;EACjF;AACD;AACA,SAAS2C,SAASA,CAACD,KAAK,EAACF,KAAK,EAAC/C,MAAM,EAAC;EACrC,IAAG,OAAOiD,KAAK,IAAI,QAAQ,EAAC;IAC3B,OAAOA,KAAK,CAAC4B,MAAM,CAAC9B,KAAK,EAAC/C,MAAM,CAAC;EAClC,CAAC,MAAI;IAAC;IACL,IAAGiD,KAAK,CAACjD,MAAM,IAAI+C,KAAK,GAAC/C,MAAM,IAAI+C,KAAK,EAAC;MACxC,OAAO,IAAI+B,IAAI,CAACC,IAAI,CAACC,MAAM,CAAC/B,KAAK,EAACF,KAAK,EAAC/C,MAAM,CAAC,GAAC,EAAE;IACnD;IACA,OAAOiD,KAAK;EACb;AACD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8JAA8J,CAAClF,OAAO,CAAC,MAAM,EAAC,UAAS+B,GAAG,EAAC;EAC1LtB,UAAU,CAACL,SAAS,CAAC2B,GAAG,CAAC,GAAG,YAAU;IAAC,OAAO,IAAI;EAAA,CAAC;AACpD,CAAC,CAAC;;AAEF;AACA,SAASsB,aAAaA,CAAE6D,MAAM,EAAC5E,IAAI,EAAE;EACjC,IAAI,CAAC4E,MAAM,CAAC5D,cAAc,EAAE;IACxB4D,MAAM,CAACxF,GAAG,CAAC+D,WAAW,CAACnD,IAAI,CAAC;EAChC,CAAC,MAAM;IACH4E,MAAM,CAAC5D,cAAc,CAACmC,WAAW,CAACnD,IAAI,CAAC;EAC3C;AACJ,CAAC;;AAED6E,OAAO,CAACC,YAAY,GAAG3G,UAAU;AACjC0G,OAAO,CAACrH,oBAAoB,GAAGA,oBAAoB;AACnDqH,OAAO,CAAClH,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}