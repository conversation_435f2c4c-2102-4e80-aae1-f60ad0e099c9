{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ElMessage } from 'element-plus';\nimport { openConfig } from 'common/js/system_var.js';\nimport config from \"common/config/index\";\nimport { ref, onMounted, nextTick, onUnmounted, computed } from 'vue';\nimport { fetchEventSource } from '@microsoft/fetch-event-source';\n// import { useStore } from 'vuex'\n// import { useRoute } from 'vue-router'\n// const route = useRoute()\n// const title = ref(route.query.title)\n// const details = ref({})\n\nvar __default__ = {\n  name: 'DouBaoIntelligentize'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    keyword: {\n      type: String,\n      default: ''\n    },\n    typeShow: {\n      type: Boolean,\n      default: ''\n    },\n    showMessage: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['checkOutFun'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    var props = __props;\n    var keyword = ref('');\n    var textareaRef = ref();\n    var answerLoadingUid = ref('');\n    var emit = __emit;\n    var answerLoading = ref(false);\n    var lock = ref(false);\n    var followUpLoadingUUid = ref('');\n    var tableData = ref([]);\n    var initShow = ref('0');\n    var scrollbarRef = ref();\n    var selectTool = ref('1');\n    var systemPlatform = computed(function () {\n      var _openConfig$value;\n      return ((_openConfig$value = openConfig.value) === null || _openConfig$value === void 0 ? void 0 : _openConfig$value.systemPlatform) || '';\n    });\n    var options = ref([{\n      value: '1',\n      label: 'coze'\n    }, {\n      value: '2',\n      label: 'DeepSeek(满血)'\n    }, {\n      value: '3',\n      label: 'DeepSeek(本地部署)'\n    }]);\n    var ctrl = ref(new AbortController());\n    onMounted(function () {\n      globalReadOpenConfig();\n    });\n    onUnmounted(function () {\n      // 退出页面执行 关闭链接\n    });\n    var guid = function guid() {\n      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n        var r = Math.random() * 16 | 0,\n          v = c == 'x' ? r : r & 0x3 | 0x8;\n        return v.toString(16);\n      });\n    };\n    var globalReadOpenConfig = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              // 没配置智能问答判断不调用接口\n              initShow.value = props.showMessage;\n              nextTick(function () {\n                getFetchEventSource();\n              });\n            case 2:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function globalReadOpenConfig() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    var getFetchEventSource = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var text,\n          signal,\n          token,\n          _yield$api$initMessag,\n          data,\n          uuid,\n          answerUUid,\n          followUpUUid,\n          _args2 = arguments;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              text = _args2.length > 0 && _args2[0] !== undefined ? _args2[0] : '';\n              ctrl.value.abort();\n              ctrl.value = new AbortController(); // 创建新的控制器\n              signal = ctrl.value.signal;\n              token = sessionStorage.getItem('token') || '';\n              if (!(lock.value == false && !props.typeShow && initShow.value == '1')) {\n                _context2.next = 13;\n                break;\n              }\n              tableData.value.push({\n                uid: answerUUid,\n                copyShow: false,\n                contentKey: '<span style=\"font-weight:bold;font-size:1.2em\">您好！我是您的小助手</span><br><span style=\"font-size:1em;color:#464646;\">您可以向我提出问题，我将为您做出内容推荐和解答。</span><br><span style=\"font-size:.9em;color:#a5a6a6;\">您可以试着问我：</span>',\n                children: [],\n                type: 'answer'\n              });\n              _context2.next = 9;\n              return api.initMessage({\n                bot_type: systemPlatform.value == 'NPC' ? '6' : '7'\n              });\n            case 9:\n              _yield$api$initMessag = _context2.sent;\n              data = _yield$api$initMessag.data;\n              tableData.value.push({\n                uid: followUpUUid,\n                copyShow: false,\n                contentKey: '',\n                children: data,\n                type: 'follow'\n              });\n              lock.value = true;\n            case 13:\n              uuid = guid();\n              answerUUid = guid();\n              followUpUUid = guid();\n              tableData.value.push({\n                uid: uuid,\n                contentKey: text,\n                contentKey1: '',\n                showKey1: true,\n                children: [],\n                type: 'title'\n              });\n              tableData.value.push({\n                uid: answerUUid,\n                contentKey: '',\n                children: [],\n                type: 'answer'\n              });\n              tableData.value.push({\n                uid: followUpUUid,\n                contentKey: '',\n                children: [],\n                type: 'follow'\n              });\n              answerLoadingUid.value = answerUUid;\n              followUpLoadingUUid.value = followUpUUid;\n              answerLoading.value = true;\n              tableData.value[tableData.value.length - 2].contentKey1 = '';\n              tableData.value[tableData.value.length - 2].showKey1 = true;\n              _context2.next = 26;\n              return fetchEventSource(`${config.API_URL}${selectTool.value == 1 ? '/chat/intelligentStream' : selectTool.value == 2 ? '/chat/interactiveQnAChat' : '/chat/interactiveQnAEndpoint'}`, {\n                method: 'POST',\n                headers: {\n                  'Content-Type': 'application/json',\n                  authorization: token\n                },\n                body: JSON.stringify({\n                  content: text,\n                  //  wordNumber: wordNumber.value || 200,\n                  bot_type: selectTool.value == 1 ? props.typeShow ? '5' : systemPlatform.value == 'NPC' ? '7' : '6' : ''\n                }),\n                openWhenHidden: true,\n                // 取消visibilityChange事件\n                signal,\n                onmessage: function onmessage(res) {\n                  if (selectTool.value == 2 || selectTool.value == 3) {\n                    if (JSON.parse(res.data).think && JSON.parse(res.data).think != '') {\n                      var _JSON$parse;\n                      tableData.value[tableData.value.length - 2].contentKey1 += ((_JSON$parse = JSON.parse(res.data)) === null || _JSON$parse === void 0 ? void 0 : _JSON$parse.think) || '';\n                    } else {\n                      var _JSON$parse2;\n                      tableData.value[tableData.value.length - 2].contentKey += ((_JSON$parse2 = JSON.parse(res.data)) === null || _JSON$parse2 === void 0 ? void 0 : _JSON$parse2.response) || '';\n                      tableData.value[tableData.value.length - 2].contentKey = tableData.value[tableData.value.length - 2].contentKey.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>');\n                      tableData.value[tableData.value.length - 2].copyShow = true;\n                    }\n                    nextTick(function () {\n                      scrollDown();\n                    });\n                  } else {\n                    var resData = JSON.parse(res.data);\n                    if (['conversation.message.delta'].includes(resData.event) && ['answer'].includes(resData.data.type)) {\n                      if (answerLoadingUid.value === answerUUid) {\n                        var _JSON$parse3;\n                        tableData.value[tableData.value.length - 2].contentKey += ((_JSON$parse3 = JSON.parse(res.data)) === null || _JSON$parse3 === void 0 || (_JSON$parse3 = _JSON$parse3.data) === null || _JSON$parse3 === void 0 ? void 0 : _JSON$parse3.content) || '';\n                        tableData.value[tableData.value.length - 2].contentKey = tableData.value[tableData.value.length - 2].contentKey.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>');\n                      }\n                    } else if (['conversation.message.completed'].includes(resData.event) && ['follow_up'].includes(resData.data.type)) {\n                      if (followUpLoadingUUid.value === followUpUUid) {\n                        var _JSON$parse4;\n                        tableData.value[tableData.value.length - 1].children.push(((_JSON$parse4 = JSON.parse(res.data)) === null || _JSON$parse4 === void 0 || (_JSON$parse4 = _JSON$parse4.data) === null || _JSON$parse4 === void 0 ? void 0 : _JSON$parse4.content) || '');\n                      }\n                    }\n                    nextTick(function () {\n                      scrollDown();\n                    });\n                  }\n                },\n                onclose: function onclose(data) {\n                  var chineseRegex = /[\\u4e00-\\u9fff]/;\n                  var showKey1 = chineseRegex.test(tableData.value[tableData.value.length - 2].contentKey1);\n                  tableData.value[tableData.value.length - 2].showKey1 = showKey1;\n                  answerLoading.value = false;\n                },\n                onerror: function onerror(err) {\n                  tableData.value.push({\n                    uid: answerUUid,\n                    contentKey: '系统繁忙。。。',\n                    children: [],\n                    type: 'answer'\n                  });\n                  answerLoading.value = false;\n                  throw err;\n                }\n              });\n            case 26:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function getFetchEventSource() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n    var copyButtonFun = /*#__PURE__*/function () {\n      var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3(text) {\n        var originalText2, originalText, el;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              _context3.prev = 0;\n              originalText2 = text.contentKey.replace(/<\\/?think>/g, '');\n              originalText = originalText2.replace(/<\\/?strong>/g, '');\n              _context3.next = 5;\n              return navigator.clipboard.writeText(originalText);\n            case 5:\n              ElMessage({\n                message: '复制成功',\n                type: 'success'\n              });\n              _context3.next = 20;\n              break;\n            case 8:\n              _context3.prev = 8;\n              _context3.t0 = _context3[\"catch\"](0);\n              // 作为备选方案，可以使用临时元素来复制文本\n              el = document.createElement('textarea');\n              el.value = text.contentKey;\n              el.setAttribute('readonly', '');\n              el.style.position = 'absolute';\n              el.style.left = '-9999px';\n              document.body.appendChild(el);\n              el.select();\n              document.execCommand('copy');\n              document.body.removeChild(el);\n              ElMessage({\n                message: '复制成功',\n                type: 'success'\n              });\n            case 20:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3, null, [[0, 8]]);\n      }));\n      return function copyButtonFun(_x) {\n        return _ref4.apply(this, arguments);\n      };\n    }();\n    var clearTableData = function clearTableData() {\n      tableData.value = [];\n    };\n    var checkOut = function checkOut() {\n      emit('checkOutFun');\n    };\n\n    // const chatIntelligentAnswer = async (text = '') => {\n    //   const { data, code } = await api.chatIntelligentAnswer({\n    //     content: text,\n    //     //  wordNumber: wordNumber.value || 200,\n    //     bot_type: '5'\n    //   })\n    //   tableData.value.push({ type: 'answer', contentKey: '', uid: guid(), children: data?.filter(v => ['answer'].includes(v.type))?.map(v => ({ ...v, uid: guid() })) || [] })\n    //   tableData.value.push({ type: 'follow', contentKey: '', uid: guid(), children: data?.filter(v => ['follow'].includes(v.type))?.map(v => ({ ...v, uid: guid() })) || [] })\n    //   nextTick(() => { scrollDown() })\n    // }\n    var scrollDown = /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4() {\n        var wrap;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              wrap = scrollbarRef.value.wrapRef;\n              wrap.scrollTop = wrap.scrollHeight;\n              // 等待渲染更新\n              _context4.next = 4;\n              return nextTick();\n            case 4:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4);\n      }));\n      return function scrollDown() {\n        return _ref5.apply(this, arguments);\n      };\n    }();\n    var handleQuery = function handleQuery() {\n      if (keyword.value == '') return;\n      answerLoading.value = false;\n      ctrl.value.abort();\n      ctrl.value = new AbortController(); // 重置以备下次使用\n      if (keyword.value) {\n        textClick(keyword.value);\n      }\n    };\n    var textClick = function textClick() {\n      var text = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';\n      keyword.value = '';\n      ctrl.value.abort();\n      nextTick(function () {\n        scrollDown();\n      });\n      getFetchEventSource(text);\n    };\n    __expose({\n      handleQuery,\n      textClick\n    });\n    var __returned__ = {\n      props,\n      keyword,\n      textareaRef,\n      answerLoadingUid,\n      emit,\n      answerLoading,\n      lock,\n      followUpLoadingUUid,\n      tableData,\n      initShow,\n      scrollbarRef,\n      selectTool,\n      systemPlatform,\n      options,\n      get ctrl() {\n        return ctrl;\n      },\n      set ctrl(v) {\n        ctrl = v;\n      },\n      guid,\n      globalReadOpenConfig,\n      getFetchEventSource,\n      copyButtonFun,\n      clearTableData,\n      checkOut,\n      scrollDown,\n      handleQuery,\n      textClick,\n      get api() {\n        return api;\n      },\n      get ElMessage() {\n        return ElMessage;\n      },\n      get openConfig() {\n        return openConfig;\n      },\n      get config() {\n        return config;\n      },\n      ref,\n      onMounted,\n      nextTick,\n      onUnmounted,\n      computed,\n      get fetchEventSource() {\n        return fetchEventSource;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ElMessage", "openConfig", "config", "ref", "onMounted", "nextTick", "onUnmounted", "computed", "fetchEventSource", "__default__", "props", "__props", "keyword", "textareaRef", "answerLoadingUid", "emit", "__emit", "answerLoading", "lock", "followUpLoadingUUid", "tableData", "initShow", "scrollbarRef", "selectTool", "systemPlatform", "_openConfig$value", "options", "label", "ctrl", "AbortController", "globalReadOpenConfig", "guid", "replace", "Math", "random", "toString", "_ref2", "_callee", "_callee$", "_context", "showMessage", "getFetchEventSource", "_ref3", "_callee2", "text", "signal", "token", "_yield$api$initMessag", "data", "uuid", "answerUUid", "followUpUUid", "_args2", "_callee2$", "_context2", "undefined", "abort", "sessionStorage", "getItem", "typeShow", "uid", "copyShow", "contentKey", "children", "initMessage", "bot_type", "contentKey1", "showKey1", "API_URL", "headers", "authorization", "body", "JSON", "stringify", "content", "openWhenHidden", "onmessage", "res", "parse", "think", "_JSON$parse", "_JSON$parse2", "response", "scrollDown", "resData", "includes", "event", "_JSON$parse3", "_JSON$parse4", "onclose", "chineseRegex", "test", "onerror", "err", "copyButtonFun", "_ref4", "_callee3", "originalText2", "originalText", "el", "_callee3$", "_context3", "navigator", "clipboard", "writeText", "message", "t0", "document", "createElement", "setAttribute", "style", "position", "left", "append<PERSON><PERSON><PERSON>", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "_x", "clearTableData", "checkOut", "_ref5", "_callee4", "_callee4$", "_context4", "wrapRef", "scrollTop", "scrollHeight", "handleQuery", "textClick", "__expose"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/Intelligentize/Intelligentize/DouBaoIntelligentize.vue"], "sourcesContent": ["<template>\r\n  <div class=\"DouBaoIntelligentize card\" :style=\"`${!typeShow ? 'border-radius: 0;' : ''}`\">\r\n    <div class=\"DouBao-intelligent-assistant\" v-if=\"typeShow\">\r\n    </div>\r\n\r\n    <div class=\"DouBaoIntelligentizeBody\">\r\n      <div class=\"DouBaoIntelligentizeBodyTop\" v-if=\"!typeShow\">\r\n        <div class=\"DouBaoIntelligentizeBodyLeft\">\r\n          <img src=\"../../img/intelligence.gif\" alt=\"\">\r\n          <div class=\"LeftText\">智能问答</div>\r\n          <el-select v-model=\"selectTool\" class=\"LeftSelect\" placeholder=\"请选择\">\r\n            <el-option v-for=\"item in options\" :key=\"item.value\" :label=\"item.label\" :value=\"item.value\">\r\n            </el-option>\r\n          </el-select>\r\n        </div>\r\n        <div class=\"DouBaoIntelligentizeBodyRight\">\r\n          <img @click=\"clearTableData\" class=\"RightImg\" src=\"../../img/delectMessage.png\" alt=\"\">\r\n          <img @click=\"checkOut\" src=\"../../img/delect.png\" alt=\"\">\r\n        </div>\r\n      </div>\r\n      <el-scrollbar ref=\"scrollbarRef\">\r\n        <div class=\"tableModule\" v-for=\"item in tableData\" :key=\"item.uid\">\r\n          <template v-if=\"item.type === 'title'\">\r\n            <div class=\"tableModuleContentKey\" v-if=\"item.contentKey\">\r\n              <div class=\"tableModuleContentKeyWord\">{{ item.contentKey }}</div>\r\n            </div>\r\n          </template>\r\n          <template v-if=\"item.type === 'answer'\">\r\n            <div class=\"tableModuleContentAnswer\">\r\n              <div class=\"tableModuleItem\">\r\n                <div class=\"tableModuleItemContent\" v-if=\"selectTool == 2 || selectTool == 3 && item.showKey1\">\r\n                  <span v-if=\"answerLoading && (answerLoadingUid === item.uid)\">深度思考中</span>\r\n                  <div class=\"loader3\" v-if=\"answerLoading && (answerLoadingUid === item.uid)\">\r\n                    <div class=\"circle1\"></div>\r\n                    <div class=\"circle1\"></div>\r\n                    <div class=\"circle1\"></div>\r\n                  </div>\r\n                  <span class=\"ContentText\" v-html=\"item.contentKey1\"></span>\r\n                </div>\r\n                <span class=\"ContentTextTow\" v-html=\"item.contentKey\"></span>\r\n                <div class=\"loader3\" v-if=\"answerLoading && (answerLoadingUid === item.uid)\">\r\n                  <div class=\"circle1\"></div>\r\n                  <div class=\"circle1\"></div>\r\n                  <div class=\"circle1\"></div>\r\n                </div>\r\n                <template v-if=\"selectTool == 2 || selectTool == 3\">\r\n                  <button class=\"copyButton\" @click=\"copyButtonFun(item)\" v-if=\"item.copyShow && !answerLoading\">\r\n                    <el-icon>\r\n                      <Document />\r\n                    </el-icon>\r\n                    复制\r\n                  </button>\r\n                </template>\r\n              </div>\r\n              <!-- <ul class=\"tableModuleContentAnswer\"\r\n                v-if=\"item.contentKey\">\r\n              <li class=\"tableModuleItem\">{{ item.contentKey }}</li>\r\n            </ul> -->\r\n            </div>\r\n            <!-- <ul class=\"tableModuleContentAnswer\"\r\n                v-if=\"item.children?.length\">\r\n              <li v-for=\"children in item.children\"\r\n                  :key=\"children.uid\"\r\n                  class=\"tableModuleItem\">\r\n                {{ children.output }}\r\n              </li>\r\n            </ul> -->\r\n          </template>\r\n          <template v-if=\"item.type === 'answerMin'\">\r\n            <div class=\"tableModuleContentAnswer\">\r\n              <div class=\"tableModuleItem\">\r\n                <span v-html=\"item.contentKey\"></span>\r\n                <div class=\"loader3\" v-if=\"answerLoading && (answerLoadingUid === item.uid)\">\r\n                  <div class=\"circle1\"></div>\r\n                  <div class=\"circle1\"></div>\r\n                  <div class=\"circle1\"></div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </template>\r\n          <template v-if=\"item.type === 'follow'\">\r\n            <div class=\"tableModuleContentFollow\" v-if=\"item.children?.length\">\r\n              <div class=\"tableModuleContentFollowWord\" v-for=\"children in item.children\" :key=\"children\"\r\n                @click=\"textClick(children)\">\r\n                <div v-if=\"!typeShow\" class=\"wordShow\">\r\n                  #\r\n                </div>\r\n                {{ children }}\r\n                <el-icon>\r\n                  <Right />\r\n                </el-icon>\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </div>\r\n      </el-scrollbar>\r\n    </div>\r\n    <div class=\"DouBaoIntelligentizeSeach\">\r\n      <el-input v-model=\"keyword\" v-if=\"typeShow\" ref=\"textareaRef\" type=\"textarea\"\r\n        :autosize=\"{ minRows: 1, maxRows: 6 }\" @keyup.enter=\"handleQuery\" resize=\"none\" />\r\n      <el-input v-model=\"keyword\" ref=\"textareaRef\" v-if=\"!typeShow\" type=\"textarea\" maxlength=\"500\"\r\n        placeholder=\"请输入你的问题\" :rows=\"3\" @keyup.enter=\"handleQuery\" resize=\"none\" />\r\n      <el-button type=\"primary\" v-if=\"typeShow\" class=\"DouBaoIntelligentizeButton\" icon=\"Position\" circle\r\n        @click=\"handleQuery()\"></el-button>\r\n      <el-button type=\"primary\" v-if=\"!typeShow\" class=\"sendingButton\" circle @click=\"handleQuery()\"> <img\r\n          src=\"../../img/sending.png\" alt=\"\"> 发送</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'DouBaoIntelligentize' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ElMessage } from 'element-plus'\r\nimport { openConfig } from 'common/js/system_var.js'\r\nimport config from \"common/config/index\"\r\nimport { ref, onMounted, nextTick, onUnmounted, defineEmits, computed } from 'vue'\r\nimport { fetchEventSource } from '@microsoft/fetch-event-source'\r\n// import { useStore } from 'vuex'\r\n// import { useRoute } from 'vue-router'\r\n// const route = useRoute()\r\n// const title = ref(route.query.title)\r\n// const details = ref({})\r\nconst props = defineProps({ keyword: { type: String, default: '' }, typeShow: { type: Boolean, default: '' }, showMessage: { type: String, default: '' } })\r\nconst keyword = ref('')\r\nconst textareaRef = ref()\r\nconst answerLoadingUid = ref('')\r\nconst emit = defineEmits(['checkOutFun'])\r\nconst answerLoading = ref(false)\r\nconst lock = ref(false)\r\nconst followUpLoadingUUid = ref('')\r\nconst tableData = ref([])\r\nconst initShow = ref('0')\r\nconst scrollbarRef = ref()\r\nconst selectTool = ref('1')\r\nconst systemPlatform = computed(() => openConfig.value?.systemPlatform || '')\r\nconst options = ref([{\r\n  value: '1',\r\n  label: 'coze'\r\n}, {\r\n  value: '2',\r\n  label: 'DeepSeek(满血)'\r\n}, {\r\n  value: '3',\r\n  label: 'DeepSeek(本地部署)'\r\n}]\r\n)\r\nvar ctrl = ref(new AbortController())\r\nonMounted(() => {\r\n  globalReadOpenConfig()\r\n})\r\nonUnmounted(() => {\r\n  // 退出页面执行 关闭链接\r\n})\r\nconst guid = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8)\r\n    return v.toString(16)\r\n  })\r\n}\r\nconst globalReadOpenConfig = async () => {  // 没配置智能问答判断不调用接口\r\n  initShow.value = props.showMessage\r\n\r\n  nextTick(() => {\r\n    getFetchEventSource()\r\n  })\r\n}\r\nconst getFetchEventSource = async (text = '') => {\r\n  ctrl.value.abort()\r\n  ctrl.value = new AbortController(); // 创建新的控制器\r\n  const { signal } = ctrl.value;\r\n  const token = sessionStorage.getItem('token') || ''\r\n  if (lock.value == false && !props.typeShow && initShow.value == '1') {\r\n    tableData.value.push({ uid: answerUUid, copyShow: false, contentKey: '<span style=\"font-weight:bold;font-size:1.2em\">您好！我是您的小助手</span><br><span style=\"font-size:1em;color:#464646;\">您可以向我提出问题，我将为您做出内容推荐和解答。</span><br><span style=\"font-size:.9em;color:#a5a6a6;\">您可以试着问我：</span>', children: [], type: 'answer' })\r\n    const { data } = await api.initMessage({ bot_type: systemPlatform.value == 'NPC' ? '6' : '7' })\r\n    tableData.value.push({ uid: followUpUUid, copyShow: false, contentKey: '', children: data, type: 'follow' })\r\n    lock.value = true\r\n  }\r\n  var uuid = guid()\r\n  var answerUUid = guid()\r\n  var followUpUUid = guid()\r\n  tableData.value.push({ uid: uuid, contentKey: text, contentKey1: '', showKey1: true, children: [], type: 'title' })\r\n  tableData.value.push({ uid: answerUUid, contentKey: '', children: [], type: 'answer' })\r\n  tableData.value.push({ uid: followUpUUid, contentKey: '', children: [], type: 'follow' })\r\n  answerLoadingUid.value = answerUUid\r\n  followUpLoadingUUid.value = followUpUUid\r\n  answerLoading.value = true\r\n  tableData.value[tableData.value.length - 2].contentKey1 = ''\r\n  tableData.value[tableData.value.length - 2].showKey1 = true\r\n  await fetchEventSource(`${config.API_URL}${selectTool.value == 1 ? '/chat/intelligentStream' : selectTool.value == 2 ? '/chat/interactiveQnAChat' : '/chat/interactiveQnAEndpoint'}`, {\r\n    method: 'POST',\r\n    headers: {\r\n      'Content-Type': 'application/json',\r\n      authorization: token,\r\n    },\r\n    body: JSON.stringify({\r\n      content: text,\r\n      //  wordNumber: wordNumber.value || 200,\r\n      bot_type: selectTool.value == 1 ? props.typeShow ? '5' : systemPlatform.value == 'NPC' ? '7' : '6' : ''\r\n    }),\r\n    openWhenHidden: true, // 取消visibilityChange事件\r\n    signal,\r\n    onmessage: (res) => {\r\n      if (selectTool.value == 2 || selectTool.value == 3) {\r\n        if (JSON.parse(res.data).think && JSON.parse(res.data).think != '') {\r\n          tableData.value[tableData.value.length - 2].contentKey1 += (JSON.parse(res.data)?.think || '')\r\n        } else {\r\n          tableData.value[tableData.value.length - 2].contentKey += JSON.parse(res.data)?.response || ''\r\n          tableData.value[tableData.value.length - 2].contentKey = tableData.value[tableData.value.length - 2].contentKey.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\r\n          tableData.value[tableData.value.length - 2].copyShow = true\r\n        }\r\n        nextTick(() => {\r\n          scrollDown()\r\n        })\r\n      } else {\r\n        var resData = JSON.parse(res.data)\r\n        if (['conversation.message.delta'].includes(resData.event) && ['answer'].includes(resData.data.type)) {\r\n          if (answerLoadingUid.value === answerUUid) {\r\n            tableData.value[tableData.value.length - 2].contentKey += JSON.parse(res.data)?.data?.content || ''\r\n            tableData.value[tableData.value.length - 2].contentKey = tableData.value[tableData.value.length - 2].contentKey.replace(/\\*\\*(.*?)\\*\\*/g, '<strong>$1</strong>')\r\n          }\r\n        } else if (['conversation.message.completed'].includes(resData.event) && ['follow_up'].includes(resData.data.type)) {\r\n          if (followUpLoadingUUid.value === followUpUUid) {\r\n            tableData.value[tableData.value.length - 1].children.push(JSON.parse(res.data)?.data?.content || '')\r\n          }\r\n        }\r\n        nextTick(() => { scrollDown() })\r\n      }\r\n    },\r\n    onclose: (data) => {\r\n      const chineseRegex = /[\\u4e00-\\u9fff]/;\r\n      let showKey1 = chineseRegex.test(tableData.value[tableData.value.length - 2].contentKey1)\r\n      tableData.value[tableData.value.length - 2].showKey1 = showKey1\r\n      answerLoading.value = false\r\n    },\r\n    onerror: (err) => {\r\n      tableData.value.push({ uid: answerUUid, contentKey: '系统繁忙。。。', children: [], type: 'answer' })\r\n      answerLoading.value = false\r\n      throw err\r\n    },\r\n  })\r\n}\r\nconst copyButtonFun = async (text) => {\r\n  try {\r\n    const originalText2 = text.contentKey.replace(/<\\/?think>/g, '')\r\n    const originalText = originalText2.replace(/<\\/?strong>/g, '')\r\n    await navigator.clipboard.writeText(originalText);\r\n    ElMessage({\r\n      message: '复制成功',\r\n      type: 'success',\r\n    })\r\n  } catch (err) {\r\n    // 作为备选方案，可以使用临时元素来复制文本\r\n    const el = document.createElement('textarea');\r\n    el.value = text.contentKey;\r\n    el.setAttribute('readonly', '');\r\n    el.style.position = 'absolute';\r\n    el.style.left = '-9999px';\r\n    document.body.appendChild(el);\r\n    el.select();\r\n    document.execCommand('copy');\r\n    document.body.removeChild(el);\r\n    ElMessage({\r\n      message: '复制成功',\r\n      type: 'success',\r\n    })\r\n  }\r\n}\r\nconst clearTableData = () => {\r\n  tableData.value = []\r\n}\r\nconst checkOut = () => {\r\n  emit('checkOutFun')\r\n}\r\n\r\n// const chatIntelligentAnswer = async (text = '') => {\r\n//   const { data, code } = await api.chatIntelligentAnswer({\r\n//     content: text,\r\n//     //  wordNumber: wordNumber.value || 200,\r\n//     bot_type: '5'\r\n//   })\r\n//   tableData.value.push({ type: 'answer', contentKey: '', uid: guid(), children: data?.filter(v => ['answer'].includes(v.type))?.map(v => ({ ...v, uid: guid() })) || [] })\r\n//   tableData.value.push({ type: 'follow', contentKey: '', uid: guid(), children: data?.filter(v => ['follow'].includes(v.type))?.map(v => ({ ...v, uid: guid() })) || [] })\r\n//   nextTick(() => { scrollDown() })\r\n// }\r\nconst scrollDown = async () => {\r\n  const wrap = scrollbarRef.value.wrapRef\r\n  wrap.scrollTop = wrap.scrollHeight\r\n  // 等待渲染更新\r\n  await nextTick();\r\n}\r\nconst handleQuery = () => {\r\n  if (keyword.value == '') return\r\n  answerLoading.value = false\r\n  ctrl.value.abort();\r\n  ctrl.value = new AbortController(); // 重置以备下次使用\r\n  if (keyword.value) {\r\n    textClick(keyword.value)\r\n  }\r\n}\r\nconst textClick = (text = '') => {\r\n  keyword.value = ''\r\n  ctrl.value.abort()\r\n  nextTick(() => { scrollDown() })\r\n  getFetchEventSource(text)\r\n}\r\n\r\ndefineExpose({ handleQuery, textClick })\r\n</script>\r\n<style lang=\"scss\">\r\n.DouBaoIntelligentize {\r\n  width: 100%;\r\n  height: 100%;\r\n  border-radius: 20px;\r\n  box-shadow: var(--zy-el-box-shadow);\r\n  background-color: rgb(249, 250, 251);\r\n  border: 2px solid var(--zy-el-border-color-lighter);\r\n  display: flex;\r\n  flex-direction: column;\r\n  position: relative;\r\n\r\n  .DouBao-intelligent-assistant {\r\n    position: absolute;\r\n    top: -44px;\r\n    right: 10px;\r\n    width: 40px;\r\n    height: 40px;\r\n\r\n    .zy-el-image {\r\n      width: 100%;\r\n      height: 100%;\r\n    }\r\n  }\r\n\r\n  .DouBaoIntelligentizeBody {\r\n    padding: 20px 10px 20px 20px;\r\n    width: 100%;\r\n    // border: 1px solid salmon;\r\n    height: 100%;\r\n    align-items: center;\r\n    display: flex;\r\n    flex: 1 1;\r\n    flex-direction: column;\r\n    height: 100%;\r\n    overflow: hidden;\r\n    width: 100%;\r\n\r\n    .zy-el-scrollbar {\r\n      width: 100%;\r\n    }\r\n\r\n    .zy-el-scrollbar__view {\r\n      padding: 0 !important;\r\n    }\r\n\r\n    .tableModule {\r\n      padding: 10px 0;\r\n    }\r\n\r\n    .tableModuleContentKey {\r\n      margin: 10px 0;\r\n      padding: 0 20px;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: flex-end;\r\n      width: 100%;\r\n\r\n      .tableModuleContentKeyWord {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        padding: 8px 10px;\r\n        border-radius: 8px;\r\n        overflow-anchor: auto;\r\n        flex-direction: row-reverse;\r\n        background-color: rgba(0, 0, 0, .04);\r\n        white-space: pre-wrap;\r\n        word-wrap: break-word;\r\n        // height: -webkit-fit-content;\r\n        // height: -moz-fit-content;\r\n        // height: fit-content;\r\n      }\r\n    }\r\n\r\n    .tableModuleContentFollow {\r\n      padding-right: 20px;\r\n      margin: 10px 0;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: flex-start;\r\n      width: 100%;\r\n\r\n      .tableModuleContentFollowWord {\r\n        cursor: pointer;\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        padding: 8px 10px;\r\n        border-radius: 8px;\r\n        display: flex;\r\n        align-items: center;\r\n        background-color: rgba(0, 0, 0, .04);\r\n        margin-bottom: 10px;\r\n\r\n        &:hover {\r\n          color: var(--zy-el-color-primary);\r\n        }\r\n\r\n        .wordShow {\r\n          color: var(--zy-el-color-primary);\r\n          font-weight: bold;\r\n          margin-right: 5px;\r\n        }\r\n\r\n        .zy-el-icon {\r\n          margin-left: 5px;\r\n          width: var(--zy-name-font-size);\r\n          height: var(--zy-name-font-size);\r\n\r\n          svg {\r\n            width: var(--zy-name-font-size);\r\n            height: var(--zy-name-font-size);\r\n          }\r\n        }\r\n\r\n        // height: -webkit-fit-content;\r\n        // height: -moz-fit-content;\r\n        // height: fit-content;\r\n      }\r\n    }\r\n\r\n    .tableModuleContentAnswer {\r\n      // padding: 0 20px;\r\n      padding-right: 20px;\r\n      white-space: pre-wrap;\r\n      word-wrap: break-word;\r\n    }\r\n\r\n    .tableModuleItem {\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n      // margin-bottom: var(--zy-font-name-distance-five);\r\n      margin-bottom: 50px;\r\n\r\n\r\n      .copyButton {\r\n        display: block;\r\n        padding: 5px;\r\n        border: 0;\r\n        border-radius: 5px;\r\n        opacity: .5;\r\n        margin-top: 10px;\r\n\r\n        &:active {\r\n          opacity: .8;\r\n        }\r\n      }\r\n\r\n      .tableModuleItemContent {\r\n        border-left: 1px solid var(--zy-el-border-color);\r\n        padding-left: 10px;\r\n\r\n        .ContentText {\r\n          font-size: 1em;\r\n          opacity: .8;\r\n          color: var(zy-el-text-color-secondary);\r\n        }\r\n      }\r\n\r\n      .ContentTextTow {\r\n        font-size: 1.2em;\r\n        margin-top: -20px;\r\n      }\r\n\r\n      // padding-left: 20px;\r\n    }\r\n\r\n    .tableModuleContentAnswer {\r\n\r\n      /* From Uiverse.io by Satwinder04 */\r\n\r\n\r\n      .circle1 {\r\n        width: 4px;\r\n        height: 4px;\r\n        border-radius: 50%;\r\n        margin: 0 5px;\r\n        background-color: #333;\r\n        animation: circle1 1s ease-in-out infinite;\r\n      }\r\n\r\n      .circle1:nth-child(2) {\r\n        animation-delay: 0.33s;\r\n      }\r\n\r\n      .circle1:nth-child(3) {\r\n        animation-delay: 0.67s;\r\n      }\r\n\r\n      .circle1:nth-child(4) {\r\n        animation-delay: 0.6s;\r\n      }\r\n\r\n      .circle1:nth-child(5) {\r\n        animation-delay: 0.8s;\r\n      }\r\n\r\n      @keyframes circle1 {\r\n        0% {\r\n          transform: scale(1);\r\n          opacity: 1;\r\n        }\r\n\r\n        50% {\r\n          transform: scale(1.5);\r\n          opacity: 0.5;\r\n        }\r\n\r\n        100% {\r\n          transform: scale(1);\r\n          opacity: 1;\r\n        }\r\n      }\r\n\r\n    }\r\n  }\r\n\r\n  .DouBaoIntelligentizeSeach {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    flex-shrink: 0;\r\n    height: -webkit-fit-content;\r\n    height: -moz-fit-content;\r\n    height: fit-content;\r\n    width: 100%;\r\n    position: relative;\r\n\r\n    .zy-el-textarea__inner {\r\n      padding-right: 50px;\r\n      min-height: 36px !important;\r\n      border-top-left-radius: 0;\r\n      border-top-right-radius: 0;\r\n      border-bottom-left-radius: 16px;\r\n      border-bottom-right-radius: 16px;\r\n    }\r\n\r\n    .DouBaoIntelligentizeInputCarpetRows {\r\n      width: 100%;\r\n    }\r\n\r\n    .DouBaoIntelligentizeCarpetButton {\r\n      width: 100%;\r\n    }\r\n\r\n    .DouBaoIntelligentizeButton {\r\n      position: absolute;\r\n      content: \"\";\r\n      bottom: 0;\r\n      right: 10px;\r\n      width: 40px;\r\n    }\r\n\r\n    .sendingButton {\r\n      position: absolute;\r\n      content: \"\";\r\n      bottom: 5px;\r\n      right: 10px;\r\n      width: 55px;\r\n      font-size: 12px;\r\n      height: 25px;\r\n      border-radius: 20px;\r\n\r\n\r\n      img {\r\n        margin-right: 5px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 首页智能问答定制样式\r\n.DouBaoIntelligentizeBodyTop {\r\n  width: 100%;\r\n  margin-top: -10px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n\r\n  .DouBaoIntelligentizeBodyLeft {\r\n    display: flex;\r\n    width: 60%;\r\n\r\n    img {\r\n      width: 30px;\r\n      height: 30px;\r\n    }\r\n\r\n    .LeftText {\r\n      margin-left: 5px;\r\n      white-space: nowrap;\r\n    }\r\n\r\n    .LeftSelect {\r\n      width: 200px;\r\n      height: 30px;\r\n      transform: translate(20px, -5px);\r\n    }\r\n  }\r\n\r\n  .DouBaoIntelligentizeBodyRight {\r\n    .RightImg {\r\n      width: 20px;\r\n      height: 20px;\r\n      margin-right: 20px;\r\n    }\r\n  }\r\n}\r\n\r\n.loader3 {\r\n  display: inline-flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n}\r\n</style>\r\n"], "mappings": "+CAkHA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,UAAU,QAAQ,yBAAyB;AACpD,OAAOC,MAAM,MAAM,qBAAqB;AACxC,SAASC,GAAG,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,WAAwB,EAAEC,QAAQ,QAAQ,KAAK;AAClF,SAASC,gBAAgB,QAAQ,+BAA+B;AAChE;AACA;AACA;AACA;AACA;;AAbA,IAAAC,WAAA,GAAe;EAAErC,IAAI,EAAE;AAAuB,CAAC;;;;;;;;;;;;;;;;;;;;IAc/C,IAAMsC,KAAK,GAAGC,OAA6I;IAC3J,IAAMC,OAAO,GAAGT,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMU,WAAW,GAAGV,GAAG,CAAC,CAAC;IACzB,IAAMW,gBAAgB,GAAGX,GAAG,CAAC,EAAE,CAAC;IAChC,IAAMY,IAAI,GAAGC,MAA4B;IACzC,IAAMC,aAAa,GAAGd,GAAG,CAAC,KAAK,CAAC;IAChC,IAAMe,IAAI,GAAGf,GAAG,CAAC,KAAK,CAAC;IACvB,IAAMgB,mBAAmB,GAAGhB,GAAG,CAAC,EAAE,CAAC;IACnC,IAAMiB,SAAS,GAAGjB,GAAG,CAAC,EAAE,CAAC;IACzB,IAAMkB,QAAQ,GAAGlB,GAAG,CAAC,GAAG,CAAC;IACzB,IAAMmB,YAAY,GAAGnB,GAAG,CAAC,CAAC;IAC1B,IAAMoB,UAAU,GAAGpB,GAAG,CAAC,GAAG,CAAC;IAC3B,IAAMqB,cAAc,GAAGjB,QAAQ,CAAC;MAAA,IAAAkB,iBAAA;MAAA,OAAM,EAAAA,iBAAA,GAAAxB,UAAU,CAACtG,KAAK,cAAA8H,iBAAA,uBAAhBA,iBAAA,CAAkBD,cAAc,KAAI,EAAE;IAAA,EAAC;IAC7E,IAAME,OAAO,GAAGvB,GAAG,CAAC,CAAC;MACnBxG,KAAK,EAAE,GAAG;MACVgI,KAAK,EAAE;IACT,CAAC,EAAE;MACDhI,KAAK,EAAE,GAAG;MACVgI,KAAK,EAAE;IACT,CAAC,EAAE;MACDhI,KAAK,EAAE,GAAG;MACVgI,KAAK,EAAE;IACT,CAAC,CACD,CAAC;IACD,IAAIC,IAAI,GAAGzB,GAAG,CAAC,IAAI0B,eAAe,CAAC,CAAC,CAAC;IACrCzB,SAAS,CAAC,YAAM;MACd0B,oBAAoB,CAAC,CAAC;IACxB,CAAC,CAAC;IACFxB,WAAW,CAAC,YAAM;MAChB;IAAA,CACD,CAAC;IACF,IAAMyB,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;MACjB,OAAO,sCAAsC,CAACC,OAAO,CAAC,OAAO,EAAE,UAAChI,CAAC,EAAK;QACpE,IAAIZ,CAAC,GAAG6I,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;UAAEvG,CAAC,GAAG3B,CAAC,IAAI,GAAG,GAAGZ,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAG,GAAI;QAClE,OAAOuC,CAAC,CAACwG,QAAQ,CAAC,EAAE,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC;IACD,IAAML,oBAAoB;MAAA,IAAAM,KAAA,GAAA1C,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAgE,QAAA;QAAA,OAAApJ,mBAAA,GAAAuB,IAAA,UAAA8H,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAzD,IAAA,GAAAyD,QAAA,CAAApF,IAAA;YAAA;cAAe;cAC1CkE,QAAQ,CAAC1H,KAAK,GAAG+G,KAAK,CAAC8B,WAAW;cAElCnC,QAAQ,CAAC,YAAM;gBACboC,mBAAmB,CAAC,CAAC;cACvB,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAF,QAAA,CAAAtD,IAAA;UAAA;QAAA,GAAAoD,OAAA;MAAA,CACH;MAAA,gBANKP,oBAAoBA,CAAA;QAAA,OAAAM,KAAA,CAAAxC,KAAA,OAAAD,SAAA;MAAA;IAAA,GAMzB;IACD,IAAM8C,mBAAmB;MAAA,IAAAC,KAAA,GAAAhD,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAsE,SAAA;QAAA,IAAAC,IAAA;UAAAC,MAAA;UAAAC,KAAA;UAAAC,qBAAA;UAAAC,IAAA;UAAAC,IAAA;UAAAC,UAAA;UAAAC,YAAA;UAAAC,MAAA,GAAAzD,SAAA;QAAA,OAAA1G,mBAAA,GAAAuB,IAAA,UAAA6I,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAxE,IAAA,GAAAwE,SAAA,CAAAnG,IAAA;YAAA;cAAOyF,IAAI,GAAAQ,MAAA,CAAApF,MAAA,QAAAoF,MAAA,QAAAG,SAAA,GAAAH,MAAA,MAAG,EAAE;cAC1CxB,IAAI,CAACjI,KAAK,CAAC6J,KAAK,CAAC,CAAC;cAClB5B,IAAI,CAACjI,KAAK,GAAG,IAAIkI,eAAe,CAAC,CAAC,CAAC,CAAC;cAC5BgB,MAAM,GAAKjB,IAAI,CAACjI,KAAK,CAArBkJ,MAAM;cACRC,KAAK,GAAGW,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE;cAAA,MAC/CxC,IAAI,CAACvH,KAAK,IAAI,KAAK,IAAI,CAAC+G,KAAK,CAACiD,QAAQ,IAAItC,QAAQ,CAAC1H,KAAK,IAAI,GAAG;gBAAA2J,SAAA,CAAAnG,IAAA;gBAAA;cAAA;cACjEiE,SAAS,CAACzH,KAAK,CAACgE,IAAI,CAAC;gBAAEiG,GAAG,EAAEV,UAAU;gBAAEW,QAAQ,EAAE,KAAK;gBAAEC,UAAU,EAAE,+MAA+M;gBAAEC,QAAQ,EAAE,EAAE;gBAAEjJ,IAAI,EAAE;cAAS,CAAC,CAAC;cAAAwI,SAAA,CAAAnG,IAAA;cAAA,OAC9R4C,GAAG,CAACiE,WAAW,CAAC;gBAAEC,QAAQ,EAAEzC,cAAc,CAAC7H,KAAK,IAAI,KAAK,GAAG,GAAG,GAAG;cAAI,CAAC,CAAC;YAAA;cAAAoJ,qBAAA,GAAAO,SAAA,CAAA1G,IAAA;cAAvFoG,IAAI,GAAAD,qBAAA,CAAJC,IAAI;cACZ5B,SAAS,CAACzH,KAAK,CAACgE,IAAI,CAAC;gBAAEiG,GAAG,EAAET,YAAY;gBAAEU,QAAQ,EAAE,KAAK;gBAAEC,UAAU,EAAE,EAAE;gBAAEC,QAAQ,EAAEf,IAAI;gBAAElI,IAAI,EAAE;cAAS,CAAC,CAAC;cAC5GoG,IAAI,CAACvH,KAAK,GAAG,IAAI;YAAA;cAEfsJ,IAAI,GAAGlB,IAAI,CAAC,CAAC;cACbmB,UAAU,GAAGnB,IAAI,CAAC,CAAC;cACnBoB,YAAY,GAAGpB,IAAI,CAAC,CAAC;cACzBX,SAAS,CAACzH,KAAK,CAACgE,IAAI,CAAC;gBAAEiG,GAAG,EAAEX,IAAI;gBAAEa,UAAU,EAAElB,IAAI;gBAAEsB,WAAW,EAAE,EAAE;gBAAEC,QAAQ,EAAE,IAAI;gBAAEJ,QAAQ,EAAE,EAAE;gBAAEjJ,IAAI,EAAE;cAAQ,CAAC,CAAC;cACnHsG,SAAS,CAACzH,KAAK,CAACgE,IAAI,CAAC;gBAAEiG,GAAG,EAAEV,UAAU;gBAAEY,UAAU,EAAE,EAAE;gBAAEC,QAAQ,EAAE,EAAE;gBAAEjJ,IAAI,EAAE;cAAS,CAAC,CAAC;cACvFsG,SAAS,CAACzH,KAAK,CAACgE,IAAI,CAAC;gBAAEiG,GAAG,EAAET,YAAY;gBAAEW,UAAU,EAAE,EAAE;gBAAEC,QAAQ,EAAE,EAAE;gBAAEjJ,IAAI,EAAE;cAAS,CAAC,CAAC;cACzFgG,gBAAgB,CAACnH,KAAK,GAAGuJ,UAAU;cACnC/B,mBAAmB,CAACxH,KAAK,GAAGwJ,YAAY;cACxClC,aAAa,CAACtH,KAAK,GAAG,IAAI;cAC1ByH,SAAS,CAACzH,KAAK,CAACyH,SAAS,CAACzH,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAACkG,WAAW,GAAG,EAAE;cAC5D9C,SAAS,CAACzH,KAAK,CAACyH,SAAS,CAACzH,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAACmG,QAAQ,GAAG,IAAI;cAAAb,SAAA,CAAAnG,IAAA;cAAA,OACrDqD,gBAAgB,CAAC,GAAGN,MAAM,CAACkE,OAAO,GAAG7C,UAAU,CAAC5H,KAAK,IAAI,CAAC,GAAG,yBAAyB,GAAG4H,UAAU,CAAC5H,KAAK,IAAI,CAAC,GAAG,0BAA0B,GAAG,8BAA8B,EAAE,EAAE;gBACpL8C,MAAM,EAAE,MAAM;gBACd4H,OAAO,EAAE;kBACP,cAAc,EAAE,kBAAkB;kBAClCC,aAAa,EAAExB;gBACjB,CAAC;gBACDyB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;kBACnBC,OAAO,EAAE9B,IAAI;kBACb;kBACAqB,QAAQ,EAAE1C,UAAU,CAAC5H,KAAK,IAAI,CAAC,GAAG+G,KAAK,CAACiD,QAAQ,GAAG,GAAG,GAAGnC,cAAc,CAAC7H,KAAK,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG;gBACvG,CAAC,CAAC;gBACFgL,cAAc,EAAE,IAAI;gBAAE;gBACtB9B,MAAM;gBACN+B,SAAS,EAAE,SAAXA,SAASA,CAAGC,GAAG,EAAK;kBAClB,IAAItD,UAAU,CAAC5H,KAAK,IAAI,CAAC,IAAI4H,UAAU,CAAC5H,KAAK,IAAI,CAAC,EAAE;oBAClD,IAAI6K,IAAI,CAACM,KAAK,CAACD,GAAG,CAAC7B,IAAI,CAAC,CAAC+B,KAAK,IAAIP,IAAI,CAACM,KAAK,CAACD,GAAG,CAAC7B,IAAI,CAAC,CAAC+B,KAAK,IAAI,EAAE,EAAE;sBAAA,IAAAC,WAAA;sBAClE5D,SAAS,CAACzH,KAAK,CAACyH,SAAS,CAACzH,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAACkG,WAAW,IAAK,EAAAc,WAAA,GAAAR,IAAI,CAACM,KAAK,CAACD,GAAG,CAAC7B,IAAI,CAAC,cAAAgC,WAAA,uBAApBA,WAAA,CAAsBD,KAAK,KAAI,EAAG;oBAChG,CAAC,MAAM;sBAAA,IAAAE,YAAA;sBACL7D,SAAS,CAACzH,KAAK,CAACyH,SAAS,CAACzH,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAAC8F,UAAU,IAAI,EAAAmB,YAAA,GAAAT,IAAI,CAACM,KAAK,CAACD,GAAG,CAAC7B,IAAI,CAAC,cAAAiC,YAAA,uBAApBA,YAAA,CAAsBC,QAAQ,KAAI,EAAE;sBAC9F9D,SAAS,CAACzH,KAAK,CAACyH,SAAS,CAACzH,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAAC8F,UAAU,GAAG1C,SAAS,CAACzH,KAAK,CAACyH,SAAS,CAACzH,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAAC8F,UAAU,CAAC9B,OAAO,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;sBAChKZ,SAAS,CAACzH,KAAK,CAACyH,SAAS,CAACzH,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAAC6F,QAAQ,GAAG,IAAI;oBAC7D;oBACAxD,QAAQ,CAAC,YAAM;sBACb8E,UAAU,CAAC,CAAC;oBACd,CAAC,CAAC;kBACJ,CAAC,MAAM;oBACL,IAAIC,OAAO,GAAGZ,IAAI,CAACM,KAAK,CAACD,GAAG,CAAC7B,IAAI,CAAC;oBAClC,IAAI,CAAC,4BAA4B,CAAC,CAACqC,QAAQ,CAACD,OAAO,CAACE,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAACD,QAAQ,CAACD,OAAO,CAACpC,IAAI,CAAClI,IAAI,CAAC,EAAE;sBACpG,IAAIgG,gBAAgB,CAACnH,KAAK,KAAKuJ,UAAU,EAAE;wBAAA,IAAAqC,YAAA;wBACzCnE,SAAS,CAACzH,KAAK,CAACyH,SAAS,CAACzH,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAAC8F,UAAU,IAAI,EAAAyB,YAAA,GAAAf,IAAI,CAACM,KAAK,CAACD,GAAG,CAAC7B,IAAI,CAAC,cAAAuC,YAAA,gBAAAA,YAAA,GAApBA,YAAA,CAAsBvC,IAAI,cAAAuC,YAAA,uBAA1BA,YAAA,CAA4Bb,OAAO,KAAI,EAAE;wBACnGtD,SAAS,CAACzH,KAAK,CAACyH,SAAS,CAACzH,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAAC8F,UAAU,GAAG1C,SAAS,CAACzH,KAAK,CAACyH,SAAS,CAACzH,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAAC8F,UAAU,CAAC9B,OAAO,CAAC,gBAAgB,EAAE,qBAAqB,CAAC;sBAClK;oBACF,CAAC,MAAM,IAAI,CAAC,gCAAgC,CAAC,CAACqD,QAAQ,CAACD,OAAO,CAACE,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,CAACD,QAAQ,CAACD,OAAO,CAACpC,IAAI,CAAClI,IAAI,CAAC,EAAE;sBAClH,IAAIqG,mBAAmB,CAACxH,KAAK,KAAKwJ,YAAY,EAAE;wBAAA,IAAAqC,YAAA;wBAC9CpE,SAAS,CAACzH,KAAK,CAACyH,SAAS,CAACzH,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAAC+F,QAAQ,CAACpG,IAAI,CAAC,EAAA6H,YAAA,GAAAhB,IAAI,CAACM,KAAK,CAACD,GAAG,CAAC7B,IAAI,CAAC,cAAAwC,YAAA,gBAAAA,YAAA,GAApBA,YAAA,CAAsBxC,IAAI,cAAAwC,YAAA,uBAA1BA,YAAA,CAA4Bd,OAAO,KAAI,EAAE,CAAC;sBACtG;oBACF;oBACArE,QAAQ,CAAC,YAAM;sBAAE8E,UAAU,CAAC,CAAC;oBAAC,CAAC,CAAC;kBAClC;gBACF,CAAC;gBACDM,OAAO,EAAE,SAATA,OAAOA,CAAGzC,IAAI,EAAK;kBACjB,IAAM0C,YAAY,GAAG,iBAAiB;kBACtC,IAAIvB,QAAQ,GAAGuB,YAAY,CAACC,IAAI,CAACvE,SAAS,CAACzH,KAAK,CAACyH,SAAS,CAACzH,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAACkG,WAAW,CAAC;kBACzF9C,SAAS,CAACzH,KAAK,CAACyH,SAAS,CAACzH,KAAK,CAACqE,MAAM,GAAG,CAAC,CAAC,CAACmG,QAAQ,GAAGA,QAAQ;kBAC/DlD,aAAa,CAACtH,KAAK,GAAG,KAAK;gBAC7B,CAAC;gBACDiM,OAAO,EAAE,SAATA,OAAOA,CAAGC,GAAG,EAAK;kBAChBzE,SAAS,CAACzH,KAAK,CAACgE,IAAI,CAAC;oBAAEiG,GAAG,EAAEV,UAAU;oBAAEY,UAAU,EAAE,SAAS;oBAAEC,QAAQ,EAAE,EAAE;oBAAEjJ,IAAI,EAAE;kBAAS,CAAC,CAAC;kBAC9FmG,aAAa,CAACtH,KAAK,GAAG,KAAK;kBAC3B,MAAMkM,GAAG;gBACX;cACF,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAvC,SAAA,CAAArE,IAAA;UAAA;QAAA,GAAA0D,QAAA;MAAA,CACH;MAAA,gBA1EKF,mBAAmBA,CAAA;QAAA,OAAAC,KAAA,CAAA9C,KAAA,OAAAD,SAAA;MAAA;IAAA,GA0ExB;IACD,IAAMmG,aAAa;MAAA,IAAAC,KAAA,GAAArG,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2H,SAAOpD,IAAI;QAAA,IAAAqD,aAAA,EAAAC,YAAA,EAAAC,EAAA;QAAA,OAAAlN,mBAAA,GAAAuB,IAAA,UAAA4L,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAvH,IAAA,GAAAuH,SAAA,CAAAlJ,IAAA;YAAA;cAAAkJ,SAAA,CAAAvH,IAAA;cAEvBmH,aAAa,GAAGrD,IAAI,CAACkB,UAAU,CAAC9B,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;cAC1DkE,YAAY,GAAGD,aAAa,CAACjE,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC;cAAAqE,SAAA,CAAAlJ,IAAA;cAAA,OACxDmJ,SAAS,CAACC,SAAS,CAACC,SAAS,CAACN,YAAY,CAAC;YAAA;cACjDlG,SAAS,CAAC;gBACRyG,OAAO,EAAE,MAAM;gBACf3L,IAAI,EAAE;cACR,CAAC,CAAC;cAAAuL,SAAA,CAAAlJ,IAAA;cAAA;YAAA;cAAAkJ,SAAA,CAAAvH,IAAA;cAAAuH,SAAA,CAAAK,EAAA,GAAAL,SAAA;cAEF;cACMF,EAAE,GAAGQ,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;cAC7CT,EAAE,CAACxM,KAAK,GAAGiJ,IAAI,CAACkB,UAAU;cAC1BqC,EAAE,CAACU,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC;cAC/BV,EAAE,CAACW,KAAK,CAACC,QAAQ,GAAG,UAAU;cAC9BZ,EAAE,CAACW,KAAK,CAACE,IAAI,GAAG,SAAS;cACzBL,QAAQ,CAACpC,IAAI,CAAC0C,WAAW,CAACd,EAAE,CAAC;cAC7BA,EAAE,CAACe,MAAM,CAAC,CAAC;cACXP,QAAQ,CAACQ,WAAW,CAAC,MAAM,CAAC;cAC5BR,QAAQ,CAACpC,IAAI,CAAC6C,WAAW,CAACjB,EAAE,CAAC;cAC7BnG,SAAS,CAAC;gBACRyG,OAAO,EAAE,MAAM;gBACf3L,IAAI,EAAE;cACR,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAuL,SAAA,CAAApH,IAAA;UAAA;QAAA,GAAA+G,QAAA;MAAA,CAEL;MAAA,gBAzBKF,aAAaA,CAAAuB,EAAA;QAAA,OAAAtB,KAAA,CAAAnG,KAAA,OAAAD,SAAA;MAAA;IAAA,GAyBlB;IACD,IAAM2H,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3BlG,SAAS,CAACzH,KAAK,GAAG,EAAE;IACtB,CAAC;IACD,IAAM4N,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrBxG,IAAI,CAAC,aAAa,CAAC;IACrB,CAAC;;IAED;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAMoE,UAAU;MAAA,IAAAqC,KAAA,GAAA9H,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAoJ,SAAA;QAAA,IAAAjN,IAAA;QAAA,OAAAvB,mBAAA,GAAAuB,IAAA,UAAAkN,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7I,IAAA,GAAA6I,SAAA,CAAAxK,IAAA;YAAA;cACX3C,IAAI,GAAG8G,YAAY,CAAC3H,KAAK,CAACiO,OAAO;cACvCpN,IAAI,CAACqN,SAAS,GAAGrN,IAAI,CAACsN,YAAY;cAClC;cAAAH,SAAA,CAAAxK,IAAA;cAAA,OACMkD,QAAQ,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAsH,SAAA,CAAA1I,IAAA;UAAA;QAAA,GAAAwI,QAAA;MAAA,CACjB;MAAA,gBALKtC,UAAUA,CAAA;QAAA,OAAAqC,KAAA,CAAA5H,KAAA,OAAAD,SAAA;MAAA;IAAA,GAKf;IACD,IAAMoI,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB,IAAInH,OAAO,CAACjH,KAAK,IAAI,EAAE,EAAE;MACzBsH,aAAa,CAACtH,KAAK,GAAG,KAAK;MAC3BiI,IAAI,CAACjI,KAAK,CAAC6J,KAAK,CAAC,CAAC;MAClB5B,IAAI,CAACjI,KAAK,GAAG,IAAIkI,eAAe,CAAC,CAAC,CAAC,CAAC;MACpC,IAAIjB,OAAO,CAACjH,KAAK,EAAE;QACjBqO,SAAS,CAACpH,OAAO,CAACjH,KAAK,CAAC;MAC1B;IACF,CAAC;IACD,IAAMqO,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAkB;MAAA,IAAdpF,IAAI,GAAAjD,SAAA,CAAA3B,MAAA,QAAA2B,SAAA,QAAA4D,SAAA,GAAA5D,SAAA,MAAG,EAAE;MAC1BiB,OAAO,CAACjH,KAAK,GAAG,EAAE;MAClBiI,IAAI,CAACjI,KAAK,CAAC6J,KAAK,CAAC,CAAC;MAClBnD,QAAQ,CAAC,YAAM;QAAE8E,UAAU,CAAC,CAAC;MAAC,CAAC,CAAC;MAChC1C,mBAAmB,CAACG,IAAI,CAAC;IAC3B,CAAC;IAEDqF,QAAY,CAAC;MAAEF,WAAW;MAAEC;IAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}