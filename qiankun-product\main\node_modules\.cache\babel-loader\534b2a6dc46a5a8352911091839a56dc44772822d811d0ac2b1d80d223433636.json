{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, normalizeStyle as _normalizeStyle, openBlock as _openBlock, createElement<PERSON>lock as _createElementBlock } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_Search = _resolveComponent(\"Search\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_Collection = _resolveComponent(\"Collection\");\n  var _component_CopyDocument = _resolveComponent(\"CopyDocument\");\n  return _openBlock(), _createElementBlock(\"div\", {\n    ref: \"controlRef\",\n    class: \"GlobalAiControls forbidSelect\",\n    style: _normalizeStyle({\n      top: $setup.top,\n      left: $setup.left\n    }),\n    onMouseenter: $setup.handleMouseEnter,\n    onMouseleave: $setup.handleMouseLeave\n  }, [_createElementVNode(\"div\", {\n    class: \"GlobalAiControlsItem\",\n    onClick: $setup.handleSearch\n  }, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_Search)];\n    }),\n    _: 1 /* STABLE */\n  }), _cache[0] || (_cache[0] = _createTextVNode(\"AI搜索 \"))]), _createElementVNode(\"div\", {\n    class: \"GlobalAiControlsItem\",\n    onClick: $setup.handleParaphrase\n  }, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_Collection)];\n    }),\n    _: 1 /* STABLE */\n  }), _cache[1] || (_cache[1] = _createTextVNode(\"释义 \"))]), _createElementVNode(\"div\", {\n    class: \"GlobalAiControlsItem\",\n    onClick: $setup.handleCopy\n  }, [_createVNode(_component_el_icon, null, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_CopyDocument)];\n    }),\n    _: 1 /* STABLE */\n  }), _cache[2] || (_cache[2] = _createTextVNode(\"复制 \"))])], 36 /* STYLE, NEED_HYDRATION */);\n}", "map": {"version": 3, "names": ["_createElementBlock", "ref", "class", "style", "_normalizeStyle", "top", "$setup", "left", "onMouseenter", "handleMouseEnter", "onMouseleave", "handleMouseLeave", "_createElementVNode", "onClick", "handleSearch", "_createVNode", "_component_el_icon", "default", "_withCtx", "_component_Search", "_", "_createTextVNode", "handleParaphrase", "_component_Collection", "handleCopy", "_component_CopyDocument"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LayoutContainer\\components\\GlobalAiControls.vue"], "sourcesContent": ["<template>\r\n  <div ref=\"controlRef\" class=\"GlobalAiControls forbidSelect\" :style=\"{ top: top, left: left }\"\r\n    @mouseenter=\"handleMouseEnter\" @mouseleave=\"handleMouseLeave\">\r\n    <div class=\"GlobalAiControlsItem\" @click=\"handleSearch\">\r\n      <el-icon>\r\n        <Search />\r\n      </el-icon>AI搜索\r\n    </div>\r\n    <div class=\"GlobalAiControlsItem\" @click=\"handleParaphrase\">\r\n      <el-icon>\r\n        <Collection />\r\n      </el-icon>释义\r\n    </div>\r\n    <div class=\"GlobalAiControlsItem\" @click=\"handleCopy\">\r\n      <el-icon>\r\n        <CopyDocument />\r\n      </el-icon>复制\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalAiControls' }\r\n</script>\r\n<script setup>\r\nimport { ref, inject, onMounted } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { ElMessage } from 'element-plus'\r\nconst store = useStore()\r\nconst textSelection = inject('textSelection')\r\nconst top = ref('-100%')\r\nconst left = ref('-100%')\r\nconst content = ref('')\r\nconst isHovering = ref(false)\r\nconst isActive = ref(false)\r\nconst controlRef = ref(null)\r\n\r\nconst handleMouseEnter = () => {\r\n  isHovering.value = true\r\n}\r\n\r\nconst handleMouseLeave = () => {\r\n  isHovering.value = false\r\n  if (isActive.value) {\r\n    handleClick()\r\n  }\r\n}\r\n\r\nconst handleClick = (force = false) => {\r\n  if (!isHovering.value || force) {\r\n    content.value = ''\r\n    top.value = '-100%'\r\n    left.value = '-100%'\r\n    isActive.value = false\r\n  }\r\n}\r\n\r\nconst handleSearch = () => {\r\n  store.commit('setAiChatConfig', {\r\n    AiChatWindow: true,\r\n    AiChatSendMessage: '搜索一下：' + content.value\r\n  })\r\n  handleClick(true)\r\n}\r\nconst handleParaphrase = () => {\r\n  store.commit('setAiChatConfig', {\r\n    AiChatWindow: true,\r\n    AiChatSendMessage: '释义：' + content.value\r\n  })\r\n  handleClick(true)\r\n}\r\nconst handleCopy = () => {\r\n  const textarea = document.createElement('textarea')\r\n  textarea.readOnly = 'readonly'\r\n  textarea.style.position = 'absolute'\r\n  textarea.style.left = '-9999px'\r\n  textarea.value = content.value\r\n  document.body.appendChild(textarea)\r\n  textarea.select()\r\n  const result = document.execCommand('Copy')\r\n  if (result) {\r\n    ElMessage({ message: '复制成功', type: 'success' })\r\n  }\r\n  document.body.removeChild(textarea)\r\n  handleClick(true)\r\n}\r\n\r\nconst calculateSafePosition = (x, y) => {\r\n  const control = controlRef.value\r\n  if (!control) return { x, y }\r\n  const controlWidth = control.offsetWidth\r\n  const controlHeight = control.offsetHeight\r\n  const viewportWidth = window.innerWidth\r\n  const viewportHeight = window.innerHeight\r\n  // 计算安全的 X 坐标\r\n  let safeX = x\r\n  if (x + controlWidth > viewportWidth) {\r\n    safeX = viewportWidth - controlWidth - 10\r\n  }\r\n  if (safeX < 10) safeX = 10\r\n  // 计算安全的 Y 坐标\r\n  let safeY = y\r\n  // 默认显示在上方，距离为一个控件高度\r\n  safeY = y - controlHeight - 10\r\n  // 如果上方空间不足，则显示在下方\r\n  if (safeY < 10) {\r\n    safeY = y + controlHeight\r\n  }\r\n  // 如果下方也没有足够空间，则尽可能靠近顶部或底部\r\n  if (safeY + controlHeight > viewportHeight - 10) {\r\n    safeY = viewportHeight - controlHeight - 10\r\n  }\r\n  return { x: safeX, y: safeY }\r\n}\r\n\r\nonMounted(() => {\r\n  textSelection.listen(\r\n    (text, event, hasClick, coordinates) => {\r\n      if (!hasClick) {\r\n        content.value = text\r\n        const { x, y } = calculateSafePosition(coordinates.x, coordinates.y)\r\n        top.value = `${y}px`\r\n        left.value = `${x}px`\r\n      }\r\n    },\r\n    (event) => {\r\n      isActive.value = true\r\n      if (!isHovering.value) {\r\n        handleClick()\r\n      }\r\n    }\r\n  )\r\n}) \r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.GlobalAiControls {\r\n  height: var(--zy-height);\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 12px;\r\n  background: #fff;\r\n  box-shadow: var(--zy-el-box-shadow-light);\r\n  border-radius: 6px;\r\n  position: fixed;\r\n  // transform: translateY(-120%);\r\n  z-index: 9999;\r\n\r\n  .GlobalAiControlsItem {\r\n    height: var(--zy-height-secondary);\r\n    display: flex;\r\n    align-items: center;\r\n    font-size: var(--zy-text-font-size);\r\n    border-radius: 6px;\r\n    padding: 0 12px;\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      color: var(--zy-el-color-primary);\r\n      background: var(--zy-el-color-primary-light-9);\r\n    }\r\n\r\n    .zy-el-icon {\r\n      margin-right: 2px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;uBACEA,mBAAA,CAiBM;IAjBDC,GAAG,EAAC,YAAY;IAACC,KAAK,EAAC,+BAA+B;IAAEC,KAAK,EADpEC,eAAA;MAAAC,GAAA,EAC6EC,MAAA,CAAAD,GAAG;MAAAE,IAAA,EAAQD,MAAA,CAAAC;IAAI;IACvFC,YAAU,EAAEF,MAAA,CAAAG,gBAAgB;IAAGC,YAAU,EAAEJ,MAAA,CAAAK;MAC5CC,mBAAA,CAIM;IAJDV,KAAK,EAAC,sBAAsB;IAAEW,OAAK,EAAEP,MAAA,CAAAQ;MACxCC,YAAA,CAEUC,kBAAA;IANhBC,OAAA,EAAAC,QAAA,CAKQ;MAAA,OAAU,CAAVH,YAAA,CAAUI,iBAAA,E;;IALlBC,CAAA;gCAAAC,gBAAA,CAMgB,OACZ,G,GACAT,mBAAA,CAIM;IAJDV,KAAK,EAAC,sBAAsB;IAAEW,OAAK,EAAEP,MAAA,CAAAgB;MACxCP,YAAA,CAEUC,kBAAA;IAXhBC,OAAA,EAAAC,QAAA,CAUQ;MAAA,OAAc,CAAdH,YAAA,CAAcQ,qBAAA,E;;IAVtBH,CAAA;gCAAAC,gBAAA,CAWgB,KACZ,G,GACAT,mBAAA,CAIM;IAJDV,KAAK,EAAC,sBAAsB;IAAEW,OAAK,EAAEP,MAAA,CAAAkB;MACxCT,YAAA,CAEUC,kBAAA;IAhBhBC,OAAA,EAAAC,QAAA,CAeQ;MAAA,OAAgB,CAAhBH,YAAA,CAAgBU,uBAAA,E;;IAfxBL,CAAA;gCAAAC,gBAAA,CAgBgB,KACZ,G", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}