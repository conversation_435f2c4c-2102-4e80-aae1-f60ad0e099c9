{"ast": null, "code": "// Parser state class\n\n'use strict';\n\nvar Token = require('../token');\nvar isSpace = require('../common/utils').isSpace;\nfunction StateBlock(src, md, env, tokens) {\n  var ch, s, start, pos, len, indent, offset, indent_found;\n  this.src = src;\n\n  // link to parser instance\n  this.md = md;\n  this.env = env;\n\n  //\n  // Internal state vartiables\n  //\n\n  this.tokens = tokens;\n  this.bMarks = []; // line begin offsets for fast jumps\n  this.eMarks = []; // line end offsets for fast jumps\n  this.tShift = []; // offsets of the first non-space characters (tabs not expanded)\n  this.sCount = []; // indents for each line (tabs expanded)\n\n  // An amount of virtual spaces (tabs expanded) between beginning\n  // of each line (bMarks) and real beginning of that line.\n  //\n  // It exists only as a hack because blockquotes override bMarks\n  // losing information in the process.\n  //\n  // It's used only when expanding tabs, you can think about it as\n  // an initial tab length, e.g. bsCount=21 applied to string `\\t123`\n  // means first tab should be expanded to 4-21%4 === 3 spaces.\n  //\n  this.bsCount = [];\n\n  // block parser variables\n  this.blkIndent = 0; // required block content indent (for example, if we are\n  // inside a list, it would be positioned after list marker)\n  this.line = 0; // line index in src\n  this.lineMax = 0; // lines count\n  this.tight = false; // loose/tight mode for lists\n  this.ddIndent = -1; // indent of the current dd block (-1 if there isn't any)\n  this.listIndent = -1; // indent of the current list block (-1 if there isn't any)\n\n  // can be 'blockquote', 'list', 'root', 'paragraph' or 'reference'\n  // used in lists to determine if they interrupt a paragraph\n  this.parentType = 'root';\n  this.level = 0;\n\n  // renderer\n  this.result = '';\n\n  // Create caches\n  // Generate markers.\n  s = this.src;\n  indent_found = false;\n  for (start = pos = indent = offset = 0, len = s.length; pos < len; pos++) {\n    ch = s.charCodeAt(pos);\n    if (!indent_found) {\n      if (isSpace(ch)) {\n        indent++;\n        if (ch === 0x09) {\n          offset += 4 - offset % 4;\n        } else {\n          offset++;\n        }\n        continue;\n      } else {\n        indent_found = true;\n      }\n    }\n    if (ch === 0x0A || pos === len - 1) {\n      if (ch !== 0x0A) {\n        pos++;\n      }\n      this.bMarks.push(start);\n      this.eMarks.push(pos);\n      this.tShift.push(indent);\n      this.sCount.push(offset);\n      this.bsCount.push(0);\n      indent_found = false;\n      indent = 0;\n      offset = 0;\n      start = pos + 1;\n    }\n  }\n\n  // Push fake entry to simplify cache bounds checks\n  this.bMarks.push(s.length);\n  this.eMarks.push(s.length);\n  this.tShift.push(0);\n  this.sCount.push(0);\n  this.bsCount.push(0);\n  this.lineMax = this.bMarks.length - 1; // don't count last fake line\n}\n\n// Push new token to \"stream\".\n//\nStateBlock.prototype.push = function (type, tag, nesting) {\n  var token = new Token(type, tag, nesting);\n  token.block = true;\n  if (nesting < 0) this.level--; // closing tag\n  token.level = this.level;\n  if (nesting > 0) this.level++; // opening tag\n\n  this.tokens.push(token);\n  return token;\n};\nStateBlock.prototype.isEmpty = function isEmpty(line) {\n  return this.bMarks[line] + this.tShift[line] >= this.eMarks[line];\n};\nStateBlock.prototype.skipEmptyLines = function skipEmptyLines(from) {\n  for (var max = this.lineMax; from < max; from++) {\n    if (this.bMarks[from] + this.tShift[from] < this.eMarks[from]) {\n      break;\n    }\n  }\n  return from;\n};\n\n// Skip spaces from given position.\nStateBlock.prototype.skipSpaces = function skipSpaces(pos) {\n  var ch;\n  for (var max = this.src.length; pos < max; pos++) {\n    ch = this.src.charCodeAt(pos);\n    if (!isSpace(ch)) {\n      break;\n    }\n  }\n  return pos;\n};\n\n// Skip spaces from given position in reverse.\nStateBlock.prototype.skipSpacesBack = function skipSpacesBack(pos, min) {\n  if (pos <= min) {\n    return pos;\n  }\n  while (pos > min) {\n    if (!isSpace(this.src.charCodeAt(--pos))) {\n      return pos + 1;\n    }\n  }\n  return pos;\n};\n\n// Skip char codes from given position\nStateBlock.prototype.skipChars = function skipChars(pos, code) {\n  for (var max = this.src.length; pos < max; pos++) {\n    if (this.src.charCodeAt(pos) !== code) {\n      break;\n    }\n  }\n  return pos;\n};\n\n// Skip char codes reverse from given position - 1\nStateBlock.prototype.skipCharsBack = function skipCharsBack(pos, code, min) {\n  if (pos <= min) {\n    return pos;\n  }\n  while (pos > min) {\n    if (code !== this.src.charCodeAt(--pos)) {\n      return pos + 1;\n    }\n  }\n  return pos;\n};\n\n// cut lines range from source.\nStateBlock.prototype.getLines = function getLines(begin, end, indent, keepLastLF) {\n  var i,\n    lineIndent,\n    ch,\n    first,\n    last,\n    queue,\n    lineStart,\n    line = begin;\n  if (begin >= end) {\n    return '';\n  }\n  queue = new Array(end - begin);\n  for (i = 0; line < end; line++, i++) {\n    lineIndent = 0;\n    lineStart = first = this.bMarks[line];\n    if (line + 1 < end || keepLastLF) {\n      // No need for bounds check because we have fake entry on tail.\n      last = this.eMarks[line] + 1;\n    } else {\n      last = this.eMarks[line];\n    }\n    while (first < last && lineIndent < indent) {\n      ch = this.src.charCodeAt(first);\n      if (isSpace(ch)) {\n        if (ch === 0x09) {\n          lineIndent += 4 - (lineIndent + this.bsCount[line]) % 4;\n        } else {\n          lineIndent++;\n        }\n      } else if (first - lineStart < this.tShift[line]) {\n        // patched tShift masked characters to look like spaces (blockquotes, list markers)\n        lineIndent++;\n      } else {\n        break;\n      }\n      first++;\n    }\n    if (lineIndent > indent) {\n      // partially expanding tabs in code blocks, e.g '\\t\\tfoobar'\n      // with indent=2 becomes '  \\tfoobar'\n      queue[i] = new Array(lineIndent - indent + 1).join(' ') + this.src.slice(first, last);\n    } else {\n      queue[i] = this.src.slice(first, last);\n    }\n  }\n  return queue.join('');\n};\n\n// re-export Token class to use in block rules\nStateBlock.prototype.Token = Token;\nmodule.exports = StateBlock;", "map": {"version": 3, "names": ["Token", "require", "isSpace", "StateBlock", "src", "md", "env", "tokens", "ch", "s", "start", "pos", "len", "indent", "offset", "indent_found", "bMarks", "eMarks", "tShift", "sCount", "bsCount", "blkIndent", "line", "lineMax", "tight", "ddIndent", "listIndent", "parentType", "level", "result", "length", "charCodeAt", "push", "prototype", "type", "tag", "nesting", "token", "block", "isEmpty", "skipEmptyLines", "from", "max", "skipSpaces", "skipSpacesBack", "min", "<PERSON><PERSON><PERSON><PERSON>", "code", "skip<PERSON><PERSON><PERSON><PERSON><PERSON>", "getLines", "begin", "end", "keepLastLF", "i", "lineIndent", "first", "last", "queue", "lineStart", "Array", "join", "slice", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_block/state_block.js"], "sourcesContent": ["// Parser state class\n\n'use strict';\n\nvar Token = require('../token');\nvar isSpace = require('../common/utils').isSpace;\n\n\nfunction StateBlock(src, md, env, tokens) {\n  var ch, s, start, pos, len, indent, offset, indent_found;\n\n  this.src = src;\n\n  // link to parser instance\n  this.md     = md;\n\n  this.env = env;\n\n  //\n  // Internal state vartiables\n  //\n\n  this.tokens = tokens;\n\n  this.bMarks = [];  // line begin offsets for fast jumps\n  this.eMarks = [];  // line end offsets for fast jumps\n  this.tShift = [];  // offsets of the first non-space characters (tabs not expanded)\n  this.sCount = [];  // indents for each line (tabs expanded)\n\n  // An amount of virtual spaces (tabs expanded) between beginning\n  // of each line (bMarks) and real beginning of that line.\n  //\n  // It exists only as a hack because blockquotes override bMarks\n  // losing information in the process.\n  //\n  // It's used only when expanding tabs, you can think about it as\n  // an initial tab length, e.g. bsCount=21 applied to string `\\t123`\n  // means first tab should be expanded to 4-21%4 === 3 spaces.\n  //\n  this.bsCount = [];\n\n  // block parser variables\n  this.blkIndent  = 0; // required block content indent (for example, if we are\n                       // inside a list, it would be positioned after list marker)\n  this.line       = 0; // line index in src\n  this.lineMax    = 0; // lines count\n  this.tight      = false;  // loose/tight mode for lists\n  this.ddIndent   = -1; // indent of the current dd block (-1 if there isn't any)\n  this.listIndent = -1; // indent of the current list block (-1 if there isn't any)\n\n  // can be 'blockquote', 'list', 'root', 'paragraph' or 'reference'\n  // used in lists to determine if they interrupt a paragraph\n  this.parentType = 'root';\n\n  this.level = 0;\n\n  // renderer\n  this.result = '';\n\n  // Create caches\n  // Generate markers.\n  s = this.src;\n  indent_found = false;\n\n  for (start = pos = indent = offset = 0, len = s.length; pos < len; pos++) {\n    ch = s.charCodeAt(pos);\n\n    if (!indent_found) {\n      if (isSpace(ch)) {\n        indent++;\n\n        if (ch === 0x09) {\n          offset += 4 - offset % 4;\n        } else {\n          offset++;\n        }\n        continue;\n      } else {\n        indent_found = true;\n      }\n    }\n\n    if (ch === 0x0A || pos === len - 1) {\n      if (ch !== 0x0A) { pos++; }\n      this.bMarks.push(start);\n      this.eMarks.push(pos);\n      this.tShift.push(indent);\n      this.sCount.push(offset);\n      this.bsCount.push(0);\n\n      indent_found = false;\n      indent = 0;\n      offset = 0;\n      start = pos + 1;\n    }\n  }\n\n  // Push fake entry to simplify cache bounds checks\n  this.bMarks.push(s.length);\n  this.eMarks.push(s.length);\n  this.tShift.push(0);\n  this.sCount.push(0);\n  this.bsCount.push(0);\n\n  this.lineMax = this.bMarks.length - 1; // don't count last fake line\n}\n\n// Push new token to \"stream\".\n//\nStateBlock.prototype.push = function (type, tag, nesting) {\n  var token = new Token(type, tag, nesting);\n  token.block = true;\n\n  if (nesting < 0) this.level--; // closing tag\n  token.level = this.level;\n  if (nesting > 0) this.level++; // opening tag\n\n  this.tokens.push(token);\n  return token;\n};\n\nStateBlock.prototype.isEmpty = function isEmpty(line) {\n  return this.bMarks[line] + this.tShift[line] >= this.eMarks[line];\n};\n\nStateBlock.prototype.skipEmptyLines = function skipEmptyLines(from) {\n  for (var max = this.lineMax; from < max; from++) {\n    if (this.bMarks[from] + this.tShift[from] < this.eMarks[from]) {\n      break;\n    }\n  }\n  return from;\n};\n\n// Skip spaces from given position.\nStateBlock.prototype.skipSpaces = function skipSpaces(pos) {\n  var ch;\n\n  for (var max = this.src.length; pos < max; pos++) {\n    ch = this.src.charCodeAt(pos);\n    if (!isSpace(ch)) { break; }\n  }\n  return pos;\n};\n\n// Skip spaces from given position in reverse.\nStateBlock.prototype.skipSpacesBack = function skipSpacesBack(pos, min) {\n  if (pos <= min) { return pos; }\n\n  while (pos > min) {\n    if (!isSpace(this.src.charCodeAt(--pos))) { return pos + 1; }\n  }\n  return pos;\n};\n\n// Skip char codes from given position\nStateBlock.prototype.skipChars = function skipChars(pos, code) {\n  for (var max = this.src.length; pos < max; pos++) {\n    if (this.src.charCodeAt(pos) !== code) { break; }\n  }\n  return pos;\n};\n\n// Skip char codes reverse from given position - 1\nStateBlock.prototype.skipCharsBack = function skipCharsBack(pos, code, min) {\n  if (pos <= min) { return pos; }\n\n  while (pos > min) {\n    if (code !== this.src.charCodeAt(--pos)) { return pos + 1; }\n  }\n  return pos;\n};\n\n// cut lines range from source.\nStateBlock.prototype.getLines = function getLines(begin, end, indent, keepLastLF) {\n  var i, lineIndent, ch, first, last, queue, lineStart,\n      line = begin;\n\n  if (begin >= end) {\n    return '';\n  }\n\n  queue = new Array(end - begin);\n\n  for (i = 0; line < end; line++, i++) {\n    lineIndent = 0;\n    lineStart = first = this.bMarks[line];\n\n    if (line + 1 < end || keepLastLF) {\n      // No need for bounds check because we have fake entry on tail.\n      last = this.eMarks[line] + 1;\n    } else {\n      last = this.eMarks[line];\n    }\n\n    while (first < last && lineIndent < indent) {\n      ch = this.src.charCodeAt(first);\n\n      if (isSpace(ch)) {\n        if (ch === 0x09) {\n          lineIndent += 4 - (lineIndent + this.bsCount[line]) % 4;\n        } else {\n          lineIndent++;\n        }\n      } else if (first - lineStart < this.tShift[line]) {\n        // patched tShift masked characters to look like spaces (blockquotes, list markers)\n        lineIndent++;\n      } else {\n        break;\n      }\n\n      first++;\n    }\n\n    if (lineIndent > indent) {\n      // partially expanding tabs in code blocks, e.g '\\t\\tfoobar'\n      // with indent=2 becomes '  \\tfoobar'\n      queue[i] = new Array(lineIndent - indent + 1).join(' ') + this.src.slice(first, last);\n    } else {\n      queue[i] = this.src.slice(first, last);\n    }\n  }\n\n  return queue.join('');\n};\n\n// re-export Token class to use in block rules\nStateBlock.prototype.Token = Token;\n\n\nmodule.exports = StateBlock;\n"], "mappings": "AAAA;;AAEA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,UAAU,CAAC;AAC/B,IAAIC,OAAO,GAAGD,OAAO,CAAC,iBAAiB,CAAC,CAACC,OAAO;AAGhD,SAASC,UAAUA,CAACC,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,MAAM,EAAE;EACxC,IAAIC,EAAE,EAAEC,CAAC,EAAEC,KAAK,EAAEC,GAAG,EAAEC,GAAG,EAAEC,MAAM,EAAEC,MAAM,EAAEC,YAAY;EAExD,IAAI,CAACX,GAAG,GAAGA,GAAG;;EAEd;EACA,IAAI,CAACC,EAAE,GAAOA,EAAE;EAEhB,IAAI,CAACC,GAAG,GAAGA,GAAG;;EAEd;EACA;EACA;;EAEA,IAAI,CAACC,MAAM,GAAGA,MAAM;EAEpB,IAAI,CAACS,MAAM,GAAG,EAAE,CAAC,CAAE;EACnB,IAAI,CAACC,MAAM,GAAG,EAAE,CAAC,CAAE;EACnB,IAAI,CAACC,MAAM,GAAG,EAAE,CAAC,CAAE;EACnB,IAAI,CAACC,MAAM,GAAG,EAAE,CAAC,CAAE;;EAEnB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,CAACC,OAAO,GAAG,EAAE;;EAEjB;EACA,IAAI,CAACC,SAAS,GAAI,CAAC,CAAC,CAAC;EACA;EACrB,IAAI,CAACC,IAAI,GAAS,CAAC,CAAC,CAAC;EACrB,IAAI,CAACC,OAAO,GAAM,CAAC,CAAC,CAAC;EACrB,IAAI,CAACC,KAAK,GAAQ,KAAK,CAAC,CAAE;EAC1B,IAAI,CAACC,QAAQ,GAAK,CAAC,CAAC,CAAC,CAAC;EACtB,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEtB;EACA;EACA,IAAI,CAACC,UAAU,GAAG,MAAM;EAExB,IAAI,CAACC,KAAK,GAAG,CAAC;;EAEd;EACA,IAAI,CAACC,MAAM,GAAG,EAAE;;EAEhB;EACA;EACApB,CAAC,GAAG,IAAI,CAACL,GAAG;EACZW,YAAY,GAAG,KAAK;EAEpB,KAAKL,KAAK,GAAGC,GAAG,GAAGE,MAAM,GAAGC,MAAM,GAAG,CAAC,EAAEF,GAAG,GAAGH,CAAC,CAACqB,MAAM,EAAEnB,GAAG,GAAGC,GAAG,EAAED,GAAG,EAAE,EAAE;IACxEH,EAAE,GAAGC,CAAC,CAACsB,UAAU,CAACpB,GAAG,CAAC;IAEtB,IAAI,CAACI,YAAY,EAAE;MACjB,IAAIb,OAAO,CAACM,EAAE,CAAC,EAAE;QACfK,MAAM,EAAE;QAER,IAAIL,EAAE,KAAK,IAAI,EAAE;UACfM,MAAM,IAAI,CAAC,GAAGA,MAAM,GAAG,CAAC;QAC1B,CAAC,MAAM;UACLA,MAAM,EAAE;QACV;QACA;MACF,CAAC,MAAM;QACLC,YAAY,GAAG,IAAI;MACrB;IACF;IAEA,IAAIP,EAAE,KAAK,IAAI,IAAIG,GAAG,KAAKC,GAAG,GAAG,CAAC,EAAE;MAClC,IAAIJ,EAAE,KAAK,IAAI,EAAE;QAAEG,GAAG,EAAE;MAAE;MAC1B,IAAI,CAACK,MAAM,CAACgB,IAAI,CAACtB,KAAK,CAAC;MACvB,IAAI,CAACO,MAAM,CAACe,IAAI,CAACrB,GAAG,CAAC;MACrB,IAAI,CAACO,MAAM,CAACc,IAAI,CAACnB,MAAM,CAAC;MACxB,IAAI,CAACM,MAAM,CAACa,IAAI,CAAClB,MAAM,CAAC;MACxB,IAAI,CAACM,OAAO,CAACY,IAAI,CAAC,CAAC,CAAC;MAEpBjB,YAAY,GAAG,KAAK;MACpBF,MAAM,GAAG,CAAC;MACVC,MAAM,GAAG,CAAC;MACVJ,KAAK,GAAGC,GAAG,GAAG,CAAC;IACjB;EACF;;EAEA;EACA,IAAI,CAACK,MAAM,CAACgB,IAAI,CAACvB,CAAC,CAACqB,MAAM,CAAC;EAC1B,IAAI,CAACb,MAAM,CAACe,IAAI,CAACvB,CAAC,CAACqB,MAAM,CAAC;EAC1B,IAAI,CAACZ,MAAM,CAACc,IAAI,CAAC,CAAC,CAAC;EACnB,IAAI,CAACb,MAAM,CAACa,IAAI,CAAC,CAAC,CAAC;EACnB,IAAI,CAACZ,OAAO,CAACY,IAAI,CAAC,CAAC,CAAC;EAEpB,IAAI,CAACT,OAAO,GAAG,IAAI,CAACP,MAAM,CAACc,MAAM,GAAG,CAAC,CAAC,CAAC;AACzC;;AAEA;AACA;AACA3B,UAAU,CAAC8B,SAAS,CAACD,IAAI,GAAG,UAAUE,IAAI,EAAEC,GAAG,EAAEC,OAAO,EAAE;EACxD,IAAIC,KAAK,GAAG,IAAIrC,KAAK,CAACkC,IAAI,EAAEC,GAAG,EAAEC,OAAO,CAAC;EACzCC,KAAK,CAACC,KAAK,GAAG,IAAI;EAElB,IAAIF,OAAO,GAAG,CAAC,EAAE,IAAI,CAACR,KAAK,EAAE,CAAC,CAAC;EAC/BS,KAAK,CAACT,KAAK,GAAG,IAAI,CAACA,KAAK;EACxB,IAAIQ,OAAO,GAAG,CAAC,EAAE,IAAI,CAACR,KAAK,EAAE,CAAC,CAAC;;EAE/B,IAAI,CAACrB,MAAM,CAACyB,IAAI,CAACK,KAAK,CAAC;EACvB,OAAOA,KAAK;AACd,CAAC;AAEDlC,UAAU,CAAC8B,SAAS,CAACM,OAAO,GAAG,SAASA,OAAOA,CAACjB,IAAI,EAAE;EACpD,OAAO,IAAI,CAACN,MAAM,CAACM,IAAI,CAAC,GAAG,IAAI,CAACJ,MAAM,CAACI,IAAI,CAAC,IAAI,IAAI,CAACL,MAAM,CAACK,IAAI,CAAC;AACnE,CAAC;AAEDnB,UAAU,CAAC8B,SAAS,CAACO,cAAc,GAAG,SAASA,cAAcA,CAACC,IAAI,EAAE;EAClE,KAAK,IAAIC,GAAG,GAAG,IAAI,CAACnB,OAAO,EAAEkB,IAAI,GAAGC,GAAG,EAAED,IAAI,EAAE,EAAE;IAC/C,IAAI,IAAI,CAACzB,MAAM,CAACyB,IAAI,CAAC,GAAG,IAAI,CAACvB,MAAM,CAACuB,IAAI,CAAC,GAAG,IAAI,CAACxB,MAAM,CAACwB,IAAI,CAAC,EAAE;MAC7D;IACF;EACF;EACA,OAAOA,IAAI;AACb,CAAC;;AAED;AACAtC,UAAU,CAAC8B,SAAS,CAACU,UAAU,GAAG,SAASA,UAAUA,CAAChC,GAAG,EAAE;EACzD,IAAIH,EAAE;EAEN,KAAK,IAAIkC,GAAG,GAAG,IAAI,CAACtC,GAAG,CAAC0B,MAAM,EAAEnB,GAAG,GAAG+B,GAAG,EAAE/B,GAAG,EAAE,EAAE;IAChDH,EAAE,GAAG,IAAI,CAACJ,GAAG,CAAC2B,UAAU,CAACpB,GAAG,CAAC;IAC7B,IAAI,CAACT,OAAO,CAACM,EAAE,CAAC,EAAE;MAAE;IAAO;EAC7B;EACA,OAAOG,GAAG;AACZ,CAAC;;AAED;AACAR,UAAU,CAAC8B,SAAS,CAACW,cAAc,GAAG,SAASA,cAAcA,CAACjC,GAAG,EAAEkC,GAAG,EAAE;EACtE,IAAIlC,GAAG,IAAIkC,GAAG,EAAE;IAAE,OAAOlC,GAAG;EAAE;EAE9B,OAAOA,GAAG,GAAGkC,GAAG,EAAE;IAChB,IAAI,CAAC3C,OAAO,CAAC,IAAI,CAACE,GAAG,CAAC2B,UAAU,CAAC,EAAEpB,GAAG,CAAC,CAAC,EAAE;MAAE,OAAOA,GAAG,GAAG,CAAC;IAAE;EAC9D;EACA,OAAOA,GAAG;AACZ,CAAC;;AAED;AACAR,UAAU,CAAC8B,SAAS,CAACa,SAAS,GAAG,SAASA,SAASA,CAACnC,GAAG,EAAEoC,IAAI,EAAE;EAC7D,KAAK,IAAIL,GAAG,GAAG,IAAI,CAACtC,GAAG,CAAC0B,MAAM,EAAEnB,GAAG,GAAG+B,GAAG,EAAE/B,GAAG,EAAE,EAAE;IAChD,IAAI,IAAI,CAACP,GAAG,CAAC2B,UAAU,CAACpB,GAAG,CAAC,KAAKoC,IAAI,EAAE;MAAE;IAAO;EAClD;EACA,OAAOpC,GAAG;AACZ,CAAC;;AAED;AACAR,UAAU,CAAC8B,SAAS,CAACe,aAAa,GAAG,SAASA,aAAaA,CAACrC,GAAG,EAAEoC,IAAI,EAAEF,GAAG,EAAE;EAC1E,IAAIlC,GAAG,IAAIkC,GAAG,EAAE;IAAE,OAAOlC,GAAG;EAAE;EAE9B,OAAOA,GAAG,GAAGkC,GAAG,EAAE;IAChB,IAAIE,IAAI,KAAK,IAAI,CAAC3C,GAAG,CAAC2B,UAAU,CAAC,EAAEpB,GAAG,CAAC,EAAE;MAAE,OAAOA,GAAG,GAAG,CAAC;IAAE;EAC7D;EACA,OAAOA,GAAG;AACZ,CAAC;;AAED;AACAR,UAAU,CAAC8B,SAAS,CAACgB,QAAQ,GAAG,SAASA,QAAQA,CAACC,KAAK,EAAEC,GAAG,EAAEtC,MAAM,EAAEuC,UAAU,EAAE;EAChF,IAAIC,CAAC;IAAEC,UAAU;IAAE9C,EAAE;IAAE+C,KAAK;IAAEC,IAAI;IAAEC,KAAK;IAAEC,SAAS;IAChDpC,IAAI,GAAG4B,KAAK;EAEhB,IAAIA,KAAK,IAAIC,GAAG,EAAE;IAChB,OAAO,EAAE;EACX;EAEAM,KAAK,GAAG,IAAIE,KAAK,CAACR,GAAG,GAAGD,KAAK,CAAC;EAE9B,KAAKG,CAAC,GAAG,CAAC,EAAE/B,IAAI,GAAG6B,GAAG,EAAE7B,IAAI,EAAE,EAAE+B,CAAC,EAAE,EAAE;IACnCC,UAAU,GAAG,CAAC;IACdI,SAAS,GAAGH,KAAK,GAAG,IAAI,CAACvC,MAAM,CAACM,IAAI,CAAC;IAErC,IAAIA,IAAI,GAAG,CAAC,GAAG6B,GAAG,IAAIC,UAAU,EAAE;MAChC;MACAI,IAAI,GAAG,IAAI,CAACvC,MAAM,CAACK,IAAI,CAAC,GAAG,CAAC;IAC9B,CAAC,MAAM;MACLkC,IAAI,GAAG,IAAI,CAACvC,MAAM,CAACK,IAAI,CAAC;IAC1B;IAEA,OAAOiC,KAAK,GAAGC,IAAI,IAAIF,UAAU,GAAGzC,MAAM,EAAE;MAC1CL,EAAE,GAAG,IAAI,CAACJ,GAAG,CAAC2B,UAAU,CAACwB,KAAK,CAAC;MAE/B,IAAIrD,OAAO,CAACM,EAAE,CAAC,EAAE;QACf,IAAIA,EAAE,KAAK,IAAI,EAAE;UACf8C,UAAU,IAAI,CAAC,GAAG,CAACA,UAAU,GAAG,IAAI,CAAClC,OAAO,CAACE,IAAI,CAAC,IAAI,CAAC;QACzD,CAAC,MAAM;UACLgC,UAAU,EAAE;QACd;MACF,CAAC,MAAM,IAAIC,KAAK,GAAGG,SAAS,GAAG,IAAI,CAACxC,MAAM,CAACI,IAAI,CAAC,EAAE;QAChD;QACAgC,UAAU,EAAE;MACd,CAAC,MAAM;QACL;MACF;MAEAC,KAAK,EAAE;IACT;IAEA,IAAID,UAAU,GAAGzC,MAAM,EAAE;MACvB;MACA;MACA4C,KAAK,CAACJ,CAAC,CAAC,GAAG,IAAIM,KAAK,CAACL,UAAU,GAAGzC,MAAM,GAAG,CAAC,CAAC,CAAC+C,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAACxD,GAAG,CAACyD,KAAK,CAACN,KAAK,EAAEC,IAAI,CAAC;IACvF,CAAC,MAAM;MACLC,KAAK,CAACJ,CAAC,CAAC,GAAG,IAAI,CAACjD,GAAG,CAACyD,KAAK,CAACN,KAAK,EAAEC,IAAI,CAAC;IACxC;EACF;EAEA,OAAOC,KAAK,CAACG,IAAI,CAAC,EAAE,CAAC;AACvB,CAAC;;AAED;AACAzD,UAAU,CAAC8B,SAAS,CAACjC,KAAK,GAAGA,KAAK;AAGlC8D,MAAM,CAACC,OAAO,GAAG5D,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}