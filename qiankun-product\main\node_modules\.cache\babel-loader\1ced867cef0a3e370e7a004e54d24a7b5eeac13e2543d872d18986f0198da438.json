{"ast": null, "code": "var deburrLetter = require('./_deburrLetter'),\n  toString = require('./toString');\n\n/** Used to match Latin Unicode letters (excluding mathematical operators). */\nvar reLatin = /[\\xc0-\\xd6\\xd8-\\xf6\\xf8-\\xff\\u0100-\\u017f]/g;\n\n/** Used to compose unicode character classes. */\nvar rsComboMarksRange = \"\\\\u0300-\\\\u036f\",\n  reComboHalfMarksRange = \"\\\\ufe20-\\\\ufe2f\",\n  rsComboSymbolsRange = \"\\\\u20d0-\\\\u20ff\",\n  rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange;\n\n/** Used to compose unicode capture groups. */\nvar rsCombo = '[' + rsComboRange + ']';\n\n/**\n * Used to match [combining diacritical marks](https://en.wikipedia.org/wiki/Combining_Diacritical_Marks) and\n * [combining diacritical marks for symbols](https://en.wikipedia.org/wiki/Combining_Diacritical_Marks_for_Symbols).\n */\nvar reComboMark = RegExp(rsCombo, 'g');\n\n/**\n * Deburrs `string` by converting\n * [Latin-1 Supplement](https://en.wikipedia.org/wiki/Latin-1_Supplement_(Unicode_block)#Character_table)\n * and [Latin Extended-A](https://en.wikipedia.org/wiki/Latin_Extended-A)\n * letters to basic Latin letters and removing\n * [combining diacritical marks](https://en.wikipedia.org/wiki/Combining_Diacritical_Marks).\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to deburr.\n * @returns {string} Returns the deburred string.\n * @example\n *\n * _.deburr('déjà vu');\n * // => 'deja vu'\n */\nfunction deburr(string) {\n  string = toString(string);\n  return string && string.replace(reLatin, deburrLetter).replace(reComboMark, '');\n}\nmodule.exports = deburr;", "map": {"version": 3, "names": ["deburrLetter", "require", "toString", "reLatin", "rsComboMarksRange", "reComboHalfMarksRange", "rsComboSymbolsRange", "rsComboRange", "rsCombo", "reComboMark", "RegExp", "deburr", "string", "replace", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/deburr.js"], "sourcesContent": ["var deburrLetter = require('./_deburrLetter'),\n    toString = require('./toString');\n\n/** Used to match Latin Unicode letters (excluding mathematical operators). */\nvar reLatin = /[\\xc0-\\xd6\\xd8-\\xf6\\xf8-\\xff\\u0100-\\u017f]/g;\n\n/** Used to compose unicode character classes. */\nvar rsComboMarksRange = '\\\\u0300-\\\\u036f',\n    reComboHalfMarksRange = '\\\\ufe20-\\\\ufe2f',\n    rsComboSymbolsRange = '\\\\u20d0-\\\\u20ff',\n    rsComboRange = rsComboMarksRange + reComboHalfMarksRange + rsComboSymbolsRange;\n\n/** Used to compose unicode capture groups. */\nvar rsCombo = '[' + rsComboRange + ']';\n\n/**\n * Used to match [combining diacritical marks](https://en.wikipedia.org/wiki/Combining_Diacritical_Marks) and\n * [combining diacritical marks for symbols](https://en.wikipedia.org/wiki/Combining_Diacritical_Marks_for_Symbols).\n */\nvar reComboMark = RegExp(rsCombo, 'g');\n\n/**\n * Deburrs `string` by converting\n * [Latin-1 Supplement](https://en.wikipedia.org/wiki/Latin-1_Supplement_(Unicode_block)#Character_table)\n * and [Latin Extended-A](https://en.wikipedia.org/wiki/Latin_Extended-A)\n * letters to basic Latin letters and removing\n * [combining diacritical marks](https://en.wikipedia.org/wiki/Combining_Diacritical_Marks).\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to deburr.\n * @returns {string} Returns the deburred string.\n * @example\n *\n * _.deburr('déjà vu');\n * // => 'deja vu'\n */\nfunction deburr(string) {\n  string = toString(string);\n  return string && string.replace(reLatin, deburrLetter).replace(reComboMark, '');\n}\n\nmodule.exports = deburr;\n"], "mappings": "AAAA,IAAIA,YAAY,GAAGC,OAAO,CAAC,iBAAiB,CAAC;EACzCC,QAAQ,GAAGD,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA,IAAIE,OAAO,GAAG,6CAA6C;;AAE3D;AACA,IAAIC,iBAAiB,GAAG,iBAAiB;EACrCC,qBAAqB,GAAG,iBAAiB;EACzCC,mBAAmB,GAAG,iBAAiB;EACvCC,YAAY,GAAGH,iBAAiB,GAAGC,qBAAqB,GAAGC,mBAAmB;;AAElF;AACA,IAAIE,OAAO,GAAG,GAAG,GAAGD,YAAY,GAAG,GAAG;;AAEtC;AACA;AACA;AACA;AACA,IAAIE,WAAW,GAAGC,MAAM,CAACF,OAAO,EAAE,GAAG,CAAC;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,MAAMA,CAACC,MAAM,EAAE;EACtBA,MAAM,GAAGV,QAAQ,CAACU,MAAM,CAAC;EACzB,OAAOA,MAAM,IAAIA,MAAM,CAACC,OAAO,CAACV,OAAO,EAAEH,YAAY,CAAC,CAACa,OAAO,CAACJ,WAAW,EAAE,EAAE,CAAC;AACjF;AAEAK,MAAM,CAACC,OAAO,GAAGJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}