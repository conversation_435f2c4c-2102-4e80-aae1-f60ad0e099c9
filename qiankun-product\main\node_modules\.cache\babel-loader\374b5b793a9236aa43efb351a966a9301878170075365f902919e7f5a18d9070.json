{"ast": null, "code": "/**\n * Module dependencies.\n */\n\nvar Transport = require('../transport');\nvar parseqs = require('parseqs');\nvar parser = require('engine.io-parser');\nvar inherit = require('component-inherit');\nvar yeast = require('yeast');\nvar debug = require('debug')('engine.io-client:polling');\n\n/**\n * Module exports.\n */\n\nmodule.exports = Polling;\n\n/**\n * Is XHR2 supported?\n */\n\nvar hasXHR2 = function () {\n  var XMLHttpRequest = require('./xmlhttprequest');\n  var xhr = new XMLHttpRequest({\n    xdomain: false\n  });\n  return null != xhr.responseType;\n}();\n\n/**\n * Polling interface.\n *\n * @param {Object} opts\n * @api private\n */\n\nfunction Polling(opts) {\n  var forceBase64 = opts && opts.forceBase64;\n  if (!hasXHR2 || forceBase64) {\n    this.supportsBinary = false;\n  }\n  Transport.call(this, opts);\n}\n\n/**\n * Inherits from Transport.\n */\n\ninherit(Polling, Transport);\n\n/**\n * Transport name.\n */\n\nPolling.prototype.name = 'polling';\n\n/**\n * Opens the socket (triggers polling). We write a PING message to determine\n * when the transport is open.\n *\n * @api private\n */\n\nPolling.prototype.doOpen = function () {\n  this.poll();\n};\n\n/**\n * Pauses polling.\n *\n * @param {Function} callback upon buffers are flushed and transport is paused\n * @api private\n */\n\nPolling.prototype.pause = function (onPause) {\n  var self = this;\n  this.readyState = 'pausing';\n  function pause() {\n    debug('paused');\n    self.readyState = 'paused';\n    onPause();\n  }\n  if (this.polling || !this.writable) {\n    var total = 0;\n    if (this.polling) {\n      debug('we are currently polling - waiting to pause');\n      total++;\n      this.once('pollComplete', function () {\n        debug('pre-pause polling complete');\n        --total || pause();\n      });\n    }\n    if (!this.writable) {\n      debug('we are currently writing - waiting to pause');\n      total++;\n      this.once('drain', function () {\n        debug('pre-pause writing complete');\n        --total || pause();\n      });\n    }\n  } else {\n    pause();\n  }\n};\n\n/**\n * Starts polling cycle.\n *\n * @api public\n */\n\nPolling.prototype.poll = function () {\n  debug('polling');\n  this.polling = true;\n  this.doPoll();\n  this.emit('poll');\n};\n\n/**\n * Overloads onData to detect payloads.\n *\n * @api private\n */\n\nPolling.prototype.onData = function (data) {\n  var self = this;\n  debug('polling got data %s', data);\n  var callback = function callback(packet, index, total) {\n    // if its the first message we consider the transport open\n    if ('opening' === self.readyState && packet.type === 'open') {\n      self.onOpen();\n    }\n\n    // if its a close packet, we close the ongoing requests\n    if ('close' === packet.type) {\n      self.onClose();\n      return false;\n    }\n\n    // otherwise bypass onData and handle the message\n    self.onPacket(packet);\n  };\n\n  // decode payload\n  parser.decodePayload(data, this.socket.binaryType, callback);\n\n  // if an event did not trigger closing\n  if ('closed' !== this.readyState) {\n    // if we got data we're not polling\n    this.polling = false;\n    this.emit('pollComplete');\n    if ('open' === this.readyState) {\n      this.poll();\n    } else {\n      debug('ignoring poll - transport state \"%s\"', this.readyState);\n    }\n  }\n};\n\n/**\n * For polling, send a close packet.\n *\n * @api private\n */\n\nPolling.prototype.doClose = function () {\n  var self = this;\n  function close() {\n    debug('writing close packet');\n    self.write([{\n      type: 'close'\n    }]);\n  }\n  if ('open' === this.readyState) {\n    debug('transport open - closing');\n    close();\n  } else {\n    // in case we're trying to close while\n    // handshaking is in progress (GH-164)\n    debug('transport not open - deferring close');\n    this.once('open', close);\n  }\n};\n\n/**\n * Writes a packets payload.\n *\n * @param {Array} data packets\n * @param {Function} drain callback\n * @api private\n */\n\nPolling.prototype.write = function (packets) {\n  var self = this;\n  this.writable = false;\n  var callbackfn = function callbackfn() {\n    self.writable = true;\n    self.emit('drain');\n  };\n  parser.encodePayload(packets, this.supportsBinary, function (data) {\n    self.doWrite(data, callbackfn);\n  });\n};\n\n/**\n * Generates uri for connection.\n *\n * @api private\n */\n\nPolling.prototype.uri = function () {\n  var query = this.query || {};\n  var schema = this.secure ? 'https' : 'http';\n  var port = '';\n\n  // cache busting is forced\n  if (false !== this.timestampRequests) {\n    query[this.timestampParam] = yeast();\n  }\n  if (!this.supportsBinary && !query.sid) {\n    query.b64 = 1;\n  }\n  query = parseqs.encode(query);\n\n  // avoid port if default for schema\n  if (this.port && ('https' === schema && Number(this.port) !== 443 || 'http' === schema && Number(this.port) !== 80)) {\n    port = ':' + this.port;\n  }\n\n  // prepend ? to query\n  if (query.length) {\n    query = '?' + query;\n  }\n  var ipv6 = this.hostname.indexOf(':') !== -1;\n  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n};", "map": {"version": 3, "names": ["Transport", "require", "parseqs", "parser", "inherit", "yeast", "debug", "module", "exports", "Polling", "hasXHR2", "XMLHttpRequest", "xhr", "xdomain", "responseType", "opts", "forceBase64", "supportsBinary", "call", "prototype", "name", "doOpen", "poll", "pause", "onPause", "self", "readyState", "polling", "writable", "total", "once", "doPoll", "emit", "onData", "data", "callback", "packet", "index", "type", "onOpen", "onClose", "onPacket", "decodePayload", "socket", "binaryType", "doClose", "close", "write", "packets", "callbackfn", "encodePayload", "doWrite", "uri", "query", "schema", "secure", "port", "timestampRequests", "timestampParam", "sid", "b64", "encode", "Number", "length", "ipv6", "hostname", "indexOf", "path"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/engine.io-client@3.5.4/node_modules/engine.io-client/lib/transports/polling.js"], "sourcesContent": ["/**\n * Module dependencies.\n */\n\nvar Transport = require('../transport');\nvar parseqs = require('parseqs');\nvar parser = require('engine.io-parser');\nvar inherit = require('component-inherit');\nvar yeast = require('yeast');\nvar debug = require('debug')('engine.io-client:polling');\n\n/**\n * Module exports.\n */\n\nmodule.exports = Polling;\n\n/**\n * Is XHR2 supported?\n */\n\nvar hasXHR2 = (function () {\n  var XMLHttpRequest = require('./xmlhttprequest');\n  var xhr = new XMLHttpRequest({ xdomain: false });\n  return null != xhr.responseType;\n})();\n\n/**\n * Polling interface.\n *\n * @param {Object} opts\n * @api private\n */\n\nfunction Polling (opts) {\n  var forceBase64 = (opts && opts.forceBase64);\n  if (!hasXHR2 || forceBase64) {\n    this.supportsBinary = false;\n  }\n  Transport.call(this, opts);\n}\n\n/**\n * Inherits from Transport.\n */\n\ninherit(Polling, Transport);\n\n/**\n * Transport name.\n */\n\nPolling.prototype.name = 'polling';\n\n/**\n * Opens the socket (triggers polling). We write a PING message to determine\n * when the transport is open.\n *\n * @api private\n */\n\nPolling.prototype.doOpen = function () {\n  this.poll();\n};\n\n/**\n * Pauses polling.\n *\n * @param {Function} callback upon buffers are flushed and transport is paused\n * @api private\n */\n\nPolling.prototype.pause = function (onPause) {\n  var self = this;\n\n  this.readyState = 'pausing';\n\n  function pause () {\n    debug('paused');\n    self.readyState = 'paused';\n    onPause();\n  }\n\n  if (this.polling || !this.writable) {\n    var total = 0;\n\n    if (this.polling) {\n      debug('we are currently polling - waiting to pause');\n      total++;\n      this.once('pollComplete', function () {\n        debug('pre-pause polling complete');\n        --total || pause();\n      });\n    }\n\n    if (!this.writable) {\n      debug('we are currently writing - waiting to pause');\n      total++;\n      this.once('drain', function () {\n        debug('pre-pause writing complete');\n        --total || pause();\n      });\n    }\n  } else {\n    pause();\n  }\n};\n\n/**\n * Starts polling cycle.\n *\n * @api public\n */\n\nPolling.prototype.poll = function () {\n  debug('polling');\n  this.polling = true;\n  this.doPoll();\n  this.emit('poll');\n};\n\n/**\n * Overloads onData to detect payloads.\n *\n * @api private\n */\n\nPolling.prototype.onData = function (data) {\n  var self = this;\n  debug('polling got data %s', data);\n  var callback = function (packet, index, total) {\n    // if its the first message we consider the transport open\n    if ('opening' === self.readyState && packet.type === 'open') {\n      self.onOpen();\n    }\n\n    // if its a close packet, we close the ongoing requests\n    if ('close' === packet.type) {\n      self.onClose();\n      return false;\n    }\n\n    // otherwise bypass onData and handle the message\n    self.onPacket(packet);\n  };\n\n  // decode payload\n  parser.decodePayload(data, this.socket.binaryType, callback);\n\n  // if an event did not trigger closing\n  if ('closed' !== this.readyState) {\n    // if we got data we're not polling\n    this.polling = false;\n    this.emit('pollComplete');\n\n    if ('open' === this.readyState) {\n      this.poll();\n    } else {\n      debug('ignoring poll - transport state \"%s\"', this.readyState);\n    }\n  }\n};\n\n/**\n * For polling, send a close packet.\n *\n * @api private\n */\n\nPolling.prototype.doClose = function () {\n  var self = this;\n\n  function close () {\n    debug('writing close packet');\n    self.write([{ type: 'close' }]);\n  }\n\n  if ('open' === this.readyState) {\n    debug('transport open - closing');\n    close();\n  } else {\n    // in case we're trying to close while\n    // handshaking is in progress (GH-164)\n    debug('transport not open - deferring close');\n    this.once('open', close);\n  }\n};\n\n/**\n * Writes a packets payload.\n *\n * @param {Array} data packets\n * @param {Function} drain callback\n * @api private\n */\n\nPolling.prototype.write = function (packets) {\n  var self = this;\n  this.writable = false;\n  var callbackfn = function () {\n    self.writable = true;\n    self.emit('drain');\n  };\n\n  parser.encodePayload(packets, this.supportsBinary, function (data) {\n    self.doWrite(data, callbackfn);\n  });\n};\n\n/**\n * Generates uri for connection.\n *\n * @api private\n */\n\nPolling.prototype.uri = function () {\n  var query = this.query || {};\n  var schema = this.secure ? 'https' : 'http';\n  var port = '';\n\n  // cache busting is forced\n  if (false !== this.timestampRequests) {\n    query[this.timestampParam] = yeast();\n  }\n\n  if (!this.supportsBinary && !query.sid) {\n    query.b64 = 1;\n  }\n\n  query = parseqs.encode(query);\n\n  // avoid port if default for schema\n  if (this.port && (('https' === schema && Number(this.port) !== 443) ||\n     ('http' === schema && Number(this.port) !== 80))) {\n    port = ':' + this.port;\n  }\n\n  // prepend ? to query\n  if (query.length) {\n    query = '?' + query;\n  }\n\n  var ipv6 = this.hostname.indexOf(':') !== -1;\n  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;AACvC,IAAIC,OAAO,GAAGD,OAAO,CAAC,SAAS,CAAC;AAChC,IAAIE,MAAM,GAAGF,OAAO,CAAC,kBAAkB,CAAC;AACxC,IAAIG,OAAO,GAAGH,OAAO,CAAC,mBAAmB,CAAC;AAC1C,IAAII,KAAK,GAAGJ,OAAO,CAAC,OAAO,CAAC;AAC5B,IAAIK,KAAK,GAAGL,OAAO,CAAC,OAAO,CAAC,CAAC,0BAA0B,CAAC;;AAExD;AACA;AACA;;AAEAM,MAAM,CAACC,OAAO,GAAGC,OAAO;;AAExB;AACA;AACA;;AAEA,IAAIC,OAAO,GAAI,YAAY;EACzB,IAAIC,cAAc,GAAGV,OAAO,CAAC,kBAAkB,CAAC;EAChD,IAAIW,GAAG,GAAG,IAAID,cAAc,CAAC;IAAEE,OAAO,EAAE;EAAM,CAAC,CAAC;EAChD,OAAO,IAAI,IAAID,GAAG,CAACE,YAAY;AACjC,CAAC,CAAE,CAAC;;AAEJ;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASL,OAAOA,CAAEM,IAAI,EAAE;EACtB,IAAIC,WAAW,GAAID,IAAI,IAAIA,IAAI,CAACC,WAAY;EAC5C,IAAI,CAACN,OAAO,IAAIM,WAAW,EAAE;IAC3B,IAAI,CAACC,cAAc,GAAG,KAAK;EAC7B;EACAjB,SAAS,CAACkB,IAAI,CAAC,IAAI,EAAEH,IAAI,CAAC;AAC5B;;AAEA;AACA;AACA;;AAEAX,OAAO,CAACK,OAAO,EAAET,SAAS,CAAC;;AAE3B;AACA;AACA;;AAEAS,OAAO,CAACU,SAAS,CAACC,IAAI,GAAG,SAAS;;AAElC;AACA;AACA;AACA;AACA;AACA;;AAEAX,OAAO,CAACU,SAAS,CAACE,MAAM,GAAG,YAAY;EACrC,IAAI,CAACC,IAAI,CAAC,CAAC;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEAb,OAAO,CAACU,SAAS,CAACI,KAAK,GAAG,UAAUC,OAAO,EAAE;EAC3C,IAAIC,IAAI,GAAG,IAAI;EAEf,IAAI,CAACC,UAAU,GAAG,SAAS;EAE3B,SAASH,KAAKA,CAAA,EAAI;IAChBjB,KAAK,CAAC,QAAQ,CAAC;IACfmB,IAAI,CAACC,UAAU,GAAG,QAAQ;IAC1BF,OAAO,CAAC,CAAC;EACX;EAEA,IAAI,IAAI,CAACG,OAAO,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;IAClC,IAAIC,KAAK,GAAG,CAAC;IAEb,IAAI,IAAI,CAACF,OAAO,EAAE;MAChBrB,KAAK,CAAC,6CAA6C,CAAC;MACpDuB,KAAK,EAAE;MACP,IAAI,CAACC,IAAI,CAAC,cAAc,EAAE,YAAY;QACpCxB,KAAK,CAAC,4BAA4B,CAAC;QACnC,EAAEuB,KAAK,IAAIN,KAAK,CAAC,CAAC;MACpB,CAAC,CAAC;IACJ;IAEA,IAAI,CAAC,IAAI,CAACK,QAAQ,EAAE;MAClBtB,KAAK,CAAC,6CAA6C,CAAC;MACpDuB,KAAK,EAAE;MACP,IAAI,CAACC,IAAI,CAAC,OAAO,EAAE,YAAY;QAC7BxB,KAAK,CAAC,4BAA4B,CAAC;QACnC,EAAEuB,KAAK,IAAIN,KAAK,CAAC,CAAC;MACpB,CAAC,CAAC;IACJ;EACF,CAAC,MAAM;IACLA,KAAK,CAAC,CAAC;EACT;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEAd,OAAO,CAACU,SAAS,CAACG,IAAI,GAAG,YAAY;EACnChB,KAAK,CAAC,SAAS,CAAC;EAChB,IAAI,CAACqB,OAAO,GAAG,IAAI;EACnB,IAAI,CAACI,MAAM,CAAC,CAAC;EACb,IAAI,CAACC,IAAI,CAAC,MAAM,CAAC;AACnB,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEAvB,OAAO,CAACU,SAAS,CAACc,MAAM,GAAG,UAAUC,IAAI,EAAE;EACzC,IAAIT,IAAI,GAAG,IAAI;EACfnB,KAAK,CAAC,qBAAqB,EAAE4B,IAAI,CAAC;EAClC,IAAIC,QAAQ,GAAG,SAAXA,QAAQA,CAAaC,MAAM,EAAEC,KAAK,EAAER,KAAK,EAAE;IAC7C;IACA,IAAI,SAAS,KAAKJ,IAAI,CAACC,UAAU,IAAIU,MAAM,CAACE,IAAI,KAAK,MAAM,EAAE;MAC3Db,IAAI,CAACc,MAAM,CAAC,CAAC;IACf;;IAEA;IACA,IAAI,OAAO,KAAKH,MAAM,CAACE,IAAI,EAAE;MAC3Bb,IAAI,CAACe,OAAO,CAAC,CAAC;MACd,OAAO,KAAK;IACd;;IAEA;IACAf,IAAI,CAACgB,QAAQ,CAACL,MAAM,CAAC;EACvB,CAAC;;EAED;EACAjC,MAAM,CAACuC,aAAa,CAACR,IAAI,EAAE,IAAI,CAACS,MAAM,CAACC,UAAU,EAAET,QAAQ,CAAC;;EAE5D;EACA,IAAI,QAAQ,KAAK,IAAI,CAACT,UAAU,EAAE;IAChC;IACA,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACK,IAAI,CAAC,cAAc,CAAC;IAEzB,IAAI,MAAM,KAAK,IAAI,CAACN,UAAU,EAAE;MAC9B,IAAI,CAACJ,IAAI,CAAC,CAAC;IACb,CAAC,MAAM;MACLhB,KAAK,CAAC,sCAAsC,EAAE,IAAI,CAACoB,UAAU,CAAC;IAChE;EACF;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEAjB,OAAO,CAACU,SAAS,CAAC0B,OAAO,GAAG,YAAY;EACtC,IAAIpB,IAAI,GAAG,IAAI;EAEf,SAASqB,KAAKA,CAAA,EAAI;IAChBxC,KAAK,CAAC,sBAAsB,CAAC;IAC7BmB,IAAI,CAACsB,KAAK,CAAC,CAAC;MAAET,IAAI,EAAE;IAAQ,CAAC,CAAC,CAAC;EACjC;EAEA,IAAI,MAAM,KAAK,IAAI,CAACZ,UAAU,EAAE;IAC9BpB,KAAK,CAAC,0BAA0B,CAAC;IACjCwC,KAAK,CAAC,CAAC;EACT,CAAC,MAAM;IACL;IACA;IACAxC,KAAK,CAAC,sCAAsC,CAAC;IAC7C,IAAI,CAACwB,IAAI,CAAC,MAAM,EAAEgB,KAAK,CAAC;EAC1B;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEArC,OAAO,CAACU,SAAS,CAAC4B,KAAK,GAAG,UAAUC,OAAO,EAAE;EAC3C,IAAIvB,IAAI,GAAG,IAAI;EACf,IAAI,CAACG,QAAQ,GAAG,KAAK;EACrB,IAAIqB,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAe;IAC3BxB,IAAI,CAACG,QAAQ,GAAG,IAAI;IACpBH,IAAI,CAACO,IAAI,CAAC,OAAO,CAAC;EACpB,CAAC;EAED7B,MAAM,CAAC+C,aAAa,CAACF,OAAO,EAAE,IAAI,CAAC/B,cAAc,EAAE,UAAUiB,IAAI,EAAE;IACjET,IAAI,CAAC0B,OAAO,CAACjB,IAAI,EAAEe,UAAU,CAAC;EAChC,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEAxC,OAAO,CAACU,SAAS,CAACiC,GAAG,GAAG,YAAY;EAClC,IAAIC,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,CAAC,CAAC;EAC5B,IAAIC,MAAM,GAAG,IAAI,CAACC,MAAM,GAAG,OAAO,GAAG,MAAM;EAC3C,IAAIC,IAAI,GAAG,EAAE;;EAEb;EACA,IAAI,KAAK,KAAK,IAAI,CAACC,iBAAiB,EAAE;IACpCJ,KAAK,CAAC,IAAI,CAACK,cAAc,CAAC,GAAGrD,KAAK,CAAC,CAAC;EACtC;EAEA,IAAI,CAAC,IAAI,CAACY,cAAc,IAAI,CAACoC,KAAK,CAACM,GAAG,EAAE;IACtCN,KAAK,CAACO,GAAG,GAAG,CAAC;EACf;EAEAP,KAAK,GAAGnD,OAAO,CAAC2D,MAAM,CAACR,KAAK,CAAC;;EAE7B;EACA,IAAI,IAAI,CAACG,IAAI,KAAM,OAAO,KAAKF,MAAM,IAAIQ,MAAM,CAAC,IAAI,CAACN,IAAI,CAAC,KAAK,GAAG,IAC9D,MAAM,KAAKF,MAAM,IAAIQ,MAAM,CAAC,IAAI,CAACN,IAAI,CAAC,KAAK,EAAG,CAAC,EAAE;IACnDA,IAAI,GAAG,GAAG,GAAG,IAAI,CAACA,IAAI;EACxB;;EAEA;EACA,IAAIH,KAAK,CAACU,MAAM,EAAE;IAChBV,KAAK,GAAG,GAAG,GAAGA,KAAK;EACrB;EAEA,IAAIW,IAAI,GAAG,IAAI,CAACC,QAAQ,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;EAC5C,OAAOZ,MAAM,GAAG,KAAK,IAAIU,IAAI,GAAG,GAAG,GAAG,IAAI,CAACC,QAAQ,GAAG,GAAG,GAAG,IAAI,CAACA,QAAQ,CAAC,GAAGT,IAAI,GAAG,IAAI,CAACW,IAAI,GAAGd,KAAK;AACvG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}