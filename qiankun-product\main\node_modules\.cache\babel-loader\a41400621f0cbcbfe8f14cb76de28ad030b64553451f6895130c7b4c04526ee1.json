{"ast": null, "code": "'use strict';\n\nmodule.exports = function emoji_html(tokens, idx /*, options, env */) {\n  return tokens[idx].content;\n};", "map": {"version": 3, "names": ["module", "exports", "emoji_html", "tokens", "idx", "content"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it-emoji@2.0.2/node_modules/markdown-it-emoji/lib/render.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = function emoji_html(tokens, idx /*, options, env */) {\n  return tokens[idx].content;\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,SAASC,UAAUA,CAACC,MAAM,EAAEC,GAAG,CAAC,qBAAqB;EACpE,OAAOD,MAAM,CAACC,GAAG,CAAC,CAACC,OAAO;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}