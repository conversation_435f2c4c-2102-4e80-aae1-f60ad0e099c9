{"ast": null, "code": "\"use strict\";\n\nvar StringReader = require(\"./stringReader.js\");\nvar NodeBufferReader = require(\"./nodeBufferReader.js\");\nvar Uint8ArrayReader = require(\"./uint8ArrayReader.js\");\nvar ArrayReader = require(\"./arrayReader.js\");\nvar utils = require(\"./utils.js\");\nvar sig = require(\"./signature.js\");\nvar ZipEntry = require(\"./zipEntry.js\");\nvar support = require(\"./support.js\");\n//  class ZipEntries {{{\n/**\n * All the entries in the zip file.\n * @constructor\n * @param {String|ArrayBuffer|Uint8Array} data the binary stream to load.\n * @param {Object} loadOptions Options for loading the stream.\n */\nfunction ZipEntries(data, loadOptions) {\n  this.files = [];\n  this.loadOptions = loadOptions;\n  if (data) {\n    this.load(data);\n  }\n}\nZipEntries.prototype = {\n  /**\n   * Check that the reader is on the speficied signature.\n   * @param {string} expectedSignature the expected signature.\n   * @throws {Error} if it is an other signature.\n   */\n  checkSignature: function checkSignature(expectedSignature) {\n    var signature = this.reader.readString(4);\n    if (signature !== expectedSignature) {\n      throw new Error(\"Corrupted zip or bug : unexpected signature \" + \"(\" + utils.pretty(signature) + \", expected \" + utils.pretty(expectedSignature) + \")\");\n    }\n  },\n  /**\n   * Check if the given signature is at the given index.\n   * @param {number} askedIndex the index to check.\n   * @param {string} expectedSignature the signature to expect.\n   * @return {boolean} true if the signature is here, false otherwise.\n   */\n  isSignature: function isSignature(askedIndex, expectedSignature) {\n    var currentIndex = this.reader.index;\n    this.reader.setIndex(askedIndex);\n    var signature = this.reader.readString(4);\n    var result = signature === expectedSignature;\n    this.reader.setIndex(currentIndex);\n    return result;\n  },\n  /**\n   * Read the end of the central directory.\n   */\n  readBlockEndOfCentral: function readBlockEndOfCentral() {\n    this.diskNumber = this.reader.readInt(2);\n    this.diskWithCentralDirStart = this.reader.readInt(2);\n    this.centralDirRecordsOnThisDisk = this.reader.readInt(2);\n    this.centralDirRecords = this.reader.readInt(2);\n    this.centralDirSize = this.reader.readInt(4);\n    this.centralDirOffset = this.reader.readInt(4);\n    this.zipCommentLength = this.reader.readInt(2);\n    // warning : the encoding depends of the system locale\n    // On a linux machine with LANG=en_US.utf8, this field is utf8 encoded.\n    // On a windows machine, this field is encoded with the localized windows code page.\n    var zipComment = this.reader.readData(this.zipCommentLength);\n    var decodeParamType = support.uint8array ? \"uint8array\" : \"array\";\n    // To get consistent behavior with the generation part, we will assume that\n    // this is utf8 encoded unless specified otherwise.\n    var decodeContent = utils.transformTo(decodeParamType, zipComment);\n    this.zipComment = this.loadOptions.decodeFileName(decodeContent);\n  },\n  /**\n   * Read the end of the Zip 64 central directory.\n   * Not merged with the method readEndOfCentral :\n   * The end of central can coexist with its Zip64 brother,\n   * I don't want to read the wrong number of bytes !\n   */\n  readBlockZip64EndOfCentral: function readBlockZip64EndOfCentral() {\n    this.zip64EndOfCentralSize = this.reader.readInt(8);\n    this.versionMadeBy = this.reader.readString(2);\n    this.versionNeeded = this.reader.readInt(2);\n    this.diskNumber = this.reader.readInt(4);\n    this.diskWithCentralDirStart = this.reader.readInt(4);\n    this.centralDirRecordsOnThisDisk = this.reader.readInt(8);\n    this.centralDirRecords = this.reader.readInt(8);\n    this.centralDirSize = this.reader.readInt(8);\n    this.centralDirOffset = this.reader.readInt(8);\n    this.zip64ExtensibleData = {};\n    var extraDataSize = this.zip64EndOfCentralSize - 44;\n    var index = 0;\n    var extraFieldId, extraFieldLength, extraFieldValue;\n    while (index < extraDataSize) {\n      extraFieldId = this.reader.readInt(2);\n      extraFieldLength = this.reader.readInt(4);\n      extraFieldValue = this.reader.readString(extraFieldLength);\n      this.zip64ExtensibleData[extraFieldId] = {\n        id: extraFieldId,\n        length: extraFieldLength,\n        value: extraFieldValue\n      };\n    }\n  },\n  /**\n   * Read the end of the Zip 64 central directory locator.\n   */\n  readBlockZip64EndOfCentralLocator: function readBlockZip64EndOfCentralLocator() {\n    this.diskWithZip64CentralDirStart = this.reader.readInt(4);\n    this.relativeOffsetEndOfZip64CentralDir = this.reader.readInt(8);\n    this.disksCount = this.reader.readInt(4);\n    if (this.disksCount > 1) {\n      throw new Error(\"Multi-volumes zip are not supported\");\n    }\n  },\n  /**\n   * Read the local files, based on the offset read in the central part.\n   */\n  readLocalFiles: function readLocalFiles() {\n    var i, file;\n    for (i = 0; i < this.files.length; i++) {\n      file = this.files[i];\n      this.reader.setIndex(file.localHeaderOffset);\n      this.checkSignature(sig.LOCAL_FILE_HEADER);\n      file.readLocalPart(this.reader);\n      file.handleUTF8();\n      file.processAttributes();\n    }\n  },\n  /**\n   * Read the central directory.\n   */\n  readCentralDir: function readCentralDir() {\n    var file;\n    this.reader.setIndex(this.centralDirOffset);\n    while (this.reader.readString(4) === sig.CENTRAL_FILE_HEADER) {\n      file = new ZipEntry({\n        zip64: this.zip64\n      }, this.loadOptions);\n      file.readCentralPart(this.reader);\n      this.files.push(file);\n    }\n    if (this.centralDirRecords !== this.files.length) {\n      if (this.centralDirRecords !== 0 && this.files.length === 0) {\n        // We expected some records but couldn't find ANY.\n        // This is really suspicious, as if something went wrong.\n        throw new Error(\"Corrupted zip or bug: expected \" + this.centralDirRecords + \" records in central dir, got \" + this.files.length);\n      } else {\n        // We found some records but not all.\n        // Something is wrong but we got something for the user: no error here.\n        // console.warn(\"expected\", this.centralDirRecords, \"records in central dir, got\", this.files.length);\n      }\n    }\n  },\n  /**\n   * Read the end of central directory.\n   */\n  readEndOfCentral: function readEndOfCentral() {\n    var offset = this.reader.lastIndexOfSignature(sig.CENTRAL_DIRECTORY_END);\n    if (offset < 0) {\n      // Check if the content is a truncated zip or complete garbage.\n      // A \"LOCAL_FILE_HEADER\" is not required at the beginning (auto\n      // extractible zip for example) but it can give a good hint.\n      // If an ajax request was used without responseType, we will also\n      // get unreadable data.\n      var isGarbage = !this.isSignature(0, sig.LOCAL_FILE_HEADER);\n      if (isGarbage) {\n        throw new Error(\"Can't find end of central directory : is this a zip file ?\");\n      } else {\n        throw new Error(\"Corrupted zip : can't find end of central directory\");\n      }\n    }\n    this.reader.setIndex(offset);\n    var endOfCentralDirOffset = offset;\n    this.checkSignature(sig.CENTRAL_DIRECTORY_END);\n    this.readBlockEndOfCentral();\n\n    /* extract from the zip spec :\n              4)  If one of the fields in the end of central directory\n                  record is too small to hold required data, the field\n                  should be set to -1 (0xFFFF or 0xFFFFFFFF) and the\n                  ZIP64 format record should be created.\n              5)  The end of central directory record and the\n                  Zip64 end of central directory locator record must\n                  reside on the same disk when splitting or spanning\n                  an archive.\n           */\n    if (this.diskNumber === utils.MAX_VALUE_16BITS || this.diskWithCentralDirStart === utils.MAX_VALUE_16BITS || this.centralDirRecordsOnThisDisk === utils.MAX_VALUE_16BITS || this.centralDirRecords === utils.MAX_VALUE_16BITS || this.centralDirSize === utils.MAX_VALUE_32BITS || this.centralDirOffset === utils.MAX_VALUE_32BITS) {\n      this.zip64 = true;\n\n      /*\n               Warning : the zip64 extension is supported, but ONLY if the 64bits integer read from\n               the zip file can fit into a 32bits integer. This cannot be solved : Javascript represents\n               all numbers as 64-bit double precision IEEE 754 floating point numbers.\n               So, we have 53bits for integers and bitwise operations treat everything as 32bits.\n               see https://developer.mozilla.org/en-US/docs/JavaScript/Reference/Operators/Bitwise_Operators\n               and http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-262.pdf section 8.5\n               */\n\n      // should look for a zip64 EOCD locator\n      offset = this.reader.lastIndexOfSignature(sig.ZIP64_CENTRAL_DIRECTORY_LOCATOR);\n      if (offset < 0) {\n        throw new Error(\"Corrupted zip : can't find the ZIP64 end of central directory locator\");\n      }\n      this.reader.setIndex(offset);\n      this.checkSignature(sig.ZIP64_CENTRAL_DIRECTORY_LOCATOR);\n      this.readBlockZip64EndOfCentralLocator();\n\n      // now the zip64 EOCD record\n      if (!this.isSignature(this.relativeOffsetEndOfZip64CentralDir, sig.ZIP64_CENTRAL_DIRECTORY_END)) {\n        // console.warn(\"ZIP64 end of central directory not where expected.\");\n        this.relativeOffsetEndOfZip64CentralDir = this.reader.lastIndexOfSignature(sig.ZIP64_CENTRAL_DIRECTORY_END);\n        if (this.relativeOffsetEndOfZip64CentralDir < 0) {\n          throw new Error(\"Corrupted zip : can't find the ZIP64 end of central directory\");\n        }\n      }\n      this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir);\n      this.checkSignature(sig.ZIP64_CENTRAL_DIRECTORY_END);\n      this.readBlockZip64EndOfCentral();\n    }\n    var expectedEndOfCentralDirOffset = this.centralDirOffset + this.centralDirSize;\n    if (this.zip64) {\n      expectedEndOfCentralDirOffset += 20; // end of central dir 64 locator\n      expectedEndOfCentralDirOffset += 12 /* should not include the leading 12 bytes */ + this.zip64EndOfCentralSize;\n    }\n    var extraBytes = endOfCentralDirOffset - expectedEndOfCentralDirOffset;\n    if (extraBytes > 0) {\n      // console.warn(extraBytes, \"extra bytes at beginning or within zipfile\");\n      if (this.isSignature(endOfCentralDirOffset, sig.CENTRAL_FILE_HEADER)) {\n        // The offsets seem wrong, but we have something at the specified offset.\n        // So… we keep it.\n      } else {\n        // the offset is wrong, update the \"zero\" of the reader\n        // this happens if data has been prepended (crx files for example)\n        this.reader.zero = extraBytes;\n      }\n    } else if (extraBytes < 0) {\n      throw new Error(\"Corrupted zip: missing \" + Math.abs(extraBytes) + \" bytes.\");\n    }\n  },\n  prepareReader: function prepareReader(data) {\n    var type = utils.getTypeOf(data);\n    utils.checkSupport(type);\n    if (type === \"string\" && !support.uint8array) {\n      this.reader = new StringReader(data, this.loadOptions.optimizedBinaryString);\n    } else if (type === \"nodebuffer\") {\n      this.reader = new NodeBufferReader(data);\n    } else if (support.uint8array) {\n      this.reader = new Uint8ArrayReader(utils.transformTo(\"uint8array\", data));\n    } else if (support.array) {\n      this.reader = new ArrayReader(utils.transformTo(\"array\", data));\n    } else {\n      throw new Error(\"Unexpected error: unsupported type '\" + type + \"'\");\n    }\n  },\n  /**\n   * Read a zip file and create ZipEntries.\n   * @param {String|ArrayBuffer|Uint8Array|Buffer} data the binary string representing a zip file.\n   */\n  load: function load(data) {\n    this.prepareReader(data);\n    this.readEndOfCentral();\n    this.readCentralDir();\n    this.readLocalFiles();\n  }\n};\n// }}} end of ZipEntries\nmodule.exports = ZipEntries;", "map": {"version": 3, "names": ["StringReader", "require", "NodeBufferReader", "Uint8ArrayReader", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "utils", "sig", "ZipEntry", "support", "ZipEntries", "data", "loadOptions", "files", "load", "prototype", "checkSignature", "expectedSignature", "signature", "reader", "readString", "Error", "pretty", "isSignature", "askedIndex", "currentIndex", "index", "setIndex", "result", "readBlockEndOfCentral", "diskNumber", "readInt", "diskWithCentralDirStart", "centralDirRecordsOnThisDisk", "centralDirRecords", "centralDirSize", "centralDirOffset", "zipCommentLength", "zipComment", "readData", "decodeParamType", "uint8array", "decodeContent", "transformTo", "decodeFileName", "readBlockZip64EndOfCentral", "zip64EndOfCentralSize", "versionMadeBy", "versionNeeded", "zip64ExtensibleData", "extraDataSize", "extraFieldId", "extraField<PERSON>ength", "extraFieldValue", "id", "length", "value", "readBlockZip64EndOfCentralLocator", "diskWithZip64CentralDirStart", "relativeOffsetEndOfZip64CentralDir", "disksCount", "readLocalFiles", "i", "file", "localHeaderOffset", "LOCAL_FILE_HEADER", "readLocalPart", "handleUTF8", "processAttributes", "readCentralDir", "CENTRAL_FILE_HEADER", "zip64", "readCentralPart", "push", "readEndOfCentral", "offset", "lastIndexOfSignature", "CENTRAL_DIRECTORY_END", "isGarbage", "endOfCentralDirOffset", "MAX_VALUE_16BITS", "MAX_VALUE_32BITS", "ZIP64_CENTRAL_DIRECTORY_LOCATOR", "ZIP64_CENTRAL_DIRECTORY_END", "expectedEndOfCentralDirOffset", "extraBytes", "zero", "Math", "abs", "prepare<PERSON>eader", "type", "getTypeOf", "checkSupport", "optimizedBinaryString", "array", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/pizzip@3.1.7/node_modules/pizzip/js/zipEntries.js"], "sourcesContent": ["\"use strict\";\n\nvar StringReader = require(\"./stringReader.js\");\nvar NodeBufferReader = require(\"./nodeBufferReader.js\");\nvar Uint8ArrayReader = require(\"./uint8ArrayReader.js\");\nvar ArrayReader = require(\"./arrayReader.js\");\nvar utils = require(\"./utils.js\");\nvar sig = require(\"./signature.js\");\nvar ZipEntry = require(\"./zipEntry.js\");\nvar support = require(\"./support.js\");\n//  class ZipEntries {{{\n/**\n * All the entries in the zip file.\n * @constructor\n * @param {String|ArrayBuffer|Uint8Array} data the binary stream to load.\n * @param {Object} loadOptions Options for loading the stream.\n */\nfunction ZipEntries(data, loadOptions) {\n  this.files = [];\n  this.loadOptions = loadOptions;\n  if (data) {\n    this.load(data);\n  }\n}\nZipEntries.prototype = {\n  /**\n   * Check that the reader is on the speficied signature.\n   * @param {string} expectedSignature the expected signature.\n   * @throws {Error} if it is an other signature.\n   */\n  checkSignature: function checkSignature(expectedSignature) {\n    var signature = this.reader.readString(4);\n    if (signature !== expectedSignature) {\n      throw new Error(\"Corrupted zip or bug : unexpected signature \" + \"(\" + utils.pretty(signature) + \", expected \" + utils.pretty(expectedSignature) + \")\");\n    }\n  },\n  /**\n   * Check if the given signature is at the given index.\n   * @param {number} askedIndex the index to check.\n   * @param {string} expectedSignature the signature to expect.\n   * @return {boolean} true if the signature is here, false otherwise.\n   */\n  isSignature: function isSignature(askedIndex, expectedSignature) {\n    var currentIndex = this.reader.index;\n    this.reader.setIndex(askedIndex);\n    var signature = this.reader.readString(4);\n    var result = signature === expectedSignature;\n    this.reader.setIndex(currentIndex);\n    return result;\n  },\n  /**\n   * Read the end of the central directory.\n   */\n  readBlockEndOfCentral: function readBlockEndOfCentral() {\n    this.diskNumber = this.reader.readInt(2);\n    this.diskWithCentralDirStart = this.reader.readInt(2);\n    this.centralDirRecordsOnThisDisk = this.reader.readInt(2);\n    this.centralDirRecords = this.reader.readInt(2);\n    this.centralDirSize = this.reader.readInt(4);\n    this.centralDirOffset = this.reader.readInt(4);\n    this.zipCommentLength = this.reader.readInt(2);\n    // warning : the encoding depends of the system locale\n    // On a linux machine with LANG=en_US.utf8, this field is utf8 encoded.\n    // On a windows machine, this field is encoded with the localized windows code page.\n    var zipComment = this.reader.readData(this.zipCommentLength);\n    var decodeParamType = support.uint8array ? \"uint8array\" : \"array\";\n    // To get consistent behavior with the generation part, we will assume that\n    // this is utf8 encoded unless specified otherwise.\n    var decodeContent = utils.transformTo(decodeParamType, zipComment);\n    this.zipComment = this.loadOptions.decodeFileName(decodeContent);\n  },\n  /**\n   * Read the end of the Zip 64 central directory.\n   * Not merged with the method readEndOfCentral :\n   * The end of central can coexist with its Zip64 brother,\n   * I don't want to read the wrong number of bytes !\n   */\n  readBlockZip64EndOfCentral: function readBlockZip64EndOfCentral() {\n    this.zip64EndOfCentralSize = this.reader.readInt(8);\n    this.versionMadeBy = this.reader.readString(2);\n    this.versionNeeded = this.reader.readInt(2);\n    this.diskNumber = this.reader.readInt(4);\n    this.diskWithCentralDirStart = this.reader.readInt(4);\n    this.centralDirRecordsOnThisDisk = this.reader.readInt(8);\n    this.centralDirRecords = this.reader.readInt(8);\n    this.centralDirSize = this.reader.readInt(8);\n    this.centralDirOffset = this.reader.readInt(8);\n    this.zip64ExtensibleData = {};\n    var extraDataSize = this.zip64EndOfCentralSize - 44;\n    var index = 0;\n    var extraFieldId, extraFieldLength, extraFieldValue;\n    while (index < extraDataSize) {\n      extraFieldId = this.reader.readInt(2);\n      extraFieldLength = this.reader.readInt(4);\n      extraFieldValue = this.reader.readString(extraFieldLength);\n      this.zip64ExtensibleData[extraFieldId] = {\n        id: extraFieldId,\n        length: extraFieldLength,\n        value: extraFieldValue\n      };\n    }\n  },\n  /**\n   * Read the end of the Zip 64 central directory locator.\n   */\n  readBlockZip64EndOfCentralLocator: function readBlockZip64EndOfCentralLocator() {\n    this.diskWithZip64CentralDirStart = this.reader.readInt(4);\n    this.relativeOffsetEndOfZip64CentralDir = this.reader.readInt(8);\n    this.disksCount = this.reader.readInt(4);\n    if (this.disksCount > 1) {\n      throw new Error(\"Multi-volumes zip are not supported\");\n    }\n  },\n  /**\n   * Read the local files, based on the offset read in the central part.\n   */\n  readLocalFiles: function readLocalFiles() {\n    var i, file;\n    for (i = 0; i < this.files.length; i++) {\n      file = this.files[i];\n      this.reader.setIndex(file.localHeaderOffset);\n      this.checkSignature(sig.LOCAL_FILE_HEADER);\n      file.readLocalPart(this.reader);\n      file.handleUTF8();\n      file.processAttributes();\n    }\n  },\n  /**\n   * Read the central directory.\n   */\n  readCentralDir: function readCentralDir() {\n    var file;\n    this.reader.setIndex(this.centralDirOffset);\n    while (this.reader.readString(4) === sig.CENTRAL_FILE_HEADER) {\n      file = new ZipEntry({\n        zip64: this.zip64\n      }, this.loadOptions);\n      file.readCentralPart(this.reader);\n      this.files.push(file);\n    }\n    if (this.centralDirRecords !== this.files.length) {\n      if (this.centralDirRecords !== 0 && this.files.length === 0) {\n        // We expected some records but couldn't find ANY.\n        // This is really suspicious, as if something went wrong.\n        throw new Error(\"Corrupted zip or bug: expected \" + this.centralDirRecords + \" records in central dir, got \" + this.files.length);\n      } else {\n        // We found some records but not all.\n        // Something is wrong but we got something for the user: no error here.\n        // console.warn(\"expected\", this.centralDirRecords, \"records in central dir, got\", this.files.length);\n      }\n    }\n  },\n  /**\n   * Read the end of central directory.\n   */\n  readEndOfCentral: function readEndOfCentral() {\n    var offset = this.reader.lastIndexOfSignature(sig.CENTRAL_DIRECTORY_END);\n    if (offset < 0) {\n      // Check if the content is a truncated zip or complete garbage.\n      // A \"LOCAL_FILE_HEADER\" is not required at the beginning (auto\n      // extractible zip for example) but it can give a good hint.\n      // If an ajax request was used without responseType, we will also\n      // get unreadable data.\n      var isGarbage = !this.isSignature(0, sig.LOCAL_FILE_HEADER);\n      if (isGarbage) {\n        throw new Error(\"Can't find end of central directory : is this a zip file ?\");\n      } else {\n        throw new Error(\"Corrupted zip : can't find end of central directory\");\n      }\n    }\n    this.reader.setIndex(offset);\n    var endOfCentralDirOffset = offset;\n    this.checkSignature(sig.CENTRAL_DIRECTORY_END);\n    this.readBlockEndOfCentral();\n\n    /* extract from the zip spec :\n              4)  If one of the fields in the end of central directory\n                  record is too small to hold required data, the field\n                  should be set to -1 (0xFFFF or 0xFFFFFFFF) and the\n                  ZIP64 format record should be created.\n              5)  The end of central directory record and the\n                  Zip64 end of central directory locator record must\n                  reside on the same disk when splitting or spanning\n                  an archive.\n           */\n    if (this.diskNumber === utils.MAX_VALUE_16BITS || this.diskWithCentralDirStart === utils.MAX_VALUE_16BITS || this.centralDirRecordsOnThisDisk === utils.MAX_VALUE_16BITS || this.centralDirRecords === utils.MAX_VALUE_16BITS || this.centralDirSize === utils.MAX_VALUE_32BITS || this.centralDirOffset === utils.MAX_VALUE_32BITS) {\n      this.zip64 = true;\n\n      /*\n               Warning : the zip64 extension is supported, but ONLY if the 64bits integer read from\n               the zip file can fit into a 32bits integer. This cannot be solved : Javascript represents\n               all numbers as 64-bit double precision IEEE 754 floating point numbers.\n               So, we have 53bits for integers and bitwise operations treat everything as 32bits.\n               see https://developer.mozilla.org/en-US/docs/JavaScript/Reference/Operators/Bitwise_Operators\n               and http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-262.pdf section 8.5\n               */\n\n      // should look for a zip64 EOCD locator\n      offset = this.reader.lastIndexOfSignature(sig.ZIP64_CENTRAL_DIRECTORY_LOCATOR);\n      if (offset < 0) {\n        throw new Error(\"Corrupted zip : can't find the ZIP64 end of central directory locator\");\n      }\n      this.reader.setIndex(offset);\n      this.checkSignature(sig.ZIP64_CENTRAL_DIRECTORY_LOCATOR);\n      this.readBlockZip64EndOfCentralLocator();\n\n      // now the zip64 EOCD record\n      if (!this.isSignature(this.relativeOffsetEndOfZip64CentralDir, sig.ZIP64_CENTRAL_DIRECTORY_END)) {\n        // console.warn(\"ZIP64 end of central directory not where expected.\");\n        this.relativeOffsetEndOfZip64CentralDir = this.reader.lastIndexOfSignature(sig.ZIP64_CENTRAL_DIRECTORY_END);\n        if (this.relativeOffsetEndOfZip64CentralDir < 0) {\n          throw new Error(\"Corrupted zip : can't find the ZIP64 end of central directory\");\n        }\n      }\n      this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir);\n      this.checkSignature(sig.ZIP64_CENTRAL_DIRECTORY_END);\n      this.readBlockZip64EndOfCentral();\n    }\n    var expectedEndOfCentralDirOffset = this.centralDirOffset + this.centralDirSize;\n    if (this.zip64) {\n      expectedEndOfCentralDirOffset += 20; // end of central dir 64 locator\n      expectedEndOfCentralDirOffset += 12 /* should not include the leading 12 bytes */ + this.zip64EndOfCentralSize;\n    }\n    var extraBytes = endOfCentralDirOffset - expectedEndOfCentralDirOffset;\n    if (extraBytes > 0) {\n      // console.warn(extraBytes, \"extra bytes at beginning or within zipfile\");\n      if (this.isSignature(endOfCentralDirOffset, sig.CENTRAL_FILE_HEADER)) {\n        // The offsets seem wrong, but we have something at the specified offset.\n        // So… we keep it.\n      } else {\n        // the offset is wrong, update the \"zero\" of the reader\n        // this happens if data has been prepended (crx files for example)\n        this.reader.zero = extraBytes;\n      }\n    } else if (extraBytes < 0) {\n      throw new Error(\"Corrupted zip: missing \" + Math.abs(extraBytes) + \" bytes.\");\n    }\n  },\n  prepareReader: function prepareReader(data) {\n    var type = utils.getTypeOf(data);\n    utils.checkSupport(type);\n    if (type === \"string\" && !support.uint8array) {\n      this.reader = new StringReader(data, this.loadOptions.optimizedBinaryString);\n    } else if (type === \"nodebuffer\") {\n      this.reader = new NodeBufferReader(data);\n    } else if (support.uint8array) {\n      this.reader = new Uint8ArrayReader(utils.transformTo(\"uint8array\", data));\n    } else if (support.array) {\n      this.reader = new ArrayReader(utils.transformTo(\"array\", data));\n    } else {\n      throw new Error(\"Unexpected error: unsupported type '\" + type + \"'\");\n    }\n  },\n  /**\n   * Read a zip file and create ZipEntries.\n   * @param {String|ArrayBuffer|Uint8Array|Buffer} data the binary string representing a zip file.\n   */\n  load: function load(data) {\n    this.prepareReader(data);\n    this.readEndOfCentral();\n    this.readCentralDir();\n    this.readLocalFiles();\n  }\n};\n// }}} end of ZipEntries\nmodule.exports = ZipEntries;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,mBAAmB,CAAC;AAC/C,IAAIC,gBAAgB,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AACvD,IAAIE,gBAAgB,GAAGF,OAAO,CAAC,uBAAuB,CAAC;AACvD,IAAIG,WAAW,GAAGH,OAAO,CAAC,kBAAkB,CAAC;AAC7C,IAAII,KAAK,GAAGJ,OAAO,CAAC,YAAY,CAAC;AACjC,IAAIK,GAAG,GAAGL,OAAO,CAAC,gBAAgB,CAAC;AACnC,IAAIM,QAAQ,GAAGN,OAAO,CAAC,eAAe,CAAC;AACvC,IAAIO,OAAO,GAAGP,OAAO,CAAC,cAAc,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,UAAUA,CAACC,IAAI,EAAEC,WAAW,EAAE;EACrC,IAAI,CAACC,KAAK,GAAG,EAAE;EACf,IAAI,CAACD,WAAW,GAAGA,WAAW;EAC9B,IAAID,IAAI,EAAE;IACR,IAAI,CAACG,IAAI,CAACH,IAAI,CAAC;EACjB;AACF;AACAD,UAAU,CAACK,SAAS,GAAG;EACrB;AACF;AACA;AACA;AACA;EACEC,cAAc,EAAE,SAASA,cAAcA,CAACC,iBAAiB,EAAE;IACzD,IAAIC,SAAS,GAAG,IAAI,CAACC,MAAM,CAACC,UAAU,CAAC,CAAC,CAAC;IACzC,IAAIF,SAAS,KAAKD,iBAAiB,EAAE;MACnC,MAAM,IAAII,KAAK,CAAC,8CAA8C,GAAG,GAAG,GAAGf,KAAK,CAACgB,MAAM,CAACJ,SAAS,CAAC,GAAG,aAAa,GAAGZ,KAAK,CAACgB,MAAM,CAACL,iBAAiB,CAAC,GAAG,GAAG,CAAC;IACzJ;EACF,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACEM,WAAW,EAAE,SAASA,WAAWA,CAACC,UAAU,EAAEP,iBAAiB,EAAE;IAC/D,IAAIQ,YAAY,GAAG,IAAI,CAACN,MAAM,CAACO,KAAK;IACpC,IAAI,CAACP,MAAM,CAACQ,QAAQ,CAACH,UAAU,CAAC;IAChC,IAAIN,SAAS,GAAG,IAAI,CAACC,MAAM,CAACC,UAAU,CAAC,CAAC,CAAC;IACzC,IAAIQ,MAAM,GAAGV,SAAS,KAAKD,iBAAiB;IAC5C,IAAI,CAACE,MAAM,CAACQ,QAAQ,CAACF,YAAY,CAAC;IAClC,OAAOG,MAAM;EACf,CAAC;EACD;AACF;AACA;EACEC,qBAAqB,EAAE,SAASA,qBAAqBA,CAAA,EAAG;IACtD,IAAI,CAACC,UAAU,GAAG,IAAI,CAACX,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;IACxC,IAAI,CAACC,uBAAuB,GAAG,IAAI,CAACb,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;IACrD,IAAI,CAACE,2BAA2B,GAAG,IAAI,CAACd,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;IACzD,IAAI,CAACG,iBAAiB,GAAG,IAAI,CAACf,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;IAC/C,IAAI,CAACI,cAAc,GAAG,IAAI,CAAChB,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;IAC5C,IAAI,CAACK,gBAAgB,GAAG,IAAI,CAACjB,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACM,gBAAgB,GAAG,IAAI,CAAClB,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;IAC9C;IACA;IACA;IACA,IAAIO,UAAU,GAAG,IAAI,CAACnB,MAAM,CAACoB,QAAQ,CAAC,IAAI,CAACF,gBAAgB,CAAC;IAC5D,IAAIG,eAAe,GAAG/B,OAAO,CAACgC,UAAU,GAAG,YAAY,GAAG,OAAO;IACjE;IACA;IACA,IAAIC,aAAa,GAAGpC,KAAK,CAACqC,WAAW,CAACH,eAAe,EAAEF,UAAU,CAAC;IAClE,IAAI,CAACA,UAAU,GAAG,IAAI,CAAC1B,WAAW,CAACgC,cAAc,CAACF,aAAa,CAAC;EAClE,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACEG,0BAA0B,EAAE,SAASA,0BAA0BA,CAAA,EAAG;IAChE,IAAI,CAACC,qBAAqB,GAAG,IAAI,CAAC3B,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;IACnD,IAAI,CAACgB,aAAa,GAAG,IAAI,CAAC5B,MAAM,CAACC,UAAU,CAAC,CAAC,CAAC;IAC9C,IAAI,CAAC4B,aAAa,GAAG,IAAI,CAAC7B,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;IAC3C,IAAI,CAACD,UAAU,GAAG,IAAI,CAACX,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;IACxC,IAAI,CAACC,uBAAuB,GAAG,IAAI,CAACb,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;IACrD,IAAI,CAACE,2BAA2B,GAAG,IAAI,CAACd,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;IACzD,IAAI,CAACG,iBAAiB,GAAG,IAAI,CAACf,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;IAC/C,IAAI,CAACI,cAAc,GAAG,IAAI,CAAChB,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;IAC5C,IAAI,CAACK,gBAAgB,GAAG,IAAI,CAACjB,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACkB,mBAAmB,GAAG,CAAC,CAAC;IAC7B,IAAIC,aAAa,GAAG,IAAI,CAACJ,qBAAqB,GAAG,EAAE;IACnD,IAAIpB,KAAK,GAAG,CAAC;IACb,IAAIyB,YAAY,EAAEC,gBAAgB,EAAEC,eAAe;IACnD,OAAO3B,KAAK,GAAGwB,aAAa,EAAE;MAC5BC,YAAY,GAAG,IAAI,CAAChC,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;MACrCqB,gBAAgB,GAAG,IAAI,CAACjC,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;MACzCsB,eAAe,GAAG,IAAI,CAAClC,MAAM,CAACC,UAAU,CAACgC,gBAAgB,CAAC;MAC1D,IAAI,CAACH,mBAAmB,CAACE,YAAY,CAAC,GAAG;QACvCG,EAAE,EAAEH,YAAY;QAChBI,MAAM,EAAEH,gBAAgB;QACxBI,KAAK,EAAEH;MACT,CAAC;IACH;EACF,CAAC;EACD;AACF;AACA;EACEI,iCAAiC,EAAE,SAASA,iCAAiCA,CAAA,EAAG;IAC9E,IAAI,CAACC,4BAA4B,GAAG,IAAI,CAACvC,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;IAC1D,IAAI,CAAC4B,kCAAkC,GAAG,IAAI,CAACxC,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;IAChE,IAAI,CAAC6B,UAAU,GAAG,IAAI,CAACzC,MAAM,CAACY,OAAO,CAAC,CAAC,CAAC;IACxC,IAAI,IAAI,CAAC6B,UAAU,GAAG,CAAC,EAAE;MACvB,MAAM,IAAIvC,KAAK,CAAC,qCAAqC,CAAC;IACxD;EACF,CAAC;EACD;AACF;AACA;EACEwC,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG;IACxC,IAAIC,CAAC,EAAEC,IAAI;IACX,KAAKD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACjD,KAAK,CAAC0C,MAAM,EAAEO,CAAC,EAAE,EAAE;MACtCC,IAAI,GAAG,IAAI,CAAClD,KAAK,CAACiD,CAAC,CAAC;MACpB,IAAI,CAAC3C,MAAM,CAACQ,QAAQ,CAACoC,IAAI,CAACC,iBAAiB,CAAC;MAC5C,IAAI,CAAChD,cAAc,CAACT,GAAG,CAAC0D,iBAAiB,CAAC;MAC1CF,IAAI,CAACG,aAAa,CAAC,IAAI,CAAC/C,MAAM,CAAC;MAC/B4C,IAAI,CAACI,UAAU,CAAC,CAAC;MACjBJ,IAAI,CAACK,iBAAiB,CAAC,CAAC;IAC1B;EACF,CAAC;EACD;AACF;AACA;EACEC,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG;IACxC,IAAIN,IAAI;IACR,IAAI,CAAC5C,MAAM,CAACQ,QAAQ,CAAC,IAAI,CAACS,gBAAgB,CAAC;IAC3C,OAAO,IAAI,CAACjB,MAAM,CAACC,UAAU,CAAC,CAAC,CAAC,KAAKb,GAAG,CAAC+D,mBAAmB,EAAE;MAC5DP,IAAI,GAAG,IAAIvD,QAAQ,CAAC;QAClB+D,KAAK,EAAE,IAAI,CAACA;MACd,CAAC,EAAE,IAAI,CAAC3D,WAAW,CAAC;MACpBmD,IAAI,CAACS,eAAe,CAAC,IAAI,CAACrD,MAAM,CAAC;MACjC,IAAI,CAACN,KAAK,CAAC4D,IAAI,CAACV,IAAI,CAAC;IACvB;IACA,IAAI,IAAI,CAAC7B,iBAAiB,KAAK,IAAI,CAACrB,KAAK,CAAC0C,MAAM,EAAE;MAChD,IAAI,IAAI,CAACrB,iBAAiB,KAAK,CAAC,IAAI,IAAI,CAACrB,KAAK,CAAC0C,MAAM,KAAK,CAAC,EAAE;QAC3D;QACA;QACA,MAAM,IAAIlC,KAAK,CAAC,iCAAiC,GAAG,IAAI,CAACa,iBAAiB,GAAG,+BAA+B,GAAG,IAAI,CAACrB,KAAK,CAAC0C,MAAM,CAAC;MACnI,CAAC,MAAM;QACL;QACA;QACA;MAAA;IAEJ;EACF,CAAC;EACD;AACF;AACA;EACEmB,gBAAgB,EAAE,SAASA,gBAAgBA,CAAA,EAAG;IAC5C,IAAIC,MAAM,GAAG,IAAI,CAACxD,MAAM,CAACyD,oBAAoB,CAACrE,GAAG,CAACsE,qBAAqB,CAAC;IACxE,IAAIF,MAAM,GAAG,CAAC,EAAE;MACd;MACA;MACA;MACA;MACA;MACA,IAAIG,SAAS,GAAG,CAAC,IAAI,CAACvD,WAAW,CAAC,CAAC,EAAEhB,GAAG,CAAC0D,iBAAiB,CAAC;MAC3D,IAAIa,SAAS,EAAE;QACb,MAAM,IAAIzD,KAAK,CAAC,4DAA4D,CAAC;MAC/E,CAAC,MAAM;QACL,MAAM,IAAIA,KAAK,CAAC,qDAAqD,CAAC;MACxE;IACF;IACA,IAAI,CAACF,MAAM,CAACQ,QAAQ,CAACgD,MAAM,CAAC;IAC5B,IAAII,qBAAqB,GAAGJ,MAAM;IAClC,IAAI,CAAC3D,cAAc,CAACT,GAAG,CAACsE,qBAAqB,CAAC;IAC9C,IAAI,CAAChD,qBAAqB,CAAC,CAAC;;IAE5B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAI,IAAI,CAACC,UAAU,KAAKxB,KAAK,CAAC0E,gBAAgB,IAAI,IAAI,CAAChD,uBAAuB,KAAK1B,KAAK,CAAC0E,gBAAgB,IAAI,IAAI,CAAC/C,2BAA2B,KAAK3B,KAAK,CAAC0E,gBAAgB,IAAI,IAAI,CAAC9C,iBAAiB,KAAK5B,KAAK,CAAC0E,gBAAgB,IAAI,IAAI,CAAC7C,cAAc,KAAK7B,KAAK,CAAC2E,gBAAgB,IAAI,IAAI,CAAC7C,gBAAgB,KAAK9B,KAAK,CAAC2E,gBAAgB,EAAE;MACnU,IAAI,CAACV,KAAK,GAAG,IAAI;;MAEjB;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;MAEM;MACAI,MAAM,GAAG,IAAI,CAACxD,MAAM,CAACyD,oBAAoB,CAACrE,GAAG,CAAC2E,+BAA+B,CAAC;MAC9E,IAAIP,MAAM,GAAG,CAAC,EAAE;QACd,MAAM,IAAItD,KAAK,CAAC,uEAAuE,CAAC;MAC1F;MACA,IAAI,CAACF,MAAM,CAACQ,QAAQ,CAACgD,MAAM,CAAC;MAC5B,IAAI,CAAC3D,cAAc,CAACT,GAAG,CAAC2E,+BAA+B,CAAC;MACxD,IAAI,CAACzB,iCAAiC,CAAC,CAAC;;MAExC;MACA,IAAI,CAAC,IAAI,CAAClC,WAAW,CAAC,IAAI,CAACoC,kCAAkC,EAAEpD,GAAG,CAAC4E,2BAA2B,CAAC,EAAE;QAC/F;QACA,IAAI,CAACxB,kCAAkC,GAAG,IAAI,CAACxC,MAAM,CAACyD,oBAAoB,CAACrE,GAAG,CAAC4E,2BAA2B,CAAC;QAC3G,IAAI,IAAI,CAACxB,kCAAkC,GAAG,CAAC,EAAE;UAC/C,MAAM,IAAItC,KAAK,CAAC,+DAA+D,CAAC;QAClF;MACF;MACA,IAAI,CAACF,MAAM,CAACQ,QAAQ,CAAC,IAAI,CAACgC,kCAAkC,CAAC;MAC7D,IAAI,CAAC3C,cAAc,CAACT,GAAG,CAAC4E,2BAA2B,CAAC;MACpD,IAAI,CAACtC,0BAA0B,CAAC,CAAC;IACnC;IACA,IAAIuC,6BAA6B,GAAG,IAAI,CAAChD,gBAAgB,GAAG,IAAI,CAACD,cAAc;IAC/E,IAAI,IAAI,CAACoC,KAAK,EAAE;MACda,6BAA6B,IAAI,EAAE,CAAC,CAAC;MACrCA,6BAA6B,IAAI,EAAE,CAAC,gDAAgD,IAAI,CAACtC,qBAAqB;IAChH;IACA,IAAIuC,UAAU,GAAGN,qBAAqB,GAAGK,6BAA6B;IACtE,IAAIC,UAAU,GAAG,CAAC,EAAE;MAClB;MACA,IAAI,IAAI,CAAC9D,WAAW,CAACwD,qBAAqB,EAAExE,GAAG,CAAC+D,mBAAmB,CAAC,EAAE;QACpE;QACA;MAAA,CACD,MAAM;QACL;QACA;QACA,IAAI,CAACnD,MAAM,CAACmE,IAAI,GAAGD,UAAU;MAC/B;IACF,CAAC,MAAM,IAAIA,UAAU,GAAG,CAAC,EAAE;MACzB,MAAM,IAAIhE,KAAK,CAAC,yBAAyB,GAAGkE,IAAI,CAACC,GAAG,CAACH,UAAU,CAAC,GAAG,SAAS,CAAC;IAC/E;EACF,CAAC;EACDI,aAAa,EAAE,SAASA,aAAaA,CAAC9E,IAAI,EAAE;IAC1C,IAAI+E,IAAI,GAAGpF,KAAK,CAACqF,SAAS,CAAChF,IAAI,CAAC;IAChCL,KAAK,CAACsF,YAAY,CAACF,IAAI,CAAC;IACxB,IAAIA,IAAI,KAAK,QAAQ,IAAI,CAACjF,OAAO,CAACgC,UAAU,EAAE;MAC5C,IAAI,CAACtB,MAAM,GAAG,IAAIlB,YAAY,CAACU,IAAI,EAAE,IAAI,CAACC,WAAW,CAACiF,qBAAqB,CAAC;IAC9E,CAAC,MAAM,IAAIH,IAAI,KAAK,YAAY,EAAE;MAChC,IAAI,CAACvE,MAAM,GAAG,IAAIhB,gBAAgB,CAACQ,IAAI,CAAC;IAC1C,CAAC,MAAM,IAAIF,OAAO,CAACgC,UAAU,EAAE;MAC7B,IAAI,CAACtB,MAAM,GAAG,IAAIf,gBAAgB,CAACE,KAAK,CAACqC,WAAW,CAAC,YAAY,EAAEhC,IAAI,CAAC,CAAC;IAC3E,CAAC,MAAM,IAAIF,OAAO,CAACqF,KAAK,EAAE;MACxB,IAAI,CAAC3E,MAAM,GAAG,IAAId,WAAW,CAACC,KAAK,CAACqC,WAAW,CAAC,OAAO,EAAEhC,IAAI,CAAC,CAAC;IACjE,CAAC,MAAM;MACL,MAAM,IAAIU,KAAK,CAAC,sCAAsC,GAAGqE,IAAI,GAAG,GAAG,CAAC;IACtE;EACF,CAAC;EACD;AACF;AACA;AACA;EACE5E,IAAI,EAAE,SAASA,IAAIA,CAACH,IAAI,EAAE;IACxB,IAAI,CAAC8E,aAAa,CAAC9E,IAAI,CAAC;IACxB,IAAI,CAAC+D,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACL,cAAc,CAAC,CAAC;IACrB,IAAI,CAACR,cAAc,CAAC,CAAC;EACvB;AACF,CAAC;AACD;AACAkC,MAAM,CAACC,OAAO,GAAGtF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}