{"ast": null, "code": "/**\n * Module exports.\n *\n * Logic borrowed from Modernizr:\n *\n *   - https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cors.js\n */\n\ntry {\n  module.exports = typeof XMLHttpRequest !== 'undefined' && 'withCredentials' in new XMLHttpRequest();\n} catch (err) {\n  // if XMLHttp support is disabled in IE then it will throw\n  // when trying to create\n  module.exports = false;\n}", "map": {"version": 3, "names": ["module", "exports", "XMLHttpRequest", "err"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/has-cors@1.1.0/node_modules/has-cors/index.js"], "sourcesContent": ["\n/**\n * Module exports.\n *\n * Logic borrowed from Modernizr:\n *\n *   - https://github.com/Modernizr/Modernizr/blob/master/feature-detects/cors.js\n */\n\ntry {\n  module.exports = typeof XMLHttpRequest !== 'undefined' &&\n    'withCredentials' in new XMLHttpRequest();\n} catch (err) {\n  // if XMLHttp support is disabled in IE then it will throw\n  // when trying to create\n  module.exports = false;\n}\n"], "mappings": "AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAI;EACFA,MAAM,CAACC,OAAO,GAAG,OAAOC,cAAc,KAAK,WAAW,IACpD,iBAAiB,IAAI,IAAIA,cAAc,CAAC,CAAC;AAC7C,CAAC,CAAC,OAAOC,GAAG,EAAE;EACZ;EACA;EACAH,MAAM,CAACC,OAAO,GAAG,KAAK;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}