{"ast": null, "code": "\"use strict\";\n\nvar utils = require(\"./utils.js\");\n\n/**\n * An object to write any content to a string.\n * @constructor\n */\nfunction StringWriter() {\n  this.data = [];\n}\nStringWriter.prototype = {\n  /**\n   * Append any content to the current string.\n   * @param {Object} input the content to add.\n   */\n  append: function append(input) {\n    input = utils.transformTo(\"string\", input);\n    this.data.push(input);\n  },\n  /**\n   * Finalize the construction an return the result.\n   * @return {string} the generated string.\n   */\n  finalize: function finalize() {\n    return this.data.join(\"\");\n  }\n};\nmodule.exports = StringWriter;", "map": {"version": 3, "names": ["utils", "require", "StringWriter", "data", "prototype", "append", "input", "transformTo", "push", "finalize", "join", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/pizzip@3.1.7/node_modules/pizzip/js/stringWriter.js"], "sourcesContent": ["\"use strict\";\n\nvar utils = require(\"./utils.js\");\n\n/**\n * An object to write any content to a string.\n * @constructor\n */\nfunction StringWriter() {\n  this.data = [];\n}\nStringWriter.prototype = {\n  /**\n   * Append any content to the current string.\n   * @param {Object} input the content to add.\n   */\n  append: function append(input) {\n    input = utils.transformTo(\"string\", input);\n    this.data.push(input);\n  },\n  /**\n   * Finalize the construction an return the result.\n   * @return {string} the generated string.\n   */\n  finalize: function finalize() {\n    return this.data.join(\"\");\n  }\n};\nmodule.exports = StringWriter;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;;AAEjC;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAAA,EAAG;EACtB,IAAI,CAACC,IAAI,GAAG,EAAE;AAChB;AACAD,YAAY,CAACE,SAAS,GAAG;EACvB;AACF;AACA;AACA;EACEC,MAAM,EAAE,SAASA,MAAMA,CAACC,KAAK,EAAE;IAC7BA,KAAK,GAAGN,KAAK,CAACO,WAAW,CAAC,QAAQ,EAAED,KAAK,CAAC;IAC1C,IAAI,CAACH,IAAI,CAACK,IAAI,CAACF,KAAK,CAAC;EACvB,CAAC;EACD;AACF;AACA;AACA;EACEG,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;IAC5B,OAAO,IAAI,CAACN,IAAI,CAACO,IAAI,CAAC,EAAE,CAAC;EAC3B;AACF,CAAC;AACDC,MAAM,CAACC,OAAO,GAAGV,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}