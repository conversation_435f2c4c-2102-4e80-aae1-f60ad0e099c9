{"ast": null, "code": "// lheading (---, ===)\n\n'use strict';\n\nmodule.exports = function lheading(state, startLine, endLine /*, silent*/) {\n  var content,\n    terminate,\n    i,\n    l,\n    token,\n    pos,\n    max,\n    level,\n    marker,\n    nextLine = startLine + 1,\n    oldParentType,\n    terminatorRules = state.md.block.ruler.getRules('paragraph');\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) {\n    return false;\n  }\n  oldParentType = state.parentType;\n  state.parentType = 'paragraph'; // use paragraph to match terminatorRules\n\n  // jump line-by-line until empty one or EOF\n  for (; nextLine < endLine && !state.isEmpty(nextLine); nextLine++) {\n    // this would be a code block normally, but after paragraph\n    // it's considered a lazy continuation regardless of what's there\n    if (state.sCount[nextLine] - state.blkIndent > 3) {\n      continue;\n    }\n\n    //\n    // Check for underline in setext header\n    //\n    if (state.sCount[nextLine] >= state.blkIndent) {\n      pos = state.bMarks[nextLine] + state.tShift[nextLine];\n      max = state.eMarks[nextLine];\n      if (pos < max) {\n        marker = state.src.charCodeAt(pos);\n        if (marker === 0x2D /* - */ || marker === 0x3D /* = */) {\n          pos = state.skipChars(pos, marker);\n          pos = state.skipSpaces(pos);\n          if (pos >= max) {\n            level = marker === 0x3D /* = */ ? 1 : 2;\n            break;\n          }\n        }\n      }\n    }\n\n    // quirk for blockquotes, this line should already be checked by that rule\n    if (state.sCount[nextLine] < 0) {\n      continue;\n    }\n\n    // Some tags can terminate paragraph without empty line.\n    terminate = false;\n    for (i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true;\n        break;\n      }\n    }\n    if (terminate) {\n      break;\n    }\n  }\n  if (!level) {\n    // Didn't find valid underline\n    return false;\n  }\n  content = state.getLines(startLine, nextLine, state.blkIndent, false).trim();\n  state.line = nextLine + 1;\n  token = state.push('heading_open', 'h' + String(level), 1);\n  token.markup = String.fromCharCode(marker);\n  token.map = [startLine, state.line];\n  token = state.push('inline', '', 0);\n  token.content = content;\n  token.map = [startLine, state.line - 1];\n  token.children = [];\n  token = state.push('heading_close', 'h' + String(level), -1);\n  token.markup = String.fromCharCode(marker);\n  state.parentType = oldParentType;\n  return true;\n};", "map": {"version": 3, "names": ["module", "exports", "lheading", "state", "startLine", "endLine", "content", "terminate", "i", "l", "token", "pos", "max", "level", "marker", "nextLine", "oldParentType", "terminatorRules", "md", "block", "ruler", "getRules", "sCount", "blkIndent", "parentType", "isEmpty", "bMarks", "tShift", "eMarks", "src", "charCodeAt", "<PERSON><PERSON><PERSON><PERSON>", "skipSpaces", "length", "getLines", "trim", "line", "push", "String", "markup", "fromCharCode", "map", "children"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_block/lheading.js"], "sourcesContent": ["// lheading (---, ===)\n\n'use strict';\n\n\nmodule.exports = function lheading(state, startLine, endLine/*, silent*/) {\n  var content, terminate, i, l, token, pos, max, level, marker,\n      nextLine = startLine + 1, oldParentType,\n      terminatorRules = state.md.block.ruler.getRules('paragraph');\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false; }\n\n  oldParentType = state.parentType;\n  state.parentType = 'paragraph'; // use paragraph to match terminatorRules\n\n  // jump line-by-line until empty one or EOF\n  for (; nextLine < endLine && !state.isEmpty(nextLine); nextLine++) {\n    // this would be a code block normally, but after paragraph\n    // it's considered a lazy continuation regardless of what's there\n    if (state.sCount[nextLine] - state.blkIndent > 3) { continue; }\n\n    //\n    // Check for underline in setext header\n    //\n    if (state.sCount[nextLine] >= state.blkIndent) {\n      pos = state.bMarks[nextLine] + state.tShift[nextLine];\n      max = state.eMarks[nextLine];\n\n      if (pos < max) {\n        marker = state.src.charCodeAt(pos);\n\n        if (marker === 0x2D/* - */ || marker === 0x3D/* = */) {\n          pos = state.skipChars(pos, marker);\n          pos = state.skipSpaces(pos);\n\n          if (pos >= max) {\n            level = (marker === 0x3D/* = */ ? 1 : 2);\n            break;\n          }\n        }\n      }\n    }\n\n    // quirk for blockquotes, this line should already be checked by that rule\n    if (state.sCount[nextLine] < 0) { continue; }\n\n    // Some tags can terminate paragraph without empty line.\n    terminate = false;\n    for (i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true;\n        break;\n      }\n    }\n    if (terminate) { break; }\n  }\n\n  if (!level) {\n    // Didn't find valid underline\n    return false;\n  }\n\n  content = state.getLines(startLine, nextLine, state.blkIndent, false).trim();\n\n  state.line = nextLine + 1;\n\n  token          = state.push('heading_open', 'h' + String(level), 1);\n  token.markup   = String.fromCharCode(marker);\n  token.map      = [ startLine, state.line ];\n\n  token          = state.push('inline', '', 0);\n  token.content  = content;\n  token.map      = [ startLine, state.line - 1 ];\n  token.children = [];\n\n  token          = state.push('heading_close', 'h' + String(level), -1);\n  token.markup   = String.fromCharCode(marker);\n\n  state.parentType = oldParentType;\n\n  return true;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAGZA,MAAM,CAACC,OAAO,GAAG,SAASC,QAAQA,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,eAAc;EACxE,IAAIC,OAAO;IAAEC,SAAS;IAAEC,CAAC;IAAEC,CAAC;IAAEC,KAAK;IAAEC,GAAG;IAAEC,GAAG;IAAEC,KAAK;IAAEC,MAAM;IACxDC,QAAQ,GAAGX,SAAS,GAAG,CAAC;IAAEY,aAAa;IACvCC,eAAe,GAAGd,KAAK,CAACe,EAAE,CAACC,KAAK,CAACC,KAAK,CAACC,QAAQ,CAAC,WAAW,CAAC;;EAEhE;EACA,IAAIlB,KAAK,CAACmB,MAAM,CAAClB,SAAS,CAAC,GAAGD,KAAK,CAACoB,SAAS,IAAI,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAEpEP,aAAa,GAAGb,KAAK,CAACqB,UAAU;EAChCrB,KAAK,CAACqB,UAAU,GAAG,WAAW,CAAC,CAAC;;EAEhC;EACA,OAAOT,QAAQ,GAAGV,OAAO,IAAI,CAACF,KAAK,CAACsB,OAAO,CAACV,QAAQ,CAAC,EAAEA,QAAQ,EAAE,EAAE;IACjE;IACA;IACA,IAAIZ,KAAK,CAACmB,MAAM,CAACP,QAAQ,CAAC,GAAGZ,KAAK,CAACoB,SAAS,GAAG,CAAC,EAAE;MAAE;IAAU;;IAE9D;IACA;IACA;IACA,IAAIpB,KAAK,CAACmB,MAAM,CAACP,QAAQ,CAAC,IAAIZ,KAAK,CAACoB,SAAS,EAAE;MAC7CZ,GAAG,GAAGR,KAAK,CAACuB,MAAM,CAACX,QAAQ,CAAC,GAAGZ,KAAK,CAACwB,MAAM,CAACZ,QAAQ,CAAC;MACrDH,GAAG,GAAGT,KAAK,CAACyB,MAAM,CAACb,QAAQ,CAAC;MAE5B,IAAIJ,GAAG,GAAGC,GAAG,EAAE;QACbE,MAAM,GAAGX,KAAK,CAAC0B,GAAG,CAACC,UAAU,CAACnB,GAAG,CAAC;QAElC,IAAIG,MAAM,KAAK,IAAI,YAAWA,MAAM,KAAK,IAAI,UAAS;UACpDH,GAAG,GAAGR,KAAK,CAAC4B,SAAS,CAACpB,GAAG,EAAEG,MAAM,CAAC;UAClCH,GAAG,GAAGR,KAAK,CAAC6B,UAAU,CAACrB,GAAG,CAAC;UAE3B,IAAIA,GAAG,IAAIC,GAAG,EAAE;YACdC,KAAK,GAAIC,MAAM,KAAK,IAAI,WAAU,CAAC,GAAG,CAAE;YACxC;UACF;QACF;MACF;IACF;;IAEA;IACA,IAAIX,KAAK,CAACmB,MAAM,CAACP,QAAQ,CAAC,GAAG,CAAC,EAAE;MAAE;IAAU;;IAE5C;IACAR,SAAS,GAAG,KAAK;IACjB,KAAKC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGQ,eAAe,CAACgB,MAAM,EAAEzB,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAClD,IAAIS,eAAe,CAACT,CAAC,CAAC,CAACL,KAAK,EAAEY,QAAQ,EAAEV,OAAO,EAAE,IAAI,CAAC,EAAE;QACtDE,SAAS,GAAG,IAAI;QAChB;MACF;IACF;IACA,IAAIA,SAAS,EAAE;MAAE;IAAO;EAC1B;EAEA,IAAI,CAACM,KAAK,EAAE;IACV;IACA,OAAO,KAAK;EACd;EAEAP,OAAO,GAAGH,KAAK,CAAC+B,QAAQ,CAAC9B,SAAS,EAAEW,QAAQ,EAAEZ,KAAK,CAACoB,SAAS,EAAE,KAAK,CAAC,CAACY,IAAI,CAAC,CAAC;EAE5EhC,KAAK,CAACiC,IAAI,GAAGrB,QAAQ,GAAG,CAAC;EAEzBL,KAAK,GAAYP,KAAK,CAACkC,IAAI,CAAC,cAAc,EAAE,GAAG,GAAGC,MAAM,CAACzB,KAAK,CAAC,EAAE,CAAC,CAAC;EACnEH,KAAK,CAAC6B,MAAM,GAAKD,MAAM,CAACE,YAAY,CAAC1B,MAAM,CAAC;EAC5CJ,KAAK,CAAC+B,GAAG,GAAQ,CAAErC,SAAS,EAAED,KAAK,CAACiC,IAAI,CAAE;EAE1C1B,KAAK,GAAYP,KAAK,CAACkC,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;EAC5C3B,KAAK,CAACJ,OAAO,GAAIA,OAAO;EACxBI,KAAK,CAAC+B,GAAG,GAAQ,CAAErC,SAAS,EAAED,KAAK,CAACiC,IAAI,GAAG,CAAC,CAAE;EAC9C1B,KAAK,CAACgC,QAAQ,GAAG,EAAE;EAEnBhC,KAAK,GAAYP,KAAK,CAACkC,IAAI,CAAC,eAAe,EAAE,GAAG,GAAGC,MAAM,CAACzB,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;EACrEH,KAAK,CAAC6B,MAAM,GAAKD,MAAM,CAACE,YAAY,CAAC1B,MAAM,CAAC;EAE5CX,KAAK,CAACqB,UAAU,GAAGR,aAAa;EAEhC,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}