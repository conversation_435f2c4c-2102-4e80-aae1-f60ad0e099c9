{"ast": null, "code": "function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nimport '../../../utils/index.mjs';\nimport { isNumber } from '../../../utils/types.mjs';\nvar timeUnits = [[\"Y\", 1e3 * 60 * 60 * 24 * 365], [\"M\", 1e3 * 60 * 60 * 24 * 30], [\"D\", 1e3 * 60 * 60 * 24], [\"H\", 1e3 * 60 * 60], [\"m\", 1e3 * 60], [\"s\", 1e3], [\"S\", 1]];\nvar getTime = function getTime(value) {\n  return isNumber(value) ? new Date(value).getTime() : value.valueOf();\n};\nvar formatTime = function formatTime(timestamp, format) {\n  var timeLeft = timestamp;\n  var escapeRegex = /\\[([^\\]]*)]/g;\n  var replacedText = timeUnits.reduce(function (current, _ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      name = _ref2[0],\n      unit = _ref2[1];\n    var replaceRegex = new RegExp(`${name}+(?![^\\\\[\\\\]]*\\\\])`, \"g\");\n    if (replaceRegex.test(current)) {\n      var value = Math.floor(timeLeft / unit);\n      timeLeft -= value * unit;\n      return current.replace(replaceRegex, function (match) {\n        return String(value).padStart(match.length, \"0\");\n      });\n    }\n    return current;\n  }, format);\n  return replacedText.replace(escapeRegex, \"$1\");\n};\nexport { formatTime, getTime };", "map": {"version": 3, "names": ["timeUnits", "getTime", "value", "isNumber", "Date", "valueOf", "formatTime", "timestamp", "format", "timeLeft", "escapeRegex", "replacedText", "reduce", "current", "_ref", "_ref2", "_slicedToArray", "name", "unit", "replaceRegex", "RegExp", "test", "Math", "floor", "replace", "match", "String", "padStart", "length"], "sources": ["../../../../../../packages/components/countdown/src/utils.ts"], "sourcesContent": ["import { isNumber } from '@element-plus/utils'\n\nimport type { Dayjs } from 'dayjs'\n\nconst timeUnits = [\n  ['Y', 1000 * 60 * 60 * 24 * 365], // years\n  ['M', 1000 * 60 * 60 * 24 * 30], // months\n  ['D', 1000 * 60 * 60 * 24], // days\n  ['H', 1000 * 60 * 60], // hours\n  ['m', 1000 * 60], // minutes\n  ['s', 1000], // seconds\n  ['S', 1], // million seconds\n] as const\n\nexport const getTime = (value: number | Dayjs) => {\n  return isNumber(value) ? new Date(value).getTime() : value.valueOf()\n}\n\nexport const formatTime = (timestamp: number, format: string) => {\n  let timeLeft = timestamp\n  const escapeRegex = /\\[([^\\]]*)]/g\n\n  const replacedText = timeUnits.reduce((current, [name, unit]) => {\n    const replaceRegex = new RegExp(`${name}+(?![^\\\\[\\\\]]*\\\\])`, 'g')\n    if (replaceRegex.test(current)) {\n      const value = Math.floor(timeLeft / unit)\n      timeLeft -= value * unit\n      return current.replace(replaceRegex, (match) =>\n        String(value).padStart(match.length, '0')\n      )\n    }\n    return current\n  }, format)\n\n  return replacedText.replace(escapeRegex, '$1')\n}\n"], "mappings": ";;;;;;;;AACA,IAAMA,SAAS,GAAG,CAChB,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,GAAG,CAAC,EAC/B,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAC9B,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EACzB,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,GAAG,EAAE,CAAC,EACpB,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC,EACf,CAAC,GAAG,EAAE,GAAG,CAAC,EACV,CAAC,GAAG,EAAE,CAAC,CAAC,CACT;AACW,IAACC,OAAO,GAAG,SAAVA,OAAOA,CAAIC,KAAK,EAAK;EAChC,OAAOC,QAAQ,CAACD,KAAK,CAAC,GAAG,IAAIE,IAAI,CAACF,KAAK,CAAC,CAACD,OAAO,EAAE,GAAGC,KAAK,CAACG,OAAO,EAAE;AACtE;AACY,IAACC,UAAU,GAAG,SAAbA,UAAUA,CAAIC,SAAS,EAAEC,MAAM,EAAK;EAC/C,IAAIC,QAAQ,GAAGF,SAAS;EACxB,IAAMG,WAAW,GAAG,cAAc;EAClC,IAAMC,YAAY,GAAGX,SAAS,CAACY,MAAM,CAAC,UAACC,OAAO,EAAAC,IAAA,EAAmB;IAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAF,IAAA;MAAhBG,IAAI,GAAAF,KAAA;MAAEG,IAAI,GAAAH,KAAA;IACzD,IAAMI,YAAY,GAAG,IAAIC,MAAM,CAAC,GAAGH,IAAI,oBAAoB,EAAE,GAAG,CAAC;IACjE,IAAIE,YAAY,CAACE,IAAI,CAACR,OAAO,CAAC,EAAE;MAC9B,IAAMX,KAAK,GAAGoB,IAAI,CAACC,KAAK,CAACd,QAAQ,GAAGS,IAAI,CAAC;MACzCT,QAAQ,IAAIP,KAAK,GAAGgB,IAAI;MACxB,OAAOL,OAAO,CAACW,OAAO,CAACL,YAAY,EAAE,UAACM,KAAK;QAAA,OAAKC,MAAM,CAACxB,KAAK,CAAC,CAACyB,QAAQ,CAACF,KAAK,CAACG,MAAM,EAAE,GAAG,CAAC;MAAA,EAAC;IAChG;IACI,OAAOf,OAAO;EAClB,CAAG,EAAEL,MAAM,CAAC;EACV,OAAOG,YAAY,CAACa,OAAO,CAACd,WAAW,EAAE,IAAI,CAAC;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}