{"ast": null, "code": "var root = require('./_root');\n\n/** Used to detect overreaching core-js shims. */\nvar coreJsData = root['__core-js_shared__'];\nmodule.exports = coreJsData;", "map": {"version": 3, "names": ["root", "require", "coreJsData", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_coreJsData.js"], "sourcesContent": ["var root = require('./_root');\n\n/** Used to detect overreaching core-js shims. */\nvar coreJsData = root['__core-js_shared__'];\n\nmodule.exports = coreJsData;\n"], "mappings": "AAAA,IAAIA,IAAI,GAAGC,OAAO,CAAC,SAAS,CAAC;;AAE7B;AACA,IAAIC,UAAU,GAAGF,IAAI,CAAC,oBAAoB,CAAC;AAE3CG,MAAM,CAACC,OAAO,GAAGF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}