{"ast": null, "code": "import { ref, watch, nextTick } from 'vue';\nvar __default__ = {\n  name: 'SettingPopupWindow'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    },\n    beforeClose: Function\n  },\n  emits: ['update:modelValue'],\n  setup(__props, _ref) {\n    var _window$electron;\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var emit = __emit;\n    var isMac = (_window$electron = window.electron) === null || _window$electron === void 0 ? void 0 : _window$electron.isMac;\n    var show = ref(props.modelValue);\n    var isShow = ref(false);\n    var bodyShow = ref(false);\n    watch(function () {\n      return props.modelValue;\n    }, function () {\n      show.value = props.modelValue;\n      if (props.modelValue) {\n        isShow.value = true;\n        nextTick(function () {\n          bodyShow.value = true;\n        });\n      } else {\n        bodyShow.value = false;\n        setTimeout(function () {\n          isShow.value = false;\n        }, 99);\n      }\n    });\n    var closeClick = function closeClick() {\n      if (typeof props.beforeClose === 'function') {\n        props.beforeClose(function () {\n          emit('update:modelValue', false);\n        });\n      } else {\n        emit('update:modelValue', false);\n      }\n    };\n    var __returned__ = {\n      props,\n      emit,\n      isMac,\n      show,\n      isShow,\n      bodyShow,\n      closeClick,\n      ref,\n      watch,\n      nextTick\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "watch", "nextTick", "__default__", "name", "props", "__props", "emit", "__emit", "isMac", "_window$electron", "window", "electron", "show", "modelValue", "isShow", "bodyShow", "value", "setTimeout", "closeClick", "beforeClose"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/GlobalChat/components/setting-popup-window/setting-popup-window.vue"], "sourcesContent": ["<template>\r\n  <div class=\"setting-popup-window\" :class=\"{ 'setting-popup-window-mac': isMac }\" @click.stop=\"closeClick\"\r\n    v-if=\"isShow\">\r\n    <transition enter-active-class=\"animate__animated animate__fadeInRight animate__faster\"\r\n      leave-active-class=\"animate__animated animate__fadeOutRight animate__faster\">\r\n      <div class=\"setting-popup-window-body forbidSelect\" @click.stop v-if=\"bodyShow\">\r\n        <slot></slot>\r\n      </div>\r\n    </transition>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'SettingPopupWindow' }\r\n</script>\r\n<script setup>\r\nimport { ref, watch, nextTick } from 'vue'\r\nconst props = defineProps({\r\n  modelValue: { type: Boolean, default: false },\r\n  beforeClose: Function\r\n})\r\nconst emit = defineEmits(['update:modelValue'])\r\nconst isMac = window.electron?.isMac\r\n\r\nconst show = ref(props.modelValue)\r\nconst isShow = ref(false)\r\nconst bodyShow = ref(false)\r\n\r\nwatch(() => props.modelValue, () => {\r\n  show.value = props.modelValue\r\n  if (props.modelValue) {\r\n    isShow.value = true\r\n    nextTick(() => { bodyShow.value = true })\r\n  } else {\r\n    bodyShow.value = false\r\n    setTimeout(() => { isShow.value = false }, 99)\r\n  }\r\n})\r\nconst closeClick = () => {\r\n  if (typeof props.beforeClose === 'function') {\r\n    props.beforeClose(() => { emit('update:modelValue', false) })\r\n  } else {\r\n    emit('update:modelValue', false)\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.setting-popup-window {\r\n  width: 100%;\r\n  height: calc(100% - 66px);\r\n  position: absolute;\r\n  top: 66px;\r\n  left: 0;\r\n  z-index: 998;\r\n  overflow: hidden;\r\n\r\n  &.setting-popup-window-mac {\r\n    height: calc(100% - 56px);\r\n    top: 56px;\r\n  }\r\n\r\n  .setting-popup-window-body {\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    min-width: 320px;\r\n    height: 100%;\r\n    background: #fff;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAeA,SAASA,GAAG,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,KAAK;AAH1C,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAqB,CAAC;;;;;;;;;;;;;;;IAI7C,IAAMC,KAAK,GAAGC,OAGZ;IACF,IAAMC,IAAI,GAAGC,MAAkC;IAC/C,IAAMC,KAAK,IAAAC,gBAAA,GAAGC,MAAM,CAACC,QAAQ,cAAAF,gBAAA,uBAAfA,gBAAA,CAAiBD,KAAK;IAEpC,IAAMI,IAAI,GAAGb,GAAG,CAACK,KAAK,CAACS,UAAU,CAAC;IAClC,IAAMC,MAAM,GAAGf,GAAG,CAAC,KAAK,CAAC;IACzB,IAAMgB,QAAQ,GAAGhB,GAAG,CAAC,KAAK,CAAC;IAE3BC,KAAK,CAAC;MAAA,OAAMI,KAAK,CAACS,UAAU;IAAA,GAAE,YAAM;MAClCD,IAAI,CAACI,KAAK,GAAGZ,KAAK,CAACS,UAAU;MAC7B,IAAIT,KAAK,CAACS,UAAU,EAAE;QACpBC,MAAM,CAACE,KAAK,GAAG,IAAI;QACnBf,QAAQ,CAAC,YAAM;UAAEc,QAAQ,CAACC,KAAK,GAAG,IAAI;QAAC,CAAC,CAAC;MAC3C,CAAC,MAAM;QACLD,QAAQ,CAACC,KAAK,GAAG,KAAK;QACtBC,UAAU,CAAC,YAAM;UAAEH,MAAM,CAACE,KAAK,GAAG,KAAK;QAAC,CAAC,EAAE,EAAE,CAAC;MAChD;IACF,CAAC,CAAC;IACF,IAAME,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAI,OAAOd,KAAK,CAACe,WAAW,KAAK,UAAU,EAAE;QAC3Cf,KAAK,CAACe,WAAW,CAAC,YAAM;UAAEb,IAAI,CAAC,mBAAmB,EAAE,KAAK,CAAC;QAAC,CAAC,CAAC;MAC/D,CAAC,MAAM;QACLA,IAAI,CAAC,mBAAmB,EAAE,KAAK,CAAC;MAClC;IACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}