{"ast": null, "code": "// Process ![image](<src> \"title\")\n\n'use strict';\n\nvar normalizeReference = require('../common/utils').normalizeReference;\nvar isSpace = require('../common/utils').isSpace;\nmodule.exports = function image(state, silent) {\n  var attrs,\n    code,\n    content,\n    label,\n    labelEnd,\n    labelStart,\n    pos,\n    ref,\n    res,\n    title,\n    token,\n    tokens,\n    start,\n    href = '',\n    oldPos = state.pos,\n    max = state.posMax;\n  if (state.src.charCodeAt(state.pos) !== 0x21 /* ! */) {\n    return false;\n  }\n  if (state.src.charCodeAt(state.pos + 1) !== 0x5B /* [ */) {\n    return false;\n  }\n  labelStart = state.pos + 2;\n  labelEnd = state.md.helpers.parseLinkLabel(state, state.pos + 1, false);\n\n  // parser failed to find ']', so it's not a valid link\n  if (labelEnd < 0) {\n    return false;\n  }\n  pos = labelEnd + 1;\n  if (pos < max && state.src.charCodeAt(pos) === 0x28 /* ( */) {\n    //\n    // Inline link\n    //\n\n    // [link](  <href>  \"title\"  )\n    //        ^^ skipping these spaces\n    pos++;\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos);\n      if (!isSpace(code) && code !== 0x0A) {\n        break;\n      }\n    }\n    if (pos >= max) {\n      return false;\n    }\n\n    // [link](  <href>  \"title\"  )\n    //          ^^^^^^ parsing link destination\n    start = pos;\n    res = state.md.helpers.parseLinkDestination(state.src, pos, state.posMax);\n    if (res.ok) {\n      href = state.md.normalizeLink(res.str);\n      if (state.md.validateLink(href)) {\n        pos = res.pos;\n      } else {\n        href = '';\n      }\n    }\n\n    // [link](  <href>  \"title\"  )\n    //                ^^ skipping these spaces\n    start = pos;\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos);\n      if (!isSpace(code) && code !== 0x0A) {\n        break;\n      }\n    }\n\n    // [link](  <href>  \"title\"  )\n    //                  ^^^^^^^ parsing link title\n    res = state.md.helpers.parseLinkTitle(state.src, pos, state.posMax);\n    if (pos < max && start !== pos && res.ok) {\n      title = res.str;\n      pos = res.pos;\n\n      // [link](  <href>  \"title\"  )\n      //                         ^^ skipping these spaces\n      for (; pos < max; pos++) {\n        code = state.src.charCodeAt(pos);\n        if (!isSpace(code) && code !== 0x0A) {\n          break;\n        }\n      }\n    } else {\n      title = '';\n    }\n    if (pos >= max || state.src.charCodeAt(pos) !== 0x29 /* ) */) {\n      state.pos = oldPos;\n      return false;\n    }\n    pos++;\n  } else {\n    //\n    // Link reference\n    //\n    if (typeof state.env.references === 'undefined') {\n      return false;\n    }\n    if (pos < max && state.src.charCodeAt(pos) === 0x5B /* [ */) {\n      start = pos + 1;\n      pos = state.md.helpers.parseLinkLabel(state, pos);\n      if (pos >= 0) {\n        label = state.src.slice(start, pos++);\n      } else {\n        pos = labelEnd + 1;\n      }\n    } else {\n      pos = labelEnd + 1;\n    }\n\n    // covers label === '' and label === undefined\n    // (collapsed reference link and shortcut reference link respectively)\n    if (!label) {\n      label = state.src.slice(labelStart, labelEnd);\n    }\n    ref = state.env.references[normalizeReference(label)];\n    if (!ref) {\n      state.pos = oldPos;\n      return false;\n    }\n    href = ref.href;\n    title = ref.title;\n  }\n\n  //\n  // We found the end of the link, and know for a fact it's a valid link;\n  // so all that's left to do is to call tokenizer.\n  //\n  if (!silent) {\n    content = state.src.slice(labelStart, labelEnd);\n    state.md.inline.parse(content, state.md, state.env, tokens = []);\n    token = state.push('image', 'img', 0);\n    token.attrs = attrs = [['src', href], ['alt', '']];\n    token.children = tokens;\n    token.content = content;\n    if (title) {\n      attrs.push(['title', title]);\n    }\n  }\n  state.pos = pos;\n  state.posMax = max;\n  return true;\n};", "map": {"version": 3, "names": ["normalizeReference", "require", "isSpace", "module", "exports", "image", "state", "silent", "attrs", "code", "content", "label", "labelEnd", "labelStart", "pos", "ref", "res", "title", "token", "tokens", "start", "href", "oldPos", "max", "posMax", "src", "charCodeAt", "md", "helpers", "parseLinkLabel", "parseLinkDestination", "ok", "normalizeLink", "str", "validateLink", "parseLinkTitle", "env", "references", "slice", "inline", "parse", "push", "children"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_inline/image.js"], "sourcesContent": ["// Process ![image](<src> \"title\")\n\n'use strict';\n\nvar normalizeReference   = require('../common/utils').normalizeReference;\nvar isSpace              = require('../common/utils').isSpace;\n\n\nmodule.exports = function image(state, silent) {\n  var attrs,\n      code,\n      content,\n      label,\n      labelEnd,\n      labelStart,\n      pos,\n      ref,\n      res,\n      title,\n      token,\n      tokens,\n      start,\n      href = '',\n      oldPos = state.pos,\n      max = state.posMax;\n\n  if (state.src.charCodeAt(state.pos) !== 0x21/* ! */) { return false; }\n  if (state.src.charCodeAt(state.pos + 1) !== 0x5B/* [ */) { return false; }\n\n  labelStart = state.pos + 2;\n  labelEnd = state.md.helpers.parseLinkLabel(state, state.pos + 1, false);\n\n  // parser failed to find ']', so it's not a valid link\n  if (labelEnd < 0) { return false; }\n\n  pos = labelEnd + 1;\n  if (pos < max && state.src.charCodeAt(pos) === 0x28/* ( */) {\n    //\n    // Inline link\n    //\n\n    // [link](  <href>  \"title\"  )\n    //        ^^ skipping these spaces\n    pos++;\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos);\n      if (!isSpace(code) && code !== 0x0A) { break; }\n    }\n    if (pos >= max) { return false; }\n\n    // [link](  <href>  \"title\"  )\n    //          ^^^^^^ parsing link destination\n    start = pos;\n    res = state.md.helpers.parseLinkDestination(state.src, pos, state.posMax);\n    if (res.ok) {\n      href = state.md.normalizeLink(res.str);\n      if (state.md.validateLink(href)) {\n        pos = res.pos;\n      } else {\n        href = '';\n      }\n    }\n\n    // [link](  <href>  \"title\"  )\n    //                ^^ skipping these spaces\n    start = pos;\n    for (; pos < max; pos++) {\n      code = state.src.charCodeAt(pos);\n      if (!isSpace(code) && code !== 0x0A) { break; }\n    }\n\n    // [link](  <href>  \"title\"  )\n    //                  ^^^^^^^ parsing link title\n    res = state.md.helpers.parseLinkTitle(state.src, pos, state.posMax);\n    if (pos < max && start !== pos && res.ok) {\n      title = res.str;\n      pos = res.pos;\n\n      // [link](  <href>  \"title\"  )\n      //                         ^^ skipping these spaces\n      for (; pos < max; pos++) {\n        code = state.src.charCodeAt(pos);\n        if (!isSpace(code) && code !== 0x0A) { break; }\n      }\n    } else {\n      title = '';\n    }\n\n    if (pos >= max || state.src.charCodeAt(pos) !== 0x29/* ) */) {\n      state.pos = oldPos;\n      return false;\n    }\n    pos++;\n  } else {\n    //\n    // Link reference\n    //\n    if (typeof state.env.references === 'undefined') { return false; }\n\n    if (pos < max && state.src.charCodeAt(pos) === 0x5B/* [ */) {\n      start = pos + 1;\n      pos = state.md.helpers.parseLinkLabel(state, pos);\n      if (pos >= 0) {\n        label = state.src.slice(start, pos++);\n      } else {\n        pos = labelEnd + 1;\n      }\n    } else {\n      pos = labelEnd + 1;\n    }\n\n    // covers label === '' and label === undefined\n    // (collapsed reference link and shortcut reference link respectively)\n    if (!label) { label = state.src.slice(labelStart, labelEnd); }\n\n    ref = state.env.references[normalizeReference(label)];\n    if (!ref) {\n      state.pos = oldPos;\n      return false;\n    }\n    href = ref.href;\n    title = ref.title;\n  }\n\n  //\n  // We found the end of the link, and know for a fact it's a valid link;\n  // so all that's left to do is to call tokenizer.\n  //\n  if (!silent) {\n    content = state.src.slice(labelStart, labelEnd);\n\n    state.md.inline.parse(\n      content,\n      state.md,\n      state.env,\n      tokens = []\n    );\n\n    token          = state.push('image', 'img', 0);\n    token.attrs    = attrs = [ [ 'src', href ], [ 'alt', '' ] ];\n    token.children = tokens;\n    token.content  = content;\n\n    if (title) {\n      attrs.push([ 'title', title ]);\n    }\n  }\n\n  state.pos = pos;\n  state.posMax = max;\n  return true;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAEZ,IAAIA,kBAAkB,GAAKC,OAAO,CAAC,iBAAiB,CAAC,CAACD,kBAAkB;AACxE,IAAIE,OAAO,GAAgBD,OAAO,CAAC,iBAAiB,CAAC,CAACC,OAAO;AAG7DC,MAAM,CAACC,OAAO,GAAG,SAASC,KAAKA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC7C,IAAIC,KAAK;IACLC,IAAI;IACJC,OAAO;IACPC,KAAK;IACLC,QAAQ;IACRC,UAAU;IACVC,GAAG;IACHC,GAAG;IACHC,GAAG;IACHC,KAAK;IACLC,KAAK;IACLC,MAAM;IACNC,KAAK;IACLC,IAAI,GAAG,EAAE;IACTC,MAAM,GAAGhB,KAAK,CAACQ,GAAG;IAClBS,GAAG,GAAGjB,KAAK,CAACkB,MAAM;EAEtB,IAAIlB,KAAK,CAACmB,GAAG,CAACC,UAAU,CAACpB,KAAK,CAACQ,GAAG,CAAC,KAAK,IAAI,UAAS;IAAE,OAAO,KAAK;EAAE;EACrE,IAAIR,KAAK,CAACmB,GAAG,CAACC,UAAU,CAACpB,KAAK,CAACQ,GAAG,GAAG,CAAC,CAAC,KAAK,IAAI,UAAS;IAAE,OAAO,KAAK;EAAE;EAEzED,UAAU,GAAGP,KAAK,CAACQ,GAAG,GAAG,CAAC;EAC1BF,QAAQ,GAAGN,KAAK,CAACqB,EAAE,CAACC,OAAO,CAACC,cAAc,CAACvB,KAAK,EAAEA,KAAK,CAACQ,GAAG,GAAG,CAAC,EAAE,KAAK,CAAC;;EAEvE;EACA,IAAIF,QAAQ,GAAG,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAElCE,GAAG,GAAGF,QAAQ,GAAG,CAAC;EAClB,IAAIE,GAAG,GAAGS,GAAG,IAAIjB,KAAK,CAACmB,GAAG,CAACC,UAAU,CAACZ,GAAG,CAAC,KAAK,IAAI,UAAS;IAC1D;IACA;IACA;;IAEA;IACA;IACAA,GAAG,EAAE;IACL,OAAOA,GAAG,GAAGS,GAAG,EAAET,GAAG,EAAE,EAAE;MACvBL,IAAI,GAAGH,KAAK,CAACmB,GAAG,CAACC,UAAU,CAACZ,GAAG,CAAC;MAChC,IAAI,CAACZ,OAAO,CAACO,IAAI,CAAC,IAAIA,IAAI,KAAK,IAAI,EAAE;QAAE;MAAO;IAChD;IACA,IAAIK,GAAG,IAAIS,GAAG,EAAE;MAAE,OAAO,KAAK;IAAE;;IAEhC;IACA;IACAH,KAAK,GAAGN,GAAG;IACXE,GAAG,GAAGV,KAAK,CAACqB,EAAE,CAACC,OAAO,CAACE,oBAAoB,CAACxB,KAAK,CAACmB,GAAG,EAAEX,GAAG,EAAER,KAAK,CAACkB,MAAM,CAAC;IACzE,IAAIR,GAAG,CAACe,EAAE,EAAE;MACVV,IAAI,GAAGf,KAAK,CAACqB,EAAE,CAACK,aAAa,CAAChB,GAAG,CAACiB,GAAG,CAAC;MACtC,IAAI3B,KAAK,CAACqB,EAAE,CAACO,YAAY,CAACb,IAAI,CAAC,EAAE;QAC/BP,GAAG,GAAGE,GAAG,CAACF,GAAG;MACf,CAAC,MAAM;QACLO,IAAI,GAAG,EAAE;MACX;IACF;;IAEA;IACA;IACAD,KAAK,GAAGN,GAAG;IACX,OAAOA,GAAG,GAAGS,GAAG,EAAET,GAAG,EAAE,EAAE;MACvBL,IAAI,GAAGH,KAAK,CAACmB,GAAG,CAACC,UAAU,CAACZ,GAAG,CAAC;MAChC,IAAI,CAACZ,OAAO,CAACO,IAAI,CAAC,IAAIA,IAAI,KAAK,IAAI,EAAE;QAAE;MAAO;IAChD;;IAEA;IACA;IACAO,GAAG,GAAGV,KAAK,CAACqB,EAAE,CAACC,OAAO,CAACO,cAAc,CAAC7B,KAAK,CAACmB,GAAG,EAAEX,GAAG,EAAER,KAAK,CAACkB,MAAM,CAAC;IACnE,IAAIV,GAAG,GAAGS,GAAG,IAAIH,KAAK,KAAKN,GAAG,IAAIE,GAAG,CAACe,EAAE,EAAE;MACxCd,KAAK,GAAGD,GAAG,CAACiB,GAAG;MACfnB,GAAG,GAAGE,GAAG,CAACF,GAAG;;MAEb;MACA;MACA,OAAOA,GAAG,GAAGS,GAAG,EAAET,GAAG,EAAE,EAAE;QACvBL,IAAI,GAAGH,KAAK,CAACmB,GAAG,CAACC,UAAU,CAACZ,GAAG,CAAC;QAChC,IAAI,CAACZ,OAAO,CAACO,IAAI,CAAC,IAAIA,IAAI,KAAK,IAAI,EAAE;UAAE;QAAO;MAChD;IACF,CAAC,MAAM;MACLQ,KAAK,GAAG,EAAE;IACZ;IAEA,IAAIH,GAAG,IAAIS,GAAG,IAAIjB,KAAK,CAACmB,GAAG,CAACC,UAAU,CAACZ,GAAG,CAAC,KAAK,IAAI,UAAS;MAC3DR,KAAK,CAACQ,GAAG,GAAGQ,MAAM;MAClB,OAAO,KAAK;IACd;IACAR,GAAG,EAAE;EACP,CAAC,MAAM;IACL;IACA;IACA;IACA,IAAI,OAAOR,KAAK,CAAC8B,GAAG,CAACC,UAAU,KAAK,WAAW,EAAE;MAAE,OAAO,KAAK;IAAE;IAEjE,IAAIvB,GAAG,GAAGS,GAAG,IAAIjB,KAAK,CAACmB,GAAG,CAACC,UAAU,CAACZ,GAAG,CAAC,KAAK,IAAI,UAAS;MAC1DM,KAAK,GAAGN,GAAG,GAAG,CAAC;MACfA,GAAG,GAAGR,KAAK,CAACqB,EAAE,CAACC,OAAO,CAACC,cAAc,CAACvB,KAAK,EAAEQ,GAAG,CAAC;MACjD,IAAIA,GAAG,IAAI,CAAC,EAAE;QACZH,KAAK,GAAGL,KAAK,CAACmB,GAAG,CAACa,KAAK,CAAClB,KAAK,EAAEN,GAAG,EAAE,CAAC;MACvC,CAAC,MAAM;QACLA,GAAG,GAAGF,QAAQ,GAAG,CAAC;MACpB;IACF,CAAC,MAAM;MACLE,GAAG,GAAGF,QAAQ,GAAG,CAAC;IACpB;;IAEA;IACA;IACA,IAAI,CAACD,KAAK,EAAE;MAAEA,KAAK,GAAGL,KAAK,CAACmB,GAAG,CAACa,KAAK,CAACzB,UAAU,EAAED,QAAQ,CAAC;IAAE;IAE7DG,GAAG,GAAGT,KAAK,CAAC8B,GAAG,CAACC,UAAU,CAACrC,kBAAkB,CAACW,KAAK,CAAC,CAAC;IACrD,IAAI,CAACI,GAAG,EAAE;MACRT,KAAK,CAACQ,GAAG,GAAGQ,MAAM;MAClB,OAAO,KAAK;IACd;IACAD,IAAI,GAAGN,GAAG,CAACM,IAAI;IACfJ,KAAK,GAAGF,GAAG,CAACE,KAAK;EACnB;;EAEA;EACA;EACA;EACA;EACA,IAAI,CAACV,MAAM,EAAE;IACXG,OAAO,GAAGJ,KAAK,CAACmB,GAAG,CAACa,KAAK,CAACzB,UAAU,EAAED,QAAQ,CAAC;IAE/CN,KAAK,CAACqB,EAAE,CAACY,MAAM,CAACC,KAAK,CACnB9B,OAAO,EACPJ,KAAK,CAACqB,EAAE,EACRrB,KAAK,CAAC8B,GAAG,EACTjB,MAAM,GAAG,EACX,CAAC;IAEDD,KAAK,GAAYZ,KAAK,CAACmC,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;IAC9CvB,KAAK,CAACV,KAAK,GAAMA,KAAK,GAAG,CAAE,CAAE,KAAK,EAAEa,IAAI,CAAE,EAAE,CAAE,KAAK,EAAE,EAAE,CAAE,CAAE;IAC3DH,KAAK,CAACwB,QAAQ,GAAGvB,MAAM;IACvBD,KAAK,CAACR,OAAO,GAAIA,OAAO;IAExB,IAAIO,KAAK,EAAE;MACTT,KAAK,CAACiC,IAAI,CAAC,CAAE,OAAO,EAAExB,KAAK,CAAE,CAAC;IAChC;EACF;EAEAX,KAAK,CAACQ,GAAG,GAAGA,GAAG;EACfR,KAAK,CAACkB,MAAM,GAAGD,GAAG;EAClB,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}