{"ast": null, "code": "module.exports = function () {\n  if (typeof self !== 'undefined') {\n    return self;\n  } else if (typeof window !== 'undefined') {\n    return window;\n  } else {\n    return Function('return this')(); // eslint-disable-line no-new-func\n  }\n}();", "map": {"version": 3, "names": ["module", "exports", "self", "window", "Function"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/engine.io-client@3.5.4/node_modules/engine.io-client/lib/globalThis.browser.js"], "sourcesContent": ["module.exports = (function () {\n  if (typeof self !== 'undefined') {\n    return self;\n  } else if (typeof window !== 'undefined') {\n    return window;\n  } else {\n    return Function('return this')(); // eslint-disable-line no-new-func\n  }\n})();\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAI,YAAY;EAC5B,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;IAC/B,OAAOA,IAAI;EACb,CAAC,MAAM,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;IACxC,OAAOA,MAAM;EACf,CAAC,MAAM;IACL,OAAOC,QAAQ,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC;EACpC;AACF,CAAC,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}