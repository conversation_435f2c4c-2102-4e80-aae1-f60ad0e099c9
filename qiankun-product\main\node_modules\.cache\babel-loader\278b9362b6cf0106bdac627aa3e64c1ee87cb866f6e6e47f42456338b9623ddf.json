{"ast": null, "code": "// Normalize input string\n\n'use strict';\n\n// https://spec.commonmark.org/0.29/#line-ending\nvar NEWLINES_RE = /\\r\\n?|\\n/g;\nvar NULL_RE = /\\0/g;\nmodule.exports = function normalize(state) {\n  var str;\n\n  // Normalize newlines\n  str = state.src.replace(NEWLINES_RE, '\\n');\n\n  // Replace NULL characters\n  str = str.replace(NULL_RE, \"\\uFFFD\");\n  state.src = str;\n};", "map": {"version": 3, "names": ["NEWLINES_RE", "NULL_RE", "module", "exports", "normalize", "state", "str", "src", "replace"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_core/normalize.js"], "sourcesContent": ["// Normalize input string\n\n'use strict';\n\n\n// https://spec.commonmark.org/0.29/#line-ending\nvar NEWLINES_RE  = /\\r\\n?|\\n/g;\nvar NULL_RE      = /\\0/g;\n\n\nmodule.exports = function normalize(state) {\n  var str;\n\n  // Normalize newlines\n  str = state.src.replace(NEWLINES_RE, '\\n');\n\n  // Replace NULL characters\n  str = str.replace(NULL_RE, '\\uFFFD');\n\n  state.src = str;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAGZ;AACA,IAAIA,WAAW,GAAI,WAAW;AAC9B,IAAIC,OAAO,GAAQ,KAAK;AAGxBC,MAAM,CAACC,OAAO,GAAG,SAASC,SAASA,CAACC,KAAK,EAAE;EACzC,IAAIC,GAAG;;EAEP;EACAA,GAAG,GAAGD,KAAK,CAACE,GAAG,CAACC,OAAO,CAACR,WAAW,EAAE,IAAI,CAAC;;EAE1C;EACAM,GAAG,GAAGA,GAAG,CAACE,OAAO,CAACP,OAAO,EAAE,QAAQ,CAAC;EAEpCI,KAAK,CAACE,GAAG,GAAGD,GAAG;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}