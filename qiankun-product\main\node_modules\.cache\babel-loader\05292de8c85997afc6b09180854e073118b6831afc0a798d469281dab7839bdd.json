{"ast": null, "code": "/*\nLanguage: VHDL\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nDescription: VHDL is a hardware description language used in electronic design automation to describe digital and mixed-signal systems.\nWebsite: https://en.wikipedia.org/wiki/VHDL\nCategory: hardware\n*/\n\nfunction vhdl(hljs) {\n  // Regular expression for VHDL numeric literals.\n\n  // Decimal literal:\n  var INTEGER_RE = '\\\\d(_|\\\\d)*';\n  var EXPONENT_RE = '[eE][-+]?' + INTEGER_RE;\n  var DECIMAL_LITERAL_RE = INTEGER_RE + '(\\\\.' + INTEGER_RE + ')?' + '(' + EXPONENT_RE + ')?';\n  // Based literal:\n  var BASED_INTEGER_RE = '\\\\w+';\n  var BASED_LITERAL_RE = INTEGER_RE + '#' + BASED_INTEGER_RE + '(\\\\.' + BASED_INTEGER_RE + ')?' + '#' + '(' + EXPONENT_RE + ')?';\n  var NUMBER_RE = '\\\\b(' + BASED_LITERAL_RE + '|' + DECIMAL_LITERAL_RE + ')';\n  var KEYWORDS = [\"abs\", \"access\", \"after\", \"alias\", \"all\", \"and\", \"architecture\", \"array\", \"assert\", \"assume\", \"assume_guarantee\", \"attribute\", \"begin\", \"block\", \"body\", \"buffer\", \"bus\", \"case\", \"component\", \"configuration\", \"constant\", \"context\", \"cover\", \"disconnect\", \"downto\", \"default\", \"else\", \"elsif\", \"end\", \"entity\", \"exit\", \"fairness\", \"file\", \"for\", \"force\", \"function\", \"generate\", \"generic\", \"group\", \"guarded\", \"if\", \"impure\", \"in\", \"inertial\", \"inout\", \"is\", \"label\", \"library\", \"linkage\", \"literal\", \"loop\", \"map\", \"mod\", \"nand\", \"new\", \"next\", \"nor\", \"not\", \"null\", \"of\", \"on\", \"open\", \"or\", \"others\", \"out\", \"package\", \"parameter\", \"port\", \"postponed\", \"procedure\", \"process\", \"property\", \"protected\", \"pure\", \"range\", \"record\", \"register\", \"reject\", \"release\", \"rem\", \"report\", \"restrict\", \"restrict_guarantee\", \"return\", \"rol\", \"ror\", \"select\", \"sequence\", \"severity\", \"shared\", \"signal\", \"sla\", \"sll\", \"sra\", \"srl\", \"strong\", \"subtype\", \"then\", \"to\", \"transport\", \"type\", \"unaffected\", \"units\", \"until\", \"use\", \"variable\", \"view\", \"vmode\", \"vprop\", \"vunit\", \"wait\", \"when\", \"while\", \"with\", \"xnor\", \"xor\"];\n  var BUILT_INS = [\"boolean\", \"bit\", \"character\", \"integer\", \"time\", \"delay_length\", \"natural\", \"positive\", \"string\", \"bit_vector\", \"file_open_kind\", \"file_open_status\", \"std_logic\", \"std_logic_vector\", \"unsigned\", \"signed\", \"boolean_vector\", \"integer_vector\", \"std_ulogic\", \"std_ulogic_vector\", \"unresolved_unsigned\", \"u_unsigned\", \"unresolved_signed\", \"u_signed\", \"real_vector\", \"time_vector\"];\n  var LITERALS = [\n  // severity_level\n  \"false\", \"true\", \"note\", \"warning\", \"error\", \"failure\",\n  // textio\n  \"line\", \"text\", \"side\", \"width\"];\n  return {\n    name: 'VHDL',\n    case_insensitive: true,\n    keywords: {\n      keyword: KEYWORDS,\n      built_in: BUILT_INS,\n      literal: LITERALS\n    },\n    illegal: /\\{/,\n    contains: [hljs.C_BLOCK_COMMENT_MODE,\n    // VHDL-2008 block commenting.\n    hljs.COMMENT('--', '$'), hljs.QUOTE_STRING_MODE, {\n      className: 'number',\n      begin: NUMBER_RE,\n      relevance: 0\n    }, {\n      className: 'string',\n      begin: '\\'(U|X|0|1|Z|W|L|H|-)\\'',\n      contains: [hljs.BACKSLASH_ESCAPE]\n    }, {\n      className: 'symbol',\n      begin: '\\'[A-Za-z](_?[A-Za-z0-9])*',\n      contains: [hljs.BACKSLASH_ESCAPE]\n    }]\n  };\n}\nexport { vhdl as default };", "map": {"version": 3, "names": ["vhdl", "hljs", "INTEGER_RE", "EXPONENT_RE", "DECIMAL_LITERAL_RE", "BASED_INTEGER_RE", "BASED_LITERAL_RE", "NUMBER_RE", "KEYWORDS", "BUILT_INS", "LITERALS", "name", "case_insensitive", "keywords", "keyword", "built_in", "literal", "illegal", "contains", "C_BLOCK_COMMENT_MODE", "COMMENT", "QUOTE_STRING_MODE", "className", "begin", "relevance", "BACKSLASH_ESCAPE", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/highlight.js@11.11.1/node_modules/highlight.js/es/languages/vhdl.js"], "sourcesContent": ["/*\nLanguage: VHDL\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nDescription: VHDL is a hardware description language used in electronic design automation to describe digital and mixed-signal systems.\nWebsite: https://en.wikipedia.org/wiki/VHDL\nCategory: hardware\n*/\n\nfunction vhdl(hljs) {\n  // Regular expression for VHDL numeric literals.\n\n  // Decimal literal:\n  const INTEGER_RE = '\\\\d(_|\\\\d)*';\n  const EXPONENT_RE = '[eE][-+]?' + INTEGER_RE;\n  const DECIMAL_LITERAL_RE = INTEGER_RE + '(\\\\.' + INTEGER_RE + ')?' + '(' + EXPONENT_RE + ')?';\n  // Based literal:\n  const BASED_INTEGER_RE = '\\\\w+';\n  const BASED_LITERAL_RE = INTEGER_RE + '#' + BASED_INTEGER_RE + '(\\\\.' + BASED_INTEGER_RE + ')?' + '#' + '(' + EXPONENT_RE + ')?';\n\n  const NUMBER_RE = '\\\\b(' + BASED_LITERAL_RE + '|' + DECIMAL_LITERAL_RE + ')';\n\n  const KEYWORDS = [\n    \"abs\",\n    \"access\",\n    \"after\",\n    \"alias\",\n    \"all\",\n    \"and\",\n    \"architecture\",\n    \"array\",\n    \"assert\",\n    \"assume\",\n    \"assume_guarantee\",\n    \"attribute\",\n    \"begin\",\n    \"block\",\n    \"body\",\n    \"buffer\",\n    \"bus\",\n    \"case\",\n    \"component\",\n    \"configuration\",\n    \"constant\",\n    \"context\",\n    \"cover\",\n    \"disconnect\",\n    \"downto\",\n    \"default\",\n    \"else\",\n    \"elsif\",\n    \"end\",\n    \"entity\",\n    \"exit\",\n    \"fairness\",\n    \"file\",\n    \"for\",\n    \"force\",\n    \"function\",\n    \"generate\",\n    \"generic\",\n    \"group\",\n    \"guarded\",\n    \"if\",\n    \"impure\",\n    \"in\",\n    \"inertial\",\n    \"inout\",\n    \"is\",\n    \"label\",\n    \"library\",\n    \"linkage\",\n    \"literal\",\n    \"loop\",\n    \"map\",\n    \"mod\",\n    \"nand\",\n    \"new\",\n    \"next\",\n    \"nor\",\n    \"not\",\n    \"null\",\n    \"of\",\n    \"on\",\n    \"open\",\n    \"or\",\n    \"others\",\n    \"out\",\n    \"package\",\n    \"parameter\",\n    \"port\",\n    \"postponed\",\n    \"procedure\",\n    \"process\",\n    \"property\",\n    \"protected\",\n    \"pure\",\n    \"range\",\n    \"record\",\n    \"register\",\n    \"reject\",\n    \"release\",\n    \"rem\",\n    \"report\",\n    \"restrict\",\n    \"restrict_guarantee\",\n    \"return\",\n    \"rol\",\n    \"ror\",\n    \"select\",\n    \"sequence\",\n    \"severity\",\n    \"shared\",\n    \"signal\",\n    \"sla\",\n    \"sll\",\n    \"sra\",\n    \"srl\",\n    \"strong\",\n    \"subtype\",\n    \"then\",\n    \"to\",\n    \"transport\",\n    \"type\",\n    \"unaffected\",\n    \"units\",\n    \"until\",\n    \"use\",\n    \"variable\",\n    \"view\",\n    \"vmode\",\n    \"vprop\",\n    \"vunit\",\n    \"wait\",\n    \"when\",\n    \"while\",\n    \"with\",\n    \"xnor\",\n    \"xor\"\n  ];\n  const BUILT_INS = [\n    \"boolean\",\n    \"bit\",\n    \"character\",\n    \"integer\",\n    \"time\",\n    \"delay_length\",\n    \"natural\",\n    \"positive\",\n    \"string\",\n    \"bit_vector\",\n    \"file_open_kind\",\n    \"file_open_status\",\n    \"std_logic\",\n    \"std_logic_vector\",\n    \"unsigned\",\n    \"signed\",\n    \"boolean_vector\",\n    \"integer_vector\",\n    \"std_ulogic\",\n    \"std_ulogic_vector\",\n    \"unresolved_unsigned\",\n    \"u_unsigned\",\n    \"unresolved_signed\",\n    \"u_signed\",\n    \"real_vector\",\n    \"time_vector\"\n  ];\n  const LITERALS = [\n    // severity_level\n    \"false\",\n    \"true\",\n    \"note\",\n    \"warning\",\n    \"error\",\n    \"failure\",\n    // textio\n    \"line\",\n    \"text\",\n    \"side\",\n    \"width\"\n  ];\n\n  return {\n    name: 'VHDL',\n    case_insensitive: true,\n    keywords: {\n      keyword: KEYWORDS,\n      built_in: BUILT_INS,\n      literal: LITERALS\n    },\n    illegal: /\\{/,\n    contains: [\n      hljs.C_BLOCK_COMMENT_MODE, // VHDL-2008 block commenting.\n      hljs.COMMENT('--', '$'),\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'number',\n        begin: NUMBER_RE,\n        relevance: 0\n      },\n      {\n        className: 'string',\n        begin: '\\'(U|X|0|1|Z|W|L|H|-)\\'',\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        className: 'symbol',\n        begin: '\\'[A-Za-z](_?[A-Za-z0-9])*',\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      }\n    ]\n  };\n}\n\nexport { vhdl as default };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,IAAIA,CAACC,IAAI,EAAE;EAClB;;EAEA;EACA,IAAMC,UAAU,GAAG,aAAa;EAChC,IAAMC,WAAW,GAAG,WAAW,GAAGD,UAAU;EAC5C,IAAME,kBAAkB,GAAGF,UAAU,GAAG,MAAM,GAAGA,UAAU,GAAG,IAAI,GAAG,GAAG,GAAGC,WAAW,GAAG,IAAI;EAC7F;EACA,IAAME,gBAAgB,GAAG,MAAM;EAC/B,IAAMC,gBAAgB,GAAGJ,UAAU,GAAG,GAAG,GAAGG,gBAAgB,GAAG,MAAM,GAAGA,gBAAgB,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAGF,WAAW,GAAG,IAAI;EAEhI,IAAMI,SAAS,GAAG,MAAM,GAAGD,gBAAgB,GAAG,GAAG,GAAGF,kBAAkB,GAAG,GAAG;EAE5E,IAAMI,QAAQ,GAAG,CACf,KAAK,EACL,QAAQ,EACR,OAAO,EACP,OAAO,EACP,KAAK,EACL,KAAK,EACL,cAAc,EACd,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,kBAAkB,EAClB,WAAW,EACX,OAAO,EACP,OAAO,EACP,MAAM,EACN,QAAQ,EACR,KAAK,EACL,MAAM,EACN,WAAW,EACX,eAAe,EACf,UAAU,EACV,SAAS,EACT,OAAO,EACP,YAAY,EACZ,QAAQ,EACR,SAAS,EACT,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,MAAM,EACN,UAAU,EACV,MAAM,EACN,KAAK,EACL,OAAO,EACP,UAAU,EACV,UAAU,EACV,SAAS,EACT,OAAO,EACP,SAAS,EACT,IAAI,EACJ,QAAQ,EACR,IAAI,EACJ,UAAU,EACV,OAAO,EACP,IAAI,EACJ,OAAO,EACP,SAAS,EACT,SAAS,EACT,SAAS,EACT,MAAM,EACN,KAAK,EACL,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,MAAM,EACN,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,KAAK,EACL,SAAS,EACT,WAAW,EACX,MAAM,EACN,WAAW,EACX,WAAW,EACX,SAAS,EACT,UAAU,EACV,WAAW,EACX,MAAM,EACN,OAAO,EACP,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,SAAS,EACT,KAAK,EACL,QAAQ,EACR,UAAU,EACV,oBAAoB,EACpB,QAAQ,EACR,KAAK,EACL,KAAK,EACL,QAAQ,EACR,UAAU,EACV,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,KAAK,EACL,KAAK,EACL,KAAK,EACL,KAAK,EACL,QAAQ,EACR,SAAS,EACT,MAAM,EACN,IAAI,EACJ,WAAW,EACX,MAAM,EACN,YAAY,EACZ,OAAO,EACP,OAAO,EACP,KAAK,EACL,UAAU,EACV,MAAM,EACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,MAAM,EACN,OAAO,EACP,MAAM,EACN,MAAM,EACN,KAAK,CACN;EACD,IAAMC,SAAS,GAAG,CAChB,SAAS,EACT,KAAK,EACL,WAAW,EACX,SAAS,EACT,MAAM,EACN,cAAc,EACd,SAAS,EACT,UAAU,EACV,QAAQ,EACR,YAAY,EACZ,gBAAgB,EAChB,kBAAkB,EAClB,WAAW,EACX,kBAAkB,EAClB,UAAU,EACV,QAAQ,EACR,gBAAgB,EAChB,gBAAgB,EAChB,YAAY,EACZ,mBAAmB,EACnB,qBAAqB,EACrB,YAAY,EACZ,mBAAmB,EACnB,UAAU,EACV,aAAa,EACb,aAAa,CACd;EACD,IAAMC,QAAQ,GAAG;EACf;EACA,OAAO,EACP,MAAM,EACN,MAAM,EACN,SAAS,EACT,OAAO,EACP,SAAS;EACT;EACA,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,CACR;EAED,OAAO;IACLC,IAAI,EAAE,MAAM;IACZC,gBAAgB,EAAE,IAAI;IACtBC,QAAQ,EAAE;MACRC,OAAO,EAAEN,QAAQ;MACjBO,QAAQ,EAAEN,SAAS;MACnBO,OAAO,EAAEN;IACX,CAAC;IACDO,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,CACRjB,IAAI,CAACkB,oBAAoB;IAAE;IAC3BlB,IAAI,CAACmB,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,EACvBnB,IAAI,CAACoB,iBAAiB,EACtB;MACEC,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAEhB,SAAS;MAChBiB,SAAS,EAAE;IACb,CAAC,EACD;MACEF,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,yBAAyB;MAChCL,QAAQ,EAAE,CAAEjB,IAAI,CAACwB,gBAAgB;IACnC,CAAC,EACD;MACEH,SAAS,EAAE,QAAQ;MACnBC,KAAK,EAAE,4BAA4B;MACnCL,QAAQ,EAAE,CAAEjB,IAAI,CAACwB,gBAAgB;IACnC,CAAC;EAEL,CAAC;AACH;AAEA,SAASzB,IAAI,IAAI0B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}