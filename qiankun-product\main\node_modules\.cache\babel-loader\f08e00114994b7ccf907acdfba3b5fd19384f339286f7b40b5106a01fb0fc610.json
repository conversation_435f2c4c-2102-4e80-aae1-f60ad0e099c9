{"ast": null, "code": "import { ref, inject, onMounted } from 'vue';\nimport { useStore } from 'vuex';\nimport { ElMessage } from 'element-plus';\nvar __default__ = {\n  name: 'GlobalAiControls'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var store = useStore();\n    var textSelection = inject('textSelection');\n    var top = ref('-100%');\n    var left = ref('-100%');\n    var content = ref('');\n    var isHovering = ref(false);\n    var isActive = ref(false);\n    var controlRef = ref(null);\n    var handleMouseEnter = function handleMouseEnter() {\n      isHovering.value = true;\n    };\n    var handleMouseLeave = function handleMouseLeave() {\n      isHovering.value = false;\n      if (isActive.value) {\n        handleClick();\n      }\n    };\n    var handleClick = function handleClick() {\n      var force = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n      if (!isHovering.value || force) {\n        content.value = '';\n        top.value = '-100%';\n        left.value = '-100%';\n        isActive.value = false;\n      }\n    };\n    var handleSearch = function handleSearch() {\n      store.commit('setAiChatConfig', {\n        AiChatWindow: true,\n        AiChatSendMessage: '搜索一下：' + content.value\n      });\n      handleClick(true);\n    };\n    var handleParaphrase = function handleParaphrase() {\n      store.commit('setAiChatConfig', {\n        AiChatWindow: true,\n        AiChatSendMessage: '释义：' + content.value\n      });\n      handleClick(true);\n    };\n    var handleCopy = function handleCopy() {\n      var textarea = document.createElement('textarea');\n      textarea.readOnly = 'readonly';\n      textarea.style.position = 'absolute';\n      textarea.style.left = '-9999px';\n      textarea.value = content.value;\n      document.body.appendChild(textarea);\n      textarea.select();\n      var result = document.execCommand('Copy');\n      if (result) {\n        ElMessage({\n          message: '复制成功',\n          type: 'success'\n        });\n      }\n      document.body.removeChild(textarea);\n      handleClick(true);\n    };\n    var calculateSafePosition = function calculateSafePosition(x, y) {\n      var control = controlRef.value;\n      if (!control) return {\n        x,\n        y\n      };\n      var controlWidth = control.offsetWidth;\n      var controlHeight = control.offsetHeight;\n      var viewportWidth = window.innerWidth;\n      var viewportHeight = window.innerHeight;\n      // 计算安全的 X 坐标\n      var safeX = x;\n      if (x + controlWidth > viewportWidth) {\n        safeX = viewportWidth - controlWidth - 10;\n      }\n      if (safeX < 10) safeX = 10;\n      // 计算安全的 Y 坐标\n      var safeY = y;\n      // 默认显示在上方，距离为一个控件高度\n      safeY = y - controlHeight - 10;\n      // 如果上方空间不足，则显示在下方\n      if (safeY < 10) {\n        safeY = y + controlHeight;\n      }\n      // 如果下方也没有足够空间，则尽可能靠近顶部或底部\n      if (safeY + controlHeight > viewportHeight - 10) {\n        safeY = viewportHeight - controlHeight - 10;\n      }\n      return {\n        x: safeX,\n        y: safeY\n      };\n    };\n    onMounted(function () {\n      textSelection.listen(function (text, event, hasClick, coordinates) {\n        if (!hasClick) {\n          content.value = text;\n          var _calculateSafePositio = calculateSafePosition(coordinates.x, coordinates.y),\n            x = _calculateSafePositio.x,\n            y = _calculateSafePositio.y;\n          top.value = `${y}px`;\n          left.value = `${x}px`;\n        }\n      }, function (event) {\n        isActive.value = true;\n        if (!isHovering.value) {\n          handleClick();\n        }\n      });\n    });\n    var __returned__ = {\n      store,\n      textSelection,\n      top,\n      left,\n      content,\n      isHovering,\n      isActive,\n      controlRef,\n      handleMouseEnter,\n      handleMouseLeave,\n      handleClick,\n      handleSearch,\n      handleParaphrase,\n      handleCopy,\n      calculateSafePosition,\n      ref,\n      inject,\n      onMounted,\n      get useStore() {\n        return useStore;\n      },\n      get ElMessage() {\n        return ElMessage;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "inject", "onMounted", "useStore", "ElMessage", "__default__", "name", "store", "textSelection", "top", "left", "content", "isHovering", "isActive", "controlRef", "handleMouseEnter", "value", "handleMouseLeave", "handleClick", "force", "arguments", "length", "undefined", "handleSearch", "commit", "AiChatWindow", "AiChatSendMessage", "handleParaphrase", "handleCopy", "textarea", "document", "createElement", "readOnly", "style", "position", "body", "append<PERSON><PERSON><PERSON>", "select", "result", "execCommand", "message", "type", "<PERSON><PERSON><PERSON><PERSON>", "calculateSafePosition", "x", "y", "control", "controlWidth", "offsetWidth", "controlHeight", "offsetHeight", "viewportWidth", "window", "innerWidth", "viewportHeight", "innerHeight", "safeX", "safeY", "listen", "text", "event", "hasClick", "coordinates", "_calculateSafePositio"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/LayoutContainer/components/GlobalAiControls.vue"], "sourcesContent": ["<template>\r\n  <div ref=\"controlRef\" class=\"GlobalAiControls forbidSelect\" :style=\"{ top: top, left: left }\"\r\n    @mouseenter=\"handleMouseEnter\" @mouseleave=\"handleMouseLeave\">\r\n    <div class=\"GlobalAiControlsItem\" @click=\"handleSearch\">\r\n      <el-icon>\r\n        <Search />\r\n      </el-icon>AI搜索\r\n    </div>\r\n    <div class=\"GlobalAiControlsItem\" @click=\"handleParaphrase\">\r\n      <el-icon>\r\n        <Collection />\r\n      </el-icon>释义\r\n    </div>\r\n    <div class=\"GlobalAiControlsItem\" @click=\"handleCopy\">\r\n      <el-icon>\r\n        <CopyDocument />\r\n      </el-icon>复制\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalAiControls' }\r\n</script>\r\n<script setup>\r\nimport { ref, inject, onMounted } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { ElMessage } from 'element-plus'\r\nconst store = useStore()\r\nconst textSelection = inject('textSelection')\r\nconst top = ref('-100%')\r\nconst left = ref('-100%')\r\nconst content = ref('')\r\nconst isHovering = ref(false)\r\nconst isActive = ref(false)\r\nconst controlRef = ref(null)\r\n\r\nconst handleMouseEnter = () => {\r\n  isHovering.value = true\r\n}\r\n\r\nconst handleMouseLeave = () => {\r\n  isHovering.value = false\r\n  if (isActive.value) {\r\n    handleClick()\r\n  }\r\n}\r\n\r\nconst handleClick = (force = false) => {\r\n  if (!isHovering.value || force) {\r\n    content.value = ''\r\n    top.value = '-100%'\r\n    left.value = '-100%'\r\n    isActive.value = false\r\n  }\r\n}\r\n\r\nconst handleSearch = () => {\r\n  store.commit('setAiChatConfig', {\r\n    AiChatWindow: true,\r\n    AiChatSendMessage: '搜索一下：' + content.value\r\n  })\r\n  handleClick(true)\r\n}\r\nconst handleParaphrase = () => {\r\n  store.commit('setAiChatConfig', {\r\n    AiChatWindow: true,\r\n    AiChatSendMessage: '释义：' + content.value\r\n  })\r\n  handleClick(true)\r\n}\r\nconst handleCopy = () => {\r\n  const textarea = document.createElement('textarea')\r\n  textarea.readOnly = 'readonly'\r\n  textarea.style.position = 'absolute'\r\n  textarea.style.left = '-9999px'\r\n  textarea.value = content.value\r\n  document.body.appendChild(textarea)\r\n  textarea.select()\r\n  const result = document.execCommand('Copy')\r\n  if (result) {\r\n    ElMessage({ message: '复制成功', type: 'success' })\r\n  }\r\n  document.body.removeChild(textarea)\r\n  handleClick(true)\r\n}\r\n\r\nconst calculateSafePosition = (x, y) => {\r\n  const control = controlRef.value\r\n  if (!control) return { x, y }\r\n  const controlWidth = control.offsetWidth\r\n  const controlHeight = control.offsetHeight\r\n  const viewportWidth = window.innerWidth\r\n  const viewportHeight = window.innerHeight\r\n  // 计算安全的 X 坐标\r\n  let safeX = x\r\n  if (x + controlWidth > viewportWidth) {\r\n    safeX = viewportWidth - controlWidth - 10\r\n  }\r\n  if (safeX < 10) safeX = 10\r\n  // 计算安全的 Y 坐标\r\n  let safeY = y\r\n  // 默认显示在上方，距离为一个控件高度\r\n  safeY = y - controlHeight - 10\r\n  // 如果上方空间不足，则显示在下方\r\n  if (safeY < 10) {\r\n    safeY = y + controlHeight\r\n  }\r\n  // 如果下方也没有足够空间，则尽可能靠近顶部或底部\r\n  if (safeY + controlHeight > viewportHeight - 10) {\r\n    safeY = viewportHeight - controlHeight - 10\r\n  }\r\n  return { x: safeX, y: safeY }\r\n}\r\n\r\nonMounted(() => {\r\n  textSelection.listen(\r\n    (text, event, hasClick, coordinates) => {\r\n      if (!hasClick) {\r\n        content.value = text\r\n        const { x, y } = calculateSafePosition(coordinates.x, coordinates.y)\r\n        top.value = `${y}px`\r\n        left.value = `${x}px`\r\n      }\r\n    },\r\n    (event) => {\r\n      isActive.value = true\r\n      if (!isHovering.value) {\r\n        handleClick()\r\n      }\r\n    }\r\n  )\r\n}) \r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.GlobalAiControls {\r\n  height: var(--zy-height);\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 12px;\r\n  background: #fff;\r\n  box-shadow: var(--zy-el-box-shadow-light);\r\n  border-radius: 6px;\r\n  position: fixed;\r\n  // transform: translateY(-120%);\r\n  z-index: 9999;\r\n\r\n  .GlobalAiControlsItem {\r\n    height: var(--zy-height-secondary);\r\n    display: flex;\r\n    align-items: center;\r\n    font-size: var(--zy-text-font-size);\r\n    border-radius: 6px;\r\n    padding: 0 12px;\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      color: var(--zy-el-color-primary);\r\n      background: var(--zy-el-color-primary-light-9);\r\n    }\r\n\r\n    .zy-el-icon {\r\n      margin-right: 2px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAwBA,SAASA,GAAG,EAAEC,MAAM,EAAEC,SAAS,QAAQ,KAAK;AAC5C,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,SAAS,QAAQ,cAAc;AALxC,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAmB,CAAC;;;;;IAM3C,IAAMC,KAAK,GAAGJ,QAAQ,CAAC,CAAC;IACxB,IAAMK,aAAa,GAAGP,MAAM,CAAC,eAAe,CAAC;IAC7C,IAAMQ,GAAG,GAAGT,GAAG,CAAC,OAAO,CAAC;IACxB,IAAMU,IAAI,GAAGV,GAAG,CAAC,OAAO,CAAC;IACzB,IAAMW,OAAO,GAAGX,GAAG,CAAC,EAAE,CAAC;IACvB,IAAMY,UAAU,GAAGZ,GAAG,CAAC,KAAK,CAAC;IAC7B,IAAMa,QAAQ,GAAGb,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMc,UAAU,GAAGd,GAAG,CAAC,IAAI,CAAC;IAE5B,IAAMe,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;MAC7BH,UAAU,CAACI,KAAK,GAAG,IAAI;IACzB,CAAC;IAED,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;MAC7BL,UAAU,CAACI,KAAK,GAAG,KAAK;MACxB,IAAIH,QAAQ,CAACG,KAAK,EAAE;QAClBE,WAAW,CAAC,CAAC;MACf;IACF,CAAC;IAED,IAAMA,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAsB;MAAA,IAAlBC,KAAK,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;MAChC,IAAI,CAACR,UAAU,CAACI,KAAK,IAAIG,KAAK,EAAE;QAC9BR,OAAO,CAACK,KAAK,GAAG,EAAE;QAClBP,GAAG,CAACO,KAAK,GAAG,OAAO;QACnBN,IAAI,CAACM,KAAK,GAAG,OAAO;QACpBH,QAAQ,CAACG,KAAK,GAAG,KAAK;MACxB;IACF,CAAC;IAED,IAAMO,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzBhB,KAAK,CAACiB,MAAM,CAAC,iBAAiB,EAAE;QAC9BC,YAAY,EAAE,IAAI;QAClBC,iBAAiB,EAAE,OAAO,GAAGf,OAAO,CAACK;MACvC,CAAC,CAAC;MACFE,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC;IACD,IAAMS,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;MAC7BpB,KAAK,CAACiB,MAAM,CAAC,iBAAiB,EAAE;QAC9BC,YAAY,EAAE,IAAI;QAClBC,iBAAiB,EAAE,KAAK,GAAGf,OAAO,CAACK;MACrC,CAAC,CAAC;MACFE,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC;IACD,IAAMU,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAMC,QAAQ,GAAGC,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;MACnDF,QAAQ,CAACG,QAAQ,GAAG,UAAU;MAC9BH,QAAQ,CAACI,KAAK,CAACC,QAAQ,GAAG,UAAU;MACpCL,QAAQ,CAACI,KAAK,CAACvB,IAAI,GAAG,SAAS;MAC/BmB,QAAQ,CAACb,KAAK,GAAGL,OAAO,CAACK,KAAK;MAC9Bc,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,QAAQ,CAAC;MACnCA,QAAQ,CAACQ,MAAM,CAAC,CAAC;MACjB,IAAMC,MAAM,GAAGR,QAAQ,CAACS,WAAW,CAAC,MAAM,CAAC;MAC3C,IAAID,MAAM,EAAE;QACVlC,SAAS,CAAC;UAAEoC,OAAO,EAAE,MAAM;UAAEC,IAAI,EAAE;QAAU,CAAC,CAAC;MACjD;MACAX,QAAQ,CAACK,IAAI,CAACO,WAAW,CAACb,QAAQ,CAAC;MACnCX,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC;IAED,IAAMyB,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIC,CAAC,EAAEC,CAAC,EAAK;MACtC,IAAMC,OAAO,GAAGhC,UAAU,CAACE,KAAK;MAChC,IAAI,CAAC8B,OAAO,EAAE,OAAO;QAAEF,CAAC;QAAEC;MAAE,CAAC;MAC7B,IAAME,YAAY,GAAGD,OAAO,CAACE,WAAW;MACxC,IAAMC,aAAa,GAAGH,OAAO,CAACI,YAAY;MAC1C,IAAMC,aAAa,GAAGC,MAAM,CAACC,UAAU;MACvC,IAAMC,cAAc,GAAGF,MAAM,CAACG,WAAW;MACzC;MACA,IAAIC,KAAK,GAAGZ,CAAC;MACb,IAAIA,CAAC,GAAGG,YAAY,GAAGI,aAAa,EAAE;QACpCK,KAAK,GAAGL,aAAa,GAAGJ,YAAY,GAAG,EAAE;MAC3C;MACA,IAAIS,KAAK,GAAG,EAAE,EAAEA,KAAK,GAAG,EAAE;MAC1B;MACA,IAAIC,KAAK,GAAGZ,CAAC;MACb;MACAY,KAAK,GAAGZ,CAAC,GAAGI,aAAa,GAAG,EAAE;MAC9B;MACA,IAAIQ,KAAK,GAAG,EAAE,EAAE;QACdA,KAAK,GAAGZ,CAAC,GAAGI,aAAa;MAC3B;MACA;MACA,IAAIQ,KAAK,GAAGR,aAAa,GAAGK,cAAc,GAAG,EAAE,EAAE;QAC/CG,KAAK,GAAGH,cAAc,GAAGL,aAAa,GAAG,EAAE;MAC7C;MACA,OAAO;QAAEL,CAAC,EAAEY,KAAK;QAAEX,CAAC,EAAEY;MAAM,CAAC;IAC/B,CAAC;IAEDvD,SAAS,CAAC,YAAM;MACdM,aAAa,CAACkD,MAAM,CAClB,UAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,WAAW,EAAK;QACtC,IAAI,CAACD,QAAQ,EAAE;UACblD,OAAO,CAACK,KAAK,GAAG2C,IAAI;UACpB,IAAAI,qBAAA,GAAiBpB,qBAAqB,CAACmB,WAAW,CAAClB,CAAC,EAAEkB,WAAW,CAACjB,CAAC,CAAC;YAA5DD,CAAC,GAAAmB,qBAAA,CAADnB,CAAC;YAAEC,CAAC,GAAAkB,qBAAA,CAADlB,CAAC;UACZpC,GAAG,CAACO,KAAK,GAAG,GAAG6B,CAAC,IAAI;UACpBnC,IAAI,CAACM,KAAK,GAAG,GAAG4B,CAAC,IAAI;QACvB;MACF,CAAC,EACD,UAACgB,KAAK,EAAK;QACT/C,QAAQ,CAACG,KAAK,GAAG,IAAI;QACrB,IAAI,CAACJ,UAAU,CAACI,KAAK,EAAE;UACrBE,WAAW,CAAC,CAAC;QACf;MACF,CACF,CAAC;IACH,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}