{"ast": null, "code": "function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };", "map": {"version": 3, "names": ["_setPrototypeOf", "t", "e", "Object", "setPrototypeOf", "bind", "__proto__", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/@babel+runtime@7.26.0/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js"], "sourcesContent": ["function _setPrototypeOf(t, e) {\n  return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) {\n    return t.__proto__ = e, t;\n  }, _setPrototypeOf(t, e);\n}\nexport { _setPrototypeOf as default };"], "mappings": "AAAA,SAASA,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC7B,OAAOF,eAAe,GAAGG,MAAM,CAACC,cAAc,GAAGD,MAAM,CAACC,cAAc,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUJ,CAAC,EAAEC,CAAC,EAAE;IAC9F,OAAOD,CAAC,CAACK,SAAS,GAAGJ,CAAC,EAAED,CAAC;EAC3B,CAAC,EAAED,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;AAC1B;AACA,SAASF,eAAe,IAAIO,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}