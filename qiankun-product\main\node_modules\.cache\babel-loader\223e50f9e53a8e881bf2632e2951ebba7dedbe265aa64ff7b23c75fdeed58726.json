{"ast": null, "code": "//[4]   \tNameStartChar\t   ::=   \t\":\" | [A-Z] | \"_\" | [a-z] | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x2FF] | [#x370-#x37D] | [#x37F-#x1FFF] | [#x200C-#x200D] | [#x2070-#x218F] | [#x2C00-#x2FEF] | [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n//[4a]   \tNameChar\t   ::=   \tNameStartChar | \"-\" | \".\" | [0-9] | #xB7 | [#x0300-#x036F] | [#x203F-#x2040]\n//[5]   \tName\t   ::=   \tNameStartChar (NameChar)*\nvar nameStartChar = /[A-Z_a-z\\xC0-\\xD6\\xD8-\\xF6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]/; //\\u10000-\\uEFFFF\nvar nameChar = new RegExp(\"[\\\\-\\\\.0-9\" + nameStartChar.source.slice(1, -1) + \"\\\\u00B7\\\\u0300-\\\\u036F\\\\u203F-\\\\u2040]\");\nvar tagNamePattern = new RegExp('^' + nameStartChar.source + nameChar.source + '*(?:\\:' + nameStartChar.source + nameChar.source + '*)?$');\n//var tagNamePattern = /^[a-zA-Z_][\\w\\-\\.]*(?:\\:[a-zA-Z_][\\w\\-\\.]*)?$/\n//var handlers = 'resolveEntity,getExternalSubset,characters,endDocument,endElement,endPrefixMapping,ignorableWhitespace,processingInstruction,setDocumentLocator,skippedEntity,startDocument,startElement,startPrefixMapping,notationDecl,unparsedEntityDecl,error,fatalError,warning,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,comment,endCDATA,endDTD,endEntity,startCDATA,startDTD,startEntity'.split(',')\n\n//S_TAG,\tS_ATTR,\tS_EQ,\tS_ATTR_NOQUOT_VALUE\n//S_ATTR_SPACE,\tS_ATTR_END,\tS_TAG_SPACE, S_TAG_CLOSE\nvar S_TAG = 0; //tag name offerring\nvar S_ATTR = 1; //attr name offerring \nvar S_ATTR_SPACE = 2; //attr name end and space offer\nvar S_EQ = 3; //=space?\nvar S_ATTR_NOQUOT_VALUE = 4; //attr value(no quot value only)\nvar S_ATTR_END = 5; //attr value end and no space(quot end)\nvar S_TAG_SPACE = 6; //(attr value end || tag end ) && (space offer)\nvar S_TAG_CLOSE = 7; //closed el<el />\n\nfunction XMLReader() {}\nXMLReader.prototype = {\n  parse: function parse(source, defaultNSMap, entityMap) {\n    var domBuilder = this.domBuilder;\n    domBuilder.startDocument();\n    _copy(defaultNSMap, defaultNSMap = {});\n    _parse(source, defaultNSMap, entityMap, domBuilder, this.errorHandler);\n    domBuilder.endDocument();\n  }\n};\nfunction _parse(source, defaultNSMapCopy, entityMap, domBuilder, errorHandler) {\n  function fixedFromCharCode(code) {\n    // String.prototype.fromCharCode does not supports\n    // > 2 bytes unicode chars directly\n    if (code > 0xffff) {\n      code -= 0x10000;\n      var surrogate1 = 0xd800 + (code >> 10),\n        surrogate2 = 0xdc00 + (code & 0x3ff);\n      return String.fromCharCode(surrogate1, surrogate2);\n    } else {\n      return String.fromCharCode(code);\n    }\n  }\n  function entityReplacer(a) {\n    var k = a.slice(1, -1);\n    if (k in entityMap) {\n      return entityMap[k];\n    } else if (k.charAt(0) === '#') {\n      return fixedFromCharCode(parseInt(k.substr(1).replace('x', '0x')));\n    } else {\n      errorHandler.error('entity not found:' + a);\n      return a;\n    }\n  }\n  function appendText(end) {\n    //has some bugs\n    if (end > start) {\n      var xt = source.substring(start, end).replace(/&#?\\w+;/g, entityReplacer);\n      locator && position(start);\n      domBuilder.characters(xt, 0, end - start);\n      start = end;\n    }\n  }\n  function position(p, m) {\n    while (p >= lineEnd && (m = linePattern.exec(source))) {\n      lineStart = m.index;\n      lineEnd = lineStart + m[0].length;\n      locator.lineNumber++;\n      //console.log('line++:',locator,startPos,endPos)\n    }\n    locator.columnNumber = p - lineStart + 1;\n  }\n  var lineStart = 0;\n  var lineEnd = 0;\n  var linePattern = /.*(?:\\r\\n?|\\n)|.*$/g;\n  var locator = domBuilder.locator;\n  var parseStack = [{\n    currentNSMap: defaultNSMapCopy\n  }];\n  var closeMap = {};\n  var start = 0;\n  while (true) {\n    try {\n      var tagStart = source.indexOf('<', start);\n      if (tagStart < 0) {\n        if (!source.substr(start).match(/^\\s*$/)) {\n          var doc = domBuilder.doc;\n          var text = doc.createTextNode(source.substr(start));\n          doc.appendChild(text);\n          domBuilder.currentElement = text;\n        }\n        return;\n      }\n      if (tagStart > start) {\n        appendText(tagStart);\n      }\n      switch (source.charAt(tagStart + 1)) {\n        case '/':\n          var end = source.indexOf('>', tagStart + 3);\n          var tagName = source.substring(tagStart + 2, end);\n          var config = parseStack.pop();\n          if (end < 0) {\n            tagName = source.substring(tagStart + 2).replace(/[\\s<].*/, '');\n            //console.error('#@@@@@@'+tagName)\n            errorHandler.error(\"end tag name: \" + tagName + ' is not complete:' + config.tagName);\n            end = tagStart + 1 + tagName.length;\n          } else if (tagName.match(/\\s</)) {\n            tagName = tagName.replace(/[\\s<].*/, '');\n            errorHandler.error(\"end tag name: \" + tagName + ' maybe not complete');\n            end = tagStart + 1 + tagName.length;\n          }\n          //console.error(parseStack.length,parseStack)\n          //console.error(config);\n          var localNSMap = config.localNSMap;\n          var endMatch = config.tagName == tagName;\n          var endIgnoreCaseMach = endMatch || config.tagName && config.tagName.toLowerCase() == tagName.toLowerCase();\n          if (endIgnoreCaseMach) {\n            domBuilder.endElement(config.uri, config.localName, tagName);\n            if (localNSMap) {\n              for (var prefix in localNSMap) {\n                domBuilder.endPrefixMapping(prefix);\n              }\n            }\n            if (!endMatch) {\n              errorHandler.fatalError(\"end tag name: \" + tagName + ' is not match the current start tagName:' + config.tagName);\n            }\n          } else {\n            parseStack.push(config);\n          }\n          end++;\n          break;\n        // end elment\n        case '?':\n          // <?...?>\n          locator && position(tagStart);\n          end = parseInstruction(source, tagStart, domBuilder);\n          break;\n        case '!':\n          // <!doctype,<![CDATA,<!--\n          locator && position(tagStart);\n          end = parseDCC(source, tagStart, domBuilder, errorHandler);\n          break;\n        default:\n          locator && position(tagStart);\n          var el = new ElementAttributes();\n          var currentNSMap = parseStack[parseStack.length - 1].currentNSMap;\n          //elStartEnd\n          var end = parseElementStartPart(source, tagStart, el, currentNSMap, entityReplacer, errorHandler);\n          var len = el.length;\n          if (!el.closed && fixSelfClosed(source, end, el.tagName, closeMap)) {\n            el.closed = true;\n            if (!entityMap.nbsp) {\n              errorHandler.warning('unclosed xml attribute');\n            }\n          }\n          if (locator && len) {\n            var locator2 = copyLocator(locator, {});\n            //try{//attribute position fixed\n            for (var i = 0; i < len; i++) {\n              var a = el[i];\n              position(a.offset);\n              a.locator = copyLocator(locator, {});\n            }\n            //}catch(e){console.error('@@@@@'+e)}\n            domBuilder.locator = locator2;\n            if (appendElement(el, domBuilder, currentNSMap)) {\n              parseStack.push(el);\n            }\n            domBuilder.locator = locator;\n          } else {\n            if (appendElement(el, domBuilder, currentNSMap)) {\n              parseStack.push(el);\n            }\n          }\n          if (el.uri === 'http://www.w3.org/1999/xhtml' && !el.closed) {\n            end = parseHtmlSpecialContent(source, end, el.tagName, entityReplacer, domBuilder);\n          } else {\n            end++;\n          }\n      }\n    } catch (e) {\n      errorHandler.error('element parse error: ' + e);\n      //errorHandler.error('element parse error: '+e);\n      end = -1;\n      //throw e;\n    }\n    if (end > start) {\n      start = end;\n    } else {\n      //TODO: 这里有可能sax回退，有位置错误风险\n      appendText(Math.max(tagStart, start) + 1);\n    }\n  }\n}\nfunction copyLocator(f, t) {\n  t.lineNumber = f.lineNumber;\n  t.columnNumber = f.columnNumber;\n  return t;\n}\n\n/**\r\n * @see #appendElement(source,elStartEnd,el,selfClosed,entityReplacer,domBuilder,parseStack);\r\n * @return end of the elementStartPart(end of elementEndPart for selfClosed el)\r\n */\nfunction parseElementStartPart(source, start, el, currentNSMap, entityReplacer, errorHandler) {\n  var attrName;\n  var value;\n  var p = ++start;\n  var s = S_TAG; //status\n  while (true) {\n    var c = source.charAt(p);\n    switch (c) {\n      case '=':\n        if (s === S_ATTR) {\n          //attrName\n          attrName = source.slice(start, p);\n          s = S_EQ;\n        } else if (s === S_ATTR_SPACE) {\n          s = S_EQ;\n        } else {\n          //fatalError: equal must after attrName or space after attrName\n          throw new Error('attribute equal must after attrName');\n        }\n        break;\n      case '\\'':\n      case '\"':\n        if (s === S_EQ || s === S_ATTR //|| s == S_ATTR_SPACE\n        ) {\n          //equal\n          if (s === S_ATTR) {\n            errorHandler.warning('attribute value must after \"=\"');\n            attrName = source.slice(start, p);\n          }\n          start = p + 1;\n          p = source.indexOf(c, start);\n          if (p > 0) {\n            value = source.slice(start, p).replace(/&#?\\w+;/g, entityReplacer);\n            el.add(attrName, value, start - 1);\n            s = S_ATTR_END;\n          } else {\n            //fatalError: no end quot match\n            throw new Error('attribute value no end \\'' + c + '\\' match');\n          }\n        } else if (s == S_ATTR_NOQUOT_VALUE) {\n          value = source.slice(start, p).replace(/&#?\\w+;/g, entityReplacer);\n          //console.log(attrName,value,start,p)\n          el.add(attrName, value, start);\n          //console.dir(el)\n          errorHandler.warning('attribute \"' + attrName + '\" missed start quot(' + c + ')!!');\n          start = p + 1;\n          s = S_ATTR_END;\n        } else {\n          //fatalError: no equal before\n          throw new Error('attribute value must after \"=\"');\n        }\n        break;\n      case '/':\n        switch (s) {\n          case S_TAG:\n            el.setTagName(source.slice(start, p));\n          case S_ATTR_END:\n          case S_TAG_SPACE:\n          case S_TAG_CLOSE:\n            s = S_TAG_CLOSE;\n            el.closed = true;\n          case S_ATTR_NOQUOT_VALUE:\n          case S_ATTR:\n          case S_ATTR_SPACE:\n            break;\n          //case S_EQ:\n          default:\n            throw new Error(\"attribute invalid close char('/')\");\n        }\n        break;\n      case '':\n        //end document\n        //throw new Error('unexpected end of input')\n        errorHandler.error('unexpected end of input');\n        if (s == S_TAG) {\n          el.setTagName(source.slice(start, p));\n        }\n        return p;\n      case '>':\n        switch (s) {\n          case S_TAG:\n            el.setTagName(source.slice(start, p));\n          case S_ATTR_END:\n          case S_TAG_SPACE:\n          case S_TAG_CLOSE:\n            break;\n          //normal\n          case S_ATTR_NOQUOT_VALUE: //Compatible state\n          case S_ATTR:\n            value = source.slice(start, p);\n            if (value.slice(-1) === '/') {\n              el.closed = true;\n              value = value.slice(0, -1);\n            }\n          case S_ATTR_SPACE:\n            if (s === S_ATTR_SPACE) {\n              value = attrName;\n            }\n            if (s == S_ATTR_NOQUOT_VALUE) {\n              errorHandler.warning('attribute \"' + value + '\" missed quot(\")!!');\n              el.add(attrName, value.replace(/&#?\\w+;/g, entityReplacer), start);\n            } else {\n              if (currentNSMap[''] !== 'http://www.w3.org/1999/xhtml' || !value.match(/^(?:disabled|checked|selected)$/i)) {\n                errorHandler.warning('attribute \"' + value + '\" missed value!! \"' + value + '\" instead!!');\n              }\n              el.add(value, value, start);\n            }\n            break;\n          case S_EQ:\n            throw new Error('attribute value missed!!');\n        }\n        //\t\t\tconsole.log(tagName,tagNamePattern,tagNamePattern.test(tagName))\n        return p;\n      /*xml space '\\x20' | #x9 | #xD | #xA; */\n      case \"\\x80\":\n        c = ' ';\n      default:\n        if (c <= ' ') {\n          //space\n          switch (s) {\n            case S_TAG:\n              el.setTagName(source.slice(start, p)); //tagName\n              s = S_TAG_SPACE;\n              break;\n            case S_ATTR:\n              attrName = source.slice(start, p);\n              s = S_ATTR_SPACE;\n              break;\n            case S_ATTR_NOQUOT_VALUE:\n              var value = source.slice(start, p).replace(/&#?\\w+;/g, entityReplacer);\n              errorHandler.warning('attribute \"' + value + '\" missed quot(\")!!');\n              el.add(attrName, value, start);\n            case S_ATTR_END:\n              s = S_TAG_SPACE;\n              break;\n            //case S_TAG_SPACE:\n            //case S_EQ:\n            //case S_ATTR_SPACE:\n            //\tvoid();break;\n            //case S_TAG_CLOSE:\n            //ignore warning\n          }\n        } else {\n          //not space\n          //S_TAG,\tS_ATTR,\tS_EQ,\tS_ATTR_NOQUOT_VALUE\n          //S_ATTR_SPACE,\tS_ATTR_END,\tS_TAG_SPACE, S_TAG_CLOSE\n          switch (s) {\n            //case S_TAG:void();break;\n            //case S_ATTR:void();break;\n            //case S_ATTR_NOQUOT_VALUE:void();break;\n            case S_ATTR_SPACE:\n              var tagName = el.tagName;\n              if (currentNSMap[''] !== 'http://www.w3.org/1999/xhtml' || !attrName.match(/^(?:disabled|checked|selected)$/i)) {\n                errorHandler.warning('attribute \"' + attrName + '\" missed value!! \"' + attrName + '\" instead2!!');\n              }\n              el.add(attrName, attrName, start);\n              start = p;\n              s = S_ATTR;\n              break;\n            case S_ATTR_END:\n              errorHandler.warning('attribute space is required\"' + attrName + '\"!!');\n            case S_TAG_SPACE:\n              s = S_ATTR;\n              start = p;\n              break;\n            case S_EQ:\n              s = S_ATTR_NOQUOT_VALUE;\n              start = p;\n              break;\n            case S_TAG_CLOSE:\n              throw new Error(\"elements closed character '/' and '>' must be connected to\");\n          }\n        }\n    } //end outer switch\n    //console.log('p++',p)\n    p++;\n  }\n}\n/**\r\n * @return true if has new namespace define\r\n */\nfunction appendElement(el, domBuilder, currentNSMap) {\n  var tagName = el.tagName;\n  var localNSMap = null;\n  //var currentNSMap = parseStack[parseStack.length-1].currentNSMap;\n  var i = el.length;\n  while (i--) {\n    var a = el[i];\n    var qName = a.qName;\n    var value = a.value;\n    var nsp = qName.indexOf(':');\n    if (nsp > 0) {\n      var prefix = a.prefix = qName.slice(0, nsp);\n      var localName = qName.slice(nsp + 1);\n      var nsPrefix = prefix === 'xmlns' && localName;\n    } else {\n      localName = qName;\n      prefix = null;\n      nsPrefix = qName === 'xmlns' && '';\n    }\n    //can not set prefix,because prefix !== ''\n    a.localName = localName;\n    //prefix == null for no ns prefix attribute \n    if (nsPrefix !== false) {\n      //hack!!\n      if (localNSMap == null) {\n        localNSMap = {};\n        //console.log(currentNSMap,0)\n        _copy(currentNSMap, currentNSMap = {});\n        //console.log(currentNSMap,1)\n      }\n      currentNSMap[nsPrefix] = localNSMap[nsPrefix] = value;\n      a.uri = 'http://www.w3.org/2000/xmlns/';\n      domBuilder.startPrefixMapping(nsPrefix, value);\n    }\n  }\n  var i = el.length;\n  while (i--) {\n    a = el[i];\n    var prefix = a.prefix;\n    if (prefix) {\n      //no prefix attribute has no namespace\n      if (prefix === 'xml') {\n        a.uri = 'http://www.w3.org/XML/1998/namespace';\n      }\n      if (prefix !== 'xmlns') {\n        a.uri = currentNSMap[prefix || ''];\n\n        //{console.log('###'+a.qName,domBuilder.locator.systemId+'',currentNSMap,a.uri)}\n      }\n    }\n  }\n  var nsp = tagName.indexOf(':');\n  if (nsp > 0) {\n    prefix = el.prefix = tagName.slice(0, nsp);\n    localName = el.localName = tagName.slice(nsp + 1);\n  } else {\n    prefix = null; //important!!\n    localName = el.localName = tagName;\n  }\n  //no prefix element has default namespace\n  var ns = el.uri = currentNSMap[prefix || ''];\n  domBuilder.startElement(ns, localName, tagName, el);\n  //endPrefixMapping and startPrefixMapping have not any help for dom builder\n  //localNSMap = null\n  if (el.closed) {\n    domBuilder.endElement(ns, localName, tagName);\n    if (localNSMap) {\n      for (prefix in localNSMap) {\n        domBuilder.endPrefixMapping(prefix);\n      }\n    }\n  } else {\n    el.currentNSMap = currentNSMap;\n    el.localNSMap = localNSMap;\n    //parseStack.push(el);\n    return true;\n  }\n}\nfunction parseHtmlSpecialContent(source, elStartEnd, tagName, entityReplacer, domBuilder) {\n  if (/^(?:script|textarea)$/i.test(tagName)) {\n    var elEndStart = source.indexOf('</' + tagName + '>', elStartEnd);\n    var text = source.substring(elStartEnd + 1, elEndStart);\n    if (/[&<]/.test(text)) {\n      if (/^script$/i.test(tagName)) {\n        //if(!/\\]\\]>/.test(text)){\n        //lexHandler.startCDATA();\n        domBuilder.characters(text, 0, text.length);\n        //lexHandler.endCDATA();\n        return elEndStart;\n        //}\n      } //}else{//text area\n      text = text.replace(/&#?\\w+;/g, entityReplacer);\n      domBuilder.characters(text, 0, text.length);\n      return elEndStart;\n      //}\n    }\n  }\n  return elStartEnd + 1;\n}\nfunction fixSelfClosed(source, elStartEnd, tagName, closeMap) {\n  //if(tagName in closeMap){\n  var pos = closeMap[tagName];\n  if (pos == null) {\n    //console.log(tagName)\n    pos = source.lastIndexOf('</' + tagName + '>');\n    if (pos < elStartEnd) {\n      //忘记闭合\n      pos = source.lastIndexOf('</' + tagName);\n    }\n    closeMap[tagName] = pos;\n  }\n  return pos < elStartEnd;\n  //} \n}\nfunction _copy(source, target) {\n  for (var n in source) {\n    target[n] = source[n];\n  }\n}\nfunction parseDCC(source, start, domBuilder, errorHandler) {\n  //sure start with '<!'\n  var next = source.charAt(start + 2);\n  switch (next) {\n    case '-':\n      if (source.charAt(start + 3) === '-') {\n        var end = source.indexOf('-->', start + 4);\n        //append comment source.substring(4,end)//<!--\n        if (end > start) {\n          domBuilder.comment(source, start + 4, end - start - 4);\n          return end + 3;\n        } else {\n          errorHandler.error(\"Unclosed comment\");\n          return -1;\n        }\n      } else {\n        //error\n        return -1;\n      }\n    default:\n      if (source.substr(start + 3, 6) == 'CDATA[') {\n        var end = source.indexOf(']]>', start + 9);\n        domBuilder.startCDATA();\n        domBuilder.characters(source, start + 9, end - start - 9);\n        domBuilder.endCDATA();\n        return end + 3;\n      }\n      //<!DOCTYPE\n      //startDTD(java.lang.String name, java.lang.String publicId, java.lang.String systemId) \n      var matchs = split(source, start);\n      var len = matchs.length;\n      if (len > 1 && /!doctype/i.test(matchs[0][0])) {\n        var name = matchs[1][0];\n        var pubid = len > 3 && /^public$/i.test(matchs[2][0]) && matchs[3][0];\n        var sysid = len > 4 && matchs[4][0];\n        var lastMatch = matchs[len - 1];\n        domBuilder.startDTD(name, pubid && pubid.replace(/^(['\"])(.*?)\\1$/, '$2'), sysid && sysid.replace(/^(['\"])(.*?)\\1$/, '$2'));\n        domBuilder.endDTD();\n        return lastMatch.index + lastMatch[0].length;\n      }\n  }\n  return -1;\n}\nfunction parseInstruction(source, start, domBuilder) {\n  var end = source.indexOf('?>', start);\n  if (end) {\n    var match = source.substring(start, end).match(/^<\\?(\\S*)\\s*([\\s\\S]*?)\\s*$/);\n    if (match) {\n      var len = match[0].length;\n      domBuilder.processingInstruction(match[1], match[2]);\n      return end + 2;\n    } else {\n      //error\n      return -1;\n    }\n  }\n  return -1;\n}\n\n/**\r\n * @param source\r\n */\nfunction ElementAttributes(source) {}\nElementAttributes.prototype = {\n  setTagName: function setTagName(tagName) {\n    if (!tagNamePattern.test(tagName)) {\n      throw new Error('invalid tagName:' + tagName);\n    }\n    this.tagName = tagName;\n  },\n  add: function add(qName, value, offset) {\n    if (!tagNamePattern.test(qName)) {\n      throw new Error('invalid attribute:' + qName);\n    }\n    this[this.length++] = {\n      qName: qName,\n      value: value,\n      offset: offset\n    };\n  },\n  length: 0,\n  getLocalName: function getLocalName(i) {\n    return this[i].localName;\n  },\n  getLocator: function getLocator(i) {\n    return this[i].locator;\n  },\n  getQName: function getQName(i) {\n    return this[i].qName;\n  },\n  getURI: function getURI(i) {\n    return this[i].uri;\n  },\n  getValue: function getValue(i) {\n    return this[i].value;\n  }\n  //\t,getIndex:function(uri, localName)){\n  //\t\tif(localName){\n  //\t\t\t\n  //\t\t}else{\n  //\t\t\tvar qName = uri\n  //\t\t}\n  //\t},\n  //\tgetValue:function(){return this.getValue(this.getIndex.apply(this,arguments))},\n  //\tgetType:function(uri,localName){}\n  //\tgetType:function(i){},\n};\nfunction _set_proto_(thiz, parent) {\n  thiz.__proto__ = parent;\n  return thiz;\n}\nif (!(_set_proto_({}, _set_proto_.prototype) instanceof _set_proto_)) {\n  _set_proto_ = function _set_proto_(thiz, parent) {\n    function p() {}\n    ;\n    p.prototype = parent;\n    p = new p();\n    for (parent in thiz) {\n      p[parent] = thiz[parent];\n    }\n    return p;\n  };\n}\nfunction split(source, start) {\n  var match;\n  var buf = [];\n  var reg = /'[^']+'|\"[^\"]+\"|[^\\s<>\\/=]+=?|(\\/?\\s*>|<)/g;\n  reg.lastIndex = start;\n  reg.exec(source); //skip <\n  while (match = reg.exec(source)) {\n    buf.push(match);\n    if (match[1]) return buf;\n  }\n}\nexports.XMLReader = XMLReader;", "map": {"version": 3, "names": ["nameStartChar", "nameChar", "RegExp", "source", "slice", "tagNamePattern", "S_TAG", "S_ATTR", "S_ATTR_SPACE", "S_EQ", "S_ATTR_NOQUOT_VALUE", "S_ATTR_END", "S_TAG_SPACE", "S_TAG_CLOSE", "XMLReader", "prototype", "parse", "defaultNSMap", "entityMap", "domBuilder", "startDocument", "_copy", "<PERSON><PERSON><PERSON><PERSON>", "endDocument", "defaultNSMapCopy", "fixedFromCharCode", "code", "surrogate1", "surrogate2", "String", "fromCharCode", "entityReplacer", "a", "k", "char<PERSON>t", "parseInt", "substr", "replace", "error", "appendText", "end", "start", "xt", "substring", "locator", "position", "characters", "p", "m", "lineEnd", "linePattern", "exec", "lineStart", "index", "length", "lineNumber", "columnNumber", "parseStack", "currentNSMap", "closeMap", "tagStart", "indexOf", "match", "doc", "text", "createTextNode", "append<PERSON><PERSON><PERSON>", "currentElement", "tagName", "config", "pop", "localNSMap", "endMatch", "endIgnoreCaseMach", "toLowerCase", "endElement", "uri", "localName", "prefix", "endPrefixMapping", "fatalError", "push", "parseInstruction", "parseDCC", "el", "ElementAttributes", "parseElementStartPart", "len", "closed", "fixSelfClosed", "nbsp", "warning", "locator2", "copyLocator", "i", "offset", "appendElement", "parseHtmlSpecialContent", "e", "Math", "max", "f", "t", "attrName", "value", "s", "c", "Error", "add", "setTagName", "qName", "nsp", "nsPrefix", "startPrefixMapping", "ns", "startElement", "elStartEnd", "test", "elEndStart", "pos", "lastIndexOf", "target", "n", "next", "comment", "startCDATA", "endCDATA", "matchs", "split", "name", "pubid", "sysid", "lastMatch", "startDTD", "endDTD", "processingInstruction", "getLocalName", "getLocator", "getQName", "getURI", "getValue", "_set_proto_", "thiz", "parent", "__proto__", "buf", "reg", "lastIndex", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/xmldom@0.1.31/node_modules/xmldom/sax.js"], "sourcesContent": ["//[4]   \tNameStartChar\t   ::=   \t\":\" | [A-Z] | \"_\" | [a-z] | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x2FF] | [#x370-#x37D] | [#x37F-#x1FFF] | [#x200C-#x200D] | [#x2070-#x218F] | [#x2C00-#x2FEF] | [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\r\n//[4a]   \tNameChar\t   ::=   \tNameStartChar | \"-\" | \".\" | [0-9] | #xB7 | [#x0300-#x036F] | [#x203F-#x2040]\r\n//[5]   \tName\t   ::=   \tNameStartChar (NameChar)*\r\nvar nameStartChar = /[A-Z_a-z\\xC0-\\xD6\\xD8-\\xF6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]///\\u10000-\\uEFFFF\r\nvar nameChar = new RegExp(\"[\\\\-\\\\.0-9\"+nameStartChar.source.slice(1,-1)+\"\\\\u00B7\\\\u0300-\\\\u036F\\\\u203F-\\\\u2040]\");\r\nvar tagNamePattern = new RegExp('^'+nameStartChar.source+nameChar.source+'*(?:\\:'+nameStartChar.source+nameChar.source+'*)?$');\r\n//var tagNamePattern = /^[a-zA-Z_][\\w\\-\\.]*(?:\\:[a-zA-Z_][\\w\\-\\.]*)?$/\r\n//var handlers = 'resolveEntity,getExternalSubset,characters,endDocument,endElement,endPrefixMapping,ignorableWhitespace,processingInstruction,setDocumentLocator,skippedEntity,startDocument,startElement,startPrefixMapping,notationDecl,unparsedEntityDecl,error,fatalError,warning,attributeDecl,elementDecl,externalEntityDecl,internalEntityDecl,comment,endCDATA,endDTD,endEntity,startCDATA,startDTD,startEntity'.split(',')\r\n\r\n//S_TAG,\tS_ATTR,\tS_EQ,\tS_ATTR_NOQUOT_VALUE\r\n//S_ATTR_SPACE,\tS_ATTR_END,\tS_TAG_SPACE, S_TAG_CLOSE\r\nvar S_TAG = 0;//tag name offerring\r\nvar S_ATTR = 1;//attr name offerring \r\nvar S_ATTR_SPACE=2;//attr name end and space offer\r\nvar S_EQ = 3;//=space?\r\nvar S_ATTR_NOQUOT_VALUE = 4;//attr value(no quot value only)\r\nvar S_ATTR_END = 5;//attr value end and no space(quot end)\r\nvar S_TAG_SPACE = 6;//(attr value end || tag end ) && (space offer)\r\nvar S_TAG_CLOSE = 7;//closed el<el />\r\n\r\nfunction XMLReader(){\r\n\t\r\n}\r\n\r\nXMLReader.prototype = {\r\n\tparse:function(source,defaultNSMap,entityMap){\r\n\t\tvar domBuilder = this.domBuilder;\r\n\t\tdomBuilder.startDocument();\r\n\t\t_copy(defaultNSMap ,defaultNSMap = {})\r\n\t\tparse(source,defaultNSMap,entityMap,\r\n\t\t\t\tdomBuilder,this.errorHandler);\r\n\t\tdomBuilder.endDocument();\r\n\t}\r\n}\r\nfunction parse(source,defaultNSMapCopy,entityMap,domBuilder,errorHandler){\r\n\tfunction fixedFromCharCode(code) {\r\n\t\t// String.prototype.fromCharCode does not supports\r\n\t\t// > 2 bytes unicode chars directly\r\n\t\tif (code > 0xffff) {\r\n\t\t\tcode -= 0x10000;\r\n\t\t\tvar surrogate1 = 0xd800 + (code >> 10)\r\n\t\t\t\t, surrogate2 = 0xdc00 + (code & 0x3ff);\r\n\r\n\t\t\treturn String.fromCharCode(surrogate1, surrogate2);\r\n\t\t} else {\r\n\t\t\treturn String.fromCharCode(code);\r\n\t\t}\r\n\t}\r\n\tfunction entityReplacer(a){\r\n\t\tvar k = a.slice(1,-1);\r\n\t\tif(k in entityMap){\r\n\t\t\treturn entityMap[k]; \r\n\t\t}else if(k.charAt(0) === '#'){\r\n\t\t\treturn fixedFromCharCode(parseInt(k.substr(1).replace('x','0x')))\r\n\t\t}else{\r\n\t\t\terrorHandler.error('entity not found:'+a);\r\n\t\t\treturn a;\r\n\t\t}\r\n\t}\r\n\tfunction appendText(end){//has some bugs\r\n\t\tif(end>start){\r\n\t\t\tvar xt = source.substring(start,end).replace(/&#?\\w+;/g,entityReplacer);\r\n\t\t\tlocator&&position(start);\r\n\t\t\tdomBuilder.characters(xt,0,end-start);\r\n\t\t\tstart = end\r\n\t\t}\r\n\t}\r\n\tfunction position(p,m){\r\n\t\twhile(p>=lineEnd && (m = linePattern.exec(source))){\r\n\t\t\tlineStart = m.index;\r\n\t\t\tlineEnd = lineStart + m[0].length;\r\n\t\t\tlocator.lineNumber++;\r\n\t\t\t//console.log('line++:',locator,startPos,endPos)\r\n\t\t}\r\n\t\tlocator.columnNumber = p-lineStart+1;\r\n\t}\r\n\tvar lineStart = 0;\r\n\tvar lineEnd = 0;\r\n\tvar linePattern = /.*(?:\\r\\n?|\\n)|.*$/g\r\n\tvar locator = domBuilder.locator;\r\n\t\r\n\tvar parseStack = [{currentNSMap:defaultNSMapCopy}]\r\n\tvar closeMap = {};\r\n\tvar start = 0;\r\n\twhile(true){\r\n\t\ttry{\r\n\t\t\tvar tagStart = source.indexOf('<',start);\r\n\t\t\tif(tagStart<0){\r\n\t\t\t\tif(!source.substr(start).match(/^\\s*$/)){\r\n\t\t\t\t\tvar doc = domBuilder.doc;\r\n\t    \t\t\tvar text = doc.createTextNode(source.substr(start));\r\n\t    \t\t\tdoc.appendChild(text);\r\n\t    \t\t\tdomBuilder.currentElement = text;\r\n\t\t\t\t}\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\tif(tagStart>start){\r\n\t\t\t\tappendText(tagStart);\r\n\t\t\t}\r\n\t\t\tswitch(source.charAt(tagStart+1)){\r\n\t\t\tcase '/':\r\n\t\t\t\tvar end = source.indexOf('>',tagStart+3);\r\n\t\t\t\tvar tagName = source.substring(tagStart+2,end);\r\n\t\t\t\tvar config = parseStack.pop();\r\n\t\t\t\tif(end<0){\r\n\t\t\t\t\t\r\n\t        \t\ttagName = source.substring(tagStart+2).replace(/[\\s<].*/,'');\r\n\t        \t\t//console.error('#@@@@@@'+tagName)\r\n\t        \t\terrorHandler.error(\"end tag name: \"+tagName+' is not complete:'+config.tagName);\r\n\t        \t\tend = tagStart+1+tagName.length;\r\n\t        \t}else if(tagName.match(/\\s</)){\r\n\t        \t\ttagName = tagName.replace(/[\\s<].*/,'');\r\n\t        \t\terrorHandler.error(\"end tag name: \"+tagName+' maybe not complete');\r\n\t        \t\tend = tagStart+1+tagName.length;\r\n\t\t\t\t}\r\n\t\t\t\t//console.error(parseStack.length,parseStack)\r\n\t\t\t\t//console.error(config);\r\n\t\t\t\tvar localNSMap = config.localNSMap;\r\n\t\t\t\tvar endMatch = config.tagName == tagName;\r\n\t\t\t\tvar endIgnoreCaseMach = endMatch || config.tagName&&config.tagName.toLowerCase() == tagName.toLowerCase()\r\n\t\t        if(endIgnoreCaseMach){\r\n\t\t        \tdomBuilder.endElement(config.uri,config.localName,tagName);\r\n\t\t\t\t\tif(localNSMap){\r\n\t\t\t\t\t\tfor(var prefix in localNSMap){\r\n\t\t\t\t\t\t\tdomBuilder.endPrefixMapping(prefix) ;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(!endMatch){\r\n\t\t            \terrorHandler.fatalError(\"end tag name: \"+tagName+' is not match the current start tagName:'+config.tagName );\r\n\t\t\t\t\t}\r\n\t\t        }else{\r\n\t\t        \tparseStack.push(config)\r\n\t\t        }\r\n\t\t\t\t\r\n\t\t\t\tend++;\r\n\t\t\t\tbreak;\r\n\t\t\t\t// end elment\r\n\t\t\tcase '?':// <?...?>\r\n\t\t\t\tlocator&&position(tagStart);\r\n\t\t\t\tend = parseInstruction(source,tagStart,domBuilder);\r\n\t\t\t\tbreak;\r\n\t\t\tcase '!':// <!doctype,<![CDATA,<!--\r\n\t\t\t\tlocator&&position(tagStart);\r\n\t\t\t\tend = parseDCC(source,tagStart,domBuilder,errorHandler);\r\n\t\t\t\tbreak;\r\n\t\t\tdefault:\r\n\t\t\t\tlocator&&position(tagStart);\r\n\t\t\t\tvar el = new ElementAttributes();\r\n\t\t\t\tvar currentNSMap = parseStack[parseStack.length-1].currentNSMap;\r\n\t\t\t\t//elStartEnd\r\n\t\t\t\tvar end = parseElementStartPart(source,tagStart,el,currentNSMap,entityReplacer,errorHandler);\r\n\t\t\t\tvar len = el.length;\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\tif(!el.closed && fixSelfClosed(source,end,el.tagName,closeMap)){\r\n\t\t\t\t\tel.closed = true;\r\n\t\t\t\t\tif(!entityMap.nbsp){\r\n\t\t\t\t\t\terrorHandler.warning('unclosed xml attribute');\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif(locator && len){\r\n\t\t\t\t\tvar locator2 = copyLocator(locator,{});\r\n\t\t\t\t\t//try{//attribute position fixed\r\n\t\t\t\t\tfor(var i = 0;i<len;i++){\r\n\t\t\t\t\t\tvar a = el[i];\r\n\t\t\t\t\t\tposition(a.offset);\r\n\t\t\t\t\t\ta.locator = copyLocator(locator,{});\r\n\t\t\t\t\t}\r\n\t\t\t\t\t//}catch(e){console.error('@@@@@'+e)}\r\n\t\t\t\t\tdomBuilder.locator = locator2\r\n\t\t\t\t\tif(appendElement(el,domBuilder,currentNSMap)){\r\n\t\t\t\t\t\tparseStack.push(el)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tdomBuilder.locator = locator;\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif(appendElement(el,domBuilder,currentNSMap)){\r\n\t\t\t\t\t\tparseStack.push(el)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\t\r\n\t\t\t\tif(el.uri === 'http://www.w3.org/1999/xhtml' && !el.closed){\r\n\t\t\t\t\tend = parseHtmlSpecialContent(source,end,el.tagName,entityReplacer,domBuilder)\r\n\t\t\t\t}else{\r\n\t\t\t\t\tend++;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}catch(e){\r\n\t\t\terrorHandler.error('element parse error: '+e)\r\n\t\t\t//errorHandler.error('element parse error: '+e);\r\n\t\t\tend = -1;\r\n\t\t\t//throw e;\r\n\t\t}\r\n\t\tif(end>start){\r\n\t\t\tstart = end;\r\n\t\t}else{\r\n\t\t\t//TODO: 这里有可能sax回退，有位置错误风险\r\n\t\t\tappendText(Math.max(tagStart,start)+1);\r\n\t\t}\r\n\t}\r\n}\r\nfunction copyLocator(f,t){\r\n\tt.lineNumber = f.lineNumber;\r\n\tt.columnNumber = f.columnNumber;\r\n\treturn t;\r\n}\r\n\r\n/**\r\n * @see #appendElement(source,elStartEnd,el,selfClosed,entityReplacer,domBuilder,parseStack);\r\n * @return end of the elementStartPart(end of elementEndPart for selfClosed el)\r\n */\r\nfunction parseElementStartPart(source,start,el,currentNSMap,entityReplacer,errorHandler){\r\n\tvar attrName;\r\n\tvar value;\r\n\tvar p = ++start;\r\n\tvar s = S_TAG;//status\r\n\twhile(true){\r\n\t\tvar c = source.charAt(p);\r\n\t\tswitch(c){\r\n\t\tcase '=':\r\n\t\t\tif(s === S_ATTR){//attrName\r\n\t\t\t\tattrName = source.slice(start,p);\r\n\t\t\t\ts = S_EQ;\r\n\t\t\t}else if(s === S_ATTR_SPACE){\r\n\t\t\t\ts = S_EQ;\r\n\t\t\t}else{\r\n\t\t\t\t//fatalError: equal must after attrName or space after attrName\r\n\t\t\t\tthrow new Error('attribute equal must after attrName');\r\n\t\t\t}\r\n\t\t\tbreak;\r\n\t\tcase '\\'':\r\n\t\tcase '\"':\r\n\t\t\tif(s === S_EQ || s === S_ATTR //|| s == S_ATTR_SPACE\r\n\t\t\t\t){//equal\r\n\t\t\t\tif(s === S_ATTR){\r\n\t\t\t\t\terrorHandler.warning('attribute value must after \"=\"')\r\n\t\t\t\t\tattrName = source.slice(start,p)\r\n\t\t\t\t}\r\n\t\t\t\tstart = p+1;\r\n\t\t\t\tp = source.indexOf(c,start)\r\n\t\t\t\tif(p>0){\r\n\t\t\t\t\tvalue = source.slice(start,p).replace(/&#?\\w+;/g,entityReplacer);\r\n\t\t\t\t\tel.add(attrName,value,start-1);\r\n\t\t\t\t\ts = S_ATTR_END;\r\n\t\t\t\t}else{\r\n\t\t\t\t\t//fatalError: no end quot match\r\n\t\t\t\t\tthrow new Error('attribute value no end \\''+c+'\\' match');\r\n\t\t\t\t}\r\n\t\t\t}else if(s == S_ATTR_NOQUOT_VALUE){\r\n\t\t\t\tvalue = source.slice(start,p).replace(/&#?\\w+;/g,entityReplacer);\r\n\t\t\t\t//console.log(attrName,value,start,p)\r\n\t\t\t\tel.add(attrName,value,start);\r\n\t\t\t\t//console.dir(el)\r\n\t\t\t\terrorHandler.warning('attribute \"'+attrName+'\" missed start quot('+c+')!!');\r\n\t\t\t\tstart = p+1;\r\n\t\t\t\ts = S_ATTR_END\r\n\t\t\t}else{\r\n\t\t\t\t//fatalError: no equal before\r\n\t\t\t\tthrow new Error('attribute value must after \"=\"');\r\n\t\t\t}\r\n\t\t\tbreak;\r\n\t\tcase '/':\r\n\t\t\tswitch(s){\r\n\t\t\tcase S_TAG:\r\n\t\t\t\tel.setTagName(source.slice(start,p));\r\n\t\t\tcase S_ATTR_END:\r\n\t\t\tcase S_TAG_SPACE:\r\n\t\t\tcase S_TAG_CLOSE:\r\n\t\t\t\ts =S_TAG_CLOSE;\r\n\t\t\t\tel.closed = true;\r\n\t\t\tcase S_ATTR_NOQUOT_VALUE:\r\n\t\t\tcase S_ATTR:\r\n\t\t\tcase S_ATTR_SPACE:\r\n\t\t\t\tbreak;\r\n\t\t\t//case S_EQ:\r\n\t\t\tdefault:\r\n\t\t\t\tthrow new Error(\"attribute invalid close char('/')\")\r\n\t\t\t}\r\n\t\t\tbreak;\r\n\t\tcase ''://end document\r\n\t\t\t//throw new Error('unexpected end of input')\r\n\t\t\terrorHandler.error('unexpected end of input');\r\n\t\t\tif(s == S_TAG){\r\n\t\t\t\tel.setTagName(source.slice(start,p));\r\n\t\t\t}\r\n\t\t\treturn p;\r\n\t\tcase '>':\r\n\t\t\tswitch(s){\r\n\t\t\tcase S_TAG:\r\n\t\t\t\tel.setTagName(source.slice(start,p));\r\n\t\t\tcase S_ATTR_END:\r\n\t\t\tcase S_TAG_SPACE:\r\n\t\t\tcase S_TAG_CLOSE:\r\n\t\t\t\tbreak;//normal\r\n\t\t\tcase S_ATTR_NOQUOT_VALUE://Compatible state\r\n\t\t\tcase S_ATTR:\r\n\t\t\t\tvalue = source.slice(start,p);\r\n\t\t\t\tif(value.slice(-1) === '/'){\r\n\t\t\t\t\tel.closed  = true;\r\n\t\t\t\t\tvalue = value.slice(0,-1)\r\n\t\t\t\t}\r\n\t\t\tcase S_ATTR_SPACE:\r\n\t\t\t\tif(s === S_ATTR_SPACE){\r\n\t\t\t\t\tvalue = attrName;\r\n\t\t\t\t}\r\n\t\t\t\tif(s == S_ATTR_NOQUOT_VALUE){\r\n\t\t\t\t\terrorHandler.warning('attribute \"'+value+'\" missed quot(\")!!');\r\n\t\t\t\t\tel.add(attrName,value.replace(/&#?\\w+;/g,entityReplacer),start)\r\n\t\t\t\t}else{\r\n\t\t\t\t\tif(currentNSMap[''] !== 'http://www.w3.org/1999/xhtml' || !value.match(/^(?:disabled|checked|selected)$/i)){\r\n\t\t\t\t\t\terrorHandler.warning('attribute \"'+value+'\" missed value!! \"'+value+'\" instead!!')\r\n\t\t\t\t\t}\r\n\t\t\t\t\tel.add(value,value,start)\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase S_EQ:\r\n\t\t\t\tthrow new Error('attribute value missed!!');\r\n\t\t\t}\r\n//\t\t\tconsole.log(tagName,tagNamePattern,tagNamePattern.test(tagName))\r\n\t\t\treturn p;\r\n\t\t/*xml space '\\x20' | #x9 | #xD | #xA; */\r\n\t\tcase '\\u0080':\r\n\t\t\tc = ' ';\r\n\t\tdefault:\r\n\t\t\tif(c<= ' '){//space\r\n\t\t\t\tswitch(s){\r\n\t\t\t\tcase S_TAG:\r\n\t\t\t\t\tel.setTagName(source.slice(start,p));//tagName\r\n\t\t\t\t\ts = S_TAG_SPACE;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase S_ATTR:\r\n\t\t\t\t\tattrName = source.slice(start,p)\r\n\t\t\t\t\ts = S_ATTR_SPACE;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase S_ATTR_NOQUOT_VALUE:\r\n\t\t\t\t\tvar value = source.slice(start,p).replace(/&#?\\w+;/g,entityReplacer);\r\n\t\t\t\t\terrorHandler.warning('attribute \"'+value+'\" missed quot(\")!!');\r\n\t\t\t\t\tel.add(attrName,value,start)\r\n\t\t\t\tcase S_ATTR_END:\r\n\t\t\t\t\ts = S_TAG_SPACE;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\t//case S_TAG_SPACE:\r\n\t\t\t\t//case S_EQ:\r\n\t\t\t\t//case S_ATTR_SPACE:\r\n\t\t\t\t//\tvoid();break;\r\n\t\t\t\t//case S_TAG_CLOSE:\r\n\t\t\t\t\t//ignore warning\r\n\t\t\t\t}\r\n\t\t\t}else{//not space\r\n//S_TAG,\tS_ATTR,\tS_EQ,\tS_ATTR_NOQUOT_VALUE\r\n//S_ATTR_SPACE,\tS_ATTR_END,\tS_TAG_SPACE, S_TAG_CLOSE\r\n\t\t\t\tswitch(s){\r\n\t\t\t\t//case S_TAG:void();break;\r\n\t\t\t\t//case S_ATTR:void();break;\r\n\t\t\t\t//case S_ATTR_NOQUOT_VALUE:void();break;\r\n\t\t\t\tcase S_ATTR_SPACE:\r\n\t\t\t\t\tvar tagName =  el.tagName;\r\n\t\t\t\t\tif(currentNSMap[''] !== 'http://www.w3.org/1999/xhtml' || !attrName.match(/^(?:disabled|checked|selected)$/i)){\r\n\t\t\t\t\t\terrorHandler.warning('attribute \"'+attrName+'\" missed value!! \"'+attrName+'\" instead2!!')\r\n\t\t\t\t\t}\r\n\t\t\t\t\tel.add(attrName,attrName,start);\r\n\t\t\t\t\tstart = p;\r\n\t\t\t\t\ts = S_ATTR;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase S_ATTR_END:\r\n\t\t\t\t\terrorHandler.warning('attribute space is required\"'+attrName+'\"!!')\r\n\t\t\t\tcase S_TAG_SPACE:\r\n\t\t\t\t\ts = S_ATTR;\r\n\t\t\t\t\tstart = p;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase S_EQ:\r\n\t\t\t\t\ts = S_ATTR_NOQUOT_VALUE;\r\n\t\t\t\t\tstart = p;\r\n\t\t\t\t\tbreak;\r\n\t\t\t\tcase S_TAG_CLOSE:\r\n\t\t\t\t\tthrow new Error(\"elements closed character '/' and '>' must be connected to\");\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}//end outer switch\r\n\t\t//console.log('p++',p)\r\n\t\tp++;\r\n\t}\r\n}\r\n/**\r\n * @return true if has new namespace define\r\n */\r\nfunction appendElement(el,domBuilder,currentNSMap){\r\n\tvar tagName = el.tagName;\r\n\tvar localNSMap = null;\r\n\t//var currentNSMap = parseStack[parseStack.length-1].currentNSMap;\r\n\tvar i = el.length;\r\n\twhile(i--){\r\n\t\tvar a = el[i];\r\n\t\tvar qName = a.qName;\r\n\t\tvar value = a.value;\r\n\t\tvar nsp = qName.indexOf(':');\r\n\t\tif(nsp>0){\r\n\t\t\tvar prefix = a.prefix = qName.slice(0,nsp);\r\n\t\t\tvar localName = qName.slice(nsp+1);\r\n\t\t\tvar nsPrefix = prefix === 'xmlns' && localName\r\n\t\t}else{\r\n\t\t\tlocalName = qName;\r\n\t\t\tprefix = null\r\n\t\t\tnsPrefix = qName === 'xmlns' && ''\r\n\t\t}\r\n\t\t//can not set prefix,because prefix !== ''\r\n\t\ta.localName = localName ;\r\n\t\t//prefix == null for no ns prefix attribute \r\n\t\tif(nsPrefix !== false){//hack!!\r\n\t\t\tif(localNSMap == null){\r\n\t\t\t\tlocalNSMap = {}\r\n\t\t\t\t//console.log(currentNSMap,0)\r\n\t\t\t\t_copy(currentNSMap,currentNSMap={})\r\n\t\t\t\t//console.log(currentNSMap,1)\r\n\t\t\t}\r\n\t\t\tcurrentNSMap[nsPrefix] = localNSMap[nsPrefix] = value;\r\n\t\t\ta.uri = 'http://www.w3.org/2000/xmlns/'\r\n\t\t\tdomBuilder.startPrefixMapping(nsPrefix, value) \r\n\t\t}\r\n\t}\r\n\tvar i = el.length;\r\n\twhile(i--){\r\n\t\ta = el[i];\r\n\t\tvar prefix = a.prefix;\r\n\t\tif(prefix){//no prefix attribute has no namespace\r\n\t\t\tif(prefix === 'xml'){\r\n\t\t\t\ta.uri = 'http://www.w3.org/XML/1998/namespace';\r\n\t\t\t}if(prefix !== 'xmlns'){\r\n\t\t\t\ta.uri = currentNSMap[prefix || '']\r\n\t\t\t\t\r\n\t\t\t\t//{console.log('###'+a.qName,domBuilder.locator.systemId+'',currentNSMap,a.uri)}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\tvar nsp = tagName.indexOf(':');\r\n\tif(nsp>0){\r\n\t\tprefix = el.prefix = tagName.slice(0,nsp);\r\n\t\tlocalName = el.localName = tagName.slice(nsp+1);\r\n\t}else{\r\n\t\tprefix = null;//important!!\r\n\t\tlocalName = el.localName = tagName;\r\n\t}\r\n\t//no prefix element has default namespace\r\n\tvar ns = el.uri = currentNSMap[prefix || ''];\r\n\tdomBuilder.startElement(ns,localName,tagName,el);\r\n\t//endPrefixMapping and startPrefixMapping have not any help for dom builder\r\n\t//localNSMap = null\r\n\tif(el.closed){\r\n\t\tdomBuilder.endElement(ns,localName,tagName);\r\n\t\tif(localNSMap){\r\n\t\t\tfor(prefix in localNSMap){\r\n\t\t\t\tdomBuilder.endPrefixMapping(prefix) \r\n\t\t\t}\r\n\t\t}\r\n\t}else{\r\n\t\tel.currentNSMap = currentNSMap;\r\n\t\tel.localNSMap = localNSMap;\r\n\t\t//parseStack.push(el);\r\n\t\treturn true;\r\n\t}\r\n}\r\nfunction parseHtmlSpecialContent(source,elStartEnd,tagName,entityReplacer,domBuilder){\r\n\tif(/^(?:script|textarea)$/i.test(tagName)){\r\n\t\tvar elEndStart =  source.indexOf('</'+tagName+'>',elStartEnd);\r\n\t\tvar text = source.substring(elStartEnd+1,elEndStart);\r\n\t\tif(/[&<]/.test(text)){\r\n\t\t\tif(/^script$/i.test(tagName)){\r\n\t\t\t\t//if(!/\\]\\]>/.test(text)){\r\n\t\t\t\t\t//lexHandler.startCDATA();\r\n\t\t\t\t\tdomBuilder.characters(text,0,text.length);\r\n\t\t\t\t\t//lexHandler.endCDATA();\r\n\t\t\t\t\treturn elEndStart;\r\n\t\t\t\t//}\r\n\t\t\t}//}else{//text area\r\n\t\t\t\ttext = text.replace(/&#?\\w+;/g,entityReplacer);\r\n\t\t\t\tdomBuilder.characters(text,0,text.length);\r\n\t\t\t\treturn elEndStart;\r\n\t\t\t//}\r\n\t\t\t\r\n\t\t}\r\n\t}\r\n\treturn elStartEnd+1;\r\n}\r\nfunction fixSelfClosed(source,elStartEnd,tagName,closeMap){\r\n\t//if(tagName in closeMap){\r\n\tvar pos = closeMap[tagName];\r\n\tif(pos == null){\r\n\t\t//console.log(tagName)\r\n\t\tpos =  source.lastIndexOf('</'+tagName+'>')\r\n\t\tif(pos<elStartEnd){//忘记闭合\r\n\t\t\tpos = source.lastIndexOf('</'+tagName)\r\n\t\t}\r\n\t\tcloseMap[tagName] =pos\r\n\t}\r\n\treturn pos<elStartEnd;\r\n\t//} \r\n}\r\nfunction _copy(source,target){\r\n\tfor(var n in source){target[n] = source[n]}\r\n}\r\nfunction parseDCC(source,start,domBuilder,errorHandler){//sure start with '<!'\r\n\tvar next= source.charAt(start+2)\r\n\tswitch(next){\r\n\tcase '-':\r\n\t\tif(source.charAt(start + 3) === '-'){\r\n\t\t\tvar end = source.indexOf('-->',start+4);\r\n\t\t\t//append comment source.substring(4,end)//<!--\r\n\t\t\tif(end>start){\r\n\t\t\t\tdomBuilder.comment(source,start+4,end-start-4);\r\n\t\t\t\treturn end+3;\r\n\t\t\t}else{\r\n\t\t\t\terrorHandler.error(\"Unclosed comment\");\r\n\t\t\t\treturn -1;\r\n\t\t\t}\r\n\t\t}else{\r\n\t\t\t//error\r\n\t\t\treturn -1;\r\n\t\t}\r\n\tdefault:\r\n\t\tif(source.substr(start+3,6) == 'CDATA['){\r\n\t\t\tvar end = source.indexOf(']]>',start+9);\r\n\t\t\tdomBuilder.startCDATA();\r\n\t\t\tdomBuilder.characters(source,start+9,end-start-9);\r\n\t\t\tdomBuilder.endCDATA() \r\n\t\t\treturn end+3;\r\n\t\t}\r\n\t\t//<!DOCTYPE\r\n\t\t//startDTD(java.lang.String name, java.lang.String publicId, java.lang.String systemId) \r\n\t\tvar matchs = split(source,start);\r\n\t\tvar len = matchs.length;\r\n\t\tif(len>1 && /!doctype/i.test(matchs[0][0])){\r\n\t\t\tvar name = matchs[1][0];\r\n\t\t\tvar pubid = len>3 && /^public$/i.test(matchs[2][0]) && matchs[3][0]\r\n\t\t\tvar sysid = len>4 && matchs[4][0];\r\n\t\t\tvar lastMatch = matchs[len-1]\r\n\t\t\tdomBuilder.startDTD(name,pubid && pubid.replace(/^(['\"])(.*?)\\1$/,'$2'),\r\n\t\t\t\t\tsysid && sysid.replace(/^(['\"])(.*?)\\1$/,'$2'));\r\n\t\t\tdomBuilder.endDTD();\r\n\t\t\t\r\n\t\t\treturn lastMatch.index+lastMatch[0].length\r\n\t\t}\r\n\t}\r\n\treturn -1;\r\n}\r\n\r\n\r\n\r\nfunction parseInstruction(source,start,domBuilder){\r\n\tvar end = source.indexOf('?>',start);\r\n\tif(end){\r\n\t\tvar match = source.substring(start,end).match(/^<\\?(\\S*)\\s*([\\s\\S]*?)\\s*$/);\r\n\t\tif(match){\r\n\t\t\tvar len = match[0].length;\r\n\t\t\tdomBuilder.processingInstruction(match[1], match[2]) ;\r\n\t\t\treturn end+2;\r\n\t\t}else{//error\r\n\t\t\treturn -1;\r\n\t\t}\r\n\t}\r\n\treturn -1;\r\n}\r\n\r\n/**\r\n * @param source\r\n */\r\nfunction ElementAttributes(source){\r\n\t\r\n}\r\nElementAttributes.prototype = {\r\n\tsetTagName:function(tagName){\r\n\t\tif(!tagNamePattern.test(tagName)){\r\n\t\t\tthrow new Error('invalid tagName:'+tagName)\r\n\t\t}\r\n\t\tthis.tagName = tagName\r\n\t},\r\n\tadd:function(qName,value,offset){\r\n\t\tif(!tagNamePattern.test(qName)){\r\n\t\t\tthrow new Error('invalid attribute:'+qName)\r\n\t\t}\r\n\t\tthis[this.length++] = {qName:qName,value:value,offset:offset}\r\n\t},\r\n\tlength:0,\r\n\tgetLocalName:function(i){return this[i].localName},\r\n\tgetLocator:function(i){return this[i].locator},\r\n\tgetQName:function(i){return this[i].qName},\r\n\tgetURI:function(i){return this[i].uri},\r\n\tgetValue:function(i){return this[i].value}\r\n//\t,getIndex:function(uri, localName)){\r\n//\t\tif(localName){\r\n//\t\t\t\r\n//\t\t}else{\r\n//\t\t\tvar qName = uri\r\n//\t\t}\r\n//\t},\r\n//\tgetValue:function(){return this.getValue(this.getIndex.apply(this,arguments))},\r\n//\tgetType:function(uri,localName){}\r\n//\tgetType:function(i){},\r\n}\r\n\r\n\r\n\r\n\r\nfunction _set_proto_(thiz,parent){\r\n\tthiz.__proto__ = parent;\r\n\treturn thiz;\r\n}\r\nif(!(_set_proto_({},_set_proto_.prototype) instanceof _set_proto_)){\r\n\t_set_proto_ = function(thiz,parent){\r\n\t\tfunction p(){};\r\n\t\tp.prototype = parent;\r\n\t\tp = new p();\r\n\t\tfor(parent in thiz){\r\n\t\t\tp[parent] = thiz[parent];\r\n\t\t}\r\n\t\treturn p;\r\n\t}\r\n}\r\n\r\nfunction split(source,start){\r\n\tvar match;\r\n\tvar buf = [];\r\n\tvar reg = /'[^']+'|\"[^\"]+\"|[^\\s<>\\/=]+=?|(\\/?\\s*>|<)/g;\r\n\treg.lastIndex = start;\r\n\treg.exec(source);//skip <\r\n\twhile(match = reg.exec(source)){\r\n\t\tbuf.push(match);\r\n\t\tif(match[1])return buf;\r\n\t}\r\n}\r\n\r\nexports.XMLReader = XMLReader;\r\n\r\n"], "mappings": "AAAA;AACA;AACA;AACA,IAAIA,aAAa,GAAG,kJAAkJ;AACtK,IAAIC,QAAQ,GAAG,IAAIC,MAAM,CAAC,YAAY,GAACF,aAAa,CAACG,MAAM,CAACC,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC,GAAC,wCAAwC,CAAC;AACjH,IAAIC,cAAc,GAAG,IAAIH,MAAM,CAAC,GAAG,GAACF,aAAa,CAACG,MAAM,GAACF,QAAQ,CAACE,MAAM,GAAC,QAAQ,GAACH,aAAa,CAACG,MAAM,GAACF,QAAQ,CAACE,MAAM,GAAC,MAAM,CAAC;AAC9H;AACA;;AAEA;AACA;AACA,IAAIG,KAAK,GAAG,CAAC,CAAC;AACd,IAAIC,MAAM,GAAG,CAAC,CAAC;AACf,IAAIC,YAAY,GAAC,CAAC,CAAC;AACnB,IAAIC,IAAI,GAAG,CAAC,CAAC;AACb,IAAIC,mBAAmB,GAAG,CAAC,CAAC;AAC5B,IAAIC,UAAU,GAAG,CAAC,CAAC;AACnB,IAAIC,WAAW,GAAG,CAAC,CAAC;AACpB,IAAIC,WAAW,GAAG,CAAC,CAAC;;AAEpB,SAASC,SAASA,CAAA,EAAE,CAEpB;AAEAA,SAAS,CAACC,SAAS,GAAG;EACrBC,KAAK,EAAC,SAANA,KAAKA,CAAUb,MAAM,EAACc,YAAY,EAACC,SAAS,EAAC;IAC5C,IAAIC,UAAU,GAAG,IAAI,CAACA,UAAU;IAChCA,UAAU,CAACC,aAAa,CAAC,CAAC;IAC1BC,KAAK,CAACJ,YAAY,EAAEA,YAAY,GAAG,CAAC,CAAC,CAAC;IACtCD,MAAK,CAACb,MAAM,EAACc,YAAY,EAACC,SAAS,EACjCC,UAAU,EAAC,IAAI,CAACG,YAAY,CAAC;IAC/BH,UAAU,CAACI,WAAW,CAAC,CAAC;EACzB;AACD,CAAC;AACD,SAASP,MAAKA,CAACb,MAAM,EAACqB,gBAAgB,EAACN,SAAS,EAACC,UAAU,EAACG,YAAY,EAAC;EACxE,SAASG,iBAAiBA,CAACC,IAAI,EAAE;IAChC;IACA;IACA,IAAIA,IAAI,GAAG,MAAM,EAAE;MAClBA,IAAI,IAAI,OAAO;MACf,IAAIC,UAAU,GAAG,MAAM,IAAID,IAAI,IAAI,EAAE,CAAC;QACnCE,UAAU,GAAG,MAAM,IAAIF,IAAI,GAAG,KAAK,CAAC;MAEvC,OAAOG,MAAM,CAACC,YAAY,CAACH,UAAU,EAAEC,UAAU,CAAC;IACnD,CAAC,MAAM;MACN,OAAOC,MAAM,CAACC,YAAY,CAACJ,IAAI,CAAC;IACjC;EACD;EACA,SAASK,cAAcA,CAACC,CAAC,EAAC;IACzB,IAAIC,CAAC,GAAGD,CAAC,CAAC5B,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;IACrB,IAAG6B,CAAC,IAAIf,SAAS,EAAC;MACjB,OAAOA,SAAS,CAACe,CAAC,CAAC;IACpB,CAAC,MAAK,IAAGA,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAC;MAC5B,OAAOT,iBAAiB,CAACU,QAAQ,CAACF,CAAC,CAACG,MAAM,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAC,IAAI,CAAC,CAAC,CAAC;IAClE,CAAC,MAAI;MACJf,YAAY,CAACgB,KAAK,CAAC,mBAAmB,GAACN,CAAC,CAAC;MACzC,OAAOA,CAAC;IACT;EACD;EACA,SAASO,UAAUA,CAACC,GAAG,EAAC;IAAC;IACxB,IAAGA,GAAG,GAACC,KAAK,EAAC;MACZ,IAAIC,EAAE,GAAGvC,MAAM,CAACwC,SAAS,CAACF,KAAK,EAACD,GAAG,CAAC,CAACH,OAAO,CAAC,UAAU,EAACN,cAAc,CAAC;MACvEa,OAAO,IAAEC,QAAQ,CAACJ,KAAK,CAAC;MACxBtB,UAAU,CAAC2B,UAAU,CAACJ,EAAE,EAAC,CAAC,EAACF,GAAG,GAACC,KAAK,CAAC;MACrCA,KAAK,GAAGD,GAAG;IACZ;EACD;EACA,SAASK,QAAQA,CAACE,CAAC,EAACC,CAAC,EAAC;IACrB,OAAMD,CAAC,IAAEE,OAAO,KAAKD,CAAC,GAAGE,WAAW,CAACC,IAAI,CAAChD,MAAM,CAAC,CAAC,EAAC;MAClDiD,SAAS,GAAGJ,CAAC,CAACK,KAAK;MACnBJ,OAAO,GAAGG,SAAS,GAAGJ,CAAC,CAAC,CAAC,CAAC,CAACM,MAAM;MACjCV,OAAO,CAACW,UAAU,EAAE;MACpB;IACD;IACAX,OAAO,CAACY,YAAY,GAAGT,CAAC,GAACK,SAAS,GAAC,CAAC;EACrC;EACA,IAAIA,SAAS,GAAG,CAAC;EACjB,IAAIH,OAAO,GAAG,CAAC;EACf,IAAIC,WAAW,GAAG,qBAAqB;EACvC,IAAIN,OAAO,GAAGzB,UAAU,CAACyB,OAAO;EAEhC,IAAIa,UAAU,GAAG,CAAC;IAACC,YAAY,EAAClC;EAAgB,CAAC,CAAC;EAClD,IAAImC,QAAQ,GAAG,CAAC,CAAC;EACjB,IAAIlB,KAAK,GAAG,CAAC;EACb,OAAM,IAAI,EAAC;IACV,IAAG;MACF,IAAImB,QAAQ,GAAGzD,MAAM,CAAC0D,OAAO,CAAC,GAAG,EAACpB,KAAK,CAAC;MACxC,IAAGmB,QAAQ,GAAC,CAAC,EAAC;QACb,IAAG,CAACzD,MAAM,CAACiC,MAAM,CAACK,KAAK,CAAC,CAACqB,KAAK,CAAC,OAAO,CAAC,EAAC;UACvC,IAAIC,GAAG,GAAG5C,UAAU,CAAC4C,GAAG;UACrB,IAAIC,IAAI,GAAGD,GAAG,CAACE,cAAc,CAAC9D,MAAM,CAACiC,MAAM,CAACK,KAAK,CAAC,CAAC;UACnDsB,GAAG,CAACG,WAAW,CAACF,IAAI,CAAC;UACrB7C,UAAU,CAACgD,cAAc,GAAGH,IAAI;QACpC;QACA;MACD;MACA,IAAGJ,QAAQ,GAACnB,KAAK,EAAC;QACjBF,UAAU,CAACqB,QAAQ,CAAC;MACrB;MACA,QAAOzD,MAAM,CAAC+B,MAAM,CAAC0B,QAAQ,GAAC,CAAC,CAAC;QAChC,KAAK,GAAG;UACP,IAAIpB,GAAG,GAAGrC,MAAM,CAAC0D,OAAO,CAAC,GAAG,EAACD,QAAQ,GAAC,CAAC,CAAC;UACxC,IAAIQ,OAAO,GAAGjE,MAAM,CAACwC,SAAS,CAACiB,QAAQ,GAAC,CAAC,EAACpB,GAAG,CAAC;UAC9C,IAAI6B,MAAM,GAAGZ,UAAU,CAACa,GAAG,CAAC,CAAC;UAC7B,IAAG9B,GAAG,GAAC,CAAC,EAAC;YAEF4B,OAAO,GAAGjE,MAAM,CAACwC,SAAS,CAACiB,QAAQ,GAAC,CAAC,CAAC,CAACvB,OAAO,CAAC,SAAS,EAAC,EAAE,CAAC;YAC5D;YACAf,YAAY,CAACgB,KAAK,CAAC,gBAAgB,GAAC8B,OAAO,GAAC,mBAAmB,GAACC,MAAM,CAACD,OAAO,CAAC;YAC/E5B,GAAG,GAAGoB,QAAQ,GAAC,CAAC,GAACQ,OAAO,CAACd,MAAM;UAChC,CAAC,MAAK,IAAGc,OAAO,CAACN,KAAK,CAAC,KAAK,CAAC,EAAC;YAC7BM,OAAO,GAAGA,OAAO,CAAC/B,OAAO,CAAC,SAAS,EAAC,EAAE,CAAC;YACvCf,YAAY,CAACgB,KAAK,CAAC,gBAAgB,GAAC8B,OAAO,GAAC,qBAAqB,CAAC;YAClE5B,GAAG,GAAGoB,QAAQ,GAAC,CAAC,GAACQ,OAAO,CAACd,MAAM;UACtC;UACA;UACA;UACA,IAAIiB,UAAU,GAAGF,MAAM,CAACE,UAAU;UAClC,IAAIC,QAAQ,GAAGH,MAAM,CAACD,OAAO,IAAIA,OAAO;UACxC,IAAIK,iBAAiB,GAAGD,QAAQ,IAAIH,MAAM,CAACD,OAAO,IAAEC,MAAM,CAACD,OAAO,CAACM,WAAW,CAAC,CAAC,IAAIN,OAAO,CAACM,WAAW,CAAC,CAAC;UACnG,IAAGD,iBAAiB,EAAC;YACpBtD,UAAU,CAACwD,UAAU,CAACN,MAAM,CAACO,GAAG,EAACP,MAAM,CAACQ,SAAS,EAACT,OAAO,CAAC;YAChE,IAAGG,UAAU,EAAC;cACb,KAAI,IAAIO,MAAM,IAAIP,UAAU,EAAC;gBAC5BpD,UAAU,CAAC4D,gBAAgB,CAACD,MAAM,CAAC;cACpC;YACD;YACA,IAAG,CAACN,QAAQ,EAAC;cACHlD,YAAY,CAAC0D,UAAU,CAAC,gBAAgB,GAACZ,OAAO,GAAC,0CAA0C,GAACC,MAAM,CAACD,OAAQ,CAAC;YACtH;UACK,CAAC,MAAI;YACJX,UAAU,CAACwB,IAAI,CAACZ,MAAM,CAAC;UACxB;UAEN7B,GAAG,EAAE;UACL;QACA;QACD,KAAK,GAAG;UAAC;UACRI,OAAO,IAAEC,QAAQ,CAACe,QAAQ,CAAC;UAC3BpB,GAAG,GAAG0C,gBAAgB,CAAC/E,MAAM,EAACyD,QAAQ,EAACzC,UAAU,CAAC;UAClD;QACD,KAAK,GAAG;UAAC;UACRyB,OAAO,IAAEC,QAAQ,CAACe,QAAQ,CAAC;UAC3BpB,GAAG,GAAG2C,QAAQ,CAAChF,MAAM,EAACyD,QAAQ,EAACzC,UAAU,EAACG,YAAY,CAAC;UACvD;QACD;UACCsB,OAAO,IAAEC,QAAQ,CAACe,QAAQ,CAAC;UAC3B,IAAIwB,EAAE,GAAG,IAAIC,iBAAiB,CAAC,CAAC;UAChC,IAAI3B,YAAY,GAAGD,UAAU,CAACA,UAAU,CAACH,MAAM,GAAC,CAAC,CAAC,CAACI,YAAY;UAC/D;UACA,IAAIlB,GAAG,GAAG8C,qBAAqB,CAACnF,MAAM,EAACyD,QAAQ,EAACwB,EAAE,EAAC1B,YAAY,EAAC3B,cAAc,EAACT,YAAY,CAAC;UAC5F,IAAIiE,GAAG,GAAGH,EAAE,CAAC9B,MAAM;UAGnB,IAAG,CAAC8B,EAAE,CAACI,MAAM,IAAIC,aAAa,CAACtF,MAAM,EAACqC,GAAG,EAAC4C,EAAE,CAAChB,OAAO,EAACT,QAAQ,CAAC,EAAC;YAC9DyB,EAAE,CAACI,MAAM,GAAG,IAAI;YAChB,IAAG,CAACtE,SAAS,CAACwE,IAAI,EAAC;cAClBpE,YAAY,CAACqE,OAAO,CAAC,wBAAwB,CAAC;YAC/C;UACD;UACA,IAAG/C,OAAO,IAAI2C,GAAG,EAAC;YACjB,IAAIK,QAAQ,GAAGC,WAAW,CAACjD,OAAO,EAAC,CAAC,CAAC,CAAC;YACtC;YACA,KAAI,IAAIkD,CAAC,GAAG,CAAC,EAACA,CAAC,GAACP,GAAG,EAACO,CAAC,EAAE,EAAC;cACvB,IAAI9D,CAAC,GAAGoD,EAAE,CAACU,CAAC,CAAC;cACbjD,QAAQ,CAACb,CAAC,CAAC+D,MAAM,CAAC;cAClB/D,CAAC,CAACY,OAAO,GAAGiD,WAAW,CAACjD,OAAO,EAAC,CAAC,CAAC,CAAC;YACpC;YACA;YACAzB,UAAU,CAACyB,OAAO,GAAGgD,QAAQ;YAC7B,IAAGI,aAAa,CAACZ,EAAE,EAACjE,UAAU,EAACuC,YAAY,CAAC,EAAC;cAC5CD,UAAU,CAACwB,IAAI,CAACG,EAAE,CAAC;YACpB;YACAjE,UAAU,CAACyB,OAAO,GAAGA,OAAO;UAC7B,CAAC,MAAI;YACJ,IAAGoD,aAAa,CAACZ,EAAE,EAACjE,UAAU,EAACuC,YAAY,CAAC,EAAC;cAC5CD,UAAU,CAACwB,IAAI,CAACG,EAAE,CAAC;YACpB;UACD;UAIA,IAAGA,EAAE,CAACR,GAAG,KAAK,8BAA8B,IAAI,CAACQ,EAAE,CAACI,MAAM,EAAC;YAC1DhD,GAAG,GAAGyD,uBAAuB,CAAC9F,MAAM,EAACqC,GAAG,EAAC4C,EAAE,CAAChB,OAAO,EAACrC,cAAc,EAACZ,UAAU,CAAC;UAC/E,CAAC,MAAI;YACJqB,GAAG,EAAE;UACN;MACD;IACD,CAAC,QAAM0D,CAAC,EAAC;MACR5E,YAAY,CAACgB,KAAK,CAAC,uBAAuB,GAAC4D,CAAC,CAAC;MAC7C;MACA1D,GAAG,GAAG,CAAC,CAAC;MACR;IACD;IACA,IAAGA,GAAG,GAACC,KAAK,EAAC;MACZA,KAAK,GAAGD,GAAG;IACZ,CAAC,MAAI;MACJ;MACAD,UAAU,CAAC4D,IAAI,CAACC,GAAG,CAACxC,QAAQ,EAACnB,KAAK,CAAC,GAAC,CAAC,CAAC;IACvC;EACD;AACD;AACA,SAASoD,WAAWA,CAACQ,CAAC,EAACC,CAAC,EAAC;EACxBA,CAAC,CAAC/C,UAAU,GAAG8C,CAAC,CAAC9C,UAAU;EAC3B+C,CAAC,CAAC9C,YAAY,GAAG6C,CAAC,CAAC7C,YAAY;EAC/B,OAAO8C,CAAC;AACT;;AAEA;AACA;AACA;AACA;AACA,SAAShB,qBAAqBA,CAACnF,MAAM,EAACsC,KAAK,EAAC2C,EAAE,EAAC1B,YAAY,EAAC3B,cAAc,EAACT,YAAY,EAAC;EACvF,IAAIiF,QAAQ;EACZ,IAAIC,KAAK;EACT,IAAIzD,CAAC,GAAG,EAAEN,KAAK;EACf,IAAIgE,CAAC,GAAGnG,KAAK,CAAC;EACd,OAAM,IAAI,EAAC;IACV,IAAIoG,CAAC,GAAGvG,MAAM,CAAC+B,MAAM,CAACa,CAAC,CAAC;IACxB,QAAO2D,CAAC;MACR,KAAK,GAAG;QACP,IAAGD,CAAC,KAAKlG,MAAM,EAAC;UAAC;UAChBgG,QAAQ,GAAGpG,MAAM,CAACC,KAAK,CAACqC,KAAK,EAACM,CAAC,CAAC;UAChC0D,CAAC,GAAGhG,IAAI;QACT,CAAC,MAAK,IAAGgG,CAAC,KAAKjG,YAAY,EAAC;UAC3BiG,CAAC,GAAGhG,IAAI;QACT,CAAC,MAAI;UACJ;UACA,MAAM,IAAIkG,KAAK,CAAC,qCAAqC,CAAC;QACvD;QACA;MACD,KAAK,IAAI;MACT,KAAK,GAAG;QACP,IAAGF,CAAC,KAAKhG,IAAI,IAAIgG,CAAC,KAAKlG,MAAM,CAAC;QAAA,EAC5B;UAAC;UACF,IAAGkG,CAAC,KAAKlG,MAAM,EAAC;YACfe,YAAY,CAACqE,OAAO,CAAC,gCAAgC,CAAC;YACtDY,QAAQ,GAAGpG,MAAM,CAACC,KAAK,CAACqC,KAAK,EAACM,CAAC,CAAC;UACjC;UACAN,KAAK,GAAGM,CAAC,GAAC,CAAC;UACXA,CAAC,GAAG5C,MAAM,CAAC0D,OAAO,CAAC6C,CAAC,EAACjE,KAAK,CAAC;UAC3B,IAAGM,CAAC,GAAC,CAAC,EAAC;YACNyD,KAAK,GAAGrG,MAAM,CAACC,KAAK,CAACqC,KAAK,EAACM,CAAC,CAAC,CAACV,OAAO,CAAC,UAAU,EAACN,cAAc,CAAC;YAChEqD,EAAE,CAACwB,GAAG,CAACL,QAAQ,EAACC,KAAK,EAAC/D,KAAK,GAAC,CAAC,CAAC;YAC9BgE,CAAC,GAAG9F,UAAU;UACf,CAAC,MAAI;YACJ;YACA,MAAM,IAAIgG,KAAK,CAAC,2BAA2B,GAACD,CAAC,GAAC,UAAU,CAAC;UAC1D;QACD,CAAC,MAAK,IAAGD,CAAC,IAAI/F,mBAAmB,EAAC;UACjC8F,KAAK,GAAGrG,MAAM,CAACC,KAAK,CAACqC,KAAK,EAACM,CAAC,CAAC,CAACV,OAAO,CAAC,UAAU,EAACN,cAAc,CAAC;UAChE;UACAqD,EAAE,CAACwB,GAAG,CAACL,QAAQ,EAACC,KAAK,EAAC/D,KAAK,CAAC;UAC5B;UACAnB,YAAY,CAACqE,OAAO,CAAC,aAAa,GAACY,QAAQ,GAAC,sBAAsB,GAACG,CAAC,GAAC,KAAK,CAAC;UAC3EjE,KAAK,GAAGM,CAAC,GAAC,CAAC;UACX0D,CAAC,GAAG9F,UAAU;QACf,CAAC,MAAI;UACJ;UACA,MAAM,IAAIgG,KAAK,CAAC,gCAAgC,CAAC;QAClD;QACA;MACD,KAAK,GAAG;QACP,QAAOF,CAAC;UACR,KAAKnG,KAAK;YACT8E,EAAE,CAACyB,UAAU,CAAC1G,MAAM,CAACC,KAAK,CAACqC,KAAK,EAACM,CAAC,CAAC,CAAC;UACrC,KAAKpC,UAAU;UACf,KAAKC,WAAW;UAChB,KAAKC,WAAW;YACf4F,CAAC,GAAE5F,WAAW;YACduE,EAAE,CAACI,MAAM,GAAG,IAAI;UACjB,KAAK9E,mBAAmB;UACxB,KAAKH,MAAM;UACX,KAAKC,YAAY;YAChB;UACD;UACA;YACC,MAAM,IAAImG,KAAK,CAAC,mCAAmC,CAAC;QACrD;QACA;MACD,KAAK,EAAE;QAAC;QACP;QACArF,YAAY,CAACgB,KAAK,CAAC,yBAAyB,CAAC;QAC7C,IAAGmE,CAAC,IAAInG,KAAK,EAAC;UACb8E,EAAE,CAACyB,UAAU,CAAC1G,MAAM,CAACC,KAAK,CAACqC,KAAK,EAACM,CAAC,CAAC,CAAC;QACrC;QACA,OAAOA,CAAC;MACT,KAAK,GAAG;QACP,QAAO0D,CAAC;UACR,KAAKnG,KAAK;YACT8E,EAAE,CAACyB,UAAU,CAAC1G,MAAM,CAACC,KAAK,CAACqC,KAAK,EAACM,CAAC,CAAC,CAAC;UACrC,KAAKpC,UAAU;UACf,KAAKC,WAAW;UAChB,KAAKC,WAAW;YACf;UAAM;UACP,KAAKH,mBAAmB,CAAC;UACzB,KAAKH,MAAM;YACViG,KAAK,GAAGrG,MAAM,CAACC,KAAK,CAACqC,KAAK,EAACM,CAAC,CAAC;YAC7B,IAAGyD,KAAK,CAACpG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,EAAC;cAC1BgF,EAAE,CAACI,MAAM,GAAI,IAAI;cACjBgB,KAAK,GAAGA,KAAK,CAACpG,KAAK,CAAC,CAAC,EAAC,CAAC,CAAC,CAAC;YAC1B;UACD,KAAKI,YAAY;YAChB,IAAGiG,CAAC,KAAKjG,YAAY,EAAC;cACrBgG,KAAK,GAAGD,QAAQ;YACjB;YACA,IAAGE,CAAC,IAAI/F,mBAAmB,EAAC;cAC3BY,YAAY,CAACqE,OAAO,CAAC,aAAa,GAACa,KAAK,GAAC,oBAAoB,CAAC;cAC9DpB,EAAE,CAACwB,GAAG,CAACL,QAAQ,EAACC,KAAK,CAACnE,OAAO,CAAC,UAAU,EAACN,cAAc,CAAC,EAACU,KAAK,CAAC;YAChE,CAAC,MAAI;cACJ,IAAGiB,YAAY,CAAC,EAAE,CAAC,KAAK,8BAA8B,IAAI,CAAC8C,KAAK,CAAC1C,KAAK,CAAC,kCAAkC,CAAC,EAAC;gBAC1GxC,YAAY,CAACqE,OAAO,CAAC,aAAa,GAACa,KAAK,GAAC,oBAAoB,GAACA,KAAK,GAAC,aAAa,CAAC;cACnF;cACApB,EAAE,CAACwB,GAAG,CAACJ,KAAK,EAACA,KAAK,EAAC/D,KAAK,CAAC;YAC1B;YACA;UACD,KAAKhC,IAAI;YACR,MAAM,IAAIkG,KAAK,CAAC,0BAA0B,CAAC;QAC5C;QACH;QACG,OAAO5D,CAAC;MACT;MACA,KAAK,MAAQ;QACZ2D,CAAC,GAAG,GAAG;MACR;QACC,IAAGA,CAAC,IAAG,GAAG,EAAC;UAAC;UACX,QAAOD,CAAC;YACR,KAAKnG,KAAK;cACT8E,EAAE,CAACyB,UAAU,CAAC1G,MAAM,CAACC,KAAK,CAACqC,KAAK,EAACM,CAAC,CAAC,CAAC,CAAC;cACrC0D,CAAC,GAAG7F,WAAW;cACf;YACD,KAAKL,MAAM;cACVgG,QAAQ,GAAGpG,MAAM,CAACC,KAAK,CAACqC,KAAK,EAACM,CAAC,CAAC;cAChC0D,CAAC,GAAGjG,YAAY;cAChB;YACD,KAAKE,mBAAmB;cACvB,IAAI8F,KAAK,GAAGrG,MAAM,CAACC,KAAK,CAACqC,KAAK,EAACM,CAAC,CAAC,CAACV,OAAO,CAAC,UAAU,EAACN,cAAc,CAAC;cACpET,YAAY,CAACqE,OAAO,CAAC,aAAa,GAACa,KAAK,GAAC,oBAAoB,CAAC;cAC9DpB,EAAE,CAACwB,GAAG,CAACL,QAAQ,EAACC,KAAK,EAAC/D,KAAK,CAAC;YAC7B,KAAK9B,UAAU;cACd8F,CAAC,GAAG7F,WAAW;cACf;YACD;YACA;YACA;YACA;YACA;YACC;UACD;QACD,CAAC,MAAI;UAAC;UACT;UACA;UACI,QAAO6F,CAAC;YACR;YACA;YACA;YACA,KAAKjG,YAAY;cAChB,IAAI4D,OAAO,GAAIgB,EAAE,CAAChB,OAAO;cACzB,IAAGV,YAAY,CAAC,EAAE,CAAC,KAAK,8BAA8B,IAAI,CAAC6C,QAAQ,CAACzC,KAAK,CAAC,kCAAkC,CAAC,EAAC;gBAC7GxC,YAAY,CAACqE,OAAO,CAAC,aAAa,GAACY,QAAQ,GAAC,oBAAoB,GAACA,QAAQ,GAAC,cAAc,CAAC;cAC1F;cACAnB,EAAE,CAACwB,GAAG,CAACL,QAAQ,EAACA,QAAQ,EAAC9D,KAAK,CAAC;cAC/BA,KAAK,GAAGM,CAAC;cACT0D,CAAC,GAAGlG,MAAM;cACV;YACD,KAAKI,UAAU;cACdW,YAAY,CAACqE,OAAO,CAAC,8BAA8B,GAACY,QAAQ,GAAC,KAAK,CAAC;YACpE,KAAK3F,WAAW;cACf6F,CAAC,GAAGlG,MAAM;cACVkC,KAAK,GAAGM,CAAC;cACT;YACD,KAAKtC,IAAI;cACRgG,CAAC,GAAG/F,mBAAmB;cACvB+B,KAAK,GAAGM,CAAC;cACT;YACD,KAAKlC,WAAW;cACf,MAAM,IAAI8F,KAAK,CAAC,4DAA4D,CAAC;UAC9E;QACD;IACD,CAAC;IACD;IACA5D,CAAC,EAAE;EACJ;AACD;AACA;AACA;AACA;AACA,SAASiD,aAAaA,CAACZ,EAAE,EAACjE,UAAU,EAACuC,YAAY,EAAC;EACjD,IAAIU,OAAO,GAAGgB,EAAE,CAAChB,OAAO;EACxB,IAAIG,UAAU,GAAG,IAAI;EACrB;EACA,IAAIuB,CAAC,GAAGV,EAAE,CAAC9B,MAAM;EACjB,OAAMwC,CAAC,EAAE,EAAC;IACT,IAAI9D,CAAC,GAAGoD,EAAE,CAACU,CAAC,CAAC;IACb,IAAIgB,KAAK,GAAG9E,CAAC,CAAC8E,KAAK;IACnB,IAAIN,KAAK,GAAGxE,CAAC,CAACwE,KAAK;IACnB,IAAIO,GAAG,GAAGD,KAAK,CAACjD,OAAO,CAAC,GAAG,CAAC;IAC5B,IAAGkD,GAAG,GAAC,CAAC,EAAC;MACR,IAAIjC,MAAM,GAAG9C,CAAC,CAAC8C,MAAM,GAAGgC,KAAK,CAAC1G,KAAK,CAAC,CAAC,EAAC2G,GAAG,CAAC;MAC1C,IAAIlC,SAAS,GAAGiC,KAAK,CAAC1G,KAAK,CAAC2G,GAAG,GAAC,CAAC,CAAC;MAClC,IAAIC,QAAQ,GAAGlC,MAAM,KAAK,OAAO,IAAID,SAAS;IAC/C,CAAC,MAAI;MACJA,SAAS,GAAGiC,KAAK;MACjBhC,MAAM,GAAG,IAAI;MACbkC,QAAQ,GAAGF,KAAK,KAAK,OAAO,IAAI,EAAE;IACnC;IACA;IACA9E,CAAC,CAAC6C,SAAS,GAAGA,SAAS;IACvB;IACA,IAAGmC,QAAQ,KAAK,KAAK,EAAC;MAAC;MACtB,IAAGzC,UAAU,IAAI,IAAI,EAAC;QACrBA,UAAU,GAAG,CAAC,CAAC;QACf;QACAlD,KAAK,CAACqC,YAAY,EAACA,YAAY,GAAC,CAAC,CAAC,CAAC;QACnC;MACD;MACAA,YAAY,CAACsD,QAAQ,CAAC,GAAGzC,UAAU,CAACyC,QAAQ,CAAC,GAAGR,KAAK;MACrDxE,CAAC,CAAC4C,GAAG,GAAG,+BAA+B;MACvCzD,UAAU,CAAC8F,kBAAkB,CAACD,QAAQ,EAAER,KAAK,CAAC;IAC/C;EACD;EACA,IAAIV,CAAC,GAAGV,EAAE,CAAC9B,MAAM;EACjB,OAAMwC,CAAC,EAAE,EAAC;IACT9D,CAAC,GAAGoD,EAAE,CAACU,CAAC,CAAC;IACT,IAAIhB,MAAM,GAAG9C,CAAC,CAAC8C,MAAM;IACrB,IAAGA,MAAM,EAAC;MAAC;MACV,IAAGA,MAAM,KAAK,KAAK,EAAC;QACnB9C,CAAC,CAAC4C,GAAG,GAAG,sCAAsC;MAC/C;MAAC,IAAGE,MAAM,KAAK,OAAO,EAAC;QACtB9C,CAAC,CAAC4C,GAAG,GAAGlB,YAAY,CAACoB,MAAM,IAAI,EAAE,CAAC;;QAElC;MACD;IACD;EACD;EACA,IAAIiC,GAAG,GAAG3C,OAAO,CAACP,OAAO,CAAC,GAAG,CAAC;EAC9B,IAAGkD,GAAG,GAAC,CAAC,EAAC;IACRjC,MAAM,GAAGM,EAAE,CAACN,MAAM,GAAGV,OAAO,CAAChE,KAAK,CAAC,CAAC,EAAC2G,GAAG,CAAC;IACzClC,SAAS,GAAGO,EAAE,CAACP,SAAS,GAAGT,OAAO,CAAChE,KAAK,CAAC2G,GAAG,GAAC,CAAC,CAAC;EAChD,CAAC,MAAI;IACJjC,MAAM,GAAG,IAAI,CAAC;IACdD,SAAS,GAAGO,EAAE,CAACP,SAAS,GAAGT,OAAO;EACnC;EACA;EACA,IAAI8C,EAAE,GAAG9B,EAAE,CAACR,GAAG,GAAGlB,YAAY,CAACoB,MAAM,IAAI,EAAE,CAAC;EAC5C3D,UAAU,CAACgG,YAAY,CAACD,EAAE,EAACrC,SAAS,EAACT,OAAO,EAACgB,EAAE,CAAC;EAChD;EACA;EACA,IAAGA,EAAE,CAACI,MAAM,EAAC;IACZrE,UAAU,CAACwD,UAAU,CAACuC,EAAE,EAACrC,SAAS,EAACT,OAAO,CAAC;IAC3C,IAAGG,UAAU,EAAC;MACb,KAAIO,MAAM,IAAIP,UAAU,EAAC;QACxBpD,UAAU,CAAC4D,gBAAgB,CAACD,MAAM,CAAC;MACpC;IACD;EACD,CAAC,MAAI;IACJM,EAAE,CAAC1B,YAAY,GAAGA,YAAY;IAC9B0B,EAAE,CAACb,UAAU,GAAGA,UAAU;IAC1B;IACA,OAAO,IAAI;EACZ;AACD;AACA,SAAS0B,uBAAuBA,CAAC9F,MAAM,EAACiH,UAAU,EAAChD,OAAO,EAACrC,cAAc,EAACZ,UAAU,EAAC;EACpF,IAAG,wBAAwB,CAACkG,IAAI,CAACjD,OAAO,CAAC,EAAC;IACzC,IAAIkD,UAAU,GAAInH,MAAM,CAAC0D,OAAO,CAAC,IAAI,GAACO,OAAO,GAAC,GAAG,EAACgD,UAAU,CAAC;IAC7D,IAAIpD,IAAI,GAAG7D,MAAM,CAACwC,SAAS,CAACyE,UAAU,GAAC,CAAC,EAACE,UAAU,CAAC;IACpD,IAAG,MAAM,CAACD,IAAI,CAACrD,IAAI,CAAC,EAAC;MACpB,IAAG,WAAW,CAACqD,IAAI,CAACjD,OAAO,CAAC,EAAC;QAC5B;QACC;QACAjD,UAAU,CAAC2B,UAAU,CAACkB,IAAI,EAAC,CAAC,EAACA,IAAI,CAACV,MAAM,CAAC;QACzC;QACA,OAAOgE,UAAU;QAClB;MACD,CAAC;MACAtD,IAAI,GAAGA,IAAI,CAAC3B,OAAO,CAAC,UAAU,EAACN,cAAc,CAAC;MAC9CZ,UAAU,CAAC2B,UAAU,CAACkB,IAAI,EAAC,CAAC,EAACA,IAAI,CAACV,MAAM,CAAC;MACzC,OAAOgE,UAAU;MAClB;IAED;EACD;EACA,OAAOF,UAAU,GAAC,CAAC;AACpB;AACA,SAAS3B,aAAaA,CAACtF,MAAM,EAACiH,UAAU,EAAChD,OAAO,EAACT,QAAQ,EAAC;EACzD;EACA,IAAI4D,GAAG,GAAG5D,QAAQ,CAACS,OAAO,CAAC;EAC3B,IAAGmD,GAAG,IAAI,IAAI,EAAC;IACd;IACAA,GAAG,GAAIpH,MAAM,CAACqH,WAAW,CAAC,IAAI,GAACpD,OAAO,GAAC,GAAG,CAAC;IAC3C,IAAGmD,GAAG,GAACH,UAAU,EAAC;MAAC;MAClBG,GAAG,GAAGpH,MAAM,CAACqH,WAAW,CAAC,IAAI,GAACpD,OAAO,CAAC;IACvC;IACAT,QAAQ,CAACS,OAAO,CAAC,GAAEmD,GAAG;EACvB;EACA,OAAOA,GAAG,GAACH,UAAU;EACrB;AACD;AACA,SAAS/F,KAAKA,CAAClB,MAAM,EAACsH,MAAM,EAAC;EAC5B,KAAI,IAAIC,CAAC,IAAIvH,MAAM,EAAC;IAACsH,MAAM,CAACC,CAAC,CAAC,GAAGvH,MAAM,CAACuH,CAAC,CAAC;EAAA;AAC3C;AACA,SAASvC,QAAQA,CAAChF,MAAM,EAACsC,KAAK,EAACtB,UAAU,EAACG,YAAY,EAAC;EAAC;EACvD,IAAIqG,IAAI,GAAExH,MAAM,CAAC+B,MAAM,CAACO,KAAK,GAAC,CAAC,CAAC;EAChC,QAAOkF,IAAI;IACX,KAAK,GAAG;MACP,IAAGxH,MAAM,CAAC+B,MAAM,CAACO,KAAK,GAAG,CAAC,CAAC,KAAK,GAAG,EAAC;QACnC,IAAID,GAAG,GAAGrC,MAAM,CAAC0D,OAAO,CAAC,KAAK,EAACpB,KAAK,GAAC,CAAC,CAAC;QACvC;QACA,IAAGD,GAAG,GAACC,KAAK,EAAC;UACZtB,UAAU,CAACyG,OAAO,CAACzH,MAAM,EAACsC,KAAK,GAAC,CAAC,EAACD,GAAG,GAACC,KAAK,GAAC,CAAC,CAAC;UAC9C,OAAOD,GAAG,GAAC,CAAC;QACb,CAAC,MAAI;UACJlB,YAAY,CAACgB,KAAK,CAAC,kBAAkB,CAAC;UACtC,OAAO,CAAC,CAAC;QACV;MACD,CAAC,MAAI;QACJ;QACA,OAAO,CAAC,CAAC;MACV;IACD;MACC,IAAGnC,MAAM,CAACiC,MAAM,CAACK,KAAK,GAAC,CAAC,EAAC,CAAC,CAAC,IAAI,QAAQ,EAAC;QACvC,IAAID,GAAG,GAAGrC,MAAM,CAAC0D,OAAO,CAAC,KAAK,EAACpB,KAAK,GAAC,CAAC,CAAC;QACvCtB,UAAU,CAAC0G,UAAU,CAAC,CAAC;QACvB1G,UAAU,CAAC2B,UAAU,CAAC3C,MAAM,EAACsC,KAAK,GAAC,CAAC,EAACD,GAAG,GAACC,KAAK,GAAC,CAAC,CAAC;QACjDtB,UAAU,CAAC2G,QAAQ,CAAC,CAAC;QACrB,OAAOtF,GAAG,GAAC,CAAC;MACb;MACA;MACA;MACA,IAAIuF,MAAM,GAAGC,KAAK,CAAC7H,MAAM,EAACsC,KAAK,CAAC;MAChC,IAAI8C,GAAG,GAAGwC,MAAM,CAACzE,MAAM;MACvB,IAAGiC,GAAG,GAAC,CAAC,IAAI,WAAW,CAAC8B,IAAI,CAACU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAC;QAC1C,IAAIE,IAAI,GAAGF,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACvB,IAAIG,KAAK,GAAG3C,GAAG,GAAC,CAAC,IAAI,WAAW,CAAC8B,IAAI,CAACU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACnE,IAAII,KAAK,GAAG5C,GAAG,GAAC,CAAC,IAAIwC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjC,IAAIK,SAAS,GAAGL,MAAM,CAACxC,GAAG,GAAC,CAAC,CAAC;QAC7BpE,UAAU,CAACkH,QAAQ,CAACJ,IAAI,EAACC,KAAK,IAAIA,KAAK,CAAC7F,OAAO,CAAC,iBAAiB,EAAC,IAAI,CAAC,EACrE8F,KAAK,IAAIA,KAAK,CAAC9F,OAAO,CAAC,iBAAiB,EAAC,IAAI,CAAC,CAAC;QACjDlB,UAAU,CAACmH,MAAM,CAAC,CAAC;QAEnB,OAAOF,SAAS,CAAC/E,KAAK,GAAC+E,SAAS,CAAC,CAAC,CAAC,CAAC9E,MAAM;MAC3C;EACD;EACA,OAAO,CAAC,CAAC;AACV;AAIA,SAAS4B,gBAAgBA,CAAC/E,MAAM,EAACsC,KAAK,EAACtB,UAAU,EAAC;EACjD,IAAIqB,GAAG,GAAGrC,MAAM,CAAC0D,OAAO,CAAC,IAAI,EAACpB,KAAK,CAAC;EACpC,IAAGD,GAAG,EAAC;IACN,IAAIsB,KAAK,GAAG3D,MAAM,CAACwC,SAAS,CAACF,KAAK,EAACD,GAAG,CAAC,CAACsB,KAAK,CAAC,4BAA4B,CAAC;IAC3E,IAAGA,KAAK,EAAC;MACR,IAAIyB,GAAG,GAAGzB,KAAK,CAAC,CAAC,CAAC,CAACR,MAAM;MACzBnC,UAAU,CAACoH,qBAAqB,CAACzE,KAAK,CAAC,CAAC,CAAC,EAAEA,KAAK,CAAC,CAAC,CAAC,CAAC;MACpD,OAAOtB,GAAG,GAAC,CAAC;IACb,CAAC,MAAI;MAAC;MACL,OAAO,CAAC,CAAC;IACV;EACD;EACA,OAAO,CAAC,CAAC;AACV;;AAEA;AACA;AACA;AACA,SAAS6C,iBAAiBA,CAAClF,MAAM,EAAC,CAElC;AACAkF,iBAAiB,CAACtE,SAAS,GAAG;EAC7B8F,UAAU,EAAC,SAAXA,UAAUA,CAAUzC,OAAO,EAAC;IAC3B,IAAG,CAAC/D,cAAc,CAACgH,IAAI,CAACjD,OAAO,CAAC,EAAC;MAChC,MAAM,IAAIuC,KAAK,CAAC,kBAAkB,GAACvC,OAAO,CAAC;IAC5C;IACA,IAAI,CAACA,OAAO,GAAGA,OAAO;EACvB,CAAC;EACDwC,GAAG,EAAC,SAAJA,GAAGA,CAAUE,KAAK,EAACN,KAAK,EAACT,MAAM,EAAC;IAC/B,IAAG,CAAC1F,cAAc,CAACgH,IAAI,CAACP,KAAK,CAAC,EAAC;MAC9B,MAAM,IAAIH,KAAK,CAAC,oBAAoB,GAACG,KAAK,CAAC;IAC5C;IACA,IAAI,CAAC,IAAI,CAACxD,MAAM,EAAE,CAAC,GAAG;MAACwD,KAAK,EAACA,KAAK;MAACN,KAAK,EAACA,KAAK;MAACT,MAAM,EAACA;IAAM,CAAC;EAC9D,CAAC;EACDzC,MAAM,EAAC,CAAC;EACRkF,YAAY,EAAC,SAAbA,YAAYA,CAAU1C,CAAC,EAAC;IAAC,OAAO,IAAI,CAACA,CAAC,CAAC,CAACjB,SAAS;EAAA,CAAC;EAClD4D,UAAU,EAAC,SAAXA,UAAUA,CAAU3C,CAAC,EAAC;IAAC,OAAO,IAAI,CAACA,CAAC,CAAC,CAAClD,OAAO;EAAA,CAAC;EAC9C8F,QAAQ,EAAC,SAATA,QAAQA,CAAU5C,CAAC,EAAC;IAAC,OAAO,IAAI,CAACA,CAAC,CAAC,CAACgB,KAAK;EAAA,CAAC;EAC1C6B,MAAM,EAAC,SAAPA,MAAMA,CAAU7C,CAAC,EAAC;IAAC,OAAO,IAAI,CAACA,CAAC,CAAC,CAAClB,GAAG;EAAA,CAAC;EACtCgE,QAAQ,EAAC,SAATA,QAAQA,CAAU9C,CAAC,EAAC;IAAC,OAAO,IAAI,CAACA,CAAC,CAAC,CAACU,KAAK;EAAA;EAC1C;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACA,CAAC;AAKD,SAASqC,WAAWA,CAACC,IAAI,EAACC,MAAM,EAAC;EAChCD,IAAI,CAACE,SAAS,GAAGD,MAAM;EACvB,OAAOD,IAAI;AACZ;AACA,IAAG,EAAED,WAAW,CAAC,CAAC,CAAC,EAACA,WAAW,CAAC9H,SAAS,CAAC,YAAY8H,WAAW,CAAC,EAAC;EAClEA,WAAW,GAAG,SAAdA,WAAWA,CAAYC,IAAI,EAACC,MAAM,EAAC;IAClC,SAAShG,CAACA,CAAA,EAAE,CAAC;IAAC;IACdA,CAAC,CAAChC,SAAS,GAAGgI,MAAM;IACpBhG,CAAC,GAAG,IAAIA,CAAC,CAAC,CAAC;IACX,KAAIgG,MAAM,IAAID,IAAI,EAAC;MAClB/F,CAAC,CAACgG,MAAM,CAAC,GAAGD,IAAI,CAACC,MAAM,CAAC;IACzB;IACA,OAAOhG,CAAC;EACT,CAAC;AACF;AAEA,SAASiF,KAAKA,CAAC7H,MAAM,EAACsC,KAAK,EAAC;EAC3B,IAAIqB,KAAK;EACT,IAAImF,GAAG,GAAG,EAAE;EACZ,IAAIC,GAAG,GAAG,4CAA4C;EACtDA,GAAG,CAACC,SAAS,GAAG1G,KAAK;EACrByG,GAAG,CAAC/F,IAAI,CAAChD,MAAM,CAAC,CAAC;EACjB,OAAM2D,KAAK,GAAGoF,GAAG,CAAC/F,IAAI,CAAChD,MAAM,CAAC,EAAC;IAC9B8I,GAAG,CAAChE,IAAI,CAACnB,KAAK,CAAC;IACf,IAAGA,KAAK,CAAC,CAAC,CAAC,EAAC,OAAOmF,GAAG;EACvB;AACD;AAEAG,OAAO,CAACtI,SAAS,GAAGA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}