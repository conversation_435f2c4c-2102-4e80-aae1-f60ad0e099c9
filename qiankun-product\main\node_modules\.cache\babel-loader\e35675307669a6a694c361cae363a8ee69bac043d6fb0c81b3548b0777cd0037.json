{"ast": null, "code": "import { ref, computed, onMounted, onUnmounted } from 'vue';\nimport { useStore } from 'vuex';\nimport { IntelligentAssistant } from 'common/js/system_var.js';\nimport GlobalAiChat from '../../GlobalAiChat/GlobalAiChat';\nvar __default__ = {\n  name: 'GlobalFloatingWindow'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    },\n    disabled: {\n      type: Boolean,\n      default: false\n    }\n  },\n  emits: ['update:modelValue'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var store = useStore();\n    var props = __props;\n    var emit = __emit;\n    var AiChatElShow = computed(function () {\n      return store.state.AiChatElShow;\n    });\n    var elShow = computed({\n      get() {\n        return props.modelValue;\n      },\n      set(value) {\n        emit('update:modelValue', value);\n      }\n    });\n    var disabled = computed(function () {\n      return props.disabled;\n    });\n    var elContainer = ref();\n    var isDragging = ref(false);\n    var positionX = ref(window.innerWidth - 100);\n    var positionY = ref(window.innerHeight - 268);\n    var startPosX = ref(0);\n    var startPosY = ref(0);\n    // const elShow = ref(false)\n    var isActive = ref(false);\n    var isActiveType = ref(false);\n    var handleClick = function handleClick() {\n      if (!isDragging.value && !isActive.value) elShow.value = !elShow.value;\n      isActive.value = false;\n    };\n    var handleMousedown = function handleMousedown(event) {\n      isDragging.value = true;\n      startPosX.value = event.clientX - positionX.value;\n      startPosY.value = event.clientY - positionY.value;\n      document.addEventListener('mousemove', handleMousemove);\n      document.addEventListener('mouseup', _handleMouseup);\n    };\n    var _handleMouseup = function handleMouseup() {\n      if (isDragging.value) {\n        isDragging.value = false;\n        document.removeEventListener('mousemove', handleMousemove);\n        document.removeEventListener('mouseup', _handleMouseup);\n        handleAutoSnap();\n      }\n    };\n    var handleMousemove = function handleMousemove(event) {\n      if (isDragging.value) {\n        elShow.value = false;\n        isActive.value = true;\n        var newX = event.clientX - startPosX.value;\n        var newY = event.clientY - startPosY.value;\n        var windowWidth = window.innerWidth;\n        var windowHeight = window.innerHeight;\n        var elWidth = elContainer.value.offsetWidth;\n        var elHeight = elContainer.value.offsetHeight;\n        positionX.value = Math.max(0, Math.min(windowWidth - elWidth, newX));\n        positionY.value = Math.max(0, Math.min(windowHeight - elHeight, newY));\n        elContainer.value.style.top = positionY.value + 'px';\n        elContainer.value.style.left = positionX.value + 'px';\n      }\n    };\n    // 自动吸附到最近的侧边\n    var handleAutoSnap = function handleAutoSnap() {\n      var windowWidth = window.innerWidth;\n      var elWidth = elContainer.value.offsetWidth;\n      if (positionX.value + elWidth / 2 < windowWidth / 2) {\n        positionX.value = 0;\n        isActiveType.value = true;\n      } else {\n        isActiveType.value = false;\n        positionX.value = windowWidth - elWidth;\n      }\n      elContainer.value.style.top = positionY.value + 'px';\n      elContainer.value.style.left = positionX.value + 'px';\n    };\n    onMounted(function () {\n      positionX.value = window.innerWidth - elContainer.value.offsetWidth;\n      elContainer.value.style.top = positionY.value + 'px';\n      elContainer.value.style.left = positionX.value + 'px';\n      window.addEventListener('resize', handleAutoSnap);\n    });\n    onUnmounted(function () {\n      window.removeEventListener('resize', handleAutoSnap);\n    });\n    var __returned__ = {\n      store,\n      props,\n      emit,\n      AiChatElShow,\n      elShow,\n      disabled,\n      elContainer,\n      isDragging,\n      positionX,\n      positionY,\n      startPosX,\n      startPosY,\n      isActive,\n      isActiveType,\n      handleClick,\n      handleMousedown,\n      handleMouseup: _handleMouseup,\n      handleMousemove,\n      handleAutoSnap,\n      ref,\n      computed,\n      onMounted,\n      onUnmounted,\n      get useStore() {\n        return useStore;\n      },\n      get IntelligentAssistant() {\n        return IntelligentAssistant;\n      },\n      get GlobalAiChat() {\n        return GlobalAiChat;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "computed", "onMounted", "onUnmounted", "useStore", "IntelligentAssistant", "GlobalAiChat", "__default__", "name", "store", "props", "__props", "emit", "__emit", "AiChatElShow", "state", "elShow", "get", "modelValue", "set", "value", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "isDragging", "positionX", "window", "innerWidth", "positionY", "innerHeight", "startPosX", "startPosY", "isActive", "isActiveType", "handleClick", "handleMousedown", "event", "clientX", "clientY", "document", "addEventListener", "handleMousemove", "handleMouseup", "removeEventListener", "handleAutoSnap", "newX", "newY", "windowWidth", "windowHeight", "<PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "elHeight", "offsetHeight", "Math", "max", "min", "style", "top", "left"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/LayoutContainer/components/GlobalFloatingWindow.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalFloatingWindow forbidSelect\" ref=\"elContainer\" @mousedown=\"handleMousedown\" @mouseup=\"handleMouseup\"\r\n    v-show=\"AiChatElShow && (!disabled || !elShow)\">\r\n    <el-popover :visible=\"elShow\" :disabled=\"disabled\" placement=\"left\" popper-class=\"GlobalFloatingWindowPopover\">\r\n      <template #reference>\r\n        <div :class=\"['GlobalFloatingWindowBody', isActiveType ? 'is-left ' : 'is-right', { 'is-active': isActive }]\"\r\n          @click=\"handleClick\">\r\n          <el-image :src=\"IntelligentAssistant\" loading=\"lazy\" fit=\"cover\" draggable=\"false\" />\r\n        </div>\r\n      </template>\r\n      <GlobalAiChat v-model=\"elShow\" v-if=\"!disabled\"></GlobalAiChat>\r\n    </el-popover>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalFloatingWindow' }\r\n</script>\r\n<script setup>\r\nimport { ref, computed, onMounted, onUnmounted } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport { IntelligentAssistant } from 'common/js/system_var.js'\r\nimport GlobalAiChat from '../../GlobalAiChat/GlobalAiChat'\r\nconst store = useStore()\r\nconst props = defineProps({\r\n  modelValue: { type: Boolean, default: false },\r\n  disabled: { type: Boolean, default: false }\r\n})\r\nconst emit = defineEmits(['update:modelValue'])\r\nconst AiChatElShow = computed(() => store.state.AiChatElShow)\r\nconst elShow = computed({\r\n  get () { return props.modelValue },\r\n  set (value) { emit('update:modelValue', value) }\r\n})\r\nconst disabled = computed(() => props.disabled)\r\nconst elContainer = ref()\r\nconst isDragging = ref(false)\r\nconst positionX = ref(window.innerWidth - 100)\r\nconst positionY = ref(window.innerHeight - 268)\r\nconst startPosX = ref(0)\r\nconst startPosY = ref(0)\r\n// const elShow = ref(false)\r\nconst isActive = ref(false)\r\nconst isActiveType = ref(false)\r\nconst handleClick = () => {\r\n  if (!isDragging.value && !isActive.value) elShow.value = !elShow.value\r\n  isActive.value = false\r\n}\r\nconst handleMousedown = (event) => {\r\n  isDragging.value = true\r\n  startPosX.value = event.clientX - positionX.value\r\n  startPosY.value = event.clientY - positionY.value\r\n  document.addEventListener('mousemove', handleMousemove)\r\n  document.addEventListener('mouseup', handleMouseup)\r\n}\r\nconst handleMouseup = () => {\r\n  if (isDragging.value) {\r\n    isDragging.value = false\r\n    document.removeEventListener('mousemove', handleMousemove)\r\n    document.removeEventListener('mouseup', handleMouseup)\r\n    handleAutoSnap()\r\n  }\r\n}\r\nconst handleMousemove = (event) => {\r\n  if (isDragging.value) {\r\n    elShow.value = false\r\n    isActive.value = true\r\n    const newX = event.clientX - startPosX.value\r\n    const newY = event.clientY - startPosY.value\r\n    const windowWidth = window.innerWidth\r\n    const windowHeight = window.innerHeight\r\n    const elWidth = elContainer.value.offsetWidth\r\n    const elHeight = elContainer.value.offsetHeight\r\n    positionX.value = Math.max(0, Math.min(windowWidth - elWidth, newX))\r\n    positionY.value = Math.max(0, Math.min(windowHeight - elHeight, newY))\r\n    elContainer.value.style.top = positionY.value + 'px'\r\n    elContainer.value.style.left = positionX.value + 'px'\r\n  }\r\n}\r\n// 自动吸附到最近的侧边\r\nconst handleAutoSnap = () => {\r\n  const windowWidth = window.innerWidth\r\n  const elWidth = elContainer.value.offsetWidth\r\n  if (positionX.value + elWidth / 2 < windowWidth / 2) {\r\n    positionX.value = 0\r\n    isActiveType.value = true\r\n  } else {\r\n    isActiveType.value = false\r\n    positionX.value = windowWidth - elWidth\r\n  }\r\n  elContainer.value.style.top = positionY.value + 'px'\r\n  elContainer.value.style.left = positionX.value + 'px'\r\n}\r\nonMounted(() => {\r\n  positionX.value = window.innerWidth - elContainer.value.offsetWidth\r\n  elContainer.value.style.top = positionY.value + 'px'\r\n  elContainer.value.style.left = positionX.value + 'px'\r\n  window.addEventListener('resize', handleAutoSnap)\r\n})\r\nonUnmounted(() => {\r\n  window.removeEventListener('resize', handleAutoSnap)\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.GlobalFloatingWindow {\r\n  width: 66px;\r\n  height: 52px;\r\n  position: fixed;\r\n  cursor: pointer;\r\n  z-index: 99;\r\n\r\n  .GlobalFloatingWindowBody {\r\n    width: 66px;\r\n    height: 52px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    background: #fff;\r\n    box-shadow: var(--zy-el-box-shadow-light);\r\n    overflow: hidden;\r\n\r\n    &.is-left {\r\n      padding-right: 6px;\r\n      border-radius: 0 26px 26px 0;\r\n    }\r\n\r\n    &.is-right {\r\n      padding-left: 6px;\r\n      border-radius: 26px 0 0 26px;\r\n    }\r\n\r\n    &.is-active {\r\n      padding: 0;\r\n      border-radius: 28px;\r\n    }\r\n\r\n    .zy-el-image {\r\n      width: 44px;\r\n      height: 44px;\r\n    }\r\n  }\r\n}\r\n\r\n.GlobalFloatingWindowPopover {\r\n  width: 580px !important;\r\n  height: 80% !important;\r\n  padding: 0 !important;\r\n}\r\n</style>"], "mappings": "AAkBA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,KAAK;AAC3D,SAASC,QAAQ,QAAQ,MAAM;AAC/B,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,OAAOC,YAAY,MAAM,iCAAiC;AAN1D,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAuB,CAAC;;;;;;;;;;;;;;;;;IAO/C,IAAMC,KAAK,GAAGL,QAAQ,CAAC,CAAC;IACxB,IAAMM,KAAK,GAAGC,OAGZ;IACF,IAAMC,IAAI,GAAGC,MAAkC;IAC/C,IAAMC,YAAY,GAAGb,QAAQ,CAAC;MAAA,OAAMQ,KAAK,CAACM,KAAK,CAACD,YAAY;IAAA,EAAC;IAC7D,IAAME,MAAM,GAAGf,QAAQ,CAAC;MACtBgB,GAAGA,CAAA,EAAI;QAAE,OAAOP,KAAK,CAACQ,UAAU;MAAC,CAAC;MAClCC,GAAGA,CAAEC,KAAK,EAAE;QAAER,IAAI,CAAC,mBAAmB,EAAEQ,KAAK,CAAC;MAAC;IACjD,CAAC,CAAC;IACF,IAAMC,QAAQ,GAAGpB,QAAQ,CAAC;MAAA,OAAMS,KAAK,CAACW,QAAQ;IAAA,EAAC;IAC/C,IAAMC,WAAW,GAAGtB,GAAG,CAAC,CAAC;IACzB,IAAMuB,UAAU,GAAGvB,GAAG,CAAC,KAAK,CAAC;IAC7B,IAAMwB,SAAS,GAAGxB,GAAG,CAACyB,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;IAC9C,IAAMC,SAAS,GAAG3B,GAAG,CAACyB,MAAM,CAACG,WAAW,GAAG,GAAG,CAAC;IAC/C,IAAMC,SAAS,GAAG7B,GAAG,CAAC,CAAC,CAAC;IACxB,IAAM8B,SAAS,GAAG9B,GAAG,CAAC,CAAC,CAAC;IACxB;IACA,IAAM+B,QAAQ,GAAG/B,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMgC,YAAY,GAAGhC,GAAG,CAAC,KAAK,CAAC;IAC/B,IAAMiC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB,IAAI,CAACV,UAAU,CAACH,KAAK,IAAI,CAACW,QAAQ,CAACX,KAAK,EAAEJ,MAAM,CAACI,KAAK,GAAG,CAACJ,MAAM,CAACI,KAAK;MACtEW,QAAQ,CAACX,KAAK,GAAG,KAAK;IACxB,CAAC;IACD,IAAMc,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,KAAK,EAAK;MACjCZ,UAAU,CAACH,KAAK,GAAG,IAAI;MACvBS,SAAS,CAACT,KAAK,GAAGe,KAAK,CAACC,OAAO,GAAGZ,SAAS,CAACJ,KAAK;MACjDU,SAAS,CAACV,KAAK,GAAGe,KAAK,CAACE,OAAO,GAAGV,SAAS,CAACP,KAAK;MACjDkB,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEC,eAAe,CAAC;MACvDF,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEE,cAAa,CAAC;IACrD,CAAC;IACD,IAAMA,cAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1B,IAAIlB,UAAU,CAACH,KAAK,EAAE;QACpBG,UAAU,CAACH,KAAK,GAAG,KAAK;QACxBkB,QAAQ,CAACI,mBAAmB,CAAC,WAAW,EAAEF,eAAe,CAAC;QAC1DF,QAAQ,CAACI,mBAAmB,CAAC,SAAS,EAAED,cAAa,CAAC;QACtDE,cAAc,CAAC,CAAC;MAClB;IACF,CAAC;IACD,IAAMH,eAAe,GAAG,SAAlBA,eAAeA,CAAIL,KAAK,EAAK;MACjC,IAAIZ,UAAU,CAACH,KAAK,EAAE;QACpBJ,MAAM,CAACI,KAAK,GAAG,KAAK;QACpBW,QAAQ,CAACX,KAAK,GAAG,IAAI;QACrB,IAAMwB,IAAI,GAAGT,KAAK,CAACC,OAAO,GAAGP,SAAS,CAACT,KAAK;QAC5C,IAAMyB,IAAI,GAAGV,KAAK,CAACE,OAAO,GAAGP,SAAS,CAACV,KAAK;QAC5C,IAAM0B,WAAW,GAAGrB,MAAM,CAACC,UAAU;QACrC,IAAMqB,YAAY,GAAGtB,MAAM,CAACG,WAAW;QACvC,IAAMoB,OAAO,GAAG1B,WAAW,CAACF,KAAK,CAAC6B,WAAW;QAC7C,IAAMC,QAAQ,GAAG5B,WAAW,CAACF,KAAK,CAAC+B,YAAY;QAC/C3B,SAAS,CAACJ,KAAK,GAAGgC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACR,WAAW,GAAGE,OAAO,EAAEJ,IAAI,CAAC,CAAC;QACpEjB,SAAS,CAACP,KAAK,GAAGgC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACP,YAAY,GAAGG,QAAQ,EAAEL,IAAI,CAAC,CAAC;QACtEvB,WAAW,CAACF,KAAK,CAACmC,KAAK,CAACC,GAAG,GAAG7B,SAAS,CAACP,KAAK,GAAG,IAAI;QACpDE,WAAW,CAACF,KAAK,CAACmC,KAAK,CAACE,IAAI,GAAGjC,SAAS,CAACJ,KAAK,GAAG,IAAI;MACvD;IACF,CAAC;IACD;IACA,IAAMuB,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3B,IAAMG,WAAW,GAAGrB,MAAM,CAACC,UAAU;MACrC,IAAMsB,OAAO,GAAG1B,WAAW,CAACF,KAAK,CAAC6B,WAAW;MAC7C,IAAIzB,SAAS,CAACJ,KAAK,GAAG4B,OAAO,GAAG,CAAC,GAAGF,WAAW,GAAG,CAAC,EAAE;QACnDtB,SAAS,CAACJ,KAAK,GAAG,CAAC;QACnBY,YAAY,CAACZ,KAAK,GAAG,IAAI;MAC3B,CAAC,MAAM;QACLY,YAAY,CAACZ,KAAK,GAAG,KAAK;QAC1BI,SAAS,CAACJ,KAAK,GAAG0B,WAAW,GAAGE,OAAO;MACzC;MACA1B,WAAW,CAACF,KAAK,CAACmC,KAAK,CAACC,GAAG,GAAG7B,SAAS,CAACP,KAAK,GAAG,IAAI;MACpDE,WAAW,CAACF,KAAK,CAACmC,KAAK,CAACE,IAAI,GAAGjC,SAAS,CAACJ,KAAK,GAAG,IAAI;IACvD,CAAC;IACDlB,SAAS,CAAC,YAAM;MACdsB,SAAS,CAACJ,KAAK,GAAGK,MAAM,CAACC,UAAU,GAAGJ,WAAW,CAACF,KAAK,CAAC6B,WAAW;MACnE3B,WAAW,CAACF,KAAK,CAACmC,KAAK,CAACC,GAAG,GAAG7B,SAAS,CAACP,KAAK,GAAG,IAAI;MACpDE,WAAW,CAACF,KAAK,CAACmC,KAAK,CAACE,IAAI,GAAGjC,SAAS,CAACJ,KAAK,GAAG,IAAI;MACrDK,MAAM,CAACc,gBAAgB,CAAC,QAAQ,EAAEI,cAAc,CAAC;IACnD,CAAC,CAAC;IACFxC,WAAW,CAAC,YAAM;MAChBsB,MAAM,CAACiB,mBAAmB,CAAC,QAAQ,EAAEC,cAAc,CAAC;IACtD,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}