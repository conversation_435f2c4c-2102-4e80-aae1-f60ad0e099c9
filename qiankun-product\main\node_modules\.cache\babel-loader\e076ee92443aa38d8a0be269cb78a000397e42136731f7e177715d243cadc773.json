{"ast": null, "code": "// Code block (4 spaces padded)\n\n'use strict';\n\nmodule.exports = function code(state, startLine, endLine /*, silent*/) {\n  var nextLine, last, token;\n  if (state.sCount[startLine] - state.blkIndent < 4) {\n    return false;\n  }\n  last = nextLine = startLine + 1;\n  while (nextLine < endLine) {\n    if (state.isEmpty(nextLine)) {\n      nextLine++;\n      continue;\n    }\n    if (state.sCount[nextLine] - state.blkIndent >= 4) {\n      nextLine++;\n      last = nextLine;\n      continue;\n    }\n    break;\n  }\n  state.line = last;\n  token = state.push('code_block', 'code', 0);\n  token.content = state.getLines(startLine, last, 4 + state.blkIndent, false) + '\\n';\n  token.map = [startLine, state.line];\n  return true;\n};", "map": {"version": 3, "names": ["module", "exports", "code", "state", "startLine", "endLine", "nextLine", "last", "token", "sCount", "blkIndent", "isEmpty", "line", "push", "content", "getLines", "map"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_block/code.js"], "sourcesContent": ["// Code block (4 spaces padded)\n\n'use strict';\n\n\nmodule.exports = function code(state, startLine, endLine/*, silent*/) {\n  var nextLine, last, token;\n\n  if (state.sCount[startLine] - state.blkIndent < 4) { return false; }\n\n  last = nextLine = startLine + 1;\n\n  while (nextLine < endLine) {\n    if (state.isEmpty(nextLine)) {\n      nextLine++;\n      continue;\n    }\n\n    if (state.sCount[nextLine] - state.blkIndent >= 4) {\n      nextLine++;\n      last = nextLine;\n      continue;\n    }\n    break;\n  }\n\n  state.line = last;\n\n  token         = state.push('code_block', 'code', 0);\n  token.content = state.getLines(startLine, last, 4 + state.blkIndent, false) + '\\n';\n  token.map     = [ startLine, state.line ];\n\n  return true;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAGZA,MAAM,CAACC,OAAO,GAAG,SAASC,IAAIA,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,eAAc;EACpE,IAAIC,QAAQ,EAAEC,IAAI,EAAEC,KAAK;EAEzB,IAAIL,KAAK,CAACM,MAAM,CAACL,SAAS,CAAC,GAAGD,KAAK,CAACO,SAAS,GAAG,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAEnEH,IAAI,GAAGD,QAAQ,GAAGF,SAAS,GAAG,CAAC;EAE/B,OAAOE,QAAQ,GAAGD,OAAO,EAAE;IACzB,IAAIF,KAAK,CAACQ,OAAO,CAACL,QAAQ,CAAC,EAAE;MAC3BA,QAAQ,EAAE;MACV;IACF;IAEA,IAAIH,KAAK,CAACM,MAAM,CAACH,QAAQ,CAAC,GAAGH,KAAK,CAACO,SAAS,IAAI,CAAC,EAAE;MACjDJ,QAAQ,EAAE;MACVC,IAAI,GAAGD,QAAQ;MACf;IACF;IACA;EACF;EAEAH,KAAK,CAACS,IAAI,GAAGL,IAAI;EAEjBC,KAAK,GAAWL,KAAK,CAACU,IAAI,CAAC,YAAY,EAAE,MAAM,EAAE,CAAC,CAAC;EACnDL,KAAK,CAACM,OAAO,GAAGX,KAAK,CAACY,QAAQ,CAACX,SAAS,EAAEG,IAAI,EAAE,CAAC,GAAGJ,KAAK,CAACO,SAAS,EAAE,KAAK,CAAC,GAAG,IAAI;EAClFF,KAAK,CAACQ,GAAG,GAAO,CAAEZ,SAAS,EAAED,KAAK,CAACS,IAAI,CAAE;EAEzC,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}