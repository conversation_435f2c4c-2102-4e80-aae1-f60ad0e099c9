{"ast": null, "code": "'use strict';\n\nvar alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split(''),\n  length = 64,\n  map = {},\n  seed = 0,\n  i = 0,\n  prev;\n\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nfunction encode(num) {\n  var encoded = '';\n  do {\n    encoded = alphabet[num % length] + encoded;\n    num = Math.floor(num / length);\n  } while (num > 0);\n  return encoded;\n}\n\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nfunction decode(str) {\n  var decoded = 0;\n  for (i = 0; i < str.length; i++) {\n    decoded = decoded * length + map[str.charAt(i)];\n  }\n  return decoded;\n}\n\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nfunction yeast() {\n  var now = encode(+new Date());\n  if (now !== prev) return seed = 0, prev = now;\n  return now + '.' + encode(seed++);\n}\n\n//\n// Map each character to its index.\n//\nfor (; i < length; i++) map[alphabet[i]] = i;\n\n//\n// Expose the `yeast`, `encode` and `decode` functions.\n//\nyeast.encode = encode;\nyeast.decode = decode;\nmodule.exports = yeast;", "map": {"version": 3, "names": ["alphabet", "split", "length", "map", "seed", "i", "prev", "encode", "num", "encoded", "Math", "floor", "decode", "str", "decoded", "char<PERSON>t", "yeast", "now", "Date", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/yeast@0.1.2/node_modules/yeast/index.js"], "sourcesContent": ["'use strict';\n\nvar alphabet = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_'.split('')\n  , length = 64\n  , map = {}\n  , seed = 0\n  , i = 0\n  , prev;\n\n/**\n * Return a string representing the specified number.\n *\n * @param {Number} num The number to convert.\n * @returns {String} The string representation of the number.\n * @api public\n */\nfunction encode(num) {\n  var encoded = '';\n\n  do {\n    encoded = alphabet[num % length] + encoded;\n    num = Math.floor(num / length);\n  } while (num > 0);\n\n  return encoded;\n}\n\n/**\n * Return the integer value specified by the given string.\n *\n * @param {String} str The string to convert.\n * @returns {Number} The integer value represented by the string.\n * @api public\n */\nfunction decode(str) {\n  var decoded = 0;\n\n  for (i = 0; i < str.length; i++) {\n    decoded = decoded * length + map[str.charAt(i)];\n  }\n\n  return decoded;\n}\n\n/**\n * Yeast: A tiny growing id generator.\n *\n * @returns {String} A unique id.\n * @api public\n */\nfunction yeast() {\n  var now = encode(+new Date());\n\n  if (now !== prev) return seed = 0, prev = now;\n  return now +'.'+ encode(seed++);\n}\n\n//\n// Map each character to its index.\n//\nfor (; i < length; i++) map[alphabet[i]] = i;\n\n//\n// Expose the `yeast`, `encode` and `decode` functions.\n//\nyeast.encode = encode;\nyeast.decode = decode;\nmodule.exports = yeast;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAG,kEAAkE,CAACC,KAAK,CAAC,EAAE,CAAC;EACvFC,MAAM,GAAG,EAAE;EACXC,GAAG,GAAG,CAAC,CAAC;EACRC,IAAI,GAAG,CAAC;EACRC,CAAC,GAAG,CAAC;EACLC,IAAI;;AAER;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,MAAMA,CAACC,GAAG,EAAE;EACnB,IAAIC,OAAO,GAAG,EAAE;EAEhB,GAAG;IACDA,OAAO,GAAGT,QAAQ,CAACQ,GAAG,GAAGN,MAAM,CAAC,GAAGO,OAAO;IAC1CD,GAAG,GAAGE,IAAI,CAACC,KAAK,CAACH,GAAG,GAAGN,MAAM,CAAC;EAChC,CAAC,QAAQM,GAAG,GAAG,CAAC;EAEhB,OAAOC,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,MAAMA,CAACC,GAAG,EAAE;EACnB,IAAIC,OAAO,GAAG,CAAC;EAEf,KAAKT,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGQ,GAAG,CAACX,MAAM,EAAEG,CAAC,EAAE,EAAE;IAC/BS,OAAO,GAAGA,OAAO,GAAGZ,MAAM,GAAGC,GAAG,CAACU,GAAG,CAACE,MAAM,CAACV,CAAC,CAAC,CAAC;EACjD;EAEA,OAAOS,OAAO;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,KAAKA,CAAA,EAAG;EACf,IAAIC,GAAG,GAAGV,MAAM,CAAC,CAAC,IAAIW,IAAI,CAAC,CAAC,CAAC;EAE7B,IAAID,GAAG,KAAKX,IAAI,EAAE,OAAOF,IAAI,GAAG,CAAC,EAAEE,IAAI,GAAGW,GAAG;EAC7C,OAAOA,GAAG,GAAE,GAAG,GAAEV,MAAM,CAACH,IAAI,EAAE,CAAC;AACjC;;AAEA;AACA;AACA;AACA,OAAOC,CAAC,GAAGH,MAAM,EAAEG,CAAC,EAAE,EAAEF,GAAG,CAACH,QAAQ,CAACK,CAAC,CAAC,CAAC,GAAGA,CAAC;;AAE5C;AACA;AACA;AACAW,KAAK,CAACT,MAAM,GAAGA,MAAM;AACrBS,KAAK,CAACJ,MAAM,GAAGA,MAAM;AACrBO,MAAM,CAACC,OAAO,GAAGJ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}