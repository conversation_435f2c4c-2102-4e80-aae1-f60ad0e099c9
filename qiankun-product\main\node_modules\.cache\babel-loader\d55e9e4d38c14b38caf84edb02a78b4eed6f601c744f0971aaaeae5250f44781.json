{"ast": null, "code": "var dom = require('./dom');\nexports.DOMImplementation = dom.DOMImplementation;\nexports.XMLSerializer = dom.XMLSerializer;\nexports.DOMParser = require('./dom-parser').DOMParser;", "map": {"version": 3, "names": ["dom", "require", "exports", "DOMImplementation", "XMLSerializer", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/@xmldom+xmldom@0.8.10/node_modules/@xmldom/xmldom/lib/index.js"], "sourcesContent": ["var dom = require('./dom')\nexports.DOMImplementation = dom.DOMImplementation\nexports.XMLSerializer = dom.XMLSerializer\nexports.DOMParser = require('./dom-parser').DOMParser\n"], "mappings": "AAAA,IAAIA,GAAG,GAAGC,OAAO,CAAC,OAAO,CAAC;AAC1BC,OAAO,CAACC,iBAAiB,GAAGH,GAAG,CAACG,iBAAiB;AACjDD,OAAO,CAACE,aAAa,GAAGJ,GAAG,CAACI,aAAa;AACzCF,OAAO,CAACG,SAAS,GAAGJ,OAAO,CAAC,cAAc,CAAC,CAACI,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}