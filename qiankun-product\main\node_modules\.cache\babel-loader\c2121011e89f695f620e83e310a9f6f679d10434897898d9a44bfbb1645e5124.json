{"ast": null, "code": "\"use strict\";\n\nvar _require = require(\"./errors.js\"),\n  throwUnimplementedTagType = _require.throwUnimplementedTagType,\n  XTScopeParserError = _require.XTScopeParserError;\nvar getResolvedId = require(\"./get-resolved-id.js\");\nfunction moduleRender(part, options) {\n  var moduleRendered;\n  for (var i = 0, l = options.modules.length; i < l; i++) {\n    var _module = options.modules[i];\n    moduleRendered = _module.render(part, options);\n    if (moduleRendered) {\n      return moduleRendered;\n    }\n  }\n  return false;\n}\nfunction render(options) {\n  var baseNullGetter = options.baseNullGetter;\n  var compiled = options.compiled,\n    scopeManager = options.scopeManager;\n  options.nullGetter = function (part, sm) {\n    return baseNullGetter(part, sm || scopeManager);\n  };\n  var errors = [];\n  var parts = compiled.map(function (part, i) {\n    options.index = i;\n    options.resolvedId = getResolvedId(part, options);\n    var moduleRendered;\n    try {\n      moduleRendered = moduleRender(part, options);\n    } catch (e) {\n      if (e instanceof XTScopeParserError) {\n        errors.push(e);\n        return part;\n      }\n      throw e;\n    }\n    if (moduleRendered) {\n      if (moduleRendered.errors) {\n        Array.prototype.push.apply(errors, moduleRendered.errors);\n      }\n      return moduleRendered;\n    }\n    if (part.type === \"content\" || part.type === \"tag\") {\n      return part;\n    }\n    throwUnimplementedTagType(part, i);\n  }).reduce(function (parts, _ref) {\n    var value = _ref.value;\n    if (value instanceof Array) {\n      for (var i = 0, len = value.length; i < len; i++) {\n        parts.push(value[i]);\n      }\n    } else if (value) {\n      parts.push(value);\n    }\n    return parts;\n  }, []);\n  return {\n    errors: errors,\n    parts: parts\n  };\n}\nmodule.exports = render;", "map": {"version": 3, "names": ["_require", "require", "throwUnimplementedTagType", "XTScopeParserError", "getResolvedId", "moduleRender", "part", "options", "moduleRendered", "i", "l", "modules", "length", "_module", "render", "baseNullGetter", "compiled", "scopeManager", "nullGetter", "sm", "errors", "parts", "map", "index", "resolvedId", "e", "push", "Array", "prototype", "apply", "type", "reduce", "_ref", "value", "len", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/docxtemplater@3.52.0/node_modules/docxtemplater/js/render.js"], "sourcesContent": ["\"use strict\";\n\nvar _require = require(\"./errors.js\"),\n  throwUnimplementedTagType = _require.throwUnimplementedTagType,\n  XTScopeParserError = _require.XTScopeParserError;\nvar getResolvedId = require(\"./get-resolved-id.js\");\nfunction moduleRender(part, options) {\n  var moduleRendered;\n  for (var i = 0, l = options.modules.length; i < l; i++) {\n    var _module = options.modules[i];\n    moduleRendered = _module.render(part, options);\n    if (moduleRendered) {\n      return moduleRendered;\n    }\n  }\n  return false;\n}\nfunction render(options) {\n  var baseNullGetter = options.baseNullGetter;\n  var compiled = options.compiled,\n    scopeManager = options.scopeManager;\n  options.nullGetter = function (part, sm) {\n    return baseNullGetter(part, sm || scopeManager);\n  };\n  var errors = [];\n  var parts = compiled.map(function (part, i) {\n    options.index = i;\n    options.resolvedId = getResolvedId(part, options);\n    var moduleRendered;\n    try {\n      moduleRendered = moduleRender(part, options);\n    } catch (e) {\n      if (e instanceof XTScopeParserError) {\n        errors.push(e);\n        return part;\n      }\n      throw e;\n    }\n    if (moduleRendered) {\n      if (moduleRendered.errors) {\n        Array.prototype.push.apply(errors, moduleRendered.errors);\n      }\n      return moduleRendered;\n    }\n    if (part.type === \"content\" || part.type === \"tag\") {\n      return part;\n    }\n    throwUnimplementedTagType(part, i);\n  }).reduce(function (parts, _ref) {\n    var value = _ref.value;\n    if (value instanceof Array) {\n      for (var i = 0, len = value.length; i < len; i++) {\n        parts.push(value[i]);\n      }\n    } else if (value) {\n      parts.push(value);\n    }\n    return parts;\n  }, []);\n  return {\n    errors: errors,\n    parts: parts\n  };\n}\nmodule.exports = render;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAa,CAAC;EACnCC,yBAAyB,GAAGF,QAAQ,CAACE,yBAAyB;EAC9DC,kBAAkB,GAAGH,QAAQ,CAACG,kBAAkB;AAClD,IAAIC,aAAa,GAAGH,OAAO,CAAC,sBAAsB,CAAC;AACnD,SAASI,YAAYA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACnC,IAAIC,cAAc;EAClB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,OAAO,CAACI,OAAO,CAACC,MAAM,EAAEH,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;IACtD,IAAII,OAAO,GAAGN,OAAO,CAACI,OAAO,CAACF,CAAC,CAAC;IAChCD,cAAc,GAAGK,OAAO,CAACC,MAAM,CAACR,IAAI,EAAEC,OAAO,CAAC;IAC9C,IAAIC,cAAc,EAAE;MAClB,OAAOA,cAAc;IACvB;EACF;EACA,OAAO,KAAK;AACd;AACA,SAASM,MAAMA,CAACP,OAAO,EAAE;EACvB,IAAIQ,cAAc,GAAGR,OAAO,CAACQ,cAAc;EAC3C,IAAIC,QAAQ,GAAGT,OAAO,CAACS,QAAQ;IAC7BC,YAAY,GAAGV,OAAO,CAACU,YAAY;EACrCV,OAAO,CAACW,UAAU,GAAG,UAAUZ,IAAI,EAAEa,EAAE,EAAE;IACvC,OAAOJ,cAAc,CAACT,IAAI,EAAEa,EAAE,IAAIF,YAAY,CAAC;EACjD,CAAC;EACD,IAAIG,MAAM,GAAG,EAAE;EACf,IAAIC,KAAK,GAAGL,QAAQ,CAACM,GAAG,CAAC,UAAUhB,IAAI,EAAEG,CAAC,EAAE;IAC1CF,OAAO,CAACgB,KAAK,GAAGd,CAAC;IACjBF,OAAO,CAACiB,UAAU,GAAGpB,aAAa,CAACE,IAAI,EAAEC,OAAO,CAAC;IACjD,IAAIC,cAAc;IAClB,IAAI;MACFA,cAAc,GAAGH,YAAY,CAACC,IAAI,EAAEC,OAAO,CAAC;IAC9C,CAAC,CAAC,OAAOkB,CAAC,EAAE;MACV,IAAIA,CAAC,YAAYtB,kBAAkB,EAAE;QACnCiB,MAAM,CAACM,IAAI,CAACD,CAAC,CAAC;QACd,OAAOnB,IAAI;MACb;MACA,MAAMmB,CAAC;IACT;IACA,IAAIjB,cAAc,EAAE;MAClB,IAAIA,cAAc,CAACY,MAAM,EAAE;QACzBO,KAAK,CAACC,SAAS,CAACF,IAAI,CAACG,KAAK,CAACT,MAAM,EAAEZ,cAAc,CAACY,MAAM,CAAC;MAC3D;MACA,OAAOZ,cAAc;IACvB;IACA,IAAIF,IAAI,CAACwB,IAAI,KAAK,SAAS,IAAIxB,IAAI,CAACwB,IAAI,KAAK,KAAK,EAAE;MAClD,OAAOxB,IAAI;IACb;IACAJ,yBAAyB,CAACI,IAAI,EAAEG,CAAC,CAAC;EACpC,CAAC,CAAC,CAACsB,MAAM,CAAC,UAAUV,KAAK,EAAEW,IAAI,EAAE;IAC/B,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IACtB,IAAIA,KAAK,YAAYN,KAAK,EAAE;MAC1B,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEyB,GAAG,GAAGD,KAAK,CAACrB,MAAM,EAAEH,CAAC,GAAGyB,GAAG,EAAEzB,CAAC,EAAE,EAAE;QAChDY,KAAK,CAACK,IAAI,CAACO,KAAK,CAACxB,CAAC,CAAC,CAAC;MACtB;IACF,CAAC,MAAM,IAAIwB,KAAK,EAAE;MAChBZ,KAAK,CAACK,IAAI,CAACO,KAAK,CAAC;IACnB;IACA,OAAOZ,KAAK;EACd,CAAC,EAAE,EAAE,CAAC;EACN,OAAO;IACLD,MAAM,EAAEA,MAAM;IACdC,KAAK,EAAEA;EACT,CAAC;AACH;AACAc,MAAM,CAACC,OAAO,GAAGtB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}