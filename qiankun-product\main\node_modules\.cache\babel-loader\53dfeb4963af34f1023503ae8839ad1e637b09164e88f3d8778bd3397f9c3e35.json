{"ast": null, "code": "import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';\nimport markdownIt from './markdown-it';\nimport { deepCloneAndUpdate, buildCodeBlock } from './code-block.js';\nvar __default__ = {\n  name: 'GlobalMarkdown'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    modelValue: {\n      type: String,\n      default: ''\n    },\n    content: {\n      type: String,\n      default: ''\n    },\n    onLinkClick: {\n      type: Function,\n      default: null\n    }\n  },\n  emits: ['update:modelValue', 'update'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    var props = __props;\n    var emit = __emit;\n    var elRef = ref();\n    var htmlValue = computed({\n      get() {\n        return props.modelValue;\n      },\n      set(value) {\n        emit('update:modelValue', value);\n      }\n    });\n    var htmlData = ref('');\n    var isRendering = false;\n    var renderQueue = ref([]);\n    var renderTimeout = null;\n\n    /** 检查元素是否有效 */\n    var isValidElement = function isValidElement() {\n      return elRef.value && elRef.value.isConnected;\n    };\n\n    /** Step 4. 渲染markdown的 HTML Element. */\n    var renderMarkdown = function renderMarkdown(data) {\n      try {\n        if (!isValidElement()) return;\n        var tmpDiv = document.createElement('div');\n        tmpDiv.innerHTML = markdownIt.render(data);\n        buildCodeBlock(tmpDiv);\n        deepCloneAndUpdate(elRef.value, tmpDiv);\n      } catch (error) {\n        console.error('Error rendering markdown:', error);\n      }\n    };\n\n    /** Step 3. 处理异步渲染 */\n    var _processRenderQueue = function processRenderQueue() {\n      if (!isValidElement() || renderQueue.value.length === 0) {\n        isRendering = false;\n        htmlValue.value = htmlData.value;\n        return;\n      }\n      var data = renderQueue.value.shift();\n      renderMarkdown(data);\n\n      // 使用 requestAnimationFrame 替代 setTimeout 以获得更好的性能\n      renderTimeout = requestAnimationFrame(function () {\n        _processRenderQueue();\n      });\n    };\n\n    /** Step 2. 异步队列控制渲染 */\n    var enqueueRender = function enqueueRender(data) {\n      if (!isValidElement()) return;\n      htmlData.value += data;\n      renderQueue.value.push(htmlData.value);\n      if (!isRendering) {\n        isRendering = true;\n        _processRenderQueue();\n      }\n    };\n\n    /** Step 1. 清空内容 */\n    var clearContent = function clearContent() {\n      htmlData.value = '';\n      htmlValue.value = '';\n      elRef.value.innerHTML = '';\n    };\n\n    // 监听内容变化\n    watch(function () {\n      return props.content;\n    }, function () {\n      nextTick(function () {\n        if (!isValidElement()) return;\n        elRef.value.innerHTML = '';\n        htmlData.value = props.content;\n        htmlValue.value = props.content;\n        if (props.content) {\n          emit('update');\n          renderMarkdown(props.content);\n        }\n      });\n    }, {\n      immediate: true\n    });\n    var handleClick = function handleClick(e) {\n      var target = e.target;\n      if (target.tagName.toLowerCase() === 'a') {\n        if (props.onLinkClick) {\n          e.preventDefault();\n          e.stopPropagation();\n          props.onLinkClick({\n            href: target.href,\n            text: target.textContent,\n            event: e\n          });\n        }\n      }\n    };\n\n    // 组件挂载时的初始化\n    onMounted(function () {\n      if (props.content) {\n        emit('update');\n        renderMarkdown(props.content);\n      }\n    });\n\n    // 组件卸载时的清理\n    onUnmounted(function () {\n      if (renderTimeout) {\n        cancelAnimationFrame(renderTimeout);\n      }\n      renderQueue.value = [];\n      isRendering = false;\n    });\n    __expose({\n      elRef,\n      enqueueRender,\n      clearContent\n    });\n    var __returned__ = {\n      props,\n      emit,\n      elRef,\n      htmlValue,\n      htmlData,\n      get isRendering() {\n        return isRendering;\n      },\n      set isRendering(v) {\n        isRendering = v;\n      },\n      renderQueue,\n      get renderTimeout() {\n        return renderTimeout;\n      },\n      set renderTimeout(v) {\n        renderTimeout = v;\n      },\n      isValidElement,\n      renderMarkdown,\n      processRenderQueue: _processRenderQueue,\n      enqueueRender,\n      clearContent,\n      handleClick,\n      ref,\n      computed,\n      watch,\n      nextTick,\n      onMounted,\n      onUnmounted,\n      get markdownIt() {\n        return markdownIt;\n      },\n      get deepCloneAndUpdate() {\n        return deepCloneAndUpdate;\n      },\n      get buildCodeBlock() {\n        return buildCodeBlock;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "computed", "watch", "nextTick", "onMounted", "onUnmounted", "markdownIt", "deepCloneAndUpdate", "buildCodeBlock", "__default__", "name", "props", "__props", "emit", "__emit", "elRef", "htmlValue", "get", "modelValue", "set", "value", "htmlData", "isRendering", "renderQueue", "renderTimeout", "isValidElement", "isConnected", "renderMarkdown", "data", "tmpDiv", "document", "createElement", "innerHTML", "render", "error", "console", "processRenderQueue", "length", "shift", "requestAnimationFrame", "enqueueRender", "push", "clearContent", "content", "immediate", "handleClick", "e", "target", "tagName", "toLowerCase", "onLinkClick", "preventDefault", "stopPropagation", "href", "text", "textContent", "event", "cancelAnimationFrame", "__expose"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/components/global-markdown/global-markdown.vue"], "sourcesContent": ["<template>\r\n  <div class=\"global-markdown\" ref=\"elRef\" @click=\"handleClick\"></div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalMarkdown' }\r\n</script>\r\n<script setup>\r\nimport { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue'\r\nimport markdownIt from './markdown-it'\r\nimport { deepCloneAndUpdate, buildCodeBlock } from './code-block.js'\r\n\r\nconst props = defineProps({\r\n  modelValue: { type: String, default: '' },\r\n  content: { type: String, default: '' },\r\n  onLinkClick: { type: Function, default: null }\r\n})\r\n\r\nconst emit = defineEmits(['update:modelValue', 'update'])\r\nconst elRef = ref()\r\nconst htmlValue = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(value) {\r\n    emit('update:modelValue', value)\r\n  }\r\n})\r\nconst htmlData = ref('')\r\nlet isRendering = false\r\nconst renderQueue = ref([])\r\nlet renderTimeout = null\r\n\r\n/** 检查元素是否有效 */\r\nconst isValidElement = () => {\r\n  return elRef.value && elRef.value.isConnected\r\n}\r\n\r\n/** Step 4. 渲染markdown的 HTML Element. */\r\nconst renderMarkdown = (data) => {\r\n  try {\r\n    if (!isValidElement()) return\r\n\r\n    const tmpDiv = document.createElement('div')\r\n    tmpDiv.innerHTML = markdownIt.render(data)\r\n    buildCodeBlock(tmpDiv)\r\n    deepCloneAndUpdate(elRef.value, tmpDiv)\r\n  } catch (error) {\r\n    console.error('Error rendering markdown:', error)\r\n  }\r\n}\r\n\r\n/** Step 3. 处理异步渲染 */\r\nconst processRenderQueue = () => {\r\n  if (!isValidElement() || renderQueue.value.length === 0) {\r\n    isRendering = false\r\n    htmlValue.value = htmlData.value\r\n    return\r\n  }\r\n\r\n  const data = renderQueue.value.shift()\r\n  renderMarkdown(data)\r\n\r\n  // 使用 requestAnimationFrame 替代 setTimeout 以获得更好的性能\r\n  renderTimeout = requestAnimationFrame(() => {\r\n    processRenderQueue()\r\n  })\r\n}\r\n\r\n/** Step 2. 异步队列控制渲染 */\r\nconst enqueueRender = (data) => {\r\n  if (!isValidElement()) return\r\n\r\n  htmlData.value += data\r\n  renderQueue.value.push(htmlData.value)\r\n\r\n  if (!isRendering) {\r\n    isRendering = true\r\n    processRenderQueue()\r\n  }\r\n}\r\n\r\n/** Step 1. 清空内容 */\r\nconst clearContent = () => {\r\n  htmlData.value = ''\r\n  htmlValue.value = ''\r\n  elRef.value.innerHTML = ''\r\n}\r\n\r\n// 监听内容变化\r\nwatch(\r\n  () => props.content,\r\n  () => {\r\n    nextTick(() => {\r\n      if (!isValidElement()) return\r\n\r\n      elRef.value.innerHTML = ''\r\n      htmlData.value = props.content\r\n      htmlValue.value = props.content\r\n\r\n      if (props.content) {\r\n        emit('update')\r\n        renderMarkdown(props.content)\r\n      }\r\n    })\r\n  },\r\n  { immediate: true }\r\n)\r\n\r\nconst handleClick = (e) => {\r\n  const target = e.target\r\n  if (target.tagName.toLowerCase() === 'a') {\r\n    if (props.onLinkClick) {\r\n      e.preventDefault()\r\n      e.stopPropagation()\r\n      props.onLinkClick({\r\n        href: target.href,\r\n        text: target.textContent,\r\n        event: e\r\n      })\r\n    }\r\n  }\r\n}\r\n\r\n// 组件挂载时的初始化\r\nonMounted(() => {\r\n  if (props.content) {\r\n    emit('update')\r\n    renderMarkdown(props.content)\r\n  }\r\n})\r\n\r\n// 组件卸载时的清理\r\nonUnmounted(() => {\r\n  if (renderTimeout) {\r\n    cancelAnimationFrame(renderTimeout)\r\n  }\r\n  renderQueue.value = []\r\n  isRendering = false\r\n})\r\n\r\ndefineExpose({ elRef, enqueueRender, clearContent })\r\n</script>\r\n<style lang=\"scss\">\r\n@import './index.scss';\r\n\r\n.global-markdown {\r\n  width: 100%;\r\n  background: #fff;\r\n  line-height: var(--zy-line-height);\r\n  font-size: var(--zy-text-font-size);\r\n\r\n  img {\r\n    display: block;\r\n    margin: 15px auto 15px;\r\n    border-radius: 6px;\r\n    width: 100%;\r\n    cursor: pointer;\r\n    cursor: zoom-in;\r\n    box-shadow: 0 1px 15px rgba(27, 31, 35, 0.15), 0 0 1px rgba(106, 115, 125, 0.35);\r\n  }\r\n\r\n  h1 code,\r\n  h2 code,\r\n  h3 code,\r\n  h4 code,\r\n  h5 code,\r\n  h6 code,\r\n  p > code,\r\n  li > code,\r\n  table code {\r\n    color: #c7254e;\r\n    font-family: consolas !important;\r\n    vertical-align: middle;\r\n    margin: 0 3px;\r\n    background-color: #f9f2f4 !important;\r\n    line-height: var(--zy-line-height);\r\n    font-size: var(--zy-text-font-size);\r\n    padding: 0.2em 0.3em !important;\r\n    border-radius: 3px !important;\r\n    border: 1px solid #f9f2f4 !important;\r\n  }\r\n\r\n  p {\r\n    color: var(--text-color);\r\n    line-height: var(--zy-line-height);\r\n    font-size: var(--zy-text-font-size);\r\n  }\r\n\r\n  h1,\r\n  h2,\r\n  h3,\r\n  h4,\r\n  h5,\r\n  h6 {\r\n    overflow: hidden;\r\n    -webkit-line-clamp: 4;\r\n    text-overflow: ellipsis;\r\n    display: -webkit-box;\r\n    -webkit-box-orient: vertical;\r\n    color: #1f2d3d;\r\n    transition: all 0.2s ease-out;\r\n  }\r\n\r\n  h4,\r\n  h5,\r\n  h6 {\r\n    font-size: 16px;\r\n  }\r\n\r\n  h1 {\r\n    font-size: 26px;\r\n    margin: 10px 0;\r\n  }\r\n\r\n  h2 {\r\n    font-size: 22px;\r\n  }\r\n\r\n  h3 {\r\n    font-size: 18px;\r\n  }\r\n\r\n  /* 代码样式 */\r\n  pre {\r\n    white-space: pre;\r\n    position: relative;\r\n    border-radius: 7px;\r\n    color: #bababa;\r\n    background-color: #282c34;\r\n    font-size: 14px;\r\n    padding: 0;\r\n\r\n    code {\r\n      border: none;\r\n      border-radius: 7px;\r\n      font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace !important;\r\n      line-height: 21px;\r\n    }\r\n  }\r\n\r\n  kbd {\r\n    background-color: #f7f7f7;\r\n    color: #222325;\r\n    border-radius: 0.25rem;\r\n    border: 1px solid #cbcccd;\r\n    box-shadow: 0 2px 0 1px #cbcccd;\r\n    cursor: default;\r\n    font-family: Arial, sans-serif;\r\n    font-size: 0.75em;\r\n    line-height: 1;\r\n    min-width: 0.75rem;\r\n    padding: 2px 5px;\r\n    position: relative;\r\n    top: -1px;\r\n\r\n    &:hover {\r\n      box-shadow: 0 1px 0 0.5px #cbcccd;\r\n      top: 1px;\r\n    }\r\n  }\r\n\r\n  a {\r\n    color: #2d8cf0;\r\n    text-decoration: none;\r\n    transition: all 0.3s ease;\r\n    position: relative;\r\n\r\n    &::after {\r\n      content: '';\r\n      display: block;\r\n      width: 0;\r\n      height: 1px;\r\n      position: absolute;\r\n      left: 0;\r\n      bottom: -2px;\r\n      background: #2d8cf0;\r\n      transition: all 0.3s ease-in-out;\r\n    }\r\n\r\n    &:hover::after {\r\n      width: 100%;\r\n    }\r\n  }\r\n\r\n  hr {\r\n    position: relative;\r\n    margin: 20px 0;\r\n    border: 2px dashed #bfe4fb;\r\n    width: 100%;\r\n    box-sizing: content-box;\r\n    height: 0;\r\n    overflow: visible;\r\n    box-sizing: border-box;\r\n  }\r\n\r\n  hr::before {\r\n    position: absolute;\r\n    top: -11px;\r\n    left: 2%;\r\n    z-index: 1;\r\n    color: #bfe4fb;\r\n    content: '✂';\r\n    font-size: 21px;\r\n    line-height: 1;\r\n    -webkit-transition: all 1s ease-in-out;\r\n    -moz-transition: all 1s ease-in-out;\r\n    -o-transition: all 1s ease-in-out;\r\n    -ms-transition: all 1s ease-in-out;\r\n    transition: all 1s ease-in-out;\r\n  }\r\n\r\n  hr:hover::before {\r\n    left: calc(98% - 20px);\r\n  }\r\n\r\n  table {\r\n    font-size: 15px;\r\n    width: 100%;\r\n    margin: 15px 0px;\r\n    display: block;\r\n    overflow-x: auto;\r\n    border: none;\r\n    border-collapse: collapse;\r\n    border-spacing: 0;\r\n\r\n    &::-webkit-scrollbar {\r\n      height: 4px !important;\r\n    }\r\n\r\n    th {\r\n      background: #bfe4fb;\r\n      border: 1px solid #a6d6f5;\r\n      white-space: nowrap;\r\n      font-weight: 400;\r\n      padding: 6px 15px;\r\n      min-width: 100px;\r\n    }\r\n\r\n    td {\r\n      border: 1px solid #a6d6f5;\r\n      padding: 6px 15px;\r\n      min-width: 100px;\r\n    }\r\n  }\r\n\r\n  ul,\r\n  ol {\r\n    padding-left: 2em;\r\n\r\n    li {\r\n      margin: 4px 0px;\r\n    }\r\n  }\r\n\r\n  ul li {\r\n    list-style: circle;\r\n\r\n    &::marker {\r\n      transition: all 0.4s;\r\n      /* color: #49b1f5; */\r\n      color: var(--theme-color);\r\n      font-weight: 600;\r\n      font-size: 1.05em;\r\n    }\r\n\r\n    &:hover::marker {\r\n      color: #ff7242;\r\n    }\r\n  }\r\n\r\n  blockquote {\r\n    border: none;\r\n    margin: 15px 0px;\r\n    color: inherit;\r\n    border-radius: 4px;\r\n    padding: 1px 15px;\r\n    border-left: 4px solid var(--theme-color);\r\n    background-color: #f8f8f8;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAOA,SAASA,GAAG,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,KAAK;AAC5E,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,kBAAkB,EAAEC,cAAc,QAAQ,iBAAiB;AALpE,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAiB,CAAC;;;;;;;;;;;;;;;;;;;;IAOzC,IAAMC,KAAK,GAAGC,OAIZ;IAEF,IAAMC,IAAI,GAAGC,MAA4C;IACzD,IAAMC,KAAK,GAAGf,GAAG,CAAC,CAAC;IACnB,IAAMgB,SAAS,GAAGf,QAAQ,CAAC;MACzBgB,GAAGA,CAAA,EAAG;QACJ,OAAON,KAAK,CAACO,UAAU;MACzB,CAAC;MACDC,GAAGA,CAACC,KAAK,EAAE;QACTP,IAAI,CAAC,mBAAmB,EAAEO,KAAK,CAAC;MAClC;IACF,CAAC,CAAC;IACF,IAAMC,QAAQ,GAAGrB,GAAG,CAAC,EAAE,CAAC;IACxB,IAAIsB,WAAW,GAAG,KAAK;IACvB,IAAMC,WAAW,GAAGvB,GAAG,CAAC,EAAE,CAAC;IAC3B,IAAIwB,aAAa,GAAG,IAAI;;IAExB;IACA,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3B,OAAOV,KAAK,CAACK,KAAK,IAAIL,KAAK,CAACK,KAAK,CAACM,WAAW;IAC/C,CAAC;;IAED;IACA,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIC,IAAI,EAAK;MAC/B,IAAI;QACF,IAAI,CAACH,cAAc,CAAC,CAAC,EAAE;QAEvB,IAAMI,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;QAC5CF,MAAM,CAACG,SAAS,GAAG1B,UAAU,CAAC2B,MAAM,CAACL,IAAI,CAAC;QAC1CpB,cAAc,CAACqB,MAAM,CAAC;QACtBtB,kBAAkB,CAACQ,KAAK,CAACK,KAAK,EAAES,MAAM,CAAC;MACzC,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;IACF,CAAC;;IAED;IACA,IAAME,mBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/B,IAAI,CAACX,cAAc,CAAC,CAAC,IAAIF,WAAW,CAACH,KAAK,CAACiB,MAAM,KAAK,CAAC,EAAE;QACvDf,WAAW,GAAG,KAAK;QACnBN,SAAS,CAACI,KAAK,GAAGC,QAAQ,CAACD,KAAK;QAChC;MACF;MAEA,IAAMQ,IAAI,GAAGL,WAAW,CAACH,KAAK,CAACkB,KAAK,CAAC,CAAC;MACtCX,cAAc,CAACC,IAAI,CAAC;;MAEpB;MACAJ,aAAa,GAAGe,qBAAqB,CAAC,YAAM;QAC1CH,mBAAkB,CAAC,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC;;IAED;IACA,IAAMI,aAAa,GAAG,SAAhBA,aAAaA,CAAIZ,IAAI,EAAK;MAC9B,IAAI,CAACH,cAAc,CAAC,CAAC,EAAE;MAEvBJ,QAAQ,CAACD,KAAK,IAAIQ,IAAI;MACtBL,WAAW,CAACH,KAAK,CAACqB,IAAI,CAACpB,QAAQ,CAACD,KAAK,CAAC;MAEtC,IAAI,CAACE,WAAW,EAAE;QAChBA,WAAW,GAAG,IAAI;QAClBc,mBAAkB,CAAC,CAAC;MACtB;IACF,CAAC;;IAED;IACA,IAAMM,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MACzBrB,QAAQ,CAACD,KAAK,GAAG,EAAE;MACnBJ,SAAS,CAACI,KAAK,GAAG,EAAE;MACpBL,KAAK,CAACK,KAAK,CAACY,SAAS,GAAG,EAAE;IAC5B,CAAC;;IAED;IACA9B,KAAK,CACH;MAAA,OAAMS,KAAK,CAACgC,OAAO;IAAA,GACnB,YAAM;MACJxC,QAAQ,CAAC,YAAM;QACb,IAAI,CAACsB,cAAc,CAAC,CAAC,EAAE;QAEvBV,KAAK,CAACK,KAAK,CAACY,SAAS,GAAG,EAAE;QAC1BX,QAAQ,CAACD,KAAK,GAAGT,KAAK,CAACgC,OAAO;QAC9B3B,SAAS,CAACI,KAAK,GAAGT,KAAK,CAACgC,OAAO;QAE/B,IAAIhC,KAAK,CAACgC,OAAO,EAAE;UACjB9B,IAAI,CAAC,QAAQ,CAAC;UACdc,cAAc,CAAChB,KAAK,CAACgC,OAAO,CAAC;QAC/B;MACF,CAAC,CAAC;IACJ,CAAC,EACD;MAAEC,SAAS,EAAE;IAAK,CACpB,CAAC;IAED,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,CAAC,EAAK;MACzB,IAAMC,MAAM,GAAGD,CAAC,CAACC,MAAM;MACvB,IAAIA,MAAM,CAACC,OAAO,CAACC,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;QACxC,IAAItC,KAAK,CAACuC,WAAW,EAAE;UACrBJ,CAAC,CAACK,cAAc,CAAC,CAAC;UAClBL,CAAC,CAACM,eAAe,CAAC,CAAC;UACnBzC,KAAK,CAACuC,WAAW,CAAC;YAChBG,IAAI,EAAEN,MAAM,CAACM,IAAI;YACjBC,IAAI,EAAEP,MAAM,CAACQ,WAAW;YACxBC,KAAK,EAAEV;UACT,CAAC,CAAC;QACJ;MACF;IACF,CAAC;;IAED;IACA1C,SAAS,CAAC,YAAM;MACd,IAAIO,KAAK,CAACgC,OAAO,EAAE;QACjB9B,IAAI,CAAC,QAAQ,CAAC;QACdc,cAAc,CAAChB,KAAK,CAACgC,OAAO,CAAC;MAC/B;IACF,CAAC,CAAC;;IAEF;IACAtC,WAAW,CAAC,YAAM;MAChB,IAAImB,aAAa,EAAE;QACjBiC,oBAAoB,CAACjC,aAAa,CAAC;MACrC;MACAD,WAAW,CAACH,KAAK,GAAG,EAAE;MACtBE,WAAW,GAAG,KAAK;IACrB,CAAC,CAAC;IAEFoC,QAAY,CAAC;MAAE3C,KAAK;MAAEyB,aAAa;MAAEE;IAAa,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}