{"name": "hls.js", "license": "Apache-2.0", "description": "JavaScript HLS client using MediaSourceExtension", "homepage": "https://github.com/video-dev/hls.js", "authors": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/video-dev/hls.js"}, "bugs": {"url": "https://github.com/video-dev/hls.js/issues"}, "main": "./dist/hls.js", "module": "./dist/hls.mjs", "types": "./dist/hls.d.ts", "exports": {".": {"import": "./dist/hls.mjs", "require": "./dist/hls.js"}, "./light": {"import": "./dist/hls.light.mjs", "require": "./dist/hls.light.js"}, "./dist/*": "./dist/*", "./package.json": "./package.json"}, "files": ["dist/**/*", "src/**/*"], "publishConfig": {"access": "public"}, "scripts": {"build": "rollup --config && npm run build:types", "build:ci": "rollup --config && tsc --build tsconfig-lib.json && api-extractor run && npm run build:copy-types && es-check", "build:debug": "rollup --config --configType full --configType demo", "build:watch": "rollup --config --configType full --configType demo --watch", "build:types": "tsc --build tsconfig-lib.json && api-extractor run --local && npm run build:copy-types", "build:copy-types": "cp ./dist/hls.d.ts ./dist/hls.d.mts && cp ./dist/hls.d.ts ./dist/hls.js.d.ts", "dev": "run-p build:watch serve", "serve": "http-server -o /demo .", "docs": "doctoc ./docs/API.md && api-documenter markdown -i api-extractor -o api-extractor/api-documenter && rm api-extractor/api-documenter/index.md && npm run docs-md-to-html", "docs-md-to-html": "generate-md --layout github --input api-extractor/api-documenter --output api-docs", "lint": "eslint --cache src/ tests/ --ext .js --ext .ts", "lint:fix": "npm run lint -- --fix", "lint:quiet": "npm run lint -- --quiet", "lint:staged": "lint-staged", "prettier": "prettier --cache --write .", "prettier:verify": "prettier --cache --check .", "pretest": "npm run lint", "sanity-check": "npm run lint && npm run prettier:verify && npm run type-check && npm run build && es-check && npm run docs && npm run test:unit", "start": "npm run dev", "test": "npm run test:unit && npm run test:func", "test:unit": "karma start karma.conf.js", "test:unit:debug": "DEBUG_UNIT_TESTS=1 karma start karma.conf.js --auto-watch --no-single-run --browsers Chrome", "test:unit:watch": "karma start karma.conf.js --auto-watch --no-single-run", "test:func": "BABEL_ENV=development mocha --require @babel/register tests/functional/auto/setup.js --timeout 40000 --exit", "test:func:light": "BABEL_ENV=development HLSJS_LIGHT=1 mocha --require @babel/register tests/functional/auto/setup.js --timeout 40000 --exit", "test:func:sauce": "SAUCE=1 UA=safari OS='OS X 10.15' BABEL_ENV=development mocha --require @babel/register tests/functional/auto/setup.js --timeout 40000 --exit", "type-check": "tsc --noEmit", "type-check:watch": "npm run type-check -- --watch", "prepare": "husky"}, "devDependencies": {"@babel/core": "7.28.0", "@babel/helper-module-imports": "7.27.1", "@babel/plugin-transform-class-properties": "7.27.1", "@babel/plugin-transform-object-assign": "7.27.1", "@babel/plugin-transform-object-rest-spread": "7.28.0", "@babel/plugin-transform-optional-chaining": "7.27.1", "@babel/preset-env": "7.28.0", "@babel/preset-typescript": "7.27.1", "@babel/register": "7.27.1", "@microsoft/api-documenter": "7.26.29", "@microsoft/api-extractor": "7.52.8", "@rollup/plugin-alias": "5.1.1", "@rollup/plugin-babel": "6.0.4", "@rollup/plugin-commonjs": "28.0.6", "@rollup/plugin-node-resolve": "16.0.1", "@rollup/plugin-replace": "6.0.2", "@rollup/plugin-terser": "0.4.4", "@rollup/plugin-typescript": "12.1.4", "@svta/common-media-library": "0.17.1", "@types/chai": "4.3.20", "@types/chart.js": "2.9.41", "@types/mocha": "10.0.10", "@types/sinon-chai": "3.2.12", "@typescript-eslint/eslint-plugin": "8.38.0", "@typescript-eslint/parser": "8.38.0", "babel-loader": "10.0.0", "babel-plugin-transform-remove-console": "6.9.4", "chai": "4.5.0", "chart.js": "2.9.4", "chromedriver": "138.0.3", "doctoc": "2.2.1", "es-check": "9.1.4", "eslint": "8.57.1", "eslint-config-prettier": "10.1.8", "eslint-plugin-import": "2.32.0", "eslint-plugin-mocha": "10.5.0", "eslint-plugin-n": "17.21.0", "eslint-plugin-no-for-of-loops": "1.0.1", "eslint-plugin-promise": "7.2.1", "eventemitter3": "5.0.1", "http-server": "14.1.1", "husky": "9.1.7", "jsonpack": "1.1.5", "karma": "6.4.4", "karma-chrome-launcher": "3.2.0", "karma-coverage": "2.2.1", "karma-mocha": "2.0.1", "karma-mocha-reporter": "2.2.5", "karma-rollup-preprocessor": "github:jlmakes/karma-rollup-preprocessor#7a7268d91149307b3cf2888ee4e65ccd079955a3", "karma-sinon-chai": "2.0.2", "karma-sourcemap-loader": "0.4.0", "lint-staged": "16.1.2", "markdown-styles": "3.2.0", "micromatch": "4.0.8", "mocha": "11.7.1", "node-fetch": "3.3.2", "npm-run-all2": "8.0.4", "prettier": "3.6.2", "promise-polyfill": "8.3.0", "rollup": "4.45.1", "rollup-plugin-istanbul": "5.0.0", "sauce-connect-launcher": "1.3.2", "selenium-webdriver": "4.34.0", "semver": "7.7.2", "sinon": "19.0.5", "sinon-chai": "3.7.0", "typescript": "5.8.3", "url-toolkit": "2.2.5", "wrangler": "4.26.1"}, "version": "1.6.10"}