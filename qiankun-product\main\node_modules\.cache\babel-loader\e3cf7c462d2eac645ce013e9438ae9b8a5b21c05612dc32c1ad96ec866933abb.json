{"ast": null, "code": "import getPrototypeOf from \"./getPrototypeOf.js\";\nimport isNativeReflectConstruct from \"./isNativeReflectConstruct.js\";\nimport possibleConstructorReturn from \"./possibleConstructorReturn.js\";\nfunction _createSuper(t) {\n  var r = isNativeReflectConstruct();\n  return function () {\n    var e,\n      o = getPrototypeOf(t);\n    if (r) {\n      var s = getPrototypeOf(this).constructor;\n      e = Reflect.construct(o, arguments, s);\n    } else e = o.apply(this, arguments);\n    return possibleConstructorReturn(this, e);\n  };\n}\nexport { _createSuper as default };", "map": {"version": 3, "names": ["getPrototypeOf", "isNativeReflectConstruct", "possibleConstructorReturn", "_createSuper", "t", "r", "e", "o", "s", "constructor", "Reflect", "construct", "arguments", "apply", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/@babel+runtime@7.26.0/node_modules/@babel/runtime/helpers/esm/createSuper.js"], "sourcesContent": ["import getPrototypeOf from \"./getPrototypeOf.js\";\nimport isNativeReflectConstruct from \"./isNativeReflectConstruct.js\";\nimport possibleConstructorReturn from \"./possibleConstructorReturn.js\";\nfunction _createSuper(t) {\n  var r = isNativeReflectConstruct();\n  return function () {\n    var e,\n      o = getPrototypeOf(t);\n    if (r) {\n      var s = getPrototypeOf(this).constructor;\n      e = Reflect.construct(o, arguments, s);\n    } else e = o.apply(this, arguments);\n    return possibleConstructorReturn(this, e);\n  };\n}\nexport { _createSuper as default };"], "mappings": "AAAA,OAAOA,cAAc,MAAM,qBAAqB;AAChD,OAAOC,wBAAwB,MAAM,+BAA+B;AACpE,OAAOC,yBAAyB,MAAM,gCAAgC;AACtE,SAASC,YAAYA,CAACC,CAAC,EAAE;EACvB,IAAIC,CAAC,GAAGJ,wBAAwB,CAAC,CAAC;EAClC,OAAO,YAAY;IACjB,IAAIK,CAAC;MACHC,CAAC,GAAGP,cAAc,CAACI,CAAC,CAAC;IACvB,IAAIC,CAAC,EAAE;MACL,IAAIG,CAAC,GAAGR,cAAc,CAAC,IAAI,CAAC,CAACS,WAAW;MACxCH,CAAC,GAAGI,OAAO,CAACC,SAAS,CAACJ,CAAC,EAAEK,SAAS,EAAEJ,CAAC,CAAC;IACxC,CAAC,MAAMF,CAAC,GAAGC,CAAC,CAACM,KAAK,CAAC,IAAI,EAAED,SAAS,CAAC;IACnC,OAAOV,yBAAyB,CAAC,IAAI,EAAEI,CAAC,CAAC;EAC3C,CAAC;AACH;AACA,SAASH,YAAY,IAAIW,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}