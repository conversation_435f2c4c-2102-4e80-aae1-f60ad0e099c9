{"ast": null, "code": "'use strict';\n\n////////////////////////////////////////////////////////////////////////////////\n// Helpers\n\n// Merge objects\n//\nfunction assign(obj /*from1, from2, from3, ...*/) {\n  var sources = Array.prototype.slice.call(arguments, 1);\n  sources.forEach(function (source) {\n    if (!source) {\n      return;\n    }\n    Object.keys(source).forEach(function (key) {\n      obj[key] = source[key];\n    });\n  });\n  return obj;\n}\nfunction _class(obj) {\n  return Object.prototype.toString.call(obj);\n}\nfunction isString(obj) {\n  return _class(obj) === '[object String]';\n}\nfunction isObject(obj) {\n  return _class(obj) === '[object Object]';\n}\nfunction isRegExp(obj) {\n  return _class(obj) === '[object RegExp]';\n}\nfunction isFunction(obj) {\n  return _class(obj) === '[object Function]';\n}\nfunction escapeRE(str) {\n  return str.replace(/[.?*+^$[\\]\\\\(){}|-]/g, '\\\\$&');\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\nvar defaultOptions = {\n  fuzzyLink: true,\n  fuzzyEmail: true,\n  fuzzyIP: false\n};\nfunction isOptionsObj(obj) {\n  return Object.keys(obj || {}).reduce(function (acc, k) {\n    return acc || defaultOptions.hasOwnProperty(k);\n  }, false);\n}\nvar defaultSchemas = {\n  'http:': {\n    validate: function validate(text, pos, self) {\n      var tail = text.slice(pos);\n      if (!self.re.http) {\n        // compile lazily, because \"host\"-containing variables can change on tlds update.\n        self.re.http = new RegExp('^\\\\/\\\\/' + self.re.src_auth + self.re.src_host_port_strict + self.re.src_path, 'i');\n      }\n      if (self.re.http.test(tail)) {\n        return tail.match(self.re.http)[0].length;\n      }\n      return 0;\n    }\n  },\n  'https:': 'http:',\n  'ftp:': 'http:',\n  '//': {\n    validate: function validate(text, pos, self) {\n      var tail = text.slice(pos);\n      if (!self.re.no_http) {\n        // compile lazily, because \"host\"-containing variables can change on tlds update.\n        self.re.no_http = new RegExp('^' + self.re.src_auth +\n        // Don't allow single-level domains, because of false positives like '//test'\n        // with code comments\n        '(?:localhost|(?:(?:' + self.re.src_domain + ')\\\\.)+' + self.re.src_domain_root + ')' + self.re.src_port + self.re.src_host_terminator + self.re.src_path, 'i');\n      }\n      if (self.re.no_http.test(tail)) {\n        // should not be `://` & `///`, that protects from errors in protocol name\n        if (pos >= 3 && text[pos - 3] === ':') {\n          return 0;\n        }\n        if (pos >= 3 && text[pos - 3] === '/') {\n          return 0;\n        }\n        return tail.match(self.re.no_http)[0].length;\n      }\n      return 0;\n    }\n  },\n  'mailto:': {\n    validate: function validate(text, pos, self) {\n      var tail = text.slice(pos);\n      if (!self.re.mailto) {\n        self.re.mailto = new RegExp('^' + self.re.src_email_name + '@' + self.re.src_host_strict, 'i');\n      }\n      if (self.re.mailto.test(tail)) {\n        return tail.match(self.re.mailto)[0].length;\n      }\n      return 0;\n    }\n  }\n};\n\n/*eslint-disable max-len*/\n\n// RE pattern for 2-character tlds (autogenerated by ./support/tlds_2char_gen.js)\nvar tlds_2ch_src_re = 'a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]';\n\n// DON'T try to make PRs with changes. Extend TLDs with LinkifyIt.tlds() instead\nvar tlds_default = 'biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф'.split('|');\n\n/*eslint-enable max-len*/\n\n////////////////////////////////////////////////////////////////////////////////\n\nfunction resetScanCache(self) {\n  self.__index__ = -1;\n  self.__text_cache__ = '';\n}\nfunction createValidator(re) {\n  return function (text, pos) {\n    var tail = text.slice(pos);\n    if (re.test(tail)) {\n      return tail.match(re)[0].length;\n    }\n    return 0;\n  };\n}\nfunction createNormalizer() {\n  return function (match, self) {\n    self.normalize(match);\n  };\n}\n\n// Schemas compiler. Build regexps.\n//\nfunction compile(self) {\n  // Load & clone RE patterns.\n  var re = self.re = require('./lib/re')(self.__opts__);\n\n  // Define dynamic patterns\n  var tlds = self.__tlds__.slice();\n  self.onCompile();\n  if (!self.__tlds_replaced__) {\n    tlds.push(tlds_2ch_src_re);\n  }\n  tlds.push(re.src_xn);\n  re.src_tlds = tlds.join('|');\n  function untpl(tpl) {\n    return tpl.replace('%TLDS%', re.src_tlds);\n  }\n  re.email_fuzzy = RegExp(untpl(re.tpl_email_fuzzy), 'i');\n  re.link_fuzzy = RegExp(untpl(re.tpl_link_fuzzy), 'i');\n  re.link_no_ip_fuzzy = RegExp(untpl(re.tpl_link_no_ip_fuzzy), 'i');\n  re.host_fuzzy_test = RegExp(untpl(re.tpl_host_fuzzy_test), 'i');\n\n  //\n  // Compile each schema\n  //\n\n  var aliases = [];\n  self.__compiled__ = {}; // Reset compiled data\n\n  function schemaError(name, val) {\n    throw new Error('(LinkifyIt) Invalid schema \"' + name + '\": ' + val);\n  }\n  Object.keys(self.__schemas__).forEach(function (name) {\n    var val = self.__schemas__[name];\n\n    // skip disabled methods\n    if (val === null) {\n      return;\n    }\n    var compiled = {\n      validate: null,\n      link: null\n    };\n    self.__compiled__[name] = compiled;\n    if (isObject(val)) {\n      if (isRegExp(val.validate)) {\n        compiled.validate = createValidator(val.validate);\n      } else if (isFunction(val.validate)) {\n        compiled.validate = val.validate;\n      } else {\n        schemaError(name, val);\n      }\n      if (isFunction(val.normalize)) {\n        compiled.normalize = val.normalize;\n      } else if (!val.normalize) {\n        compiled.normalize = createNormalizer();\n      } else {\n        schemaError(name, val);\n      }\n      return;\n    }\n    if (isString(val)) {\n      aliases.push(name);\n      return;\n    }\n    schemaError(name, val);\n  });\n\n  //\n  // Compile postponed aliases\n  //\n\n  aliases.forEach(function (alias) {\n    if (!self.__compiled__[self.__schemas__[alias]]) {\n      // Silently fail on missed schemas to avoid errons on disable.\n      // schemaError(alias, self.__schemas__[alias]);\n      return;\n    }\n    self.__compiled__[alias].validate = self.__compiled__[self.__schemas__[alias]].validate;\n    self.__compiled__[alias].normalize = self.__compiled__[self.__schemas__[alias]].normalize;\n  });\n\n  //\n  // Fake record for guessed links\n  //\n  self.__compiled__[''] = {\n    validate: null,\n    normalize: createNormalizer()\n  };\n\n  //\n  // Build schema condition\n  //\n  var slist = Object.keys(self.__compiled__).filter(function (name) {\n    // Filter disabled & fake schemas\n    return name.length > 0 && self.__compiled__[name];\n  }).map(escapeRE).join('|');\n  // (?!_) cause 1.5x slowdown\n  self.re.schema_test = RegExp(\"(^|(?!_)(?:[><\\uFF5C]|\" + re.src_ZPCc + '))(' + slist + ')', 'i');\n  self.re.schema_search = RegExp(\"(^|(?!_)(?:[><\\uFF5C]|\" + re.src_ZPCc + '))(' + slist + ')', 'ig');\n  self.re.pretest = RegExp('(' + self.re.schema_test.source + ')|(' + self.re.host_fuzzy_test.source + ')|@', 'i');\n\n  //\n  // Cleanup\n  //\n\n  resetScanCache(self);\n}\n\n/**\n * class Match\n *\n * Match result. Single element of array, returned by [[LinkifyIt#match]]\n **/\nfunction Match(self, shift) {\n  var start = self.__index__,\n    end = self.__last_index__,\n    text = self.__text_cache__.slice(start, end);\n\n  /**\n   * Match#schema -> String\n   *\n   * Prefix (protocol) for matched string.\n   **/\n  this.schema = self.__schema__.toLowerCase();\n  /**\n   * Match#index -> Number\n   *\n   * First position of matched string.\n   **/\n  this.index = start + shift;\n  /**\n   * Match#lastIndex -> Number\n   *\n   * Next position after matched string.\n   **/\n  this.lastIndex = end + shift;\n  /**\n   * Match#raw -> String\n   *\n   * Matched string.\n   **/\n  this.raw = text;\n  /**\n   * Match#text -> String\n   *\n   * Notmalized text of matched string.\n   **/\n  this.text = text;\n  /**\n   * Match#url -> String\n   *\n   * Normalized url of matched string.\n   **/\n  this.url = text;\n}\nfunction createMatch(self, shift) {\n  var match = new Match(self, shift);\n  self.__compiled__[match.schema].normalize(match, self);\n  return match;\n}\n\n/**\n * class LinkifyIt\n **/\n\n/**\n * new LinkifyIt(schemas, options)\n * - schemas (Object): Optional. Additional schemas to validate (prefix/validator)\n * - options (Object): { fuzzyLink|fuzzyEmail|fuzzyIP: true|false }\n *\n * Creates new linkifier instance with optional additional schemas.\n * Can be called without `new` keyword for convenience.\n *\n * By default understands:\n *\n * - `http(s)://...` , `ftp://...`, `mailto:...` & `//...` links\n * - \"fuzzy\" links and emails (example.com, <EMAIL>).\n *\n * `schemas` is an object, where each key/value describes protocol/rule:\n *\n * - __key__ - link prefix (usually, protocol name with `:` at the end, `skype:`\n *   for example). `linkify-it` makes shure that prefix is not preceeded with\n *   alphanumeric char and symbols. Only whitespaces and punctuation allowed.\n * - __value__ - rule to check tail after link prefix\n *   - _String_ - just alias to existing rule\n *   - _Object_\n *     - _validate_ - validator function (should return matched length on success),\n *       or `RegExp`.\n *     - _normalize_ - optional function to normalize text & url of matched result\n *       (for example, for @twitter mentions).\n *\n * `options`:\n *\n * - __fuzzyLink__ - recognige URL-s without `http(s):` prefix. Default `true`.\n * - __fuzzyIP__ - allow IPs in fuzzy links above. Can conflict with some texts\n *   like version numbers. Default `false`.\n * - __fuzzyEmail__ - recognize emails without `mailto:` prefix.\n *\n **/\nfunction LinkifyIt(schemas, options) {\n  if (!(this instanceof LinkifyIt)) {\n    return new LinkifyIt(schemas, options);\n  }\n  if (!options) {\n    if (isOptionsObj(schemas)) {\n      options = schemas;\n      schemas = {};\n    }\n  }\n  this.__opts__ = assign({}, defaultOptions, options);\n\n  // Cache last tested result. Used to skip repeating steps on next `match` call.\n  this.__index__ = -1;\n  this.__last_index__ = -1; // Next scan position\n  this.__schema__ = '';\n  this.__text_cache__ = '';\n  this.__schemas__ = assign({}, defaultSchemas, schemas);\n  this.__compiled__ = {};\n  this.__tlds__ = tlds_default;\n  this.__tlds_replaced__ = false;\n  this.re = {};\n  compile(this);\n}\n\n/** chainable\n * LinkifyIt#add(schema, definition)\n * - schema (String): rule name (fixed pattern prefix)\n * - definition (String|RegExp|Object): schema definition\n *\n * Add new rule definition. See constructor description for details.\n **/\nLinkifyIt.prototype.add = function add(schema, definition) {\n  this.__schemas__[schema] = definition;\n  compile(this);\n  return this;\n};\n\n/** chainable\n * LinkifyIt#set(options)\n * - options (Object): { fuzzyLink|fuzzyEmail|fuzzyIP: true|false }\n *\n * Set recognition options for links without schema.\n **/\nLinkifyIt.prototype.set = function set(options) {\n  this.__opts__ = assign(this.__opts__, options);\n  return this;\n};\n\n/**\n * LinkifyIt#test(text) -> Boolean\n *\n * Searches linkifiable pattern and returns `true` on success or `false` on fail.\n **/\nLinkifyIt.prototype.test = function test(text) {\n  // Reset scan cache\n  this.__text_cache__ = text;\n  this.__index__ = -1;\n  if (!text.length) {\n    return false;\n  }\n  var m, ml, me, len, shift, next, re, tld_pos, at_pos;\n\n  // try to scan for link with schema - that's the most simple rule\n  if (this.re.schema_test.test(text)) {\n    re = this.re.schema_search;\n    re.lastIndex = 0;\n    while ((m = re.exec(text)) !== null) {\n      len = this.testSchemaAt(text, m[2], re.lastIndex);\n      if (len) {\n        this.__schema__ = m[2];\n        this.__index__ = m.index + m[1].length;\n        this.__last_index__ = m.index + m[0].length + len;\n        break;\n      }\n    }\n  }\n  if (this.__opts__.fuzzyLink && this.__compiled__['http:']) {\n    // guess schemaless links\n    tld_pos = text.search(this.re.host_fuzzy_test);\n    if (tld_pos >= 0) {\n      // if tld is located after found link - no need to check fuzzy pattern\n      if (this.__index__ < 0 || tld_pos < this.__index__) {\n        if ((ml = text.match(this.__opts__.fuzzyIP ? this.re.link_fuzzy : this.re.link_no_ip_fuzzy)) !== null) {\n          shift = ml.index + ml[1].length;\n          if (this.__index__ < 0 || shift < this.__index__) {\n            this.__schema__ = '';\n            this.__index__ = shift;\n            this.__last_index__ = ml.index + ml[0].length;\n          }\n        }\n      }\n    }\n  }\n  if (this.__opts__.fuzzyEmail && this.__compiled__['mailto:']) {\n    // guess schemaless emails\n    at_pos = text.indexOf('@');\n    if (at_pos >= 0) {\n      // We can't skip this check, because this cases are possible:\n      // <EMAIL>, <EMAIL>\n      if ((me = text.match(this.re.email_fuzzy)) !== null) {\n        shift = me.index + me[1].length;\n        next = me.index + me[0].length;\n        if (this.__index__ < 0 || shift < this.__index__ || shift === this.__index__ && next > this.__last_index__) {\n          this.__schema__ = 'mailto:';\n          this.__index__ = shift;\n          this.__last_index__ = next;\n        }\n      }\n    }\n  }\n  return this.__index__ >= 0;\n};\n\n/**\n * LinkifyIt#pretest(text) -> Boolean\n *\n * Very quick check, that can give false positives. Returns true if link MAY BE\n * can exists. Can be used for speed optimization, when you need to check that\n * link NOT exists.\n **/\nLinkifyIt.prototype.pretest = function pretest(text) {\n  return this.re.pretest.test(text);\n};\n\n/**\n * LinkifyIt#testSchemaAt(text, name, position) -> Number\n * - text (String): text to scan\n * - name (String): rule (schema) name\n * - position (Number): text offset to check from\n *\n * Similar to [[LinkifyIt#test]] but checks only specific protocol tail exactly\n * at given position. Returns length of found pattern (0 on fail).\n **/\nLinkifyIt.prototype.testSchemaAt = function testSchemaAt(text, schema, pos) {\n  // If not supported schema check requested - terminate\n  if (!this.__compiled__[schema.toLowerCase()]) {\n    return 0;\n  }\n  return this.__compiled__[schema.toLowerCase()].validate(text, pos, this);\n};\n\n/**\n * LinkifyIt#match(text) -> Array|null\n *\n * Returns array of found link descriptions or `null` on fail. We strongly\n * recommend to use [[LinkifyIt#test]] first, for best speed.\n *\n * ##### Result match description\n *\n * - __schema__ - link schema, can be empty for fuzzy links, or `//` for\n *   protocol-neutral  links.\n * - __index__ - offset of matched text\n * - __lastIndex__ - index of next char after mathch end\n * - __raw__ - matched text\n * - __text__ - normalized text\n * - __url__ - link, generated from matched text\n **/\nLinkifyIt.prototype.match = function match(text) {\n  var shift = 0,\n    result = [];\n\n  // Try to take previous element from cache, if .test() called before\n  if (this.__index__ >= 0 && this.__text_cache__ === text) {\n    result.push(createMatch(this, shift));\n    shift = this.__last_index__;\n  }\n\n  // Cut head if cache was used\n  var tail = shift ? text.slice(shift) : text;\n\n  // Scan string until end reached\n  while (this.test(tail)) {\n    result.push(createMatch(this, shift));\n    tail = tail.slice(this.__last_index__);\n    shift += this.__last_index__;\n  }\n  if (result.length) {\n    return result;\n  }\n  return null;\n};\n\n/** chainable\n * LinkifyIt#tlds(list [, keepOld]) -> this\n * - list (Array): list of tlds\n * - keepOld (Boolean): merge with current list if `true` (`false` by default)\n *\n * Load (or merge) new tlds list. Those are user for fuzzy links (without prefix)\n * to avoid false positives. By default this algorythm used:\n *\n * - hostname with any 2-letter root zones are ok.\n * - biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф\n *   are ok.\n * - encoded (`xn--...`) root zones are ok.\n *\n * If list is replaced, then exact match for 2-chars root zones will be checked.\n **/\nLinkifyIt.prototype.tlds = function tlds(list, keepOld) {\n  list = Array.isArray(list) ? list : [list];\n  if (!keepOld) {\n    this.__tlds__ = list.slice();\n    this.__tlds_replaced__ = true;\n    compile(this);\n    return this;\n  }\n  this.__tlds__ = this.__tlds__.concat(list).sort().filter(function (el, idx, arr) {\n    return el !== arr[idx - 1];\n  }).reverse();\n  compile(this);\n  return this;\n};\n\n/**\n * LinkifyIt#normalize(match)\n *\n * Default normalizer (if schema does not define it's own).\n **/\nLinkifyIt.prototype.normalize = function normalize(match) {\n  // Do minimal possible changes by default. Need to collect feedback prior\n  // to move forward https://github.com/markdown-it/linkify-it/issues/1\n\n  if (!match.schema) {\n    match.url = 'http://' + match.url;\n  }\n  if (match.schema === 'mailto:' && !/^mailto:/i.test(match.url)) {\n    match.url = 'mailto:' + match.url;\n  }\n};\n\n/**\n * LinkifyIt#onCompile()\n *\n * Override to modify basic RegExp-s.\n **/\nLinkifyIt.prototype.onCompile = function onCompile() {};\nmodule.exports = LinkifyIt;", "map": {"version": 3, "names": ["assign", "obj", "sources", "Array", "prototype", "slice", "call", "arguments", "for<PERSON>ach", "source", "Object", "keys", "key", "_class", "toString", "isString", "isObject", "isRegExp", "isFunction", "escapeRE", "str", "replace", "defaultOptions", "fuzzyLink", "fuzzyEmail", "fuzzyIP", "isOptionsObj", "reduce", "acc", "k", "hasOwnProperty", "defaultSchemas", "validate", "text", "pos", "self", "tail", "re", "http", "RegExp", "src_auth", "src_host_port_strict", "src_path", "test", "match", "length", "no_http", "src_domain", "src_domain_root", "src_port", "src_host_terminator", "mailto", "src_email_name", "src_host_strict", "tlds_2ch_src_re", "tlds_default", "split", "resetScanCache", "__index__", "__text_cache__", "createValidator", "createNormalizer", "normalize", "compile", "require", "__opts__", "tlds", "__tlds__", "onCompile", "__tlds_replaced__", "push", "src_xn", "src_tlds", "join", "untpl", "tpl", "email_fuzzy", "tpl_email_fuzzy", "link_fuzzy", "tpl_link_fuzzy", "link_no_ip_fuzzy", "tpl_link_no_ip_fuzzy", "host_fuzzy_test", "tpl_host_fuzzy_test", "aliases", "__compiled__", "schemaError", "name", "val", "Error", "__schemas__", "compiled", "link", "alias", "slist", "filter", "map", "schema_test", "src_ZPCc", "schema_search", "pretest", "Match", "shift", "start", "end", "__last_index__", "schema", "__schema__", "toLowerCase", "index", "lastIndex", "raw", "url", "createMatch", "LinkifyIt", "schemas", "options", "add", "definition", "set", "m", "ml", "me", "len", "next", "tld_pos", "at_pos", "exec", "testSchemaAt", "search", "indexOf", "result", "list", "keepOld", "isArray", "concat", "sort", "el", "idx", "arr", "reverse", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/linkify-it@3.0.3/node_modules/linkify-it/index.js"], "sourcesContent": ["'use strict';\n\n\n////////////////////////////////////////////////////////////////////////////////\n// Helpers\n\n// Merge objects\n//\nfunction assign(obj /*from1, from2, from3, ...*/) {\n  var sources = Array.prototype.slice.call(arguments, 1);\n\n  sources.forEach(function (source) {\n    if (!source) { return; }\n\n    Object.keys(source).forEach(function (key) {\n      obj[key] = source[key];\n    });\n  });\n\n  return obj;\n}\n\nfunction _class(obj) { return Object.prototype.toString.call(obj); }\nfunction isString(obj) { return _class(obj) === '[object String]'; }\nfunction isObject(obj) { return _class(obj) === '[object Object]'; }\nfunction isRegExp(obj) { return _class(obj) === '[object RegExp]'; }\nfunction isFunction(obj) { return _class(obj) === '[object Function]'; }\n\n\nfunction escapeRE(str) { return str.replace(/[.?*+^$[\\]\\\\(){}|-]/g, '\\\\$&'); }\n\n////////////////////////////////////////////////////////////////////////////////\n\n\nvar defaultOptions = {\n  fuzzyLink: true,\n  fuzzyEmail: true,\n  fuzzyIP: false\n};\n\n\nfunction isOptionsObj(obj) {\n  return Object.keys(obj || {}).reduce(function (acc, k) {\n    return acc || defaultOptions.hasOwnProperty(k);\n  }, false);\n}\n\n\nvar defaultSchemas = {\n  'http:': {\n    validate: function (text, pos, self) {\n      var tail = text.slice(pos);\n\n      if (!self.re.http) {\n        // compile lazily, because \"host\"-containing variables can change on tlds update.\n        self.re.http =  new RegExp(\n          '^\\\\/\\\\/' + self.re.src_auth + self.re.src_host_port_strict + self.re.src_path, 'i'\n        );\n      }\n      if (self.re.http.test(tail)) {\n        return tail.match(self.re.http)[0].length;\n      }\n      return 0;\n    }\n  },\n  'https:':  'http:',\n  'ftp:':    'http:',\n  '//':      {\n    validate: function (text, pos, self) {\n      var tail = text.slice(pos);\n\n      if (!self.re.no_http) {\n      // compile lazily, because \"host\"-containing variables can change on tlds update.\n        self.re.no_http =  new RegExp(\n          '^' +\n          self.re.src_auth +\n          // Don't allow single-level domains, because of false positives like '//test'\n          // with code comments\n          '(?:localhost|(?:(?:' + self.re.src_domain + ')\\\\.)+' + self.re.src_domain_root + ')' +\n          self.re.src_port +\n          self.re.src_host_terminator +\n          self.re.src_path,\n\n          'i'\n        );\n      }\n\n      if (self.re.no_http.test(tail)) {\n        // should not be `://` & `///`, that protects from errors in protocol name\n        if (pos >= 3 && text[pos - 3] === ':') { return 0; }\n        if (pos >= 3 && text[pos - 3] === '/') { return 0; }\n        return tail.match(self.re.no_http)[0].length;\n      }\n      return 0;\n    }\n  },\n  'mailto:': {\n    validate: function (text, pos, self) {\n      var tail = text.slice(pos);\n\n      if (!self.re.mailto) {\n        self.re.mailto =  new RegExp(\n          '^' + self.re.src_email_name + '@' + self.re.src_host_strict, 'i'\n        );\n      }\n      if (self.re.mailto.test(tail)) {\n        return tail.match(self.re.mailto)[0].length;\n      }\n      return 0;\n    }\n  }\n};\n\n/*eslint-disable max-len*/\n\n// RE pattern for 2-character tlds (autogenerated by ./support/tlds_2char_gen.js)\nvar tlds_2ch_src_re = 'a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]';\n\n// DON'T try to make PRs with changes. Extend TLDs with LinkifyIt.tlds() instead\nvar tlds_default = 'biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф'.split('|');\n\n/*eslint-enable max-len*/\n\n////////////////////////////////////////////////////////////////////////////////\n\nfunction resetScanCache(self) {\n  self.__index__ = -1;\n  self.__text_cache__   = '';\n}\n\nfunction createValidator(re) {\n  return function (text, pos) {\n    var tail = text.slice(pos);\n\n    if (re.test(tail)) {\n      return tail.match(re)[0].length;\n    }\n    return 0;\n  };\n}\n\nfunction createNormalizer() {\n  return function (match, self) {\n    self.normalize(match);\n  };\n}\n\n// Schemas compiler. Build regexps.\n//\nfunction compile(self) {\n\n  // Load & clone RE patterns.\n  var re = self.re = require('./lib/re')(self.__opts__);\n\n  // Define dynamic patterns\n  var tlds = self.__tlds__.slice();\n\n  self.onCompile();\n\n  if (!self.__tlds_replaced__) {\n    tlds.push(tlds_2ch_src_re);\n  }\n  tlds.push(re.src_xn);\n\n  re.src_tlds = tlds.join('|');\n\n  function untpl(tpl) { return tpl.replace('%TLDS%', re.src_tlds); }\n\n  re.email_fuzzy      = RegExp(untpl(re.tpl_email_fuzzy), 'i');\n  re.link_fuzzy       = RegExp(untpl(re.tpl_link_fuzzy), 'i');\n  re.link_no_ip_fuzzy = RegExp(untpl(re.tpl_link_no_ip_fuzzy), 'i');\n  re.host_fuzzy_test  = RegExp(untpl(re.tpl_host_fuzzy_test), 'i');\n\n  //\n  // Compile each schema\n  //\n\n  var aliases = [];\n\n  self.__compiled__ = {}; // Reset compiled data\n\n  function schemaError(name, val) {\n    throw new Error('(LinkifyIt) Invalid schema \"' + name + '\": ' + val);\n  }\n\n  Object.keys(self.__schemas__).forEach(function (name) {\n    var val = self.__schemas__[name];\n\n    // skip disabled methods\n    if (val === null) { return; }\n\n    var compiled = { validate: null, link: null };\n\n    self.__compiled__[name] = compiled;\n\n    if (isObject(val)) {\n      if (isRegExp(val.validate)) {\n        compiled.validate = createValidator(val.validate);\n      } else if (isFunction(val.validate)) {\n        compiled.validate = val.validate;\n      } else {\n        schemaError(name, val);\n      }\n\n      if (isFunction(val.normalize)) {\n        compiled.normalize = val.normalize;\n      } else if (!val.normalize) {\n        compiled.normalize = createNormalizer();\n      } else {\n        schemaError(name, val);\n      }\n\n      return;\n    }\n\n    if (isString(val)) {\n      aliases.push(name);\n      return;\n    }\n\n    schemaError(name, val);\n  });\n\n  //\n  // Compile postponed aliases\n  //\n\n  aliases.forEach(function (alias) {\n    if (!self.__compiled__[self.__schemas__[alias]]) {\n      // Silently fail on missed schemas to avoid errons on disable.\n      // schemaError(alias, self.__schemas__[alias]);\n      return;\n    }\n\n    self.__compiled__[alias].validate =\n      self.__compiled__[self.__schemas__[alias]].validate;\n    self.__compiled__[alias].normalize =\n      self.__compiled__[self.__schemas__[alias]].normalize;\n  });\n\n  //\n  // Fake record for guessed links\n  //\n  self.__compiled__[''] = { validate: null, normalize: createNormalizer() };\n\n  //\n  // Build schema condition\n  //\n  var slist = Object.keys(self.__compiled__)\n                      .filter(function (name) {\n                        // Filter disabled & fake schemas\n                        return name.length > 0 && self.__compiled__[name];\n                      })\n                      .map(escapeRE)\n                      .join('|');\n  // (?!_) cause 1.5x slowdown\n  self.re.schema_test   = RegExp('(^|(?!_)(?:[><\\uff5c]|' + re.src_ZPCc + '))(' + slist + ')', 'i');\n  self.re.schema_search = RegExp('(^|(?!_)(?:[><\\uff5c]|' + re.src_ZPCc + '))(' + slist + ')', 'ig');\n\n  self.re.pretest = RegExp(\n    '(' + self.re.schema_test.source + ')|(' + self.re.host_fuzzy_test.source + ')|@',\n    'i'\n  );\n\n  //\n  // Cleanup\n  //\n\n  resetScanCache(self);\n}\n\n/**\n * class Match\n *\n * Match result. Single element of array, returned by [[LinkifyIt#match]]\n **/\nfunction Match(self, shift) {\n  var start = self.__index__,\n      end   = self.__last_index__,\n      text  = self.__text_cache__.slice(start, end);\n\n  /**\n   * Match#schema -> String\n   *\n   * Prefix (protocol) for matched string.\n   **/\n  this.schema    = self.__schema__.toLowerCase();\n  /**\n   * Match#index -> Number\n   *\n   * First position of matched string.\n   **/\n  this.index     = start + shift;\n  /**\n   * Match#lastIndex -> Number\n   *\n   * Next position after matched string.\n   **/\n  this.lastIndex = end + shift;\n  /**\n   * Match#raw -> String\n   *\n   * Matched string.\n   **/\n  this.raw       = text;\n  /**\n   * Match#text -> String\n   *\n   * Notmalized text of matched string.\n   **/\n  this.text      = text;\n  /**\n   * Match#url -> String\n   *\n   * Normalized url of matched string.\n   **/\n  this.url       = text;\n}\n\nfunction createMatch(self, shift) {\n  var match = new Match(self, shift);\n\n  self.__compiled__[match.schema].normalize(match, self);\n\n  return match;\n}\n\n\n/**\n * class LinkifyIt\n **/\n\n/**\n * new LinkifyIt(schemas, options)\n * - schemas (Object): Optional. Additional schemas to validate (prefix/validator)\n * - options (Object): { fuzzyLink|fuzzyEmail|fuzzyIP: true|false }\n *\n * Creates new linkifier instance with optional additional schemas.\n * Can be called without `new` keyword for convenience.\n *\n * By default understands:\n *\n * - `http(s)://...` , `ftp://...`, `mailto:...` & `//...` links\n * - \"fuzzy\" links and emails (example.com, <EMAIL>).\n *\n * `schemas` is an object, where each key/value describes protocol/rule:\n *\n * - __key__ - link prefix (usually, protocol name with `:` at the end, `skype:`\n *   for example). `linkify-it` makes shure that prefix is not preceeded with\n *   alphanumeric char and symbols. Only whitespaces and punctuation allowed.\n * - __value__ - rule to check tail after link prefix\n *   - _String_ - just alias to existing rule\n *   - _Object_\n *     - _validate_ - validator function (should return matched length on success),\n *       or `RegExp`.\n *     - _normalize_ - optional function to normalize text & url of matched result\n *       (for example, for @twitter mentions).\n *\n * `options`:\n *\n * - __fuzzyLink__ - recognige URL-s without `http(s):` prefix. Default `true`.\n * - __fuzzyIP__ - allow IPs in fuzzy links above. Can conflict with some texts\n *   like version numbers. Default `false`.\n * - __fuzzyEmail__ - recognize emails without `mailto:` prefix.\n *\n **/\nfunction LinkifyIt(schemas, options) {\n  if (!(this instanceof LinkifyIt)) {\n    return new LinkifyIt(schemas, options);\n  }\n\n  if (!options) {\n    if (isOptionsObj(schemas)) {\n      options = schemas;\n      schemas = {};\n    }\n  }\n\n  this.__opts__           = assign({}, defaultOptions, options);\n\n  // Cache last tested result. Used to skip repeating steps on next `match` call.\n  this.__index__          = -1;\n  this.__last_index__     = -1; // Next scan position\n  this.__schema__         = '';\n  this.__text_cache__     = '';\n\n  this.__schemas__        = assign({}, defaultSchemas, schemas);\n  this.__compiled__       = {};\n\n  this.__tlds__           = tlds_default;\n  this.__tlds_replaced__  = false;\n\n  this.re = {};\n\n  compile(this);\n}\n\n\n/** chainable\n * LinkifyIt#add(schema, definition)\n * - schema (String): rule name (fixed pattern prefix)\n * - definition (String|RegExp|Object): schema definition\n *\n * Add new rule definition. See constructor description for details.\n **/\nLinkifyIt.prototype.add = function add(schema, definition) {\n  this.__schemas__[schema] = definition;\n  compile(this);\n  return this;\n};\n\n\n/** chainable\n * LinkifyIt#set(options)\n * - options (Object): { fuzzyLink|fuzzyEmail|fuzzyIP: true|false }\n *\n * Set recognition options for links without schema.\n **/\nLinkifyIt.prototype.set = function set(options) {\n  this.__opts__ = assign(this.__opts__, options);\n  return this;\n};\n\n\n/**\n * LinkifyIt#test(text) -> Boolean\n *\n * Searches linkifiable pattern and returns `true` on success or `false` on fail.\n **/\nLinkifyIt.prototype.test = function test(text) {\n  // Reset scan cache\n  this.__text_cache__ = text;\n  this.__index__      = -1;\n\n  if (!text.length) { return false; }\n\n  var m, ml, me, len, shift, next, re, tld_pos, at_pos;\n\n  // try to scan for link with schema - that's the most simple rule\n  if (this.re.schema_test.test(text)) {\n    re = this.re.schema_search;\n    re.lastIndex = 0;\n    while ((m = re.exec(text)) !== null) {\n      len = this.testSchemaAt(text, m[2], re.lastIndex);\n      if (len) {\n        this.__schema__     = m[2];\n        this.__index__      = m.index + m[1].length;\n        this.__last_index__ = m.index + m[0].length + len;\n        break;\n      }\n    }\n  }\n\n  if (this.__opts__.fuzzyLink && this.__compiled__['http:']) {\n    // guess schemaless links\n    tld_pos = text.search(this.re.host_fuzzy_test);\n    if (tld_pos >= 0) {\n      // if tld is located after found link - no need to check fuzzy pattern\n      if (this.__index__ < 0 || tld_pos < this.__index__) {\n        if ((ml = text.match(this.__opts__.fuzzyIP ? this.re.link_fuzzy : this.re.link_no_ip_fuzzy)) !== null) {\n\n          shift = ml.index + ml[1].length;\n\n          if (this.__index__ < 0 || shift < this.__index__) {\n            this.__schema__     = '';\n            this.__index__      = shift;\n            this.__last_index__ = ml.index + ml[0].length;\n          }\n        }\n      }\n    }\n  }\n\n  if (this.__opts__.fuzzyEmail && this.__compiled__['mailto:']) {\n    // guess schemaless emails\n    at_pos = text.indexOf('@');\n    if (at_pos >= 0) {\n      // We can't skip this check, because this cases are possible:\n      // <EMAIL>, <EMAIL>\n      if ((me = text.match(this.re.email_fuzzy)) !== null) {\n\n        shift = me.index + me[1].length;\n        next  = me.index + me[0].length;\n\n        if (this.__index__ < 0 || shift < this.__index__ ||\n            (shift === this.__index__ && next > this.__last_index__)) {\n          this.__schema__     = 'mailto:';\n          this.__index__      = shift;\n          this.__last_index__ = next;\n        }\n      }\n    }\n  }\n\n  return this.__index__ >= 0;\n};\n\n\n/**\n * LinkifyIt#pretest(text) -> Boolean\n *\n * Very quick check, that can give false positives. Returns true if link MAY BE\n * can exists. Can be used for speed optimization, when you need to check that\n * link NOT exists.\n **/\nLinkifyIt.prototype.pretest = function pretest(text) {\n  return this.re.pretest.test(text);\n};\n\n\n/**\n * LinkifyIt#testSchemaAt(text, name, position) -> Number\n * - text (String): text to scan\n * - name (String): rule (schema) name\n * - position (Number): text offset to check from\n *\n * Similar to [[LinkifyIt#test]] but checks only specific protocol tail exactly\n * at given position. Returns length of found pattern (0 on fail).\n **/\nLinkifyIt.prototype.testSchemaAt = function testSchemaAt(text, schema, pos) {\n  // If not supported schema check requested - terminate\n  if (!this.__compiled__[schema.toLowerCase()]) {\n    return 0;\n  }\n  return this.__compiled__[schema.toLowerCase()].validate(text, pos, this);\n};\n\n\n/**\n * LinkifyIt#match(text) -> Array|null\n *\n * Returns array of found link descriptions or `null` on fail. We strongly\n * recommend to use [[LinkifyIt#test]] first, for best speed.\n *\n * ##### Result match description\n *\n * - __schema__ - link schema, can be empty for fuzzy links, or `//` for\n *   protocol-neutral  links.\n * - __index__ - offset of matched text\n * - __lastIndex__ - index of next char after mathch end\n * - __raw__ - matched text\n * - __text__ - normalized text\n * - __url__ - link, generated from matched text\n **/\nLinkifyIt.prototype.match = function match(text) {\n  var shift = 0, result = [];\n\n  // Try to take previous element from cache, if .test() called before\n  if (this.__index__ >= 0 && this.__text_cache__ === text) {\n    result.push(createMatch(this, shift));\n    shift = this.__last_index__;\n  }\n\n  // Cut head if cache was used\n  var tail = shift ? text.slice(shift) : text;\n\n  // Scan string until end reached\n  while (this.test(tail)) {\n    result.push(createMatch(this, shift));\n\n    tail = tail.slice(this.__last_index__);\n    shift += this.__last_index__;\n  }\n\n  if (result.length) {\n    return result;\n  }\n\n  return null;\n};\n\n\n/** chainable\n * LinkifyIt#tlds(list [, keepOld]) -> this\n * - list (Array): list of tlds\n * - keepOld (Boolean): merge with current list if `true` (`false` by default)\n *\n * Load (or merge) new tlds list. Those are user for fuzzy links (without prefix)\n * to avoid false positives. By default this algorythm used:\n *\n * - hostname with any 2-letter root zones are ok.\n * - biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф\n *   are ok.\n * - encoded (`xn--...`) root zones are ok.\n *\n * If list is replaced, then exact match for 2-chars root zones will be checked.\n **/\nLinkifyIt.prototype.tlds = function tlds(list, keepOld) {\n  list = Array.isArray(list) ? list : [ list ];\n\n  if (!keepOld) {\n    this.__tlds__ = list.slice();\n    this.__tlds_replaced__ = true;\n    compile(this);\n    return this;\n  }\n\n  this.__tlds__ = this.__tlds__.concat(list)\n                                  .sort()\n                                  .filter(function (el, idx, arr) {\n                                    return el !== arr[idx - 1];\n                                  })\n                                  .reverse();\n\n  compile(this);\n  return this;\n};\n\n/**\n * LinkifyIt#normalize(match)\n *\n * Default normalizer (if schema does not define it's own).\n **/\nLinkifyIt.prototype.normalize = function normalize(match) {\n\n  // Do minimal possible changes by default. Need to collect feedback prior\n  // to move forward https://github.com/markdown-it/linkify-it/issues/1\n\n  if (!match.schema) { match.url = 'http://' + match.url; }\n\n  if (match.schema === 'mailto:' && !/^mailto:/i.test(match.url)) {\n    match.url = 'mailto:' + match.url;\n  }\n};\n\n\n/**\n * LinkifyIt#onCompile()\n *\n * Override to modify basic RegExp-s.\n **/\nLinkifyIt.prototype.onCompile = function onCompile() {\n};\n\n\nmodule.exports = LinkifyIt;\n"], "mappings": "AAAA,YAAY;;AAGZ;AACA;;AAEA;AACA;AACA,SAASA,MAAMA,CAACC,GAAG,CAAC,8BAA8B;EAChD,IAAIC,OAAO,GAAGC,KAAK,CAACC,SAAS,CAACC,KAAK,CAACC,IAAI,CAACC,SAAS,EAAE,CAAC,CAAC;EAEtDL,OAAO,CAACM,OAAO,CAAC,UAAUC,MAAM,EAAE;IAChC,IAAI,CAACA,MAAM,EAAE;MAAE;IAAQ;IAEvBC,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACD,OAAO,CAAC,UAAUI,GAAG,EAAE;MACzCX,GAAG,CAACW,GAAG,CAAC,GAAGH,MAAM,CAACG,GAAG,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOX,GAAG;AACZ;AAEA,SAASY,MAAMA,CAACZ,GAAG,EAAE;EAAE,OAAOS,MAAM,CAACN,SAAS,CAACU,QAAQ,CAACR,IAAI,CAACL,GAAG,CAAC;AAAE;AACnE,SAASc,QAAQA,CAACd,GAAG,EAAE;EAAE,OAAOY,MAAM,CAACZ,GAAG,CAAC,KAAK,iBAAiB;AAAE;AACnE,SAASe,QAAQA,CAACf,GAAG,EAAE;EAAE,OAAOY,MAAM,CAACZ,GAAG,CAAC,KAAK,iBAAiB;AAAE;AACnE,SAASgB,QAAQA,CAAChB,GAAG,EAAE;EAAE,OAAOY,MAAM,CAACZ,GAAG,CAAC,KAAK,iBAAiB;AAAE;AACnE,SAASiB,UAAUA,CAACjB,GAAG,EAAE;EAAE,OAAOY,MAAM,CAACZ,GAAG,CAAC,KAAK,mBAAmB;AAAE;AAGvE,SAASkB,QAAQA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,CAACC,OAAO,CAAC,sBAAsB,EAAE,MAAM,CAAC;AAAE;;AAE7E;;AAGA,IAAIC,cAAc,GAAG;EACnBC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE;AACX,CAAC;AAGD,SAASC,YAAYA,CAACzB,GAAG,EAAE;EACzB,OAAOS,MAAM,CAACC,IAAI,CAACV,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC0B,MAAM,CAAC,UAAUC,GAAG,EAAEC,CAAC,EAAE;IACrD,OAAOD,GAAG,IAAIN,cAAc,CAACQ,cAAc,CAACD,CAAC,CAAC;EAChD,CAAC,EAAE,KAAK,CAAC;AACX;AAGA,IAAIE,cAAc,GAAG;EACnB,OAAO,EAAE;IACPC,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAE;MACnC,IAAIC,IAAI,GAAGH,IAAI,CAAC5B,KAAK,CAAC6B,GAAG,CAAC;MAE1B,IAAI,CAACC,IAAI,CAACE,EAAE,CAACC,IAAI,EAAE;QACjB;QACAH,IAAI,CAACE,EAAE,CAACC,IAAI,GAAI,IAAIC,MAAM,CACxB,SAAS,GAAGJ,IAAI,CAACE,EAAE,CAACG,QAAQ,GAAGL,IAAI,CAACE,EAAE,CAACI,oBAAoB,GAAGN,IAAI,CAACE,EAAE,CAACK,QAAQ,EAAE,GAClF,CAAC;MACH;MACA,IAAIP,IAAI,CAACE,EAAE,CAACC,IAAI,CAACK,IAAI,CAACP,IAAI,CAAC,EAAE;QAC3B,OAAOA,IAAI,CAACQ,KAAK,CAACT,IAAI,CAACE,EAAE,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC,CAACO,MAAM;MAC3C;MACA,OAAO,CAAC;IACV;EACF,CAAC;EACD,QAAQ,EAAG,OAAO;EAClB,MAAM,EAAK,OAAO;EAClB,IAAI,EAAO;IACTb,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAE;MACnC,IAAIC,IAAI,GAAGH,IAAI,CAAC5B,KAAK,CAAC6B,GAAG,CAAC;MAE1B,IAAI,CAACC,IAAI,CAACE,EAAE,CAACS,OAAO,EAAE;QACtB;QACEX,IAAI,CAACE,EAAE,CAACS,OAAO,GAAI,IAAIP,MAAM,CAC3B,GAAG,GACHJ,IAAI,CAACE,EAAE,CAACG,QAAQ;QAChB;QACA;QACA,qBAAqB,GAAGL,IAAI,CAACE,EAAE,CAACU,UAAU,GAAG,QAAQ,GAAGZ,IAAI,CAACE,EAAE,CAACW,eAAe,GAAG,GAAG,GACrFb,IAAI,CAACE,EAAE,CAACY,QAAQ,GAChBd,IAAI,CAACE,EAAE,CAACa,mBAAmB,GAC3Bf,IAAI,CAACE,EAAE,CAACK,QAAQ,EAEhB,GACF,CAAC;MACH;MAEA,IAAIP,IAAI,CAACE,EAAE,CAACS,OAAO,CAACH,IAAI,CAACP,IAAI,CAAC,EAAE;QAC9B;QACA,IAAIF,GAAG,IAAI,CAAC,IAAID,IAAI,CAACC,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;UAAE,OAAO,CAAC;QAAE;QACnD,IAAIA,GAAG,IAAI,CAAC,IAAID,IAAI,CAACC,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;UAAE,OAAO,CAAC;QAAE;QACnD,OAAOE,IAAI,CAACQ,KAAK,CAACT,IAAI,CAACE,EAAE,CAACS,OAAO,CAAC,CAAC,CAAC,CAAC,CAACD,MAAM;MAC9C;MACA,OAAO,CAAC;IACV;EACF,CAAC;EACD,SAAS,EAAE;IACTb,QAAQ,EAAE,SAAVA,QAAQA,CAAYC,IAAI,EAAEC,GAAG,EAAEC,IAAI,EAAE;MACnC,IAAIC,IAAI,GAAGH,IAAI,CAAC5B,KAAK,CAAC6B,GAAG,CAAC;MAE1B,IAAI,CAACC,IAAI,CAACE,EAAE,CAACc,MAAM,EAAE;QACnBhB,IAAI,CAACE,EAAE,CAACc,MAAM,GAAI,IAAIZ,MAAM,CAC1B,GAAG,GAAGJ,IAAI,CAACE,EAAE,CAACe,cAAc,GAAG,GAAG,GAAGjB,IAAI,CAACE,EAAE,CAACgB,eAAe,EAAE,GAChE,CAAC;MACH;MACA,IAAIlB,IAAI,CAACE,EAAE,CAACc,MAAM,CAACR,IAAI,CAACP,IAAI,CAAC,EAAE;QAC7B,OAAOA,IAAI,CAACQ,KAAK,CAACT,IAAI,CAACE,EAAE,CAACc,MAAM,CAAC,CAAC,CAAC,CAAC,CAACN,MAAM;MAC7C;MACA,OAAO,CAAC;IACV;EACF;AACF,CAAC;;AAED;;AAEA;AACA,IAAIS,eAAe,GAAG,yVAAyV;;AAE/W;AACA,IAAIC,YAAY,GAAG,6EAA6E,CAACC,KAAK,CAAC,GAAG,CAAC;;AAE3G;;AAEA;;AAEA,SAASC,cAAcA,CAACtB,IAAI,EAAE;EAC5BA,IAAI,CAACuB,SAAS,GAAG,CAAC,CAAC;EACnBvB,IAAI,CAACwB,cAAc,GAAK,EAAE;AAC5B;AAEA,SAASC,eAAeA,CAACvB,EAAE,EAAE;EAC3B,OAAO,UAAUJ,IAAI,EAAEC,GAAG,EAAE;IAC1B,IAAIE,IAAI,GAAGH,IAAI,CAAC5B,KAAK,CAAC6B,GAAG,CAAC;IAE1B,IAAIG,EAAE,CAACM,IAAI,CAACP,IAAI,CAAC,EAAE;MACjB,OAAOA,IAAI,CAACQ,KAAK,CAACP,EAAE,CAAC,CAAC,CAAC,CAAC,CAACQ,MAAM;IACjC;IACA,OAAO,CAAC;EACV,CAAC;AACH;AAEA,SAASgB,gBAAgBA,CAAA,EAAG;EAC1B,OAAO,UAAUjB,KAAK,EAAET,IAAI,EAAE;IAC5BA,IAAI,CAAC2B,SAAS,CAAClB,KAAK,CAAC;EACvB,CAAC;AACH;;AAEA;AACA;AACA,SAASmB,OAAOA,CAAC5B,IAAI,EAAE;EAErB;EACA,IAAIE,EAAE,GAAGF,IAAI,CAACE,EAAE,GAAG2B,OAAO,CAAC,UAAU,CAAC,CAAC7B,IAAI,CAAC8B,QAAQ,CAAC;;EAErD;EACA,IAAIC,IAAI,GAAG/B,IAAI,CAACgC,QAAQ,CAAC9D,KAAK,CAAC,CAAC;EAEhC8B,IAAI,CAACiC,SAAS,CAAC,CAAC;EAEhB,IAAI,CAACjC,IAAI,CAACkC,iBAAiB,EAAE;IAC3BH,IAAI,CAACI,IAAI,CAAChB,eAAe,CAAC;EAC5B;EACAY,IAAI,CAACI,IAAI,CAACjC,EAAE,CAACkC,MAAM,CAAC;EAEpBlC,EAAE,CAACmC,QAAQ,GAAGN,IAAI,CAACO,IAAI,CAAC,GAAG,CAAC;EAE5B,SAASC,KAAKA,CAACC,GAAG,EAAE;IAAE,OAAOA,GAAG,CAACtD,OAAO,CAAC,QAAQ,EAAEgB,EAAE,CAACmC,QAAQ,CAAC;EAAE;EAEjEnC,EAAE,CAACuC,WAAW,GAAQrC,MAAM,CAACmC,KAAK,CAACrC,EAAE,CAACwC,eAAe,CAAC,EAAE,GAAG,CAAC;EAC5DxC,EAAE,CAACyC,UAAU,GAASvC,MAAM,CAACmC,KAAK,CAACrC,EAAE,CAAC0C,cAAc,CAAC,EAAE,GAAG,CAAC;EAC3D1C,EAAE,CAAC2C,gBAAgB,GAAGzC,MAAM,CAACmC,KAAK,CAACrC,EAAE,CAAC4C,oBAAoB,CAAC,EAAE,GAAG,CAAC;EACjE5C,EAAE,CAAC6C,eAAe,GAAI3C,MAAM,CAACmC,KAAK,CAACrC,EAAE,CAAC8C,mBAAmB,CAAC,EAAE,GAAG,CAAC;;EAEhE;EACA;EACA;;EAEA,IAAIC,OAAO,GAAG,EAAE;EAEhBjD,IAAI,CAACkD,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;;EAExB,SAASC,WAAWA,CAACC,IAAI,EAAEC,GAAG,EAAE;IAC9B,MAAM,IAAIC,KAAK,CAAC,8BAA8B,GAAGF,IAAI,GAAG,KAAK,GAAGC,GAAG,CAAC;EACtE;EAEA9E,MAAM,CAACC,IAAI,CAACwB,IAAI,CAACuD,WAAW,CAAC,CAAClF,OAAO,CAAC,UAAU+E,IAAI,EAAE;IACpD,IAAIC,GAAG,GAAGrD,IAAI,CAACuD,WAAW,CAACH,IAAI,CAAC;;IAEhC;IACA,IAAIC,GAAG,KAAK,IAAI,EAAE;MAAE;IAAQ;IAE5B,IAAIG,QAAQ,GAAG;MAAE3D,QAAQ,EAAE,IAAI;MAAE4D,IAAI,EAAE;IAAK,CAAC;IAE7CzD,IAAI,CAACkD,YAAY,CAACE,IAAI,CAAC,GAAGI,QAAQ;IAElC,IAAI3E,QAAQ,CAACwE,GAAG,CAAC,EAAE;MACjB,IAAIvE,QAAQ,CAACuE,GAAG,CAACxD,QAAQ,CAAC,EAAE;QAC1B2D,QAAQ,CAAC3D,QAAQ,GAAG4B,eAAe,CAAC4B,GAAG,CAACxD,QAAQ,CAAC;MACnD,CAAC,MAAM,IAAId,UAAU,CAACsE,GAAG,CAACxD,QAAQ,CAAC,EAAE;QACnC2D,QAAQ,CAAC3D,QAAQ,GAAGwD,GAAG,CAACxD,QAAQ;MAClC,CAAC,MAAM;QACLsD,WAAW,CAACC,IAAI,EAAEC,GAAG,CAAC;MACxB;MAEA,IAAItE,UAAU,CAACsE,GAAG,CAAC1B,SAAS,CAAC,EAAE;QAC7B6B,QAAQ,CAAC7B,SAAS,GAAG0B,GAAG,CAAC1B,SAAS;MACpC,CAAC,MAAM,IAAI,CAAC0B,GAAG,CAAC1B,SAAS,EAAE;QACzB6B,QAAQ,CAAC7B,SAAS,GAAGD,gBAAgB,CAAC,CAAC;MACzC,CAAC,MAAM;QACLyB,WAAW,CAACC,IAAI,EAAEC,GAAG,CAAC;MACxB;MAEA;IACF;IAEA,IAAIzE,QAAQ,CAACyE,GAAG,CAAC,EAAE;MACjBJ,OAAO,CAACd,IAAI,CAACiB,IAAI,CAAC;MAClB;IACF;IAEAD,WAAW,CAACC,IAAI,EAAEC,GAAG,CAAC;EACxB,CAAC,CAAC;;EAEF;EACA;EACA;;EAEAJ,OAAO,CAAC5E,OAAO,CAAC,UAAUqF,KAAK,EAAE;IAC/B,IAAI,CAAC1D,IAAI,CAACkD,YAAY,CAAClD,IAAI,CAACuD,WAAW,CAACG,KAAK,CAAC,CAAC,EAAE;MAC/C;MACA;MACA;IACF;IAEA1D,IAAI,CAACkD,YAAY,CAACQ,KAAK,CAAC,CAAC7D,QAAQ,GAC/BG,IAAI,CAACkD,YAAY,CAAClD,IAAI,CAACuD,WAAW,CAACG,KAAK,CAAC,CAAC,CAAC7D,QAAQ;IACrDG,IAAI,CAACkD,YAAY,CAACQ,KAAK,CAAC,CAAC/B,SAAS,GAChC3B,IAAI,CAACkD,YAAY,CAAClD,IAAI,CAACuD,WAAW,CAACG,KAAK,CAAC,CAAC,CAAC/B,SAAS;EACxD,CAAC,CAAC;;EAEF;EACA;EACA;EACA3B,IAAI,CAACkD,YAAY,CAAC,EAAE,CAAC,GAAG;IAAErD,QAAQ,EAAE,IAAI;IAAE8B,SAAS,EAAED,gBAAgB,CAAC;EAAE,CAAC;;EAEzE;EACA;EACA;EACA,IAAIiC,KAAK,GAAGpF,MAAM,CAACC,IAAI,CAACwB,IAAI,CAACkD,YAAY,CAAC,CACrBU,MAAM,CAAC,UAAUR,IAAI,EAAE;IACtB;IACA,OAAOA,IAAI,CAAC1C,MAAM,GAAG,CAAC,IAAIV,IAAI,CAACkD,YAAY,CAACE,IAAI,CAAC;EACnD,CAAC,CAAC,CACDS,GAAG,CAAC7E,QAAQ,CAAC,CACbsD,IAAI,CAAC,GAAG,CAAC;EAC9B;EACAtC,IAAI,CAACE,EAAE,CAAC4D,WAAW,GAAK1D,MAAM,CAAC,wBAAwB,GAAGF,EAAE,CAAC6D,QAAQ,GAAG,KAAK,GAAGJ,KAAK,GAAG,GAAG,EAAE,GAAG,CAAC;EACjG3D,IAAI,CAACE,EAAE,CAAC8D,aAAa,GAAG5D,MAAM,CAAC,wBAAwB,GAAGF,EAAE,CAAC6D,QAAQ,GAAG,KAAK,GAAGJ,KAAK,GAAG,GAAG,EAAE,IAAI,CAAC;EAElG3D,IAAI,CAACE,EAAE,CAAC+D,OAAO,GAAG7D,MAAM,CACtB,GAAG,GAAGJ,IAAI,CAACE,EAAE,CAAC4D,WAAW,CAACxF,MAAM,GAAG,KAAK,GAAG0B,IAAI,CAACE,EAAE,CAAC6C,eAAe,CAACzE,MAAM,GAAG,KAAK,EACjF,GACF,CAAC;;EAED;EACA;EACA;;EAEAgD,cAAc,CAACtB,IAAI,CAAC;AACtB;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASkE,KAAKA,CAAClE,IAAI,EAAEmE,KAAK,EAAE;EAC1B,IAAIC,KAAK,GAAGpE,IAAI,CAACuB,SAAS;IACtB8C,GAAG,GAAKrE,IAAI,CAACsE,cAAc;IAC3BxE,IAAI,GAAIE,IAAI,CAACwB,cAAc,CAACtD,KAAK,CAACkG,KAAK,EAAEC,GAAG,CAAC;;EAEjD;AACF;AACA;AACA;AACA;EACE,IAAI,CAACE,MAAM,GAAMvE,IAAI,CAACwE,UAAU,CAACC,WAAW,CAAC,CAAC;EAC9C;AACF;AACA;AACA;AACA;EACE,IAAI,CAACC,KAAK,GAAON,KAAK,GAAGD,KAAK;EAC9B;AACF;AACA;AACA;AACA;EACE,IAAI,CAACQ,SAAS,GAAGN,GAAG,GAAGF,KAAK;EAC5B;AACF;AACA;AACA;AACA;EACE,IAAI,CAACS,GAAG,GAAS9E,IAAI;EACrB;AACF;AACA;AACA;AACA;EACE,IAAI,CAACA,IAAI,GAAQA,IAAI;EACrB;AACF;AACA;AACA;AACA;EACE,IAAI,CAAC+E,GAAG,GAAS/E,IAAI;AACvB;AAEA,SAASgF,WAAWA,CAAC9E,IAAI,EAAEmE,KAAK,EAAE;EAChC,IAAI1D,KAAK,GAAG,IAAIyD,KAAK,CAAClE,IAAI,EAAEmE,KAAK,CAAC;EAElCnE,IAAI,CAACkD,YAAY,CAACzC,KAAK,CAAC8D,MAAM,CAAC,CAAC5C,SAAS,CAAClB,KAAK,EAAET,IAAI,CAAC;EAEtD,OAAOS,KAAK;AACd;;AAGA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASsE,SAASA,CAACC,OAAO,EAAEC,OAAO,EAAE;EACnC,IAAI,EAAE,IAAI,YAAYF,SAAS,CAAC,EAAE;IAChC,OAAO,IAAIA,SAAS,CAACC,OAAO,EAAEC,OAAO,CAAC;EACxC;EAEA,IAAI,CAACA,OAAO,EAAE;IACZ,IAAI1F,YAAY,CAACyF,OAAO,CAAC,EAAE;MACzBC,OAAO,GAAGD,OAAO;MACjBA,OAAO,GAAG,CAAC,CAAC;IACd;EACF;EAEA,IAAI,CAAClD,QAAQ,GAAajE,MAAM,CAAC,CAAC,CAAC,EAAEsB,cAAc,EAAE8F,OAAO,CAAC;;EAE7D;EACA,IAAI,CAAC1D,SAAS,GAAY,CAAC,CAAC;EAC5B,IAAI,CAAC+C,cAAc,GAAO,CAAC,CAAC,CAAC,CAAC;EAC9B,IAAI,CAACE,UAAU,GAAW,EAAE;EAC5B,IAAI,CAAChD,cAAc,GAAO,EAAE;EAE5B,IAAI,CAAC+B,WAAW,GAAU1F,MAAM,CAAC,CAAC,CAAC,EAAE+B,cAAc,EAAEoF,OAAO,CAAC;EAC7D,IAAI,CAAC9B,YAAY,GAAS,CAAC,CAAC;EAE5B,IAAI,CAAClB,QAAQ,GAAaZ,YAAY;EACtC,IAAI,CAACc,iBAAiB,GAAI,KAAK;EAE/B,IAAI,CAAChC,EAAE,GAAG,CAAC,CAAC;EAEZ0B,OAAO,CAAC,IAAI,CAAC;AACf;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACAmD,SAAS,CAAC9G,SAAS,CAACiH,GAAG,GAAG,SAASA,GAAGA,CAACX,MAAM,EAAEY,UAAU,EAAE;EACzD,IAAI,CAAC5B,WAAW,CAACgB,MAAM,CAAC,GAAGY,UAAU;EACrCvD,OAAO,CAAC,IAAI,CAAC;EACb,OAAO,IAAI;AACb,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACAmD,SAAS,CAAC9G,SAAS,CAACmH,GAAG,GAAG,SAASA,GAAGA,CAACH,OAAO,EAAE;EAC9C,IAAI,CAACnD,QAAQ,GAAGjE,MAAM,CAAC,IAAI,CAACiE,QAAQ,EAAEmD,OAAO,CAAC;EAC9C,OAAO,IAAI;AACb,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACAF,SAAS,CAAC9G,SAAS,CAACuC,IAAI,GAAG,SAASA,IAAIA,CAACV,IAAI,EAAE;EAC7C;EACA,IAAI,CAAC0B,cAAc,GAAG1B,IAAI;EAC1B,IAAI,CAACyB,SAAS,GAAQ,CAAC,CAAC;EAExB,IAAI,CAACzB,IAAI,CAACY,MAAM,EAAE;IAAE,OAAO,KAAK;EAAE;EAElC,IAAI2E,CAAC,EAAEC,EAAE,EAAEC,EAAE,EAAEC,GAAG,EAAErB,KAAK,EAAEsB,IAAI,EAAEvF,EAAE,EAAEwF,OAAO,EAAEC,MAAM;;EAEpD;EACA,IAAI,IAAI,CAACzF,EAAE,CAAC4D,WAAW,CAACtD,IAAI,CAACV,IAAI,CAAC,EAAE;IAClCI,EAAE,GAAG,IAAI,CAACA,EAAE,CAAC8D,aAAa;IAC1B9D,EAAE,CAACyE,SAAS,GAAG,CAAC;IAChB,OAAO,CAACU,CAAC,GAAGnF,EAAE,CAAC0F,IAAI,CAAC9F,IAAI,CAAC,MAAM,IAAI,EAAE;MACnC0F,GAAG,GAAG,IAAI,CAACK,YAAY,CAAC/F,IAAI,EAAEuF,CAAC,CAAC,CAAC,CAAC,EAAEnF,EAAE,CAACyE,SAAS,CAAC;MACjD,IAAIa,GAAG,EAAE;QACP,IAAI,CAAChB,UAAU,GAAOa,CAAC,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC9D,SAAS,GAAQ8D,CAAC,CAACX,KAAK,GAAGW,CAAC,CAAC,CAAC,CAAC,CAAC3E,MAAM;QAC3C,IAAI,CAAC4D,cAAc,GAAGe,CAAC,CAACX,KAAK,GAAGW,CAAC,CAAC,CAAC,CAAC,CAAC3E,MAAM,GAAG8E,GAAG;QACjD;MACF;IACF;EACF;EAEA,IAAI,IAAI,CAAC1D,QAAQ,CAAC1C,SAAS,IAAI,IAAI,CAAC8D,YAAY,CAAC,OAAO,CAAC,EAAE;IACzD;IACAwC,OAAO,GAAG5F,IAAI,CAACgG,MAAM,CAAC,IAAI,CAAC5F,EAAE,CAAC6C,eAAe,CAAC;IAC9C,IAAI2C,OAAO,IAAI,CAAC,EAAE;MAChB;MACA,IAAI,IAAI,CAACnE,SAAS,GAAG,CAAC,IAAImE,OAAO,GAAG,IAAI,CAACnE,SAAS,EAAE;QAClD,IAAI,CAAC+D,EAAE,GAAGxF,IAAI,CAACW,KAAK,CAAC,IAAI,CAACqB,QAAQ,CAACxC,OAAO,GAAG,IAAI,CAACY,EAAE,CAACyC,UAAU,GAAG,IAAI,CAACzC,EAAE,CAAC2C,gBAAgB,CAAC,MAAM,IAAI,EAAE;UAErGsB,KAAK,GAAGmB,EAAE,CAACZ,KAAK,GAAGY,EAAE,CAAC,CAAC,CAAC,CAAC5E,MAAM;UAE/B,IAAI,IAAI,CAACa,SAAS,GAAG,CAAC,IAAI4C,KAAK,GAAG,IAAI,CAAC5C,SAAS,EAAE;YAChD,IAAI,CAACiD,UAAU,GAAO,EAAE;YACxB,IAAI,CAACjD,SAAS,GAAQ4C,KAAK;YAC3B,IAAI,CAACG,cAAc,GAAGgB,EAAE,CAACZ,KAAK,GAAGY,EAAE,CAAC,CAAC,CAAC,CAAC5E,MAAM;UAC/C;QACF;MACF;IACF;EACF;EAEA,IAAI,IAAI,CAACoB,QAAQ,CAACzC,UAAU,IAAI,IAAI,CAAC6D,YAAY,CAAC,SAAS,CAAC,EAAE;IAC5D;IACAyC,MAAM,GAAG7F,IAAI,CAACiG,OAAO,CAAC,GAAG,CAAC;IAC1B,IAAIJ,MAAM,IAAI,CAAC,EAAE;MACf;MACA;MACA,IAAI,CAACJ,EAAE,GAAGzF,IAAI,CAACW,KAAK,CAAC,IAAI,CAACP,EAAE,CAACuC,WAAW,CAAC,MAAM,IAAI,EAAE;QAEnD0B,KAAK,GAAGoB,EAAE,CAACb,KAAK,GAAGa,EAAE,CAAC,CAAC,CAAC,CAAC7E,MAAM;QAC/B+E,IAAI,GAAIF,EAAE,CAACb,KAAK,GAAGa,EAAE,CAAC,CAAC,CAAC,CAAC7E,MAAM;QAE/B,IAAI,IAAI,CAACa,SAAS,GAAG,CAAC,IAAI4C,KAAK,GAAG,IAAI,CAAC5C,SAAS,IAC3C4C,KAAK,KAAK,IAAI,CAAC5C,SAAS,IAAIkE,IAAI,GAAG,IAAI,CAACnB,cAAe,EAAE;UAC5D,IAAI,CAACE,UAAU,GAAO,SAAS;UAC/B,IAAI,CAACjD,SAAS,GAAQ4C,KAAK;UAC3B,IAAI,CAACG,cAAc,GAAGmB,IAAI;QAC5B;MACF;IACF;EACF;EAEA,OAAO,IAAI,CAAClE,SAAS,IAAI,CAAC;AAC5B,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACAwD,SAAS,CAAC9G,SAAS,CAACgG,OAAO,GAAG,SAASA,OAAOA,CAACnE,IAAI,EAAE;EACnD,OAAO,IAAI,CAACI,EAAE,CAAC+D,OAAO,CAACzD,IAAI,CAACV,IAAI,CAAC;AACnC,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAiF,SAAS,CAAC9G,SAAS,CAAC4H,YAAY,GAAG,SAASA,YAAYA,CAAC/F,IAAI,EAAEyE,MAAM,EAAExE,GAAG,EAAE;EAC1E;EACA,IAAI,CAAC,IAAI,CAACmD,YAAY,CAACqB,MAAM,CAACE,WAAW,CAAC,CAAC,CAAC,EAAE;IAC5C,OAAO,CAAC;EACV;EACA,OAAO,IAAI,CAACvB,YAAY,CAACqB,MAAM,CAACE,WAAW,CAAC,CAAC,CAAC,CAAC5E,QAAQ,CAACC,IAAI,EAAEC,GAAG,EAAE,IAAI,CAAC;AAC1E,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAgF,SAAS,CAAC9G,SAAS,CAACwC,KAAK,GAAG,SAASA,KAAKA,CAACX,IAAI,EAAE;EAC/C,IAAIqE,KAAK,GAAG,CAAC;IAAE6B,MAAM,GAAG,EAAE;;EAE1B;EACA,IAAI,IAAI,CAACzE,SAAS,IAAI,CAAC,IAAI,IAAI,CAACC,cAAc,KAAK1B,IAAI,EAAE;IACvDkG,MAAM,CAAC7D,IAAI,CAAC2C,WAAW,CAAC,IAAI,EAAEX,KAAK,CAAC,CAAC;IACrCA,KAAK,GAAG,IAAI,CAACG,cAAc;EAC7B;;EAEA;EACA,IAAIrE,IAAI,GAAGkE,KAAK,GAAGrE,IAAI,CAAC5B,KAAK,CAACiG,KAAK,CAAC,GAAGrE,IAAI;;EAE3C;EACA,OAAO,IAAI,CAACU,IAAI,CAACP,IAAI,CAAC,EAAE;IACtB+F,MAAM,CAAC7D,IAAI,CAAC2C,WAAW,CAAC,IAAI,EAAEX,KAAK,CAAC,CAAC;IAErClE,IAAI,GAAGA,IAAI,CAAC/B,KAAK,CAAC,IAAI,CAACoG,cAAc,CAAC;IACtCH,KAAK,IAAI,IAAI,CAACG,cAAc;EAC9B;EAEA,IAAI0B,MAAM,CAACtF,MAAM,EAAE;IACjB,OAAOsF,MAAM;EACf;EAEA,OAAO,IAAI;AACb,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAjB,SAAS,CAAC9G,SAAS,CAAC8D,IAAI,GAAG,SAASA,IAAIA,CAACkE,IAAI,EAAEC,OAAO,EAAE;EACtDD,IAAI,GAAGjI,KAAK,CAACmI,OAAO,CAACF,IAAI,CAAC,GAAGA,IAAI,GAAG,CAAEA,IAAI,CAAE;EAE5C,IAAI,CAACC,OAAO,EAAE;IACZ,IAAI,CAAClE,QAAQ,GAAGiE,IAAI,CAAC/H,KAAK,CAAC,CAAC;IAC5B,IAAI,CAACgE,iBAAiB,GAAG,IAAI;IAC7BN,OAAO,CAAC,IAAI,CAAC;IACb,OAAO,IAAI;EACb;EAEA,IAAI,CAACI,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACoE,MAAM,CAACH,IAAI,CAAC,CACTI,IAAI,CAAC,CAAC,CACNzC,MAAM,CAAC,UAAU0C,EAAE,EAAEC,GAAG,EAAEC,GAAG,EAAE;IAC9B,OAAOF,EAAE,KAAKE,GAAG,CAACD,GAAG,GAAG,CAAC,CAAC;EAC5B,CAAC,CAAC,CACDE,OAAO,CAAC,CAAC;EAE1C7E,OAAO,CAAC,IAAI,CAAC;EACb,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACAmD,SAAS,CAAC9G,SAAS,CAAC0D,SAAS,GAAG,SAASA,SAASA,CAAClB,KAAK,EAAE;EAExD;EACA;;EAEA,IAAI,CAACA,KAAK,CAAC8D,MAAM,EAAE;IAAE9D,KAAK,CAACoE,GAAG,GAAG,SAAS,GAAGpE,KAAK,CAACoE,GAAG;EAAE;EAExD,IAAIpE,KAAK,CAAC8D,MAAM,KAAK,SAAS,IAAI,CAAC,WAAW,CAAC/D,IAAI,CAACC,KAAK,CAACoE,GAAG,CAAC,EAAE;IAC9DpE,KAAK,CAACoE,GAAG,GAAG,SAAS,GAAGpE,KAAK,CAACoE,GAAG;EACnC;AACF,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACAE,SAAS,CAAC9G,SAAS,CAACgE,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG,CACrD,CAAC;AAGDyE,MAAM,CAACC,OAAO,GAAG5B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}