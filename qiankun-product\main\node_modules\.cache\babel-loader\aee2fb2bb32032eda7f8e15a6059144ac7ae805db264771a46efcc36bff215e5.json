{"ast": null, "code": "// Parse link title\n//\n'use strict';\n\nvar unescapeAll = require('../common/utils').unescapeAll;\nmodule.exports = function parseLinkTitle(str, pos, max) {\n  var code,\n    marker,\n    lines = 0,\n    start = pos,\n    result = {\n      ok: false,\n      pos: 0,\n      lines: 0,\n      str: ''\n    };\n  if (pos >= max) {\n    return result;\n  }\n  marker = str.charCodeAt(pos);\n  if (marker !== 0x22 /* \" */ && marker !== 0x27 /* ' */ && marker !== 0x28 /* ( */) {\n    return result;\n  }\n  pos++;\n\n  // if opening marker is \"(\", switch it to closing marker \")\"\n  if (marker === 0x28) {\n    marker = 0x29;\n  }\n  while (pos < max) {\n    code = str.charCodeAt(pos);\n    if (code === marker) {\n      result.pos = pos + 1;\n      result.lines = lines;\n      result.str = unescapeAll(str.slice(start + 1, pos));\n      result.ok = true;\n      return result;\n    } else if (code === 0x28 /* ( */ && marker === 0x29 /* ) */) {\n      return result;\n    } else if (code === 0x0A) {\n      lines++;\n    } else if (code === 0x5C /* \\ */ && pos + 1 < max) {\n      pos++;\n      if (str.charCodeAt(pos) === 0x0A) {\n        lines++;\n      }\n    }\n    pos++;\n  }\n  return result;\n};", "map": {"version": 3, "names": ["unescapeAll", "require", "module", "exports", "parseLinkTitle", "str", "pos", "max", "code", "marker", "lines", "start", "result", "ok", "charCodeAt", "slice"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/helpers/parse_link_title.js"], "sourcesContent": ["// Parse link title\n//\n'use strict';\n\n\nvar unescapeAll = require('../common/utils').unescapeAll;\n\n\nmodule.exports = function parseLinkTitle(str, pos, max) {\n  var code,\n      marker,\n      lines = 0,\n      start = pos,\n      result = {\n        ok: false,\n        pos: 0,\n        lines: 0,\n        str: ''\n      };\n\n  if (pos >= max) { return result; }\n\n  marker = str.charCodeAt(pos);\n\n  if (marker !== 0x22 /* \" */ && marker !== 0x27 /* ' */ && marker !== 0x28 /* ( */) { return result; }\n\n  pos++;\n\n  // if opening marker is \"(\", switch it to closing marker \")\"\n  if (marker === 0x28) { marker = 0x29; }\n\n  while (pos < max) {\n    code = str.charCodeAt(pos);\n    if (code === marker) {\n      result.pos = pos + 1;\n      result.lines = lines;\n      result.str = unescapeAll(str.slice(start + 1, pos));\n      result.ok = true;\n      return result;\n    } else if (code === 0x28 /* ( */ && marker === 0x29 /* ) */) {\n      return result;\n    } else if (code === 0x0A) {\n      lines++;\n    } else if (code === 0x5C /* \\ */ && pos + 1 < max) {\n      pos++;\n      if (str.charCodeAt(pos) === 0x0A) {\n        lines++;\n      }\n    }\n\n    pos++;\n  }\n\n  return result;\n};\n"], "mappings": "AAAA;AACA;AACA,YAAY;;AAGZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,iBAAiB,CAAC,CAACD,WAAW;AAGxDE,MAAM,CAACC,OAAO,GAAG,SAASC,cAAcA,CAACC,GAAG,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACtD,IAAIC,IAAI;IACJC,MAAM;IACNC,KAAK,GAAG,CAAC;IACTC,KAAK,GAAGL,GAAG;IACXM,MAAM,GAAG;MACPC,EAAE,EAAE,KAAK;MACTP,GAAG,EAAE,CAAC;MACNI,KAAK,EAAE,CAAC;MACRL,GAAG,EAAE;IACP,CAAC;EAEL,IAAIC,GAAG,IAAIC,GAAG,EAAE;IAAE,OAAOK,MAAM;EAAE;EAEjCH,MAAM,GAAGJ,GAAG,CAACS,UAAU,CAACR,GAAG,CAAC;EAE5B,IAAIG,MAAM,KAAK,IAAI,CAAC,WAAWA,MAAM,KAAK,IAAI,CAAC,WAAWA,MAAM,KAAK,IAAI,CAAC,SAAS;IAAE,OAAOG,MAAM;EAAE;EAEpGN,GAAG,EAAE;;EAEL;EACA,IAAIG,MAAM,KAAK,IAAI,EAAE;IAAEA,MAAM,GAAG,IAAI;EAAE;EAEtC,OAAOH,GAAG,GAAGC,GAAG,EAAE;IAChBC,IAAI,GAAGH,GAAG,CAACS,UAAU,CAACR,GAAG,CAAC;IAC1B,IAAIE,IAAI,KAAKC,MAAM,EAAE;MACnBG,MAAM,CAACN,GAAG,GAAGA,GAAG,GAAG,CAAC;MACpBM,MAAM,CAACF,KAAK,GAAGA,KAAK;MACpBE,MAAM,CAACP,GAAG,GAAGL,WAAW,CAACK,GAAG,CAACU,KAAK,CAACJ,KAAK,GAAG,CAAC,EAAEL,GAAG,CAAC,CAAC;MACnDM,MAAM,CAACC,EAAE,GAAG,IAAI;MAChB,OAAOD,MAAM;IACf,CAAC,MAAM,IAAIJ,IAAI,KAAK,IAAI,CAAC,WAAWC,MAAM,KAAK,IAAI,CAAC,SAAS;MAC3D,OAAOG,MAAM;IACf,CAAC,MAAM,IAAIJ,IAAI,KAAK,IAAI,EAAE;MACxBE,KAAK,EAAE;IACT,CAAC,MAAM,IAAIF,IAAI,KAAK,IAAI,CAAC,WAAWF,GAAG,GAAG,CAAC,GAAGC,GAAG,EAAE;MACjDD,GAAG,EAAE;MACL,IAAID,GAAG,CAACS,UAAU,CAACR,GAAG,CAAC,KAAK,IAAI,EAAE;QAChCI,KAAK,EAAE;MACT;IACF;IAEAJ,GAAG,EAAE;EACP;EAEA,OAAOM,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}