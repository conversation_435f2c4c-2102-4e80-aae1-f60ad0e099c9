{"ast": null, "code": "import api from '@/api';\nimport { MicroGlobal } from 'common/config/qiankun';\nimport { ElLoading } from 'element-plus';\nexport var Print = {\n  /**\r\n   * 初始化\r\n   * @param {HTMLElement} dom - 要打印的DOM元素\r\n   * @param {Object} options - 配置选项\r\n   * @param {boolean} options.waitForImages - 是否等待图片加载完成，默认true\r\n   * @param {number} options.imageTimeout - 图片加载超时时间(ms)，默认10000\r\n   * @param {boolean} options.showLoading - 是否显示加载动画，默认true\r\n   */\n  init(dom) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var config = {\n      waitForImages: options.waitForImages !== false,\n      // 默认等待图片加载\n      imageTimeout: options.imageTimeout || 10000,\n      showLoading: options.showLoading !== false // 默认显示加载动画\n    };\n    this.writeIframe(this.getStyle() + dom.outerHTML, config);\n  },\n  /**\r\n   * 复制原网页的样式\r\n   */\n  getStyle() {\n    var str = '';\n    var styles = [];\n    var qiankun = new MicroGlobal();\n    if (window.__POWERED_BY_QIANKUN__) {\n      styles = document.querySelector(`#${qiankun.name}`).querySelectorAll('style,link');\n    } else {\n      styles = document.querySelector('head').querySelectorAll('style,link');\n    }\n    for (var i = 0; i < styles.length; i++) {\n      str += styles[i].outerHTML;\n    }\n    str += '<style>*{margin: 0;padding: 0;-webkit-print-color-adjust: exact;}html,body{height: auto!important;box-sizing: border-box;}.no-print{display:none;}</style>';\n    return str;\n  },\n  /**\r\n   * 创建iframe\r\n   */\n  writeIframe(content, config) {\n    var _this = this;\n    var iframe = document.createElement('iframe');\n    var iframeEl = document.body.appendChild(iframe);\n    iframe.id = 'myIframe';\n    iframe.setAttribute('style', 'position:absolute;width:0;height:0;top:-10px;left:-10px;');\n\n    // 创建加载动画\n    var loadingInstance = null;\n    if (config.showLoading) {\n      loadingInstance = ElLoading.service({\n        lock: true,\n        text: '正在准备打印内容...',\n        background: 'rgba(0, 0, 0, 0.7)',\n        customClass: 'print-loading'\n      });\n    }\n    var processPrint = function processPrint(res) {\n      var w = iframeEl.contentWindow || iframeEl.contentDocument;\n      var doc = iframeEl.contentDocument || iframeEl.contentWindow.document;\n      doc.open();\n      doc.write(content);\n      _this.changeTheme(iframe, res === null || res === void 0 ? void 0 : res.data);\n      if (config.waitForImages) {\n        // 更新加载文本\n        if (loadingInstance) {\n          loadingInstance.setText('正在加载图片...');\n        }\n\n        // 等待所有图片加载完成\n        _this.waitForImagesLoaded(doc, config.imageTimeout, loadingInstance).then(function () {\n          if (loadingInstance) {\n            loadingInstance.setText('正在生成打印预览...');\n          }\n          setTimeout(function () {\n            doc.close();\n            _this.toPrint(w);\n            if (loadingInstance) {\n              loadingInstance.close();\n            }\n            setTimeout(function () {\n              document.body.removeChild(iframe);\n            }, 1000);\n          }, 500);\n        }).catch(function (error) {\n          // 如果图片加载超时，仍然进行打印\n          console.warn('图片加载超时，继续打印:', error.message);\n          if (loadingInstance) {\n            loadingInstance.setText('正在生成打印预览...');\n          }\n          setTimeout(function () {\n            doc.close();\n            _this.toPrint(w);\n            if (loadingInstance) {\n              loadingInstance.close();\n            }\n            setTimeout(function () {\n              document.body.removeChild(iframe);\n            }, 1000);\n          }, 500);\n        });\n      } else {\n        // 不等待图片加载，直接打印\n        if (loadingInstance) {\n          loadingInstance.setText('正在生成打印预览...');\n        }\n        setTimeout(function () {\n          doc.close();\n          _this.toPrint(w);\n          if (loadingInstance) {\n            loadingInstance.close();\n          }\n          setTimeout(function () {\n            document.body.removeChild(iframe);\n          }, 1000);\n        }, 500); // 给一个较短的延时让DOM渲染\n      }\n    };\n    api.currentTheme({}).then(function (res) {\n      processPrint(res);\n    }).catch(function () {\n      processPrint();\n    });\n  },\n  /**\r\n   * 等待所有图片加载完成\r\n   */\n  waitForImagesLoaded(doc) {\n    var timeout = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 10000;\n    var loadingInstance = arguments.length > 2 ? arguments[2] : undefined;\n    return new Promise(function (resolve, reject) {\n      var images = doc.querySelectorAll('img');\n      if (images.length === 0) {\n        resolve();\n        return;\n      }\n      var loadedCount = 0;\n      var errorCount = 0;\n      var totalImages = images.length;\n      var timeoutId = setTimeout(function () {\n        reject(new Error(`图片加载超时，已加载 ${loadedCount}/${totalImages} 张图片`));\n      }, timeout);\n      var checkComplete = function checkComplete() {\n        loadedCount++;\n        // 更新加载进度\n        if (loadingInstance) {\n          var progress = Math.round(loadedCount / totalImages * 100);\n          loadingInstance.setText(`正在加载图片... (${loadedCount}/${totalImages}) ${progress}%`);\n        }\n        if (loadedCount === totalImages) {\n          clearTimeout(timeoutId);\n          if (errorCount > 0) {\n            console.warn(`图片加载完成，但有 ${errorCount} 张图片加载失败`);\n            if (loadingInstance) {\n              loadingInstance.setText(`图片加载完成，${errorCount} 张图片加载失败`);\n            }\n          }\n          resolve();\n        }\n      };\n      images.forEach(function (img, index) {\n        if (img.complete && img.naturalHeight !== 0) {\n          // 图片已经加载完成\n          checkComplete();\n        } else {\n          // 监听图片加载事件\n          img.onload = function () {\n            checkComplete();\n          };\n          img.onerror = function () {\n            errorCount++;\n            console.warn(`图片加载失败: ${img.src || `第${index + 1}张图片`}`);\n            checkComplete(); // 即使加载失败也继续\n          };\n\n          // 如果图片src为空或者无效，直接算作完成\n          if (!img.src || img.src === '' || img.src === 'data:') {\n            checkComplete();\n          }\n        }\n      });\n    });\n  },\n  /**\r\n  打印\r\n  */\n  toPrint(f) {\n    try {\n      setTimeout(function () {\n        f.focus();\n        try {\n          if (!f.document.execCommand('print', false, null)) {\n            f.print();\n          }\n        } catch (e) {\n          f.print();\n        }\n        f.close();\n      }, 10);\n    } catch (err) {\n      console.log('err', err);\n    }\n  },\n  changeTheme(iframe, data) {\n    var obj = {\n      primary: (data === null || data === void 0 ? void 0 : data.primaryColor) || '#bc1d1d',\n      success: (data === null || data === void 0 ? void 0 : data.success) || '',\n      warning: (data === null || data === void 0 ? void 0 : data.warning) || '',\n      danger: (data === null || data === void 0 ? void 0 : data.danger) || '',\n      info: (data === null || data === void 0 ? void 0 : data.info) || '',\n      height: (data === null || data === void 0 ? void 0 : data.height) || '36px',\n      height_routine: (data === null || data === void 0 ? void 0 : data.heightRoutine) || '32px',\n      height_secondary: (data === null || data === void 0 ? void 0 : data.heightSecondary) || '26px',\n      line_height: (data === null || data === void 0 ? void 0 : data.lineHeight) || '1.5',\n      system_font_size: (data === null || data === void 0 ? void 0 : data.systemFontSize) || '28px',\n      title_font_size: (data === null || data === void 0 ? void 0 : data.titleFontSize) || '26px',\n      navigation_font_size: (data === null || data === void 0 ? void 0 : data.navigationFontSize) || '18px',\n      name_font_size: (data === null || data === void 0 ? void 0 : data.nameFontSize) || '16px',\n      text_font_size: (data === null || data === void 0 ? void 0 : data.textFontSize) || '14px',\n      text_color_primary: (data === null || data === void 0 ? void 0 : data.textColorPrimary) || '#303133',\n      text_color_regular: (data === null || data === void 0 ? void 0 : data.textColorRegular) || '#606266',\n      text_color_secondary: (data === null || data === void 0 ? void 0 : data.textColorSecondary) || '#909399',\n      border_color: (data === null || data === void 0 ? void 0 : data.borderColor) || '#dcdfe6',\n      border_color_light: (data === null || data === void 0 ? void 0 : data.borderColorLight) || '#e4e7ed',\n      border_color_lighter: (data === null || data === void 0 ? void 0 : data.borderColorLighter) || '#ebeef5',\n      border_color_extra_light: (data === null || data === void 0 ? void 0 : data.borderColorExtraLight) || '#f2f6fc',\n      distance_one: (data === null || data === void 0 ? void 0 : data.distanceOne) || '40px',\n      distance_two: (data === null || data === void 0 ? void 0 : data.distanceTwo) || '20px',\n      distance_three: (data === null || data === void 0 ? void 0 : data.distanceThree) || '16px',\n      distance_four: (data === null || data === void 0 ? void 0 : data.distanceFour) || '12px',\n      distance_five: (data === null || data === void 0 ? void 0 : data.distanceFive) || '10px',\n      font_name_distance_five: (data === null || data === void 0 ? void 0 : data.fontNameDistanceFive) || '6px',\n      font_text_distance_five: (data === null || data === void 0 ? void 0 : data.fontTextDistanceFive) || '3px',\n      border_radius_base: (data === null || data === void 0 ? void 0 : data.borderRadiusBase) || '4px',\n      border_radius_small: (data === null || data === void 0 ? void 0 : data.borderRadiusSmall) || '2px',\n      box_shadow: (data === null || data === void 0 ? void 0 : data.boxShadow) || '0px 12px 32px 4px rgba(0,0,0,.04),0px 8px 20px rgba(0,0,0,.08)',\n      box_shadow_light: (data === null || data === void 0 ? void 0 : data.boxShadowLight) || '0px 0px 12px rgba(0,0,0,.12)',\n      box_shadow_lighter: (data === null || data === void 0 ? void 0 : data.boxShadowLighter) || '0px 0px 6px rgba(0,0,0,.12)',\n      form_width_one: (data === null || data === void 0 ? void 0 : data.formWidthOne) || 'calc(var(--zy-distance-one) + (var(--zy-distance-two) * 4) + (var(--zy-form-width-item) * 3))',\n      form_width_two: (data === null || data === void 0 ? void 0 : data.formWidthTwo) || 'calc(var(--zy-distance-one) + (var(--zy-distance-two) * 3) + (var(--zy-form-width-item) * 2))',\n      form_width_item: (data === null || data === void 0 ? void 0 : data.formWidthItem) || '290px',\n      form_distance_bottom: (data === null || data === void 0 ? void 0 : data.formDistanceBottom) || '22px'\n    };\n    var el = iframe.contentWindow.document.documentElement;\n    // 主题颜色\n    if (obj.primary) {\n      this.setThemeColor(el, 'primary', obj.primary);\n    }\n    if (obj.success && success !== '#67c23a') {\n      this.setThemeColor(el, 'success', obj.success);\n    }\n    if (obj.warning && warning !== '#e6a23c') {\n      this.setThemeColor(el, 'warning', obj.warning);\n    }\n    if (obj.danger && danger !== '#f56c6c') {\n      this.setThemeColor(el, 'danger', obj.danger);\n    }\n    if (obj.danger && danger !== '#f56c6c') {\n      this.setThemeColor(el, 'error', obj.danger);\n    }\n    if (obj.info && info !== '#909399') {\n      this.setThemeColor(el, 'info', obj.info);\n    }\n    // 高度\n    if (obj.height) {\n      el.style.setProperty('--zy-height', obj.height);\n    }\n    if (obj.height_routine) {\n      el.style.setProperty('--zy-height-routine', obj.height_routine);\n    }\n    if (obj.height_secondary) {\n      el.style.setProperty('--zy-height-secondary', obj.height_secondary);\n    }\n    // 行高\n    if (obj.line_height) {\n      el.style.setProperty('--zy-line-height', obj.line_height);\n    }\n    // 字体大小\n    if (obj.system_font_size) {\n      el.style.setProperty('--zy-system-font-size', obj.system_font_size);\n    }\n    if (obj.title_font_size) {\n      el.style.setProperty('--zy-title-font-size', obj.title_font_size);\n    }\n    if (obj.navigation_font_size) {\n      el.style.setProperty('--zy-navigation-font-size', obj.navigation_font_size);\n    }\n    if (obj.name_font_size) {\n      el.style.setProperty('--zy-name-font-size', obj.name_font_size);\n    }\n    if (obj.text_font_size) {\n      el.style.setProperty('--zy-text-font-size', obj.text_font_size);\n    }\n    // 文字颜色\n    if (obj.text_color_primary) {\n      el.style.setProperty('--zy-el-text-color-primary', obj.text_color_primary);\n    }\n    if (obj.text_color_regular) {\n      el.style.setProperty('--zy-el-text-color-regular', obj.text_color_regular);\n    }\n    if (obj.text_color_secondary) {\n      el.style.setProperty('--zy-el-text-color-secondary', obj.text_color_secondary);\n    }\n    // 边框颜色\n    if (obj.border_color) {\n      el.style.setProperty('--zy-el-border-color', obj.border_color);\n    }\n    if (obj.border_color_light) {\n      el.style.setProperty('--zy-el-border-color-light', obj.border_color_light);\n    }\n    if (obj.border_color_lighter) {\n      el.style.setProperty('--zy-el-border-color-lighter', obj.border_color_lighter);\n    }\n    if (obj.border_color_extra_light) {\n      el.style.setProperty('--zy-el-border-color-extra-light', obj.border_color_extra_light);\n    }\n    // 边距\n    if (obj.distance_one) {\n      el.style.setProperty('--zy-distance-one', obj.distance_one);\n    }\n    if (obj.distance_two) {\n      el.style.setProperty('--zy-distance-two', obj.distance_two);\n    }\n    if (obj.distance_three) {\n      el.style.setProperty('--zy-distance-three', obj.distance_three);\n    }\n    if (obj.distance_four) {\n      el.style.setProperty('--zy-distance-four', obj.distance_four);\n    }\n    if (obj.distance_five) {\n      el.style.setProperty('--zy-distance-five', obj.distance_five);\n    }\n    if (obj.font_name_distance_five) {\n      el.style.setProperty('--zy-font-name-distance-five', obj.font_name_distance_five);\n    }\n    if (obj.font_text_distance_five) {\n      el.style.setProperty('--zy-font-text-distance-five', obj.font_text_distance_five);\n    }\n    // 圆角\n    if (obj.border_radius_base) {\n      el.style.setProperty('--el-border-radius-base', obj.border_radius_base);\n    }\n    if (obj.border_radius_small) {\n      el.style.setProperty('--el-border-radius-small', obj.border_radius_small);\n    }\n    // 盒子阴影\n    if (obj.box_shadow) {\n      el.style.setProperty('--zy-el-box-shadow', obj.box_shadow);\n    }\n    if (obj.box_shadow_light) {\n      el.style.setProperty('--zy-el-box-shadow-light', obj.box_shadow_light);\n    }\n    if (obj.box_shadow_lighter) {\n      el.style.setProperty('--zy-el-box-shadow-lighter', obj.box_shadow_lighter);\n    }\n    // 表单宽度，边距\n    if (obj.form_width_one) {\n      el.style.setProperty('--zy-form-width-one', obj.form_width_one);\n    }\n    if (obj.form_width_two) {\n      el.style.setProperty('--zy-form-width-two', obj.form_width_two);\n    }\n    if (obj.form_width_item) {\n      el.style.setProperty('--zy-form-width-item', obj.form_width_item);\n    }\n    if (obj.form_distance_bottom) {\n      el.style.setProperty('--zy-form-distance-bottom', obj.form_distance_bottom);\n    }\n  },\n  setThemeColor(el, type, color) {\n    el.style.setProperty(`--zy-el-color-${type}`, color);\n    el.style.setProperty(`--zy-el-color-${type}-light-3`, this.colourBlend(color, '#ffffff', 3 / 10));\n    el.style.setProperty(`--zy-el-color-${type}-light-5`, this.colourBlend(color, '#ffffff', 5 / 10));\n    el.style.setProperty(`--zy-el-color-${type}-light-7`, this.colourBlend(color, '#ffffff', 7 / 10));\n    el.style.setProperty(`--zy-el-color-${type}-light-8`, this.colourBlend(color, '#ffffff', 8 / 10));\n    el.style.setProperty(`--zy-el-color-${type}-light-9`, this.colourBlend(color, '#ffffff', 9 / 10));\n    el.style.setProperty(`--zy-el-color-${type}-dark-2`, this.colourBlend(color, '#000000', 2 / 10));\n  },\n  colourBlend(c1, c2, ratio) {\n    ratio = Math.max(Math.min(Number(ratio), 1), 0);\n    var r1 = parseInt(c1.substring(1, 3), 16);\n    var g1 = parseInt(c1.substring(3, 5), 16);\n    var b1 = parseInt(c1.substring(5, 7), 16);\n    var r2 = parseInt(c2.substring(1, 3), 16);\n    var g2 = parseInt(c2.substring(3, 5), 16);\n    var b2 = parseInt(c2.substring(5, 7), 16);\n    var r = Math.round(r1 * (1 - ratio) + r2 * ratio);\n    var g = Math.round(g1 * (1 - ratio) + g2 * ratio);\n    var b = Math.round(b1 * (1 - ratio) + b2 * ratio);\n    r = ('0' + (r || 0).toString(16)).slice(-2);\n    g = ('0' + (g || 0).toString(16)).slice(-2);\n    b = ('0' + (b || 0).toString(16)).slice(-2);\n    return '#' + r + g + b;\n  }\n};\n\n/*\r\n使用示例：\r\n\r\n// 基本使用（等待图片加载，显示加载动画）\r\nPrint.init(document.getElementById('printArea'))\r\n\r\n// 不等待图片加载，直接打印\r\nPrint.init(document.getElementById('printArea'), {\r\n  waitForImages: false\r\n})\r\n\r\n// 自定义图片加载超时时间\r\nPrint.init(document.getElementById('printArea'), {\r\n  imageTimeout: 15000 // 15秒超时\r\n})\r\n\r\n// 不显示加载动画\r\nPrint.init(document.getElementById('printArea'), {\r\n  showLoading: false\r\n})\r\n\r\n// 完整配置\r\nPrint.init(document.getElementById('printArea'), {\r\n  waitForImages: true,\r\n  imageTimeout: 20000,\r\n  showLoading: true\r\n})\r\n*/\n\n// 添加自定义样式\nvar style = document.createElement('style');\nstyle.textContent = `\n.print-loading .el-loading-spinner {\n  background: rgba(255, 255, 255, 0.9);\n  border-radius: 8px;\n  padding: 20px;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n}\n\n.print-loading .el-loading-text {\n  color: #409eff;\n  font-size: 14px;\n  font-weight: 500;\n  margin-top: 10px;\n}\n\n.print-loading .el-loading-spinner .circular {\n  width: 42px;\n  height: 42px;\n  animation: loading-rotate 2s linear infinite;\n}\n\n.print-loading .el-loading-spinner .path {\n  stroke: #409eff;\n  stroke-width: 2;\n  stroke-dasharray: 90,150;\n  stroke-dashoffset: 0;\n  stroke-linecap: round;\n  animation: loading-dash 1.5s ease-in-out infinite;\n}\n\n@keyframes loading-rotate {\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n@keyframes loading-dash {\n  0% {\n    stroke-dasharray: 1,200;\n    stroke-dashoffset: 0;\n  }\n  50% {\n    stroke-dasharray: 90,150;\n    stroke-dashoffset: -40px;\n  }\n  100% {\n    stroke-dasharray: 90,150;\n    stroke-dashoffset: -120px;\n  }\n}\n`;\ndocument.head.appendChild(style);", "map": {"version": 3, "names": ["api", "MicroGlobal", "ElLoading", "Print", "init", "dom", "options", "arguments", "length", "undefined", "config", "waitForImages", "imageTimeout", "showLoading", "writeIframe", "getStyle", "outerHTML", "str", "styles", "qiankun", "window", "__POWERED_BY_QIANKUN__", "document", "querySelector", "name", "querySelectorAll", "i", "content", "_this", "iframe", "createElement", "iframeEl", "body", "append<PERSON><PERSON><PERSON>", "id", "setAttribute", "loadingInstance", "service", "lock", "text", "background", "customClass", "processPrint", "res", "w", "contentWindow", "contentDocument", "doc", "open", "write", "changeTheme", "data", "setText", "waitForImagesLoaded", "then", "setTimeout", "close", "to<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "catch", "error", "console", "warn", "message", "currentTheme", "timeout", "Promise", "resolve", "reject", "images", "loadedCount", "errorCount", "totalImages", "timeoutId", "Error", "checkComplete", "progress", "Math", "round", "clearTimeout", "for<PERSON>ach", "img", "index", "complete", "naturalHeight", "onload", "onerror", "src", "f", "focus", "execCommand", "print", "e", "err", "log", "obj", "primary", "primaryColor", "success", "warning", "danger", "info", "height", "height_routine", "heightRoutine", "height_secondary", "heightSecondary", "line_height", "lineHeight", "system_font_size", "systemFontSize", "title_font_size", "titleFontSize", "navigation_font_size", "navigationFontSize", "name_font_size", "nameFontSize", "text_font_size", "textFontSize", "text_color_primary", "textColorPrimary", "text_color_regular", "textColorRegular", "text_color_secondary", "textColorSecondary", "border_color", "borderColor", "border_color_light", "borderColorLight", "border_color_lighter", "borderColorLighter", "border_color_extra_light", "borderColorExtraLight", "distance_one", "distanceOne", "distance_two", "distanceTwo", "distance_three", "distanceThree", "distance_four", "distanceFour", "distance_five", "distanceFive", "font_name_distance_five", "fontNameDistanceFive", "font_text_distance_five", "fontTextDistanceFive", "border_radius_base", "borderRadiusBase", "border_radius_small", "borderRadiusSmall", "box_shadow", "boxShadow", "box_shadow_light", "boxShadowLight", "box_shadow_lighter", "boxShadowLighter", "form_width_one", "formWidthOne", "form_width_two", "formWidthTwo", "form_width_item", "formWidthItem", "form_distance_bottom", "formDistanceBottom", "el", "documentElement", "setThemeColor", "style", "setProperty", "type", "color", "colourBlend", "c1", "c2", "ratio", "max", "min", "Number", "r1", "parseInt", "substring", "g1", "b1", "r2", "g2", "b2", "r", "g", "b", "toString", "slice", "textContent", "head"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/js/print.js"], "sourcesContent": ["import api from '@/api'\r\nimport { MicroGlobal } from 'common/config/qiankun'\r\nimport { ElLoading } from 'element-plus'\r\n\r\nexport const Print = {\r\n  /**\r\n   * 初始化\r\n   * @param {HTMLElement} dom - 要打印的DOM元素\r\n   * @param {Object} options - 配置选项\r\n   * @param {boolean} options.waitForImages - 是否等待图片加载完成，默认true\r\n   * @param {number} options.imageTimeout - 图片加载超时时间(ms)，默认10000\r\n   * @param {boolean} options.showLoading - 是否显示加载动画，默认true\r\n   */\r\n  init(dom, options = {}) {\r\n    const config = {\r\n      waitForImages: options.waitForImages !== false, // 默认等待图片加载\r\n      imageTimeout: options.imageTimeout || 10000,\r\n      showLoading: options.showLoading !== false // 默认显示加载动画\r\n    }\r\n    this.writeIframe(this.getStyle() + dom.outerHTML, config)\r\n  },\r\n  /**\r\n   * 复制原网页的样式\r\n   */\r\n  getStyle() {\r\n    var str = ''\r\n    var styles = []\r\n    const qiankun = new MicroGlobal()\r\n    if (window.__POWERED_BY_QIANKUN__) {\r\n      styles = document.querySelector(`#${qiankun.name}`).querySelectorAll('style,link')\r\n    } else {\r\n      styles = document.querySelector('head').querySelectorAll('style,link')\r\n    }\r\n    for (var i = 0; i < styles.length; i++) {\r\n      str += styles[i].outerHTML\r\n    }\r\n    str +=\r\n      '<style>*{margin: 0;padding: 0;-webkit-print-color-adjust: exact;}html,body{height: auto!important;box-sizing: border-box;}.no-print{display:none;}</style>'\r\n    return str\r\n  },\r\n  /**\r\n   * 创建iframe\r\n   */\r\n  writeIframe(content, config) {\r\n    var iframe = document.createElement('iframe')\r\n    var iframeEl = document.body.appendChild(iframe)\r\n    iframe.id = 'myIframe'\r\n    iframe.setAttribute('style', 'position:absolute;width:0;height:0;top:-10px;left:-10px;')\r\n\r\n    // 创建加载动画\r\n    let loadingInstance = null\r\n    if (config.showLoading) {\r\n      loadingInstance = ElLoading.service({\r\n        lock: true,\r\n        text: '正在准备打印内容...',\r\n        background: 'rgba(0, 0, 0, 0.7)',\r\n        customClass: 'print-loading'\r\n      })\r\n    }\r\n\r\n    const processPrint = (res) => {\r\n      const w = iframeEl.contentWindow || iframeEl.contentDocument\r\n      const doc = iframeEl.contentDocument || iframeEl.contentWindow.document\r\n      doc.open()\r\n      doc.write(content)\r\n      this.changeTheme(iframe, res?.data)\r\n\r\n      if (config.waitForImages) {\r\n        // 更新加载文本\r\n        if (loadingInstance) {\r\n          loadingInstance.setText('正在加载图片...')\r\n        }\r\n\r\n        // 等待所有图片加载完成\r\n        this.waitForImagesLoaded(doc, config.imageTimeout, loadingInstance)\r\n          .then(() => {\r\n            if (loadingInstance) {\r\n              loadingInstance.setText('正在生成打印预览...')\r\n            }\r\n            setTimeout(() => {\r\n              doc.close()\r\n              this.toPrint(w)\r\n              if (loadingInstance) {\r\n                loadingInstance.close()\r\n              }\r\n              setTimeout(() => {\r\n                document.body.removeChild(iframe)\r\n              }, 1000)\r\n            }, 500)\r\n          })\r\n          .catch((error) => {\r\n            // 如果图片加载超时，仍然进行打印\r\n            console.warn('图片加载超时，继续打印:', error.message)\r\n            if (loadingInstance) {\r\n              loadingInstance.setText('正在生成打印预览...')\r\n            }\r\n            setTimeout(() => {\r\n              doc.close()\r\n              this.toPrint(w)\r\n              if (loadingInstance) {\r\n                loadingInstance.close()\r\n              }\r\n              setTimeout(() => {\r\n                document.body.removeChild(iframe)\r\n              }, 1000)\r\n            }, 500)\r\n          })\r\n      } else {\r\n        // 不等待图片加载，直接打印\r\n        if (loadingInstance) {\r\n          loadingInstance.setText('正在生成打印预览...')\r\n        }\r\n        setTimeout(() => {\r\n          doc.close()\r\n          this.toPrint(w)\r\n          if (loadingInstance) {\r\n            loadingInstance.close()\r\n          }\r\n          setTimeout(() => {\r\n            document.body.removeChild(iframe)\r\n          }, 1000)\r\n        }, 500) // 给一个较短的延时让DOM渲染\r\n      }\r\n    }\r\n\r\n    api\r\n      .currentTheme({})\r\n      .then((res) => {\r\n        processPrint(res)\r\n      })\r\n      .catch(() => {\r\n        processPrint()\r\n      })\r\n  },\r\n  /**\r\n   * 等待所有图片加载完成\r\n   */\r\n  waitForImagesLoaded(doc, timeout = 10000, loadingInstance) {\r\n    return new Promise((resolve, reject) => {\r\n      const images = doc.querySelectorAll('img')\r\n      if (images.length === 0) {\r\n        resolve()\r\n        return\r\n      }\r\n\r\n      let loadedCount = 0\r\n      let errorCount = 0\r\n      const totalImages = images.length\r\n      const timeoutId = setTimeout(() => {\r\n        reject(new Error(`图片加载超时，已加载 ${loadedCount}/${totalImages} 张图片`))\r\n      }, timeout)\r\n\r\n      const checkComplete = () => {\r\n        loadedCount++\r\n        // 更新加载进度\r\n        if (loadingInstance) {\r\n          const progress = Math.round((loadedCount / totalImages) * 100)\r\n          loadingInstance.setText(`正在加载图片... (${loadedCount}/${totalImages}) ${progress}%`)\r\n        }\r\n\r\n        if (loadedCount === totalImages) {\r\n          clearTimeout(timeoutId)\r\n          if (errorCount > 0) {\r\n            console.warn(`图片加载完成，但有 ${errorCount} 张图片加载失败`)\r\n            if (loadingInstance) {\r\n              loadingInstance.setText(`图片加载完成，${errorCount} 张图片加载失败`)\r\n            }\r\n          }\r\n          resolve()\r\n        }\r\n      }\r\n\r\n      images.forEach((img, index) => {\r\n        if (img.complete && img.naturalHeight !== 0) {\r\n          // 图片已经加载完成\r\n          checkComplete()\r\n        } else {\r\n          // 监听图片加载事件\r\n          img.onload = () => {\r\n            checkComplete()\r\n          }\r\n          img.onerror = () => {\r\n            errorCount++\r\n            console.warn(`图片加载失败: ${img.src || `第${index + 1}张图片`}`)\r\n            checkComplete() // 即使加载失败也继续\r\n          }\r\n\r\n          // 如果图片src为空或者无效，直接算作完成\r\n          if (!img.src || img.src === '' || img.src === 'data:') {\r\n            checkComplete()\r\n          }\r\n        }\r\n      })\r\n    })\r\n  },\r\n  /**\r\n  打印\r\n  */\r\n  toPrint(f) {\r\n    try {\r\n      setTimeout(() => {\r\n        f.focus()\r\n        try {\r\n          if (!f.document.execCommand('print', false, null)) {\r\n            f.print()\r\n          }\r\n        } catch (e) {\r\n          f.print()\r\n        }\r\n        f.close()\r\n      }, 10)\r\n    } catch (err) {\r\n      console.log('err', err)\r\n    }\r\n  },\r\n  changeTheme(iframe, data) {\r\n    const obj = {\r\n      primary: data?.primaryColor || '#bc1d1d',\r\n      success: data?.success || '',\r\n      warning: data?.warning || '',\r\n      danger: data?.danger || '',\r\n      info: data?.info || '',\r\n      height: data?.height || '36px',\r\n      height_routine: data?.heightRoutine || '32px',\r\n      height_secondary: data?.heightSecondary || '26px',\r\n      line_height: data?.lineHeight || '1.5',\r\n      system_font_size: data?.systemFontSize || '28px',\r\n      title_font_size: data?.titleFontSize || '26px',\r\n      navigation_font_size: data?.navigationFontSize || '18px',\r\n      name_font_size: data?.nameFontSize || '16px',\r\n      text_font_size: data?.textFontSize || '14px',\r\n      text_color_primary: data?.textColorPrimary || '#303133',\r\n      text_color_regular: data?.textColorRegular || '#606266',\r\n      text_color_secondary: data?.textColorSecondary || '#909399',\r\n      border_color: data?.borderColor || '#dcdfe6',\r\n      border_color_light: data?.borderColorLight || '#e4e7ed',\r\n      border_color_lighter: data?.borderColorLighter || '#ebeef5',\r\n      border_color_extra_light: data?.borderColorExtraLight || '#f2f6fc',\r\n      distance_one: data?.distanceOne || '40px',\r\n      distance_two: data?.distanceTwo || '20px',\r\n      distance_three: data?.distanceThree || '16px',\r\n      distance_four: data?.distanceFour || '12px',\r\n      distance_five: data?.distanceFive || '10px',\r\n      font_name_distance_five: data?.fontNameDistanceFive || '6px',\r\n      font_text_distance_five: data?.fontTextDistanceFive || '3px',\r\n      border_radius_base: data?.borderRadiusBase || '4px',\r\n      border_radius_small: data?.borderRadiusSmall || '2px',\r\n      box_shadow: data?.boxShadow || '0px 12px 32px 4px rgba(0,0,0,.04),0px 8px 20px rgba(0,0,0,.08)',\r\n      box_shadow_light: data?.boxShadowLight || '0px 0px 12px rgba(0,0,0,.12)',\r\n      box_shadow_lighter: data?.boxShadowLighter || '0px 0px 6px rgba(0,0,0,.12)',\r\n      form_width_one:\r\n        data?.formWidthOne ||\r\n        'calc(var(--zy-distance-one) + (var(--zy-distance-two) * 4) + (var(--zy-form-width-item) * 3))',\r\n      form_width_two:\r\n        data?.formWidthTwo ||\r\n        'calc(var(--zy-distance-one) + (var(--zy-distance-two) * 3) + (var(--zy-form-width-item) * 2))',\r\n      form_width_item: data?.formWidthItem || '290px',\r\n      form_distance_bottom: data?.formDistanceBottom || '22px'\r\n    }\r\n    const el = iframe.contentWindow.document.documentElement\r\n    // 主题颜色\r\n    if (obj.primary) {\r\n      this.setThemeColor(el, 'primary', obj.primary)\r\n    }\r\n    if (obj.success && success !== '#67c23a') {\r\n      this.setThemeColor(el, 'success', obj.success)\r\n    }\r\n    if (obj.warning && warning !== '#e6a23c') {\r\n      this.setThemeColor(el, 'warning', obj.warning)\r\n    }\r\n    if (obj.danger && danger !== '#f56c6c') {\r\n      this.setThemeColor(el, 'danger', obj.danger)\r\n    }\r\n    if (obj.danger && danger !== '#f56c6c') {\r\n      this.setThemeColor(el, 'error', obj.danger)\r\n    }\r\n    if (obj.info && info !== '#909399') {\r\n      this.setThemeColor(el, 'info', obj.info)\r\n    }\r\n    // 高度\r\n    if (obj.height) {\r\n      el.style.setProperty('--zy-height', obj.height)\r\n    }\r\n    if (obj.height_routine) {\r\n      el.style.setProperty('--zy-height-routine', obj.height_routine)\r\n    }\r\n    if (obj.height_secondary) {\r\n      el.style.setProperty('--zy-height-secondary', obj.height_secondary)\r\n    }\r\n    // 行高\r\n    if (obj.line_height) {\r\n      el.style.setProperty('--zy-line-height', obj.line_height)\r\n    }\r\n    // 字体大小\r\n    if (obj.system_font_size) {\r\n      el.style.setProperty('--zy-system-font-size', obj.system_font_size)\r\n    }\r\n    if (obj.title_font_size) {\r\n      el.style.setProperty('--zy-title-font-size', obj.title_font_size)\r\n    }\r\n    if (obj.navigation_font_size) {\r\n      el.style.setProperty('--zy-navigation-font-size', obj.navigation_font_size)\r\n    }\r\n    if (obj.name_font_size) {\r\n      el.style.setProperty('--zy-name-font-size', obj.name_font_size)\r\n    }\r\n    if (obj.text_font_size) {\r\n      el.style.setProperty('--zy-text-font-size', obj.text_font_size)\r\n    }\r\n    // 文字颜色\r\n    if (obj.text_color_primary) {\r\n      el.style.setProperty('--zy-el-text-color-primary', obj.text_color_primary)\r\n    }\r\n    if (obj.text_color_regular) {\r\n      el.style.setProperty('--zy-el-text-color-regular', obj.text_color_regular)\r\n    }\r\n    if (obj.text_color_secondary) {\r\n      el.style.setProperty('--zy-el-text-color-secondary', obj.text_color_secondary)\r\n    }\r\n    // 边框颜色\r\n    if (obj.border_color) {\r\n      el.style.setProperty('--zy-el-border-color', obj.border_color)\r\n    }\r\n    if (obj.border_color_light) {\r\n      el.style.setProperty('--zy-el-border-color-light', obj.border_color_light)\r\n    }\r\n    if (obj.border_color_lighter) {\r\n      el.style.setProperty('--zy-el-border-color-lighter', obj.border_color_lighter)\r\n    }\r\n    if (obj.border_color_extra_light) {\r\n      el.style.setProperty('--zy-el-border-color-extra-light', obj.border_color_extra_light)\r\n    }\r\n    // 边距\r\n    if (obj.distance_one) {\r\n      el.style.setProperty('--zy-distance-one', obj.distance_one)\r\n    }\r\n    if (obj.distance_two) {\r\n      el.style.setProperty('--zy-distance-two', obj.distance_two)\r\n    }\r\n    if (obj.distance_three) {\r\n      el.style.setProperty('--zy-distance-three', obj.distance_three)\r\n    }\r\n    if (obj.distance_four) {\r\n      el.style.setProperty('--zy-distance-four', obj.distance_four)\r\n    }\r\n    if (obj.distance_five) {\r\n      el.style.setProperty('--zy-distance-five', obj.distance_five)\r\n    }\r\n    if (obj.font_name_distance_five) {\r\n      el.style.setProperty('--zy-font-name-distance-five', obj.font_name_distance_five)\r\n    }\r\n    if (obj.font_text_distance_five) {\r\n      el.style.setProperty('--zy-font-text-distance-five', obj.font_text_distance_five)\r\n    }\r\n    // 圆角\r\n    if (obj.border_radius_base) {\r\n      el.style.setProperty('--el-border-radius-base', obj.border_radius_base)\r\n    }\r\n    if (obj.border_radius_small) {\r\n      el.style.setProperty('--el-border-radius-small', obj.border_radius_small)\r\n    }\r\n    // 盒子阴影\r\n    if (obj.box_shadow) {\r\n      el.style.setProperty('--zy-el-box-shadow', obj.box_shadow)\r\n    }\r\n    if (obj.box_shadow_light) {\r\n      el.style.setProperty('--zy-el-box-shadow-light', obj.box_shadow_light)\r\n    }\r\n    if (obj.box_shadow_lighter) {\r\n      el.style.setProperty('--zy-el-box-shadow-lighter', obj.box_shadow_lighter)\r\n    }\r\n    // 表单宽度，边距\r\n    if (obj.form_width_one) {\r\n      el.style.setProperty('--zy-form-width-one', obj.form_width_one)\r\n    }\r\n    if (obj.form_width_two) {\r\n      el.style.setProperty('--zy-form-width-two', obj.form_width_two)\r\n    }\r\n    if (obj.form_width_item) {\r\n      el.style.setProperty('--zy-form-width-item', obj.form_width_item)\r\n    }\r\n    if (obj.form_distance_bottom) {\r\n      el.style.setProperty('--zy-form-distance-bottom', obj.form_distance_bottom)\r\n    }\r\n  },\r\n  setThemeColor(el, type, color) {\r\n    el.style.setProperty(`--zy-el-color-${type}`, color)\r\n    el.style.setProperty(`--zy-el-color-${type}-light-3`, this.colourBlend(color, '#ffffff', 3 / 10))\r\n    el.style.setProperty(`--zy-el-color-${type}-light-5`, this.colourBlend(color, '#ffffff', 5 / 10))\r\n    el.style.setProperty(`--zy-el-color-${type}-light-7`, this.colourBlend(color, '#ffffff', 7 / 10))\r\n    el.style.setProperty(`--zy-el-color-${type}-light-8`, this.colourBlend(color, '#ffffff', 8 / 10))\r\n    el.style.setProperty(`--zy-el-color-${type}-light-9`, this.colourBlend(color, '#ffffff', 9 / 10))\r\n    el.style.setProperty(`--zy-el-color-${type}-dark-2`, this.colourBlend(color, '#000000', 2 / 10))\r\n  },\r\n  colourBlend(c1, c2, ratio) {\r\n    ratio = Math.max(Math.min(Number(ratio), 1), 0)\r\n    let r1 = parseInt(c1.substring(1, 3), 16)\r\n    let g1 = parseInt(c1.substring(3, 5), 16)\r\n    let b1 = parseInt(c1.substring(5, 7), 16)\r\n    let r2 = parseInt(c2.substring(1, 3), 16)\r\n    let g2 = parseInt(c2.substring(3, 5), 16)\r\n    let b2 = parseInt(c2.substring(5, 7), 16)\r\n    let r = Math.round(r1 * (1 - ratio) + r2 * ratio)\r\n    let g = Math.round(g1 * (1 - ratio) + g2 * ratio)\r\n    let b = Math.round(b1 * (1 - ratio) + b2 * ratio)\r\n    r = ('0' + (r || 0).toString(16)).slice(-2)\r\n    g = ('0' + (g || 0).toString(16)).slice(-2)\r\n    b = ('0' + (b || 0).toString(16)).slice(-2)\r\n    return '#' + r + g + b\r\n  }\r\n}\r\n\r\n/*\r\n使用示例：\r\n\r\n// 基本使用（等待图片加载，显示加载动画）\r\nPrint.init(document.getElementById('printArea'))\r\n\r\n// 不等待图片加载，直接打印\r\nPrint.init(document.getElementById('printArea'), {\r\n  waitForImages: false\r\n})\r\n\r\n// 自定义图片加载超时时间\r\nPrint.init(document.getElementById('printArea'), {\r\n  imageTimeout: 15000 // 15秒超时\r\n})\r\n\r\n// 不显示加载动画\r\nPrint.init(document.getElementById('printArea'), {\r\n  showLoading: false\r\n})\r\n\r\n// 完整配置\r\nPrint.init(document.getElementById('printArea'), {\r\n  waitForImages: true,\r\n  imageTimeout: 20000,\r\n  showLoading: true\r\n})\r\n*/\r\n\r\n// 添加自定义样式\r\nconst style = document.createElement('style')\r\nstyle.textContent = `\r\n.print-loading .el-loading-spinner {\r\n  background: rgba(255, 255, 255, 0.9);\r\n  border-radius: 8px;\r\n  padding: 20px;\r\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.print-loading .el-loading-text {\r\n  color: #409eff;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n  margin-top: 10px;\r\n}\r\n\r\n.print-loading .el-loading-spinner .circular {\r\n  width: 42px;\r\n  height: 42px;\r\n  animation: loading-rotate 2s linear infinite;\r\n}\r\n\r\n.print-loading .el-loading-spinner .path {\r\n  stroke: #409eff;\r\n  stroke-width: 2;\r\n  stroke-dasharray: 90,150;\r\n  stroke-dashoffset: 0;\r\n  stroke-linecap: round;\r\n  animation: loading-dash 1.5s ease-in-out infinite;\r\n}\r\n\r\n@keyframes loading-rotate {\r\n  100% {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n@keyframes loading-dash {\r\n  0% {\r\n    stroke-dasharray: 1,200;\r\n    stroke-dashoffset: 0;\r\n  }\r\n  50% {\r\n    stroke-dasharray: 90,150;\r\n    stroke-dashoffset: -40px;\r\n  }\r\n  100% {\r\n    stroke-dasharray: 90,150;\r\n    stroke-dashoffset: -120px;\r\n  }\r\n}\r\n`\r\ndocument.head.appendChild(style)\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,OAAO;AACvB,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,SAAS,QAAQ,cAAc;AAExC,OAAO,IAAMC,KAAK,GAAG;EACnB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEC,IAAIA,CAACC,GAAG,EAAgB;IAAA,IAAdC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACpB,IAAMG,MAAM,GAAG;MACbC,aAAa,EAAEL,OAAO,CAACK,aAAa,KAAK,KAAK;MAAE;MAChDC,YAAY,EAAEN,OAAO,CAACM,YAAY,IAAI,KAAK;MAC3CC,WAAW,EAAEP,OAAO,CAACO,WAAW,KAAK,KAAK,CAAC;IAC7C,CAAC;IACD,IAAI,CAACC,WAAW,CAAC,IAAI,CAACC,QAAQ,CAAC,CAAC,GAAGV,GAAG,CAACW,SAAS,EAAEN,MAAM,CAAC;EAC3D,CAAC;EACD;AACF;AACA;EACEK,QAAQA,CAAA,EAAG;IACT,IAAIE,GAAG,GAAG,EAAE;IACZ,IAAIC,MAAM,GAAG,EAAE;IACf,IAAMC,OAAO,GAAG,IAAIlB,WAAW,CAAC,CAAC;IACjC,IAAImB,MAAM,CAACC,sBAAsB,EAAE;MACjCH,MAAM,GAAGI,QAAQ,CAACC,aAAa,CAAC,IAAIJ,OAAO,CAACK,IAAI,EAAE,CAAC,CAACC,gBAAgB,CAAC,YAAY,CAAC;IACpF,CAAC,MAAM;MACLP,MAAM,GAAGI,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC,CAACE,gBAAgB,CAAC,YAAY,CAAC;IACxE;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,MAAM,CAACV,MAAM,EAAEkB,CAAC,EAAE,EAAE;MACtCT,GAAG,IAAIC,MAAM,CAACQ,CAAC,CAAC,CAACV,SAAS;IAC5B;IACAC,GAAG,IACD,4JAA4J;IAC9J,OAAOA,GAAG;EACZ,CAAC;EACD;AACF;AACA;EACEH,WAAWA,CAACa,OAAO,EAAEjB,MAAM,EAAE;IAAA,IAAAkB,KAAA;IAC3B,IAAIC,MAAM,GAAGP,QAAQ,CAACQ,aAAa,CAAC,QAAQ,CAAC;IAC7C,IAAIC,QAAQ,GAAGT,QAAQ,CAACU,IAAI,CAACC,WAAW,CAACJ,MAAM,CAAC;IAChDA,MAAM,CAACK,EAAE,GAAG,UAAU;IACtBL,MAAM,CAACM,YAAY,CAAC,OAAO,EAAE,0DAA0D,CAAC;;IAExF;IACA,IAAIC,eAAe,GAAG,IAAI;IAC1B,IAAI1B,MAAM,CAACG,WAAW,EAAE;MACtBuB,eAAe,GAAGlC,SAAS,CAACmC,OAAO,CAAC;QAClCC,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE,aAAa;QACnBC,UAAU,EAAE,oBAAoB;QAChCC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ;IAEA,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,GAAG,EAAK;MAC5B,IAAMC,CAAC,GAAGb,QAAQ,CAACc,aAAa,IAAId,QAAQ,CAACe,eAAe;MAC5D,IAAMC,GAAG,GAAGhB,QAAQ,CAACe,eAAe,IAAIf,QAAQ,CAACc,aAAa,CAACvB,QAAQ;MACvEyB,GAAG,CAACC,IAAI,CAAC,CAAC;MACVD,GAAG,CAACE,KAAK,CAACtB,OAAO,CAAC;MAClBC,KAAI,CAACsB,WAAW,CAACrB,MAAM,EAAEc,GAAG,aAAHA,GAAG,uBAAHA,GAAG,CAAEQ,IAAI,CAAC;MAEnC,IAAIzC,MAAM,CAACC,aAAa,EAAE;QACxB;QACA,IAAIyB,eAAe,EAAE;UACnBA,eAAe,CAACgB,OAAO,CAAC,WAAW,CAAC;QACtC;;QAEA;QACAxB,KAAI,CAACyB,mBAAmB,CAACN,GAAG,EAAErC,MAAM,CAACE,YAAY,EAAEwB,eAAe,CAAC,CAChEkB,IAAI,CAAC,YAAM;UACV,IAAIlB,eAAe,EAAE;YACnBA,eAAe,CAACgB,OAAO,CAAC,aAAa,CAAC;UACxC;UACAG,UAAU,CAAC,YAAM;YACfR,GAAG,CAACS,KAAK,CAAC,CAAC;YACX5B,KAAI,CAAC6B,OAAO,CAACb,CAAC,CAAC;YACf,IAAIR,eAAe,EAAE;cACnBA,eAAe,CAACoB,KAAK,CAAC,CAAC;YACzB;YACAD,UAAU,CAAC,YAAM;cACfjC,QAAQ,CAACU,IAAI,CAAC0B,WAAW,CAAC7B,MAAM,CAAC;YACnC,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,CAAC,CACD8B,KAAK,CAAC,UAACC,KAAK,EAAK;UAChB;UACAC,OAAO,CAACC,IAAI,CAAC,cAAc,EAAEF,KAAK,CAACG,OAAO,CAAC;UAC3C,IAAI3B,eAAe,EAAE;YACnBA,eAAe,CAACgB,OAAO,CAAC,aAAa,CAAC;UACxC;UACAG,UAAU,CAAC,YAAM;YACfR,GAAG,CAACS,KAAK,CAAC,CAAC;YACX5B,KAAI,CAAC6B,OAAO,CAACb,CAAC,CAAC;YACf,IAAIR,eAAe,EAAE;cACnBA,eAAe,CAACoB,KAAK,CAAC,CAAC;YACzB;YACAD,UAAU,CAAC,YAAM;cACfjC,QAAQ,CAACU,IAAI,CAAC0B,WAAW,CAAC7B,MAAM,CAAC;YACnC,CAAC,EAAE,IAAI,CAAC;UACV,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,CAAC;MACN,CAAC,MAAM;QACL;QACA,IAAIO,eAAe,EAAE;UACnBA,eAAe,CAACgB,OAAO,CAAC,aAAa,CAAC;QACxC;QACAG,UAAU,CAAC,YAAM;UACfR,GAAG,CAACS,KAAK,CAAC,CAAC;UACX5B,KAAI,CAAC6B,OAAO,CAACb,CAAC,CAAC;UACf,IAAIR,eAAe,EAAE;YACnBA,eAAe,CAACoB,KAAK,CAAC,CAAC;UACzB;UACAD,UAAU,CAAC,YAAM;YACfjC,QAAQ,CAACU,IAAI,CAAC0B,WAAW,CAAC7B,MAAM,CAAC;UACnC,CAAC,EAAE,IAAI,CAAC;QACV,CAAC,EAAE,GAAG,CAAC,EAAC;MACV;IACF,CAAC;IAED7B,GAAG,CACAgE,YAAY,CAAC,CAAC,CAAC,CAAC,CAChBV,IAAI,CAAC,UAACX,GAAG,EAAK;MACbD,YAAY,CAACC,GAAG,CAAC;IACnB,CAAC,CAAC,CACDgB,KAAK,CAAC,YAAM;MACXjB,YAAY,CAAC,CAAC;IAChB,CAAC,CAAC;EACN,CAAC;EACD;AACF;AACA;EACEW,mBAAmBA,CAACN,GAAG,EAAoC;IAAA,IAAlCkB,OAAO,GAAA1D,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;IAAA,IAAE6B,eAAe,GAAA7B,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;IACvD,OAAO,IAAIyD,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;MACtC,IAAMC,MAAM,GAAGtB,GAAG,CAACtB,gBAAgB,CAAC,KAAK,CAAC;MAC1C,IAAI4C,MAAM,CAAC7D,MAAM,KAAK,CAAC,EAAE;QACvB2D,OAAO,CAAC,CAAC;QACT;MACF;MAEA,IAAIG,WAAW,GAAG,CAAC;MACnB,IAAIC,UAAU,GAAG,CAAC;MAClB,IAAMC,WAAW,GAAGH,MAAM,CAAC7D,MAAM;MACjC,IAAMiE,SAAS,GAAGlB,UAAU,CAAC,YAAM;QACjCa,MAAM,CAAC,IAAIM,KAAK,CAAC,cAAcJ,WAAW,IAAIE,WAAW,MAAM,CAAC,CAAC;MACnE,CAAC,EAAEP,OAAO,CAAC;MAEX,IAAMU,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;QAC1BL,WAAW,EAAE;QACb;QACA,IAAIlC,eAAe,EAAE;UACnB,IAAMwC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAER,WAAW,GAAGE,WAAW,GAAI,GAAG,CAAC;UAC9DpC,eAAe,CAACgB,OAAO,CAAC,cAAckB,WAAW,IAAIE,WAAW,KAAKI,QAAQ,GAAG,CAAC;QACnF;QAEA,IAAIN,WAAW,KAAKE,WAAW,EAAE;UAC/BO,YAAY,CAACN,SAAS,CAAC;UACvB,IAAIF,UAAU,GAAG,CAAC,EAAE;YAClBV,OAAO,CAACC,IAAI,CAAC,aAAaS,UAAU,UAAU,CAAC;YAC/C,IAAInC,eAAe,EAAE;cACnBA,eAAe,CAACgB,OAAO,CAAC,UAAUmB,UAAU,UAAU,CAAC;YACzD;UACF;UACAJ,OAAO,CAAC,CAAC;QACX;MACF,CAAC;MAEDE,MAAM,CAACW,OAAO,CAAC,UAACC,GAAG,EAAEC,KAAK,EAAK;QAC7B,IAAID,GAAG,CAACE,QAAQ,IAAIF,GAAG,CAACG,aAAa,KAAK,CAAC,EAAE;UAC3C;UACAT,aAAa,CAAC,CAAC;QACjB,CAAC,MAAM;UACL;UACAM,GAAG,CAACI,MAAM,GAAG,YAAM;YACjBV,aAAa,CAAC,CAAC;UACjB,CAAC;UACDM,GAAG,CAACK,OAAO,GAAG,YAAM;YAClBf,UAAU,EAAE;YACZV,OAAO,CAACC,IAAI,CAAC,WAAWmB,GAAG,CAACM,GAAG,IAAI,IAAIL,KAAK,GAAG,CAAC,KAAK,EAAE,CAAC;YACxDP,aAAa,CAAC,CAAC,EAAC;UAClB,CAAC;;UAED;UACA,IAAI,CAACM,GAAG,CAACM,GAAG,IAAIN,GAAG,CAACM,GAAG,KAAK,EAAE,IAAIN,GAAG,CAACM,GAAG,KAAK,OAAO,EAAE;YACrDZ,aAAa,CAAC,CAAC;UACjB;QACF;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD;AACF;AACA;EACElB,OAAOA,CAAC+B,CAAC,EAAE;IACT,IAAI;MACFjC,UAAU,CAAC,YAAM;QACfiC,CAAC,CAACC,KAAK,CAAC,CAAC;QACT,IAAI;UACF,IAAI,CAACD,CAAC,CAAClE,QAAQ,CAACoE,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,EAAE;YACjDF,CAAC,CAACG,KAAK,CAAC,CAAC;UACX;QACF,CAAC,CAAC,OAAOC,CAAC,EAAE;UACVJ,CAAC,CAACG,KAAK,CAAC,CAAC;QACX;QACAH,CAAC,CAAChC,KAAK,CAAC,CAAC;MACX,CAAC,EAAE,EAAE,CAAC;IACR,CAAC,CAAC,OAAOqC,GAAG,EAAE;MACZhC,OAAO,CAACiC,GAAG,CAAC,KAAK,EAAED,GAAG,CAAC;IACzB;EACF,CAAC;EACD3C,WAAWA,CAACrB,MAAM,EAAEsB,IAAI,EAAE;IACxB,IAAM4C,GAAG,GAAG;MACVC,OAAO,EAAE,CAAA7C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE8C,YAAY,KAAI,SAAS;MACxCC,OAAO,EAAE,CAAA/C,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+C,OAAO,KAAI,EAAE;MAC5BC,OAAO,EAAE,CAAAhD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEgD,OAAO,KAAI,EAAE;MAC5BC,MAAM,EAAE,CAAAjD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiD,MAAM,KAAI,EAAE;MAC1BC,IAAI,EAAE,CAAAlD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEkD,IAAI,KAAI,EAAE;MACtBC,MAAM,EAAE,CAAAnD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmD,MAAM,KAAI,MAAM;MAC9BC,cAAc,EAAE,CAAApD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqD,aAAa,KAAI,MAAM;MAC7CC,gBAAgB,EAAE,CAAAtD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuD,eAAe,KAAI,MAAM;MACjDC,WAAW,EAAE,CAAAxD,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyD,UAAU,KAAI,KAAK;MACtCC,gBAAgB,EAAE,CAAA1D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2D,cAAc,KAAI,MAAM;MAChDC,eAAe,EAAE,CAAA5D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6D,aAAa,KAAI,MAAM;MAC9CC,oBAAoB,EAAE,CAAA9D,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+D,kBAAkB,KAAI,MAAM;MACxDC,cAAc,EAAE,CAAAhE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiE,YAAY,KAAI,MAAM;MAC5CC,cAAc,EAAE,CAAAlE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmE,YAAY,KAAI,MAAM;MAC5CC,kBAAkB,EAAE,CAAApE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqE,gBAAgB,KAAI,SAAS;MACvDC,kBAAkB,EAAE,CAAAtE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuE,gBAAgB,KAAI,SAAS;MACvDC,oBAAoB,EAAE,CAAAxE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyE,kBAAkB,KAAI,SAAS;MAC3DC,YAAY,EAAE,CAAA1E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2E,WAAW,KAAI,SAAS;MAC5CC,kBAAkB,EAAE,CAAA5E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6E,gBAAgB,KAAI,SAAS;MACvDC,oBAAoB,EAAE,CAAA9E,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+E,kBAAkB,KAAI,SAAS;MAC3DC,wBAAwB,EAAE,CAAAhF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiF,qBAAqB,KAAI,SAAS;MAClEC,YAAY,EAAE,CAAAlF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmF,WAAW,KAAI,MAAM;MACzCC,YAAY,EAAE,CAAApF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqF,WAAW,KAAI,MAAM;MACzCC,cAAc,EAAE,CAAAtF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuF,aAAa,KAAI,MAAM;MAC7CC,aAAa,EAAE,CAAAxF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyF,YAAY,KAAI,MAAM;MAC3CC,aAAa,EAAE,CAAA1F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2F,YAAY,KAAI,MAAM;MAC3CC,uBAAuB,EAAE,CAAA5F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6F,oBAAoB,KAAI,KAAK;MAC5DC,uBAAuB,EAAE,CAAA9F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+F,oBAAoB,KAAI,KAAK;MAC5DC,kBAAkB,EAAE,CAAAhG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiG,gBAAgB,KAAI,KAAK;MACnDC,mBAAmB,EAAE,CAAAlG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEmG,iBAAiB,KAAI,KAAK;MACrDC,UAAU,EAAE,CAAApG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEqG,SAAS,KAAI,gEAAgE;MAC/FC,gBAAgB,EAAE,CAAAtG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEuG,cAAc,KAAI,8BAA8B;MACxEC,kBAAkB,EAAE,CAAAxG,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEyG,gBAAgB,KAAI,6BAA6B;MAC3EC,cAAc,EACZ,CAAA1G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE2G,YAAY,KAClB,+FAA+F;MACjGC,cAAc,EACZ,CAAA5G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6G,YAAY,KAClB,+FAA+F;MACjGC,eAAe,EAAE,CAAA9G,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+G,aAAa,KAAI,OAAO;MAC/CC,oBAAoB,EAAE,CAAAhH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEiH,kBAAkB,KAAI;IACpD,CAAC;IACD,IAAMC,EAAE,GAAGxI,MAAM,CAACgB,aAAa,CAACvB,QAAQ,CAACgJ,eAAe;IACxD;IACA,IAAIvE,GAAG,CAACC,OAAO,EAAE;MACf,IAAI,CAACuE,aAAa,CAACF,EAAE,EAAE,SAAS,EAAEtE,GAAG,CAACC,OAAO,CAAC;IAChD;IACA,IAAID,GAAG,CAACG,OAAO,IAAIA,OAAO,KAAK,SAAS,EAAE;MACxC,IAAI,CAACqE,aAAa,CAACF,EAAE,EAAE,SAAS,EAAEtE,GAAG,CAACG,OAAO,CAAC;IAChD;IACA,IAAIH,GAAG,CAACI,OAAO,IAAIA,OAAO,KAAK,SAAS,EAAE;MACxC,IAAI,CAACoE,aAAa,CAACF,EAAE,EAAE,SAAS,EAAEtE,GAAG,CAACI,OAAO,CAAC;IAChD;IACA,IAAIJ,GAAG,CAACK,MAAM,IAAIA,MAAM,KAAK,SAAS,EAAE;MACtC,IAAI,CAACmE,aAAa,CAACF,EAAE,EAAE,QAAQ,EAAEtE,GAAG,CAACK,MAAM,CAAC;IAC9C;IACA,IAAIL,GAAG,CAACK,MAAM,IAAIA,MAAM,KAAK,SAAS,EAAE;MACtC,IAAI,CAACmE,aAAa,CAACF,EAAE,EAAE,OAAO,EAAEtE,GAAG,CAACK,MAAM,CAAC;IAC7C;IACA,IAAIL,GAAG,CAACM,IAAI,IAAIA,IAAI,KAAK,SAAS,EAAE;MAClC,IAAI,CAACkE,aAAa,CAACF,EAAE,EAAE,MAAM,EAAEtE,GAAG,CAACM,IAAI,CAAC;IAC1C;IACA;IACA,IAAIN,GAAG,CAACO,MAAM,EAAE;MACd+D,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,aAAa,EAAE1E,GAAG,CAACO,MAAM,CAAC;IACjD;IACA,IAAIP,GAAG,CAACQ,cAAc,EAAE;MACtB8D,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,qBAAqB,EAAE1E,GAAG,CAACQ,cAAc,CAAC;IACjE;IACA,IAAIR,GAAG,CAACU,gBAAgB,EAAE;MACxB4D,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,uBAAuB,EAAE1E,GAAG,CAACU,gBAAgB,CAAC;IACrE;IACA;IACA,IAAIV,GAAG,CAACY,WAAW,EAAE;MACnB0D,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,kBAAkB,EAAE1E,GAAG,CAACY,WAAW,CAAC;IAC3D;IACA;IACA,IAAIZ,GAAG,CAACc,gBAAgB,EAAE;MACxBwD,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,uBAAuB,EAAE1E,GAAG,CAACc,gBAAgB,CAAC;IACrE;IACA,IAAId,GAAG,CAACgB,eAAe,EAAE;MACvBsD,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,sBAAsB,EAAE1E,GAAG,CAACgB,eAAe,CAAC;IACnE;IACA,IAAIhB,GAAG,CAACkB,oBAAoB,EAAE;MAC5BoD,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,2BAA2B,EAAE1E,GAAG,CAACkB,oBAAoB,CAAC;IAC7E;IACA,IAAIlB,GAAG,CAACoB,cAAc,EAAE;MACtBkD,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,qBAAqB,EAAE1E,GAAG,CAACoB,cAAc,CAAC;IACjE;IACA,IAAIpB,GAAG,CAACsB,cAAc,EAAE;MACtBgD,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,qBAAqB,EAAE1E,GAAG,CAACsB,cAAc,CAAC;IACjE;IACA;IACA,IAAItB,GAAG,CAACwB,kBAAkB,EAAE;MAC1B8C,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,4BAA4B,EAAE1E,GAAG,CAACwB,kBAAkB,CAAC;IAC5E;IACA,IAAIxB,GAAG,CAAC0B,kBAAkB,EAAE;MAC1B4C,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,4BAA4B,EAAE1E,GAAG,CAAC0B,kBAAkB,CAAC;IAC5E;IACA,IAAI1B,GAAG,CAAC4B,oBAAoB,EAAE;MAC5B0C,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,8BAA8B,EAAE1E,GAAG,CAAC4B,oBAAoB,CAAC;IAChF;IACA;IACA,IAAI5B,GAAG,CAAC8B,YAAY,EAAE;MACpBwC,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,sBAAsB,EAAE1E,GAAG,CAAC8B,YAAY,CAAC;IAChE;IACA,IAAI9B,GAAG,CAACgC,kBAAkB,EAAE;MAC1BsC,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,4BAA4B,EAAE1E,GAAG,CAACgC,kBAAkB,CAAC;IAC5E;IACA,IAAIhC,GAAG,CAACkC,oBAAoB,EAAE;MAC5BoC,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,8BAA8B,EAAE1E,GAAG,CAACkC,oBAAoB,CAAC;IAChF;IACA,IAAIlC,GAAG,CAACoC,wBAAwB,EAAE;MAChCkC,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,kCAAkC,EAAE1E,GAAG,CAACoC,wBAAwB,CAAC;IACxF;IACA;IACA,IAAIpC,GAAG,CAACsC,YAAY,EAAE;MACpBgC,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,mBAAmB,EAAE1E,GAAG,CAACsC,YAAY,CAAC;IAC7D;IACA,IAAItC,GAAG,CAACwC,YAAY,EAAE;MACpB8B,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,mBAAmB,EAAE1E,GAAG,CAACwC,YAAY,CAAC;IAC7D;IACA,IAAIxC,GAAG,CAAC0C,cAAc,EAAE;MACtB4B,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,qBAAqB,EAAE1E,GAAG,CAAC0C,cAAc,CAAC;IACjE;IACA,IAAI1C,GAAG,CAAC4C,aAAa,EAAE;MACrB0B,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,oBAAoB,EAAE1E,GAAG,CAAC4C,aAAa,CAAC;IAC/D;IACA,IAAI5C,GAAG,CAAC8C,aAAa,EAAE;MACrBwB,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,oBAAoB,EAAE1E,GAAG,CAAC8C,aAAa,CAAC;IAC/D;IACA,IAAI9C,GAAG,CAACgD,uBAAuB,EAAE;MAC/BsB,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,8BAA8B,EAAE1E,GAAG,CAACgD,uBAAuB,CAAC;IACnF;IACA,IAAIhD,GAAG,CAACkD,uBAAuB,EAAE;MAC/BoB,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,8BAA8B,EAAE1E,GAAG,CAACkD,uBAAuB,CAAC;IACnF;IACA;IACA,IAAIlD,GAAG,CAACoD,kBAAkB,EAAE;MAC1BkB,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,yBAAyB,EAAE1E,GAAG,CAACoD,kBAAkB,CAAC;IACzE;IACA,IAAIpD,GAAG,CAACsD,mBAAmB,EAAE;MAC3BgB,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,0BAA0B,EAAE1E,GAAG,CAACsD,mBAAmB,CAAC;IAC3E;IACA;IACA,IAAItD,GAAG,CAACwD,UAAU,EAAE;MAClBc,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,oBAAoB,EAAE1E,GAAG,CAACwD,UAAU,CAAC;IAC5D;IACA,IAAIxD,GAAG,CAAC0D,gBAAgB,EAAE;MACxBY,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,0BAA0B,EAAE1E,GAAG,CAAC0D,gBAAgB,CAAC;IACxE;IACA,IAAI1D,GAAG,CAAC4D,kBAAkB,EAAE;MAC1BU,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,4BAA4B,EAAE1E,GAAG,CAAC4D,kBAAkB,CAAC;IAC5E;IACA;IACA,IAAI5D,GAAG,CAAC8D,cAAc,EAAE;MACtBQ,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,qBAAqB,EAAE1E,GAAG,CAAC8D,cAAc,CAAC;IACjE;IACA,IAAI9D,GAAG,CAACgE,cAAc,EAAE;MACtBM,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,qBAAqB,EAAE1E,GAAG,CAACgE,cAAc,CAAC;IACjE;IACA,IAAIhE,GAAG,CAACkE,eAAe,EAAE;MACvBI,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,sBAAsB,EAAE1E,GAAG,CAACkE,eAAe,CAAC;IACnE;IACA,IAAIlE,GAAG,CAACoE,oBAAoB,EAAE;MAC5BE,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,2BAA2B,EAAE1E,GAAG,CAACoE,oBAAoB,CAAC;IAC7E;EACF,CAAC;EACDI,aAAaA,CAACF,EAAE,EAAEK,IAAI,EAAEC,KAAK,EAAE;IAC7BN,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,iBAAiBC,IAAI,EAAE,EAAEC,KAAK,CAAC;IACpDN,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,iBAAiBC,IAAI,UAAU,EAAE,IAAI,CAACE,WAAW,CAACD,KAAK,EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IACjGN,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,iBAAiBC,IAAI,UAAU,EAAE,IAAI,CAACE,WAAW,CAACD,KAAK,EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IACjGN,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,iBAAiBC,IAAI,UAAU,EAAE,IAAI,CAACE,WAAW,CAACD,KAAK,EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IACjGN,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,iBAAiBC,IAAI,UAAU,EAAE,IAAI,CAACE,WAAW,CAACD,KAAK,EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IACjGN,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,iBAAiBC,IAAI,UAAU,EAAE,IAAI,CAACE,WAAW,CAACD,KAAK,EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IACjGN,EAAE,CAACG,KAAK,CAACC,WAAW,CAAC,iBAAiBC,IAAI,SAAS,EAAE,IAAI,CAACE,WAAW,CAACD,KAAK,EAAE,SAAS,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;EAClG,CAAC;EACDC,WAAWA,CAACC,EAAE,EAAEC,EAAE,EAAEC,KAAK,EAAE;IACzBA,KAAK,GAAGlG,IAAI,CAACmG,GAAG,CAACnG,IAAI,CAACoG,GAAG,CAACC,MAAM,CAACH,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/C,IAAII,EAAE,GAAGC,QAAQ,CAACP,EAAE,CAACQ,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACzC,IAAIC,EAAE,GAAGF,QAAQ,CAACP,EAAE,CAACQ,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACzC,IAAIE,EAAE,GAAGH,QAAQ,CAACP,EAAE,CAACQ,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACzC,IAAIG,EAAE,GAAGJ,QAAQ,CAACN,EAAE,CAACO,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACzC,IAAII,EAAE,GAAGL,QAAQ,CAACN,EAAE,CAACO,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACzC,IAAIK,EAAE,GAAGN,QAAQ,CAACN,EAAE,CAACO,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC;IACzC,IAAIM,CAAC,GAAG9G,IAAI,CAACC,KAAK,CAACqG,EAAE,IAAI,CAAC,GAAGJ,KAAK,CAAC,GAAGS,EAAE,GAAGT,KAAK,CAAC;IACjD,IAAIa,CAAC,GAAG/G,IAAI,CAACC,KAAK,CAACwG,EAAE,IAAI,CAAC,GAAGP,KAAK,CAAC,GAAGU,EAAE,GAAGV,KAAK,CAAC;IACjD,IAAIc,CAAC,GAAGhH,IAAI,CAACC,KAAK,CAACyG,EAAE,IAAI,CAAC,GAAGR,KAAK,CAAC,GAAGW,EAAE,GAAGX,KAAK,CAAC;IACjDY,CAAC,GAAG,CAAC,GAAG,GAAG,CAACA,CAAC,IAAI,CAAC,EAAEG,QAAQ,CAAC,EAAE,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3CH,CAAC,GAAG,CAAC,GAAG,GAAG,CAACA,CAAC,IAAI,CAAC,EAAEE,QAAQ,CAAC,EAAE,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3CF,CAAC,GAAG,CAAC,GAAG,GAAG,CAACA,CAAC,IAAI,CAAC,EAAEC,QAAQ,CAAC,EAAE,CAAC,EAAEC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3C,OAAO,GAAG,GAAGJ,CAAC,GAAGC,CAAC,GAAGC,CAAC;EACxB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,IAAMrB,KAAK,GAAGlJ,QAAQ,CAACQ,aAAa,CAAC,OAAO,CAAC;AAC7C0I,KAAK,CAACwB,WAAW,GAAG;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AACD1K,QAAQ,CAAC2K,IAAI,CAAChK,WAAW,CAACuI,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}