{"ast": null, "code": "/*\nLanguage: C#\nAuthor: <PERSON> <<EMAIL>>\nContributor: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://docs.microsoft.com/dotnet/csharp/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction csharp(hljs) {\n  var BUILT_IN_KEYWORDS = ['bool', 'byte', 'char', 'decimal', 'delegate', 'double', 'dynamic', 'enum', 'float', 'int', 'long', 'nint', 'nuint', 'object', 'sbyte', 'short', 'string', 'ulong', 'uint', 'ushort'];\n  var FUNCTION_MODIFIERS = ['public', 'private', 'protected', 'static', 'internal', 'protected', 'abstract', 'async', 'extern', 'override', 'unsafe', 'virtual', 'new', 'sealed', 'partial'];\n  var LITERAL_KEYWORDS = ['default', 'false', 'null', 'true'];\n  var NORMAL_KEYWORDS = ['abstract', 'as', 'base', 'break', 'case', 'catch', 'class', 'const', 'continue', 'do', 'else', 'event', 'explicit', 'extern', 'finally', 'fixed', 'for', 'foreach', 'goto', 'if', 'implicit', 'in', 'interface', 'internal', 'is', 'lock', 'namespace', 'new', 'operator', 'out', 'override', 'params', 'private', 'protected', 'public', 'readonly', 'record', 'ref', 'return', 'scoped', 'sealed', 'sizeof', 'stackalloc', 'static', 'struct', 'switch', 'this', 'throw', 'try', 'typeof', 'unchecked', 'unsafe', 'using', 'virtual', 'void', 'volatile', 'while'];\n  var CONTEXTUAL_KEYWORDS = ['add', 'alias', 'and', 'ascending', 'args', 'async', 'await', 'by', 'descending', 'dynamic', 'equals', 'file', 'from', 'get', 'global', 'group', 'init', 'into', 'join', 'let', 'nameof', 'not', 'notnull', 'on', 'or', 'orderby', 'partial', 'record', 'remove', 'required', 'scoped', 'select', 'set', 'unmanaged', 'value|0', 'var', 'when', 'where', 'with', 'yield'];\n  var KEYWORDS = {\n    keyword: NORMAL_KEYWORDS.concat(CONTEXTUAL_KEYWORDS),\n    built_in: BUILT_IN_KEYWORDS,\n    literal: LITERAL_KEYWORDS\n  };\n  var TITLE_MODE = hljs.inherit(hljs.TITLE_MODE, {\n    begin: '[a-zA-Z](\\\\.?\\\\w)*'\n  });\n  var NUMBERS = {\n    className: 'number',\n    variants: [{\n      begin: '\\\\b(0b[01\\']+)'\n    }, {\n      begin: '(-?)\\\\b([\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)(u|U|l|L|ul|UL|f|F|b|B)'\n    }, {\n      begin: '(-?)(\\\\b0[xX][a-fA-F0-9\\']+|(\\\\b[\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)([eE][-+]?[\\\\d\\']+)?)'\n    }],\n    relevance: 0\n  };\n  var RAW_STRING = {\n    className: 'string',\n    begin: /\"\"\"(\"*)(?!\")(.|\\n)*?\"\"\"\\1/,\n    relevance: 1\n  };\n  var VERBATIM_STRING = {\n    className: 'string',\n    begin: '@\"',\n    end: '\"',\n    contains: [{\n      begin: '\"\"'\n    }]\n  };\n  var VERBATIM_STRING_NO_LF = hljs.inherit(VERBATIM_STRING, {\n    illegal: /\\n/\n  });\n  var SUBST = {\n    className: 'subst',\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS\n  };\n  var SUBST_NO_LF = hljs.inherit(SUBST, {\n    illegal: /\\n/\n  });\n  var INTERPOLATED_STRING = {\n    className: 'string',\n    begin: /\\$\"/,\n    end: '\"',\n    illegal: /\\n/,\n    contains: [{\n      begin: /\\{\\{/\n    }, {\n      begin: /\\}\\}/\n    }, hljs.BACKSLASH_ESCAPE, SUBST_NO_LF]\n  };\n  var INTERPOLATED_VERBATIM_STRING = {\n    className: 'string',\n    begin: /\\$@\"/,\n    end: '\"',\n    contains: [{\n      begin: /\\{\\{/\n    }, {\n      begin: /\\}\\}/\n    }, {\n      begin: '\"\"'\n    }, SUBST]\n  };\n  var INTERPOLATED_VERBATIM_STRING_NO_LF = hljs.inherit(INTERPOLATED_VERBATIM_STRING, {\n    illegal: /\\n/,\n    contains: [{\n      begin: /\\{\\{/\n    }, {\n      begin: /\\}\\}/\n    }, {\n      begin: '\"\"'\n    }, SUBST_NO_LF]\n  });\n  SUBST.contains = [INTERPOLATED_VERBATIM_STRING, INTERPOLATED_STRING, VERBATIM_STRING, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, NUMBERS, hljs.C_BLOCK_COMMENT_MODE];\n  SUBST_NO_LF.contains = [INTERPOLATED_VERBATIM_STRING_NO_LF, INTERPOLATED_STRING, VERBATIM_STRING_NO_LF, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, NUMBERS, hljs.inherit(hljs.C_BLOCK_COMMENT_MODE, {\n    illegal: /\\n/\n  })];\n  var STRING = {\n    variants: [RAW_STRING, INTERPOLATED_VERBATIM_STRING, INTERPOLATED_STRING, VERBATIM_STRING, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE]\n  };\n  var GENERIC_MODIFIER = {\n    begin: \"<\",\n    end: \">\",\n    contains: [{\n      beginKeywords: \"in out\"\n    }, TITLE_MODE]\n  };\n  var TYPE_IDENT_RE = hljs.IDENT_RE + '(<' + hljs.IDENT_RE + '(\\\\s*,\\\\s*' + hljs.IDENT_RE + ')*>)?(\\\\[\\\\])?';\n  var AT_IDENTIFIER = {\n    // prevents expressions like `@class` from incorrect flagging\n    // `class` as a keyword\n    begin: \"@\" + hljs.IDENT_RE,\n    relevance: 0\n  };\n  return {\n    name: 'C#',\n    aliases: ['cs', 'c#'],\n    keywords: KEYWORDS,\n    illegal: /::/,\n    contains: [hljs.COMMENT('///', '$', {\n      returnBegin: true,\n      contains: [{\n        className: 'doctag',\n        variants: [{\n          begin: '///',\n          relevance: 0\n        }, {\n          begin: '<!--|-->'\n        }, {\n          begin: '</?',\n          end: '>'\n        }]\n      }]\n    }), hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, {\n      className: 'meta',\n      begin: '#',\n      end: '$',\n      keywords: {\n        keyword: 'if else elif endif define undef warning error line region endregion pragma checksum'\n      }\n    }, STRING, NUMBERS, {\n      beginKeywords: 'class interface',\n      relevance: 0,\n      end: /[{;=]/,\n      illegal: /[^\\s:,]/,\n      contains: [{\n        beginKeywords: \"where class\"\n      }, TITLE_MODE, GENERIC_MODIFIER, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n    }, {\n      beginKeywords: 'namespace',\n      relevance: 0,\n      end: /[{;=]/,\n      illegal: /[^\\s:]/,\n      contains: [TITLE_MODE, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n    }, {\n      beginKeywords: 'record',\n      relevance: 0,\n      end: /[{;=]/,\n      illegal: /[^\\s:]/,\n      contains: [TITLE_MODE, GENERIC_MODIFIER, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n    }, {\n      // [Attributes(\"\")]\n      className: 'meta',\n      begin: '^\\\\s*\\\\[(?=[\\\\w])',\n      excludeBegin: true,\n      end: '\\\\]',\n      excludeEnd: true,\n      contains: [{\n        className: 'string',\n        begin: /\"/,\n        end: /\"/\n      }]\n    }, {\n      // Expression keywords prevent 'keyword Name(...)' from being\n      // recognized as a function definition\n      beginKeywords: 'new return throw await else',\n      relevance: 0\n    }, {\n      className: 'function',\n      begin: '(' + TYPE_IDENT_RE + '\\\\s+)+' + hljs.IDENT_RE + '\\\\s*(<[^=]+>\\\\s*)?\\\\(',\n      returnBegin: true,\n      end: /\\s*[{;=]/,\n      excludeEnd: true,\n      keywords: KEYWORDS,\n      contains: [\n      // prevents these from being highlighted `title`\n      {\n        beginKeywords: FUNCTION_MODIFIERS.join(\" \"),\n        relevance: 0\n      }, {\n        begin: hljs.IDENT_RE + '\\\\s*(<[^=]+>\\\\s*)?\\\\(',\n        returnBegin: true,\n        contains: [hljs.TITLE_MODE, GENERIC_MODIFIER],\n        relevance: 0\n      }, {\n        match: /\\(\\)/\n      }, {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        excludeBegin: true,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        relevance: 0,\n        contains: [STRING, NUMBERS, hljs.C_BLOCK_COMMENT_MODE]\n      }, hljs.C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n    }, AT_IDENTIFIER]\n  };\n}\nexport { csharp as default };", "map": {"version": 3, "names": ["csharp", "hljs", "BUILT_IN_KEYWORDS", "FUNCTION_MODIFIERS", "LITERAL_KEYWORDS", "NORMAL_KEYWORDS", "CONTEXTUAL_KEYWORDS", "KEYWORDS", "keyword", "concat", "built_in", "literal", "TITLE_MODE", "inherit", "begin", "NUMBERS", "className", "variants", "relevance", "RAW_STRING", "VERBATIM_STRING", "end", "contains", "VERBATIM_STRING_NO_LF", "illegal", "SUBST", "keywords", "SUBST_NO_LF", "INTERPOLATED_STRING", "BACKSLASH_ESCAPE", "INTERPOLATED_VERBATIM_STRING", "INTERPOLATED_VERBATIM_STRING_NO_LF", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "C_BLOCK_COMMENT_MODE", "STRING", "GENERIC_MODIFIER", "beginKeywords", "TYPE_IDENT_RE", "IDENT_RE", "AT_IDENTIFIER", "name", "aliases", "COMMENT", "returnBegin", "C_LINE_COMMENT_MODE", "excludeBegin", "excludeEnd", "join", "match", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/highlight.js@11.11.1/node_modules/highlight.js/es/languages/csharp.js"], "sourcesContent": ["/*\nLanguage: C#\nAuthor: <PERSON> <<EMAIL>>\nContributor: <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://docs.microsoft.com/dotnet/csharp/\nCategory: common\n*/\n\n/** @type LanguageFn */\nfunction csharp(hljs) {\n  const BUILT_IN_KEYWORDS = [\n    'bool',\n    'byte',\n    'char',\n    'decimal',\n    'delegate',\n    'double',\n    'dynamic',\n    'enum',\n    'float',\n    'int',\n    'long',\n    'nint',\n    'nuint',\n    'object',\n    'sbyte',\n    'short',\n    'string',\n    'ulong',\n    'uint',\n    'ushort'\n  ];\n  const FUNCTION_MODIFIERS = [\n    'public',\n    'private',\n    'protected',\n    'static',\n    'internal',\n    'protected',\n    'abstract',\n    'async',\n    'extern',\n    'override',\n    'unsafe',\n    'virtual',\n    'new',\n    'sealed',\n    'partial'\n  ];\n  const LITERAL_KEYWORDS = [\n    'default',\n    'false',\n    'null',\n    'true'\n  ];\n  const NORMAL_KEYWORDS = [\n    'abstract',\n    'as',\n    'base',\n    'break',\n    'case',\n    'catch',\n    'class',\n    'const',\n    'continue',\n    'do',\n    'else',\n    'event',\n    'explicit',\n    'extern',\n    'finally',\n    'fixed',\n    'for',\n    'foreach',\n    'goto',\n    'if',\n    'implicit',\n    'in',\n    'interface',\n    'internal',\n    'is',\n    'lock',\n    'namespace',\n    'new',\n    'operator',\n    'out',\n    'override',\n    'params',\n    'private',\n    'protected',\n    'public',\n    'readonly',\n    'record',\n    'ref',\n    'return',\n    'scoped',\n    'sealed',\n    'sizeof',\n    'stackalloc',\n    'static',\n    'struct',\n    'switch',\n    'this',\n    'throw',\n    'try',\n    'typeof',\n    'unchecked',\n    'unsafe',\n    'using',\n    'virtual',\n    'void',\n    'volatile',\n    'while'\n  ];\n  const CONTEXTUAL_KEYWORDS = [\n    'add',\n    'alias',\n    'and',\n    'ascending',\n    'args',\n    'async',\n    'await',\n    'by',\n    'descending',\n    'dynamic',\n    'equals',\n    'file',\n    'from',\n    'get',\n    'global',\n    'group',\n    'init',\n    'into',\n    'join',\n    'let',\n    'nameof',\n    'not',\n    'notnull',\n    'on',\n    'or',\n    'orderby',\n    'partial',\n    'record',\n    'remove',\n    'required',\n    'scoped',\n    'select',\n    'set',\n    'unmanaged',\n    'value|0',\n    'var',\n    'when',\n    'where',\n    'with',\n    'yield'\n  ];\n\n  const KEYWORDS = {\n    keyword: NORMAL_KEYWORDS.concat(CONTEXTUAL_KEYWORDS),\n    built_in: BUILT_IN_KEYWORDS,\n    literal: LITERAL_KEYWORDS\n  };\n  const TITLE_MODE = hljs.inherit(hljs.TITLE_MODE, { begin: '[a-zA-Z](\\\\.?\\\\w)*' });\n  const NUMBERS = {\n    className: 'number',\n    variants: [\n      { begin: '\\\\b(0b[01\\']+)' },\n      { begin: '(-?)\\\\b([\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)(u|U|l|L|ul|UL|f|F|b|B)' },\n      { begin: '(-?)(\\\\b0[xX][a-fA-F0-9\\']+|(\\\\b[\\\\d\\']+(\\\\.[\\\\d\\']*)?|\\\\.[\\\\d\\']+)([eE][-+]?[\\\\d\\']+)?)' }\n    ],\n    relevance: 0\n  };\n  const RAW_STRING = {\n    className: 'string',\n    begin: /\"\"\"(\"*)(?!\")(.|\\n)*?\"\"\"\\1/,\n    relevance: 1\n  };\n  const VERBATIM_STRING = {\n    className: 'string',\n    begin: '@\"',\n    end: '\"',\n    contains: [ { begin: '\"\"' } ]\n  };\n  const VERBATIM_STRING_NO_LF = hljs.inherit(VERBATIM_STRING, { illegal: /\\n/ });\n  const SUBST = {\n    className: 'subst',\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS\n  };\n  const SUBST_NO_LF = hljs.inherit(SUBST, { illegal: /\\n/ });\n  const INTERPOLATED_STRING = {\n    className: 'string',\n    begin: /\\$\"/,\n    end: '\"',\n    illegal: /\\n/,\n    contains: [\n      { begin: /\\{\\{/ },\n      { begin: /\\}\\}/ },\n      hljs.BACKSLASH_ESCAPE,\n      SUBST_NO_LF\n    ]\n  };\n  const INTERPOLATED_VERBATIM_STRING = {\n    className: 'string',\n    begin: /\\$@\"/,\n    end: '\"',\n    contains: [\n      { begin: /\\{\\{/ },\n      { begin: /\\}\\}/ },\n      { begin: '\"\"' },\n      SUBST\n    ]\n  };\n  const INTERPOLATED_VERBATIM_STRING_NO_LF = hljs.inherit(INTERPOLATED_VERBATIM_STRING, {\n    illegal: /\\n/,\n    contains: [\n      { begin: /\\{\\{/ },\n      { begin: /\\}\\}/ },\n      { begin: '\"\"' },\n      SUBST_NO_LF\n    ]\n  });\n  SUBST.contains = [\n    INTERPOLATED_VERBATIM_STRING,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    NUMBERS,\n    hljs.C_BLOCK_COMMENT_MODE\n  ];\n  SUBST_NO_LF.contains = [\n    INTERPOLATED_VERBATIM_STRING_NO_LF,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING_NO_LF,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    NUMBERS,\n    hljs.inherit(hljs.C_BLOCK_COMMENT_MODE, { illegal: /\\n/ })\n  ];\n  const STRING = { variants: [\n    RAW_STRING,\n    INTERPOLATED_VERBATIM_STRING,\n    INTERPOLATED_STRING,\n    VERBATIM_STRING,\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE\n  ] };\n\n  const GENERIC_MODIFIER = {\n    begin: \"<\",\n    end: \">\",\n    contains: [\n      { beginKeywords: \"in out\" },\n      TITLE_MODE\n    ]\n  };\n  const TYPE_IDENT_RE = hljs.IDENT_RE + '(<' + hljs.IDENT_RE + '(\\\\s*,\\\\s*' + hljs.IDENT_RE + ')*>)?(\\\\[\\\\])?';\n  const AT_IDENTIFIER = {\n    // prevents expressions like `@class` from incorrect flagging\n    // `class` as a keyword\n    begin: \"@\" + hljs.IDENT_RE,\n    relevance: 0\n  };\n\n  return {\n    name: 'C#',\n    aliases: [\n      'cs',\n      'c#'\n    ],\n    keywords: KEYWORDS,\n    illegal: /::/,\n    contains: [\n      hljs.COMMENT(\n        '///',\n        '$',\n        {\n          returnBegin: true,\n          contains: [\n            {\n              className: 'doctag',\n              variants: [\n                {\n                  begin: '///',\n                  relevance: 0\n                },\n                { begin: '<!--|-->' },\n                {\n                  begin: '</?',\n                  end: '>'\n                }\n              ]\n            }\n          ]\n        }\n      ),\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      {\n        className: 'meta',\n        begin: '#',\n        end: '$',\n        keywords: { keyword: 'if else elif endif define undef warning error line region endregion pragma checksum' }\n      },\n      STRING,\n      NUMBERS,\n      {\n        beginKeywords: 'class interface',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:,]/,\n        contains: [\n          { beginKeywords: \"where class\" },\n          TITLE_MODE,\n          GENERIC_MODIFIER,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        beginKeywords: 'namespace',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:]/,\n        contains: [\n          TITLE_MODE,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        beginKeywords: 'record',\n        relevance: 0,\n        end: /[{;=]/,\n        illegal: /[^\\s:]/,\n        contains: [\n          TITLE_MODE,\n          GENERIC_MODIFIER,\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      {\n        // [Attributes(\"\")]\n        className: 'meta',\n        begin: '^\\\\s*\\\\[(?=[\\\\w])',\n        excludeBegin: true,\n        end: '\\\\]',\n        excludeEnd: true,\n        contains: [\n          {\n            className: 'string',\n            begin: /\"/,\n            end: /\"/\n          }\n        ]\n      },\n      {\n        // Expression keywords prevent 'keyword Name(...)' from being\n        // recognized as a function definition\n        beginKeywords: 'new return throw await else',\n        relevance: 0\n      },\n      {\n        className: 'function',\n        begin: '(' + TYPE_IDENT_RE + '\\\\s+)+' + hljs.IDENT_RE + '\\\\s*(<[^=]+>\\\\s*)?\\\\(',\n        returnBegin: true,\n        end: /\\s*[{;=]/,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: [\n          // prevents these from being highlighted `title`\n          {\n            beginKeywords: FUNCTION_MODIFIERS.join(\" \"),\n            relevance: 0\n          },\n          {\n            begin: hljs.IDENT_RE + '\\\\s*(<[^=]+>\\\\s*)?\\\\(',\n            returnBegin: true,\n            contains: [\n              hljs.TITLE_MODE,\n              GENERIC_MODIFIER\n            ],\n            relevance: 0\n          },\n          { match: /\\(\\)/ },\n          {\n            className: 'params',\n            begin: /\\(/,\n            end: /\\)/,\n            excludeBegin: true,\n            excludeEnd: true,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              STRING,\n              NUMBERS,\n              hljs.C_BLOCK_COMMENT_MODE\n            ]\n          },\n          hljs.C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE\n        ]\n      },\n      AT_IDENTIFIER\n    ]\n  };\n}\n\nexport { csharp as default };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,IAAMC,iBAAiB,GAAG,CACxB,MAAM,EACN,MAAM,EACN,MAAM,EACN,SAAS,EACT,UAAU,EACV,QAAQ,EACR,SAAS,EACT,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,MAAM,EACN,OAAO,EACP,QAAQ,EACR,OAAO,EACP,OAAO,EACP,QAAQ,EACR,OAAO,EACP,MAAM,EACN,QAAQ,CACT;EACD,IAAMC,kBAAkB,GAAG,CACzB,QAAQ,EACR,SAAS,EACT,WAAW,EACX,QAAQ,EACR,UAAU,EACV,WAAW,EACX,UAAU,EACV,OAAO,EACP,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,SAAS,EACT,KAAK,EACL,QAAQ,EACR,SAAS,CACV;EACD,IAAMC,gBAAgB,GAAG,CACvB,SAAS,EACT,OAAO,EACP,MAAM,EACN,MAAM,CACP;EACD,IAAMC,eAAe,GAAG,CACtB,UAAU,EACV,IAAI,EACJ,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,EACP,OAAO,EACP,OAAO,EACP,UAAU,EACV,IAAI,EACJ,MAAM,EACN,OAAO,EACP,UAAU,EACV,QAAQ,EACR,SAAS,EACT,OAAO,EACP,KAAK,EACL,SAAS,EACT,MAAM,EACN,IAAI,EACJ,UAAU,EACV,IAAI,EACJ,WAAW,EACX,UAAU,EACV,IAAI,EACJ,MAAM,EACN,WAAW,EACX,KAAK,EACL,UAAU,EACV,KAAK,EACL,UAAU,EACV,QAAQ,EACR,SAAS,EACT,WAAW,EACX,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,YAAY,EACZ,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,OAAO,EACP,SAAS,EACT,MAAM,EACN,UAAU,EACV,OAAO,CACR;EACD,IAAMC,mBAAmB,GAAG,CAC1B,KAAK,EACL,OAAO,EACP,KAAK,EACL,WAAW,EACX,MAAM,EACN,OAAO,EACP,OAAO,EACP,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,QAAQ,EACR,MAAM,EACN,MAAM,EACN,KAAK,EACL,QAAQ,EACR,OAAO,EACP,MAAM,EACN,MAAM,EACN,MAAM,EACN,KAAK,EACL,QAAQ,EACR,KAAK,EACL,SAAS,EACT,IAAI,EACJ,IAAI,EACJ,SAAS,EACT,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,KAAK,EACL,WAAW,EACX,SAAS,EACT,KAAK,EACL,MAAM,EACN,OAAO,EACP,MAAM,EACN,OAAO,CACR;EAED,IAAMC,QAAQ,GAAG;IACfC,OAAO,EAAEH,eAAe,CAACI,MAAM,CAACH,mBAAmB,CAAC;IACpDI,QAAQ,EAAER,iBAAiB;IAC3BS,OAAO,EAAEP;EACX,CAAC;EACD,IAAMQ,UAAU,GAAGX,IAAI,CAACY,OAAO,CAACZ,IAAI,CAACW,UAAU,EAAE;IAAEE,KAAK,EAAE;EAAqB,CAAC,CAAC;EACjF,IAAMC,OAAO,GAAG;IACdC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,CACR;MAAEH,KAAK,EAAE;IAAiB,CAAC,EAC3B;MAAEA,KAAK,EAAE;IAAqE,CAAC,EAC/E;MAAEA,KAAK,EAAE;IAA2F,CAAC,CACtG;IACDI,SAAS,EAAE;EACb,CAAC;EACD,IAAMC,UAAU,GAAG;IACjBH,SAAS,EAAE,QAAQ;IACnBF,KAAK,EAAE,2BAA2B;IAClCI,SAAS,EAAE;EACb,CAAC;EACD,IAAME,eAAe,GAAG;IACtBJ,SAAS,EAAE,QAAQ;IACnBF,KAAK,EAAE,IAAI;IACXO,GAAG,EAAE,GAAG;IACRC,QAAQ,EAAE,CAAE;MAAER,KAAK,EAAE;IAAK,CAAC;EAC7B,CAAC;EACD,IAAMS,qBAAqB,GAAGtB,IAAI,CAACY,OAAO,CAACO,eAAe,EAAE;IAAEI,OAAO,EAAE;EAAK,CAAC,CAAC;EAC9E,IAAMC,KAAK,GAAG;IACZT,SAAS,EAAE,OAAO;IAClBF,KAAK,EAAE,IAAI;IACXO,GAAG,EAAE,IAAI;IACTK,QAAQ,EAAEnB;EACZ,CAAC;EACD,IAAMoB,WAAW,GAAG1B,IAAI,CAACY,OAAO,CAACY,KAAK,EAAE;IAAED,OAAO,EAAE;EAAK,CAAC,CAAC;EAC1D,IAAMI,mBAAmB,GAAG;IAC1BZ,SAAS,EAAE,QAAQ;IACnBF,KAAK,EAAE,KAAK;IACZO,GAAG,EAAE,GAAG;IACRG,OAAO,EAAE,IAAI;IACbF,QAAQ,EAAE,CACR;MAAER,KAAK,EAAE;IAAO,CAAC,EACjB;MAAEA,KAAK,EAAE;IAAO,CAAC,EACjBb,IAAI,CAAC4B,gBAAgB,EACrBF,WAAW;EAEf,CAAC;EACD,IAAMG,4BAA4B,GAAG;IACnCd,SAAS,EAAE,QAAQ;IACnBF,KAAK,EAAE,MAAM;IACbO,GAAG,EAAE,GAAG;IACRC,QAAQ,EAAE,CACR;MAAER,KAAK,EAAE;IAAO,CAAC,EACjB;MAAEA,KAAK,EAAE;IAAO,CAAC,EACjB;MAAEA,KAAK,EAAE;IAAK,CAAC,EACfW,KAAK;EAET,CAAC;EACD,IAAMM,kCAAkC,GAAG9B,IAAI,CAACY,OAAO,CAACiB,4BAA4B,EAAE;IACpFN,OAAO,EAAE,IAAI;IACbF,QAAQ,EAAE,CACR;MAAER,KAAK,EAAE;IAAO,CAAC,EACjB;MAAEA,KAAK,EAAE;IAAO,CAAC,EACjB;MAAEA,KAAK,EAAE;IAAK,CAAC,EACfa,WAAW;EAEf,CAAC,CAAC;EACFF,KAAK,CAACH,QAAQ,GAAG,CACfQ,4BAA4B,EAC5BF,mBAAmB,EACnBR,eAAe,EACfnB,IAAI,CAAC+B,gBAAgB,EACrB/B,IAAI,CAACgC,iBAAiB,EACtBlB,OAAO,EACPd,IAAI,CAACiC,oBAAoB,CAC1B;EACDP,WAAW,CAACL,QAAQ,GAAG,CACrBS,kCAAkC,EAClCH,mBAAmB,EACnBL,qBAAqB,EACrBtB,IAAI,CAAC+B,gBAAgB,EACrB/B,IAAI,CAACgC,iBAAiB,EACtBlB,OAAO,EACPd,IAAI,CAACY,OAAO,CAACZ,IAAI,CAACiC,oBAAoB,EAAE;IAAEV,OAAO,EAAE;EAAK,CAAC,CAAC,CAC3D;EACD,IAAMW,MAAM,GAAG;IAAElB,QAAQ,EAAE,CACzBE,UAAU,EACVW,4BAA4B,EAC5BF,mBAAmB,EACnBR,eAAe,EACfnB,IAAI,CAAC+B,gBAAgB,EACrB/B,IAAI,CAACgC,iBAAiB;EACtB,CAAC;EAEH,IAAMG,gBAAgB,GAAG;IACvBtB,KAAK,EAAE,GAAG;IACVO,GAAG,EAAE,GAAG;IACRC,QAAQ,EAAE,CACR;MAAEe,aAAa,EAAE;IAAS,CAAC,EAC3BzB,UAAU;EAEd,CAAC;EACD,IAAM0B,aAAa,GAAGrC,IAAI,CAACsC,QAAQ,GAAG,IAAI,GAAGtC,IAAI,CAACsC,QAAQ,GAAG,YAAY,GAAGtC,IAAI,CAACsC,QAAQ,GAAG,gBAAgB;EAC5G,IAAMC,aAAa,GAAG;IACpB;IACA;IACA1B,KAAK,EAAE,GAAG,GAAGb,IAAI,CAACsC,QAAQ;IAC1BrB,SAAS,EAAE;EACb,CAAC;EAED,OAAO;IACLuB,IAAI,EAAE,IAAI;IACVC,OAAO,EAAE,CACP,IAAI,EACJ,IAAI,CACL;IACDhB,QAAQ,EAAEnB,QAAQ;IAClBiB,OAAO,EAAE,IAAI;IACbF,QAAQ,EAAE,CACRrB,IAAI,CAAC0C,OAAO,CACV,KAAK,EACL,GAAG,EACH;MACEC,WAAW,EAAE,IAAI;MACjBtB,QAAQ,EAAE,CACR;QACEN,SAAS,EAAE,QAAQ;QACnBC,QAAQ,EAAE,CACR;UACEH,KAAK,EAAE,KAAK;UACZI,SAAS,EAAE;QACb,CAAC,EACD;UAAEJ,KAAK,EAAE;QAAW,CAAC,EACrB;UACEA,KAAK,EAAE,KAAK;UACZO,GAAG,EAAE;QACP,CAAC;MAEL,CAAC;IAEL,CACF,CAAC,EACDpB,IAAI,CAAC4C,mBAAmB,EACxB5C,IAAI,CAACiC,oBAAoB,EACzB;MACElB,SAAS,EAAE,MAAM;MACjBF,KAAK,EAAE,GAAG;MACVO,GAAG,EAAE,GAAG;MACRK,QAAQ,EAAE;QAAElB,OAAO,EAAE;MAAsF;IAC7G,CAAC,EACD2B,MAAM,EACNpB,OAAO,EACP;MACEsB,aAAa,EAAE,iBAAiB;MAChCnB,SAAS,EAAE,CAAC;MACZG,GAAG,EAAE,OAAO;MACZG,OAAO,EAAE,SAAS;MAClBF,QAAQ,EAAE,CACR;QAAEe,aAAa,EAAE;MAAc,CAAC,EAChCzB,UAAU,EACVwB,gBAAgB,EAChBnC,IAAI,CAAC4C,mBAAmB,EACxB5C,IAAI,CAACiC,oBAAoB;IAE7B,CAAC,EACD;MACEG,aAAa,EAAE,WAAW;MAC1BnB,SAAS,EAAE,CAAC;MACZG,GAAG,EAAE,OAAO;MACZG,OAAO,EAAE,QAAQ;MACjBF,QAAQ,EAAE,CACRV,UAAU,EACVX,IAAI,CAAC4C,mBAAmB,EACxB5C,IAAI,CAACiC,oBAAoB;IAE7B,CAAC,EACD;MACEG,aAAa,EAAE,QAAQ;MACvBnB,SAAS,EAAE,CAAC;MACZG,GAAG,EAAE,OAAO;MACZG,OAAO,EAAE,QAAQ;MACjBF,QAAQ,EAAE,CACRV,UAAU,EACVwB,gBAAgB,EAChBnC,IAAI,CAAC4C,mBAAmB,EACxB5C,IAAI,CAACiC,oBAAoB;IAE7B,CAAC,EACD;MACE;MACAlB,SAAS,EAAE,MAAM;MACjBF,KAAK,EAAE,mBAAmB;MAC1BgC,YAAY,EAAE,IAAI;MAClBzB,GAAG,EAAE,KAAK;MACV0B,UAAU,EAAE,IAAI;MAChBzB,QAAQ,EAAE,CACR;QACEN,SAAS,EAAE,QAAQ;QACnBF,KAAK,EAAE,GAAG;QACVO,GAAG,EAAE;MACP,CAAC;IAEL,CAAC,EACD;MACE;MACA;MACAgB,aAAa,EAAE,6BAA6B;MAC5CnB,SAAS,EAAE;IACb,CAAC,EACD;MACEF,SAAS,EAAE,UAAU;MACrBF,KAAK,EAAE,GAAG,GAAGwB,aAAa,GAAG,QAAQ,GAAGrC,IAAI,CAACsC,QAAQ,GAAG,uBAAuB;MAC/EK,WAAW,EAAE,IAAI;MACjBvB,GAAG,EAAE,UAAU;MACf0B,UAAU,EAAE,IAAI;MAChBrB,QAAQ,EAAEnB,QAAQ;MAClBe,QAAQ,EAAE;MACR;MACA;QACEe,aAAa,EAAElC,kBAAkB,CAAC6C,IAAI,CAAC,GAAG,CAAC;QAC3C9B,SAAS,EAAE;MACb,CAAC,EACD;QACEJ,KAAK,EAAEb,IAAI,CAACsC,QAAQ,GAAG,uBAAuB;QAC9CK,WAAW,EAAE,IAAI;QACjBtB,QAAQ,EAAE,CACRrB,IAAI,CAACW,UAAU,EACfwB,gBAAgB,CACjB;QACDlB,SAAS,EAAE;MACb,CAAC,EACD;QAAE+B,KAAK,EAAE;MAAO,CAAC,EACjB;QACEjC,SAAS,EAAE,QAAQ;QACnBF,KAAK,EAAE,IAAI;QACXO,GAAG,EAAE,IAAI;QACTyB,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE,IAAI;QAChBrB,QAAQ,EAAEnB,QAAQ;QAClBW,SAAS,EAAE,CAAC;QACZI,QAAQ,EAAE,CACRa,MAAM,EACNpB,OAAO,EACPd,IAAI,CAACiC,oBAAoB;MAE7B,CAAC,EACDjC,IAAI,CAAC4C,mBAAmB,EACxB5C,IAAI,CAACiC,oBAAoB;IAE7B,CAAC,EACDM,aAAa;EAEjB,CAAC;AACH;AAEA,SAASxC,MAAM,IAAIkD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}