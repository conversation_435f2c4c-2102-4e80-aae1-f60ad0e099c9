{"ast": null, "code": "'use strict';\n\nmodule.exports = function (opts) {\n  var re = {};\n\n  // Use direct extract instead of `regenerate` to reduse browserified size\n  re.src_Any = require('uc.micro/properties/Any/regex').source;\n  re.src_Cc = require('uc.micro/categories/Cc/regex').source;\n  re.src_Z = require('uc.micro/categories/Z/regex').source;\n  re.src_P = require('uc.micro/categories/P/regex').source;\n\n  // \\p{\\Z\\P\\Cc\\CF} (white spaces + control + format + punctuation)\n  re.src_ZPCc = [re.src_Z, re.src_P, re.src_Cc].join('|');\n\n  // \\p{\\Z\\Cc} (white spaces + control)\n  re.src_ZCc = [re.src_Z, re.src_Cc].join('|');\n\n  // Experimental. List of chars, completely prohibited in links\n  // because can separate it from other part of text\n  var text_separators = \"[><\\uFF5C]\";\n\n  // All possible word characters (everything without punctuation, spaces & controls)\n  // Defined via punctuation & spaces to save space\n  // Should be something like \\p{\\L\\N\\S\\M} (\\w but without `_`)\n  re.src_pseudo_letter = '(?:(?!' + text_separators + '|' + re.src_ZPCc + ')' + re.src_Any + ')';\n  // The same as abothe but without [0-9]\n  // var src_pseudo_letter_non_d = '(?:(?![0-9]|' + src_ZPCc + ')' + src_Any + ')';\n\n  ////////////////////////////////////////////////////////////////////////////////\n\n  re.src_ip4 = '(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)';\n\n  // Prohibit any of \"@/[]()\" in user/pass to avoid wrong domain fetch.\n  re.src_auth = '(?:(?:(?!' + re.src_ZCc + '|[@/\\\\[\\\\]()]).)+@)?';\n  re.src_port = '(?::(?:6(?:[0-4]\\\\d{3}|5(?:[0-4]\\\\d{2}|5(?:[0-2]\\\\d|3[0-5])))|[1-5]?\\\\d{1,4}))?';\n  re.src_host_terminator = '(?=$|' + text_separators + '|' + re.src_ZPCc + ')(?!-|_|:\\\\d|\\\\.-|\\\\.(?!$|' + re.src_ZPCc + '))';\n  re.src_path = '(?:' + '[/?#]' + '(?:' + '(?!' + re.src_ZCc + '|' + text_separators + '|[()[\\\\]{}.,\"\\'?!\\\\-;]).|' + '\\\\[(?:(?!' + re.src_ZCc + '|\\\\]).)*\\\\]|' + '\\\\((?:(?!' + re.src_ZCc + '|[)]).)*\\\\)|' + '\\\\{(?:(?!' + re.src_ZCc + '|[}]).)*\\\\}|' + '\\\\\"(?:(?!' + re.src_ZCc + '|[\"]).)+\\\\\"|' + \"\\\\'(?:(?!\" + re.src_ZCc + \"|[']).)+\\\\'|\" + \"\\\\'(?=\" + re.src_pseudo_letter + '|[-]).|' +\n  // allow `I'm_king` if no pair found\n  '\\\\.{2,}[a-zA-Z0-9%/&]|' +\n  // google has many dots in \"google search\" links (#66, #81).\n  // github has ... in commit range links,\n  // Restrict to\n  // - english\n  // - percent-encoded\n  // - parts of file path\n  // - params separator\n  // until more examples found.\n  '\\\\.(?!' + re.src_ZCc + '|[.]).|' + (opts && opts['---'] ? '\\\\-(?!--(?:[^-]|$))(?:-*)|' // `---` => long dash, terminate\n  : '\\\\-+|') + ',(?!' + re.src_ZCc + ').|' +\n  // allow `,,,` in paths\n  ';(?!' + re.src_ZCc + ').|' +\n  // allow `;` if not followed by space-like char\n  '\\\\!+(?!' + re.src_ZCc + '|[!]).|' +\n  // allow `!!!` in paths, but not at the end\n  '\\\\?(?!' + re.src_ZCc + '|[?]).' + ')+' + '|\\\\/' + ')?';\n\n  // Allow anything in markdown spec, forbid quote (\") at the first position\n  // because emails enclosed in quotes are far more common\n  re.src_email_name = '[\\\\-;:&=\\\\+\\\\$,\\\\.a-zA-Z0-9_][\\\\-;:&=\\\\+\\\\$,\\\\\"\\\\.a-zA-Z0-9_]*';\n  re.src_xn = 'xn--[a-z0-9\\\\-]{1,59}';\n\n  // More to read about domain names\n  // http://serverfault.com/questions/638260/\n\n  re.src_domain_root =\n  // Allow letters & digits (http://test1)\n  '(?:' + re.src_xn + '|' + re.src_pseudo_letter + '{1,63}' + ')';\n  re.src_domain = '(?:' + re.src_xn + '|' + '(?:' + re.src_pseudo_letter + ')' + '|' + '(?:' + re.src_pseudo_letter + '(?:-|' + re.src_pseudo_letter + '){0,61}' + re.src_pseudo_letter + ')' + ')';\n  re.src_host = '(?:' +\n  // Don't need IP check, because digits are already allowed in normal domain names\n  //   src_ip4 +\n  // '|' +\n  '(?:(?:(?:' + re.src_domain + ')\\\\.)*' + re.src_domain /*_root*/ + ')' + ')';\n  re.tpl_host_fuzzy = '(?:' + re.src_ip4 + '|' + '(?:(?:(?:' + re.src_domain + ')\\\\.)+(?:%TLDS%))' + ')';\n  re.tpl_host_no_ip_fuzzy = '(?:(?:(?:' + re.src_domain + ')\\\\.)+(?:%TLDS%))';\n  re.src_host_strict = re.src_host + re.src_host_terminator;\n  re.tpl_host_fuzzy_strict = re.tpl_host_fuzzy + re.src_host_terminator;\n  re.src_host_port_strict = re.src_host + re.src_port + re.src_host_terminator;\n  re.tpl_host_port_fuzzy_strict = re.tpl_host_fuzzy + re.src_port + re.src_host_terminator;\n  re.tpl_host_port_no_ip_fuzzy_strict = re.tpl_host_no_ip_fuzzy + re.src_port + re.src_host_terminator;\n\n  ////////////////////////////////////////////////////////////////////////////////\n  // Main rules\n\n  // Rude test fuzzy links by host, for quick deny\n  re.tpl_host_fuzzy_test = 'localhost|www\\\\.|\\\\.\\\\d{1,3}\\\\.|(?:\\\\.(?:%TLDS%)(?:' + re.src_ZPCc + '|>|$))';\n  re.tpl_email_fuzzy = '(^|' + text_separators + '|\"|\\\\(|' + re.src_ZCc + ')' + '(' + re.src_email_name + '@' + re.tpl_host_fuzzy_strict + ')';\n  re.tpl_link_fuzzy =\n  // Fuzzy link can't be prepended with .:/\\- and non punctuation.\n  // but can start with > (markdown blockquote)\n  \"(^|(?![.:/\\\\-_@])(?:[$+<=>^`|\\uFF5C]|\" + re.src_ZPCc + '))' + \"((?![$+<=>^`|\\uFF5C])\" + re.tpl_host_port_fuzzy_strict + re.src_path + ')';\n  re.tpl_link_no_ip_fuzzy =\n  // Fuzzy link can't be prepended with .:/\\- and non punctuation.\n  // but can start with > (markdown blockquote)\n  \"(^|(?![.:/\\\\-_@])(?:[$+<=>^`|\\uFF5C]|\" + re.src_ZPCc + '))' + \"((?![$+<=>^`|\\uFF5C])\" + re.tpl_host_port_no_ip_fuzzy_strict + re.src_path + ')';\n  return re;\n};", "map": {"version": 3, "names": ["module", "exports", "opts", "re", "src_Any", "require", "source", "src_Cc", "src_Z", "src_P", "src_ZPCc", "join", "src_ZCc", "text_separators", "src_pseudo_letter", "src_ip4", "src_auth", "src_port", "src_host_terminator", "src_path", "src_email_name", "src_xn", "src_domain_root", "src_domain", "src_host", "tpl_host_fuzzy", "tpl_host_no_ip_fuzzy", "src_host_strict", "tpl_host_fuzzy_strict", "src_host_port_strict", "tpl_host_port_fuzzy_strict", "tpl_host_port_no_ip_fuzzy_strict", "tpl_host_fuzzy_test", "tpl_email_fuzzy", "tpl_link_fuzzy", "tpl_link_no_ip_fuzzy"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/linkify-it@3.0.3/node_modules/linkify-it/lib/re.js"], "sourcesContent": ["'use strict';\n\n\nmodule.exports = function (opts) {\n  var re = {};\n\n  // Use direct extract instead of `regenerate` to reduse browserified size\n  re.src_Any = require('uc.micro/properties/Any/regex').source;\n  re.src_Cc  = require('uc.micro/categories/Cc/regex').source;\n  re.src_Z   = require('uc.micro/categories/Z/regex').source;\n  re.src_P   = require('uc.micro/categories/P/regex').source;\n\n  // \\p{\\Z\\P\\Cc\\CF} (white spaces + control + format + punctuation)\n  re.src_ZPCc = [ re.src_Z, re.src_P, re.src_Cc ].join('|');\n\n  // \\p{\\Z\\Cc} (white spaces + control)\n  re.src_ZCc = [ re.src_Z, re.src_Cc ].join('|');\n\n  // Experimental. List of chars, completely prohibited in links\n  // because can separate it from other part of text\n  var text_separators = '[><\\uff5c]';\n\n  // All possible word characters (everything without punctuation, spaces & controls)\n  // Defined via punctuation & spaces to save space\n  // Should be something like \\p{\\L\\N\\S\\M} (\\w but without `_`)\n  re.src_pseudo_letter       = '(?:(?!' + text_separators + '|' + re.src_ZPCc + ')' + re.src_Any + ')';\n  // The same as abothe but without [0-9]\n  // var src_pseudo_letter_non_d = '(?:(?![0-9]|' + src_ZPCc + ')' + src_Any + ')';\n\n  ////////////////////////////////////////////////////////////////////////////////\n\n  re.src_ip4 =\n\n    '(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)';\n\n  // Prohibit any of \"@/[]()\" in user/pass to avoid wrong domain fetch.\n  re.src_auth    = '(?:(?:(?!' + re.src_ZCc + '|[@/\\\\[\\\\]()]).)+@)?';\n\n  re.src_port =\n\n    '(?::(?:6(?:[0-4]\\\\d{3}|5(?:[0-4]\\\\d{2}|5(?:[0-2]\\\\d|3[0-5])))|[1-5]?\\\\d{1,4}))?';\n\n  re.src_host_terminator =\n\n    '(?=$|' + text_separators + '|' + re.src_ZPCc + ')(?!-|_|:\\\\d|\\\\.-|\\\\.(?!$|' + re.src_ZPCc + '))';\n\n  re.src_path =\n\n    '(?:' +\n      '[/?#]' +\n        '(?:' +\n          '(?!' + re.src_ZCc + '|' + text_separators + '|[()[\\\\]{}.,\"\\'?!\\\\-;]).|' +\n          '\\\\[(?:(?!' + re.src_ZCc + '|\\\\]).)*\\\\]|' +\n          '\\\\((?:(?!' + re.src_ZCc + '|[)]).)*\\\\)|' +\n          '\\\\{(?:(?!' + re.src_ZCc + '|[}]).)*\\\\}|' +\n          '\\\\\"(?:(?!' + re.src_ZCc + '|[\"]).)+\\\\\"|' +\n          \"\\\\'(?:(?!\" + re.src_ZCc + \"|[']).)+\\\\'|\" +\n          \"\\\\'(?=\" + re.src_pseudo_letter + '|[-]).|' +  // allow `I'm_king` if no pair found\n          '\\\\.{2,}[a-zA-Z0-9%/&]|' + // google has many dots in \"google search\" links (#66, #81).\n                                     // github has ... in commit range links,\n                                     // Restrict to\n                                     // - english\n                                     // - percent-encoded\n                                     // - parts of file path\n                                     // - params separator\n                                     // until more examples found.\n          '\\\\.(?!' + re.src_ZCc + '|[.]).|' +\n          (opts && opts['---'] ?\n            '\\\\-(?!--(?:[^-]|$))(?:-*)|' // `---` => long dash, terminate\n            :\n            '\\\\-+|'\n          ) +\n          ',(?!' + re.src_ZCc + ').|' +       // allow `,,,` in paths\n          ';(?!' + re.src_ZCc + ').|' +       // allow `;` if not followed by space-like char\n          '\\\\!+(?!' + re.src_ZCc + '|[!]).|' +  // allow `!!!` in paths, but not at the end\n          '\\\\?(?!' + re.src_ZCc + '|[?]).' +\n        ')+' +\n      '|\\\\/' +\n    ')?';\n\n  // Allow anything in markdown spec, forbid quote (\") at the first position\n  // because emails enclosed in quotes are far more common\n  re.src_email_name =\n\n    '[\\\\-;:&=\\\\+\\\\$,\\\\.a-zA-Z0-9_][\\\\-;:&=\\\\+\\\\$,\\\\\"\\\\.a-zA-Z0-9_]*';\n\n  re.src_xn =\n\n    'xn--[a-z0-9\\\\-]{1,59}';\n\n  // More to read about domain names\n  // http://serverfault.com/questions/638260/\n\n  re.src_domain_root =\n\n    // Allow letters & digits (http://test1)\n    '(?:' +\n      re.src_xn +\n      '|' +\n      re.src_pseudo_letter + '{1,63}' +\n    ')';\n\n  re.src_domain =\n\n    '(?:' +\n      re.src_xn +\n      '|' +\n      '(?:' + re.src_pseudo_letter + ')' +\n      '|' +\n      '(?:' + re.src_pseudo_letter + '(?:-|' + re.src_pseudo_letter + '){0,61}' + re.src_pseudo_letter + ')' +\n    ')';\n\n  re.src_host =\n\n    '(?:' +\n    // Don't need IP check, because digits are already allowed in normal domain names\n    //   src_ip4 +\n    // '|' +\n      '(?:(?:(?:' + re.src_domain + ')\\\\.)*' + re.src_domain/*_root*/ + ')' +\n    ')';\n\n  re.tpl_host_fuzzy =\n\n    '(?:' +\n      re.src_ip4 +\n    '|' +\n      '(?:(?:(?:' + re.src_domain + ')\\\\.)+(?:%TLDS%))' +\n    ')';\n\n  re.tpl_host_no_ip_fuzzy =\n\n    '(?:(?:(?:' + re.src_domain + ')\\\\.)+(?:%TLDS%))';\n\n  re.src_host_strict =\n\n    re.src_host + re.src_host_terminator;\n\n  re.tpl_host_fuzzy_strict =\n\n    re.tpl_host_fuzzy + re.src_host_terminator;\n\n  re.src_host_port_strict =\n\n    re.src_host + re.src_port + re.src_host_terminator;\n\n  re.tpl_host_port_fuzzy_strict =\n\n    re.tpl_host_fuzzy + re.src_port + re.src_host_terminator;\n\n  re.tpl_host_port_no_ip_fuzzy_strict =\n\n    re.tpl_host_no_ip_fuzzy + re.src_port + re.src_host_terminator;\n\n\n  ////////////////////////////////////////////////////////////////////////////////\n  // Main rules\n\n  // Rude test fuzzy links by host, for quick deny\n  re.tpl_host_fuzzy_test =\n\n    'localhost|www\\\\.|\\\\.\\\\d{1,3}\\\\.|(?:\\\\.(?:%TLDS%)(?:' + re.src_ZPCc + '|>|$))';\n\n  re.tpl_email_fuzzy =\n\n      '(^|' + text_separators + '|\"|\\\\(|' + re.src_ZCc + ')' +\n      '(' + re.src_email_name + '@' + re.tpl_host_fuzzy_strict + ')';\n\n  re.tpl_link_fuzzy =\n      // Fuzzy link can't be prepended with .:/\\- and non punctuation.\n      // but can start with > (markdown blockquote)\n      '(^|(?![.:/\\\\-_@])(?:[$+<=>^`|\\uff5c]|' + re.src_ZPCc + '))' +\n      '((?![$+<=>^`|\\uff5c])' + re.tpl_host_port_fuzzy_strict + re.src_path + ')';\n\n  re.tpl_link_no_ip_fuzzy =\n      // Fuzzy link can't be prepended with .:/\\- and non punctuation.\n      // but can start with > (markdown blockquote)\n      '(^|(?![.:/\\\\-_@])(?:[$+<=>^`|\\uff5c]|' + re.src_ZPCc + '))' +\n      '((?![$+<=>^`|\\uff5c])' + re.tpl_host_port_no_ip_fuzzy_strict + re.src_path + ')';\n\n  return re;\n};\n"], "mappings": "AAAA,YAAY;;AAGZA,MAAM,CAACC,OAAO,GAAG,UAAUC,IAAI,EAAE;EAC/B,IAAIC,EAAE,GAAG,CAAC,CAAC;;EAEX;EACAA,EAAE,CAACC,OAAO,GAAGC,OAAO,CAAC,+BAA+B,CAAC,CAACC,MAAM;EAC5DH,EAAE,CAACI,MAAM,GAAIF,OAAO,CAAC,8BAA8B,CAAC,CAACC,MAAM;EAC3DH,EAAE,CAACK,KAAK,GAAKH,OAAO,CAAC,6BAA6B,CAAC,CAACC,MAAM;EAC1DH,EAAE,CAACM,KAAK,GAAKJ,OAAO,CAAC,6BAA6B,CAAC,CAACC,MAAM;;EAE1D;EACAH,EAAE,CAACO,QAAQ,GAAG,CAAEP,EAAE,CAACK,KAAK,EAAEL,EAAE,CAACM,KAAK,EAAEN,EAAE,CAACI,MAAM,CAAE,CAACI,IAAI,CAAC,GAAG,CAAC;;EAEzD;EACAR,EAAE,CAACS,OAAO,GAAG,CAAET,EAAE,CAACK,KAAK,EAAEL,EAAE,CAACI,MAAM,CAAE,CAACI,IAAI,CAAC,GAAG,CAAC;;EAE9C;EACA;EACA,IAAIE,eAAe,GAAG,YAAY;;EAElC;EACA;EACA;EACAV,EAAE,CAACW,iBAAiB,GAAS,QAAQ,GAAGD,eAAe,GAAG,GAAG,GAAGV,EAAE,CAACO,QAAQ,GAAG,GAAG,GAAGP,EAAE,CAACC,OAAO,GAAG,GAAG;EACpG;EACA;;EAEA;;EAEAD,EAAE,CAACY,OAAO,GAER,wFAAwF;;EAE1F;EACAZ,EAAE,CAACa,QAAQ,GAAM,WAAW,GAAGb,EAAE,CAACS,OAAO,GAAG,sBAAsB;EAElET,EAAE,CAACc,QAAQ,GAET,iFAAiF;EAEnFd,EAAE,CAACe,mBAAmB,GAEpB,OAAO,GAAGL,eAAe,GAAG,GAAG,GAAGV,EAAE,CAACO,QAAQ,GAAG,4BAA4B,GAAGP,EAAE,CAACO,QAAQ,GAAG,IAAI;EAEnGP,EAAE,CAACgB,QAAQ,GAET,KAAK,GACH,OAAO,GACL,KAAK,GACH,KAAK,GAAGhB,EAAE,CAACS,OAAO,GAAG,GAAG,GAAGC,eAAe,GAAG,2BAA2B,GACxE,WAAW,GAAGV,EAAE,CAACS,OAAO,GAAG,cAAc,GACzC,WAAW,GAAGT,EAAE,CAACS,OAAO,GAAG,cAAc,GACzC,WAAW,GAAGT,EAAE,CAACS,OAAO,GAAG,cAAc,GACzC,WAAW,GAAGT,EAAE,CAACS,OAAO,GAAG,cAAc,GACzC,WAAW,GAAGT,EAAE,CAACS,OAAO,GAAG,cAAc,GACzC,QAAQ,GAAGT,EAAE,CAACW,iBAAiB,GAAG,SAAS;EAAI;EAC/C,wBAAwB;EAAG;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAC3B,QAAQ,GAAGX,EAAE,CAACS,OAAO,GAAG,SAAS,IAChCV,IAAI,IAAIA,IAAI,CAAC,KAAK,CAAC,GAClB,4BAA4B,CAAC;EAAA,EAE7B,OAAO,CACR,GACD,MAAM,GAAGC,EAAE,CAACS,OAAO,GAAG,KAAK;EAAS;EACpC,MAAM,GAAGT,EAAE,CAACS,OAAO,GAAG,KAAK;EAAS;EACpC,SAAS,GAAGT,EAAE,CAACS,OAAO,GAAG,SAAS;EAAI;EACtC,QAAQ,GAAGT,EAAE,CAACS,OAAO,GAAG,QAAQ,GAClC,IAAI,GACN,MAAM,GACR,IAAI;;EAEN;EACA;EACAT,EAAE,CAACiB,cAAc,GAEf,gEAAgE;EAElEjB,EAAE,CAACkB,MAAM,GAEP,uBAAuB;;EAEzB;EACA;;EAEAlB,EAAE,CAACmB,eAAe;EAEhB;EACA,KAAK,GACHnB,EAAE,CAACkB,MAAM,GACT,GAAG,GACHlB,EAAE,CAACW,iBAAiB,GAAG,QAAQ,GACjC,GAAG;EAELX,EAAE,CAACoB,UAAU,GAEX,KAAK,GACHpB,EAAE,CAACkB,MAAM,GACT,GAAG,GACH,KAAK,GAAGlB,EAAE,CAACW,iBAAiB,GAAG,GAAG,GAClC,GAAG,GACH,KAAK,GAAGX,EAAE,CAACW,iBAAiB,GAAG,OAAO,GAAGX,EAAE,CAACW,iBAAiB,GAAG,SAAS,GAAGX,EAAE,CAACW,iBAAiB,GAAG,GAAG,GACxG,GAAG;EAELX,EAAE,CAACqB,QAAQ,GAET,KAAK;EACL;EACA;EACA;EACE,WAAW,GAAGrB,EAAE,CAACoB,UAAU,GAAG,QAAQ,GAAGpB,EAAE,CAACoB,UAAU,aAAY,GAAG,GACvE,GAAG;EAELpB,EAAE,CAACsB,cAAc,GAEf,KAAK,GACHtB,EAAE,CAACY,OAAO,GACZ,GAAG,GACD,WAAW,GAAGZ,EAAE,CAACoB,UAAU,GAAG,mBAAmB,GACnD,GAAG;EAELpB,EAAE,CAACuB,oBAAoB,GAErB,WAAW,GAAGvB,EAAE,CAACoB,UAAU,GAAG,mBAAmB;EAEnDpB,EAAE,CAACwB,eAAe,GAEhBxB,EAAE,CAACqB,QAAQ,GAAGrB,EAAE,CAACe,mBAAmB;EAEtCf,EAAE,CAACyB,qBAAqB,GAEtBzB,EAAE,CAACsB,cAAc,GAAGtB,EAAE,CAACe,mBAAmB;EAE5Cf,EAAE,CAAC0B,oBAAoB,GAErB1B,EAAE,CAACqB,QAAQ,GAAGrB,EAAE,CAACc,QAAQ,GAAGd,EAAE,CAACe,mBAAmB;EAEpDf,EAAE,CAAC2B,0BAA0B,GAE3B3B,EAAE,CAACsB,cAAc,GAAGtB,EAAE,CAACc,QAAQ,GAAGd,EAAE,CAACe,mBAAmB;EAE1Df,EAAE,CAAC4B,gCAAgC,GAEjC5B,EAAE,CAACuB,oBAAoB,GAAGvB,EAAE,CAACc,QAAQ,GAAGd,EAAE,CAACe,mBAAmB;;EAGhE;EACA;;EAEA;EACAf,EAAE,CAAC6B,mBAAmB,GAEpB,qDAAqD,GAAG7B,EAAE,CAACO,QAAQ,GAAG,QAAQ;EAEhFP,EAAE,CAAC8B,eAAe,GAEd,KAAK,GAAGpB,eAAe,GAAG,SAAS,GAAGV,EAAE,CAACS,OAAO,GAAG,GAAG,GACtD,GAAG,GAAGT,EAAE,CAACiB,cAAc,GAAG,GAAG,GAAGjB,EAAE,CAACyB,qBAAqB,GAAG,GAAG;EAElEzB,EAAE,CAAC+B,cAAc;EACb;EACA;EACA,uCAAuC,GAAG/B,EAAE,CAACO,QAAQ,GAAG,IAAI,GAC5D,uBAAuB,GAAGP,EAAE,CAAC2B,0BAA0B,GAAG3B,EAAE,CAACgB,QAAQ,GAAG,GAAG;EAE/EhB,EAAE,CAACgC,oBAAoB;EACnB;EACA;EACA,uCAAuC,GAAGhC,EAAE,CAACO,QAAQ,GAAG,IAAI,GAC5D,uBAAuB,GAAGP,EAAE,CAAC4B,gCAAgC,GAAG5B,EAAE,CAACgB,QAAQ,GAAG,GAAG;EAErF,OAAOhB,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}