{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, resolveComponent as _resolveComponent, createBlock as _createBlock, createCommentVNode as _createCommentVNode, withCtx as _withCtx, createVNode as _createVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"PublicSentimentInfoBody\"\n};\nvar _hoisted_2 = {\n  class: \"PublicSentimentInfoList\"\n};\nvar _hoisted_3 = [\"onClick\"];\nvar _hoisted_4 = [\"innerHTML\"];\nvar _hoisted_5 = {\n  class: \"PublicSentimentInfoText\"\n};\nvar _hoisted_6 = {\n  class: \"globalPagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_empty = _resolveComponent(\"el-empty\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_scrollbar, {\n    always: \"\",\n    class: \"PublicSentimentInfoScrollbar\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_2, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.tableData, function (item, index) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"PublicSentimentInfoItem\",\n          key: `${index}-${$setup.props.type}`,\n          onClick: function onClick($event) {\n            return $setup.handleRowClick(item);\n          }\n        }, [_createElementVNode(\"div\", {\n          class: \"PublicSentimentInfoName\",\n          innerHTML: item.title || item.summary\n        }, null, 8 /* PROPS */, _hoisted_4), _createElementVNode(\"div\", _hoisted_5, [_createElementVNode(\"span\", null, \"来源：\" + _toDisplayString(item.captureWebsite), 1 /* TEXT */), _createElementVNode(\"span\", null, \"发布时间：\" + _toDisplayString(item.publishTime), 1 /* TEXT */)])], 8 /* PROPS */, _hoisted_3);\n      }), 128 /* KEYED_FRAGMENT */)), !$setup.tableData.length ? (_openBlock(), _createBlock(_component_el_empty, {\n        key: 0,\n        description: \"暂无数据\",\n        size: \"20\"\n      })) : _createCommentVNode(\"v-if\", true)])];\n    }),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", _hoisted_6, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.pageNo,\n    \"onUpdate:currentPage\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.pageNo = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[1] || (_cache[1] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": $setup.pageSizes,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleQuery,\n    onCurrentChange: $setup.handleQuery,\n    total: $setup.totals,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"page-sizes\", \"total\"])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_scrollbar", "always", "default", "_withCtx", "_createElementVNode", "_hoisted_2", "_Fragment", "_renderList", "$setup", "tableData", "item", "index", "key", "props", "type", "onClick", "$event", "handleRowClick", "innerHTML", "title", "summary", "_hoisted_4", "_hoisted_5", "_toDisplayString", "captureWebsite", "publishTime", "_hoisted_3", "length", "_createBlock", "_component_el_empty", "description", "size", "_createCommentVNode", "_", "_hoisted_6", "_component_el_pagination", "currentPage", "pageNo", "_cache", "pageSize", "pageSizes", "layout", "onSizeChange", "handleQuery", "onCurrentChange", "total", "totals", "background"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\PublicSentimentInfo\\components\\PublicSentimentInfoBody.vue"], "sourcesContent": ["<template>\r\n  <div class=\"PublicSentimentInfoBody\">\r\n    <el-scrollbar always class=\"PublicSentimentInfoScrollbar\">\r\n      <div class=\"PublicSentimentInfoList\">\r\n        <div class=\"PublicSentimentInfoItem\" v-for=\"(item, index) in tableData\" :key=\"`${index}-${props.type}`\"\r\n          @click=\"handleRowClick(item)\">\r\n          <div class=\"PublicSentimentInfoName\" v-html=\"item.title || item.summary\"></div>\r\n          <div class=\"PublicSentimentInfoText\">\r\n            <span>来源：{{ item.captureWebsite }}</span>\r\n            <span>发布时间：{{ item.publishTime }}</span>\r\n          </div>\r\n        </div>\r\n        <el-empty description=\"暂无数据\" size=\"20\" v-if=\"!tableData.length\" />\r\n      </div>\r\n    </el-scrollbar>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\" v-model:page-size=\"pageSize\" :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\" @size-change=\"handleQuery\" @current-change=\"handleQuery\"\r\n        :total=\"totals\" background />\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'PublicSentimentInfoBody' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onMounted } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { useStore } from 'vuex'\r\nimport { defaultPageSize, pageSizes } from 'common/js/system_var.js'\r\nconst props = defineProps({ type: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst route = useRoute()\r\nconst store = useStore()\r\nconst ticketType = ref('')\r\nconst totals = ref(0)\r\nconst pageNo = ref(1)\r\nconst pageSize = ref(defaultPageSize.value)\r\nconst tableData = ref([])\r\nconst stateData = computed(() => store.getters.getPublicSentimentInfoFn)\r\nconst handleQuery = () => {\r\n  publicOpinionMonitoring(true)\r\n}\r\n\r\nconst publicOpinionMonitoring = async (isType) => {\r\n  if (stateData.value[`${props.type}-${ticketType.value}`] && !isType) {\r\n    totals.value = stateData.value[`${props.type}-${ticketType.value}`].totals\r\n    tableData.value = stateData.value[`${props.type}-${ticketType.value}`].tableData\r\n  } else {\r\n    const { data } = await api.publicOpinionMonitoring({ sensitivityType: props.type, ticketType: ticketType.value, pageNo: pageNo.value, pageSize: pageSize.value })\r\n    tableData.value = data?.list || []\r\n    totals.value = data?.totalCount || 0\r\n    store.commit('setPublicSentimentInfo', { key: `${props.type}-${ticketType.value}`, params: { totals: totals.value, tableData: tableData.value } })\r\n  }\r\n  emit('callback', { type: props.type, totals: totals.value })\r\n}\r\nconst handleRowClick = (row) => {\r\n  window.open(row.url, '_blank')\r\n}\r\nonMounted(() => {\r\n  ticketType.value = route.query.ticketType || '2'\r\n  publicOpinionMonitoring(false)\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.PublicSentimentInfoBody {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .PublicSentimentInfoScrollbar {\r\n    height: calc(100% - 42px);\r\n    border-bottom: 2px solid var(--zy-el-border-color-lighter);\r\n\r\n    .PublicSentimentInfoList {\r\n      padding: var(--zy-distance-five);\r\n      padding-bottom: var(--zy-distance-two);\r\n\r\n      .PublicSentimentInfoItem {\r\n        width: 100%;\r\n        padding: var(--zy-distance-five) 0;\r\n        cursor: pointer;\r\n\r\n        .PublicSentimentInfoName {\r\n          padding-top: var(--zy-distance-five);\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: var(--zy-line-height);\r\n        }\r\n\r\n        .PublicSentimentInfoText {\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          color: var(--zy-el-text-color-secondary);\r\n          padding-top: var(--zy-font-name-distance-five);\r\n          padding-bottom: var(--zy-distance-five);\r\n\r\n          span+span {\r\n            margin-left: 40px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAyB;;EAE3BA,KAAK,EAAC;AAAyB;iBAH1C;iBAAA;;EAOeA,KAAK,EAAC;AAAyB;;EAQrCA,KAAK,EAAC;AAAkB;;;;;uBAd/BC,mBAAA,CAmBM,OAnBNC,UAmBM,GAlBJC,YAAA,CAYeC,uBAAA;IAZDC,MAAM,EAAN,EAAM;IAACL,KAAK,EAAC;;IAF/BM,OAAA,EAAAC,QAAA,CAGM;MAAA,OAUM,CAVNC,mBAAA,CAUM,OAVNC,UAUM,I,kBATJR,mBAAA,CAOMS,SAAA,QAXdC,WAAA,CAIqEC,MAAA,CAAAC,SAAS,EAJ9E,UAIqDC,IAAI,EAAEC,KAAK;6BAAxDd,mBAAA,CAOM;UAPDD,KAAK,EAAC,yBAAyB;UAAqCgB,GAAG,KAAKD,KAAK,IAAIH,MAAA,CAAAK,KAAK,CAACC,IAAI;UACjGC,OAAK,WAALA,OAAKA,CAAAC,MAAA;YAAA,OAAER,MAAA,CAAAS,cAAc,CAACP,IAAI;UAAA;YAC3BN,mBAAA,CAA+E;UAA1ER,KAAK,EAAC,yBAAyB;UAACsB,SAAmC,EAA3BR,IAAI,CAACS,KAAK,IAAIT,IAAI,CAACU;gCAN1EC,UAAA,GAOUjB,mBAAA,CAGM,OAHNkB,UAGM,GAFJlB,mBAAA,CAAyC,cAAnC,KAAG,GAAAmB,gBAAA,CAAGb,IAAI,CAACc,cAAc,kBAC/BpB,mBAAA,CAAwC,cAAlC,OAAK,GAAAmB,gBAAA,CAAGb,IAAI,CAACe,WAAW,iB,mBAT1CC,UAAA;uCAYsDlB,MAAA,CAAAC,SAAS,CAACkB,MAAM,I,cAA9DC,YAAA,CAAkEC,mBAAA;QAZ1EjB,GAAA;QAYkBkB,WAAW,EAAC,MAAM;QAACC,IAAI,EAAC;YAZ1CC,mBAAA,e;;IAAAC,CAAA;MAeI7B,mBAAA,CAIM,OAJN8B,UAIM,GAHJnC,YAAA,CAE+BoC,wBAAA;IAFRC,WAAW,EAAE5B,MAAA,CAAA6B,MAAM;IAhBhD,wBAAAC,MAAA,QAAAA,MAAA,gBAAAtB,MAAA;MAAA,OAgB0CR,MAAA,CAAA6B,MAAM,GAAArB,MAAA;IAAA;IAAU,WAAS,EAAER,MAAA,CAAA+B,QAAQ;IAhB7E,qBAAAD,MAAA,QAAAA,MAAA,gBAAAtB,MAAA;MAAA,OAgBqER,MAAA,CAAA+B,QAAQ,GAAAvB,MAAA;IAAA;IAAG,YAAU,EAAER,MAAA,CAAAgC,SAAS;IAC7FC,MAAM,EAAC,yCAAyC;IAAEC,YAAW,EAAElC,MAAA,CAAAmC,WAAW;IAAGC,eAAc,EAAEpC,MAAA,CAAAmC,WAAW;IACvGE,KAAK,EAAErC,MAAA,CAAAsC,MAAM;IAAEC,UAAU,EAAV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}