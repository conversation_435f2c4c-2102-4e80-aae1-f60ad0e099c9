{"ast": null, "code": "\"use strict\";\n\nvar DocUtils = require(\"docxtemplater\").DocUtils;\nDocUtils.convertPixelsToEmus = function (pixel) {\n  return Math.round(pixel * 9525);\n};\nmodule.exports = DocUtils;", "map": {"version": 3, "names": ["DocUtils", "require", "convertPixelsToEmus", "pixel", "Math", "round", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/docxtemplater-image-module-free@1.1.1/node_modules/docxtemplater-image-module-free/js/docUtils.js"], "sourcesContent": ["\"use strict\";\n\nvar DocUtils = require(\"docxtemplater\").DocUtils;\nDocUtils.convertPixelsToEmus = function (pixel) {\n\treturn Math.round(pixel * 9525);\n};\nmodule.exports = DocUtils;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,eAAe,CAAC,CAACD,QAAQ;AAChDA,QAAQ,CAACE,mBAAmB,GAAG,UAAUC,KAAK,EAAE;EAC/C,OAAOC,IAAI,CAACC,KAAK,CAACF,KAAK,GAAG,IAAI,CAAC;AAChC,CAAC;AACDG,MAAM,CAACC,OAAO,GAAGP,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}