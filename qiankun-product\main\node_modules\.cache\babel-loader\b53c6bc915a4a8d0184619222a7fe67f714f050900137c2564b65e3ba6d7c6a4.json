{"ast": null, "code": "/*\nLanguage: Python\nDescription: Python is an interpreted, object-oriented, high-level programming language with dynamic semantics.\nWebsite: https://www.python.org\nCategory: common\n*/\n\nfunction python(hljs) {\n  var regex = hljs.regex;\n  var IDENT_RE = /(?:[A-Z_a-z\\xAA\\xB5\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0370-\\u0374\\u0376\\u0377\\u037B-\\u037D\\u037F\\u0386\\u0388-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u05D0-\\u05EA\\u05EF-\\u05F2\\u0620-\\u064A\\u066E\\u066F\\u0671-\\u06D3\\u06D5\\u06E5\\u06E6\\u06EE\\u06EF\\u06FA-\\u06FC\\u06FF\\u0710\\u0712-\\u072F\\u074D-\\u07A5\\u07B1\\u07CA-\\u07EA\\u07F4\\u07F5\\u07FA\\u0800-\\u0815\\u081A\\u0824\\u0828\\u0840-\\u0858\\u0860-\\u086A\\u0870-\\u0887\\u0889-\\u088E\\u08A0-\\u08C9\\u0904-\\u0939\\u093D\\u0950\\u0958-\\u0961\\u0971-\\u0980\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BD\\u09CE\\u09DC\\u09DD\\u09DF-\\u09E1\\u09F0\\u09F1\\u09FC\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A59-\\u0A5C\\u0A5E\\u0A72-\\u0A74\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABD\\u0AD0\\u0AE0\\u0AE1\\u0AF9\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3D\\u0B5C\\u0B5D\\u0B5F-\\u0B61\\u0B71\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BD0\\u0C05-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3D\\u0C58-\\u0C5A\\u0C5D\\u0C60\\u0C61\\u0C80\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBD\\u0CDD\\u0CDE\\u0CE0\\u0CE1\\u0CF1\\u0CF2\\u0D04-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D3A\\u0D3D\\u0D4E\\u0D54-\\u0D56\\u0D5F-\\u0D61\\u0D7A-\\u0D7F\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0E01-\\u0E30\\u0E32\\u0E40-\\u0E46\\u0E81\\u0E82\\u0E84\\u0E86-\\u0E8A\\u0E8C-\\u0EA3\\u0EA5\\u0EA7-\\u0EB0\\u0EB2\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EDC-\\u0EDF\\u0F00\\u0F40-\\u0F47\\u0F49-\\u0F6C\\u0F88-\\u0F8C\\u1000-\\u102A\\u103F\\u1050-\\u1055\\u105A-\\u105D\\u1061\\u1065\\u1066\\u106E-\\u1070\\u1075-\\u1081\\u108E\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u1711\\u171F-\\u1731\\u1740-\\u1751\\u1760-\\u176C\\u176E-\\u1770\\u1780-\\u17B3\\u17D7\\u17DC\\u1820-\\u1878\\u1880-\\u18A8\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1950-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u1A00-\\u1A16\\u1A20-\\u1A54\\u1AA7\\u1B05-\\u1B33\\u1B45-\\u1B4C\\u1B83-\\u1BA0\\u1BAE\\u1BAF\\u1BBA-\\u1BE5\\u1C00-\\u1C23\\u1C4D-\\u1C4F\\u1C5A-\\u1C7D\\u1C80-\\u1C8A\\u1C90-\\u1CBA\\u1CBD-\\u1CBF\\u1CE9-\\u1CEC\\u1CEE-\\u1CF3\\u1CF5\\u1CF6\\u1CFA\\u1D00-\\u1DBF\\u1E00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u2071\\u207F\\u2090-\\u209C\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2118-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2CE4\\u2CEB-\\u2CEE\\u2CF2\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D80-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u3005-\\u3007\\u3021-\\u3029\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u309D-\\u309F\\u30A1-\\u30FA\\u30FC-\\u30FF\\u3105-\\u312F\\u3131-\\u318E\\u31A0-\\u31BF\\u31F0-\\u31FF\\u3400-\\u4DBF\\u4E00-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA61F\\uA62A\\uA62B\\uA640-\\uA66E\\uA67F-\\uA69D\\uA6A0-\\uA6EF\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7CD\\uA7D0\\uA7D1\\uA7D3\\uA7D5-\\uA7DC\\uA7F2-\\uA801\\uA803-\\uA805\\uA807-\\uA80A\\uA80C-\\uA822\\uA840-\\uA873\\uA882-\\uA8B3\\uA8F2-\\uA8F7\\uA8FB\\uA8FD\\uA8FE\\uA90A-\\uA925\\uA930-\\uA946\\uA960-\\uA97C\\uA984-\\uA9B2\\uA9CF\\uA9E0-\\uA9E4\\uA9E6-\\uA9EF\\uA9FA-\\uA9FE\\uAA00-\\uAA28\\uAA40-\\uAA42\\uAA44-\\uAA4B\\uAA60-\\uAA76\\uAA7A\\uAA7E-\\uAAAF\\uAAB1\\uAAB5\\uAAB6\\uAAB9-\\uAABD\\uAAC0\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEA\\uAAF2-\\uAAF4\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB69\\uAB70-\\uABE2\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D\\uFB1F-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFC5D\\uFC64-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDF9\\uFE71\\uFE73\\uFE77\\uFE79\\uFE7B\\uFE7D\\uFE7F-\\uFEFC\\uFF21-\\uFF3A\\uFF41-\\uFF5A\\uFF66-\\uFF9D\\uFFA0-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDD40-\\uDD74\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDF00-\\uDF1F\\uDF2D-\\uDF4A\\uDF50-\\uDF75\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF\\uDFD1-\\uDFD5]|\\uD801[\\uDC00-\\uDC9D\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDD70-\\uDD7A\\uDD7C-\\uDD8A\\uDD8C-\\uDD92\\uDD94\\uDD95\\uDD97-\\uDDA1\\uDDA3-\\uDDB1\\uDDB3-\\uDDB9\\uDDBB\\uDDBC\\uDDC0-\\uDDF3\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67\\uDF80-\\uDF85\\uDF87-\\uDFB0\\uDFB2-\\uDFBA]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00\\uDE10-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE35\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE4\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2\\uDD00-\\uDD23\\uDD4A-\\uDD65\\uDD6F-\\uDD85\\uDE80-\\uDEA9\\uDEB0\\uDEB1\\uDEC2-\\uDEC4\\uDF00-\\uDF1C\\uDF27\\uDF30-\\uDF45\\uDF70-\\uDF81\\uDFB0-\\uDFC4\\uDFE0-\\uDFF6]|\\uD804[\\uDC03-\\uDC37\\uDC71\\uDC72\\uDC75\\uDC83-\\uDCAF\\uDCD0-\\uDCE8\\uDD03-\\uDD26\\uDD44\\uDD47\\uDD50-\\uDD72\\uDD76\\uDD83-\\uDDB2\\uDDC1-\\uDDC4\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE2B\\uDE3F\\uDE40\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEDE\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3D\\uDF50\\uDF5D-\\uDF61\\uDF80-\\uDF89\\uDF8B\\uDF8E\\uDF90-\\uDFB5\\uDFB7\\uDFD1\\uDFD3]|\\uD805[\\uDC00-\\uDC34\\uDC47-\\uDC4A\\uDC5F-\\uDC61\\uDC80-\\uDCAF\\uDCC4\\uDCC5\\uDCC7\\uDD80-\\uDDAE\\uDDD8-\\uDDDB\\uDE00-\\uDE2F\\uDE44\\uDE80-\\uDEAA\\uDEB8\\uDF00-\\uDF1A\\uDF40-\\uDF46]|\\uD806[\\uDC00-\\uDC2B\\uDCA0-\\uDCDF\\uDCFF-\\uDD06\\uDD09\\uDD0C-\\uDD13\\uDD15\\uDD16\\uDD18-\\uDD2F\\uDD3F\\uDD41\\uDDA0-\\uDDA7\\uDDAA-\\uDDD0\\uDDE1\\uDDE3\\uDE00\\uDE0B-\\uDE32\\uDE3A\\uDE50\\uDE5C-\\uDE89\\uDE9D\\uDEB0-\\uDEF8\\uDFC0-\\uDFE0]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC2E\\uDC40\\uDC72-\\uDC8F\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD30\\uDD46\\uDD60-\\uDD65\\uDD67\\uDD68\\uDD6A-\\uDD89\\uDD98\\uDEE0-\\uDEF2\\uDF02\\uDF04-\\uDF10\\uDF12-\\uDF33\\uDFB0]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC00-\\uDC6E\\uDC80-\\uDD43]|\\uD80B[\\uDF90-\\uDFF0]|[\\uD80C\\uD80E\\uD80F\\uD81C-\\uD820\\uD822\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879\\uD880-\\uD883\\uD885-\\uD887][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2F\\uDC41-\\uDC46\\uDC60-\\uDFFF]|\\uD810[\\uDC00-\\uDFFA]|\\uD811[\\uDC00-\\uDE46]|\\uD818[\\uDD00-\\uDD1D]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDE70-\\uDEBE\\uDED0-\\uDEED\\uDF00-\\uDF2F\\uDF40-\\uDF43\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDD40-\\uDD6C\\uDE40-\\uDE7F\\uDF00-\\uDF4A\\uDF50\\uDF93-\\uDF9F\\uDFE0\\uDFE1\\uDFE3]|\\uD821[\\uDC00-\\uDFF7]|\\uD823[\\uDC00-\\uDCD5\\uDCFF-\\uDD08]|\\uD82B[\\uDFF0-\\uDFF3\\uDFF5-\\uDFFB\\uDFFD\\uDFFE]|\\uD82C[\\uDC00-\\uDD22\\uDD32\\uDD50-\\uDD52\\uDD55\\uDD64-\\uDD67\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB]|\\uD837[\\uDF00-\\uDF1E\\uDF25-\\uDF2A]|\\uD838[\\uDC30-\\uDC6D\\uDD00-\\uDD2C\\uDD37-\\uDD3D\\uDD4E\\uDE90-\\uDEAD\\uDEC0-\\uDEEB]|\\uD839[\\uDCD0-\\uDCEB\\uDDD0-\\uDDED\\uDDF0\\uDFE0-\\uDFE6\\uDFE8-\\uDFEB\\uDFED\\uDFEE\\uDFF0-\\uDFFE]|\\uD83A[\\uDC00-\\uDCC4\\uDD00-\\uDD43\\uDD4B]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD869[\\uDC00-\\uDEDF\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF39\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0\\uDFF0-\\uDFFF]|\\uD87B[\\uDC00-\\uDE5D]|\\uD87E[\\uDC00-\\uDE1D]|\\uD884[\\uDC00-\\uDF4A\\uDF50-\\uDFFF]|\\uD888[\\uDC00-\\uDFAF])(?:[0-9A-Z_a-z\\xAA\\xB5\\xB7\\xBA\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02C1\\u02C6-\\u02D1\\u02E0-\\u02E4\\u02EC\\u02EE\\u0300-\\u0374\\u0376\\u0377\\u037B-\\u037D\\u037F\\u0386-\\u038A\\u038C\\u038E-\\u03A1\\u03A3-\\u03F5\\u03F7-\\u0481\\u0483-\\u0487\\u048A-\\u052F\\u0531-\\u0556\\u0559\\u0560-\\u0588\\u0591-\\u05BD\\u05BF\\u05C1\\u05C2\\u05C4\\u05C5\\u05C7\\u05D0-\\u05EA\\u05EF-\\u05F2\\u0610-\\u061A\\u0620-\\u0669\\u066E-\\u06D3\\u06D5-\\u06DC\\u06DF-\\u06E8\\u06EA-\\u06FC\\u06FF\\u0710-\\u074A\\u074D-\\u07B1\\u07C0-\\u07F5\\u07FA\\u07FD\\u0800-\\u082D\\u0840-\\u085B\\u0860-\\u086A\\u0870-\\u0887\\u0889-\\u088E\\u0897-\\u08E1\\u08E3-\\u0963\\u0966-\\u096F\\u0971-\\u0983\\u0985-\\u098C\\u098F\\u0990\\u0993-\\u09A8\\u09AA-\\u09B0\\u09B2\\u09B6-\\u09B9\\u09BC-\\u09C4\\u09C7\\u09C8\\u09CB-\\u09CE\\u09D7\\u09DC\\u09DD\\u09DF-\\u09E3\\u09E6-\\u09F1\\u09FC\\u09FE\\u0A01-\\u0A03\\u0A05-\\u0A0A\\u0A0F\\u0A10\\u0A13-\\u0A28\\u0A2A-\\u0A30\\u0A32\\u0A33\\u0A35\\u0A36\\u0A38\\u0A39\\u0A3C\\u0A3E-\\u0A42\\u0A47\\u0A48\\u0A4B-\\u0A4D\\u0A51\\u0A59-\\u0A5C\\u0A5E\\u0A66-\\u0A75\\u0A81-\\u0A83\\u0A85-\\u0A8D\\u0A8F-\\u0A91\\u0A93-\\u0AA8\\u0AAA-\\u0AB0\\u0AB2\\u0AB3\\u0AB5-\\u0AB9\\u0ABC-\\u0AC5\\u0AC7-\\u0AC9\\u0ACB-\\u0ACD\\u0AD0\\u0AE0-\\u0AE3\\u0AE6-\\u0AEF\\u0AF9-\\u0AFF\\u0B01-\\u0B03\\u0B05-\\u0B0C\\u0B0F\\u0B10\\u0B13-\\u0B28\\u0B2A-\\u0B30\\u0B32\\u0B33\\u0B35-\\u0B39\\u0B3C-\\u0B44\\u0B47\\u0B48\\u0B4B-\\u0B4D\\u0B55-\\u0B57\\u0B5C\\u0B5D\\u0B5F-\\u0B63\\u0B66-\\u0B6F\\u0B71\\u0B82\\u0B83\\u0B85-\\u0B8A\\u0B8E-\\u0B90\\u0B92-\\u0B95\\u0B99\\u0B9A\\u0B9C\\u0B9E\\u0B9F\\u0BA3\\u0BA4\\u0BA8-\\u0BAA\\u0BAE-\\u0BB9\\u0BBE-\\u0BC2\\u0BC6-\\u0BC8\\u0BCA-\\u0BCD\\u0BD0\\u0BD7\\u0BE6-\\u0BEF\\u0C00-\\u0C0C\\u0C0E-\\u0C10\\u0C12-\\u0C28\\u0C2A-\\u0C39\\u0C3C-\\u0C44\\u0C46-\\u0C48\\u0C4A-\\u0C4D\\u0C55\\u0C56\\u0C58-\\u0C5A\\u0C5D\\u0C60-\\u0C63\\u0C66-\\u0C6F\\u0C80-\\u0C83\\u0C85-\\u0C8C\\u0C8E-\\u0C90\\u0C92-\\u0CA8\\u0CAA-\\u0CB3\\u0CB5-\\u0CB9\\u0CBC-\\u0CC4\\u0CC6-\\u0CC8\\u0CCA-\\u0CCD\\u0CD5\\u0CD6\\u0CDD\\u0CDE\\u0CE0-\\u0CE3\\u0CE6-\\u0CEF\\u0CF1-\\u0CF3\\u0D00-\\u0D0C\\u0D0E-\\u0D10\\u0D12-\\u0D44\\u0D46-\\u0D48\\u0D4A-\\u0D4E\\u0D54-\\u0D57\\u0D5F-\\u0D63\\u0D66-\\u0D6F\\u0D7A-\\u0D7F\\u0D81-\\u0D83\\u0D85-\\u0D96\\u0D9A-\\u0DB1\\u0DB3-\\u0DBB\\u0DBD\\u0DC0-\\u0DC6\\u0DCA\\u0DCF-\\u0DD4\\u0DD6\\u0DD8-\\u0DDF\\u0DE6-\\u0DEF\\u0DF2\\u0DF3\\u0E01-\\u0E3A\\u0E40-\\u0E4E\\u0E50-\\u0E59\\u0E81\\u0E82\\u0E84\\u0E86-\\u0E8A\\u0E8C-\\u0EA3\\u0EA5\\u0EA7-\\u0EBD\\u0EC0-\\u0EC4\\u0EC6\\u0EC8-\\u0ECE\\u0ED0-\\u0ED9\\u0EDC-\\u0EDF\\u0F00\\u0F18\\u0F19\\u0F20-\\u0F29\\u0F35\\u0F37\\u0F39\\u0F3E-\\u0F47\\u0F49-\\u0F6C\\u0F71-\\u0F84\\u0F86-\\u0F97\\u0F99-\\u0FBC\\u0FC6\\u1000-\\u1049\\u1050-\\u109D\\u10A0-\\u10C5\\u10C7\\u10CD\\u10D0-\\u10FA\\u10FC-\\u1248\\u124A-\\u124D\\u1250-\\u1256\\u1258\\u125A-\\u125D\\u1260-\\u1288\\u128A-\\u128D\\u1290-\\u12B0\\u12B2-\\u12B5\\u12B8-\\u12BE\\u12C0\\u12C2-\\u12C5\\u12C8-\\u12D6\\u12D8-\\u1310\\u1312-\\u1315\\u1318-\\u135A\\u135D-\\u135F\\u1369-\\u1371\\u1380-\\u138F\\u13A0-\\u13F5\\u13F8-\\u13FD\\u1401-\\u166C\\u166F-\\u167F\\u1681-\\u169A\\u16A0-\\u16EA\\u16EE-\\u16F8\\u1700-\\u1715\\u171F-\\u1734\\u1740-\\u1753\\u1760-\\u176C\\u176E-\\u1770\\u1772\\u1773\\u1780-\\u17D3\\u17D7\\u17DC\\u17DD\\u17E0-\\u17E9\\u180B-\\u180D\\u180F-\\u1819\\u1820-\\u1878\\u1880-\\u18AA\\u18B0-\\u18F5\\u1900-\\u191E\\u1920-\\u192B\\u1930-\\u193B\\u1946-\\u196D\\u1970-\\u1974\\u1980-\\u19AB\\u19B0-\\u19C9\\u19D0-\\u19DA\\u1A00-\\u1A1B\\u1A20-\\u1A5E\\u1A60-\\u1A7C\\u1A7F-\\u1A89\\u1A90-\\u1A99\\u1AA7\\u1AB0-\\u1ABD\\u1ABF-\\u1ACE\\u1B00-\\u1B4C\\u1B50-\\u1B59\\u1B6B-\\u1B73\\u1B80-\\u1BF3\\u1C00-\\u1C37\\u1C40-\\u1C49\\u1C4D-\\u1C7D\\u1C80-\\u1C8A\\u1C90-\\u1CBA\\u1CBD-\\u1CBF\\u1CD0-\\u1CD2\\u1CD4-\\u1CFA\\u1D00-\\u1F15\\u1F18-\\u1F1D\\u1F20-\\u1F45\\u1F48-\\u1F4D\\u1F50-\\u1F57\\u1F59\\u1F5B\\u1F5D\\u1F5F-\\u1F7D\\u1F80-\\u1FB4\\u1FB6-\\u1FBC\\u1FBE\\u1FC2-\\u1FC4\\u1FC6-\\u1FCC\\u1FD0-\\u1FD3\\u1FD6-\\u1FDB\\u1FE0-\\u1FEC\\u1FF2-\\u1FF4\\u1FF6-\\u1FFC\\u200C\\u200D\\u203F\\u2040\\u2054\\u2071\\u207F\\u2090-\\u209C\\u20D0-\\u20DC\\u20E1\\u20E5-\\u20F0\\u2102\\u2107\\u210A-\\u2113\\u2115\\u2118-\\u211D\\u2124\\u2126\\u2128\\u212A-\\u2139\\u213C-\\u213F\\u2145-\\u2149\\u214E\\u2160-\\u2188\\u2C00-\\u2CE4\\u2CEB-\\u2CF3\\u2D00-\\u2D25\\u2D27\\u2D2D\\u2D30-\\u2D67\\u2D6F\\u2D7F-\\u2D96\\u2DA0-\\u2DA6\\u2DA8-\\u2DAE\\u2DB0-\\u2DB6\\u2DB8-\\u2DBE\\u2DC0-\\u2DC6\\u2DC8-\\u2DCE\\u2DD0-\\u2DD6\\u2DD8-\\u2DDE\\u2DE0-\\u2DFF\\u3005-\\u3007\\u3021-\\u302F\\u3031-\\u3035\\u3038-\\u303C\\u3041-\\u3096\\u3099\\u309A\\u309D-\\u309F\\u30A1-\\u30FF\\u3105-\\u312F\\u3131-\\u318E\\u31A0-\\u31BF\\u31F0-\\u31FF\\u3400-\\u4DBF\\u4E00-\\uA48C\\uA4D0-\\uA4FD\\uA500-\\uA60C\\uA610-\\uA62B\\uA640-\\uA66F\\uA674-\\uA67D\\uA67F-\\uA6F1\\uA717-\\uA71F\\uA722-\\uA788\\uA78B-\\uA7CD\\uA7D0\\uA7D1\\uA7D3\\uA7D5-\\uA7DC\\uA7F2-\\uA827\\uA82C\\uA840-\\uA873\\uA880-\\uA8C5\\uA8D0-\\uA8D9\\uA8E0-\\uA8F7\\uA8FB\\uA8FD-\\uA92D\\uA930-\\uA953\\uA960-\\uA97C\\uA980-\\uA9C0\\uA9CF-\\uA9D9\\uA9E0-\\uA9FE\\uAA00-\\uAA36\\uAA40-\\uAA4D\\uAA50-\\uAA59\\uAA60-\\uAA76\\uAA7A-\\uAAC2\\uAADB-\\uAADD\\uAAE0-\\uAAEF\\uAAF2-\\uAAF6\\uAB01-\\uAB06\\uAB09-\\uAB0E\\uAB11-\\uAB16\\uAB20-\\uAB26\\uAB28-\\uAB2E\\uAB30-\\uAB5A\\uAB5C-\\uAB69\\uAB70-\\uABEA\\uABEC\\uABED\\uABF0-\\uABF9\\uAC00-\\uD7A3\\uD7B0-\\uD7C6\\uD7CB-\\uD7FB\\uF900-\\uFA6D\\uFA70-\\uFAD9\\uFB00-\\uFB06\\uFB13-\\uFB17\\uFB1D-\\uFB28\\uFB2A-\\uFB36\\uFB38-\\uFB3C\\uFB3E\\uFB40\\uFB41\\uFB43\\uFB44\\uFB46-\\uFBB1\\uFBD3-\\uFC5D\\uFC64-\\uFD3D\\uFD50-\\uFD8F\\uFD92-\\uFDC7\\uFDF0-\\uFDF9\\uFE00-\\uFE0F\\uFE20-\\uFE2F\\uFE33\\uFE34\\uFE4D-\\uFE4F\\uFE71\\uFE73\\uFE77\\uFE79\\uFE7B\\uFE7D\\uFE7F-\\uFEFC\\uFF10-\\uFF19\\uFF21-\\uFF3A\\uFF3F\\uFF41-\\uFF5A\\uFF65-\\uFFBE\\uFFC2-\\uFFC7\\uFFCA-\\uFFCF\\uFFD2-\\uFFD7\\uFFDA-\\uFFDC]|\\uD800[\\uDC00-\\uDC0B\\uDC0D-\\uDC26\\uDC28-\\uDC3A\\uDC3C\\uDC3D\\uDC3F-\\uDC4D\\uDC50-\\uDC5D\\uDC80-\\uDCFA\\uDD40-\\uDD74\\uDDFD\\uDE80-\\uDE9C\\uDEA0-\\uDED0\\uDEE0\\uDF00-\\uDF1F\\uDF2D-\\uDF4A\\uDF50-\\uDF7A\\uDF80-\\uDF9D\\uDFA0-\\uDFC3\\uDFC8-\\uDFCF\\uDFD1-\\uDFD5]|\\uD801[\\uDC00-\\uDC9D\\uDCA0-\\uDCA9\\uDCB0-\\uDCD3\\uDCD8-\\uDCFB\\uDD00-\\uDD27\\uDD30-\\uDD63\\uDD70-\\uDD7A\\uDD7C-\\uDD8A\\uDD8C-\\uDD92\\uDD94\\uDD95\\uDD97-\\uDDA1\\uDDA3-\\uDDB1\\uDDB3-\\uDDB9\\uDDBB\\uDDBC\\uDDC0-\\uDDF3\\uDE00-\\uDF36\\uDF40-\\uDF55\\uDF60-\\uDF67\\uDF80-\\uDF85\\uDF87-\\uDFB0\\uDFB2-\\uDFBA]|\\uD802[\\uDC00-\\uDC05\\uDC08\\uDC0A-\\uDC35\\uDC37\\uDC38\\uDC3C\\uDC3F-\\uDC55\\uDC60-\\uDC76\\uDC80-\\uDC9E\\uDCE0-\\uDCF2\\uDCF4\\uDCF5\\uDD00-\\uDD15\\uDD20-\\uDD39\\uDD80-\\uDDB7\\uDDBE\\uDDBF\\uDE00-\\uDE03\\uDE05\\uDE06\\uDE0C-\\uDE13\\uDE15-\\uDE17\\uDE19-\\uDE35\\uDE38-\\uDE3A\\uDE3F\\uDE60-\\uDE7C\\uDE80-\\uDE9C\\uDEC0-\\uDEC7\\uDEC9-\\uDEE6\\uDF00-\\uDF35\\uDF40-\\uDF55\\uDF60-\\uDF72\\uDF80-\\uDF91]|\\uD803[\\uDC00-\\uDC48\\uDC80-\\uDCB2\\uDCC0-\\uDCF2\\uDD00-\\uDD27\\uDD30-\\uDD39\\uDD40-\\uDD65\\uDD69-\\uDD6D\\uDD6F-\\uDD85\\uDE80-\\uDEA9\\uDEAB\\uDEAC\\uDEB0\\uDEB1\\uDEC2-\\uDEC4\\uDEFC-\\uDF1C\\uDF27\\uDF30-\\uDF50\\uDF70-\\uDF85\\uDFB0-\\uDFC4\\uDFE0-\\uDFF6]|\\uD804[\\uDC00-\\uDC46\\uDC66-\\uDC75\\uDC7F-\\uDCBA\\uDCC2\\uDCD0-\\uDCE8\\uDCF0-\\uDCF9\\uDD00-\\uDD34\\uDD36-\\uDD3F\\uDD44-\\uDD47\\uDD50-\\uDD73\\uDD76\\uDD80-\\uDDC4\\uDDC9-\\uDDCC\\uDDCE-\\uDDDA\\uDDDC\\uDE00-\\uDE11\\uDE13-\\uDE37\\uDE3E-\\uDE41\\uDE80-\\uDE86\\uDE88\\uDE8A-\\uDE8D\\uDE8F-\\uDE9D\\uDE9F-\\uDEA8\\uDEB0-\\uDEEA\\uDEF0-\\uDEF9\\uDF00-\\uDF03\\uDF05-\\uDF0C\\uDF0F\\uDF10\\uDF13-\\uDF28\\uDF2A-\\uDF30\\uDF32\\uDF33\\uDF35-\\uDF39\\uDF3B-\\uDF44\\uDF47\\uDF48\\uDF4B-\\uDF4D\\uDF50\\uDF57\\uDF5D-\\uDF63\\uDF66-\\uDF6C\\uDF70-\\uDF74\\uDF80-\\uDF89\\uDF8B\\uDF8E\\uDF90-\\uDFB5\\uDFB7-\\uDFC0\\uDFC2\\uDFC5\\uDFC7-\\uDFCA\\uDFCC-\\uDFD3\\uDFE1\\uDFE2]|\\uD805[\\uDC00-\\uDC4A\\uDC50-\\uDC59\\uDC5E-\\uDC61\\uDC80-\\uDCC5\\uDCC7\\uDCD0-\\uDCD9\\uDD80-\\uDDB5\\uDDB8-\\uDDC0\\uDDD8-\\uDDDD\\uDE00-\\uDE40\\uDE44\\uDE50-\\uDE59\\uDE80-\\uDEB8\\uDEC0-\\uDEC9\\uDED0-\\uDEE3\\uDF00-\\uDF1A\\uDF1D-\\uDF2B\\uDF30-\\uDF39\\uDF40-\\uDF46]|\\uD806[\\uDC00-\\uDC3A\\uDCA0-\\uDCE9\\uDCFF-\\uDD06\\uDD09\\uDD0C-\\uDD13\\uDD15\\uDD16\\uDD18-\\uDD35\\uDD37\\uDD38\\uDD3B-\\uDD43\\uDD50-\\uDD59\\uDDA0-\\uDDA7\\uDDAA-\\uDDD7\\uDDDA-\\uDDE1\\uDDE3\\uDDE4\\uDE00-\\uDE3E\\uDE47\\uDE50-\\uDE99\\uDE9D\\uDEB0-\\uDEF8\\uDFC0-\\uDFE0\\uDFF0-\\uDFF9]|\\uD807[\\uDC00-\\uDC08\\uDC0A-\\uDC36\\uDC38-\\uDC40\\uDC50-\\uDC59\\uDC72-\\uDC8F\\uDC92-\\uDCA7\\uDCA9-\\uDCB6\\uDD00-\\uDD06\\uDD08\\uDD09\\uDD0B-\\uDD36\\uDD3A\\uDD3C\\uDD3D\\uDD3F-\\uDD47\\uDD50-\\uDD59\\uDD60-\\uDD65\\uDD67\\uDD68\\uDD6A-\\uDD8E\\uDD90\\uDD91\\uDD93-\\uDD98\\uDDA0-\\uDDA9\\uDEE0-\\uDEF6\\uDF00-\\uDF10\\uDF12-\\uDF3A\\uDF3E-\\uDF42\\uDF50-\\uDF5A\\uDFB0]|\\uD808[\\uDC00-\\uDF99]|\\uD809[\\uDC00-\\uDC6E\\uDC80-\\uDD43]|\\uD80B[\\uDF90-\\uDFF0]|[\\uD80C\\uD80E\\uD80F\\uD81C-\\uD820\\uD822\\uD840-\\uD868\\uD86A-\\uD86C\\uD86F-\\uD872\\uD874-\\uD879\\uD880-\\uD883\\uD885-\\uD887][\\uDC00-\\uDFFF]|\\uD80D[\\uDC00-\\uDC2F\\uDC40-\\uDC55\\uDC60-\\uDFFF]|\\uD810[\\uDC00-\\uDFFA]|\\uD811[\\uDC00-\\uDE46]|\\uD818[\\uDD00-\\uDD39]|\\uD81A[\\uDC00-\\uDE38\\uDE40-\\uDE5E\\uDE60-\\uDE69\\uDE70-\\uDEBE\\uDEC0-\\uDEC9\\uDED0-\\uDEED\\uDEF0-\\uDEF4\\uDF00-\\uDF36\\uDF40-\\uDF43\\uDF50-\\uDF59\\uDF63-\\uDF77\\uDF7D-\\uDF8F]|\\uD81B[\\uDD40-\\uDD6C\\uDD70-\\uDD79\\uDE40-\\uDE7F\\uDF00-\\uDF4A\\uDF4F-\\uDF87\\uDF8F-\\uDF9F\\uDFE0\\uDFE1\\uDFE3\\uDFE4\\uDFF0\\uDFF1]|\\uD821[\\uDC00-\\uDFF7]|\\uD823[\\uDC00-\\uDCD5\\uDCFF-\\uDD08]|\\uD82B[\\uDFF0-\\uDFF3\\uDFF5-\\uDFFB\\uDFFD\\uDFFE]|\\uD82C[\\uDC00-\\uDD22\\uDD32\\uDD50-\\uDD52\\uDD55\\uDD64-\\uDD67\\uDD70-\\uDEFB]|\\uD82F[\\uDC00-\\uDC6A\\uDC70-\\uDC7C\\uDC80-\\uDC88\\uDC90-\\uDC99\\uDC9D\\uDC9E]|\\uD833[\\uDCF0-\\uDCF9\\uDF00-\\uDF2D\\uDF30-\\uDF46]|\\uD834[\\uDD65-\\uDD69\\uDD6D-\\uDD72\\uDD7B-\\uDD82\\uDD85-\\uDD8B\\uDDAA-\\uDDAD\\uDE42-\\uDE44]|\\uD835[\\uDC00-\\uDC54\\uDC56-\\uDC9C\\uDC9E\\uDC9F\\uDCA2\\uDCA5\\uDCA6\\uDCA9-\\uDCAC\\uDCAE-\\uDCB9\\uDCBB\\uDCBD-\\uDCC3\\uDCC5-\\uDD05\\uDD07-\\uDD0A\\uDD0D-\\uDD14\\uDD16-\\uDD1C\\uDD1E-\\uDD39\\uDD3B-\\uDD3E\\uDD40-\\uDD44\\uDD46\\uDD4A-\\uDD50\\uDD52-\\uDEA5\\uDEA8-\\uDEC0\\uDEC2-\\uDEDA\\uDEDC-\\uDEFA\\uDEFC-\\uDF14\\uDF16-\\uDF34\\uDF36-\\uDF4E\\uDF50-\\uDF6E\\uDF70-\\uDF88\\uDF8A-\\uDFA8\\uDFAA-\\uDFC2\\uDFC4-\\uDFCB\\uDFCE-\\uDFFF]|\\uD836[\\uDE00-\\uDE36\\uDE3B-\\uDE6C\\uDE75\\uDE84\\uDE9B-\\uDE9F\\uDEA1-\\uDEAF]|\\uD837[\\uDF00-\\uDF1E\\uDF25-\\uDF2A]|\\uD838[\\uDC00-\\uDC06\\uDC08-\\uDC18\\uDC1B-\\uDC21\\uDC23\\uDC24\\uDC26-\\uDC2A\\uDC30-\\uDC6D\\uDC8F\\uDD00-\\uDD2C\\uDD30-\\uDD3D\\uDD40-\\uDD49\\uDD4E\\uDE90-\\uDEAE\\uDEC0-\\uDEF9]|\\uD839[\\uDCD0-\\uDCF9\\uDDD0-\\uDDFA\\uDFE0-\\uDFE6\\uDFE8-\\uDFEB\\uDFED\\uDFEE\\uDFF0-\\uDFFE]|\\uD83A[\\uDC00-\\uDCC4\\uDCD0-\\uDCD6\\uDD00-\\uDD4B\\uDD50-\\uDD59]|\\uD83B[\\uDE00-\\uDE03\\uDE05-\\uDE1F\\uDE21\\uDE22\\uDE24\\uDE27\\uDE29-\\uDE32\\uDE34-\\uDE37\\uDE39\\uDE3B\\uDE42\\uDE47\\uDE49\\uDE4B\\uDE4D-\\uDE4F\\uDE51\\uDE52\\uDE54\\uDE57\\uDE59\\uDE5B\\uDE5D\\uDE5F\\uDE61\\uDE62\\uDE64\\uDE67-\\uDE6A\\uDE6C-\\uDE72\\uDE74-\\uDE77\\uDE79-\\uDE7C\\uDE7E\\uDE80-\\uDE89\\uDE8B-\\uDE9B\\uDEA1-\\uDEA3\\uDEA5-\\uDEA9\\uDEAB-\\uDEBB]|\\uD83E[\\uDFF0-\\uDFF9]|\\uD869[\\uDC00-\\uDEDF\\uDF00-\\uDFFF]|\\uD86D[\\uDC00-\\uDF39\\uDF40-\\uDFFF]|\\uD86E[\\uDC00-\\uDC1D\\uDC20-\\uDFFF]|\\uD873[\\uDC00-\\uDEA1\\uDEB0-\\uDFFF]|\\uD87A[\\uDC00-\\uDFE0\\uDFF0-\\uDFFF]|\\uD87B[\\uDC00-\\uDE5D]|\\uD87E[\\uDC00-\\uDE1D]|\\uD884[\\uDC00-\\uDF4A\\uDF50-\\uDFFF]|\\uD888[\\uDC00-\\uDFAF]|\\uDB40[\\uDD00-\\uDDEF])*/;\n  var RESERVED_WORDS = ['and', 'as', 'assert', 'async', 'await', 'break', 'case', 'class', 'continue', 'def', 'del', 'elif', 'else', 'except', 'finally', 'for', 'from', 'global', 'if', 'import', 'in', 'is', 'lambda', 'match', 'nonlocal|10', 'not', 'or', 'pass', 'raise', 'return', 'try', 'while', 'with', 'yield'];\n  var BUILT_INS = ['__import__', 'abs', 'all', 'any', 'ascii', 'bin', 'bool', 'breakpoint', 'bytearray', 'bytes', 'callable', 'chr', 'classmethod', 'compile', 'complex', 'delattr', 'dict', 'dir', 'divmod', 'enumerate', 'eval', 'exec', 'filter', 'float', 'format', 'frozenset', 'getattr', 'globals', 'hasattr', 'hash', 'help', 'hex', 'id', 'input', 'int', 'isinstance', 'issubclass', 'iter', 'len', 'list', 'locals', 'map', 'max', 'memoryview', 'min', 'next', 'object', 'oct', 'open', 'ord', 'pow', 'print', 'property', 'range', 'repr', 'reversed', 'round', 'set', 'setattr', 'slice', 'sorted', 'staticmethod', 'str', 'sum', 'super', 'tuple', 'type', 'vars', 'zip'];\n  var LITERALS = ['__debug__', 'Ellipsis', 'False', 'None', 'NotImplemented', 'True'];\n\n  // https://docs.python.org/3/library/typing.html\n  // TODO: Could these be supplemented by a CamelCase matcher in certain\n  // contexts, leaving these remaining only for relevance hinting?\n  var TYPES = [\"Any\", \"Callable\", \"Coroutine\", \"Dict\", \"List\", \"Literal\", \"Generic\", \"Optional\", \"Sequence\", \"Set\", \"Tuple\", \"Type\", \"Union\"];\n  var KEYWORDS = {\n    $pattern: /[A-Za-z]\\w+|__\\w+__/,\n    keyword: RESERVED_WORDS,\n    built_in: BUILT_INS,\n    literal: LITERALS,\n    type: TYPES\n  };\n  var PROMPT = {\n    className: 'meta',\n    begin: /^(>>>|\\.\\.\\.) /\n  };\n  var SUBST = {\n    className: 'subst',\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS,\n    illegal: /#/\n  };\n  var LITERAL_BRACKET = {\n    begin: /\\{\\{/,\n    relevance: 0\n  };\n  var STRING = {\n    className: 'string',\n    contains: [hljs.BACKSLASH_ESCAPE],\n    variants: [{\n      begin: /([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?'''/,\n      end: /'''/,\n      contains: [hljs.BACKSLASH_ESCAPE, PROMPT],\n      relevance: 10\n    }, {\n      begin: /([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?\"\"\"/,\n      end: /\"\"\"/,\n      contains: [hljs.BACKSLASH_ESCAPE, PROMPT],\n      relevance: 10\n    }, {\n      begin: /([fF][rR]|[rR][fF]|[fF])'''/,\n      end: /'''/,\n      contains: [hljs.BACKSLASH_ESCAPE, PROMPT, LITERAL_BRACKET, SUBST]\n    }, {\n      begin: /([fF][rR]|[rR][fF]|[fF])\"\"\"/,\n      end: /\"\"\"/,\n      contains: [hljs.BACKSLASH_ESCAPE, PROMPT, LITERAL_BRACKET, SUBST]\n    }, {\n      begin: /([uU]|[rR])'/,\n      end: /'/,\n      relevance: 10\n    }, {\n      begin: /([uU]|[rR])\"/,\n      end: /\"/,\n      relevance: 10\n    }, {\n      begin: /([bB]|[bB][rR]|[rR][bB])'/,\n      end: /'/\n    }, {\n      begin: /([bB]|[bB][rR]|[rR][bB])\"/,\n      end: /\"/\n    }, {\n      begin: /([fF][rR]|[rR][fF]|[fF])'/,\n      end: /'/,\n      contains: [hljs.BACKSLASH_ESCAPE, LITERAL_BRACKET, SUBST]\n    }, {\n      begin: /([fF][rR]|[rR][fF]|[fF])\"/,\n      end: /\"/,\n      contains: [hljs.BACKSLASH_ESCAPE, LITERAL_BRACKET, SUBST]\n    }, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE]\n  };\n\n  // https://docs.python.org/3.9/reference/lexical_analysis.html#numeric-literals\n  var digitpart = '[0-9](_?[0-9])*';\n  var pointfloat = `(\\\\b(${digitpart}))?\\\\.(${digitpart})|\\\\b(${digitpart})\\\\.`;\n  // Whitespace after a number (or any lexical token) is needed only if its absence\n  // would change the tokenization\n  // https://docs.python.org/3.9/reference/lexical_analysis.html#whitespace-between-tokens\n  // We deviate slightly, requiring a word boundary or a keyword\n  // to avoid accidentally recognizing *prefixes* (e.g., `0` in `0x41` or `08` or `0__1`)\n  var lookahead = `\\\\b|${RESERVED_WORDS.join('|')}`;\n  var NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n    // exponentfloat, pointfloat\n    // https://docs.python.org/3.9/reference/lexical_analysis.html#floating-point-literals\n    // optionally imaginary\n    // https://docs.python.org/3.9/reference/lexical_analysis.html#imaginary-literals\n    // Note: no leading \\b because floats can start with a decimal point\n    // and we don't want to mishandle e.g. `fn(.5)`,\n    // no trailing \\b for pointfloat because it can end with a decimal point\n    // and we don't want to mishandle e.g. `0..hex()`; this should be safe\n    // because both MUST contain a decimal point and so cannot be confused with\n    // the interior part of an identifier\n    {\n      begin: `(\\\\b(${digitpart})|(${pointfloat}))[eE][+-]?(${digitpart})[jJ]?(?=${lookahead})`\n    }, {\n      begin: `(${pointfloat})[jJ]?`\n    },\n    // decinteger, bininteger, octinteger, hexinteger\n    // https://docs.python.org/3.9/reference/lexical_analysis.html#integer-literals\n    // optionally \"long\" in Python 2\n    // https://docs.python.org/2.7/reference/lexical_analysis.html#integer-and-long-integer-literals\n    // decinteger is optionally imaginary\n    // https://docs.python.org/3.9/reference/lexical_analysis.html#imaginary-literals\n    {\n      begin: `\\\\b([1-9](_?[0-9])*|0+(_?0)*)[lLjJ]?(?=${lookahead})`\n    }, {\n      begin: `\\\\b0[bB](_?[01])+[lL]?(?=${lookahead})`\n    }, {\n      begin: `\\\\b0[oO](_?[0-7])+[lL]?(?=${lookahead})`\n    }, {\n      begin: `\\\\b0[xX](_?[0-9a-fA-F])+[lL]?(?=${lookahead})`\n    },\n    // imagnumber (digitpart-based)\n    // https://docs.python.org/3.9/reference/lexical_analysis.html#imaginary-literals\n    {\n      begin: `\\\\b(${digitpart})[jJ](?=${lookahead})`\n    }]\n  };\n  var COMMENT_TYPE = {\n    className: \"comment\",\n    begin: regex.lookahead(/# type:/),\n    end: /$/,\n    keywords: KEYWORDS,\n    contains: [{\n      // prevent keywords from coloring `type`\n      begin: /# type:/\n    },\n    // comment within a datatype comment includes no keywords\n    {\n      begin: /#/,\n      end: /\\b\\B/,\n      endsWithParent: true\n    }]\n  };\n  var PARAMS = {\n    className: 'params',\n    variants: [\n    // Exclude params in functions without params\n    {\n      className: \"\",\n      begin: /\\(\\s*\\)/,\n      skip: true\n    }, {\n      begin: /\\(/,\n      end: /\\)/,\n      excludeBegin: true,\n      excludeEnd: true,\n      keywords: KEYWORDS,\n      contains: ['self', PROMPT, NUMBER, STRING, hljs.HASH_COMMENT_MODE]\n    }]\n  };\n  SUBST.contains = [STRING, NUMBER, PROMPT];\n  return {\n    name: 'Python',\n    aliases: ['py', 'gyp', 'ipython'],\n    unicodeRegex: true,\n    keywords: KEYWORDS,\n    illegal: /(<\\/|\\?)|=>/,\n    contains: [PROMPT, NUMBER, {\n      // very common convention\n      scope: 'variable.language',\n      match: /\\bself\\b/\n    }, {\n      // eat \"if\" prior to string so that it won't accidentally be\n      // labeled as an f-string\n      beginKeywords: \"if\",\n      relevance: 0\n    }, {\n      match: /\\bor\\b/,\n      scope: \"keyword\"\n    }, STRING, COMMENT_TYPE, hljs.HASH_COMMENT_MODE, {\n      match: [/\\bdef/, /\\s+/, IDENT_RE],\n      scope: {\n        1: \"keyword\",\n        3: \"title.function\"\n      },\n      contains: [PARAMS]\n    }, {\n      variants: [{\n        match: [/\\bclass/, /\\s+/, IDENT_RE, /\\s*/, /\\(\\s*/, IDENT_RE, /\\s*\\)/]\n      }, {\n        match: [/\\bclass/, /\\s+/, IDENT_RE]\n      }],\n      scope: {\n        1: \"keyword\",\n        3: \"title.class\",\n        6: \"title.class.inherited\"\n      }\n    }, {\n      className: 'meta',\n      begin: /^[\\t ]*@/,\n      end: /(?=#)|$/,\n      contains: [NUMBER, PARAMS, STRING]\n    }]\n  };\n}\nexport { python as default };", "map": {"version": 3, "names": ["python", "hljs", "regex", "IDENT_RE", "RESERVED_WORDS", "BUILT_INS", "LITERALS", "TYPES", "KEYWORDS", "$pattern", "keyword", "built_in", "literal", "type", "PROMPT", "className", "begin", "SUBST", "end", "keywords", "illegal", "LITERAL_BRACKET", "relevance", "STRING", "contains", "BACKSLASH_ESCAPE", "variants", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "digitpart", "pointfloat", "<PERSON><PERSON><PERSON>", "join", "NUMBER", "COMMENT_TYPE", "endsWithParent", "PARAMS", "skip", "excludeBegin", "excludeEnd", "HASH_COMMENT_MODE", "name", "aliases", "unicodeRegex", "scope", "match", "beginKeywords", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/highlight.js@11.11.1/node_modules/highlight.js/es/languages/python.js"], "sourcesContent": ["/*\nLanguage: Python\nDescription: Python is an interpreted, object-oriented, high-level programming language with dynamic semantics.\nWebsite: https://www.python.org\nCategory: common\n*/\n\nfunction python(hljs) {\n  const regex = hljs.regex;\n  const IDENT_RE = /[\\p{XID_Start}_]\\p{XID_Continue}*/u;\n  const RESERVED_WORDS = [\n    'and',\n    'as',\n    'assert',\n    'async',\n    'await',\n    'break',\n    'case',\n    'class',\n    'continue',\n    'def',\n    'del',\n    'elif',\n    'else',\n    'except',\n    'finally',\n    'for',\n    'from',\n    'global',\n    'if',\n    'import',\n    'in',\n    'is',\n    'lambda',\n    'match',\n    'nonlocal|10',\n    'not',\n    'or',\n    'pass',\n    'raise',\n    'return',\n    'try',\n    'while',\n    'with',\n    'yield'\n  ];\n\n  const BUILT_INS = [\n    '__import__',\n    'abs',\n    'all',\n    'any',\n    'ascii',\n    'bin',\n    'bool',\n    'breakpoint',\n    'bytearray',\n    'bytes',\n    'callable',\n    'chr',\n    'classmethod',\n    'compile',\n    'complex',\n    'delattr',\n    'dict',\n    'dir',\n    'divmod',\n    'enumerate',\n    'eval',\n    'exec',\n    'filter',\n    'float',\n    'format',\n    'frozenset',\n    'getattr',\n    'globals',\n    'hasattr',\n    'hash',\n    'help',\n    'hex',\n    'id',\n    'input',\n    'int',\n    'isinstance',\n    'issubclass',\n    'iter',\n    'len',\n    'list',\n    'locals',\n    'map',\n    'max',\n    'memoryview',\n    'min',\n    'next',\n    'object',\n    'oct',\n    'open',\n    'ord',\n    'pow',\n    'print',\n    'property',\n    'range',\n    'repr',\n    'reversed',\n    'round',\n    'set',\n    'setattr',\n    'slice',\n    'sorted',\n    'staticmethod',\n    'str',\n    'sum',\n    'super',\n    'tuple',\n    'type',\n    'vars',\n    'zip'\n  ];\n\n  const LITERALS = [\n    '__debug__',\n    'Ellipsis',\n    'False',\n    'None',\n    'NotImplemented',\n    'True'\n  ];\n\n  // https://docs.python.org/3/library/typing.html\n  // TODO: Could these be supplemented by a CamelCase matcher in certain\n  // contexts, leaving these remaining only for relevance hinting?\n  const TYPES = [\n    \"Any\",\n    \"Callable\",\n    \"Coroutine\",\n    \"Dict\",\n    \"List\",\n    \"Literal\",\n    \"Generic\",\n    \"Optional\",\n    \"Sequence\",\n    \"Set\",\n    \"Tuple\",\n    \"Type\",\n    \"Union\"\n  ];\n\n  const KEYWORDS = {\n    $pattern: /[A-Za-z]\\w+|__\\w+__/,\n    keyword: RESERVED_WORDS,\n    built_in: BUILT_INS,\n    literal: LITERALS,\n    type: TYPES\n  };\n\n  const PROMPT = {\n    className: 'meta',\n    begin: /^(>>>|\\.\\.\\.) /\n  };\n\n  const SUBST = {\n    className: 'subst',\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS,\n    illegal: /#/\n  };\n\n  const LITERAL_BRACKET = {\n    begin: /\\{\\{/,\n    relevance: 0\n  };\n\n  const STRING = {\n    className: 'string',\n    contains: [ hljs.BACKSLASH_ESCAPE ],\n    variants: [\n      {\n        begin: /([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?'''/,\n        end: /'''/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT\n        ],\n        relevance: 10\n      },\n      {\n        begin: /([uU]|[bB]|[rR]|[bB][rR]|[rR][bB])?\"\"\"/,\n        end: /\"\"\"/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT\n        ],\n        relevance: 10\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])'''/,\n        end: /'''/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])\"\"\"/,\n        end: /\"\"\"/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          PROMPT,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      {\n        begin: /([uU]|[rR])'/,\n        end: /'/,\n        relevance: 10\n      },\n      {\n        begin: /([uU]|[rR])\"/,\n        end: /\"/,\n        relevance: 10\n      },\n      {\n        begin: /([bB]|[bB][rR]|[rR][bB])'/,\n        end: /'/\n      },\n      {\n        begin: /([bB]|[bB][rR]|[rR][bB])\"/,\n        end: /\"/\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])'/,\n        end: /'/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      {\n        begin: /([fF][rR]|[rR][fF]|[fF])\"/,\n        end: /\"/,\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          LITERAL_BRACKET,\n          SUBST\n        ]\n      },\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE\n    ]\n  };\n\n  // https://docs.python.org/3.9/reference/lexical_analysis.html#numeric-literals\n  const digitpart = '[0-9](_?[0-9])*';\n  const pointfloat = `(\\\\b(${digitpart}))?\\\\.(${digitpart})|\\\\b(${digitpart})\\\\.`;\n  // Whitespace after a number (or any lexical token) is needed only if its absence\n  // would change the tokenization\n  // https://docs.python.org/3.9/reference/lexical_analysis.html#whitespace-between-tokens\n  // We deviate slightly, requiring a word boundary or a keyword\n  // to avoid accidentally recognizing *prefixes* (e.g., `0` in `0x41` or `08` or `0__1`)\n  const lookahead = `\\\\b|${RESERVED_WORDS.join('|')}`;\n  const NUMBER = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      // exponentfloat, pointfloat\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#floating-point-literals\n      // optionally imaginary\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#imaginary-literals\n      // Note: no leading \\b because floats can start with a decimal point\n      // and we don't want to mishandle e.g. `fn(.5)`,\n      // no trailing \\b for pointfloat because it can end with a decimal point\n      // and we don't want to mishandle e.g. `0..hex()`; this should be safe\n      // because both MUST contain a decimal point and so cannot be confused with\n      // the interior part of an identifier\n      {\n        begin: `(\\\\b(${digitpart})|(${pointfloat}))[eE][+-]?(${digitpart})[jJ]?(?=${lookahead})`\n      },\n      {\n        begin: `(${pointfloat})[jJ]?`\n      },\n\n      // decinteger, bininteger, octinteger, hexinteger\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#integer-literals\n      // optionally \"long\" in Python 2\n      // https://docs.python.org/2.7/reference/lexical_analysis.html#integer-and-long-integer-literals\n      // decinteger is optionally imaginary\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#imaginary-literals\n      {\n        begin: `\\\\b([1-9](_?[0-9])*|0+(_?0)*)[lLjJ]?(?=${lookahead})`\n      },\n      {\n        begin: `\\\\b0[bB](_?[01])+[lL]?(?=${lookahead})`\n      },\n      {\n        begin: `\\\\b0[oO](_?[0-7])+[lL]?(?=${lookahead})`\n      },\n      {\n        begin: `\\\\b0[xX](_?[0-9a-fA-F])+[lL]?(?=${lookahead})`\n      },\n\n      // imagnumber (digitpart-based)\n      // https://docs.python.org/3.9/reference/lexical_analysis.html#imaginary-literals\n      {\n        begin: `\\\\b(${digitpart})[jJ](?=${lookahead})`\n      }\n    ]\n  };\n  const COMMENT_TYPE = {\n    className: \"comment\",\n    begin: regex.lookahead(/# type:/),\n    end: /$/,\n    keywords: KEYWORDS,\n    contains: [\n      { // prevent keywords from coloring `type`\n        begin: /# type:/\n      },\n      // comment within a datatype comment includes no keywords\n      {\n        begin: /#/,\n        end: /\\b\\B/,\n        endsWithParent: true\n      }\n    ]\n  };\n  const PARAMS = {\n    className: 'params',\n    variants: [\n      // Exclude params in functions without params\n      {\n        className: \"\",\n        begin: /\\(\\s*\\)/,\n        skip: true\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        excludeBegin: true,\n        excludeEnd: true,\n        keywords: KEYWORDS,\n        contains: [\n          'self',\n          PROMPT,\n          NUMBER,\n          STRING,\n          hljs.HASH_COMMENT_MODE\n        ]\n      }\n    ]\n  };\n  SUBST.contains = [\n    STRING,\n    NUMBER,\n    PROMPT\n  ];\n\n  return {\n    name: 'Python',\n    aliases: [\n      'py',\n      'gyp',\n      'ipython'\n    ],\n    unicodeRegex: true,\n    keywords: KEYWORDS,\n    illegal: /(<\\/|\\?)|=>/,\n    contains: [\n      PROMPT,\n      NUMBER,\n      {\n        // very common convention\n        scope: 'variable.language',\n        match: /\\bself\\b/\n      },\n      {\n        // eat \"if\" prior to string so that it won't accidentally be\n        // labeled as an f-string\n        beginKeywords: \"if\",\n        relevance: 0\n      },\n      { match: /\\bor\\b/, scope: \"keyword\" },\n      STRING,\n      COMMENT_TYPE,\n      hljs.HASH_COMMENT_MODE,\n      {\n        match: [\n          /\\bdef/, /\\s+/,\n          IDENT_RE,\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.function\"\n        },\n        contains: [ PARAMS ]\n      },\n      {\n        variants: [\n          {\n            match: [\n              /\\bclass/, /\\s+/,\n              IDENT_RE, /\\s*/,\n              /\\(\\s*/, IDENT_RE,/\\s*\\)/\n            ],\n          },\n          {\n            match: [\n              /\\bclass/, /\\s+/,\n              IDENT_RE\n            ],\n          }\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\",\n          6: \"title.class.inherited\",\n        }\n      },\n      {\n        className: 'meta',\n        begin: /^[\\t ]*@/,\n        end: /(?=#)|$/,\n        contains: [\n          NUMBER,\n          PARAMS,\n          STRING\n        ]\n      }\n    ]\n  };\n}\n\nexport { python as default };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,IAAMC,KAAK,GAAGD,IAAI,CAACC,KAAK;EACxB,IAAMC,QAAQ,GAAG,46jBAAoC;EACrD,IAAMC,cAAc,GAAG,CACrB,KAAK,EACL,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,UAAU,EACV,KAAK,EACL,KAAK,EACL,MAAM,EACN,MAAM,EACN,QAAQ,EACR,SAAS,EACT,KAAK,EACL,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,QAAQ,EACR,OAAO,EACP,aAAa,EACb,KAAK,EACL,IAAI,EACJ,MAAM,EACN,OAAO,EACP,QAAQ,EACR,KAAK,EACL,OAAO,EACP,MAAM,EACN,OAAO,CACR;EAED,IAAMC,SAAS,GAAG,CAChB,YAAY,EACZ,KAAK,EACL,KAAK,EACL,KAAK,EACL,OAAO,EACP,KAAK,EACL,MAAM,EACN,YAAY,EACZ,WAAW,EACX,OAAO,EACP,UAAU,EACV,KAAK,EACL,aAAa,EACb,SAAS,EACT,SAAS,EACT,SAAS,EACT,MAAM,EACN,KAAK,EACL,QAAQ,EACR,WAAW,EACX,MAAM,EACN,MAAM,EACN,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,WAAW,EACX,SAAS,EACT,SAAS,EACT,SAAS,EACT,MAAM,EACN,MAAM,EACN,KAAK,EACL,IAAI,EACJ,OAAO,EACP,KAAK,EACL,YAAY,EACZ,YAAY,EACZ,MAAM,EACN,KAAK,EACL,MAAM,EACN,QAAQ,EACR,KAAK,EACL,KAAK,EACL,YAAY,EACZ,KAAK,EACL,MAAM,EACN,QAAQ,EACR,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,OAAO,EACP,UAAU,EACV,OAAO,EACP,MAAM,EACN,UAAU,EACV,OAAO,EACP,KAAK,EACL,SAAS,EACT,OAAO,EACP,QAAQ,EACR,cAAc,EACd,KAAK,EACL,KAAK,EACL,OAAO,EACP,OAAO,EACP,MAAM,EACN,MAAM,EACN,KAAK,CACN;EAED,IAAMC,QAAQ,GAAG,CACf,WAAW,EACX,UAAU,EACV,OAAO,EACP,MAAM,EACN,gBAAgB,EAChB,MAAM,CACP;;EAED;EACA;EACA;EACA,IAAMC,KAAK,GAAG,CACZ,KAAK,EACL,UAAU,EACV,WAAW,EACX,MAAM,EACN,MAAM,EACN,SAAS,EACT,SAAS,EACT,UAAU,EACV,UAAU,EACV,KAAK,EACL,OAAO,EACP,MAAM,EACN,OAAO,CACR;EAED,IAAMC,QAAQ,GAAG;IACfC,QAAQ,EAAE,qBAAqB;IAC/BC,OAAO,EAAEN,cAAc;IACvBO,QAAQ,EAAEN,SAAS;IACnBO,OAAO,EAAEN,QAAQ;IACjBO,IAAI,EAAEN;EACR,CAAC;EAED,IAAMO,MAAM,GAAG;IACbC,SAAS,EAAE,MAAM;IACjBC,KAAK,EAAE;EACT,CAAC;EAED,IAAMC,KAAK,GAAG;IACZF,SAAS,EAAE,OAAO;IAClBC,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,IAAI;IACTC,QAAQ,EAAEX,QAAQ;IAClBY,OAAO,EAAE;EACX,CAAC;EAED,IAAMC,eAAe,GAAG;IACtBL,KAAK,EAAE,MAAM;IACbM,SAAS,EAAE;EACb,CAAC;EAED,IAAMC,MAAM,GAAG;IACbR,SAAS,EAAE,QAAQ;IACnBS,QAAQ,EAAE,CAAEvB,IAAI,CAACwB,gBAAgB,CAAE;IACnCC,QAAQ,EAAE,CACR;MACEV,KAAK,EAAE,wCAAwC;MAC/CE,GAAG,EAAE,KAAK;MACVM,QAAQ,EAAE,CACRvB,IAAI,CAACwB,gBAAgB,EACrBX,MAAM,CACP;MACDQ,SAAS,EAAE;IACb,CAAC,EACD;MACEN,KAAK,EAAE,wCAAwC;MAC/CE,GAAG,EAAE,KAAK;MACVM,QAAQ,EAAE,CACRvB,IAAI,CAACwB,gBAAgB,EACrBX,MAAM,CACP;MACDQ,SAAS,EAAE;IACb,CAAC,EACD;MACEN,KAAK,EAAE,6BAA6B;MACpCE,GAAG,EAAE,KAAK;MACVM,QAAQ,EAAE,CACRvB,IAAI,CAACwB,gBAAgB,EACrBX,MAAM,EACNO,eAAe,EACfJ,KAAK;IAET,CAAC,EACD;MACED,KAAK,EAAE,6BAA6B;MACpCE,GAAG,EAAE,KAAK;MACVM,QAAQ,EAAE,CACRvB,IAAI,CAACwB,gBAAgB,EACrBX,MAAM,EACNO,eAAe,EACfJ,KAAK;IAET,CAAC,EACD;MACED,KAAK,EAAE,cAAc;MACrBE,GAAG,EAAE,GAAG;MACRI,SAAS,EAAE;IACb,CAAC,EACD;MACEN,KAAK,EAAE,cAAc;MACrBE,GAAG,EAAE,GAAG;MACRI,SAAS,EAAE;IACb,CAAC,EACD;MACEN,KAAK,EAAE,2BAA2B;MAClCE,GAAG,EAAE;IACP,CAAC,EACD;MACEF,KAAK,EAAE,2BAA2B;MAClCE,GAAG,EAAE;IACP,CAAC,EACD;MACEF,KAAK,EAAE,2BAA2B;MAClCE,GAAG,EAAE,GAAG;MACRM,QAAQ,EAAE,CACRvB,IAAI,CAACwB,gBAAgB,EACrBJ,eAAe,EACfJ,KAAK;IAET,CAAC,EACD;MACED,KAAK,EAAE,2BAA2B;MAClCE,GAAG,EAAE,GAAG;MACRM,QAAQ,EAAE,CACRvB,IAAI,CAACwB,gBAAgB,EACrBJ,eAAe,EACfJ,KAAK;IAET,CAAC,EACDhB,IAAI,CAAC0B,gBAAgB,EACrB1B,IAAI,CAAC2B,iBAAiB;EAE1B,CAAC;;EAED;EACA,IAAMC,SAAS,GAAG,iBAAiB;EACnC,IAAMC,UAAU,GAAG,QAAQD,SAAS,UAAUA,SAAS,SAASA,SAAS,MAAM;EAC/E;EACA;EACA;EACA;EACA;EACA,IAAME,SAAS,GAAG,OAAO3B,cAAc,CAAC4B,IAAI,CAAC,GAAG,CAAC,EAAE;EACnD,IAAMC,MAAM,GAAG;IACblB,SAAS,EAAE,QAAQ;IACnBO,SAAS,EAAE,CAAC;IACZI,QAAQ,EAAE;IACR;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;MACEV,KAAK,EAAE,QAAQa,SAAS,MAAMC,UAAU,eAAeD,SAAS,YAAYE,SAAS;IACvF,CAAC,EACD;MACEf,KAAK,EAAE,IAAIc,UAAU;IACvB,CAAC;IAED;IACA;IACA;IACA;IACA;IACA;IACA;MACEd,KAAK,EAAE,0CAA0Ce,SAAS;IAC5D,CAAC,EACD;MACEf,KAAK,EAAE,4BAA4Be,SAAS;IAC9C,CAAC,EACD;MACEf,KAAK,EAAE,6BAA6Be,SAAS;IAC/C,CAAC,EACD;MACEf,KAAK,EAAE,mCAAmCe,SAAS;IACrD,CAAC;IAED;IACA;IACA;MACEf,KAAK,EAAE,OAAOa,SAAS,WAAWE,SAAS;IAC7C,CAAC;EAEL,CAAC;EACD,IAAMG,YAAY,GAAG;IACnBnB,SAAS,EAAE,SAAS;IACpBC,KAAK,EAAEd,KAAK,CAAC6B,SAAS,CAAC,SAAS,CAAC;IACjCb,GAAG,EAAE,GAAG;IACRC,QAAQ,EAAEX,QAAQ;IAClBgB,QAAQ,EAAE,CACR;MAAE;MACAR,KAAK,EAAE;IACT,CAAC;IACD;IACA;MACEA,KAAK,EAAE,GAAG;MACVE,GAAG,EAAE,MAAM;MACXiB,cAAc,EAAE;IAClB,CAAC;EAEL,CAAC;EACD,IAAMC,MAAM,GAAG;IACbrB,SAAS,EAAE,QAAQ;IACnBW,QAAQ,EAAE;IACR;IACA;MACEX,SAAS,EAAE,EAAE;MACbC,KAAK,EAAE,SAAS;MAChBqB,IAAI,EAAE;IACR,CAAC,EACD;MACErB,KAAK,EAAE,IAAI;MACXE,GAAG,EAAE,IAAI;MACToB,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,IAAI;MAChBpB,QAAQ,EAAEX,QAAQ;MAClBgB,QAAQ,EAAE,CACR,MAAM,EACNV,MAAM,EACNmB,MAAM,EACNV,MAAM,EACNtB,IAAI,CAACuC,iBAAiB;IAE1B,CAAC;EAEL,CAAC;EACDvB,KAAK,CAACO,QAAQ,GAAG,CACfD,MAAM,EACNU,MAAM,EACNnB,MAAM,CACP;EAED,OAAO;IACL2B,IAAI,EAAE,QAAQ;IACdC,OAAO,EAAE,CACP,IAAI,EACJ,KAAK,EACL,SAAS,CACV;IACDC,YAAY,EAAE,IAAI;IAClBxB,QAAQ,EAAEX,QAAQ;IAClBY,OAAO,EAAE,aAAa;IACtBI,QAAQ,EAAE,CACRV,MAAM,EACNmB,MAAM,EACN;MACE;MACAW,KAAK,EAAE,mBAAmB;MAC1BC,KAAK,EAAE;IACT,CAAC,EACD;MACE;MACA;MACAC,aAAa,EAAE,IAAI;MACnBxB,SAAS,EAAE;IACb,CAAC,EACD;MAAEuB,KAAK,EAAE,QAAQ;MAAED,KAAK,EAAE;IAAU,CAAC,EACrCrB,MAAM,EACNW,YAAY,EACZjC,IAAI,CAACuC,iBAAiB,EACtB;MACEK,KAAK,EAAE,CACL,OAAO,EAAE,KAAK,EACd1C,QAAQ,CACT;MACDyC,KAAK,EAAE;QACL,CAAC,EAAE,SAAS;QACZ,CAAC,EAAE;MACL,CAAC;MACDpB,QAAQ,EAAE,CAAEY,MAAM;IACpB,CAAC,EACD;MACEV,QAAQ,EAAE,CACR;QACEmB,KAAK,EAAE,CACL,SAAS,EAAE,KAAK,EAChB1C,QAAQ,EAAE,KAAK,EACf,OAAO,EAAEA,QAAQ,EAAC,OAAO;MAE7B,CAAC,EACD;QACE0C,KAAK,EAAE,CACL,SAAS,EAAE,KAAK,EAChB1C,QAAQ;MAEZ,CAAC,CACF;MACDyC,KAAK,EAAE;QACL,CAAC,EAAE,SAAS;QACZ,CAAC,EAAE,aAAa;QAChB,CAAC,EAAE;MACL;IACF,CAAC,EACD;MACE7B,SAAS,EAAE,MAAM;MACjBC,KAAK,EAAE,UAAU;MACjBE,GAAG,EAAE,SAAS;MACdM,QAAQ,EAAE,CACRS,MAAM,EACNG,MAAM,EACNb,MAAM;IAEV,CAAC;EAEL,CAAC;AACH;AAEA,SAASvB,MAAM,IAAI+C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}