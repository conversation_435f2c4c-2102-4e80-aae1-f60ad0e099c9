{"ast": null, "code": "import { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"preview-video\"\n};\nvar _hoisted_2 = {\n  class: \"preview-video-body\",\n  controlslist: \"nodownload\",\n  ref: \"videoRef\",\n  controls: \"\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"video\", _hoisted_2, null, 512 /* NEED_PATCH */)]);\n}", "map": {"version": 3, "names": ["class", "controlslist", "ref", "controls", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\main\\src\\components\\global-file-preview\\components\\preview-video.vue"], "sourcesContent": ["<template>\r\n  <div class=\"preview-video\">\r\n    <video class=\"preview-video-body\" controlslist=\"nodownload\" ref=\"videoRef\" controls></video>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'PreviewVideo' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport flvjs from 'flv.js'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst videoRef = ref()\r\nconst flvPlayer = ref()\r\nconst fileUrl = ref('')\r\nonMounted(() => { globalDownload() })\r\n\r\nconst globalDownload = async () => {\r\n  const res = await api.globalDownload(props.id)\r\n  fileUrl.value = URL.createObjectURL(res)\r\n  createdPlay()\r\n}\r\n// 检测浏览器是否支持 flv.js\r\nconst createdPlay = () => {\r\n  if (flvjs.isSupported()) {\r\n    // 创建一个播放器实例\r\n    flvPlayer.value = flvjs.createPlayer({\r\n      type: 'mp4', // 媒体类型，默认是 flv\r\n      isLive: true, // 是否是直播流\r\n      hasAudio: true, // 是否有音频\r\n      hanVideo: true, // 是否有视频\r\n      url: fileUrl.value\r\n    }, {\r\n      autoCleanupMinBackwardDuration: true, // 清除缓存 对 SourceBuffer 进行自动清理\r\n    })\r\n    flvPlayer.value.attachMediaElement(videoRef.value)\r\n    console.log(flvPlayer.value)\r\n    flvPlayer.value.load()\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.preview-video {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  .preview-video-body {\r\n    width: 691px;\r\n    height: 388px;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAe;;EACjBA,KAAK,EAAC,oBAAoB;EAACC,YAAY,EAAC,YAAY;EAACC,GAAG,EAAC,UAAU;EAACC,QAAQ,EAAR;;;uBAD7EC,mBAAA,CAEM,OAFNC,UAEM,GADJC,mBAAA,CAA4F,SAA5FC,UAA4F,8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}