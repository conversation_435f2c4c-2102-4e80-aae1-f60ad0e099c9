{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, createElementBlock as _createElementBlock, normalizeClass as _normalizeClass, createTextVNode as _createTextVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalChatGroupList forbidSelect\"\n};\nvar _hoisted_2 = {\n  class: \"GlobalChatGroupInput\"\n};\nvar _hoisted_3 = {\n  class: \"GlobalChatGroupItem forbidSelect\"\n};\nvar _hoisted_4 = {\n  class: \"GlobalChatGroupName ellipsis\"\n};\nvar _hoisted_5 = {\n  class: \"GlobalChatGroupScroll\"\n};\nvar _hoisted_6 = [\"onClick\"];\nvar _hoisted_7 = {\n  class: \"GlobalChatGroupName ellipsis\"\n};\nvar _hoisted_8 = {\n  key: 0,\n  class: \"GlobalChatGroupBody\"\n};\nvar _hoisted_9 = {\n  class: \"GlobalChatGroupInfo\"\n};\nvar _hoisted_10 = {\n  class: \"GlobalChatGroupName ellipsis\"\n};\nvar _hoisted_11 = {\n  class: \"GlobalChatGroupUserBody\"\n};\nvar _hoisted_12 = {\n  key: 0,\n  class: \"GlobalChatGroupUserLogo forbidSelect\"\n};\nvar _hoisted_13 = {\n  class: \"GlobalChatGroupUserName ellipsis\"\n};\nvar _hoisted_14 = {\n  key: 1,\n  class: \"GlobalChatGroupDrag\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_autocomplete = _resolveComponent(\"el-autocomplete\");\n  var _component_el_empty = _resolveComponent(\"el-empty\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: _normalizeClass([\"GlobalChatGroup\", {\n      GlobalChatMacGroup: $setup.isMac\n    }])\n  }, [_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_autocomplete, {\n    modelValue: $setup.keyword,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.keyword = $event;\n    }),\n    \"prefix-icon\": $setup.Search,\n    \"fetch-suggestions\": $setup.querySearch,\n    placeholder: \"搜索\",\n    \"popper-class\": \"GlobalChatGroupAutocomplete\",\n    clearable: \"\",\n    onSelect: $setup.handleClick\n  }, {\n    default: _withCtx(function (_ref) {\n      var item = _ref.item;\n      return [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_image, {\n        src: $setup.imgUrl(item.groupImg),\n        fit: \"cover\",\n        draggable: \"false\"\n      }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_4, _toDisplayString(item.groupName), 1 /* TEXT */)])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"prefix-icon\"])]), _createVNode(_component_el_scrollbar, {\n    ref: \"scrollRef\",\n    class: \"GlobalChatGroupScrollbar\",\n    onScroll: $setup.handleScroll\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_5, [!$setup.tableData.length ? (_openBlock(), _createBlock(_component_el_empty, {\n        key: 0,\n        \"image-size\": 120,\n        description: \"暂无数据\"\n      })) : _createCommentVNode(\"v-if\", true), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.tableData, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: _normalizeClass(['GlobalChatGroupItem', {\n            'is-active': item.id === $setup.groupId\n          }]),\n          key: item.id,\n          onClick: function onClick($event) {\n            return $setup.handleClick(item);\n          }\n        }, [_createVNode(_component_el_image, {\n          src: $setup.imgUrl(item.groupImg),\n          fit: \"cover\",\n          draggable: \"false\"\n        }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_7, _toDisplayString(item.groupName), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_6);\n      }), 128 /* KEYED_FRAGMENT */))])];\n    }),\n    _: 1 /* STABLE */\n  }, 512 /* NEED_PATCH */)]), $setup.groupId ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_createElementVNode(\"div\", _hoisted_9, [_createVNode(_component_el_image, {\n    src: $setup.imgUrl($setup.groupInfo.groupImg),\n    fit: \"cover\",\n    draggable: \"false\"\n  }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_10, _toDisplayString($setup.groupInfo.groupName), 1 /* TEXT */), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleCreateGroup\n  }, {\n    default: _withCtx(function () {\n      return _cache[1] || (_cache[1] = [_createTextVNode(\"发送消息\")]);\n    }),\n    _: 1 /* STABLE */\n  })]), _createVNode(_component_el_scrollbar, {\n    class: \"GlobalChatGroupUserScroll\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_11, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.groupUser, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"GlobalChatGroupUser\",\n          key: item.id\n        }, [item.isOwner ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, \"群主\")) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_image, {\n          src: $setup.imgUrl(item.photo || item.headImg),\n          fit: \"cover\",\n          draggable: \"false\"\n        }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_13, _toDisplayString(item.userName), 1 /* TEXT */)]);\n      }), 128 /* KEYED_FRAGMENT */))])];\n    }),\n    _: 1 /* STABLE */\n  })])) : _createCommentVNode(\"v-if\", true), !$setup.groupId ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14)) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_normalizeClass", "GlobalChatMacGroup", "$setup", "isMac", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_createVNode", "_component_el_autocomplete", "modelValue", "keyword", "_cache", "$event", "Search", "querySearch", "placeholder", "clearable", "onSelect", "handleClick", "default", "_withCtx", "_ref", "item", "_hoisted_3", "_component_el_image", "src", "imgUrl", "groupImg", "fit", "draggable", "_hoisted_4", "_toDisplayString", "groupName", "_", "_component_el_scrollbar", "ref", "onScroll", "handleScroll", "_hoisted_5", "tableData", "length", "_createBlock", "_component_el_empty", "description", "_createCommentVNode", "_Fragment", "_renderList", "id", "groupId", "onClick", "_hoisted_7", "_hoisted_6", "_hoisted_8", "_hoisted_9", "groupInfo", "_hoisted_10", "_component_el_button", "type", "handleCreateGroup", "_createTextVNode", "_hoisted_11", "groupUser", "isOwner", "_hoisted_12", "photo", "headImg", "_hoisted_13", "userName", "_hoisted_14"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalChat\\components\\GlobalChatGroup.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalChatGroup\" :class=\"{ GlobalChatMacGroup: isMac }\">\r\n    <div class=\"GlobalChatGroupList forbidSelect\">\r\n      <div class=\"GlobalChatGroupInput\">\r\n        <el-autocomplete\r\n          v-model=\"keyword\"\r\n          :prefix-icon=\"Search\"\r\n          :fetch-suggestions=\"querySearch\"\r\n          placeholder=\"搜索\"\r\n          popper-class=\"GlobalChatGroupAutocomplete\"\r\n          clearable\r\n          @select=\"handleClick\">\r\n          <template #default=\"{ item }\">\r\n            <div class=\"GlobalChatGroupItem forbidSelect\">\r\n              <el-image :src=\"imgUrl(item.groupImg)\" fit=\"cover\" draggable=\"false\" />\r\n              <div class=\"GlobalChatGroupName ellipsis\">{{ item.groupName }}</div>\r\n            </div>\r\n          </template>\r\n        </el-autocomplete>\r\n      </div>\r\n      <el-scrollbar ref=\"scrollRef\" class=\"GlobalChatGroupScrollbar\" @scroll=\"handleScroll\">\r\n        <div class=\"GlobalChatGroupScroll\">\r\n          <el-empty v-if=\"!tableData.length\" :image-size=\"120\" description=\"暂无数据\" />\r\n          <div\r\n            :class=\"['GlobalChatGroupItem', { 'is-active': item.id === groupId }]\"\r\n            v-for=\"item in tableData\"\r\n            :key=\"item.id\"\r\n            @click=\"handleClick(item)\">\r\n            <el-image :src=\"imgUrl(item.groupImg)\" fit=\"cover\" draggable=\"false\" />\r\n            <div class=\"GlobalChatGroupName ellipsis\">{{ item.groupName }}</div>\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n    </div>\r\n    <div class=\"GlobalChatGroupBody\" v-if=\"groupId\">\r\n      <div class=\"GlobalChatGroupInfo\">\r\n        <el-image :src=\"imgUrl(groupInfo.groupImg)\" fit=\"cover\" draggable=\"false\" />\r\n        <div class=\"GlobalChatGroupName ellipsis\">{{ groupInfo.groupName }}</div>\r\n        <el-button type=\"primary\" @click=\"handleCreateGroup\">发送消息</el-button>\r\n      </div>\r\n      <el-scrollbar class=\"GlobalChatGroupUserScroll\">\r\n        <div class=\"GlobalChatGroupUserBody\">\r\n          <div class=\"GlobalChatGroupUser\" v-for=\"item in groupUser\" :key=\"item.id\">\r\n            <div class=\"GlobalChatGroupUserLogo forbidSelect\" v-if=\"item.isOwner\">群主</div>\r\n            <el-image :src=\"imgUrl(item.photo || item.headImg)\" fit=\"cover\" draggable=\"false\" />\r\n            <div class=\"GlobalChatGroupUserName ellipsis\">{{ item.userName }}</div>\r\n          </div>\r\n        </div>\r\n      </el-scrollbar>\r\n    </div>\r\n    <div class=\"GlobalChatGroupDrag\" v-if=\"!groupId\"></div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalChatGroup' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onMounted } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport * as RongIMLib from '@rongcloud/imlib-next'\r\nimport { appOnlyHeader } from 'common/js/system_var.js'\r\nimport { chatGroupMemberList } from '../js/ChatMethod.js'\r\nimport { Search } from '@element-plus/icons-vue'\r\nconst store = useStore()\r\nconst emit = defineEmits(['send'])\r\nconst rongCloudUrl = computed(() => store.getters.getRongCloudUrl)\r\nconst isPrivatization = computed(() => store.getters.getIsPrivatization)\r\nconst isMac = window.electron?.isMac\r\nconst scrollRef = ref()\r\nconst loadingScroll = ref(false)\r\nconst keyword = ref('')\r\nconst pageNo = ref(1)\r\nconst pageSize = ref(20)\r\nconst totals = ref(0)\r\nconst isShow = ref(false)\r\nconst loading = ref(true)\r\nconst tableData = ref([])\r\nconst groupId = ref('')\r\nconst groupInfo = ref({})\r\nconst groupUser = ref([])\r\nconst imgUrl = (url) => (url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg'))\r\nconst handleScroll = ({ scrollTop }) => {\r\n  if (!scrollRef.value) return\r\n  const { scrollHeight, clientHeight } = scrollRef.value.wrapRef\r\n  if (scrollHeight - scrollTop <= clientHeight + 50 && !loadingScroll.value) {\r\n    load()\r\n  }\r\n}\r\nconst load = () => {\r\n  if (pageNo.value * pageSize.value >= totals.value) return\r\n  loadingScroll.value = true\r\n  pageNo.value += 1\r\n  chatGroupList()\r\n}\r\nconst chatGroupList = async () => {\r\n  const { data, total } = await api.chatGroupList({\r\n    isMine: 1,\r\n    keyword: '',\r\n    pageNo: pageNo.value,\r\n    pageSize: pageSize.value\r\n  })\r\n  tableData.value = [...tableData.value, ...(data || [])]\r\n  totals.value = total\r\n  loading.value = pageNo.value * pageSize.value < totals.value\r\n  isShow.value = pageNo.value * pageSize.value >= totals.value\r\n  loadingScroll.value = false\r\n}\r\nconst handleRefresh = async () => {\r\n  const { data, total } = await api.chatGroupList({\r\n    isMine: 1,\r\n    keyword: '',\r\n    pageNo: 1,\r\n    pageSize: tableData.value.length\r\n  })\r\n  tableData.value = data\r\n  totals.value = total\r\n  loading.value = pageNo.value * pageSize.value < totals.value\r\n  isShow.value = pageNo.value * pageSize.value >= totals.value\r\n  loadingScroll.value = false\r\n}\r\nconst querySearch = async (queryString, cb) => {\r\n  const results = queryString ? await handleUserData(queryString) : []\r\n  cb(results)\r\n}\r\nconst handleUserData = async () => {\r\n  const { data } = await api.chatGroupList({ isMine: 1, keyword: keyword.value, pageNo: 1, pageSize: 999 })\r\n  return data\r\n}\r\nconst handleClick = async (item) => {\r\n  groupId.value = item.id\r\n  chatGroupInfo()\r\n  groupUser.value = await chatGroupMemberList(groupId.value)\r\n}\r\nconst chatGroupInfo = async () => {\r\n  const { data } = await api.chatGroupInfo({ detailId: groupId.value })\r\n  groupInfo.value = data\r\n}\r\nconst handleMessages = async (conversationType, targetId) => {\r\n  const res = await RongIMLib.getConversation({ conversationType, targetId })\r\n  return res\r\n}\r\nconst handleCreateGroup = async () => {\r\n  const { code } = await api.rongCloud(\r\n    rongCloudUrl.value,\r\n    {\r\n      type: 'createGroup',\r\n      userIds: groupInfo.value?.memberUserIds?.map((v) => `${appOnlyHeader.value}${v}`).join(','),\r\n      groupId: `${appOnlyHeader.value}${groupInfo.value?.id}`,\r\n      groupName: groupInfo.value?.groupName,\r\n      environment: 1\r\n    },\r\n    isPrivatization.value\r\n  )\r\n  if (code === 200) handleSendMessage()\r\n}\r\nconst handleSendMessage = async () => {\r\n  const targetId = appOnlyHeader.value + groupInfo.value.id\r\n  const { code, data } = await handleMessages(3, targetId)\r\n  if (!code) {\r\n    let newSendMessage = {\r\n      isTemporary: true,\r\n      isTop: data.isTop,\r\n      isNotInform: data.notificationStatus,\r\n      id: data.targetId,\r\n      targetId: data.targetId,\r\n      type: data.conversationType,\r\n      chatObjectInfo: {\r\n        uid: data.targetId,\r\n        id: groupInfo.value.id,\r\n        name: groupInfo.value.groupName,\r\n        img: groupInfo.value.groupImg,\r\n        userIdData: groupInfo.value.memberUserIds,\r\n        chatGroupType:\r\n          groupInfo.value?.chatGroupType?.value !== '0' ? groupInfo.value?.chatGroupType?.name?.slice(0, 2) : ''\r\n      },\r\n      sentTime: data.latestMessage?.sentTime || Date.parse(new Date()),\r\n      messageType: data.latestMessage?.messageType || 'RC:TxtMsg',\r\n      content: data.latestMessage?.content || { content: '' },\r\n      count: data.unreadMessageCount\r\n    }\r\n    emit('send', newSendMessage)\r\n  }\r\n}\r\nonMounted(() => {\r\n  pageNo.value = 1\r\n  tableData.value = []\r\n  totals.value = 0\r\n  isShow.value = false\r\n  loading.value = true\r\n  chatGroupList()\r\n})\r\ndefineExpose({ refresh: handleRefresh })\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalChatGroup {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n\r\n  &.GlobalChatMacGroup {\r\n    .GlobalChatGroupList {\r\n      .GlobalChatGroupInput {\r\n        height: 56px;\r\n      }\r\n\r\n      .GlobalChatGroupScrollbar {\r\n        height: calc(100% - 56px);\r\n      }\r\n    }\r\n\r\n    .GlobalChatGroupDrag {\r\n      height: 56px;\r\n    }\r\n  }\r\n\r\n  .GlobalChatGroupList {\r\n    width: 280px;\r\n    height: 100%;\r\n    border-right: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n    .GlobalChatGroupInput {\r\n      width: 100%;\r\n      height: 66px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 6px 20px 0 20px;\r\n      -webkit-app-region: drag;\r\n\r\n      .zy-el-autocomplete {\r\n        width: 240px;\r\n        height: var(--zy-height-routine);\r\n        -webkit-app-region: no-drag;\r\n\r\n        .zy-el-input {\r\n          width: 240px;\r\n          height: var(--zy-height-routine);\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalChatGroupScrollbar {\r\n      width: 100%;\r\n      height: calc(100% - 66px);\r\n    }\r\n\r\n    .GlobalChatGroupScroll {\r\n      width: 100%;\r\n      height: 100%;\r\n      overflow: hidden;\r\n\r\n      .GlobalChatGroupItem {\r\n        width: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        position: relative;\r\n        padding: 10px 20px;\r\n\r\n        &:hover {\r\n          background: #f9f9fa;\r\n        }\r\n\r\n        &.is-active {\r\n          background: #f8f8f8;\r\n        }\r\n\r\n        .zy-el-image {\r\n          width: 42px;\r\n          height: 42px;\r\n          border-radius: 50%;\r\n          overflow: hidden;\r\n        }\r\n\r\n        .GlobalChatGroupName {\r\n          width: calc(100% - 56px);\r\n          font-size: 14px;\r\n\r\n          &::after {\r\n            content: '';\r\n            width: calc(100% - 96px);\r\n            border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n            position: absolute;\r\n            right: 20px;\r\n            bottom: 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalChatGroupDrag {\r\n    width: calc(100% - 280px);\r\n    height: 66px;\r\n    position: relative;\r\n    -webkit-app-region: drag;\r\n\r\n    &::before {\r\n      content: '';\r\n      width: 96px;\r\n      height: 28px;\r\n      position: absolute;\r\n      top: 0;\r\n      right: 0;\r\n      background: transparent;\r\n      -webkit-app-region: no-drag;\r\n    }\r\n  }\r\n\r\n  .GlobalChatGroupBody {\r\n    width: calc(100% - 280px);\r\n    height: 100%;\r\n\r\n    .GlobalChatGroupInfo {\r\n      width: 100%;\r\n      height: 180px;\r\n      display: flex;\r\n      align-items: center;\r\n      flex-direction: column;\r\n      justify-content: center;\r\n      position: relative;\r\n      border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n      -webkit-app-region: drag;\r\n\r\n      &::before {\r\n        content: '';\r\n        width: 96px;\r\n        height: 28px;\r\n        position: absolute;\r\n        top: 0;\r\n        right: 0;\r\n        background: transparent;\r\n        -webkit-app-region: no-drag;\r\n      }\r\n\r\n      .zy-el-image {\r\n        width: 68px;\r\n        height: 68px;\r\n        border-radius: 8%;\r\n        overflow: hidden;\r\n        -webkit-app-region: no-drag;\r\n      }\r\n\r\n      .GlobalChatGroupName {\r\n        width: 100%;\r\n        font-size: 18px;\r\n        text-align: center;\r\n        padding-top: 12px;\r\n        -webkit-app-region: no-drag;\r\n      }\r\n\r\n      .zy-el-button {\r\n        position: absolute;\r\n        right: var(--zy-distance-two);\r\n        bottom: 12px;\r\n        height: var(--zy-height-secondary);\r\n        -webkit-app-region: no-drag;\r\n      }\r\n    }\r\n\r\n    .GlobalChatGroupUserScroll {\r\n      width: 100%;\r\n      height: calc(100% - 180px);\r\n    }\r\n\r\n    .GlobalChatGroupUserBody {\r\n      width: 100%;\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      align-items: center;\r\n      padding: 0 20px;\r\n\r\n      .GlobalChatGroupUser {\r\n        width: 20%;\r\n        display: flex;\r\n        align-items: center;\r\n        flex-direction: column;\r\n        padding: 20px 0;\r\n        position: relative;\r\n\r\n        .GlobalChatGroupUserLogo {\r\n          position: absolute;\r\n          top: 20px;\r\n          left: 50%;\r\n          transform: translate(12px, -50%);\r\n          padding: 0px 4px;\r\n          background: var(--zy-el-color-primary);\r\n          border-radius: 4px;\r\n          font-size: 12px;\r\n          color: #fff;\r\n          z-index: 2;\r\n        }\r\n\r\n        .zy-el-image {\r\n          width: 52px;\r\n          height: 52px;\r\n          border-radius: 50%;\r\n          overflow: hidden;\r\n        }\r\n\r\n        .GlobalChatGroupUserName {\r\n          width: 100%;\r\n          font-size: 14px;\r\n          text-align: center;\r\n          padding-top: 6px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.GlobalChatGroupAutocomplete {\r\n  .GlobalChatGroupItem {\r\n    width: 218px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    position: relative;\r\n    padding: 10px 0;\r\n\r\n    &:hover {\r\n      background: #f9f9fa;\r\n    }\r\n\r\n    &.is-active {\r\n      background: #f8f8f8;\r\n    }\r\n\r\n    .zy-el-image {\r\n      width: 42px;\r\n      height: 42px;\r\n      border-radius: 50%;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .GlobalChatGroupName {\r\n      width: calc(100% - 56px);\r\n      font-size: 14px;\r\n      line-height: normal;\r\n\r\n      &::after {\r\n        content: '';\r\n        width: calc(100% - 56px);\r\n        border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n        position: absolute;\r\n        right: 0;\r\n        bottom: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAESA,KAAK,EAAC;AAAkC;;EACtCA,KAAK,EAAC;AAAsB;;EAUtBA,KAAK,EAAC;AAAkC;;EAEtCA,KAAK,EAAC;AAA8B;;EAM1CA,KAAK,EAAC;AAAuB;iBArB1C;;EA6BiBA,KAAK,EAAC;AAA8B;;EA7BrDC,GAAA;EAkCSD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAqB;;EAEzBA,KAAK,EAAC;AAA8B;;EAIpCA,KAAK,EAAC;AAAyB;;EAzC5CC,GAAA;EA2CiBD,KAAK,EAAC;;;EAENA,KAAK,EAAC;AAAkC;;EA7CzDC,GAAA;EAkDSD,KAAK,EAAC;;;;;;;;uBAjDbE,mBAAA,CAkDM;IAlDDF,KAAK,EADZG,eAAA,EACa,iBAAiB;MAAAC,kBAAA,EAA+BC,MAAA,CAAAC;IAAK;MAC9DC,mBAAA,CA+BM,OA/BNC,UA+BM,GA9BJD,mBAAA,CAgBM,OAhBNE,UAgBM,GAfJC,YAAA,CAckBC,0BAAA;IAlB1BC,UAAA,EAKmBP,MAAA,CAAAQ,OAAO;IAL1B,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAKmBV,MAAA,CAAAQ,OAAO,GAAAE,MAAA;IAAA;IACf,aAAW,EAAEV,MAAA,CAAAW,MAAM;IACnB,mBAAiB,EAAEX,MAAA,CAAAY,WAAW;IAC/BC,WAAW,EAAC,IAAI;IAChB,cAAY,EAAC,6BAA6B;IAC1CC,SAAS,EAAT,EAAS;IACRC,QAAM,EAAEf,MAAA,CAAAgB;;IACEC,OAAO,EAAAC,QAAA,CAChB,UAAAC,IAAA;MAAA,IADoBC,IAAI,GAAAD,IAAA,CAAJC,IAAI;MAAA,QACxBlB,mBAAA,CAGM,OAHNmB,UAGM,GAFJhB,YAAA,CAAuEiB,mBAAA;QAA5DC,GAAG,EAAEvB,MAAA,CAAAwB,MAAM,CAACJ,IAAI,CAACK,QAAQ;QAAGC,GAAG,EAAC,OAAO;QAACC,SAAS,EAAC;wCAC7DzB,mBAAA,CAAoE,OAApE0B,UAAoE,EAAAC,gBAAA,CAAvBT,IAAI,CAACU,SAAS,iB;;IAfzEC,CAAA;sDAoBM1B,YAAA,CAYe2B,uBAAA;IAZDC,GAAG,EAAC,WAAW;IAACtC,KAAK,EAAC,0BAA0B;IAAEuC,QAAM,EAAElC,MAAA,CAAAmC;;IApB9ElB,OAAA,EAAAC,QAAA,CAqBQ;MAAA,OAUM,CAVNhB,mBAAA,CAUM,OAVNkC,UAUM,G,CATapC,MAAA,CAAAqC,SAAS,CAACC,MAAM,I,cAAjCC,YAAA,CAA0EC,mBAAA;QAtBpF5C,GAAA;QAsB8C,YAAU,EAAE,GAAG;QAAE6C,WAAW,EAAC;YAtB3EC,mBAAA,iB,kBAuBU7C,mBAAA,CAOM8C,SAAA,QA9BhBC,WAAA,CAyB2B5C,MAAA,CAAAqC,SAAS,EAzBpC,UAyBmBjB,IAAI;6BAFbvB,mBAAA,CAOM;UANHF,KAAK,EAxBlBG,eAAA;YAAA,aAwB2DsB,IAAI,CAACyB,EAAE,KAAK7C,MAAA,CAAA8C;UAAO;UAEjElD,GAAG,EAAEwB,IAAI,CAACyB,EAAE;UACZE,OAAK,WAALA,OAAKA,CAAArC,MAAA;YAAA,OAAEV,MAAA,CAAAgB,WAAW,CAACI,IAAI;UAAA;YACxBf,YAAA,CAAuEiB,mBAAA;UAA5DC,GAAG,EAAEvB,MAAA,CAAAwB,MAAM,CAACJ,IAAI,CAACK,QAAQ;UAAGC,GAAG,EAAC,OAAO;UAACC,SAAS,EAAC;0CAC7DzB,mBAAA,CAAoE,OAApE8C,UAAoE,EAAAnB,gBAAA,CAAvBT,IAAI,CAACU,SAAS,iB,yBA7BvEmB,UAAA;;;IAAAlB,CAAA;8BAkC2C/B,MAAA,CAAA8C,OAAO,I,cAA9CjD,mBAAA,CAeM,OAfNqD,UAeM,GAdJhD,mBAAA,CAIM,OAJNiD,UAIM,GAHJ9C,YAAA,CAA4EiB,mBAAA;IAAjEC,GAAG,EAAEvB,MAAA,CAAAwB,MAAM,CAACxB,MAAA,CAAAoD,SAAS,CAAC3B,QAAQ;IAAGC,GAAG,EAAC,OAAO;IAACC,SAAS,EAAC;oCAClEzB,mBAAA,CAAyE,OAAzEmD,WAAyE,EAAAxB,gBAAA,CAA5B7B,MAAA,CAAAoD,SAAS,CAACtB,SAAS,kBAChEzB,YAAA,CAAqEiD,oBAAA;IAA1DC,IAAI,EAAC,SAAS;IAAER,OAAK,EAAE/C,MAAA,CAAAwD;;IAtC1CvC,OAAA,EAAAC,QAAA,CAsC6D;MAAA,OAAIT,MAAA,QAAAA,MAAA,OAtCjEgD,gBAAA,CAsC6D,MAAI,E;;IAtCjE1B,CAAA;QAwCM1B,YAAA,CAQe2B,uBAAA;IARDrC,KAAK,EAAC;EAA2B;IAxCrDsB,OAAA,EAAAC,QAAA,CAyCQ;MAAA,OAMM,CANNhB,mBAAA,CAMM,OANNwD,WAMM,I,kBALJ7D,mBAAA,CAIM8C,SAAA,QA9ChBC,WAAA,CA0C0D5C,MAAA,CAAA2D,SAAS,EA1CnE,UA0CkDvC,IAAI;6BAA5CvB,mBAAA,CAIM;UAJDF,KAAK,EAAC,qBAAqB;UAA4BC,GAAG,EAAEwB,IAAI,CAACyB;YACZzB,IAAI,CAACwC,OAAO,I,cAApE/D,mBAAA,CAA8E,OAA9EgE,WAA8E,EAAR,IAAE,KA3CpFnB,mBAAA,gBA4CYrC,YAAA,CAAoFiB,mBAAA;UAAzEC,GAAG,EAAEvB,MAAA,CAAAwB,MAAM,CAACJ,IAAI,CAAC0C,KAAK,IAAI1C,IAAI,CAAC2C,OAAO;UAAGrC,GAAG,EAAC,OAAO;UAACC,SAAS,EAAC;0CAC1EzB,mBAAA,CAAuE,OAAvE8D,WAAuE,EAAAnC,gBAAA,CAAtBT,IAAI,CAAC6C,QAAQ,iB;;;IA7C1ElC,CAAA;UAAAW,mBAAA,gB,CAkD4C1C,MAAA,CAAA8C,OAAO,I,cAA/CjD,mBAAA,CAAuD,OAAvDqE,WAAuD,KAlD3DxB,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}