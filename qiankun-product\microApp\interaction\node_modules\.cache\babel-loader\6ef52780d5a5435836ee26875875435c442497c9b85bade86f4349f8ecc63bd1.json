{"ast": null, "code": "import { ref, onActivated } from 'vue';\nimport { format } from 'common/js/time.js';\nimport { GlobalTable } from 'common/js/GlobalTable.js';\nimport { qiankunMicro } from 'common/config/MicroGlobal';\nimport LiveManagementNew from './LiveManagementNew';\nvar __default__ = {\n  name: 'LiveManagement'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var buttonList = [{\n      id: 'del',\n      name: '删除',\n      type: 'primary',\n      has: 'del'\n    }];\n    var tableButtonList = [\n    // { id: 'liveConsole', name: '直播控制台', width: 100, has: 'liveConsole', whetherShow: true },\n    {\n      id: 'edit',\n      name: '编辑',\n      width: 100,\n      has: 'edit',\n      whetherShow: true\n    }];\n    var id = ref();\n    var show = ref(false);\n    var _GlobalTable = GlobalTable({\n        tableApi: 'videoConnectionList',\n        delApi: 'videoConnectionDelLive',\n        tableDataObj: {\n          query: {\n            isLive: 1\n          }\n        }\n      }),\n      keyword = _GlobalTable.keyword,\n      tableRef = _GlobalTable.tableRef,\n      totals = _GlobalTable.totals,\n      pageNo = _GlobalTable.pageNo,\n      pageSize = _GlobalTable.pageSize,\n      pageSizes = _GlobalTable.pageSizes,\n      tableData = _GlobalTable.tableData,\n      handleQuery = _GlobalTable.handleQuery,\n      handleTableSelect = _GlobalTable.handleTableSelect,\n      handleDel = _GlobalTable.handleDel,\n      tableRefReset = _GlobalTable.tableRefReset;\n    onActivated(function () {\n      handleQuery();\n    });\n    var handleButton = function handleButton(isType) {\n      switch (isType) {\n        case 'del':\n          handleDel('直播');\n          break;\n        default:\n          break;\n      }\n    };\n    var handleCommand = function handleCommand(row, isType) {\n      switch (isType) {\n        case 'liveConsole':\n          console.log('点击了直播控制台');\n          break;\n        case 'edit':\n          console.log('点击了编辑');\n          handleEdit(row);\n          break;\n        default:\n          break;\n      }\n    };\n    var handleReset = function handleReset() {\n      keyword.value = '';\n      handleQuery();\n    };\n    var handleEdit = function handleEdit(row) {\n      id.value = row.id;\n      show.value = true;\n    };\n    var handleDetails = function handleDetails(item) {\n      qiankunMicro.setGlobalState({\n        openRoute: {\n          name: '视频会议详情',\n          path: '/interaction/VideoMeetinDetails',\n          query: {\n            id: item.id\n          }\n        }\n      });\n    };\n    var handleElWhetherShow = function handleElWhetherShow(row, isType) {\n      console.log('row===>', row);\n      console.log('isType===>', isType);\n      if (isType === 'xx') {\n        return true;\n      }\n      return true;\n    };\n    var callback = function callback() {\n      tableRefReset();\n      handleQuery();\n      show.value = false;\n    };\n    var __returned__ = {\n      buttonList,\n      tableButtonList,\n      id,\n      show,\n      keyword,\n      tableRef,\n      totals,\n      pageNo,\n      pageSize,\n      pageSizes,\n      tableData,\n      handleQuery,\n      handleTableSelect,\n      handleDel,\n      tableRefReset,\n      handleButton,\n      handleCommand,\n      handleReset,\n      handleEdit,\n      handleDetails,\n      handleElWhetherShow,\n      callback,\n      ref,\n      onActivated,\n      get format() {\n        return format;\n      },\n      get GlobalTable() {\n        return GlobalTable;\n      },\n      get qiankunMicro() {\n        return qiankunMicro;\n      },\n      get LiveManagementNew() {\n        return LiveManagementNew;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onActivated", "format", "GlobalTable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LiveManagementNew", "__default__", "name", "buttonList", "id", "type", "has", "tableButtonList", "width", "whetherShow", "show", "_GlobalTable", "tableApi", "del<PERSON><PERSON>", "tableDataObj", "query", "isLive", "keyword", "tableRef", "totals", "pageNo", "pageSize", "pageSizes", "tableData", "handleQuery", "handleTableSelect", "handleDel", "tableRefReset", "handleButton", "isType", "handleCommand", "row", "console", "log", "handleEdit", "handleReset", "value", "handleDetails", "item", "setGlobalState", "openRoute", "path", "handleElWhetherShow", "callback"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/interaction/src/views/LiveManagement/LiveManagement.vue"], "sourcesContent": ["<template>\r\n  <div class=\"LiveManagement\">\r\n    <xyl-search-button @queryClick=\"handleQuery\" @resetClick=\"handleReset\" @handleButton=\"handleButton\"\r\n      :buttonList=\"buttonList\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\" row-key=\"id\" :data=\"tableData\" @select=\"handleTableSelect\"\r\n        @select-all=\"handleTableSelect\">\r\n        <el-table-column type=\"selection\" reserve-selection width=\"60\" fixed />\r\n        <el-table-column label=\"直播标题\" min-width=\"200\" show-overflow-tooltip>\r\n          <template #default=\"scope\">\r\n            <el-link type=\"primary\" @click=\"handleDetails(scope.row)\">{{ scope.row.theme }}</el-link>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"直播计划开始时间\" width=\"200\">\r\n          <template #default=\"scope\">{{ format(scope.row.startTime) }}</template>\r\n        </el-table-column>\r\n        <el-table-column label=\"直播计划结束时间\" width=\"200\">\r\n          <template #default=\"scope\">{{ format(scope.row.endTime) }}</template>\r\n        </el-table-column>\r\n        <el-table-column label=\"直播状态\" width=\"140\" prop=\"meetingStatus\" />\r\n        <el-table-column label=\"直播互动\" width=\"140\" prop=\"\" />\r\n        <xyl-global-table-button :data=\"tableButtonList\" @buttonClick=\"handleCommand\"\r\n          :elWhetherShow=\"handleElWhetherShow\"></xyl-global-table-button>\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\" v-model:page-size=\"pageSize\" :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\" @size-change=\"handleQuery\" @current-change=\"handleQuery\"\r\n        :total=\"totals\" background />\r\n    </div>\r\n  </div>\r\n  <xyl-popup-window v-model=\"show\" :name=\"id ? '编辑' : '新增'\">\r\n    <LiveManagementNew :id=\"id\" @callback=\"callback\"></LiveManagementNew>\r\n  </xyl-popup-window>\r\n</template>\r\n<script>\r\nexport default { name: 'LiveManagement' }\r\n</script>\r\n<script setup>\r\nimport { ref, onActivated } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport LiveManagementNew from './LiveManagementNew'\r\nconst buttonList = [\r\n  { id: 'del', name: '删除', type: 'primary', has: 'del' }\r\n]\r\nconst tableButtonList = [\r\n  // { id: 'liveConsole', name: '直播控制台', width: 100, has: 'liveConsole', whetherShow: true },\r\n  { id: 'edit', name: '编辑', width: 100, has: 'edit', whetherShow: true }\r\n]\r\nconst id = ref()\r\nconst show = ref(false)\r\nconst {\r\n  keyword,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableData,\r\n  handleQuery,\r\n  handleTableSelect,\r\n  handleDel,\r\n  tableRefReset\r\n} = GlobalTable({ tableApi: 'videoConnectionList', delApi: 'videoConnectionDelLive', tableDataObj: { query: { isLive: 1 } } })\r\nonActivated(() => {\r\n  handleQuery()\r\n})\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'del':\r\n      handleDel('直播')\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleCommand = (row, isType) => {\r\n  switch (isType) {\r\n    case 'liveConsole':\r\n      console.log('点击了直播控制台')\r\n      break\r\n    case 'edit':\r\n      console.log('点击了编辑')\r\n      handleEdit(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\nconst handleEdit = (row) => {\r\n  id.value = row.id\r\n  show.value = true\r\n}\r\nconst handleDetails = (item) => {\r\n  qiankunMicro.setGlobalState({ openRoute: { name: '视频会议详情', path: '/interaction/VideoMeetinDetails', query: { id: item.id } } })\r\n}\r\nconst handleElWhetherShow = (row, isType) => {\r\n  console.log('row===>', row)\r\n  console.log('isType===>', isType)\r\n  if (isType === 'xx') {\r\n    return true\r\n  }\r\n  return true\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  handleQuery()\r\n  show.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.LiveManagement {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AA2CA,SAASA,GAAG,EAAEC,WAAW,QAAQ,KAAK;AACtC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,YAAY,QAAQ,2BAA2B;AACxD,OAAOC,iBAAiB,MAAM,qBAAqB;AAPnD,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAiB,CAAC;;;;;IAQzC,IAAMC,UAAU,GAAG,CACjB;MAAEC,EAAE,EAAE,KAAK;MAAEF,IAAI,EAAE,IAAI;MAAEG,IAAI,EAAE,SAAS;MAAEC,GAAG,EAAE;IAAM,CAAC,CACvD;IACD,IAAMC,eAAe,GAAG;IACtB;IACA;MAAEH,EAAE,EAAE,MAAM;MAAEF,IAAI,EAAE,IAAI;MAAEM,KAAK,EAAE,GAAG;MAAEF,GAAG,EAAE,MAAM;MAAEG,WAAW,EAAE;IAAK,CAAC,CACvE;IACD,IAAML,EAAE,GAAGT,GAAG,CAAC,CAAC;IAChB,IAAMe,IAAI,GAAGf,GAAG,CAAC,KAAK,CAAC;IACvB,IAAAgB,YAAA,GAYIb,WAAW,CAAC;QAAEc,QAAQ,EAAE,qBAAqB;QAAEC,MAAM,EAAE,wBAAwB;QAAEC,YAAY,EAAE;UAAEC,KAAK,EAAE;YAAEC,MAAM,EAAE;UAAE;QAAE;MAAE,CAAC,CAAC;MAX5HC,OAAO,GAAAN,YAAA,CAAPM,OAAO;MACPC,QAAQ,GAAAP,YAAA,CAARO,QAAQ;MACRC,MAAM,GAAAR,YAAA,CAANQ,MAAM;MACNC,MAAM,GAAAT,YAAA,CAANS,MAAM;MACNC,QAAQ,GAAAV,YAAA,CAARU,QAAQ;MACRC,SAAS,GAAAX,YAAA,CAATW,SAAS;MACTC,SAAS,GAAAZ,YAAA,CAATY,SAAS;MACTC,WAAW,GAAAb,YAAA,CAAXa,WAAW;MACXC,iBAAiB,GAAAd,YAAA,CAAjBc,iBAAiB;MACjBC,SAAS,GAAAf,YAAA,CAATe,SAAS;MACTC,aAAa,GAAAhB,YAAA,CAAbgB,aAAa;IAEf/B,WAAW,CAAC,YAAM;MAChB4B,WAAW,CAAC,CAAC;IACf,CAAC,CAAC;IACF,IAAMI,YAAY,GAAG,SAAfA,YAAYA,CAAIC,MAAM,EAAK;MAC/B,QAAQA,MAAM;QACZ,KAAK,KAAK;UACRH,SAAS,CAAC,IAAI,CAAC;UACf;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMI,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,GAAG,EAAEF,MAAM,EAAK;MACrC,QAAQA,MAAM;QACZ,KAAK,aAAa;UAChBG,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;UACvB;QACF,KAAK,MAAM;UACTD,OAAO,CAACC,GAAG,CAAC,OAAO,CAAC;UACpBC,UAAU,CAACH,GAAG,CAAC;UACf;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMI,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBlB,OAAO,CAACmB,KAAK,GAAG,EAAE;MAClBZ,WAAW,CAAC,CAAC;IACf,CAAC;IACD,IAAMU,UAAU,GAAG,SAAbA,UAAUA,CAAIH,GAAG,EAAK;MAC1B3B,EAAE,CAACgC,KAAK,GAAGL,GAAG,CAAC3B,EAAE;MACjBM,IAAI,CAAC0B,KAAK,GAAG,IAAI;IACnB,CAAC;IACD,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI,EAAK;MAC9BvC,YAAY,CAACwC,cAAc,CAAC;QAAEC,SAAS,EAAE;UAAEtC,IAAI,EAAE,QAAQ;UAAEuC,IAAI,EAAE,iCAAiC;UAAE1B,KAAK,EAAE;YAAEX,EAAE,EAAEkC,IAAI,CAAClC;UAAG;QAAE;MAAE,CAAC,CAAC;IACjI,CAAC;IACD,IAAMsC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIX,GAAG,EAAEF,MAAM,EAAK;MAC3CG,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEF,GAAG,CAAC;MAC3BC,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEJ,MAAM,CAAC;MACjC,IAAIA,MAAM,KAAK,IAAI,EAAE;QACnB,OAAO,IAAI;MACb;MACA,OAAO,IAAI;IACb,CAAC;IACD,IAAMc,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrBhB,aAAa,CAAC,CAAC;MACfH,WAAW,CAAC,CAAC;MACbd,IAAI,CAAC0B,KAAK,GAAG,KAAK;IACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}