{"ast": null, "code": "function _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport { ref, watch, onMounted, computed, nextTick, provide, onUnmounted } from 'vue';\nimport api from '@/api';\nimport { useStore } from 'vuex';\nimport config from 'common/config';\nimport { qiankunActions, loadFilterApp } from '@/qiankun';\nimport { handleCompareVersion } from 'common/js/CheckVersion';\nimport { globalReadOpenConfig } from 'common/js/GlobalMethod';\nimport { get_font_family, change_font_family } from 'common/js/utils';\nimport elementResizeDetectorMaker from 'element-resize-detector';\nimport unauthorized from 'common/components/unauthorized';\nimport { ElMessage, ElMessageBox } from 'element-plus';\nexport var refreshIcon = `<svg t=\"1743990581190\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"52145\" width=\"30\" height=\"30\"><path d=\"M512 128a384 384 0 1 1 0 768A384 384 0 0 1 512 128z m0 48.192C335.296 176.192 174.08 335.36 174.08 512c0 176.704 161.216 336.512 337.92 336.512S849.152 688.704 849.152 512 688.704 176.192 512 176.192z\" fill=\"#ffffff\" p-id=\"52146\"></path><path d=\"M244.352 506.688h73.6c0-123.2 86.016-220.032 196.48-215.68 36.8 0 69.568 13.248 102.272 30.848l-36.8 39.68c-20.48-8.96-45.056-17.664-69.568-17.664-77.76 0-143.232 70.4-143.232 162.816h73.6l-98.176 110.08-98.176-110.08zM705.088 515.84c0 117.248-86.848 217.28-194.368 217.28-37.248 0-70.4-17.344-103.424-34.752l37.184-39.04c20.736 8.704 45.568 17.344 70.4 17.344 78.592 0 144.768-69.504 144.768-160.768H581.056l99.328-108.608 99.2 108.544h-74.496z\" fill=\"#ffffff\" p-id=\"52147\"></path></svg>`;\nexport var LayoutView = function LayoutView(route, router) {\n  var store = useStore();\n  var menuIcon = `${config.API_URL}/pageImg/open/menuIcon`;\n  var erd = elementResizeDetectorMaker();\n  var left = ref(0);\n  var width = ref('');\n  var LayoutViewBox = ref(null);\n  var LayoutViewInfo = ref(null);\n  var editPassWordShow = ref(false);\n  var verifyEditPassWord = ref('');\n  var verifyEditPassWordShow = ref(false);\n  var MicroApp = ref([]);\n  var MicroAppObj = ref({});\n  // 用户信息\n  var user = computed(function () {\n    return store.getters.getUserFn;\n  });\n  // 地区信息\n  var area = computed(function () {\n    return store.getters.getAreaFn;\n  });\n  // 角色信息\n  var role = computed(function () {\n    return store.getters.getRoleFn;\n  });\n  var openConfig = computed(function () {\n    return store.getters.getReadOpenConfig;\n  });\n  var helpShow = ref(false);\n  onMounted(function () {\n    handleRegionSelect();\n    nextTick(function () {\n      erd.listenTo(LayoutViewBox.value, function (element) {\n        left.value = element.offsetWidth;\n      });\n      erd.listenTo(LayoutViewInfo.value, function (element) {\n        width.value = `width: calc(100% - ${element.offsetWidth + 16}px);`;\n      });\n    });\n  });\n  var handleRegionSelect = function handleRegionSelect() {\n    var _user$value, _user$value2;\n    var oldRegionInfo = sessionStorage.getItem('oldRegionInfo') || '';\n    var isRegionSelect = sessionStorage.getItem('isRegionSelect') || '';\n    if (((_user$value = user.value) === null || _user$value === void 0 ? void 0 : _user$value.accountId) !== '1' && ((_user$value2 = user.value) === null || _user$value2 === void 0 ? void 0 : _user$value2.areaTotal) > 1 && oldRegionInfo && !isRegionSelect) {\n      isRegionSelectShow.value = true;\n    }\n  };\n  var handleCommand = function handleCommand(type) {\n    if (type === 'task') {\n      store.commit('setGlobalCentralControlObj', {\n        show: true\n      });\n    } else if (type === 'refresh') {\n      // window.location.reload(true)\n      // window.location.reload(window.location.href)\n      window.location.href = `${config.mainPath}?v=${new Date().getTime()}`;\n    } else if (type === 'locale') {\n      if (get_font_family() === 'cn') return change_font_family('tw');\n      if (get_font_family() === 'tw') return change_font_family('cn');\n    } else if (type === 'help') {\n      helpShow.value = true;\n    } else if (type === 'edit_password') {\n      verifyEditPassWord.value = '';\n      editPassWordShow.value = true;\n    } else if (type === 'exit') {\n      handleExit();\n    } else {\n      ElMessage({\n        type: 'info',\n        message: '正在开发中！'\n      });\n    }\n  };\n  var editPassWordCallback = function editPassWordCallback(type) {\n    if (type) {\n      loginOut('请使用新密码重新登录！');\n    }\n    editPassWordShow.value = false;\n  };\n  var handleExit = function handleExit() {\n    ElMessageBox.confirm('此操作将退出当前系统, 是否继续?', '提示', {\n      confirmButtonText: '确定',\n      cancelButtonText: '取消',\n      type: 'warning'\n    }).then(function () {\n      loginOut('已安全退出！');\n    }).catch(function () {\n      ElMessage({\n        type: 'info',\n        message: '已取消退出'\n      });\n    });\n  };\n  var loginOut = /*#__PURE__*/function () {\n    var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(text) {\n      var _yield$api$loginOut, code, goal_login_router_path, goal_login_router_query;\n      return _regeneratorRuntime().wrap(function _callee$(_context) {\n        while (1) switch (_context.prev = _context.next) {\n          case 0:\n            _context.next = 2;\n            return api.loginOut();\n          case 2:\n            _yield$api$loginOut = _context.sent;\n            code = _yield$api$loginOut.code;\n            if (code === 200) {\n              sessionStorage.clear();\n              goal_login_router_path = localStorage.getItem('goal_login_router_path');\n              if (goal_login_router_path) {\n                goal_login_router_query = localStorage.getItem('goal_login_router_query') || '';\n                router.push({\n                  path: goal_login_router_path,\n                  query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {}\n                });\n              } else {\n                router.push({\n                  path: '/LoginView'\n                });\n              }\n              store.commit('setState');\n              globalReadOpenConfig();\n              // store.state.socket.disconnect()\n              // store.state.socket = null\n              ElMessage({\n                message: text,\n                showClose: true,\n                type: 'success'\n              });\n            }\n          case 5:\n          case \"end\":\n            return _context.stop();\n        }\n      }, _callee);\n    }));\n    return function loginOut(_x) {\n      return _ref.apply(this, arguments);\n    };\n  }();\n\n  // 地区id\n  var regionId = ref('');\n  var regionName = ref('');\n  var isRegionSelectShow = ref(false);\n  var regionSelect = function regionSelect(item) {\n    var _user$value3;\n    regionName.value = item.name;\n    isRegionSelectShow.value = false;\n    sessionStorage.setItem('AreaRow', JSON.stringify(item));\n    if (((_user$value3 = user.value) === null || _user$value3 === void 0 ? void 0 : _user$value3.areaId) === item.id) return;\n    verifyLoginUser(item);\n  };\n  var verifyLoginUser = /*#__PURE__*/function () {\n    var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(item) {\n      var _yield$api$verifyLogi, code, _user$value4;\n      return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n        while (1) switch (_context2.prev = _context2.next) {\n          case 0:\n            _context2.next = 2;\n            return api.verifyLoginUser({}, item.id);\n          case 2:\n            _yield$api$verifyLogi = _context2.sent;\n            code = _yield$api$verifyLogi.code;\n            if (code === 200) {\n              tabMenu.value = '';\n              sessionStorage.setItem('AreaId', item.id);\n              globalReadOpenConfig();\n              store.dispatch('loginUser', 'login');\n              store.commit('setBoxMessageRefresh', true);\n              store.commit('setPersonalDoRefresh', true);\n            } else {\n              regionId.value = (_user$value4 = user.value) === null || _user$value4 === void 0 ? void 0 : _user$value4.areaId;\n              unauthorized({\n                name: item.name\n              });\n            }\n          case 5:\n          case \"end\":\n            return _context2.stop();\n        }\n      }, _callee2);\n    }));\n    return function verifyLoginUser(_x2) {\n      return _ref2.apply(this, arguments);\n    };\n  }();\n  watch(function () {\n    return user.value;\n  }, function () {\n    var _user$value5, _user$value6;\n    regionId.value = (_user$value5 = user.value) === null || _user$value5 === void 0 ? void 0 : _user$value5.areaId;\n    if ((_user$value6 = user.value) !== null && _user$value6 !== void 0 && _user$value6.accountId) {\n      var _user$value7;\n      var verify = sessionStorage.getItem('verify');\n      if (verify && !Number(verify) && ((_user$value7 = user.value) === null || _user$value7 === void 0 ? void 0 : _user$value7.accountId) !== '1') {\n        var _openConfig$value;\n        if (((_openConfig$value = openConfig.value) === null || _openConfig$value === void 0 ? void 0 : _openConfig$value.forbidWeakPassword) === 'true') {\n          nextTick(function () {\n            verifyEditPassWord.value = 'yes';\n            verifyEditPassWordShow.value = true;\n          });\n        } else {\n          nextTick(function () {\n            verifyEditPassWord.value = 'no';\n            editPassWordShow.value = true;\n          });\n        }\n      }\n    }\n  }, {\n    immediate: true\n  });\n  // 菜单过滤\n  var _filterMenu = function filterMenu(menuList) {\n    var newMenuList = [];\n    for (var i = 0, len = menuList.length; i < len; i++) {\n      newMenuList.push({\n        id: menuList[i].menuId,\n        name: menuList[i].name,\n        routePath: menuList[i].routePath,\n        menuFunction: menuList[i].menuFunction,\n        menuRouteType: menuList[i].menuRouteType,\n        icon: menuList[i].iconUrl ? `${api.fileURL(menuList[i].iconUrl)}` : menuIcon,\n        has: menuList[i].permissions,\n        children: _filterMenu(menuList[i].children || [])\n      });\n    }\n    return newMenuList;\n  };\n  // 顶部菜单id\n  var tabMenu = ref('');\n  // 顶部菜单data\n  var tabMenuData = computed(function () {\n    return _filterMenu(store.getters.getMenuFn || []);\n  });\n  // 是否显示左侧菜单\n  var isView = ref(false);\n  // 工作台跳转具体应用\n  var isChildView = ref(false);\n  // 具体打开页面id\n  var isOpen = ref(false);\n  // 具体打开页面id\n  var openPageId = ref('');\n  var openPageObj = ref({});\n  // 打开页面工作台应用id\n  var openPageChildId = ref('');\n  // 工作台对象\n  var WorkBenchObj = ref({});\n  // 工作台子应用data\n  var WorkBenchList = ref([]);\n  // 具体工作台子应用对象\n  var childData = ref({});\n  watch(function () {\n    return tabMenuData.value;\n  }, function () {\n    isView.value = false;\n    isChildView.value = false;\n    WorkBenchObj.value = {};\n    WorkBenchList.value = [];\n    childData.value = {};\n    if (tabMenuData.value.length) {\n      var query = JSON.parse(sessionStorage.getItem('query')) || {};\n      if (query.openPageValue) {\n        openPage({\n          key: query.openPageKey || 'id',\n          value: query.openPageValue\n        });\n      } else {\n        nextTick(function () {\n          var _tabMenuData$value$;\n          tabMenu.value = (_tabMenuData$value$ = tabMenuData.value[0]) === null || _tabMenuData$value$ === void 0 ? void 0 : _tabMenuData$value$.id;\n          handleClick();\n        });\n      }\n    }\n  }, {\n    immediate: true\n  });\n  // 顶部菜单切换事件\n  var handleClick = function handleClick() {\n    if (process.env.NODE_ENV !== 'development') {\n      var detection_version = openConfig.value.DetectionVersion || '';\n      if (detection_version !== 'true') handleCompareVersion();\n    }\n    menuId.value = '';\n    menuData.value = [];\n    tabData.value = [];\n    isTabData.value = [];\n    keepAliveRoute.value = [];\n    nextTick(function () {\n      var _loop = function _loop() {\n          var item = tabMenuData.value[i];\n          if (tabMenu.value === item.id) {\n            sessionStorage.setItem('has', JSON.stringify(item));\n            if (['/WorkBench', '/WorkBenchCopy'].includes(item.routePath)) {\n              // 切换到工作台\n              isView.value = false;\n              router.push({\n                path: item.routePath,\n                query: routePathData(item.routePath)\n              });\n              WorkBenchObj.value = item;\n              WorkBenchList.value = item.children;\n              nextTick(function () {\n                if (openPageChildId.value) {\n                  if (openPageChildId.value === openPageId.value) {\n                    openPageId.value = '';\n                    openPageObj.value = {};\n                  }\n                  for (var r = 0, length = item.children.length; r < length; r++) {\n                    if (item.children[r].id === openPageChildId.value) {\n                      leftMenuData(item.children[r]);\n                    }\n                  }\n                }\n              });\n            } else {\n              if (item.routePath.includes('/GlobalHome')) {\n                if (openPageId.value) {\n                  WorkBenchObj.value = item;\n                  leftMenuData(item, false);\n                } else {\n                  isView.value = false;\n                  router.push({\n                    path: item.routePath,\n                    query: routePathData(item.routePath)\n                  });\n                }\n                return {\n                  v: void 0\n                };\n              }\n              // 不是工作台页面判断是否有子级菜单\n              if (item.children && item.children.length) {\n                // 有子级菜单按照左侧菜单显示\n                leftMenuData(item, true);\n              } else {\n                if (['3', '4'].includes(item.menuRouteType.value)) {\n                  isView.value = false;\n                  isChildView.value = false;\n                  router.push({\n                    path: item.routePath,\n                    query: _objectSpread(_objectSpread({}, routePathData(item.routePath)), {}, {\n                      menuRouteType: item.menuRouteType.value\n                    })\n                  });\n                } else {\n                  // const menuUrl = item.routePath.substring(0, item.routePath.indexOf('?')) || item.routePath\n                  // if (routePath(menuUrl) === '/') {\n                  //   isView.value = false\n                  //   const query = { menuRouteType: item.menuRouteType?.value, ...routePathData(item.routePath) }\n                  //   router.push({ path: item.routePath, query: query })\n                  //   const mainAppName = [mainRoutePath(item.routePath)]\n                  //   keepAliveRoute.value = mainAppName\n                  // } else {\n                  leftMenuData(item, true);\n                  // }\n                }\n              }\n            }\n          }\n        },\n        _ret;\n      for (var i = 0, len = tabMenuData.value.length; i < len; i++) {\n        _ret = _loop();\n        if (_ret) return _ret.v;\n      }\n    });\n  };\n  var WorkBenchMenu = function WorkBenchMenu(tabMenuId, tabMenuChildren) {\n    tabMenu.value = tabMenuId;\n    menuId.value = '';\n    menuData.value = [];\n    tabData.value = [];\n    isTabData.value = [];\n    keepAliveRoute.value = [];\n    leftMenuData(tabMenuChildren);\n  };\n  var leftMenuData = function leftMenuData(item, type) {\n    // 显示左侧菜单方法\n    // 不是工作台页面判断是否有子级菜单\n    if (item.children && item.children.length) {\n      // 有子级菜单按照左侧菜单显示\n      if (type) {\n        isView.value = true;\n        isChildView.value = false;\n      } else {\n        isView.value = true;\n        isChildView.value = true;\n        childData.value = item;\n      }\n      menuData.value = item.children;\n      var obj = openPageId.value ? _menuOpenPage(item.children) : _menuDefault(item.children);\n      menuId.value = obj.id;\n      menuClick(obj);\n      nextTick(function () {\n        openPageId.value = '';\n        openPageChildId.value = '';\n      });\n    } else {\n      if (['3', '4'].includes(item.menuRouteType.value)) {\n        isView.value = false;\n        isChildView.value = true;\n        childData.value = item;\n        router.push({\n          path: item.routePath,\n          query: _objectSpread(_objectSpread({}, routePathData(item.routePath)), {}, {\n            menuRouteType: item.menuRouteType.value\n          })\n        });\n      } else {\n        if (type) {\n          isView.value = false;\n          isChildView.value = false;\n        } else {\n          isView.value = false;\n          isChildView.value = true;\n          childData.value = item;\n        }\n        menuData.value = [item];\n        var _obj = openPageId.value ? _menuOpenPage([item]) : _menuDefault([item]);\n        menuId.value = _obj.id;\n        menuClick(_obj);\n        nextTick(function () {\n          openPageId.value = '';\n          openPageChildId.value = '';\n        });\n      }\n    }\n  };\n  var _menuDefault = function menuDefault(data) {\n    // 获取左侧菜单第一个菜单\n    var defaultObj = {};\n    for (var i = 0, len = data.length; i < len; i++) {\n      if (i === 0) {\n        if (data[i].children.length === 0) {\n          defaultObj = data[i];\n        } else {\n          defaultObj = _menuDefault(data[i].children);\n        }\n      }\n    }\n    return defaultObj;\n  };\n  var _menuOpenPage = function menuOpenPage(data) {\n    // 获取左侧菜单第一个菜单\n    var defaultObj = {};\n    for (var i = 0, len = data.length; i < len; i++) {\n      if (openPageId.value === data[i].id) {\n        defaultObj = data[i];\n      }\n      if (data[i].children.length) {\n        var obj = _menuOpenPage(data[i].children);\n        defaultObj = obj.id ? obj : defaultObj;\n      }\n    }\n    return defaultObj;\n  };\n  var menuId = ref('');\n  var menuData = ref([]);\n  var menuClick = function menuClick(item) {\n    handleCloseOther(item.id);\n    // 左侧菜单点击事件\n    if (!tabData.value.length) {\n      qiankunActions.setGlobalState({\n        keepAliveRoute: []\n      });\n    }\n    if (!tabData.value.map(function (v) {\n      return v.id;\n    }).includes(item.id)) {\n      tabData.value.push(item);\n    }\n    tabClick();\n  };\n  var WorkBenchReturn = function WorkBenchReturn() {\n    // 工作台具体应用返回工作台\n    if (isChildView.value) {\n      isView.value = false;\n      isChildView.value = false;\n      handleClick();\n    }\n  };\n  var handleBreadcrumb = function handleBreadcrumb(item, index) {\n    if (index + 1 === tabData.value.length) return;\n    var newTabData = tabData.value.slice(0, index + 1);\n    var delTabData = tabData.value.slice(index + 1).map(function (v) {\n      return v.id;\n    });\n    tabData.value = newTabData;\n    isTabData.value = isTabData.value.filter(function (item) {\n      return !delTabData.includes(item.id);\n    });\n    var mainAppList = tabData.value.filter(function (v) {\n      return routePath(v.routePath.substring(0, v.routePath.indexOf('?')) || v.routePath) === '/';\n    });\n    var mainAppName = Array.from(new Set(mainAppList.map(function (v) {\n      return mainRoutePath(v.routePath);\n    })));\n    keepAliveRoute.value = mainAppName;\n    menuId.value = item.id;\n    tabClick();\n  };\n  var tabData = ref([]); // tab数据\n  var isTabData = ref([]);\n  var isMicroApp = ref('');\n  var noMicroApp = ref([]);\n  var keepAliveRoute = ref([]);\n  var add_msg = function add_msg(a, b) {\n    return a.filter(function (v) {\n      return b.indexOf(v) === -1;\n    });\n  };\n  // const delete_msg = (a, b) => b.filter(v => a.indexOf(v) === -1)\n  var tabClick = function tabClick() {\n    var microAppName = Object.keys(config.microApp);\n    var MicroAppData = Array.from(new Set(tabData.value.map(function (v) {\n      return routePath(v.routePath);\n    }))).filter(function (v) {\n      return microAppName.includes(v);\n    });\n    var addMicroApp = add_msg(MicroAppData, MicroApp.value);\n    // const delMicroApp = delete_msg(MicroAppData, MicroApp.value)\n    MicroApp.value = [].concat(_toConsumableArray(MicroApp.value), _toConsumableArray(addMicroApp));\n    if (!addMicroApp.length) {\n      menuRouterPush();\n      return;\n    }\n    nextTick(function () {\n      var _loop2 = function _loop2() {\n        var v = addMicroApp[i];\n        if (!MicroAppObj.value[v]) {\n          MicroAppObj.value[v] = loadFilterApp(v);\n          MicroAppObj.value[v].loadPromise.then(function () {\n            MicroAppObj.value[v].mountPromise.then(function () {\n              qiankunActions.setGlobalState({\n                theme: store.getters.getThemeFn,\n                user: store.getters.getUserFn,\n                menu: store.getters.getMenuFn,\n                area: store.getters.getAreaFn,\n                role: store.getters.getRoleFn,\n                readConfig: store.getters.getReadConfig,\n                readOpenConfig: store.getters.getReadOpenConfig\n              });\n            });\n          }).catch(function (err) {\n            noMicroApp.value.push(v);\n          });\n        }\n      };\n      for (var i = 0, len = addMicroApp.length; i < len; i++) {\n        _loop2();\n      }\n      setTimeout(function () {\n        menuRouterPush();\n      }, 52);\n    });\n  };\n  var menuRouterPush = function menuRouterPush() {\n    for (var i = 0, len = tabData.value.length; i < len; i++) {\n      var item = tabData.value[i];\n      if (menuId.value === item.id) {\n        sessionStorage.setItem('has', JSON.stringify(item));\n        var menuUrl = item.routePath.substring(0, item.routePath.indexOf('?')) || item.routePath;\n        var getMicroName = routePath(menuUrl);\n        isMicroApp.value = getMicroName;\n        if (MicroApp.value.includes(getMicroName) && !noMicroApp.value.includes(getMicroName)) {\n          var _item$menuRouteType;\n          var query = _objectSpread(_objectSpread({\n            menuRouteType: (_item$menuRouteType = item.menuRouteType) === null || _item$menuRouteType === void 0 ? void 0 : _item$menuRouteType.value\n          }, routePathData(item.routePath)), item.query);\n          router.push({\n            path: item.routePath,\n            query: query\n          });\n          MicroAppObj.value[getMicroName].mountPromise.then(function () {\n            qiankunActions.setGlobalState({\n              keepAliveRoute: tabData.value.map(function (v) {\n                return v.routePath;\n              })\n            });\n          });\n        } else {\n          if (getMicroName === '/') {\n            var _item$menuRouteType2;\n            var _query = _objectSpread(_objectSpread({\n              menuRouteType: (_item$menuRouteType2 = item.menuRouteType) === null || _item$menuRouteType2 === void 0 ? void 0 : _item$menuRouteType2.value\n            }, routePathData(item.routePath)), item.query);\n            router.push({\n              path: item.routePath,\n              query: _query\n            });\n            var mainAppList = tabData.value.filter(function (v) {\n              return routePath(v.routePath.substring(0, v.routePath.indexOf('?')) || v.routePath) === '/';\n            });\n            var mainAppName = Array.from(new Set(mainAppList.map(function (v) {\n              return mainRoutePath(v.routePath);\n            })));\n            keepAliveRoute.value = mainAppName;\n          } else {\n            router.push({\n              path: '/NotFoundPage'\n            });\n          }\n        }\n      }\n    }\n  };\n  var mainRoutePath = function mainRoutePath(url) {\n    var path = '';\n    var start = url.indexOf('/') + 1;\n    var end = url.indexOf('?');\n    if (end === -1) {\n      path = url.substring(1);\n    } else {\n      path = url.substring(start, end);\n    }\n    return path;\n  };\n  var routePathData = function routePathData(href) {\n    var params = {};\n    href = href.substring(href.indexOf('?') + 1);\n    var arr = href.split('&');\n    arr.forEach(function (item) {\n      var a = item.split('=');\n      params[a[0]] = a[1];\n    });\n    return params;\n  };\n  // 获取第一个斜杠和第二个斜杠之间的内容\n  var routePath = function routePath(url) {\n    var path = ''; // 第二个斜杠前内容\n    var first = url.indexOf('/') + 1; // 从第一个斜杠算起（+1表示不包括该斜杠）\n    var kong = url.indexOf(' ', first); // 第一个斜杠后的第一个空格\n    var heng = url.indexOf('/', first); // 第一个斜杠后的第一个斜杠（即第二个斜杠）\n    if (heng === -1) {\n      path = url.substring(1, kong);\n    } else {\n      path = url.substring(1, heng);\n    }\n    return path;\n  };\n  watch(function () {\n    return store.state.openRoute;\n  }, function (val) {\n    if (val.path) {\n      openRoute(val);\n    }\n  }, {\n    immediate: true\n  });\n  watch(function () {\n    return store.state.closeOpenRoute;\n  }, function (val) {\n    if (val.closeId) {\n      delRoute(val);\n    }\n  }, {\n    immediate: true\n  });\n  var openRoute = function openRoute(val) {\n    if (isTabData.value.map(function (v) {\n      return v.isData;\n    }).includes(JSON.stringify(val))) {\n      for (var i = 0, len = isTabData.value.length; i < len; i++) {\n        var item = isTabData.value[i];\n        if (item.isData === JSON.stringify(val)) {\n          menuId.value = item.id;\n          tabClick();\n        }\n      }\n    } else {\n      var id = guid();\n      isTabData.value.push({\n        id: id,\n        isData: JSON.stringify(val)\n      });\n      tabData.value.push({\n        id,\n        name: val.name,\n        routePath: val.path,\n        query: _objectSpread(_objectSpread({}, val.query), {}, {\n          routeId: id,\n          oldRouteId: menuId.value\n        })\n      });\n      menuId.value = id;\n      tabClick();\n    }\n    qiankunActions.setGlobalState({\n      openRoute: {\n        name: '',\n        path: '',\n        query: {}\n      }\n    });\n  };\n  var delRoute = function delRoute(val) {\n    if (val.openId) {\n      isTabData.value = isTabData.value.filter(function (item) {\n        return item.id !== val.closeId;\n      });\n      tabData.value = tabData.value.filter(function (item) {\n        return item.id !== val.closeId;\n      });\n      menuId.value = val.openId;\n      tabClick();\n    } else {\n      handleClose(val.closeId);\n    }\n    qiankunActions.setGlobalState({\n      closeOpenRoute: {\n        openId: '',\n        closeId: ''\n      }\n    });\n  };\n  var isRefresh = ref(true);\n  var handleRefresh = function handleRefresh(id) {\n    if (route.meta.moduleName === 'main') {\n      keepAliveRoute.value = keepAliveRoute.value.filter(function (v) {\n        return v !== route.name;\n      });\n      isRefresh.value = false;\n      setTimeout(function () {\n        keepAliveRoute.value.push(route.name);\n        isRefresh.value = true;\n      }, 200);\n    } else {\n      for (var i = 0, len = tabData.value.length; i < len; i++) {\n        var item = tabData.value[i];\n        if (item.id === id) {\n          qiankunActions.setGlobalState({\n            refreshRoute: item.routePath\n          });\n          setTimeout(function () {\n            qiankunActions.setGlobalState({\n              refreshRoute: ''\n            });\n          }, 222);\n        }\n      }\n    }\n  };\n  var handleClose = function handleClose(id) {\n    if (menuId.value === id) {\n      for (var i = 0, len = tabData.value.length; i < len; i++) {\n        var item = tabData.value[i];\n        if (item.id === id) {\n          menuId.value = tabData.value[i ? i - 1 : 1].id;\n          tabClick();\n        }\n      }\n    }\n    isTabData.value = isTabData.value.filter(function (item) {\n      return item.id !== id;\n    });\n    tabData.value = tabData.value.filter(function (item) {\n      return item.id !== id;\n    });\n  };\n  var handleCloseOther = function handleCloseOther(id) {\n    isTabData.value = isTabData.value.filter(function (item) {\n      return item.id === id;\n    });\n    tabData.value = tabData.value.filter(function (item) {\n      return item.id === id;\n    });\n    menuId.value = id;\n    tabClick();\n  };\n  var _openPageMenu = function openPageMenu(nodes, key, value) {\n    if (!nodes || !nodes.length) return []; // eslint-disable-line\n    var children = [];\n    var _iterator = _createForOfIteratorHelper(nodes),\n      _step;\n    try {\n      for (_iterator.s(); !(_step = _iterator.n()).done;) {\n        var node = _step.value;\n        if (node[key] === value && !openPageId.value) {\n          openPageId.value = node.id;\n          openPageObj.value = node;\n        }\n        node = Object.assign({}, node);\n        var sub = _openPageMenu(node.children, key, value);\n        if (sub && sub.length || node[key] === value) {\n          sub.length && (node.children = sub);\n          children.push(node);\n        }\n      }\n    } catch (err) {\n      _iterator.e(err);\n    } finally {\n      _iterator.f();\n    }\n    return children.length ? children : []; // eslint-disable-line\n  };\n  var openPage = function openPage(_ref3) {\n    var _ref3$key = _ref3.key,\n      key = _ref3$key === void 0 ? 'id' : _ref3$key,\n      value = _ref3.value;\n    if (isOpen.value) return;\n    isOpen.value = true;\n    var openPagedata = _openPageMenu(tabMenuData.value, key, value)[0] || {};\n    if (openPagedata.id) {\n      if (tabMenu.value === openPagedata.id) {\n        if (openPagedata.routePath === '/WorkBench') {\n          var _openPagedata$childre;\n          if (openPagedata.id === openPageId.value) {\n            openPageId.value = '';\n            openPageObj.value = {};\n            WorkBenchReturn();\n            return;\n          }\n          if (((_openPagedata$childre = openPagedata.children[0]) === null || _openPagedata$childre === void 0 ? void 0 : _openPagedata$childre.id) === childData.value.id) {\n            menuId.value = openPageId.value;\n            menuClick(openPageObj.value);\n            openPageId.value = '';\n            openPageObj.value = {};\n            nextTick(function () {\n              if (isOpen.value) {\n                isOpen.value = false;\n              }\n            });\n          } else {\n            WorkBenchReturn();\n            setTimeout(function () {\n              nextTick(function () {\n                var _openPagedata$childre2;\n                openPageChildId.value = (_openPagedata$childre2 = openPagedata.children[0]) === null || _openPagedata$childre2 === void 0 ? void 0 : _openPagedata$childre2.id;\n                handleClick();\n                nextTick(function () {\n                  if (isOpen.value) {\n                    isOpen.value = false;\n                  }\n                });\n              });\n            }, 200);\n          }\n        } else {\n          if (openPagedata.routePath.includes('/GlobalHome')) {\n            handleClick();\n            nextTick(function () {\n              if (isOpen.value) {\n                isOpen.value = false;\n              }\n            });\n            return;\n          }\n          menuId.value = openPageId.value;\n          menuClick(openPageObj.value);\n          openPageId.value = '';\n          openPageObj.value = {};\n          nextTick(function () {\n            if (isOpen.value) {\n              isOpen.value = false;\n            }\n          });\n        }\n      } else {\n        if (isChildView.value) {\n          WorkBenchReturn();\n          setTimeout(function () {\n            nextTick(function () {\n              tabMenu.value = openPagedata.id;\n              if (openPagedata.id === openPageId.value) {\n                openPageId.value = '';\n                openPageObj.value = {};\n              } else {\n                var _openPagedata$childre3;\n                openPageChildId.value = (_openPagedata$childre3 = openPagedata.children[0]) === null || _openPagedata$childre3 === void 0 ? void 0 : _openPagedata$childre3.id;\n              }\n              handleClick();\n              nextTick(function () {\n                if (isOpen.value) {\n                  isOpen.value = false;\n                }\n              });\n            });\n          }, 200);\n        } else {\n          tabMenu.value = openPagedata.id;\n          if (openPagedata.id === openPageId.value) {\n            openPageId.value = '';\n            openPageObj.value = {};\n          } else {\n            var _openPagedata$childre4;\n            openPageChildId.value = (_openPagedata$childre4 = openPagedata.children[0]) === null || _openPagedata$childre4 === void 0 ? void 0 : _openPagedata$childre4.id;\n          }\n          handleClick();\n          nextTick(function () {\n            if (isOpen.value) {\n              isOpen.value = false;\n            }\n          });\n        }\n      }\n    } else {\n      isOpen.value = false;\n      ElMessage({\n        type: 'warning',\n        message: '未检测到你有此菜单！'\n      });\n    }\n  };\n  var setOpenPageId = function setOpenPageId(id) {\n    openPageId.value = id;\n  };\n  var guid = function guid() {\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\n      var r = Math.random() * 16 | 0,\n        v = c == 'x' ? r : r & 0x3 | 0x8;\n      return v.toString(16);\n    });\n  };\n  onUnmounted(function () {\n    var _loop3 = function _loop3() {\n      var v = MicroApp.value[i];\n      MicroAppObj.value[v].unmount();\n      MicroAppObj.value[v].unmountPromise.then(function () {\n        MicroAppObj.value[v] = null;\n      });\n    };\n    for (var i = 0, len = MicroApp.value.length; i < len; i++) {\n      _loop3();\n    }\n    MicroApp.value = [];\n  });\n  provide('WorkBenchList', WorkBenchList);\n  provide('WorkBenchMenu', WorkBenchMenu);\n  provide('leftMenuData', leftMenuData);\n  provide('setOpenPageId', setOpenPageId);\n  provide('openPage', openPage);\n  provide('openRoute', openRoute);\n  provide('delRoute', delRoute);\n  provide('regionId', regionId);\n  provide('regionSelect', regionSelect);\n  provide('area', area);\n  return {\n    user,\n    area,\n    role,\n    left,\n    width,\n    LayoutViewBox,\n    LayoutViewInfo,\n    helpShow,\n    handleCommand,\n    editPassWordShow,\n    verifyEditPassWord,\n    verifyEditPassWordShow,\n    editPassWordCallback,\n    regionId,\n    regionName,\n    regionSelect,\n    isRegionSelectShow,\n    isView,\n    isChildView,\n    tabMenu,\n    tabMenuData,\n    handleClick,\n    menuId,\n    menuData,\n    menuClick,\n    handleBreadcrumb,\n    WorkBenchObj,\n    WorkBenchList,\n    childData,\n    WorkBenchReturn,\n    isRefresh,\n    keepAliveRoute,\n    tabData,\n    tabClick,\n    handleRefresh,\n    handleClose,\n    handleCloseOther,\n    isMicroApp,\n    MicroApp,\n    openPage,\n    leftMenuData\n  };\n};\nexport var qiankun = function qiankun(route) {\n  var isMain = ref(false);\n  var isMainPage = function isMainPage() {\n    isMain.value = route.meta.moduleName === 'main';\n  };\n  watch(function () {\n    return route;\n  }, function () {\n    isMainPage();\n  }, {\n    deep: true,\n    immediate: true\n  });\n  onMounted(function () {\n    isMainPage(route);\n  });\n  return {\n    isMain\n  };\n};\nexport var ChatMethod = function ChatMethod() {\n  var store = useStore();\n  var rongCloudToken = computed(function () {\n    return store.getters.getRongCloudToken;\n  });\n  return {\n    rongCloudToken\n  };\n};\nexport var AiChatMethod = function AiChatMethod() {\n  var store = useStore();\n  var AiChatWidth = computed(function () {\n    return store.state.AiChatWidth;\n  });\n  var AiChatTargetWidth = computed(function () {\n    return `${AiChatWidth.value}px`;\n  });\n  var AiChatViewType = ref(false);\n  var AiChatWindowShow = ref(false);\n  // 自动吸附到最近的侧边\n  var handleResizeFloatingWindow = function handleResizeFloatingWindow() {\n    if (window.innerWidth > 1280 + 400) {\n      var width = window.innerWidth - 1280 > 520 ? 520 : 400;\n      store.commit('setAiChatWidth', width);\n      if (!AiChatViewType.value) AiChatWindowShow.value = false;\n      AiChatViewType.value = true;\n    } else {\n      store.commit('setAiChatWidth', 400);\n      if (AiChatViewType.value) AiChatWindowShow.value = false;\n      AiChatViewType.value = false;\n    }\n  };\n  onMounted(function () {\n    handleResizeFloatingWindow();\n    window.addEventListener('resize', handleResizeFloatingWindow);\n  });\n  onUnmounted(function () {\n    window.removeEventListener('resize', handleResizeFloatingWindow);\n  });\n  return {\n    AiChatTargetWidth,\n    AiChatViewType,\n    AiChatWindowShow\n  };\n};\nexport var loginHintMethod = function loginHintMethod() {\n  var store = useStore();\n  var user = computed(function () {\n    return store.state.user;\n  });\n  var openConfig = computed(function () {\n    return store.getters.getReadOpenConfig;\n  });\n  var systemPlatform = computed(function () {\n    var _openConfig$value2;\n    return (_openConfig$value2 = openConfig.value) === null || _openConfig$value2 === void 0 ? void 0 : _openConfig$value2.systemPlatform;\n  });\n  var loginHintShow = ref(false);\n  var hasDuplicates = function hasDuplicates(arr1, arr2) {\n    var set = new Set(arr1);\n    return arr2.some(function (item) {\n      return set.has(item);\n    });\n  };\n  var getLoginHintConfig = /*#__PURE__*/function () {\n    var _ref4 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3() {\n      var _yield$api$globalRead, data;\n      return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n        while (1) switch (_context3.prev = _context3.next) {\n          case 0:\n            _context3.next = 2;\n            return api.globalReadOpenConfig({\n              codes: ['loginPopShow']\n            });\n          case 2:\n            _yield$api$globalRead = _context3.sent;\n            data = _yield$api$globalRead.data;\n            if (data.loginPopShow) {\n              loginHintShow.value = data.loginPopShow == 'true';\n            } else {\n              loginHintShow.value = false;\n            }\n          case 5:\n          case \"end\":\n            return _context3.stop();\n        }\n      }, _callee3);\n    }));\n    return function getLoginHintConfig() {\n      return _ref4.apply(this, arguments);\n    };\n  }();\n  onMounted(function () {});\n  watch(function () {\n    return user.value;\n  }, function () {\n    if (user.value.id) {\n      var specialRoleKeys = user.value.specialRoleKeys || [];\n      var npcRoleKeys = ['npc_contact_committee', 'suggestion_office_user', 'delegation_manager', 'npc_member'];\n      var cppccRoleKeys = ['proposal_committee', 'suggestion_office_user', 'cppcc_member'];\n      var canSee = systemPlatform.value === 'CPPCC' ? cppccRoleKeys : npcRoleKeys;\n      loginHintShow.value = hasDuplicates(specialRoleKeys, canSee);\n      if (loginHintShow.value) getLoginHintConfig();\n    }\n  }, {\n    immediate: true\n  });\n  return {\n    loginHintShow\n  };\n};", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "ref", "watch", "onMounted", "computed", "nextTick", "provide", "onUnmounted", "api", "useStore", "config", "qiankunActions", "loadFilterApp", "handleCompareVersion", "globalReadOpenConfig", "get_font_family", "change_font_family", "elementResizeDetectorMaker", "unauthorized", "ElMessage", "ElMessageBox", "refreshIcon", "LayoutView", "route", "router", "store", "menuIcon", "API_URL", "erd", "left", "width", "LayoutViewBox", "LayoutViewInfo", "editPassWordShow", "verifyEditPassWord", "verifyEditPassWordShow", "MicroApp", "MicroAppObj", "user", "getters", "getUserFn", "area", "getAreaFn", "role", "getRoleFn", "openConfig", "getReadOpenConfig", "helpShow", "handleRegionSelect", "listenTo", "element", "offsetWidth", "_user$value", "_user$value2", "oldRegionInfo", "sessionStorage", "getItem", "isRegionSelect", "accountId", "areaTotal", "isRegionSelectShow", "handleCommand", "commit", "show", "window", "location", "href", "mainP<PERSON>", "Date", "getTime", "handleExit", "message", "editPassWordCallback", "loginOut", "confirm", "confirmButtonText", "cancelButtonText", "_ref", "_callee", "text", "_yield$api$loginOut", "code", "goal_login_router_path", "goal_login_router_query", "_callee$", "_context", "clear", "localStorage", "path", "query", "JSON", "parse", "showClose", "_x", "regionId", "regionName", "regionSelect", "item", "_user$value3", "setItem", "stringify", "areaId", "id", "verifyLoginUser", "_ref2", "_callee2", "_yield$api$verifyLogi", "_user$value4", "_callee2$", "_context2", "tabMenu", "dispatch", "_x2", "_user$value5", "_user$value6", "_user$value7", "verify", "Number", "_openConfig$value", "forbidWeakPassword", "immediate", "filterMenu", "menuList", "newMenuList", "len", "menuId", "routePath", "menuFunction", "menuRouteType", "icon", "iconUrl", "fileURL", "has", "permissions", "children", "tabMenuData", "getMenuFn", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isOpen", "openPageId", "openPageObj", "openPageChildId", "WorkBenchObj", "WorkBenchList", "childData", "openPageValue", "openPage", "key", "openPageKey", "_tabMenuData$value$", "handleClick", "process", "env", "NODE_ENV", "detection_version", "DetectionVersion", "menuData", "tabData", "isTabData", "keepAliveRoute", "_loop", "includes", "routePathData", "leftMenuData", "_objectSpread", "_ret", "WorkBenchMenu", "tabMenuId", "tabMenuChildren", "obj", "menuOpenPage", "menuDefault", "menuClick", "data", "defaultObj", "handleCloseOther", "setGlobalState", "map", "tabClick", "WorkBenchReturn", "handleBreadcrumb", "index", "newTabData", "delTabData", "filter", "mainAppList", "substring", "indexOf", "mainAppName", "Array", "from", "Set", "mainRoutePath", "isMicroApp", "noMicroApp", "add_msg", "b", "microAppName", "microApp", "MicroAppData", "addMicroApp", "concat", "_toConsumableArray", "menuRouterPush", "_loop2", "loadPromise", "mountPromise", "theme", "getThemeFn", "menu", "readConfig", "getReadConfig", "readOpenConfig", "err", "setTimeout", "menuUrl", "getMicroName", "_item$menuRouteType", "_item$menuRouteType2", "url", "start", "end", "params", "arr", "split", "first", "kong", "heng", "state", "openRoute", "val", "closeOpenRoute", "closeId", "delRoute", "isData", "guid", "routeId", "oldRouteId", "openId", "handleClose", "isRefresh", "handleRefresh", "meta", "moduleName", "refreshRoute", "openPageMenu", "nodes", "_iterator", "_createForOfIteratorHelper", "_step", "node", "assign", "sub", "_ref3", "_ref3$key", "openPagedata", "_openPagedata$childre", "_openPagedata$childre2", "_openPagedata$childre3", "_openPagedata$childre4", "setOpenPageId", "replace", "Math", "random", "toString", "_loop3", "unmount", "unmountPromise", "qiankun", "is<PERSON><PERSON>", "isMainPage", "deep", "ChatMethod", "rongCloudToken", "getRongCloudToken", "AiChatMethod", "AiChatWidth", "AiChatTargetWidth", "AiChatViewType", "AiChatWindowShow", "handleResizeFloatingWindow", "innerWidth", "addEventListener", "removeEventListener", "loginHintMethod", "systemPlatform", "_openConfig$value2", "loginHintShow", "hasDuplicates", "arr1", "arr2", "set", "some", "getLoginHintConfig", "_ref4", "_callee3", "_yield$api$globalRead", "_callee3$", "_context3", "codes", "loginPopShow", "special<PERSON><PERSON><PERSON><PERSON>s", "npcRoleKeys", "cppccRoleKeys", "canSee"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/LayoutView/LayoutView.js"], "sourcesContent": ["import { ref, watch, onMounted, computed, nextTick, provide, onUnmounted } from 'vue'\r\nimport api from '@/api'\r\nimport { useStore } from 'vuex'\r\nimport config from 'common/config'\r\nimport { qiankunActions, loadFilterApp } from '@/qiankun'\r\nimport { handleCompareVersion } from 'common/js/CheckVersion'\r\nimport { globalReadOpenConfig } from 'common/js/GlobalMethod'\r\nimport { get_font_family, change_font_family } from 'common/js/utils'\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nimport unauthorized from 'common/components/unauthorized'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nexport const refreshIcon = `<svg t=\"1743990581190\" class=\"icon\" viewBox=\"0 0 1024 1024\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" p-id=\"52145\" width=\"30\" height=\"30\"><path d=\"M512 128a384 384 0 1 1 0 768A384 384 0 0 1 512 128z m0 48.192C335.296 176.192 174.08 335.36 174.08 512c0 176.704 161.216 336.512 337.92 336.512S849.152 688.704 849.152 512 688.704 176.192 512 176.192z\" fill=\"#ffffff\" p-id=\"52146\"></path><path d=\"M244.352 506.688h73.6c0-123.2 86.016-220.032 196.48-215.68 36.8 0 69.568 13.248 102.272 30.848l-36.8 39.68c-20.48-8.96-45.056-17.664-69.568-17.664-77.76 0-143.232 70.4-143.232 162.816h73.6l-98.176 110.08-98.176-110.08zM705.088 515.84c0 117.248-86.848 217.28-194.368 217.28-37.248 0-70.4-17.344-103.424-34.752l37.184-39.04c20.736 8.704 45.568 17.344 70.4 17.344 78.592 0 144.768-69.504 144.768-160.768H581.056l99.328-108.608 99.2 108.544h-74.496z\" fill=\"#ffffff\" p-id=\"52147\"></path></svg>`\r\n\r\nexport const LayoutView = (route, router) => {\r\n  const store = useStore()\r\n  const menuIcon = `${config.API_URL}/pageImg/open/menuIcon`\r\n\r\n  const erd = elementResizeDetectorMaker()\r\n  const left = ref(0)\r\n  const width = ref('')\r\n  const LayoutViewBox = ref(null)\r\n  const LayoutViewInfo = ref(null)\r\n  const editPassWordShow = ref(false)\r\n  const verifyEditPassWord = ref('')\r\n  const verifyEditPassWordShow = ref(false)\r\n  const MicroApp = ref([])\r\n  const MicroAppObj = ref({})\r\n  // 用户信息\r\n  const user = computed(() => store.getters.getUserFn)\r\n  // 地区信息\r\n  const area = computed(() => store.getters.getAreaFn)\r\n  // 角色信息\r\n  const role = computed(() => store.getters.getRoleFn)\r\n  const openConfig = computed(() => store.getters.getReadOpenConfig)\r\n  const helpShow = ref(false)\r\n\r\n  onMounted(() => {\r\n    handleRegionSelect()\r\n    nextTick(() => {\r\n      erd.listenTo(LayoutViewBox.value, (element) => {\r\n        left.value = element.offsetWidth\r\n      })\r\n      erd.listenTo(LayoutViewInfo.value, (element) => {\r\n        width.value = `width: calc(100% - ${element.offsetWidth + 16}px);`\r\n      })\r\n    })\r\n  })\r\n  const handleRegionSelect = () => {\r\n    const oldRegionInfo = sessionStorage.getItem('oldRegionInfo') || ''\r\n    const isRegionSelect = sessionStorage.getItem('isRegionSelect') || ''\r\n    if (user.value?.accountId !== '1' && user.value?.areaTotal > 1 && oldRegionInfo && !isRegionSelect) {\r\n      isRegionSelectShow.value = true\r\n    }\r\n  }\r\n  const handleCommand = (type) => {\r\n    if (type === 'task') {\r\n      store.commit('setGlobalCentralControlObj', { show: true })\r\n    } else if (type === 'refresh') {\r\n      // window.location.reload(true)\r\n      // window.location.reload(window.location.href)\r\n      window.location.href = `${config.mainPath}?v=${new Date().getTime()}`\r\n    } else if (type === 'locale') {\r\n      if (get_font_family() === 'cn') return change_font_family('tw')\r\n      if (get_font_family() === 'tw') return change_font_family('cn')\r\n    } else if (type === 'help') {\r\n      helpShow.value = true\r\n    } else if (type === 'edit_password') {\r\n      verifyEditPassWord.value = ''\r\n      editPassWordShow.value = true\r\n    } else if (type === 'exit') {\r\n      handleExit()\r\n    } else {\r\n      ElMessage({ type: 'info', message: '正在开发中！' })\r\n    }\r\n  }\r\n  const editPassWordCallback = (type) => {\r\n    if (type) {\r\n      loginOut('请使用新密码重新登录！')\r\n    }\r\n    editPassWordShow.value = false\r\n  }\r\n  const handleExit = () => {\r\n    ElMessageBox.confirm('此操作将退出当前系统, 是否继续?', '提示', {\r\n      confirmButtonText: '确定',\r\n      cancelButtonText: '取消',\r\n      type: 'warning'\r\n    })\r\n      .then(() => {\r\n        loginOut('已安全退出！')\r\n      })\r\n      .catch(() => {\r\n        ElMessage({ type: 'info', message: '已取消退出' })\r\n      })\r\n  }\r\n  const loginOut = async (text) => {\r\n    const { code } = await api.loginOut()\r\n    if (code === 200) {\r\n      sessionStorage.clear()\r\n      const goal_login_router_path = localStorage.getItem('goal_login_router_path')\r\n      if (goal_login_router_path) {\r\n        const goal_login_router_query = localStorage.getItem('goal_login_router_query') || ''\r\n        router.push({\r\n          path: goal_login_router_path,\r\n          query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {}\r\n        })\r\n      } else {\r\n        router.push({ path: '/LoginView' })\r\n      }\r\n      store.commit('setState')\r\n      globalReadOpenConfig()\r\n      // store.state.socket.disconnect()\r\n      // store.state.socket = null\r\n      ElMessage({ message: text, showClose: true, type: 'success' })\r\n    }\r\n  }\r\n\r\n  // 地区id\r\n  const regionId = ref('')\r\n  const regionName = ref('')\r\n  const isRegionSelectShow = ref(false)\r\n  const regionSelect = (item) => {\r\n    regionName.value = item.name\r\n    isRegionSelectShow.value = false\r\n    sessionStorage.setItem('AreaRow', JSON.stringify(item))\r\n    if (user.value?.areaId === item.id) return\r\n    verifyLoginUser(item)\r\n  }\r\n  const verifyLoginUser = async (item) => {\r\n    const { code } = await api.verifyLoginUser({}, item.id)\r\n    if (code === 200) {\r\n      tabMenu.value = ''\r\n      sessionStorage.setItem('AreaId', item.id)\r\n      globalReadOpenConfig()\r\n      store.dispatch('loginUser', 'login')\r\n      store.commit('setBoxMessageRefresh', true)\r\n      store.commit('setPersonalDoRefresh', true)\r\n    } else {\r\n      regionId.value = user.value?.areaId\r\n      unauthorized({ name: item.name })\r\n    }\r\n  }\r\n  watch(\r\n    () => user.value,\r\n    () => {\r\n      regionId.value = user.value?.areaId\r\n      if (user.value?.accountId) {\r\n        const verify = sessionStorage.getItem('verify')\r\n        if (verify && !Number(verify) && user.value?.accountId !== '1') {\r\n          if (openConfig.value?.forbidWeakPassword === 'true') {\r\n            nextTick(() => {\r\n              verifyEditPassWord.value = 'yes'\r\n              verifyEditPassWordShow.value = true\r\n            })\r\n          } else {\r\n            nextTick(() => {\r\n              verifyEditPassWord.value = 'no'\r\n              editPassWordShow.value = true\r\n            })\r\n          }\r\n        }\r\n      }\r\n    },\r\n    { immediate: true }\r\n  )\r\n  // 菜单过滤\r\n  const filterMenu = (menuList) => {\r\n    let newMenuList = []\r\n    for (let i = 0, len = menuList.length; i < len; i++) {\r\n      newMenuList.push({\r\n        id: menuList[i].menuId,\r\n        name: menuList[i].name,\r\n        routePath: menuList[i].routePath,\r\n        menuFunction: menuList[i].menuFunction,\r\n        menuRouteType: menuList[i].menuRouteType,\r\n        icon: menuList[i].iconUrl ? `${api.fileURL(menuList[i].iconUrl)}` : menuIcon,\r\n        has: menuList[i].permissions,\r\n        children: filterMenu(menuList[i].children || [])\r\n      })\r\n    }\r\n    return newMenuList\r\n  }\r\n  // 顶部菜单id\r\n  const tabMenu = ref('')\r\n  // 顶部菜单data\r\n  const tabMenuData = computed(() => filterMenu(store.getters.getMenuFn || []))\r\n  // 是否显示左侧菜单\r\n  const isView = ref(false)\r\n  // 工作台跳转具体应用\r\n  const isChildView = ref(false)\r\n  // 具体打开页面id\r\n  const isOpen = ref(false)\r\n  // 具体打开页面id\r\n  const openPageId = ref('')\r\n  const openPageObj = ref({})\r\n  // 打开页面工作台应用id\r\n  const openPageChildId = ref('')\r\n  // 工作台对象\r\n  const WorkBenchObj = ref({})\r\n  // 工作台子应用data\r\n  const WorkBenchList = ref([])\r\n  // 具体工作台子应用对象\r\n  const childData = ref({})\r\n  watch(\r\n    () => tabMenuData.value,\r\n    () => {\r\n      isView.value = false\r\n      isChildView.value = false\r\n      WorkBenchObj.value = {}\r\n      WorkBenchList.value = []\r\n      childData.value = {}\r\n      if (tabMenuData.value.length) {\r\n        const query = JSON.parse(sessionStorage.getItem('query')) || {}\r\n        if (query.openPageValue) {\r\n          openPage({ key: query.openPageKey || 'id', value: query.openPageValue })\r\n        } else {\r\n          nextTick(() => {\r\n            tabMenu.value = tabMenuData.value[0]?.id\r\n            handleClick()\r\n          })\r\n        }\r\n      }\r\n    },\r\n    { immediate: true }\r\n  )\r\n  // 顶部菜单切换事件\r\n  const handleClick = () => {\r\n    if (process.env.NODE_ENV !== 'development') {\r\n      const detection_version = openConfig.value.DetectionVersion || ''\r\n      if (detection_version !== 'true') handleCompareVersion()\r\n    }\r\n    menuId.value = ''\r\n    menuData.value = []\r\n    tabData.value = []\r\n    isTabData.value = []\r\n    keepAliveRoute.value = []\r\n    nextTick(() => {\r\n      for (let i = 0, len = tabMenuData.value.length; i < len; i++) {\r\n        const item = tabMenuData.value[i]\r\n        if (tabMenu.value === item.id) {\r\n          sessionStorage.setItem('has', JSON.stringify(item))\r\n          if (['/WorkBench', '/WorkBenchCopy'].includes(item.routePath)) { // 切换到工作台\r\n            isView.value = false\r\n            router.push({ path: item.routePath, query: routePathData(item.routePath) })\r\n            WorkBenchObj.value = item\r\n            WorkBenchList.value = item.children\r\n            nextTick(() => {\r\n              if (openPageChildId.value) {\r\n                if (openPageChildId.value === openPageId.value) {\r\n                  openPageId.value = ''\r\n                  openPageObj.value = {}\r\n                }\r\n                for (let r = 0, length = item.children.length; r < length; r++) {\r\n                  if (item.children[r].id === openPageChildId.value) {\r\n                    leftMenuData(item.children[r])\r\n                  }\r\n                }\r\n              }\r\n            })\r\n          } else {\r\n            if (item.routePath.includes('/GlobalHome')) {\r\n              if (openPageId.value) {\r\n                WorkBenchObj.value = item\r\n                leftMenuData(item, false)\r\n              } else {\r\n                isView.value = false\r\n                router.push({ path: item.routePath, query: routePathData(item.routePath) })\r\n              }\r\n              return\r\n            }\r\n            // 不是工作台页面判断是否有子级菜单\r\n            if (item.children && item.children.length) {\r\n              // 有子级菜单按照左侧菜单显示\r\n              leftMenuData(item, true)\r\n            } else {\r\n              if (['3', '4'].includes(item.menuRouteType.value)) {\r\n                isView.value = false\r\n                isChildView.value = false\r\n                router.push({\r\n                  path: item.routePath,\r\n                  query: { ...routePathData(item.routePath), menuRouteType: item.menuRouteType.value }\r\n                })\r\n              } else {\r\n                // const menuUrl = item.routePath.substring(0, item.routePath.indexOf('?')) || item.routePath\r\n                // if (routePath(menuUrl) === '/') {\r\n                //   isView.value = false\r\n                //   const query = { menuRouteType: item.menuRouteType?.value, ...routePathData(item.routePath) }\r\n                //   router.push({ path: item.routePath, query: query })\r\n                //   const mainAppName = [mainRoutePath(item.routePath)]\r\n                //   keepAliveRoute.value = mainAppName\r\n                // } else {\r\n                leftMenuData(item, true)\r\n                // }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    })\r\n  }\r\n  const WorkBenchMenu = (tabMenuId, tabMenuChildren) => {\r\n    tabMenu.value = tabMenuId\r\n    menuId.value = ''\r\n    menuData.value = []\r\n    tabData.value = []\r\n    isTabData.value = []\r\n    keepAliveRoute.value = []\r\n    leftMenuData(tabMenuChildren)\r\n  }\r\n  const leftMenuData = (item, type) => {\r\n    // 显示左侧菜单方法\r\n    // 不是工作台页面判断是否有子级菜单\r\n    if (item.children && item.children.length) {\r\n      // 有子级菜单按照左侧菜单显示\r\n      if (type) {\r\n        isView.value = true\r\n        isChildView.value = false\r\n      } else {\r\n        isView.value = true\r\n        isChildView.value = true\r\n        childData.value = item\r\n      }\r\n      menuData.value = item.children\r\n      const obj = openPageId.value ? menuOpenPage(item.children) : menuDefault(item.children)\r\n      menuId.value = obj.id\r\n      menuClick(obj)\r\n      nextTick(() => {\r\n        openPageId.value = ''\r\n        openPageChildId.value = ''\r\n      })\r\n    } else {\r\n      if (['3', '4'].includes(item.menuRouteType.value)) {\r\n        isView.value = false\r\n        isChildView.value = true\r\n        childData.value = item\r\n        router.push({\r\n          path: item.routePath,\r\n          query: { ...routePathData(item.routePath), menuRouteType: item.menuRouteType.value }\r\n        })\r\n      } else {\r\n        if (type) {\r\n          isView.value = false\r\n          isChildView.value = false\r\n        } else {\r\n          isView.value = false\r\n          isChildView.value = true\r\n          childData.value = item\r\n        }\r\n        menuData.value = [item]\r\n        const obj = openPageId.value ? menuOpenPage([item]) : menuDefault([item])\r\n        menuId.value = obj.id\r\n        menuClick(obj)\r\n        nextTick(() => {\r\n          openPageId.value = ''\r\n          openPageChildId.value = ''\r\n        })\r\n      }\r\n    }\r\n  }\r\n\r\n  const menuDefault = (data) => {\r\n    // 获取左侧菜单第一个菜单\r\n    var defaultObj = {}\r\n    for (let i = 0, len = data.length; i < len; i++) {\r\n      if (i === 0) {\r\n        if (data[i].children.length === 0) {\r\n          defaultObj = data[i]\r\n        } else {\r\n          defaultObj = menuDefault(data[i].children)\r\n        }\r\n      }\r\n    }\r\n    return defaultObj\r\n  }\r\n  const menuOpenPage = (data) => {\r\n    // 获取左侧菜单第一个菜单\r\n    var defaultObj = {}\r\n    for (let i = 0, len = data.length; i < len; i++) {\r\n      if (openPageId.value === data[i].id) {\r\n        defaultObj = data[i]\r\n      }\r\n      if (data[i].children.length) {\r\n        const obj = menuOpenPage(data[i].children)\r\n        defaultObj = obj.id ? obj : defaultObj\r\n      }\r\n    }\r\n    return defaultObj\r\n  }\r\n  const menuId = ref('')\r\n  const menuData = ref([])\r\n  const menuClick = (item) => {\r\n    handleCloseOther(item.id)\r\n    // 左侧菜单点击事件\r\n    if (!tabData.value.length) {\r\n      qiankunActions.setGlobalState({ keepAliveRoute: [] })\r\n    }\r\n    if (!tabData.value.map((v) => v.id).includes(item.id)) {\r\n      tabData.value.push(item)\r\n    }\r\n    tabClick()\r\n  }\r\n  const WorkBenchReturn = () => {\r\n    // 工作台具体应用返回工作台\r\n    if (isChildView.value) {\r\n      isView.value = false\r\n      isChildView.value = false\r\n      handleClick()\r\n    }\r\n  }\r\n  const handleBreadcrumb = (item, index) => {\r\n    if (index + 1 === tabData.value.length) return\r\n    const newTabData = tabData.value.slice(0, index + 1)\r\n    const delTabData = tabData.value.slice(index + 1).map((v) => v.id)\r\n    tabData.value = newTabData\r\n    isTabData.value = isTabData.value.filter((item) => !delTabData.includes(item.id))\r\n    const mainAppList = tabData.value.filter(\r\n      (v) => routePath(v.routePath.substring(0, v.routePath.indexOf('?')) || v.routePath) === '/'\r\n    )\r\n    const mainAppName = Array.from(new Set(mainAppList.map((v) => mainRoutePath(v.routePath))))\r\n    keepAliveRoute.value = mainAppName\r\n    menuId.value = item.id\r\n    tabClick()\r\n  }\r\n\r\n  const tabData = ref([]) // tab数据\r\n  const isTabData = ref([])\r\n  const isMicroApp = ref('')\r\n  const noMicroApp = ref([])\r\n  const keepAliveRoute = ref([])\r\n  const add_msg = (a, b) => a.filter((v) => b.indexOf(v) === -1)\r\n  // const delete_msg = (a, b) => b.filter(v => a.indexOf(v) === -1)\r\n  const tabClick = () => {\r\n    const microAppName = Object.keys(config.microApp)\r\n    const MicroAppData = Array.from(new Set(tabData.value.map((v) => routePath(v.routePath)))).filter((v) =>\r\n      microAppName.includes(v)\r\n    )\r\n    const addMicroApp = add_msg(MicroAppData, MicroApp.value)\r\n    // const delMicroApp = delete_msg(MicroAppData, MicroApp.value)\r\n    MicroApp.value = [...MicroApp.value, ...addMicroApp]\r\n    if (!addMicroApp.length) {\r\n      menuRouterPush()\r\n      return\r\n    }\r\n    nextTick(() => {\r\n      for (let i = 0, len = addMicroApp.length; i < len; i++) {\r\n        const v = addMicroApp[i]\r\n        if (!MicroAppObj.value[v]) {\r\n          MicroAppObj.value[v] = loadFilterApp(v)\r\n          MicroAppObj.value[v].loadPromise\r\n            .then(() => {\r\n              MicroAppObj.value[v].mountPromise.then(() => {\r\n                qiankunActions.setGlobalState({\r\n                  theme: store.getters.getThemeFn,\r\n                  user: store.getters.getUserFn,\r\n                  menu: store.getters.getMenuFn,\r\n                  area: store.getters.getAreaFn,\r\n                  role: store.getters.getRoleFn,\r\n                  readConfig: store.getters.getReadConfig,\r\n                  readOpenConfig: store.getters.getReadOpenConfig\r\n                })\r\n              })\r\n            })\r\n            .catch((err) => {\r\n              noMicroApp.value.push(v)\r\n            })\r\n        }\r\n      }\r\n      setTimeout(() => {\r\n        menuRouterPush()\r\n      }, 52)\r\n    })\r\n  }\r\n  const menuRouterPush = () => {\r\n    for (let i = 0, len = tabData.value.length; i < len; i++) {\r\n      const item = tabData.value[i]\r\n      if (menuId.value === item.id) {\r\n        sessionStorage.setItem('has', JSON.stringify(item))\r\n        const menuUrl = item.routePath.substring(0, item.routePath.indexOf('?')) || item.routePath\r\n        const getMicroName = routePath(menuUrl)\r\n        isMicroApp.value = getMicroName\r\n        if (MicroApp.value.includes(getMicroName) && !noMicroApp.value.includes(getMicroName)) {\r\n          const query = { menuRouteType: item.menuRouteType?.value, ...routePathData(item.routePath), ...item.query }\r\n          router.push({ path: item.routePath, query: query })\r\n          MicroAppObj.value[getMicroName].mountPromise.then(() => {\r\n            qiankunActions.setGlobalState({ keepAliveRoute: tabData.value.map((v) => v.routePath) })\r\n          })\r\n        } else {\r\n          if (getMicroName === '/') {\r\n            const query = { menuRouteType: item.menuRouteType?.value, ...routePathData(item.routePath), ...item.query }\r\n            router.push({ path: item.routePath, query: query })\r\n            const mainAppList = tabData.value.filter(\r\n              (v) => routePath(v.routePath.substring(0, v.routePath.indexOf('?')) || v.routePath) === '/'\r\n            )\r\n            const mainAppName = Array.from(new Set(mainAppList.map((v) => mainRoutePath(v.routePath))))\r\n            keepAliveRoute.value = mainAppName\r\n          } else {\r\n            router.push({ path: '/NotFoundPage' })\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n  const mainRoutePath = (url) => {\r\n    let path = ''\r\n    const start = url.indexOf('/') + 1\r\n    const end = url.indexOf('?')\r\n    if (end === -1) {\r\n      path = url.substring(1)\r\n    } else {\r\n      path = url.substring(start, end)\r\n    }\r\n    return path\r\n  }\r\n  const routePathData = (href) => {\r\n    let params = {}\r\n    href = href.substring(href.indexOf('?') + 1)\r\n    let arr = href.split('&')\r\n    arr.forEach((item) => {\r\n      let a = item.split('=')\r\n      params[a[0]] = a[1]\r\n    })\r\n    return params\r\n  }\r\n  // 获取第一个斜杠和第二个斜杠之间的内容\r\n  const routePath = (url) => {\r\n    let path = '' // 第二个斜杠前内容\r\n    const first = url.indexOf('/') + 1 // 从第一个斜杠算起（+1表示不包括该斜杠）\r\n    const kong = url.indexOf(' ', first) // 第一个斜杠后的第一个空格\r\n    const heng = url.indexOf('/', first) // 第一个斜杠后的第一个斜杠（即第二个斜杠）\r\n    if (heng === -1) {\r\n      path = url.substring(1, kong)\r\n    } else {\r\n      path = url.substring(1, heng)\r\n    }\r\n    return path\r\n  }\r\n  watch(\r\n    () => store.state.openRoute,\r\n    (val) => {\r\n      if (val.path) {\r\n        openRoute(val)\r\n      }\r\n    },\r\n    { immediate: true }\r\n  )\r\n  watch(\r\n    () => store.state.closeOpenRoute,\r\n    (val) => {\r\n      if (val.closeId) {\r\n        delRoute(val)\r\n      }\r\n    },\r\n    { immediate: true }\r\n  )\r\n\r\n  const openRoute = (val) => {\r\n    if (isTabData.value.map((v) => v.isData).includes(JSON.stringify(val))) {\r\n      for (let i = 0, len = isTabData.value.length; i < len; i++) {\r\n        const item = isTabData.value[i]\r\n        if (item.isData === JSON.stringify(val)) {\r\n          menuId.value = item.id\r\n          tabClick()\r\n        }\r\n      }\r\n    } else {\r\n      const id = guid()\r\n      isTabData.value.push({ id: id, isData: JSON.stringify(val) })\r\n      tabData.value.push({\r\n        id,\r\n        name: val.name,\r\n        routePath: val.path,\r\n        query: { ...val.query, routeId: id, oldRouteId: menuId.value }\r\n      })\r\n      menuId.value = id\r\n      tabClick()\r\n    }\r\n    qiankunActions.setGlobalState({ openRoute: { name: '', path: '', query: {} } })\r\n  }\r\n  const delRoute = (val) => {\r\n    if (val.openId) {\r\n      isTabData.value = isTabData.value.filter((item) => item.id !== val.closeId)\r\n      tabData.value = tabData.value.filter((item) => item.id !== val.closeId)\r\n      menuId.value = val.openId\r\n      tabClick()\r\n    } else {\r\n      handleClose(val.closeId)\r\n    }\r\n    qiankunActions.setGlobalState({ closeOpenRoute: { openId: '', closeId: '' } })\r\n  }\r\n  const isRefresh = ref(true)\r\n  const handleRefresh = (id) => {\r\n    if (route.meta.moduleName === 'main') {\r\n      keepAliveRoute.value = keepAliveRoute.value.filter((v) => v !== route.name)\r\n      isRefresh.value = false\r\n      setTimeout(() => {\r\n        keepAliveRoute.value.push(route.name)\r\n        isRefresh.value = true\r\n      }, 200)\r\n    } else {\r\n      for (let i = 0, len = tabData.value.length; i < len; i++) {\r\n        const item = tabData.value[i]\r\n        if (item.id === id) {\r\n          qiankunActions.setGlobalState({ refreshRoute: item.routePath })\r\n          setTimeout(() => {\r\n            qiankunActions.setGlobalState({ refreshRoute: '' })\r\n          }, 222)\r\n        }\r\n      }\r\n    }\r\n  }\r\n  const handleClose = (id) => {\r\n    if (menuId.value === id) {\r\n      for (let i = 0, len = tabData.value.length; i < len; i++) {\r\n        const item = tabData.value[i]\r\n        if (item.id === id) {\r\n          menuId.value = tabData.value[i ? i - 1 : 1].id\r\n          tabClick()\r\n        }\r\n      }\r\n    }\r\n    isTabData.value = isTabData.value.filter((item) => item.id !== id)\r\n    tabData.value = tabData.value.filter((item) => item.id !== id)\r\n  }\r\n  const handleCloseOther = (id) => {\r\n    isTabData.value = isTabData.value.filter((item) => item.id === id)\r\n    tabData.value = tabData.value.filter((item) => item.id === id)\r\n    menuId.value = id\r\n    tabClick()\r\n  }\r\n\r\n  const openPageMenu = (nodes, key, value) => {\r\n    if (!nodes || !nodes.length) return [] // eslint-disable-line\r\n    const children = []\r\n    for (let node of nodes) {\r\n      if (node[key] === value && !openPageId.value) {\r\n        openPageId.value = node.id\r\n        openPageObj.value = node\r\n      }\r\n      node = Object.assign({}, node)\r\n      const sub = openPageMenu(node.children, key, value)\r\n      if ((sub && sub.length) || node[key] === value) {\r\n        sub.length && (node.children = sub)\r\n        children.push(node)\r\n      }\r\n    }\r\n    return children.length ? children : [] // eslint-disable-line\r\n  }\r\n  const openPage = ({ key = 'id', value }) => {\r\n    if (isOpen.value) return\r\n    isOpen.value = true\r\n    const openPagedata = openPageMenu(tabMenuData.value, key, value)[0] || {}\r\n    if (openPagedata.id) {\r\n      if (tabMenu.value === openPagedata.id) {\r\n        if (openPagedata.routePath === '/WorkBench') {\r\n          if (openPagedata.id === openPageId.value) {\r\n            openPageId.value = ''\r\n            openPageObj.value = {}\r\n            WorkBenchReturn()\r\n            return\r\n          }\r\n          if (openPagedata.children[0]?.id === childData.value.id) {\r\n            menuId.value = openPageId.value\r\n            menuClick(openPageObj.value)\r\n            openPageId.value = ''\r\n            openPageObj.value = {}\r\n            nextTick(() => {\r\n              if (isOpen.value) {\r\n                isOpen.value = false\r\n              }\r\n            })\r\n          } else {\r\n            WorkBenchReturn()\r\n            setTimeout(() => {\r\n              nextTick(() => {\r\n                openPageChildId.value = openPagedata.children[0]?.id\r\n                handleClick()\r\n                nextTick(() => {\r\n                  if (isOpen.value) {\r\n                    isOpen.value = false\r\n                  }\r\n                })\r\n              })\r\n            }, 200)\r\n          }\r\n        } else {\r\n          if (openPagedata.routePath.includes('/GlobalHome')) {\r\n            handleClick()\r\n            nextTick(() => {\r\n              if (isOpen.value) {\r\n                isOpen.value = false\r\n              }\r\n            })\r\n            return\r\n          }\r\n          menuId.value = openPageId.value\r\n          menuClick(openPageObj.value)\r\n          openPageId.value = ''\r\n          openPageObj.value = {}\r\n          nextTick(() => {\r\n            if (isOpen.value) {\r\n              isOpen.value = false\r\n            }\r\n          })\r\n        }\r\n      } else {\r\n        if (isChildView.value) {\r\n          WorkBenchReturn()\r\n          setTimeout(() => {\r\n            nextTick(() => {\r\n              tabMenu.value = openPagedata.id\r\n              if (openPagedata.id === openPageId.value) {\r\n                openPageId.value = ''\r\n                openPageObj.value = {}\r\n              } else {\r\n                openPageChildId.value = openPagedata.children[0]?.id\r\n              }\r\n              handleClick()\r\n              nextTick(() => {\r\n                if (isOpen.value) {\r\n                  isOpen.value = false\r\n                }\r\n              })\r\n            })\r\n          }, 200)\r\n        } else {\r\n          tabMenu.value = openPagedata.id\r\n          if (openPagedata.id === openPageId.value) {\r\n            openPageId.value = ''\r\n            openPageObj.value = {}\r\n          } else {\r\n            openPageChildId.value = openPagedata.children[0]?.id\r\n          }\r\n          handleClick()\r\n          nextTick(() => {\r\n            if (isOpen.value) {\r\n              isOpen.value = false\r\n            }\r\n          })\r\n        }\r\n      }\r\n    } else {\r\n      isOpen.value = false\r\n      ElMessage({ type: 'warning', message: '未检测到你有此菜单！' })\r\n    }\r\n  }\r\n  const setOpenPageId = (id) => {\r\n    openPageId.value = id\r\n  }\r\n\r\n  const guid = () => {\r\n    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\r\n      var r = (Math.random() * 16) | 0,\r\n        v = c == 'x' ? r : (r & 0x3) | 0x8\r\n      return v.toString(16)\r\n    })\r\n  }\r\n\r\n  onUnmounted(() => {\r\n    for (let i = 0, len = MicroApp.value.length; i < len; i++) {\r\n      const v = MicroApp.value[i]\r\n      MicroAppObj.value[v].unmount()\r\n      MicroAppObj.value[v].unmountPromise.then(() => {\r\n        MicroAppObj.value[v] = null\r\n      })\r\n    }\r\n    MicroApp.value = []\r\n  })\r\n\r\n  provide('WorkBenchList', WorkBenchList)\r\n  provide('WorkBenchMenu', WorkBenchMenu)\r\n  provide('leftMenuData', leftMenuData)\r\n  provide('setOpenPageId', setOpenPageId)\r\n  provide('openPage', openPage)\r\n  provide('openRoute', openRoute)\r\n  provide('delRoute', delRoute)\r\n  provide('regionId', regionId)\r\n  provide('regionSelect', regionSelect)\r\n  provide('area', area)\r\n  return {\r\n    user, area, role, left, width, LayoutViewBox, LayoutViewInfo, helpShow, handleCommand,\r\n    editPassWordShow, verifyEditPassWord, verifyEditPassWordShow, editPassWordCallback,\r\n    regionId, regionName, regionSelect, isRegionSelectShow, isView, isChildView, tabMenu, tabMenuData, handleClick,\r\n    menuId, menuData, menuClick, handleBreadcrumb, WorkBenchObj, WorkBenchList, childData, WorkBenchReturn,\r\n    isRefresh, keepAliveRoute, tabData, tabClick, handleRefresh, handleClose, handleCloseOther,\r\n    isMicroApp, MicroApp, openPage, leftMenuData\r\n  }\r\n}\r\nexport const qiankun = (route) => {\r\n  const isMain = ref(false)\r\n  const isMainPage = () => {\r\n    isMain.value = route.meta.moduleName === 'main'\r\n  }\r\n\r\n  watch(\r\n    () => route,\r\n    () => {\r\n      isMainPage()\r\n    },\r\n    { deep: true, immediate: true }\r\n  )\r\n  onMounted(() => {\r\n    isMainPage(route)\r\n  })\r\n  return { isMain }\r\n}\r\n\r\nexport const ChatMethod = () => {\r\n  const store = useStore()\r\n  const rongCloudToken = computed(() => store.getters.getRongCloudToken)\r\n  return { rongCloudToken }\r\n}\r\n\r\nexport const AiChatMethod = () => {\r\n  const store = useStore()\r\n  const AiChatWidth = computed(() => store.state.AiChatWidth)\r\n  const AiChatTargetWidth = computed(() => `${AiChatWidth.value}px`)\r\n  const AiChatViewType = ref(false)\r\n  const AiChatWindowShow = ref(false)\r\n  // 自动吸附到最近的侧边\r\n  const handleResizeFloatingWindow = () => {\r\n    if (window.innerWidth > 1280 + 400) {\r\n      const width = window.innerWidth - 1280 > 520 ? 520 : 400\r\n      store.commit('setAiChatWidth', width)\r\n      if (!AiChatViewType.value) AiChatWindowShow.value = false\r\n      AiChatViewType.value = true\r\n    } else {\r\n      store.commit('setAiChatWidth', 400)\r\n      if (AiChatViewType.value) AiChatWindowShow.value = false\r\n      AiChatViewType.value = false\r\n    }\r\n  }\r\n  onMounted(() => {\r\n    handleResizeFloatingWindow()\r\n    window.addEventListener('resize', handleResizeFloatingWindow)\r\n  })\r\n  onUnmounted(() => {\r\n    window.removeEventListener('resize', handleResizeFloatingWindow)\r\n  })\r\n  return { AiChatTargetWidth, AiChatViewType, AiChatWindowShow }\r\n}\r\n\r\nexport const loginHintMethod = () => {\r\n  const store = useStore()\r\n  const user = computed(() => store.state.user)\r\n  const openConfig = computed(() => store.getters.getReadOpenConfig)\r\n  const systemPlatform = computed(() => openConfig.value?.systemPlatform)\r\n  const loginHintShow = ref(false)\r\n  const hasDuplicates = (arr1, arr2) => {\r\n    const set = new Set(arr1)\r\n    return arr2.some((item) => set.has(item))\r\n  }\r\n  const getLoginHintConfig = async () => {\r\n    const { data } = await api.globalReadOpenConfig({ codes: ['loginPopShow'] })\r\n    if (data.loginPopShow) {\r\n      loginHintShow.value = data.loginPopShow == 'true'\r\n    } else {\r\n      loginHintShow.value = false\r\n    }\r\n  }\r\n  onMounted(() => { })\r\n  watch(\r\n    () => user.value,\r\n    () => {\r\n      if (user.value.id) {\r\n        const specialRoleKeys = user.value.specialRoleKeys || []\r\n        const npcRoleKeys = ['npc_contact_committee', 'suggestion_office_user', 'delegation_manager', 'npc_member']\r\n        const cppccRoleKeys = ['proposal_committee', 'suggestion_office_user', 'cppcc_member']\r\n        const canSee = systemPlatform.value === 'CPPCC' ? cppccRoleKeys : npcRoleKeys\r\n        loginHintShow.value = hasDuplicates(specialRoleKeys, canSee)\r\n        if (loginHintShow.value) getLoginHintConfig()\r\n      }\r\n    },\r\n    { immediate: true }\r\n  )\r\n  return { loginHintShow }\r\n}\r\n"], "mappings": ";;;;;;;;;;;;+CACA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,SAASE,GAAG,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,QAAQ,KAAK;AACrF,OAAOC,GAAG,MAAM,OAAO;AACvB,SAASC,QAAQ,QAAQ,MAAM;AAC/B,OAAOC,MAAM,MAAM,eAAe;AAClC,SAASC,cAAc,EAAEC,aAAa,QAAQ,WAAW;AACzD,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,oBAAoB,QAAQ,wBAAwB;AAC7D,SAASC,eAAe,EAAEC,kBAAkB,QAAQ,iBAAiB;AACrE,OAAOC,0BAA0B,MAAM,yBAAyB;AAChE,OAAOC,YAAY,MAAM,gCAAgC;AACzD,SAASC,SAAS,EAAEC,YAAY,QAAQ,cAAc;AACtD,OAAO,IAAMC,WAAW,GAAG,03BAA03B;AAEr5B,OAAO,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAIC,KAAK,EAAEC,MAAM,EAAK;EAC3C,IAAMC,KAAK,GAAGhB,QAAQ,CAAC,CAAC;EACxB,IAAMiB,QAAQ,GAAG,GAAGhB,MAAM,CAACiB,OAAO,wBAAwB;EAE1D,IAAMC,GAAG,GAAGX,0BAA0B,CAAC,CAAC;EACxC,IAAMY,IAAI,GAAG5B,GAAG,CAAC,CAAC,CAAC;EACnB,IAAM6B,KAAK,GAAG7B,GAAG,CAAC,EAAE,CAAC;EACrB,IAAM8B,aAAa,GAAG9B,GAAG,CAAC,IAAI,CAAC;EAC/B,IAAM+B,cAAc,GAAG/B,GAAG,CAAC,IAAI,CAAC;EAChC,IAAMgC,gBAAgB,GAAGhC,GAAG,CAAC,KAAK,CAAC;EACnC,IAAMiC,kBAAkB,GAAGjC,GAAG,CAAC,EAAE,CAAC;EAClC,IAAMkC,sBAAsB,GAAGlC,GAAG,CAAC,KAAK,CAAC;EACzC,IAAMmC,QAAQ,GAAGnC,GAAG,CAAC,EAAE,CAAC;EACxB,IAAMoC,WAAW,GAAGpC,GAAG,CAAC,CAAC,CAAC,CAAC;EAC3B;EACA,IAAMqC,IAAI,GAAGlC,QAAQ,CAAC;IAAA,OAAMqB,KAAK,CAACc,OAAO,CAACC,SAAS;EAAA,EAAC;EACpD;EACA,IAAMC,IAAI,GAAGrC,QAAQ,CAAC;IAAA,OAAMqB,KAAK,CAACc,OAAO,CAACG,SAAS;EAAA,EAAC;EACpD;EACA,IAAMC,IAAI,GAAGvC,QAAQ,CAAC;IAAA,OAAMqB,KAAK,CAACc,OAAO,CAACK,SAAS;EAAA,EAAC;EACpD,IAAMC,UAAU,GAAGzC,QAAQ,CAAC;IAAA,OAAMqB,KAAK,CAACc,OAAO,CAACO,iBAAiB;EAAA,EAAC;EAClE,IAAMC,QAAQ,GAAG9C,GAAG,CAAC,KAAK,CAAC;EAE3BE,SAAS,CAAC,YAAM;IACd6C,kBAAkB,CAAC,CAAC;IACpB3C,QAAQ,CAAC,YAAM;MACbuB,GAAG,CAACqB,QAAQ,CAAClB,aAAa,CAAClI,KAAK,EAAE,UAACqJ,OAAO,EAAK;QAC7CrB,IAAI,CAAChI,KAAK,GAAGqJ,OAAO,CAACC,WAAW;MAClC,CAAC,CAAC;MACFvB,GAAG,CAACqB,QAAQ,CAACjB,cAAc,CAACnI,KAAK,EAAE,UAACqJ,OAAO,EAAK;QAC9CpB,KAAK,CAACjI,KAAK,GAAG,sBAAsBqJ,OAAO,CAACC,WAAW,GAAG,EAAE,MAAM;MACpE,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,IAAMH,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;IAAA,IAAAI,WAAA,EAAAC,YAAA;IAC/B,IAAMC,aAAa,GAAGC,cAAc,CAACC,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE;IACnE,IAAMC,cAAc,GAAGF,cAAc,CAACC,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE;IACrE,IAAI,EAAAJ,WAAA,GAAAd,IAAI,CAACzI,KAAK,cAAAuJ,WAAA,uBAAVA,WAAA,CAAYM,SAAS,MAAK,GAAG,IAAI,EAAAL,YAAA,GAAAf,IAAI,CAACzI,KAAK,cAAAwJ,YAAA,uBAAVA,YAAA,CAAYM,SAAS,IAAG,CAAC,IAAIL,aAAa,IAAI,CAACG,cAAc,EAAE;MAClGG,kBAAkB,CAAC/J,KAAK,GAAG,IAAI;IACjC;EACF,CAAC;EACD,IAAMgK,aAAa,GAAG,SAAhBA,aAAaA,CAAI7I,IAAI,EAAK;IAC9B,IAAIA,IAAI,KAAK,MAAM,EAAE;MACnByG,KAAK,CAACqC,MAAM,CAAC,4BAA4B,EAAE;QAAEC,IAAI,EAAE;MAAK,CAAC,CAAC;IAC5D,CAAC,MAAM,IAAI/I,IAAI,KAAK,SAAS,EAAE;MAC7B;MACA;MACAgJ,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,GAAGxD,MAAM,CAACyD,QAAQ,MAAM,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,EAAE;IACvE,CAAC,MAAM,IAAIrJ,IAAI,KAAK,QAAQ,EAAE;MAC5B,IAAI+F,eAAe,CAAC,CAAC,KAAK,IAAI,EAAE,OAAOC,kBAAkB,CAAC,IAAI,CAAC;MAC/D,IAAID,eAAe,CAAC,CAAC,KAAK,IAAI,EAAE,OAAOC,kBAAkB,CAAC,IAAI,CAAC;IACjE,CAAC,MAAM,IAAIhG,IAAI,KAAK,MAAM,EAAE;MAC1B+H,QAAQ,CAAClJ,KAAK,GAAG,IAAI;IACvB,CAAC,MAAM,IAAImB,IAAI,KAAK,eAAe,EAAE;MACnCkH,kBAAkB,CAACrI,KAAK,GAAG,EAAE;MAC7BoI,gBAAgB,CAACpI,KAAK,GAAG,IAAI;IAC/B,CAAC,MAAM,IAAImB,IAAI,KAAK,MAAM,EAAE;MAC1BsJ,UAAU,CAAC,CAAC;IACd,CAAC,MAAM;MACLnD,SAAS,CAAC;QAAEnG,IAAI,EAAE,MAAM;QAAEuJ,OAAO,EAAE;MAAS,CAAC,CAAC;IAChD;EACF,CAAC;EACD,IAAMC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAIxJ,IAAI,EAAK;IACrC,IAAIA,IAAI,EAAE;MACRyJ,QAAQ,CAAC,aAAa,CAAC;IACzB;IACAxC,gBAAgB,CAACpI,KAAK,GAAG,KAAK;EAChC,CAAC;EACD,IAAMyK,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;IACvBlD,YAAY,CAACsD,OAAO,CAAC,mBAAmB,EAAE,IAAI,EAAE;MAC9CC,iBAAiB,EAAE,IAAI;MACvBC,gBAAgB,EAAE,IAAI;MACtB5J,IAAI,EAAE;IACR,CAAC,CAAC,CACCuB,IAAI,CAAC,YAAM;MACVkI,QAAQ,CAAC,QAAQ,CAAC;IACpB,CAAC,CAAC,CACDjF,KAAK,CAAC,YAAM;MACX2B,SAAS,CAAC;QAAEnG,IAAI,EAAE,MAAM;QAAEuJ,OAAO,EAAE;MAAQ,CAAC,CAAC;IAC/C,CAAC,CAAC;EACN,CAAC;EACD,IAAME,QAAQ;IAAA,IAAAI,IAAA,GAAAjF,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAuG,QAAOC,IAAI;MAAA,IAAAC,mBAAA,EAAAC,IAAA,EAAAC,sBAAA,EAAAC,uBAAA;MAAA,OAAAhM,mBAAA,GAAAuB,IAAA,UAAA0K,SAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAArG,IAAA,GAAAqG,QAAA,CAAAhI,IAAA;UAAA;YAAAgI,QAAA,CAAAhI,IAAA;YAAA,OACHmD,GAAG,CAACiE,QAAQ,CAAC,CAAC;UAAA;YAAAO,mBAAA,GAAAK,QAAA,CAAAvI,IAAA;YAA7BmI,IAAI,GAAAD,mBAAA,CAAJC,IAAI;YACZ,IAAIA,IAAI,KAAK,GAAG,EAAE;cAChB1B,cAAc,CAAC+B,KAAK,CAAC,CAAC;cAChBJ,sBAAsB,GAAGK,YAAY,CAAC/B,OAAO,CAAC,wBAAwB,CAAC;cAC7E,IAAI0B,sBAAsB,EAAE;gBACpBC,uBAAuB,GAAGI,YAAY,CAAC/B,OAAO,CAAC,yBAAyB,CAAC,IAAI,EAAE;gBACrFhC,MAAM,CAAC3D,IAAI,CAAC;kBACV2H,IAAI,EAAEN,sBAAsB;kBAC5BO,KAAK,EAAEN,uBAAuB,GAAGO,IAAI,CAACC,KAAK,CAACR,uBAAuB,CAAC,GAAG,CAAC;gBAC1E,CAAC,CAAC;cACJ,CAAC,MAAM;gBACL3D,MAAM,CAAC3D,IAAI,CAAC;kBAAE2H,IAAI,EAAE;gBAAa,CAAC,CAAC;cACrC;cACA/D,KAAK,CAACqC,MAAM,CAAC,UAAU,CAAC;cACxBhD,oBAAoB,CAAC,CAAC;cACtB;cACA;cACAK,SAAS,CAAC;gBAAEoD,OAAO,EAAEQ,IAAI;gBAAEa,SAAS,EAAE,IAAI;gBAAE5K,IAAI,EAAE;cAAU,CAAC,CAAC;YAChE;UAAC;UAAA;YAAA,OAAAqK,QAAA,CAAAlG,IAAA;QAAA;MAAA,GAAA2F,OAAA;IAAA,CACF;IAAA,gBApBKL,QAAQA,CAAAoB,EAAA;MAAA,OAAAhB,IAAA,CAAA/E,KAAA,OAAAD,SAAA;IAAA;EAAA,GAoBb;;EAED;EACA,IAAMiG,QAAQ,GAAG7F,GAAG,CAAC,EAAE,CAAC;EACxB,IAAM8F,UAAU,GAAG9F,GAAG,CAAC,EAAE,CAAC;EAC1B,IAAM2D,kBAAkB,GAAG3D,GAAG,CAAC,KAAK,CAAC;EACrC,IAAM+F,YAAY,GAAG,SAAfA,YAAYA,CAAIC,IAAI,EAAK;IAAA,IAAAC,YAAA;IAC7BH,UAAU,CAAClM,KAAK,GAAGoM,IAAI,CAAC3H,IAAI;IAC5BsF,kBAAkB,CAAC/J,KAAK,GAAG,KAAK;IAChC0J,cAAc,CAAC4C,OAAO,CAAC,SAAS,EAAET,IAAI,CAACU,SAAS,CAACH,IAAI,CAAC,CAAC;IACvD,IAAI,EAAAC,YAAA,GAAA5D,IAAI,CAACzI,KAAK,cAAAqM,YAAA,uBAAVA,YAAA,CAAYG,MAAM,MAAKJ,IAAI,CAACK,EAAE,EAAE;IACpCC,eAAe,CAACN,IAAI,CAAC;EACvB,CAAC;EACD,IAAMM,eAAe;IAAA,IAAAC,KAAA,GAAA5G,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAkI,SAAOR,IAAI;MAAA,IAAAS,qBAAA,EAAAzB,IAAA,EAAA0B,YAAA;MAAA,OAAAxN,mBAAA,GAAAuB,IAAA,UAAAkM,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAA7H,IAAA,GAAA6H,SAAA,CAAAxJ,IAAA;UAAA;YAAAwJ,SAAA,CAAAxJ,IAAA;YAAA,OACVmD,GAAG,CAAC+F,eAAe,CAAC,CAAC,CAAC,EAAEN,IAAI,CAACK,EAAE,CAAC;UAAA;YAAAI,qBAAA,GAAAG,SAAA,CAAA/J,IAAA;YAA/CmI,IAAI,GAAAyB,qBAAA,CAAJzB,IAAI;YACZ,IAAIA,IAAI,KAAK,GAAG,EAAE;cAChB6B,OAAO,CAACjN,KAAK,GAAG,EAAE;cAClB0J,cAAc,CAAC4C,OAAO,CAAC,QAAQ,EAAEF,IAAI,CAACK,EAAE,CAAC;cACzCxF,oBAAoB,CAAC,CAAC;cACtBW,KAAK,CAACsF,QAAQ,CAAC,WAAW,EAAE,OAAO,CAAC;cACpCtF,KAAK,CAACqC,MAAM,CAAC,sBAAsB,EAAE,IAAI,CAAC;cAC1CrC,KAAK,CAACqC,MAAM,CAAC,sBAAsB,EAAE,IAAI,CAAC;YAC5C,CAAC,MAAM;cACLgC,QAAQ,CAACjM,KAAK,IAAA8M,YAAA,GAAGrE,IAAI,CAACzI,KAAK,cAAA8M,YAAA,uBAAVA,YAAA,CAAYN,MAAM;cACnCnF,YAAY,CAAC;gBAAE5C,IAAI,EAAE2H,IAAI,CAAC3H;cAAK,CAAC,CAAC;YACnC;UAAC;UAAA;YAAA,OAAAuI,SAAA,CAAA1H,IAAA;QAAA;MAAA,GAAAsH,QAAA;IAAA,CACF;IAAA,gBAbKF,eAAeA,CAAAS,GAAA;MAAA,OAAAR,KAAA,CAAA1G,KAAA,OAAAD,SAAA;IAAA;EAAA,GAapB;EACDK,KAAK,CACH;IAAA,OAAMoC,IAAI,CAACzI,KAAK;EAAA,GAChB,YAAM;IAAA,IAAAoN,YAAA,EAAAC,YAAA;IACJpB,QAAQ,CAACjM,KAAK,IAAAoN,YAAA,GAAG3E,IAAI,CAACzI,KAAK,cAAAoN,YAAA,uBAAVA,YAAA,CAAYZ,MAAM;IACnC,KAAAa,YAAA,GAAI5E,IAAI,CAACzI,KAAK,cAAAqN,YAAA,eAAVA,YAAA,CAAYxD,SAAS,EAAE;MAAA,IAAAyD,YAAA;MACzB,IAAMC,MAAM,GAAG7D,cAAc,CAACC,OAAO,CAAC,QAAQ,CAAC;MAC/C,IAAI4D,MAAM,IAAI,CAACC,MAAM,CAACD,MAAM,CAAC,IAAI,EAAAD,YAAA,GAAA7E,IAAI,CAACzI,KAAK,cAAAsN,YAAA,uBAAVA,YAAA,CAAYzD,SAAS,MAAK,GAAG,EAAE;QAAA,IAAA4D,iBAAA;QAC9D,IAAI,EAAAA,iBAAA,GAAAzE,UAAU,CAAChJ,KAAK,cAAAyN,iBAAA,uBAAhBA,iBAAA,CAAkBC,kBAAkB,MAAK,MAAM,EAAE;UACnDlH,QAAQ,CAAC,YAAM;YACb6B,kBAAkB,CAACrI,KAAK,GAAG,KAAK;YAChCsI,sBAAsB,CAACtI,KAAK,GAAG,IAAI;UACrC,CAAC,CAAC;QACJ,CAAC,MAAM;UACLwG,QAAQ,CAAC,YAAM;YACb6B,kBAAkB,CAACrI,KAAK,GAAG,IAAI;YAC/BoI,gBAAgB,CAACpI,KAAK,GAAG,IAAI;UAC/B,CAAC,CAAC;QACJ;MACF;IACF;EACF,CAAC,EACD;IAAE2N,SAAS,EAAE;EAAK,CACpB,CAAC;EACD;EACA,IAAMC,WAAU,GAAG,SAAbA,UAAUA,CAAIC,QAAQ,EAAK;IAC/B,IAAIC,WAAW,GAAG,EAAE;IACpB,KAAK,IAAI7N,CAAC,GAAG,CAAC,EAAE8N,GAAG,GAAGF,QAAQ,CAACxJ,MAAM,EAAEpE,CAAC,GAAG8N,GAAG,EAAE9N,CAAC,EAAE,EAAE;MACnD6N,WAAW,CAAC9J,IAAI,CAAC;QACfyI,EAAE,EAAEoB,QAAQ,CAAC5N,CAAC,CAAC,CAAC+N,MAAM;QACtBvJ,IAAI,EAAEoJ,QAAQ,CAAC5N,CAAC,CAAC,CAACwE,IAAI;QACtBwJ,SAAS,EAAEJ,QAAQ,CAAC5N,CAAC,CAAC,CAACgO,SAAS;QAChCC,YAAY,EAAEL,QAAQ,CAAC5N,CAAC,CAAC,CAACiO,YAAY;QACtCC,aAAa,EAAEN,QAAQ,CAAC5N,CAAC,CAAC,CAACkO,aAAa;QACxCC,IAAI,EAAEP,QAAQ,CAAC5N,CAAC,CAAC,CAACoO,OAAO,GAAG,GAAG1H,GAAG,CAAC2H,OAAO,CAACT,QAAQ,CAAC5N,CAAC,CAAC,CAACoO,OAAO,CAAC,EAAE,GAAGxG,QAAQ;QAC5E0G,GAAG,EAAEV,QAAQ,CAAC5N,CAAC,CAAC,CAACuO,WAAW;QAC5BC,QAAQ,EAAEb,WAAU,CAACC,QAAQ,CAAC5N,CAAC,CAAC,CAACwO,QAAQ,IAAI,EAAE;MACjD,CAAC,CAAC;IACJ;IACA,OAAOX,WAAW;EACpB,CAAC;EACD;EACA,IAAMb,OAAO,GAAG7G,GAAG,CAAC,EAAE,CAAC;EACvB;EACA,IAAMsI,WAAW,GAAGnI,QAAQ,CAAC;IAAA,OAAMqH,WAAU,CAAChG,KAAK,CAACc,OAAO,CAACiG,SAAS,IAAI,EAAE,CAAC;EAAA,EAAC;EAC7E;EACA,IAAMC,MAAM,GAAGxI,GAAG,CAAC,KAAK,CAAC;EACzB;EACA,IAAMyI,WAAW,GAAGzI,GAAG,CAAC,KAAK,CAAC;EAC9B;EACA,IAAM0I,MAAM,GAAG1I,GAAG,CAAC,KAAK,CAAC;EACzB;EACA,IAAM2I,UAAU,GAAG3I,GAAG,CAAC,EAAE,CAAC;EAC1B,IAAM4I,WAAW,GAAG5I,GAAG,CAAC,CAAC,CAAC,CAAC;EAC3B;EACA,IAAM6I,eAAe,GAAG7I,GAAG,CAAC,EAAE,CAAC;EAC/B;EACA,IAAM8I,YAAY,GAAG9I,GAAG,CAAC,CAAC,CAAC,CAAC;EAC5B;EACA,IAAM+I,aAAa,GAAG/I,GAAG,CAAC,EAAE,CAAC;EAC7B;EACA,IAAMgJ,SAAS,GAAGhJ,GAAG,CAAC,CAAC,CAAC,CAAC;EACzBC,KAAK,CACH;IAAA,OAAMqI,WAAW,CAAC1O,KAAK;EAAA,GACvB,YAAM;IACJ4O,MAAM,CAAC5O,KAAK,GAAG,KAAK;IACpB6O,WAAW,CAAC7O,KAAK,GAAG,KAAK;IACzBkP,YAAY,CAAClP,KAAK,GAAG,CAAC,CAAC;IACvBmP,aAAa,CAACnP,KAAK,GAAG,EAAE;IACxBoP,SAAS,CAACpP,KAAK,GAAG,CAAC,CAAC;IACpB,IAAI0O,WAAW,CAAC1O,KAAK,CAACqE,MAAM,EAAE;MAC5B,IAAMuH,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACpC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC;MAC/D,IAAIiC,KAAK,CAACyD,aAAa,EAAE;QACvBC,QAAQ,CAAC;UAAEC,GAAG,EAAE3D,KAAK,CAAC4D,WAAW,IAAI,IAAI;UAAExP,KAAK,EAAE4L,KAAK,CAACyD;QAAc,CAAC,CAAC;MAC1E,CAAC,MAAM;QACL7I,QAAQ,CAAC,YAAM;UAAA,IAAAiJ,mBAAA;UACbxC,OAAO,CAACjN,KAAK,IAAAyP,mBAAA,GAAGf,WAAW,CAAC1O,KAAK,CAAC,CAAC,CAAC,cAAAyP,mBAAA,uBAApBA,mBAAA,CAAsBhD,EAAE;UACxCiD,WAAW,CAAC,CAAC;QACf,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EACD;IAAE/B,SAAS,EAAE;EAAK,CACpB,CAAC;EACD;EACA,IAAM+B,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;IACxB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;MAC1C,IAAMC,iBAAiB,GAAG9G,UAAU,CAAChJ,KAAK,CAAC+P,gBAAgB,IAAI,EAAE;MACjE,IAAID,iBAAiB,KAAK,MAAM,EAAE9I,oBAAoB,CAAC,CAAC;IAC1D;IACAgH,MAAM,CAAChO,KAAK,GAAG,EAAE;IACjBgQ,QAAQ,CAAChQ,KAAK,GAAG,EAAE;IACnBiQ,OAAO,CAACjQ,KAAK,GAAG,EAAE;IAClBkQ,SAAS,CAAClQ,KAAK,GAAG,EAAE;IACpBmQ,cAAc,CAACnQ,KAAK,GAAG,EAAE;IACzBwG,QAAQ,CAAC,YAAM;MAAA,IAAA4J,KAAA,YAAAA,MAAA,EACiD;UAC5D,IAAMhE,IAAI,GAAGsC,WAAW,CAAC1O,KAAK,CAACC,CAAC,CAAC;UACjC,IAAIgN,OAAO,CAACjN,KAAK,KAAKoM,IAAI,CAACK,EAAE,EAAE;YAC7B/C,cAAc,CAAC4C,OAAO,CAAC,KAAK,EAAET,IAAI,CAACU,SAAS,CAACH,IAAI,CAAC,CAAC;YACnD,IAAI,CAAC,YAAY,EAAE,gBAAgB,CAAC,CAACiE,QAAQ,CAACjE,IAAI,CAAC6B,SAAS,CAAC,EAAE;cAAE;cAC/DW,MAAM,CAAC5O,KAAK,GAAG,KAAK;cACpB2H,MAAM,CAAC3D,IAAI,CAAC;gBAAE2H,IAAI,EAAES,IAAI,CAAC6B,SAAS;gBAAErC,KAAK,EAAE0E,aAAa,CAAClE,IAAI,CAAC6B,SAAS;cAAE,CAAC,CAAC;cAC3EiB,YAAY,CAAClP,KAAK,GAAGoM,IAAI;cACzB+C,aAAa,CAACnP,KAAK,GAAGoM,IAAI,CAACqC,QAAQ;cACnCjI,QAAQ,CAAC,YAAM;gBACb,IAAIyI,eAAe,CAACjP,KAAK,EAAE;kBACzB,IAAIiP,eAAe,CAACjP,KAAK,KAAK+O,UAAU,CAAC/O,KAAK,EAAE;oBAC9C+O,UAAU,CAAC/O,KAAK,GAAG,EAAE;oBACrBgP,WAAW,CAAChP,KAAK,GAAG,CAAC,CAAC;kBACxB;kBACA,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAE4E,MAAM,GAAG+H,IAAI,CAACqC,QAAQ,CAACpK,MAAM,EAAE5E,CAAC,GAAG4E,MAAM,EAAE5E,CAAC,EAAE,EAAE;oBAC9D,IAAI2M,IAAI,CAACqC,QAAQ,CAAChP,CAAC,CAAC,CAACgN,EAAE,KAAKwC,eAAe,CAACjP,KAAK,EAAE;sBACjDuQ,YAAY,CAACnE,IAAI,CAACqC,QAAQ,CAAChP,CAAC,CAAC,CAAC;oBAChC;kBACF;gBACF;cACF,CAAC,CAAC;YACJ,CAAC,MAAM;cACL,IAAI2M,IAAI,CAAC6B,SAAS,CAACoC,QAAQ,CAAC,aAAa,CAAC,EAAE;gBAC1C,IAAItB,UAAU,CAAC/O,KAAK,EAAE;kBACpBkP,YAAY,CAAClP,KAAK,GAAGoM,IAAI;kBACzBmE,YAAY,CAACnE,IAAI,EAAE,KAAK,CAAC;gBAC3B,CAAC,MAAM;kBACLwC,MAAM,CAAC5O,KAAK,GAAG,KAAK;kBACpB2H,MAAM,CAAC3D,IAAI,CAAC;oBAAE2H,IAAI,EAAES,IAAI,CAAC6B,SAAS;oBAAErC,KAAK,EAAE0E,aAAa,CAAClE,IAAI,CAAC6B,SAAS;kBAAE,CAAC,CAAC;gBAC7E;gBAAC;kBAAAjM,CAAA;gBAAA;cAEH;cACA;cACA,IAAIoK,IAAI,CAACqC,QAAQ,IAAIrC,IAAI,CAACqC,QAAQ,CAACpK,MAAM,EAAE;gBACzC;gBACAkM,YAAY,CAACnE,IAAI,EAAE,IAAI,CAAC;cAC1B,CAAC,MAAM;gBACL,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAACiE,QAAQ,CAACjE,IAAI,CAAC+B,aAAa,CAACnO,KAAK,CAAC,EAAE;kBACjD4O,MAAM,CAAC5O,KAAK,GAAG,KAAK;kBACpB6O,WAAW,CAAC7O,KAAK,GAAG,KAAK;kBACzB2H,MAAM,CAAC3D,IAAI,CAAC;oBACV2H,IAAI,EAAES,IAAI,CAAC6B,SAAS;oBACpBrC,KAAK,EAAA4E,aAAA,CAAAA,aAAA,KAAOF,aAAa,CAAClE,IAAI,CAAC6B,SAAS,CAAC;sBAAEE,aAAa,EAAE/B,IAAI,CAAC+B,aAAa,CAACnO;oBAAK;kBACpF,CAAC,CAAC;gBACJ,CAAC,MAAM;kBACL;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACAuQ,YAAY,CAACnE,IAAI,EAAE,IAAI,CAAC;kBACxB;gBACF;cACF;YACF;UACF;QACF,CAAC;QAAAqE,IAAA;MA5DD,KAAK,IAAIxQ,CAAC,GAAG,CAAC,EAAE8N,GAAG,GAAGW,WAAW,CAAC1O,KAAK,CAACqE,MAAM,EAAEpE,CAAC,GAAG8N,GAAG,EAAE9N,CAAC,EAAE;QAAAwQ,IAAA,GAAAL,KAAA;QAAA,IAAAK,IAAA,SAAAA,IAAA,CAAAzO,CAAA;MAAA;IA6D9D,CAAC,CAAC;EACJ,CAAC;EACD,IAAM0O,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,SAAS,EAAEC,eAAe,EAAK;IACpD3D,OAAO,CAACjN,KAAK,GAAG2Q,SAAS;IACzB3C,MAAM,CAAChO,KAAK,GAAG,EAAE;IACjBgQ,QAAQ,CAAChQ,KAAK,GAAG,EAAE;IACnBiQ,OAAO,CAACjQ,KAAK,GAAG,EAAE;IAClBkQ,SAAS,CAAClQ,KAAK,GAAG,EAAE;IACpBmQ,cAAc,CAACnQ,KAAK,GAAG,EAAE;IACzBuQ,YAAY,CAACK,eAAe,CAAC;EAC/B,CAAC;EACD,IAAML,YAAY,GAAG,SAAfA,YAAYA,CAAInE,IAAI,EAAEjL,IAAI,EAAK;IACnC;IACA;IACA,IAAIiL,IAAI,CAACqC,QAAQ,IAAIrC,IAAI,CAACqC,QAAQ,CAACpK,MAAM,EAAE;MACzC;MACA,IAAIlD,IAAI,EAAE;QACRyN,MAAM,CAAC5O,KAAK,GAAG,IAAI;QACnB6O,WAAW,CAAC7O,KAAK,GAAG,KAAK;MAC3B,CAAC,MAAM;QACL4O,MAAM,CAAC5O,KAAK,GAAG,IAAI;QACnB6O,WAAW,CAAC7O,KAAK,GAAG,IAAI;QACxBoP,SAAS,CAACpP,KAAK,GAAGoM,IAAI;MACxB;MACA4D,QAAQ,CAAChQ,KAAK,GAAGoM,IAAI,CAACqC,QAAQ;MAC9B,IAAMoC,GAAG,GAAG9B,UAAU,CAAC/O,KAAK,GAAG8Q,aAAY,CAAC1E,IAAI,CAACqC,QAAQ,CAAC,GAAGsC,YAAW,CAAC3E,IAAI,CAACqC,QAAQ,CAAC;MACvFT,MAAM,CAAChO,KAAK,GAAG6Q,GAAG,CAACpE,EAAE;MACrBuE,SAAS,CAACH,GAAG,CAAC;MACdrK,QAAQ,CAAC,YAAM;QACbuI,UAAU,CAAC/O,KAAK,GAAG,EAAE;QACrBiP,eAAe,CAACjP,KAAK,GAAG,EAAE;MAC5B,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAC,GAAG,EAAE,GAAG,CAAC,CAACqQ,QAAQ,CAACjE,IAAI,CAAC+B,aAAa,CAACnO,KAAK,CAAC,EAAE;QACjD4O,MAAM,CAAC5O,KAAK,GAAG,KAAK;QACpB6O,WAAW,CAAC7O,KAAK,GAAG,IAAI;QACxBoP,SAAS,CAACpP,KAAK,GAAGoM,IAAI;QACtBzE,MAAM,CAAC3D,IAAI,CAAC;UACV2H,IAAI,EAAES,IAAI,CAAC6B,SAAS;UACpBrC,KAAK,EAAA4E,aAAA,CAAAA,aAAA,KAAOF,aAAa,CAAClE,IAAI,CAAC6B,SAAS,CAAC;YAAEE,aAAa,EAAE/B,IAAI,CAAC+B,aAAa,CAACnO;UAAK;QACpF,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAImB,IAAI,EAAE;UACRyN,MAAM,CAAC5O,KAAK,GAAG,KAAK;UACpB6O,WAAW,CAAC7O,KAAK,GAAG,KAAK;QAC3B,CAAC,MAAM;UACL4O,MAAM,CAAC5O,KAAK,GAAG,KAAK;UACpB6O,WAAW,CAAC7O,KAAK,GAAG,IAAI;UACxBoP,SAAS,CAACpP,KAAK,GAAGoM,IAAI;QACxB;QACA4D,QAAQ,CAAChQ,KAAK,GAAG,CAACoM,IAAI,CAAC;QACvB,IAAMyE,IAAG,GAAG9B,UAAU,CAAC/O,KAAK,GAAG8Q,aAAY,CAAC,CAAC1E,IAAI,CAAC,CAAC,GAAG2E,YAAW,CAAC,CAAC3E,IAAI,CAAC,CAAC;QACzE4B,MAAM,CAAChO,KAAK,GAAG6Q,IAAG,CAACpE,EAAE;QACrBuE,SAAS,CAACH,IAAG,CAAC;QACdrK,QAAQ,CAAC,YAAM;UACbuI,UAAU,CAAC/O,KAAK,GAAG,EAAE;UACrBiP,eAAe,CAACjP,KAAK,GAAG,EAAE;QAC5B,CAAC,CAAC;MACJ;IACF;EACF,CAAC;EAED,IAAM+Q,YAAW,GAAG,SAAdA,WAAWA,CAAIE,IAAI,EAAK;IAC5B;IACA,IAAIC,UAAU,GAAG,CAAC,CAAC;IACnB,KAAK,IAAIjR,CAAC,GAAG,CAAC,EAAE8N,GAAG,GAAGkD,IAAI,CAAC5M,MAAM,EAAEpE,CAAC,GAAG8N,GAAG,EAAE9N,CAAC,EAAE,EAAE;MAC/C,IAAIA,CAAC,KAAK,CAAC,EAAE;QACX,IAAIgR,IAAI,CAAChR,CAAC,CAAC,CAACwO,QAAQ,CAACpK,MAAM,KAAK,CAAC,EAAE;UACjC6M,UAAU,GAAGD,IAAI,CAAChR,CAAC,CAAC;QACtB,CAAC,MAAM;UACLiR,UAAU,GAAGH,YAAW,CAACE,IAAI,CAAChR,CAAC,CAAC,CAACwO,QAAQ,CAAC;QAC5C;MACF;IACF;IACA,OAAOyC,UAAU;EACnB,CAAC;EACD,IAAMJ,aAAY,GAAG,SAAfA,YAAYA,CAAIG,IAAI,EAAK;IAC7B;IACA,IAAIC,UAAU,GAAG,CAAC,CAAC;IACnB,KAAK,IAAIjR,CAAC,GAAG,CAAC,EAAE8N,GAAG,GAAGkD,IAAI,CAAC5M,MAAM,EAAEpE,CAAC,GAAG8N,GAAG,EAAE9N,CAAC,EAAE,EAAE;MAC/C,IAAI8O,UAAU,CAAC/O,KAAK,KAAKiR,IAAI,CAAChR,CAAC,CAAC,CAACwM,EAAE,EAAE;QACnCyE,UAAU,GAAGD,IAAI,CAAChR,CAAC,CAAC;MACtB;MACA,IAAIgR,IAAI,CAAChR,CAAC,CAAC,CAACwO,QAAQ,CAACpK,MAAM,EAAE;QAC3B,IAAMwM,GAAG,GAAGC,aAAY,CAACG,IAAI,CAAChR,CAAC,CAAC,CAACwO,QAAQ,CAAC;QAC1CyC,UAAU,GAAGL,GAAG,CAACpE,EAAE,GAAGoE,GAAG,GAAGK,UAAU;MACxC;IACF;IACA,OAAOA,UAAU;EACnB,CAAC;EACD,IAAMlD,MAAM,GAAG5H,GAAG,CAAC,EAAE,CAAC;EACtB,IAAM4J,QAAQ,GAAG5J,GAAG,CAAC,EAAE,CAAC;EACxB,IAAM4K,SAAS,GAAG,SAAZA,SAASA,CAAI5E,IAAI,EAAK;IAC1B+E,gBAAgB,CAAC/E,IAAI,CAACK,EAAE,CAAC;IACzB;IACA,IAAI,CAACwD,OAAO,CAACjQ,KAAK,CAACqE,MAAM,EAAE;MACzByC,cAAc,CAACsK,cAAc,CAAC;QAAEjB,cAAc,EAAE;MAAG,CAAC,CAAC;IACvD;IACA,IAAI,CAACF,OAAO,CAACjQ,KAAK,CAACqR,GAAG,CAAC,UAACrP,CAAC;MAAA,OAAKA,CAAC,CAACyK,EAAE;IAAA,EAAC,CAAC4D,QAAQ,CAACjE,IAAI,CAACK,EAAE,CAAC,EAAE;MACrDwD,OAAO,CAACjQ,KAAK,CAACgE,IAAI,CAACoI,IAAI,CAAC;IAC1B;IACAkF,QAAQ,CAAC,CAAC;EACZ,CAAC;EACD,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;IAC5B;IACA,IAAI1C,WAAW,CAAC7O,KAAK,EAAE;MACrB4O,MAAM,CAAC5O,KAAK,GAAG,KAAK;MACpB6O,WAAW,CAAC7O,KAAK,GAAG,KAAK;MACzB0P,WAAW,CAAC,CAAC;IACf;EACF,CAAC;EACD,IAAM8B,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIpF,IAAI,EAAEqF,KAAK,EAAK;IACxC,IAAIA,KAAK,GAAG,CAAC,KAAKxB,OAAO,CAACjQ,KAAK,CAACqE,MAAM,EAAE;IACxC,IAAMqN,UAAU,GAAGzB,OAAO,CAACjQ,KAAK,CAACqF,KAAK,CAAC,CAAC,EAAEoM,KAAK,GAAG,CAAC,CAAC;IACpD,IAAME,UAAU,GAAG1B,OAAO,CAACjQ,KAAK,CAACqF,KAAK,CAACoM,KAAK,GAAG,CAAC,CAAC,CAACJ,GAAG,CAAC,UAACrP,CAAC;MAAA,OAAKA,CAAC,CAACyK,EAAE;IAAA,EAAC;IAClEwD,OAAO,CAACjQ,KAAK,GAAG0R,UAAU;IAC1BxB,SAAS,CAAClQ,KAAK,GAAGkQ,SAAS,CAAClQ,KAAK,CAAC4R,MAAM,CAAC,UAACxF,IAAI;MAAA,OAAK,CAACuF,UAAU,CAACtB,QAAQ,CAACjE,IAAI,CAACK,EAAE,CAAC;IAAA,EAAC;IACjF,IAAMoF,WAAW,GAAG5B,OAAO,CAACjQ,KAAK,CAAC4R,MAAM,CACtC,UAAC5P,CAAC;MAAA,OAAKiM,SAAS,CAACjM,CAAC,CAACiM,SAAS,CAAC6D,SAAS,CAAC,CAAC,EAAE9P,CAAC,CAACiM,SAAS,CAAC8D,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI/P,CAAC,CAACiM,SAAS,CAAC,KAAK,GAAG;IAAA,CAC7F,CAAC;IACD,IAAM+D,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACN,WAAW,CAACR,GAAG,CAAC,UAACrP,CAAC;MAAA,OAAKoQ,aAAa,CAACpQ,CAAC,CAACiM,SAAS,CAAC;IAAA,EAAC,CAAC,CAAC;IAC3FkC,cAAc,CAACnQ,KAAK,GAAGgS,WAAW;IAClChE,MAAM,CAAChO,KAAK,GAAGoM,IAAI,CAACK,EAAE;IACtB6E,QAAQ,CAAC,CAAC;EACZ,CAAC;EAED,IAAMrB,OAAO,GAAG7J,GAAG,CAAC,EAAE,CAAC,EAAC;EACxB,IAAM8J,SAAS,GAAG9J,GAAG,CAAC,EAAE,CAAC;EACzB,IAAMiM,UAAU,GAAGjM,GAAG,CAAC,EAAE,CAAC;EAC1B,IAAMkM,UAAU,GAAGlM,GAAG,CAAC,EAAE,CAAC;EAC1B,IAAM+J,cAAc,GAAG/J,GAAG,CAAC,EAAE,CAAC;EAC9B,IAAMmM,OAAO,GAAG,SAAVA,OAAOA,CAAIpS,CAAC,EAAEqS,CAAC;IAAA,OAAKrS,CAAC,CAACyR,MAAM,CAAC,UAAC5P,CAAC;MAAA,OAAKwQ,CAAC,CAACT,OAAO,CAAC/P,CAAC,CAAC,KAAK,CAAC,CAAC;IAAA,EAAC;EAAA;EAC9D;EACA,IAAMsP,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;IACrB,IAAMmB,YAAY,GAAG/S,MAAM,CAACsF,IAAI,CAAC6B,MAAM,CAAC6L,QAAQ,CAAC;IACjD,IAAMC,YAAY,GAAGV,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAAClC,OAAO,CAACjQ,KAAK,CAACqR,GAAG,CAAC,UAACrP,CAAC;MAAA,OAAKiM,SAAS,CAACjM,CAAC,CAACiM,SAAS,CAAC;IAAA,EAAC,CAAC,CAAC,CAAC2D,MAAM,CAAC,UAAC5P,CAAC;MAAA,OAClGyQ,YAAY,CAACpC,QAAQ,CAACrO,CAAC,CAAC;IAAA,CAC1B,CAAC;IACD,IAAM4Q,WAAW,GAAGL,OAAO,CAACI,YAAY,EAAEpK,QAAQ,CAACvI,KAAK,CAAC;IACzD;IACAuI,QAAQ,CAACvI,KAAK,MAAA6S,MAAA,CAAAC,kBAAA,CAAOvK,QAAQ,CAACvI,KAAK,GAAA8S,kBAAA,CAAKF,WAAW,EAAC;IACpD,IAAI,CAACA,WAAW,CAACvO,MAAM,EAAE;MACvB0O,cAAc,CAAC,CAAC;MAChB;IACF;IACAvM,QAAQ,CAAC,YAAM;MAAA,IAAAwM,MAAA,YAAAA,OAAA,EAC2C;QACtD,IAAMhR,CAAC,GAAG4Q,WAAW,CAAC3S,CAAC,CAAC;QACxB,IAAI,CAACuI,WAAW,CAACxI,KAAK,CAACgC,CAAC,CAAC,EAAE;UACzBwG,WAAW,CAACxI,KAAK,CAACgC,CAAC,CAAC,GAAG+E,aAAa,CAAC/E,CAAC,CAAC;UACvCwG,WAAW,CAACxI,KAAK,CAACgC,CAAC,CAAC,CAACiR,WAAW,CAC7BvQ,IAAI,CAAC,YAAM;YACV8F,WAAW,CAACxI,KAAK,CAACgC,CAAC,CAAC,CAACkR,YAAY,CAACxQ,IAAI,CAAC,YAAM;cAC3CoE,cAAc,CAACsK,cAAc,CAAC;gBAC5B+B,KAAK,EAAEvL,KAAK,CAACc,OAAO,CAAC0K,UAAU;gBAC/B3K,IAAI,EAAEb,KAAK,CAACc,OAAO,CAACC,SAAS;gBAC7B0K,IAAI,EAAEzL,KAAK,CAACc,OAAO,CAACiG,SAAS;gBAC7B/F,IAAI,EAAEhB,KAAK,CAACc,OAAO,CAACG,SAAS;gBAC7BC,IAAI,EAAElB,KAAK,CAACc,OAAO,CAACK,SAAS;gBAC7BuK,UAAU,EAAE1L,KAAK,CAACc,OAAO,CAAC6K,aAAa;gBACvCC,cAAc,EAAE5L,KAAK,CAACc,OAAO,CAACO;cAChC,CAAC,CAAC;YACJ,CAAC,CAAC;UACJ,CAAC,CAAC,CACDtD,KAAK,CAAC,UAAC8N,GAAG,EAAK;YACdnB,UAAU,CAACtS,KAAK,CAACgE,IAAI,CAAChC,CAAC,CAAC;UAC1B,CAAC,CAAC;QACN;MACF,CAAC;MAtBD,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAE8N,GAAG,GAAG6E,WAAW,CAACvO,MAAM,EAAEpE,CAAC,GAAG8N,GAAG,EAAE9N,CAAC,EAAE;QAAA+S,MAAA;MAAA;MAuBtDU,UAAU,CAAC,YAAM;QACfX,cAAc,CAAC,CAAC;MAClB,CAAC,EAAE,EAAE,CAAC;IACR,CAAC,CAAC;EACJ,CAAC;EACD,IAAMA,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3B,KAAK,IAAI9S,CAAC,GAAG,CAAC,EAAE8N,GAAG,GAAGkC,OAAO,CAACjQ,KAAK,CAACqE,MAAM,EAAEpE,CAAC,GAAG8N,GAAG,EAAE9N,CAAC,EAAE,EAAE;MACxD,IAAMmM,IAAI,GAAG6D,OAAO,CAACjQ,KAAK,CAACC,CAAC,CAAC;MAC7B,IAAI+N,MAAM,CAAChO,KAAK,KAAKoM,IAAI,CAACK,EAAE,EAAE;QAC5B/C,cAAc,CAAC4C,OAAO,CAAC,KAAK,EAAET,IAAI,CAACU,SAAS,CAACH,IAAI,CAAC,CAAC;QACnD,IAAMuH,OAAO,GAAGvH,IAAI,CAAC6B,SAAS,CAAC6D,SAAS,CAAC,CAAC,EAAE1F,IAAI,CAAC6B,SAAS,CAAC8D,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI3F,IAAI,CAAC6B,SAAS;QAC1F,IAAM2F,YAAY,GAAG3F,SAAS,CAAC0F,OAAO,CAAC;QACvCtB,UAAU,CAACrS,KAAK,GAAG4T,YAAY;QAC/B,IAAIrL,QAAQ,CAACvI,KAAK,CAACqQ,QAAQ,CAACuD,YAAY,CAAC,IAAI,CAACtB,UAAU,CAACtS,KAAK,CAACqQ,QAAQ,CAACuD,YAAY,CAAC,EAAE;UAAA,IAAAC,mBAAA;UACrF,IAAMjI,KAAK,GAAA4E,aAAA,CAAAA,aAAA;YAAKrC,aAAa,GAAA0F,mBAAA,GAAEzH,IAAI,CAAC+B,aAAa,cAAA0F,mBAAA,uBAAlBA,mBAAA,CAAoB7T;UAAK,GAAKsQ,aAAa,CAAClE,IAAI,CAAC6B,SAAS,CAAC,GAAK7B,IAAI,CAACR,KAAK,CAAE;UAC3GjE,MAAM,CAAC3D,IAAI,CAAC;YAAE2H,IAAI,EAAES,IAAI,CAAC6B,SAAS;YAAErC,KAAK,EAAEA;UAAM,CAAC,CAAC;UACnDpD,WAAW,CAACxI,KAAK,CAAC4T,YAAY,CAAC,CAACV,YAAY,CAACxQ,IAAI,CAAC,YAAM;YACtDoE,cAAc,CAACsK,cAAc,CAAC;cAAEjB,cAAc,EAAEF,OAAO,CAACjQ,KAAK,CAACqR,GAAG,CAAC,UAACrP,CAAC;gBAAA,OAAKA,CAAC,CAACiM,SAAS;cAAA;YAAE,CAAC,CAAC;UAC1F,CAAC,CAAC;QACJ,CAAC,MAAM;UACL,IAAI2F,YAAY,KAAK,GAAG,EAAE;YAAA,IAAAE,oBAAA;YACxB,IAAMlI,MAAK,GAAA4E,aAAA,CAAAA,aAAA;cAAKrC,aAAa,GAAA2F,oBAAA,GAAE1H,IAAI,CAAC+B,aAAa,cAAA2F,oBAAA,uBAAlBA,oBAAA,CAAoB9T;YAAK,GAAKsQ,aAAa,CAAClE,IAAI,CAAC6B,SAAS,CAAC,GAAK7B,IAAI,CAACR,KAAK,CAAE;YAC3GjE,MAAM,CAAC3D,IAAI,CAAC;cAAE2H,IAAI,EAAES,IAAI,CAAC6B,SAAS;cAAErC,KAAK,EAAEA;YAAM,CAAC,CAAC;YACnD,IAAMiG,WAAW,GAAG5B,OAAO,CAACjQ,KAAK,CAAC4R,MAAM,CACtC,UAAC5P,CAAC;cAAA,OAAKiM,SAAS,CAACjM,CAAC,CAACiM,SAAS,CAAC6D,SAAS,CAAC,CAAC,EAAE9P,CAAC,CAACiM,SAAS,CAAC8D,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI/P,CAAC,CAACiM,SAAS,CAAC,KAAK,GAAG;YAAA,CAC7F,CAAC;YACD,IAAM+D,WAAW,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,GAAG,CAACN,WAAW,CAACR,GAAG,CAAC,UAACrP,CAAC;cAAA,OAAKoQ,aAAa,CAACpQ,CAAC,CAACiM,SAAS,CAAC;YAAA,EAAC,CAAC,CAAC;YAC3FkC,cAAc,CAACnQ,KAAK,GAAGgS,WAAW;UACpC,CAAC,MAAM;YACLrK,MAAM,CAAC3D,IAAI,CAAC;cAAE2H,IAAI,EAAE;YAAgB,CAAC,CAAC;UACxC;QACF;MACF;IACF;EACF,CAAC;EACD,IAAMyG,aAAa,GAAG,SAAhBA,aAAaA,CAAI2B,GAAG,EAAK;IAC7B,IAAIpI,IAAI,GAAG,EAAE;IACb,IAAMqI,KAAK,GAAGD,GAAG,CAAChC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;IAClC,IAAMkC,GAAG,GAAGF,GAAG,CAAChC,OAAO,CAAC,GAAG,CAAC;IAC5B,IAAIkC,GAAG,KAAK,CAAC,CAAC,EAAE;MACdtI,IAAI,GAAGoI,GAAG,CAACjC,SAAS,CAAC,CAAC,CAAC;IACzB,CAAC,MAAM;MACLnG,IAAI,GAAGoI,GAAG,CAACjC,SAAS,CAACkC,KAAK,EAAEC,GAAG,CAAC;IAClC;IACA,OAAOtI,IAAI;EACb,CAAC;EACD,IAAM2E,aAAa,GAAG,SAAhBA,aAAaA,CAAIjG,IAAI,EAAK;IAC9B,IAAI6J,MAAM,GAAG,CAAC,CAAC;IACf7J,IAAI,GAAGA,IAAI,CAACyH,SAAS,CAACzH,IAAI,CAAC0H,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC5C,IAAIoC,GAAG,GAAG9J,IAAI,CAAC+J,KAAK,CAAC,GAAG,CAAC;IACzBD,GAAG,CAAC/R,OAAO,CAAC,UAACgK,IAAI,EAAK;MACpB,IAAIjM,CAAC,GAAGiM,IAAI,CAACgI,KAAK,CAAC,GAAG,CAAC;MACvBF,MAAM,CAAC/T,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC;IACrB,CAAC,CAAC;IACF,OAAO+T,MAAM;EACf,CAAC;EACD;EACA,IAAMjG,SAAS,GAAG,SAAZA,SAASA,CAAI8F,GAAG,EAAK;IACzB,IAAIpI,IAAI,GAAG,EAAE,EAAC;IACd,IAAM0I,KAAK,GAAGN,GAAG,CAAChC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAC;IACnC,IAAMuC,IAAI,GAAGP,GAAG,CAAChC,OAAO,CAAC,GAAG,EAAEsC,KAAK,CAAC,EAAC;IACrC,IAAME,IAAI,GAAGR,GAAG,CAAChC,OAAO,CAAC,GAAG,EAAEsC,KAAK,CAAC,EAAC;IACrC,IAAIE,IAAI,KAAK,CAAC,CAAC,EAAE;MACf5I,IAAI,GAAGoI,GAAG,CAACjC,SAAS,CAAC,CAAC,EAAEwC,IAAI,CAAC;IAC/B,CAAC,MAAM;MACL3I,IAAI,GAAGoI,GAAG,CAACjC,SAAS,CAAC,CAAC,EAAEyC,IAAI,CAAC;IAC/B;IACA,OAAO5I,IAAI;EACb,CAAC;EACDtF,KAAK,CACH;IAAA,OAAMuB,KAAK,CAAC4M,KAAK,CAACC,SAAS;EAAA,GAC3B,UAACC,GAAG,EAAK;IACP,IAAIA,GAAG,CAAC/I,IAAI,EAAE;MACZ8I,SAAS,CAACC,GAAG,CAAC;IAChB;EACF,CAAC,EACD;IAAE/G,SAAS,EAAE;EAAK,CACpB,CAAC;EACDtH,KAAK,CACH;IAAA,OAAMuB,KAAK,CAAC4M,KAAK,CAACG,cAAc;EAAA,GAChC,UAACD,GAAG,EAAK;IACP,IAAIA,GAAG,CAACE,OAAO,EAAE;MACfC,QAAQ,CAACH,GAAG,CAAC;IACf;EACF,CAAC,EACD;IAAE/G,SAAS,EAAE;EAAK,CACpB,CAAC;EAED,IAAM8G,SAAS,GAAG,SAAZA,SAASA,CAAIC,GAAG,EAAK;IACzB,IAAIxE,SAAS,CAAClQ,KAAK,CAACqR,GAAG,CAAC,UAACrP,CAAC;MAAA,OAAKA,CAAC,CAAC8S,MAAM;IAAA,EAAC,CAACzE,QAAQ,CAACxE,IAAI,CAACU,SAAS,CAACmI,GAAG,CAAC,CAAC,EAAE;MACtE,KAAK,IAAIzU,CAAC,GAAG,CAAC,EAAE8N,GAAG,GAAGmC,SAAS,CAAClQ,KAAK,CAACqE,MAAM,EAAEpE,CAAC,GAAG8N,GAAG,EAAE9N,CAAC,EAAE,EAAE;QAC1D,IAAMmM,IAAI,GAAG8D,SAAS,CAAClQ,KAAK,CAACC,CAAC,CAAC;QAC/B,IAAImM,IAAI,CAAC0I,MAAM,KAAKjJ,IAAI,CAACU,SAAS,CAACmI,GAAG,CAAC,EAAE;UACvC1G,MAAM,CAAChO,KAAK,GAAGoM,IAAI,CAACK,EAAE;UACtB6E,QAAQ,CAAC,CAAC;QACZ;MACF;IACF,CAAC,MAAM;MACL,IAAM7E,EAAE,GAAGsI,IAAI,CAAC,CAAC;MACjB7E,SAAS,CAAClQ,KAAK,CAACgE,IAAI,CAAC;QAAEyI,EAAE,EAAEA,EAAE;QAAEqI,MAAM,EAAEjJ,IAAI,CAACU,SAAS,CAACmI,GAAG;MAAE,CAAC,CAAC;MAC7DzE,OAAO,CAACjQ,KAAK,CAACgE,IAAI,CAAC;QACjByI,EAAE;QACFhI,IAAI,EAAEiQ,GAAG,CAACjQ,IAAI;QACdwJ,SAAS,EAAEyG,GAAG,CAAC/I,IAAI;QACnBC,KAAK,EAAA4E,aAAA,CAAAA,aAAA,KAAOkE,GAAG,CAAC9I,KAAK;UAAEoJ,OAAO,EAAEvI,EAAE;UAAEwI,UAAU,EAAEjH,MAAM,CAAChO;QAAK;MAC9D,CAAC,CAAC;MACFgO,MAAM,CAAChO,KAAK,GAAGyM,EAAE;MACjB6E,QAAQ,CAAC,CAAC;IACZ;IACAxK,cAAc,CAACsK,cAAc,CAAC;MAAEqD,SAAS,EAAE;QAAEhQ,IAAI,EAAE,EAAE;QAAEkH,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE,CAAC;MAAE;IAAE,CAAC,CAAC;EACjF,CAAC;EACD,IAAMiJ,QAAQ,GAAG,SAAXA,QAAQA,CAAIH,GAAG,EAAK;IACxB,IAAIA,GAAG,CAACQ,MAAM,EAAE;MACdhF,SAAS,CAAClQ,KAAK,GAAGkQ,SAAS,CAAClQ,KAAK,CAAC4R,MAAM,CAAC,UAACxF,IAAI;QAAA,OAAKA,IAAI,CAACK,EAAE,KAAKiI,GAAG,CAACE,OAAO;MAAA,EAAC;MAC3E3E,OAAO,CAACjQ,KAAK,GAAGiQ,OAAO,CAACjQ,KAAK,CAAC4R,MAAM,CAAC,UAACxF,IAAI;QAAA,OAAKA,IAAI,CAACK,EAAE,KAAKiI,GAAG,CAACE,OAAO;MAAA,EAAC;MACvE5G,MAAM,CAAChO,KAAK,GAAG0U,GAAG,CAACQ,MAAM;MACzB5D,QAAQ,CAAC,CAAC;IACZ,CAAC,MAAM;MACL6D,WAAW,CAACT,GAAG,CAACE,OAAO,CAAC;IAC1B;IACA9N,cAAc,CAACsK,cAAc,CAAC;MAAEuD,cAAc,EAAE;QAAEO,MAAM,EAAE,EAAE;QAAEN,OAAO,EAAE;MAAG;IAAE,CAAC,CAAC;EAChF,CAAC;EACD,IAAMQ,SAAS,GAAGhP,GAAG,CAAC,IAAI,CAAC;EAC3B,IAAMiP,aAAa,GAAG,SAAhBA,aAAaA,CAAI5I,EAAE,EAAK;IAC5B,IAAI/E,KAAK,CAAC4N,IAAI,CAACC,UAAU,KAAK,MAAM,EAAE;MACpCpF,cAAc,CAACnQ,KAAK,GAAGmQ,cAAc,CAACnQ,KAAK,CAAC4R,MAAM,CAAC,UAAC5P,CAAC;QAAA,OAAKA,CAAC,KAAK0F,KAAK,CAACjD,IAAI;MAAA,EAAC;MAC3E2Q,SAAS,CAACpV,KAAK,GAAG,KAAK;MACvB0T,UAAU,CAAC,YAAM;QACfvD,cAAc,CAACnQ,KAAK,CAACgE,IAAI,CAAC0D,KAAK,CAACjD,IAAI,CAAC;QACrC2Q,SAAS,CAACpV,KAAK,GAAG,IAAI;MACxB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,MAAM;MACL,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAE8N,GAAG,GAAGkC,OAAO,CAACjQ,KAAK,CAACqE,MAAM,EAAEpE,CAAC,GAAG8N,GAAG,EAAE9N,CAAC,EAAE,EAAE;QACxD,IAAMmM,IAAI,GAAG6D,OAAO,CAACjQ,KAAK,CAACC,CAAC,CAAC;QAC7B,IAAImM,IAAI,CAACK,EAAE,KAAKA,EAAE,EAAE;UAClB3F,cAAc,CAACsK,cAAc,CAAC;YAAEoE,YAAY,EAAEpJ,IAAI,CAAC6B;UAAU,CAAC,CAAC;UAC/DyF,UAAU,CAAC,YAAM;YACf5M,cAAc,CAACsK,cAAc,CAAC;cAAEoE,YAAY,EAAE;YAAG,CAAC,CAAC;UACrD,CAAC,EAAE,GAAG,CAAC;QACT;MACF;IACF;EACF,CAAC;EACD,IAAML,WAAW,GAAG,SAAdA,WAAWA,CAAI1I,EAAE,EAAK;IAC1B,IAAIuB,MAAM,CAAChO,KAAK,KAAKyM,EAAE,EAAE;MACvB,KAAK,IAAIxM,CAAC,GAAG,CAAC,EAAE8N,GAAG,GAAGkC,OAAO,CAACjQ,KAAK,CAACqE,MAAM,EAAEpE,CAAC,GAAG8N,GAAG,EAAE9N,CAAC,EAAE,EAAE;QACxD,IAAMmM,IAAI,GAAG6D,OAAO,CAACjQ,KAAK,CAACC,CAAC,CAAC;QAC7B,IAAImM,IAAI,CAACK,EAAE,KAAKA,EAAE,EAAE;UAClBuB,MAAM,CAAChO,KAAK,GAAGiQ,OAAO,CAACjQ,KAAK,CAACC,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAACwM,EAAE;UAC9C6E,QAAQ,CAAC,CAAC;QACZ;MACF;IACF;IACApB,SAAS,CAAClQ,KAAK,GAAGkQ,SAAS,CAAClQ,KAAK,CAAC4R,MAAM,CAAC,UAACxF,IAAI;MAAA,OAAKA,IAAI,CAACK,EAAE,KAAKA,EAAE;IAAA,EAAC;IAClEwD,OAAO,CAACjQ,KAAK,GAAGiQ,OAAO,CAACjQ,KAAK,CAAC4R,MAAM,CAAC,UAACxF,IAAI;MAAA,OAAKA,IAAI,CAACK,EAAE,KAAKA,EAAE;IAAA,EAAC;EAChE,CAAC;EACD,IAAM0E,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAI1E,EAAE,EAAK;IAC/ByD,SAAS,CAAClQ,KAAK,GAAGkQ,SAAS,CAAClQ,KAAK,CAAC4R,MAAM,CAAC,UAACxF,IAAI;MAAA,OAAKA,IAAI,CAACK,EAAE,KAAKA,EAAE;IAAA,EAAC;IAClEwD,OAAO,CAACjQ,KAAK,GAAGiQ,OAAO,CAACjQ,KAAK,CAAC4R,MAAM,CAAC,UAACxF,IAAI;MAAA,OAAKA,IAAI,CAACK,EAAE,KAAKA,EAAE;IAAA,EAAC;IAC9DuB,MAAM,CAAChO,KAAK,GAAGyM,EAAE;IACjB6E,QAAQ,CAAC,CAAC;EACZ,CAAC;EAED,IAAMmE,aAAY,GAAG,SAAfA,YAAYA,CAAIC,KAAK,EAAEnG,GAAG,EAAEvP,KAAK,EAAK;IAC1C,IAAI,CAAC0V,KAAK,IAAI,CAACA,KAAK,CAACrR,MAAM,EAAE,OAAO,EAAE,EAAC;IACvC,IAAMoK,QAAQ,GAAG,EAAE;IAAA,IAAAkH,SAAA,GAAAC,0BAAA,CACFF,KAAK;MAAAG,KAAA;IAAA;MAAtB,KAAAF,SAAA,CAAAlU,CAAA,MAAAoU,KAAA,GAAAF,SAAA,CAAA/V,CAAA,IAAAiD,IAAA,GAAwB;QAAA,IAAfiT,IAAI,GAAAD,KAAA,CAAA7V,KAAA;QACX,IAAI8V,IAAI,CAACvG,GAAG,CAAC,KAAKvP,KAAK,IAAI,CAAC+O,UAAU,CAAC/O,KAAK,EAAE;UAC5C+O,UAAU,CAAC/O,KAAK,GAAG8V,IAAI,CAACrJ,EAAE;UAC1BuC,WAAW,CAAChP,KAAK,GAAG8V,IAAI;QAC1B;QACAA,IAAI,GAAGpW,MAAM,CAACqW,MAAM,CAAC,CAAC,CAAC,EAAED,IAAI,CAAC;QAC9B,IAAME,GAAG,GAAGP,aAAY,CAACK,IAAI,CAACrH,QAAQ,EAAEc,GAAG,EAAEvP,KAAK,CAAC;QACnD,IAAKgW,GAAG,IAAIA,GAAG,CAAC3R,MAAM,IAAKyR,IAAI,CAACvG,GAAG,CAAC,KAAKvP,KAAK,EAAE;UAC9CgW,GAAG,CAAC3R,MAAM,KAAKyR,IAAI,CAACrH,QAAQ,GAAGuH,GAAG,CAAC;UACnCvH,QAAQ,CAACzK,IAAI,CAAC8R,IAAI,CAAC;QACrB;MACF;IAAC,SAAArC,GAAA;MAAAkC,SAAA,CAAApW,CAAA,CAAAkU,GAAA;IAAA;MAAAkC,SAAA,CAAAnU,CAAA;IAAA;IACD,OAAOiN,QAAQ,CAACpK,MAAM,GAAGoK,QAAQ,GAAG,EAAE,EAAC;EACzC,CAAC;EACD,IAAMa,QAAQ,GAAG,SAAXA,QAAQA,CAAA2G,KAAA,EAA8B;IAAA,IAAAC,SAAA,GAAAD,KAAA,CAAxB1G,GAAG;MAAHA,GAAG,GAAA2G,SAAA,cAAG,IAAI,GAAAA,SAAA;MAAElW,KAAK,GAAAiW,KAAA,CAALjW,KAAK;IACnC,IAAI8O,MAAM,CAAC9O,KAAK,EAAE;IAClB8O,MAAM,CAAC9O,KAAK,GAAG,IAAI;IACnB,IAAMmW,YAAY,GAAGV,aAAY,CAAC/G,WAAW,CAAC1O,KAAK,EAAEuP,GAAG,EAAEvP,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACzE,IAAImW,YAAY,CAAC1J,EAAE,EAAE;MACnB,IAAIQ,OAAO,CAACjN,KAAK,KAAKmW,YAAY,CAAC1J,EAAE,EAAE;QACrC,IAAI0J,YAAY,CAAClI,SAAS,KAAK,YAAY,EAAE;UAAA,IAAAmI,qBAAA;UAC3C,IAAID,YAAY,CAAC1J,EAAE,KAAKsC,UAAU,CAAC/O,KAAK,EAAE;YACxC+O,UAAU,CAAC/O,KAAK,GAAG,EAAE;YACrBgP,WAAW,CAAChP,KAAK,GAAG,CAAC,CAAC;YACtBuR,eAAe,CAAC,CAAC;YACjB;UACF;UACA,IAAI,EAAA6E,qBAAA,GAAAD,YAAY,CAAC1H,QAAQ,CAAC,CAAC,CAAC,cAAA2H,qBAAA,uBAAxBA,qBAAA,CAA0B3J,EAAE,MAAK2C,SAAS,CAACpP,KAAK,CAACyM,EAAE,EAAE;YACvDuB,MAAM,CAAChO,KAAK,GAAG+O,UAAU,CAAC/O,KAAK;YAC/BgR,SAAS,CAAChC,WAAW,CAAChP,KAAK,CAAC;YAC5B+O,UAAU,CAAC/O,KAAK,GAAG,EAAE;YACrBgP,WAAW,CAAChP,KAAK,GAAG,CAAC,CAAC;YACtBwG,QAAQ,CAAC,YAAM;cACb,IAAIsI,MAAM,CAAC9O,KAAK,EAAE;gBAChB8O,MAAM,CAAC9O,KAAK,GAAG,KAAK;cACtB;YACF,CAAC,CAAC;UACJ,CAAC,MAAM;YACLuR,eAAe,CAAC,CAAC;YACjBmC,UAAU,CAAC,YAAM;cACflN,QAAQ,CAAC,YAAM;gBAAA,IAAA6P,sBAAA;gBACbpH,eAAe,CAACjP,KAAK,IAAAqW,sBAAA,GAAGF,YAAY,CAAC1H,QAAQ,CAAC,CAAC,CAAC,cAAA4H,sBAAA,uBAAxBA,sBAAA,CAA0B5J,EAAE;gBACpDiD,WAAW,CAAC,CAAC;gBACblJ,QAAQ,CAAC,YAAM;kBACb,IAAIsI,MAAM,CAAC9O,KAAK,EAAE;oBAChB8O,MAAM,CAAC9O,KAAK,GAAG,KAAK;kBACtB;gBACF,CAAC,CAAC;cACJ,CAAC,CAAC;YACJ,CAAC,EAAE,GAAG,CAAC;UACT;QACF,CAAC,MAAM;UACL,IAAImW,YAAY,CAAClI,SAAS,CAACoC,QAAQ,CAAC,aAAa,CAAC,EAAE;YAClDX,WAAW,CAAC,CAAC;YACblJ,QAAQ,CAAC,YAAM;cACb,IAAIsI,MAAM,CAAC9O,KAAK,EAAE;gBAChB8O,MAAM,CAAC9O,KAAK,GAAG,KAAK;cACtB;YACF,CAAC,CAAC;YACF;UACF;UACAgO,MAAM,CAAChO,KAAK,GAAG+O,UAAU,CAAC/O,KAAK;UAC/BgR,SAAS,CAAChC,WAAW,CAAChP,KAAK,CAAC;UAC5B+O,UAAU,CAAC/O,KAAK,GAAG,EAAE;UACrBgP,WAAW,CAAChP,KAAK,GAAG,CAAC,CAAC;UACtBwG,QAAQ,CAAC,YAAM;YACb,IAAIsI,MAAM,CAAC9O,KAAK,EAAE;cAChB8O,MAAM,CAAC9O,KAAK,GAAG,KAAK;YACtB;UACF,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL,IAAI6O,WAAW,CAAC7O,KAAK,EAAE;UACrBuR,eAAe,CAAC,CAAC;UACjBmC,UAAU,CAAC,YAAM;YACflN,QAAQ,CAAC,YAAM;cACbyG,OAAO,CAACjN,KAAK,GAAGmW,YAAY,CAAC1J,EAAE;cAC/B,IAAI0J,YAAY,CAAC1J,EAAE,KAAKsC,UAAU,CAAC/O,KAAK,EAAE;gBACxC+O,UAAU,CAAC/O,KAAK,GAAG,EAAE;gBACrBgP,WAAW,CAAChP,KAAK,GAAG,CAAC,CAAC;cACxB,CAAC,MAAM;gBAAA,IAAAsW,sBAAA;gBACLrH,eAAe,CAACjP,KAAK,IAAAsW,sBAAA,GAAGH,YAAY,CAAC1H,QAAQ,CAAC,CAAC,CAAC,cAAA6H,sBAAA,uBAAxBA,sBAAA,CAA0B7J,EAAE;cACtD;cACAiD,WAAW,CAAC,CAAC;cACblJ,QAAQ,CAAC,YAAM;gBACb,IAAIsI,MAAM,CAAC9O,KAAK,EAAE;kBAChB8O,MAAM,CAAC9O,KAAK,GAAG,KAAK;gBACtB;cACF,CAAC,CAAC;YACJ,CAAC,CAAC;UACJ,CAAC,EAAE,GAAG,CAAC;QACT,CAAC,MAAM;UACLiN,OAAO,CAACjN,KAAK,GAAGmW,YAAY,CAAC1J,EAAE;UAC/B,IAAI0J,YAAY,CAAC1J,EAAE,KAAKsC,UAAU,CAAC/O,KAAK,EAAE;YACxC+O,UAAU,CAAC/O,KAAK,GAAG,EAAE;YACrBgP,WAAW,CAAChP,KAAK,GAAG,CAAC,CAAC;UACxB,CAAC,MAAM;YAAA,IAAAuW,sBAAA;YACLtH,eAAe,CAACjP,KAAK,IAAAuW,sBAAA,GAAGJ,YAAY,CAAC1H,QAAQ,CAAC,CAAC,CAAC,cAAA8H,sBAAA,uBAAxBA,sBAAA,CAA0B9J,EAAE;UACtD;UACAiD,WAAW,CAAC,CAAC;UACblJ,QAAQ,CAAC,YAAM;YACb,IAAIsI,MAAM,CAAC9O,KAAK,EAAE;cAChB8O,MAAM,CAAC9O,KAAK,GAAG,KAAK;YACtB;UACF,CAAC,CAAC;QACJ;MACF;IACF,CAAC,MAAM;MACL8O,MAAM,CAAC9O,KAAK,GAAG,KAAK;MACpBsH,SAAS,CAAC;QAAEnG,IAAI,EAAE,SAAS;QAAEuJ,OAAO,EAAE;MAAa,CAAC,CAAC;IACvD;EACF,CAAC;EACD,IAAM8L,aAAa,GAAG,SAAhBA,aAAaA,CAAI/J,EAAE,EAAK;IAC5BsC,UAAU,CAAC/O,KAAK,GAAGyM,EAAE;EACvB,CAAC;EAED,IAAMsI,IAAI,GAAG,SAAPA,IAAIA,CAAA,EAAS;IACjB,OAAO,sCAAsC,CAAC0B,OAAO,CAAC,OAAO,EAAE,UAACpW,CAAC,EAAK;MACpE,IAAIZ,CAAC,GAAIiX,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAI,CAAC;QAC9B3U,CAAC,GAAG3B,CAAC,IAAI,GAAG,GAAGZ,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG;MACpC,OAAOuC,CAAC,CAAC4U,QAAQ,CAAC,EAAE,CAAC;IACvB,CAAC,CAAC;EACJ,CAAC;EAEDlQ,WAAW,CAAC,YAAM;IAAA,IAAAmQ,MAAA,YAAAA,OAAA,EAC2C;MACzD,IAAM7U,CAAC,GAAGuG,QAAQ,CAACvI,KAAK,CAACC,CAAC,CAAC;MAC3BuI,WAAW,CAACxI,KAAK,CAACgC,CAAC,CAAC,CAAC8U,OAAO,CAAC,CAAC;MAC9BtO,WAAW,CAACxI,KAAK,CAACgC,CAAC,CAAC,CAAC+U,cAAc,CAACrU,IAAI,CAAC,YAAM;QAC7C8F,WAAW,CAACxI,KAAK,CAACgC,CAAC,CAAC,GAAG,IAAI;MAC7B,CAAC,CAAC;IACJ,CAAC;IAND,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAE8N,GAAG,GAAGxF,QAAQ,CAACvI,KAAK,CAACqE,MAAM,EAAEpE,CAAC,GAAG8N,GAAG,EAAE9N,CAAC,EAAE;MAAA4W,MAAA;IAAA;IAOzDtO,QAAQ,CAACvI,KAAK,GAAG,EAAE;EACrB,CAAC,CAAC;EAEFyG,OAAO,CAAC,eAAe,EAAE0I,aAAa,CAAC;EACvC1I,OAAO,CAAC,eAAe,EAAEiK,aAAa,CAAC;EACvCjK,OAAO,CAAC,cAAc,EAAE8J,YAAY,CAAC;EACrC9J,OAAO,CAAC,eAAe,EAAE+P,aAAa,CAAC;EACvC/P,OAAO,CAAC,UAAU,EAAE6I,QAAQ,CAAC;EAC7B7I,OAAO,CAAC,WAAW,EAAEgO,SAAS,CAAC;EAC/BhO,OAAO,CAAC,UAAU,EAAEoO,QAAQ,CAAC;EAC7BpO,OAAO,CAAC,UAAU,EAAEwF,QAAQ,CAAC;EAC7BxF,OAAO,CAAC,cAAc,EAAE0F,YAAY,CAAC;EACrC1F,OAAO,CAAC,MAAM,EAAEmC,IAAI,CAAC;EACrB,OAAO;IACLH,IAAI;IAAEG,IAAI;IAAEE,IAAI;IAAEd,IAAI;IAAEC,KAAK;IAAEC,aAAa;IAAEC,cAAc;IAAEe,QAAQ;IAAEc,aAAa;IACrF5B,gBAAgB;IAAEC,kBAAkB;IAAEC,sBAAsB;IAAEqC,oBAAoB;IAClFsB,QAAQ;IAAEC,UAAU;IAAEC,YAAY;IAAEpC,kBAAkB;IAAE6E,MAAM;IAAEC,WAAW;IAAE5B,OAAO;IAAEyB,WAAW;IAAEgB,WAAW;IAC9G1B,MAAM;IAAEgC,QAAQ;IAAEgB,SAAS;IAAEQ,gBAAgB;IAAEtC,YAAY;IAAEC,aAAa;IAAEC,SAAS;IAAEmC,eAAe;IACtG6D,SAAS;IAAEjF,cAAc;IAAEF,OAAO;IAAEqB,QAAQ;IAAE+D,aAAa;IAAEF,WAAW;IAAEhE,gBAAgB;IAC1FkB,UAAU;IAAE9J,QAAQ;IAAE+G,QAAQ;IAAEiB;EAClC,CAAC;AACH,CAAC;AACD,OAAO,IAAMyG,OAAO,GAAG,SAAVA,OAAOA,CAAItP,KAAK,EAAK;EAChC,IAAMuP,MAAM,GAAG7Q,GAAG,CAAC,KAAK,CAAC;EACzB,IAAM8Q,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;IACvBD,MAAM,CAACjX,KAAK,GAAG0H,KAAK,CAAC4N,IAAI,CAACC,UAAU,KAAK,MAAM;EACjD,CAAC;EAEDlP,KAAK,CACH;IAAA,OAAMqB,KAAK;EAAA,GACX,YAAM;IACJwP,UAAU,CAAC,CAAC;EACd,CAAC,EACD;IAAEC,IAAI,EAAE,IAAI;IAAExJ,SAAS,EAAE;EAAK,CAChC,CAAC;EACDrH,SAAS,CAAC,YAAM;IACd4Q,UAAU,CAACxP,KAAK,CAAC;EACnB,CAAC,CAAC;EACF,OAAO;IAAEuP;EAAO,CAAC;AACnB,CAAC;AAED,OAAO,IAAMG,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;EAC9B,IAAMxP,KAAK,GAAGhB,QAAQ,CAAC,CAAC;EACxB,IAAMyQ,cAAc,GAAG9Q,QAAQ,CAAC;IAAA,OAAMqB,KAAK,CAACc,OAAO,CAAC4O,iBAAiB;EAAA,EAAC;EACtE,OAAO;IAAED;EAAe,CAAC;AAC3B,CAAC;AAED,OAAO,IAAME,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;EAChC,IAAM3P,KAAK,GAAGhB,QAAQ,CAAC,CAAC;EACxB,IAAM4Q,WAAW,GAAGjR,QAAQ,CAAC;IAAA,OAAMqB,KAAK,CAAC4M,KAAK,CAACgD,WAAW;EAAA,EAAC;EAC3D,IAAMC,iBAAiB,GAAGlR,QAAQ,CAAC;IAAA,OAAM,GAAGiR,WAAW,CAACxX,KAAK,IAAI;EAAA,EAAC;EAClE,IAAM0X,cAAc,GAAGtR,GAAG,CAAC,KAAK,CAAC;EACjC,IAAMuR,gBAAgB,GAAGvR,GAAG,CAAC,KAAK,CAAC;EACnC;EACA,IAAMwR,0BAA0B,GAAG,SAA7BA,0BAA0BA,CAAA,EAAS;IACvC,IAAIzN,MAAM,CAAC0N,UAAU,GAAG,IAAI,GAAG,GAAG,EAAE;MAClC,IAAM5P,KAAK,GAAGkC,MAAM,CAAC0N,UAAU,GAAG,IAAI,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;MACxDjQ,KAAK,CAACqC,MAAM,CAAC,gBAAgB,EAAEhC,KAAK,CAAC;MACrC,IAAI,CAACyP,cAAc,CAAC1X,KAAK,EAAE2X,gBAAgB,CAAC3X,KAAK,GAAG,KAAK;MACzD0X,cAAc,CAAC1X,KAAK,GAAG,IAAI;IAC7B,CAAC,MAAM;MACL4H,KAAK,CAACqC,MAAM,CAAC,gBAAgB,EAAE,GAAG,CAAC;MACnC,IAAIyN,cAAc,CAAC1X,KAAK,EAAE2X,gBAAgB,CAAC3X,KAAK,GAAG,KAAK;MACxD0X,cAAc,CAAC1X,KAAK,GAAG,KAAK;IAC9B;EACF,CAAC;EACDsG,SAAS,CAAC,YAAM;IACdsR,0BAA0B,CAAC,CAAC;IAC5BzN,MAAM,CAAC2N,gBAAgB,CAAC,QAAQ,EAAEF,0BAA0B,CAAC;EAC/D,CAAC,CAAC;EACFlR,WAAW,CAAC,YAAM;IAChByD,MAAM,CAAC4N,mBAAmB,CAAC,QAAQ,EAAEH,0BAA0B,CAAC;EAClE,CAAC,CAAC;EACF,OAAO;IAAEH,iBAAiB;IAAEC,cAAc;IAAEC;EAAiB,CAAC;AAChE,CAAC;AAED,OAAO,IAAMK,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;EACnC,IAAMpQ,KAAK,GAAGhB,QAAQ,CAAC,CAAC;EACxB,IAAM6B,IAAI,GAAGlC,QAAQ,CAAC;IAAA,OAAMqB,KAAK,CAAC4M,KAAK,CAAC/L,IAAI;EAAA,EAAC;EAC7C,IAAMO,UAAU,GAAGzC,QAAQ,CAAC;IAAA,OAAMqB,KAAK,CAACc,OAAO,CAACO,iBAAiB;EAAA,EAAC;EAClE,IAAMgP,cAAc,GAAG1R,QAAQ,CAAC;IAAA,IAAA2R,kBAAA;IAAA,QAAAA,kBAAA,GAAMlP,UAAU,CAAChJ,KAAK,cAAAkY,kBAAA,uBAAhBA,kBAAA,CAAkBD,cAAc;EAAA,EAAC;EACvE,IAAME,aAAa,GAAG/R,GAAG,CAAC,KAAK,CAAC;EAChC,IAAMgS,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI,EAAEC,IAAI,EAAK;IACpC,IAAMC,GAAG,GAAG,IAAIpG,GAAG,CAACkG,IAAI,CAAC;IACzB,OAAOC,IAAI,CAACE,IAAI,CAAC,UAACpM,IAAI;MAAA,OAAKmM,GAAG,CAAChK,GAAG,CAACnC,IAAI,CAAC;IAAA,EAAC;EAC3C,CAAC;EACD,IAAMqM,kBAAkB;IAAA,IAAAC,KAAA,GAAA3S,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAiU,SAAA;MAAA,IAAAC,qBAAA,EAAA3H,IAAA;MAAA,OAAA3R,mBAAA,GAAAuB,IAAA,UAAAgY,UAAAC,SAAA;QAAA,kBAAAA,SAAA,CAAA3T,IAAA,GAAA2T,SAAA,CAAAtV,IAAA;UAAA;YAAAsV,SAAA,CAAAtV,IAAA;YAAA,OACFmD,GAAG,CAACM,oBAAoB,CAAC;cAAE8R,KAAK,EAAE,CAAC,cAAc;YAAE,CAAC,CAAC;UAAA;YAAAH,qBAAA,GAAAE,SAAA,CAAA7V,IAAA;YAApEgO,IAAI,GAAA2H,qBAAA,CAAJ3H,IAAI;YACZ,IAAIA,IAAI,CAAC+H,YAAY,EAAE;cACrBb,aAAa,CAACnY,KAAK,GAAGiR,IAAI,CAAC+H,YAAY,IAAI,MAAM;YACnD,CAAC,MAAM;cACLb,aAAa,CAACnY,KAAK,GAAG,KAAK;YAC7B;UAAC;UAAA;YAAA,OAAA8Y,SAAA,CAAAxT,IAAA;QAAA;MAAA,GAAAqT,QAAA;IAAA,CACF;IAAA,gBAPKF,kBAAkBA,CAAA;MAAA,OAAAC,KAAA,CAAAzS,KAAA,OAAAD,SAAA;IAAA;EAAA,GAOvB;EACDM,SAAS,CAAC,YAAM,CAAE,CAAC,CAAC;EACpBD,KAAK,CACH;IAAA,OAAMoC,IAAI,CAACzI,KAAK;EAAA,GAChB,YAAM;IACJ,IAAIyI,IAAI,CAACzI,KAAK,CAACyM,EAAE,EAAE;MACjB,IAAMwM,eAAe,GAAGxQ,IAAI,CAACzI,KAAK,CAACiZ,eAAe,IAAI,EAAE;MACxD,IAAMC,WAAW,GAAG,CAAC,uBAAuB,EAAE,wBAAwB,EAAE,oBAAoB,EAAE,YAAY,CAAC;MAC3G,IAAMC,aAAa,GAAG,CAAC,oBAAoB,EAAE,wBAAwB,EAAE,cAAc,CAAC;MACtF,IAAMC,MAAM,GAAGnB,cAAc,CAACjY,KAAK,KAAK,OAAO,GAAGmZ,aAAa,GAAGD,WAAW;MAC7Ef,aAAa,CAACnY,KAAK,GAAGoY,aAAa,CAACa,eAAe,EAAEG,MAAM,CAAC;MAC5D,IAAIjB,aAAa,CAACnY,KAAK,EAAEyY,kBAAkB,CAAC,CAAC;IAC/C;EACF,CAAC,EACD;IAAE9K,SAAS,EAAE;EAAK,CACpB,CAAC;EACD,OAAO;IAAEwK;EAAc,CAAC;AAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}