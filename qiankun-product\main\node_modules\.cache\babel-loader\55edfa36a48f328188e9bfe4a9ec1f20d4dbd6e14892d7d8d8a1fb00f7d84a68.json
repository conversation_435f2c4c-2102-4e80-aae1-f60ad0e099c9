{"ast": null, "code": "/*!\n * clipboard.js v2.0.11\n * https://clipboardjs.com/\n *\n * Licensed MIT © Zeno Rocha\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n  if (typeof exports === 'object' && typeof module === 'object') module.exports = factory();else if (typeof define === 'function' && define.amd) define([], factory);else if (typeof exports === 'object') exports[\"ClipboardJS\"] = factory();else root[\"ClipboardJS\"] = factory();\n})(this, function () {\n  return /******/function () {\n    // webpackBootstrap\n    /******/\n    var __webpack_modules__ = {\n      /***/686: (/***/function _(__unused_webpack_module, __webpack_exports__, __webpack_require__) {\n        \"use strict\";\n\n        // EXPORTS\n        __webpack_require__.d(__webpack_exports__, {\n          \"default\": function _default() {\n            return /* binding */clipboard;\n          }\n        });\n\n        // EXTERNAL MODULE: ./node_modules/tiny-emitter/index.js\n        var tiny_emitter = __webpack_require__(279);\n        var tiny_emitter_default = /*#__PURE__*/__webpack_require__.n(tiny_emitter);\n        // EXTERNAL MODULE: ./node_modules/good-listener/src/listen.js\n        var listen = __webpack_require__(370);\n        var listen_default = /*#__PURE__*/__webpack_require__.n(listen);\n        // EXTERNAL MODULE: ./node_modules/select/src/select.js\n        var src_select = __webpack_require__(817);\n        var select_default = /*#__PURE__*/__webpack_require__.n(src_select);\n        ; // CONCATENATED MODULE: ./src/common/command.js\n        /**\n         * Executes a given operation type.\n         * @param {String} type\n         * @return {Boolean}\n         */\n        function command(type) {\n          try {\n            return document.execCommand(type);\n          } catch (err) {\n            return false;\n          }\n        }\n        ; // CONCATENATED MODULE: ./src/actions/cut.js\n\n        /**\n         * Cut action wrapper.\n         * @param {String|HTMLElement} target\n         * @return {String}\n         */\n\n        var ClipboardActionCut = function ClipboardActionCut(target) {\n          var selectedText = select_default()(target);\n          command('cut');\n          return selectedText;\n        };\n\n        /* harmony default export */\n        var actions_cut = ClipboardActionCut;\n        ; // CONCATENATED MODULE: ./src/common/create-fake-element.js\n        /**\n         * Creates a fake textarea element with a value.\n         * @param {String} value\n         * @return {HTMLElement}\n         */\n        function createFakeElement(value) {\n          var isRTL = document.documentElement.getAttribute('dir') === 'rtl';\n          var fakeElement = document.createElement('textarea'); // Prevent zooming on iOS\n\n          fakeElement.style.fontSize = '12pt'; // Reset box model\n\n          fakeElement.style.border = '0';\n          fakeElement.style.padding = '0';\n          fakeElement.style.margin = '0'; // Move element out of screen horizontally\n\n          fakeElement.style.position = 'absolute';\n          fakeElement.style[isRTL ? 'right' : 'left'] = '-9999px'; // Move element to the same position vertically\n\n          var yPosition = window.pageYOffset || document.documentElement.scrollTop;\n          fakeElement.style.top = \"\".concat(yPosition, \"px\");\n          fakeElement.setAttribute('readonly', '');\n          fakeElement.value = value;\n          return fakeElement;\n        }\n        ; // CONCATENATED MODULE: ./src/actions/copy.js\n\n        /**\n         * Create fake copy action wrapper using a fake element.\n         * @param {String} target\n         * @param {Object} options\n         * @return {String}\n         */\n\n        var fakeCopyAction = function fakeCopyAction(value, options) {\n          var fakeElement = createFakeElement(value);\n          options.container.appendChild(fakeElement);\n          var selectedText = select_default()(fakeElement);\n          command('copy');\n          fakeElement.remove();\n          return selectedText;\n        };\n        /**\n         * Copy action wrapper.\n         * @param {String|HTMLElement} target\n         * @param {Object} options\n         * @return {String}\n         */\n\n        var ClipboardActionCopy = function ClipboardActionCopy(target) {\n          var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n            container: document.body\n          };\n          var selectedText = '';\n          if (typeof target === 'string') {\n            selectedText = fakeCopyAction(target, options);\n          } else if (target instanceof HTMLInputElement && !['text', 'search', 'url', 'tel', 'password'].includes(target === null || target === void 0 ? void 0 : target.type)) {\n            // If input type doesn't support `setSelectionRange`. Simulate it. https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setSelectionRange\n            selectedText = fakeCopyAction(target.value, options);\n          } else {\n            selectedText = select_default()(target);\n            command('copy');\n          }\n          return selectedText;\n        };\n\n        /* harmony default export */\n        var actions_copy = ClipboardActionCopy;\n        ; // CONCATENATED MODULE: ./src/actions/default.js\n        function _typeof(obj) {\n          \"@babel/helpers - typeof\";\n\n          if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n            _typeof = function _typeof(obj) {\n              return typeof obj;\n            };\n          } else {\n            _typeof = function _typeof(obj) {\n              return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n            };\n          }\n          return _typeof(obj);\n        }\n\n        /**\n         * Inner function which performs selection from either `text` or `target`\n         * properties and then executes copy or cut operations.\n         * @param {Object} options\n         */\n\n        var ClipboardActionDefault = function ClipboardActionDefault() {\n          var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n          // Defines base properties passed from constructor.\n          var _options$action = options.action,\n            action = _options$action === void 0 ? 'copy' : _options$action,\n            container = options.container,\n            target = options.target,\n            text = options.text; // Sets the `action` to be performed which can be either 'copy' or 'cut'.\n\n          if (action !== 'copy' && action !== 'cut') {\n            throw new Error('Invalid \"action\" value, use either \"copy\" or \"cut\"');\n          } // Sets the `target` property using an element that will be have its content copied.\n\n          if (target !== undefined) {\n            if (target && _typeof(target) === 'object' && target.nodeType === 1) {\n              if (action === 'copy' && target.hasAttribute('disabled')) {\n                throw new Error('Invalid \"target\" attribute. Please use \"readonly\" instead of \"disabled\" attribute');\n              }\n              if (action === 'cut' && (target.hasAttribute('readonly') || target.hasAttribute('disabled'))) {\n                throw new Error('Invalid \"target\" attribute. You can\\'t cut text from elements with \"readonly\" or \"disabled\" attributes');\n              }\n            } else {\n              throw new Error('Invalid \"target\" value, use a valid Element');\n            }\n          } // Define selection strategy based on `text` property.\n\n          if (text) {\n            return actions_copy(text, {\n              container: container\n            });\n          } // Defines which selection strategy based on `target` property.\n\n          if (target) {\n            return action === 'cut' ? actions_cut(target) : actions_copy(target, {\n              container: container\n            });\n          }\n        };\n\n        /* harmony default export */\n        var actions_default = ClipboardActionDefault;\n        ; // CONCATENATED MODULE: ./src/clipboard.js\n        function clipboard_typeof(obj) {\n          \"@babel/helpers - typeof\";\n\n          if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n            clipboard_typeof = function _typeof(obj) {\n              return typeof obj;\n            };\n          } else {\n            clipboard_typeof = function _typeof(obj) {\n              return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n            };\n          }\n          return clipboard_typeof(obj);\n        }\n        function _classCallCheck(instance, Constructor) {\n          if (!(instance instanceof Constructor)) {\n            throw new TypeError(\"Cannot call a class as a function\");\n          }\n        }\n        function _defineProperties(target, props) {\n          for (var i = 0; i < props.length; i++) {\n            var descriptor = props[i];\n            descriptor.enumerable = descriptor.enumerable || false;\n            descriptor.configurable = true;\n            if (\"value\" in descriptor) descriptor.writable = true;\n            Object.defineProperty(target, descriptor.key, descriptor);\n          }\n        }\n        function _createClass(Constructor, protoProps, staticProps) {\n          if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n          if (staticProps) _defineProperties(Constructor, staticProps);\n          return Constructor;\n        }\n        function _inherits(subClass, superClass) {\n          if (typeof superClass !== \"function\" && superClass !== null) {\n            throw new TypeError(\"Super expression must either be null or a function\");\n          }\n          subClass.prototype = Object.create(superClass && superClass.prototype, {\n            constructor: {\n              value: subClass,\n              writable: true,\n              configurable: true\n            }\n          });\n          if (superClass) _setPrototypeOf(subClass, superClass);\n        }\n        function _setPrototypeOf(o, p) {\n          _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n            o.__proto__ = p;\n            return o;\n          };\n          return _setPrototypeOf(o, p);\n        }\n        function _createSuper(Derived) {\n          var hasNativeReflectConstruct = _isNativeReflectConstruct();\n          return function _createSuperInternal() {\n            var Super = _getPrototypeOf(Derived),\n              result;\n            if (hasNativeReflectConstruct) {\n              var NewTarget = _getPrototypeOf(this).constructor;\n              result = Reflect.construct(Super, arguments, NewTarget);\n            } else {\n              result = Super.apply(this, arguments);\n            }\n            return _possibleConstructorReturn(this, result);\n          };\n        }\n        function _possibleConstructorReturn(self, call) {\n          if (call && (clipboard_typeof(call) === \"object\" || typeof call === \"function\")) {\n            return call;\n          }\n          return _assertThisInitialized(self);\n        }\n        function _assertThisInitialized(self) {\n          if (self === void 0) {\n            throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n          }\n          return self;\n        }\n        function _isNativeReflectConstruct() {\n          if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n          if (Reflect.construct.sham) return false;\n          if (typeof Proxy === \"function\") return true;\n          try {\n            Date.prototype.toString.call(Reflect.construct(Date, [], function () {}));\n            return true;\n          } catch (e) {\n            return false;\n          }\n        }\n        function _getPrototypeOf(o) {\n          _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n            return o.__proto__ || Object.getPrototypeOf(o);\n          };\n          return _getPrototypeOf(o);\n        }\n\n        /**\n         * Helper function to retrieve attribute value.\n         * @param {String} suffix\n         * @param {Element} element\n         */\n\n        function getAttributeValue(suffix, element) {\n          var attribute = \"data-clipboard-\".concat(suffix);\n          if (!element.hasAttribute(attribute)) {\n            return;\n          }\n          return element.getAttribute(attribute);\n        }\n        /**\n         * Base class which takes one or more elements, adds event listeners to them,\n         * and instantiates a new `ClipboardAction` on each click.\n         */\n\n        var Clipboard = /*#__PURE__*/function (_Emitter) {\n          _inherits(Clipboard, _Emitter);\n          var _super = _createSuper(Clipboard);\n\n          /**\n           * @param {String|HTMLElement|HTMLCollection|NodeList} trigger\n           * @param {Object} options\n           */\n          function Clipboard(trigger, options) {\n            var _this;\n            _classCallCheck(this, Clipboard);\n            _this = _super.call(this);\n            _this.resolveOptions(options);\n            _this.listenClick(trigger);\n            return _this;\n          }\n          /**\n           * Defines if attributes would be resolved using internal setter functions\n           * or custom functions that were passed in the constructor.\n           * @param {Object} options\n           */\n\n          _createClass(Clipboard, [{\n            key: \"resolveOptions\",\n            value: function resolveOptions() {\n              var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n              this.action = typeof options.action === 'function' ? options.action : this.defaultAction;\n              this.target = typeof options.target === 'function' ? options.target : this.defaultTarget;\n              this.text = typeof options.text === 'function' ? options.text : this.defaultText;\n              this.container = clipboard_typeof(options.container) === 'object' ? options.container : document.body;\n            }\n            /**\n             * Adds a click event listener to the passed trigger.\n             * @param {String|HTMLElement|HTMLCollection|NodeList} trigger\n             */\n          }, {\n            key: \"listenClick\",\n            value: function listenClick(trigger) {\n              var _this2 = this;\n              this.listener = listen_default()(trigger, 'click', function (e) {\n                return _this2.onClick(e);\n              });\n            }\n            /**\n             * Defines a new `ClipboardAction` on each click event.\n             * @param {Event} e\n             */\n          }, {\n            key: \"onClick\",\n            value: function onClick(e) {\n              var trigger = e.delegateTarget || e.currentTarget;\n              var action = this.action(trigger) || 'copy';\n              var text = actions_default({\n                action: action,\n                container: this.container,\n                target: this.target(trigger),\n                text: this.text(trigger)\n              }); // Fires an event based on the copy operation result.\n\n              this.emit(text ? 'success' : 'error', {\n                action: action,\n                text: text,\n                trigger: trigger,\n                clearSelection: function clearSelection() {\n                  if (trigger) {\n                    trigger.focus();\n                  }\n                  window.getSelection().removeAllRanges();\n                }\n              });\n            }\n            /**\n             * Default `action` lookup function.\n             * @param {Element} trigger\n             */\n          }, {\n            key: \"defaultAction\",\n            value: function defaultAction(trigger) {\n              return getAttributeValue('action', trigger);\n            }\n            /**\n             * Default `target` lookup function.\n             * @param {Element} trigger\n             */\n          }, {\n            key: \"defaultTarget\",\n            value: function defaultTarget(trigger) {\n              var selector = getAttributeValue('target', trigger);\n              if (selector) {\n                return document.querySelector(selector);\n              }\n            }\n            /**\n             * Allow fire programmatically a copy action\n             * @param {String|HTMLElement} target\n             * @param {Object} options\n             * @returns Text copied.\n             */\n          }, {\n            key: \"defaultText\",\n            /**\n             * Default `text` lookup function.\n             * @param {Element} trigger\n             */\n            value: function defaultText(trigger) {\n              return getAttributeValue('text', trigger);\n            }\n            /**\n             * Destroy lifecycle.\n             */\n          }, {\n            key: \"destroy\",\n            value: function destroy() {\n              this.listener.destroy();\n            }\n          }], [{\n            key: \"copy\",\n            value: function copy(target) {\n              var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n                container: document.body\n              };\n              return actions_copy(target, options);\n            }\n            /**\n             * Allow fire programmatically a cut action\n             * @param {String|HTMLElement} target\n             * @returns Text cutted.\n             */\n          }, {\n            key: \"cut\",\n            value: function cut(target) {\n              return actions_cut(target);\n            }\n            /**\n             * Returns the support of the given action, or all actions if no action is\n             * given.\n             * @param {String} [action]\n             */\n          }, {\n            key: \"isSupported\",\n            value: function isSupported() {\n              var action = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ['copy', 'cut'];\n              var actions = typeof action === 'string' ? [action] : action;\n              var support = !!document.queryCommandSupported;\n              actions.forEach(function (action) {\n                support = support && !!document.queryCommandSupported(action);\n              });\n              return support;\n            }\n          }]);\n          return Clipboard;\n        }(tiny_emitter_default());\n\n        /* harmony default export */\n        var clipboard = Clipboard;\n\n        /***/\n      }),\n      /***/828: (/***/function _(module) {\n        var DOCUMENT_NODE_TYPE = 9;\n\n        /**\n         * A polyfill for Element.matches()\n         */\n        if (typeof Element !== 'undefined' && !Element.prototype.matches) {\n          var proto = Element.prototype;\n          proto.matches = proto.matchesSelector || proto.mozMatchesSelector || proto.msMatchesSelector || proto.oMatchesSelector || proto.webkitMatchesSelector;\n        }\n\n        /**\n         * Finds the closest parent that matches a selector.\n         *\n         * @param {Element} element\n         * @param {String} selector\n         * @return {Function}\n         */\n        function closest(element, selector) {\n          while (element && element.nodeType !== DOCUMENT_NODE_TYPE) {\n            if (typeof element.matches === 'function' && element.matches(selector)) {\n              return element;\n            }\n            element = element.parentNode;\n          }\n        }\n        module.exports = closest;\n\n        /***/\n      }),\n      /***/438: (/***/function _(module, __unused_webpack_exports, __webpack_require__) {\n        var closest = __webpack_require__(828);\n\n        /**\n         * Delegates event to a selector.\n         *\n         * @param {Element} element\n         * @param {String} selector\n         * @param {String} type\n         * @param {Function} callback\n         * @param {Boolean} useCapture\n         * @return {Object}\n         */\n        function _delegate(element, selector, type, callback, useCapture) {\n          var listenerFn = listener.apply(this, arguments);\n          element.addEventListener(type, listenerFn, useCapture);\n          return {\n            destroy: function destroy() {\n              element.removeEventListener(type, listenerFn, useCapture);\n            }\n          };\n        }\n\n        /**\n         * Delegates event to a selector.\n         *\n         * @param {Element|String|Array} [elements]\n         * @param {String} selector\n         * @param {String} type\n         * @param {Function} callback\n         * @param {Boolean} useCapture\n         * @return {Object}\n         */\n        function delegate(elements, selector, type, callback, useCapture) {\n          // Handle the regular Element usage\n          if (typeof elements.addEventListener === 'function') {\n            return _delegate.apply(null, arguments);\n          }\n\n          // Handle Element-less usage, it defaults to global delegation\n          if (typeof type === 'function') {\n            // Use `document` as the first parameter, then apply arguments\n            // This is a short way to .unshift `arguments` without running into deoptimizations\n            return _delegate.bind(null, document).apply(null, arguments);\n          }\n\n          // Handle Selector-based usage\n          if (typeof elements === 'string') {\n            elements = document.querySelectorAll(elements);\n          }\n\n          // Handle Array-like based usage\n          return Array.prototype.map.call(elements, function (element) {\n            return _delegate(element, selector, type, callback, useCapture);\n          });\n        }\n\n        /**\n         * Finds closest match and invokes callback.\n         *\n         * @param {Element} element\n         * @param {String} selector\n         * @param {String} type\n         * @param {Function} callback\n         * @return {Function}\n         */\n        function listener(element, selector, type, callback) {\n          return function (e) {\n            e.delegateTarget = closest(e.target, selector);\n            if (e.delegateTarget) {\n              callback.call(element, e);\n            }\n          };\n        }\n        module.exports = delegate;\n\n        /***/\n      }),\n      /***/879: (/***/function _(__unused_webpack_module, exports) {\n        /**\n         * Check if argument is a HTML element.\n         *\n         * @param {Object} value\n         * @return {Boolean}\n         */\n        exports.node = function (value) {\n          return value !== undefined && value instanceof HTMLElement && value.nodeType === 1;\n        };\n\n        /**\n         * Check if argument is a list of HTML elements.\n         *\n         * @param {Object} value\n         * @return {Boolean}\n         */\n        exports.nodeList = function (value) {\n          var type = Object.prototype.toString.call(value);\n          return value !== undefined && (type === '[object NodeList]' || type === '[object HTMLCollection]') && 'length' in value && (value.length === 0 || exports.node(value[0]));\n        };\n\n        /**\n         * Check if argument is a string.\n         *\n         * @param {Object} value\n         * @return {Boolean}\n         */\n        exports.string = function (value) {\n          return typeof value === 'string' || value instanceof String;\n        };\n\n        /**\n         * Check if argument is a function.\n         *\n         * @param {Object} value\n         * @return {Boolean}\n         */\n        exports.fn = function (value) {\n          var type = Object.prototype.toString.call(value);\n          return type === '[object Function]';\n        };\n\n        /***/\n      }),\n      /***/370: (/***/function _(module, __unused_webpack_exports, __webpack_require__) {\n        var is = __webpack_require__(879);\n        var delegate = __webpack_require__(438);\n\n        /**\n         * Validates all params and calls the right\n         * listener function based on its target type.\n         *\n         * @param {String|HTMLElement|HTMLCollection|NodeList} target\n         * @param {String} type\n         * @param {Function} callback\n         * @return {Object}\n         */\n        function listen(target, type, callback) {\n          if (!target && !type && !callback) {\n            throw new Error('Missing required arguments');\n          }\n          if (!is.string(type)) {\n            throw new TypeError('Second argument must be a String');\n          }\n          if (!is.fn(callback)) {\n            throw new TypeError('Third argument must be a Function');\n          }\n          if (is.node(target)) {\n            return listenNode(target, type, callback);\n          } else if (is.nodeList(target)) {\n            return listenNodeList(target, type, callback);\n          } else if (is.string(target)) {\n            return listenSelector(target, type, callback);\n          } else {\n            throw new TypeError('First argument must be a String, HTMLElement, HTMLCollection, or NodeList');\n          }\n        }\n\n        /**\n         * Adds an event listener to a HTML element\n         * and returns a remove listener function.\n         *\n         * @param {HTMLElement} node\n         * @param {String} type\n         * @param {Function} callback\n         * @return {Object}\n         */\n        function listenNode(node, type, callback) {\n          node.addEventListener(type, callback);\n          return {\n            destroy: function destroy() {\n              node.removeEventListener(type, callback);\n            }\n          };\n        }\n\n        /**\n         * Add an event listener to a list of HTML elements\n         * and returns a remove listener function.\n         *\n         * @param {NodeList|HTMLCollection} nodeList\n         * @param {String} type\n         * @param {Function} callback\n         * @return {Object}\n         */\n        function listenNodeList(nodeList, type, callback) {\n          Array.prototype.forEach.call(nodeList, function (node) {\n            node.addEventListener(type, callback);\n          });\n          return {\n            destroy: function destroy() {\n              Array.prototype.forEach.call(nodeList, function (node) {\n                node.removeEventListener(type, callback);\n              });\n            }\n          };\n        }\n\n        /**\n         * Add an event listener to a selector\n         * and returns a remove listener function.\n         *\n         * @param {String} selector\n         * @param {String} type\n         * @param {Function} callback\n         * @return {Object}\n         */\n        function listenSelector(selector, type, callback) {\n          return delegate(document.body, selector, type, callback);\n        }\n        module.exports = listen;\n\n        /***/\n      }),\n      /***/817: (/***/function _(module) {\n        function select(element) {\n          var selectedText;\n          if (element.nodeName === 'SELECT') {\n            element.focus();\n            selectedText = element.value;\n          } else if (element.nodeName === 'INPUT' || element.nodeName === 'TEXTAREA') {\n            var isReadOnly = element.hasAttribute('readonly');\n            if (!isReadOnly) {\n              element.setAttribute('readonly', '');\n            }\n            element.select();\n            element.setSelectionRange(0, element.value.length);\n            if (!isReadOnly) {\n              element.removeAttribute('readonly');\n            }\n            selectedText = element.value;\n          } else {\n            if (element.hasAttribute('contenteditable')) {\n              element.focus();\n            }\n            var selection = window.getSelection();\n            var range = document.createRange();\n            range.selectNodeContents(element);\n            selection.removeAllRanges();\n            selection.addRange(range);\n            selectedText = selection.toString();\n          }\n          return selectedText;\n        }\n        module.exports = select;\n\n        /***/\n      }),\n      /***/279: (/***/function _(module) {\n        function E() {\n          // Keep this empty so it's easier to inherit from\n          // (via https://github.com/lipsmack from https://github.com/scottcorgan/tiny-emitter/issues/3)\n        }\n        E.prototype = {\n          on: function on(name, callback, ctx) {\n            var e = this.e || (this.e = {});\n            (e[name] || (e[name] = [])).push({\n              fn: callback,\n              ctx: ctx\n            });\n            return this;\n          },\n          once: function once(name, callback, ctx) {\n            var self = this;\n            function listener() {\n              self.off(name, listener);\n              callback.apply(ctx, arguments);\n            }\n            ;\n            listener._ = callback;\n            return this.on(name, listener, ctx);\n          },\n          emit: function emit(name) {\n            var data = [].slice.call(arguments, 1);\n            var evtArr = ((this.e || (this.e = {}))[name] || []).slice();\n            var i = 0;\n            var len = evtArr.length;\n            for (i; i < len; i++) {\n              evtArr[i].fn.apply(evtArr[i].ctx, data);\n            }\n            return this;\n          },\n          off: function off(name, callback) {\n            var e = this.e || (this.e = {});\n            var evts = e[name];\n            var liveEvents = [];\n            if (evts && callback) {\n              for (var i = 0, len = evts.length; i < len; i++) {\n                if (evts[i].fn !== callback && evts[i].fn._ !== callback) liveEvents.push(evts[i]);\n              }\n            }\n\n            // Remove event from queue to prevent memory leak\n            // Suggested by https://github.com/lazd\n            // Ref: https://github.com/scottcorgan/tiny-emitter/commit/c6ebfaa9bc973b33d110a84a307742b7cf94c953#commitcomment-5024910\n\n            liveEvents.length ? e[name] = liveEvents : delete e[name];\n            return this;\n          }\n        };\n        module.exports = E;\n        module.exports.TinyEmitter = E;\n\n        /***/\n      })\n\n      /******/\n    };\n    /************************************************************************/\n    /******/ // The module cache\n    /******/\n    var __webpack_module_cache__ = {};\n    /******/\n    /******/ // The require function\n    /******/\n    function __webpack_require__(moduleId) {\n      /******/ // Check if module is in cache\n      /******/if (__webpack_module_cache__[moduleId]) {\n        /******/return __webpack_module_cache__[moduleId].exports;\n        /******/\n      }\n      /******/ // Create a new module (and put it into the cache)\n      /******/\n      var module = __webpack_module_cache__[moduleId] = {\n        /******/ // no module.id needed\n        /******/ // no module.loaded needed\n        /******/exports: {}\n        /******/\n      };\n      /******/\n      /******/ // Execute the module function\n      /******/\n      __webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n      /******/\n      /******/ // Return the exports of the module\n      /******/\n      return module.exports;\n      /******/\n    }\n    /******/\n    /************************************************************************/\n    /******/ /* webpack/runtime/compat get default export */\n    /******/\n    !function () {\n      /******/ // getDefaultExport function for compatibility with non-harmony modules\n      /******/__webpack_require__.n = function (module) {\n        /******/var getter = module && module.__esModule ? /******/function () {\n          return module['default'];\n        } : /******/function () {\n          return module;\n        };\n        /******/\n        __webpack_require__.d(getter, {\n          a: getter\n        });\n        /******/\n        return getter;\n        /******/\n      };\n      /******/\n    }();\n    /******/\n    /******/ /* webpack/runtime/define property getters */\n    /******/\n    !function () {\n      /******/ // define getter functions for harmony exports\n      /******/__webpack_require__.d = function (exports, definition) {\n        /******/for (var key in definition) {\n          /******/if (__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n            /******/Object.defineProperty(exports, key, {\n              enumerable: true,\n              get: definition[key]\n            });\n            /******/\n          }\n          /******/\n        }\n        /******/\n      };\n      /******/\n    }();\n    /******/\n    /******/ /* webpack/runtime/hasOwnProperty shorthand */\n    /******/\n    !function () {\n      /******/__webpack_require__.o = function (obj, prop) {\n        return Object.prototype.hasOwnProperty.call(obj, prop);\n      };\n      /******/\n    }();\n    /******/\n    /************************************************************************/\n    /******/ // module exports must be returned from runtime so entry inlining is disabled\n    /******/ // startup\n    /******/ // Load entry module and return exports\n    /******/\n    return __webpack_require__(686);\n    /******/\n  }().default;\n});", "map": {"version": 3, "names": ["webpackUniversalModuleDefinition", "root", "factory", "exports", "module", "define", "amd", "__webpack_modules__", "_", "__unused_webpack_module", "__webpack_exports__", "__webpack_require__", "d", "_default", "clipboard", "tiny_emitter", "tiny_emitter_default", "n", "listen", "listen_default", "src_select", "select_default", "command", "type", "document", "execCommand", "err", "ClipboardActionCut", "target", "selectedText", "actions_cut", "createFakeElement", "value", "isRTL", "documentElement", "getAttribute", "fakeElement", "createElement", "style", "fontSize", "border", "padding", "margin", "position", "yPosition", "window", "pageYOffset", "scrollTop", "top", "concat", "setAttribute", "fakeCopyAction", "options", "container", "append<PERSON><PERSON><PERSON>", "remove", "ClipboardActionCopy", "arguments", "length", "undefined", "body", "HTMLInputElement", "includes", "actions_copy", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "ClipboardActionDefault", "_options$action", "action", "text", "Error", "nodeType", "hasAttribute", "actions_default", "clipboard_typeof", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "props", "i", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "_inherits", "subClass", "superClass", "create", "_setPrototypeOf", "o", "p", "setPrototypeOf", "__proto__", "_createSuper", "Derived", "hasNativeReflectConstruct", "_isNativeReflectConstruct", "_createSuperInternal", "Super", "_getPrototypeOf", "result", "<PERSON><PERSON><PERSON><PERSON>", "Reflect", "construct", "apply", "_possibleConstructorReturn", "self", "call", "_assertThisInitialized", "ReferenceError", "sham", "Proxy", "Date", "toString", "e", "getPrototypeOf", "getAttributeValue", "suffix", "element", "attribute", "Clipboard", "_Emitter", "_super", "trigger", "_this", "resolveOptions", "listenClick", "defaultAction", "defaultTarget", "defaultText", "_this2", "listener", "onClick", "<PERSON><PERSON><PERSON><PERSON>", "currentTarget", "emit", "clearSelection", "focus", "getSelection", "removeAllRanges", "selector", "querySelector", "destroy", "copy", "cut", "isSupported", "actions", "support", "queryCommandSupported", "for<PERSON>ach", "DOCUMENT_NODE_TYPE", "Element", "matches", "proto", "matchesSelector", "mozMatchesSelector", "msMatchesSelector", "oMatchesSelector", "webkitMatchesSelector", "closest", "parentNode", "__unused_webpack_exports", "_delegate", "callback", "useCapture", "listenerFn", "addEventListener", "removeEventListener", "delegate", "elements", "bind", "querySelectorAll", "Array", "map", "node", "HTMLElement", "nodeList", "string", "String", "fn", "is", "listenNode", "listenNodeList", "listenSelector", "select", "nodeName", "isReadOnly", "setSelectionRange", "removeAttribute", "selection", "range", "createRange", "selectNodeContents", "addRange", "E", "on", "name", "ctx", "push", "once", "off", "data", "slice", "evtArr", "len", "evts", "liveEvents", "TinyEmitter", "__webpack_module_cache__", "moduleId", "getter", "__esModule", "a", "definition", "get", "prop", "hasOwnProperty", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/clipboard@2.0.11/node_modules/clipboard/dist/clipboard.js"], "sourcesContent": ["/*!\n * clipboard.js v2.0.11\n * https://clipboardjs.com/\n *\n * Licensed MIT © Zeno Rocha\n */\n(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"ClipboardJS\"] = factory();\n\telse\n\t\troot[\"ClipboardJS\"] = factory();\n})(this, function() {\nreturn /******/ (function() { // webpackBootstrap\n/******/ \tvar __webpack_modules__ = ({\n\n/***/ 686:\n/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {\n\n\"use strict\";\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ clipboard; }\n});\n\n// EXTERNAL MODULE: ./node_modules/tiny-emitter/index.js\nvar tiny_emitter = __webpack_require__(279);\nvar tiny_emitter_default = /*#__PURE__*/__webpack_require__.n(tiny_emitter);\n// EXTERNAL MODULE: ./node_modules/good-listener/src/listen.js\nvar listen = __webpack_require__(370);\nvar listen_default = /*#__PURE__*/__webpack_require__.n(listen);\n// EXTERNAL MODULE: ./node_modules/select/src/select.js\nvar src_select = __webpack_require__(817);\nvar select_default = /*#__PURE__*/__webpack_require__.n(src_select);\n;// CONCATENATED MODULE: ./src/common/command.js\n/**\n * Executes a given operation type.\n * @param {String} type\n * @return {Boolean}\n */\nfunction command(type) {\n  try {\n    return document.execCommand(type);\n  } catch (err) {\n    return false;\n  }\n}\n;// CONCATENATED MODULE: ./src/actions/cut.js\n\n\n/**\n * Cut action wrapper.\n * @param {String|HTMLElement} target\n * @return {String}\n */\n\nvar ClipboardActionCut = function ClipboardActionCut(target) {\n  var selectedText = select_default()(target);\n  command('cut');\n  return selectedText;\n};\n\n/* harmony default export */ var actions_cut = (ClipboardActionCut);\n;// CONCATENATED MODULE: ./src/common/create-fake-element.js\n/**\n * Creates a fake textarea element with a value.\n * @param {String} value\n * @return {HTMLElement}\n */\nfunction createFakeElement(value) {\n  var isRTL = document.documentElement.getAttribute('dir') === 'rtl';\n  var fakeElement = document.createElement('textarea'); // Prevent zooming on iOS\n\n  fakeElement.style.fontSize = '12pt'; // Reset box model\n\n  fakeElement.style.border = '0';\n  fakeElement.style.padding = '0';\n  fakeElement.style.margin = '0'; // Move element out of screen horizontally\n\n  fakeElement.style.position = 'absolute';\n  fakeElement.style[isRTL ? 'right' : 'left'] = '-9999px'; // Move element to the same position vertically\n\n  var yPosition = window.pageYOffset || document.documentElement.scrollTop;\n  fakeElement.style.top = \"\".concat(yPosition, \"px\");\n  fakeElement.setAttribute('readonly', '');\n  fakeElement.value = value;\n  return fakeElement;\n}\n;// CONCATENATED MODULE: ./src/actions/copy.js\n\n\n\n/**\n * Create fake copy action wrapper using a fake element.\n * @param {String} target\n * @param {Object} options\n * @return {String}\n */\n\nvar fakeCopyAction = function fakeCopyAction(value, options) {\n  var fakeElement = createFakeElement(value);\n  options.container.appendChild(fakeElement);\n  var selectedText = select_default()(fakeElement);\n  command('copy');\n  fakeElement.remove();\n  return selectedText;\n};\n/**\n * Copy action wrapper.\n * @param {String|HTMLElement} target\n * @param {Object} options\n * @return {String}\n */\n\n\nvar ClipboardActionCopy = function ClipboardActionCopy(target) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n    container: document.body\n  };\n  var selectedText = '';\n\n  if (typeof target === 'string') {\n    selectedText = fakeCopyAction(target, options);\n  } else if (target instanceof HTMLInputElement && !['text', 'search', 'url', 'tel', 'password'].includes(target === null || target === void 0 ? void 0 : target.type)) {\n    // If input type doesn't support `setSelectionRange`. Simulate it. https://developer.mozilla.org/en-US/docs/Web/API/HTMLInputElement/setSelectionRange\n    selectedText = fakeCopyAction(target.value, options);\n  } else {\n    selectedText = select_default()(target);\n    command('copy');\n  }\n\n  return selectedText;\n};\n\n/* harmony default export */ var actions_copy = (ClipboardActionCopy);\n;// CONCATENATED MODULE: ./src/actions/default.js\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof = function _typeof(obj) { return typeof obj; }; } else { _typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof(obj); }\n\n\n\n/**\n * Inner function which performs selection from either `text` or `target`\n * properties and then executes copy or cut operations.\n * @param {Object} options\n */\n\nvar ClipboardActionDefault = function ClipboardActionDefault() {\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  // Defines base properties passed from constructor.\n  var _options$action = options.action,\n      action = _options$action === void 0 ? 'copy' : _options$action,\n      container = options.container,\n      target = options.target,\n      text = options.text; // Sets the `action` to be performed which can be either 'copy' or 'cut'.\n\n  if (action !== 'copy' && action !== 'cut') {\n    throw new Error('Invalid \"action\" value, use either \"copy\" or \"cut\"');\n  } // Sets the `target` property using an element that will be have its content copied.\n\n\n  if (target !== undefined) {\n    if (target && _typeof(target) === 'object' && target.nodeType === 1) {\n      if (action === 'copy' && target.hasAttribute('disabled')) {\n        throw new Error('Invalid \"target\" attribute. Please use \"readonly\" instead of \"disabled\" attribute');\n      }\n\n      if (action === 'cut' && (target.hasAttribute('readonly') || target.hasAttribute('disabled'))) {\n        throw new Error('Invalid \"target\" attribute. You can\\'t cut text from elements with \"readonly\" or \"disabled\" attributes');\n      }\n    } else {\n      throw new Error('Invalid \"target\" value, use a valid Element');\n    }\n  } // Define selection strategy based on `text` property.\n\n\n  if (text) {\n    return actions_copy(text, {\n      container: container\n    });\n  } // Defines which selection strategy based on `target` property.\n\n\n  if (target) {\n    return action === 'cut' ? actions_cut(target) : actions_copy(target, {\n      container: container\n    });\n  }\n};\n\n/* harmony default export */ var actions_default = (ClipboardActionDefault);\n;// CONCATENATED MODULE: ./src/clipboard.js\nfunction clipboard_typeof(obj) { \"@babel/helpers - typeof\"; if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { clipboard_typeof = function _typeof(obj) { return typeof obj; }; } else { clipboard_typeof = function _typeof(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return clipboard_typeof(obj); }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function\"); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, writable: true, configurable: true } }); if (superClass) _setPrototypeOf(subClass, superClass); }\n\nfunction _setPrototypeOf(o, p) { _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) { o.__proto__ = p; return o; }; return _setPrototypeOf(o, p); }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _possibleConstructorReturn(self, call) { if (call && (clipboard_typeof(call) === \"object\" || typeof call === \"function\")) { return call; } return _assertThisInitialized(self); }\n\nfunction _assertThisInitialized(self) { if (self === void 0) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return self; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Date.prototype.toString.call(Reflect.construct(Date, [], function () {})); return true; } catch (e) { return false; } }\n\nfunction _getPrototypeOf(o) { _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) { return o.__proto__ || Object.getPrototypeOf(o); }; return _getPrototypeOf(o); }\n\n\n\n\n\n\n/**\n * Helper function to retrieve attribute value.\n * @param {String} suffix\n * @param {Element} element\n */\n\nfunction getAttributeValue(suffix, element) {\n  var attribute = \"data-clipboard-\".concat(suffix);\n\n  if (!element.hasAttribute(attribute)) {\n    return;\n  }\n\n  return element.getAttribute(attribute);\n}\n/**\n * Base class which takes one or more elements, adds event listeners to them,\n * and instantiates a new `ClipboardAction` on each click.\n */\n\n\nvar Clipboard = /*#__PURE__*/function (_Emitter) {\n  _inherits(Clipboard, _Emitter);\n\n  var _super = _createSuper(Clipboard);\n\n  /**\n   * @param {String|HTMLElement|HTMLCollection|NodeList} trigger\n   * @param {Object} options\n   */\n  function Clipboard(trigger, options) {\n    var _this;\n\n    _classCallCheck(this, Clipboard);\n\n    _this = _super.call(this);\n\n    _this.resolveOptions(options);\n\n    _this.listenClick(trigger);\n\n    return _this;\n  }\n  /**\n   * Defines if attributes would be resolved using internal setter functions\n   * or custom functions that were passed in the constructor.\n   * @param {Object} options\n   */\n\n\n  _createClass(Clipboard, [{\n    key: \"resolveOptions\",\n    value: function resolveOptions() {\n      var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      this.action = typeof options.action === 'function' ? options.action : this.defaultAction;\n      this.target = typeof options.target === 'function' ? options.target : this.defaultTarget;\n      this.text = typeof options.text === 'function' ? options.text : this.defaultText;\n      this.container = clipboard_typeof(options.container) === 'object' ? options.container : document.body;\n    }\n    /**\n     * Adds a click event listener to the passed trigger.\n     * @param {String|HTMLElement|HTMLCollection|NodeList} trigger\n     */\n\n  }, {\n    key: \"listenClick\",\n    value: function listenClick(trigger) {\n      var _this2 = this;\n\n      this.listener = listen_default()(trigger, 'click', function (e) {\n        return _this2.onClick(e);\n      });\n    }\n    /**\n     * Defines a new `ClipboardAction` on each click event.\n     * @param {Event} e\n     */\n\n  }, {\n    key: \"onClick\",\n    value: function onClick(e) {\n      var trigger = e.delegateTarget || e.currentTarget;\n      var action = this.action(trigger) || 'copy';\n      var text = actions_default({\n        action: action,\n        container: this.container,\n        target: this.target(trigger),\n        text: this.text(trigger)\n      }); // Fires an event based on the copy operation result.\n\n      this.emit(text ? 'success' : 'error', {\n        action: action,\n        text: text,\n        trigger: trigger,\n        clearSelection: function clearSelection() {\n          if (trigger) {\n            trigger.focus();\n          }\n\n          window.getSelection().removeAllRanges();\n        }\n      });\n    }\n    /**\n     * Default `action` lookup function.\n     * @param {Element} trigger\n     */\n\n  }, {\n    key: \"defaultAction\",\n    value: function defaultAction(trigger) {\n      return getAttributeValue('action', trigger);\n    }\n    /**\n     * Default `target` lookup function.\n     * @param {Element} trigger\n     */\n\n  }, {\n    key: \"defaultTarget\",\n    value: function defaultTarget(trigger) {\n      var selector = getAttributeValue('target', trigger);\n\n      if (selector) {\n        return document.querySelector(selector);\n      }\n    }\n    /**\n     * Allow fire programmatically a copy action\n     * @param {String|HTMLElement} target\n     * @param {Object} options\n     * @returns Text copied.\n     */\n\n  }, {\n    key: \"defaultText\",\n\n    /**\n     * Default `text` lookup function.\n     * @param {Element} trigger\n     */\n    value: function defaultText(trigger) {\n      return getAttributeValue('text', trigger);\n    }\n    /**\n     * Destroy lifecycle.\n     */\n\n  }, {\n    key: \"destroy\",\n    value: function destroy() {\n      this.listener.destroy();\n    }\n  }], [{\n    key: \"copy\",\n    value: function copy(target) {\n      var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n        container: document.body\n      };\n      return actions_copy(target, options);\n    }\n    /**\n     * Allow fire programmatically a cut action\n     * @param {String|HTMLElement} target\n     * @returns Text cutted.\n     */\n\n  }, {\n    key: \"cut\",\n    value: function cut(target) {\n      return actions_cut(target);\n    }\n    /**\n     * Returns the support of the given action, or all actions if no action is\n     * given.\n     * @param {String} [action]\n     */\n\n  }, {\n    key: \"isSupported\",\n    value: function isSupported() {\n      var action = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : ['copy', 'cut'];\n      var actions = typeof action === 'string' ? [action] : action;\n      var support = !!document.queryCommandSupported;\n      actions.forEach(function (action) {\n        support = support && !!document.queryCommandSupported(action);\n      });\n      return support;\n    }\n  }]);\n\n  return Clipboard;\n}((tiny_emitter_default()));\n\n/* harmony default export */ var clipboard = (Clipboard);\n\n/***/ }),\n\n/***/ 828:\n/***/ (function(module) {\n\nvar DOCUMENT_NODE_TYPE = 9;\n\n/**\n * A polyfill for Element.matches()\n */\nif (typeof Element !== 'undefined' && !Element.prototype.matches) {\n    var proto = Element.prototype;\n\n    proto.matches = proto.matchesSelector ||\n                    proto.mozMatchesSelector ||\n                    proto.msMatchesSelector ||\n                    proto.oMatchesSelector ||\n                    proto.webkitMatchesSelector;\n}\n\n/**\n * Finds the closest parent that matches a selector.\n *\n * @param {Element} element\n * @param {String} selector\n * @return {Function}\n */\nfunction closest (element, selector) {\n    while (element && element.nodeType !== DOCUMENT_NODE_TYPE) {\n        if (typeof element.matches === 'function' &&\n            element.matches(selector)) {\n          return element;\n        }\n        element = element.parentNode;\n    }\n}\n\nmodule.exports = closest;\n\n\n/***/ }),\n\n/***/ 438:\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\nvar closest = __webpack_require__(828);\n\n/**\n * Delegates event to a selector.\n *\n * @param {Element} element\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @param {Boolean} useCapture\n * @return {Object}\n */\nfunction _delegate(element, selector, type, callback, useCapture) {\n    var listenerFn = listener.apply(this, arguments);\n\n    element.addEventListener(type, listenerFn, useCapture);\n\n    return {\n        destroy: function() {\n            element.removeEventListener(type, listenerFn, useCapture);\n        }\n    }\n}\n\n/**\n * Delegates event to a selector.\n *\n * @param {Element|String|Array} [elements]\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @param {Boolean} useCapture\n * @return {Object}\n */\nfunction delegate(elements, selector, type, callback, useCapture) {\n    // Handle the regular Element usage\n    if (typeof elements.addEventListener === 'function') {\n        return _delegate.apply(null, arguments);\n    }\n\n    // Handle Element-less usage, it defaults to global delegation\n    if (typeof type === 'function') {\n        // Use `document` as the first parameter, then apply arguments\n        // This is a short way to .unshift `arguments` without running into deoptimizations\n        return _delegate.bind(null, document).apply(null, arguments);\n    }\n\n    // Handle Selector-based usage\n    if (typeof elements === 'string') {\n        elements = document.querySelectorAll(elements);\n    }\n\n    // Handle Array-like based usage\n    return Array.prototype.map.call(elements, function (element) {\n        return _delegate(element, selector, type, callback, useCapture);\n    });\n}\n\n/**\n * Finds closest match and invokes callback.\n *\n * @param {Element} element\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @return {Function}\n */\nfunction listener(element, selector, type, callback) {\n    return function(e) {\n        e.delegateTarget = closest(e.target, selector);\n\n        if (e.delegateTarget) {\n            callback.call(element, e);\n        }\n    }\n}\n\nmodule.exports = delegate;\n\n\n/***/ }),\n\n/***/ 879:\n/***/ (function(__unused_webpack_module, exports) {\n\n/**\n * Check if argument is a HTML element.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.node = function(value) {\n    return value !== undefined\n        && value instanceof HTMLElement\n        && value.nodeType === 1;\n};\n\n/**\n * Check if argument is a list of HTML elements.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.nodeList = function(value) {\n    var type = Object.prototype.toString.call(value);\n\n    return value !== undefined\n        && (type === '[object NodeList]' || type === '[object HTMLCollection]')\n        && ('length' in value)\n        && (value.length === 0 || exports.node(value[0]));\n};\n\n/**\n * Check if argument is a string.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.string = function(value) {\n    return typeof value === 'string'\n        || value instanceof String;\n};\n\n/**\n * Check if argument is a function.\n *\n * @param {Object} value\n * @return {Boolean}\n */\nexports.fn = function(value) {\n    var type = Object.prototype.toString.call(value);\n\n    return type === '[object Function]';\n};\n\n\n/***/ }),\n\n/***/ 370:\n/***/ (function(module, __unused_webpack_exports, __webpack_require__) {\n\nvar is = __webpack_require__(879);\nvar delegate = __webpack_require__(438);\n\n/**\n * Validates all params and calls the right\n * listener function based on its target type.\n *\n * @param {String|HTMLElement|HTMLCollection|NodeList} target\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listen(target, type, callback) {\n    if (!target && !type && !callback) {\n        throw new Error('Missing required arguments');\n    }\n\n    if (!is.string(type)) {\n        throw new TypeError('Second argument must be a String');\n    }\n\n    if (!is.fn(callback)) {\n        throw new TypeError('Third argument must be a Function');\n    }\n\n    if (is.node(target)) {\n        return listenNode(target, type, callback);\n    }\n    else if (is.nodeList(target)) {\n        return listenNodeList(target, type, callback);\n    }\n    else if (is.string(target)) {\n        return listenSelector(target, type, callback);\n    }\n    else {\n        throw new TypeError('First argument must be a String, HTMLElement, HTMLCollection, or NodeList');\n    }\n}\n\n/**\n * Adds an event listener to a HTML element\n * and returns a remove listener function.\n *\n * @param {HTMLElement} node\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listenNode(node, type, callback) {\n    node.addEventListener(type, callback);\n\n    return {\n        destroy: function() {\n            node.removeEventListener(type, callback);\n        }\n    }\n}\n\n/**\n * Add an event listener to a list of HTML elements\n * and returns a remove listener function.\n *\n * @param {NodeList|HTMLCollection} nodeList\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listenNodeList(nodeList, type, callback) {\n    Array.prototype.forEach.call(nodeList, function(node) {\n        node.addEventListener(type, callback);\n    });\n\n    return {\n        destroy: function() {\n            Array.prototype.forEach.call(nodeList, function(node) {\n                node.removeEventListener(type, callback);\n            });\n        }\n    }\n}\n\n/**\n * Add an event listener to a selector\n * and returns a remove listener function.\n *\n * @param {String} selector\n * @param {String} type\n * @param {Function} callback\n * @return {Object}\n */\nfunction listenSelector(selector, type, callback) {\n    return delegate(document.body, selector, type, callback);\n}\n\nmodule.exports = listen;\n\n\n/***/ }),\n\n/***/ 817:\n/***/ (function(module) {\n\nfunction select(element) {\n    var selectedText;\n\n    if (element.nodeName === 'SELECT') {\n        element.focus();\n\n        selectedText = element.value;\n    }\n    else if (element.nodeName === 'INPUT' || element.nodeName === 'TEXTAREA') {\n        var isReadOnly = element.hasAttribute('readonly');\n\n        if (!isReadOnly) {\n            element.setAttribute('readonly', '');\n        }\n\n        element.select();\n        element.setSelectionRange(0, element.value.length);\n\n        if (!isReadOnly) {\n            element.removeAttribute('readonly');\n        }\n\n        selectedText = element.value;\n    }\n    else {\n        if (element.hasAttribute('contenteditable')) {\n            element.focus();\n        }\n\n        var selection = window.getSelection();\n        var range = document.createRange();\n\n        range.selectNodeContents(element);\n        selection.removeAllRanges();\n        selection.addRange(range);\n\n        selectedText = selection.toString();\n    }\n\n    return selectedText;\n}\n\nmodule.exports = select;\n\n\n/***/ }),\n\n/***/ 279:\n/***/ (function(module) {\n\nfunction E () {\n  // Keep this empty so it's easier to inherit from\n  // (via https://github.com/lipsmack from https://github.com/scottcorgan/tiny-emitter/issues/3)\n}\n\nE.prototype = {\n  on: function (name, callback, ctx) {\n    var e = this.e || (this.e = {});\n\n    (e[name] || (e[name] = [])).push({\n      fn: callback,\n      ctx: ctx\n    });\n\n    return this;\n  },\n\n  once: function (name, callback, ctx) {\n    var self = this;\n    function listener () {\n      self.off(name, listener);\n      callback.apply(ctx, arguments);\n    };\n\n    listener._ = callback\n    return this.on(name, listener, ctx);\n  },\n\n  emit: function (name) {\n    var data = [].slice.call(arguments, 1);\n    var evtArr = ((this.e || (this.e = {}))[name] || []).slice();\n    var i = 0;\n    var len = evtArr.length;\n\n    for (i; i < len; i++) {\n      evtArr[i].fn.apply(evtArr[i].ctx, data);\n    }\n\n    return this;\n  },\n\n  off: function (name, callback) {\n    var e = this.e || (this.e = {});\n    var evts = e[name];\n    var liveEvents = [];\n\n    if (evts && callback) {\n      for (var i = 0, len = evts.length; i < len; i++) {\n        if (evts[i].fn !== callback && evts[i].fn._ !== callback)\n          liveEvents.push(evts[i]);\n      }\n    }\n\n    // Remove event from queue to prevent memory leak\n    // Suggested by https://github.com/lazd\n    // Ref: https://github.com/scottcorgan/tiny-emitter/commit/c6ebfaa9bc973b33d110a84a307742b7cf94c953#commitcomment-5024910\n\n    (liveEvents.length)\n      ? e[name] = liveEvents\n      : delete e[name];\n\n    return this;\n  }\n};\n\nmodule.exports = E;\nmodule.exports.TinyEmitter = E;\n\n\n/***/ })\n\n/******/ \t});\n/************************************************************************/\n/******/ \t// The module cache\n/******/ \tvar __webpack_module_cache__ = {};\n/******/ \t\n/******/ \t// The require function\n/******/ \tfunction __webpack_require__(moduleId) {\n/******/ \t\t// Check if module is in cache\n/******/ \t\tif(__webpack_module_cache__[moduleId]) {\n/******/ \t\t\treturn __webpack_module_cache__[moduleId].exports;\n/******/ \t\t}\n/******/ \t\t// Create a new module (and put it into the cache)\n/******/ \t\tvar module = __webpack_module_cache__[moduleId] = {\n/******/ \t\t\t// no module.id needed\n/******/ \t\t\t// no module.loaded needed\n/******/ \t\t\texports: {}\n/******/ \t\t};\n/******/ \t\n/******/ \t\t// Execute the module function\n/******/ \t\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n/******/ \t\n/******/ \t\t// Return the exports of the module\n/******/ \t\treturn module.exports;\n/******/ \t}\n/******/ \t\n/************************************************************************/\n/******/ \t/* webpack/runtime/compat get default export */\n/******/ \t!function() {\n/******/ \t\t// getDefaultExport function for compatibility with non-harmony modules\n/******/ \t\t__webpack_require__.n = function(module) {\n/******/ \t\t\tvar getter = module && module.__esModule ?\n/******/ \t\t\t\tfunction() { return module['default']; } :\n/******/ \t\t\t\tfunction() { return module; };\n/******/ \t\t\t__webpack_require__.d(getter, { a: getter });\n/******/ \t\t\treturn getter;\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/define property getters */\n/******/ \t!function() {\n/******/ \t\t// define getter functions for harmony exports\n/******/ \t\t__webpack_require__.d = function(exports, definition) {\n/******/ \t\t\tfor(var key in definition) {\n/******/ \t\t\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n/******/ \t\t\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n/******/ \t\t\t\t}\n/******/ \t\t\t}\n/******/ \t\t};\n/******/ \t}();\n/******/ \t\n/******/ \t/* webpack/runtime/hasOwnProperty shorthand */\n/******/ \t!function() {\n/******/ \t\t__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }\n/******/ \t}();\n/******/ \t\n/************************************************************************/\n/******/ \t// module exports must be returned from runtime so entry inlining is disabled\n/******/ \t// startup\n/******/ \t// Load entry module and return exports\n/******/ \treturn __webpack_require__(686);\n/******/ })()\n.default;\n});"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC,SAASA,gCAAgCA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACzD,IAAG,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,QAAQ,EAC3DA,MAAM,CAACD,OAAO,GAAGD,OAAO,CAAC,CAAC,CAAC,KACvB,IAAG,OAAOG,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,EACjDD,MAAM,CAAC,EAAE,EAAEH,OAAO,CAAC,CAAC,KAChB,IAAG,OAAOC,OAAO,KAAK,QAAQ,EAClCA,OAAO,CAAC,aAAa,CAAC,GAAGD,OAAO,CAAC,CAAC,CAAC,KAEnCD,IAAI,CAAC,aAAa,CAAC,GAAGC,OAAO,CAAC,CAAC;AACjC,CAAC,EAAE,IAAI,EAAE,YAAW;EACpB,OAAO,QAAU,YAAW;IAAE;IAC9B;IAAU,IAAIK,mBAAmB,GAAI;MAErC,KAAM,GAAG,GACT,KAAO,SADDC,CAAGA,CACOC,uBAAuB,EAAEC,mBAAmB,EAAEC,mBAAmB,EAAE;QAEnF,YAAY;;QAEZ;QACAA,mBAAmB,CAACC,CAAC,CAACF,mBAAmB,EAAE;UACzC,SAAS,EAAE,SAAXG,QAASA,CAAA,EAAa;YAAE,OAAO,aAAcC,SAAS;UAAE;QAC1D,CAAC,CAAC;;QAEF;QACA,IAAIC,YAAY,GAAGJ,mBAAmB,CAAC,GAAG,CAAC;QAC3C,IAAIK,oBAAoB,GAAG,aAAaL,mBAAmB,CAACM,CAAC,CAACF,YAAY,CAAC;QAC3E;QACA,IAAIG,MAAM,GAAGP,mBAAmB,CAAC,GAAG,CAAC;QACrC,IAAIQ,cAAc,GAAG,aAAaR,mBAAmB,CAACM,CAAC,CAACC,MAAM,CAAC;QAC/D;QACA,IAAIE,UAAU,GAAGT,mBAAmB,CAAC,GAAG,CAAC;QACzC,IAAIU,cAAc,GAAG,aAAaV,mBAAmB,CAACM,CAAC,CAACG,UAAU,CAAC;QACnE,CAAC;QACD;AACA;AACA;AACA;AACA;QACA,SAASE,OAAOA,CAACC,IAAI,EAAE;UACrB,IAAI;YACF,OAAOC,QAAQ,CAACC,WAAW,CAACF,IAAI,CAAC;UACnC,CAAC,CAAC,OAAOG,GAAG,EAAE;YACZ,OAAO,KAAK;UACd;QACF;QACA,CAAC;;QAGD;AACA;AACA;AACA;AACA;;QAEA,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,MAAM,EAAE;UAC3D,IAAIC,YAAY,GAAGR,cAAc,CAAC,CAAC,CAACO,MAAM,CAAC;UAC3CN,OAAO,CAAC,KAAK,CAAC;UACd,OAAOO,YAAY;QACrB,CAAC;;QAED;QAA6B,IAAIC,WAAW,GAAIH,kBAAmB;QACnE,CAAC;QACD;AACA;AACA;AACA;AACA;QACA,SAASI,iBAAiBA,CAACC,KAAK,EAAE;UAChC,IAAIC,KAAK,GAAGT,QAAQ,CAACU,eAAe,CAACC,YAAY,CAAC,KAAK,CAAC,KAAK,KAAK;UAClE,IAAIC,WAAW,GAAGZ,QAAQ,CAACa,aAAa,CAAC,UAAU,CAAC,CAAC,CAAC;;UAEtDD,WAAW,CAACE,KAAK,CAACC,QAAQ,GAAG,MAAM,CAAC,CAAC;;UAErCH,WAAW,CAACE,KAAK,CAACE,MAAM,GAAG,GAAG;UAC9BJ,WAAW,CAACE,KAAK,CAACG,OAAO,GAAG,GAAG;UAC/BL,WAAW,CAACE,KAAK,CAACI,MAAM,GAAG,GAAG,CAAC,CAAC;;UAEhCN,WAAW,CAACE,KAAK,CAACK,QAAQ,GAAG,UAAU;UACvCP,WAAW,CAACE,KAAK,CAACL,KAAK,GAAG,OAAO,GAAG,MAAM,CAAC,GAAG,SAAS,CAAC,CAAC;;UAEzD,IAAIW,SAAS,GAAGC,MAAM,CAACC,WAAW,IAAItB,QAAQ,CAACU,eAAe,CAACa,SAAS;UACxEX,WAAW,CAACE,KAAK,CAACU,GAAG,GAAG,EAAE,CAACC,MAAM,CAACL,SAAS,EAAE,IAAI,CAAC;UAClDR,WAAW,CAACc,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC;UACxCd,WAAW,CAACJ,KAAK,GAAGA,KAAK;UACzB,OAAOI,WAAW;QACpB;QACA,CAAC;;QAID;AACA;AACA;AACA;AACA;AACA;;QAEA,IAAIe,cAAc,GAAG,SAASA,cAAcA,CAACnB,KAAK,EAAEoB,OAAO,EAAE;UAC3D,IAAIhB,WAAW,GAAGL,iBAAiB,CAACC,KAAK,CAAC;UAC1CoB,OAAO,CAACC,SAAS,CAACC,WAAW,CAAClB,WAAW,CAAC;UAC1C,IAAIP,YAAY,GAAGR,cAAc,CAAC,CAAC,CAACe,WAAW,CAAC;UAChDd,OAAO,CAAC,MAAM,CAAC;UACfc,WAAW,CAACmB,MAAM,CAAC,CAAC;UACpB,OAAO1B,YAAY;QACrB,CAAC;QACD;AACA;AACA;AACA;AACA;AACA;;QAGA,IAAI2B,mBAAmB,GAAG,SAASA,mBAAmBA,CAAC5B,MAAM,EAAE;UAC7D,IAAIwB,OAAO,GAAGK,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG;YAChFJ,SAAS,EAAE7B,QAAQ,CAACoC;UACtB,CAAC;UACD,IAAI/B,YAAY,GAAG,EAAE;UAErB,IAAI,OAAOD,MAAM,KAAK,QAAQ,EAAE;YAC9BC,YAAY,GAAGsB,cAAc,CAACvB,MAAM,EAAEwB,OAAO,CAAC;UAChD,CAAC,MAAM,IAAIxB,MAAM,YAAYiC,gBAAgB,IAAI,CAAC,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,CAAC,CAACC,QAAQ,CAAClC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACL,IAAI,CAAC,EAAE;YACpK;YACAM,YAAY,GAAGsB,cAAc,CAACvB,MAAM,CAACI,KAAK,EAAEoB,OAAO,CAAC;UACtD,CAAC,MAAM;YACLvB,YAAY,GAAGR,cAAc,CAAC,CAAC,CAACO,MAAM,CAAC;YACvCN,OAAO,CAAC,MAAM,CAAC;UACjB;UAEA,OAAOO,YAAY;QACrB,CAAC;;QAED;QAA6B,IAAIkC,YAAY,GAAIP,mBAAoB;QACrE,CAAC;QACD,SAASQ,OAAOA,CAACC,GAAG,EAAE;UAAE,yBAAyB;;UAAE,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;YAAEH,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;cAAE,OAAO,OAAOA,GAAG;YAAE,CAAC;UAAE,CAAC,MAAM;YAAED,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;cAAE,OAAOA,GAAG,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;YAAE,CAAC;UAAE;UAAE,OAAOD,OAAO,CAACC,GAAG,CAAC;QAAE;;QAIzX;AACA;AACA;AACA;AACA;;QAEA,IAAIK,sBAAsB,GAAG,SAASA,sBAAsBA,CAAA,EAAG;UAC7D,IAAIlB,OAAO,GAAGK,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;UACpF;UACA,IAAIc,eAAe,GAAGnB,OAAO,CAACoB,MAAM;YAChCA,MAAM,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,eAAe;YAC9DlB,SAAS,GAAGD,OAAO,CAACC,SAAS;YAC7BzB,MAAM,GAAGwB,OAAO,CAACxB,MAAM;YACvB6C,IAAI,GAAGrB,OAAO,CAACqB,IAAI,CAAC,CAAC;;UAEzB,IAAID,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,KAAK,EAAE;YACzC,MAAM,IAAIE,KAAK,CAAC,oDAAoD,CAAC;UACvE,CAAC,CAAC;;UAGF,IAAI9C,MAAM,KAAK+B,SAAS,EAAE;YACxB,IAAI/B,MAAM,IAAIoC,OAAO,CAACpC,MAAM,CAAC,KAAK,QAAQ,IAAIA,MAAM,CAAC+C,QAAQ,KAAK,CAAC,EAAE;cACnE,IAAIH,MAAM,KAAK,MAAM,IAAI5C,MAAM,CAACgD,YAAY,CAAC,UAAU,CAAC,EAAE;gBACxD,MAAM,IAAIF,KAAK,CAAC,mFAAmF,CAAC;cACtG;cAEA,IAAIF,MAAM,KAAK,KAAK,KAAK5C,MAAM,CAACgD,YAAY,CAAC,UAAU,CAAC,IAAIhD,MAAM,CAACgD,YAAY,CAAC,UAAU,CAAC,CAAC,EAAE;gBAC5F,MAAM,IAAIF,KAAK,CAAC,wGAAwG,CAAC;cAC3H;YACF,CAAC,MAAM;cACL,MAAM,IAAIA,KAAK,CAAC,6CAA6C,CAAC;YAChE;UACF,CAAC,CAAC;;UAGF,IAAID,IAAI,EAAE;YACR,OAAOV,YAAY,CAACU,IAAI,EAAE;cACxBpB,SAAS,EAAEA;YACb,CAAC,CAAC;UACJ,CAAC,CAAC;;UAGF,IAAIzB,MAAM,EAAE;YACV,OAAO4C,MAAM,KAAK,KAAK,GAAG1C,WAAW,CAACF,MAAM,CAAC,GAAGmC,YAAY,CAACnC,MAAM,EAAE;cACnEyB,SAAS,EAAEA;YACb,CAAC,CAAC;UACJ;QACF,CAAC;;QAED;QAA6B,IAAIwB,eAAe,GAAIP,sBAAuB;QAC3E,CAAC;QACD,SAASQ,gBAAgBA,CAACb,GAAG,EAAE;UAAE,yBAAyB;;UAAE,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;YAAEW,gBAAgB,GAAG,SAASd,OAAOA,CAACC,GAAG,EAAE;cAAE,OAAO,OAAOA,GAAG;YAAE,CAAC;UAAE,CAAC,MAAM;YAAEa,gBAAgB,GAAG,SAASd,OAAOA,CAACC,GAAG,EAAE;cAAE,OAAOA,GAAG,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;YAAE,CAAC;UAAE;UAAE,OAAOa,gBAAgB,CAACb,GAAG,CAAC;QAAE;QAE7Z,SAASc,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;UAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;YAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;UAAE;QAAE;QAExJ,SAASC,iBAAiBA,CAACvD,MAAM,EAAEwD,KAAK,EAAE;UAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAAC1B,MAAM,EAAE2B,CAAC,EAAE,EAAE;YAAE,IAAIC,UAAU,GAAGF,KAAK,CAACC,CAAC,CAAC;YAAEC,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;YAAED,UAAU,CAACE,YAAY,GAAG,IAAI;YAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;YAAEC,MAAM,CAACC,cAAc,CAAC/D,MAAM,EAAE0D,UAAU,CAACM,GAAG,EAAEN,UAAU,CAAC;UAAE;QAAE;QAE5T,SAASO,YAAYA,CAACZ,WAAW,EAAEa,UAAU,EAAEC,WAAW,EAAE;UAAE,IAAID,UAAU,EAAEX,iBAAiB,CAACF,WAAW,CAACZ,SAAS,EAAEyB,UAAU,CAAC;UAAE,IAAIC,WAAW,EAAEZ,iBAAiB,CAACF,WAAW,EAAEc,WAAW,CAAC;UAAE,OAAOd,WAAW;QAAE;QAEtN,SAASe,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;UAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;YAAE,MAAM,IAAIhB,SAAS,CAAC,oDAAoD,CAAC;UAAE;UAAEe,QAAQ,CAAC5B,SAAS,GAAGqB,MAAM,CAACS,MAAM,CAACD,UAAU,IAAIA,UAAU,CAAC7B,SAAS,EAAE;YAAED,WAAW,EAAE;cAAEpC,KAAK,EAAEiE,QAAQ;cAAER,QAAQ,EAAE,IAAI;cAAED,YAAY,EAAE;YAAK;UAAE,CAAC,CAAC;UAAE,IAAIU,UAAU,EAAEE,eAAe,CAACH,QAAQ,EAAEC,UAAU,CAAC;QAAE;QAEhY,SAASE,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;UAAEF,eAAe,GAAGV,MAAM,CAACa,cAAc,IAAI,SAASH,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;YAAED,CAAC,CAACG,SAAS,GAAGF,CAAC;YAAE,OAAOD,CAAC;UAAE,CAAC;UAAE,OAAOD,eAAe,CAACC,CAAC,EAAEC,CAAC,CAAC;QAAE;QAEzK,SAASG,YAAYA,CAACC,OAAO,EAAE;UAAE,IAAIC,yBAAyB,GAAGC,yBAAyB,CAAC,CAAC;UAAE,OAAO,SAASC,oBAAoBA,CAAA,EAAG;YAAE,IAAIC,KAAK,GAAGC,eAAe,CAACL,OAAO,CAAC;cAAEM,MAAM;YAAE,IAAIL,yBAAyB,EAAE;cAAE,IAAIM,SAAS,GAAGF,eAAe,CAAC,IAAI,CAAC,CAAC3C,WAAW;cAAE4C,MAAM,GAAGE,OAAO,CAACC,SAAS,CAACL,KAAK,EAAErD,SAAS,EAAEwD,SAAS,CAAC;YAAE,CAAC,MAAM;cAAED,MAAM,GAAGF,KAAK,CAACM,KAAK,CAAC,IAAI,EAAE3D,SAAS,CAAC;YAAE;YAAE,OAAO4D,0BAA0B,CAAC,IAAI,EAAEL,MAAM,CAAC;UAAE,CAAC;QAAE;QAExa,SAASK,0BAA0BA,CAACC,IAAI,EAAEC,IAAI,EAAE;UAAE,IAAIA,IAAI,KAAKzC,gBAAgB,CAACyC,IAAI,CAAC,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;YAAE,OAAOA,IAAI;UAAE;UAAE,OAAOC,sBAAsB,CAACF,IAAI,CAAC;QAAE;QAEzL,SAASE,sBAAsBA,CAACF,IAAI,EAAE;UAAE,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;YAAE,MAAM,IAAIG,cAAc,CAAC,2DAA2D,CAAC;UAAE;UAAE,OAAOH,IAAI;QAAE;QAErK,SAASV,yBAAyBA,CAAA,EAAG;UAAE,IAAI,OAAOM,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAACC,SAAS,EAAE,OAAO,KAAK;UAAE,IAAID,OAAO,CAACC,SAAS,CAACO,IAAI,EAAE,OAAO,KAAK;UAAE,IAAI,OAAOC,KAAK,KAAK,UAAU,EAAE,OAAO,IAAI;UAAE,IAAI;YAAEC,IAAI,CAACvD,SAAS,CAACwD,QAAQ,CAACN,IAAI,CAACL,OAAO,CAACC,SAAS,CAACS,IAAI,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;YAAE,OAAO,IAAI;UAAE,CAAC,CAAC,OAAOE,CAAC,EAAE;YAAE,OAAO,KAAK;UAAE;QAAE;QAEnU,SAASf,eAAeA,CAACV,CAAC,EAAE;UAAEU,eAAe,GAAGrB,MAAM,CAACa,cAAc,GAAGb,MAAM,CAACqC,cAAc,GAAG,SAAShB,eAAeA,CAACV,CAAC,EAAE;YAAE,OAAOA,CAAC,CAACG,SAAS,IAAId,MAAM,CAACqC,cAAc,CAAC1B,CAAC,CAAC;UAAE,CAAC;UAAE,OAAOU,eAAe,CAACV,CAAC,CAAC;QAAE;;QAO5M;AACA;AACA;AACA;AACA;;QAEA,SAAS2B,iBAAiBA,CAACC,MAAM,EAAEC,OAAO,EAAE;UAC1C,IAAIC,SAAS,GAAG,iBAAiB,CAAClF,MAAM,CAACgF,MAAM,CAAC;UAEhD,IAAI,CAACC,OAAO,CAACtD,YAAY,CAACuD,SAAS,CAAC,EAAE;YACpC;UACF;UAEA,OAAOD,OAAO,CAAC/F,YAAY,CAACgG,SAAS,CAAC;QACxC;QACA;AACA;AACA;AACA;;QAGA,IAAIC,SAAS,GAAG,aAAa,UAAUC,QAAQ,EAAE;UAC/CrC,SAAS,CAACoC,SAAS,EAAEC,QAAQ,CAAC;UAE9B,IAAIC,MAAM,GAAG7B,YAAY,CAAC2B,SAAS,CAAC;;UAEpC;AACF;AACA;AACA;UACE,SAASA,SAASA,CAACG,OAAO,EAAEnF,OAAO,EAAE;YACnC,IAAIoF,KAAK;YAETzD,eAAe,CAAC,IAAI,EAAEqD,SAAS,CAAC;YAEhCI,KAAK,GAAGF,MAAM,CAACf,IAAI,CAAC,IAAI,CAAC;YAEzBiB,KAAK,CAACC,cAAc,CAACrF,OAAO,CAAC;YAE7BoF,KAAK,CAACE,WAAW,CAACH,OAAO,CAAC;YAE1B,OAAOC,KAAK;UACd;UACA;AACF;AACA;AACA;AACA;;UAGE3C,YAAY,CAACuC,SAAS,EAAE,CAAC;YACvBxC,GAAG,EAAE,gBAAgB;YACrB5D,KAAK,EAAE,SAASyG,cAAcA,CAAA,EAAG;cAC/B,IAAIrF,OAAO,GAAGK,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;cACpF,IAAI,CAACe,MAAM,GAAG,OAAOpB,OAAO,CAACoB,MAAM,KAAK,UAAU,GAAGpB,OAAO,CAACoB,MAAM,GAAG,IAAI,CAACmE,aAAa;cACxF,IAAI,CAAC/G,MAAM,GAAG,OAAOwB,OAAO,CAACxB,MAAM,KAAK,UAAU,GAAGwB,OAAO,CAACxB,MAAM,GAAG,IAAI,CAACgH,aAAa;cACxF,IAAI,CAACnE,IAAI,GAAG,OAAOrB,OAAO,CAACqB,IAAI,KAAK,UAAU,GAAGrB,OAAO,CAACqB,IAAI,GAAG,IAAI,CAACoE,WAAW;cAChF,IAAI,CAACxF,SAAS,GAAGyB,gBAAgB,CAAC1B,OAAO,CAACC,SAAS,CAAC,KAAK,QAAQ,GAAGD,OAAO,CAACC,SAAS,GAAG7B,QAAQ,CAACoC,IAAI;YACvG;YACA;AACJ;AACA;AACA;UAEE,CAAC,EAAE;YACDgC,GAAG,EAAE,aAAa;YAClB5D,KAAK,EAAE,SAAS0G,WAAWA,CAACH,OAAO,EAAE;cACnC,IAAIO,MAAM,GAAG,IAAI;cAEjB,IAAI,CAACC,QAAQ,GAAG5H,cAAc,CAAC,CAAC,CAACoH,OAAO,EAAE,OAAO,EAAE,UAAUT,CAAC,EAAE;gBAC9D,OAAOgB,MAAM,CAACE,OAAO,CAAClB,CAAC,CAAC;cAC1B,CAAC,CAAC;YACJ;YACA;AACJ;AACA;AACA;UAEE,CAAC,EAAE;YACDlC,GAAG,EAAE,SAAS;YACd5D,KAAK,EAAE,SAASgH,OAAOA,CAAClB,CAAC,EAAE;cACzB,IAAIS,OAAO,GAAGT,CAAC,CAACmB,cAAc,IAAInB,CAAC,CAACoB,aAAa;cACjD,IAAI1E,MAAM,GAAG,IAAI,CAACA,MAAM,CAAC+D,OAAO,CAAC,IAAI,MAAM;cAC3C,IAAI9D,IAAI,GAAGI,eAAe,CAAC;gBACzBL,MAAM,EAAEA,MAAM;gBACdnB,SAAS,EAAE,IAAI,CAACA,SAAS;gBACzBzB,MAAM,EAAE,IAAI,CAACA,MAAM,CAAC2G,OAAO,CAAC;gBAC5B9D,IAAI,EAAE,IAAI,CAACA,IAAI,CAAC8D,OAAO;cACzB,CAAC,CAAC,CAAC,CAAC;;cAEJ,IAAI,CAACY,IAAI,CAAC1E,IAAI,GAAG,SAAS,GAAG,OAAO,EAAE;gBACpCD,MAAM,EAAEA,MAAM;gBACdC,IAAI,EAAEA,IAAI;gBACV8D,OAAO,EAAEA,OAAO;gBAChBa,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG;kBACxC,IAAIb,OAAO,EAAE;oBACXA,OAAO,CAACc,KAAK,CAAC,CAAC;kBACjB;kBAEAxG,MAAM,CAACyG,YAAY,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC;gBACzC;cACF,CAAC,CAAC;YACJ;YACA;AACJ;AACA;AACA;UAEE,CAAC,EAAE;YACD3D,GAAG,EAAE,eAAe;YACpB5D,KAAK,EAAE,SAAS2G,aAAaA,CAACJ,OAAO,EAAE;cACrC,OAAOP,iBAAiB,CAAC,QAAQ,EAAEO,OAAO,CAAC;YAC7C;YACA;AACJ;AACA;AACA;UAEE,CAAC,EAAE;YACD3C,GAAG,EAAE,eAAe;YACpB5D,KAAK,EAAE,SAAS4G,aAAaA,CAACL,OAAO,EAAE;cACrC,IAAIiB,QAAQ,GAAGxB,iBAAiB,CAAC,QAAQ,EAAEO,OAAO,CAAC;cAEnD,IAAIiB,QAAQ,EAAE;gBACZ,OAAOhI,QAAQ,CAACiI,aAAa,CAACD,QAAQ,CAAC;cACzC;YACF;YACA;AACJ;AACA;AACA;AACA;AACA;UAEE,CAAC,EAAE;YACD5D,GAAG,EAAE,aAAa;YAElB;AACJ;AACA;AACA;YACI5D,KAAK,EAAE,SAAS6G,WAAWA,CAACN,OAAO,EAAE;cACnC,OAAOP,iBAAiB,CAAC,MAAM,EAAEO,OAAO,CAAC;YAC3C;YACA;AACJ;AACA;UAEE,CAAC,EAAE;YACD3C,GAAG,EAAE,SAAS;YACd5D,KAAK,EAAE,SAAS0H,OAAOA,CAAA,EAAG;cACxB,IAAI,CAACX,QAAQ,CAACW,OAAO,CAAC,CAAC;YACzB;UACF,CAAC,CAAC,EAAE,CAAC;YACH9D,GAAG,EAAE,MAAM;YACX5D,KAAK,EAAE,SAAS2H,IAAIA,CAAC/H,MAAM,EAAE;cAC3B,IAAIwB,OAAO,GAAGK,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG;gBAChFJ,SAAS,EAAE7B,QAAQ,CAACoC;cACtB,CAAC;cACD,OAAOG,YAAY,CAACnC,MAAM,EAAEwB,OAAO,CAAC;YACtC;YACA;AACJ;AACA;AACA;AACA;UAEE,CAAC,EAAE;YACDwC,GAAG,EAAE,KAAK;YACV5D,KAAK,EAAE,SAAS4H,GAAGA,CAAChI,MAAM,EAAE;cAC1B,OAAOE,WAAW,CAACF,MAAM,CAAC;YAC5B;YACA;AACJ;AACA;AACA;AACA;UAEE,CAAC,EAAE;YACDgE,GAAG,EAAE,aAAa;YAClB5D,KAAK,EAAE,SAAS6H,WAAWA,CAAA,EAAG;cAC5B,IAAIrF,MAAM,GAAGf,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC;cAChG,IAAIqG,OAAO,GAAG,OAAOtF,MAAM,KAAK,QAAQ,GAAG,CAACA,MAAM,CAAC,GAAGA,MAAM;cAC5D,IAAIuF,OAAO,GAAG,CAAC,CAACvI,QAAQ,CAACwI,qBAAqB;cAC9CF,OAAO,CAACG,OAAO,CAAC,UAAUzF,MAAM,EAAE;gBAChCuF,OAAO,GAAGA,OAAO,IAAI,CAAC,CAACvI,QAAQ,CAACwI,qBAAqB,CAACxF,MAAM,CAAC;cAC/D,CAAC,CAAC;cACF,OAAOuF,OAAO;YAChB;UACF,CAAC,CAAC,CAAC;UAEH,OAAO3B,SAAS;QAClB,CAAC,CAAEpH,oBAAoB,CAAC,CAAE,CAAC;;QAE3B;QAA6B,IAAIF,SAAS,GAAIsH,SAAU;;QAExD;MAAM,CAAC,CAAC;MAER,KAAM,GAAG,GACT,KAAO,SADD5H,CAAGA,CACOJ,MAAM,EAAE;QAExB,IAAI8J,kBAAkB,GAAG,CAAC;;QAE1B;AACA;AACA;QACA,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAI,CAACA,OAAO,CAAC9F,SAAS,CAAC+F,OAAO,EAAE;UAC9D,IAAIC,KAAK,GAAGF,OAAO,CAAC9F,SAAS;UAE7BgG,KAAK,CAACD,OAAO,GAAGC,KAAK,CAACC,eAAe,IACrBD,KAAK,CAACE,kBAAkB,IACxBF,KAAK,CAACG,iBAAiB,IACvBH,KAAK,CAACI,gBAAgB,IACtBJ,KAAK,CAACK,qBAAqB;QAC/C;;QAEA;AACA;AACA;AACA;AACA;AACA;AACA;QACA,SAASC,OAAOA,CAAEzC,OAAO,EAAEsB,QAAQ,EAAE;UACjC,OAAOtB,OAAO,IAAIA,OAAO,CAACvD,QAAQ,KAAKuF,kBAAkB,EAAE;YACvD,IAAI,OAAOhC,OAAO,CAACkC,OAAO,KAAK,UAAU,IACrClC,OAAO,CAACkC,OAAO,CAACZ,QAAQ,CAAC,EAAE;cAC7B,OAAOtB,OAAO;YAChB;YACAA,OAAO,GAAGA,OAAO,CAAC0C,UAAU;UAChC;QACJ;QAEAxK,MAAM,CAACD,OAAO,GAAGwK,OAAO;;QAGxB;MAAM,CAAC,CAAC;MAER,KAAM,GAAG,GACT,KAAO,SADDnK,CAAGA,CACOJ,MAAM,EAAEyK,wBAAwB,EAAElK,mBAAmB,EAAE;QAEvE,IAAIgK,OAAO,GAAGhK,mBAAmB,CAAC,GAAG,CAAC;;QAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACA,SAASmK,SAASA,CAAC5C,OAAO,EAAEsB,QAAQ,EAAEjI,IAAI,EAAEwJ,QAAQ,EAAEC,UAAU,EAAE;UAC9D,IAAIC,UAAU,GAAGlC,QAAQ,CAAC3B,KAAK,CAAC,IAAI,EAAE3D,SAAS,CAAC;UAEhDyE,OAAO,CAACgD,gBAAgB,CAAC3J,IAAI,EAAE0J,UAAU,EAAED,UAAU,CAAC;UAEtD,OAAO;YACHtB,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAa;cAChBxB,OAAO,CAACiD,mBAAmB,CAAC5J,IAAI,EAAE0J,UAAU,EAAED,UAAU,CAAC;YAC7D;UACJ,CAAC;QACL;;QAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACA,SAASI,QAAQA,CAACC,QAAQ,EAAE7B,QAAQ,EAAEjI,IAAI,EAAEwJ,QAAQ,EAAEC,UAAU,EAAE;UAC9D;UACA,IAAI,OAAOK,QAAQ,CAACH,gBAAgB,KAAK,UAAU,EAAE;YACjD,OAAOJ,SAAS,CAAC1D,KAAK,CAAC,IAAI,EAAE3D,SAAS,CAAC;UAC3C;;UAEA;UACA,IAAI,OAAOlC,IAAI,KAAK,UAAU,EAAE;YAC5B;YACA;YACA,OAAOuJ,SAAS,CAACQ,IAAI,CAAC,IAAI,EAAE9J,QAAQ,CAAC,CAAC4F,KAAK,CAAC,IAAI,EAAE3D,SAAS,CAAC;UAChE;;UAEA;UACA,IAAI,OAAO4H,QAAQ,KAAK,QAAQ,EAAE;YAC9BA,QAAQ,GAAG7J,QAAQ,CAAC+J,gBAAgB,CAACF,QAAQ,CAAC;UAClD;;UAEA;UACA,OAAOG,KAAK,CAACnH,SAAS,CAACoH,GAAG,CAAClE,IAAI,CAAC8D,QAAQ,EAAE,UAAUnD,OAAO,EAAE;YACzD,OAAO4C,SAAS,CAAC5C,OAAO,EAAEsB,QAAQ,EAAEjI,IAAI,EAAEwJ,QAAQ,EAAEC,UAAU,CAAC;UACnE,CAAC,CAAC;QACN;;QAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACA,SAASjC,QAAQA,CAACb,OAAO,EAAEsB,QAAQ,EAAEjI,IAAI,EAAEwJ,QAAQ,EAAE;UACjD,OAAO,UAASjD,CAAC,EAAE;YACfA,CAAC,CAACmB,cAAc,GAAG0B,OAAO,CAAC7C,CAAC,CAAClG,MAAM,EAAE4H,QAAQ,CAAC;YAE9C,IAAI1B,CAAC,CAACmB,cAAc,EAAE;cAClB8B,QAAQ,CAACxD,IAAI,CAACW,OAAO,EAAEJ,CAAC,CAAC;YAC7B;UACJ,CAAC;QACL;QAEA1H,MAAM,CAACD,OAAO,GAAGiL,QAAQ;;QAGzB;MAAM,CAAC,CAAC;MAER,KAAM,GAAG,GACT,KAAO,SADD5K,CAAGA,CACOC,uBAAuB,EAAEN,OAAO,EAAE;QAElD;AACA;AACA;AACA;AACA;AACA;QACAA,OAAO,CAACuL,IAAI,GAAG,UAAS1J,KAAK,EAAE;UAC3B,OAAOA,KAAK,KAAK2B,SAAS,IACnB3B,KAAK,YAAY2J,WAAW,IAC5B3J,KAAK,CAAC2C,QAAQ,KAAK,CAAC;QAC/B,CAAC;;QAED;AACA;AACA;AACA;AACA;AACA;QACAxE,OAAO,CAACyL,QAAQ,GAAG,UAAS5J,KAAK,EAAE;UAC/B,IAAIT,IAAI,GAAGmE,MAAM,CAACrB,SAAS,CAACwD,QAAQ,CAACN,IAAI,CAACvF,KAAK,CAAC;UAEhD,OAAOA,KAAK,KAAK2B,SAAS,KAClBpC,IAAI,KAAK,mBAAmB,IAAIA,IAAI,KAAK,yBAAyB,CAAC,IACnE,QAAQ,IAAIS,KAAM,KAClBA,KAAK,CAAC0B,MAAM,KAAK,CAAC,IAAIvD,OAAO,CAACuL,IAAI,CAAC1J,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QACzD,CAAC;;QAED;AACA;AACA;AACA;AACA;AACA;QACA7B,OAAO,CAAC0L,MAAM,GAAG,UAAS7J,KAAK,EAAE;UAC7B,OAAO,OAAOA,KAAK,KAAK,QAAQ,IACzBA,KAAK,YAAY8J,MAAM;QAClC,CAAC;;QAED;AACA;AACA;AACA;AACA;AACA;QACA3L,OAAO,CAAC4L,EAAE,GAAG,UAAS/J,KAAK,EAAE;UACzB,IAAIT,IAAI,GAAGmE,MAAM,CAACrB,SAAS,CAACwD,QAAQ,CAACN,IAAI,CAACvF,KAAK,CAAC;UAEhD,OAAOT,IAAI,KAAK,mBAAmB;QACvC,CAAC;;QAGD;MAAM,CAAC,CAAC;MAER,KAAM,GAAG,GACT,KAAO,SADDf,CAAGA,CACOJ,MAAM,EAAEyK,wBAAwB,EAAElK,mBAAmB,EAAE;QAEvE,IAAIqL,EAAE,GAAGrL,mBAAmB,CAAC,GAAG,CAAC;QACjC,IAAIyK,QAAQ,GAAGzK,mBAAmB,CAAC,GAAG,CAAC;;QAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACA,SAASO,MAAMA,CAACU,MAAM,EAAEL,IAAI,EAAEwJ,QAAQ,EAAE;UACpC,IAAI,CAACnJ,MAAM,IAAI,CAACL,IAAI,IAAI,CAACwJ,QAAQ,EAAE;YAC/B,MAAM,IAAIrG,KAAK,CAAC,4BAA4B,CAAC;UACjD;UAEA,IAAI,CAACsH,EAAE,CAACH,MAAM,CAACtK,IAAI,CAAC,EAAE;YAClB,MAAM,IAAI2D,SAAS,CAAC,kCAAkC,CAAC;UAC3D;UAEA,IAAI,CAAC8G,EAAE,CAACD,EAAE,CAAChB,QAAQ,CAAC,EAAE;YAClB,MAAM,IAAI7F,SAAS,CAAC,mCAAmC,CAAC;UAC5D;UAEA,IAAI8G,EAAE,CAACN,IAAI,CAAC9J,MAAM,CAAC,EAAE;YACjB,OAAOqK,UAAU,CAACrK,MAAM,EAAEL,IAAI,EAAEwJ,QAAQ,CAAC;UAC7C,CAAC,MACI,IAAIiB,EAAE,CAACJ,QAAQ,CAAChK,MAAM,CAAC,EAAE;YAC1B,OAAOsK,cAAc,CAACtK,MAAM,EAAEL,IAAI,EAAEwJ,QAAQ,CAAC;UACjD,CAAC,MACI,IAAIiB,EAAE,CAACH,MAAM,CAACjK,MAAM,CAAC,EAAE;YACxB,OAAOuK,cAAc,CAACvK,MAAM,EAAEL,IAAI,EAAEwJ,QAAQ,CAAC;UACjD,CAAC,MACI;YACD,MAAM,IAAI7F,SAAS,CAAC,2EAA2E,CAAC;UACpG;QACJ;;QAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACA,SAAS+G,UAAUA,CAACP,IAAI,EAAEnK,IAAI,EAAEwJ,QAAQ,EAAE;UACtCW,IAAI,CAACR,gBAAgB,CAAC3J,IAAI,EAAEwJ,QAAQ,CAAC;UAErC,OAAO;YACHrB,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAa;cAChBgC,IAAI,CAACP,mBAAmB,CAAC5J,IAAI,EAAEwJ,QAAQ,CAAC;YAC5C;UACJ,CAAC;QACL;;QAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACA,SAASmB,cAAcA,CAACN,QAAQ,EAAErK,IAAI,EAAEwJ,QAAQ,EAAE;UAC9CS,KAAK,CAACnH,SAAS,CAAC4F,OAAO,CAAC1C,IAAI,CAACqE,QAAQ,EAAE,UAASF,IAAI,EAAE;YAClDA,IAAI,CAACR,gBAAgB,CAAC3J,IAAI,EAAEwJ,QAAQ,CAAC;UACzC,CAAC,CAAC;UAEF,OAAO;YACHrB,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAa;cAChB8B,KAAK,CAACnH,SAAS,CAAC4F,OAAO,CAAC1C,IAAI,CAACqE,QAAQ,EAAE,UAASF,IAAI,EAAE;gBAClDA,IAAI,CAACP,mBAAmB,CAAC5J,IAAI,EAAEwJ,QAAQ,CAAC;cAC5C,CAAC,CAAC;YACN;UACJ,CAAC;QACL;;QAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACA,SAASoB,cAAcA,CAAC3C,QAAQ,EAAEjI,IAAI,EAAEwJ,QAAQ,EAAE;UAC9C,OAAOK,QAAQ,CAAC5J,QAAQ,CAACoC,IAAI,EAAE4F,QAAQ,EAAEjI,IAAI,EAAEwJ,QAAQ,CAAC;QAC5D;QAEA3K,MAAM,CAACD,OAAO,GAAGe,MAAM;;QAGvB;MAAM,CAAC,CAAC;MAER,KAAM,GAAG,GACT,KAAO,SADDV,CAAGA,CACOJ,MAAM,EAAE;QAExB,SAASgM,MAAMA,CAAClE,OAAO,EAAE;UACrB,IAAIrG,YAAY;UAEhB,IAAIqG,OAAO,CAACmE,QAAQ,KAAK,QAAQ,EAAE;YAC/BnE,OAAO,CAACmB,KAAK,CAAC,CAAC;YAEfxH,YAAY,GAAGqG,OAAO,CAAClG,KAAK;UAChC,CAAC,MACI,IAAIkG,OAAO,CAACmE,QAAQ,KAAK,OAAO,IAAInE,OAAO,CAACmE,QAAQ,KAAK,UAAU,EAAE;YACtE,IAAIC,UAAU,GAAGpE,OAAO,CAACtD,YAAY,CAAC,UAAU,CAAC;YAEjD,IAAI,CAAC0H,UAAU,EAAE;cACbpE,OAAO,CAAChF,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC;YACxC;YAEAgF,OAAO,CAACkE,MAAM,CAAC,CAAC;YAChBlE,OAAO,CAACqE,iBAAiB,CAAC,CAAC,EAAErE,OAAO,CAAClG,KAAK,CAAC0B,MAAM,CAAC;YAElD,IAAI,CAAC4I,UAAU,EAAE;cACbpE,OAAO,CAACsE,eAAe,CAAC,UAAU,CAAC;YACvC;YAEA3K,YAAY,GAAGqG,OAAO,CAAClG,KAAK;UAChC,CAAC,MACI;YACD,IAAIkG,OAAO,CAACtD,YAAY,CAAC,iBAAiB,CAAC,EAAE;cACzCsD,OAAO,CAACmB,KAAK,CAAC,CAAC;YACnB;YAEA,IAAIoD,SAAS,GAAG5J,MAAM,CAACyG,YAAY,CAAC,CAAC;YACrC,IAAIoD,KAAK,GAAGlL,QAAQ,CAACmL,WAAW,CAAC,CAAC;YAElCD,KAAK,CAACE,kBAAkB,CAAC1E,OAAO,CAAC;YACjCuE,SAAS,CAAClD,eAAe,CAAC,CAAC;YAC3BkD,SAAS,CAACI,QAAQ,CAACH,KAAK,CAAC;YAEzB7K,YAAY,GAAG4K,SAAS,CAAC5E,QAAQ,CAAC,CAAC;UACvC;UAEA,OAAOhG,YAAY;QACvB;QAEAzB,MAAM,CAACD,OAAO,GAAGiM,MAAM;;QAGvB;MAAM,CAAC,CAAC;MAER,KAAM,GAAG,GACT,KAAO,SADD5L,CAAGA,CACOJ,MAAM,EAAE;QAExB,SAAS0M,CAACA,CAAA,EAAI;UACZ;UACA;QAAA;QAGFA,CAAC,CAACzI,SAAS,GAAG;UACZ0I,EAAE,EAAE,SAAJA,EAAEA,CAAYC,IAAI,EAAEjC,QAAQ,EAAEkC,GAAG,EAAE;YACjC,IAAInF,CAAC,GAAG,IAAI,CAACA,CAAC,KAAK,IAAI,CAACA,CAAC,GAAG,CAAC,CAAC,CAAC;YAE/B,CAACA,CAAC,CAACkF,IAAI,CAAC,KAAKlF,CAAC,CAACkF,IAAI,CAAC,GAAG,EAAE,CAAC,EAAEE,IAAI,CAAC;cAC/BnB,EAAE,EAAEhB,QAAQ;cACZkC,GAAG,EAAEA;YACP,CAAC,CAAC;YAEF,OAAO,IAAI;UACb,CAAC;UAEDE,IAAI,EAAE,SAANA,IAAIA,CAAYH,IAAI,EAAEjC,QAAQ,EAAEkC,GAAG,EAAE;YACnC,IAAI3F,IAAI,GAAG,IAAI;YACf,SAASyB,QAAQA,CAAA,EAAI;cACnBzB,IAAI,CAAC8F,GAAG,CAACJ,IAAI,EAAEjE,QAAQ,CAAC;cACxBgC,QAAQ,CAAC3D,KAAK,CAAC6F,GAAG,EAAExJ,SAAS,CAAC;YAChC;YAAC;YAEDsF,QAAQ,CAACvI,CAAC,GAAGuK,QAAQ;YACrB,OAAO,IAAI,CAACgC,EAAE,CAACC,IAAI,EAAEjE,QAAQ,EAAEkE,GAAG,CAAC;UACrC,CAAC;UAED9D,IAAI,EAAE,SAANA,IAAIA,CAAY6D,IAAI,EAAE;YACpB,IAAIK,IAAI,GAAG,EAAE,CAACC,KAAK,CAAC/F,IAAI,CAAC9D,SAAS,EAAE,CAAC,CAAC;YACtC,IAAI8J,MAAM,GAAG,CAAC,CAAC,IAAI,CAACzF,CAAC,KAAK,IAAI,CAACA,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEkF,IAAI,CAAC,IAAI,EAAE,EAAEM,KAAK,CAAC,CAAC;YAC5D,IAAIjI,CAAC,GAAG,CAAC;YACT,IAAImI,GAAG,GAAGD,MAAM,CAAC7J,MAAM;YAEvB,KAAK2B,CAAC,EAAEA,CAAC,GAAGmI,GAAG,EAAEnI,CAAC,EAAE,EAAE;cACpBkI,MAAM,CAAClI,CAAC,CAAC,CAAC0G,EAAE,CAAC3E,KAAK,CAACmG,MAAM,CAAClI,CAAC,CAAC,CAAC4H,GAAG,EAAEI,IAAI,CAAC;YACzC;YAEA,OAAO,IAAI;UACb,CAAC;UAEDD,GAAG,EAAE,SAALA,GAAGA,CAAYJ,IAAI,EAAEjC,QAAQ,EAAE;YAC7B,IAAIjD,CAAC,GAAG,IAAI,CAACA,CAAC,KAAK,IAAI,CAACA,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/B,IAAI2F,IAAI,GAAG3F,CAAC,CAACkF,IAAI,CAAC;YAClB,IAAIU,UAAU,GAAG,EAAE;YAEnB,IAAID,IAAI,IAAI1C,QAAQ,EAAE;cACpB,KAAK,IAAI1F,CAAC,GAAG,CAAC,EAAEmI,GAAG,GAAGC,IAAI,CAAC/J,MAAM,EAAE2B,CAAC,GAAGmI,GAAG,EAAEnI,CAAC,EAAE,EAAE;gBAC/C,IAAIoI,IAAI,CAACpI,CAAC,CAAC,CAAC0G,EAAE,KAAKhB,QAAQ,IAAI0C,IAAI,CAACpI,CAAC,CAAC,CAAC0G,EAAE,CAACvL,CAAC,KAAKuK,QAAQ,EACtD2C,UAAU,CAACR,IAAI,CAACO,IAAI,CAACpI,CAAC,CAAC,CAAC;cAC5B;YACF;;YAEA;YACA;YACA;;YAECqI,UAAU,CAAChK,MAAM,GACdoE,CAAC,CAACkF,IAAI,CAAC,GAAGU,UAAU,GACpB,OAAO5F,CAAC,CAACkF,IAAI,CAAC;YAElB,OAAO,IAAI;UACb;QACF,CAAC;QAED5M,MAAM,CAACD,OAAO,GAAG2M,CAAC;QAClB1M,MAAM,CAACD,OAAO,CAACwN,WAAW,GAAGb,CAAC;;QAG9B;MAAM,CAAC;;MAEP;IAAU,CAAE;IACZ;IACA,SAAU;IACV;IAAU,IAAIc,wBAAwB,GAAG,CAAC,CAAC;IAC3C;IACA,SAAU;IACV;IAAU,SAASjN,mBAAmBA,CAACkN,QAAQ,EAAE;MACjD,SAAW;MACX,QAAW,IAAGD,wBAAwB,CAACC,QAAQ,CAAC,EAAE;QAClD,QAAY,OAAOD,wBAAwB,CAACC,QAAQ,CAAC,CAAC1N,OAAO;QAC7D;MAAW;MACX,SAAW;MACX;MAAW,IAAIC,MAAM,GAAGwN,wBAAwB,CAACC,QAAQ,CAAC,GAAG;QAC7D,SAAY;QACZ,SAAY;QACZ,QAAY1N,OAAO,EAAE,CAAC;QACtB;MAAW,CAAC;MACZ;MACA,SAAW;MACX;MAAWI,mBAAmB,CAACsN,QAAQ,CAAC,CAACzN,MAAM,EAAEA,MAAM,CAACD,OAAO,EAAEQ,mBAAmB,CAAC;MACrF;MACA,SAAW;MACX;MAAW,OAAOP,MAAM,CAACD,OAAO;MAChC;IAAU;IACV;IACA;IACA,SAAU;IACV;IAAU,CAAC,YAAW;MACtB,SAAW;MACX,QAAWQ,mBAAmB,CAACM,CAAC,GAAG,UAASb,MAAM,EAAE;QACpD,QAAY,IAAI0N,MAAM,GAAG1N,MAAM,IAAIA,MAAM,CAAC2N,UAAU,GACpD,QAAa,YAAW;UAAE,OAAO3N,MAAM,CAAC,SAAS,CAAC;QAAE,CAAC,GACrD,QAAa,YAAW;UAAE,OAAOA,MAAM;QAAE,CAAC;QAC1C;QAAYO,mBAAmB,CAACC,CAAC,CAACkN,MAAM,EAAE;UAAEE,CAAC,EAAEF;QAAO,CAAC,CAAC;QACxD;QAAY,OAAOA,MAAM;QACzB;MAAW,CAAC;MACZ;IAAU,CAAC,CAAC,CAAC;IACb;IACA,SAAU;IACV;IAAU,CAAC,YAAW;MACtB,SAAW;MACX,QAAWnN,mBAAmB,CAACC,CAAC,GAAG,UAAST,OAAO,EAAE8N,UAAU,EAAE;QACjE,QAAY,KAAI,IAAIrI,GAAG,IAAIqI,UAAU,EAAE;UACvC,QAAa,IAAGtN,mBAAmB,CAAC0F,CAAC,CAAC4H,UAAU,EAAErI,GAAG,CAAC,IAAI,CAACjF,mBAAmB,CAAC0F,CAAC,CAAClG,OAAO,EAAEyF,GAAG,CAAC,EAAE;YAChG,QAAcF,MAAM,CAACC,cAAc,CAACxF,OAAO,EAAEyF,GAAG,EAAE;cAAEL,UAAU,EAAE,IAAI;cAAE2I,GAAG,EAAED,UAAU,CAACrI,GAAG;YAAE,CAAC,CAAC;YAC7F;UAAa;UACb;QAAY;QACZ;MAAW,CAAC;MACZ;IAAU,CAAC,CAAC,CAAC;IACb;IACA,SAAU;IACV;IAAU,CAAC,YAAW;MACtB,QAAWjF,mBAAmB,CAAC0F,CAAC,GAAG,UAASpC,GAAG,EAAEkK,IAAI,EAAE;QAAE,OAAOzI,MAAM,CAACrB,SAAS,CAAC+J,cAAc,CAAC7G,IAAI,CAACtD,GAAG,EAAEkK,IAAI,CAAC;MAAE,CAAC;MAClH;IAAU,CAAC,CAAC,CAAC;IACb;IACA;IACA,SAAU;IACV,SAAU;IACV,SAAU;IACV;IAAU,OAAOxN,mBAAmB,CAAC,GAAG,CAAC;IACzC;EAAS,CAAC,CAAE,CAAC,CACZ0N,OAAO;AACR,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}