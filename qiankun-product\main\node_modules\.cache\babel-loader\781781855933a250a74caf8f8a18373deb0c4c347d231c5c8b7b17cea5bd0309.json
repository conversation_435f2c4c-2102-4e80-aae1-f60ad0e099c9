{"ast": null, "code": "import { createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"short-message-box\"\n};\nvar _hoisted_2 = {\n  class: \"short-message-box-name\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_cache[2] || (_cache[2] = _createTextVNode(\" 短信模板 \")), _createVNode(_component_el_button, {\n    onClick: $setup.templateInfo,\n    type: \"primary\"\n  }, {\n    default: _withCtx(function () {\n      return _cache[1] || (_cache[1] = [_createTextVNode(\"更新模板\")]);\n    }),\n    _: 1 /* STABLE */\n  })]), _createVNode(_component_el_input, {\n    modelValue: $setup.content,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.content = $event;\n    }),\n    placeholder: \"请输入短信模板\",\n    type: \"textarea\",\n    rows: 5\n  }, null, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createTextVNode", "_createVNode", "_component_el_button", "onClick", "$setup", "templateInfo", "type", "default", "_withCtx", "_cache", "_", "_component_el_input", "modelValue", "content", "$event", "placeholder", "rows"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\common\\components\\short-message-box\\short-message-box.vue"], "sourcesContent": ["<template>\r\n  <div class=\"short-message-box\">\r\n    <div class=\"short-message-box-name\">\r\n      短信模板\r\n      <el-button @click=\"templateInfo\" type=\"primary\">更新模板</el-button>\r\n    </div>\r\n    <el-input v-model=\"content\" placeholder=\"请输入短信模板\" type=\"textarea\" :rows=\"5\" />\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ShortMessageBox' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { computed, onMounted } from 'vue'\r\nconst props = defineProps({\r\n  modelValue: [String, Number],\r\n  code: { type: String, default: '' }\r\n})\r\nconst emit = defineEmits(['update:modelValue'])\r\n\r\nconst content = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(value) {\r\n    emit('update:modelValue', value)\r\n  }\r\n})\r\n\r\nonMounted(() => {\r\n  if (props.code) {\r\n    templateInfo()\r\n  }\r\n})\r\n\r\nconst templateInfo = async () => {\r\n  const { data } = await api.templateInfo({ businessCode: props.code })\r\n  content.value = data\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.short-message-box {\r\n  width: 100%;\r\n\r\n  .short-message-box-name {\r\n    display: flex;\r\n    align-items: flex-end;\r\n    font-size: var(--zy-text-font-size);\r\n    line-height: var(--zy-line-height);\r\n    margin-bottom: var(--zy-distance-four);\r\n\r\n    .zy-el-button {\r\n      margin-left: var(--zy-distance-two);\r\n      height: var(--zy-height-secondary);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAmB;;EACvBA,KAAK,EAAC;AAAwB;;;;uBADrCC,mBAAA,CAMM,OANNC,UAMM,GALJC,mBAAA,CAGM,OAHNC,UAGM,G,0BALVC,gBAAA,CAEwC,QAElC,IAAAC,YAAA,CAAgEC,oBAAA;IAApDC,OAAK,EAAEC,MAAA,CAAAC,YAAY;IAAEC,IAAI,EAAC;;IAJ5CC,OAAA,EAAAC,QAAA,CAIsD;MAAA,OAAIC,MAAA,QAAAA,MAAA,OAJ1DT,gBAAA,CAIsD,MAAI,E;;IAJ1DU,CAAA;QAMIT,YAAA,CAA8EU,mBAAA;IANlFC,UAAA,EAMuBR,MAAA,CAAAS,OAAO;IAN9B,uBAAAJ,MAAA,QAAAA,MAAA,gBAAAK,MAAA;MAAA,OAMuBV,MAAA,CAAAS,OAAO,GAAAC,MAAA;IAAA;IAAEC,WAAW,EAAC,SAAS;IAACT,IAAI,EAAC,UAAU;IAAEU,IAAI,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}