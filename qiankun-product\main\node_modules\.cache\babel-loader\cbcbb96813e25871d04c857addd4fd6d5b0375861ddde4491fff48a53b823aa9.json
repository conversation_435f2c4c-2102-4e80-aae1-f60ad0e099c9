{"ast": null, "code": "import { createVNode as _createVNode, resolveComponent as _resolveComponent, resolveDirective as _resolveDirective, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, withDirectives as _withDirectives } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _withDirectives((_openBlock(), _createBlock(_component_el_scrollbar, {\n    class: \"preview-ppt\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"VueOfficePptx\"], {\n        src: $setup.fileUrl,\n        onRendered: $setup.renderedHandler,\n        onError: $setup.errorHandler\n      }, null, 8 /* PROPS */, [\"src\"])];\n    }),\n    _: 1 /* STABLE */\n  })), [[_directive_loading, $setup.loading]]);\n}", "map": {"version": 3, "names": ["_createBlock", "_component_el_scrollbar", "class", "default", "_withCtx", "_createVNode", "$setup", "src", "fileUrl", "onRendered", "<PERSON><PERSON><PERSON><PERSON>", "onError", "<PERSON><PERSON><PERSON><PERSON>", "_", "loading"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\main\\src\\components\\global-file-preview\\components\\preview-ppt.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar class=\"preview-ppt\" v-loading=\"loading\">\r\n    <vue-office-pptx :src=\"fileUrl\" @rendered=\"renderedHandler\" @error=\"errorHandler\" />\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nexport default { name: 'PreviewPpt' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport VueOfficePptx from '@vue-office/pptx'\r\nconst props = defineProps({ id: { type: String, default: '' }, type: { type: String, default: '' } })\r\nconst fileUrl = ref('')\r\nconst loading = ref(false)\r\nonMounted(() => { globalDownload() })\r\n\r\nconst globalDownload = async () => {\r\n  loading.value = true\r\n  const res = await api.globalDownload(props.id)\r\n  fileUrl.value = URL.createObjectURL(res)\r\n}\r\nconst renderedHandler = () => {\r\n  loading.value = false\r\n  console.log(\"渲染完成\")\r\n}\r\nconst errorHandler = () => {\r\n  loading.value = false\r\n  console.log(\"渲染失败\")\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.preview-ppt {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .vue-office-pptx {\r\n    width: 100%;\r\n    max-width: 1200px;\r\n    height: auto !important;\r\n    margin: 0 auto;\r\n\r\n    .vue-office-pptx-main {\r\n      height: auto !important;\r\n\r\n      .pptx-preview-wrapper {\r\n        height: auto !important;\r\n        overflow: hidden;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;wCACEA,YAAA,CAEeC,uBAAA;IAFDC,KAAK,EAAC;EAAa;IADnCC,OAAA,EAAAC,QAAA,CAEI;MAAA,OAAoF,CAApFC,YAAA,CAAoFC,MAAA;QAAlEC,GAAG,EAAED,MAAA,CAAAE,OAAO;QAAGC,UAAQ,EAAEH,MAAA,CAAAI,eAAe;QAAGC,OAAK,EAAEL,MAAA,CAAAM;;;IAFxEC,CAAA;6BAC+CP,MAAA,CAAAQ,OAAO,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}