{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, with<PERSON><PERSON><PERSON> as _with<PERSON>ey<PERSON>, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"NoticeAnnouncement\"\n};\nvar _hoisted_2 = {\n  class: \"globalLayout\"\n};\nvar _hoisted_3 = {\n  class: \"globalTable\"\n};\nvar _hoisted_4 = {\n  class: \"theme-icon\"\n};\nvar _hoisted_5 = {\n  class: \"globalPagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_View = _resolveComponent(\"View\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_xyl_global_tree = _resolveComponent(\"xyl-global-tree\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_xyl_search_button = _resolveComponent(\"xyl-search-button\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_Hide = _resolveComponent(\"Hide\");\n  var _component_el_link = _resolveComponent(\"el-link\");\n  var _component_xyl_global_table = _resolveComponent(\"xyl-global-table\");\n  var _component_xyl_global_table_button = _resolveComponent(\"xyl-global-table-button\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  var _component_xyl_popup_window = _resolveComponent(\"xyl-popup-window\");\n  var _component_auroral_push = _resolveComponent(\"auroral-push\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_xyl_global_tree, {\n    name: \"类型\",\n    has: \"type_manage\",\n    isOne: \"\",\n    isControl: \"\",\n    draggable: \"\",\n    modelValue: $setup.typeId,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.typeId = $event;\n    }),\n    data: $setup.typeTree,\n    noManage: ['0'],\n    noDraggable: ['0'],\n    onTreeNew: $setup.treeNew,\n    onTreeEdit: $setup.treeEdit,\n    onTreeDel: $setup.treeDel,\n    onSelect: $setup.handleTree,\n    onDraggable: $setup.handleDraggable,\n    props: {\n      label: 'channelName',\n      children: 'children'\n    }\n  }, {\n    icon: _withCtx(function (scope) {\n      return [scope.treeObj.isViewByRole ? (_openBlock(), _createBlock(_component_el_icon, {\n        key: 0\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_View)];\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"data\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_xyl_search_button, {\n    onQueryClick: $setup.handleQuery,\n    onResetClick: $setup.handleReset,\n    onHandleButton: $setup.handleButton,\n    buttonList: $setup.buttonList,\n    data: $setup.tableHead,\n    ref: \"queryRef\"\n  }, {\n    search: _withCtx(function () {\n      return [_createVNode(_component_el_input, {\n        modelValue: $setup.keyword,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n          return $setup.keyword = $event;\n        }),\n        placeholder: \"请输入关键词\",\n        onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"onKeyup\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"onQueryClick\", \"data\"]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_table, {\n    ref: \"tableRef\",\n    \"row-key\": \"id\",\n    data: $setup.tableData,\n    onSelect: $setup.handleTableSelect,\n    onSelectAll: $setup.handleTableSelect,\n    onSortChange: $setup.handleSortChange,\n    \"header-cell-class-name\": $setup.handleHeaderClass\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        \"reserve-selection\": \"\",\n        width: \"60\",\n        fixed: \"\"\n      }), _createVNode(_component_xyl_global_table, {\n        tableHead: $setup.tableHead,\n        onTableClick: $setup.handleTableClick\n      }, {\n        theme: _withCtx(function (scope) {\n          return [_createVNode(_component_el_link, {\n            type: \"primary\",\n            onClick: function onClick($event) {\n              return $setup.handleTableClick('theme', scope.row);\n            }\n          }, {\n            default: _withCtx(function () {\n              return [_createElementVNode(\"div\", _hoisted_4, [['some_user', 'exclude_user'].includes(scope.row.rangeCode) ? (_openBlock(), _createBlock(_component_el_icon, {\n                key: 0\n              }, {\n                default: _withCtx(function () {\n                  return [_createVNode(_component_View)];\n                }),\n                _: 1 /* STABLE */\n              })) : _createCommentVNode(\"v-if\", true), scope.row.rangeCode === 'non_user' ? (_openBlock(), _createBlock(_component_el_icon, {\n                key: 1\n              }, {\n                default: _withCtx(function () {\n                  return [_createVNode(_component_Hide)];\n                }),\n                _: 1 /* STABLE */\n              })) : _createCommentVNode(\"v-if\", true)]), _createTextVNode(_toDisplayString(scope.row.theme), 1 /* TEXT */)];\n            }),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"tableHead\"]), _createVNode(_component_el_table_column, {\n        label: \"阅读详情\",\n        width: \"120\",\n        prop: \"dictCode\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createVNode(_component_el_link, {\n            onClick: function onClick($event) {\n              return $setup.handleReading(scope.row);\n            },\n            type: \"primary\"\n          }, {\n            default: _withCtx(function () {\n              return _cache[8] || (_cache[8] = [_createTextVNode(\"阅读详情\")]);\n            }),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"回执详情\",\n        width: \"120\",\n        prop: \"dictCode\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createVNode(_component_el_link, {\n            onClick: function onClick($event) {\n              return $setup.handleReceipt(scope.row);\n            },\n            disabled: !scope.row.isReceipt,\n            type: scope.row.isReceipt ? 'primary' : 'default'\n          }, {\n            default: _withCtx(function () {\n              return _cache[9] || (_cache[9] = [_createTextVNode(\"回执详情\")]);\n            }),\n            _: 2 /* DYNAMIC */\n          }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"onClick\", \"disabled\", \"type\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_xyl_global_table_button, {\n        data: $setup.tableButtonList,\n        onButtonClick: $setup.handleCommand,\n        editCustomTableHead: $setup.handleEditorCustom\n      }, null, 8 /* PROPS */, [\"editCustomTableHead\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelect\", \"onSelectAll\", \"onSortChange\", \"header-cell-class-name\"])]), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.pageNo,\n    \"onUpdate:currentPage\": _cache[2] || (_cache[2] = function ($event) {\n      return $setup.pageNo = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[3] || (_cache[3] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": $setup.pageSizes,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleQuery,\n    onCurrentChange: $setup.handleQuery,\n    total: $setup.totals,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"page-sizes\", \"onSizeChange\", \"onCurrentChange\", \"total\"])])]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.show,\n    \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n      return $setup.show = $event;\n    }),\n    name: $setup.id ? '编辑类型' : '新增类型'\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"NoticeAnnouncementType\"], {\n        id: $setup.id,\n        onCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\", \"name\"]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.isShow,\n    \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n      return $setup.isShow = $event;\n    }),\n    name: \"阅读详情\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"ReadingUser\"], {\n        id: $setup.id\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.ifShow,\n    \"onUpdate:modelValue\": _cache[6] || (_cache[6] = function ($event) {\n      return $setup.ifShow = $event;\n    }),\n    name: \"回执详情\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode($setup[\"ReturnReceiptUser\"], {\n        id: $setup.id\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_xyl_popup_window, {\n    modelValue: $setup.pushShow,\n    \"onUpdate:modelValue\": _cache[7] || (_cache[7] = function ($event) {\n      return $setup.pushShow = $event;\n    }),\n    name: \"推送\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_auroral_push, {\n        id: $setup.pushId,\n        code: \"22\",\n        content: \"您收到一条新的通知公告，请注意查收\",\n        onCallback: $setup.callback\n      }, null, 8 /* PROPS */, [\"id\"])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"modelValue\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_xyl_global_tree", "name", "has", "isOne", "isControl", "draggable", "modelValue", "$setup", "typeId", "_cache", "$event", "data", "typeTree", "noManage", "noDraggable", "onTreeNew", "treeNew", "onTreeEdit", "treeEdit", "onTreeDel", "treeDel", "onSelect", "handleTree", "onDraggable", "handleDraggable", "props", "label", "children", "icon", "_withCtx", "scope", "treeObj", "isViewByRole", "_createBlock", "_component_el_icon", "key", "default", "_component_View", "_", "_createCommentVNode", "_createElementVNode", "_hoisted_2", "_component_xyl_search_button", "onQueryClick", "handleQuery", "onResetClick", "handleReset", "onHandleButton", "handleButton", "buttonList", "tableHead", "ref", "search", "_component_el_input", "keyword", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "clearable", "_hoisted_3", "_component_el_table", "tableData", "handleTableSelect", "onSelectAll", "onSortChange", "handleSortChange", "handleHeaderClass", "_component_el_table_column", "type", "width", "fixed", "_component_xyl_global_table", "onTableClick", "handleTableClick", "theme", "_component_el_link", "onClick", "row", "_hoisted_4", "includes", "rangeCode", "_component_Hide", "_createTextVNode", "_toDisplayString", "prop", "handleReading", "handleReceipt", "disabled", "isReceipt", "_component_xyl_global_table_button", "tableButtonList", "onButtonClick", "handleCommand", "editCustomTableHead", "handleEditorCustom", "_hoisted_5", "_component_el_pagination", "currentPage", "pageNo", "pageSize", "pageSizes", "layout", "onSizeChange", "onCurrentChange", "total", "totals", "background", "_component_xyl_popup_window", "show", "id", "onCallback", "callback", "isShow", "ifShow", "pushShow", "_component_auroral_push", "pushId", "code", "content"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\NoticeAnnouncement\\NoticeAnnouncement.vue"], "sourcesContent": ["<template>\r\n  <div class=\"NoticeAnnouncement\">\r\n    <xyl-global-tree name=\"类型\" has=\"type_manage\" isOne isControl draggable v-model=\"typeId\" :data=\"typeTree\"\r\n      :noManage=\"['0']\" :noDraggable=\"['0']\" @treeNew=\"treeNew\" @treeEdit=\"treeEdit\" @treeDel=\"treeDel\"\r\n      @select=\"handleTree\" @draggable=\"handleDraggable\" :props=\"{ label: 'channelName', children: 'children' }\">\r\n      <template #icon=\"scope\">\r\n        <el-icon v-if=\"scope.treeObj.isViewByRole\">\r\n          <View />\r\n        </el-icon>\r\n      </template>\r\n    </xyl-global-tree>\r\n    <div class=\"globalLayout\">\r\n      <xyl-search-button @queryClick=\"handleQuery\" @resetClick=\"handleReset\" @handleButton=\"handleButton\"\r\n        :buttonList=\"buttonList\" :data=\"tableHead\" ref=\"queryRef\">\r\n        <template #search>\r\n          <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n        </template>\r\n      </xyl-search-button>\r\n      <div class=\"globalTable\">\r\n        <el-table ref=\"tableRef\" row-key=\"id\" :data=\"tableData\" @select=\"handleTableSelect\"\r\n          @select-all=\"handleTableSelect\" @sort-change=\"handleSortChange\" :header-cell-class-name=\"handleHeaderClass\">\r\n          <el-table-column type=\"selection\" reserve-selection width=\"60\" fixed />\r\n          <xyl-global-table :tableHead=\"tableHead\" @tableClick=\"handleTableClick\">\r\n            <template #theme=\"scope\">\r\n              <el-link type=\"primary\" @click=\"handleTableClick('theme', scope.row)\">\r\n                <div class=\"theme-icon\">\r\n                  <el-icon v-if=\"['some_user', 'exclude_user'].includes(scope.row.rangeCode)\">\r\n                    <View />\r\n                  </el-icon>\r\n                  <el-icon v-if=\"scope.row.rangeCode === 'non_user'\">\r\n                    <Hide />\r\n                  </el-icon>\r\n                </div>{{ scope.row.theme }}\r\n              </el-link>\r\n            </template>\r\n          </xyl-global-table>\r\n          <el-table-column label=\"阅读详情\" width=\"120\" prop=\"dictCode\">\r\n            <template #default=\"scope\">\r\n              <el-link @click=\"handleReading(scope.row)\" type=\"primary\">阅读详情</el-link>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column label=\"回执详情\" width=\"120\" prop=\"dictCode\">\r\n            <template #default=\"scope\">\r\n              <el-link @click=\"handleReceipt(scope.row)\" :disabled=\"!scope.row.isReceipt\"\r\n                :type=\"scope.row.isReceipt ? 'primary' : 'default'\">回执详情</el-link>\r\n            </template>\r\n          </el-table-column>\r\n          <xyl-global-table-button :data=\"tableButtonList\" @buttonClick=\"handleCommand\"\r\n            :editCustomTableHead=\"handleEditorCustom\"></xyl-global-table-button>\r\n        </el-table>\r\n      </div>\r\n      <div class=\"globalPagination\">\r\n        <el-pagination v-model:currentPage=\"pageNo\" v-model:page-size=\"pageSize\" :page-sizes=\"pageSizes\"\r\n          layout=\"total, sizes, prev, pager, next, jumper\" @size-change=\"handleQuery\" @current-change=\"handleQuery\"\r\n          :total=\"totals\" background />\r\n      </div>\r\n    </div>\r\n    <xyl-popup-window v-model=\"show\" :name=\"id ? '编辑类型' : '新增类型'\">\r\n      <NoticeAnnouncementType :id=\"id\" @callback=\"callback\"></NoticeAnnouncementType>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"isShow\" name=\"阅读详情\">\r\n      <ReadingUser :id=\"id\"></ReadingUser>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"ifShow\" name=\"回执详情\">\r\n      <ReturnReceiptUser :id=\"id\"></ReturnReceiptUser>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"pushShow\" name=\"推送\">\r\n      <auroral-push :id=\"pushId\" code=\"22\" content=\"您收到一条新的通知公告，请注意查收\" @callback=\"callback\"></auroral-push>\r\n    </xyl-popup-window>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'NoticeAnnouncement' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onActivated } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\nimport NoticeAnnouncementType from './component/NoticeAnnouncementType'\r\nimport ReadingUser from './component/ReadingUser'\r\nimport ReturnReceiptUser from './component/ReturnReceiptUser'\r\nconst route = useRoute()\r\nconst buttonList = [\r\n  { id: 'new', name: '新增', type: 'primary', has: 'new' },\r\n  { id: 'del', name: '删除', type: '', has: 'del' }\r\n]\r\nconst tableButtonList = [\r\n  { id: 'edit', name: '编辑', width: 80, has: 'edit' },\r\n  { id: 'push', name: '推送', width: 80, has: 'push' },\r\n  { id: 'withdraw', name: '撤回', width: 80, has: '' }\r\n]\r\nconst id = ref('')\r\nconst show = ref(false)\r\nconst isShow = ref(false)\r\nconst ifShow = ref(false)\r\nconst pushId = ref('')\r\nconst pushShow = ref(false)\r\nconst typeId = ref('0')\r\nconst typeTree = ref([])\r\nconst {\r\n  keyword,\r\n  queryRef,\r\n  tableRef,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableHead,\r\n  tableData,\r\n  handleQuery,\r\n  handleSortChange,\r\n  handleHeaderClass,\r\n  handleTableSelect,\r\n  handleDel,\r\n  tableRefReset,\r\n  handleEditorCustom,\r\n  tableQuery\r\n} = GlobalTable({ tableId: 'id_message_notification', tableApi: 'NoticeAnnouncementList', delApi: 'NoticeAnnouncementDel', tableDataMap: (data) => data.map(v => ({ ...v, isTopSwitch: v.isTop ? true : false })) })\r\n\r\nonActivated(() => {\r\n  NoticeAnnouncementTypeList()\r\n  const openData = JSON.parse(sessionStorage.getItem('BoxMessage')) || ''\r\n  if (openData) {\r\n    if (openData?.isAdd) {\r\n      handleNew()\r\n    } else {\r\n      details(openData)\r\n    }\r\n    sessionStorage.setItem('BoxMessage', JSON.stringify(''))\r\n  }\r\n})\r\nconst handleButton = (id) => {\r\n  switch (id) {\r\n    case 'new':\r\n      handleNew()\r\n      break\r\n    case 'del':\r\n      handleDel('通知公告')\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleTableClick = (key, row) => {\r\n  switch (key) {\r\n    case 'details':\r\n      details(row)\r\n      break\r\n    case 'theme':\r\n      details(row)\r\n      break\r\n    case 'isTopSwitch':\r\n      switchChange(row)\r\n      break\r\n    case 'isSort':\r\n      globalJson(row)\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleCommand = (row, isType) => {\r\n  switch (isType) {\r\n    case 'edit':\r\n      handleEdit(row)\r\n      break\r\n    case 'push':\r\n      pushId.value = row.id\r\n      pushShow.value = true\r\n      break\r\n    case 'withdraw':\r\n      console.log('withdraw')\r\n      ElMessageBox.confirm('此操作将撤回当前数据, 是否继续?', '提示', {\r\n        confirmButtonText: '确定',\r\n        cancelButtonText: '取消',\r\n        type: 'warning'\r\n      }).then(() => { handleWithdraw(row) }).catch(() => { ElMessage({ type: 'info', message: '已取消删除' }) })\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\n// 撤回\r\nconst handleWithdraw = async (row) => {\r\n  const { code } = await api.globalJson('/notification/edit', {\r\n    form: {\r\n      id: row.id,\r\n      isDraft: 1\r\n    }\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '撤回成功' })\r\n    handleQuery()\r\n  }\r\n}\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\nconst NoticeAnnouncementTypeList = async () => {\r\n  const res = await api.NoticeAnnouncementTypeList({ pageNo: 1, pageSize: 999 })\r\n  var { data } = res\r\n  typeTree.value = [{ id: '0', channelName: '所有' }, ...data]\r\n}\r\nconst treeNew = () => {\r\n  id.value = ''\r\n  show.value = true\r\n}\r\nconst treeEdit = (node, data) => {\r\n  id.value = data.id\r\n  show.value = true\r\n}\r\nconst treeDel = (node, data) => {\r\n  ElMessageBox.confirm('此操作将删除当前选中的类型, 是否继续?', '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(() => { NoticeAnnouncementTypeDel([data.id]) }).catch(() => { ElMessage({ type: 'info', message: '已取消删除' }) })\r\n}\r\nconst NoticeAnnouncementTypeDel = async (ids) => {\r\n  const { code } = await api.NoticeAnnouncementTypeDel({ ids })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '删除成功' })\r\n    NoticeAnnouncementTypeList()\r\n  }\r\n}\r\nconst handleTree = () => {\r\n  tableQuery.value = { isSelectForManager: 1, query: { isDraft: 0, channelId: typeId.value === '0' ? null : typeId.value } }\r\n  handleQuery()\r\n}\r\nconst handleDraggable = (data) => {\r\n  NoticeAnnouncementTypeSort(data.filter(v => v.id !== '0').map((v, index) => ({ id: v.id, sort: index + 1 })))\r\n}\r\nconst NoticeAnnouncementTypeSort = async (data) => {\r\n  try {\r\n    const { code } = await api.NoticeAnnouncementTypeSort({ sorts: data })\r\n    if (code === 200) {\r\n      ElMessage({ type: 'success', message: '排序成功' })\r\n      NoticeAnnouncementTypeList()\r\n    }\r\n  } catch (err) {\r\n    NoticeAnnouncementTypeList()\r\n  }\r\n}\r\nconst handleNew = () => {\r\n  qiankunMicro.setGlobalState({ openRoute: { name: '新增通知公告', path: '/interaction/PublishNoticeAnnouncement', query: { type: 'is', typeId: typeId.value, tabCode: route.query.tabCode } } })\r\n}\r\nconst details = (item) => {\r\n  qiankunMicro.setGlobalState({ openRoute: { name: '通知公告详情', path: '/interaction/NoticeAnnouncementDetails', query: { id: item.id, disNotice: item?.disNotice || '' } } })\r\n}\r\nconst handleEdit = (item) => {\r\n  qiankunMicro.setGlobalState({ openRoute: { name: '编辑通知公告', path: '/interaction/PublishNoticeAnnouncement', query: { id: item.id, tabCode: route.query.tabCode } } })\r\n}\r\nconst handleReading = (row) => {\r\n  id.value = row.id\r\n  isShow.value = true\r\n}\r\nconst handleReceipt = (row) => {\r\n  id.value = row.id\r\n  ifShow.value = true\r\n}\r\nconst callback = () => {\r\n  tableRefReset()\r\n  NoticeAnnouncementTypeList()\r\n  show.value = false\r\n  pushShow.value = false\r\n}\r\nconst globalJson = async (form) => {\r\n  const { code } = await api.globalJson('/notification/edit', { form })\r\n  if (code === 200) {\r\n    handleQuery()\r\n    ElMessage({ type: 'success', message: '排序成功' })\r\n  }\r\n}\r\nconst switchChange = (row) => {\r\n  ElMessageBox.confirm(`确定将当前选中的通知公告${row.isTop ? '取消' : ''}置顶?`, '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(() => {\r\n    NoticeAnnouncementTop(row)\r\n  }).catch(() => {\r\n    handleQuery()\r\n    ElMessage({ type: 'info', message: '已取消操作' })\r\n  })\r\n}\r\nconst NoticeAnnouncementTop = async (row) => {\r\n  try {\r\n    const { code } = await api.NoticeAnnouncementTop({ notificationId: row.id, cancelTop: row.isTop })\r\n    if (code === 200) {\r\n      ElMessage({ type: 'success', message: `${row.isTop ? '取消' : ''}置顶成功` })\r\n      handleQuery()\r\n    }\r\n  } catch (err) {\r\n    handleQuery()\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.NoticeAnnouncement {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n\r\n  .globalLayout {\r\n    height: 100%;\r\n\r\n    .globalTable {\r\n      width: 100%;\r\n      height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n\r\n      .table-slot-theme {\r\n        .cell {\r\n          padding-left: 26px;\r\n        }\r\n\r\n        .zy-el-link {\r\n          position: relative;\r\n\r\n          .theme-icon {\r\n            position: absolute;\r\n            top: 0;\r\n            left: -26px;\r\n            font-size: var(--zy-name-font-size);\r\n            color: var(--zy-el-table-text-color);\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAoB;;EAUxBA,KAAK,EAAC;AAAc;;EAOlBA,KAAK,EAAC;AAAa;;EAOTA,KAAK,EAAC;AAAY;;EA0B5BA,KAAK,EAAC;AAAkB;;;;;;;;;;;;;;;;uBAlDjCC,mBAAA,CAoEM,OApENC,UAoEM,GAnEJC,YAAA,CAQkBC,0BAAA;IARDC,IAAI,EAAC,IAAI;IAACC,GAAG,EAAC,aAAa;IAACC,KAAK,EAAL,EAAK;IAACC,SAAS,EAAT,EAAS;IAACC,SAAS,EAAT,EAAS;IAF1EC,UAAA,EAEoFC,MAAA,CAAAC,MAAM;IAF1F,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAEoFH,MAAA,CAAAC,MAAM,GAAAE,MAAA;IAAA;IAAGC,IAAI,EAAEJ,MAAA,CAAAK,QAAQ;IACpGC,QAAQ,EAAE,KAAK;IAAGC,WAAW,EAAE,KAAK;IAAGC,SAAO,EAAER,MAAA,CAAAS,OAAO;IAAGC,UAAQ,EAAEV,MAAA,CAAAW,QAAQ;IAAGC,SAAO,EAAEZ,MAAA,CAAAa,OAAO;IAC/FC,QAAM,EAAEd,MAAA,CAAAe,UAAU;IAAGC,WAAS,EAAEhB,MAAA,CAAAiB,eAAe;IAAGC,KAAK,EAAE;MAAAC,KAAA;MAAAC,QAAA;IAAA;;IAC/CC,IAAI,EAAAC,QAAA,CACyE,UACvBC,KAF3C;MAAA,QACLA,KAAK,CAACC,OAAO,CAACC,YAAY,I,cAAzCC,YAAA,CAEUC,kBAAA;QARlBC,GAAA;MAAA;QAAAC,OAAA,EAAAP,QAAA,CAOU;UAAA,OAAQ,CAAR9B,YAAA,CAAQsC,eAAA,E;;QAPlBC,CAAA;YAAAC,mBAAA,e;;IAAAD,CAAA;6CAWIE,mBAAA,CA6CM,OA7CNC,UA6CM,GA5CJ1C,YAAA,CAKoB2C,4BAAA;IALAC,YAAU,EAAEpC,MAAA,CAAAqC,WAAW;IAAGC,YAAU,EAAEtC,MAAA,CAAAuC,WAAW;IAAGC,cAAY,EAAExC,MAAA,CAAAyC,YAAY;IAC/FC,UAAU,EAAE1C,MAAA,CAAA0C,UAAU;IAAGtC,IAAI,EAAEJ,MAAA,CAAA2C,SAAS;IAAEC,GAAG,EAAC;;IACpCC,MAAM,EAAAvB,QAAA,CACf;MAAA,OAAwF,CAAxF9B,YAAA,CAAwFsD,mBAAA;QAflG/C,UAAA,EAe6BC,MAAA,CAAA+C,OAAO;QAfpC,uBAAA7C,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAe6BH,MAAA,CAAA+C,OAAO,GAAA5C,MAAA;QAAA;QAAE6C,WAAW,EAAC,QAAQ;QAAEC,OAAK,EAfjEC,SAAA,CAeyElD,MAAA,CAAAqC,WAAW;QAAEc,SAAS,EAAT;;;IAftFpB,CAAA;+CAkBME,mBAAA,CAgCM,OAhCNmB,UAgCM,GA/BJ5D,YAAA,CA8BW6D,mBAAA;IA9BDT,GAAG,EAAC,UAAU;IAAC,SAAO,EAAC,IAAI;IAAExC,IAAI,EAAEJ,MAAA,CAAAsD,SAAS;IAAGxC,QAAM,EAAEd,MAAA,CAAAuD,iBAAiB;IAC/EC,WAAU,EAAExD,MAAA,CAAAuD,iBAAiB;IAAGE,YAAW,EAAEzD,MAAA,CAAA0D,gBAAgB;IAAG,wBAAsB,EAAE1D,MAAA,CAAA2D;;IApBnG9B,OAAA,EAAAP,QAAA,CAqBU;MAAA,OAAuE,CAAvE9B,YAAA,CAAuEoE,0BAAA;QAAtDC,IAAI,EAAC,WAAW;QAAC,mBAAiB,EAAjB,EAAiB;QAACC,KAAK,EAAC,IAAI;QAACC,KAAK,EAAL;UAC/DvE,YAAA,CAamBwE,2BAAA;QAbArB,SAAS,EAAE3C,MAAA,CAAA2C,SAAS;QAAGsB,YAAU,EAAEjE,MAAA,CAAAkE;;QACzCC,KAAK,EAAA7C,QAAA,CACd,UASUC,KAVW;UAAA,QACrB/B,YAAA,CASU4E,kBAAA;YATDP,IAAI,EAAC,SAAS;YAAEQ,OAAK,WAALA,OAAKA,CAAAlE,MAAA;cAAA,OAAEH,MAAA,CAAAkE,gBAAgB,UAAU3C,KAAK,CAAC+C,GAAG;YAAA;;YAxBjFzC,OAAA,EAAAP,QAAA,CAyBgB;cAAA,OAOM,CAPNW,mBAAA,CAOM,OAPNsC,UAOM,G,8BANyCC,QAAQ,CAACjD,KAAK,CAAC+C,GAAG,CAACG,SAAS,K,cAAzE/C,YAAA,CAEUC,kBAAA;gBA5B5BC,GAAA;cAAA;gBAAAC,OAAA,EAAAP,QAAA,CA2BoB;kBAAA,OAAQ,CAAR9B,YAAA,CAAQsC,eAAA,E;;gBA3B5BC,CAAA;oBAAAC,mBAAA,gBA6BiCT,KAAK,CAAC+C,GAAG,CAACG,SAAS,mB,cAAlC/C,YAAA,CAEUC,kBAAA;gBA/B5BC,GAAA;cAAA;gBAAAC,OAAA,EAAAP,QAAA,CA8BoB;kBAAA,OAAQ,CAAR9B,YAAA,CAAQkF,eAAA,E;;gBA9B5B3C,CAAA;oBAAAC,mBAAA,e,GAAA2C,gBAAA,CAAAC,gBAAA,CAgCyBrD,KAAK,CAAC+C,GAAG,CAACH,KAAK,iB;;YAhCxCpC,CAAA;;;QAAAA,CAAA;wCAoCUvC,YAAA,CAIkBoE,0BAAA;QAJDzC,KAAK,EAAC,MAAM;QAAC2C,KAAK,EAAC,KAAK;QAACe,IAAI,EAAC;;QAClChD,OAAO,EAAAP,QAAA,CAChB,UAAwEC,KADjD;UAAA,QACvB/B,YAAA,CAAwE4E,kBAAA;YAA9DC,OAAK,WAALA,OAAKA,CAAAlE,MAAA;cAAA,OAAEH,MAAA,CAAA8E,aAAa,CAACvD,KAAK,CAAC+C,GAAG;YAAA;YAAGT,IAAI,EAAC;;YAtC9DhC,OAAA,EAAAP,QAAA,CAsCwE;cAAA,OAAIpB,MAAA,QAAAA,MAAA,OAtC5EyE,gBAAA,CAsCwE,MAAI,E;;YAtC5E5C,CAAA;;;QAAAA,CAAA;UAyCUvC,YAAA,CAKkBoE,0BAAA;QALDzC,KAAK,EAAC,MAAM;QAAC2C,KAAK,EAAC,KAAK;QAACe,IAAI,EAAC;;QAClChD,OAAO,EAAAP,QAAA,CAChB,UACoEC,KAF7C;UAAA,QACvB/B,YAAA,CACoE4E,kBAAA;YAD1DC,OAAK,WAALA,OAAKA,CAAAlE,MAAA;cAAA,OAAEH,MAAA,CAAA+E,aAAa,CAACxD,KAAK,CAAC+C,GAAG;YAAA;YAAIU,QAAQ,GAAGzD,KAAK,CAAC+C,GAAG,CAACW,SAAS;YACvEpB,IAAI,EAAEtC,KAAK,CAAC+C,GAAG,CAACW,SAAS;;YA5C1CpD,OAAA,EAAAP,QAAA,CA4CoE;cAAA,OAAIpB,MAAA,QAAAA,MAAA,OA5CxEyE,gBAAA,CA4CoE,MAAI,E;;YA5CxE5C,CAAA;;;QAAAA,CAAA;UA+CUvC,YAAA,CACsE0F,kCAAA;QAD5C9E,IAAI,EAAEJ,MAAA,CAAAmF,eAAe;QAAGC,aAAW,EAAEpF,MAAA,CAAAqF,aAAa;QACzEC,mBAAmB,EAAEtF,MAAA,CAAAuF;;;IAhDlCxD,CAAA;sGAmDME,mBAAA,CAIM,OAJNuD,UAIM,GAHJhG,YAAA,CAE+BiG,wBAAA;IAFRC,WAAW,EAAE1F,MAAA,CAAA2F,MAAM;IApDlD,wBAAAzF,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAoD4CH,MAAA,CAAA2F,MAAM,GAAAxF,MAAA;IAAA;IAAU,WAAS,EAAEH,MAAA,CAAA4F,QAAQ;IApD/E,qBAAA1F,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAoDuEH,MAAA,CAAA4F,QAAQ,GAAAzF,MAAA;IAAA;IAAG,YAAU,EAAEH,MAAA,CAAA6F,SAAS;IAC7FC,MAAM,EAAC,yCAAyC;IAAEC,YAAW,EAAE/F,MAAA,CAAAqC,WAAW;IAAG2D,eAAc,EAAEhG,MAAA,CAAAqC,WAAW;IACvG4D,KAAK,EAAEjG,MAAA,CAAAkG,MAAM;IAAEC,UAAU,EAAV;uHAGtB3G,YAAA,CAEmB4G,2BAAA;IA3DvBrG,UAAA,EAyD+BC,MAAA,CAAAqG,IAAI;IAzDnC,uBAAAnG,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAyD+BH,MAAA,CAAAqG,IAAI,GAAAlG,MAAA;IAAA;IAAGT,IAAI,EAAEM,MAAA,CAAAsG,EAAE;;IAzD9CzE,OAAA,EAAAP,QAAA,CA0DM;MAAA,OAA+E,CAA/E9B,YAAA,CAA+EQ,MAAA;QAAtDsG,EAAE,EAAEtG,MAAA,CAAAsG,EAAE;QAAGC,UAAQ,EAAEvG,MAAA,CAAAwG;;;IA1DlDzE,CAAA;6CA4DIvC,YAAA,CAEmB4G,2BAAA;IA9DvBrG,UAAA,EA4D+BC,MAAA,CAAAyG,MAAM;IA5DrC,uBAAAvG,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA4D+BH,MAAA,CAAAyG,MAAM,GAAAtG,MAAA;IAAA;IAAET,IAAI,EAAC;;IA5D5CmC,OAAA,EAAAP,QAAA,CA6DM;MAAA,OAAoC,CAApC9B,YAAA,CAAoCQ,MAAA;QAAtBsG,EAAE,EAAEtG,MAAA,CAAAsG;MAAE,gC;;IA7D1BvE,CAAA;qCA+DIvC,YAAA,CAEmB4G,2BAAA;IAjEvBrG,UAAA,EA+D+BC,MAAA,CAAA0G,MAAM;IA/DrC,uBAAAxG,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA+D+BH,MAAA,CAAA0G,MAAM,GAAAvG,MAAA;IAAA;IAAET,IAAI,EAAC;;IA/D5CmC,OAAA,EAAAP,QAAA,CAgEM;MAAA,OAAgD,CAAhD9B,YAAA,CAAgDQ,MAAA;QAA5BsG,EAAE,EAAEtG,MAAA,CAAAsG;MAAE,gC;;IAhEhCvE,CAAA;qCAkEIvC,YAAA,CAEmB4G,2BAAA;IApEvBrG,UAAA,EAkE+BC,MAAA,CAAA2G,QAAQ;IAlEvC,uBAAAzG,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAkE+BH,MAAA,CAAA2G,QAAQ,GAAAxG,MAAA;IAAA;IAAET,IAAI,EAAC;;IAlE9CmC,OAAA,EAAAP,QAAA,CAmEM;MAAA,OAAqG,CAArG9B,YAAA,CAAqGoH,uBAAA;QAAtFN,EAAE,EAAEtG,MAAA,CAAA6G,MAAM;QAAEC,IAAI,EAAC,IAAI;QAACC,OAAO,EAAC,mBAAmB;QAAER,UAAQ,EAAEvG,MAAA,CAAAwG;;;IAnElFzE,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}