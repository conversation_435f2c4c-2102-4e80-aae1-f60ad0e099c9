{"ast": null, "code": "'use strict';\n\nmodule.exports = function block(state) {\n  var token;\n  if (state.inlineMode) {\n    token = new state.Token('inline', '', 0);\n    token.content = state.src;\n    token.map = [0, 1];\n    token.children = [];\n    state.tokens.push(token);\n  } else {\n    state.md.block.parse(state.src, state.md, state.env, state.tokens);\n  }\n};", "map": {"version": 3, "names": ["module", "exports", "block", "state", "token", "inlineMode", "Token", "content", "src", "map", "children", "tokens", "push", "md", "parse", "env"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_core/block.js"], "sourcesContent": ["'use strict';\n\n\nmodule.exports = function block(state) {\n  var token;\n\n  if (state.inlineMode) {\n    token          = new state.Token('inline', '', 0);\n    token.content  = state.src;\n    token.map      = [ 0, 1 ];\n    token.children = [];\n    state.tokens.push(token);\n  } else {\n    state.md.block.parse(state.src, state.md, state.env, state.tokens);\n  }\n};\n"], "mappings": "AAAA,YAAY;;AAGZA,MAAM,CAACC,OAAO,GAAG,SAASC,KAAKA,CAACC,KAAK,EAAE;EACrC,IAAIC,KAAK;EAET,IAAID,KAAK,CAACE,UAAU,EAAE;IACpBD,KAAK,GAAY,IAAID,KAAK,CAACG,KAAK,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IACjDF,KAAK,CAACG,OAAO,GAAIJ,KAAK,CAACK,GAAG;IAC1BJ,KAAK,CAACK,GAAG,GAAQ,CAAE,CAAC,EAAE,CAAC,CAAE;IACzBL,KAAK,CAACM,QAAQ,GAAG,EAAE;IACnBP,KAAK,CAACQ,MAAM,CAACC,IAAI,CAACR,KAAK,CAAC;EAC1B,CAAC,MAAM;IACLD,KAAK,CAACU,EAAE,CAACX,KAAK,CAACY,KAAK,CAACX,KAAK,CAACK,GAAG,EAAEL,KAAK,CAACU,EAAE,EAAEV,KAAK,CAACY,GAAG,EAAEZ,KAAK,CAACQ,MAAM,CAAC;EACpE;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}