{"ast": null, "code": "\"use strict\";\n\nvar _require = require(\"./doc-utils.js\"),\n  startsWith = _require.startsWith,\n  endsWith = _require.endsWith,\n  isStarting = _require.isStarting,\n  isEnding = _require.isEnding,\n  isWhiteSpace = _require.isWhiteSpace;\nvar filetypes = require(\"./filetypes.js\");\nfunction addEmptyParagraphAfterTable(parts) {\n  var lastNonEmpty = \"\";\n  for (var i = 0, len = parts.length; i < len; i++) {\n    var p = parts[i];\n    if (isWhiteSpace(p)) {\n      continue;\n    }\n    if (endsWith(lastNonEmpty, \"</w:tbl>\")) {\n      if (!startsWith(p, \"<w:p\") && !startsWith(p, \"<w:tbl\") && !startsWith(p, \"<w:sectPr\")) {\n        p = \"<w:p/>\".concat(p);\n      }\n    }\n    lastNonEmpty = p;\n    parts[i] = p;\n  }\n  return parts;\n}\n\n// eslint-disable-next-line complexity\nfunction joinUncorrupt(parts, options) {\n  var contains = options.fileTypeConfig.tagShouldContain || [];\n  /*\n   * Before doing this \"uncorruption\" method here, this was done with the\n   * `part.emptyValue` trick, however, there were some corruptions that were\n   * not handled, for example with a template like this :\n   *\n   * ------------------------------------------------\n   * | {-w:p falsy}My para{/falsy}   |              |\n   * | {-w:p falsy}My para{/falsy}   |              |\n   * ------------------------------------------------\n   */\n  var collecting = \"\";\n  var currentlyCollecting = -1;\n  if (filetypes.docx.indexOf(options.contentType) !== -1) {\n    parts = addEmptyParagraphAfterTable(parts);\n  }\n  var startIndex = -1;\n  for (var j = 0, len2 = contains.length; j < len2; j++) {\n    var _contains$j = contains[j],\n      tag = _contains$j.tag,\n      shouldContain = _contains$j.shouldContain,\n      value = _contains$j.value,\n      drop = _contains$j.drop,\n      dropParent = _contains$j.dropParent;\n    for (var i = 0, len = parts.length; i < len; i++) {\n      var part = parts[i];\n      if (currentlyCollecting === j) {\n        if (isEnding(part, tag)) {\n          currentlyCollecting = -1;\n          if (dropParent) {\n            var start = -1;\n            for (var k = startIndex; k > 0; k--) {\n              if (isStarting(parts[k], dropParent)) {\n                start = k;\n                break;\n              }\n            }\n            for (var _k = start; _k <= parts.length; _k++) {\n              if (isEnding(parts[_k], dropParent)) {\n                parts[_k] = \"\";\n                break;\n              }\n              parts[_k] = \"\";\n            }\n          } else {\n            for (var _k2 = startIndex; _k2 <= i; _k2++) {\n              parts[_k2] = \"\";\n            }\n            if (!drop) {\n              parts[i] = collecting + value + part;\n            }\n          }\n        }\n        collecting += part;\n        for (var _k3 = 0, len3 = shouldContain.length; _k3 < len3; _k3++) {\n          var sc = shouldContain[_k3];\n          if (isStarting(part, sc)) {\n            currentlyCollecting = -1;\n            break;\n          }\n        }\n      }\n      if (currentlyCollecting === -1 && isStarting(part, tag) &&\n      // to verify that the part doesn't have multiple tags,\n      // such as <w:tc><w:p>\n      part.substr(1).indexOf(\"<\") === -1) {\n        // self-closing tag such as <w:t/>\n        if (part[part.length - 2] === \"/\") {\n          parts[i] = \"\";\n        } else {\n          startIndex = i;\n          currentlyCollecting = j;\n          collecting = part;\n        }\n      }\n    }\n  }\n  return parts;\n}\nmodule.exports = joinUncorrupt;", "map": {"version": 3, "names": ["_require", "require", "startsWith", "endsWith", "isStarting", "isEnding", "isWhiteSpace", "filetypes", "addEmptyParagraphAfterTable", "parts", "lastNonEmpty", "i", "len", "length", "p", "concat", "joinUncorrupt", "options", "contains", "fileTypeConfig", "tagShouldContain", "collecting", "currentlyCollecting", "docx", "indexOf", "contentType", "startIndex", "j", "len2", "_contains$j", "tag", "shouldContain", "value", "drop", "dropParent", "part", "start", "k", "_k", "_k2", "_k3", "len3", "sc", "substr", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/docxtemplater@3.52.0/node_modules/docxtemplater/js/join-uncorrupt.js"], "sourcesContent": ["\"use strict\";\n\nvar _require = require(\"./doc-utils.js\"),\n  startsWith = _require.startsWith,\n  endsWith = _require.endsWith,\n  isStarting = _require.isStarting,\n  isEnding = _require.isEnding,\n  isWhiteSpace = _require.isWhiteSpace;\nvar filetypes = require(\"./filetypes.js\");\nfunction addEmptyParagraphAfterTable(parts) {\n  var lastNonEmpty = \"\";\n  for (var i = 0, len = parts.length; i < len; i++) {\n    var p = parts[i];\n    if (isWhiteSpace(p)) {\n      continue;\n    }\n    if (endsWith(lastNonEmpty, \"</w:tbl>\")) {\n      if (!startsWith(p, \"<w:p\") && !startsWith(p, \"<w:tbl\") && !startsWith(p, \"<w:sectPr\")) {\n        p = \"<w:p/>\".concat(p);\n      }\n    }\n    lastNonEmpty = p;\n    parts[i] = p;\n  }\n  return parts;\n}\n\n// eslint-disable-next-line complexity\nfunction joinUncorrupt(parts, options) {\n  var contains = options.fileTypeConfig.tagShouldContain || [];\n  /*\n   * Before doing this \"uncorruption\" method here, this was done with the\n   * `part.emptyValue` trick, however, there were some corruptions that were\n   * not handled, for example with a template like this :\n   *\n   * ------------------------------------------------\n   * | {-w:p falsy}My para{/falsy}   |              |\n   * | {-w:p falsy}My para{/falsy}   |              |\n   * ------------------------------------------------\n   */\n  var collecting = \"\";\n  var currentlyCollecting = -1;\n  if (filetypes.docx.indexOf(options.contentType) !== -1) {\n    parts = addEmptyParagraphAfterTable(parts);\n  }\n  var startIndex = -1;\n  for (var j = 0, len2 = contains.length; j < len2; j++) {\n    var _contains$j = contains[j],\n      tag = _contains$j.tag,\n      shouldContain = _contains$j.shouldContain,\n      value = _contains$j.value,\n      drop = _contains$j.drop,\n      dropParent = _contains$j.dropParent;\n    for (var i = 0, len = parts.length; i < len; i++) {\n      var part = parts[i];\n      if (currentlyCollecting === j) {\n        if (isEnding(part, tag)) {\n          currentlyCollecting = -1;\n          if (dropParent) {\n            var start = -1;\n            for (var k = startIndex; k > 0; k--) {\n              if (isStarting(parts[k], dropParent)) {\n                start = k;\n                break;\n              }\n            }\n            for (var _k = start; _k <= parts.length; _k++) {\n              if (isEnding(parts[_k], dropParent)) {\n                parts[_k] = \"\";\n                break;\n              }\n              parts[_k] = \"\";\n            }\n          } else {\n            for (var _k2 = startIndex; _k2 <= i; _k2++) {\n              parts[_k2] = \"\";\n            }\n            if (!drop) {\n              parts[i] = collecting + value + part;\n            }\n          }\n        }\n        collecting += part;\n        for (var _k3 = 0, len3 = shouldContain.length; _k3 < len3; _k3++) {\n          var sc = shouldContain[_k3];\n          if (isStarting(part, sc)) {\n            currentlyCollecting = -1;\n            break;\n          }\n        }\n      }\n      if (currentlyCollecting === -1 && isStarting(part, tag) &&\n      // to verify that the part doesn't have multiple tags,\n      // such as <w:tc><w:p>\n      part.substr(1).indexOf(\"<\") === -1) {\n        // self-closing tag such as <w:t/>\n        if (part[part.length - 2] === \"/\") {\n          parts[i] = \"\";\n        } else {\n          startIndex = i;\n          currentlyCollecting = j;\n          collecting = part;\n        }\n      }\n    }\n  }\n  return parts;\n}\nmodule.exports = joinUncorrupt;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,gBAAgB,CAAC;EACtCC,UAAU,GAAGF,QAAQ,CAACE,UAAU;EAChCC,QAAQ,GAAGH,QAAQ,CAACG,QAAQ;EAC5BC,UAAU,GAAGJ,QAAQ,CAACI,UAAU;EAChCC,QAAQ,GAAGL,QAAQ,CAACK,QAAQ;EAC5BC,YAAY,GAAGN,QAAQ,CAACM,YAAY;AACtC,IAAIC,SAAS,GAAGN,OAAO,CAAC,gBAAgB,CAAC;AACzC,SAASO,2BAA2BA,CAACC,KAAK,EAAE;EAC1C,IAAIC,YAAY,GAAG,EAAE;EACrB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGH,KAAK,CAACI,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;IAChD,IAAIG,CAAC,GAAGL,KAAK,CAACE,CAAC,CAAC;IAChB,IAAIL,YAAY,CAACQ,CAAC,CAAC,EAAE;MACnB;IACF;IACA,IAAIX,QAAQ,CAACO,YAAY,EAAE,UAAU,CAAC,EAAE;MACtC,IAAI,CAACR,UAAU,CAACY,CAAC,EAAE,MAAM,CAAC,IAAI,CAACZ,UAAU,CAACY,CAAC,EAAE,QAAQ,CAAC,IAAI,CAACZ,UAAU,CAACY,CAAC,EAAE,WAAW,CAAC,EAAE;QACrFA,CAAC,GAAG,QAAQ,CAACC,MAAM,CAACD,CAAC,CAAC;MACxB;IACF;IACAJ,YAAY,GAAGI,CAAC;IAChBL,KAAK,CAACE,CAAC,CAAC,GAAGG,CAAC;EACd;EACA,OAAOL,KAAK;AACd;;AAEA;AACA,SAASO,aAAaA,CAACP,KAAK,EAAEQ,OAAO,EAAE;EACrC,IAAIC,QAAQ,GAAGD,OAAO,CAACE,cAAc,CAACC,gBAAgB,IAAI,EAAE;EAC5D;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIC,UAAU,GAAG,EAAE;EACnB,IAAIC,mBAAmB,GAAG,CAAC,CAAC;EAC5B,IAAIf,SAAS,CAACgB,IAAI,CAACC,OAAO,CAACP,OAAO,CAACQ,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;IACtDhB,KAAK,GAAGD,2BAA2B,CAACC,KAAK,CAAC;EAC5C;EACA,IAAIiB,UAAU,GAAG,CAAC,CAAC;EACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,IAAI,GAAGV,QAAQ,CAACL,MAAM,EAAEc,CAAC,GAAGC,IAAI,EAAED,CAAC,EAAE,EAAE;IACrD,IAAIE,WAAW,GAAGX,QAAQ,CAACS,CAAC,CAAC;MAC3BG,GAAG,GAAGD,WAAW,CAACC,GAAG;MACrBC,aAAa,GAAGF,WAAW,CAACE,aAAa;MACzCC,KAAK,GAAGH,WAAW,CAACG,KAAK;MACzBC,IAAI,GAAGJ,WAAW,CAACI,IAAI;MACvBC,UAAU,GAAGL,WAAW,CAACK,UAAU;IACrC,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEC,GAAG,GAAGH,KAAK,CAACI,MAAM,EAAEF,CAAC,GAAGC,GAAG,EAAED,CAAC,EAAE,EAAE;MAChD,IAAIwB,IAAI,GAAG1B,KAAK,CAACE,CAAC,CAAC;MACnB,IAAIW,mBAAmB,KAAKK,CAAC,EAAE;QAC7B,IAAItB,QAAQ,CAAC8B,IAAI,EAAEL,GAAG,CAAC,EAAE;UACvBR,mBAAmB,GAAG,CAAC,CAAC;UACxB,IAAIY,UAAU,EAAE;YACd,IAAIE,KAAK,GAAG,CAAC,CAAC;YACd,KAAK,IAAIC,CAAC,GAAGX,UAAU,EAAEW,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;cACnC,IAAIjC,UAAU,CAACK,KAAK,CAAC4B,CAAC,CAAC,EAAEH,UAAU,CAAC,EAAE;gBACpCE,KAAK,GAAGC,CAAC;gBACT;cACF;YACF;YACA,KAAK,IAAIC,EAAE,GAAGF,KAAK,EAAEE,EAAE,IAAI7B,KAAK,CAACI,MAAM,EAAEyB,EAAE,EAAE,EAAE;cAC7C,IAAIjC,QAAQ,CAACI,KAAK,CAAC6B,EAAE,CAAC,EAAEJ,UAAU,CAAC,EAAE;gBACnCzB,KAAK,CAAC6B,EAAE,CAAC,GAAG,EAAE;gBACd;cACF;cACA7B,KAAK,CAAC6B,EAAE,CAAC,GAAG,EAAE;YAChB;UACF,CAAC,MAAM;YACL,KAAK,IAAIC,GAAG,GAAGb,UAAU,EAAEa,GAAG,IAAI5B,CAAC,EAAE4B,GAAG,EAAE,EAAE;cAC1C9B,KAAK,CAAC8B,GAAG,CAAC,GAAG,EAAE;YACjB;YACA,IAAI,CAACN,IAAI,EAAE;cACTxB,KAAK,CAACE,CAAC,CAAC,GAAGU,UAAU,GAAGW,KAAK,GAAGG,IAAI;YACtC;UACF;QACF;QACAd,UAAU,IAAIc,IAAI;QAClB,KAAK,IAAIK,GAAG,GAAG,CAAC,EAAEC,IAAI,GAAGV,aAAa,CAAClB,MAAM,EAAE2B,GAAG,GAAGC,IAAI,EAAED,GAAG,EAAE,EAAE;UAChE,IAAIE,EAAE,GAAGX,aAAa,CAACS,GAAG,CAAC;UAC3B,IAAIpC,UAAU,CAAC+B,IAAI,EAAEO,EAAE,CAAC,EAAE;YACxBpB,mBAAmB,GAAG,CAAC,CAAC;YACxB;UACF;QACF;MACF;MACA,IAAIA,mBAAmB,KAAK,CAAC,CAAC,IAAIlB,UAAU,CAAC+B,IAAI,EAAEL,GAAG,CAAC;MACvD;MACA;MACAK,IAAI,CAACQ,MAAM,CAAC,CAAC,CAAC,CAACnB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;QAClC;QACA,IAAIW,IAAI,CAACA,IAAI,CAACtB,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;UACjCJ,KAAK,CAACE,CAAC,CAAC,GAAG,EAAE;QACf,CAAC,MAAM;UACLe,UAAU,GAAGf,CAAC;UACdW,mBAAmB,GAAGK,CAAC;UACvBN,UAAU,GAAGc,IAAI;QACnB;MACF;IACF;EACF;EACA,OAAO1B,KAAK;AACd;AACAmC,MAAM,CAACC,OAAO,GAAG7B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}