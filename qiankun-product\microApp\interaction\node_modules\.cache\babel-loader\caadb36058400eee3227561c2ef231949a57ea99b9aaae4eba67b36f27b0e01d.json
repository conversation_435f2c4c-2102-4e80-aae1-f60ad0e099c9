{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, normalizeStyle as _normalizeStyle, Fragment as _Fragment, createTextVNode as _createTextVNode, createBlock as _createBlock, normalizeClass as _normalizeClass, vShow as _vShow, withDirectives as _withDirectives, Transition as _Transition } from \"vue\";\nvar _hoisted_1 = {\n  class: \"LiveBroadcastDetails\"\n};\nvar _hoisted_2 = {\n  class: \"LiveBroadcastDetailsHeader\"\n};\nvar _hoisted_3 = [\"src\"];\nvar _hoisted_4 = {\n  class: \"LiveBroadcastDetailsHeadInfo\"\n};\nvar _hoisted_5 = {\n  class: \"LiveBroadcastDetailsTitle\"\n};\nvar _hoisted_6 = {\n  class: \"LiveBroadcastDetailsTime\"\n};\nvar _hoisted_7 = {\n  class: \"LiveBroadcastDetailsBody\"\n};\nvar _hoisted_8 = {\n  class: \"LiveBroadcastDetailsCanvas\"\n};\nvar _hoisted_9 = {\n  key: 0,\n  class: \"LiveBroadcastDetailsPoster\"\n};\nvar _hoisted_10 = [\"src\"];\nvar _hoisted_11 = {\n  class: \"LiveBroadcastDetailsCountdown\"\n};\nvar _hoisted_12 = {\n  class: \"countdown-line1\"\n};\nvar _hoisted_13 = {\n  class: \"countdown-line2\"\n};\nvar _hoisted_14 = {\n  class: \"LiveBroadcastDetailsLiveOverlay\"\n};\nvar _hoisted_15 = {\n  class: \"player-container\"\n};\nvar _hoisted_16 = {\n  ref: \"videoPlayer\",\n  id: \"video-player\",\n  controls: \"\"\n};\nvar _hoisted_17 = {\n  class: \"status-panel\"\n};\nvar _hoisted_18 = {\n  class: \"status-message\"\n};\nvar _hoisted_19 = {\n  class: \"LiveBroadcastDetailsEnded\"\n};\nvar _hoisted_20 = {\n  class: \"LiveBroadcastDetailsEndedWrap\"\n};\nvar _hoisted_21 = {\n  class: \"LiveBroadcastDetailsSidebar\"\n};\nvar _hoisted_22 = {\n  class: \"LiveBroadcastDetailsTabs\"\n};\nvar _hoisted_23 = {\n  key: 0,\n  class: \"LiveBroadcastDetailsTabPane\"\n};\nvar _hoisted_24 = {\n  class: \"detailsTitle\"\n};\nvar _hoisted_25 = {\n  class: \"detailsTime\"\n};\nvar _hoisted_26 = {\n  class: \"detailsDesc\"\n};\nvar _hoisted_27 = {\n  key: 1,\n  class: \"LiveBroadcastDetailsTabPane\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createBlock(_Transition, {\n    name: \"details-fade\",\n    persisted: \"\"\n  }, {\n    default: _withCtx(function () {\n      var _$setup$details, _$setup$details2, _$setup$details3;\n      return [_withDirectives(_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [$setup.logoSrc ? (_openBlock(), _createElementBlock(\"img\", {\n        key: 0,\n        class: \"LiveBroadcastDetailsEmblem\",\n        src: $setup.logoSrc,\n        alt: \"emblem\"\n      }, null, 8 /* PROPS */, _hoisted_3)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, _toDisplayString($setup.details.theme), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_6, \"直播时间：\" + _toDisplayString($setup.format($setup.details.startTime)) + \" 到 \" + _toDisplayString($setup.format($setup.details.endTime)), 1 /* TEXT */)]), _createVNode(_component_el_icon, {\n        class: \"LiveBroadcastDetailsClose\",\n        onClick: $setup.handleClose\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode($setup[\"Close\"])];\n        }),\n        _: 1 /* STABLE */\n      })]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createCommentVNode(\" 未开始：显示封面图 + 倒计时 \"), $setup.details.meetingStatus === '未开始' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createElementVNode(\"img\", {\n        src: $setup.imgUrl,\n        alt: \"海报/播放画面区域\",\n        style: {\n          \"width\": \"100%\",\n          \"height\": \"100%\",\n          \"object-fit\": \"contain\"\n        }\n      }, null, 8 /* PROPS */, _hoisted_10), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, \"距离 \" + _toDisplayString($setup.format($setup.details.startTime, 'MM/DD HH:mm')) + \" 直播开始还有\", 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_13, _toDisplayString($setup.countdownTimeOnly), 1 /* TEXT */)])])) : $setup.details.meetingStatus === '进行中' ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 1\n      }, [_createCommentVNode(\" 进行中：直播播放器 \"), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, [_createElementVNode(\"video\", _hoisted_16, null, 512 /* NEED_PATCH */)]), _createElementVNode(\"div\", _hoisted_17, [_createElementVNode(\"div\", _hoisted_18, _toDisplayString(_ctx.statusMessage), 1 /* TEXT */), _ctx.errorMessage ? (_openBlock(), _createElementBlock(\"div\", {\n        key: 0,\n        class: \"error-message\",\n        style: _normalizeStyle({\n          display: _ctx.errorMessage ? 'block' : 'none'\n        })\n      }, _toDisplayString(_ctx.errorMessage), 5 /* TEXT, STYLE */)) : _createCommentVNode(\"v-if\", true)])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : $setup.details.meetingStatus === '已结束' ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 2\n      }, [_createCommentVNode(\" 已结束：遮罩 + 回放按钮 \"), _createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", _hoisted_20, [_cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n        class: \"endedTitle\"\n      }, \"直播已结束\", -1 /* HOISTED */)), $setup.details.isReplay == 1 ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 0,\n        type: \"primary\",\n        class: \"replayBtn\"\n      }, {\n        default: _withCtx(function () {\n          return _cache[2] || (_cache[2] = [_createTextVNode(\"观看回放\")]);\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true)])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_21, [_createElementVNode(\"div\", _hoisted_22, [_createElementVNode(\"div\", {\n        class: _normalizeClass(['LiveBroadcastDetailsTab', {\n          active: $setup.activeTab === 'details'\n        }]),\n        onClick: _cache[0] || (_cache[0] = function ($event) {\n          return $setup.activeTab = 'details';\n        })\n      }, \" 直播详情\", 2 /* CLASS */), _createElementVNode(\"div\", {\n        class: _normalizeClass(['LiveBroadcastDetailsTab', {\n          active: $setup.activeTab === 'interact'\n        }]),\n        onClick: _cache[1] || (_cache[1] = function ($event) {\n          return $setup.activeTab = 'interact';\n        })\n      }, \" 互动\", 2 /* CLASS */)]), $setup.activeTab === 'details' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_23, [_createElementVNode(\"div\", _hoisted_24, _toDisplayString(((_$setup$details = $setup.details) === null || _$setup$details === void 0 ? void 0 : _$setup$details.theme) || '-'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_25, \"时间：\" + _toDisplayString($setup.format((_$setup$details2 = $setup.details) === null || _$setup$details2 === void 0 ? void 0 : _$setup$details2.startTime)) + \" - \" + _toDisplayString($setup.format((_$setup$details3 = $setup.details) === null || _$setup$details3 === void 0 ? void 0 : _$setup$details3.endTime)), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_26, _toDisplayString($setup.details.liveDescribes || '暂无简介'), 1 /* TEXT */)])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_27, _cache[4] || (_cache[4] = [_createElementVNode(\"div\", {\n        class: \"LiveBroadcastDetailsPanelTitle\"\n      }, \"互动\", -1 /* HOISTED */), _createElementVNode(\"div\", {\n        class: \"LiveBroadcastDetailsPanelText\"\n      }, \"暂无互动内容\", -1 /* HOISTED */)])))])])], 512 /* NEED_PATCH */), [[_vShow, $props.modelValue]])];\n    }),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["class", "key", "ref", "id", "controls", "_createBlock", "_Transition", "name", "persisted", "default", "_withCtx", "_$setup$details", "_$setup$details2", "_$setup$details3", "_createElementVNode", "_hoisted_1", "_hoisted_2", "$setup", "logoSrc", "_createElementBlock", "src", "alt", "_hoisted_3", "_createCommentVNode", "_hoisted_4", "_hoisted_5", "_toDisplayString", "details", "theme", "_hoisted_6", "format", "startTime", "endTime", "_createVNode", "_component_el_icon", "onClick", "handleClose", "_", "_hoisted_7", "_hoisted_8", "meetingStatus", "_hoisted_9", "imgUrl", "style", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "countdownTimeOnly", "_Fragment", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_ctx", "statusMessage", "errorMessage", "_normalizeStyle", "display", "_hoisted_19", "_hoisted_20", "isReplay", "_component_el_button", "type", "_cache", "_createTextVNode", "_hoisted_21", "_hoisted_22", "_normalizeClass", "active", "activeTab", "$event", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "liveDescribes", "_hoisted_27", "$props", "modelValue"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\LiveBroadcastDetails.vue"], "sourcesContent": ["<template>\r\n  <transition name=\"details-fade\">\r\n    <div v-show=\"modelValue\" class=\"LiveBroadcastDetails\">\r\n      <div class=\"LiveBroadcastDetailsHeader\">\r\n        <img v-if=\"logoSrc\" class=\"LiveBroadcastDetailsEmblem\" :src=\"logoSrc\" alt=\"emblem\" />\r\n        <div class=\"LiveBroadcastDetailsHeadInfo\">\r\n          <div class=\"LiveBroadcastDetailsTitle\">{{ details.theme }}</div>\r\n          <div class=\"LiveBroadcastDetailsTime\">直播时间：{{ format(details.startTime) }} 到 {{\r\n            format(details.endTime) }}</div>\r\n        </div>\r\n        <el-icon class=\"LiveBroadcastDetailsClose\" @click=\"handleClose\">\r\n          <Close />\r\n        </el-icon>\r\n      </div>\r\n      <div class=\"LiveBroadcastDetailsBody\">\r\n        <div class=\"LiveBroadcastDetailsCanvas\">\r\n          <!-- 未开始：显示封面图 + 倒计时 -->\r\n          <div v-if=\"details.meetingStatus === '未开始'\" class=\"LiveBroadcastDetailsPoster\">\r\n            <img :src=\"imgUrl\" alt=\"海报/播放画面区域\" style=\"width: 100%;height: 100%;object-fit: contain;\">\r\n            <div class=\"LiveBroadcastDetailsCountdown\">\r\n              <div class=\"countdown-line1\">距离 {{ format(details.startTime, 'MM/DD HH:mm') }} 直播开始还有</div>\r\n              <div class=\"countdown-line2\">{{ countdownTimeOnly }}</div>\r\n            </div>\r\n          </div>\r\n          <!-- 进行中：直播播放器 -->\r\n          <div v-else-if=\"details.meetingStatus === '进行中'\" class=\"LiveBroadcastDetailsLiveOverlay\">\r\n            <div class=\"player-container\">\r\n              <video ref=\"videoPlayer\" id=\"video-player\" controls></video>\r\n            </div>\r\n            <div class=\"status-panel\">\r\n              <div class=\"status-message\">{{ statusMessage }}</div>\r\n              <div class=\"error-message\" v-if=\"errorMessage\" :style=\"{ display: errorMessage ? 'block' : 'none' }\">\r\n                {{ errorMessage }}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <!-- 已结束：遮罩 + 回放按钮 -->\r\n          <div v-else-if=\"details.meetingStatus === '已结束'\" class=\"LiveBroadcastDetailsEnded\">\r\n            <div class=\"LiveBroadcastDetailsEndedWrap\">\r\n              <div class=\"endedTitle\">直播已结束</div>\r\n              <el-button type=\"primary\" class=\"replayBtn\" v-if=\"details.isReplay == 1\">观看回放</el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"LiveBroadcastDetailsSidebar\">\r\n          <div class=\"LiveBroadcastDetailsTabs\">\r\n            <div :class=\"['LiveBroadcastDetailsTab', { active: activeTab === 'details' }]\"\r\n              @click=\"activeTab = 'details'\">\r\n              直播详情</div>\r\n            <div :class=\"['LiveBroadcastDetailsTab', { active: activeTab === 'interact' }]\"\r\n              @click=\"activeTab = 'interact'\">\r\n              互动</div>\r\n          </div>\r\n          <div class=\"LiveBroadcastDetailsTabPane\" v-if=\"activeTab === 'details'\">\r\n            <div class=\"detailsTitle\">{{ details?.theme || '-' }}</div>\r\n            <div class=\"detailsTime\">时间：{{ format(details?.startTime) }} - {{ format(details?.endTime) }}</div>\r\n            <div class=\"detailsDesc\">{{ details.liveDescribes || '暂无简介' }}</div>\r\n          </div>\r\n          <div class=\"LiveBroadcastDetailsTabPane\" v-else>\r\n            <div class=\"LiveBroadcastDetailsPanelTitle\">互动</div>\r\n            <div class=\"LiveBroadcastDetailsPanelText\">暂无互动内容</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </transition>\r\n\r\n</template>\r\n<script>\r\nexport default { name: 'LiveBroadcastDetails' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onBeforeUnmount, watch, onMounted, computed, nextTick } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { Close } from '@element-plus/icons-vue'\r\nimport Hls from 'hls.js'\r\n// 使用 hls.js 播放 HLS 流\r\nconst props = defineProps({\r\n  modelValue: { type: Boolean, default: false },\r\n  id: { type: String, default: '' }\r\n})\r\nconst emit = defineEmits(['callback', 'update:modelValue'])\r\nconst details = ref({})\r\nconst logoSrc = ref('https://xazx.cszysoft.com:8131/lzt/pageImg/open/logo?areaId=610100')\r\n\r\nconst activeTab = ref('details')\r\nconst imgUrl = computed(() => details.value.coverImg ? api.fileURL(details.value.coverImg) : '')\r\nconst countdownText = ref('')\r\nconst countdownTimeOnly = ref('')\r\n\r\n// 视频播放器相关\r\nconst videoPlayer = ref(null)\r\nconst player = ref(null)\r\nconst hls = ref(null)\r\nconst isPlayerInitialized = ref(false)\r\nonMounted(() => {\r\n  // 在 onMounted 中，如果组件已经显示且有 id，则加载数据\r\n  if (props.modelValue && props.id) {\r\n    getInfo()\r\n  }\r\n})\r\n\r\n// 监听 modelValue 变化 - 当组件从隐藏变为显示时加载数据\r\nwatch(() => props.modelValue, (newVal, oldVal) => {\r\n  if (newVal && props.id && !oldVal) {\r\n    // 只有从 false 变为 true 且有 id 时才加载数据\r\n    getInfo()\r\n  }\r\n})\r\n\r\n// 监听 id 变化 - 当 id 改变且组件显示时加载数据\r\nwatch(() => props.id, (newVal, oldVal) => {\r\n  if (newVal && props.modelValue && newVal !== oldVal) {\r\n    getInfo()\r\n  }\r\n})\r\n\r\nconst getInfo = async () => {\r\n  const res = await api.videoConnectionInfo({ detailId: props.id })\r\n  var { data } = res\r\n  details.value = data\r\n  applyStatusFromProps()\r\n}\r\n\r\n// 监听状态变化\r\nwatch(() => details.value.meetingStatus, (newStatus, oldStatus) => {\r\n  if (newStatus === '进行中' && oldStatus !== '进行中') {\r\n    // 从未开始变为进行中，初始化播放器\r\n    nextTick(() => {\r\n      initVideoPlayer()\r\n    })\r\n  } else if (newStatus !== '进行中' && oldStatus === '进行中') {\r\n    // 从进行中变为其他状态，销毁播放器\r\n    destroyVideoPlayer()\r\n  }\r\n})\r\n\r\nconst tickCountdown = () => {\r\n  if (!details.value.startTime) return\r\n  const start = new Date(details.value.startTime).getTime()\r\n  const now = Date.now()\r\n  let diff = Math.max(0, start - now)\r\n\r\n  const sec = Math.floor(diff / 1000) % 60\r\n  const min = Math.floor(diff / (1000 * 60)) % 60\r\n  const hour = Math.floor(diff / (1000 * 60 * 60)) % 24\r\n  const day = Math.floor(diff / (1000 * 60 * 60 * 24))\r\n\r\n  let timeText = ''\r\n\r\n  if (day > 0) {\r\n    // 大于1天：显示 X天X时X分\r\n    timeText = `${day} 天 ${hour} 时 ${min} 分`\r\n  } else if (hour > 0) {\r\n    // 大于1小时但小于1天：显示 X时X分X秒\r\n    timeText = `${hour} 时 ${min} 分 ${sec} 秒`\r\n  } else if (min > 0) {\r\n    // 大于1分钟但小于1小时：显示 X分X秒\r\n    timeText = `${min} 分 ${sec} 秒`\r\n  } else {\r\n    // 小于1分钟：显示 X秒\r\n    timeText = `${sec} 秒`\r\n  }\r\n\r\n  // 设置完整的倒计时文本（保留原有逻辑）\r\n  countdownText.value = `距离 ${format(details.value.startTime, 'MM/DD HH:mm')} 直播开始还有 ${timeText}`\r\n  // 设置只包含时间的文本（用于两行显示）\r\n  countdownTimeOnly.value = timeText\r\n}\r\n\r\nlet timer = null\r\nconst applyStatusFromProps = () => {\r\n  if (timer) clearInterval(timer)\r\n  if (details.value.meetingStatus === '未开始') {\r\n    tickCountdown()\r\n    timer = setInterval(tickCountdown, 1000)\r\n  } else if (details.value.meetingStatus === '进行中') {\r\n    // 初始化播放器\r\n    nextTick(() => {\r\n      initVideoPlayer()\r\n    })\r\n  }\r\n}\r\n\r\nconst initVideoPlayer = async () => {\r\n  if (isPlayerInitialized.value || !videoPlayer.value) return\r\n  // 销毁现有播放器\r\n  destroyVideoPlayer()\r\n  const video = videoPlayer.value\r\n  player.value = video\r\n  isPlayerInitialized.value = true\r\n  // HLS视频流地址 - 使用您提供的地址\r\n  const hlsUrl =\r\n    'http://prdpulllive.xylink.com/prdnemo/9681c99c9088f6520198def4ad835a39.m3u8?auth_key=0cd581927cc20104121e61dae8be17eb-1756182609-c60224498c3e4842a0034bb0575b00fb-'\r\n  // 检查浏览器是否原生支持HLS\r\n  if (video.canPlayType('application/vnd.apple.mpegurl')) {\r\n    // 原生支持HLS\r\n    video.src = hlsUrl\r\n    setupVideoEvents()\r\n  } else if (Hls.isSupported()) {\r\n    // 使用HLS.js库\r\n    hls.value = new Hls({\r\n      maxBufferLength: 30,\r\n      maxMaxBufferLength: 60,\r\n      startLevel: -1, // 自动选择适合的初始清晰度\r\n      maxBufferHole: 0.5,\r\n      highLatencyMode: false\r\n    })\r\n    // 加载视频流\r\n    hls.value.loadSource(hlsUrl)\r\n    hls.value.attachMedia(video)\r\n    // HLS事件监听\r\n    hls.value.on(Hls.Events.MANIFEST_PARSED, function () {\r\n      console.log('视频流准备就绪，点击播放按钮开始')\r\n    })\r\n    // 错误处理\r\n    hls.value.on(Hls.Events.ERROR, function (_, data) {\r\n      console.error('HLS错误:', data)\r\n      switch (data.type) {\r\n        case Hls.ErrorTypes.NETWORK_ERROR:\r\n          hls.value.startLoad() // 尝试重新加载\r\n          break\r\n        case Hls.ErrorTypes.MEDIA_ERROR:\r\n          hls.value.recoverMediaError() // 尝试恢复媒体错误\r\n          break\r\n        default:\r\n          // 无法恢复的错误，尝试重新初始化\r\n          setTimeout(initVideoPlayer, 3000)\r\n          break\r\n      }\r\n    })\r\n    setupVideoEvents()\r\n  }\r\n}\r\n\r\n// 设置视频事件监听\r\nconst setupVideoEvents = () => {\r\n  const video = player.value\r\n  if (!video) return\r\n\r\n  // 视频可以播放时\r\n  video.addEventListener('canplay', function () {\r\n    statusMessage.value = '视频准备就绪，点击播放按钮开始'\r\n  })\r\n\r\n  // 播放事件\r\n  video.addEventListener('play', function () {\r\n    isPlaying.value = true\r\n    statusMessage.value = '正在播放HLS视频流'\r\n  })\r\n\r\n  // 暂停事件\r\n  video.addEventListener('pause', function () {\r\n    isPlaying.value = false\r\n    statusMessage.value = 'HLS视频流已暂停'\r\n  })\r\n\r\n  // 视频结束事件\r\n  video.addEventListener('ended', function () {\r\n    isPlaying.value = false\r\n    statusMessage.value = '视频播放已结束'\r\n  })\r\n\r\n  // 音量变化事件\r\n  video.addEventListener('volumechange', function () {\r\n    volume.value = video.volume\r\n  })\r\n}\r\n// 销毁视频播放器\r\nconst destroyVideoPlayer = () => {\r\n  // 销毁 hls.js 实例\r\n  if (hls.value) {\r\n    try {\r\n      hls.value.destroy()\r\n    } catch (error) {\r\n      console.error('销毁HLS实例错误:', error)\r\n    }\r\n    hls.value = null\r\n  }\r\n\r\n  if (player.value) {\r\n    try {\r\n      player.value.pause()\r\n      player.value.src = ''\r\n      player.value.load()\r\n    } catch (error) {\r\n      console.error('销毁播放器错误:', error)\r\n    }\r\n    player.value = null\r\n    isPlayerInitialized.value = false\r\n  }\r\n}\r\n\r\nonBeforeUnmount(() => {\r\n  if (timer) clearInterval(timer)\r\n  destroyVideoPlayer()\r\n})\r\n\r\nconst handleClose = () => {\r\n  emit('update:modelValue', false)\r\n  emit('callback')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.LiveBroadcastDetails {\r\n  position: fixed;\r\n  inset: 0;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  background: #0F0F0F;\r\n  display: flex;\r\n  flex-direction: column;\r\n  z-index: 9999;\r\n\r\n  .LiveBroadcastDetailsHeader {\r\n    position: relative;\r\n    height: 80px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 16px;\r\n    color: #fff;\r\n    background: #2B2B2B;\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.08);\r\n\r\n    .LiveBroadcastDetailsEmblem {\r\n      width: 58px;\r\n      height: 58px;\r\n      margin-right: 14px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsHeadInfo {\r\n      display: flex;\r\n      flex-direction: column;\r\n    }\r\n\r\n    .LiveBroadcastDetailsTitle {\r\n      font-family: Microsoft YaHei, Microsoft YaHei;\r\n      font-weight: bold;\r\n      font-size: 20px;\r\n      color: #FFFFFF;\r\n      margin-bottom: 12px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsTime {\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #D9D9D9;\r\n    }\r\n\r\n    .LiveBroadcastDetailsClose {\r\n      position: absolute;\r\n      right: 16px;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      color: #fff;\r\n      cursor: pointer;\r\n      font-size: 18px;\r\n      opacity: .85;\r\n    }\r\n  }\r\n\r\n  .LiveBroadcastDetailsBody {\r\n    flex: 1;\r\n    display: grid;\r\n    grid-template-columns: 1fr 360px;\r\n    gap: 16px;\r\n    padding: 16px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .LiveBroadcastDetailsCanvas {\r\n    position: relative;\r\n    height: 100%;\r\n    background: #111;\r\n    overflow: hidden;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .LiveBroadcastDetailsPoster {\r\n      width: 100%;\r\n      height: 100%;\r\n      background: #000;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      color: #d23a2e;\r\n      font-weight: bold;\r\n      font-size: 22px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsCountdown {\r\n      position: absolute;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      height: 95px;\r\n      background: rgba(0, 0, 0, 0.8);\r\n      color: #fff;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n\r\n      .countdown-line1 {\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        margin-bottom: 8px;\r\n        opacity: 0.9;\r\n      }\r\n\r\n      .countdown-line2 {\r\n        font-size: 24px;\r\n        font-weight: 600;\r\n        letter-spacing: 2px;\r\n      }\r\n    }\r\n\r\n    .LiveBroadcastDetailsLiveOverlay {\r\n      position: absolute;\r\n      left: 0;\r\n      right: 0;\r\n      top: 0;\r\n      bottom: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n\r\n      .video-container {\r\n        width: 100%;\r\n        height: 100%;\r\n        position: relative;\r\n\r\n        .live-video-player {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: contain;\r\n        }\r\n\r\n        .custom-video-controls {\r\n          position: absolute;\r\n          bottom: 0;\r\n          left: 0;\r\n          right: 0;\r\n          background: rgba(0, 0, 0, 0.7);\r\n          padding: 10px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: space-between;\r\n          color: white;\r\n          font-size: 14px;\r\n\r\n          button {\r\n            background: none;\r\n            border: none;\r\n            color: white;\r\n            cursor: pointer;\r\n            font-size: 14px;\r\n            padding: 5px;\r\n            border-radius: 3px;\r\n            transition: background-color 0.2s;\r\n\r\n            &:hover {\r\n              background: rgba(255, 255, 255, 0.1);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .LiveBroadcastDetailsEnded {\r\n      position: absolute;\r\n      inset: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      background: rgba(0, 0, 0, .45);\r\n\r\n      .LiveBroadcastDetailsEndedWrap {\r\n        text-align: center;\r\n\r\n        .endedTitle {\r\n          color: #fff;\r\n          font-size: 18px;\r\n          margin-bottom: 12px;\r\n        }\r\n\r\n        .replayBtn {\r\n          background: linear-gradient(90deg, #5bc0ff, #5f7cff);\r\n          border: none;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .LiveBroadcastDetailsSidebar {\r\n    height: 100%;\r\n    background: #191919;\r\n    border-left: 1px solid rgba(255, 255, 255, 0.05);\r\n    color: #e8e8e8;\r\n    // padding: 14px 16px 16px 16px;\r\n    overflow: auto;\r\n\r\n    .LiveBroadcastDetailsTabs {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-around;\r\n      gap: 40px;\r\n      margin-bottom: 10px;\r\n      background: #2B2B2B;\r\n      padding: 14px 16px;\r\n\r\n      .LiveBroadcastDetailsTab {\r\n        cursor: pointer;\r\n        color: #999999;\r\n        position: relative;\r\n        font-size: 16px;\r\n        line-height: 1;\r\n        transition: color .2s ease;\r\n        font-weight: 500;\r\n\r\n        &.active {\r\n          color: #ffffff;\r\n          font-weight: 700;\r\n        }\r\n\r\n        &.active::after {\r\n          content: '';\r\n          position: absolute;\r\n          left: 0;\r\n          right: 0;\r\n          bottom: -8px;\r\n          height: 3px;\r\n          background: #54BDFF;\r\n          border-radius: 3px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LiveBroadcastDetailsTabPane {\r\n      padding: 12px 15px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsPanelTitle {\r\n      font-weight: bold;\r\n      margin-bottom: 10px;\r\n      font-size: 15px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsPanelText {\r\n      font-size: 13px;\r\n      line-height: 1.8;\r\n      color: #cfcfcf;\r\n    }\r\n\r\n    /* 详情tab内样式 */\r\n    .detailsTitle {\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #FFFFFF;\r\n      margin-bottom: 14px;\r\n    }\r\n\r\n    .detailsTime {\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #666666;\r\n      margin-bottom: 12px;\r\n    }\r\n\r\n    .detailsDesc {\r\n      font-size: 13px;\r\n      color: #d9d9d9;\r\n      line-height: 1.8;\r\n    }\r\n  }\r\n}\r\n\r\n.details-fade-enter-active,\r\n.details-fade-leave-active {\r\n  transition: opacity .2s ease;\r\n}\r\n\r\n.details-fade-enter-from,\r\n.details-fade-leave-to {\r\n  opacity: 0;\r\n}\r\n</style>\r\n"], "mappings": ";;EAE6BA,KAAK,EAAC;AAAsB;;EAC9CA,KAAK,EAAC;AAA4B;iBAH7C;;EAKaA,KAAK,EAAC;AAA8B;;EAClCA,KAAK,EAAC;AAA2B;;EACjCA,KAAK,EAAC;AAA0B;;EAOpCA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAA4B;;EAf/CC,GAAA;EAiBsDD,KAAK,EAAC;;kBAjB5D;;EAmBiBA,KAAK,EAAC;AAA+B;;EACnCA,KAAK,EAAC;AAAiB;;EACvBA,KAAK,EAAC;AAAiB;;EAIiBA,KAAK,EAAC;AAAiC;;EACjFA,KAAK,EAAC;AAAkB;;EACpBE,GAAG,EAAC,aAAa;EAACC,EAAE,EAAC,cAAc;EAACC,QAAQ,EAAR;;;EAExCJ,KAAK,EAAC;AAAc;;EAClBA,KAAK,EAAC;AAAgB;;EAOkBA,KAAK,EAAC;AAA2B;;EAC3EA,KAAK,EAAC;AAA+B;;EAMzCA,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAA0B;;EA7C/CC,GAAA;EAqDeD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;EAxDpCC,GAAA;EA0DeD,KAAK,EAAC;;;;;uBAzDnBK,YAAA,CAgEaC,WAAA;IAhEDC,IAAI,EAAC,cAAc;IAA/BC,SAgEa,EAhEb;;IADFC,OAAA,EAAAC,QAAA,CAEI;MAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;MAAA,OA8DM,C,gBA9DNC,mBAAA,CA8DM,OA9DNC,UA8DM,GA7DJD,mBAAA,CAUM,OAVNE,UAUM,GATOC,MAAA,CAAAC,OAAO,I,cAAlBC,mBAAA,CAAqF;QAJ7FlB,GAAA;QAI4BD,KAAK,EAAC,4BAA4B;QAAEoB,GAAG,EAAEH,MAAA,CAAAC,OAAO;QAAEG,GAAG,EAAC;8BAJlFC,UAAA,KAAAC,mBAAA,gBAKQT,mBAAA,CAIM,OAJNU,UAIM,GAHJV,mBAAA,CAAgE,OAAhEW,UAAgE,EAAAC,gBAAA,CAAtBT,MAAA,CAAAU,OAAO,CAACC,KAAK,kBACvDd,mBAAA,CACkC,OADlCe,UACkC,EADI,OAAK,GAAAH,gBAAA,CAAGT,MAAA,CAAAa,MAAM,CAACb,MAAA,CAAAU,OAAO,CAACI,SAAS,KAAI,KAAG,GAAAL,gBAAA,CAC3ET,MAAA,CAAAa,MAAM,CAACb,MAAA,CAAAU,OAAO,CAACK,OAAO,kB,GAE1BC,YAAA,CAEUC,kBAAA;QAFDlC,KAAK,EAAC,2BAA2B;QAAEmC,OAAK,EAAElB,MAAA,CAAAmB;;QAV3D3B,OAAA,EAAAC,QAAA,CAWU;UAAA,OAAS,CAATuB,YAAA,CAAShB,MAAA,W;;QAXnBoB,CAAA;YAcMvB,mBAAA,CAiDM,OAjDNwB,UAiDM,GAhDJxB,mBAAA,CA4BM,OA5BNyB,UA4BM,GA3BJhB,mBAAA,qBAAwB,EACbN,MAAA,CAAAU,OAAO,CAACa,aAAa,c,cAAhCrB,mBAAA,CAMM,OANNsB,UAMM,GALJ3B,mBAAA,CAAyF;QAAnFM,GAAG,EAAEH,MAAA,CAAAyB,MAAM;QAAErB,GAAG,EAAC,WAAW;QAACsB,KAAqD,EAArD;UAAA;UAAA;UAAA;QAAA;8BAlB/CC,WAAA,GAmBY9B,mBAAA,CAGM,OAHN+B,WAGM,GAFJ/B,mBAAA,CAA2F,OAA3FgC,WAA2F,EAA9D,KAAG,GAAApB,gBAAA,CAAGT,MAAA,CAAAa,MAAM,CAACb,MAAA,CAAAU,OAAO,CAACI,SAAS,oBAAmB,SAAO,iBACrFjB,mBAAA,CAA0D,OAA1DiC,WAA0D,EAAArB,gBAAA,CAA1BT,MAAA,CAAA+B,iBAAiB,iB,OAIrC/B,MAAA,CAAAU,OAAO,CAACa,aAAa,c,cAArCrB,mBAAA,CAUM8B,SAAA;QAnChBhD,GAAA;MAAA,IAwBUsB,mBAAA,eAAkB,EAClBT,mBAAA,CAUM,OAVNoC,WAUM,GATJpC,mBAAA,CAEM,OAFNqC,WAEM,GADJrC,mBAAA,CAA4D,SAA5DsC,WAA4D,8B,GAE9DtC,mBAAA,CAKM,OALNuC,WAKM,GAJJvC,mBAAA,CAAqD,OAArDwC,WAAqD,EAAA5B,gBAAA,CAAtB6B,IAAA,CAAAC,aAAa,kBACXD,IAAA,CAAAE,YAAY,I,cAA7CtC,mBAAA,CAEM;QAjCpBlB,GAAA;QA+BmBD,KAAK,EAAC,eAAe;QAAsB2C,KAAK,EA/BnEe,eAAA;UAAAC,OAAA,EA+BgFJ,IAAA,CAAAE,YAAY;QAAA;0BACzEF,IAAA,CAAAE,YAAY,2BAhC/BlC,mBAAA,e,wDAqC0BN,MAAA,CAAAU,OAAO,CAACa,aAAa,c,cAArCrB,mBAAA,CAKM8B,SAAA;QA1ChBhD,GAAA;MAAA,IAoCUsB,mBAAA,mBAAsB,EACtBT,mBAAA,CAKM,OALN8C,WAKM,GAJJ9C,mBAAA,CAGM,OAHN+C,WAGM,G,0BAFJ/C,mBAAA,CAAmC;QAA9Bd,KAAK,EAAC;MAAY,GAAC,OAAK,sBACqBiB,MAAA,CAAAU,OAAO,CAACmC,QAAQ,S,cAAlEzD,YAAA,CAAyF0D,oBAAA;QAxCvG9D,GAAA;QAwCyB+D,IAAI,EAAC,SAAS;QAAChE,KAAK,EAAC;;QAxC9CS,OAAA,EAAAC,QAAA,CAwCuF;UAAA,OAAIuD,MAAA,QAAAA,MAAA,OAxC3FC,gBAAA,CAwCuF,MAAI,E;;QAxC3F7B,CAAA;YAAAd,mBAAA,e,wDAAAA,mBAAA,e,GA4CQT,mBAAA,CAkBM,OAlBNqD,WAkBM,GAjBJrD,mBAAA,CAOM,OAPNsD,WAOM,GANJtD,mBAAA,CAEY;QAFNd,KAAK,EA9CvBqE,eAAA;UAAAC,MAAA,EA8C+DrD,MAAA,CAAAsD,SAAS;QAAA;QACzDpC,OAAK,EAAA8B,MAAA,QAAAA,MAAA,gBAAAO,MAAA;UAAA,OAAEvD,MAAA,CAAAsD,SAAS;QAAA;SAAc,OAC3B,kBACNzD,mBAAA,CAEU;QAFJd,KAAK,EAjDvBqE,eAAA;UAAAC,MAAA,EAiD+DrD,MAAA,CAAAsD,SAAS;QAAA;QACzDpC,OAAK,EAAA8B,MAAA,QAAAA,MAAA,gBAAAO,MAAA;UAAA,OAAEvD,MAAA,CAAAsD,SAAS;QAAA;SAAe,KAC9B,iB,GAEyCtD,MAAA,CAAAsD,SAAS,kB,cAAxDpD,mBAAA,CAIM,OAJNsD,WAIM,GAHJ3D,mBAAA,CAA2D,OAA3D4D,WAA2D,EAAAhD,gBAAA,CAA9B,EAAAf,eAAA,GAAAM,MAAA,CAAAU,OAAO,cAAAhB,eAAA,uBAAPA,eAAA,CAASiB,KAAK,0BAC3Cd,mBAAA,CAAmG,OAAnG6D,WAAmG,EAA1E,KAAG,GAAAjD,gBAAA,CAAGT,MAAA,CAAAa,MAAM,EAAAlB,gBAAA,GAACK,MAAA,CAAAU,OAAO,cAAAf,gBAAA,uBAAPA,gBAAA,CAASmB,SAAS,KAAI,KAAG,GAAAL,gBAAA,CAAGT,MAAA,CAAAa,MAAM,EAAAjB,gBAAA,GAACI,MAAA,CAAAU,OAAO,cAAAd,gBAAA,uBAAPA,gBAAA,CAASmB,OAAO,mBACzFlB,mBAAA,CAAoE,OAApE8D,WAAoE,EAAAlD,gBAAA,CAAxCT,MAAA,CAAAU,OAAO,CAACkD,aAAa,2B,oBAEnD1D,mBAAA,CAGM,OAHN2D,WAGM,EAAAb,MAAA,QAAAA,MAAA,OAFJnD,mBAAA,CAAoD;QAA/Cd,KAAK,EAAC;MAAgC,GAAC,IAAE,qBAC9Cc,mBAAA,CAAuD;QAAlDd,KAAK,EAAC;MAA+B,GAAC,QAAM,oB,2CA1D5C+E,MAAA,CAAAC,UAAU,E;;IAF3B3C,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}