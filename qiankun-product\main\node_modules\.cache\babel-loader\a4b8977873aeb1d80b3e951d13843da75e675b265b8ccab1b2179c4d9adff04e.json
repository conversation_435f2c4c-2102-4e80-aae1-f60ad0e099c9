{"ast": null, "code": "'use strict';\n\n/** Highest positive signed 32-bit float value */\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nvar maxInt = 2147483647; // aka. 0x7FFFFFFF or 2^31-1\n\n/** Bootstring parameters */\nvar base = 36;\nvar tMin = 1;\nvar tMax = 26;\nvar skew = 38;\nvar damp = 700;\nvar initialBias = 72;\nvar initialN = 128; // 0x80\nvar delimiter = '-'; // '\\x2D'\n\n/** Regular expressions */\nvar regexPunycode = /^xn--/;\nvar regexNonASCII = /[^\\0-\\x7F]/; // Note: U+007F DEL is excluded too.\nvar regexSeparators = /[\\x2E\\u3002\\uFF0E\\uFF61]/g; // RFC 3490 separators\n\n/** Error messages */\nvar errors = {\n  'overflow': 'Overflow: input needs wider integers to process',\n  'not-basic': 'Illegal input >= 0x80 (not a basic code point)',\n  'invalid-input': 'Invalid input'\n};\n\n/** Convenience shortcuts */\nvar baseMinusTMin = base - tMin;\nvar floor = Math.floor;\nvar stringFromCharCode = String.fromCharCode;\n\n/*--------------------------------------------------------------------------*/\n\n/**\n * A generic error utility function.\n * @private\n * @param {String} type The error type.\n * @returns {Error} Throws a `RangeError` with the applicable error message.\n */\nfunction error(type) {\n  throw new RangeError(errors[type]);\n}\n\n/**\n * A generic `Array#map` utility function.\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} callback The function that gets called for every array\n * item.\n * @returns {Array} A new array of values returned by the callback function.\n */\nfunction map(array, callback) {\n  var result = [];\n  var length = array.length;\n  while (length--) {\n    result[length] = callback(array[length]);\n  }\n  return result;\n}\n\n/**\n * A simple `Array#map`-like wrapper to work with domain name strings or email\n * addresses.\n * @private\n * @param {String} domain The domain name or email address.\n * @param {Function} callback The function that gets called for every\n * character.\n * @returns {String} A new string of characters returned by the callback\n * function.\n */\nfunction mapDomain(domain, callback) {\n  var parts = domain.split('@');\n  var result = '';\n  if (parts.length > 1) {\n    // In email addresses, only the domain name should be punycoded. Leave\n    // the local part (i.e. everything up to `@`) intact.\n    result = parts[0] + '@';\n    domain = parts[1];\n  }\n  // Avoid `split(regex)` for IE8 compatibility. See #17.\n  domain = domain.replace(regexSeparators, '\\x2E');\n  var labels = domain.split('.');\n  var encoded = map(labels, callback).join('.');\n  return result + encoded;\n}\n\n/**\n * Creates an array containing the numeric code points of each Unicode\n * character in the string. While JavaScript uses UCS-2 internally,\n * this function will convert a pair of surrogate halves (each of which\n * UCS-2 exposes as separate characters) into a single code point,\n * matching UTF-16.\n * @see `punycode.ucs2.encode`\n * @see <https://mathiasbynens.be/notes/javascript-encoding>\n * @memberOf punycode.ucs2\n * @name decode\n * @param {String} string The Unicode input string (UCS-2).\n * @returns {Array} The new array of code points.\n */\nfunction ucs2decode(string) {\n  var output = [];\n  var counter = 0;\n  var length = string.length;\n  while (counter < length) {\n    var value = string.charCodeAt(counter++);\n    if (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n      // It's a high surrogate, and there is a next character.\n      var extra = string.charCodeAt(counter++);\n      if ((extra & 0xFC00) == 0xDC00) {\n        // Low surrogate.\n        output.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n      } else {\n        // It's an unmatched surrogate; only append this code unit, in case the\n        // next code unit is the high surrogate of a surrogate pair.\n        output.push(value);\n        counter--;\n      }\n    } else {\n      output.push(value);\n    }\n  }\n  return output;\n}\n\n/**\n * Creates a string based on an array of numeric code points.\n * @see `punycode.ucs2.decode`\n * @memberOf punycode.ucs2\n * @name encode\n * @param {Array} codePoints The array of numeric code points.\n * @returns {String} The new Unicode string (UCS-2).\n */\nvar ucs2encode = function ucs2encode(codePoints) {\n  return String.fromCodePoint.apply(String, _toConsumableArray(codePoints));\n};\n\n/**\n * Converts a basic code point into a digit/integer.\n * @see `digitToBasic()`\n * @private\n * @param {Number} codePoint The basic numeric code point value.\n * @returns {Number} The numeric value of a basic code point (for use in\n * representing integers) in the range `0` to `base - 1`, or `base` if\n * the code point does not represent a value.\n */\nvar basicToDigit = function basicToDigit(codePoint) {\n  if (codePoint >= 0x30 && codePoint < 0x3A) {\n    return 26 + (codePoint - 0x30);\n  }\n  if (codePoint >= 0x41 && codePoint < 0x5B) {\n    return codePoint - 0x41;\n  }\n  if (codePoint >= 0x61 && codePoint < 0x7B) {\n    return codePoint - 0x61;\n  }\n  return base;\n};\n\n/**\n * Converts a digit/integer into a basic code point.\n * @see `basicToDigit()`\n * @private\n * @param {Number} digit The numeric value of a basic code point.\n * @returns {Number} The basic code point whose value (when used for\n * representing integers) is `digit`, which needs to be in the range\n * `0` to `base - 1`. If `flag` is non-zero, the uppercase form is\n * used; else, the lowercase form is used. The behavior is undefined\n * if `flag` is non-zero and `digit` has no uppercase form.\n */\nvar digitToBasic = function digitToBasic(digit, flag) {\n  //  0..25 map to ASCII a..z or A..Z\n  // 26..35 map to ASCII 0..9\n  return digit + 22 + 75 * (digit < 26) - ((flag != 0) << 5);\n};\n\n/**\n * Bias adaptation function as per section 3.4 of RFC 3492.\n * https://tools.ietf.org/html/rfc3492#section-3.4\n * @private\n */\nvar adapt = function adapt(delta, numPoints, firstTime) {\n  var k = 0;\n  delta = firstTime ? floor(delta / damp) : delta >> 1;\n  delta += floor(delta / numPoints);\n  for /* no initialization */\n  (; delta > baseMinusTMin * tMax >> 1; k += base) {\n    delta = floor(delta / baseMinusTMin);\n  }\n  return floor(k + (baseMinusTMin + 1) * delta / (delta + skew));\n};\n\n/**\n * Converts a Punycode string of ASCII-only symbols to a string of Unicode\n * symbols.\n * @memberOf punycode\n * @param {String} input The Punycode string of ASCII-only symbols.\n * @returns {String} The resulting string of Unicode symbols.\n */\nvar decode = function decode(input) {\n  // Don't use UCS-2.\n  var output = [];\n  var inputLength = input.length;\n  var i = 0;\n  var n = initialN;\n  var bias = initialBias;\n\n  // Handle the basic code points: let `basic` be the number of input code\n  // points before the last delimiter, or `0` if there is none, then copy\n  // the first basic code points to the output.\n\n  var basic = input.lastIndexOf(delimiter);\n  if (basic < 0) {\n    basic = 0;\n  }\n  for (var j = 0; j < basic; ++j) {\n    // if it's not a basic code point\n    if (input.charCodeAt(j) >= 0x80) {\n      error('not-basic');\n    }\n    output.push(input.charCodeAt(j));\n  }\n\n  // Main decoding loop: start just after the last delimiter if any basic code\n  // points were copied; start at the beginning otherwise.\n\n  for /* no final expression */\n  (var index = basic > 0 ? basic + 1 : 0; index < inputLength;) {\n    // `index` is the index of the next character to be consumed.\n    // Decode a generalized variable-length integer into `delta`,\n    // which gets added to `i`. The overflow checking is easier\n    // if we increase `i` as we go, then subtract off its starting\n    // value at the end to obtain `delta`.\n    var oldi = i;\n    for /* no condition */\n    (var w = 1, k = base;; k += base) {\n      if (index >= inputLength) {\n        error('invalid-input');\n      }\n      var digit = basicToDigit(input.charCodeAt(index++));\n      if (digit >= base) {\n        error('invalid-input');\n      }\n      if (digit > floor((maxInt - i) / w)) {\n        error('overflow');\n      }\n      i += digit * w;\n      var t = k <= bias ? tMin : k >= bias + tMax ? tMax : k - bias;\n      if (digit < t) {\n        break;\n      }\n      var baseMinusT = base - t;\n      if (w > floor(maxInt / baseMinusT)) {\n        error('overflow');\n      }\n      w *= baseMinusT;\n    }\n    var out = output.length + 1;\n    bias = adapt(i - oldi, out, oldi == 0);\n\n    // `i` was supposed to wrap around from `out` to `0`,\n    // incrementing `n` each time, so we'll fix that now:\n    if (floor(i / out) > maxInt - n) {\n      error('overflow');\n    }\n    n += floor(i / out);\n    i %= out;\n\n    // Insert `n` at position `i` of the output.\n    output.splice(i++, 0, n);\n  }\n  return String.fromCodePoint.apply(String, output);\n};\n\n/**\n * Converts a string of Unicode symbols (e.g. a domain name label) to a\n * Punycode string of ASCII-only symbols.\n * @memberOf punycode\n * @param {String} input The string of Unicode symbols.\n * @returns {String} The resulting Punycode string of ASCII-only symbols.\n */\nvar encode = function encode(input) {\n  var output = [];\n\n  // Convert the input in UCS-2 to an array of Unicode code points.\n  input = ucs2decode(input);\n\n  // Cache the length.\n  var inputLength = input.length;\n\n  // Initialize the state.\n  var n = initialN;\n  var delta = 0;\n  var bias = initialBias;\n\n  // Handle the basic code points.\n  var _iterator = _createForOfIteratorHelper(input),\n    _step;\n  try {\n    for (_iterator.s(); !(_step = _iterator.n()).done;) {\n      var _currentValue2 = _step.value;\n      if (_currentValue2 < 0x80) {\n        output.push(stringFromCharCode(_currentValue2));\n      }\n    }\n  } catch (err) {\n    _iterator.e(err);\n  } finally {\n    _iterator.f();\n  }\n  var basicLength = output.length;\n  var handledCPCount = basicLength;\n\n  // `handledCPCount` is the number of code points that have been handled;\n  // `basicLength` is the number of basic code points.\n\n  // Finish the basic string with a delimiter unless it's empty.\n  if (basicLength) {\n    output.push(delimiter);\n  }\n\n  // Main encoding loop:\n  while (handledCPCount < inputLength) {\n    // All non-basic code points < n have been handled already. Find the next\n    // larger one:\n    var m = maxInt;\n    var _iterator2 = _createForOfIteratorHelper(input),\n      _step2;\n    try {\n      for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n        var currentValue = _step2.value;\n        if (currentValue >= n && currentValue < m) {\n          m = currentValue;\n        }\n      }\n\n      // Increase `delta` enough to advance the decoder's <n,i> state to <m,0>,\n      // but guard against overflow.\n    } catch (err) {\n      _iterator2.e(err);\n    } finally {\n      _iterator2.f();\n    }\n    var handledCPCountPlusOne = handledCPCount + 1;\n    if (m - n > floor((maxInt - delta) / handledCPCountPlusOne)) {\n      error('overflow');\n    }\n    delta += (m - n) * handledCPCountPlusOne;\n    n = m;\n    var _iterator3 = _createForOfIteratorHelper(input),\n      _step3;\n    try {\n      for (_iterator3.s(); !(_step3 = _iterator3.n()).done;) {\n        var _currentValue = _step3.value;\n        if (_currentValue < n && ++delta > maxInt) {\n          error('overflow');\n        }\n        if (_currentValue === n) {\n          // Represent delta as a generalized variable-length integer.\n          var q = delta;\n          for /* no condition */\n          (var k = base;; k += base) {\n            var t = k <= bias ? tMin : k >= bias + tMax ? tMax : k - bias;\n            if (q < t) {\n              break;\n            }\n            var qMinusT = q - t;\n            var baseMinusT = base - t;\n            output.push(stringFromCharCode(digitToBasic(t + qMinusT % baseMinusT, 0)));\n            q = floor(qMinusT / baseMinusT);\n          }\n          output.push(stringFromCharCode(digitToBasic(q, 0)));\n          bias = adapt(delta, handledCPCountPlusOne, handledCPCount === basicLength);\n          delta = 0;\n          ++handledCPCount;\n        }\n      }\n    } catch (err) {\n      _iterator3.e(err);\n    } finally {\n      _iterator3.f();\n    }\n    ++delta;\n    ++n;\n  }\n  return output.join('');\n};\n\n/**\n * Converts a Punycode string representing a domain name or an email address\n * to Unicode. Only the Punycoded parts of the input will be converted, i.e.\n * it doesn't matter if you call it on a string that has already been\n * converted to Unicode.\n * @memberOf punycode\n * @param {String} input The Punycoded domain name or email address to\n * convert to Unicode.\n * @returns {String} The Unicode representation of the given Punycode\n * string.\n */\nvar toUnicode = function toUnicode(input) {\n  return mapDomain(input, function (string) {\n    return regexPunycode.test(string) ? decode(string.slice(4).toLowerCase()) : string;\n  });\n};\n\n/**\n * Converts a Unicode string representing a domain name or an email address to\n * Punycode. Only the non-ASCII parts of the domain name will be converted,\n * i.e. it doesn't matter if you call it with a domain that's already in\n * ASCII.\n * @memberOf punycode\n * @param {String} input The domain name or email address to convert, as a\n * Unicode string.\n * @returns {String} The Punycode representation of the given domain name or\n * email address.\n */\nvar toASCII = function toASCII(input) {\n  return mapDomain(input, function (string) {\n    return regexNonASCII.test(string) ? 'xn--' + encode(string) : string;\n  });\n};\n\n/*--------------------------------------------------------------------------*/\n\n/** Define the public API */\nvar punycode = {\n  /**\n   * A string representing the current Punycode.js version number.\n   * @memberOf punycode\n   * @type String\n   */\n  'version': '2.3.1',\n  /**\n   * An object of methods to convert from JavaScript's internal character\n   * representation (UCS-2) to Unicode code points, and back.\n   * @see <https://mathiasbynens.be/notes/javascript-encoding>\n   * @memberOf punycode\n   * @type Object\n   */\n  'ucs2': {\n    'decode': ucs2decode,\n    'encode': ucs2encode\n  },\n  'decode': decode,\n  'encode': encode,\n  'toASCII': toASCII,\n  'toUnicode': toUnicode\n};\nexport { ucs2decode, ucs2encode, decode, encode, toASCII, toUnicode };\nexport default punycode;", "map": {"version": 3, "names": ["_createForOfIteratorHelper", "r", "e", "t", "Symbol", "iterator", "Array", "isArray", "_unsupportedIterableToArray", "length", "_n", "F", "s", "n", "done", "value", "f", "TypeError", "o", "a", "u", "call", "next", "return", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "_nonIterableSpread", "_arrayLikeToArray", "toString", "slice", "constructor", "name", "from", "test", "maxInt", "base", "tMin", "tMax", "skew", "damp", "initialBias", "initialN", "delimiter", "regexPunycode", "regexNonASCII", "regexSeparators", "errors", "baseMinusTMin", "floor", "Math", "stringFromCharCode", "String", "fromCharCode", "error", "type", "RangeError", "map", "array", "callback", "result", "mapDomain", "domain", "parts", "split", "replace", "labels", "encoded", "join", "ucs2decode", "string", "output", "counter", "charCodeAt", "extra", "push", "ucs2encode", "codePoints", "fromCodePoint", "apply", "basicToDigit", "codePoint", "digitToBasic", "digit", "flag", "adapt", "delta", "numPoints", "firstTime", "k", "decode", "input", "inputLength", "i", "bias", "basic", "lastIndexOf", "j", "index", "oldi", "w", "baseMinusT", "out", "splice", "encode", "_iterator", "_step", "currentValue", "err", "basicLength", "handledCPCount", "m", "_iterator2", "_step2", "handledCPCountPlusOne", "_iterator3", "_step3", "q", "qMinusT", "toUnicode", "toLowerCase", "toASCII", "punycode"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/punycode@2.3.1/node_modules/punycode/punycode.es6.js"], "sourcesContent": ["'use strict';\n\n/** Highest positive signed 32-bit float value */\nconst maxInt = 2147483647; // aka. 0x7FFFFFFF or 2^31-1\n\n/** Bootstring parameters */\nconst base = 36;\nconst tMin = 1;\nconst tMax = 26;\nconst skew = 38;\nconst damp = 700;\nconst initialBias = 72;\nconst initialN = 128; // 0x80\nconst delimiter = '-'; // '\\x2D'\n\n/** Regular expressions */\nconst regexPunycode = /^xn--/;\nconst regexNonASCII = /[^\\0-\\x7F]/; // Note: U+007F DEL is excluded too.\nconst regexSeparators = /[\\x2E\\u3002\\uFF0E\\uFF61]/g; // RFC 3490 separators\n\n/** Error messages */\nconst errors = {\n\t'overflow': 'Overflow: input needs wider integers to process',\n\t'not-basic': 'Illegal input >= 0x80 (not a basic code point)',\n\t'invalid-input': 'Invalid input'\n};\n\n/** Convenience shortcuts */\nconst baseMinusTMin = base - tMin;\nconst floor = Math.floor;\nconst stringFromCharCode = String.fromCharCode;\n\n/*--------------------------------------------------------------------------*/\n\n/**\n * A generic error utility function.\n * @private\n * @param {String} type The error type.\n * @returns {Error} Throws a `RangeError` with the applicable error message.\n */\nfunction error(type) {\n\tthrow new RangeError(errors[type]);\n}\n\n/**\n * A generic `Array#map` utility function.\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} callback The function that gets called for every array\n * item.\n * @returns {Array} A new array of values returned by the callback function.\n */\nfunction map(array, callback) {\n\tconst result = [];\n\tlet length = array.length;\n\twhile (length--) {\n\t\tresult[length] = callback(array[length]);\n\t}\n\treturn result;\n}\n\n/**\n * A simple `Array#map`-like wrapper to work with domain name strings or email\n * addresses.\n * @private\n * @param {String} domain The domain name or email address.\n * @param {Function} callback The function that gets called for every\n * character.\n * @returns {String} A new string of characters returned by the callback\n * function.\n */\nfunction mapDomain(domain, callback) {\n\tconst parts = domain.split('@');\n\tlet result = '';\n\tif (parts.length > 1) {\n\t\t// In email addresses, only the domain name should be punycoded. Leave\n\t\t// the local part (i.e. everything up to `@`) intact.\n\t\tresult = parts[0] + '@';\n\t\tdomain = parts[1];\n\t}\n\t// Avoid `split(regex)` for IE8 compatibility. See #17.\n\tdomain = domain.replace(regexSeparators, '\\x2E');\n\tconst labels = domain.split('.');\n\tconst encoded = map(labels, callback).join('.');\n\treturn result + encoded;\n}\n\n/**\n * Creates an array containing the numeric code points of each Unicode\n * character in the string. While JavaScript uses UCS-2 internally,\n * this function will convert a pair of surrogate halves (each of which\n * UCS-2 exposes as separate characters) into a single code point,\n * matching UTF-16.\n * @see `punycode.ucs2.encode`\n * @see <https://mathiasbynens.be/notes/javascript-encoding>\n * @memberOf punycode.ucs2\n * @name decode\n * @param {String} string The Unicode input string (UCS-2).\n * @returns {Array} The new array of code points.\n */\nfunction ucs2decode(string) {\n\tconst output = [];\n\tlet counter = 0;\n\tconst length = string.length;\n\twhile (counter < length) {\n\t\tconst value = string.charCodeAt(counter++);\n\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t// It's a high surrogate, and there is a next character.\n\t\t\tconst extra = string.charCodeAt(counter++);\n\t\t\tif ((extra & 0xFC00) == 0xDC00) { // Low surrogate.\n\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t} else {\n\t\t\t\t// It's an unmatched surrogate; only append this code unit, in case the\n\t\t\t\t// next code unit is the high surrogate of a surrogate pair.\n\t\t\t\toutput.push(value);\n\t\t\t\tcounter--;\n\t\t\t}\n\t\t} else {\n\t\t\toutput.push(value);\n\t\t}\n\t}\n\treturn output;\n}\n\n/**\n * Creates a string based on an array of numeric code points.\n * @see `punycode.ucs2.decode`\n * @memberOf punycode.ucs2\n * @name encode\n * @param {Array} codePoints The array of numeric code points.\n * @returns {String} The new Unicode string (UCS-2).\n */\nconst ucs2encode = codePoints => String.fromCodePoint(...codePoints);\n\n/**\n * Converts a basic code point into a digit/integer.\n * @see `digitToBasic()`\n * @private\n * @param {Number} codePoint The basic numeric code point value.\n * @returns {Number} The numeric value of a basic code point (for use in\n * representing integers) in the range `0` to `base - 1`, or `base` if\n * the code point does not represent a value.\n */\nconst basicToDigit = function(codePoint) {\n\tif (codePoint >= 0x30 && codePoint < 0x3A) {\n\t\treturn 26 + (codePoint - 0x30);\n\t}\n\tif (codePoint >= 0x41 && codePoint < 0x5B) {\n\t\treturn codePoint - 0x41;\n\t}\n\tif (codePoint >= 0x61 && codePoint < 0x7B) {\n\t\treturn codePoint - 0x61;\n\t}\n\treturn base;\n};\n\n/**\n * Converts a digit/integer into a basic code point.\n * @see `basicToDigit()`\n * @private\n * @param {Number} digit The numeric value of a basic code point.\n * @returns {Number} The basic code point whose value (when used for\n * representing integers) is `digit`, which needs to be in the range\n * `0` to `base - 1`. If `flag` is non-zero, the uppercase form is\n * used; else, the lowercase form is used. The behavior is undefined\n * if `flag` is non-zero and `digit` has no uppercase form.\n */\nconst digitToBasic = function(digit, flag) {\n\t//  0..25 map to ASCII a..z or A..Z\n\t// 26..35 map to ASCII 0..9\n\treturn digit + 22 + 75 * (digit < 26) - ((flag != 0) << 5);\n};\n\n/**\n * Bias adaptation function as per section 3.4 of RFC 3492.\n * https://tools.ietf.org/html/rfc3492#section-3.4\n * @private\n */\nconst adapt = function(delta, numPoints, firstTime) {\n\tlet k = 0;\n\tdelta = firstTime ? floor(delta / damp) : delta >> 1;\n\tdelta += floor(delta / numPoints);\n\tfor (/* no initialization */; delta > baseMinusTMin * tMax >> 1; k += base) {\n\t\tdelta = floor(delta / baseMinusTMin);\n\t}\n\treturn floor(k + (baseMinusTMin + 1) * delta / (delta + skew));\n};\n\n/**\n * Converts a Punycode string of ASCII-only symbols to a string of Unicode\n * symbols.\n * @memberOf punycode\n * @param {String} input The Punycode string of ASCII-only symbols.\n * @returns {String} The resulting string of Unicode symbols.\n */\nconst decode = function(input) {\n\t// Don't use UCS-2.\n\tconst output = [];\n\tconst inputLength = input.length;\n\tlet i = 0;\n\tlet n = initialN;\n\tlet bias = initialBias;\n\n\t// Handle the basic code points: let `basic` be the number of input code\n\t// points before the last delimiter, or `0` if there is none, then copy\n\t// the first basic code points to the output.\n\n\tlet basic = input.lastIndexOf(delimiter);\n\tif (basic < 0) {\n\t\tbasic = 0;\n\t}\n\n\tfor (let j = 0; j < basic; ++j) {\n\t\t// if it's not a basic code point\n\t\tif (input.charCodeAt(j) >= 0x80) {\n\t\t\terror('not-basic');\n\t\t}\n\t\toutput.push(input.charCodeAt(j));\n\t}\n\n\t// Main decoding loop: start just after the last delimiter if any basic code\n\t// points were copied; start at the beginning otherwise.\n\n\tfor (let index = basic > 0 ? basic + 1 : 0; index < inputLength; /* no final expression */) {\n\n\t\t// `index` is the index of the next character to be consumed.\n\t\t// Decode a generalized variable-length integer into `delta`,\n\t\t// which gets added to `i`. The overflow checking is easier\n\t\t// if we increase `i` as we go, then subtract off its starting\n\t\t// value at the end to obtain `delta`.\n\t\tconst oldi = i;\n\t\tfor (let w = 1, k = base; /* no condition */; k += base) {\n\n\t\t\tif (index >= inputLength) {\n\t\t\t\terror('invalid-input');\n\t\t\t}\n\n\t\t\tconst digit = basicToDigit(input.charCodeAt(index++));\n\n\t\t\tif (digit >= base) {\n\t\t\t\terror('invalid-input');\n\t\t\t}\n\t\t\tif (digit > floor((maxInt - i) / w)) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\n\t\t\ti += digit * w;\n\t\t\tconst t = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);\n\n\t\t\tif (digit < t) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tconst baseMinusT = base - t;\n\t\t\tif (w > floor(maxInt / baseMinusT)) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\n\t\t\tw *= baseMinusT;\n\n\t\t}\n\n\t\tconst out = output.length + 1;\n\t\tbias = adapt(i - oldi, out, oldi == 0);\n\n\t\t// `i` was supposed to wrap around from `out` to `0`,\n\t\t// incrementing `n` each time, so we'll fix that now:\n\t\tif (floor(i / out) > maxInt - n) {\n\t\t\terror('overflow');\n\t\t}\n\n\t\tn += floor(i / out);\n\t\ti %= out;\n\n\t\t// Insert `n` at position `i` of the output.\n\t\toutput.splice(i++, 0, n);\n\n\t}\n\n\treturn String.fromCodePoint(...output);\n};\n\n/**\n * Converts a string of Unicode symbols (e.g. a domain name label) to a\n * Punycode string of ASCII-only symbols.\n * @memberOf punycode\n * @param {String} input The string of Unicode symbols.\n * @returns {String} The resulting Punycode string of ASCII-only symbols.\n */\nconst encode = function(input) {\n\tconst output = [];\n\n\t// Convert the input in UCS-2 to an array of Unicode code points.\n\tinput = ucs2decode(input);\n\n\t// Cache the length.\n\tconst inputLength = input.length;\n\n\t// Initialize the state.\n\tlet n = initialN;\n\tlet delta = 0;\n\tlet bias = initialBias;\n\n\t// Handle the basic code points.\n\tfor (const currentValue of input) {\n\t\tif (currentValue < 0x80) {\n\t\t\toutput.push(stringFromCharCode(currentValue));\n\t\t}\n\t}\n\n\tconst basicLength = output.length;\n\tlet handledCPCount = basicLength;\n\n\t// `handledCPCount` is the number of code points that have been handled;\n\t// `basicLength` is the number of basic code points.\n\n\t// Finish the basic string with a delimiter unless it's empty.\n\tif (basicLength) {\n\t\toutput.push(delimiter);\n\t}\n\n\t// Main encoding loop:\n\twhile (handledCPCount < inputLength) {\n\n\t\t// All non-basic code points < n have been handled already. Find the next\n\t\t// larger one:\n\t\tlet m = maxInt;\n\t\tfor (const currentValue of input) {\n\t\t\tif (currentValue >= n && currentValue < m) {\n\t\t\t\tm = currentValue;\n\t\t\t}\n\t\t}\n\n\t\t// Increase `delta` enough to advance the decoder's <n,i> state to <m,0>,\n\t\t// but guard against overflow.\n\t\tconst handledCPCountPlusOne = handledCPCount + 1;\n\t\tif (m - n > floor((maxInt - delta) / handledCPCountPlusOne)) {\n\t\t\terror('overflow');\n\t\t}\n\n\t\tdelta += (m - n) * handledCPCountPlusOne;\n\t\tn = m;\n\n\t\tfor (const currentValue of input) {\n\t\t\tif (currentValue < n && ++delta > maxInt) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\t\t\tif (currentValue === n) {\n\t\t\t\t// Represent delta as a generalized variable-length integer.\n\t\t\t\tlet q = delta;\n\t\t\t\tfor (let k = base; /* no condition */; k += base) {\n\t\t\t\t\tconst t = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);\n\t\t\t\t\tif (q < t) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tconst qMinusT = q - t;\n\t\t\t\t\tconst baseMinusT = base - t;\n\t\t\t\t\toutput.push(\n\t\t\t\t\t\tstringFromCharCode(digitToBasic(t + qMinusT % baseMinusT, 0))\n\t\t\t\t\t);\n\t\t\t\t\tq = floor(qMinusT / baseMinusT);\n\t\t\t\t}\n\n\t\t\t\toutput.push(stringFromCharCode(digitToBasic(q, 0)));\n\t\t\t\tbias = adapt(delta, handledCPCountPlusOne, handledCPCount === basicLength);\n\t\t\t\tdelta = 0;\n\t\t\t\t++handledCPCount;\n\t\t\t}\n\t\t}\n\n\t\t++delta;\n\t\t++n;\n\n\t}\n\treturn output.join('');\n};\n\n/**\n * Converts a Punycode string representing a domain name or an email address\n * to Unicode. Only the Punycoded parts of the input will be converted, i.e.\n * it doesn't matter if you call it on a string that has already been\n * converted to Unicode.\n * @memberOf punycode\n * @param {String} input The Punycoded domain name or email address to\n * convert to Unicode.\n * @returns {String} The Unicode representation of the given Punycode\n * string.\n */\nconst toUnicode = function(input) {\n\treturn mapDomain(input, function(string) {\n\t\treturn regexPunycode.test(string)\n\t\t\t? decode(string.slice(4).toLowerCase())\n\t\t\t: string;\n\t});\n};\n\n/**\n * Converts a Unicode string representing a domain name or an email address to\n * Punycode. Only the non-ASCII parts of the domain name will be converted,\n * i.e. it doesn't matter if you call it with a domain that's already in\n * ASCII.\n * @memberOf punycode\n * @param {String} input The domain name or email address to convert, as a\n * Unicode string.\n * @returns {String} The Punycode representation of the given domain name or\n * email address.\n */\nconst toASCII = function(input) {\n\treturn mapDomain(input, function(string) {\n\t\treturn regexNonASCII.test(string)\n\t\t\t? 'xn--' + encode(string)\n\t\t\t: string;\n\t});\n};\n\n/*--------------------------------------------------------------------------*/\n\n/** Define the public API */\nconst punycode = {\n\t/**\n\t * A string representing the current Punycode.js version number.\n\t * @memberOf punycode\n\t * @type String\n\t */\n\t'version': '2.3.1',\n\t/**\n\t * An object of methods to convert from JavaScript's internal character\n\t * representation (UCS-2) to Unicode code points, and back.\n\t * @see <https://mathiasbynens.be/notes/javascript-encoding>\n\t * @memberOf punycode\n\t * @type Object\n\t */\n\t'ucs2': {\n\t\t'decode': ucs2decode,\n\t\t'encode': ucs2encode\n\t},\n\t'decode': decode,\n\t'encode': encode,\n\t'toASCII': toASCII,\n\t'toUnicode': toUnicode\n};\n\nexport { ucs2decode, ucs2encode, decode, encode, toASCII, toUnicode };\nexport default punycode;\n"], "mappings": "AAAA,YAAY;;AAEZ;AAAA,SAAAA,2BAAAC,CAAA,EAAAC,CAAA,QAAAC,CAAA,yBAAAC,MAAA,IAAAH,CAAA,CAAAG,MAAA,CAAAC,QAAA,KAAAJ,CAAA,qBAAAE,CAAA,QAAAG,KAAA,CAAAC,OAAA,CAAAN,CAAA,MAAAE,CAAA,GAAAK,2BAAA,CAAAP,CAAA,MAAAC,CAAA,IAAAD,CAAA,uBAAAA,CAAA,CAAAQ,MAAA,IAAAN,CAAA,KAAAF,CAAA,GAAAE,CAAA,OAAAO,EAAA,MAAAC,CAAA,YAAAA,EAAA,eAAAC,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAA,EAAA,WAAAH,EAAA,IAAAT,CAAA,CAAAQ,MAAA,KAAAK,IAAA,WAAAA,IAAA,MAAAC,KAAA,EAAAd,CAAA,CAAAS,EAAA,UAAAR,CAAA,WAAAA,EAAAD,CAAA,UAAAA,CAAA,KAAAe,CAAA,EAAAL,CAAA,gBAAAM,SAAA,iJAAAC,CAAA,EAAAC,CAAA,OAAAC,CAAA,gBAAAR,CAAA,WAAAA,EAAA,IAAAT,CAAA,GAAAA,CAAA,CAAAkB,IAAA,CAAApB,CAAA,MAAAY,CAAA,WAAAA,EAAA,QAAAZ,CAAA,GAAAE,CAAA,CAAAmB,IAAA,WAAAH,CAAA,GAAAlB,CAAA,CAAAa,IAAA,EAAAb,CAAA,KAAAC,CAAA,WAAAA,EAAAD,CAAA,IAAAmB,CAAA,OAAAF,CAAA,GAAAjB,CAAA,KAAAe,CAAA,WAAAA,EAAA,UAAAG,CAAA,YAAAhB,CAAA,CAAAoB,MAAA,IAAApB,CAAA,CAAAoB,MAAA,oBAAAH,CAAA,QAAAF,CAAA;AAAA,SAAAM,mBAAAvB,CAAA,WAAAwB,kBAAA,CAAAxB,CAAA,KAAAyB,gBAAA,CAAAzB,CAAA,KAAAO,2BAAA,CAAAP,CAAA,KAAA0B,kBAAA;AAAA,SAAAA,mBAAA,cAAAV,SAAA;AAAA,SAAAT,4BAAAP,CAAA,EAAAkB,CAAA,QAAAlB,CAAA,2BAAAA,CAAA,SAAA2B,iBAAA,CAAA3B,CAAA,EAAAkB,CAAA,OAAAhB,CAAA,MAAA0B,QAAA,CAAAR,IAAA,CAAApB,CAAA,EAAA6B,KAAA,6BAAA3B,CAAA,IAAAF,CAAA,CAAA8B,WAAA,KAAA5B,CAAA,GAAAF,CAAA,CAAA8B,WAAA,CAAAC,IAAA,aAAA7B,CAAA,cAAAA,CAAA,GAAAG,KAAA,CAAA2B,IAAA,CAAAhC,CAAA,oBAAAE,CAAA,+CAAA+B,IAAA,CAAA/B,CAAA,IAAAyB,iBAAA,CAAA3B,CAAA,EAAAkB,CAAA;AAAA,SAAAO,iBAAAzB,CAAA,8BAAAG,MAAA,YAAAH,CAAA,CAAAG,MAAA,CAAAC,QAAA,aAAAJ,CAAA,uBAAAK,KAAA,CAAA2B,IAAA,CAAAhC,CAAA;AAAA,SAAAwB,mBAAAxB,CAAA,QAAAK,KAAA,CAAAC,OAAA,CAAAN,CAAA,UAAA2B,iBAAA,CAAA3B,CAAA;AAAA,SAAA2B,kBAAA3B,CAAA,EAAAkB,CAAA,aAAAA,CAAA,IAAAA,CAAA,GAAAlB,CAAA,CAAAQ,MAAA,MAAAU,CAAA,GAAAlB,CAAA,CAAAQ,MAAA,YAAAP,CAAA,MAAAW,CAAA,GAAAP,KAAA,CAAAa,CAAA,GAAAjB,CAAA,GAAAiB,CAAA,EAAAjB,CAAA,IAAAW,CAAA,CAAAX,CAAA,IAAAD,CAAA,CAAAC,CAAA,UAAAW,CAAA;AACA,IAAMsB,MAAM,GAAG,UAAU,CAAC,CAAC;;AAE3B;AACA,IAAMC,IAAI,GAAG,EAAE;AACf,IAAMC,IAAI,GAAG,CAAC;AACd,IAAMC,IAAI,GAAG,EAAE;AACf,IAAMC,IAAI,GAAG,EAAE;AACf,IAAMC,IAAI,GAAG,GAAG;AAChB,IAAMC,WAAW,GAAG,EAAE;AACtB,IAAMC,QAAQ,GAAG,GAAG,CAAC,CAAC;AACtB,IAAMC,SAAS,GAAG,GAAG,CAAC,CAAC;;AAEvB;AACA,IAAMC,aAAa,GAAG,OAAO;AAC7B,IAAMC,aAAa,GAAG,YAAY,CAAC,CAAC;AACpC,IAAMC,eAAe,GAAG,2BAA2B,CAAC,CAAC;;AAErD;AACA,IAAMC,MAAM,GAAG;EACd,UAAU,EAAE,iDAAiD;EAC7D,WAAW,EAAE,gDAAgD;EAC7D,eAAe,EAAE;AAClB,CAAC;;AAED;AACA,IAAMC,aAAa,GAAGZ,IAAI,GAAGC,IAAI;AACjC,IAAMY,KAAK,GAAGC,IAAI,CAACD,KAAK;AACxB,IAAME,kBAAkB,GAAGC,MAAM,CAACC,YAAY;;AAE9C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,IAAI,EAAE;EACpB,MAAM,IAAIC,UAAU,CAACT,MAAM,CAACQ,IAAI,CAAC,CAAC;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,GAAGA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAC7B,IAAMC,MAAM,GAAG,EAAE;EACjB,IAAInD,MAAM,GAAGiD,KAAK,CAACjD,MAAM;EACzB,OAAOA,MAAM,EAAE,EAAE;IAChBmD,MAAM,CAACnD,MAAM,CAAC,GAAGkD,QAAQ,CAACD,KAAK,CAACjD,MAAM,CAAC,CAAC;EACzC;EACA,OAAOmD,MAAM;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,MAAM,EAAEH,QAAQ,EAAE;EACpC,IAAMI,KAAK,GAAGD,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC;EAC/B,IAAIJ,MAAM,GAAG,EAAE;EACf,IAAIG,KAAK,CAACtD,MAAM,GAAG,CAAC,EAAE;IACrB;IACA;IACAmD,MAAM,GAAGG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG;IACvBD,MAAM,GAAGC,KAAK,CAAC,CAAC,CAAC;EAClB;EACA;EACAD,MAAM,GAAGA,MAAM,CAACG,OAAO,CAACnB,eAAe,EAAE,MAAM,CAAC;EAChD,IAAMoB,MAAM,GAAGJ,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC;EAChC,IAAMG,OAAO,GAAGV,GAAG,CAACS,MAAM,EAAEP,QAAQ,CAAC,CAACS,IAAI,CAAC,GAAG,CAAC;EAC/C,OAAOR,MAAM,GAAGO,OAAO;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,UAAUA,CAACC,MAAM,EAAE;EAC3B,IAAMC,MAAM,GAAG,EAAE;EACjB,IAAIC,OAAO,GAAG,CAAC;EACf,IAAM/D,MAAM,GAAG6D,MAAM,CAAC7D,MAAM;EAC5B,OAAO+D,OAAO,GAAG/D,MAAM,EAAE;IACxB,IAAMM,KAAK,GAAGuD,MAAM,CAACG,UAAU,CAACD,OAAO,EAAE,CAAC;IAC1C,IAAIzD,KAAK,IAAI,MAAM,IAAIA,KAAK,IAAI,MAAM,IAAIyD,OAAO,GAAG/D,MAAM,EAAE;MAC3D;MACA,IAAMiE,KAAK,GAAGJ,MAAM,CAACG,UAAU,CAACD,OAAO,EAAE,CAAC;MAC1C,IAAI,CAACE,KAAK,GAAG,MAAM,KAAK,MAAM,EAAE;QAAE;QACjCH,MAAM,CAACI,IAAI,CAAC,CAAC,CAAC5D,KAAK,GAAG,KAAK,KAAK,EAAE,KAAK2D,KAAK,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC;MACjE,CAAC,MAAM;QACN;QACA;QACAH,MAAM,CAACI,IAAI,CAAC5D,KAAK,CAAC;QAClByD,OAAO,EAAE;MACV;IACD,CAAC,MAAM;MACND,MAAM,CAACI,IAAI,CAAC5D,KAAK,CAAC;IACnB;EACD;EACA,OAAOwD,MAAM;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMK,UAAU,GAAG,SAAbA,UAAUA,CAAGC,UAAU;EAAA,OAAIzB,MAAM,CAAC0B,aAAa,CAAAC,KAAA,CAApB3B,MAAM,EAAA5B,kBAAA,CAAkBqD,UAAU,EAAC;AAAA;;AAEpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMG,YAAY,GAAG,SAAfA,YAAYA,CAAYC,SAAS,EAAE;EACxC,IAAIA,SAAS,IAAI,IAAI,IAAIA,SAAS,GAAG,IAAI,EAAE;IAC1C,OAAO,EAAE,IAAIA,SAAS,GAAG,IAAI,CAAC;EAC/B;EACA,IAAIA,SAAS,IAAI,IAAI,IAAIA,SAAS,GAAG,IAAI,EAAE;IAC1C,OAAOA,SAAS,GAAG,IAAI;EACxB;EACA,IAAIA,SAAS,IAAI,IAAI,IAAIA,SAAS,GAAG,IAAI,EAAE;IAC1C,OAAOA,SAAS,GAAG,IAAI;EACxB;EACA,OAAO7C,IAAI;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAM8C,YAAY,GAAG,SAAfA,YAAYA,CAAYC,KAAK,EAAEC,IAAI,EAAE;EAC1C;EACA;EACA,OAAOD,KAAK,GAAG,EAAE,GAAG,EAAE,IAAIA,KAAK,GAAG,EAAE,CAAC,IAAI,CAACC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,IAAMC,KAAK,GAAG,SAARA,KAAKA,CAAYC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE;EACnD,IAAIC,CAAC,GAAG,CAAC;EACTH,KAAK,GAAGE,SAAS,GAAGvC,KAAK,CAACqC,KAAK,GAAG9C,IAAI,CAAC,GAAG8C,KAAK,IAAI,CAAC;EACpDA,KAAK,IAAIrC,KAAK,CAACqC,KAAK,GAAGC,SAAS,CAAC;EACjC,IAAK;EAAA,GAAyBD,KAAK,GAAGtC,aAAa,GAAGV,IAAI,IAAI,CAAC,EAAEmD,CAAC,IAAIrD,IAAI,EAAE;IAC3EkD,KAAK,GAAGrC,KAAK,CAACqC,KAAK,GAAGtC,aAAa,CAAC;EACrC;EACA,OAAOC,KAAK,CAACwC,CAAC,GAAG,CAACzC,aAAa,GAAG,CAAC,IAAIsC,KAAK,IAAIA,KAAK,GAAG/C,IAAI,CAAC,CAAC;AAC/D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMmD,MAAM,GAAG,SAATA,MAAMA,CAAYC,KAAK,EAAE;EAC9B;EACA,IAAMpB,MAAM,GAAG,EAAE;EACjB,IAAMqB,WAAW,GAAGD,KAAK,CAAClF,MAAM;EAChC,IAAIoF,CAAC,GAAG,CAAC;EACT,IAAIhF,CAAC,GAAG6B,QAAQ;EAChB,IAAIoD,IAAI,GAAGrD,WAAW;;EAEtB;EACA;EACA;;EAEA,IAAIsD,KAAK,GAAGJ,KAAK,CAACK,WAAW,CAACrD,SAAS,CAAC;EACxC,IAAIoD,KAAK,GAAG,CAAC,EAAE;IACdA,KAAK,GAAG,CAAC;EACV;EAEA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,EAAE,EAAEE,CAAC,EAAE;IAC/B;IACA,IAAIN,KAAK,CAAClB,UAAU,CAACwB,CAAC,CAAC,IAAI,IAAI,EAAE;MAChC3C,KAAK,CAAC,WAAW,CAAC;IACnB;IACAiB,MAAM,CAACI,IAAI,CAACgB,KAAK,CAAClB,UAAU,CAACwB,CAAC,CAAC,CAAC;EACjC;;EAEA;EACA;;EAEA,IAAiE;EAAA,CAA5D,IAAIC,KAAK,GAAGH,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,EAAEG,KAAK,GAAGN,WAAW,GAA6B;IAE3F;IACA;IACA;IACA;IACA;IACA,IAAMO,IAAI,GAAGN,CAAC;IACd,IAA0B;IAAA,CAArB,IAAIO,CAAC,GAAG,CAAC,EAAEX,CAAC,GAAGrD,IAAI,GAAsBqD,CAAC,IAAIrD,IAAI,EAAE;MAExD,IAAI8D,KAAK,IAAIN,WAAW,EAAE;QACzBtC,KAAK,CAAC,eAAe,CAAC;MACvB;MAEA,IAAM6B,KAAK,GAAGH,YAAY,CAACW,KAAK,CAAClB,UAAU,CAACyB,KAAK,EAAE,CAAC,CAAC;MAErD,IAAIf,KAAK,IAAI/C,IAAI,EAAE;QAClBkB,KAAK,CAAC,eAAe,CAAC;MACvB;MACA,IAAI6B,KAAK,GAAGlC,KAAK,CAAC,CAACd,MAAM,GAAG0D,CAAC,IAAIO,CAAC,CAAC,EAAE;QACpC9C,KAAK,CAAC,UAAU,CAAC;MAClB;MAEAuC,CAAC,IAAIV,KAAK,GAAGiB,CAAC;MACd,IAAMjG,CAAC,GAAGsF,CAAC,IAAIK,IAAI,GAAGzD,IAAI,GAAIoD,CAAC,IAAIK,IAAI,GAAGxD,IAAI,GAAGA,IAAI,GAAGmD,CAAC,GAAGK,IAAK;MAEjE,IAAIX,KAAK,GAAGhF,CAAC,EAAE;QACd;MACD;MAEA,IAAMkG,UAAU,GAAGjE,IAAI,GAAGjC,CAAC;MAC3B,IAAIiG,CAAC,GAAGnD,KAAK,CAACd,MAAM,GAAGkE,UAAU,CAAC,EAAE;QACnC/C,KAAK,CAAC,UAAU,CAAC;MAClB;MAEA8C,CAAC,IAAIC,UAAU;IAEhB;IAEA,IAAMC,GAAG,GAAG/B,MAAM,CAAC9D,MAAM,GAAG,CAAC;IAC7BqF,IAAI,GAAGT,KAAK,CAACQ,CAAC,GAAGM,IAAI,EAAEG,GAAG,EAAEH,IAAI,IAAI,CAAC,CAAC;;IAEtC;IACA;IACA,IAAIlD,KAAK,CAAC4C,CAAC,GAAGS,GAAG,CAAC,GAAGnE,MAAM,GAAGtB,CAAC,EAAE;MAChCyC,KAAK,CAAC,UAAU,CAAC;IAClB;IAEAzC,CAAC,IAAIoC,KAAK,CAAC4C,CAAC,GAAGS,GAAG,CAAC;IACnBT,CAAC,IAAIS,GAAG;;IAER;IACA/B,MAAM,CAACgC,MAAM,CAACV,CAAC,EAAE,EAAE,CAAC,EAAEhF,CAAC,CAAC;EAEzB;EAEA,OAAOuC,MAAM,CAAC0B,aAAa,CAAAC,KAAA,CAApB3B,MAAM,EAAkBmB,MAAM,CAAC;AACvC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMiC,MAAM,GAAG,SAATA,MAAMA,CAAYb,KAAK,EAAE;EAC9B,IAAMpB,MAAM,GAAG,EAAE;;EAEjB;EACAoB,KAAK,GAAGtB,UAAU,CAACsB,KAAK,CAAC;;EAEzB;EACA,IAAMC,WAAW,GAAGD,KAAK,CAAClF,MAAM;;EAEhC;EACA,IAAII,CAAC,GAAG6B,QAAQ;EAChB,IAAI4C,KAAK,GAAG,CAAC;EACb,IAAIQ,IAAI,GAAGrD,WAAW;;EAEtB;EAAA,IAAAgE,SAAA,GAAAzG,0BAAA,CAC2B2F,KAAK;IAAAe,KAAA;EAAA;IAAhC,KAAAD,SAAA,CAAA7F,CAAA,MAAA8F,KAAA,GAAAD,SAAA,CAAA5F,CAAA,IAAAC,IAAA,GAAkC;MAAA,IAAvB6F,cAAY,GAAAD,KAAA,CAAA3F,KAAA;MACtB,IAAI4F,cAAY,GAAG,IAAI,EAAE;QACxBpC,MAAM,CAACI,IAAI,CAACxB,kBAAkB,CAACwD,cAAY,CAAC,CAAC;MAC9C;IACD;EAAC,SAAAC,GAAA;IAAAH,SAAA,CAAAvG,CAAA,CAAA0G,GAAA;EAAA;IAAAH,SAAA,CAAAzF,CAAA;EAAA;EAED,IAAM6F,WAAW,GAAGtC,MAAM,CAAC9D,MAAM;EACjC,IAAIqG,cAAc,GAAGD,WAAW;;EAEhC;EACA;;EAEA;EACA,IAAIA,WAAW,EAAE;IAChBtC,MAAM,CAACI,IAAI,CAAChC,SAAS,CAAC;EACvB;;EAEA;EACA,OAAOmE,cAAc,GAAGlB,WAAW,EAAE;IAEpC;IACA;IACA,IAAImB,CAAC,GAAG5E,MAAM;IAAC,IAAA6E,UAAA,GAAAhH,0BAAA,CACY2F,KAAK;MAAAsB,MAAA;IAAA;MAAhC,KAAAD,UAAA,CAAApG,CAAA,MAAAqG,MAAA,GAAAD,UAAA,CAAAnG,CAAA,IAAAC,IAAA,GAAkC;QAAA,IAAvB6F,YAAY,GAAAM,MAAA,CAAAlG,KAAA;QACtB,IAAI4F,YAAY,IAAI9F,CAAC,IAAI8F,YAAY,GAAGI,CAAC,EAAE;UAC1CA,CAAC,GAAGJ,YAAY;QACjB;MACD;;MAEA;MACA;IAAA,SAAAC,GAAA;MAAAI,UAAA,CAAA9G,CAAA,CAAA0G,GAAA;IAAA;MAAAI,UAAA,CAAAhG,CAAA;IAAA;IACA,IAAMkG,qBAAqB,GAAGJ,cAAc,GAAG,CAAC;IAChD,IAAIC,CAAC,GAAGlG,CAAC,GAAGoC,KAAK,CAAC,CAACd,MAAM,GAAGmD,KAAK,IAAI4B,qBAAqB,CAAC,EAAE;MAC5D5D,KAAK,CAAC,UAAU,CAAC;IAClB;IAEAgC,KAAK,IAAI,CAACyB,CAAC,GAAGlG,CAAC,IAAIqG,qBAAqB;IACxCrG,CAAC,GAAGkG,CAAC;IAAC,IAAAI,UAAA,GAAAnH,0BAAA,CAEqB2F,KAAK;MAAAyB,MAAA;IAAA;MAAhC,KAAAD,UAAA,CAAAvG,CAAA,MAAAwG,MAAA,GAAAD,UAAA,CAAAtG,CAAA,IAAAC,IAAA,GAAkC;QAAA,IAAvB6F,aAAY,GAAAS,MAAA,CAAArG,KAAA;QACtB,IAAI4F,aAAY,GAAG9F,CAAC,IAAI,EAAEyE,KAAK,GAAGnD,MAAM,EAAE;UACzCmB,KAAK,CAAC,UAAU,CAAC;QAClB;QACA,IAAIqD,aAAY,KAAK9F,CAAC,EAAE;UACvB;UACA,IAAIwG,CAAC,GAAG/B,KAAK;UACb,IAAmB;UAAA,CAAd,IAAIG,CAAC,GAAGrD,IAAI,GAAsBqD,CAAC,IAAIrD,IAAI,EAAE;YACjD,IAAMjC,CAAC,GAAGsF,CAAC,IAAIK,IAAI,GAAGzD,IAAI,GAAIoD,CAAC,IAAIK,IAAI,GAAGxD,IAAI,GAAGA,IAAI,GAAGmD,CAAC,GAAGK,IAAK;YACjE,IAAIuB,CAAC,GAAGlH,CAAC,EAAE;cACV;YACD;YACA,IAAMmH,OAAO,GAAGD,CAAC,GAAGlH,CAAC;YACrB,IAAMkG,UAAU,GAAGjE,IAAI,GAAGjC,CAAC;YAC3BoE,MAAM,CAACI,IAAI,CACVxB,kBAAkB,CAAC+B,YAAY,CAAC/E,CAAC,GAAGmH,OAAO,GAAGjB,UAAU,EAAE,CAAC,CAAC,CAC7D,CAAC;YACDgB,CAAC,GAAGpE,KAAK,CAACqE,OAAO,GAAGjB,UAAU,CAAC;UAChC;UAEA9B,MAAM,CAACI,IAAI,CAACxB,kBAAkB,CAAC+B,YAAY,CAACmC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACnDvB,IAAI,GAAGT,KAAK,CAACC,KAAK,EAAE4B,qBAAqB,EAAEJ,cAAc,KAAKD,WAAW,CAAC;UAC1EvB,KAAK,GAAG,CAAC;UACT,EAAEwB,cAAc;QACjB;MACD;IAAC,SAAAF,GAAA;MAAAO,UAAA,CAAAjH,CAAA,CAAA0G,GAAA;IAAA;MAAAO,UAAA,CAAAnG,CAAA;IAAA;IAED,EAAEsE,KAAK;IACP,EAAEzE,CAAC;EAEJ;EACA,OAAO0D,MAAM,CAACH,IAAI,CAAC,EAAE,CAAC;AACvB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMmD,SAAS,GAAG,SAAZA,SAASA,CAAY5B,KAAK,EAAE;EACjC,OAAO9B,SAAS,CAAC8B,KAAK,EAAE,UAASrB,MAAM,EAAE;IACxC,OAAO1B,aAAa,CAACV,IAAI,CAACoC,MAAM,CAAC,GAC9BoB,MAAM,CAACpB,MAAM,CAACxC,KAAK,CAAC,CAAC,CAAC,CAAC0F,WAAW,CAAC,CAAC,CAAC,GACrClD,MAAM;EACV,CAAC,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMmD,OAAO,GAAG,SAAVA,OAAOA,CAAY9B,KAAK,EAAE;EAC/B,OAAO9B,SAAS,CAAC8B,KAAK,EAAE,UAASrB,MAAM,EAAE;IACxC,OAAOzB,aAAa,CAACX,IAAI,CAACoC,MAAM,CAAC,GAC9B,MAAM,GAAGkC,MAAM,CAAClC,MAAM,CAAC,GACvBA,MAAM;EACV,CAAC,CAAC;AACH,CAAC;;AAED;;AAEA;AACA,IAAMoD,QAAQ,GAAG;EAChB;AACD;AACA;AACA;AACA;EACC,SAAS,EAAE,OAAO;EAClB;AACD;AACA;AACA;AACA;AACA;AACA;EACC,MAAM,EAAE;IACP,QAAQ,EAAErD,UAAU;IACpB,QAAQ,EAAEO;EACX,CAAC;EACD,QAAQ,EAAEc,MAAM;EAChB,QAAQ,EAAEc,MAAM;EAChB,SAAS,EAAEiB,OAAO;EAClB,WAAW,EAAEF;AACd,CAAC;AAED,SAASlD,UAAU,EAAEO,UAAU,EAAEc,MAAM,EAAEc,MAAM,EAAEiB,OAAO,EAAEF,SAAS;AACnE,eAAeG,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}