{"ast": null, "code": "\"use strict\";\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nvar traits = require(\"../traits.js\");\nvar _require = require(\"../doc-utils.js\"),\n  isContent = _require.isContent;\nvar _require2 = require(\"../errors.js\"),\n  throwRawTagShouldBeOnlyTextInParagraph = _require2.throwRawTagShouldBeOnlyTextInParagraph,\n  getInvalidRawXMLValueException = _require2.getInvalidRawXMLValueException;\nvar moduleName = \"rawxml\";\nvar wrapper = require(\"../module-wrapper.js\");\nfunction getInner(_ref) {\n  var part = _ref.part,\n    left = _ref.left,\n    right = _ref.right,\n    postparsed = _ref.postparsed,\n    index = _ref.index;\n  var paragraphParts = postparsed.slice(left + 1, right);\n  paragraphParts.forEach(function (p, i) {\n    if (i === index - left - 1) {\n      return;\n    }\n    if (isContent(p)) {\n      throwRawTagShouldBeOnlyTextInParagraph({\n        paragraphParts: paragraphParts,\n        part: part\n      });\n    }\n  });\n  return part;\n}\nvar RawXmlModule = /*#__PURE__*/function () {\n  function RawXmlModule() {\n    _classCallCheck(this, RawXmlModule);\n    this.name = \"RawXmlModule\";\n    this.prefix = \"@\";\n  }\n  return _createClass(RawXmlModule, [{\n    key: \"optionsTransformer\",\n    value: function optionsTransformer(options, docxtemplater) {\n      this.fileTypeConfig = docxtemplater.fileTypeConfig;\n      return options;\n    }\n  }, {\n    key: \"matchers\",\n    value: function matchers() {\n      return [[this.prefix, moduleName]];\n    }\n  }, {\n    key: \"postparse\",\n    value: function postparse(postparsed) {\n      return traits.expandToOne(postparsed, {\n        moduleName: moduleName,\n        getInner: getInner,\n        expandTo: this.fileTypeConfig.tagRawXml,\n        error: {\n          message: \"Raw tag not in paragraph\",\n          id: \"raw_tag_outerxml_invalid\",\n          explanation: function explanation(part) {\n            return \"The tag \\\"\".concat(part.value, \"\\\" is not inside a paragraph, putting raw tags inside an inline loop is disallowed.\");\n          }\n        }\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render(part, options) {\n      if (part.module !== moduleName) {\n        return null;\n      }\n      var value;\n      var errors = [];\n      try {\n        value = options.scopeManager.getValue(part.value, {\n          part: part\n        });\n        if (value == null) {\n          value = options.nullGetter(part);\n        }\n      } catch (e) {\n        errors.push(e);\n        return {\n          errors: errors\n        };\n      }\n      value = value ? value : \"\";\n      if (typeof value === \"string\") {\n        return {\n          value: value\n        };\n      }\n      return {\n        errors: [getInvalidRawXMLValueException({\n          tag: part.value,\n          value: value,\n          offset: part.offset\n        })]\n      };\n    }\n  }]);\n}();\nmodule.exports = function () {\n  return wrapper(new RawXmlModule());\n};", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_classCallCheck", "a", "n", "TypeError", "_defineProperties", "e", "r", "t", "length", "enumerable", "configurable", "writable", "Object", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "i", "_toPrimitive", "toPrimitive", "call", "String", "Number", "traits", "require", "_require", "<PERSON><PERSON><PERSON><PERSON>", "_require2", "throwRawTagShouldBeOnlyTextInParagraph", "getInvalidRawXMLValueException", "moduleName", "wrapper", "getInner", "_ref", "part", "left", "right", "postparsed", "index", "paragraphParts", "slice", "for<PERSON>ach", "p", "RawXmlModule", "name", "prefix", "value", "optionsTransformer", "options", "docxtemplater", "fileTypeConfig", "matchers", "postparse", "expandToOne", "expandTo", "tagRawXml", "error", "message", "id", "explanation", "concat", "render", "module", "errors", "scopeManager", "getValue", "nullGetter", "push", "tag", "offset", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/docxtemplater@3.52.0/node_modules/docxtemplater/js/modules/rawxml.js"], "sourcesContent": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nvar traits = require(\"../traits.js\");\nvar _require = require(\"../doc-utils.js\"),\n  isContent = _require.isContent;\nvar _require2 = require(\"../errors.js\"),\n  throwRawTagShouldBeOnlyTextInParagraph = _require2.throwRawTagShouldBeOnlyTextInParagraph,\n  getInvalidRawXMLValueException = _require2.getInvalidRawXMLValueException;\nvar moduleName = \"rawxml\";\nvar wrapper = require(\"../module-wrapper.js\");\nfunction getInner(_ref) {\n  var part = _ref.part,\n    left = _ref.left,\n    right = _ref.right,\n    postparsed = _ref.postparsed,\n    index = _ref.index;\n  var paragraphParts = postparsed.slice(left + 1, right);\n  paragraphParts.forEach(function (p, i) {\n    if (i === index - left - 1) {\n      return;\n    }\n    if (isContent(p)) {\n      throwRawTagShouldBeOnlyTextInParagraph({\n        paragraphParts: paragraphParts,\n        part: part\n      });\n    }\n  });\n  return part;\n}\nvar RawXmlModule = /*#__PURE__*/function () {\n  function RawXmlModule() {\n    _classCallCheck(this, RawXmlModule);\n    this.name = \"RawXmlModule\";\n    this.prefix = \"@\";\n  }\n  return _createClass(RawXmlModule, [{\n    key: \"optionsTransformer\",\n    value: function optionsTransformer(options, docxtemplater) {\n      this.fileTypeConfig = docxtemplater.fileTypeConfig;\n      return options;\n    }\n  }, {\n    key: \"matchers\",\n    value: function matchers() {\n      return [[this.prefix, moduleName]];\n    }\n  }, {\n    key: \"postparse\",\n    value: function postparse(postparsed) {\n      return traits.expandToOne(postparsed, {\n        moduleName: moduleName,\n        getInner: getInner,\n        expandTo: this.fileTypeConfig.tagRawXml,\n        error: {\n          message: \"Raw tag not in paragraph\",\n          id: \"raw_tag_outerxml_invalid\",\n          explanation: function explanation(part) {\n            return \"The tag \\\"\".concat(part.value, \"\\\" is not inside a paragraph, putting raw tags inside an inline loop is disallowed.\");\n          }\n        }\n      });\n    }\n  }, {\n    key: \"render\",\n    value: function render(part, options) {\n      if (part.module !== moduleName) {\n        return null;\n      }\n      var value;\n      var errors = [];\n      try {\n        value = options.scopeManager.getValue(part.value, {\n          part: part\n        });\n        if (value == null) {\n          value = options.nullGetter(part);\n        }\n      } catch (e) {\n        errors.push(e);\n        return {\n          errors: errors\n        };\n      }\n      value = value ? value : \"\";\n      if (typeof value === \"string\") {\n        return {\n          value: value\n        };\n      }\n      return {\n        errors: [getInvalidRawXMLValueException({\n          tag: part.value,\n          value: value,\n          offset: part.offset\n        })]\n      };\n    }\n  }]);\n}();\nmodule.exports = function () {\n  return wrapper(new RawXmlModule());\n};"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,EAAED,CAAC,YAAYC,CAAC,CAAC,EAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;AAAE;AAClH,SAASC,iBAAiBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIZ,CAAC,GAAGW,CAAC,CAACC,CAAC,CAAC;IAAEZ,CAAC,CAACc,UAAU,GAAGd,CAAC,CAACc,UAAU,IAAI,CAAC,CAAC,EAAEd,CAAC,CAACe,YAAY,GAAG,CAAC,CAAC,EAAE,OAAO,IAAIf,CAAC,KAAKA,CAAC,CAACgB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAEC,MAAM,CAACC,cAAc,CAACR,CAAC,EAAES,cAAc,CAACnB,CAAC,CAACoB,GAAG,CAAC,EAAEpB,CAAC,CAAC;EAAE;AAAE;AACvO,SAASqB,YAAYA,CAACX,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAOD,CAAC,IAAIF,iBAAiB,CAACC,CAAC,CAACN,SAAS,EAAEO,CAAC,CAAC,EAAEC,CAAC,IAAIH,iBAAiB,CAACC,CAAC,EAAEE,CAAC,CAAC,EAAEK,MAAM,CAACC,cAAc,CAACR,CAAC,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,EAAEN,CAAC;AAAE;AAC1K,SAASS,cAAcA,CAACP,CAAC,EAAE;EAAE,IAAIU,CAAC,GAAGC,YAAY,CAACX,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAIb,OAAO,CAACuB,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASC,YAAYA,CAACX,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIZ,OAAO,CAACa,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACX,MAAM,CAACuB,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKd,CAAC,EAAE;IAAE,IAAIY,CAAC,GAAGZ,CAAC,CAACe,IAAI,CAACb,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIZ,OAAO,CAACuB,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAId,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKG,CAAC,GAAGe,MAAM,GAAGC,MAAM,EAAEf,CAAC,CAAC;AAAE;AAC3T,IAAIgB,MAAM,GAAGC,OAAO,CAAC,cAAc,CAAC;AACpC,IAAIC,QAAQ,GAAGD,OAAO,CAAC,iBAAiB,CAAC;EACvCE,SAAS,GAAGD,QAAQ,CAACC,SAAS;AAChC,IAAIC,SAAS,GAAGH,OAAO,CAAC,cAAc,CAAC;EACrCI,sCAAsC,GAAGD,SAAS,CAACC,sCAAsC;EACzFC,8BAA8B,GAAGF,SAAS,CAACE,8BAA8B;AAC3E,IAAIC,UAAU,GAAG,QAAQ;AACzB,IAAIC,OAAO,GAAGP,OAAO,CAAC,sBAAsB,CAAC;AAC7C,SAASQ,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAIC,IAAI,GAAGD,IAAI,CAACC,IAAI;IAClBC,IAAI,GAAGF,IAAI,CAACE,IAAI;IAChBC,KAAK,GAAGH,IAAI,CAACG,KAAK;IAClBC,UAAU,GAAGJ,IAAI,CAACI,UAAU;IAC5BC,KAAK,GAAGL,IAAI,CAACK,KAAK;EACpB,IAAIC,cAAc,GAAGF,UAAU,CAACG,KAAK,CAACL,IAAI,GAAG,CAAC,EAAEC,KAAK,CAAC;EACtDG,cAAc,CAACE,OAAO,CAAC,UAAUC,CAAC,EAAEzB,CAAC,EAAE;IACrC,IAAIA,CAAC,KAAKqB,KAAK,GAAGH,IAAI,GAAG,CAAC,EAAE;MAC1B;IACF;IACA,IAAIT,SAAS,CAACgB,CAAC,CAAC,EAAE;MAChBd,sCAAsC,CAAC;QACrCW,cAAc,EAAEA,cAAc;QAC9BL,IAAI,EAAEA;MACR,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAOA,IAAI;AACb;AACA,IAAIS,YAAY,GAAG,aAAa,YAAY;EAC1C,SAASA,YAAYA,CAAA,EAAG;IACtB3C,eAAe,CAAC,IAAI,EAAE2C,YAAY,CAAC;IACnC,IAAI,CAACC,IAAI,GAAG,cAAc;IAC1B,IAAI,CAACC,MAAM,GAAG,GAAG;EACnB;EACA,OAAO7B,YAAY,CAAC2B,YAAY,EAAE,CAAC;IACjC5B,GAAG,EAAE,oBAAoB;IACzB+B,KAAK,EAAE,SAASC,kBAAkBA,CAACC,OAAO,EAAEC,aAAa,EAAE;MACzD,IAAI,CAACC,cAAc,GAAGD,aAAa,CAACC,cAAc;MAClD,OAAOF,OAAO;IAChB;EACF,CAAC,EAAE;IACDjC,GAAG,EAAE,UAAU;IACf+B,KAAK,EAAE,SAASK,QAAQA,CAAA,EAAG;MACzB,OAAO,CAAC,CAAC,IAAI,CAACN,MAAM,EAAEf,UAAU,CAAC,CAAC;IACpC;EACF,CAAC,EAAE;IACDf,GAAG,EAAE,WAAW;IAChB+B,KAAK,EAAE,SAASM,SAASA,CAACf,UAAU,EAAE;MACpC,OAAOd,MAAM,CAAC8B,WAAW,CAAChB,UAAU,EAAE;QACpCP,UAAU,EAAEA,UAAU;QACtBE,QAAQ,EAAEA,QAAQ;QAClBsB,QAAQ,EAAE,IAAI,CAACJ,cAAc,CAACK,SAAS;QACvCC,KAAK,EAAE;UACLC,OAAO,EAAE,0BAA0B;UACnCC,EAAE,EAAE,0BAA0B;UAC9BC,WAAW,EAAE,SAASA,WAAWA,CAACzB,IAAI,EAAE;YACtC,OAAO,YAAY,CAAC0B,MAAM,CAAC1B,IAAI,CAACY,KAAK,EAAE,qFAAqF,CAAC;UAC/H;QACF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACD/B,GAAG,EAAE,QAAQ;IACb+B,KAAK,EAAE,SAASe,MAAMA,CAAC3B,IAAI,EAAEc,OAAO,EAAE;MACpC,IAAId,IAAI,CAAC4B,MAAM,KAAKhC,UAAU,EAAE;QAC9B,OAAO,IAAI;MACb;MACA,IAAIgB,KAAK;MACT,IAAIiB,MAAM,GAAG,EAAE;MACf,IAAI;QACFjB,KAAK,GAAGE,OAAO,CAACgB,YAAY,CAACC,QAAQ,CAAC/B,IAAI,CAACY,KAAK,EAAE;UAChDZ,IAAI,EAAEA;QACR,CAAC,CAAC;QACF,IAAIY,KAAK,IAAI,IAAI,EAAE;UACjBA,KAAK,GAAGE,OAAO,CAACkB,UAAU,CAAChC,IAAI,CAAC;QAClC;MACF,CAAC,CAAC,OAAO7B,CAAC,EAAE;QACV0D,MAAM,CAACI,IAAI,CAAC9D,CAAC,CAAC;QACd,OAAO;UACL0D,MAAM,EAAEA;QACV,CAAC;MACH;MACAjB,KAAK,GAAGA,KAAK,GAAGA,KAAK,GAAG,EAAE;MAC1B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO;UACLA,KAAK,EAAEA;QACT,CAAC;MACH;MACA,OAAO;QACLiB,MAAM,EAAE,CAAClC,8BAA8B,CAAC;UACtCuC,GAAG,EAAElC,IAAI,CAACY,KAAK;UACfA,KAAK,EAAEA,KAAK;UACZuB,MAAM,EAAEnC,IAAI,CAACmC;QACf,CAAC,CAAC;MACJ,CAAC;IACH;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACHP,MAAM,CAACQ,OAAO,GAAG,YAAY;EAC3B,OAAOvC,OAAO,CAAC,IAAIY,YAAY,CAAC,CAAC,CAAC;AACpC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}