{"ast": null, "code": "'use strict';\n\nmodule.exports = function format(url) {\n  var result = '';\n  result += url.protocol || '';\n  result += url.slashes ? '//' : '';\n  result += url.auth ? url.auth + '@' : '';\n  if (url.hostname && url.hostname.indexOf(':') !== -1) {\n    // ipv6 address\n    result += '[' + url.hostname + ']';\n  } else {\n    result += url.hostname || '';\n  }\n  result += url.port ? ':' + url.port : '';\n  result += url.pathname || '';\n  result += url.search || '';\n  result += url.hash || '';\n  return result;\n};", "map": {"version": 3, "names": ["module", "exports", "format", "url", "result", "protocol", "slashes", "auth", "hostname", "indexOf", "port", "pathname", "search", "hash"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/mdurl@1.0.1/node_modules/mdurl/format.js"], "sourcesContent": ["\n'use strict';\n\n\nmodule.exports = function format(url) {\n  var result = '';\n\n  result += url.protocol || '';\n  result += url.slashes ? '//' : '';\n  result += url.auth ? url.auth + '@' : '';\n\n  if (url.hostname && url.hostname.indexOf(':') !== -1) {\n    // ipv6 address\n    result += '[' + url.hostname + ']';\n  } else {\n    result += url.hostname || '';\n  }\n\n  result += url.port ? ':' + url.port : '';\n  result += url.pathname || '';\n  result += url.search || '';\n  result += url.hash || '';\n\n  return result;\n};\n"], "mappings": "AACA,YAAY;;AAGZA,MAAM,CAACC,OAAO,GAAG,SAASC,MAAMA,CAACC,GAAG,EAAE;EACpC,IAAIC,MAAM,GAAG,EAAE;EAEfA,MAAM,IAAID,GAAG,CAACE,QAAQ,IAAI,EAAE;EAC5BD,MAAM,IAAID,GAAG,CAACG,OAAO,GAAG,IAAI,GAAG,EAAE;EACjCF,MAAM,IAAID,GAAG,CAACI,IAAI,GAAGJ,GAAG,CAACI,IAAI,GAAG,GAAG,GAAG,EAAE;EAExC,IAAIJ,GAAG,CAACK,QAAQ,IAAIL,GAAG,CAACK,QAAQ,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IACpD;IACAL,MAAM,IAAI,GAAG,GAAGD,GAAG,CAACK,QAAQ,GAAG,GAAG;EACpC,CAAC,MAAM;IACLJ,MAAM,IAAID,GAAG,CAACK,QAAQ,IAAI,EAAE;EAC9B;EAEAJ,MAAM,IAAID,GAAG,CAACO,IAAI,GAAG,GAAG,GAAGP,GAAG,CAACO,IAAI,GAAG,EAAE;EACxCN,MAAM,IAAID,GAAG,CAACQ,QAAQ,IAAI,EAAE;EAC5BP,MAAM,IAAID,GAAG,CAACS,MAAM,IAAI,EAAE;EAC1BR,MAAM,IAAID,GAAG,CAACU,IAAI,IAAI,EAAE;EAExB,OAAOT,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}