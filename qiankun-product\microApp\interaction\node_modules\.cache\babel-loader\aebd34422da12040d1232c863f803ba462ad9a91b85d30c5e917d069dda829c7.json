{"ast": null, "code": "function _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nimport { defineComponent as S, openBlock as y, createElementBlock as x, withDirectives as H, createElementVNode as w, normalizeStyle as b, vShow as W, createCommentVNode as O, normalizeClass as I, toDisplayString as X } from \"vue\";\nvar Y = {};\nY.getData = function (t) {\n  return new Promise(function (e, i) {\n    var s = {};\n    L(t).then(function (r) {\n      s.arrayBuffer = r;\n      try {\n        s.orientation = N(r);\n      } catch (_unused) {\n        s.orientation = -1;\n      }\n      e(s);\n    }).catch(function (r) {\n      i(r);\n    });\n  });\n};\nfunction L(t) {\n  var e = null;\n  return new Promise(function (i, s) {\n    if (t.src) {\n      if (/^data\\:/i.test(t.src)) e = k(t.src), i(e);else if (/^blob\\:/i.test(t.src)) {\n        var r = new FileReader();\n        r.onload = function (h) {\n          e = h.target.result, i(e);\n        }, E(t.src, function (h) {\n          r.readAsArrayBuffer(h);\n        });\n      } else {\n        var o = new XMLHttpRequest();\n        o.onload = function () {\n          if (this.status == 200 || this.status === 0) e = o.response, i(e);else throw \"Could not load image\";\n          o = null;\n        }, o.open(\"GET\", t.src, !0), o.responseType = \"arraybuffer\", o.send(null);\n      }\n    } else s(\"img error\");\n  });\n}\nfunction E(t, e) {\n  var i = new XMLHttpRequest();\n  i.open(\"GET\", t, !0), i.responseType = \"blob\", i.onload = function (s) {\n    (this.status == 200 || this.status === 0) && e(this.response);\n  }, i.send();\n}\nfunction k(t, e) {\n  e = e || t.match(/^data\\:([^\\;]+)\\;base64,/mi)[1] || \"\", t = t.replace(/^data\\:([^\\;]+)\\;base64,/gmi, \"\");\n  for (var i = atob(t), s = i.length % 2 == 0 ? i.length : i.length + 1, r = new ArrayBuffer(s), o = new Uint16Array(r), h = 0; h < s; h++) o[h] = i.charCodeAt(h);\n  return r;\n}\nfunction T(t, e, i) {\n  var s = \"\",\n    r;\n  for (r = e, i += e; r < i; r++) s += String.fromCharCode(t.getUint8(r));\n  return s;\n}\nfunction N(t) {\n  var e = new DataView(t),\n    i = e.byteLength,\n    s,\n    r,\n    o,\n    h,\n    a,\n    n,\n    c,\n    l,\n    f,\n    p;\n  if (e.getUint8(0) === 255 && e.getUint8(1) === 216) for (f = 2; f < i;) {\n    if (e.getUint8(f) === 255 && e.getUint8(f + 1) === 225) {\n      c = f;\n      break;\n    }\n    f++;\n  }\n  if (c && (r = c + 4, o = c + 10, T(e, r, 4) === \"Exif\" && (n = e.getUint16(o), a = n === 18761, (a || n === 19789) && e.getUint16(o + 2, a) === 42 && (h = e.getUint32(o + 4, a), h >= 8 && (l = o + h)))), l) {\n    for (i = e.getUint16(l, a), p = 0; p < i; p++) if (f = l + p * 12 + 2, e.getUint16(f, a) === 274) {\n      f += 8, s = e.getUint16(f, a);\n      break;\n    }\n  }\n  return s;\n}\nvar $ = function $(t, e) {\n    var i = t.__vccOpts || t;\n    var _iterator = _createForOfIteratorHelper(e),\n      _step;\n    try {\n      for (_iterator.s(); !(_step = _iterator.n()).done;) {\n        var _step$value = _slicedToArray(_step.value, 2),\n          s = _step$value[0],\n          r = _step$value[1];\n        i[s] = r;\n      }\n    } catch (err) {\n      _iterator.e(err);\n    } finally {\n      _iterator.f();\n    }\n    return i;\n  },\n  z = S({\n    data: function data() {\n      return {\n        // 容器高宽\n        w: 0,\n        h: 0,\n        // 图片缩放比例\n        scale: 1,\n        // 图片偏移x轴\n        x: 0,\n        // 图片偏移y轴\n        y: 0,\n        // 图片加载\n        loading: !0,\n        // 图片真实宽度\n        trueWidth: 0,\n        // 图片真实高度\n        trueHeight: 0,\n        move: !0,\n        // 移动的x\n        moveX: 0,\n        // 移动的y\n        moveY: 0,\n        // 开启截图\n        crop: !1,\n        // 正在截图\n        cropping: !1,\n        // 裁剪框大小\n        cropW: 0,\n        cropH: 0,\n        cropOldW: 0,\n        cropOldH: 0,\n        // 判断是否能够改变\n        canChangeX: !1,\n        canChangeY: !1,\n        // 改变的基准点\n        changeCropTypeX: 1,\n        changeCropTypeY: 1,\n        // 裁剪框的坐标轴\n        cropX: 0,\n        cropY: 0,\n        cropChangeX: 0,\n        cropChangeY: 0,\n        cropOffsertX: 0,\n        cropOffsertY: 0,\n        // 支持的滚动事件\n        support: \"\",\n        // 移动端手指缩放\n        touches: [],\n        touchNow: !1,\n        // 图片旋转\n        rotate: 0,\n        isIos: !1,\n        orientation: 0,\n        imgs: \"\",\n        // 图片缩放系数\n        coe: 0.2,\n        // 是否正在多次缩放\n        scaling: !1,\n        scalingSet: \"\",\n        coeStatus: \"\",\n        // 控制emit触发频率\n        isCanShow: !0,\n        // 图片是否等于截图大小\n        imgIsQqualCrop: !1\n      };\n    },\n    props: {\n      img: {\n        type: [String, Blob, null, File],\n        default: \"\"\n      },\n      // 输出图片压缩比\n      outputSize: {\n        type: Number,\n        default: 1\n      },\n      outputType: {\n        type: String,\n        default: \"jpeg\"\n      },\n      info: {\n        type: Boolean,\n        default: !0\n      },\n      // 是否开启滚轮放大缩小\n      canScale: {\n        type: Boolean,\n        default: !0\n      },\n      // 是否自成截图框\n      autoCrop: {\n        type: Boolean,\n        default: !1\n      },\n      autoCropWidth: {\n        type: [Number, String],\n        default: 0\n      },\n      autoCropHeight: {\n        type: [Number, String],\n        default: 0\n      },\n      // 是否开启固定宽高比\n      fixed: {\n        type: Boolean,\n        default: !1\n      },\n      // 宽高比 w/h\n      fixedNumber: {\n        type: Array,\n        default: function _default() {\n          return [1, 1];\n        }\n      },\n      // 固定大小 禁止改变截图框大小\n      fixedBox: {\n        type: Boolean,\n        default: !1\n      },\n      // 输出截图是否缩放\n      full: {\n        type: Boolean,\n        default: !1\n      },\n      // 是否可以拖动图片\n      canMove: {\n        type: Boolean,\n        default: !0\n      },\n      // 是否可以拖动截图框\n      canMoveBox: {\n        type: Boolean,\n        default: !0\n      },\n      // 上传图片按照原始比例显示\n      original: {\n        type: Boolean,\n        default: !1\n      },\n      // 截图框能否超过图片\n      centerBox: {\n        type: Boolean,\n        default: !1\n      },\n      // 是否根据dpr输出高清图片\n      high: {\n        type: Boolean,\n        default: !0\n      },\n      // 截图框展示宽高类型\n      infoTrue: {\n        type: Boolean,\n        default: !1\n      },\n      // 可以压缩图片宽高  默认不超过200\n      maxImgSize: {\n        type: [Number, String],\n        default: 2e3\n      },\n      // 倍数  可渲染当前截图框的n倍 0 - 1000;\n      enlarge: {\n        type: [Number, String],\n        default: 1\n      },\n      // 自动预览的固定宽度\n      preW: {\n        type: [Number, String],\n        default: 0\n      },\n      /*\n        图片布局方式 mode 实现和css背景一样的效果\n        contain  居中布局 默认不会缩放 保证图片在容器里面 mode: 'contain'\n        cover    拉伸布局 填充整个容器  mode: 'cover'\n        如果仅有一个数值被给定，这个数值将作为宽度值大小，高度值将被设定为auto。 mode: '50px'\n        如果有两个数值被给定，第一个将作为宽度值大小，第二个作为高度值大小。 mode: '50px 60px'\n      */\n      mode: {\n        type: String,\n        default: \"contain\"\n      },\n      //限制最小区域,可传1以上的数字和字符串，限制长宽都是这么大\n      // 也可以传数组[90,90] \n      limitMinSize: {\n        type: [Number, Array, String],\n        default: function _default() {\n          return 10;\n        },\n        validator: function validator(t) {\n          return Array.isArray(t) ? Number(t[0]) >= 0 && Number(t[1]) >= 0 : Number(t) >= 0;\n        }\n      },\n      // 导出时,填充背景颜色\n      fillColor: {\n        type: String,\n        default: \"\"\n      }\n    },\n    computed: {\n      cropInfo() {\n        var t = {};\n        if (t.top = this.cropOffsertY > 21 ? \"-21px\" : \"0px\", t.width = this.cropW > 0 ? this.cropW : 0, t.height = this.cropH > 0 ? this.cropH : 0, this.infoTrue) {\n          var e = 1;\n          this.high && !this.full && (e = window.devicePixelRatio), this.enlarge !== 1 & !this.full && (e = Math.abs(Number(this.enlarge))), t.width = t.width * e, t.height = t.height * e, this.full && (t.width = t.width / this.scale, t.height = t.height / this.scale);\n        }\n        return t.width = t.width.toFixed(0), t.height = t.height.toFixed(0), t;\n      },\n      isIE() {\n        return !!window.ActiveXObject || \"ActiveXObject\" in window;\n      },\n      passive() {\n        return this.isIE ? null : {\n          passive: !1\n        };\n      },\n      // 是否处于左右旋转\n      isRotateRightOrLeft() {\n        return [1, -1, 3, -3].includes(this.rotate);\n      }\n    },\n    watch: {\n      // 如果图片改变， 重新布局\n      img() {\n        this.checkedImg();\n      },\n      imgs(t) {\n        t !== \"\" && this.reload();\n      },\n      cropW() {\n        this.showPreview();\n      },\n      cropH() {\n        this.showPreview();\n      },\n      cropOffsertX() {\n        this.showPreview();\n      },\n      cropOffsertY() {\n        this.showPreview();\n      },\n      scale(t, e) {\n        this.showPreview();\n      },\n      x() {\n        this.showPreview();\n      },\n      y() {\n        this.showPreview();\n      },\n      autoCrop(t) {\n        t && this.goAutoCrop();\n      },\n      // 修改了自动截图框\n      autoCropWidth() {\n        this.autoCrop && this.goAutoCrop();\n      },\n      autoCropHeight() {\n        this.autoCrop && this.goAutoCrop();\n      },\n      mode() {\n        this.checkedImg();\n      },\n      rotate() {\n        this.showPreview(), this.autoCrop ? this.goAutoCrop(this.cropW, this.cropH) : (this.cropW > 0 || this.cropH > 0) && this.goAutoCrop(this.cropW, this.cropH);\n      }\n    },\n    methods: {\n      getVersion(t) {\n        var e = navigator.userAgent.split(\" \"),\n          i = \"\";\n        var s = 0;\n        var r = new RegExp(t, \"i\");\n        for (var o = 0; o < e.length; o++) r.test(e[o]) && (i = e[o]);\n        return i ? s = i.split(\"/\")[1].split(\".\") : s = [\"0\", \"0\", \"0\"], s;\n      },\n      checkOrientationImage(t, e, i, s) {\n        var _this = this;\n        if (this.getVersion(\"chrome\")[0] >= 81) e = -1;else if (this.getVersion(\"safari\")[0] >= 605) {\n          var h = this.getVersion(\"version\");\n          h[0] > 13 && h[1] > 1 && (e = -1);\n        } else {\n          var _h = navigator.userAgent.toLowerCase().match(/cpu iphone os (.*?) like mac os/);\n          if (_h) {\n            var a = _h[1];\n            a = a.split(\"_\"), (a[0] > 13 || a[0] >= 13 && a[1] >= 4) && (e = -1);\n          }\n        }\n        var r = document.createElement(\"canvas\"),\n          o = r.getContext(\"2d\");\n        switch (o.save(), e) {\n          case 2:\n            r.width = i, r.height = s, o.translate(i, 0), o.scale(-1, 1);\n            break;\n          case 3:\n            r.width = i, r.height = s, o.translate(i / 2, s / 2), o.rotate(180 * Math.PI / 180), o.translate(-i / 2, -s / 2);\n            break;\n          case 4:\n            r.width = i, r.height = s, o.translate(0, s), o.scale(1, -1);\n            break;\n          case 5:\n            r.height = i, r.width = s, o.rotate(0.5 * Math.PI), o.scale(1, -1);\n            break;\n          case 6:\n            r.width = s, r.height = i, o.translate(s / 2, i / 2), o.rotate(90 * Math.PI / 180), o.translate(-i / 2, -s / 2);\n            break;\n          case 7:\n            r.height = i, r.width = s, o.rotate(0.5 * Math.PI), o.translate(i, -s), o.scale(-1, 1);\n            break;\n          case 8:\n            r.height = i, r.width = s, o.translate(s / 2, i / 2), o.rotate(-90 * Math.PI / 180), o.translate(-i / 2, -s / 2);\n            break;\n          default:\n            r.width = i, r.height = s;\n        }\n        o.drawImage(t, 0, 0, i, s), o.restore(), r.toBlob(function (h) {\n          var a = URL.createObjectURL(h);\n          URL.revokeObjectURL(_this.imgs), _this.imgs = a;\n        }, \"image/\" + this.outputType, 1);\n      },\n      // checkout img\n      checkedImg() {\n        var _this2 = this;\n        if (this.img === null || this.img === \"\") {\n          this.imgs = \"\", this.clearCrop();\n          return;\n        }\n        this.loading = !0, this.scale = 1, this.rotate = 0, this.imgIsQqualCrop = !1, this.clearCrop();\n        var t = new Image();\n        if (t.onload = function () {\n          if (_this2.img === \"\") return _this2.$emit(\"img-load\", new Error(\"图片不能为空\")), !1;\n          var i = t.width,\n            s = t.height;\n          Y.getData(t).then(function (r) {\n            _this2.orientation = r.orientation || 1;\n            var o = Number(_this2.maxImgSize);\n            if (!_this2.orientation && i < o & s < o) {\n              _this2.imgs = _this2.img;\n              return;\n            }\n            i > o && (s = s / i * o, i = o), s > o && (i = i / s * o, s = o), _this2.checkOrientationImage(t, _this2.orientation, i, s);\n          }).catch(function (r) {\n            _this2.$emit(\"img-load\", \"error\"), _this2.$emit(\"img-load-error\", r);\n          });\n        }, t.onerror = function (i) {\n          _this2.$emit(\"img-load\", \"error\"), _this2.$emit(\"img-load-error\", i);\n        }, this.img.substr(0, 4) !== \"data\" && (t.crossOrigin = \"\"), this.isIE) {\n          var e = new XMLHttpRequest();\n          e.onload = function () {\n            var i = URL.createObjectURL(this.response);\n            t.src = i;\n          }, e.open(\"GET\", this.img, !0), e.responseType = \"blob\", e.send();\n        } else t.src = this.img;\n      },\n      // 当按下鼠标键\n      startMove(t) {\n        if (t.preventDefault(), this.move && !this.crop) {\n          if (!this.canMove) return !1;\n          this.moveX = (\"clientX\" in t ? t.clientX : t.touches[0].clientX) - this.x, this.moveY = (\"clientY\" in t ? t.clientY : t.touches[0].clientY) - this.y, t.touches ? (window.addEventListener(\"touchmove\", this.moveImg), window.addEventListener(\"touchend\", this.leaveImg), t.touches.length == 2 && (this.touches = t.touches, window.addEventListener(\"touchmove\", this.touchScale), window.addEventListener(\"touchend\", this.cancelTouchScale))) : (window.addEventListener(\"mousemove\", this.moveImg), window.addEventListener(\"mouseup\", this.leaveImg)), this.$emit(\"img-moving\", {\n            moving: !0,\n            axis: this.getImgAxis()\n          });\n        } else this.cropping = !0, window.addEventListener(\"mousemove\", this.createCrop), window.addEventListener(\"mouseup\", this.endCrop), window.addEventListener(\"touchmove\", this.createCrop), window.addEventListener(\"touchend\", this.endCrop), this.cropOffsertX = t.offsetX ? t.offsetX : t.touches[0].pageX - this.$refs.cropper.offsetLeft, this.cropOffsertY = t.offsetY ? t.offsetY : t.touches[0].pageY - this.$refs.cropper.offsetTop, this.cropX = \"clientX\" in t ? t.clientX : t.touches[0].clientX, this.cropY = \"clientY\" in t ? t.clientY : t.touches[0].clientY, this.cropChangeX = this.cropOffsertX, this.cropChangeY = this.cropOffsertY, this.cropW = 0, this.cropH = 0;\n      },\n      // 移动端缩放\n      touchScale(t) {\n        var _this3 = this;\n        t.preventDefault();\n        var e = this.scale;\n        var i = {\n            x: this.touches[0].clientX,\n            y: this.touches[0].clientY\n          },\n          s = {\n            x: t.touches[0].clientX,\n            y: t.touches[0].clientY\n          },\n          r = {\n            x: this.touches[1].clientX,\n            y: this.touches[1].clientY\n          },\n          o = {\n            x: t.touches[1].clientX,\n            y: t.touches[1].clientY\n          },\n          h = Math.sqrt(Math.pow(i.x - r.x, 2) + Math.pow(i.y - r.y, 2)),\n          a = Math.sqrt(Math.pow(s.x - o.x, 2) + Math.pow(s.y - o.y, 2)),\n          n = a - h,\n          c = 1;\n        c = c / this.trueWidth > c / this.trueHeight ? c / this.trueHeight : c / this.trueWidth, c = c > 0.1 ? 0.1 : c;\n        var l = c * n;\n        if (!this.touchNow) {\n          if (this.touchNow = !0, n > 0 ? e += Math.abs(l) : n < 0 && e > Math.abs(l) && (e -= Math.abs(l)), this.touches = t.touches, setTimeout(function () {\n            _this3.touchNow = !1;\n          }, 8), !this.checkoutImgAxis(this.x, this.y, e)) return !1;\n          this.scale = e;\n        }\n      },\n      cancelTouchScale(t) {\n        window.removeEventListener(\"touchmove\", this.touchScale);\n      },\n      // 移动图片\n      moveImg(t) {\n        var _this4 = this;\n        if (t.preventDefault(), t.touches && t.touches.length === 2) return this.touches = t.touches, window.addEventListener(\"touchmove\", this.touchScale), window.addEventListener(\"touchend\", this.cancelTouchScale), window.removeEventListener(\"touchmove\", this.moveImg), !1;\n        var e = \"clientX\" in t ? t.clientX : t.touches[0].clientX,\n          i = \"clientY\" in t ? t.clientY : t.touches[0].clientY,\n          s,\n          r;\n        s = e - this.moveX, r = i - this.moveY, this.$nextTick(function () {\n          if (_this4.centerBox) {\n            var o = _this4.getImgAxis(s, r, _this4.scale),\n              h = _this4.getCropAxis(),\n              a = _this4.trueHeight * _this4.scale,\n              n = _this4.trueWidth * _this4.scale,\n              c,\n              l,\n              f,\n              p;\n            switch (_this4.rotate) {\n              case 1:\n              case -1:\n              case 3:\n              case -3:\n                c = _this4.cropOffsertX - _this4.trueWidth * (1 - _this4.scale) / 2 + (a - n) / 2, l = _this4.cropOffsertY - _this4.trueHeight * (1 - _this4.scale) / 2 + (n - a) / 2, f = c - a + _this4.cropW, p = l - n + _this4.cropH;\n                break;\n              default:\n                c = _this4.cropOffsertX - _this4.trueWidth * (1 - _this4.scale) / 2, l = _this4.cropOffsertY - _this4.trueHeight * (1 - _this4.scale) / 2, f = c - n + _this4.cropW, p = l - a + _this4.cropH;\n                break;\n            }\n            o.x1 >= h.x1 && (s = c), o.y1 >= h.y1 && (r = l), o.x2 <= h.x2 && (s = f), o.y2 <= h.y2 && (r = p);\n          }\n          _this4.x = s, _this4.y = r, _this4.$emit(\"img-moving\", {\n            moving: !0,\n            axis: _this4.getImgAxis()\n          });\n        });\n      },\n      // 移动图片结束\n      leaveImg(t) {\n        window.removeEventListener(\"mousemove\", this.moveImg), window.removeEventListener(\"touchmove\", this.moveImg), window.removeEventListener(\"mouseup\", this.leaveImg), window.removeEventListener(\"touchend\", this.leaveImg), this.$emit(\"img-moving\", {\n          moving: !1,\n          axis: this.getImgAxis()\n        });\n      },\n      // 缩放图片\n      scaleImg() {\n        this.canScale && window.addEventListener(this.support, this.changeSize, this.passive);\n      },\n      // 移出框\n      cancelScale() {\n        this.canScale && window.removeEventListener(this.support, this.changeSize);\n      },\n      // 改变大小函数\n      changeSize(t) {\n        var _this5 = this;\n        t.preventDefault();\n        var e = this.scale;\n        var i = t.deltaY || t.wheelDelta,\n          s = navigator.userAgent.indexOf(\"Firefox\");\n        i = s > 0 ? i * 30 : i, this.isIE && (i = -i);\n        var r = this.coe;\n        r = r / this.trueWidth > r / this.trueHeight ? r / this.trueHeight : r / this.trueWidth;\n        var o = r * i;\n        o < 0 ? e += Math.abs(o) : e > Math.abs(o) && (e -= Math.abs(o));\n        var h = o < 0 ? \"add\" : \"reduce\";\n        if (h !== this.coeStatus && (this.coeStatus = h, this.coe = 0.2), this.scaling || (this.scalingSet = setTimeout(function () {\n          _this5.scaling = !1, _this5.coe = _this5.coe += 0.01;\n        }, 50)), this.scaling = !0, !this.checkoutImgAxis(this.x, this.y, e)) return !1;\n        this.scale = e;\n      },\n      // 修改图片大小函数\n      changeScale(t) {\n        var e = this.scale;\n        t = t || 1;\n        var i = 20;\n        if (i = i / this.trueWidth > i / this.trueHeight ? i / this.trueHeight : i / this.trueWidth, t = t * i, t > 0 ? e += Math.abs(t) : e > Math.abs(t) && (e -= Math.abs(t)), !this.checkoutImgAxis(this.x, this.y, e)) return !1;\n        this.scale = e;\n      },\n      // 创建截图框\n      createCrop(t) {\n        var _this6 = this;\n        t.preventDefault();\n        var e = \"clientX\" in t ? t.clientX : t.touches ? t.touches[0].clientX : 0,\n          i = \"clientY\" in t ? t.clientY : t.touches ? t.touches[0].clientY : 0;\n        this.$nextTick(function () {\n          var s = e - _this6.cropX,\n            r = i - _this6.cropY;\n          if (s > 0 ? (_this6.cropW = s + _this6.cropChangeX > _this6.w ? _this6.w - _this6.cropChangeX : s, _this6.cropOffsertX = _this6.cropChangeX) : (_this6.cropW = _this6.w - _this6.cropChangeX + Math.abs(s) > _this6.w ? _this6.cropChangeX : Math.abs(s), _this6.cropOffsertX = _this6.cropChangeX + s > 0 ? _this6.cropChangeX + s : 0), !_this6.fixed) r > 0 ? (_this6.cropH = r + _this6.cropChangeY > _this6.h ? _this6.h - _this6.cropChangeY : r, _this6.cropOffsertY = _this6.cropChangeY) : (_this6.cropH = _this6.h - _this6.cropChangeY + Math.abs(r) > _this6.h ? _this6.cropChangeY : Math.abs(r), _this6.cropOffsertY = _this6.cropChangeY + r > 0 ? _this6.cropChangeY + r : 0);else {\n            var o = _this6.cropW / _this6.fixedNumber[0] * _this6.fixedNumber[1];\n            o + _this6.cropOffsertY > _this6.h ? (_this6.cropH = _this6.h - _this6.cropOffsertY, _this6.cropW = _this6.cropH / _this6.fixedNumber[1] * _this6.fixedNumber[0], s > 0 ? _this6.cropOffsertX = _this6.cropChangeX : _this6.cropOffsertX = _this6.cropChangeX - _this6.cropW) : _this6.cropH = o, _this6.cropOffsertY = _this6.cropOffsertY;\n          }\n        });\n      },\n      // 改变截图框大小\n      changeCropSize(t, e, i, s, r) {\n        t.preventDefault(), window.addEventListener(\"mousemove\", this.changeCropNow), window.addEventListener(\"mouseup\", this.changeCropEnd), window.addEventListener(\"touchmove\", this.changeCropNow), window.addEventListener(\"touchend\", this.changeCropEnd), this.canChangeX = e, this.canChangeY = i, this.changeCropTypeX = s, this.changeCropTypeY = r, this.cropX = \"clientX\" in t ? t.clientX : t.touches[0].clientX, this.cropY = \"clientY\" in t ? t.clientY : t.touches[0].clientY, this.cropOldW = this.cropW, this.cropOldH = this.cropH, this.cropChangeX = this.cropOffsertX, this.cropChangeY = this.cropOffsertY, this.fixed && this.canChangeX && this.canChangeY && (this.canChangeY = 0), this.$emit(\"change-crop-size\", {\n          width: this.cropW,\n          height: this.cropH\n        });\n      },\n      // 正在改变\n      changeCropNow(t) {\n        var _this7 = this;\n        t.preventDefault();\n        var e = \"clientX\" in t ? t.clientX : t.touches ? t.touches[0].clientX : 0,\n          i = \"clientY\" in t ? t.clientY : t.touches ? t.touches[0].clientY : 0;\n        var s = this.w,\n          r = this.h,\n          o = 0,\n          h = 0;\n        if (this.centerBox) {\n          var c = this.getImgAxis(),\n            l = c.x2,\n            f = c.y2;\n          o = c.x1 > 0 ? c.x1 : 0, h = c.y1 > 0 ? c.y1 : 0, s > l && (s = l), r > f && (r = f);\n        }\n        var _this$checkCropLimitS = this.checkCropLimitSize(),\n          _this$checkCropLimitS2 = _slicedToArray(_this$checkCropLimitS, 2),\n          a = _this$checkCropLimitS2[0],\n          n = _this$checkCropLimitS2[1];\n        this.$nextTick(function () {\n          var c = e - _this7.cropX,\n            l = i - _this7.cropY;\n          if (_this7.canChangeX && (_this7.changeCropTypeX === 1 ? _this7.cropOldW - c < a ? (_this7.cropW = a, _this7.cropOffsertX = _this7.cropOldW + _this7.cropChangeX - o - a) : _this7.cropOldW - c > 0 ? (_this7.cropW = s - _this7.cropChangeX - c <= s - o ? _this7.cropOldW - c : _this7.cropOldW + _this7.cropChangeX - o, _this7.cropOffsertX = s - _this7.cropChangeX - c <= s - o ? _this7.cropChangeX + c : o) : (_this7.cropW = Math.abs(c) + _this7.cropChangeX <= s ? Math.abs(c) - _this7.cropOldW : s - _this7.cropOldW - _this7.cropChangeX, _this7.cropOffsertX = _this7.cropChangeX + _this7.cropOldW) : _this7.changeCropTypeX === 2 && (_this7.cropOldW + c < a ? _this7.cropW = a : _this7.cropOldW + c > 0 ? (_this7.cropW = _this7.cropOldW + c + _this7.cropOffsertX <= s ? _this7.cropOldW + c : s - _this7.cropOffsertX, _this7.cropOffsertX = _this7.cropChangeX) : (_this7.cropW = s - _this7.cropChangeX + Math.abs(c + _this7.cropOldW) <= s - o ? Math.abs(c + _this7.cropOldW) : _this7.cropChangeX - o, _this7.cropOffsertX = s - _this7.cropChangeX + Math.abs(c + _this7.cropOldW) <= s - o ? _this7.cropChangeX - Math.abs(c + _this7.cropOldW) : o))), _this7.canChangeY && (_this7.changeCropTypeY === 1 ? _this7.cropOldH - l < n ? (_this7.cropH = n, _this7.cropOffsertY = _this7.cropOldH + _this7.cropChangeY - h - n) : _this7.cropOldH - l > 0 ? (_this7.cropH = r - _this7.cropChangeY - l <= r - h ? _this7.cropOldH - l : _this7.cropOldH + _this7.cropChangeY - h, _this7.cropOffsertY = r - _this7.cropChangeY - l <= r - h ? _this7.cropChangeY + l : h) : (_this7.cropH = Math.abs(l) + _this7.cropChangeY <= r ? Math.abs(l) - _this7.cropOldH : r - _this7.cropOldH - _this7.cropChangeY, _this7.cropOffsertY = _this7.cropChangeY + _this7.cropOldH) : _this7.changeCropTypeY === 2 && (_this7.cropOldH + l < n ? _this7.cropH = n : _this7.cropOldH + l > 0 ? (_this7.cropH = _this7.cropOldH + l + _this7.cropOffsertY <= r ? _this7.cropOldH + l : r - _this7.cropOffsertY, _this7.cropOffsertY = _this7.cropChangeY) : (_this7.cropH = r - _this7.cropChangeY + Math.abs(l + _this7.cropOldH) <= r - h ? Math.abs(l + _this7.cropOldH) : _this7.cropChangeY - h, _this7.cropOffsertY = r - _this7.cropChangeY + Math.abs(l + _this7.cropOldH) <= r - h ? _this7.cropChangeY - Math.abs(l + _this7.cropOldH) : h))), _this7.canChangeX && _this7.fixed) {\n            var f = _this7.cropW / _this7.fixedNumber[0] * _this7.fixedNumber[1];\n            f < n ? (_this7.cropH = n, _this7.cropW = _this7.fixedNumber[0] * n / _this7.fixedNumber[1], _this7.changeCropTypeX === 1 && (_this7.cropOffsertX = _this7.cropChangeX + (_this7.cropOldW - _this7.cropW))) : f + _this7.cropOffsertY > r ? (_this7.cropH = r - _this7.cropOffsertY, _this7.cropW = _this7.cropH / _this7.fixedNumber[1] * _this7.fixedNumber[0], _this7.changeCropTypeX === 1 && (_this7.cropOffsertX = _this7.cropChangeX + (_this7.cropOldW - _this7.cropW))) : _this7.cropH = f;\n          }\n          if (_this7.canChangeY && _this7.fixed) {\n            var p = _this7.cropH / _this7.fixedNumber[1] * _this7.fixedNumber[0];\n            p < a ? (_this7.cropW = a, _this7.cropH = _this7.fixedNumber[1] * a / _this7.fixedNumber[0], _this7.cropOffsertY = _this7.cropOldH + _this7.cropChangeY - _this7.cropH) : p + _this7.cropOffsertX > s ? (_this7.cropW = s - _this7.cropOffsertX, _this7.cropH = _this7.cropW / _this7.fixedNumber[0] * _this7.fixedNumber[1]) : _this7.cropW = p;\n          }\n        });\n      },\n      checkCropLimitSize() {\n        var t = this.cropW,\n          e = this.cropH,\n          i = this.limitMinSize,\n          s = new Array();\n        return Array.isArray(i) ? s = i : s = [i, i], t = parseFloat(s[0]), e = parseFloat(s[1]), [t, e];\n      },\n      // 结束改变\n      changeCropEnd(t) {\n        window.removeEventListener(\"mousemove\", this.changeCropNow), window.removeEventListener(\"mouseup\", this.changeCropEnd), window.removeEventListener(\"touchmove\", this.changeCropNow), window.removeEventListener(\"touchend\", this.changeCropEnd);\n      },\n      // 根据比例x/y，最小宽度，最小高度，现有宽度，现有高度，得到应该有的宽度和高度\n      calculateSize(t, e, i, s, r, o) {\n        var h = t / e;\n        var a = r,\n          n = o;\n        return a < i && (a = i, n = Math.ceil(a / h)), n < s && (n = s, a = Math.ceil(n * h), a < i && (a = i, n = Math.ceil(a / h))), a < r && (a = r, n = Math.ceil(a / h)), n < o && (n = o, a = Math.ceil(n * h)), {\n          width: a,\n          height: n\n        };\n      },\n      // 创建完成\n      endCrop() {\n        this.cropW === 0 && this.cropH === 0 && (this.cropping = !1);\n        var _this$checkCropLimitS3 = this.checkCropLimitSize(),\n          _this$checkCropLimitS4 = _slicedToArray(_this$checkCropLimitS3, 2),\n          t = _this$checkCropLimitS4[0],\n          e = _this$checkCropLimitS4[1];\n        var _ref = this.fixed ? this.calculateSize(this.fixedNumber[0], this.fixedNumber[1], t, e, this.cropW, this.cropH) : {\n            width: t,\n            height: e\n          },\n          i = _ref.width,\n          s = _ref.height;\n        i > this.cropW && (this.cropW = i, this.cropOffsertX + i > this.w && (this.cropOffsertX = this.w - i)), s > this.cropH && (this.cropH = s, this.cropOffsertY + s > this.h && (this.cropOffsertY = this.h - s)), window.removeEventListener(\"mousemove\", this.createCrop), window.removeEventListener(\"mouseup\", this.endCrop), window.removeEventListener(\"touchmove\", this.createCrop), window.removeEventListener(\"touchend\", this.endCrop);\n      },\n      // 开始截图\n      startCrop() {\n        this.crop = !0;\n      },\n      // 停止截图\n      stopCrop() {\n        this.crop = !1;\n      },\n      // 清除截图\n      clearCrop() {\n        this.cropping = !1, this.cropW = 0, this.cropH = 0;\n      },\n      // 截图移动\n      cropMove(t) {\n        if (t.preventDefault(), !this.canMoveBox) return this.crop = !1, this.startMove(t), !1;\n        if (t.touches && t.touches.length === 2) return this.crop = !1, this.startMove(t), this.leaveCrop(), !1;\n        window.addEventListener(\"mousemove\", this.moveCrop), window.addEventListener(\"mouseup\", this.leaveCrop), window.addEventListener(\"touchmove\", this.moveCrop), window.addEventListener(\"touchend\", this.leaveCrop);\n        var e = \"clientX\" in t ? t.clientX : t.touches[0].clientX,\n          i = \"clientY\" in t ? t.clientY : t.touches[0].clientY,\n          s,\n          r;\n        s = e - this.cropOffsertX, r = i - this.cropOffsertY, this.cropX = s, this.cropY = r, this.$emit(\"crop-moving\", {\n          moving: !0,\n          axis: this.getCropAxis()\n        });\n      },\n      moveCrop(t, e) {\n        var _this8 = this;\n        var i = 0,\n          s = 0;\n        t && (t.preventDefault(), i = \"clientX\" in t ? t.clientX : t.touches[0].clientX, s = \"clientY\" in t ? t.clientY : t.touches[0].clientY), this.$nextTick(function () {\n          var r,\n            o,\n            h = i - _this8.cropX,\n            a = s - _this8.cropY;\n          if (e && (h = _this8.cropOffsertX, a = _this8.cropOffsertY), h <= 0 ? r = 0 : h + _this8.cropW > _this8.w ? r = _this8.w - _this8.cropW : r = h, a <= 0 ? o = 0 : a + _this8.cropH > _this8.h ? o = _this8.h - _this8.cropH : o = a, _this8.centerBox) {\n            var n = _this8.getImgAxis();\n            r <= n.x1 && (r = n.x1), r + _this8.cropW > n.x2 && (r = n.x2 - _this8.cropW), o <= n.y1 && (o = n.y1), o + _this8.cropH > n.y2 && (o = n.y2 - _this8.cropH);\n          }\n          _this8.cropOffsertX = r, _this8.cropOffsertY = o, _this8.$emit(\"crop-moving\", {\n            moving: !0,\n            axis: _this8.getCropAxis()\n          });\n        });\n      },\n      // 算出不同场景下面 图片相对于外层容器的坐标轴\n      getImgAxis(t, e, i) {\n        t = t || this.x, e = e || this.y, i = i || this.scale;\n        var s = {\n            x1: 0,\n            x2: 0,\n            y1: 0,\n            y2: 0\n          },\n          r = this.trueWidth * i,\n          o = this.trueHeight * i;\n        switch (this.rotate) {\n          case 0:\n            s.x1 = t + this.trueWidth * (1 - i) / 2, s.x2 = s.x1 + this.trueWidth * i, s.y1 = e + this.trueHeight * (1 - i) / 2, s.y2 = s.y1 + this.trueHeight * i;\n            break;\n          case 1:\n          case -1:\n          case 3:\n          case -3:\n            s.x1 = t + this.trueWidth * (1 - i) / 2 + (r - o) / 2, s.x2 = s.x1 + this.trueHeight * i, s.y1 = e + this.trueHeight * (1 - i) / 2 + (o - r) / 2, s.y2 = s.y1 + this.trueWidth * i;\n            break;\n          default:\n            s.x1 = t + this.trueWidth * (1 - i) / 2, s.x2 = s.x1 + this.trueWidth * i, s.y1 = e + this.trueHeight * (1 - i) / 2, s.y2 = s.y1 + this.trueHeight * i;\n            break;\n        }\n        return s;\n      },\n      // 获取截图框的坐标轴\n      getCropAxis() {\n        var t = {\n          x1: 0,\n          x2: 0,\n          y1: 0,\n          y2: 0\n        };\n        return t.x1 = this.cropOffsertX, t.x2 = t.x1 + this.cropW, t.y1 = this.cropOffsertY, t.y2 = t.y1 + this.cropH, t;\n      },\n      leaveCrop(t) {\n        window.removeEventListener(\"mousemove\", this.moveCrop), window.removeEventListener(\"mouseup\", this.leaveCrop), window.removeEventListener(\"touchmove\", this.moveCrop), window.removeEventListener(\"touchend\", this.leaveCrop), this.$emit(\"crop-moving\", {\n          moving: !1,\n          axis: this.getCropAxis()\n        });\n      },\n      getCropChecked(t) {\n        var _this9 = this;\n        var e = document.createElement(\"canvas\"),\n          i = e.getContext(\"2d\"),\n          s = new Image(),\n          r = this.rotate,\n          o = this.trueWidth,\n          h = this.trueHeight,\n          a = this.cropOffsertX,\n          n = this.cropOffsertY;\n        s.onload = function () {\n          if (_this9.cropW !== 0) {\n            var p = 1;\n            _this9.high & !_this9.full && (p = window.devicePixelRatio), _this9.enlarge !== 1 & !_this9.full && (p = Math.abs(Number(_this9.enlarge)));\n            var d = _this9.cropW * p,\n              C = _this9.cropH * p,\n              u = o * _this9.scale * p,\n              g = h * _this9.scale * p,\n              m = (_this9.x - a + _this9.trueWidth * (1 - _this9.scale) / 2) * p,\n              v = (_this9.y - n + _this9.trueHeight * (1 - _this9.scale) / 2) * p;\n            switch (f(d, C), i.save(), r) {\n              case 0:\n                _this9.full ? (f(d / _this9.scale, C / _this9.scale), i.drawImage(s, m / _this9.scale, v / _this9.scale, u / _this9.scale, g / _this9.scale)) : i.drawImage(s, m, v, u, g);\n                break;\n              case 1:\n              case -3:\n                _this9.full ? (f(d / _this9.scale, C / _this9.scale), m = m / _this9.scale + (u / _this9.scale - g / _this9.scale) / 2, v = v / _this9.scale + (g / _this9.scale - u / _this9.scale) / 2, i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, v, -m - g / _this9.scale, u / _this9.scale, g / _this9.scale)) : (m = m + (u - g) / 2, v = v + (g - u) / 2, i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, v, -m - g, u, g));\n                break;\n              case 2:\n              case -2:\n                _this9.full ? (f(d / _this9.scale, C / _this9.scale), i.rotate(r * 90 * Math.PI / 180), m = m / _this9.scale, v = v / _this9.scale, i.drawImage(s, -m - u / _this9.scale, -v - g / _this9.scale, u / _this9.scale, g / _this9.scale)) : (i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, -m - u, -v - g, u, g));\n                break;\n              case 3:\n              case -1:\n                _this9.full ? (f(d / _this9.scale, C / _this9.scale), m = m / _this9.scale + (u / _this9.scale - g / _this9.scale) / 2, v = v / _this9.scale + (g / _this9.scale - u / _this9.scale) / 2, i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, -v - u / _this9.scale, m, u / _this9.scale, g / _this9.scale)) : (m = m + (u - g) / 2, v = v + (g - u) / 2, i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, -v - u, m, u, g));\n                break;\n              default:\n                _this9.full ? (f(d / _this9.scale, C / _this9.scale), i.drawImage(s, m / _this9.scale, v / _this9.scale, u / _this9.scale, g / _this9.scale)) : i.drawImage(s, m, v, u, g);\n            }\n            i.restore();\n          } else {\n            var _p = o * _this9.scale,\n              _d = h * _this9.scale;\n            switch (i.save(), r) {\n              case 0:\n                f(_p, _d), i.drawImage(s, 0, 0, _p, _d);\n                break;\n              case 1:\n              case -3:\n                f(_d, _p), i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, 0, -_d, _p, _d);\n                break;\n              case 2:\n              case -2:\n                f(_p, _d), i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, -_p, -_d, _p, _d);\n                break;\n              case 3:\n              case -1:\n                f(_d, _p), i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, -_p, 0, _p, _d);\n                break;\n              default:\n                f(_p, _d), i.drawImage(s, 0, 0, _p, _d);\n            }\n            i.restore();\n          }\n          t(e);\n        };\n        var c = this.img.substr(0, 4);\n        c !== \"data\" && (s.crossOrigin = \"Anonymous\"), s.src = this.imgs;\n        var l = this.fillColor;\n        function f(p, d) {\n          e.width = Math.round(p), e.height = Math.round(d), l && (i.fillStyle = l, i.fillRect(0, 0, e.width, e.height));\n        }\n      },\n      // 获取转换成base64 的图片信息\n      getCropData(t) {\n        var _this10 = this;\n        this.getCropChecked(function (e) {\n          t(e.toDataURL(\"image/\" + _this10.outputType, _this10.outputSize));\n        });\n      },\n      //canvas获取为blob对象\n      getCropBlob(t) {\n        var _this11 = this;\n        this.getCropChecked(function (e) {\n          e.toBlob(function (i) {\n            return t(i);\n          }, \"image/\" + _this11.outputType, _this11.outputSize);\n        });\n      },\n      // 自动预览函数\n      showPreview() {\n        var _this12 = this;\n        if (this.isCanShow) this.isCanShow = !1, setTimeout(function () {\n          _this12.isCanShow = !0;\n        }, 16);else return !1;\n        var t = this.cropW,\n          e = this.cropH,\n          i = this.scale;\n        var s = {};\n        s.div = {\n          width: `${t}px`,\n          height: `${e}px`\n        };\n        var r = (this.x - this.cropOffsertX) / i,\n          o = (this.y - this.cropOffsertY) / i,\n          h = 0;\n        s.w = t, s.h = e, s.url = this.imgs, s.img = {\n          width: `${this.trueWidth}px`,\n          height: `${this.trueHeight}px`,\n          transform: `scale(${i})translate3d(${r}px, ${o}px, ${h}px)rotateZ(${this.rotate * 90}deg)`\n        }, s.html = `\n      <div class=\"show-preview\" style=\"width: ${s.w}px; height: ${s.h}px,; overflow: hidden\">\n        <div style=\"width: ${t}px; height: ${e}px\">\n          <img src=${s.url} style=\"width: ${this.trueWidth}px; height: ${this.trueHeight}px; transform:\n          scale(${i})translate3d(${r}px, ${o}px, ${h}px)rotateZ(${this.rotate * 90}deg)\">\n        </div>\n      </div>`, this.$emit(\"real-time\", s);\n      },\n      // reload 图片布局函数\n      reload() {\n        var _this13 = this;\n        var t = new Image();\n        t.onload = function () {\n          _this13.w = parseFloat(window.getComputedStyle(_this13.$refs.cropper).width), _this13.h = parseFloat(window.getComputedStyle(_this13.$refs.cropper).height), _this13.trueWidth = t.width, _this13.trueHeight = t.height, _this13.original ? _this13.scale = 1 : _this13.scale = _this13.checkedMode(), _this13.$nextTick(function () {\n            _this13.x = -(_this13.trueWidth - _this13.trueWidth * _this13.scale) / 2 + (_this13.w - _this13.trueWidth * _this13.scale) / 2, _this13.y = -(_this13.trueHeight - _this13.trueHeight * _this13.scale) / 2 + (_this13.h - _this13.trueHeight * _this13.scale) / 2, _this13.loading = !1, _this13.autoCrop && _this13.goAutoCrop(), _this13.$emit(\"img-load\", \"success\"), setTimeout(function () {\n              _this13.showPreview();\n            }, 20);\n          });\n        }, t.onerror = function () {\n          _this13.$emit(\"img-load\", \"error\");\n        }, t.src = this.imgs;\n      },\n      // 背景布局的函数\n      checkedMode() {\n        var t = 1,\n          e = this.trueWidth,\n          i = this.trueHeight;\n        var s = this.mode.split(\" \");\n        switch (s[0]) {\n          case \"contain\":\n            this.trueWidth > this.w && (t = this.w / this.trueWidth), this.trueHeight * t > this.h && (t = this.h / this.trueHeight);\n            break;\n          case \"cover\":\n            e = this.w, t = e / this.trueWidth, i = i * t, i < this.h && (i = this.h, t = i / this.trueHeight);\n            break;\n          default:\n            try {\n              var r = s[0];\n              if (r.search(\"px\") !== -1) {\n                r = r.replace(\"px\", \"\"), e = parseFloat(r);\n                var o = e / this.trueWidth;\n                var h = 1,\n                  a = s[1];\n                a.search(\"px\") !== -1 && (a = a.replace(\"px\", \"\"), i = parseFloat(a), h = i / this.trueHeight), t = Math.min(o, h);\n              }\n              if (r.search(\"%\") !== -1 && (r = r.replace(\"%\", \"\"), e = parseFloat(r) / 100 * this.w, t = e / this.trueWidth), s.length === 2 && r === \"auto\") {\n                var _o = s[1];\n                _o.search(\"px\") !== -1 && (_o = _o.replace(\"px\", \"\"), i = parseFloat(_o), t = i / this.trueHeight), _o.search(\"%\") !== -1 && (_o = _o.replace(\"%\", \"\"), i = parseFloat(_o) / 100 * this.h, t = i / this.trueHeight);\n              }\n            } catch (_unused2) {\n              t = 1;\n            }\n        }\n        return t;\n      },\n      // 自动截图函数\n      goAutoCrop(t, e) {\n        if (this.imgs === \"\" || this.imgs === null) return;\n        this.clearCrop(), this.cropping = !0;\n        var i = this.w,\n          s = this.h;\n        if (this.centerBox) {\n          var h = Math.abs(this.rotate) % 2 > 0;\n          var a = (h ? this.trueHeight : this.trueWidth) * this.scale,\n            n = (h ? this.trueWidth : this.trueHeight) * this.scale;\n          i = a < i ? a : i, s = n < s ? n : s;\n        }\n        var r = t || parseFloat(this.autoCropWidth),\n          o = e || parseFloat(this.autoCropHeight);\n        (r === 0 || o === 0) && (r = i * 0.8, o = s * 0.8), r = r > i ? i : r, o = o > s ? s : o, this.fixed && (o = r / this.fixedNumber[0] * this.fixedNumber[1]), o > this.h && (o = this.h, r = o / this.fixedNumber[1] * this.fixedNumber[0]), this.changeCrop(r, o);\n      },\n      // 手动改变截图框大小函数\n      changeCrop(t, e) {\n        var _this14 = this;\n        if (this.centerBox) {\n          var i = this.getImgAxis();\n          t > i.x2 - i.x1 && (t = i.x2 - i.x1, e = t / this.fixedNumber[0] * this.fixedNumber[1]), e > i.y2 - i.y1 && (e = i.y2 - i.y1, t = e / this.fixedNumber[1] * this.fixedNumber[0]);\n        }\n        this.cropW = t, this.cropH = e, this.checkCropLimitSize(), this.$nextTick(function () {\n          _this14.cropOffsertX = (_this14.w - _this14.cropW) / 2, _this14.cropOffsertY = (_this14.h - _this14.cropH) / 2, _this14.centerBox && _this14.moveCrop(null, !0);\n        });\n      },\n      // 重置函数， 恢复组件置初始状态\n      refresh() {\n        var _this15 = this;\n        this.img, this.imgs = \"\", this.scale = 1, this.crop = !1, this.rotate = 0, this.w = 0, this.h = 0, this.trueWidth = 0, this.trueHeight = 0, this.imgIsQqualCrop = !1, this.clearCrop(), this.$nextTick(function () {\n          _this15.checkedImg();\n        });\n      },\n      // 向左边旋转\n      rotateLeft() {\n        this.rotate = this.rotate <= -3 ? 0 : this.rotate - 1;\n      },\n      // 向右边旋转\n      rotateRight() {\n        this.rotate = this.rotate >= 3 ? 0 : this.rotate + 1;\n      },\n      // 清除旋转\n      rotateClear() {\n        this.rotate = 0;\n      },\n      // 图片坐标点校验\n      checkoutImgAxis(t, e, i) {\n        t = t || this.x, e = e || this.y, i = i || this.scale;\n        var s = !0;\n        if (this.centerBox) {\n          var r = this.getImgAxis(t, e, i),\n            o = this.getCropAxis();\n          r.x1 >= o.x1 && (s = !1), r.x2 <= o.x2 && (s = !1), r.y1 >= o.y1 && (s = !1), r.y2 <= o.y2 && (s = !1), s || this.changeImgScale(r, o, i);\n        }\n        return s;\n      },\n      // 缩放图片，将图片坐标适配截图框坐标\n      changeImgScale(t, e, i) {\n        var s = this.trueWidth,\n          r = this.trueHeight,\n          o = s * i,\n          h = r * i;\n        if (o >= this.cropW && h >= this.cropH) this.scale = i;else {\n          var a = this.cropW / s,\n            n = this.cropH / r,\n            c = this.cropH <= r * a ? a : n;\n          this.scale = c, o = s * c, h = r * c;\n        }\n        this.imgIsQqualCrop || (t.x1 >= e.x1 && (this.isRotateRightOrLeft ? this.x = e.x1 - (s - o) / 2 - (o - h) / 2 : this.x = e.x1 - (s - o) / 2), t.x2 <= e.x2 && (this.isRotateRightOrLeft ? this.x = e.x1 - (s - o) / 2 - (o - h) / 2 - h + this.cropW : this.x = e.x2 - (s - o) / 2 - o), t.y1 >= e.y1 && (this.isRotateRightOrLeft ? this.y = e.y1 - (r - h) / 2 - (h - o) / 2 : this.y = e.y1 - (r - h) / 2), t.y2 <= e.y2 && (this.isRotateRightOrLeft ? this.y = e.y2 - (r - h) / 2 - (h - o) / 2 - o : this.y = e.y2 - (r - h) / 2 - h)), (o < this.cropW || h < this.cropH) && (this.imgIsQqualCrop = !0);\n      }\n    },\n    mounted() {\n      this.support = \"onwheel\" in document.createElement(\"div\") ? \"wheel\" : document.onmousewheel !== void 0 ? \"mousewheel\" : \"DOMMouseScroll\";\n      var t = this;\n      var e = navigator.userAgent;\n      this.isIOS = !!e.match(/\\(i[^;]+;( U;)? CPU.+Mac OS X/), HTMLCanvasElement.prototype.toBlob || Object.defineProperty(HTMLCanvasElement.prototype, \"toBlob\", {\n        value: function value(i, s, r) {\n          for (var o = atob(this.toDataURL(s, r).split(\",\")[1]), h = o.length, a = new Uint8Array(h), n = 0; n < h; n++) a[n] = o.charCodeAt(n);\n          i(new Blob([a], {\n            type: t.type || \"image/png\"\n          }));\n        }\n      }), this.showPreview(), this.checkedImg();\n    },\n    unmounted() {\n      window.removeEventListener(\"mousemove\", this.moveCrop), window.removeEventListener(\"mouseup\", this.leaveCrop), window.removeEventListener(\"touchmove\", this.moveCrop), window.removeEventListener(\"touchend\", this.leaveCrop), this.cancelScale();\n    }\n  }),\n  A = {\n    key: 0,\n    class: \"cropper-box\"\n  },\n  B = [\"src\"],\n  P = {\n    class: \"cropper-view-box\"\n  },\n  R = [\"src\"],\n  D = {\n    key: 1\n  };\nfunction U(t, e, i, s, r, o) {\n  return y(), x(\"div\", {\n    class: \"vue-cropper\",\n    ref: \"cropper\",\n    onMouseover: e[28] || (e[28] = function () {\n      return t.scaleImg && t.scaleImg.apply(t, arguments);\n    }),\n    onMouseout: e[29] || (e[29] = function () {\n      return t.cancelScale && t.cancelScale.apply(t, arguments);\n    })\n  }, [t.imgs ? (y(), x(\"div\", A, [H(w(\"div\", {\n    class: \"cropper-box-canvas\",\n    style: b({\n      width: t.trueWidth + \"px\",\n      height: t.trueHeight + \"px\",\n      transform: \"scale(\" + t.scale + \",\" + t.scale + \") translate3d(\" + t.x / t.scale + \"px,\" + t.y / t.scale + \"px,0)rotateZ(\" + t.rotate * 90 + \"deg)\"\n    })\n  }, [w(\"img\", {\n    src: t.imgs,\n    alt: \"cropper-img\",\n    ref: \"cropperImg\"\n  }, null, 8, B)], 4), [[W, !t.loading]])])) : O(\"\", !0), w(\"div\", {\n    class: I([\"cropper-drag-box\", {\n      \"cropper-move\": t.move && !t.crop,\n      \"cropper-crop\": t.crop,\n      \"cropper-modal\": t.cropping\n    }]),\n    onMousedown: e[0] || (e[0] = function () {\n      return t.startMove && t.startMove.apply(t, arguments);\n    }),\n    onTouchstart: e[1] || (e[1] = function () {\n      return t.startMove && t.startMove.apply(t, arguments);\n    })\n  }, null, 34), H(w(\"div\", {\n    class: \"cropper-crop-box\",\n    style: b({\n      width: t.cropW + \"px\",\n      height: t.cropH + \"px\",\n      transform: \"translate3d(\" + t.cropOffsertX + \"px,\" + t.cropOffsertY + \"px,0)\"\n    })\n  }, [w(\"span\", P, [w(\"img\", {\n    style: b({\n      width: t.trueWidth + \"px\",\n      height: t.trueHeight + \"px\",\n      transform: \"scale(\" + t.scale + \",\" + t.scale + \") translate3d(\" + (t.x - t.cropOffsertX) / t.scale + \"px,\" + (t.y - t.cropOffsertY) / t.scale + \"px,0)rotateZ(\" + t.rotate * 90 + \"deg)\"\n    }),\n    src: t.imgs,\n    alt: \"cropper-img\"\n  }, null, 12, R)]), w(\"span\", {\n    class: \"cropper-face cropper-move\",\n    onMousedown: e[2] || (e[2] = function () {\n      return t.cropMove && t.cropMove.apply(t, arguments);\n    }),\n    onTouchstart: e[3] || (e[3] = function () {\n      return t.cropMove && t.cropMove.apply(t, arguments);\n    })\n  }, null, 32), t.info ? (y(), x(\"span\", {\n    key: 0,\n    class: \"crop-info\",\n    style: b({\n      top: t.cropInfo.top\n    })\n  }, X(t.cropInfo.width) + \" × \" + X(t.cropInfo.height), 5)) : O(\"\", !0), t.fixedBox ? O(\"\", !0) : (y(), x(\"span\", D, [w(\"span\", {\n    class: \"crop-line line-w\",\n    onMousedown: e[4] || (e[4] = function (h) {\n      return t.changeCropSize(h, !1, !0, 0, 1);\n    }),\n    onTouchstart: e[5] || (e[5] = function (h) {\n      return t.changeCropSize(h, !1, !0, 0, 1);\n    })\n  }, null, 32), w(\"span\", {\n    class: \"crop-line line-a\",\n    onMousedown: e[6] || (e[6] = function (h) {\n      return t.changeCropSize(h, !0, !1, 1, 0);\n    }),\n    onTouchstart: e[7] || (e[7] = function (h) {\n      return t.changeCropSize(h, !0, !1, 1, 0);\n    })\n  }, null, 32), w(\"span\", {\n    class: \"crop-line line-s\",\n    onMousedown: e[8] || (e[8] = function (h) {\n      return t.changeCropSize(h, !1, !0, 0, 2);\n    }),\n    onTouchstart: e[9] || (e[9] = function (h) {\n      return t.changeCropSize(h, !1, !0, 0, 2);\n    })\n  }, null, 32), w(\"span\", {\n    class: \"crop-line line-d\",\n    onMousedown: e[10] || (e[10] = function (h) {\n      return t.changeCropSize(h, !0, !1, 2, 0);\n    }),\n    onTouchstart: e[11] || (e[11] = function (h) {\n      return t.changeCropSize(h, !0, !1, 2, 0);\n    })\n  }, null, 32), w(\"span\", {\n    class: \"crop-point point1\",\n    onMousedown: e[12] || (e[12] = function (h) {\n      return t.changeCropSize(h, !0, !0, 1, 1);\n    }),\n    onTouchstart: e[13] || (e[13] = function (h) {\n      return t.changeCropSize(h, !0, !0, 1, 1);\n    })\n  }, null, 32), w(\"span\", {\n    class: \"crop-point point2\",\n    onMousedown: e[14] || (e[14] = function (h) {\n      return t.changeCropSize(h, !1, !0, 0, 1);\n    }),\n    onTouchstart: e[15] || (e[15] = function (h) {\n      return t.changeCropSize(h, !1, !0, 0, 1);\n    })\n  }, null, 32), w(\"span\", {\n    class: \"crop-point point3\",\n    onMousedown: e[16] || (e[16] = function (h) {\n      return t.changeCropSize(h, !0, !0, 2, 1);\n    }),\n    onTouchstart: e[17] || (e[17] = function (h) {\n      return t.changeCropSize(h, !0, !0, 2, 1);\n    })\n  }, null, 32), w(\"span\", {\n    class: \"crop-point point4\",\n    onMousedown: e[18] || (e[18] = function (h) {\n      return t.changeCropSize(h, !0, !1, 1, 0);\n    }),\n    onTouchstart: e[19] || (e[19] = function (h) {\n      return t.changeCropSize(h, !0, !1, 1, 0);\n    })\n  }, null, 32), w(\"span\", {\n    class: \"crop-point point5\",\n    onMousedown: e[20] || (e[20] = function (h) {\n      return t.changeCropSize(h, !0, !1, 2, 0);\n    }),\n    onTouchstart: e[21] || (e[21] = function (h) {\n      return t.changeCropSize(h, !0, !1, 2, 0);\n    })\n  }, null, 32), w(\"span\", {\n    class: \"crop-point point6\",\n    onMousedown: e[22] || (e[22] = function (h) {\n      return t.changeCropSize(h, !0, !0, 1, 2);\n    }),\n    onTouchstart: e[23] || (e[23] = function (h) {\n      return t.changeCropSize(h, !0, !0, 1, 2);\n    })\n  }, null, 32), w(\"span\", {\n    class: \"crop-point point7\",\n    onMousedown: e[24] || (e[24] = function (h) {\n      return t.changeCropSize(h, !1, !0, 0, 2);\n    }),\n    onTouchstart: e[25] || (e[25] = function (h) {\n      return t.changeCropSize(h, !1, !0, 0, 2);\n    })\n  }, null, 32), w(\"span\", {\n    class: \"crop-point point8\",\n    onMousedown: e[26] || (e[26] = function (h) {\n      return t.changeCropSize(h, !0, !0, 2, 2);\n    }),\n    onTouchstart: e[27] || (e[27] = function (h) {\n      return t.changeCropSize(h, !0, !0, 2, 2);\n    })\n  }, null, 32)]))], 4), [[W, t.cropping]])], 544);\n}\nvar M = /* @__PURE__ */$(z, [[\"render\", U], [\"__scopeId\", \"data-v-a742df44\"]]),\n  F = function F(t) {\n    t.component(\"VueCropper\", M);\n  },\n  V = {\n    version: \"1.1.4\",\n    install: F,\n    VueCropper: M\n  };\nexport { M as VueCropper, V as default, V as globalCropper };", "map": {"version": 3, "names": ["defineComponent", "S", "openBlock", "y", "createElementBlock", "x", "withDirectives", "H", "createElementVNode", "w", "normalizeStyle", "b", "vShow", "W", "createCommentVNode", "O", "normalizeClass", "I", "toDisplayString", "X", "Y", "getData", "t", "Promise", "e", "i", "s", "L", "then", "r", "arrayBuffer", "orientation", "N", "_unused", "catch", "src", "test", "k", "FileReader", "onload", "h", "target", "result", "E", "readAsA<PERSON>y<PERSON><PERSON>er", "o", "XMLHttpRequest", "status", "response", "open", "responseType", "send", "match", "replace", "atob", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Uint16Array", "charCodeAt", "T", "String", "fromCharCode", "getUint8", "DataView", "byteLength", "a", "n", "c", "l", "f", "p", "getUint16", "getUint32", "$", "__vccOpts", "_iterator", "_createForOfIteratorHelper", "_step", "done", "_step$value", "_slicedToArray", "value", "err", "z", "data", "scale", "loading", "trueWidth", "trueHeight", "move", "moveX", "moveY", "crop", "cropping", "cropW", "cropH", "cropOldW", "cropOldH", "canChangeX", "canChangeY", "changeCropTypeX", "changeCropTypeY", "cropX", "cropY", "cropChangeX", "cropChangeY", "cropOffsertX", "cropOffsertY", "support", "touches", "touchNow", "rotate", "isIos", "imgs", "coe", "scaling", "scalingSet", "coeStatus", "isCanShow", "imgIsQqualCrop", "props", "img", "type", "Blob", "File", "default", "outputSize", "Number", "outputType", "info", "Boolean", "canScale", "autoCrop", "autoCropWidth", "autoCropHeight", "fixed", "fixedNumber", "Array", "fixedBox", "full", "canMove", "canMoveBox", "original", "centerBox", "high", "infoTrue", "maxImgSize", "enlarge", "preW", "mode", "limitMinSize", "validator", "isArray", "fillColor", "computed", "cropInfo", "top", "width", "height", "window", "devicePixelRatio", "Math", "abs", "toFixed", "isIE", "ActiveXObject", "passive", "isRotateRightOrLeft", "includes", "watch", "checkedImg", "reload", "showPreview", "goAutoCrop", "methods", "getVersion", "navigator", "userAgent", "split", "RegExp", "checkOrientationImage", "_this", "toLowerCase", "document", "createElement", "getContext", "save", "translate", "PI", "drawImage", "restore", "toBlob", "URL", "createObjectURL", "revokeObjectURL", "_this2", "clearCrop", "Image", "$emit", "Error", "onerror", "substr", "crossOrigin", "startMove", "preventDefault", "clientX", "clientY", "addEventListener", "moveImg", "leaveImg", "touchScale", "cancelTouchScale", "moving", "axis", "getImgAxis", "createCrop", "endCrop", "offsetX", "pageX", "$refs", "cropper", "offsetLeft", "offsetY", "pageY", "offsetTop", "_this3", "sqrt", "pow", "setTimeout", "checkoutImgAxis", "removeEventListener", "_this4", "$nextTick", "getCropAxis", "x1", "y1", "x2", "y2", "scaleImg", "changeSize", "cancelScale", "_this5", "deltaY", "wheelDelta", "indexOf", "changeScale", "_this6", "changeCropSize", "changeCropNow", "changeCropEnd", "_this7", "_this$checkCropLimitS", "checkCropLimitSize", "_this$checkCropLimitS2", "parseFloat", "calculateSize", "ceil", "_this$checkCropLimitS3", "_this$checkCropLimitS4", "_ref", "startCrop", "stopCrop", "cropMove", "leaveCrop", "moveCrop", "_this8", "getCropChecked", "_this9", "d", "C", "u", "g", "m", "v", "round", "fillStyle", "fillRect", "getCropData", "_this10", "toDataURL", "getCropBlob", "_this11", "_this12", "div", "url", "transform", "html", "_this13", "getComputedStyle", "checkedMode", "search", "min", "_unused2", "changeCrop", "_this14", "refresh", "_this15", "rotateLeft", "rotateRight", "rotateClear", "changeImgScale", "mounted", "onmousew<PERSON><PERSON>", "isIOS", "HTMLCanvasElement", "prototype", "Object", "defineProperty", "Uint8Array", "unmounted", "A", "key", "class", "B", "P", "R", "D", "U", "ref", "onMouseover", "apply", "arguments", "onMouseout", "style", "alt", "onMousedown", "onTouchstart", "M", "F", "component", "V", "version", "install", "VueCropper", "globalCropper"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/vue-cropper@1.1.4/node_modules/vue-cropper/dist/vue-cropper.es.js"], "sourcesContent": ["import { defineComponent as S, openBlock as y, createElementBlock as x, withDirectives as H, createElementVNode as w, normalizeStyle as b, vShow as W, createCommentVNode as O, normalizeClass as I, toDisplayString as X } from \"vue\";\nconst Y = {};\nY.getData = (t) => new Promise((e, i) => {\n  let s = {};\n  L(t).then((r) => {\n    s.arrayBuffer = r;\n    try {\n      s.orientation = N(r);\n    } catch {\n      s.orientation = -1;\n    }\n    e(s);\n  }).catch((r) => {\n    i(r);\n  });\n});\nfunction L(t) {\n  let e = null;\n  return new Promise((i, s) => {\n    if (t.src)\n      if (/^data\\:/i.test(t.src))\n        e = k(t.src), i(e);\n      else if (/^blob\\:/i.test(t.src)) {\n        var r = new FileReader();\n        r.onload = function(h) {\n          e = h.target.result, i(e);\n        }, E(t.src, function(h) {\n          r.readAsArrayBuffer(h);\n        });\n      } else {\n        var o = new XMLHttpRequest();\n        o.onload = function() {\n          if (this.status == 200 || this.status === 0)\n            e = o.response, i(e);\n          else\n            throw \"Could not load image\";\n          o = null;\n        }, o.open(\"GET\", t.src, !0), o.responseType = \"arraybuffer\", o.send(null);\n      }\n    else\n      s(\"img error\");\n  });\n}\nfunction E(t, e) {\n  var i = new XMLHttpRequest();\n  i.open(\"GET\", t, !0), i.responseType = \"blob\", i.onload = function(s) {\n    (this.status == 200 || this.status === 0) && e(this.response);\n  }, i.send();\n}\nfunction k(t, e) {\n  e = e || t.match(/^data\\:([^\\;]+)\\;base64,/mi)[1] || \"\", t = t.replace(/^data\\:([^\\;]+)\\;base64,/gmi, \"\");\n  for (var i = atob(t), s = i.length % 2 == 0 ? i.length : i.length + 1, r = new ArrayBuffer(s), o = new Uint16Array(r), h = 0; h < s; h++)\n    o[h] = i.charCodeAt(h);\n  return r;\n}\nfunction T(t, e, i) {\n  var s = \"\", r;\n  for (r = e, i += e; r < i; r++)\n    s += String.fromCharCode(t.getUint8(r));\n  return s;\n}\nfunction N(t) {\n  var e = new DataView(t), i = e.byteLength, s, r, o, h, a, n, c, l, f, p;\n  if (e.getUint8(0) === 255 && e.getUint8(1) === 216)\n    for (f = 2; f < i; ) {\n      if (e.getUint8(f) === 255 && e.getUint8(f + 1) === 225) {\n        c = f;\n        break;\n      }\n      f++;\n    }\n  if (c && (r = c + 4, o = c + 10, T(e, r, 4) === \"Exif\" && (n = e.getUint16(o), a = n === 18761, (a || n === 19789) && e.getUint16(o + 2, a) === 42 && (h = e.getUint32(o + 4, a), h >= 8 && (l = o + h)))), l) {\n    for (i = e.getUint16(l, a), p = 0; p < i; p++)\n      if (f = l + p * 12 + 2, e.getUint16(f, a) === 274) {\n        f += 8, s = e.getUint16(f, a);\n        break;\n      }\n  }\n  return s;\n}\nconst $ = (t, e) => {\n  const i = t.__vccOpts || t;\n  for (const [s, r] of e)\n    i[s] = r;\n  return i;\n}, z = S({\n  data: function() {\n    return {\n      // 容器高宽\n      w: 0,\n      h: 0,\n      // 图片缩放比例\n      scale: 1,\n      // 图片偏移x轴\n      x: 0,\n      // 图片偏移y轴\n      y: 0,\n      // 图片加载\n      loading: !0,\n      // 图片真实宽度\n      trueWidth: 0,\n      // 图片真实高度\n      trueHeight: 0,\n      move: !0,\n      // 移动的x\n      moveX: 0,\n      // 移动的y\n      moveY: 0,\n      // 开启截图\n      crop: !1,\n      // 正在截图\n      cropping: !1,\n      // 裁剪框大小\n      cropW: 0,\n      cropH: 0,\n      cropOldW: 0,\n      cropOldH: 0,\n      // 判断是否能够改变\n      canChangeX: !1,\n      canChangeY: !1,\n      // 改变的基准点\n      changeCropTypeX: 1,\n      changeCropTypeY: 1,\n      // 裁剪框的坐标轴\n      cropX: 0,\n      cropY: 0,\n      cropChangeX: 0,\n      cropChangeY: 0,\n      cropOffsertX: 0,\n      cropOffsertY: 0,\n      // 支持的滚动事件\n      support: \"\",\n      // 移动端手指缩放\n      touches: [],\n      touchNow: !1,\n      // 图片旋转\n      rotate: 0,\n      isIos: !1,\n      orientation: 0,\n      imgs: \"\",\n      // 图片缩放系数\n      coe: 0.2,\n      // 是否正在多次缩放\n      scaling: !1,\n      scalingSet: \"\",\n      coeStatus: \"\",\n      // 控制emit触发频率\n      isCanShow: !0,\n      // 图片是否等于截图大小\n      imgIsQqualCrop: !1\n    };\n  },\n  props: {\n    img: {\n      type: [String, Blob, null, File],\n      default: \"\"\n    },\n    // 输出图片压缩比\n    outputSize: {\n      type: Number,\n      default: 1\n    },\n    outputType: {\n      type: String,\n      default: \"jpeg\"\n    },\n    info: {\n      type: Boolean,\n      default: !0\n    },\n    // 是否开启滚轮放大缩小\n    canScale: {\n      type: Boolean,\n      default: !0\n    },\n    // 是否自成截图框\n    autoCrop: {\n      type: Boolean,\n      default: !1\n    },\n    autoCropWidth: {\n      type: [Number, String],\n      default: 0\n    },\n    autoCropHeight: {\n      type: [Number, String],\n      default: 0\n    },\n    // 是否开启固定宽高比\n    fixed: {\n      type: Boolean,\n      default: !1\n    },\n    // 宽高比 w/h\n    fixedNumber: {\n      type: Array,\n      default: () => [1, 1]\n    },\n    // 固定大小 禁止改变截图框大小\n    fixedBox: {\n      type: Boolean,\n      default: !1\n    },\n    // 输出截图是否缩放\n    full: {\n      type: Boolean,\n      default: !1\n    },\n    // 是否可以拖动图片\n    canMove: {\n      type: Boolean,\n      default: !0\n    },\n    // 是否可以拖动截图框\n    canMoveBox: {\n      type: Boolean,\n      default: !0\n    },\n    // 上传图片按照原始比例显示\n    original: {\n      type: Boolean,\n      default: !1\n    },\n    // 截图框能否超过图片\n    centerBox: {\n      type: Boolean,\n      default: !1\n    },\n    // 是否根据dpr输出高清图片\n    high: {\n      type: Boolean,\n      default: !0\n    },\n    // 截图框展示宽高类型\n    infoTrue: {\n      type: Boolean,\n      default: !1\n    },\n    // 可以压缩图片宽高  默认不超过200\n    maxImgSize: {\n      type: [Number, String],\n      default: 2e3\n    },\n    // 倍数  可渲染当前截图框的n倍 0 - 1000;\n    enlarge: {\n      type: [Number, String],\n      default: 1\n    },\n    // 自动预览的固定宽度\n    preW: {\n      type: [Number, String],\n      default: 0\n    },\n    /*\n      图片布局方式 mode 实现和css背景一样的效果\n      contain  居中布局 默认不会缩放 保证图片在容器里面 mode: 'contain'\n      cover    拉伸布局 填充整个容器  mode: 'cover'\n      如果仅有一个数值被给定，这个数值将作为宽度值大小，高度值将被设定为auto。 mode: '50px'\n      如果有两个数值被给定，第一个将作为宽度值大小，第二个作为高度值大小。 mode: '50px 60px'\n    */\n    mode: {\n      type: String,\n      default: \"contain\"\n    },\n    //限制最小区域,可传1以上的数字和字符串，限制长宽都是这么大\n    // 也可以传数组[90,90] \n    limitMinSize: {\n      type: [Number, Array, String],\n      default: () => 10,\n      validator: function(t) {\n        return Array.isArray(t) ? Number(t[0]) >= 0 && Number(t[1]) >= 0 : Number(t) >= 0;\n      }\n    },\n    // 导出时,填充背景颜色\n    fillColor: {\n      type: String,\n      default: \"\"\n    }\n  },\n  computed: {\n    cropInfo() {\n      let t = {};\n      if (t.top = this.cropOffsertY > 21 ? \"-21px\" : \"0px\", t.width = this.cropW > 0 ? this.cropW : 0, t.height = this.cropH > 0 ? this.cropH : 0, this.infoTrue) {\n        let e = 1;\n        this.high && !this.full && (e = window.devicePixelRatio), this.enlarge !== 1 & !this.full && (e = Math.abs(Number(this.enlarge))), t.width = t.width * e, t.height = t.height * e, this.full && (t.width = t.width / this.scale, t.height = t.height / this.scale);\n      }\n      return t.width = t.width.toFixed(0), t.height = t.height.toFixed(0), t;\n    },\n    isIE() {\n      return !!window.ActiveXObject || \"ActiveXObject\" in window;\n    },\n    passive() {\n      return this.isIE ? null : {\n        passive: !1\n      };\n    },\n    // 是否处于左右旋转\n    isRotateRightOrLeft() {\n      return [1, -1, 3, -3].includes(this.rotate);\n    }\n  },\n  watch: {\n    // 如果图片改变， 重新布局\n    img() {\n      this.checkedImg();\n    },\n    imgs(t) {\n      t !== \"\" && this.reload();\n    },\n    cropW() {\n      this.showPreview();\n    },\n    cropH() {\n      this.showPreview();\n    },\n    cropOffsertX() {\n      this.showPreview();\n    },\n    cropOffsertY() {\n      this.showPreview();\n    },\n    scale(t, e) {\n      this.showPreview();\n    },\n    x() {\n      this.showPreview();\n    },\n    y() {\n      this.showPreview();\n    },\n    autoCrop(t) {\n      t && this.goAutoCrop();\n    },\n    // 修改了自动截图框\n    autoCropWidth() {\n      this.autoCrop && this.goAutoCrop();\n    },\n    autoCropHeight() {\n      this.autoCrop && this.goAutoCrop();\n    },\n    mode() {\n      this.checkedImg();\n    },\n    rotate() {\n      this.showPreview(), this.autoCrop ? this.goAutoCrop(this.cropW, this.cropH) : (this.cropW > 0 || this.cropH > 0) && this.goAutoCrop(this.cropW, this.cropH);\n    }\n  },\n  methods: {\n    getVersion(t) {\n      var e = navigator.userAgent.split(\" \"), i = \"\";\n      let s = 0;\n      const r = new RegExp(t, \"i\");\n      for (var o = 0; o < e.length; o++)\n        r.test(e[o]) && (i = e[o]);\n      return i ? s = i.split(\"/\")[1].split(\".\") : s = [\"0\", \"0\", \"0\"], s;\n    },\n    checkOrientationImage(t, e, i, s) {\n      if (this.getVersion(\"chrome\")[0] >= 81)\n        e = -1;\n      else if (this.getVersion(\"safari\")[0] >= 605) {\n        const h = this.getVersion(\"version\");\n        h[0] > 13 && h[1] > 1 && (e = -1);\n      } else {\n        const h = navigator.userAgent.toLowerCase().match(/cpu iphone os (.*?) like mac os/);\n        if (h) {\n          let a = h[1];\n          a = a.split(\"_\"), (a[0] > 13 || a[0] >= 13 && a[1] >= 4) && (e = -1);\n        }\n      }\n      let r = document.createElement(\"canvas\"), o = r.getContext(\"2d\");\n      switch (o.save(), e) {\n        case 2:\n          r.width = i, r.height = s, o.translate(i, 0), o.scale(-1, 1);\n          break;\n        case 3:\n          r.width = i, r.height = s, o.translate(i / 2, s / 2), o.rotate(180 * Math.PI / 180), o.translate(-i / 2, -s / 2);\n          break;\n        case 4:\n          r.width = i, r.height = s, o.translate(0, s), o.scale(1, -1);\n          break;\n        case 5:\n          r.height = i, r.width = s, o.rotate(0.5 * Math.PI), o.scale(1, -1);\n          break;\n        case 6:\n          r.width = s, r.height = i, o.translate(s / 2, i / 2), o.rotate(90 * Math.PI / 180), o.translate(-i / 2, -s / 2);\n          break;\n        case 7:\n          r.height = i, r.width = s, o.rotate(0.5 * Math.PI), o.translate(i, -s), o.scale(-1, 1);\n          break;\n        case 8:\n          r.height = i, r.width = s, o.translate(s / 2, i / 2), o.rotate(-90 * Math.PI / 180), o.translate(-i / 2, -s / 2);\n          break;\n        default:\n          r.width = i, r.height = s;\n      }\n      o.drawImage(t, 0, 0, i, s), o.restore(), r.toBlob(\n        (h) => {\n          let a = URL.createObjectURL(h);\n          URL.revokeObjectURL(this.imgs), this.imgs = a;\n        },\n        \"image/\" + this.outputType,\n        1\n      );\n    },\n    // checkout img\n    checkedImg() {\n      if (this.img === null || this.img === \"\") {\n        this.imgs = \"\", this.clearCrop();\n        return;\n      }\n      this.loading = !0, this.scale = 1, this.rotate = 0, this.imgIsQqualCrop = !1, this.clearCrop();\n      let t = new Image();\n      if (t.onload = () => {\n        if (this.img === \"\")\n          return this.$emit(\"img-load\", new Error(\"图片不能为空\")), !1;\n        let i = t.width, s = t.height;\n        Y.getData(t).then((r) => {\n          this.orientation = r.orientation || 1;\n          let o = Number(this.maxImgSize);\n          if (!this.orientation && i < o & s < o) {\n            this.imgs = this.img;\n            return;\n          }\n          i > o && (s = s / i * o, i = o), s > o && (i = i / s * o, s = o), this.checkOrientationImage(t, this.orientation, i, s);\n        }).catch((r) => {\n          this.$emit(\"img-load\", \"error\"), this.$emit(\"img-load-error\", r);\n        });\n      }, t.onerror = (i) => {\n        this.$emit(\"img-load\", \"error\"), this.$emit(\"img-load-error\", i);\n      }, this.img.substr(0, 4) !== \"data\" && (t.crossOrigin = \"\"), this.isIE) {\n        var e = new XMLHttpRequest();\n        e.onload = function() {\n          var i = URL.createObjectURL(this.response);\n          t.src = i;\n        }, e.open(\"GET\", this.img, !0), e.responseType = \"blob\", e.send();\n      } else\n        t.src = this.img;\n    },\n    // 当按下鼠标键\n    startMove(t) {\n      if (t.preventDefault(), this.move && !this.crop) {\n        if (!this.canMove)\n          return !1;\n        this.moveX = (\"clientX\" in t ? t.clientX : t.touches[0].clientX) - this.x, this.moveY = (\"clientY\" in t ? t.clientY : t.touches[0].clientY) - this.y, t.touches ? (window.addEventListener(\"touchmove\", this.moveImg), window.addEventListener(\"touchend\", this.leaveImg), t.touches.length == 2 && (this.touches = t.touches, window.addEventListener(\"touchmove\", this.touchScale), window.addEventListener(\"touchend\", this.cancelTouchScale))) : (window.addEventListener(\"mousemove\", this.moveImg), window.addEventListener(\"mouseup\", this.leaveImg)), this.$emit(\"img-moving\", {\n          moving: !0,\n          axis: this.getImgAxis()\n        });\n      } else\n        this.cropping = !0, window.addEventListener(\"mousemove\", this.createCrop), window.addEventListener(\"mouseup\", this.endCrop), window.addEventListener(\"touchmove\", this.createCrop), window.addEventListener(\"touchend\", this.endCrop), this.cropOffsertX = t.offsetX ? t.offsetX : t.touches[0].pageX - this.$refs.cropper.offsetLeft, this.cropOffsertY = t.offsetY ? t.offsetY : t.touches[0].pageY - this.$refs.cropper.offsetTop, this.cropX = \"clientX\" in t ? t.clientX : t.touches[0].clientX, this.cropY = \"clientY\" in t ? t.clientY : t.touches[0].clientY, this.cropChangeX = this.cropOffsertX, this.cropChangeY = this.cropOffsertY, this.cropW = 0, this.cropH = 0;\n    },\n    // 移动端缩放\n    touchScale(t) {\n      t.preventDefault();\n      let e = this.scale;\n      var i = {\n        x: this.touches[0].clientX,\n        y: this.touches[0].clientY\n      }, s = {\n        x: t.touches[0].clientX,\n        y: t.touches[0].clientY\n      }, r = {\n        x: this.touches[1].clientX,\n        y: this.touches[1].clientY\n      }, o = {\n        x: t.touches[1].clientX,\n        y: t.touches[1].clientY\n      }, h = Math.sqrt(\n        Math.pow(i.x - r.x, 2) + Math.pow(i.y - r.y, 2)\n      ), a = Math.sqrt(\n        Math.pow(s.x - o.x, 2) + Math.pow(s.y - o.y, 2)\n      ), n = a - h, c = 1;\n      c = c / this.trueWidth > c / this.trueHeight ? c / this.trueHeight : c / this.trueWidth, c = c > 0.1 ? 0.1 : c;\n      var l = c * n;\n      if (!this.touchNow) {\n        if (this.touchNow = !0, n > 0 ? e += Math.abs(l) : n < 0 && e > Math.abs(l) && (e -= Math.abs(l)), this.touches = t.touches, setTimeout(() => {\n          this.touchNow = !1;\n        }, 8), !this.checkoutImgAxis(this.x, this.y, e))\n          return !1;\n        this.scale = e;\n      }\n    },\n    cancelTouchScale(t) {\n      window.removeEventListener(\"touchmove\", this.touchScale);\n    },\n    // 移动图片\n    moveImg(t) {\n      if (t.preventDefault(), t.touches && t.touches.length === 2)\n        return this.touches = t.touches, window.addEventListener(\"touchmove\", this.touchScale), window.addEventListener(\"touchend\", this.cancelTouchScale), window.removeEventListener(\"touchmove\", this.moveImg), !1;\n      let e = \"clientX\" in t ? t.clientX : t.touches[0].clientX, i = \"clientY\" in t ? t.clientY : t.touches[0].clientY, s, r;\n      s = e - this.moveX, r = i - this.moveY, this.$nextTick(() => {\n        if (this.centerBox) {\n          let o = this.getImgAxis(s, r, this.scale), h = this.getCropAxis(), a = this.trueHeight * this.scale, n = this.trueWidth * this.scale, c, l, f, p;\n          switch (this.rotate) {\n            case 1:\n            case -1:\n            case 3:\n            case -3:\n              c = this.cropOffsertX - this.trueWidth * (1 - this.scale) / 2 + (a - n) / 2, l = this.cropOffsertY - this.trueHeight * (1 - this.scale) / 2 + (n - a) / 2, f = c - a + this.cropW, p = l - n + this.cropH;\n              break;\n            default:\n              c = this.cropOffsertX - this.trueWidth * (1 - this.scale) / 2, l = this.cropOffsertY - this.trueHeight * (1 - this.scale) / 2, f = c - n + this.cropW, p = l - a + this.cropH;\n              break;\n          }\n          o.x1 >= h.x1 && (s = c), o.y1 >= h.y1 && (r = l), o.x2 <= h.x2 && (s = f), o.y2 <= h.y2 && (r = p);\n        }\n        this.x = s, this.y = r, this.$emit(\"img-moving\", {\n          moving: !0,\n          axis: this.getImgAxis()\n        });\n      });\n    },\n    // 移动图片结束\n    leaveImg(t) {\n      window.removeEventListener(\"mousemove\", this.moveImg), window.removeEventListener(\"touchmove\", this.moveImg), window.removeEventListener(\"mouseup\", this.leaveImg), window.removeEventListener(\"touchend\", this.leaveImg), this.$emit(\"img-moving\", {\n        moving: !1,\n        axis: this.getImgAxis()\n      });\n    },\n    // 缩放图片\n    scaleImg() {\n      this.canScale && window.addEventListener(this.support, this.changeSize, this.passive);\n    },\n    // 移出框\n    cancelScale() {\n      this.canScale && window.removeEventListener(this.support, this.changeSize);\n    },\n    // 改变大小函数\n    changeSize(t) {\n      t.preventDefault();\n      let e = this.scale;\n      var i = t.deltaY || t.wheelDelta, s = navigator.userAgent.indexOf(\"Firefox\");\n      i = s > 0 ? i * 30 : i, this.isIE && (i = -i);\n      var r = this.coe;\n      r = r / this.trueWidth > r / this.trueHeight ? r / this.trueHeight : r / this.trueWidth;\n      var o = r * i;\n      o < 0 ? e += Math.abs(o) : e > Math.abs(o) && (e -= Math.abs(o));\n      let h = o < 0 ? \"add\" : \"reduce\";\n      if (h !== this.coeStatus && (this.coeStatus = h, this.coe = 0.2), this.scaling || (this.scalingSet = setTimeout(() => {\n        this.scaling = !1, this.coe = this.coe += 0.01;\n      }, 50)), this.scaling = !0, !this.checkoutImgAxis(this.x, this.y, e))\n        return !1;\n      this.scale = e;\n    },\n    // 修改图片大小函数\n    changeScale(t) {\n      let e = this.scale;\n      t = t || 1;\n      var i = 20;\n      if (i = i / this.trueWidth > i / this.trueHeight ? i / this.trueHeight : i / this.trueWidth, t = t * i, t > 0 ? e += Math.abs(t) : e > Math.abs(t) && (e -= Math.abs(t)), !this.checkoutImgAxis(this.x, this.y, e))\n        return !1;\n      this.scale = e;\n    },\n    // 创建截图框\n    createCrop(t) {\n      t.preventDefault();\n      var e = \"clientX\" in t ? t.clientX : t.touches ? t.touches[0].clientX : 0, i = \"clientY\" in t ? t.clientY : t.touches ? t.touches[0].clientY : 0;\n      this.$nextTick(() => {\n        var s = e - this.cropX, r = i - this.cropY;\n        if (s > 0 ? (this.cropW = s + this.cropChangeX > this.w ? this.w - this.cropChangeX : s, this.cropOffsertX = this.cropChangeX) : (this.cropW = this.w - this.cropChangeX + Math.abs(s) > this.w ? this.cropChangeX : Math.abs(s), this.cropOffsertX = this.cropChangeX + s > 0 ? this.cropChangeX + s : 0), !this.fixed)\n          r > 0 ? (this.cropH = r + this.cropChangeY > this.h ? this.h - this.cropChangeY : r, this.cropOffsertY = this.cropChangeY) : (this.cropH = this.h - this.cropChangeY + Math.abs(r) > this.h ? this.cropChangeY : Math.abs(r), this.cropOffsertY = this.cropChangeY + r > 0 ? this.cropChangeY + r : 0);\n        else {\n          var o = this.cropW / this.fixedNumber[0] * this.fixedNumber[1];\n          o + this.cropOffsertY > this.h ? (this.cropH = this.h - this.cropOffsertY, this.cropW = this.cropH / this.fixedNumber[1] * this.fixedNumber[0], s > 0 ? this.cropOffsertX = this.cropChangeX : this.cropOffsertX = this.cropChangeX - this.cropW) : this.cropH = o, this.cropOffsertY = this.cropOffsertY;\n        }\n      });\n    },\n    // 改变截图框大小\n    changeCropSize(t, e, i, s, r) {\n      t.preventDefault(), window.addEventListener(\"mousemove\", this.changeCropNow), window.addEventListener(\"mouseup\", this.changeCropEnd), window.addEventListener(\"touchmove\", this.changeCropNow), window.addEventListener(\"touchend\", this.changeCropEnd), this.canChangeX = e, this.canChangeY = i, this.changeCropTypeX = s, this.changeCropTypeY = r, this.cropX = \"clientX\" in t ? t.clientX : t.touches[0].clientX, this.cropY = \"clientY\" in t ? t.clientY : t.touches[0].clientY, this.cropOldW = this.cropW, this.cropOldH = this.cropH, this.cropChangeX = this.cropOffsertX, this.cropChangeY = this.cropOffsertY, this.fixed && this.canChangeX && this.canChangeY && (this.canChangeY = 0), this.$emit(\"change-crop-size\", {\n        width: this.cropW,\n        height: this.cropH\n      });\n    },\n    // 正在改变\n    changeCropNow(t) {\n      t.preventDefault();\n      var e = \"clientX\" in t ? t.clientX : t.touches ? t.touches[0].clientX : 0, i = \"clientY\" in t ? t.clientY : t.touches ? t.touches[0].clientY : 0;\n      let s = this.w, r = this.h, o = 0, h = 0;\n      if (this.centerBox) {\n        let c = this.getImgAxis(), l = c.x2, f = c.y2;\n        o = c.x1 > 0 ? c.x1 : 0, h = c.y1 > 0 ? c.y1 : 0, s > l && (s = l), r > f && (r = f);\n      }\n      const [a, n] = this.checkCropLimitSize();\n      this.$nextTick(() => {\n        var c = e - this.cropX, l = i - this.cropY;\n        if (this.canChangeX && (this.changeCropTypeX === 1 ? this.cropOldW - c < a ? (this.cropW = a, this.cropOffsertX = this.cropOldW + this.cropChangeX - o - a) : this.cropOldW - c > 0 ? (this.cropW = s - this.cropChangeX - c <= s - o ? this.cropOldW - c : this.cropOldW + this.cropChangeX - o, this.cropOffsertX = s - this.cropChangeX - c <= s - o ? this.cropChangeX + c : o) : (this.cropW = Math.abs(c) + this.cropChangeX <= s ? Math.abs(c) - this.cropOldW : s - this.cropOldW - this.cropChangeX, this.cropOffsertX = this.cropChangeX + this.cropOldW) : this.changeCropTypeX === 2 && (this.cropOldW + c < a ? this.cropW = a : this.cropOldW + c > 0 ? (this.cropW = this.cropOldW + c + this.cropOffsertX <= s ? this.cropOldW + c : s - this.cropOffsertX, this.cropOffsertX = this.cropChangeX) : (this.cropW = s - this.cropChangeX + Math.abs(c + this.cropOldW) <= s - o ? Math.abs(c + this.cropOldW) : this.cropChangeX - o, this.cropOffsertX = s - this.cropChangeX + Math.abs(c + this.cropOldW) <= s - o ? this.cropChangeX - Math.abs(c + this.cropOldW) : o))), this.canChangeY && (this.changeCropTypeY === 1 ? this.cropOldH - l < n ? (this.cropH = n, this.cropOffsertY = this.cropOldH + this.cropChangeY - h - n) : this.cropOldH - l > 0 ? (this.cropH = r - this.cropChangeY - l <= r - h ? this.cropOldH - l : this.cropOldH + this.cropChangeY - h, this.cropOffsertY = r - this.cropChangeY - l <= r - h ? this.cropChangeY + l : h) : (this.cropH = Math.abs(l) + this.cropChangeY <= r ? Math.abs(l) - this.cropOldH : r - this.cropOldH - this.cropChangeY, this.cropOffsertY = this.cropChangeY + this.cropOldH) : this.changeCropTypeY === 2 && (this.cropOldH + l < n ? this.cropH = n : this.cropOldH + l > 0 ? (this.cropH = this.cropOldH + l + this.cropOffsertY <= r ? this.cropOldH + l : r - this.cropOffsertY, this.cropOffsertY = this.cropChangeY) : (this.cropH = r - this.cropChangeY + Math.abs(l + this.cropOldH) <= r - h ? Math.abs(l + this.cropOldH) : this.cropChangeY - h, this.cropOffsertY = r - this.cropChangeY + Math.abs(l + this.cropOldH) <= r - h ? this.cropChangeY - Math.abs(l + this.cropOldH) : h))), this.canChangeX && this.fixed) {\n          var f = this.cropW / this.fixedNumber[0] * this.fixedNumber[1];\n          f < n ? (this.cropH = n, this.cropW = this.fixedNumber[0] * n / this.fixedNumber[1], this.changeCropTypeX === 1 && (this.cropOffsertX = this.cropChangeX + (this.cropOldW - this.cropW))) : f + this.cropOffsertY > r ? (this.cropH = r - this.cropOffsertY, this.cropW = this.cropH / this.fixedNumber[1] * this.fixedNumber[0], this.changeCropTypeX === 1 && (this.cropOffsertX = this.cropChangeX + (this.cropOldW - this.cropW))) : this.cropH = f;\n        }\n        if (this.canChangeY && this.fixed) {\n          var p = this.cropH / this.fixedNumber[1] * this.fixedNumber[0];\n          p < a ? (this.cropW = a, this.cropH = this.fixedNumber[1] * a / this.fixedNumber[0], this.cropOffsertY = this.cropOldH + this.cropChangeY - this.cropH) : p + this.cropOffsertX > s ? (this.cropW = s - this.cropOffsertX, this.cropH = this.cropW / this.fixedNumber[0] * this.fixedNumber[1]) : this.cropW = p;\n        }\n      });\n    },\n    checkCropLimitSize() {\n      let { cropW: t, cropH: e, limitMinSize: i } = this, s = new Array();\n      return Array.isArray(i) ? s = i : s = [i, i], t = parseFloat(s[0]), e = parseFloat(s[1]), [t, e];\n    },\n    // 结束改变\n    changeCropEnd(t) {\n      window.removeEventListener(\"mousemove\", this.changeCropNow), window.removeEventListener(\"mouseup\", this.changeCropEnd), window.removeEventListener(\"touchmove\", this.changeCropNow), window.removeEventListener(\"touchend\", this.changeCropEnd);\n    },\n    // 根据比例x/y，最小宽度，最小高度，现有宽度，现有高度，得到应该有的宽度和高度\n    calculateSize(t, e, i, s, r, o) {\n      const h = t / e;\n      let a = r, n = o;\n      return a < i && (a = i, n = Math.ceil(a / h)), n < s && (n = s, a = Math.ceil(n * h), a < i && (a = i, n = Math.ceil(a / h))), a < r && (a = r, n = Math.ceil(a / h)), n < o && (n = o, a = Math.ceil(n * h)), { width: a, height: n };\n    },\n    // 创建完成\n    endCrop() {\n      this.cropW === 0 && this.cropH === 0 && (this.cropping = !1);\n      let [t, e] = this.checkCropLimitSize();\n      const { width: i, height: s } = this.fixed ? this.calculateSize(\n        this.fixedNumber[0],\n        this.fixedNumber[1],\n        t,\n        e,\n        this.cropW,\n        this.cropH\n      ) : { width: t, height: e };\n      i > this.cropW && (this.cropW = i, this.cropOffsertX + i > this.w && (this.cropOffsertX = this.w - i)), s > this.cropH && (this.cropH = s, this.cropOffsertY + s > this.h && (this.cropOffsertY = this.h - s)), window.removeEventListener(\"mousemove\", this.createCrop), window.removeEventListener(\"mouseup\", this.endCrop), window.removeEventListener(\"touchmove\", this.createCrop), window.removeEventListener(\"touchend\", this.endCrop);\n    },\n    // 开始截图\n    startCrop() {\n      this.crop = !0;\n    },\n    // 停止截图\n    stopCrop() {\n      this.crop = !1;\n    },\n    // 清除截图\n    clearCrop() {\n      this.cropping = !1, this.cropW = 0, this.cropH = 0;\n    },\n    // 截图移动\n    cropMove(t) {\n      if (t.preventDefault(), !this.canMoveBox)\n        return this.crop = !1, this.startMove(t), !1;\n      if (t.touches && t.touches.length === 2)\n        return this.crop = !1, this.startMove(t), this.leaveCrop(), !1;\n      window.addEventListener(\"mousemove\", this.moveCrop), window.addEventListener(\"mouseup\", this.leaveCrop), window.addEventListener(\"touchmove\", this.moveCrop), window.addEventListener(\"touchend\", this.leaveCrop);\n      let e = \"clientX\" in t ? t.clientX : t.touches[0].clientX, i = \"clientY\" in t ? t.clientY : t.touches[0].clientY, s, r;\n      s = e - this.cropOffsertX, r = i - this.cropOffsertY, this.cropX = s, this.cropY = r, this.$emit(\"crop-moving\", {\n        moving: !0,\n        axis: this.getCropAxis()\n      });\n    },\n    moveCrop(t, e) {\n      let i = 0, s = 0;\n      t && (t.preventDefault(), i = \"clientX\" in t ? t.clientX : t.touches[0].clientX, s = \"clientY\" in t ? t.clientY : t.touches[0].clientY), this.$nextTick(() => {\n        let r, o, h = i - this.cropX, a = s - this.cropY;\n        if (e && (h = this.cropOffsertX, a = this.cropOffsertY), h <= 0 ? r = 0 : h + this.cropW > this.w ? r = this.w - this.cropW : r = h, a <= 0 ? o = 0 : a + this.cropH > this.h ? o = this.h - this.cropH : o = a, this.centerBox) {\n          let n = this.getImgAxis();\n          r <= n.x1 && (r = n.x1), r + this.cropW > n.x2 && (r = n.x2 - this.cropW), o <= n.y1 && (o = n.y1), o + this.cropH > n.y2 && (o = n.y2 - this.cropH);\n        }\n        this.cropOffsertX = r, this.cropOffsertY = o, this.$emit(\"crop-moving\", {\n          moving: !0,\n          axis: this.getCropAxis()\n        });\n      });\n    },\n    // 算出不同场景下面 图片相对于外层容器的坐标轴\n    getImgAxis(t, e, i) {\n      t = t || this.x, e = e || this.y, i = i || this.scale;\n      let s = {\n        x1: 0,\n        x2: 0,\n        y1: 0,\n        y2: 0\n      }, r = this.trueWidth * i, o = this.trueHeight * i;\n      switch (this.rotate) {\n        case 0:\n          s.x1 = t + this.trueWidth * (1 - i) / 2, s.x2 = s.x1 + this.trueWidth * i, s.y1 = e + this.trueHeight * (1 - i) / 2, s.y2 = s.y1 + this.trueHeight * i;\n          break;\n        case 1:\n        case -1:\n        case 3:\n        case -3:\n          s.x1 = t + this.trueWidth * (1 - i) / 2 + (r - o) / 2, s.x2 = s.x1 + this.trueHeight * i, s.y1 = e + this.trueHeight * (1 - i) / 2 + (o - r) / 2, s.y2 = s.y1 + this.trueWidth * i;\n          break;\n        default:\n          s.x1 = t + this.trueWidth * (1 - i) / 2, s.x2 = s.x1 + this.trueWidth * i, s.y1 = e + this.trueHeight * (1 - i) / 2, s.y2 = s.y1 + this.trueHeight * i;\n          break;\n      }\n      return s;\n    },\n    // 获取截图框的坐标轴\n    getCropAxis() {\n      let t = {\n        x1: 0,\n        x2: 0,\n        y1: 0,\n        y2: 0\n      };\n      return t.x1 = this.cropOffsertX, t.x2 = t.x1 + this.cropW, t.y1 = this.cropOffsertY, t.y2 = t.y1 + this.cropH, t;\n    },\n    leaveCrop(t) {\n      window.removeEventListener(\"mousemove\", this.moveCrop), window.removeEventListener(\"mouseup\", this.leaveCrop), window.removeEventListener(\"touchmove\", this.moveCrop), window.removeEventListener(\"touchend\", this.leaveCrop), this.$emit(\"crop-moving\", {\n        moving: !1,\n        axis: this.getCropAxis()\n      });\n    },\n    getCropChecked(t) {\n      let e = document.createElement(\"canvas\"), i = e.getContext(\"2d\"), s = new Image(), r = this.rotate, o = this.trueWidth, h = this.trueHeight, a = this.cropOffsertX, n = this.cropOffsertY;\n      s.onload = () => {\n        if (this.cropW !== 0) {\n          let p = 1;\n          this.high & !this.full && (p = window.devicePixelRatio), this.enlarge !== 1 & !this.full && (p = Math.abs(Number(this.enlarge)));\n          let d = this.cropW * p, C = this.cropH * p, u = o * this.scale * p, g = h * this.scale * p, m = (this.x - a + this.trueWidth * (1 - this.scale) / 2) * p, v = (this.y - n + this.trueHeight * (1 - this.scale) / 2) * p;\n          switch (f(d, C), i.save(), r) {\n            case 0:\n              this.full ? (f(d / this.scale, C / this.scale), i.drawImage(\n                s,\n                m / this.scale,\n                v / this.scale,\n                u / this.scale,\n                g / this.scale\n              )) : i.drawImage(s, m, v, u, g);\n              break;\n            case 1:\n            case -3:\n              this.full ? (f(d / this.scale, C / this.scale), m = m / this.scale + (u / this.scale - g / this.scale) / 2, v = v / this.scale + (g / this.scale - u / this.scale) / 2, i.rotate(r * 90 * Math.PI / 180), i.drawImage(\n                s,\n                v,\n                -m - g / this.scale,\n                u / this.scale,\n                g / this.scale\n              )) : (m = m + (u - g) / 2, v = v + (g - u) / 2, i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, v, -m - g, u, g));\n              break;\n            case 2:\n            case -2:\n              this.full ? (f(d / this.scale, C / this.scale), i.rotate(r * 90 * Math.PI / 180), m = m / this.scale, v = v / this.scale, i.drawImage(\n                s,\n                -m - u / this.scale,\n                -v - g / this.scale,\n                u / this.scale,\n                g / this.scale\n              )) : (i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, -m - u, -v - g, u, g));\n              break;\n            case 3:\n            case -1:\n              this.full ? (f(d / this.scale, C / this.scale), m = m / this.scale + (u / this.scale - g / this.scale) / 2, v = v / this.scale + (g / this.scale - u / this.scale) / 2, i.rotate(r * 90 * Math.PI / 180), i.drawImage(\n                s,\n                -v - u / this.scale,\n                m,\n                u / this.scale,\n                g / this.scale\n              )) : (m = m + (u - g) / 2, v = v + (g - u) / 2, i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, -v - u, m, u, g));\n              break;\n            default:\n              this.full ? (f(d / this.scale, C / this.scale), i.drawImage(\n                s,\n                m / this.scale,\n                v / this.scale,\n                u / this.scale,\n                g / this.scale\n              )) : i.drawImage(s, m, v, u, g);\n          }\n          i.restore();\n        } else {\n          let p = o * this.scale, d = h * this.scale;\n          switch (i.save(), r) {\n            case 0:\n              f(p, d), i.drawImage(s, 0, 0, p, d);\n              break;\n            case 1:\n            case -3:\n              f(d, p), i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, 0, -d, p, d);\n              break;\n            case 2:\n            case -2:\n              f(p, d), i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, -p, -d, p, d);\n              break;\n            case 3:\n            case -1:\n              f(d, p), i.rotate(r * 90 * Math.PI / 180), i.drawImage(s, -p, 0, p, d);\n              break;\n            default:\n              f(p, d), i.drawImage(s, 0, 0, p, d);\n          }\n          i.restore();\n        }\n        t(e);\n      };\n      var c = this.img.substr(0, 4);\n      c !== \"data\" && (s.crossOrigin = \"Anonymous\"), s.src = this.imgs;\n      const l = this.fillColor;\n      function f(p, d) {\n        e.width = Math.round(p), e.height = Math.round(d), l && (i.fillStyle = l, i.fillRect(0, 0, e.width, e.height));\n      }\n    },\n    // 获取转换成base64 的图片信息\n    getCropData(t) {\n      this.getCropChecked((e) => {\n        t(e.toDataURL(\"image/\" + this.outputType, this.outputSize));\n      });\n    },\n    //canvas获取为blob对象\n    getCropBlob(t) {\n      this.getCropChecked((e) => {\n        e.toBlob(\n          (i) => t(i),\n          \"image/\" + this.outputType,\n          this.outputSize\n        );\n      });\n    },\n    // 自动预览函数\n    showPreview() {\n      if (this.isCanShow)\n        this.isCanShow = !1, setTimeout(() => {\n          this.isCanShow = !0;\n        }, 16);\n      else\n        return !1;\n      let t = this.cropW, e = this.cropH, i = this.scale;\n      var s = {};\n      s.div = {\n        width: `${t}px`,\n        height: `${e}px`\n      };\n      let r = (this.x - this.cropOffsertX) / i, o = (this.y - this.cropOffsertY) / i, h = 0;\n      s.w = t, s.h = e, s.url = this.imgs, s.img = {\n        width: `${this.trueWidth}px`,\n        height: `${this.trueHeight}px`,\n        transform: `scale(${i})translate3d(${r}px, ${o}px, ${h}px)rotateZ(${this.rotate * 90}deg)`\n      }, s.html = `\n      <div class=\"show-preview\" style=\"width: ${s.w}px; height: ${s.h}px,; overflow: hidden\">\n        <div style=\"width: ${t}px; height: ${e}px\">\n          <img src=${s.url} style=\"width: ${this.trueWidth}px; height: ${this.trueHeight}px; transform:\n          scale(${i})translate3d(${r}px, ${o}px, ${h}px)rotateZ(${this.rotate * 90}deg)\">\n        </div>\n      </div>`, this.$emit(\"real-time\", s);\n    },\n    // reload 图片布局函数\n    reload() {\n      let t = new Image();\n      t.onload = () => {\n        this.w = parseFloat(window.getComputedStyle(this.$refs.cropper).width), this.h = parseFloat(window.getComputedStyle(this.$refs.cropper).height), this.trueWidth = t.width, this.trueHeight = t.height, this.original ? this.scale = 1 : this.scale = this.checkedMode(), this.$nextTick(() => {\n          this.x = -(this.trueWidth - this.trueWidth * this.scale) / 2 + (this.w - this.trueWidth * this.scale) / 2, this.y = -(this.trueHeight - this.trueHeight * this.scale) / 2 + (this.h - this.trueHeight * this.scale) / 2, this.loading = !1, this.autoCrop && this.goAutoCrop(), this.$emit(\"img-load\", \"success\"), setTimeout(() => {\n            this.showPreview();\n          }, 20);\n        });\n      }, t.onerror = () => {\n        this.$emit(\"img-load\", \"error\");\n      }, t.src = this.imgs;\n    },\n    // 背景布局的函数\n    checkedMode() {\n      let t = 1, e = this.trueWidth, i = this.trueHeight;\n      const s = this.mode.split(\" \");\n      switch (s[0]) {\n        case \"contain\":\n          this.trueWidth > this.w && (t = this.w / this.trueWidth), this.trueHeight * t > this.h && (t = this.h / this.trueHeight);\n          break;\n        case \"cover\":\n          e = this.w, t = e / this.trueWidth, i = i * t, i < this.h && (i = this.h, t = i / this.trueHeight);\n          break;\n        default:\n          try {\n            let r = s[0];\n            if (r.search(\"px\") !== -1) {\n              r = r.replace(\"px\", \"\"), e = parseFloat(r);\n              const o = e / this.trueWidth;\n              let h = 1, a = s[1];\n              a.search(\"px\") !== -1 && (a = a.replace(\"px\", \"\"), i = parseFloat(a), h = i / this.trueHeight), t = Math.min(o, h);\n            }\n            if (r.search(\"%\") !== -1 && (r = r.replace(\"%\", \"\"), e = parseFloat(r) / 100 * this.w, t = e / this.trueWidth), s.length === 2 && r === \"auto\") {\n              let o = s[1];\n              o.search(\"px\") !== -1 && (o = o.replace(\"px\", \"\"), i = parseFloat(o), t = i / this.trueHeight), o.search(\"%\") !== -1 && (o = o.replace(\"%\", \"\"), i = parseFloat(o) / 100 * this.h, t = i / this.trueHeight);\n            }\n          } catch {\n            t = 1;\n          }\n      }\n      return t;\n    },\n    // 自动截图函数\n    goAutoCrop(t, e) {\n      if (this.imgs === \"\" || this.imgs === null)\n        return;\n      this.clearCrop(), this.cropping = !0;\n      let i = this.w, s = this.h;\n      if (this.centerBox) {\n        const h = Math.abs(this.rotate) % 2 > 0;\n        let a = (h ? this.trueHeight : this.trueWidth) * this.scale, n = (h ? this.trueWidth : this.trueHeight) * this.scale;\n        i = a < i ? a : i, s = n < s ? n : s;\n      }\n      var r = t || parseFloat(this.autoCropWidth), o = e || parseFloat(this.autoCropHeight);\n      (r === 0 || o === 0) && (r = i * 0.8, o = s * 0.8), r = r > i ? i : r, o = o > s ? s : o, this.fixed && (o = r / this.fixedNumber[0] * this.fixedNumber[1]), o > this.h && (o = this.h, r = o / this.fixedNumber[1] * this.fixedNumber[0]), this.changeCrop(r, o);\n    },\n    // 手动改变截图框大小函数\n    changeCrop(t, e) {\n      if (this.centerBox) {\n        let i = this.getImgAxis();\n        t > i.x2 - i.x1 && (t = i.x2 - i.x1, e = t / this.fixedNumber[0] * this.fixedNumber[1]), e > i.y2 - i.y1 && (e = i.y2 - i.y1, t = e / this.fixedNumber[1] * this.fixedNumber[0]);\n      }\n      this.cropW = t, this.cropH = e, this.checkCropLimitSize(), this.$nextTick(() => {\n        this.cropOffsertX = (this.w - this.cropW) / 2, this.cropOffsertY = (this.h - this.cropH) / 2, this.centerBox && this.moveCrop(null, !0);\n      });\n    },\n    // 重置函数， 恢复组件置初始状态\n    refresh() {\n      this.img, this.imgs = \"\", this.scale = 1, this.crop = !1, this.rotate = 0, this.w = 0, this.h = 0, this.trueWidth = 0, this.trueHeight = 0, this.imgIsQqualCrop = !1, this.clearCrop(), this.$nextTick(() => {\n        this.checkedImg();\n      });\n    },\n    // 向左边旋转\n    rotateLeft() {\n      this.rotate = this.rotate <= -3 ? 0 : this.rotate - 1;\n    },\n    // 向右边旋转\n    rotateRight() {\n      this.rotate = this.rotate >= 3 ? 0 : this.rotate + 1;\n    },\n    // 清除旋转\n    rotateClear() {\n      this.rotate = 0;\n    },\n    // 图片坐标点校验\n    checkoutImgAxis(t, e, i) {\n      t = t || this.x, e = e || this.y, i = i || this.scale;\n      let s = !0;\n      if (this.centerBox) {\n        let r = this.getImgAxis(t, e, i), o = this.getCropAxis();\n        r.x1 >= o.x1 && (s = !1), r.x2 <= o.x2 && (s = !1), r.y1 >= o.y1 && (s = !1), r.y2 <= o.y2 && (s = !1), s || this.changeImgScale(r, o, i);\n      }\n      return s;\n    },\n    // 缩放图片，将图片坐标适配截图框坐标\n    changeImgScale(t, e, i) {\n      let s = this.trueWidth, r = this.trueHeight, o = s * i, h = r * i;\n      if (o >= this.cropW && h >= this.cropH)\n        this.scale = i;\n      else {\n        const a = this.cropW / s, n = this.cropH / r, c = this.cropH <= r * a ? a : n;\n        this.scale = c, o = s * c, h = r * c;\n      }\n      this.imgIsQqualCrop || (t.x1 >= e.x1 && (this.isRotateRightOrLeft ? this.x = e.x1 - (s - o) / 2 - (o - h) / 2 : this.x = e.x1 - (s - o) / 2), t.x2 <= e.x2 && (this.isRotateRightOrLeft ? this.x = e.x1 - (s - o) / 2 - (o - h) / 2 - h + this.cropW : this.x = e.x2 - (s - o) / 2 - o), t.y1 >= e.y1 && (this.isRotateRightOrLeft ? this.y = e.y1 - (r - h) / 2 - (h - o) / 2 : this.y = e.y1 - (r - h) / 2), t.y2 <= e.y2 && (this.isRotateRightOrLeft ? this.y = e.y2 - (r - h) / 2 - (h - o) / 2 - o : this.y = e.y2 - (r - h) / 2 - h)), (o < this.cropW || h < this.cropH) && (this.imgIsQqualCrop = !0);\n    }\n  },\n  mounted() {\n    this.support = \"onwheel\" in document.createElement(\"div\") ? \"wheel\" : document.onmousewheel !== void 0 ? \"mousewheel\" : \"DOMMouseScroll\";\n    let t = this;\n    var e = navigator.userAgent;\n    this.isIOS = !!e.match(/\\(i[^;]+;( U;)? CPU.+Mac OS X/), HTMLCanvasElement.prototype.toBlob || Object.defineProperty(HTMLCanvasElement.prototype, \"toBlob\", {\n      value: function(i, s, r) {\n        for (var o = atob(this.toDataURL(s, r).split(\",\")[1]), h = o.length, a = new Uint8Array(h), n = 0; n < h; n++)\n          a[n] = o.charCodeAt(n);\n        i(new Blob([a], { type: t.type || \"image/png\" }));\n      }\n    }), this.showPreview(), this.checkedImg();\n  },\n  unmounted() {\n    window.removeEventListener(\"mousemove\", this.moveCrop), window.removeEventListener(\"mouseup\", this.leaveCrop), window.removeEventListener(\"touchmove\", this.moveCrop), window.removeEventListener(\"touchend\", this.leaveCrop), this.cancelScale();\n  }\n}), A = {\n  key: 0,\n  class: \"cropper-box\"\n}, B = [\"src\"], P = { class: \"cropper-view-box\" }, R = [\"src\"], D = { key: 1 };\nfunction U(t, e, i, s, r, o) {\n  return y(), x(\"div\", {\n    class: \"vue-cropper\",\n    ref: \"cropper\",\n    onMouseover: e[28] || (e[28] = (...h) => t.scaleImg && t.scaleImg(...h)),\n    onMouseout: e[29] || (e[29] = (...h) => t.cancelScale && t.cancelScale(...h))\n  }, [\n    t.imgs ? (y(), x(\"div\", A, [\n      H(w(\"div\", {\n        class: \"cropper-box-canvas\",\n        style: b({\n          width: t.trueWidth + \"px\",\n          height: t.trueHeight + \"px\",\n          transform: \"scale(\" + t.scale + \",\" + t.scale + \") translate3d(\" + t.x / t.scale + \"px,\" + t.y / t.scale + \"px,0)rotateZ(\" + t.rotate * 90 + \"deg)\"\n        })\n      }, [\n        w(\"img\", {\n          src: t.imgs,\n          alt: \"cropper-img\",\n          ref: \"cropperImg\"\n        }, null, 8, B)\n      ], 4), [\n        [W, !t.loading]\n      ])\n    ])) : O(\"\", !0),\n    w(\"div\", {\n      class: I([\"cropper-drag-box\", { \"cropper-move\": t.move && !t.crop, \"cropper-crop\": t.crop, \"cropper-modal\": t.cropping }]),\n      onMousedown: e[0] || (e[0] = (...h) => t.startMove && t.startMove(...h)),\n      onTouchstart: e[1] || (e[1] = (...h) => t.startMove && t.startMove(...h))\n    }, null, 34),\n    H(w(\"div\", {\n      class: \"cropper-crop-box\",\n      style: b({\n        width: t.cropW + \"px\",\n        height: t.cropH + \"px\",\n        transform: \"translate3d(\" + t.cropOffsertX + \"px,\" + t.cropOffsertY + \"px,0)\"\n      })\n    }, [\n      w(\"span\", P, [\n        w(\"img\", {\n          style: b({\n            width: t.trueWidth + \"px\",\n            height: t.trueHeight + \"px\",\n            transform: \"scale(\" + t.scale + \",\" + t.scale + \") translate3d(\" + (t.x - t.cropOffsertX) / t.scale + \"px,\" + (t.y - t.cropOffsertY) / t.scale + \"px,0)rotateZ(\" + t.rotate * 90 + \"deg)\"\n          }),\n          src: t.imgs,\n          alt: \"cropper-img\"\n        }, null, 12, R)\n      ]),\n      w(\"span\", {\n        class: \"cropper-face cropper-move\",\n        onMousedown: e[2] || (e[2] = (...h) => t.cropMove && t.cropMove(...h)),\n        onTouchstart: e[3] || (e[3] = (...h) => t.cropMove && t.cropMove(...h))\n      }, null, 32),\n      t.info ? (y(), x(\"span\", {\n        key: 0,\n        class: \"crop-info\",\n        style: b({ top: t.cropInfo.top })\n      }, X(t.cropInfo.width) + \" × \" + X(t.cropInfo.height), 5)) : O(\"\", !0),\n      t.fixedBox ? O(\"\", !0) : (y(), x(\"span\", D, [\n        w(\"span\", {\n          class: \"crop-line line-w\",\n          onMousedown: e[4] || (e[4] = (h) => t.changeCropSize(h, !1, !0, 0, 1)),\n          onTouchstart: e[5] || (e[5] = (h) => t.changeCropSize(h, !1, !0, 0, 1))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-line line-a\",\n          onMousedown: e[6] || (e[6] = (h) => t.changeCropSize(h, !0, !1, 1, 0)),\n          onTouchstart: e[7] || (e[7] = (h) => t.changeCropSize(h, !0, !1, 1, 0))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-line line-s\",\n          onMousedown: e[8] || (e[8] = (h) => t.changeCropSize(h, !1, !0, 0, 2)),\n          onTouchstart: e[9] || (e[9] = (h) => t.changeCropSize(h, !1, !0, 0, 2))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-line line-d\",\n          onMousedown: e[10] || (e[10] = (h) => t.changeCropSize(h, !0, !1, 2, 0)),\n          onTouchstart: e[11] || (e[11] = (h) => t.changeCropSize(h, !0, !1, 2, 0))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-point point1\",\n          onMousedown: e[12] || (e[12] = (h) => t.changeCropSize(h, !0, !0, 1, 1)),\n          onTouchstart: e[13] || (e[13] = (h) => t.changeCropSize(h, !0, !0, 1, 1))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-point point2\",\n          onMousedown: e[14] || (e[14] = (h) => t.changeCropSize(h, !1, !0, 0, 1)),\n          onTouchstart: e[15] || (e[15] = (h) => t.changeCropSize(h, !1, !0, 0, 1))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-point point3\",\n          onMousedown: e[16] || (e[16] = (h) => t.changeCropSize(h, !0, !0, 2, 1)),\n          onTouchstart: e[17] || (e[17] = (h) => t.changeCropSize(h, !0, !0, 2, 1))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-point point4\",\n          onMousedown: e[18] || (e[18] = (h) => t.changeCropSize(h, !0, !1, 1, 0)),\n          onTouchstart: e[19] || (e[19] = (h) => t.changeCropSize(h, !0, !1, 1, 0))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-point point5\",\n          onMousedown: e[20] || (e[20] = (h) => t.changeCropSize(h, !0, !1, 2, 0)),\n          onTouchstart: e[21] || (e[21] = (h) => t.changeCropSize(h, !0, !1, 2, 0))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-point point6\",\n          onMousedown: e[22] || (e[22] = (h) => t.changeCropSize(h, !0, !0, 1, 2)),\n          onTouchstart: e[23] || (e[23] = (h) => t.changeCropSize(h, !0, !0, 1, 2))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-point point7\",\n          onMousedown: e[24] || (e[24] = (h) => t.changeCropSize(h, !1, !0, 0, 2)),\n          onTouchstart: e[25] || (e[25] = (h) => t.changeCropSize(h, !1, !0, 0, 2))\n        }, null, 32),\n        w(\"span\", {\n          class: \"crop-point point8\",\n          onMousedown: e[26] || (e[26] = (h) => t.changeCropSize(h, !0, !0, 2, 2)),\n          onTouchstart: e[27] || (e[27] = (h) => t.changeCropSize(h, !0, !0, 2, 2))\n        }, null, 32)\n      ]))\n    ], 4), [\n      [W, t.cropping]\n    ])\n  ], 544);\n}\nconst M = /* @__PURE__ */ $(z, [[\"render\", U], [\"__scopeId\", \"data-v-a742df44\"]]), F = function(t) {\n  t.component(\"VueCropper\", M);\n}, V = {\n  version: \"1.1.4\",\n  install: F,\n  VueCropper: M\n};\nexport {\n  M as VueCropper,\n  V as default,\n  V as globalCropper\n};\n"], "mappings": ";;;;;;;AAAA,SAASA,eAAe,IAAIC,CAAC,EAAEC,SAAS,IAAIC,CAAC,EAAEC,kBAAkB,IAAIC,CAAC,EAAEC,cAAc,IAAIC,CAAC,EAAEC,kBAAkB,IAAIC,CAAC,EAAEC,cAAc,IAAIC,CAAC,EAAEC,KAAK,IAAIC,CAAC,EAAEC,kBAAkB,IAAIC,CAAC,EAAEC,cAAc,IAAIC,CAAC,EAAEC,eAAe,IAAIC,CAAC,QAAQ,KAAK;AACtO,IAAMC,CAAC,GAAG,CAAC,CAAC;AACZA,CAAC,CAACC,OAAO,GAAG,UAACC,CAAC;EAAA,OAAK,IAAIC,OAAO,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;IACvC,IAAIC,CAAC,GAAG,CAAC,CAAC;IACVC,CAAC,CAACL,CAAC,CAAC,CAACM,IAAI,CAAC,UAACC,CAAC,EAAK;MACfH,CAAC,CAACI,WAAW,GAAGD,CAAC;MACjB,IAAI;QACFH,CAAC,CAACK,WAAW,GAAGC,CAAC,CAACH,CAAC,CAAC;MACtB,CAAC,CAAC,OAAAI,OAAA,EAAM;QACNP,CAAC,CAACK,WAAW,GAAG,CAAC,CAAC;MACpB;MACAP,CAAC,CAACE,CAAC,CAAC;IACN,CAAC,CAAC,CAACQ,KAAK,CAAC,UAACL,CAAC,EAAK;MACdJ,CAAC,CAACI,CAAC,CAAC;IACN,CAAC,CAAC;EACJ,CAAC,CAAC;AAAA;AACF,SAASF,CAACA,CAACL,CAAC,EAAE;EACZ,IAAIE,CAAC,GAAG,IAAI;EACZ,OAAO,IAAID,OAAO,CAAC,UAACE,CAAC,EAAEC,CAAC,EAAK;IAC3B,IAAIJ,CAAC,CAACa,GAAG;MACP,IAAI,UAAU,CAACC,IAAI,CAACd,CAAC,CAACa,GAAG,CAAC,EACxBX,CAAC,GAAGa,CAAC,CAACf,CAAC,CAACa,GAAG,CAAC,EAAEV,CAAC,CAACD,CAAC,CAAC,CAAC,KAChB,IAAI,UAAU,CAACY,IAAI,CAACd,CAAC,CAACa,GAAG,CAAC,EAAE;QAC/B,IAAIN,CAAC,GAAG,IAAIS,UAAU,CAAC,CAAC;QACxBT,CAAC,CAACU,MAAM,GAAG,UAASC,CAAC,EAAE;UACrBhB,CAAC,GAAGgB,CAAC,CAACC,MAAM,CAACC,MAAM,EAAEjB,CAAC,CAACD,CAAC,CAAC;QAC3B,CAAC,EAAEmB,CAAC,CAACrB,CAAC,CAACa,GAAG,EAAE,UAASK,CAAC,EAAE;UACtBX,CAAC,CAACe,iBAAiB,CAACJ,CAAC,CAAC;QACxB,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,IAAIK,CAAC,GAAG,IAAIC,cAAc,CAAC,CAAC;QAC5BD,CAAC,CAACN,MAAM,GAAG,YAAW;UACpB,IAAI,IAAI,CAACQ,MAAM,IAAI,GAAG,IAAI,IAAI,CAACA,MAAM,KAAK,CAAC,EACzCvB,CAAC,GAAGqB,CAAC,CAACG,QAAQ,EAAEvB,CAAC,CAACD,CAAC,CAAC,CAAC,KAErB,MAAM,sBAAsB;UAC9BqB,CAAC,GAAG,IAAI;QACV,CAAC,EAAEA,CAAC,CAACI,IAAI,CAAC,KAAK,EAAE3B,CAAC,CAACa,GAAG,EAAE,CAAC,CAAC,CAAC,EAAEU,CAAC,CAACK,YAAY,GAAG,aAAa,EAAEL,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC;MAC3E;IAAC,OAEDzB,CAAC,CAAC,WAAW,CAAC;EAClB,CAAC,CAAC;AACJ;AACA,SAASiB,CAACA,CAACrB,CAAC,EAAEE,CAAC,EAAE;EACf,IAAIC,CAAC,GAAG,IAAIqB,cAAc,CAAC,CAAC;EAC5BrB,CAAC,CAACwB,IAAI,CAAC,KAAK,EAAE3B,CAAC,EAAE,CAAC,CAAC,CAAC,EAAEG,CAAC,CAACyB,YAAY,GAAG,MAAM,EAAEzB,CAAC,CAACc,MAAM,GAAG,UAASb,CAAC,EAAE;IACpE,CAAC,IAAI,CAACqB,MAAM,IAAI,GAAG,IAAI,IAAI,CAACA,MAAM,KAAK,CAAC,KAAKvB,CAAC,CAAC,IAAI,CAACwB,QAAQ,CAAC;EAC/D,CAAC,EAAEvB,CAAC,CAAC0B,IAAI,CAAC,CAAC;AACb;AACA,SAASd,CAACA,CAACf,CAAC,EAAEE,CAAC,EAAE;EACfA,CAAC,GAAGA,CAAC,IAAIF,CAAC,CAAC8B,KAAK,CAAC,4BAA4B,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EAAE9B,CAAC,GAAGA,CAAC,CAAC+B,OAAO,CAAC,6BAA6B,EAAE,EAAE,CAAC;EACzG,KAAK,IAAI5B,CAAC,GAAG6B,IAAI,CAAChC,CAAC,CAAC,EAAEI,CAAC,GAAGD,CAAC,CAAC8B,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG9B,CAAC,CAAC8B,MAAM,GAAG9B,CAAC,CAAC8B,MAAM,GAAG,CAAC,EAAE1B,CAAC,GAAG,IAAI2B,WAAW,CAAC9B,CAAC,CAAC,EAAEmB,CAAC,GAAG,IAAIY,WAAW,CAAC5B,CAAC,CAAC,EAAEW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,CAAC,EAAEc,CAAC,EAAE,EACtIK,CAAC,CAACL,CAAC,CAAC,GAAGf,CAAC,CAACiC,UAAU,CAAClB,CAAC,CAAC;EACxB,OAAOX,CAAC;AACV;AACA,SAAS8B,CAACA,CAACrC,CAAC,EAAEE,CAAC,EAAEC,CAAC,EAAE;EAClB,IAAIC,CAAC,GAAG,EAAE;IAAEG,CAAC;EACb,KAAKA,CAAC,GAAGL,CAAC,EAAEC,CAAC,IAAID,CAAC,EAAEK,CAAC,GAAGJ,CAAC,EAAEI,CAAC,EAAE,EAC5BH,CAAC,IAAIkC,MAAM,CAACC,YAAY,CAACvC,CAAC,CAACwC,QAAQ,CAACjC,CAAC,CAAC,CAAC;EACzC,OAAOH,CAAC;AACV;AACA,SAASM,CAACA,CAACV,CAAC,EAAE;EACZ,IAAIE,CAAC,GAAG,IAAIuC,QAAQ,CAACzC,CAAC,CAAC;IAAEG,CAAC,GAAGD,CAAC,CAACwC,UAAU;IAAEtC,CAAC;IAAEG,CAAC;IAAEgB,CAAC;IAAEL,CAAC;IAAEyB,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;IAAEC,CAAC;EACvE,IAAI9C,CAAC,CAACsC,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,IAAItC,CAAC,CAACsC,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,EAChD,KAAKO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5C,CAAC,GAAI;IACnB,IAAID,CAAC,CAACsC,QAAQ,CAACO,CAAC,CAAC,KAAK,GAAG,IAAI7C,CAAC,CAACsC,QAAQ,CAACO,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;MACtDF,CAAC,GAAGE,CAAC;MACL;IACF;IACAA,CAAC,EAAE;EACL;EACF,IAAIF,CAAC,KAAKtC,CAAC,GAAGsC,CAAC,GAAG,CAAC,EAAEtB,CAAC,GAAGsB,CAAC,GAAG,EAAE,EAAER,CAAC,CAACnC,CAAC,EAAEK,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,KAAKqC,CAAC,GAAG1C,CAAC,CAAC+C,SAAS,CAAC1B,CAAC,CAAC,EAAEoB,CAAC,GAAGC,CAAC,KAAK,KAAK,EAAE,CAACD,CAAC,IAAIC,CAAC,KAAK,KAAK,KAAK1C,CAAC,CAAC+C,SAAS,CAAC1B,CAAC,GAAG,CAAC,EAAEoB,CAAC,CAAC,KAAK,EAAE,KAAKzB,CAAC,GAAGhB,CAAC,CAACgD,SAAS,CAAC3B,CAAC,GAAG,CAAC,EAAEoB,CAAC,CAAC,EAAEzB,CAAC,IAAI,CAAC,KAAK4B,CAAC,GAAGvB,CAAC,GAAGL,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE4B,CAAC,EAAE;IAC7M,KAAK3C,CAAC,GAAGD,CAAC,CAAC+C,SAAS,CAACH,CAAC,EAAEH,CAAC,CAAC,EAAEK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG7C,CAAC,EAAE6C,CAAC,EAAE,EAC3C,IAAID,CAAC,GAAGD,CAAC,GAAGE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE9C,CAAC,CAAC+C,SAAS,CAACF,CAAC,EAAEJ,CAAC,CAAC,KAAK,GAAG,EAAE;MACjDI,CAAC,IAAI,CAAC,EAAE3C,CAAC,GAAGF,CAAC,CAAC+C,SAAS,CAACF,CAAC,EAAEJ,CAAC,CAAC;MAC7B;IACF;EACJ;EACA,OAAOvC,CAAC;AACV;AACA,IAAM+C,CAAC,GAAG,SAAJA,CAACA,CAAInD,CAAC,EAAEE,CAAC,EAAK;IAClB,IAAMC,CAAC,GAAGH,CAAC,CAACoD,SAAS,IAAIpD,CAAC;IAAC,IAAAqD,SAAA,GAAAC,0BAAA,CACNpD,CAAC;MAAAqD,KAAA;IAAA;MAAtB,KAAAF,SAAA,CAAAjD,CAAA,MAAAmD,KAAA,GAAAF,SAAA,CAAAT,CAAA,IAAAY,IAAA,GACE;QAAA,IAAAC,WAAA,GAAAC,cAAA,CAAAH,KAAA,CAAAI,KAAA;UADUvD,CAAC,GAAAqD,WAAA;UAAElD,CAAC,GAAAkD,WAAA;QACdtD,CAAC,CAACC,CAAC,CAAC,GAAGG,CAAC;MAAA;IAAC,SAAAqD,GAAA;MAAAP,SAAA,CAAAnD,CAAA,CAAA0D,GAAA;IAAA;MAAAP,SAAA,CAAAN,CAAA;IAAA;IACX,OAAO5C,CAAC;EACV,CAAC;EAAE0D,CAAC,GAAGlF,CAAC,CAAC;IACPmF,IAAI,EAAE,SAANA,IAAIA,CAAA,EAAa;MACf,OAAO;QACL;QACA3E,CAAC,EAAE,CAAC;QACJ+B,CAAC,EAAE,CAAC;QACJ;QACA6C,KAAK,EAAE,CAAC;QACR;QACAhF,CAAC,EAAE,CAAC;QACJ;QACAF,CAAC,EAAE,CAAC;QACJ;QACAmF,OAAO,EAAE,CAAC,CAAC;QACX;QACAC,SAAS,EAAE,CAAC;QACZ;QACAC,UAAU,EAAE,CAAC;QACbC,IAAI,EAAE,CAAC,CAAC;QACR;QACAC,KAAK,EAAE,CAAC;QACR;QACAC,KAAK,EAAE,CAAC;QACR;QACAC,IAAI,EAAE,CAAC,CAAC;QACR;QACAC,QAAQ,EAAE,CAAC,CAAC;QACZ;QACAC,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE,CAAC;QACRC,QAAQ,EAAE,CAAC;QACXC,QAAQ,EAAE,CAAC;QACX;QACAC,UAAU,EAAE,CAAC,CAAC;QACdC,UAAU,EAAE,CAAC,CAAC;QACd;QACAC,eAAe,EAAE,CAAC;QAClBC,eAAe,EAAE,CAAC;QAClB;QACAC,KAAK,EAAE,CAAC;QACRC,KAAK,EAAE,CAAC;QACRC,WAAW,EAAE,CAAC;QACdC,WAAW,EAAE,CAAC;QACdC,YAAY,EAAE,CAAC;QACfC,YAAY,EAAE,CAAC;QACf;QACAC,OAAO,EAAE,EAAE;QACX;QACAC,OAAO,EAAE,EAAE;QACXC,QAAQ,EAAE,CAAC,CAAC;QACZ;QACAC,MAAM,EAAE,CAAC;QACTC,KAAK,EAAE,CAAC,CAAC;QACTjF,WAAW,EAAE,CAAC;QACdkF,IAAI,EAAE,EAAE;QACR;QACAC,GAAG,EAAE,GAAG;QACR;QACAC,OAAO,EAAE,CAAC,CAAC;QACXC,UAAU,EAAE,EAAE;QACdC,SAAS,EAAE,EAAE;QACb;QACAC,SAAS,EAAE,CAAC,CAAC;QACb;QACAC,cAAc,EAAE,CAAC;MACnB,CAAC;IACH,CAAC;IACDC,KAAK,EAAE;MACLC,GAAG,EAAE;QACHC,IAAI,EAAE,CAAC9D,MAAM,EAAE+D,IAAI,EAAE,IAAI,EAAEC,IAAI,CAAC;QAChCC,OAAO,EAAE;MACX,CAAC;MACD;MACAC,UAAU,EAAE;QACVJ,IAAI,EAAEK,MAAM;QACZF,OAAO,EAAE;MACX,CAAC;MACDG,UAAU,EAAE;QACVN,IAAI,EAAE9D,MAAM;QACZiE,OAAO,EAAE;MACX,CAAC;MACDI,IAAI,EAAE;QACJP,IAAI,EAAEQ,OAAO;QACbL,OAAO,EAAE,CAAC;MACZ,CAAC;MACD;MACAM,QAAQ,EAAE;QACRT,IAAI,EAAEQ,OAAO;QACbL,OAAO,EAAE,CAAC;MACZ,CAAC;MACD;MACAO,QAAQ,EAAE;QACRV,IAAI,EAAEQ,OAAO;QACbL,OAAO,EAAE,CAAC;MACZ,CAAC;MACDQ,aAAa,EAAE;QACbX,IAAI,EAAE,CAACK,MAAM,EAAEnE,MAAM,CAAC;QACtBiE,OAAO,EAAE;MACX,CAAC;MACDS,cAAc,EAAE;QACdZ,IAAI,EAAE,CAACK,MAAM,EAAEnE,MAAM,CAAC;QACtBiE,OAAO,EAAE;MACX,CAAC;MACD;MACAU,KAAK,EAAE;QACLb,IAAI,EAAEQ,OAAO;QACbL,OAAO,EAAE,CAAC;MACZ,CAAC;MACD;MACAW,WAAW,EAAE;QACXd,IAAI,EAAEe,KAAK;QACXZ,OAAO,EAAE,SAATA,QAAOA,CAAA;UAAA,OAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;QAAA;MACvB,CAAC;MACD;MACAa,QAAQ,EAAE;QACRhB,IAAI,EAAEQ,OAAO;QACbL,OAAO,EAAE,CAAC;MACZ,CAAC;MACD;MACAc,IAAI,EAAE;QACJjB,IAAI,EAAEQ,OAAO;QACbL,OAAO,EAAE,CAAC;MACZ,CAAC;MACD;MACAe,OAAO,EAAE;QACPlB,IAAI,EAAEQ,OAAO;QACbL,OAAO,EAAE,CAAC;MACZ,CAAC;MACD;MACAgB,UAAU,EAAE;QACVnB,IAAI,EAAEQ,OAAO;QACbL,OAAO,EAAE,CAAC;MACZ,CAAC;MACD;MACAiB,QAAQ,EAAE;QACRpB,IAAI,EAAEQ,OAAO;QACbL,OAAO,EAAE,CAAC;MACZ,CAAC;MACD;MACAkB,SAAS,EAAE;QACTrB,IAAI,EAAEQ,OAAO;QACbL,OAAO,EAAE,CAAC;MACZ,CAAC;MACD;MACAmB,IAAI,EAAE;QACJtB,IAAI,EAAEQ,OAAO;QACbL,OAAO,EAAE,CAAC;MACZ,CAAC;MACD;MACAoB,QAAQ,EAAE;QACRvB,IAAI,EAAEQ,OAAO;QACbL,OAAO,EAAE,CAAC;MACZ,CAAC;MACD;MACAqB,UAAU,EAAE;QACVxB,IAAI,EAAE,CAACK,MAAM,EAAEnE,MAAM,CAAC;QACtBiE,OAAO,EAAE;MACX,CAAC;MACD;MACAsB,OAAO,EAAE;QACPzB,IAAI,EAAE,CAACK,MAAM,EAAEnE,MAAM,CAAC;QACtBiE,OAAO,EAAE;MACX,CAAC;MACD;MACAuB,IAAI,EAAE;QACJ1B,IAAI,EAAE,CAACK,MAAM,EAAEnE,MAAM,CAAC;QACtBiE,OAAO,EAAE;MACX,CAAC;MACD;AACJ;AACA;AACA;AACA;AACA;AACA;MACIwB,IAAI,EAAE;QACJ3B,IAAI,EAAE9D,MAAM;QACZiE,OAAO,EAAE;MACX,CAAC;MACD;MACA;MACAyB,YAAY,EAAE;QACZ5B,IAAI,EAAE,CAACK,MAAM,EAAEU,KAAK,EAAE7E,MAAM,CAAC;QAC7BiE,OAAO,EAAE,SAATA,QAAOA,CAAA;UAAA,OAAQ,EAAE;QAAA;QACjB0B,SAAS,EAAE,SAAXA,SAASA,CAAWjI,CAAC,EAAE;UACrB,OAAOmH,KAAK,CAACe,OAAO,CAAClI,CAAC,CAAC,GAAGyG,MAAM,CAACzG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,IAAIyG,MAAM,CAACzG,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAGyG,MAAM,CAACzG,CAAC,CAAC,IAAI,CAAC;QACnF;MACF,CAAC;MACD;MACAmI,SAAS,EAAE;QACT/B,IAAI,EAAE9D,MAAM;QACZiE,OAAO,EAAE;MACX;IACF,CAAC;IACD6B,QAAQ,EAAE;MACRC,QAAQA,CAAA,EAAG;QACT,IAAIrI,CAAC,GAAG,CAAC,CAAC;QACV,IAAIA,CAAC,CAACsI,GAAG,GAAG,IAAI,CAACjD,YAAY,GAAG,EAAE,GAAG,OAAO,GAAG,KAAK,EAAErF,CAAC,CAACuI,KAAK,GAAG,IAAI,CAAC/D,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,GAAG,CAAC,EAAExE,CAAC,CAACwI,MAAM,GAAG,IAAI,CAAC/D,KAAK,GAAG,CAAC,GAAG,IAAI,CAACA,KAAK,GAAG,CAAC,EAAE,IAAI,CAACkD,QAAQ,EAAE;UAC1J,IAAIzH,CAAC,GAAG,CAAC;UACT,IAAI,CAACwH,IAAI,IAAI,CAAC,IAAI,CAACL,IAAI,KAAKnH,CAAC,GAAGuI,MAAM,CAACC,gBAAgB,CAAC,EAAE,IAAI,CAACb,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAACR,IAAI,KAAKnH,CAAC,GAAGyI,IAAI,CAACC,GAAG,CAACnC,MAAM,CAAC,IAAI,CAACoB,OAAO,CAAC,CAAC,CAAC,EAAE7H,CAAC,CAACuI,KAAK,GAAGvI,CAAC,CAACuI,KAAK,GAAGrI,CAAC,EAAEF,CAAC,CAACwI,MAAM,GAAGxI,CAAC,CAACwI,MAAM,GAAGtI,CAAC,EAAE,IAAI,CAACmH,IAAI,KAAKrH,CAAC,CAACuI,KAAK,GAAGvI,CAAC,CAACuI,KAAK,GAAG,IAAI,CAACxE,KAAK,EAAE/D,CAAC,CAACwI,MAAM,GAAGxI,CAAC,CAACwI,MAAM,GAAG,IAAI,CAACzE,KAAK,CAAC;QACpQ;QACA,OAAO/D,CAAC,CAACuI,KAAK,GAAGvI,CAAC,CAACuI,KAAK,CAACM,OAAO,CAAC,CAAC,CAAC,EAAE7I,CAAC,CAACwI,MAAM,GAAGxI,CAAC,CAACwI,MAAM,CAACK,OAAO,CAAC,CAAC,CAAC,EAAE7I,CAAC;MACxE,CAAC;MACD8I,IAAIA,CAAA,EAAG;QACL,OAAO,CAAC,CAACL,MAAM,CAACM,aAAa,IAAI,eAAe,IAAIN,MAAM;MAC5D,CAAC;MACDO,OAAOA,CAAA,EAAG;QACR,OAAO,IAAI,CAACF,IAAI,GAAG,IAAI,GAAG;UACxBE,OAAO,EAAE,CAAC;QACZ,CAAC;MACH,CAAC;MACD;MACAC,mBAAmBA,CAAA,EAAG;QACpB,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,IAAI,CAACzD,MAAM,CAAC;MAC7C;IACF,CAAC;IACD0D,KAAK,EAAE;MACL;MACAhD,GAAGA,CAAA,EAAG;QACJ,IAAI,CAACiD,UAAU,CAAC,CAAC;MACnB,CAAC;MACDzD,IAAIA,CAAC3F,CAAC,EAAE;QACNA,CAAC,KAAK,EAAE,IAAI,IAAI,CAACqJ,MAAM,CAAC,CAAC;MAC3B,CAAC;MACD7E,KAAKA,CAAA,EAAG;QACN,IAAI,CAAC8E,WAAW,CAAC,CAAC;MACpB,CAAC;MACD7E,KAAKA,CAAA,EAAG;QACN,IAAI,CAAC6E,WAAW,CAAC,CAAC;MACpB,CAAC;MACDlE,YAAYA,CAAA,EAAG;QACb,IAAI,CAACkE,WAAW,CAAC,CAAC;MACpB,CAAC;MACDjE,YAAYA,CAAA,EAAG;QACb,IAAI,CAACiE,WAAW,CAAC,CAAC;MACpB,CAAC;MACDvF,KAAKA,CAAC/D,CAAC,EAAEE,CAAC,EAAE;QACV,IAAI,CAACoJ,WAAW,CAAC,CAAC;MACpB,CAAC;MACDvK,CAACA,CAAA,EAAG;QACF,IAAI,CAACuK,WAAW,CAAC,CAAC;MACpB,CAAC;MACDzK,CAACA,CAAA,EAAG;QACF,IAAI,CAACyK,WAAW,CAAC,CAAC;MACpB,CAAC;MACDxC,QAAQA,CAAC9G,CAAC,EAAE;QACVA,CAAC,IAAI,IAAI,CAACuJ,UAAU,CAAC,CAAC;MACxB,CAAC;MACD;MACAxC,aAAaA,CAAA,EAAG;QACd,IAAI,CAACD,QAAQ,IAAI,IAAI,CAACyC,UAAU,CAAC,CAAC;MACpC,CAAC;MACDvC,cAAcA,CAAA,EAAG;QACf,IAAI,CAACF,QAAQ,IAAI,IAAI,CAACyC,UAAU,CAAC,CAAC;MACpC,CAAC;MACDxB,IAAIA,CAAA,EAAG;QACL,IAAI,CAACqB,UAAU,CAAC,CAAC;MACnB,CAAC;MACD3D,MAAMA,CAAA,EAAG;QACP,IAAI,CAAC6D,WAAW,CAAC,CAAC,EAAE,IAAI,CAACxC,QAAQ,GAAG,IAAI,CAACyC,UAAU,CAAC,IAAI,CAAC/E,KAAK,EAAE,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,IAAI,CAACD,KAAK,GAAG,CAAC,IAAI,IAAI,CAACC,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC8E,UAAU,CAAC,IAAI,CAAC/E,KAAK,EAAE,IAAI,CAACC,KAAK,CAAC;MAC7J;IACF,CAAC;IACD+E,OAAO,EAAE;MACPC,UAAUA,CAACzJ,CAAC,EAAE;QACZ,IAAIE,CAAC,GAAGwJ,SAAS,CAACC,SAAS,CAACC,KAAK,CAAC,GAAG,CAAC;UAAEzJ,CAAC,GAAG,EAAE;QAC9C,IAAIC,CAAC,GAAG,CAAC;QACT,IAAMG,CAAC,GAAG,IAAIsJ,MAAM,CAAC7J,CAAC,EAAE,GAAG,CAAC;QAC5B,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGrB,CAAC,CAAC+B,MAAM,EAAEV,CAAC,EAAE,EAC/BhB,CAAC,CAACO,IAAI,CAACZ,CAAC,CAACqB,CAAC,CAAC,CAAC,KAAKpB,CAAC,GAAGD,CAAC,CAACqB,CAAC,CAAC,CAAC;QAC5B,OAAOpB,CAAC,GAAGC,CAAC,GAAGD,CAAC,CAACyJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACA,KAAK,CAAC,GAAG,CAAC,GAAGxJ,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,EAAEA,CAAC;MACpE,CAAC;MACD0J,qBAAqBA,CAAC9J,CAAC,EAAEE,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;QAAA,IAAA2J,KAAA;QAChC,IAAI,IAAI,CAACN,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,EACpCvJ,CAAC,GAAG,CAAC,CAAC,CAAC,KACJ,IAAI,IAAI,CAACuJ,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;UAC5C,IAAMvI,CAAC,GAAG,IAAI,CAACuI,UAAU,CAAC,SAAS,CAAC;UACpCvI,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAIA,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAKhB,CAAC,GAAG,CAAC,CAAC,CAAC;QACnC,CAAC,MAAM;UACL,IAAMgB,EAAC,GAAGwI,SAAS,CAACC,SAAS,CAACK,WAAW,CAAC,CAAC,CAAClI,KAAK,CAAC,iCAAiC,CAAC;UACpF,IAAIZ,EAAC,EAAE;YACL,IAAIyB,CAAC,GAAGzB,EAAC,CAAC,CAAC,CAAC;YACZyB,CAAC,GAAGA,CAAC,CAACiH,KAAK,CAAC,GAAG,CAAC,EAAE,CAACjH,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,IAAIA,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAIA,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAMzC,CAAC,GAAG,CAAC,CAAC,CAAC;UACtE;QACF;QACA,IAAIK,CAAC,GAAG0J,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAAE3I,CAAC,GAAGhB,CAAC,CAAC4J,UAAU,CAAC,IAAI,CAAC;QAChE,QAAQ5I,CAAC,CAAC6I,IAAI,CAAC,CAAC,EAAElK,CAAC;UACjB,KAAK,CAAC;YACJK,CAAC,CAACgI,KAAK,GAAGpI,CAAC,EAAEI,CAAC,CAACiI,MAAM,GAAGpI,CAAC,EAAEmB,CAAC,CAAC8I,SAAS,CAAClK,CAAC,EAAE,CAAC,CAAC,EAAEoB,CAAC,CAACwC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YAC5D;UACF,KAAK,CAAC;YACJxD,CAAC,CAACgI,KAAK,GAAGpI,CAAC,EAAEI,CAAC,CAACiI,MAAM,GAAGpI,CAAC,EAAEmB,CAAC,CAAC8I,SAAS,CAAClK,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAG,CAAC,CAAC,EAAEmB,CAAC,CAACkE,MAAM,CAAC,GAAG,GAAGkD,IAAI,CAAC2B,EAAE,GAAG,GAAG,CAAC,EAAE/I,CAAC,CAAC8I,SAAS,CAAC,CAAClK,CAAC,GAAG,CAAC,EAAE,CAACC,CAAC,GAAG,CAAC,CAAC;YAChH;UACF,KAAK,CAAC;YACJG,CAAC,CAACgI,KAAK,GAAGpI,CAAC,EAAEI,CAAC,CAACiI,MAAM,GAAGpI,CAAC,EAAEmB,CAAC,CAAC8I,SAAS,CAAC,CAAC,EAAEjK,CAAC,CAAC,EAAEmB,CAAC,CAACwC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC5D;UACF,KAAK,CAAC;YACJxD,CAAC,CAACiI,MAAM,GAAGrI,CAAC,EAAEI,CAAC,CAACgI,KAAK,GAAGnI,CAAC,EAAEmB,CAAC,CAACkE,MAAM,CAAC,GAAG,GAAGkD,IAAI,CAAC2B,EAAE,CAAC,EAAE/I,CAAC,CAACwC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAClE;UACF,KAAK,CAAC;YACJxD,CAAC,CAACgI,KAAK,GAAGnI,CAAC,EAAEG,CAAC,CAACiI,MAAM,GAAGrI,CAAC,EAAEoB,CAAC,CAAC8I,SAAS,CAACjK,CAAC,GAAG,CAAC,EAAED,CAAC,GAAG,CAAC,CAAC,EAAEoB,CAAC,CAACkE,MAAM,CAAC,EAAE,GAAGkD,IAAI,CAAC2B,EAAE,GAAG,GAAG,CAAC,EAAE/I,CAAC,CAAC8I,SAAS,CAAC,CAAClK,CAAC,GAAG,CAAC,EAAE,CAACC,CAAC,GAAG,CAAC,CAAC;YAC/G;UACF,KAAK,CAAC;YACJG,CAAC,CAACiI,MAAM,GAAGrI,CAAC,EAAEI,CAAC,CAACgI,KAAK,GAAGnI,CAAC,EAAEmB,CAAC,CAACkE,MAAM,CAAC,GAAG,GAAGkD,IAAI,CAAC2B,EAAE,CAAC,EAAE/I,CAAC,CAAC8I,SAAS,CAAClK,CAAC,EAAE,CAACC,CAAC,CAAC,EAAEmB,CAAC,CAACwC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;YACtF;UACF,KAAK,CAAC;YACJxD,CAAC,CAACiI,MAAM,GAAGrI,CAAC,EAAEI,CAAC,CAACgI,KAAK,GAAGnI,CAAC,EAAEmB,CAAC,CAAC8I,SAAS,CAACjK,CAAC,GAAG,CAAC,EAAED,CAAC,GAAG,CAAC,CAAC,EAAEoB,CAAC,CAACkE,MAAM,CAAC,CAAC,EAAE,GAAGkD,IAAI,CAAC2B,EAAE,GAAG,GAAG,CAAC,EAAE/I,CAAC,CAAC8I,SAAS,CAAC,CAAClK,CAAC,GAAG,CAAC,EAAE,CAACC,CAAC,GAAG,CAAC,CAAC;YAChH;UACF;YACEG,CAAC,CAACgI,KAAK,GAAGpI,CAAC,EAAEI,CAAC,CAACiI,MAAM,GAAGpI,CAAC;QAC7B;QACAmB,CAAC,CAACgJ,SAAS,CAACvK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEG,CAAC,EAAEC,CAAC,CAAC,EAAEmB,CAAC,CAACiJ,OAAO,CAAC,CAAC,EAAEjK,CAAC,CAACkK,MAAM,CAC/C,UAACvJ,CAAC,EAAK;UACL,IAAIyB,CAAC,GAAG+H,GAAG,CAACC,eAAe,CAACzJ,CAAC,CAAC;UAC9BwJ,GAAG,CAACE,eAAe,CAACb,KAAI,CAACpE,IAAI,CAAC,EAAEoE,KAAI,CAACpE,IAAI,GAAGhD,CAAC;QAC/C,CAAC,EACD,QAAQ,GAAG,IAAI,CAAC+D,UAAU,EAC1B,CACF,CAAC;MACH,CAAC;MACD;MACA0C,UAAUA,CAAA,EAAG;QAAA,IAAAyB,MAAA;QACX,IAAI,IAAI,CAAC1E,GAAG,KAAK,IAAI,IAAI,IAAI,CAACA,GAAG,KAAK,EAAE,EAAE;UACxC,IAAI,CAACR,IAAI,GAAG,EAAE,EAAE,IAAI,CAACmF,SAAS,CAAC,CAAC;UAChC;QACF;QACA,IAAI,CAAC9G,OAAO,GAAG,CAAC,CAAC,EAAE,IAAI,CAACD,KAAK,GAAG,CAAC,EAAE,IAAI,CAAC0B,MAAM,GAAG,CAAC,EAAE,IAAI,CAACQ,cAAc,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC6E,SAAS,CAAC,CAAC;QAC9F,IAAI9K,CAAC,GAAG,IAAI+K,KAAK,CAAC,CAAC;QACnB,IAAI/K,CAAC,CAACiB,MAAM,GAAG,YAAM;UACnB,IAAI4J,MAAI,CAAC1E,GAAG,KAAK,EAAE,EACjB,OAAO0E,MAAI,CAACG,KAAK,CAAC,UAAU,EAAE,IAAIC,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;UACxD,IAAI9K,CAAC,GAAGH,CAAC,CAACuI,KAAK;YAAEnI,CAAC,GAAGJ,CAAC,CAACwI,MAAM;UAC7B1I,CAAC,CAACC,OAAO,CAACC,CAAC,CAAC,CAACM,IAAI,CAAC,UAACC,CAAC,EAAK;YACvBsK,MAAI,CAACpK,WAAW,GAAGF,CAAC,CAACE,WAAW,IAAI,CAAC;YACrC,IAAIc,CAAC,GAAGkF,MAAM,CAACoE,MAAI,CAACjD,UAAU,CAAC;YAC/B,IAAI,CAACiD,MAAI,CAACpK,WAAW,IAAIN,CAAC,GAAGoB,CAAC,GAAGnB,CAAC,GAAGmB,CAAC,EAAE;cACtCsJ,MAAI,CAAClF,IAAI,GAAGkF,MAAI,CAAC1E,GAAG;cACpB;YACF;YACAhG,CAAC,GAAGoB,CAAC,KAAKnB,CAAC,GAAGA,CAAC,GAAGD,CAAC,GAAGoB,CAAC,EAAEpB,CAAC,GAAGoB,CAAC,CAAC,EAAEnB,CAAC,GAAGmB,CAAC,KAAKpB,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGmB,CAAC,EAAEnB,CAAC,GAAGmB,CAAC,CAAC,EAAEsJ,MAAI,CAACf,qBAAqB,CAAC9J,CAAC,EAAE6K,MAAI,CAACpK,WAAW,EAAEN,CAAC,EAAEC,CAAC,CAAC;UACzH,CAAC,CAAC,CAACQ,KAAK,CAAC,UAACL,CAAC,EAAK;YACdsK,MAAI,CAACG,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,EAAEH,MAAI,CAACG,KAAK,CAAC,gBAAgB,EAAEzK,CAAC,CAAC;UAClE,CAAC,CAAC;QACJ,CAAC,EAAEP,CAAC,CAACkL,OAAO,GAAG,UAAC/K,CAAC,EAAK;UACpB0K,MAAI,CAACG,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC,EAAEH,MAAI,CAACG,KAAK,CAAC,gBAAgB,EAAE7K,CAAC,CAAC;QAClE,CAAC,EAAE,IAAI,CAACgG,GAAG,CAACgF,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,KAAKnL,CAAC,CAACoL,WAAW,GAAG,EAAE,CAAC,EAAE,IAAI,CAACtC,IAAI,EAAE;UACtE,IAAI5I,CAAC,GAAG,IAAIsB,cAAc,CAAC,CAAC;UAC5BtB,CAAC,CAACe,MAAM,GAAG,YAAW;YACpB,IAAId,CAAC,GAAGuK,GAAG,CAACC,eAAe,CAAC,IAAI,CAACjJ,QAAQ,CAAC;YAC1C1B,CAAC,CAACa,GAAG,GAAGV,CAAC;UACX,CAAC,EAAED,CAAC,CAACyB,IAAI,CAAC,KAAK,EAAE,IAAI,CAACwE,GAAG,EAAE,CAAC,CAAC,CAAC,EAAEjG,CAAC,CAAC0B,YAAY,GAAG,MAAM,EAAE1B,CAAC,CAAC2B,IAAI,CAAC,CAAC;QACnE,CAAC,MACC7B,CAAC,CAACa,GAAG,GAAG,IAAI,CAACsF,GAAG;MACpB,CAAC;MACD;MACAkF,SAASA,CAACrL,CAAC,EAAE;QACX,IAAIA,CAAC,CAACsL,cAAc,CAAC,CAAC,EAAE,IAAI,CAACnH,IAAI,IAAI,CAAC,IAAI,CAACG,IAAI,EAAE;UAC/C,IAAI,CAAC,IAAI,CAACgD,OAAO,EACf,OAAO,CAAC,CAAC;UACX,IAAI,CAAClD,KAAK,GAAG,CAAC,SAAS,IAAIpE,CAAC,GAAGA,CAAC,CAACuL,OAAO,GAAGvL,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACgG,OAAO,IAAI,IAAI,CAACxM,CAAC,EAAE,IAAI,CAACsF,KAAK,GAAG,CAAC,SAAS,IAAIrE,CAAC,GAAGA,CAAC,CAACwL,OAAO,GAAGxL,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACiG,OAAO,IAAI,IAAI,CAAC3M,CAAC,EAAEmB,CAAC,CAACuF,OAAO,IAAIkD,MAAM,CAACgD,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACC,OAAO,CAAC,EAAEjD,MAAM,CAACgD,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACE,QAAQ,CAAC,EAAE3L,CAAC,CAACuF,OAAO,CAACtD,MAAM,IAAI,CAAC,KAAK,IAAI,CAACsD,OAAO,GAAGvF,CAAC,CAACuF,OAAO,EAAEkD,MAAM,CAACgD,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACG,UAAU,CAAC,EAAEnD,MAAM,CAACgD,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACI,gBAAgB,CAAC,CAAC,KAAKpD,MAAM,CAACgD,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACC,OAAO,CAAC,EAAEjD,MAAM,CAACgD,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACE,QAAQ,CAAC,CAAC,EAAE,IAAI,CAACX,KAAK,CAAC,YAAY,EAAE;YACrjBc,MAAM,EAAE,CAAC,CAAC;YACVC,IAAI,EAAE,IAAI,CAACC,UAAU,CAAC;UACxB,CAAC,CAAC;QACJ,CAAC,MACC,IAAI,CAACzH,QAAQ,GAAG,CAAC,CAAC,EAAEkE,MAAM,CAACgD,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACQ,UAAU,CAAC,EAAExD,MAAM,CAACgD,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACS,OAAO,CAAC,EAAEzD,MAAM,CAACgD,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACQ,UAAU,CAAC,EAAExD,MAAM,CAACgD,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACS,OAAO,CAAC,EAAE,IAAI,CAAC9G,YAAY,GAAGpF,CAAC,CAACmM,OAAO,GAAGnM,CAAC,CAACmM,OAAO,GAAGnM,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAAC6G,KAAK,GAAG,IAAI,CAACC,KAAK,CAACC,OAAO,CAACC,UAAU,EAAE,IAAI,CAAClH,YAAY,GAAGrF,CAAC,CAACwM,OAAO,GAAGxM,CAAC,CAACwM,OAAO,GAAGxM,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACkH,KAAK,GAAG,IAAI,CAACJ,KAAK,CAACC,OAAO,CAACI,SAAS,EAAE,IAAI,CAAC1H,KAAK,GAAG,SAAS,IAAIhF,CAAC,GAAGA,CAAC,CAACuL,OAAO,GAAGvL,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACgG,OAAO,EAAE,IAAI,CAACtG,KAAK,GAAG,SAAS,IAAIjF,CAAC,GAAGA,CAAC,CAACwL,OAAO,GAAGxL,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACiG,OAAO,EAAE,IAAI,CAACtG,WAAW,GAAG,IAAI,CAACE,YAAY,EAAE,IAAI,CAACD,WAAW,GAAG,IAAI,CAACE,YAAY,EAAE,IAAI,CAACb,KAAK,GAAG,CAAC,EAAE,IAAI,CAACC,KAAK,GAAG,CAAC;MACppB,CAAC;MACD;MACAmH,UAAUA,CAAC5L,CAAC,EAAE;QAAA,IAAA2M,MAAA;QACZ3M,CAAC,CAACsL,cAAc,CAAC,CAAC;QAClB,IAAIpL,CAAC,GAAG,IAAI,CAAC6D,KAAK;QAClB,IAAI5D,CAAC,GAAG;YACNpB,CAAC,EAAE,IAAI,CAACwG,OAAO,CAAC,CAAC,CAAC,CAACgG,OAAO;YAC1B1M,CAAC,EAAE,IAAI,CAAC0G,OAAO,CAAC,CAAC,CAAC,CAACiG;UACrB,CAAC;UAAEpL,CAAC,GAAG;YACLrB,CAAC,EAAEiB,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACgG,OAAO;YACvB1M,CAAC,EAAEmB,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACiG;UAClB,CAAC;UAAEjL,CAAC,GAAG;YACLxB,CAAC,EAAE,IAAI,CAACwG,OAAO,CAAC,CAAC,CAAC,CAACgG,OAAO;YAC1B1M,CAAC,EAAE,IAAI,CAAC0G,OAAO,CAAC,CAAC,CAAC,CAACiG;UACrB,CAAC;UAAEjK,CAAC,GAAG;YACLxC,CAAC,EAAEiB,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACgG,OAAO;YACvB1M,CAAC,EAAEmB,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACiG;UAClB,CAAC;UAAEtK,CAAC,GAAGyH,IAAI,CAACiE,IAAI,CACdjE,IAAI,CAACkE,GAAG,CAAC1M,CAAC,CAACpB,CAAC,GAAGwB,CAAC,CAACxB,CAAC,EAAE,CAAC,CAAC,GAAG4J,IAAI,CAACkE,GAAG,CAAC1M,CAAC,CAACtB,CAAC,GAAG0B,CAAC,CAAC1B,CAAC,EAAE,CAAC,CAChD,CAAC;UAAE8D,CAAC,GAAGgG,IAAI,CAACiE,IAAI,CACdjE,IAAI,CAACkE,GAAG,CAACzM,CAAC,CAACrB,CAAC,GAAGwC,CAAC,CAACxC,CAAC,EAAE,CAAC,CAAC,GAAG4J,IAAI,CAACkE,GAAG,CAACzM,CAAC,CAACvB,CAAC,GAAG0C,CAAC,CAAC1C,CAAC,EAAE,CAAC,CAChD,CAAC;UAAE+D,CAAC,GAAGD,CAAC,GAAGzB,CAAC;UAAE2B,CAAC,GAAG,CAAC;QACnBA,CAAC,GAAGA,CAAC,GAAG,IAAI,CAACoB,SAAS,GAAGpB,CAAC,GAAG,IAAI,CAACqB,UAAU,GAAGrB,CAAC,GAAG,IAAI,CAACqB,UAAU,GAAGrB,CAAC,GAAG,IAAI,CAACoB,SAAS,EAAEpB,CAAC,GAAGA,CAAC,GAAG,GAAG,GAAG,GAAG,GAAGA,CAAC;QAC9G,IAAIC,CAAC,GAAGD,CAAC,GAAGD,CAAC;QACb,IAAI,CAAC,IAAI,CAAC4C,QAAQ,EAAE;UAClB,IAAI,IAAI,CAACA,QAAQ,GAAG,CAAC,CAAC,EAAE5C,CAAC,GAAG,CAAC,GAAG1C,CAAC,IAAIyI,IAAI,CAACC,GAAG,CAAC9F,CAAC,CAAC,GAAGF,CAAC,GAAG,CAAC,IAAI1C,CAAC,GAAGyI,IAAI,CAACC,GAAG,CAAC9F,CAAC,CAAC,KAAK5C,CAAC,IAAIyI,IAAI,CAACC,GAAG,CAAC9F,CAAC,CAAC,CAAC,EAAE,IAAI,CAACyC,OAAO,GAAGvF,CAAC,CAACuF,OAAO,EAAEuH,UAAU,CAAC,YAAM;YAC5IH,MAAI,CAACnH,QAAQ,GAAG,CAAC,CAAC;UACpB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,IAAI,CAACuH,eAAe,CAAC,IAAI,CAAChO,CAAC,EAAE,IAAI,CAACF,CAAC,EAAEqB,CAAC,CAAC,EAC7C,OAAO,CAAC,CAAC;UACX,IAAI,CAAC6D,KAAK,GAAG7D,CAAC;QAChB;MACF,CAAC;MACD2L,gBAAgBA,CAAC7L,CAAC,EAAE;QAClByI,MAAM,CAACuE,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACpB,UAAU,CAAC;MAC1D,CAAC;MACD;MACAF,OAAOA,CAAC1L,CAAC,EAAE;QAAA,IAAAiN,MAAA;QACT,IAAIjN,CAAC,CAACsL,cAAc,CAAC,CAAC,EAAEtL,CAAC,CAACuF,OAAO,IAAIvF,CAAC,CAACuF,OAAO,CAACtD,MAAM,KAAK,CAAC,EACzD,OAAO,IAAI,CAACsD,OAAO,GAAGvF,CAAC,CAACuF,OAAO,EAAEkD,MAAM,CAACgD,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACG,UAAU,CAAC,EAAEnD,MAAM,CAACgD,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACI,gBAAgB,CAAC,EAAEpD,MAAM,CAACuE,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACtB,OAAO,CAAC,EAAE,CAAC,CAAC;QAC/M,IAAIxL,CAAC,GAAG,SAAS,IAAIF,CAAC,GAAGA,CAAC,CAACuL,OAAO,GAAGvL,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACgG,OAAO;UAAEpL,CAAC,GAAG,SAAS,IAAIH,CAAC,GAAGA,CAAC,CAACwL,OAAO,GAAGxL,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACiG,OAAO;UAAEpL,CAAC;UAAEG,CAAC;QACtHH,CAAC,GAAGF,CAAC,GAAG,IAAI,CAACkE,KAAK,EAAE7D,CAAC,GAAGJ,CAAC,GAAG,IAAI,CAACkE,KAAK,EAAE,IAAI,CAAC6I,SAAS,CAAC,YAAM;UAC3D,IAAID,MAAI,CAACxF,SAAS,EAAE;YAClB,IAAIlG,CAAC,GAAG0L,MAAI,CAACjB,UAAU,CAAC5L,CAAC,EAAEG,CAAC,EAAE0M,MAAI,CAAClJ,KAAK,CAAC;cAAE7C,CAAC,GAAG+L,MAAI,CAACE,WAAW,CAAC,CAAC;cAAExK,CAAC,GAAGsK,MAAI,CAAC/I,UAAU,GAAG+I,MAAI,CAAClJ,KAAK;cAAEnB,CAAC,GAAGqK,MAAI,CAAChJ,SAAS,GAAGgJ,MAAI,CAAClJ,KAAK;cAAElB,CAAC;cAAEC,CAAC;cAAEC,CAAC;cAAEC,CAAC;YAChJ,QAAQiK,MAAI,CAACxH,MAAM;cACjB,KAAK,CAAC;cACN,KAAK,CAAC,CAAC;cACP,KAAK,CAAC;cACN,KAAK,CAAC,CAAC;gBACL5C,CAAC,GAAGoK,MAAI,CAAC7H,YAAY,GAAG6H,MAAI,CAAChJ,SAAS,IAAI,CAAC,GAAGgJ,MAAI,CAAClJ,KAAK,CAAC,GAAG,CAAC,GAAG,CAACpB,CAAC,GAAGC,CAAC,IAAI,CAAC,EAAEE,CAAC,GAAGmK,MAAI,CAAC5H,YAAY,GAAG4H,MAAI,CAAC/I,UAAU,IAAI,CAAC,GAAG+I,MAAI,CAAClJ,KAAK,CAAC,GAAG,CAAC,GAAG,CAACnB,CAAC,GAAGD,CAAC,IAAI,CAAC,EAAEI,CAAC,GAAGF,CAAC,GAAGF,CAAC,GAAGsK,MAAI,CAACzI,KAAK,EAAExB,CAAC,GAAGF,CAAC,GAAGF,CAAC,GAAGqK,MAAI,CAACxI,KAAK;gBACzM;cACF;gBACE5B,CAAC,GAAGoK,MAAI,CAAC7H,YAAY,GAAG6H,MAAI,CAAChJ,SAAS,IAAI,CAAC,GAAGgJ,MAAI,CAAClJ,KAAK,CAAC,GAAG,CAAC,EAAEjB,CAAC,GAAGmK,MAAI,CAAC5H,YAAY,GAAG4H,MAAI,CAAC/I,UAAU,IAAI,CAAC,GAAG+I,MAAI,CAAClJ,KAAK,CAAC,GAAG,CAAC,EAAEhB,CAAC,GAAGF,CAAC,GAAGD,CAAC,GAAGqK,MAAI,CAACzI,KAAK,EAAExB,CAAC,GAAGF,CAAC,GAAGH,CAAC,GAAGsK,MAAI,CAACxI,KAAK;gBAC7K;YACJ;YACAlD,CAAC,CAAC6L,EAAE,IAAIlM,CAAC,CAACkM,EAAE,KAAKhN,CAAC,GAAGyC,CAAC,CAAC,EAAEtB,CAAC,CAAC8L,EAAE,IAAInM,CAAC,CAACmM,EAAE,KAAK9M,CAAC,GAAGuC,CAAC,CAAC,EAAEvB,CAAC,CAAC+L,EAAE,IAAIpM,CAAC,CAACoM,EAAE,KAAKlN,CAAC,GAAG2C,CAAC,CAAC,EAAExB,CAAC,CAACgM,EAAE,IAAIrM,CAAC,CAACqM,EAAE,KAAKhN,CAAC,GAAGyC,CAAC,CAAC;UACpG;UACAiK,MAAI,CAAClO,CAAC,GAAGqB,CAAC,EAAE6M,MAAI,CAACpO,CAAC,GAAG0B,CAAC,EAAE0M,MAAI,CAACjC,KAAK,CAAC,YAAY,EAAE;YAC/Cc,MAAM,EAAE,CAAC,CAAC;YACVC,IAAI,EAAEkB,MAAI,CAACjB,UAAU,CAAC;UACxB,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC;MACD;MACAL,QAAQA,CAAC3L,CAAC,EAAE;QACVyI,MAAM,CAACuE,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACtB,OAAO,CAAC,EAAEjD,MAAM,CAACuE,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACtB,OAAO,CAAC,EAAEjD,MAAM,CAACuE,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACrB,QAAQ,CAAC,EAAElD,MAAM,CAACuE,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACrB,QAAQ,CAAC,EAAE,IAAI,CAACX,KAAK,CAAC,YAAY,EAAE;UAClPc,MAAM,EAAE,CAAC,CAAC;UACVC,IAAI,EAAE,IAAI,CAACC,UAAU,CAAC;QACxB,CAAC,CAAC;MACJ,CAAC;MACD;MACAwB,QAAQA,CAAA,EAAG;QACT,IAAI,CAAC3G,QAAQ,IAAI4B,MAAM,CAACgD,gBAAgB,CAAC,IAAI,CAACnG,OAAO,EAAE,IAAI,CAACmI,UAAU,EAAE,IAAI,CAACzE,OAAO,CAAC;MACvF,CAAC;MACD;MACA0E,WAAWA,CAAA,EAAG;QACZ,IAAI,CAAC7G,QAAQ,IAAI4B,MAAM,CAACuE,mBAAmB,CAAC,IAAI,CAAC1H,OAAO,EAAE,IAAI,CAACmI,UAAU,CAAC;MAC5E,CAAC;MACD;MACAA,UAAUA,CAACzN,CAAC,EAAE;QAAA,IAAA2N,MAAA;QACZ3N,CAAC,CAACsL,cAAc,CAAC,CAAC;QAClB,IAAIpL,CAAC,GAAG,IAAI,CAAC6D,KAAK;QAClB,IAAI5D,CAAC,GAAGH,CAAC,CAAC4N,MAAM,IAAI5N,CAAC,CAAC6N,UAAU;UAAEzN,CAAC,GAAGsJ,SAAS,CAACC,SAAS,CAACmE,OAAO,CAAC,SAAS,CAAC;QAC5E3N,CAAC,GAAGC,CAAC,GAAG,CAAC,GAAGD,CAAC,GAAG,EAAE,GAAGA,CAAC,EAAE,IAAI,CAAC2I,IAAI,KAAK3I,CAAC,GAAG,CAACA,CAAC,CAAC;QAC7C,IAAII,CAAC,GAAG,IAAI,CAACqF,GAAG;QAChBrF,CAAC,GAAGA,CAAC,GAAG,IAAI,CAAC0D,SAAS,GAAG1D,CAAC,GAAG,IAAI,CAAC2D,UAAU,GAAG3D,CAAC,GAAG,IAAI,CAAC2D,UAAU,GAAG3D,CAAC,GAAG,IAAI,CAAC0D,SAAS;QACvF,IAAI1C,CAAC,GAAGhB,CAAC,GAAGJ,CAAC;QACboB,CAAC,GAAG,CAAC,GAAGrB,CAAC,IAAIyI,IAAI,CAACC,GAAG,CAACrH,CAAC,CAAC,GAAGrB,CAAC,GAAGyI,IAAI,CAACC,GAAG,CAACrH,CAAC,CAAC,KAAKrB,CAAC,IAAIyI,IAAI,CAACC,GAAG,CAACrH,CAAC,CAAC,CAAC;QAChE,IAAIL,CAAC,GAAGK,CAAC,GAAG,CAAC,GAAG,KAAK,GAAG,QAAQ;QAChC,IAAIL,CAAC,KAAK,IAAI,CAAC6E,SAAS,KAAK,IAAI,CAACA,SAAS,GAAG7E,CAAC,EAAE,IAAI,CAAC0E,GAAG,GAAG,GAAG,CAAC,EAAE,IAAI,CAACC,OAAO,KAAK,IAAI,CAACC,UAAU,GAAGgH,UAAU,CAAC,YAAM;UACpHa,MAAI,CAAC9H,OAAO,GAAG,CAAC,CAAC,EAAE8H,MAAI,CAAC/H,GAAG,GAAG+H,MAAI,CAAC/H,GAAG,IAAI,IAAI;QAChD,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,IAAI,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,IAAI,CAACkH,eAAe,CAAC,IAAI,CAAChO,CAAC,EAAE,IAAI,CAACF,CAAC,EAAEqB,CAAC,CAAC,EAClE,OAAO,CAAC,CAAC;QACX,IAAI,CAAC6D,KAAK,GAAG7D,CAAC;MAChB,CAAC;MACD;MACA6N,WAAWA,CAAC/N,CAAC,EAAE;QACb,IAAIE,CAAC,GAAG,IAAI,CAAC6D,KAAK;QAClB/D,CAAC,GAAGA,CAAC,IAAI,CAAC;QACV,IAAIG,CAAC,GAAG,EAAE;QACV,IAAIA,CAAC,GAAGA,CAAC,GAAG,IAAI,CAAC8D,SAAS,GAAG9D,CAAC,GAAG,IAAI,CAAC+D,UAAU,GAAG/D,CAAC,GAAG,IAAI,CAAC+D,UAAU,GAAG/D,CAAC,GAAG,IAAI,CAAC8D,SAAS,EAAEjE,CAAC,GAAGA,CAAC,GAAGG,CAAC,EAAEH,CAAC,GAAG,CAAC,GAAGE,CAAC,IAAIyI,IAAI,CAACC,GAAG,CAAC5I,CAAC,CAAC,GAAGE,CAAC,GAAGyI,IAAI,CAACC,GAAG,CAAC5I,CAAC,CAAC,KAAKE,CAAC,IAAIyI,IAAI,CAACC,GAAG,CAAC5I,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC+M,eAAe,CAAC,IAAI,CAAChO,CAAC,EAAE,IAAI,CAACF,CAAC,EAAEqB,CAAC,CAAC,EAChN,OAAO,CAAC,CAAC;QACX,IAAI,CAAC6D,KAAK,GAAG7D,CAAC;MAChB,CAAC;MACD;MACA+L,UAAUA,CAACjM,CAAC,EAAE;QAAA,IAAAgO,MAAA;QACZhO,CAAC,CAACsL,cAAc,CAAC,CAAC;QAClB,IAAIpL,CAAC,GAAG,SAAS,IAAIF,CAAC,GAAGA,CAAC,CAACuL,OAAO,GAAGvL,CAAC,CAACuF,OAAO,GAAGvF,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACgG,OAAO,GAAG,CAAC;UAAEpL,CAAC,GAAG,SAAS,IAAIH,CAAC,GAAGA,CAAC,CAACwL,OAAO,GAAGxL,CAAC,CAACuF,OAAO,GAAGvF,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACiG,OAAO,GAAG,CAAC;QAChJ,IAAI,CAAC0B,SAAS,CAAC,YAAM;UACnB,IAAI9M,CAAC,GAAGF,CAAC,GAAG8N,MAAI,CAAChJ,KAAK;YAAEzE,CAAC,GAAGJ,CAAC,GAAG6N,MAAI,CAAC/I,KAAK;UAC1C,IAAI7E,CAAC,GAAG,CAAC,IAAI4N,MAAI,CAACxJ,KAAK,GAAGpE,CAAC,GAAG4N,MAAI,CAAC9I,WAAW,GAAG8I,MAAI,CAAC7O,CAAC,GAAG6O,MAAI,CAAC7O,CAAC,GAAG6O,MAAI,CAAC9I,WAAW,GAAG9E,CAAC,EAAE4N,MAAI,CAAC5I,YAAY,GAAG4I,MAAI,CAAC9I,WAAW,KAAK8I,MAAI,CAACxJ,KAAK,GAAGwJ,MAAI,CAAC7O,CAAC,GAAG6O,MAAI,CAAC9I,WAAW,GAAGyD,IAAI,CAACC,GAAG,CAACxI,CAAC,CAAC,GAAG4N,MAAI,CAAC7O,CAAC,GAAG6O,MAAI,CAAC9I,WAAW,GAAGyD,IAAI,CAACC,GAAG,CAACxI,CAAC,CAAC,EAAE4N,MAAI,CAAC5I,YAAY,GAAG4I,MAAI,CAAC9I,WAAW,GAAG9E,CAAC,GAAG,CAAC,GAAG4N,MAAI,CAAC9I,WAAW,GAAG9E,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC4N,MAAI,CAAC/G,KAAK,EACrT1G,CAAC,GAAG,CAAC,IAAIyN,MAAI,CAACvJ,KAAK,GAAGlE,CAAC,GAAGyN,MAAI,CAAC7I,WAAW,GAAG6I,MAAI,CAAC9M,CAAC,GAAG8M,MAAI,CAAC9M,CAAC,GAAG8M,MAAI,CAAC7I,WAAW,GAAG5E,CAAC,EAAEyN,MAAI,CAAC3I,YAAY,GAAG2I,MAAI,CAAC7I,WAAW,KAAK6I,MAAI,CAACvJ,KAAK,GAAGuJ,MAAI,CAAC9M,CAAC,GAAG8M,MAAI,CAAC7I,WAAW,GAAGwD,IAAI,CAACC,GAAG,CAACrI,CAAC,CAAC,GAAGyN,MAAI,CAAC9M,CAAC,GAAG8M,MAAI,CAAC7I,WAAW,GAAGwD,IAAI,CAACC,GAAG,CAACrI,CAAC,CAAC,EAAEyN,MAAI,CAAC3I,YAAY,GAAG2I,MAAI,CAAC7I,WAAW,GAAG5E,CAAC,GAAG,CAAC,GAAGyN,MAAI,CAAC7I,WAAW,GAAG5E,CAAC,GAAG,CAAC,CAAC,CAAC,KACpS;YACH,IAAIgB,CAAC,GAAGyM,MAAI,CAACxJ,KAAK,GAAGwJ,MAAI,CAAC9G,WAAW,CAAC,CAAC,CAAC,GAAG8G,MAAI,CAAC9G,WAAW,CAAC,CAAC,CAAC;YAC9D3F,CAAC,GAAGyM,MAAI,CAAC3I,YAAY,GAAG2I,MAAI,CAAC9M,CAAC,IAAI8M,MAAI,CAACvJ,KAAK,GAAGuJ,MAAI,CAAC9M,CAAC,GAAG8M,MAAI,CAAC3I,YAAY,EAAE2I,MAAI,CAACxJ,KAAK,GAAGwJ,MAAI,CAACvJ,KAAK,GAAGuJ,MAAI,CAAC9G,WAAW,CAAC,CAAC,CAAC,GAAG8G,MAAI,CAAC9G,WAAW,CAAC,CAAC,CAAC,EAAE9G,CAAC,GAAG,CAAC,GAAG4N,MAAI,CAAC5I,YAAY,GAAG4I,MAAI,CAAC9I,WAAW,GAAG8I,MAAI,CAAC5I,YAAY,GAAG4I,MAAI,CAAC9I,WAAW,GAAG8I,MAAI,CAACxJ,KAAK,IAAIwJ,MAAI,CAACvJ,KAAK,GAAGlD,CAAC,EAAEyM,MAAI,CAAC3I,YAAY,GAAG2I,MAAI,CAAC3I,YAAY;UAC3S;QACF,CAAC,CAAC;MACJ,CAAC;MACD;MACA4I,cAAcA,CAACjO,CAAC,EAAEE,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAE;QAC5BP,CAAC,CAACsL,cAAc,CAAC,CAAC,EAAE7C,MAAM,CAACgD,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACyC,aAAa,CAAC,EAAEzF,MAAM,CAACgD,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAAC0C,aAAa,CAAC,EAAE1F,MAAM,CAACgD,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACyC,aAAa,CAAC,EAAEzF,MAAM,CAACgD,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAAC0C,aAAa,CAAC,EAAE,IAAI,CAACvJ,UAAU,GAAG1E,CAAC,EAAE,IAAI,CAAC2E,UAAU,GAAG1E,CAAC,EAAE,IAAI,CAAC2E,eAAe,GAAG1E,CAAC,EAAE,IAAI,CAAC2E,eAAe,GAAGxE,CAAC,EAAE,IAAI,CAACyE,KAAK,GAAG,SAAS,IAAIhF,CAAC,GAAGA,CAAC,CAACuL,OAAO,GAAGvL,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACgG,OAAO,EAAE,IAAI,CAACtG,KAAK,GAAG,SAAS,IAAIjF,CAAC,GAAGA,CAAC,CAACwL,OAAO,GAAGxL,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACiG,OAAO,EAAE,IAAI,CAAC9G,QAAQ,GAAG,IAAI,CAACF,KAAK,EAAE,IAAI,CAACG,QAAQ,GAAG,IAAI,CAACF,KAAK,EAAE,IAAI,CAACS,WAAW,GAAG,IAAI,CAACE,YAAY,EAAE,IAAI,CAACD,WAAW,GAAG,IAAI,CAACE,YAAY,EAAE,IAAI,CAAC4B,KAAK,IAAI,IAAI,CAACrC,UAAU,IAAI,IAAI,CAACC,UAAU,KAAK,IAAI,CAACA,UAAU,GAAG,CAAC,CAAC,EAAE,IAAI,CAACmG,KAAK,CAAC,kBAAkB,EAAE;UACnsBzC,KAAK,EAAE,IAAI,CAAC/D,KAAK;UACjBgE,MAAM,EAAE,IAAI,CAAC/D;QACf,CAAC,CAAC;MACJ,CAAC;MACD;MACAyJ,aAAaA,CAAClO,CAAC,EAAE;QAAA,IAAAoO,MAAA;QACfpO,CAAC,CAACsL,cAAc,CAAC,CAAC;QAClB,IAAIpL,CAAC,GAAG,SAAS,IAAIF,CAAC,GAAGA,CAAC,CAACuL,OAAO,GAAGvL,CAAC,CAACuF,OAAO,GAAGvF,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACgG,OAAO,GAAG,CAAC;UAAEpL,CAAC,GAAG,SAAS,IAAIH,CAAC,GAAGA,CAAC,CAACwL,OAAO,GAAGxL,CAAC,CAACuF,OAAO,GAAGvF,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACiG,OAAO,GAAG,CAAC;QAChJ,IAAIpL,CAAC,GAAG,IAAI,CAACjB,CAAC;UAAEoB,CAAC,GAAG,IAAI,CAACW,CAAC;UAAEK,CAAC,GAAG,CAAC;UAAEL,CAAC,GAAG,CAAC;QACxC,IAAI,IAAI,CAACuG,SAAS,EAAE;UAClB,IAAI5E,CAAC,GAAG,IAAI,CAACmJ,UAAU,CAAC,CAAC;YAAElJ,CAAC,GAAGD,CAAC,CAACyK,EAAE;YAAEvK,CAAC,GAAGF,CAAC,CAAC0K,EAAE;UAC7ChM,CAAC,GAAGsB,CAAC,CAACuK,EAAE,GAAG,CAAC,GAAGvK,CAAC,CAACuK,EAAE,GAAG,CAAC,EAAElM,CAAC,GAAG2B,CAAC,CAACwK,EAAE,GAAG,CAAC,GAAGxK,CAAC,CAACwK,EAAE,GAAG,CAAC,EAAEjN,CAAC,GAAG0C,CAAC,KAAK1C,CAAC,GAAG0C,CAAC,CAAC,EAAEvC,CAAC,GAAGwC,CAAC,KAAKxC,CAAC,GAAGwC,CAAC,CAAC;QACtF;QACA,IAAAsL,qBAAA,GAAe,IAAI,CAACC,kBAAkB,CAAC,CAAC;UAAAC,sBAAA,GAAA7K,cAAA,CAAA2K,qBAAA;UAAjC1L,CAAC,GAAA4L,sBAAA;UAAE3L,CAAC,GAAA2L,sBAAA;QACX,IAAI,CAACrB,SAAS,CAAC,YAAM;UACnB,IAAIrK,CAAC,GAAG3C,CAAC,GAAGkO,MAAI,CAACpJ,KAAK;YAAElC,CAAC,GAAG3C,CAAC,GAAGiO,MAAI,CAACnJ,KAAK;UAC1C,IAAImJ,MAAI,CAACxJ,UAAU,KAAKwJ,MAAI,CAACtJ,eAAe,KAAK,CAAC,GAAGsJ,MAAI,CAAC1J,QAAQ,GAAG7B,CAAC,GAAGF,CAAC,IAAIyL,MAAI,CAAC5J,KAAK,GAAG7B,CAAC,EAAEyL,MAAI,CAAChJ,YAAY,GAAGgJ,MAAI,CAAC1J,QAAQ,GAAG0J,MAAI,CAAClJ,WAAW,GAAG3D,CAAC,GAAGoB,CAAC,IAAIyL,MAAI,CAAC1J,QAAQ,GAAG7B,CAAC,GAAG,CAAC,IAAIuL,MAAI,CAAC5J,KAAK,GAAGpE,CAAC,GAAGgO,MAAI,CAAClJ,WAAW,GAAGrC,CAAC,IAAIzC,CAAC,GAAGmB,CAAC,GAAG6M,MAAI,CAAC1J,QAAQ,GAAG7B,CAAC,GAAGuL,MAAI,CAAC1J,QAAQ,GAAG0J,MAAI,CAAClJ,WAAW,GAAG3D,CAAC,EAAE6M,MAAI,CAAChJ,YAAY,GAAGhF,CAAC,GAAGgO,MAAI,CAAClJ,WAAW,GAAGrC,CAAC,IAAIzC,CAAC,GAAGmB,CAAC,GAAG6M,MAAI,CAAClJ,WAAW,GAAGrC,CAAC,GAAGtB,CAAC,KAAK6M,MAAI,CAAC5J,KAAK,GAAGmE,IAAI,CAACC,GAAG,CAAC/F,CAAC,CAAC,GAAGuL,MAAI,CAAClJ,WAAW,IAAI9E,CAAC,GAAGuI,IAAI,CAACC,GAAG,CAAC/F,CAAC,CAAC,GAAGuL,MAAI,CAAC1J,QAAQ,GAAGtE,CAAC,GAAGgO,MAAI,CAAC1J,QAAQ,GAAG0J,MAAI,CAAClJ,WAAW,EAAEkJ,MAAI,CAAChJ,YAAY,GAAGgJ,MAAI,CAAClJ,WAAW,GAAGkJ,MAAI,CAAC1J,QAAQ,CAAC,GAAG0J,MAAI,CAACtJ,eAAe,KAAK,CAAC,KAAKsJ,MAAI,CAAC1J,QAAQ,GAAG7B,CAAC,GAAGF,CAAC,GAAGyL,MAAI,CAAC5J,KAAK,GAAG7B,CAAC,GAAGyL,MAAI,CAAC1J,QAAQ,GAAG7B,CAAC,GAAG,CAAC,IAAIuL,MAAI,CAAC5J,KAAK,GAAG4J,MAAI,CAAC1J,QAAQ,GAAG7B,CAAC,GAAGuL,MAAI,CAAChJ,YAAY,IAAIhF,CAAC,GAAGgO,MAAI,CAAC1J,QAAQ,GAAG7B,CAAC,GAAGzC,CAAC,GAAGgO,MAAI,CAAChJ,YAAY,EAAEgJ,MAAI,CAAChJ,YAAY,GAAGgJ,MAAI,CAAClJ,WAAW,KAAKkJ,MAAI,CAAC5J,KAAK,GAAGpE,CAAC,GAAGgO,MAAI,CAAClJ,WAAW,GAAGyD,IAAI,CAACC,GAAG,CAAC/F,CAAC,GAAGuL,MAAI,CAAC1J,QAAQ,CAAC,IAAItE,CAAC,GAAGmB,CAAC,GAAGoH,IAAI,CAACC,GAAG,CAAC/F,CAAC,GAAGuL,MAAI,CAAC1J,QAAQ,CAAC,GAAG0J,MAAI,CAAClJ,WAAW,GAAG3D,CAAC,EAAE6M,MAAI,CAAChJ,YAAY,GAAGhF,CAAC,GAAGgO,MAAI,CAAClJ,WAAW,GAAGyD,IAAI,CAACC,GAAG,CAAC/F,CAAC,GAAGuL,MAAI,CAAC1J,QAAQ,CAAC,IAAItE,CAAC,GAAGmB,CAAC,GAAG6M,MAAI,CAAClJ,WAAW,GAAGyD,IAAI,CAACC,GAAG,CAAC/F,CAAC,GAAGuL,MAAI,CAAC1J,QAAQ,CAAC,GAAGnD,CAAC,CAAC,CAAC,CAAC,EAAE6M,MAAI,CAACvJ,UAAU,KAAKuJ,MAAI,CAACrJ,eAAe,KAAK,CAAC,GAAGqJ,MAAI,CAACzJ,QAAQ,GAAG7B,CAAC,GAAGF,CAAC,IAAIwL,MAAI,CAAC3J,KAAK,GAAG7B,CAAC,EAAEwL,MAAI,CAAC/I,YAAY,GAAG+I,MAAI,CAACzJ,QAAQ,GAAGyJ,MAAI,CAACjJ,WAAW,GAAGjE,CAAC,GAAG0B,CAAC,IAAIwL,MAAI,CAACzJ,QAAQ,GAAG7B,CAAC,GAAG,CAAC,IAAIsL,MAAI,CAAC3J,KAAK,GAAGlE,CAAC,GAAG6N,MAAI,CAACjJ,WAAW,GAAGrC,CAAC,IAAIvC,CAAC,GAAGW,CAAC,GAAGkN,MAAI,CAACzJ,QAAQ,GAAG7B,CAAC,GAAGsL,MAAI,CAACzJ,QAAQ,GAAGyJ,MAAI,CAACjJ,WAAW,GAAGjE,CAAC,EAAEkN,MAAI,CAAC/I,YAAY,GAAG9E,CAAC,GAAG6N,MAAI,CAACjJ,WAAW,GAAGrC,CAAC,IAAIvC,CAAC,GAAGW,CAAC,GAAGkN,MAAI,CAACjJ,WAAW,GAAGrC,CAAC,GAAG5B,CAAC,KAAKkN,MAAI,CAAC3J,KAAK,GAAGkE,IAAI,CAACC,GAAG,CAAC9F,CAAC,CAAC,GAAGsL,MAAI,CAACjJ,WAAW,IAAI5E,CAAC,GAAGoI,IAAI,CAACC,GAAG,CAAC9F,CAAC,CAAC,GAAGsL,MAAI,CAACzJ,QAAQ,GAAGpE,CAAC,GAAG6N,MAAI,CAACzJ,QAAQ,GAAGyJ,MAAI,CAACjJ,WAAW,EAAEiJ,MAAI,CAAC/I,YAAY,GAAG+I,MAAI,CAACjJ,WAAW,GAAGiJ,MAAI,CAACzJ,QAAQ,CAAC,GAAGyJ,MAAI,CAACrJ,eAAe,KAAK,CAAC,KAAKqJ,MAAI,CAACzJ,QAAQ,GAAG7B,CAAC,GAAGF,CAAC,GAAGwL,MAAI,CAAC3J,KAAK,GAAG7B,CAAC,GAAGwL,MAAI,CAACzJ,QAAQ,GAAG7B,CAAC,GAAG,CAAC,IAAIsL,MAAI,CAAC3J,KAAK,GAAG2J,MAAI,CAACzJ,QAAQ,GAAG7B,CAAC,GAAGsL,MAAI,CAAC/I,YAAY,IAAI9E,CAAC,GAAG6N,MAAI,CAACzJ,QAAQ,GAAG7B,CAAC,GAAGvC,CAAC,GAAG6N,MAAI,CAAC/I,YAAY,EAAE+I,MAAI,CAAC/I,YAAY,GAAG+I,MAAI,CAACjJ,WAAW,KAAKiJ,MAAI,CAAC3J,KAAK,GAAGlE,CAAC,GAAG6N,MAAI,CAACjJ,WAAW,GAAGwD,IAAI,CAACC,GAAG,CAAC9F,CAAC,GAAGsL,MAAI,CAACzJ,QAAQ,CAAC,IAAIpE,CAAC,GAAGW,CAAC,GAAGyH,IAAI,CAACC,GAAG,CAAC9F,CAAC,GAAGsL,MAAI,CAACzJ,QAAQ,CAAC,GAAGyJ,MAAI,CAACjJ,WAAW,GAAGjE,CAAC,EAAEkN,MAAI,CAAC/I,YAAY,GAAG9E,CAAC,GAAG6N,MAAI,CAACjJ,WAAW,GAAGwD,IAAI,CAACC,GAAG,CAAC9F,CAAC,GAAGsL,MAAI,CAACzJ,QAAQ,CAAC,IAAIpE,CAAC,GAAGW,CAAC,GAAGkN,MAAI,CAACjJ,WAAW,GAAGwD,IAAI,CAACC,GAAG,CAAC9F,CAAC,GAAGsL,MAAI,CAACzJ,QAAQ,CAAC,GAAGzD,CAAC,CAAC,CAAC,CAAC,EAAEkN,MAAI,CAACxJ,UAAU,IAAIwJ,MAAI,CAACnH,KAAK,EAAE;YACnlE,IAAIlE,CAAC,GAAGqL,MAAI,CAAC5J,KAAK,GAAG4J,MAAI,CAAClH,WAAW,CAAC,CAAC,CAAC,GAAGkH,MAAI,CAAClH,WAAW,CAAC,CAAC,CAAC;YAC9DnE,CAAC,GAAGH,CAAC,IAAIwL,MAAI,CAAC3J,KAAK,GAAG7B,CAAC,EAAEwL,MAAI,CAAC5J,KAAK,GAAG4J,MAAI,CAAClH,WAAW,CAAC,CAAC,CAAC,GAAGtE,CAAC,GAAGwL,MAAI,CAAClH,WAAW,CAAC,CAAC,CAAC,EAAEkH,MAAI,CAACtJ,eAAe,KAAK,CAAC,KAAKsJ,MAAI,CAAChJ,YAAY,GAAGgJ,MAAI,CAAClJ,WAAW,IAAIkJ,MAAI,CAAC1J,QAAQ,GAAG0J,MAAI,CAAC5J,KAAK,CAAC,CAAC,IAAIzB,CAAC,GAAGqL,MAAI,CAAC/I,YAAY,GAAG9E,CAAC,IAAI6N,MAAI,CAAC3J,KAAK,GAAGlE,CAAC,GAAG6N,MAAI,CAAC/I,YAAY,EAAE+I,MAAI,CAAC5J,KAAK,GAAG4J,MAAI,CAAC3J,KAAK,GAAG2J,MAAI,CAAClH,WAAW,CAAC,CAAC,CAAC,GAAGkH,MAAI,CAAClH,WAAW,CAAC,CAAC,CAAC,EAAEkH,MAAI,CAACtJ,eAAe,KAAK,CAAC,KAAKsJ,MAAI,CAAChJ,YAAY,GAAGgJ,MAAI,CAAClJ,WAAW,IAAIkJ,MAAI,CAAC1J,QAAQ,GAAG0J,MAAI,CAAC5J,KAAK,CAAC,CAAC,IAAI4J,MAAI,CAAC3J,KAAK,GAAG1B,CAAC;UACzb;UACA,IAAIqL,MAAI,CAACvJ,UAAU,IAAIuJ,MAAI,CAACnH,KAAK,EAAE;YACjC,IAAIjE,CAAC,GAAGoL,MAAI,CAAC3J,KAAK,GAAG2J,MAAI,CAAClH,WAAW,CAAC,CAAC,CAAC,GAAGkH,MAAI,CAAClH,WAAW,CAAC,CAAC,CAAC;YAC9DlE,CAAC,GAAGL,CAAC,IAAIyL,MAAI,CAAC5J,KAAK,GAAG7B,CAAC,EAAEyL,MAAI,CAAC3J,KAAK,GAAG2J,MAAI,CAAClH,WAAW,CAAC,CAAC,CAAC,GAAGvE,CAAC,GAAGyL,MAAI,CAAClH,WAAW,CAAC,CAAC,CAAC,EAAEkH,MAAI,CAAC/I,YAAY,GAAG+I,MAAI,CAACzJ,QAAQ,GAAGyJ,MAAI,CAACjJ,WAAW,GAAGiJ,MAAI,CAAC3J,KAAK,IAAIzB,CAAC,GAAGoL,MAAI,CAAChJ,YAAY,GAAGhF,CAAC,IAAIgO,MAAI,CAAC5J,KAAK,GAAGpE,CAAC,GAAGgO,MAAI,CAAChJ,YAAY,EAAEgJ,MAAI,CAAC3J,KAAK,GAAG2J,MAAI,CAAC5J,KAAK,GAAG4J,MAAI,CAAClH,WAAW,CAAC,CAAC,CAAC,GAAGkH,MAAI,CAAClH,WAAW,CAAC,CAAC,CAAC,IAAIkH,MAAI,CAAC5J,KAAK,GAAGxB,CAAC;UAClT;QACF,CAAC,CAAC;MACJ,CAAC;MACDsL,kBAAkBA,CAAA,EAAG;QACnB,IAAatO,CAAC,GAAgC,IAAI,CAA5CwE,KAAK;UAAYtE,CAAC,GAAsB,IAAI,CAAlCuE,KAAK;UAAmBtE,CAAC,GAAK,IAAI,CAAxB6H,YAAY;UAAc5H,CAAC,GAAG,IAAI+G,KAAK,CAAC,CAAC;QACnE,OAAOA,KAAK,CAACe,OAAO,CAAC/H,CAAC,CAAC,GAAGC,CAAC,GAAGD,CAAC,GAAGC,CAAC,GAAG,CAACD,CAAC,EAAEA,CAAC,CAAC,EAAEH,CAAC,GAAGwO,UAAU,CAACpO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEF,CAAC,GAAGsO,UAAU,CAACpO,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAACJ,CAAC,EAAEE,CAAC,CAAC;MAClG,CAAC;MACD;MACAiO,aAAaA,CAACnO,CAAC,EAAE;QACfyI,MAAM,CAACuE,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACkB,aAAa,CAAC,EAAEzF,MAAM,CAACuE,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACmB,aAAa,CAAC,EAAE1F,MAAM,CAACuE,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACkB,aAAa,CAAC,EAAEzF,MAAM,CAACuE,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACmB,aAAa,CAAC;MACjP,CAAC;MACD;MACAM,aAAaA,CAACzO,CAAC,EAAEE,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAEgB,CAAC,EAAE;QAC9B,IAAML,CAAC,GAAGlB,CAAC,GAAGE,CAAC;QACf,IAAIyC,CAAC,GAAGpC,CAAC;UAAEqC,CAAC,GAAGrB,CAAC;QAChB,OAAOoB,CAAC,GAAGxC,CAAC,KAAKwC,CAAC,GAAGxC,CAAC,EAAEyC,CAAC,GAAG+F,IAAI,CAAC+F,IAAI,CAAC/L,CAAC,GAAGzB,CAAC,CAAC,CAAC,EAAE0B,CAAC,GAAGxC,CAAC,KAAKwC,CAAC,GAAGxC,CAAC,EAAEuC,CAAC,GAAGgG,IAAI,CAAC+F,IAAI,CAAC9L,CAAC,GAAG1B,CAAC,CAAC,EAAEyB,CAAC,GAAGxC,CAAC,KAAKwC,CAAC,GAAGxC,CAAC,EAAEyC,CAAC,GAAG+F,IAAI,CAAC+F,IAAI,CAAC/L,CAAC,GAAGzB,CAAC,CAAC,CAAC,CAAC,EAAEyB,CAAC,GAAGpC,CAAC,KAAKoC,CAAC,GAAGpC,CAAC,EAAEqC,CAAC,GAAG+F,IAAI,CAAC+F,IAAI,CAAC/L,CAAC,GAAGzB,CAAC,CAAC,CAAC,EAAE0B,CAAC,GAAGrB,CAAC,KAAKqB,CAAC,GAAGrB,CAAC,EAAEoB,CAAC,GAAGgG,IAAI,CAAC+F,IAAI,CAAC9L,CAAC,GAAG1B,CAAC,CAAC,CAAC,EAAE;UAAEqH,KAAK,EAAE5F,CAAC;UAAE6F,MAAM,EAAE5F;QAAE,CAAC;MACxO,CAAC;MACD;MACAsJ,OAAOA,CAAA,EAAG;QACR,IAAI,CAAC1H,KAAK,KAAK,CAAC,IAAI,IAAI,CAACC,KAAK,KAAK,CAAC,KAAK,IAAI,CAACF,QAAQ,GAAG,CAAC,CAAC,CAAC;QAC5D,IAAAoK,sBAAA,GAAa,IAAI,CAACL,kBAAkB,CAAC,CAAC;UAAAM,sBAAA,GAAAlL,cAAA,CAAAiL,sBAAA;UAAjC3O,CAAC,GAAA4O,sBAAA;UAAE1O,CAAC,GAAA0O,sBAAA;QACT,IAAAC,IAAA,GAAgC,IAAI,CAAC5H,KAAK,GAAG,IAAI,CAACwH,aAAa,CAC7D,IAAI,CAACvH,WAAW,CAAC,CAAC,CAAC,EACnB,IAAI,CAACA,WAAW,CAAC,CAAC,CAAC,EACnBlH,CAAC,EACDE,CAAC,EACD,IAAI,CAACsE,KAAK,EACV,IAAI,CAACC,KACP,CAAC,GAAG;YAAE8D,KAAK,EAAEvI,CAAC;YAAEwI,MAAM,EAAEtI;UAAE,CAAC;UAPZC,CAAC,GAAA0O,IAAA,CAARtG,KAAK;UAAanI,CAAC,GAAAyO,IAAA,CAATrG,MAAM;QAQxBrI,CAAC,GAAG,IAAI,CAACqE,KAAK,KAAK,IAAI,CAACA,KAAK,GAAGrE,CAAC,EAAE,IAAI,CAACiF,YAAY,GAAGjF,CAAC,GAAG,IAAI,CAAChB,CAAC,KAAK,IAAI,CAACiG,YAAY,GAAG,IAAI,CAACjG,CAAC,GAAGgB,CAAC,CAAC,CAAC,EAAEC,CAAC,GAAG,IAAI,CAACqE,KAAK,KAAK,IAAI,CAACA,KAAK,GAAGrE,CAAC,EAAE,IAAI,CAACiF,YAAY,GAAGjF,CAAC,GAAG,IAAI,CAACc,CAAC,KAAK,IAAI,CAACmE,YAAY,GAAG,IAAI,CAACnE,CAAC,GAAGd,CAAC,CAAC,CAAC,EAAEqI,MAAM,CAACuE,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACf,UAAU,CAAC,EAAExD,MAAM,CAACuE,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACd,OAAO,CAAC,EAAEzD,MAAM,CAACuE,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACf,UAAU,CAAC,EAAExD,MAAM,CAACuE,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACd,OAAO,CAAC;MAC/a,CAAC;MACD;MACA4C,SAASA,CAAA,EAAG;QACV,IAAI,CAACxK,IAAI,GAAG,CAAC,CAAC;MAChB,CAAC;MACD;MACAyK,QAAQA,CAAA,EAAG;QACT,IAAI,CAACzK,IAAI,GAAG,CAAC,CAAC;MAChB,CAAC;MACD;MACAwG,SAASA,CAAA,EAAG;QACV,IAAI,CAACvG,QAAQ,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,KAAK,GAAG,CAAC,EAAE,IAAI,CAACC,KAAK,GAAG,CAAC;MACpD,CAAC;MACD;MACAuK,QAAQA,CAAChP,CAAC,EAAE;QACV,IAAIA,CAAC,CAACsL,cAAc,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC/D,UAAU,EACtC,OAAO,IAAI,CAACjD,IAAI,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC+G,SAAS,CAACrL,CAAC,CAAC,EAAE,CAAC,CAAC;QAC9C,IAAIA,CAAC,CAACuF,OAAO,IAAIvF,CAAC,CAACuF,OAAO,CAACtD,MAAM,KAAK,CAAC,EACrC,OAAO,IAAI,CAACqC,IAAI,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC+G,SAAS,CAACrL,CAAC,CAAC,EAAE,IAAI,CAACiP,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;QAChExG,MAAM,CAACgD,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACyD,QAAQ,CAAC,EAAEzG,MAAM,CAACgD,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACwD,SAAS,CAAC,EAAExG,MAAM,CAACgD,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACyD,QAAQ,CAAC,EAAEzG,MAAM,CAACgD,gBAAgB,CAAC,UAAU,EAAE,IAAI,CAACwD,SAAS,CAAC;QACjN,IAAI/O,CAAC,GAAG,SAAS,IAAIF,CAAC,GAAGA,CAAC,CAACuL,OAAO,GAAGvL,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACgG,OAAO;UAAEpL,CAAC,GAAG,SAAS,IAAIH,CAAC,GAAGA,CAAC,CAACwL,OAAO,GAAGxL,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACiG,OAAO;UAAEpL,CAAC;UAAEG,CAAC;QACtHH,CAAC,GAAGF,CAAC,GAAG,IAAI,CAACkF,YAAY,EAAE7E,CAAC,GAAGJ,CAAC,GAAG,IAAI,CAACkF,YAAY,EAAE,IAAI,CAACL,KAAK,GAAG5E,CAAC,EAAE,IAAI,CAAC6E,KAAK,GAAG1E,CAAC,EAAE,IAAI,CAACyK,KAAK,CAAC,aAAa,EAAE;UAC9Gc,MAAM,EAAE,CAAC,CAAC;UACVC,IAAI,EAAE,IAAI,CAACoB,WAAW,CAAC;QACzB,CAAC,CAAC;MACJ,CAAC;MACD+B,QAAQA,CAAClP,CAAC,EAAEE,CAAC,EAAE;QAAA,IAAAiP,MAAA;QACb,IAAIhP,CAAC,GAAG,CAAC;UAAEC,CAAC,GAAG,CAAC;QAChBJ,CAAC,KAAKA,CAAC,CAACsL,cAAc,CAAC,CAAC,EAAEnL,CAAC,GAAG,SAAS,IAAIH,CAAC,GAAGA,CAAC,CAACuL,OAAO,GAAGvL,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACgG,OAAO,EAAEnL,CAAC,GAAG,SAAS,IAAIJ,CAAC,GAAGA,CAAC,CAACwL,OAAO,GAAGxL,CAAC,CAACuF,OAAO,CAAC,CAAC,CAAC,CAACiG,OAAO,CAAC,EAAE,IAAI,CAAC0B,SAAS,CAAC,YAAM;UAC5J,IAAI3M,CAAC;YAAEgB,CAAC;YAAEL,CAAC,GAAGf,CAAC,GAAGgP,MAAI,CAACnK,KAAK;YAAErC,CAAC,GAAGvC,CAAC,GAAG+O,MAAI,CAAClK,KAAK;UAChD,IAAI/E,CAAC,KAAKgB,CAAC,GAAGiO,MAAI,CAAC/J,YAAY,EAAEzC,CAAC,GAAGwM,MAAI,CAAC9J,YAAY,CAAC,EAAEnE,CAAC,IAAI,CAAC,GAAGX,CAAC,GAAG,CAAC,GAAGW,CAAC,GAAGiO,MAAI,CAAC3K,KAAK,GAAG2K,MAAI,CAAChQ,CAAC,GAAGoB,CAAC,GAAG4O,MAAI,CAAChQ,CAAC,GAAGgQ,MAAI,CAAC3K,KAAK,GAAGjE,CAAC,GAAGW,CAAC,EAAEyB,CAAC,IAAI,CAAC,GAAGpB,CAAC,GAAG,CAAC,GAAGoB,CAAC,GAAGwM,MAAI,CAAC1K,KAAK,GAAG0K,MAAI,CAACjO,CAAC,GAAGK,CAAC,GAAG4N,MAAI,CAACjO,CAAC,GAAGiO,MAAI,CAAC1K,KAAK,GAAGlD,CAAC,GAAGoB,CAAC,EAAEwM,MAAI,CAAC1H,SAAS,EAAE;YAC/N,IAAI7E,CAAC,GAAGuM,MAAI,CAACnD,UAAU,CAAC,CAAC;YACzBzL,CAAC,IAAIqC,CAAC,CAACwK,EAAE,KAAK7M,CAAC,GAAGqC,CAAC,CAACwK,EAAE,CAAC,EAAE7M,CAAC,GAAG4O,MAAI,CAAC3K,KAAK,GAAG5B,CAAC,CAAC0K,EAAE,KAAK/M,CAAC,GAAGqC,CAAC,CAAC0K,EAAE,GAAG6B,MAAI,CAAC3K,KAAK,CAAC,EAAEjD,CAAC,IAAIqB,CAAC,CAACyK,EAAE,KAAK9L,CAAC,GAAGqB,CAAC,CAACyK,EAAE,CAAC,EAAE9L,CAAC,GAAG4N,MAAI,CAAC1K,KAAK,GAAG7B,CAAC,CAAC2K,EAAE,KAAKhM,CAAC,GAAGqB,CAAC,CAAC2K,EAAE,GAAG4B,MAAI,CAAC1K,KAAK,CAAC;UACtJ;UACA0K,MAAI,CAAC/J,YAAY,GAAG7E,CAAC,EAAE4O,MAAI,CAAC9J,YAAY,GAAG9D,CAAC,EAAE4N,MAAI,CAACnE,KAAK,CAAC,aAAa,EAAE;YACtEc,MAAM,EAAE,CAAC,CAAC;YACVC,IAAI,EAAEoD,MAAI,CAAChC,WAAW,CAAC;UACzB,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC;MACD;MACAnB,UAAUA,CAAChM,CAAC,EAAEE,CAAC,EAAEC,CAAC,EAAE;QAClBH,CAAC,GAAGA,CAAC,IAAI,IAAI,CAACjB,CAAC,EAAEmB,CAAC,GAAGA,CAAC,IAAI,IAAI,CAACrB,CAAC,EAAEsB,CAAC,GAAGA,CAAC,IAAI,IAAI,CAAC4D,KAAK;QACrD,IAAI3D,CAAC,GAAG;YACNgN,EAAE,EAAE,CAAC;YACLE,EAAE,EAAE,CAAC;YACLD,EAAE,EAAE,CAAC;YACLE,EAAE,EAAE;UACN,CAAC;UAAEhN,CAAC,GAAG,IAAI,CAAC0D,SAAS,GAAG9D,CAAC;UAAEoB,CAAC,GAAG,IAAI,CAAC2C,UAAU,GAAG/D,CAAC;QAClD,QAAQ,IAAI,CAACsF,MAAM;UACjB,KAAK,CAAC;YACJrF,CAAC,CAACgN,EAAE,GAAGpN,CAAC,GAAG,IAAI,CAACiE,SAAS,IAAI,CAAC,GAAG9D,CAAC,CAAC,GAAG,CAAC,EAAEC,CAAC,CAACkN,EAAE,GAAGlN,CAAC,CAACgN,EAAE,GAAG,IAAI,CAACnJ,SAAS,GAAG9D,CAAC,EAAEC,CAAC,CAACiN,EAAE,GAAGnN,CAAC,GAAG,IAAI,CAACgE,UAAU,IAAI,CAAC,GAAG/D,CAAC,CAAC,GAAG,CAAC,EAAEC,CAAC,CAACmN,EAAE,GAAGnN,CAAC,CAACiN,EAAE,GAAG,IAAI,CAACnJ,UAAU,GAAG/D,CAAC;YACtJ;UACF,KAAK,CAAC;UACN,KAAK,CAAC,CAAC;UACP,KAAK,CAAC;UACN,KAAK,CAAC,CAAC;YACLC,CAAC,CAACgN,EAAE,GAAGpN,CAAC,GAAG,IAAI,CAACiE,SAAS,IAAI,CAAC,GAAG9D,CAAC,CAAC,GAAG,CAAC,GAAG,CAACI,CAAC,GAAGgB,CAAC,IAAI,CAAC,EAAEnB,CAAC,CAACkN,EAAE,GAAGlN,CAAC,CAACgN,EAAE,GAAG,IAAI,CAAClJ,UAAU,GAAG/D,CAAC,EAAEC,CAAC,CAACiN,EAAE,GAAGnN,CAAC,GAAG,IAAI,CAACgE,UAAU,IAAI,CAAC,GAAG/D,CAAC,CAAC,GAAG,CAAC,GAAG,CAACoB,CAAC,GAAGhB,CAAC,IAAI,CAAC,EAAEH,CAAC,CAACmN,EAAE,GAAGnN,CAAC,CAACiN,EAAE,GAAG,IAAI,CAACpJ,SAAS,GAAG9D,CAAC;YAClL;UACF;YACEC,CAAC,CAACgN,EAAE,GAAGpN,CAAC,GAAG,IAAI,CAACiE,SAAS,IAAI,CAAC,GAAG9D,CAAC,CAAC,GAAG,CAAC,EAAEC,CAAC,CAACkN,EAAE,GAAGlN,CAAC,CAACgN,EAAE,GAAG,IAAI,CAACnJ,SAAS,GAAG9D,CAAC,EAAEC,CAAC,CAACiN,EAAE,GAAGnN,CAAC,GAAG,IAAI,CAACgE,UAAU,IAAI,CAAC,GAAG/D,CAAC,CAAC,GAAG,CAAC,EAAEC,CAAC,CAACmN,EAAE,GAAGnN,CAAC,CAACiN,EAAE,GAAG,IAAI,CAACnJ,UAAU,GAAG/D,CAAC;YACtJ;QACJ;QACA,OAAOC,CAAC;MACV,CAAC;MACD;MACA+M,WAAWA,CAAA,EAAG;QACZ,IAAInN,CAAC,GAAG;UACNoN,EAAE,EAAE,CAAC;UACLE,EAAE,EAAE,CAAC;UACLD,EAAE,EAAE,CAAC;UACLE,EAAE,EAAE;QACN,CAAC;QACD,OAAOvN,CAAC,CAACoN,EAAE,GAAG,IAAI,CAAChI,YAAY,EAAEpF,CAAC,CAACsN,EAAE,GAAGtN,CAAC,CAACoN,EAAE,GAAG,IAAI,CAAC5I,KAAK,EAAExE,CAAC,CAACqN,EAAE,GAAG,IAAI,CAAChI,YAAY,EAAErF,CAAC,CAACuN,EAAE,GAAGvN,CAAC,CAACqN,EAAE,GAAG,IAAI,CAAC5I,KAAK,EAAEzE,CAAC;MAClH,CAAC;MACDiP,SAASA,CAACjP,CAAC,EAAE;QACXyI,MAAM,CAACuE,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACkC,QAAQ,CAAC,EAAEzG,MAAM,CAACuE,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACiC,SAAS,CAAC,EAAExG,MAAM,CAACuE,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACkC,QAAQ,CAAC,EAAEzG,MAAM,CAACuE,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACiC,SAAS,CAAC,EAAE,IAAI,CAACjE,KAAK,CAAC,aAAa,EAAE;UACvPc,MAAM,EAAE,CAAC,CAAC;UACVC,IAAI,EAAE,IAAI,CAACoB,WAAW,CAAC;QACzB,CAAC,CAAC;MACJ,CAAC;MACDiC,cAAcA,CAACpP,CAAC,EAAE;QAAA,IAAAqP,MAAA;QAChB,IAAInP,CAAC,GAAG+J,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;UAAE/J,CAAC,GAAGD,CAAC,CAACiK,UAAU,CAAC,IAAI,CAAC;UAAE/J,CAAC,GAAG,IAAI2K,KAAK,CAAC,CAAC;UAAExK,CAAC,GAAG,IAAI,CAACkF,MAAM;UAAElE,CAAC,GAAG,IAAI,CAAC0C,SAAS;UAAE/C,CAAC,GAAG,IAAI,CAACgD,UAAU;UAAEvB,CAAC,GAAG,IAAI,CAACyC,YAAY;UAAExC,CAAC,GAAG,IAAI,CAACyC,YAAY;QACzLjF,CAAC,CAACa,MAAM,GAAG,YAAM;UACf,IAAIoO,MAAI,CAAC7K,KAAK,KAAK,CAAC,EAAE;YACpB,IAAIxB,CAAC,GAAG,CAAC;YACTqM,MAAI,CAAC3H,IAAI,GAAG,CAAC2H,MAAI,CAAChI,IAAI,KAAKrE,CAAC,GAAGyF,MAAM,CAACC,gBAAgB,CAAC,EAAE2G,MAAI,CAACxH,OAAO,KAAK,CAAC,GAAG,CAACwH,MAAI,CAAChI,IAAI,KAAKrE,CAAC,GAAG2F,IAAI,CAACC,GAAG,CAACnC,MAAM,CAAC4I,MAAI,CAACxH,OAAO,CAAC,CAAC,CAAC;YAChI,IAAIyH,CAAC,GAAGD,MAAI,CAAC7K,KAAK,GAAGxB,CAAC;cAAEuM,CAAC,GAAGF,MAAI,CAAC5K,KAAK,GAAGzB,CAAC;cAAEwM,CAAC,GAAGjO,CAAC,GAAG8N,MAAI,CAACtL,KAAK,GAAGf,CAAC;cAAEyM,CAAC,GAAGvO,CAAC,GAAGmO,MAAI,CAACtL,KAAK,GAAGf,CAAC;cAAE0M,CAAC,GAAG,CAACL,MAAI,CAACtQ,CAAC,GAAG4D,CAAC,GAAG0M,MAAI,CAACpL,SAAS,IAAI,CAAC,GAAGoL,MAAI,CAACtL,KAAK,CAAC,GAAG,CAAC,IAAIf,CAAC;cAAE2M,CAAC,GAAG,CAACN,MAAI,CAACxQ,CAAC,GAAG+D,CAAC,GAAGyM,MAAI,CAACnL,UAAU,IAAI,CAAC,GAAGmL,MAAI,CAACtL,KAAK,CAAC,GAAG,CAAC,IAAIf,CAAC;YACvN,QAAQD,CAAC,CAACuM,CAAC,EAAEC,CAAC,CAAC,EAAEpP,CAAC,CAACiK,IAAI,CAAC,CAAC,EAAE7J,CAAC;cAC1B,KAAK,CAAC;gBACJ8O,MAAI,CAAChI,IAAI,IAAItE,CAAC,CAACuM,CAAC,GAAGD,MAAI,CAACtL,KAAK,EAAEwL,CAAC,GAAGF,MAAI,CAACtL,KAAK,CAAC,EAAE5D,CAAC,CAACoK,SAAS,CACzDnK,CAAC,EACDsP,CAAC,GAAGL,MAAI,CAACtL,KAAK,EACd4L,CAAC,GAAGN,MAAI,CAACtL,KAAK,EACdyL,CAAC,GAAGH,MAAI,CAACtL,KAAK,EACd0L,CAAC,GAAGJ,MAAI,CAACtL,KACX,CAAC,IAAI5D,CAAC,CAACoK,SAAS,CAACnK,CAAC,EAAEsP,CAAC,EAAEC,CAAC,EAAEH,CAAC,EAAEC,CAAC,CAAC;gBAC/B;cACF,KAAK,CAAC;cACN,KAAK,CAAC,CAAC;gBACLJ,MAAI,CAAChI,IAAI,IAAItE,CAAC,CAACuM,CAAC,GAAGD,MAAI,CAACtL,KAAK,EAAEwL,CAAC,GAAGF,MAAI,CAACtL,KAAK,CAAC,EAAE2L,CAAC,GAAGA,CAAC,GAAGL,MAAI,CAACtL,KAAK,GAAG,CAACyL,CAAC,GAAGH,MAAI,CAACtL,KAAK,GAAG0L,CAAC,GAAGJ,MAAI,CAACtL,KAAK,IAAI,CAAC,EAAE4L,CAAC,GAAGA,CAAC,GAAGN,MAAI,CAACtL,KAAK,GAAG,CAAC0L,CAAC,GAAGJ,MAAI,CAACtL,KAAK,GAAGyL,CAAC,GAAGH,MAAI,CAACtL,KAAK,IAAI,CAAC,EAAE5D,CAAC,CAACsF,MAAM,CAAClF,CAAC,GAAG,EAAE,GAAGoI,IAAI,CAAC2B,EAAE,GAAG,GAAG,CAAC,EAAEnK,CAAC,CAACoK,SAAS,CACnNnK,CAAC,EACDuP,CAAC,EACD,CAACD,CAAC,GAAGD,CAAC,GAAGJ,MAAI,CAACtL,KAAK,EACnByL,CAAC,GAAGH,MAAI,CAACtL,KAAK,EACd0L,CAAC,GAAGJ,MAAI,CAACtL,KACX,CAAC,KAAK2L,CAAC,GAAGA,CAAC,GAAG,CAACF,CAAC,GAAGC,CAAC,IAAI,CAAC,EAAEE,CAAC,GAAGA,CAAC,GAAG,CAACF,CAAC,GAAGD,CAAC,IAAI,CAAC,EAAErP,CAAC,CAACsF,MAAM,CAAClF,CAAC,GAAG,EAAE,GAAGoI,IAAI,CAAC2B,EAAE,GAAG,GAAG,CAAC,EAAEnK,CAAC,CAACoK,SAAS,CAACnK,CAAC,EAAEuP,CAAC,EAAE,CAACD,CAAC,GAAGD,CAAC,EAAED,CAAC,EAAEC,CAAC,CAAC,CAAC;gBAClH;cACF,KAAK,CAAC;cACN,KAAK,CAAC,CAAC;gBACLJ,MAAI,CAAChI,IAAI,IAAItE,CAAC,CAACuM,CAAC,GAAGD,MAAI,CAACtL,KAAK,EAAEwL,CAAC,GAAGF,MAAI,CAACtL,KAAK,CAAC,EAAE5D,CAAC,CAACsF,MAAM,CAAClF,CAAC,GAAG,EAAE,GAAGoI,IAAI,CAAC2B,EAAE,GAAG,GAAG,CAAC,EAAEoF,CAAC,GAAGA,CAAC,GAAGL,MAAI,CAACtL,KAAK,EAAE4L,CAAC,GAAGA,CAAC,GAAGN,MAAI,CAACtL,KAAK,EAAE5D,CAAC,CAACoK,SAAS,CACnInK,CAAC,EACD,CAACsP,CAAC,GAAGF,CAAC,GAAGH,MAAI,CAACtL,KAAK,EACnB,CAAC4L,CAAC,GAAGF,CAAC,GAAGJ,MAAI,CAACtL,KAAK,EACnByL,CAAC,GAAGH,MAAI,CAACtL,KAAK,EACd0L,CAAC,GAAGJ,MAAI,CAACtL,KACX,CAAC,KAAK5D,CAAC,CAACsF,MAAM,CAAClF,CAAC,GAAG,EAAE,GAAGoI,IAAI,CAAC2B,EAAE,GAAG,GAAG,CAAC,EAAEnK,CAAC,CAACoK,SAAS,CAACnK,CAAC,EAAE,CAACsP,CAAC,GAAGF,CAAC,EAAE,CAACG,CAAC,GAAGF,CAAC,EAAED,CAAC,EAAEC,CAAC,CAAC,CAAC;gBAC7E;cACF,KAAK,CAAC;cACN,KAAK,CAAC,CAAC;gBACLJ,MAAI,CAAChI,IAAI,IAAItE,CAAC,CAACuM,CAAC,GAAGD,MAAI,CAACtL,KAAK,EAAEwL,CAAC,GAAGF,MAAI,CAACtL,KAAK,CAAC,EAAE2L,CAAC,GAAGA,CAAC,GAAGL,MAAI,CAACtL,KAAK,GAAG,CAACyL,CAAC,GAAGH,MAAI,CAACtL,KAAK,GAAG0L,CAAC,GAAGJ,MAAI,CAACtL,KAAK,IAAI,CAAC,EAAE4L,CAAC,GAAGA,CAAC,GAAGN,MAAI,CAACtL,KAAK,GAAG,CAAC0L,CAAC,GAAGJ,MAAI,CAACtL,KAAK,GAAGyL,CAAC,GAAGH,MAAI,CAACtL,KAAK,IAAI,CAAC,EAAE5D,CAAC,CAACsF,MAAM,CAAClF,CAAC,GAAG,EAAE,GAAGoI,IAAI,CAAC2B,EAAE,GAAG,GAAG,CAAC,EAAEnK,CAAC,CAACoK,SAAS,CACnNnK,CAAC,EACD,CAACuP,CAAC,GAAGH,CAAC,GAAGH,MAAI,CAACtL,KAAK,EACnB2L,CAAC,EACDF,CAAC,GAAGH,MAAI,CAACtL,KAAK,EACd0L,CAAC,GAAGJ,MAAI,CAACtL,KACX,CAAC,KAAK2L,CAAC,GAAGA,CAAC,GAAG,CAACF,CAAC,GAAGC,CAAC,IAAI,CAAC,EAAEE,CAAC,GAAGA,CAAC,GAAG,CAACF,CAAC,GAAGD,CAAC,IAAI,CAAC,EAAErP,CAAC,CAACsF,MAAM,CAAClF,CAAC,GAAG,EAAE,GAAGoI,IAAI,CAAC2B,EAAE,GAAG,GAAG,CAAC,EAAEnK,CAAC,CAACoK,SAAS,CAACnK,CAAC,EAAE,CAACuP,CAAC,GAAGH,CAAC,EAAEE,CAAC,EAAEF,CAAC,EAAEC,CAAC,CAAC,CAAC;gBAClH;cACF;gBACEJ,MAAI,CAAChI,IAAI,IAAItE,CAAC,CAACuM,CAAC,GAAGD,MAAI,CAACtL,KAAK,EAAEwL,CAAC,GAAGF,MAAI,CAACtL,KAAK,CAAC,EAAE5D,CAAC,CAACoK,SAAS,CACzDnK,CAAC,EACDsP,CAAC,GAAGL,MAAI,CAACtL,KAAK,EACd4L,CAAC,GAAGN,MAAI,CAACtL,KAAK,EACdyL,CAAC,GAAGH,MAAI,CAACtL,KAAK,EACd0L,CAAC,GAAGJ,MAAI,CAACtL,KACX,CAAC,IAAI5D,CAAC,CAACoK,SAAS,CAACnK,CAAC,EAAEsP,CAAC,EAAEC,CAAC,EAAEH,CAAC,EAAEC,CAAC,CAAC;YACnC;YACAtP,CAAC,CAACqK,OAAO,CAAC,CAAC;UACb,CAAC,MAAM;YACL,IAAIxH,EAAC,GAAGzB,CAAC,GAAG8N,MAAI,CAACtL,KAAK;cAAEuL,EAAC,GAAGpO,CAAC,GAAGmO,MAAI,CAACtL,KAAK;YAC1C,QAAQ5D,CAAC,CAACiK,IAAI,CAAC,CAAC,EAAE7J,CAAC;cACjB,KAAK,CAAC;gBACJwC,CAAC,CAACC,EAAC,EAAEsM,EAAC,CAAC,EAAEnP,CAAC,CAACoK,SAAS,CAACnK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE4C,EAAC,EAAEsM,EAAC,CAAC;gBACnC;cACF,KAAK,CAAC;cACN,KAAK,CAAC,CAAC;gBACLvM,CAAC,CAACuM,EAAC,EAAEtM,EAAC,CAAC,EAAE7C,CAAC,CAACsF,MAAM,CAAClF,CAAC,GAAG,EAAE,GAAGoI,IAAI,CAAC2B,EAAE,GAAG,GAAG,CAAC,EAAEnK,CAAC,CAACoK,SAAS,CAACnK,CAAC,EAAE,CAAC,EAAE,CAACkP,EAAC,EAAEtM,EAAC,EAAEsM,EAAC,CAAC;gBACtE;cACF,KAAK,CAAC;cACN,KAAK,CAAC,CAAC;gBACLvM,CAAC,CAACC,EAAC,EAAEsM,EAAC,CAAC,EAAEnP,CAAC,CAACsF,MAAM,CAAClF,CAAC,GAAG,EAAE,GAAGoI,IAAI,CAAC2B,EAAE,GAAG,GAAG,CAAC,EAAEnK,CAAC,CAACoK,SAAS,CAACnK,CAAC,EAAE,CAAC4C,EAAC,EAAE,CAACsM,EAAC,EAAEtM,EAAC,EAAEsM,EAAC,CAAC;gBACvE;cACF,KAAK,CAAC;cACN,KAAK,CAAC,CAAC;gBACLvM,CAAC,CAACuM,EAAC,EAAEtM,EAAC,CAAC,EAAE7C,CAAC,CAACsF,MAAM,CAAClF,CAAC,GAAG,EAAE,GAAGoI,IAAI,CAAC2B,EAAE,GAAG,GAAG,CAAC,EAAEnK,CAAC,CAACoK,SAAS,CAACnK,CAAC,EAAE,CAAC4C,EAAC,EAAE,CAAC,EAAEA,EAAC,EAAEsM,EAAC,CAAC;gBACtE;cACF;gBACEvM,CAAC,CAACC,EAAC,EAAEsM,EAAC,CAAC,EAAEnP,CAAC,CAACoK,SAAS,CAACnK,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE4C,EAAC,EAAEsM,EAAC,CAAC;YACvC;YACAnP,CAAC,CAACqK,OAAO,CAAC,CAAC;UACb;UACAxK,CAAC,CAACE,CAAC,CAAC;QACN,CAAC;QACD,IAAI2C,CAAC,GAAG,IAAI,CAACsD,GAAG,CAACgF,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;QAC7BtI,CAAC,KAAK,MAAM,KAAKzC,CAAC,CAACgL,WAAW,GAAG,WAAW,CAAC,EAAEhL,CAAC,CAACS,GAAG,GAAG,IAAI,CAAC8E,IAAI;QAChE,IAAM7C,CAAC,GAAG,IAAI,CAACqF,SAAS;QACxB,SAASpF,CAACA,CAACC,CAAC,EAAEsM,CAAC,EAAE;UACfpP,CAAC,CAACqI,KAAK,GAAGI,IAAI,CAACiH,KAAK,CAAC5M,CAAC,CAAC,EAAE9C,CAAC,CAACsI,MAAM,GAAGG,IAAI,CAACiH,KAAK,CAACN,CAAC,CAAC,EAAExM,CAAC,KAAK3C,CAAC,CAAC0P,SAAS,GAAG/M,CAAC,EAAE3C,CAAC,CAAC2P,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE5P,CAAC,CAACqI,KAAK,EAAErI,CAAC,CAACsI,MAAM,CAAC,CAAC;QAChH;MACF,CAAC;MACD;MACAuH,WAAWA,CAAC/P,CAAC,EAAE;QAAA,IAAAgQ,OAAA;QACb,IAAI,CAACZ,cAAc,CAAC,UAAClP,CAAC,EAAK;UACzBF,CAAC,CAACE,CAAC,CAAC+P,SAAS,CAAC,QAAQ,GAAGD,OAAI,CAACtJ,UAAU,EAAEsJ,OAAI,CAACxJ,UAAU,CAAC,CAAC;QAC7D,CAAC,CAAC;MACJ,CAAC;MACD;MACA0J,WAAWA,CAAClQ,CAAC,EAAE;QAAA,IAAAmQ,OAAA;QACb,IAAI,CAACf,cAAc,CAAC,UAAClP,CAAC,EAAK;UACzBA,CAAC,CAACuK,MAAM,CACN,UAACtK,CAAC;YAAA,OAAKH,CAAC,CAACG,CAAC,CAAC;UAAA,GACX,QAAQ,GAAGgQ,OAAI,CAACzJ,UAAU,EAC1ByJ,OAAI,CAAC3J,UACP,CAAC;QACH,CAAC,CAAC;MACJ,CAAC;MACD;MACA8C,WAAWA,CAAA,EAAG;QAAA,IAAA8G,OAAA;QACZ,IAAI,IAAI,CAACpK,SAAS,EAChB,IAAI,CAACA,SAAS,GAAG,CAAC,CAAC,EAAE8G,UAAU,CAAC,YAAM;UACpCsD,OAAI,CAACpK,SAAS,GAAG,CAAC,CAAC;QACrB,CAAC,EAAE,EAAE,CAAC,CAAC,KAEP,OAAO,CAAC,CAAC;QACX,IAAIhG,CAAC,GAAG,IAAI,CAACwE,KAAK;UAAEtE,CAAC,GAAG,IAAI,CAACuE,KAAK;UAAEtE,CAAC,GAAG,IAAI,CAAC4D,KAAK;QAClD,IAAI3D,CAAC,GAAG,CAAC,CAAC;QACVA,CAAC,CAACiQ,GAAG,GAAG;UACN9H,KAAK,EAAE,GAAGvI,CAAC,IAAI;UACfwI,MAAM,EAAE,GAAGtI,CAAC;QACd,CAAC;QACD,IAAIK,CAAC,GAAG,CAAC,IAAI,CAACxB,CAAC,GAAG,IAAI,CAACqG,YAAY,IAAIjF,CAAC;UAAEoB,CAAC,GAAG,CAAC,IAAI,CAAC1C,CAAC,GAAG,IAAI,CAACwG,YAAY,IAAIlF,CAAC;UAAEe,CAAC,GAAG,CAAC;QACrFd,CAAC,CAACjB,CAAC,GAAGa,CAAC,EAAEI,CAAC,CAACc,CAAC,GAAGhB,CAAC,EAAEE,CAAC,CAACkQ,GAAG,GAAG,IAAI,CAAC3K,IAAI,EAAEvF,CAAC,CAAC+F,GAAG,GAAG;UAC3CoC,KAAK,EAAE,GAAG,IAAI,CAACtE,SAAS,IAAI;UAC5BuE,MAAM,EAAE,GAAG,IAAI,CAACtE,UAAU,IAAI;UAC9BqM,SAAS,EAAE,SAASpQ,CAAC,gBAAgBI,CAAC,OAAOgB,CAAC,OAAOL,CAAC,cAAc,IAAI,CAACuE,MAAM,GAAG,EAAE;QACtF,CAAC,EAAErF,CAAC,CAACoQ,IAAI,GAAG;AAClB,gDAAgDpQ,CAAC,CAACjB,CAAC,eAAeiB,CAAC,CAACc,CAAC;AACrE,6BAA6BlB,CAAC,eAAeE,CAAC;AAC9C,qBAAqBE,CAAC,CAACkQ,GAAG,kBAAkB,IAAI,CAACrM,SAAS,eAAe,IAAI,CAACC,UAAU;AACxF,kBAAkB/D,CAAC,gBAAgBI,CAAC,OAAOgB,CAAC,OAAOL,CAAC,cAAc,IAAI,CAACuE,MAAM,GAAG,EAAE;AAClF;AACA,aAAa,EAAE,IAAI,CAACuF,KAAK,CAAC,WAAW,EAAE5K,CAAC,CAAC;MACrC,CAAC;MACD;MACAiJ,MAAMA,CAAA,EAAG;QAAA,IAAAoH,OAAA;QACP,IAAIzQ,CAAC,GAAG,IAAI+K,KAAK,CAAC,CAAC;QACnB/K,CAAC,CAACiB,MAAM,GAAG,YAAM;UACfwP,OAAI,CAACtR,CAAC,GAAGqP,UAAU,CAAC/F,MAAM,CAACiI,gBAAgB,CAACD,OAAI,CAACpE,KAAK,CAACC,OAAO,CAAC,CAAC/D,KAAK,CAAC,EAAEkI,OAAI,CAACvP,CAAC,GAAGsN,UAAU,CAAC/F,MAAM,CAACiI,gBAAgB,CAACD,OAAI,CAACpE,KAAK,CAACC,OAAO,CAAC,CAAC9D,MAAM,CAAC,EAAEiI,OAAI,CAACxM,SAAS,GAAGjE,CAAC,CAACuI,KAAK,EAAEkI,OAAI,CAACvM,UAAU,GAAGlE,CAAC,CAACwI,MAAM,EAAEiI,OAAI,CAACjJ,QAAQ,GAAGiJ,OAAI,CAAC1M,KAAK,GAAG,CAAC,GAAG0M,OAAI,CAAC1M,KAAK,GAAG0M,OAAI,CAACE,WAAW,CAAC,CAAC,EAAEF,OAAI,CAACvD,SAAS,CAAC,YAAM;YAC5RuD,OAAI,CAAC1R,CAAC,GAAG,EAAE0R,OAAI,CAACxM,SAAS,GAAGwM,OAAI,CAACxM,SAAS,GAAGwM,OAAI,CAAC1M,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC0M,OAAI,CAACtR,CAAC,GAAGsR,OAAI,CAACxM,SAAS,GAAGwM,OAAI,CAAC1M,KAAK,IAAI,CAAC,EAAE0M,OAAI,CAAC5R,CAAC,GAAG,EAAE4R,OAAI,CAACvM,UAAU,GAAGuM,OAAI,CAACvM,UAAU,GAAGuM,OAAI,CAAC1M,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC0M,OAAI,CAACvP,CAAC,GAAGuP,OAAI,CAACvM,UAAU,GAAGuM,OAAI,CAAC1M,KAAK,IAAI,CAAC,EAAE0M,OAAI,CAACzM,OAAO,GAAG,CAAC,CAAC,EAAEyM,OAAI,CAAC3J,QAAQ,IAAI2J,OAAI,CAAClH,UAAU,CAAC,CAAC,EAAEkH,OAAI,CAACzF,KAAK,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE8B,UAAU,CAAC,YAAM;cAClU2D,OAAI,CAACnH,WAAW,CAAC,CAAC;YACpB,CAAC,EAAE,EAAE,CAAC;UACR,CAAC,CAAC;QACJ,CAAC,EAAEtJ,CAAC,CAACkL,OAAO,GAAG,YAAM;UACnBuF,OAAI,CAACzF,KAAK,CAAC,UAAU,EAAE,OAAO,CAAC;QACjC,CAAC,EAAEhL,CAAC,CAACa,GAAG,GAAG,IAAI,CAAC8E,IAAI;MACtB,CAAC;MACD;MACAgL,WAAWA,CAAA,EAAG;QACZ,IAAI3Q,CAAC,GAAG,CAAC;UAAEE,CAAC,GAAG,IAAI,CAAC+D,SAAS;UAAE9D,CAAC,GAAG,IAAI,CAAC+D,UAAU;QAClD,IAAM9D,CAAC,GAAG,IAAI,CAAC2H,IAAI,CAAC6B,KAAK,CAAC,GAAG,CAAC;QAC9B,QAAQxJ,CAAC,CAAC,CAAC,CAAC;UACV,KAAK,SAAS;YACZ,IAAI,CAAC6D,SAAS,GAAG,IAAI,CAAC9E,CAAC,KAAKa,CAAC,GAAG,IAAI,CAACb,CAAC,GAAG,IAAI,CAAC8E,SAAS,CAAC,EAAE,IAAI,CAACC,UAAU,GAAGlE,CAAC,GAAG,IAAI,CAACkB,CAAC,KAAKlB,CAAC,GAAG,IAAI,CAACkB,CAAC,GAAG,IAAI,CAACgD,UAAU,CAAC;YACxH;UACF,KAAK,OAAO;YACVhE,CAAC,GAAG,IAAI,CAACf,CAAC,EAAEa,CAAC,GAAGE,CAAC,GAAG,IAAI,CAAC+D,SAAS,EAAE9D,CAAC,GAAGA,CAAC,GAAGH,CAAC,EAAEG,CAAC,GAAG,IAAI,CAACe,CAAC,KAAKf,CAAC,GAAG,IAAI,CAACe,CAAC,EAAElB,CAAC,GAAGG,CAAC,GAAG,IAAI,CAAC+D,UAAU,CAAC;YAClG;UACF;YACE,IAAI;cACF,IAAI3D,CAAC,GAAGH,CAAC,CAAC,CAAC,CAAC;cACZ,IAAIG,CAAC,CAACqQ,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;gBACzBrQ,CAAC,GAAGA,CAAC,CAACwB,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE7B,CAAC,GAAGsO,UAAU,CAACjO,CAAC,CAAC;gBAC1C,IAAMgB,CAAC,GAAGrB,CAAC,GAAG,IAAI,CAAC+D,SAAS;gBAC5B,IAAI/C,CAAC,GAAG,CAAC;kBAAEyB,CAAC,GAAGvC,CAAC,CAAC,CAAC,CAAC;gBACnBuC,CAAC,CAACiO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAKjO,CAAC,GAAGA,CAAC,CAACZ,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE5B,CAAC,GAAGqO,UAAU,CAAC7L,CAAC,CAAC,EAAEzB,CAAC,GAAGf,CAAC,GAAG,IAAI,CAAC+D,UAAU,CAAC,EAAElE,CAAC,GAAG2I,IAAI,CAACkI,GAAG,CAACtP,CAAC,EAAEL,CAAC,CAAC;cACpH;cACA,IAAIX,CAAC,CAACqQ,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,KAAKrQ,CAAC,GAAGA,CAAC,CAACwB,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE7B,CAAC,GAAGsO,UAAU,CAACjO,CAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAACpB,CAAC,EAAEa,CAAC,GAAGE,CAAC,GAAG,IAAI,CAAC+D,SAAS,CAAC,EAAE7D,CAAC,CAAC6B,MAAM,KAAK,CAAC,IAAI1B,CAAC,KAAK,MAAM,EAAE;gBAC9I,IAAIgB,EAAC,GAAGnB,CAAC,CAAC,CAAC,CAAC;gBACZmB,EAAC,CAACqP,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,KAAKrP,EAAC,GAAGA,EAAC,CAACQ,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE5B,CAAC,GAAGqO,UAAU,CAACjN,EAAC,CAAC,EAAEvB,CAAC,GAAGG,CAAC,GAAG,IAAI,CAAC+D,UAAU,CAAC,EAAE3C,EAAC,CAACqP,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,KAAKrP,EAAC,GAAGA,EAAC,CAACQ,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE5B,CAAC,GAAGqO,UAAU,CAACjN,EAAC,CAAC,GAAG,GAAG,GAAG,IAAI,CAACL,CAAC,EAAElB,CAAC,GAAGG,CAAC,GAAG,IAAI,CAAC+D,UAAU,CAAC;cAC7M;YACF,CAAC,CAAC,OAAA4M,QAAA,EAAM;cACN9Q,CAAC,GAAG,CAAC;YACP;QACJ;QACA,OAAOA,CAAC;MACV,CAAC;MACD;MACAuJ,UAAUA,CAACvJ,CAAC,EAAEE,CAAC,EAAE;QACf,IAAI,IAAI,CAACyF,IAAI,KAAK,EAAE,IAAI,IAAI,CAACA,IAAI,KAAK,IAAI,EACxC;QACF,IAAI,CAACmF,SAAS,CAAC,CAAC,EAAE,IAAI,CAACvG,QAAQ,GAAG,CAAC,CAAC;QACpC,IAAIpE,CAAC,GAAG,IAAI,CAAChB,CAAC;UAAEiB,CAAC,GAAG,IAAI,CAACc,CAAC;QAC1B,IAAI,IAAI,CAACuG,SAAS,EAAE;UAClB,IAAMvG,CAAC,GAAGyH,IAAI,CAACC,GAAG,CAAC,IAAI,CAACnD,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;UACvC,IAAI9C,CAAC,GAAG,CAACzB,CAAC,GAAG,IAAI,CAACgD,UAAU,GAAG,IAAI,CAACD,SAAS,IAAI,IAAI,CAACF,KAAK;YAAEnB,CAAC,GAAG,CAAC1B,CAAC,GAAG,IAAI,CAAC+C,SAAS,GAAG,IAAI,CAACC,UAAU,IAAI,IAAI,CAACH,KAAK;UACpH5D,CAAC,GAAGwC,CAAC,GAAGxC,CAAC,GAAGwC,CAAC,GAAGxC,CAAC,EAAEC,CAAC,GAAGwC,CAAC,GAAGxC,CAAC,GAAGwC,CAAC,GAAGxC,CAAC;QACtC;QACA,IAAIG,CAAC,GAAGP,CAAC,IAAIwO,UAAU,CAAC,IAAI,CAACzH,aAAa,CAAC;UAAExF,CAAC,GAAGrB,CAAC,IAAIsO,UAAU,CAAC,IAAI,CAACxH,cAAc,CAAC;QACrF,CAACzG,CAAC,KAAK,CAAC,IAAIgB,CAAC,KAAK,CAAC,MAAMhB,CAAC,GAAGJ,CAAC,GAAG,GAAG,EAAEoB,CAAC,GAAGnB,CAAC,GAAG,GAAG,CAAC,EAAEG,CAAC,GAAGA,CAAC,GAAGJ,CAAC,GAAGA,CAAC,GAAGI,CAAC,EAAEgB,CAAC,GAAGA,CAAC,GAAGnB,CAAC,GAAGA,CAAC,GAAGmB,CAAC,EAAE,IAAI,CAAC0F,KAAK,KAAK1F,CAAC,GAAGhB,CAAC,GAAG,IAAI,CAAC2G,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE3F,CAAC,GAAG,IAAI,CAACL,CAAC,KAAKK,CAAC,GAAG,IAAI,CAACL,CAAC,EAAEX,CAAC,GAAGgB,CAAC,GAAG,IAAI,CAAC2F,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,WAAW,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC6J,UAAU,CAACxQ,CAAC,EAAEgB,CAAC,CAAC;MACnQ,CAAC;MACD;MACAwP,UAAUA,CAAC/Q,CAAC,EAAEE,CAAC,EAAE;QAAA,IAAA8Q,OAAA;QACf,IAAI,IAAI,CAACvJ,SAAS,EAAE;UAClB,IAAItH,CAAC,GAAG,IAAI,CAAC6L,UAAU,CAAC,CAAC;UACzBhM,CAAC,GAAGG,CAAC,CAACmN,EAAE,GAAGnN,CAAC,CAACiN,EAAE,KAAKpN,CAAC,GAAGG,CAAC,CAACmN,EAAE,GAAGnN,CAAC,CAACiN,EAAE,EAAElN,CAAC,GAAGF,CAAC,GAAG,IAAI,CAACkH,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,WAAW,CAAC,CAAC,CAAC,CAAC,EAAEhH,CAAC,GAAGC,CAAC,CAACoN,EAAE,GAAGpN,CAAC,CAACkN,EAAE,KAAKnN,CAAC,GAAGC,CAAC,CAACoN,EAAE,GAAGpN,CAAC,CAACkN,EAAE,EAAErN,CAAC,GAAGE,CAAC,GAAG,IAAI,CAACgH,WAAW,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,WAAW,CAAC,CAAC,CAAC,CAAC;QAClL;QACA,IAAI,CAAC1C,KAAK,GAAGxE,CAAC,EAAE,IAAI,CAACyE,KAAK,GAAGvE,CAAC,EAAE,IAAI,CAACoO,kBAAkB,CAAC,CAAC,EAAE,IAAI,CAACpB,SAAS,CAAC,YAAM;UAC9E8D,OAAI,CAAC5L,YAAY,GAAG,CAAC4L,OAAI,CAAC7R,CAAC,GAAG6R,OAAI,CAACxM,KAAK,IAAI,CAAC,EAAEwM,OAAI,CAAC3L,YAAY,GAAG,CAAC2L,OAAI,CAAC9P,CAAC,GAAG8P,OAAI,CAACvM,KAAK,IAAI,CAAC,EAAEuM,OAAI,CAACvJ,SAAS,IAAIuJ,OAAI,CAAC9B,QAAQ,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;QACzI,CAAC,CAAC;MACJ,CAAC;MACD;MACA+B,OAAOA,CAAA,EAAG;QAAA,IAAAC,OAAA;QACR,IAAI,CAAC/K,GAAG,EAAE,IAAI,CAACR,IAAI,GAAG,EAAE,EAAE,IAAI,CAAC5B,KAAK,GAAG,CAAC,EAAE,IAAI,CAACO,IAAI,GAAG,CAAC,CAAC,EAAE,IAAI,CAACmB,MAAM,GAAG,CAAC,EAAE,IAAI,CAACtG,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC+B,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC+C,SAAS,GAAG,CAAC,EAAE,IAAI,CAACC,UAAU,GAAG,CAAC,EAAE,IAAI,CAAC+B,cAAc,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC6E,SAAS,CAAC,CAAC,EAAE,IAAI,CAACoC,SAAS,CAAC,YAAM;UAC3MgE,OAAI,CAAC9H,UAAU,CAAC,CAAC;QACnB,CAAC,CAAC;MACJ,CAAC;MACD;MACA+H,UAAUA,CAAA,EAAG;QACX,IAAI,CAAC1L,MAAM,GAAG,IAAI,CAACA,MAAM,IAAI,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAACA,MAAM,GAAG,CAAC;MACvD,CAAC;MACD;MACA2L,WAAWA,CAAA,EAAG;QACZ,IAAI,CAAC3L,MAAM,GAAG,IAAI,CAACA,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAACA,MAAM,GAAG,CAAC;MACtD,CAAC;MACD;MACA4L,WAAWA,CAAA,EAAG;QACZ,IAAI,CAAC5L,MAAM,GAAG,CAAC;MACjB,CAAC;MACD;MACAsH,eAAeA,CAAC/M,CAAC,EAAEE,CAAC,EAAEC,CAAC,EAAE;QACvBH,CAAC,GAAGA,CAAC,IAAI,IAAI,CAACjB,CAAC,EAAEmB,CAAC,GAAGA,CAAC,IAAI,IAAI,CAACrB,CAAC,EAAEsB,CAAC,GAAGA,CAAC,IAAI,IAAI,CAAC4D,KAAK;QACrD,IAAI3D,CAAC,GAAG,CAAC,CAAC;QACV,IAAI,IAAI,CAACqH,SAAS,EAAE;UAClB,IAAIlH,CAAC,GAAG,IAAI,CAACyL,UAAU,CAAChM,CAAC,EAAEE,CAAC,EAAEC,CAAC,CAAC;YAAEoB,CAAC,GAAG,IAAI,CAAC4L,WAAW,CAAC,CAAC;UACxD5M,CAAC,CAAC6M,EAAE,IAAI7L,CAAC,CAAC6L,EAAE,KAAKhN,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEG,CAAC,CAAC+M,EAAE,IAAI/L,CAAC,CAAC+L,EAAE,KAAKlN,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEG,CAAC,CAAC8M,EAAE,IAAI9L,CAAC,CAAC8L,EAAE,KAAKjN,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEG,CAAC,CAACgN,EAAE,IAAIhM,CAAC,CAACgM,EAAE,KAAKnN,CAAC,GAAG,CAAC,CAAC,CAAC,EAAEA,CAAC,IAAI,IAAI,CAACkR,cAAc,CAAC/Q,CAAC,EAAEgB,CAAC,EAAEpB,CAAC,CAAC;QAC3I;QACA,OAAOC,CAAC;MACV,CAAC;MACD;MACAkR,cAAcA,CAACtR,CAAC,EAAEE,CAAC,EAAEC,CAAC,EAAE;QACtB,IAAIC,CAAC,GAAG,IAAI,CAAC6D,SAAS;UAAE1D,CAAC,GAAG,IAAI,CAAC2D,UAAU;UAAE3C,CAAC,GAAGnB,CAAC,GAAGD,CAAC;UAAEe,CAAC,GAAGX,CAAC,GAAGJ,CAAC;QACjE,IAAIoB,CAAC,IAAI,IAAI,CAACiD,KAAK,IAAItD,CAAC,IAAI,IAAI,CAACuD,KAAK,EACpC,IAAI,CAACV,KAAK,GAAG5D,CAAC,CAAC,KACZ;UACH,IAAMwC,CAAC,GAAG,IAAI,CAAC6B,KAAK,GAAGpE,CAAC;YAAEwC,CAAC,GAAG,IAAI,CAAC6B,KAAK,GAAGlE,CAAC;YAAEsC,CAAC,GAAG,IAAI,CAAC4B,KAAK,IAAIlE,CAAC,GAAGoC,CAAC,GAAGA,CAAC,GAAGC,CAAC;UAC7E,IAAI,CAACmB,KAAK,GAAGlB,CAAC,EAAEtB,CAAC,GAAGnB,CAAC,GAAGyC,CAAC,EAAE3B,CAAC,GAAGX,CAAC,GAAGsC,CAAC;QACtC;QACA,IAAI,CAACoD,cAAc,KAAKjG,CAAC,CAACoN,EAAE,IAAIlN,CAAC,CAACkN,EAAE,KAAK,IAAI,CAACnE,mBAAmB,GAAG,IAAI,CAAClK,CAAC,GAAGmB,CAAC,CAACkN,EAAE,GAAG,CAAChN,CAAC,GAAGmB,CAAC,IAAI,CAAC,GAAG,CAACA,CAAC,GAAGL,CAAC,IAAI,CAAC,GAAG,IAAI,CAACnC,CAAC,GAAGmB,CAAC,CAACkN,EAAE,GAAG,CAAChN,CAAC,GAAGmB,CAAC,IAAI,CAAC,CAAC,EAAEvB,CAAC,CAACsN,EAAE,IAAIpN,CAAC,CAACoN,EAAE,KAAK,IAAI,CAACrE,mBAAmB,GAAG,IAAI,CAAClK,CAAC,GAAGmB,CAAC,CAACkN,EAAE,GAAG,CAAChN,CAAC,GAAGmB,CAAC,IAAI,CAAC,GAAG,CAACA,CAAC,GAAGL,CAAC,IAAI,CAAC,GAAGA,CAAC,GAAG,IAAI,CAACsD,KAAK,GAAG,IAAI,CAACzF,CAAC,GAAGmB,CAAC,CAACoN,EAAE,GAAG,CAAClN,CAAC,GAAGmB,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC,EAAEvB,CAAC,CAACqN,EAAE,IAAInN,CAAC,CAACmN,EAAE,KAAK,IAAI,CAACpE,mBAAmB,GAAG,IAAI,CAACpK,CAAC,GAAGqB,CAAC,CAACmN,EAAE,GAAG,CAAC9M,CAAC,GAAGW,CAAC,IAAI,CAAC,GAAG,CAACA,CAAC,GAAGK,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC1C,CAAC,GAAGqB,CAAC,CAACmN,EAAE,GAAG,CAAC9M,CAAC,GAAGW,CAAC,IAAI,CAAC,CAAC,EAAElB,CAAC,CAACuN,EAAE,IAAIrN,CAAC,CAACqN,EAAE,KAAK,IAAI,CAACtE,mBAAmB,GAAG,IAAI,CAACpK,CAAC,GAAGqB,CAAC,CAACqN,EAAE,GAAG,CAAChN,CAAC,GAAGW,CAAC,IAAI,CAAC,GAAG,CAACA,CAAC,GAAGK,CAAC,IAAI,CAAC,GAAGA,CAAC,GAAG,IAAI,CAAC1C,CAAC,GAAGqB,CAAC,CAACqN,EAAE,GAAG,CAAChN,CAAC,GAAGW,CAAC,IAAI,CAAC,GAAGA,CAAC,CAAC,CAAC,EAAE,CAACK,CAAC,GAAG,IAAI,CAACiD,KAAK,IAAItD,CAAC,GAAG,IAAI,CAACuD,KAAK,MAAM,IAAI,CAACwB,cAAc,GAAG,CAAC,CAAC,CAAC;MAChlB;IACF,CAAC;IACDsL,OAAOA,CAAA,EAAG;MACR,IAAI,CAACjM,OAAO,GAAG,SAAS,IAAI2E,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC,GAAG,OAAO,GAAGD,QAAQ,CAACuH,YAAY,KAAK,KAAK,CAAC,GAAG,YAAY,GAAG,gBAAgB;MACxI,IAAIxR,CAAC,GAAG,IAAI;MACZ,IAAIE,CAAC,GAAGwJ,SAAS,CAACC,SAAS;MAC3B,IAAI,CAAC8H,KAAK,GAAG,CAAC,CAACvR,CAAC,CAAC4B,KAAK,CAAC,+BAA+B,CAAC,EAAE4P,iBAAiB,CAACC,SAAS,CAAClH,MAAM,IAAImH,MAAM,CAACC,cAAc,CAACH,iBAAiB,CAACC,SAAS,EAAE,QAAQ,EAAE;QAC1JhO,KAAK,EAAE,SAAPA,KAAKA,CAAWxD,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAE;UACvB,KAAK,IAAIgB,CAAC,GAAGS,IAAI,CAAC,IAAI,CAACiO,SAAS,CAAC7P,CAAC,EAAEG,CAAC,CAAC,CAACqJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE1I,CAAC,GAAGK,CAAC,CAACU,MAAM,EAAEU,CAAC,GAAG,IAAImP,UAAU,CAAC5Q,CAAC,CAAC,EAAE0B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG1B,CAAC,EAAE0B,CAAC,EAAE,EAC3GD,CAAC,CAACC,CAAC,CAAC,GAAGrB,CAAC,CAACa,UAAU,CAACQ,CAAC,CAAC;UACxBzC,CAAC,CAAC,IAAIkG,IAAI,CAAC,CAAC1D,CAAC,CAAC,EAAE;YAAEyD,IAAI,EAAEpG,CAAC,CAACoG,IAAI,IAAI;UAAY,CAAC,CAAC,CAAC;QACnD;MACF,CAAC,CAAC,EAAE,IAAI,CAACkD,WAAW,CAAC,CAAC,EAAE,IAAI,CAACF,UAAU,CAAC,CAAC;IAC3C,CAAC;IACD2I,SAASA,CAAA,EAAG;MACVtJ,MAAM,CAACuE,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACkC,QAAQ,CAAC,EAAEzG,MAAM,CAACuE,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACiC,SAAS,CAAC,EAAExG,MAAM,CAACuE,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACkC,QAAQ,CAAC,EAAEzG,MAAM,CAACuE,mBAAmB,CAAC,UAAU,EAAE,IAAI,CAACiC,SAAS,CAAC,EAAE,IAAI,CAACvB,WAAW,CAAC,CAAC;IACnP;EACF,CAAC,CAAC;EAAEsE,CAAC,GAAG;IACNC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE;EACT,CAAC;EAAEC,CAAC,GAAG,CAAC,KAAK,CAAC;EAAEC,CAAC,GAAG;IAAEF,KAAK,EAAE;EAAmB,CAAC;EAAEG,CAAC,GAAG,CAAC,KAAK,CAAC;EAAEC,CAAC,GAAG;IAAEL,GAAG,EAAE;EAAE,CAAC;AAC9E,SAASM,CAACA,CAACvS,CAAC,EAAEE,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEG,CAAC,EAAEgB,CAAC,EAAE;EAC3B,OAAO1C,CAAC,CAAC,CAAC,EAAEE,CAAC,CAAC,KAAK,EAAE;IACnBmT,KAAK,EAAE,aAAa;IACpBM,GAAG,EAAE,SAAS;IACdC,WAAW,EAAEvS,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG;MAAA,OAAUF,CAAC,CAACwN,QAAQ,IAAIxN,CAAC,CAACwN,QAAQ,CAAAkF,KAAA,CAAV1S,CAAC,EAAA2S,SAAc,CAAC;IAAA,EAAC;IACxEC,UAAU,EAAE1S,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG;MAAA,OAAUF,CAAC,CAAC0N,WAAW,IAAI1N,CAAC,CAAC0N,WAAW,CAAAgF,KAAA,CAAb1S,CAAC,EAAA2S,SAAiB,CAAC;IAAA;EAC9E,CAAC,EAAE,CACD3S,CAAC,CAAC2F,IAAI,IAAI9G,CAAC,CAAC,CAAC,EAAEE,CAAC,CAAC,KAAK,EAAEiT,CAAC,EAAE,CACzB/S,CAAC,CAACE,CAAC,CAAC,KAAK,EAAE;IACT+S,KAAK,EAAE,oBAAoB;IAC3BW,KAAK,EAAExT,CAAC,CAAC;MACPkJ,KAAK,EAAEvI,CAAC,CAACiE,SAAS,GAAG,IAAI;MACzBuE,MAAM,EAAExI,CAAC,CAACkE,UAAU,GAAG,IAAI;MAC3BqM,SAAS,EAAE,QAAQ,GAAGvQ,CAAC,CAAC+D,KAAK,GAAG,GAAG,GAAG/D,CAAC,CAAC+D,KAAK,GAAG,gBAAgB,GAAG/D,CAAC,CAACjB,CAAC,GAAGiB,CAAC,CAAC+D,KAAK,GAAG,KAAK,GAAG/D,CAAC,CAACnB,CAAC,GAAGmB,CAAC,CAAC+D,KAAK,GAAG,eAAe,GAAG/D,CAAC,CAACyF,MAAM,GAAG,EAAE,GAAG;IAC/I,CAAC;EACH,CAAC,EAAE,CACDtG,CAAC,CAAC,KAAK,EAAE;IACP0B,GAAG,EAAEb,CAAC,CAAC2F,IAAI;IACXmN,GAAG,EAAE,aAAa;IAClBN,GAAG,EAAE;EACP,CAAC,EAAE,IAAI,EAAE,CAAC,EAAEL,CAAC,CAAC,CACf,EAAE,CAAC,CAAC,EAAE,CACL,CAAC5S,CAAC,EAAE,CAACS,CAAC,CAACgE,OAAO,CAAC,CAChB,CAAC,CACH,CAAC,IAAIvE,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EACfN,CAAC,CAAC,KAAK,EAAE;IACP+S,KAAK,EAAEvS,CAAC,CAAC,CAAC,kBAAkB,EAAE;MAAE,cAAc,EAAEK,CAAC,CAACmE,IAAI,IAAI,CAACnE,CAAC,CAACsE,IAAI;MAAE,cAAc,EAAEtE,CAAC,CAACsE,IAAI;MAAE,eAAe,EAAEtE,CAAC,CAACuE;IAAS,CAAC,CAAC,CAAC;IAC1HwO,WAAW,EAAE7S,CAAC,CAAC,CAAC,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAG;MAAA,OAAUF,CAAC,CAACqL,SAAS,IAAIrL,CAAC,CAACqL,SAAS,CAAAqH,KAAA,CAAX1S,CAAC,EAAA2S,SAAe,CAAC;IAAA,EAAC;IACxEK,YAAY,EAAE9S,CAAC,CAAC,CAAC,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAG;MAAA,OAAUF,CAAC,CAACqL,SAAS,IAAIrL,CAAC,CAACqL,SAAS,CAAAqH,KAAA,CAAX1S,CAAC,EAAA2S,SAAe,CAAC;IAAA;EAC1E,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,EACZ1T,CAAC,CAACE,CAAC,CAAC,KAAK,EAAE;IACT+S,KAAK,EAAE,kBAAkB;IACzBW,KAAK,EAAExT,CAAC,CAAC;MACPkJ,KAAK,EAAEvI,CAAC,CAACwE,KAAK,GAAG,IAAI;MACrBgE,MAAM,EAAExI,CAAC,CAACyE,KAAK,GAAG,IAAI;MACtB8L,SAAS,EAAE,cAAc,GAAGvQ,CAAC,CAACoF,YAAY,GAAG,KAAK,GAAGpF,CAAC,CAACqF,YAAY,GAAG;IACxE,CAAC;EACH,CAAC,EAAE,CACDlG,CAAC,CAAC,MAAM,EAAEiT,CAAC,EAAE,CACXjT,CAAC,CAAC,KAAK,EAAE;IACP0T,KAAK,EAAExT,CAAC,CAAC;MACPkJ,KAAK,EAAEvI,CAAC,CAACiE,SAAS,GAAG,IAAI;MACzBuE,MAAM,EAAExI,CAAC,CAACkE,UAAU,GAAG,IAAI;MAC3BqM,SAAS,EAAE,QAAQ,GAAGvQ,CAAC,CAAC+D,KAAK,GAAG,GAAG,GAAG/D,CAAC,CAAC+D,KAAK,GAAG,gBAAgB,GAAG,CAAC/D,CAAC,CAACjB,CAAC,GAAGiB,CAAC,CAACoF,YAAY,IAAIpF,CAAC,CAAC+D,KAAK,GAAG,KAAK,GAAG,CAAC/D,CAAC,CAACnB,CAAC,GAAGmB,CAAC,CAACqF,YAAY,IAAIrF,CAAC,CAAC+D,KAAK,GAAG,eAAe,GAAG/D,CAAC,CAACyF,MAAM,GAAG,EAAE,GAAG;IACrL,CAAC,CAAC;IACF5E,GAAG,EAAEb,CAAC,CAAC2F,IAAI;IACXmN,GAAG,EAAE;EACP,CAAC,EAAE,IAAI,EAAE,EAAE,EAAET,CAAC,CAAC,CAChB,CAAC,EACFlT,CAAC,CAAC,MAAM,EAAE;IACR+S,KAAK,EAAE,2BAA2B;IAClCa,WAAW,EAAE7S,CAAC,CAAC,CAAC,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAG;MAAA,OAAUF,CAAC,CAACgP,QAAQ,IAAIhP,CAAC,CAACgP,QAAQ,CAAA0D,KAAA,CAAV1S,CAAC,EAAA2S,SAAc,CAAC;IAAA,EAAC;IACtEK,YAAY,EAAE9S,CAAC,CAAC,CAAC,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAG;MAAA,OAAUF,CAAC,CAACgP,QAAQ,IAAIhP,CAAC,CAACgP,QAAQ,CAAA0D,KAAA,CAAV1S,CAAC,EAAA2S,SAAc,CAAC;IAAA;EACxE,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,EACZ3S,CAAC,CAAC2G,IAAI,IAAI9H,CAAC,CAAC,CAAC,EAAEE,CAAC,CAAC,MAAM,EAAE;IACvBkT,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,WAAW;IAClBW,KAAK,EAAExT,CAAC,CAAC;MAAEiJ,GAAG,EAAEtI,CAAC,CAACqI,QAAQ,CAACC;IAAI,CAAC;EAClC,CAAC,EAAEzI,CAAC,CAACG,CAAC,CAACqI,QAAQ,CAACE,KAAK,CAAC,GAAG,KAAK,GAAG1I,CAAC,CAACG,CAAC,CAACqI,QAAQ,CAACG,MAAM,CAAC,EAAE,CAAC,CAAC,IAAI/I,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EACtEO,CAAC,CAACoH,QAAQ,GAAG3H,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAIZ,CAAC,CAAC,CAAC,EAAEE,CAAC,CAAC,MAAM,EAAEuT,CAAC,EAAE,CAC1CnT,CAAC,CAAC,MAAM,EAAE;IACR+S,KAAK,EAAE,kBAAkB;IACzBa,WAAW,EAAE7S,CAAC,CAAC,CAAC,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA,EAAC;IACtE8R,YAAY,EAAE9S,CAAC,CAAC,CAAC,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA;EACxE,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,EACZ/B,CAAC,CAAC,MAAM,EAAE;IACR+S,KAAK,EAAE,kBAAkB;IACzBa,WAAW,EAAE7S,CAAC,CAAC,CAAC,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA,EAAC;IACtE8R,YAAY,EAAE9S,CAAC,CAAC,CAAC,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA;EACxE,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,EACZ/B,CAAC,CAAC,MAAM,EAAE;IACR+S,KAAK,EAAE,kBAAkB;IACzBa,WAAW,EAAE7S,CAAC,CAAC,CAAC,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA,EAAC;IACtE8R,YAAY,EAAE9S,CAAC,CAAC,CAAC,CAAC,KAAKA,CAAC,CAAC,CAAC,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA;EACxE,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,EACZ/B,CAAC,CAAC,MAAM,EAAE;IACR+S,KAAK,EAAE,kBAAkB;IACzBa,WAAW,EAAE7S,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA,EAAC;IACxE8R,YAAY,EAAE9S,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA;EAC1E,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,EACZ/B,CAAC,CAAC,MAAM,EAAE;IACR+S,KAAK,EAAE,mBAAmB;IAC1Ba,WAAW,EAAE7S,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA,EAAC;IACxE8R,YAAY,EAAE9S,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA;EAC1E,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,EACZ/B,CAAC,CAAC,MAAM,EAAE;IACR+S,KAAK,EAAE,mBAAmB;IAC1Ba,WAAW,EAAE7S,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA,EAAC;IACxE8R,YAAY,EAAE9S,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA;EAC1E,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,EACZ/B,CAAC,CAAC,MAAM,EAAE;IACR+S,KAAK,EAAE,mBAAmB;IAC1Ba,WAAW,EAAE7S,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA,EAAC;IACxE8R,YAAY,EAAE9S,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA;EAC1E,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,EACZ/B,CAAC,CAAC,MAAM,EAAE;IACR+S,KAAK,EAAE,mBAAmB;IAC1Ba,WAAW,EAAE7S,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA,EAAC;IACxE8R,YAAY,EAAE9S,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA;EAC1E,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,EACZ/B,CAAC,CAAC,MAAM,EAAE;IACR+S,KAAK,EAAE,mBAAmB;IAC1Ba,WAAW,EAAE7S,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA,EAAC;IACxE8R,YAAY,EAAE9S,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA;EAC1E,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,EACZ/B,CAAC,CAAC,MAAM,EAAE;IACR+S,KAAK,EAAE,mBAAmB;IAC1Ba,WAAW,EAAE7S,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA,EAAC;IACxE8R,YAAY,EAAE9S,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA;EAC1E,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,EACZ/B,CAAC,CAAC,MAAM,EAAE;IACR+S,KAAK,EAAE,mBAAmB;IAC1Ba,WAAW,EAAE7S,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA,EAAC;IACxE8R,YAAY,EAAE9S,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA;EAC1E,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,EACZ/B,CAAC,CAAC,MAAM,EAAE;IACR+S,KAAK,EAAE,mBAAmB;IAC1Ba,WAAW,EAAE7S,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA,EAAC;IACxE8R,YAAY,EAAE9S,CAAC,CAAC,EAAE,CAAC,KAAKA,CAAC,CAAC,EAAE,CAAC,GAAG,UAACgB,CAAC;MAAA,OAAKlB,CAAC,CAACiO,cAAc,CAAC/M,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAAA;EAC1E,CAAC,EAAE,IAAI,EAAE,EAAE,CAAC,CACb,CAAC,CAAC,CACJ,EAAE,CAAC,CAAC,EAAE,CACL,CAAC3B,CAAC,EAAES,CAAC,CAACuE,QAAQ,CAAC,CAChB,CAAC,CACH,EAAE,GAAG,CAAC;AACT;AACA,IAAM0O,CAAC,GAAG,eAAgB9P,CAAC,CAACU,CAAC,EAAE,CAAC,CAAC,QAAQ,EAAE0O,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC,CAAC;EAAEW,CAAC,GAAG,SAAJA,CAACA,CAAYlT,CAAC,EAAE;IACjGA,CAAC,CAACmT,SAAS,CAAC,YAAY,EAAEF,CAAC,CAAC;EAC9B,CAAC;EAAEG,CAAC,GAAG;IACLC,OAAO,EAAE,OAAO;IAChBC,OAAO,EAAEJ,CAAC;IACVK,UAAU,EAAEN;EACd,CAAC;AACD,SACEA,CAAC,IAAIM,UAAU,EACfH,CAAC,IAAI7M,OAAO,EACZ6M,CAAC,IAAII,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}