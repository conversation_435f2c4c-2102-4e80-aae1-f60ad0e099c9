{"ast": null, "code": "// HTML5 entities map: { name -> utf16string }\n//\n'use strict';\n\n/*eslint quotes:0*/\nmodule.exports = require('entities/lib/maps/entities.json');", "map": {"version": 3, "names": ["module", "exports", "require"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/common/entities.js"], "sourcesContent": ["// HTML5 entities map: { name -> utf16string }\n//\n'use strict';\n\n/*eslint quotes:0*/\nmodule.exports = require('entities/lib/maps/entities.json');\n"], "mappings": "AAAA;AACA;AACA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAGC,OAAO,CAAC,iCAAiC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}