{"ast": null, "code": "\"use strict\";\n\nvar utils = require(\"./utils.js\");\nfunction DataReader() {\n  this.data = null; // type : see implementation\n  this.length = 0;\n  this.index = 0;\n  this.zero = 0;\n}\nDataReader.prototype = {\n  /**\n   * Check that the offset will not go too far.\n   * @param {string} offset the additional offset to check.\n   * @throws {Error} an Error if the offset is out of bounds.\n   */\n  checkOffset: function checkOffset(offset) {\n    this.checkIndex(this.index + offset);\n  },\n  /**\n   * Check that the specifed index will not be too far.\n   * @param {string} newIndex the index to check.\n   * @throws {Error} an Error if the index is out of bounds.\n   */\n  checkIndex: function checkIndex(newIndex) {\n    if (this.length < this.zero + newIndex || newIndex < 0) {\n      throw new Error(\"End of data reached (data length = \" + this.length + \", asked index = \" + newIndex + \"). Corrupted zip ?\");\n    }\n  },\n  /**\n   * Change the index.\n   * @param {number} newIndex The new index.\n   * @throws {Error} if the new index is out of the data.\n   */\n  setIndex: function setIndex(newIndex) {\n    this.checkIndex(newIndex);\n    this.index = newIndex;\n  },\n  /**\n   * Skip the next n bytes.\n   * @param {number} n the number of bytes to skip.\n   * @throws {Error} if the new index is out of the data.\n   */\n  skip: function skip(n) {\n    this.setIndex(this.index + n);\n  },\n  /**\n   * Get the byte at the specified index.\n   * @param {number} i the index to use.\n   * @return {number} a byte.\n   */\n  byteAt: function byteAt() {\n    // see implementations\n  },\n  /**\n   * Get the next number with a given byte size.\n   * @param {number} size the number of bytes to read.\n   * @return {number} the corresponding number.\n   */\n  readInt: function readInt(size) {\n    var result = 0,\n      i;\n    this.checkOffset(size);\n    for (i = this.index + size - 1; i >= this.index; i--) {\n      result = (result << 8) + this.byteAt(i);\n    }\n    this.index += size;\n    return result;\n  },\n  /**\n   * Get the next string with a given byte size.\n   * @param {number} size the number of bytes to read.\n   * @return {string} the corresponding string.\n   */\n  readString: function readString(size) {\n    return utils.transformTo(\"string\", this.readData(size));\n  },\n  /**\n   * Get raw data without conversion, <size> bytes.\n   * @param {number} size the number of bytes to read.\n   * @return {Object} the raw data, implementation specific.\n   */\n  readData: function readData() {\n    // see implementations\n  },\n  /**\n   * Find the last occurence of a zip signature (4 bytes).\n   * @param {string} sig the signature to find.\n   * @return {number} the index of the last occurence, -1 if not found.\n   */\n  lastIndexOfSignature: function lastIndexOfSignature() {\n    // see implementations\n  },\n  /**\n   * Get the next date.\n   * @return {Date} the date.\n   */\n  readDate: function readDate() {\n    var dostime = this.readInt(4);\n    return new Date((dostime >> 25 & 0x7f) + 1980,\n    // year\n    (dostime >> 21 & 0x0f) - 1,\n    // month\n    dostime >> 16 & 0x1f,\n    // day\n    dostime >> 11 & 0x1f,\n    // hour\n    dostime >> 5 & 0x3f,\n    // minute\n    (dostime & 0x1f) << 1); // second\n  }\n};\nmodule.exports = DataReader;", "map": {"version": 3, "names": ["utils", "require", "DataReader", "data", "length", "index", "zero", "prototype", "checkOffset", "offset", "checkIndex", "newIndex", "Error", "setIndex", "skip", "n", "byteAt", "readInt", "size", "result", "i", "readString", "transformTo", "readData", "lastIndexOfSignature", "readDate", "dostime", "Date", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/pizzip@3.1.7/node_modules/pizzip/js/dataReader.js"], "sourcesContent": ["\"use strict\";\n\nvar utils = require(\"./utils.js\");\nfunction DataReader() {\n  this.data = null; // type : see implementation\n  this.length = 0;\n  this.index = 0;\n  this.zero = 0;\n}\nDataReader.prototype = {\n  /**\n   * Check that the offset will not go too far.\n   * @param {string} offset the additional offset to check.\n   * @throws {Error} an Error if the offset is out of bounds.\n   */\n  checkOffset: function checkOffset(offset) {\n    this.checkIndex(this.index + offset);\n  },\n  /**\n   * Check that the specifed index will not be too far.\n   * @param {string} newIndex the index to check.\n   * @throws {Error} an Error if the index is out of bounds.\n   */\n  checkIndex: function checkIndex(newIndex) {\n    if (this.length < this.zero + newIndex || newIndex < 0) {\n      throw new Error(\"End of data reached (data length = \" + this.length + \", asked index = \" + newIndex + \"). Corrupted zip ?\");\n    }\n  },\n  /**\n   * Change the index.\n   * @param {number} newIndex The new index.\n   * @throws {Error} if the new index is out of the data.\n   */\n  setIndex: function setIndex(newIndex) {\n    this.checkIndex(newIndex);\n    this.index = newIndex;\n  },\n  /**\n   * Skip the next n bytes.\n   * @param {number} n the number of bytes to skip.\n   * @throws {Error} if the new index is out of the data.\n   */\n  skip: function skip(n) {\n    this.setIndex(this.index + n);\n  },\n  /**\n   * Get the byte at the specified index.\n   * @param {number} i the index to use.\n   * @return {number} a byte.\n   */\n  byteAt: function byteAt() {\n    // see implementations\n  },\n  /**\n   * Get the next number with a given byte size.\n   * @param {number} size the number of bytes to read.\n   * @return {number} the corresponding number.\n   */\n  readInt: function readInt(size) {\n    var result = 0,\n      i;\n    this.checkOffset(size);\n    for (i = this.index + size - 1; i >= this.index; i--) {\n      result = (result << 8) + this.byteAt(i);\n    }\n    this.index += size;\n    return result;\n  },\n  /**\n   * Get the next string with a given byte size.\n   * @param {number} size the number of bytes to read.\n   * @return {string} the corresponding string.\n   */\n  readString: function readString(size) {\n    return utils.transformTo(\"string\", this.readData(size));\n  },\n  /**\n   * Get raw data without conversion, <size> bytes.\n   * @param {number} size the number of bytes to read.\n   * @return {Object} the raw data, implementation specific.\n   */\n  readData: function readData() {\n    // see implementations\n  },\n  /**\n   * Find the last occurence of a zip signature (4 bytes).\n   * @param {string} sig the signature to find.\n   * @return {number} the index of the last occurence, -1 if not found.\n   */\n  lastIndexOfSignature: function lastIndexOfSignature() {\n    // see implementations\n  },\n  /**\n   * Get the next date.\n   * @return {Date} the date.\n   */\n  readDate: function readDate() {\n    var dostime = this.readInt(4);\n    return new Date((dostime >> 25 & 0x7f) + 1980,\n    // year\n    (dostime >> 21 & 0x0f) - 1,\n    // month\n    dostime >> 16 & 0x1f,\n    // day\n    dostime >> 11 & 0x1f,\n    // hour\n    dostime >> 5 & 0x3f,\n    // minute\n    (dostime & 0x1f) << 1); // second\n  }\n};\nmodule.exports = DataReader;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AACjC,SAASC,UAAUA,CAAA,EAAG;EACpB,IAAI,CAACC,IAAI,GAAG,IAAI,CAAC,CAAC;EAClB,IAAI,CAACC,MAAM,GAAG,CAAC;EACf,IAAI,CAACC,KAAK,GAAG,CAAC;EACd,IAAI,CAACC,IAAI,GAAG,CAAC;AACf;AACAJ,UAAU,CAACK,SAAS,GAAG;EACrB;AACF;AACA;AACA;AACA;EACEC,WAAW,EAAE,SAASA,WAAWA,CAACC,MAAM,EAAE;IACxC,IAAI,CAACC,UAAU,CAAC,IAAI,CAACL,KAAK,GAAGI,MAAM,CAAC;EACtC,CAAC;EACD;AACF;AACA;AACA;AACA;EACEC,UAAU,EAAE,SAASA,UAAUA,CAACC,QAAQ,EAAE;IACxC,IAAI,IAAI,CAACP,MAAM,GAAG,IAAI,CAACE,IAAI,GAAGK,QAAQ,IAAIA,QAAQ,GAAG,CAAC,EAAE;MACtD,MAAM,IAAIC,KAAK,CAAC,qCAAqC,GAAG,IAAI,CAACR,MAAM,GAAG,kBAAkB,GAAGO,QAAQ,GAAG,oBAAoB,CAAC;IAC7H;EACF,CAAC;EACD;AACF;AACA;AACA;AACA;EACEE,QAAQ,EAAE,SAASA,QAAQA,CAACF,QAAQ,EAAE;IACpC,IAAI,CAACD,UAAU,CAACC,QAAQ,CAAC;IACzB,IAAI,CAACN,KAAK,GAAGM,QAAQ;EACvB,CAAC;EACD;AACF;AACA;AACA;AACA;EACEG,IAAI,EAAE,SAASA,IAAIA,CAACC,CAAC,EAAE;IACrB,IAAI,CAACF,QAAQ,CAAC,IAAI,CAACR,KAAK,GAAGU,CAAC,CAAC;EAC/B,CAAC;EACD;AACF;AACA;AACA;AACA;EACEC,MAAM,EAAE,SAASA,MAAMA,CAAA,EAAG;IACxB;EAAA,CACD;EACD;AACF;AACA;AACA;AACA;EACEC,OAAO,EAAE,SAASA,OAAOA,CAACC,IAAI,EAAE;IAC9B,IAAIC,MAAM,GAAG,CAAC;MACZC,CAAC;IACH,IAAI,CAACZ,WAAW,CAACU,IAAI,CAAC;IACtB,KAAKE,CAAC,GAAG,IAAI,CAACf,KAAK,GAAGa,IAAI,GAAG,CAAC,EAAEE,CAAC,IAAI,IAAI,CAACf,KAAK,EAAEe,CAAC,EAAE,EAAE;MACpDD,MAAM,GAAG,CAACA,MAAM,IAAI,CAAC,IAAI,IAAI,CAACH,MAAM,CAACI,CAAC,CAAC;IACzC;IACA,IAAI,CAACf,KAAK,IAAIa,IAAI;IAClB,OAAOC,MAAM;EACf,CAAC;EACD;AACF;AACA;AACA;AACA;EACEE,UAAU,EAAE,SAASA,UAAUA,CAACH,IAAI,EAAE;IACpC,OAAOlB,KAAK,CAACsB,WAAW,CAAC,QAAQ,EAAE,IAAI,CAACC,QAAQ,CAACL,IAAI,CAAC,CAAC;EACzD,CAAC;EACD;AACF;AACA;AACA;AACA;EACEK,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;IAC5B;EAAA,CACD;EACD;AACF;AACA;AACA;AACA;EACEC,oBAAoB,EAAE,SAASA,oBAAoBA,CAAA,EAAG;IACpD;EAAA,CACD;EACD;AACF;AACA;AACA;EACEC,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;IAC5B,IAAIC,OAAO,GAAG,IAAI,CAACT,OAAO,CAAC,CAAC,CAAC;IAC7B,OAAO,IAAIU,IAAI,CAAC,CAACD,OAAO,IAAI,EAAE,GAAG,IAAI,IAAI,IAAI;IAC7C;IACA,CAACA,OAAO,IAAI,EAAE,GAAG,IAAI,IAAI,CAAC;IAC1B;IACAA,OAAO,IAAI,EAAE,GAAG,IAAI;IACpB;IACAA,OAAO,IAAI,EAAE,GAAG,IAAI;IACpB;IACAA,OAAO,IAAI,CAAC,GAAG,IAAI;IACnB;IACA,CAACA,OAAO,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;EAC1B;AACF,CAAC;AACDE,MAAM,CAACC,OAAO,GAAG3B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}