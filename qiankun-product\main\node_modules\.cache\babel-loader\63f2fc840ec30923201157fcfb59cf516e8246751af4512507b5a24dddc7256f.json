{"ast": null, "code": "/**\n * Module requirements.\n */\n\nvar Polling = require('./polling');\nvar inherit = require('component-inherit');\nvar globalThis = require('../globalThis');\n\n/**\n * Module exports.\n */\n\nmodule.exports = JSONPPolling;\n\n/**\n * Cached regular expressions.\n */\n\nvar rNewline = /\\n/g;\nvar rEscapedNewline = /\\\\n/g;\n\n/**\n * Global JSONP callbacks.\n */\n\nvar callbacks;\n\n/**\n * Noop.\n */\n\nfunction empty() {}\n\n/**\n * JSONP Polling constructor.\n *\n * @param {Object} opts.\n * @api public\n */\n\nfunction JSONPPolling(opts) {\n  Polling.call(this, opts);\n  this.query = this.query || {};\n\n  // define global callbacks array if not present\n  // we do this here (lazily) to avoid unneeded global pollution\n  if (!callbacks) {\n    // we need to consider multiple engines in the same page\n    callbacks = globalThis.___eio = globalThis.___eio || [];\n  }\n\n  // callback identifier\n  this.index = callbacks.length;\n\n  // add callback to jsonp global\n  var self = this;\n  callbacks.push(function (msg) {\n    self.onData(msg);\n  });\n\n  // append to query string\n  this.query.j = this.index;\n\n  // prevent spurious errors from being emitted when the window is unloaded\n  if (typeof addEventListener === 'function') {\n    addEventListener('beforeunload', function () {\n      if (self.script) self.script.onerror = empty;\n    }, false);\n  }\n}\n\n/**\n * Inherits from Polling.\n */\n\ninherit(JSONPPolling, Polling);\n\n/*\n * JSONP only supports binary as base64 encoded strings\n */\n\nJSONPPolling.prototype.supportsBinary = false;\n\n/**\n * Closes the socket.\n *\n * @api private\n */\n\nJSONPPolling.prototype.doClose = function () {\n  if (this.script) {\n    this.script.parentNode.removeChild(this.script);\n    this.script = null;\n  }\n  if (this.form) {\n    this.form.parentNode.removeChild(this.form);\n    this.form = null;\n    this.iframe = null;\n  }\n  Polling.prototype.doClose.call(this);\n};\n\n/**\n * Starts a poll cycle.\n *\n * @api private\n */\n\nJSONPPolling.prototype.doPoll = function () {\n  var self = this;\n  var script = document.createElement('script');\n  if (this.script) {\n    this.script.parentNode.removeChild(this.script);\n    this.script = null;\n  }\n  script.async = true;\n  script.src = this.uri();\n  script.onerror = function (e) {\n    self.onError('jsonp poll error', e);\n  };\n  var insertAt = document.getElementsByTagName('script')[0];\n  if (insertAt) {\n    insertAt.parentNode.insertBefore(script, insertAt);\n  } else {\n    (document.head || document.body).appendChild(script);\n  }\n  this.script = script;\n  var isUAgecko = 'undefined' !== typeof navigator && /gecko/i.test(navigator.userAgent);\n  if (isUAgecko) {\n    setTimeout(function () {\n      var iframe = document.createElement('iframe');\n      document.body.appendChild(iframe);\n      document.body.removeChild(iframe);\n    }, 100);\n  }\n};\n\n/**\n * Writes with a hidden iframe.\n *\n * @param {String} data to send\n * @param {Function} called upon flush.\n * @api private\n */\n\nJSONPPolling.prototype.doWrite = function (data, fn) {\n  var self = this;\n  if (!this.form) {\n    var form = document.createElement('form');\n    var area = document.createElement('textarea');\n    var id = this.iframeId = 'eio_iframe_' + this.index;\n    var iframe;\n    form.className = 'socketio';\n    form.style.position = 'absolute';\n    form.style.top = '-1000px';\n    form.style.left = '-1000px';\n    form.target = id;\n    form.method = 'POST';\n    form.setAttribute('accept-charset', 'utf-8');\n    area.name = 'd';\n    form.appendChild(area);\n    document.body.appendChild(form);\n    this.form = form;\n    this.area = area;\n  }\n  this.form.action = this.uri();\n  function complete() {\n    initIframe();\n    fn();\n  }\n  function initIframe() {\n    if (self.iframe) {\n      try {\n        self.form.removeChild(self.iframe);\n      } catch (e) {\n        self.onError('jsonp polling iframe removal error', e);\n      }\n    }\n    try {\n      // ie6 dynamic iframes with target=\"\" support (thanks Chris Lambacher)\n      var html = '<iframe src=\"javascript:0\" name=\"' + self.iframeId + '\">';\n      iframe = document.createElement(html);\n    } catch (e) {\n      iframe = document.createElement('iframe');\n      iframe.name = self.iframeId;\n      iframe.src = 'javascript:0';\n    }\n    iframe.id = self.iframeId;\n    self.form.appendChild(iframe);\n    self.iframe = iframe;\n  }\n  initIframe();\n\n  // escape \\n to prevent it from being converted into \\r\\n by some UAs\n  // double escaping is required for escaped new lines because unescaping of new lines can be done safely on server-side\n  data = data.replace(rEscapedNewline, '\\\\\\n');\n  this.area.value = data.replace(rNewline, '\\\\n');\n  try {\n    this.form.submit();\n  } catch (e) {}\n  if (this.iframe.attachEvent) {\n    this.iframe.onreadystatechange = function () {\n      if (self.iframe.readyState === 'complete') {\n        complete();\n      }\n    };\n  } else {\n    this.iframe.onload = complete;\n  }\n};", "map": {"version": 3, "names": ["Polling", "require", "inherit", "globalThis", "module", "exports", "JSONPPolling", "rNewline", "rEscapedNewline", "callbacks", "empty", "opts", "call", "query", "___eio", "index", "length", "self", "push", "msg", "onData", "j", "addEventListener", "script", "onerror", "prototype", "supportsBinary", "doClose", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "form", "iframe", "doPoll", "document", "createElement", "async", "src", "uri", "e", "onError", "insertAt", "getElementsByTagName", "insertBefore", "head", "body", "append<PERSON><PERSON><PERSON>", "isUAgecko", "navigator", "test", "userAgent", "setTimeout", "doWrite", "data", "fn", "area", "id", "iframeId", "className", "style", "position", "top", "left", "target", "method", "setAttribute", "name", "action", "complete", "initIframe", "html", "replace", "value", "submit", "attachEvent", "onreadystatechange", "readyState", "onload"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/engine.io-client@3.5.4/node_modules/engine.io-client/lib/transports/polling-jsonp.js"], "sourcesContent": ["/**\n * Module requirements.\n */\n\nvar Polling = require('./polling');\nvar inherit = require('component-inherit');\nvar globalThis = require('../globalThis');\n\n/**\n * Module exports.\n */\n\nmodule.exports = JSONPPolling;\n\n/**\n * Cached regular expressions.\n */\n\nvar rNewline = /\\n/g;\nvar rEscapedNewline = /\\\\n/g;\n\n/**\n * Global JSONP callbacks.\n */\n\nvar callbacks;\n\n/**\n * Noop.\n */\n\nfunction empty () { }\n\n/**\n * JSONP Polling constructor.\n *\n * @param {Object} opts.\n * @api public\n */\n\nfunction JSONPPolling (opts) {\n  Polling.call(this, opts);\n\n  this.query = this.query || {};\n\n  // define global callbacks array if not present\n  // we do this here (lazily) to avoid unneeded global pollution\n  if (!callbacks) {\n    // we need to consider multiple engines in the same page\n    callbacks = globalThis.___eio = (globalThis.___eio || []);\n  }\n\n  // callback identifier\n  this.index = callbacks.length;\n\n  // add callback to jsonp global\n  var self = this;\n  callbacks.push(function (msg) {\n    self.onData(msg);\n  });\n\n  // append to query string\n  this.query.j = this.index;\n\n  // prevent spurious errors from being emitted when the window is unloaded\n  if (typeof addEventListener === 'function') {\n    addEventListener('beforeunload', function () {\n      if (self.script) self.script.onerror = empty;\n    }, false);\n  }\n}\n\n/**\n * Inherits from Polling.\n */\n\ninherit(JSONPPolling, Polling);\n\n/*\n * JSONP only supports binary as base64 encoded strings\n */\n\nJSONPPolling.prototype.supportsBinary = false;\n\n/**\n * Closes the socket.\n *\n * @api private\n */\n\nJSONPPolling.prototype.doClose = function () {\n  if (this.script) {\n    this.script.parentNode.removeChild(this.script);\n    this.script = null;\n  }\n\n  if (this.form) {\n    this.form.parentNode.removeChild(this.form);\n    this.form = null;\n    this.iframe = null;\n  }\n\n  Polling.prototype.doClose.call(this);\n};\n\n/**\n * Starts a poll cycle.\n *\n * @api private\n */\n\nJSONPPolling.prototype.doPoll = function () {\n  var self = this;\n  var script = document.createElement('script');\n\n  if (this.script) {\n    this.script.parentNode.removeChild(this.script);\n    this.script = null;\n  }\n\n  script.async = true;\n  script.src = this.uri();\n  script.onerror = function (e) {\n    self.onError('jsonp poll error', e);\n  };\n\n  var insertAt = document.getElementsByTagName('script')[0];\n  if (insertAt) {\n    insertAt.parentNode.insertBefore(script, insertAt);\n  } else {\n    (document.head || document.body).appendChild(script);\n  }\n  this.script = script;\n\n  var isUAgecko = 'undefined' !== typeof navigator && /gecko/i.test(navigator.userAgent);\n\n  if (isUAgecko) {\n    setTimeout(function () {\n      var iframe = document.createElement('iframe');\n      document.body.appendChild(iframe);\n      document.body.removeChild(iframe);\n    }, 100);\n  }\n};\n\n/**\n * Writes with a hidden iframe.\n *\n * @param {String} data to send\n * @param {Function} called upon flush.\n * @api private\n */\n\nJSONPPolling.prototype.doWrite = function (data, fn) {\n  var self = this;\n\n  if (!this.form) {\n    var form = document.createElement('form');\n    var area = document.createElement('textarea');\n    var id = this.iframeId = 'eio_iframe_' + this.index;\n    var iframe;\n\n    form.className = 'socketio';\n    form.style.position = 'absolute';\n    form.style.top = '-1000px';\n    form.style.left = '-1000px';\n    form.target = id;\n    form.method = 'POST';\n    form.setAttribute('accept-charset', 'utf-8');\n    area.name = 'd';\n    form.appendChild(area);\n    document.body.appendChild(form);\n\n    this.form = form;\n    this.area = area;\n  }\n\n  this.form.action = this.uri();\n\n  function complete () {\n    initIframe();\n    fn();\n  }\n\n  function initIframe () {\n    if (self.iframe) {\n      try {\n        self.form.removeChild(self.iframe);\n      } catch (e) {\n        self.onError('jsonp polling iframe removal error', e);\n      }\n    }\n\n    try {\n      // ie6 dynamic iframes with target=\"\" support (thanks Chris Lambacher)\n      var html = '<iframe src=\"javascript:0\" name=\"' + self.iframeId + '\">';\n      iframe = document.createElement(html);\n    } catch (e) {\n      iframe = document.createElement('iframe');\n      iframe.name = self.iframeId;\n      iframe.src = 'javascript:0';\n    }\n\n    iframe.id = self.iframeId;\n\n    self.form.appendChild(iframe);\n    self.iframe = iframe;\n  }\n\n  initIframe();\n\n  // escape \\n to prevent it from being converted into \\r\\n by some UAs\n  // double escaping is required for escaped new lines because unescaping of new lines can be done safely on server-side\n  data = data.replace(rEscapedNewline, '\\\\\\n');\n  this.area.value = data.replace(rNewline, '\\\\n');\n\n  try {\n    this.form.submit();\n  } catch (e) {}\n\n  if (this.iframe.attachEvent) {\n    this.iframe.onreadystatechange = function () {\n      if (self.iframe.readyState === 'complete') {\n        complete();\n      }\n    };\n  } else {\n    this.iframe.onload = complete;\n  }\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA,IAAIA,OAAO,GAAGC,OAAO,CAAC,WAAW,CAAC;AAClC,IAAIC,OAAO,GAAGD,OAAO,CAAC,mBAAmB,CAAC;AAC1C,IAAIE,UAAU,GAAGF,OAAO,CAAC,eAAe,CAAC;;AAEzC;AACA;AACA;;AAEAG,MAAM,CAACC,OAAO,GAAGC,YAAY;;AAE7B;AACA;AACA;;AAEA,IAAIC,QAAQ,GAAG,KAAK;AACpB,IAAIC,eAAe,GAAG,MAAM;;AAE5B;AACA;AACA;;AAEA,IAAIC,SAAS;;AAEb;AACA;AACA;;AAEA,SAASC,KAAKA,CAAA,EAAI,CAAE;;AAEpB;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASJ,YAAYA,CAAEK,IAAI,EAAE;EAC3BX,OAAO,CAACY,IAAI,CAAC,IAAI,EAAED,IAAI,CAAC;EAExB,IAAI,CAACE,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,CAAC,CAAC;;EAE7B;EACA;EACA,IAAI,CAACJ,SAAS,EAAE;IACd;IACAA,SAAS,GAAGN,UAAU,CAACW,MAAM,GAAIX,UAAU,CAACW,MAAM,IAAI,EAAG;EAC3D;;EAEA;EACA,IAAI,CAACC,KAAK,GAAGN,SAAS,CAACO,MAAM;;EAE7B;EACA,IAAIC,IAAI,GAAG,IAAI;EACfR,SAAS,CAACS,IAAI,CAAC,UAAUC,GAAG,EAAE;IAC5BF,IAAI,CAACG,MAAM,CAACD,GAAG,CAAC;EAClB,CAAC,CAAC;;EAEF;EACA,IAAI,CAACN,KAAK,CAACQ,CAAC,GAAG,IAAI,CAACN,KAAK;;EAEzB;EACA,IAAI,OAAOO,gBAAgB,KAAK,UAAU,EAAE;IAC1CA,gBAAgB,CAAC,cAAc,EAAE,YAAY;MAC3C,IAAIL,IAAI,CAACM,MAAM,EAAEN,IAAI,CAACM,MAAM,CAACC,OAAO,GAAGd,KAAK;IAC9C,CAAC,EAAE,KAAK,CAAC;EACX;AACF;;AAEA;AACA;AACA;;AAEAR,OAAO,CAACI,YAAY,EAAEN,OAAO,CAAC;;AAE9B;AACA;AACA;;AAEAM,YAAY,CAACmB,SAAS,CAACC,cAAc,GAAG,KAAK;;AAE7C;AACA;AACA;AACA;AACA;;AAEApB,YAAY,CAACmB,SAAS,CAACE,OAAO,GAAG,YAAY;EAC3C,IAAI,IAAI,CAACJ,MAAM,EAAE;IACf,IAAI,CAACA,MAAM,CAACK,UAAU,CAACC,WAAW,CAAC,IAAI,CAACN,MAAM,CAAC;IAC/C,IAAI,CAACA,MAAM,GAAG,IAAI;EACpB;EAEA,IAAI,IAAI,CAACO,IAAI,EAAE;IACb,IAAI,CAACA,IAAI,CAACF,UAAU,CAACC,WAAW,CAAC,IAAI,CAACC,IAAI,CAAC;IAC3C,IAAI,CAACA,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,MAAM,GAAG,IAAI;EACpB;EAEA/B,OAAO,CAACyB,SAAS,CAACE,OAAO,CAACf,IAAI,CAAC,IAAI,CAAC;AACtC,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEAN,YAAY,CAACmB,SAAS,CAACO,MAAM,GAAG,YAAY;EAC1C,IAAIf,IAAI,GAAG,IAAI;EACf,IAAIM,MAAM,GAAGU,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAE7C,IAAI,IAAI,CAACX,MAAM,EAAE;IACf,IAAI,CAACA,MAAM,CAACK,UAAU,CAACC,WAAW,CAAC,IAAI,CAACN,MAAM,CAAC;IAC/C,IAAI,CAACA,MAAM,GAAG,IAAI;EACpB;EAEAA,MAAM,CAACY,KAAK,GAAG,IAAI;EACnBZ,MAAM,CAACa,GAAG,GAAG,IAAI,CAACC,GAAG,CAAC,CAAC;EACvBd,MAAM,CAACC,OAAO,GAAG,UAAUc,CAAC,EAAE;IAC5BrB,IAAI,CAACsB,OAAO,CAAC,kBAAkB,EAAED,CAAC,CAAC;EACrC,CAAC;EAED,IAAIE,QAAQ,GAAGP,QAAQ,CAACQ,oBAAoB,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;EACzD,IAAID,QAAQ,EAAE;IACZA,QAAQ,CAACZ,UAAU,CAACc,YAAY,CAACnB,MAAM,EAAEiB,QAAQ,CAAC;EACpD,CAAC,MAAM;IACL,CAACP,QAAQ,CAACU,IAAI,IAAIV,QAAQ,CAACW,IAAI,EAAEC,WAAW,CAACtB,MAAM,CAAC;EACtD;EACA,IAAI,CAACA,MAAM,GAAGA,MAAM;EAEpB,IAAIuB,SAAS,GAAG,WAAW,KAAK,OAAOC,SAAS,IAAI,QAAQ,CAACC,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC;EAEtF,IAAIH,SAAS,EAAE;IACbI,UAAU,CAAC,YAAY;MACrB,IAAInB,MAAM,GAAGE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MAC7CD,QAAQ,CAACW,IAAI,CAACC,WAAW,CAACd,MAAM,CAAC;MACjCE,QAAQ,CAACW,IAAI,CAACf,WAAW,CAACE,MAAM,CAAC;IACnC,CAAC,EAAE,GAAG,CAAC;EACT;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAzB,YAAY,CAACmB,SAAS,CAAC0B,OAAO,GAAG,UAAUC,IAAI,EAAEC,EAAE,EAAE;EACnD,IAAIpC,IAAI,GAAG,IAAI;EAEf,IAAI,CAAC,IAAI,CAACa,IAAI,EAAE;IACd,IAAIA,IAAI,GAAGG,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IACzC,IAAIoB,IAAI,GAAGrB,QAAQ,CAACC,aAAa,CAAC,UAAU,CAAC;IAC7C,IAAIqB,EAAE,GAAG,IAAI,CAACC,QAAQ,GAAG,aAAa,GAAG,IAAI,CAACzC,KAAK;IACnD,IAAIgB,MAAM;IAEVD,IAAI,CAAC2B,SAAS,GAAG,UAAU;IAC3B3B,IAAI,CAAC4B,KAAK,CAACC,QAAQ,GAAG,UAAU;IAChC7B,IAAI,CAAC4B,KAAK,CAACE,GAAG,GAAG,SAAS;IAC1B9B,IAAI,CAAC4B,KAAK,CAACG,IAAI,GAAG,SAAS;IAC3B/B,IAAI,CAACgC,MAAM,GAAGP,EAAE;IAChBzB,IAAI,CAACiC,MAAM,GAAG,MAAM;IACpBjC,IAAI,CAACkC,YAAY,CAAC,gBAAgB,EAAE,OAAO,CAAC;IAC5CV,IAAI,CAACW,IAAI,GAAG,GAAG;IACfnC,IAAI,CAACe,WAAW,CAACS,IAAI,CAAC;IACtBrB,QAAQ,CAACW,IAAI,CAACC,WAAW,CAACf,IAAI,CAAC;IAE/B,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACwB,IAAI,GAAGA,IAAI;EAClB;EAEA,IAAI,CAACxB,IAAI,CAACoC,MAAM,GAAG,IAAI,CAAC7B,GAAG,CAAC,CAAC;EAE7B,SAAS8B,QAAQA,CAAA,EAAI;IACnBC,UAAU,CAAC,CAAC;IACZf,EAAE,CAAC,CAAC;EACN;EAEA,SAASe,UAAUA,CAAA,EAAI;IACrB,IAAInD,IAAI,CAACc,MAAM,EAAE;MACf,IAAI;QACFd,IAAI,CAACa,IAAI,CAACD,WAAW,CAACZ,IAAI,CAACc,MAAM,CAAC;MACpC,CAAC,CAAC,OAAOO,CAAC,EAAE;QACVrB,IAAI,CAACsB,OAAO,CAAC,oCAAoC,EAAED,CAAC,CAAC;MACvD;IACF;IAEA,IAAI;MACF;MACA,IAAI+B,IAAI,GAAG,mCAAmC,GAAGpD,IAAI,CAACuC,QAAQ,GAAG,IAAI;MACrEzB,MAAM,GAAGE,QAAQ,CAACC,aAAa,CAACmC,IAAI,CAAC;IACvC,CAAC,CAAC,OAAO/B,CAAC,EAAE;MACVP,MAAM,GAAGE,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;MACzCH,MAAM,CAACkC,IAAI,GAAGhD,IAAI,CAACuC,QAAQ;MAC3BzB,MAAM,CAACK,GAAG,GAAG,cAAc;IAC7B;IAEAL,MAAM,CAACwB,EAAE,GAAGtC,IAAI,CAACuC,QAAQ;IAEzBvC,IAAI,CAACa,IAAI,CAACe,WAAW,CAACd,MAAM,CAAC;IAC7Bd,IAAI,CAACc,MAAM,GAAGA,MAAM;EACtB;EAEAqC,UAAU,CAAC,CAAC;;EAEZ;EACA;EACAhB,IAAI,GAAGA,IAAI,CAACkB,OAAO,CAAC9D,eAAe,EAAE,MAAM,CAAC;EAC5C,IAAI,CAAC8C,IAAI,CAACiB,KAAK,GAAGnB,IAAI,CAACkB,OAAO,CAAC/D,QAAQ,EAAE,KAAK,CAAC;EAE/C,IAAI;IACF,IAAI,CAACuB,IAAI,CAAC0C,MAAM,CAAC,CAAC;EACpB,CAAC,CAAC,OAAOlC,CAAC,EAAE,CAAC;EAEb,IAAI,IAAI,CAACP,MAAM,CAAC0C,WAAW,EAAE;IAC3B,IAAI,CAAC1C,MAAM,CAAC2C,kBAAkB,GAAG,YAAY;MAC3C,IAAIzD,IAAI,CAACc,MAAM,CAAC4C,UAAU,KAAK,UAAU,EAAE;QACzCR,QAAQ,CAAC,CAAC;MACZ;IACF,CAAC;EACH,CAAC,MAAM;IACL,IAAI,CAACpC,MAAM,CAAC6C,MAAM,GAAGT,QAAQ;EAC/B;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}