{"ast": null, "code": "/*\nLanguage: VBScript\nDescription: VBScript (\"Microsoft Visual Basic Scripting Edition\") is an Active Scripting language developed by Microsoft that is modeled on Visual Basic.\nAuthor: <PERSON><PERSON> <lenik<PERSON>@yandex.ru>\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/VBScript\nCategory: scripting\n*/\n\n/** @type LanguageFn */\nfunction vbscript(hljs) {\n  var regex = hljs.regex;\n  var BUILT_IN_FUNCTIONS = [\"lcase\", \"month\", \"vartype\", \"instrrev\", \"ubound\", \"setlocale\", \"getobject\", \"rgb\", \"getref\", \"string\", \"weekdayname\", \"rnd\", \"dateadd\", \"monthname\", \"now\", \"day\", \"minute\", \"isarray\", \"cbool\", \"round\", \"formatcurrency\", \"conversions\", \"csng\", \"timevalue\", \"second\", \"year\", \"space\", \"abs\", \"clng\", \"timeserial\", \"fixs\", \"len\", \"asc\", \"isempty\", \"maths\", \"dateserial\", \"atn\", \"timer\", \"isobject\", \"filter\", \"weekday\", \"datevalue\", \"ccur\", \"isdate\", \"instr\", \"datediff\", \"formatdatetime\", \"replace\", \"isnull\", \"right\", \"sgn\", \"array\", \"snumeric\", \"log\", \"cdbl\", \"hex\", \"chr\", \"lbound\", \"msgbox\", \"ucase\", \"getlocale\", \"cos\", \"cdate\", \"cbyte\", \"rtrim\", \"join\", \"hour\", \"oct\", \"typename\", \"trim\", \"strcomp\", \"int\", \"createobject\", \"loadpicture\", \"tan\", \"formatnumber\", \"mid\", \"split\", \"cint\", \"sin\", \"datepart\", \"ltrim\", \"sqr\", \"time\", \"derived\", \"eval\", \"date\", \"formatpercent\", \"exp\", \"inputbox\", \"left\", \"ascw\", \"chrw\", \"regexp\", \"cstr\", \"err\"];\n  var BUILT_IN_OBJECTS = [\"server\", \"response\", \"request\",\n  // take no arguments so can be called without ()\n  \"scriptengine\", \"scriptenginebuildversion\", \"scriptengineminorversion\", \"scriptenginemajorversion\"];\n  var BUILT_IN_CALL = {\n    begin: regex.concat(regex.either.apply(regex, BUILT_IN_FUNCTIONS), \"\\\\s*\\\\(\"),\n    // relevance 0 because this is acting as a beginKeywords really\n    relevance: 0,\n    keywords: {\n      built_in: BUILT_IN_FUNCTIONS\n    }\n  };\n  var LITERALS = [\"true\", \"false\", \"null\", \"nothing\", \"empty\"];\n  var KEYWORDS = [\"call\", \"class\", \"const\", \"dim\", \"do\", \"loop\", \"erase\", \"execute\", \"executeglobal\", \"exit\", \"for\", \"each\", \"next\", \"function\", \"if\", \"then\", \"else\", \"on\", \"error\", \"option\", \"explicit\", \"new\", \"private\", \"property\", \"let\", \"get\", \"public\", \"randomize\", \"redim\", \"rem\", \"select\", \"case\", \"set\", \"stop\", \"sub\", \"while\", \"wend\", \"with\", \"end\", \"to\", \"elseif\", \"is\", \"or\", \"xor\", \"and\", \"not\", \"class_initialize\", \"class_terminate\", \"default\", \"preserve\", \"in\", \"me\", \"byval\", \"byref\", \"step\", \"resume\", \"goto\"];\n  return {\n    name: 'VBScript',\n    aliases: ['vbs'],\n    case_insensitive: true,\n    keywords: {\n      keyword: KEYWORDS,\n      built_in: BUILT_IN_OBJECTS,\n      literal: LITERALS\n    },\n    illegal: '//',\n    contains: [BUILT_IN_CALL, hljs.inherit(hljs.QUOTE_STRING_MODE, {\n      contains: [{\n        begin: '\"\"'\n      }]\n    }), hljs.COMMENT(/'/, /$/, {\n      relevance: 0\n    }), hljs.C_NUMBER_MODE]\n  };\n}\nexport { vbscript as default };", "map": {"version": 3, "names": ["vbscript", "hljs", "regex", "BUILT_IN_FUNCTIONS", "BUILT_IN_OBJECTS", "BUILT_IN_CALL", "begin", "concat", "either", "apply", "relevance", "keywords", "built_in", "LITERALS", "KEYWORDS", "name", "aliases", "case_insensitive", "keyword", "literal", "illegal", "contains", "inherit", "QUOTE_STRING_MODE", "COMMENT", "C_NUMBER_MODE", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/highlight.js@11.11.1/node_modules/highlight.js/es/languages/vbscript.js"], "sourcesContent": ["/*\nLanguage: VBScript\nDescription: VBScript (\"Microsoft Visual Basic Scripting Edition\") is an Active Scripting language developed by Microsoft that is modeled on Visual Basic.\nAuthor: <PERSON><PERSON> <lenik<PERSON>@yandex.ru>\nContributors: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/VBScript\nCategory: scripting\n*/\n\n/** @type LanguageFn */\nfunction vbscript(hljs) {\n  const regex = hljs.regex;\n  const BUILT_IN_FUNCTIONS = [\n    \"lcase\",\n    \"month\",\n    \"vartype\",\n    \"instrrev\",\n    \"ubound\",\n    \"setlocale\",\n    \"getobject\",\n    \"rgb\",\n    \"getref\",\n    \"string\",\n    \"weekdayname\",\n    \"rnd\",\n    \"dateadd\",\n    \"monthname\",\n    \"now\",\n    \"day\",\n    \"minute\",\n    \"isarray\",\n    \"cbool\",\n    \"round\",\n    \"formatcurrency\",\n    \"conversions\",\n    \"csng\",\n    \"timevalue\",\n    \"second\",\n    \"year\",\n    \"space\",\n    \"abs\",\n    \"clng\",\n    \"timeserial\",\n    \"fixs\",\n    \"len\",\n    \"asc\",\n    \"isempty\",\n    \"maths\",\n    \"dateserial\",\n    \"atn\",\n    \"timer\",\n    \"isobject\",\n    \"filter\",\n    \"weekday\",\n    \"datevalue\",\n    \"ccur\",\n    \"isdate\",\n    \"instr\",\n    \"datediff\",\n    \"formatdatetime\",\n    \"replace\",\n    \"isnull\",\n    \"right\",\n    \"sgn\",\n    \"array\",\n    \"snumeric\",\n    \"log\",\n    \"cdbl\",\n    \"hex\",\n    \"chr\",\n    \"lbound\",\n    \"msgbox\",\n    \"ucase\",\n    \"getlocale\",\n    \"cos\",\n    \"cdate\",\n    \"cbyte\",\n    \"rtrim\",\n    \"join\",\n    \"hour\",\n    \"oct\",\n    \"typename\",\n    \"trim\",\n    \"strcomp\",\n    \"int\",\n    \"createobject\",\n    \"loadpicture\",\n    \"tan\",\n    \"formatnumber\",\n    \"mid\",\n    \"split\",\n    \"cint\",\n    \"sin\",\n    \"datepart\",\n    \"ltrim\",\n    \"sqr\",\n    \"time\",\n    \"derived\",\n    \"eval\",\n    \"date\",\n    \"formatpercent\",\n    \"exp\",\n    \"inputbox\",\n    \"left\",\n    \"ascw\",\n    \"chrw\",\n    \"regexp\",\n    \"cstr\",\n    \"err\"\n  ];\n  const BUILT_IN_OBJECTS = [\n    \"server\",\n    \"response\",\n    \"request\",\n    // take no arguments so can be called without ()\n    \"scriptengine\",\n    \"scriptenginebuildversion\",\n    \"scriptengineminorversion\",\n    \"scriptenginemajorversion\"\n  ];\n\n  const BUILT_IN_CALL = {\n    begin: regex.concat(regex.either(...BUILT_IN_FUNCTIONS), \"\\\\s*\\\\(\"),\n    // relevance 0 because this is acting as a beginKeywords really\n    relevance: 0,\n    keywords: { built_in: BUILT_IN_FUNCTIONS }\n  };\n\n  const LITERALS = [\n    \"true\",\n    \"false\",\n    \"null\",\n    \"nothing\",\n    \"empty\"\n  ];\n\n  const KEYWORDS = [\n    \"call\",\n    \"class\",\n    \"const\",\n    \"dim\",\n    \"do\",\n    \"loop\",\n    \"erase\",\n    \"execute\",\n    \"executeglobal\",\n    \"exit\",\n    \"for\",\n    \"each\",\n    \"next\",\n    \"function\",\n    \"if\",\n    \"then\",\n    \"else\",\n    \"on\",\n    \"error\",\n    \"option\",\n    \"explicit\",\n    \"new\",\n    \"private\",\n    \"property\",\n    \"let\",\n    \"get\",\n    \"public\",\n    \"randomize\",\n    \"redim\",\n    \"rem\",\n    \"select\",\n    \"case\",\n    \"set\",\n    \"stop\",\n    \"sub\",\n    \"while\",\n    \"wend\",\n    \"with\",\n    \"end\",\n    \"to\",\n    \"elseif\",\n    \"is\",\n    \"or\",\n    \"xor\",\n    \"and\",\n    \"not\",\n    \"class_initialize\",\n    \"class_terminate\",\n    \"default\",\n    \"preserve\",\n    \"in\",\n    \"me\",\n    \"byval\",\n    \"byref\",\n    \"step\",\n    \"resume\",\n    \"goto\"\n  ];\n\n  return {\n    name: 'VBScript',\n    aliases: [ 'vbs' ],\n    case_insensitive: true,\n    keywords: {\n      keyword: KEYWORDS,\n      built_in: BUILT_IN_OBJECTS,\n      literal: LITERALS\n    },\n    illegal: '//',\n    contains: [\n      BUILT_IN_CALL,\n      hljs.inherit(hljs.QUOTE_STRING_MODE, { contains: [ { begin: '\"\"' } ] }),\n      hljs.COMMENT(\n        /'/,\n        /$/,\n        { relevance: 0 }\n      ),\n      hljs.C_NUMBER_MODE\n    ]\n  };\n}\n\nexport { vbscript as default };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAMC,KAAK,GAAGD,IAAI,CAACC,KAAK;EACxB,IAAMC,kBAAkB,GAAG,CACzB,OAAO,EACP,OAAO,EACP,SAAS,EACT,UAAU,EACV,QAAQ,EACR,WAAW,EACX,WAAW,EACX,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,aAAa,EACb,KAAK,EACL,SAAS,EACT,WAAW,EACX,KAAK,EACL,KAAK,EACL,QAAQ,EACR,SAAS,EACT,OAAO,EACP,OAAO,EACP,gBAAgB,EAChB,aAAa,EACb,MAAM,EACN,WAAW,EACX,QAAQ,EACR,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,YAAY,EACZ,MAAM,EACN,KAAK,EACL,KAAK,EACL,SAAS,EACT,OAAO,EACP,YAAY,EACZ,KAAK,EACL,OAAO,EACP,UAAU,EACV,QAAQ,EACR,SAAS,EACT,WAAW,EACX,MAAM,EACN,QAAQ,EACR,OAAO,EACP,UAAU,EACV,gBAAgB,EAChB,SAAS,EACT,QAAQ,EACR,OAAO,EACP,KAAK,EACL,OAAO,EACP,UAAU,EACV,KAAK,EACL,MAAM,EACN,KAAK,EACL,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,WAAW,EACX,KAAK,EACL,OAAO,EACP,OAAO,EACP,OAAO,EACP,MAAM,EACN,MAAM,EACN,KAAK,EACL,UAAU,EACV,MAAM,EACN,SAAS,EACT,KAAK,EACL,cAAc,EACd,aAAa,EACb,KAAK,EACL,cAAc,EACd,KAAK,EACL,OAAO,EACP,MAAM,EACN,KAAK,EACL,UAAU,EACV,OAAO,EACP,KAAK,EACL,MAAM,EACN,SAAS,EACT,MAAM,EACN,MAAM,EACN,eAAe,EACf,KAAK,EACL,UAAU,EACV,MAAM,EACN,MAAM,EACN,MAAM,EACN,QAAQ,EACR,MAAM,EACN,KAAK,CACN;EACD,IAAMC,gBAAgB,GAAG,CACvB,QAAQ,EACR,UAAU,EACV,SAAS;EACT;EACA,cAAc,EACd,0BAA0B,EAC1B,0BAA0B,EAC1B,0BAA0B,CAC3B;EAED,IAAMC,aAAa,GAAG;IACpBC,KAAK,EAAEJ,KAAK,CAACK,MAAM,CAACL,KAAK,CAACM,MAAM,CAAAC,KAAA,CAAZP,KAAK,EAAWC,kBAAkB,CAAC,EAAE,SAAS,CAAC;IACnE;IACAO,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE;MAAEC,QAAQ,EAAET;IAAmB;EAC3C,CAAC;EAED,IAAMU,QAAQ,GAAG,CACf,MAAM,EACN,OAAO,EACP,MAAM,EACN,SAAS,EACT,OAAO,CACR;EAED,IAAMC,QAAQ,GAAG,CACf,MAAM,EACN,OAAO,EACP,OAAO,EACP,KAAK,EACL,IAAI,EACJ,MAAM,EACN,OAAO,EACP,SAAS,EACT,eAAe,EACf,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,UAAU,EACV,IAAI,EACJ,MAAM,EACN,MAAM,EACN,IAAI,EACJ,OAAO,EACP,QAAQ,EACR,UAAU,EACV,KAAK,EACL,SAAS,EACT,UAAU,EACV,KAAK,EACL,KAAK,EACL,QAAQ,EACR,WAAW,EACX,OAAO,EACP,KAAK,EACL,QAAQ,EACR,MAAM,EACN,KAAK,EACL,MAAM,EACN,KAAK,EACL,OAAO,EACP,MAAM,EACN,MAAM,EACN,KAAK,EACL,IAAI,EACJ,QAAQ,EACR,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,kBAAkB,EAClB,iBAAiB,EACjB,SAAS,EACT,UAAU,EACV,IAAI,EACJ,IAAI,EACJ,OAAO,EACP,OAAO,EACP,MAAM,EACN,QAAQ,EACR,MAAM,CACP;EAED,OAAO;IACLC,IAAI,EAAE,UAAU;IAChBC,OAAO,EAAE,CAAE,KAAK,CAAE;IAClBC,gBAAgB,EAAE,IAAI;IACtBN,QAAQ,EAAE;MACRO,OAAO,EAAEJ,QAAQ;MACjBF,QAAQ,EAAER,gBAAgB;MAC1Be,OAAO,EAAEN;IACX,CAAC;IACDO,OAAO,EAAE,IAAI;IACbC,QAAQ,EAAE,CACRhB,aAAa,EACbJ,IAAI,CAACqB,OAAO,CAACrB,IAAI,CAACsB,iBAAiB,EAAE;MAAEF,QAAQ,EAAE,CAAE;QAAEf,KAAK,EAAE;MAAK,CAAC;IAAG,CAAC,CAAC,EACvEL,IAAI,CAACuB,OAAO,CACV,GAAG,EACH,GAAG,EACH;MAAEd,SAAS,EAAE;IAAE,CACjB,CAAC,EACDT,IAAI,CAACwB,aAAa;EAEtB,CAAC;AACH;AAEA,SAASzB,QAAQ,IAAI0B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}