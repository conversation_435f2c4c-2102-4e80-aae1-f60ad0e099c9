{"ast": null, "code": "/*\n * DOM Level 2\n * Object DOMException\n * @see http://www.w3.org/TR/REC-DOM-Level-1/ecma-script-language-binding.html\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/ecma-script-binding.html\n */\n\nfunction copy(src, dest) {\n  for (var p in src) {\n    dest[p] = src[p];\n  }\n}\n/**\n^\\w+\\.prototype\\.([_\\w]+)\\s*=\\s*((?:.*\\{\\s*?[\\r\\n][\\s\\S]*?^})|\\S.*?(?=[;\\r\\n]));?\n^\\w+\\.prototype\\.([_\\w]+)\\s*=\\s*(\\S.*?(?=[;\\r\\n]));?\n */\nfunction _extends(Class, Super) {\n  var pt = Class.prototype;\n  if (Object.create) {\n    var ppt = Object.create(Super.prototype);\n    pt.__proto__ = ppt;\n  }\n  if (!(pt instanceof Super)) {\n    var t = function t() {};\n    ;\n    t.prototype = Super.prototype;\n    t = new t();\n    copy(pt, t);\n    Class.prototype = pt = t;\n  }\n  if (pt.constructor != Class) {\n    if (typeof Class != 'function') {\n      console.error(\"unknow Class:\" + Class);\n    }\n    pt.constructor = Class;\n  }\n}\nvar htmlns = 'http://www.w3.org/1999/xhtml';\n// Node Types\nvar NodeType = {};\nvar ELEMENT_NODE = NodeType.ELEMENT_NODE = 1;\nvar ATTRIBUTE_NODE = NodeType.ATTRIBUTE_NODE = 2;\nvar TEXT_NODE = NodeType.TEXT_NODE = 3;\nvar CDATA_SECTION_NODE = NodeType.CDATA_SECTION_NODE = 4;\nvar ENTITY_REFERENCE_NODE = NodeType.ENTITY_REFERENCE_NODE = 5;\nvar ENTITY_NODE = NodeType.ENTITY_NODE = 6;\nvar PROCESSING_INSTRUCTION_NODE = NodeType.PROCESSING_INSTRUCTION_NODE = 7;\nvar COMMENT_NODE = NodeType.COMMENT_NODE = 8;\nvar DOCUMENT_NODE = NodeType.DOCUMENT_NODE = 9;\nvar DOCUMENT_TYPE_NODE = NodeType.DOCUMENT_TYPE_NODE = 10;\nvar DOCUMENT_FRAGMENT_NODE = NodeType.DOCUMENT_FRAGMENT_NODE = 11;\nvar NOTATION_NODE = NodeType.NOTATION_NODE = 12;\n\n// ExceptionCode\nvar ExceptionCode = {};\nvar ExceptionMessage = {};\nvar INDEX_SIZE_ERR = ExceptionCode.INDEX_SIZE_ERR = (ExceptionMessage[1] = \"Index size error\", 1);\nvar DOMSTRING_SIZE_ERR = ExceptionCode.DOMSTRING_SIZE_ERR = (ExceptionMessage[2] = \"DOMString size error\", 2);\nvar HIERARCHY_REQUEST_ERR = ExceptionCode.HIERARCHY_REQUEST_ERR = (ExceptionMessage[3] = \"Hierarchy request error\", 3);\nvar WRONG_DOCUMENT_ERR = ExceptionCode.WRONG_DOCUMENT_ERR = (ExceptionMessage[4] = \"Wrong document\", 4);\nvar INVALID_CHARACTER_ERR = ExceptionCode.INVALID_CHARACTER_ERR = (ExceptionMessage[5] = \"Invalid character\", 5);\nvar NO_DATA_ALLOWED_ERR = ExceptionCode.NO_DATA_ALLOWED_ERR = (ExceptionMessage[6] = \"No data allowed\", 6);\nvar NO_MODIFICATION_ALLOWED_ERR = ExceptionCode.NO_MODIFICATION_ALLOWED_ERR = (ExceptionMessage[7] = \"No modification allowed\", 7);\nvar NOT_FOUND_ERR = ExceptionCode.NOT_FOUND_ERR = (ExceptionMessage[8] = \"Not found\", 8);\nvar NOT_SUPPORTED_ERR = ExceptionCode.NOT_SUPPORTED_ERR = (ExceptionMessage[9] = \"Not supported\", 9);\nvar INUSE_ATTRIBUTE_ERR = ExceptionCode.INUSE_ATTRIBUTE_ERR = (ExceptionMessage[10] = \"Attribute in use\", 10);\n//level2\nvar INVALID_STATE_ERR = ExceptionCode.INVALID_STATE_ERR = (ExceptionMessage[11] = \"Invalid state\", 11);\nvar SYNTAX_ERR = ExceptionCode.SYNTAX_ERR = (ExceptionMessage[12] = \"Syntax error\", 12);\nvar INVALID_MODIFICATION_ERR = ExceptionCode.INVALID_MODIFICATION_ERR = (ExceptionMessage[13] = \"Invalid modification\", 13);\nvar NAMESPACE_ERR = ExceptionCode.NAMESPACE_ERR = (ExceptionMessage[14] = \"Invalid namespace\", 14);\nvar INVALID_ACCESS_ERR = ExceptionCode.INVALID_ACCESS_ERR = (ExceptionMessage[15] = \"Invalid access\", 15);\nfunction DOMException(code, message) {\n  if (message instanceof Error) {\n    var error = message;\n  } else {\n    error = this;\n    Error.call(this, ExceptionMessage[code]);\n    this.message = ExceptionMessage[code];\n    if (Error.captureStackTrace) Error.captureStackTrace(this, DOMException);\n  }\n  error.code = code;\n  if (message) this.message = this.message + \": \" + message;\n  return error;\n}\n;\nDOMException.prototype = Error.prototype;\ncopy(ExceptionCode, DOMException);\n/**\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/core.html#ID-*********\n * The NodeList interface provides the abstraction of an ordered collection of nodes, without defining or constraining how this collection is implemented. NodeList objects in the DOM are live.\n * The items in the NodeList are accessible via an integral index, starting from 0.\n */\nfunction NodeList() {}\n;\nNodeList.prototype = {\n  /**\n   * The number of nodes in the list. The range of valid child node indices is 0 to length-1 inclusive.\n   * @standard level1\n   */\n  length: 0,\n  /**\n   * Returns the indexth item in the collection. If index is greater than or equal to the number of nodes in the list, this returns null.\n   * @standard level1\n   * @param index  unsigned long \n   *   Index into the collection.\n   * @return Node\n   * \tThe node at the indexth position in the NodeList, or null if that is not a valid index. \n   */\n  item: function item(index) {\n    return this[index] || null;\n  },\n  toString: function toString(isHTML, nodeFilter) {\n    for (var buf = [], i = 0; i < this.length; i++) {\n      serializeToString(this[i], buf, isHTML, nodeFilter);\n    }\n    return buf.join('');\n  }\n};\nfunction LiveNodeList(node, refresh) {\n  this._node = node;\n  this._refresh = refresh;\n  _updateLiveList(this);\n}\nfunction _updateLiveList(list) {\n  var inc = list._node._inc || list._node.ownerDocument._inc;\n  if (list._inc != inc) {\n    var ls = list._refresh(list._node);\n    //console.log(ls.length)\n    __set__(list, 'length', ls.length);\n    copy(ls, list);\n    list._inc = inc;\n  }\n}\nLiveNodeList.prototype.item = function (i) {\n  _updateLiveList(this);\n  return this[i];\n};\n_extends(LiveNodeList, NodeList);\n/**\n * \n * Objects implementing the NamedNodeMap interface are used to represent collections of nodes that can be accessed by name. Note that NamedNodeMap does not inherit from NodeList; NamedNodeMaps are not maintained in any particular order. Objects contained in an object implementing NamedNodeMap may also be accessed by an ordinal index, but this is simply to allow convenient enumeration of the contents of a NamedNodeMap, and does not imply that the DOM specifies an order to these Nodes.\n * NamedNodeMap objects in the DOM are live.\n * used for attributes or DocumentType entities \n */\nfunction NamedNodeMap() {}\n;\nfunction _findNodeIndex(list, node) {\n  var i = list.length;\n  while (i--) {\n    if (list[i] === node) {\n      return i;\n    }\n  }\n}\nfunction _addNamedNode(el, list, newAttr, oldAttr) {\n  if (oldAttr) {\n    list[_findNodeIndex(list, oldAttr)] = newAttr;\n  } else {\n    list[list.length++] = newAttr;\n  }\n  if (el) {\n    newAttr.ownerElement = el;\n    var doc = el.ownerDocument;\n    if (doc) {\n      oldAttr && _onRemoveAttribute(doc, el, oldAttr);\n      _onAddAttribute(doc, el, newAttr);\n    }\n  }\n}\nfunction _removeNamedNode(el, list, attr) {\n  //console.log('remove attr:'+attr)\n  var i = _findNodeIndex(list, attr);\n  if (i >= 0) {\n    var lastIndex = list.length - 1;\n    while (i < lastIndex) {\n      list[i] = list[++i];\n    }\n    list.length = lastIndex;\n    if (el) {\n      var doc = el.ownerDocument;\n      if (doc) {\n        _onRemoveAttribute(doc, el, attr);\n        attr.ownerElement = null;\n      }\n    }\n  } else {\n    throw DOMException(NOT_FOUND_ERR, new Error(el.tagName + '@' + attr));\n  }\n}\nNamedNodeMap.prototype = {\n  length: 0,\n  item: NodeList.prototype.item,\n  getNamedItem: function getNamedItem(key) {\n    //\t\tif(key.indexOf(':')>0 || key == 'xmlns'){\n    //\t\t\treturn null;\n    //\t\t}\n    //console.log()\n    var i = this.length;\n    while (i--) {\n      var attr = this[i];\n      //console.log(attr.nodeName,key)\n      if (attr.nodeName == key) {\n        return attr;\n      }\n    }\n  },\n  setNamedItem: function setNamedItem(attr) {\n    var el = attr.ownerElement;\n    if (el && el != this._ownerElement) {\n      throw new DOMException(INUSE_ATTRIBUTE_ERR);\n    }\n    var oldAttr = this.getNamedItem(attr.nodeName);\n    _addNamedNode(this._ownerElement, this, attr, oldAttr);\n    return oldAttr;\n  },\n  /* returns Node */\n  setNamedItemNS: function setNamedItemNS(attr) {\n    // raises: WRONG_DOCUMENT_ERR,NO_MODIFICATION_ALLOWED_ERR,INUSE_ATTRIBUTE_ERR\n    var el = attr.ownerElement,\n      oldAttr;\n    if (el && el != this._ownerElement) {\n      throw new DOMException(INUSE_ATTRIBUTE_ERR);\n    }\n    oldAttr = this.getNamedItemNS(attr.namespaceURI, attr.localName);\n    _addNamedNode(this._ownerElement, this, attr, oldAttr);\n    return oldAttr;\n  },\n  /* returns Node */\n  removeNamedItem: function removeNamedItem(key) {\n    var attr = this.getNamedItem(key);\n    _removeNamedNode(this._ownerElement, this, attr);\n    return attr;\n  },\n  // raises: NOT_FOUND_ERR,NO_MODIFICATION_ALLOWED_ERR\n\n  //for level2\n  removeNamedItemNS: function removeNamedItemNS(namespaceURI, localName) {\n    var attr = this.getNamedItemNS(namespaceURI, localName);\n    _removeNamedNode(this._ownerElement, this, attr);\n    return attr;\n  },\n  getNamedItemNS: function getNamedItemNS(namespaceURI, localName) {\n    var i = this.length;\n    while (i--) {\n      var node = this[i];\n      if (node.localName == localName && node.namespaceURI == namespaceURI) {\n        return node;\n      }\n    }\n    return null;\n  }\n};\n/**\n * @see http://www.w3.org/TR/REC-DOM-Level-1/level-one-core.html#ID-102161490\n */\nfunction DOMImplementation(/* Object */features) {\n  this._features = {};\n  if (features) {\n    for (var feature in features) {\n      this._features = features[feature];\n    }\n  }\n}\n;\nDOMImplementation.prototype = {\n  hasFeature: function hasFeature(/* string */feature, /* string */version) {\n    var versions = this._features[feature.toLowerCase()];\n    if (versions && (!version || version in versions)) {\n      return true;\n    } else {\n      return false;\n    }\n  },\n  // Introduced in DOM Level 2:\n  createDocument: function createDocument(namespaceURI, qualifiedName, doctype) {\n    // raises:INVALID_CHARACTER_ERR,NAMESPACE_ERR,WRONG_DOCUMENT_ERR\n    var doc = new Document();\n    doc.implementation = this;\n    doc.childNodes = new NodeList();\n    doc.doctype = doctype;\n    if (doctype) {\n      doc.appendChild(doctype);\n    }\n    if (qualifiedName) {\n      var root = doc.createElementNS(namespaceURI, qualifiedName);\n      doc.appendChild(root);\n    }\n    return doc;\n  },\n  // Introduced in DOM Level 2:\n  createDocumentType: function createDocumentType(qualifiedName, publicId, systemId) {\n    // raises:INVALID_CHARACTER_ERR,NAMESPACE_ERR\n    var node = new DocumentType();\n    node.name = qualifiedName;\n    node.nodeName = qualifiedName;\n    node.publicId = publicId;\n    node.systemId = systemId;\n    // Introduced in DOM Level 2:\n    //readonly attribute DOMString        internalSubset;\n\n    //TODO:..\n    //  readonly attribute NamedNodeMap     entities;\n    //  readonly attribute NamedNodeMap     notations;\n    return node;\n  }\n};\n\n/**\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/core.html#ID-1950641247\n */\n\nfunction Node() {}\n;\nNode.prototype = {\n  firstChild: null,\n  lastChild: null,\n  previousSibling: null,\n  nextSibling: null,\n  attributes: null,\n  parentNode: null,\n  childNodes: null,\n  ownerDocument: null,\n  nodeValue: null,\n  namespaceURI: null,\n  prefix: null,\n  localName: null,\n  // Modified in DOM Level 2:\n  insertBefore: function insertBefore(newChild, refChild) {\n    //raises \n    return _insertBefore(this, newChild, refChild);\n  },\n  replaceChild: function replaceChild(newChild, oldChild) {\n    //raises \n    this.insertBefore(newChild, oldChild);\n    if (oldChild) {\n      this.removeChild(oldChild);\n    }\n  },\n  removeChild: function removeChild(oldChild) {\n    return _removeChild(this, oldChild);\n  },\n  appendChild: function appendChild(newChild) {\n    return this.insertBefore(newChild, null);\n  },\n  hasChildNodes: function hasChildNodes() {\n    return this.firstChild != null;\n  },\n  cloneNode: function cloneNode(deep) {\n    return _cloneNode(this.ownerDocument || this, this, deep);\n  },\n  // Modified in DOM Level 2:\n  normalize: function normalize() {\n    var child = this.firstChild;\n    while (child) {\n      var next = child.nextSibling;\n      if (next && next.nodeType == TEXT_NODE && child.nodeType == TEXT_NODE) {\n        this.removeChild(next);\n        child.appendData(next.data);\n      } else {\n        child.normalize();\n        child = next;\n      }\n    }\n  },\n  // Introduced in DOM Level 2:\n  isSupported: function isSupported(feature, version) {\n    return this.ownerDocument.implementation.hasFeature(feature, version);\n  },\n  // Introduced in DOM Level 2:\n  hasAttributes: function hasAttributes() {\n    return this.attributes.length > 0;\n  },\n  lookupPrefix: function lookupPrefix(namespaceURI) {\n    var el = this;\n    while (el) {\n      var map = el._nsMap;\n      //console.dir(map)\n      if (map) {\n        for (var n in map) {\n          if (map[n] == namespaceURI) {\n            return n;\n          }\n        }\n      }\n      el = el.nodeType == ATTRIBUTE_NODE ? el.ownerDocument : el.parentNode;\n    }\n    return null;\n  },\n  // Introduced in DOM Level 3:\n  lookupNamespaceURI: function lookupNamespaceURI(prefix) {\n    var el = this;\n    while (el) {\n      var map = el._nsMap;\n      //console.dir(map)\n      if (map) {\n        if (prefix in map) {\n          return map[prefix];\n        }\n      }\n      el = el.nodeType == ATTRIBUTE_NODE ? el.ownerDocument : el.parentNode;\n    }\n    return null;\n  },\n  // Introduced in DOM Level 3:\n  isDefaultNamespace: function isDefaultNamespace(namespaceURI) {\n    var prefix = this.lookupPrefix(namespaceURI);\n    return prefix == null;\n  }\n};\nfunction _xmlEncoder(c) {\n  return c == '<' && '&lt;' || c == '>' && '&gt;' || c == '&' && '&amp;' || c == '\"' && '&quot;' || '&#' + c.charCodeAt() + ';';\n}\ncopy(NodeType, Node);\ncopy(NodeType, Node.prototype);\n\n/**\n * @param callback return true for continue,false for break\n * @return boolean true: break visit;\n */\nfunction _visitNode(node, callback) {\n  if (callback(node)) {\n    return true;\n  }\n  if (node = node.firstChild) {\n    do {\n      if (_visitNode(node, callback)) {\n        return true;\n      }\n    } while (node = node.nextSibling);\n  }\n}\nfunction Document() {}\nfunction _onAddAttribute(doc, el, newAttr) {\n  doc && doc._inc++;\n  var ns = newAttr.namespaceURI;\n  if (ns == 'http://www.w3.org/2000/xmlns/') {\n    //update namespace\n    el._nsMap[newAttr.prefix ? newAttr.localName : ''] = newAttr.value;\n  }\n}\nfunction _onRemoveAttribute(doc, el, newAttr, remove) {\n  doc && doc._inc++;\n  var ns = newAttr.namespaceURI;\n  if (ns == 'http://www.w3.org/2000/xmlns/') {\n    //update namespace\n    delete el._nsMap[newAttr.prefix ? newAttr.localName : ''];\n  }\n}\nfunction _onUpdateChild(doc, el, newChild) {\n  if (doc && doc._inc) {\n    doc._inc++;\n    //update childNodes\n    var cs = el.childNodes;\n    if (newChild) {\n      cs[cs.length++] = newChild;\n    } else {\n      //console.log(1)\n      var child = el.firstChild;\n      var i = 0;\n      while (child) {\n        cs[i++] = child;\n        child = child.nextSibling;\n      }\n      cs.length = i;\n    }\n  }\n}\n\n/**\n * attributes;\n * children;\n * \n * writeable properties:\n * nodeValue,Attr:value,CharacterData:data\n * prefix\n */\nfunction _removeChild(parentNode, child) {\n  var previous = child.previousSibling;\n  var next = child.nextSibling;\n  if (previous) {\n    previous.nextSibling = next;\n  } else {\n    parentNode.firstChild = next;\n  }\n  if (next) {\n    next.previousSibling = previous;\n  } else {\n    parentNode.lastChild = previous;\n  }\n  _onUpdateChild(parentNode.ownerDocument, parentNode);\n  return child;\n}\n/**\n * preformance key(refChild == null)\n */\nfunction _insertBefore(parentNode, newChild, nextChild) {\n  var cp = newChild.parentNode;\n  if (cp) {\n    cp.removeChild(newChild); //remove and update\n  }\n  if (newChild.nodeType === DOCUMENT_FRAGMENT_NODE) {\n    var newFirst = newChild.firstChild;\n    if (newFirst == null) {\n      return newChild;\n    }\n    var newLast = newChild.lastChild;\n  } else {\n    newFirst = newLast = newChild;\n  }\n  var pre = nextChild ? nextChild.previousSibling : parentNode.lastChild;\n  newFirst.previousSibling = pre;\n  newLast.nextSibling = nextChild;\n  if (pre) {\n    pre.nextSibling = newFirst;\n  } else {\n    parentNode.firstChild = newFirst;\n  }\n  if (nextChild == null) {\n    parentNode.lastChild = newLast;\n  } else {\n    nextChild.previousSibling = newLast;\n  }\n  do {\n    newFirst.parentNode = parentNode;\n  } while (newFirst !== newLast && (newFirst = newFirst.nextSibling));\n  _onUpdateChild(parentNode.ownerDocument || parentNode, parentNode);\n  //console.log(parentNode.lastChild.nextSibling == null)\n  if (newChild.nodeType == DOCUMENT_FRAGMENT_NODE) {\n    newChild.firstChild = newChild.lastChild = null;\n  }\n  return newChild;\n}\nfunction _appendSingleChild(parentNode, newChild) {\n  var cp = newChild.parentNode;\n  if (cp) {\n    var pre = parentNode.lastChild;\n    cp.removeChild(newChild); //remove and update\n    var pre = parentNode.lastChild;\n  }\n  var pre = parentNode.lastChild;\n  newChild.parentNode = parentNode;\n  newChild.previousSibling = pre;\n  newChild.nextSibling = null;\n  if (pre) {\n    pre.nextSibling = newChild;\n  } else {\n    parentNode.firstChild = newChild;\n  }\n  parentNode.lastChild = newChild;\n  _onUpdateChild(parentNode.ownerDocument, parentNode, newChild);\n  return newChild;\n  //console.log(\"__aa\",parentNode.lastChild.nextSibling == null)\n}\nDocument.prototype = {\n  //implementation : null,\n  nodeName: '#document',\n  nodeType: DOCUMENT_NODE,\n  doctype: null,\n  documentElement: null,\n  _inc: 1,\n  insertBefore: function insertBefore(newChild, refChild) {\n    //raises \n    if (newChild.nodeType == DOCUMENT_FRAGMENT_NODE) {\n      var child = newChild.firstChild;\n      while (child) {\n        var next = child.nextSibling;\n        this.insertBefore(child, refChild);\n        child = next;\n      }\n      return newChild;\n    }\n    if (this.documentElement == null && newChild.nodeType == ELEMENT_NODE) {\n      this.documentElement = newChild;\n    }\n    return _insertBefore(this, newChild, refChild), newChild.ownerDocument = this, newChild;\n  },\n  removeChild: function removeChild(oldChild) {\n    if (this.documentElement == oldChild) {\n      this.documentElement = null;\n    }\n    return _removeChild(this, oldChild);\n  },\n  // Introduced in DOM Level 2:\n  importNode: function importNode(importedNode, deep) {\n    return _importNode(this, importedNode, deep);\n  },\n  // Introduced in DOM Level 2:\n  getElementById: function getElementById(id) {\n    var rtv = null;\n    _visitNode(this.documentElement, function (node) {\n      if (node.nodeType == ELEMENT_NODE) {\n        if (node.getAttribute('id') == id) {\n          rtv = node;\n          return true;\n        }\n      }\n    });\n    return rtv;\n  },\n  //document factory method:\n  createElement: function createElement(tagName) {\n    var node = new Element();\n    node.ownerDocument = this;\n    node.nodeName = tagName;\n    node.tagName = tagName;\n    node.childNodes = new NodeList();\n    var attrs = node.attributes = new NamedNodeMap();\n    attrs._ownerElement = node;\n    return node;\n  },\n  createDocumentFragment: function createDocumentFragment() {\n    var node = new DocumentFragment();\n    node.ownerDocument = this;\n    node.childNodes = new NodeList();\n    return node;\n  },\n  createTextNode: function createTextNode(data) {\n    var node = new Text();\n    node.ownerDocument = this;\n    node.appendData(data);\n    return node;\n  },\n  createComment: function createComment(data) {\n    var node = new Comment();\n    node.ownerDocument = this;\n    node.appendData(data);\n    return node;\n  },\n  createCDATASection: function createCDATASection(data) {\n    var node = new CDATASection();\n    node.ownerDocument = this;\n    node.appendData(data);\n    return node;\n  },\n  createProcessingInstruction: function createProcessingInstruction(target, data) {\n    var node = new ProcessingInstruction();\n    node.ownerDocument = this;\n    node.tagName = node.target = target;\n    node.nodeValue = node.data = data;\n    return node;\n  },\n  createAttribute: function createAttribute(name) {\n    var node = new Attr();\n    node.ownerDocument = this;\n    node.name = name;\n    node.nodeName = name;\n    node.localName = name;\n    node.specified = true;\n    return node;\n  },\n  createEntityReference: function createEntityReference(name) {\n    var node = new EntityReference();\n    node.ownerDocument = this;\n    node.nodeName = name;\n    return node;\n  },\n  // Introduced in DOM Level 2:\n  createElementNS: function createElementNS(namespaceURI, qualifiedName) {\n    var node = new Element();\n    var pl = qualifiedName.split(':');\n    var attrs = node.attributes = new NamedNodeMap();\n    node.childNodes = new NodeList();\n    node.ownerDocument = this;\n    node.nodeName = qualifiedName;\n    node.tagName = qualifiedName;\n    node.namespaceURI = namespaceURI;\n    if (pl.length == 2) {\n      node.prefix = pl[0];\n      node.localName = pl[1];\n    } else {\n      //el.prefix = null;\n      node.localName = qualifiedName;\n    }\n    attrs._ownerElement = node;\n    return node;\n  },\n  // Introduced in DOM Level 2:\n  createAttributeNS: function createAttributeNS(namespaceURI, qualifiedName) {\n    var node = new Attr();\n    var pl = qualifiedName.split(':');\n    node.ownerDocument = this;\n    node.nodeName = qualifiedName;\n    node.name = qualifiedName;\n    node.namespaceURI = namespaceURI;\n    node.specified = true;\n    if (pl.length == 2) {\n      node.prefix = pl[0];\n      node.localName = pl[1];\n    } else {\n      //el.prefix = null;\n      node.localName = qualifiedName;\n    }\n    return node;\n  }\n};\n_extends(Document, Node);\nfunction Element() {\n  this._nsMap = {};\n}\n;\nElement.prototype = {\n  nodeType: ELEMENT_NODE,\n  hasAttribute: function hasAttribute(name) {\n    return this.getAttributeNode(name) != null;\n  },\n  getAttribute: function getAttribute(name) {\n    var attr = this.getAttributeNode(name);\n    return attr && attr.value || '';\n  },\n  getAttributeNode: function getAttributeNode(name) {\n    return this.attributes.getNamedItem(name);\n  },\n  setAttribute: function setAttribute(name, value) {\n    var attr = this.ownerDocument.createAttribute(name);\n    attr.value = attr.nodeValue = \"\" + value;\n    this.setAttributeNode(attr);\n  },\n  removeAttribute: function removeAttribute(name) {\n    var attr = this.getAttributeNode(name);\n    attr && this.removeAttributeNode(attr);\n  },\n  //four real opeartion method\n  appendChild: function appendChild(newChild) {\n    if (newChild.nodeType === DOCUMENT_FRAGMENT_NODE) {\n      return this.insertBefore(newChild, null);\n    } else {\n      return _appendSingleChild(this, newChild);\n    }\n  },\n  setAttributeNode: function setAttributeNode(newAttr) {\n    return this.attributes.setNamedItem(newAttr);\n  },\n  setAttributeNodeNS: function setAttributeNodeNS(newAttr) {\n    return this.attributes.setNamedItemNS(newAttr);\n  },\n  removeAttributeNode: function removeAttributeNode(oldAttr) {\n    //console.log(this == oldAttr.ownerElement)\n    return this.attributes.removeNamedItem(oldAttr.nodeName);\n  },\n  //get real attribute name,and remove it by removeAttributeNode\n  removeAttributeNS: function removeAttributeNS(namespaceURI, localName) {\n    var old = this.getAttributeNodeNS(namespaceURI, localName);\n    old && this.removeAttributeNode(old);\n  },\n  hasAttributeNS: function hasAttributeNS(namespaceURI, localName) {\n    return this.getAttributeNodeNS(namespaceURI, localName) != null;\n  },\n  getAttributeNS: function getAttributeNS(namespaceURI, localName) {\n    var attr = this.getAttributeNodeNS(namespaceURI, localName);\n    return attr && attr.value || '';\n  },\n  setAttributeNS: function setAttributeNS(namespaceURI, qualifiedName, value) {\n    var attr = this.ownerDocument.createAttributeNS(namespaceURI, qualifiedName);\n    attr.value = attr.nodeValue = \"\" + value;\n    this.setAttributeNode(attr);\n  },\n  getAttributeNodeNS: function getAttributeNodeNS(namespaceURI, localName) {\n    return this.attributes.getNamedItemNS(namespaceURI, localName);\n  },\n  getElementsByTagName: function getElementsByTagName(tagName) {\n    return new LiveNodeList(this, function (base) {\n      var ls = [];\n      _visitNode(base, function (node) {\n        if (node !== base && node.nodeType == ELEMENT_NODE && (tagName === '*' || node.tagName == tagName)) {\n          ls.push(node);\n        }\n      });\n      return ls;\n    });\n  },\n  getElementsByTagNameNS: function getElementsByTagNameNS(namespaceURI, localName) {\n    return new LiveNodeList(this, function (base) {\n      var ls = [];\n      _visitNode(base, function (node) {\n        if (node !== base && node.nodeType === ELEMENT_NODE && (namespaceURI === '*' || node.namespaceURI === namespaceURI) && (localName === '*' || node.localName == localName)) {\n          ls.push(node);\n        }\n      });\n      return ls;\n    });\n  }\n};\nDocument.prototype.getElementsByTagName = Element.prototype.getElementsByTagName;\nDocument.prototype.getElementsByTagNameNS = Element.prototype.getElementsByTagNameNS;\n_extends(Element, Node);\nfunction Attr() {}\n;\nAttr.prototype.nodeType = ATTRIBUTE_NODE;\n_extends(Attr, Node);\nfunction CharacterData() {}\n;\nCharacterData.prototype = {\n  data: '',\n  substringData: function substringData(offset, count) {\n    return this.data.substring(offset, offset + count);\n  },\n  appendData: function appendData(text) {\n    text = this.data + text;\n    this.nodeValue = this.data = text;\n    this.length = text.length;\n  },\n  insertData: function insertData(offset, text) {\n    this.replaceData(offset, 0, text);\n  },\n  appendChild: function appendChild(newChild) {\n    throw new Error(ExceptionMessage[HIERARCHY_REQUEST_ERR]);\n  },\n  deleteData: function deleteData(offset, count) {\n    this.replaceData(offset, count, \"\");\n  },\n  replaceData: function replaceData(offset, count, text) {\n    var start = this.data.substring(0, offset);\n    var end = this.data.substring(offset + count);\n    text = start + text + end;\n    this.nodeValue = this.data = text;\n    this.length = text.length;\n  }\n};\n_extends(CharacterData, Node);\nfunction Text() {}\n;\nText.prototype = {\n  nodeName: \"#text\",\n  nodeType: TEXT_NODE,\n  splitText: function splitText(offset) {\n    var text = this.data;\n    var newText = text.substring(offset);\n    text = text.substring(0, offset);\n    this.data = this.nodeValue = text;\n    this.length = text.length;\n    var newNode = this.ownerDocument.createTextNode(newText);\n    if (this.parentNode) {\n      this.parentNode.insertBefore(newNode, this.nextSibling);\n    }\n    return newNode;\n  }\n};\n_extends(Text, CharacterData);\nfunction Comment() {}\n;\nComment.prototype = {\n  nodeName: \"#comment\",\n  nodeType: COMMENT_NODE\n};\n_extends(Comment, CharacterData);\nfunction CDATASection() {}\n;\nCDATASection.prototype = {\n  nodeName: \"#cdata-section\",\n  nodeType: CDATA_SECTION_NODE\n};\n_extends(CDATASection, CharacterData);\nfunction DocumentType() {}\n;\nDocumentType.prototype.nodeType = DOCUMENT_TYPE_NODE;\n_extends(DocumentType, Node);\nfunction Notation() {}\n;\nNotation.prototype.nodeType = NOTATION_NODE;\n_extends(Notation, Node);\nfunction Entity() {}\n;\nEntity.prototype.nodeType = ENTITY_NODE;\n_extends(Entity, Node);\nfunction EntityReference() {}\n;\nEntityReference.prototype.nodeType = ENTITY_REFERENCE_NODE;\n_extends(EntityReference, Node);\nfunction DocumentFragment() {}\n;\nDocumentFragment.prototype.nodeName = \"#document-fragment\";\nDocumentFragment.prototype.nodeType = DOCUMENT_FRAGMENT_NODE;\n_extends(DocumentFragment, Node);\nfunction ProcessingInstruction() {}\nProcessingInstruction.prototype.nodeType = PROCESSING_INSTRUCTION_NODE;\n_extends(ProcessingInstruction, Node);\nfunction XMLSerializer() {}\nXMLSerializer.prototype.serializeToString = function (node, isHtml, nodeFilter) {\n  return nodeSerializeToString.call(node, isHtml, nodeFilter);\n};\nNode.prototype.toString = nodeSerializeToString;\nfunction nodeSerializeToString(isHtml, nodeFilter) {\n  var buf = [];\n  var refNode = this.nodeType == 9 ? this.documentElement : this;\n  var prefix = refNode.prefix;\n  var uri = refNode.namespaceURI;\n  if (uri && prefix == null) {\n    //console.log(prefix)\n    var prefix = refNode.lookupPrefix(uri);\n    if (prefix == null) {\n      //isHTML = true;\n      var visibleNamespaces = [{\n        namespace: uri,\n        prefix: null\n      }\n      //{namespace:uri,prefix:''}\n      ];\n    }\n  }\n  serializeToString(this, buf, isHtml, nodeFilter, visibleNamespaces);\n  //console.log('###',this.nodeType,uri,prefix,buf.join(''))\n  return buf.join('');\n}\nfunction needNamespaceDefine(node, isHTML, visibleNamespaces) {\n  var prefix = node.prefix || '';\n  var uri = node.namespaceURI;\n  if (!prefix && !uri) {\n    return false;\n  }\n  if (prefix === \"xml\" && uri === \"http://www.w3.org/XML/1998/namespace\" || uri == 'http://www.w3.org/2000/xmlns/') {\n    return false;\n  }\n  var i = visibleNamespaces.length;\n  //console.log('@@@@',node.tagName,prefix,uri,visibleNamespaces)\n  while (i--) {\n    var ns = visibleNamespaces[i];\n    // get namespace prefix\n    //console.log(node.nodeType,node.tagName,ns.prefix,prefix)\n    if (ns.prefix == prefix) {\n      return ns.namespace != uri;\n    }\n  }\n  //console.log(isHTML,uri,prefix=='')\n  //if(isHTML && prefix ==null && uri == 'http://www.w3.org/1999/xhtml'){\n  //\treturn false;\n  //}\n  //node.flag = '11111'\n  //console.error(3,true,node.flag,node.prefix,node.namespaceURI)\n  return true;\n}\nfunction serializeToString(node, buf, isHTML, nodeFilter, visibleNamespaces) {\n  if (nodeFilter) {\n    node = nodeFilter(node);\n    if (node) {\n      if (typeof node == 'string') {\n        buf.push(node);\n        return;\n      }\n    } else {\n      return;\n    }\n    //buf.sort.apply(attrs, attributeSorter);\n  }\n  switch (node.nodeType) {\n    case ELEMENT_NODE:\n      if (!visibleNamespaces) visibleNamespaces = [];\n      var startVisibleNamespaces = visibleNamespaces.length;\n      var attrs = node.attributes;\n      var len = attrs.length;\n      var child = node.firstChild;\n      var nodeName = node.tagName;\n      isHTML = htmlns === node.namespaceURI || isHTML;\n      buf.push('<', nodeName);\n      for (var i = 0; i < len; i++) {\n        // add namespaces for attributes\n        var attr = attrs.item(i);\n        if (attr.prefix == 'xmlns') {\n          visibleNamespaces.push({\n            prefix: attr.localName,\n            namespace: attr.value\n          });\n        } else if (attr.nodeName == 'xmlns') {\n          visibleNamespaces.push({\n            prefix: '',\n            namespace: attr.value\n          });\n        }\n      }\n      for (var i = 0; i < len; i++) {\n        var attr = attrs.item(i);\n        if (needNamespaceDefine(attr, isHTML, visibleNamespaces)) {\n          var prefix = attr.prefix || '';\n          var uri = attr.namespaceURI;\n          var ns = prefix ? ' xmlns:' + prefix : \" xmlns\";\n          buf.push(ns, '=\"', uri, '\"');\n          visibleNamespaces.push({\n            prefix: prefix,\n            namespace: uri\n          });\n        }\n        serializeToString(attr, buf, isHTML, nodeFilter, visibleNamespaces);\n      }\n      // add namespace for current node\t\t\n      if (needNamespaceDefine(node, isHTML, visibleNamespaces)) {\n        var prefix = node.prefix || '';\n        var uri = node.namespaceURI;\n        var ns = prefix ? ' xmlns:' + prefix : \" xmlns\";\n        buf.push(ns, '=\"', uri, '\"');\n        visibleNamespaces.push({\n          prefix: prefix,\n          namespace: uri\n        });\n      }\n      if (child || isHTML && !/^(?:meta|link|img|br|hr|input)$/i.test(nodeName)) {\n        buf.push('>');\n        //if is cdata child node\n        if (isHTML && /^script$/i.test(nodeName)) {\n          while (child) {\n            if (child.data) {\n              buf.push(child.data);\n            } else {\n              serializeToString(child, buf, isHTML, nodeFilter, visibleNamespaces);\n            }\n            child = child.nextSibling;\n          }\n        } else {\n          while (child) {\n            serializeToString(child, buf, isHTML, nodeFilter, visibleNamespaces);\n            child = child.nextSibling;\n          }\n        }\n        buf.push('</', nodeName, '>');\n      } else {\n        buf.push('/>');\n      }\n      // remove added visible namespaces\n      //visibleNamespaces.length = startVisibleNamespaces;\n      return;\n    case DOCUMENT_NODE:\n    case DOCUMENT_FRAGMENT_NODE:\n      var child = node.firstChild;\n      while (child) {\n        serializeToString(child, buf, isHTML, nodeFilter, visibleNamespaces);\n        child = child.nextSibling;\n      }\n      return;\n    case ATTRIBUTE_NODE:\n      return buf.push(' ', node.name, '=\"', node.value.replace(/[<&\"]/g, _xmlEncoder), '\"');\n    case TEXT_NODE:\n      return buf.push(node.data.replace(/[<&]/g, _xmlEncoder));\n    case CDATA_SECTION_NODE:\n      return buf.push('<![CDATA[', node.data, ']]>');\n    case COMMENT_NODE:\n      return buf.push(\"<!--\", node.data, \"-->\");\n    case DOCUMENT_TYPE_NODE:\n      var pubid = node.publicId;\n      var sysid = node.systemId;\n      buf.push('<!DOCTYPE ', node.name);\n      if (pubid) {\n        buf.push(' PUBLIC \"', pubid);\n        if (sysid && sysid != '.') {\n          buf.push('\" \"', sysid);\n        }\n        buf.push('\">');\n      } else if (sysid && sysid != '.') {\n        buf.push(' SYSTEM \"', sysid, '\">');\n      } else {\n        var sub = node.internalSubset;\n        if (sub) {\n          buf.push(\" [\", sub, \"]\");\n        }\n        buf.push(\">\");\n      }\n      return;\n    case PROCESSING_INSTRUCTION_NODE:\n      return buf.push(\"<?\", node.target, \" \", node.data, \"?>\");\n    case ENTITY_REFERENCE_NODE:\n      return buf.push('&', node.nodeName, ';');\n    //case ENTITY_NODE:\n    //case NOTATION_NODE:\n    default:\n      buf.push('??', node.nodeName);\n  }\n}\nfunction _importNode(doc, node, deep) {\n  var node2;\n  switch (node.nodeType) {\n    case ELEMENT_NODE:\n      node2 = node.cloneNode(false);\n      node2.ownerDocument = doc;\n    //var attrs = node2.attributes;\n    //var len = attrs.length;\n    //for(var i=0;i<len;i++){\n    //node2.setAttributeNodeNS(importNode(doc,attrs.item(i),deep));\n    //}\n    case DOCUMENT_FRAGMENT_NODE:\n      break;\n    case ATTRIBUTE_NODE:\n      deep = true;\n      break;\n    //case ENTITY_REFERENCE_NODE:\n    //case PROCESSING_INSTRUCTION_NODE:\n    ////case TEXT_NODE:\n    //case CDATA_SECTION_NODE:\n    //case COMMENT_NODE:\n    //\tdeep = false;\n    //\tbreak;\n    //case DOCUMENT_NODE:\n    //case DOCUMENT_TYPE_NODE:\n    //cannot be imported.\n    //case ENTITY_NODE:\n    //case NOTATION_NODE：\n    //can not hit in level3\n    //default:throw e;\n  }\n  if (!node2) {\n    node2 = node.cloneNode(false); //false\n  }\n  node2.ownerDocument = doc;\n  node2.parentNode = null;\n  if (deep) {\n    var child = node.firstChild;\n    while (child) {\n      node2.appendChild(_importNode(doc, child, deep));\n      child = child.nextSibling;\n    }\n  }\n  return node2;\n}\n//\n//var _relationMap = {firstChild:1,lastChild:1,previousSibling:1,nextSibling:1,\n//\t\t\t\t\tattributes:1,childNodes:1,parentNode:1,documentElement:1,doctype,};\nfunction _cloneNode(doc, node, deep) {\n  var node2 = new node.constructor();\n  for (var n in node) {\n    var v = node[n];\n    if (typeof v != 'object') {\n      if (v != node2[n]) {\n        node2[n] = v;\n      }\n    }\n  }\n  if (node.childNodes) {\n    node2.childNodes = new NodeList();\n  }\n  node2.ownerDocument = doc;\n  switch (node2.nodeType) {\n    case ELEMENT_NODE:\n      var attrs = node.attributes;\n      var attrs2 = node2.attributes = new NamedNodeMap();\n      var len = attrs.length;\n      attrs2._ownerElement = node2;\n      for (var i = 0; i < len; i++) {\n        node2.setAttributeNode(_cloneNode(doc, attrs.item(i), true));\n      }\n      break;\n      ;\n    case ATTRIBUTE_NODE:\n      deep = true;\n  }\n  if (deep) {\n    var child = node.firstChild;\n    while (child) {\n      node2.appendChild(_cloneNode(doc, child, deep));\n      child = child.nextSibling;\n    }\n  }\n  return node2;\n}\nfunction __set__(object, key, value) {\n  object[key] = value;\n}\n//do dynamic\ntry {\n  if (Object.defineProperty) {\n    var _getTextContent = function getTextContent(node) {\n      switch (node.nodeType) {\n        case ELEMENT_NODE:\n        case DOCUMENT_FRAGMENT_NODE:\n          var buf = [];\n          node = node.firstChild;\n          while (node) {\n            if (node.nodeType !== 7 && node.nodeType !== 8) {\n              buf.push(_getTextContent(node));\n            }\n            node = node.nextSibling;\n          }\n          return buf.join('');\n        default:\n          return node.nodeValue;\n      }\n    };\n    Object.defineProperty(LiveNodeList.prototype, 'length', {\n      get: function get() {\n        _updateLiveList(this);\n        return this.$$length;\n      }\n    });\n    Object.defineProperty(Node.prototype, 'textContent', {\n      get: function get() {\n        return _getTextContent(this);\n      },\n      set: function set(data) {\n        switch (this.nodeType) {\n          case ELEMENT_NODE:\n          case DOCUMENT_FRAGMENT_NODE:\n            while (this.firstChild) {\n              this.removeChild(this.firstChild);\n            }\n            if (data || String(data)) {\n              this.appendChild(this.ownerDocument.createTextNode(data));\n            }\n            break;\n          default:\n            //TODO:\n            this.data = data;\n            this.value = data;\n            this.nodeValue = data;\n        }\n      }\n    });\n    __set__ = function __set__(object, key, value) {\n      //console.log(value)\n      object['$$' + key] = value;\n    };\n  }\n} catch (e) {//ie8\n}\n\n//if(typeof require == 'function'){\nexports.DOMImplementation = DOMImplementation;\nexports.XMLSerializer = XMLSerializer;\n//}", "map": {"version": 3, "names": ["copy", "src", "dest", "p", "_extends", "Class", "Super", "pt", "prototype", "Object", "create", "ppt", "__proto__", "t", "constructor", "console", "error", "htmlns", "NodeType", "ELEMENT_NODE", "ATTRIBUTE_NODE", "TEXT_NODE", "CDATA_SECTION_NODE", "ENTITY_REFERENCE_NODE", "ENTITY_NODE", "PROCESSING_INSTRUCTION_NODE", "COMMENT_NODE", "DOCUMENT_NODE", "DOCUMENT_TYPE_NODE", "DOCUMENT_FRAGMENT_NODE", "NOTATION_NODE", "ExceptionCode", "ExceptionMessage", "INDEX_SIZE_ERR", "DOMSTRING_SIZE_ERR", "HIERARCHY_REQUEST_ERR", "WRONG_DOCUMENT_ERR", "INVALID_CHARACTER_ERR", "NO_DATA_ALLOWED_ERR", "NO_MODIFICATION_ALLOWED_ERR", "NOT_FOUND_ERR", "NOT_SUPPORTED_ERR", "INUSE_ATTRIBUTE_ERR", "INVALID_STATE_ERR", "SYNTAX_ERR", "INVALID_MODIFICATION_ERR", "NAMESPACE_ERR", "INVALID_ACCESS_ERR", "DOMException", "code", "message", "Error", "call", "captureStackTrace", "NodeList", "length", "item", "index", "toString", "isHTML", "nodeFilter", "buf", "i", "serializeToString", "join", "LiveNodeList", "node", "refresh", "_node", "_refresh", "_updateLiveList", "list", "inc", "_inc", "ownerDocument", "ls", "__set__", "NamedNodeMap", "_findNodeIndex", "_addNamedNode", "el", "newAttr", "oldAttr", "ownerElement", "doc", "_onRemoveAttribute", "_onAddAttribute", "_removeNamedNode", "attr", "lastIndex", "tagName", "getNamedItem", "key", "nodeName", "setNamedItem", "_ownerElement", "setNamedItemNS", "getNamedItemNS", "namespaceURI", "localName", "removeNamedItem", "removeNamedItemNS", "DOMImplementation", "features", "_features", "feature", "hasFeature", "version", "versions", "toLowerCase", "createDocument", "qualifiedName", "doctype", "Document", "implementation", "childNodes", "append<PERSON><PERSON><PERSON>", "root", "createElementNS", "createDocumentType", "publicId", "systemId", "DocumentType", "name", "Node", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "previousSibling", "nextS<PERSON>ling", "attributes", "parentNode", "nodeValue", "prefix", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "refChild", "_insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "_remove<PERSON><PERSON>d", "hasChildNodes", "cloneNode", "deep", "normalize", "child", "next", "nodeType", "appendData", "data", "isSupported", "hasAttributes", "lookupPrefix", "map", "_nsMap", "n", "lookupNamespaceURI", "isDefaultNamespace", "_xmlEncoder", "c", "charCodeAt", "_visitNode", "callback", "ns", "value", "remove", "_onUpdateChild", "cs", "previous", "<PERSON><PERSON><PERSON><PERSON>", "cp", "newFirst", "newLast", "pre", "_appendSingleChild", "documentElement", "importNode", "importedNode", "getElementById", "id", "rtv", "getAttribute", "createElement", "Element", "attrs", "createDocumentFragment", "DocumentFragment", "createTextNode", "Text", "createComment", "Comment", "createCDATASection", "CDATASection", "createProcessingInstruction", "target", "ProcessingInstruction", "createAttribute", "Attr", "specified", "createEntityReference", "EntityReference", "pl", "split", "createAttributeNS", "hasAttribute", "getAttributeNode", "setAttribute", "setAttributeNode", "removeAttribute", "removeAttributeNode", "setAttributeNodeNS", "removeAttributeNS", "old", "getAttributeNodeNS", "hasAttributeNS", "getAttributeNS", "setAttributeNS", "getElementsByTagName", "base", "push", "getElementsByTagNameNS", "CharacterData", "substringData", "offset", "count", "substring", "text", "insertData", "replaceData", "deleteData", "start", "end", "splitText", "newText", "newNode", "Notation", "Entity", "XMLSerializer", "isHtml", "nodeSerializeToString", "refNode", "uri", "visibleNamespaces", "namespace", "needNamespaceDefine", "startVisibleNamespaces", "len", "test", "replace", "pubid", "sysid", "sub", "internalSubset", "node2", "v", "attrs2", "object", "defineProperty", "getTextContent", "get", "$$length", "set", "String", "e", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/xmldom@0.1.31/node_modules/xmldom/dom.js"], "sourcesContent": ["/*\n * DOM Level 2\n * Object DOMException\n * @see http://www.w3.org/TR/REC-DOM-Level-1/ecma-script-language-binding.html\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/ecma-script-binding.html\n */\n\nfunction copy(src,dest){\n\tfor(var p in src){\n\t\tdest[p] = src[p];\n\t}\n}\n/**\n^\\w+\\.prototype\\.([_\\w]+)\\s*=\\s*((?:.*\\{\\s*?[\\r\\n][\\s\\S]*?^})|\\S.*?(?=[;\\r\\n]));?\n^\\w+\\.prototype\\.([_\\w]+)\\s*=\\s*(\\S.*?(?=[;\\r\\n]));?\n */\nfunction _extends(Class,Super){\n\tvar pt = Class.prototype;\n\tif(Object.create){\n\t\tvar ppt = Object.create(Super.prototype)\n\t\tpt.__proto__ = ppt;\n\t}\n\tif(!(pt instanceof Super)){\n\t\tfunction t(){};\n\t\tt.prototype = Super.prototype;\n\t\tt = new t();\n\t\tcopy(pt,t);\n\t\tClass.prototype = pt = t;\n\t}\n\tif(pt.constructor != Class){\n\t\tif(typeof Class != 'function'){\n\t\t\tconsole.error(\"unknow Class:\"+Class)\n\t\t}\n\t\tpt.constructor = Class\n\t}\n}\nvar htmlns = 'http://www.w3.org/1999/xhtml' ;\n// Node Types\nvar NodeType = {}\nvar ELEMENT_NODE                = NodeType.ELEMENT_NODE                = 1;\nvar ATTRIBUTE_NODE              = NodeType.ATTRIBUTE_NODE              = 2;\nvar TEXT_NODE                   = NodeType.TEXT_NODE                   = 3;\nvar CDATA_SECTION_NODE          = NodeType.CDATA_SECTION_NODE          = 4;\nvar ENTITY_REFERENCE_NODE       = NodeType.ENTITY_REFERENCE_NODE       = 5;\nvar ENTITY_NODE                 = NodeType.ENTITY_NODE                 = 6;\nvar PROCESSING_INSTRUCTION_NODE = NodeType.PROCESSING_INSTRUCTION_NODE = 7;\nvar COMMENT_NODE                = NodeType.COMMENT_NODE                = 8;\nvar DOCUMENT_NODE               = NodeType.DOCUMENT_NODE               = 9;\nvar DOCUMENT_TYPE_NODE          = NodeType.DOCUMENT_TYPE_NODE          = 10;\nvar DOCUMENT_FRAGMENT_NODE      = NodeType.DOCUMENT_FRAGMENT_NODE      = 11;\nvar NOTATION_NODE               = NodeType.NOTATION_NODE               = 12;\n\n// ExceptionCode\nvar ExceptionCode = {}\nvar ExceptionMessage = {};\nvar INDEX_SIZE_ERR              = ExceptionCode.INDEX_SIZE_ERR              = ((ExceptionMessage[1]=\"Index size error\"),1);\nvar DOMSTRING_SIZE_ERR          = ExceptionCode.DOMSTRING_SIZE_ERR          = ((ExceptionMessage[2]=\"DOMString size error\"),2);\nvar HIERARCHY_REQUEST_ERR       = ExceptionCode.HIERARCHY_REQUEST_ERR       = ((ExceptionMessage[3]=\"Hierarchy request error\"),3);\nvar WRONG_DOCUMENT_ERR          = ExceptionCode.WRONG_DOCUMENT_ERR          = ((ExceptionMessage[4]=\"Wrong document\"),4);\nvar INVALID_CHARACTER_ERR       = ExceptionCode.INVALID_CHARACTER_ERR       = ((ExceptionMessage[5]=\"Invalid character\"),5);\nvar NO_DATA_ALLOWED_ERR         = ExceptionCode.NO_DATA_ALLOWED_ERR         = ((ExceptionMessage[6]=\"No data allowed\"),6);\nvar NO_MODIFICATION_ALLOWED_ERR = ExceptionCode.NO_MODIFICATION_ALLOWED_ERR = ((ExceptionMessage[7]=\"No modification allowed\"),7);\nvar NOT_FOUND_ERR               = ExceptionCode.NOT_FOUND_ERR               = ((ExceptionMessage[8]=\"Not found\"),8);\nvar NOT_SUPPORTED_ERR           = ExceptionCode.NOT_SUPPORTED_ERR           = ((ExceptionMessage[9]=\"Not supported\"),9);\nvar INUSE_ATTRIBUTE_ERR         = ExceptionCode.INUSE_ATTRIBUTE_ERR         = ((ExceptionMessage[10]=\"Attribute in use\"),10);\n//level2\nvar INVALID_STATE_ERR        \t= ExceptionCode.INVALID_STATE_ERR        \t= ((ExceptionMessage[11]=\"Invalid state\"),11);\nvar SYNTAX_ERR               \t= ExceptionCode.SYNTAX_ERR               \t= ((ExceptionMessage[12]=\"Syntax error\"),12);\nvar INVALID_MODIFICATION_ERR \t= ExceptionCode.INVALID_MODIFICATION_ERR \t= ((ExceptionMessage[13]=\"Invalid modification\"),13);\nvar NAMESPACE_ERR            \t= ExceptionCode.NAMESPACE_ERR           \t= ((ExceptionMessage[14]=\"Invalid namespace\"),14);\nvar INVALID_ACCESS_ERR       \t= ExceptionCode.INVALID_ACCESS_ERR      \t= ((ExceptionMessage[15]=\"Invalid access\"),15);\n\n\nfunction DOMException(code, message) {\n\tif(message instanceof Error){\n\t\tvar error = message;\n\t}else{\n\t\terror = this;\n\t\tError.call(this, ExceptionMessage[code]);\n\t\tthis.message = ExceptionMessage[code];\n\t\tif(Error.captureStackTrace) Error.captureStackTrace(this, DOMException);\n\t}\n\terror.code = code;\n\tif(message) this.message = this.message + \": \" + message;\n\treturn error;\n};\nDOMException.prototype = Error.prototype;\ncopy(ExceptionCode,DOMException)\n/**\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/core.html#ID-*********\n * The NodeList interface provides the abstraction of an ordered collection of nodes, without defining or constraining how this collection is implemented. NodeList objects in the DOM are live.\n * The items in the NodeList are accessible via an integral index, starting from 0.\n */\nfunction NodeList() {\n};\nNodeList.prototype = {\n\t/**\n\t * The number of nodes in the list. The range of valid child node indices is 0 to length-1 inclusive.\n\t * @standard level1\n\t */\n\tlength:0, \n\t/**\n\t * Returns the indexth item in the collection. If index is greater than or equal to the number of nodes in the list, this returns null.\n\t * @standard level1\n\t * @param index  unsigned long \n\t *   Index into the collection.\n\t * @return Node\n\t * \tThe node at the indexth position in the NodeList, or null if that is not a valid index. \n\t */\n\titem: function(index) {\n\t\treturn this[index] || null;\n\t},\n\ttoString:function(isHTML,nodeFilter){\n\t\tfor(var buf = [], i = 0;i<this.length;i++){\n\t\t\tserializeToString(this[i],buf,isHTML,nodeFilter);\n\t\t}\n\t\treturn buf.join('');\n\t}\n};\nfunction LiveNodeList(node,refresh){\n\tthis._node = node;\n\tthis._refresh = refresh\n\t_updateLiveList(this);\n}\nfunction _updateLiveList(list){\n\tvar inc = list._node._inc || list._node.ownerDocument._inc;\n\tif(list._inc != inc){\n\t\tvar ls = list._refresh(list._node);\n\t\t//console.log(ls.length)\n\t\t__set__(list,'length',ls.length);\n\t\tcopy(ls,list);\n\t\tlist._inc = inc;\n\t}\n}\nLiveNodeList.prototype.item = function(i){\n\t_updateLiveList(this);\n\treturn this[i];\n}\n\n_extends(LiveNodeList,NodeList);\n/**\n * \n * Objects implementing the NamedNodeMap interface are used to represent collections of nodes that can be accessed by name. Note that NamedNodeMap does not inherit from NodeList; NamedNodeMaps are not maintained in any particular order. Objects contained in an object implementing NamedNodeMap may also be accessed by an ordinal index, but this is simply to allow convenient enumeration of the contents of a NamedNodeMap, and does not imply that the DOM specifies an order to these Nodes.\n * NamedNodeMap objects in the DOM are live.\n * used for attributes or DocumentType entities \n */\nfunction NamedNodeMap() {\n};\n\nfunction _findNodeIndex(list,node){\n\tvar i = list.length;\n\twhile(i--){\n\t\tif(list[i] === node){return i}\n\t}\n}\n\nfunction _addNamedNode(el,list,newAttr,oldAttr){\n\tif(oldAttr){\n\t\tlist[_findNodeIndex(list,oldAttr)] = newAttr;\n\t}else{\n\t\tlist[list.length++] = newAttr;\n\t}\n\tif(el){\n\t\tnewAttr.ownerElement = el;\n\t\tvar doc = el.ownerDocument;\n\t\tif(doc){\n\t\t\toldAttr && _onRemoveAttribute(doc,el,oldAttr);\n\t\t\t_onAddAttribute(doc,el,newAttr);\n\t\t}\n\t}\n}\nfunction _removeNamedNode(el,list,attr){\n\t//console.log('remove attr:'+attr)\n\tvar i = _findNodeIndex(list,attr);\n\tif(i>=0){\n\t\tvar lastIndex = list.length-1\n\t\twhile(i<lastIndex){\n\t\t\tlist[i] = list[++i]\n\t\t}\n\t\tlist.length = lastIndex;\n\t\tif(el){\n\t\t\tvar doc = el.ownerDocument;\n\t\t\tif(doc){\n\t\t\t\t_onRemoveAttribute(doc,el,attr);\n\t\t\t\tattr.ownerElement = null;\n\t\t\t}\n\t\t}\n\t}else{\n\t\tthrow DOMException(NOT_FOUND_ERR,new Error(el.tagName+'@'+attr))\n\t}\n}\nNamedNodeMap.prototype = {\n\tlength:0,\n\titem:NodeList.prototype.item,\n\tgetNamedItem: function(key) {\n//\t\tif(key.indexOf(':')>0 || key == 'xmlns'){\n//\t\t\treturn null;\n//\t\t}\n\t\t//console.log()\n\t\tvar i = this.length;\n\t\twhile(i--){\n\t\t\tvar attr = this[i];\n\t\t\t//console.log(attr.nodeName,key)\n\t\t\tif(attr.nodeName == key){\n\t\t\t\treturn attr;\n\t\t\t}\n\t\t}\n\t},\n\tsetNamedItem: function(attr) {\n\t\tvar el = attr.ownerElement;\n\t\tif(el && el!=this._ownerElement){\n\t\t\tthrow new DOMException(INUSE_ATTRIBUTE_ERR);\n\t\t}\n\t\tvar oldAttr = this.getNamedItem(attr.nodeName);\n\t\t_addNamedNode(this._ownerElement,this,attr,oldAttr);\n\t\treturn oldAttr;\n\t},\n\t/* returns Node */\n\tsetNamedItemNS: function(attr) {// raises: WRONG_DOCUMENT_ERR,NO_MODIFICATION_ALLOWED_ERR,INUSE_ATTRIBUTE_ERR\n\t\tvar el = attr.ownerElement, oldAttr;\n\t\tif(el && el!=this._ownerElement){\n\t\t\tthrow new DOMException(INUSE_ATTRIBUTE_ERR);\n\t\t}\n\t\toldAttr = this.getNamedItemNS(attr.namespaceURI,attr.localName);\n\t\t_addNamedNode(this._ownerElement,this,attr,oldAttr);\n\t\treturn oldAttr;\n\t},\n\n\t/* returns Node */\n\tremoveNamedItem: function(key) {\n\t\tvar attr = this.getNamedItem(key);\n\t\t_removeNamedNode(this._ownerElement,this,attr);\n\t\treturn attr;\n\t\t\n\t\t\n\t},// raises: NOT_FOUND_ERR,NO_MODIFICATION_ALLOWED_ERR\n\t\n\t//for level2\n\tremoveNamedItemNS:function(namespaceURI,localName){\n\t\tvar attr = this.getNamedItemNS(namespaceURI,localName);\n\t\t_removeNamedNode(this._ownerElement,this,attr);\n\t\treturn attr;\n\t},\n\tgetNamedItemNS: function(namespaceURI, localName) {\n\t\tvar i = this.length;\n\t\twhile(i--){\n\t\t\tvar node = this[i];\n\t\t\tif(node.localName == localName && node.namespaceURI == namespaceURI){\n\t\t\t\treturn node;\n\t\t\t}\n\t\t}\n\t\treturn null;\n\t}\n};\n/**\n * @see http://www.w3.org/TR/REC-DOM-Level-1/level-one-core.html#ID-102161490\n */\nfunction DOMImplementation(/* Object */ features) {\n\tthis._features = {};\n\tif (features) {\n\t\tfor (var feature in features) {\n\t\t\t this._features = features[feature];\n\t\t}\n\t}\n};\n\nDOMImplementation.prototype = {\n\thasFeature: function(/* string */ feature, /* string */ version) {\n\t\tvar versions = this._features[feature.toLowerCase()];\n\t\tif (versions && (!version || version in versions)) {\n\t\t\treturn true;\n\t\t} else {\n\t\t\treturn false;\n\t\t}\n\t},\n\t// Introduced in DOM Level 2:\n\tcreateDocument:function(namespaceURI,  qualifiedName, doctype){// raises:INVALID_CHARACTER_ERR,NAMESPACE_ERR,WRONG_DOCUMENT_ERR\n\t\tvar doc = new Document();\n\t\tdoc.implementation = this;\n\t\tdoc.childNodes = new NodeList();\n\t\tdoc.doctype = doctype;\n\t\tif(doctype){\n\t\t\tdoc.appendChild(doctype);\n\t\t}\n\t\tif(qualifiedName){\n\t\t\tvar root = doc.createElementNS(namespaceURI,qualifiedName);\n\t\t\tdoc.appendChild(root);\n\t\t}\n\t\treturn doc;\n\t},\n\t// Introduced in DOM Level 2:\n\tcreateDocumentType:function(qualifiedName, publicId, systemId){// raises:INVALID_CHARACTER_ERR,NAMESPACE_ERR\n\t\tvar node = new DocumentType();\n\t\tnode.name = qualifiedName;\n\t\tnode.nodeName = qualifiedName;\n\t\tnode.publicId = publicId;\n\t\tnode.systemId = systemId;\n\t\t// Introduced in DOM Level 2:\n\t\t//readonly attribute DOMString        internalSubset;\n\t\t\n\t\t//TODO:..\n\t\t//  readonly attribute NamedNodeMap     entities;\n\t\t//  readonly attribute NamedNodeMap     notations;\n\t\treturn node;\n\t}\n};\n\n\n/**\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/core.html#ID-1950641247\n */\n\nfunction Node() {\n};\n\nNode.prototype = {\n\tfirstChild : null,\n\tlastChild : null,\n\tpreviousSibling : null,\n\tnextSibling : null,\n\tattributes : null,\n\tparentNode : null,\n\tchildNodes : null,\n\townerDocument : null,\n\tnodeValue : null,\n\tnamespaceURI : null,\n\tprefix : null,\n\tlocalName : null,\n\t// Modified in DOM Level 2:\n\tinsertBefore:function(newChild, refChild){//raises \n\t\treturn _insertBefore(this,newChild,refChild);\n\t},\n\treplaceChild:function(newChild, oldChild){//raises \n\t\tthis.insertBefore(newChild,oldChild);\n\t\tif(oldChild){\n\t\t\tthis.removeChild(oldChild);\n\t\t}\n\t},\n\tremoveChild:function(oldChild){\n\t\treturn _removeChild(this,oldChild);\n\t},\n\tappendChild:function(newChild){\n\t\treturn this.insertBefore(newChild,null);\n\t},\n\thasChildNodes:function(){\n\t\treturn this.firstChild != null;\n\t},\n\tcloneNode:function(deep){\n\t\treturn cloneNode(this.ownerDocument||this,this,deep);\n\t},\n\t// Modified in DOM Level 2:\n\tnormalize:function(){\n\t\tvar child = this.firstChild;\n\t\twhile(child){\n\t\t\tvar next = child.nextSibling;\n\t\t\tif(next && next.nodeType == TEXT_NODE && child.nodeType == TEXT_NODE){\n\t\t\t\tthis.removeChild(next);\n\t\t\t\tchild.appendData(next.data);\n\t\t\t}else{\n\t\t\t\tchild.normalize();\n\t\t\t\tchild = next;\n\t\t\t}\n\t\t}\n\t},\n  \t// Introduced in DOM Level 2:\n\tisSupported:function(feature, version){\n\t\treturn this.ownerDocument.implementation.hasFeature(feature,version);\n\t},\n    // Introduced in DOM Level 2:\n    hasAttributes:function(){\n    \treturn this.attributes.length>0;\n    },\n    lookupPrefix:function(namespaceURI){\n    \tvar el = this;\n    \twhile(el){\n    \t\tvar map = el._nsMap;\n    \t\t//console.dir(map)\n    \t\tif(map){\n    \t\t\tfor(var n in map){\n    \t\t\t\tif(map[n] == namespaceURI){\n    \t\t\t\t\treturn n;\n    \t\t\t\t}\n    \t\t\t}\n    \t\t}\n    \t\tel = el.nodeType == ATTRIBUTE_NODE?el.ownerDocument : el.parentNode;\n    \t}\n    \treturn null;\n    },\n    // Introduced in DOM Level 3:\n    lookupNamespaceURI:function(prefix){\n    \tvar el = this;\n    \twhile(el){\n    \t\tvar map = el._nsMap;\n    \t\t//console.dir(map)\n    \t\tif(map){\n    \t\t\tif(prefix in map){\n    \t\t\t\treturn map[prefix] ;\n    \t\t\t}\n    \t\t}\n    \t\tel = el.nodeType == ATTRIBUTE_NODE?el.ownerDocument : el.parentNode;\n    \t}\n    \treturn null;\n    },\n    // Introduced in DOM Level 3:\n    isDefaultNamespace:function(namespaceURI){\n    \tvar prefix = this.lookupPrefix(namespaceURI);\n    \treturn prefix == null;\n    }\n};\n\n\nfunction _xmlEncoder(c){\n\treturn c == '<' && '&lt;' ||\n         c == '>' && '&gt;' ||\n         c == '&' && '&amp;' ||\n         c == '\"' && '&quot;' ||\n         '&#'+c.charCodeAt()+';'\n}\n\n\ncopy(NodeType,Node);\ncopy(NodeType,Node.prototype);\n\n/**\n * @param callback return true for continue,false for break\n * @return boolean true: break visit;\n */\nfunction _visitNode(node,callback){\n\tif(callback(node)){\n\t\treturn true;\n\t}\n\tif(node = node.firstChild){\n\t\tdo{\n\t\t\tif(_visitNode(node,callback)){return true}\n        }while(node=node.nextSibling)\n    }\n}\n\n\n\nfunction Document(){\n}\nfunction _onAddAttribute(doc,el,newAttr){\n\tdoc && doc._inc++;\n\tvar ns = newAttr.namespaceURI ;\n\tif(ns == 'http://www.w3.org/2000/xmlns/'){\n\t\t//update namespace\n\t\tel._nsMap[newAttr.prefix?newAttr.localName:''] = newAttr.value\n\t}\n}\nfunction _onRemoveAttribute(doc,el,newAttr,remove){\n\tdoc && doc._inc++;\n\tvar ns = newAttr.namespaceURI ;\n\tif(ns == 'http://www.w3.org/2000/xmlns/'){\n\t\t//update namespace\n\t\tdelete el._nsMap[newAttr.prefix?newAttr.localName:'']\n\t}\n}\nfunction _onUpdateChild(doc,el,newChild){\n\tif(doc && doc._inc){\n\t\tdoc._inc++;\n\t\t//update childNodes\n\t\tvar cs = el.childNodes;\n\t\tif(newChild){\n\t\t\tcs[cs.length++] = newChild;\n\t\t}else{\n\t\t\t//console.log(1)\n\t\t\tvar child = el.firstChild;\n\t\t\tvar i = 0;\n\t\t\twhile(child){\n\t\t\t\tcs[i++] = child;\n\t\t\t\tchild =child.nextSibling;\n\t\t\t}\n\t\t\tcs.length = i;\n\t\t}\n\t}\n}\n\n/**\n * attributes;\n * children;\n * \n * writeable properties:\n * nodeValue,Attr:value,CharacterData:data\n * prefix\n */\nfunction _removeChild(parentNode,child){\n\tvar previous = child.previousSibling;\n\tvar next = child.nextSibling;\n\tif(previous){\n\t\tprevious.nextSibling = next;\n\t}else{\n\t\tparentNode.firstChild = next\n\t}\n\tif(next){\n\t\tnext.previousSibling = previous;\n\t}else{\n\t\tparentNode.lastChild = previous;\n\t}\n\t_onUpdateChild(parentNode.ownerDocument,parentNode);\n\treturn child;\n}\n/**\n * preformance key(refChild == null)\n */\nfunction _insertBefore(parentNode,newChild,nextChild){\n\tvar cp = newChild.parentNode;\n\tif(cp){\n\t\tcp.removeChild(newChild);//remove and update\n\t}\n\tif(newChild.nodeType === DOCUMENT_FRAGMENT_NODE){\n\t\tvar newFirst = newChild.firstChild;\n\t\tif (newFirst == null) {\n\t\t\treturn newChild;\n\t\t}\n\t\tvar newLast = newChild.lastChild;\n\t}else{\n\t\tnewFirst = newLast = newChild;\n\t}\n\tvar pre = nextChild ? nextChild.previousSibling : parentNode.lastChild;\n\n\tnewFirst.previousSibling = pre;\n\tnewLast.nextSibling = nextChild;\n\t\n\t\n\tif(pre){\n\t\tpre.nextSibling = newFirst;\n\t}else{\n\t\tparentNode.firstChild = newFirst;\n\t}\n\tif(nextChild == null){\n\t\tparentNode.lastChild = newLast;\n\t}else{\n\t\tnextChild.previousSibling = newLast;\n\t}\n\tdo{\n\t\tnewFirst.parentNode = parentNode;\n\t}while(newFirst !== newLast && (newFirst= newFirst.nextSibling))\n\t_onUpdateChild(parentNode.ownerDocument||parentNode,parentNode);\n\t//console.log(parentNode.lastChild.nextSibling == null)\n\tif (newChild.nodeType == DOCUMENT_FRAGMENT_NODE) {\n\t\tnewChild.firstChild = newChild.lastChild = null;\n\t}\n\treturn newChild;\n}\nfunction _appendSingleChild(parentNode,newChild){\n\tvar cp = newChild.parentNode;\n\tif(cp){\n\t\tvar pre = parentNode.lastChild;\n\t\tcp.removeChild(newChild);//remove and update\n\t\tvar pre = parentNode.lastChild;\n\t}\n\tvar pre = parentNode.lastChild;\n\tnewChild.parentNode = parentNode;\n\tnewChild.previousSibling = pre;\n\tnewChild.nextSibling = null;\n\tif(pre){\n\t\tpre.nextSibling = newChild;\n\t}else{\n\t\tparentNode.firstChild = newChild;\n\t}\n\tparentNode.lastChild = newChild;\n\t_onUpdateChild(parentNode.ownerDocument,parentNode,newChild);\n\treturn newChild;\n\t//console.log(\"__aa\",parentNode.lastChild.nextSibling == null)\n}\nDocument.prototype = {\n\t//implementation : null,\n\tnodeName :  '#document',\n\tnodeType :  DOCUMENT_NODE,\n\tdoctype :  null,\n\tdocumentElement :  null,\n\t_inc : 1,\n\t\n\tinsertBefore :  function(newChild, refChild){//raises \n\t\tif(newChild.nodeType == DOCUMENT_FRAGMENT_NODE){\n\t\t\tvar child = newChild.firstChild;\n\t\t\twhile(child){\n\t\t\t\tvar next = child.nextSibling;\n\t\t\t\tthis.insertBefore(child,refChild);\n\t\t\t\tchild = next;\n\t\t\t}\n\t\t\treturn newChild;\n\t\t}\n\t\tif(this.documentElement == null && newChild.nodeType == ELEMENT_NODE){\n\t\t\tthis.documentElement = newChild;\n\t\t}\n\t\t\n\t\treturn _insertBefore(this,newChild,refChild),(newChild.ownerDocument = this),newChild;\n\t},\n\tremoveChild :  function(oldChild){\n\t\tif(this.documentElement == oldChild){\n\t\t\tthis.documentElement = null;\n\t\t}\n\t\treturn _removeChild(this,oldChild);\n\t},\n\t// Introduced in DOM Level 2:\n\timportNode : function(importedNode,deep){\n\t\treturn importNode(this,importedNode,deep);\n\t},\n\t// Introduced in DOM Level 2:\n\tgetElementById :\tfunction(id){\n\t\tvar rtv = null;\n\t\t_visitNode(this.documentElement,function(node){\n\t\t\tif(node.nodeType == ELEMENT_NODE){\n\t\t\t\tif(node.getAttribute('id') == id){\n\t\t\t\t\trtv = node;\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t\treturn rtv;\n\t},\n\t\n\t//document factory method:\n\tcreateElement :\tfunction(tagName){\n\t\tvar node = new Element();\n\t\tnode.ownerDocument = this;\n\t\tnode.nodeName = tagName;\n\t\tnode.tagName = tagName;\n\t\tnode.childNodes = new NodeList();\n\t\tvar attrs\t= node.attributes = new NamedNodeMap();\n\t\tattrs._ownerElement = node;\n\t\treturn node;\n\t},\n\tcreateDocumentFragment :\tfunction(){\n\t\tvar node = new DocumentFragment();\n\t\tnode.ownerDocument = this;\n\t\tnode.childNodes = new NodeList();\n\t\treturn node;\n\t},\n\tcreateTextNode :\tfunction(data){\n\t\tvar node = new Text();\n\t\tnode.ownerDocument = this;\n\t\tnode.appendData(data)\n\t\treturn node;\n\t},\n\tcreateComment :\tfunction(data){\n\t\tvar node = new Comment();\n\t\tnode.ownerDocument = this;\n\t\tnode.appendData(data)\n\t\treturn node;\n\t},\n\tcreateCDATASection :\tfunction(data){\n\t\tvar node = new CDATASection();\n\t\tnode.ownerDocument = this;\n\t\tnode.appendData(data)\n\t\treturn node;\n\t},\n\tcreateProcessingInstruction :\tfunction(target,data){\n\t\tvar node = new ProcessingInstruction();\n\t\tnode.ownerDocument = this;\n\t\tnode.tagName = node.target = target;\n\t\tnode.nodeValue= node.data = data;\n\t\treturn node;\n\t},\n\tcreateAttribute :\tfunction(name){\n\t\tvar node = new Attr();\n\t\tnode.ownerDocument\t= this;\n\t\tnode.name = name;\n\t\tnode.nodeName\t= name;\n\t\tnode.localName = name;\n\t\tnode.specified = true;\n\t\treturn node;\n\t},\n\tcreateEntityReference :\tfunction(name){\n\t\tvar node = new EntityReference();\n\t\tnode.ownerDocument\t= this;\n\t\tnode.nodeName\t= name;\n\t\treturn node;\n\t},\n\t// Introduced in DOM Level 2:\n\tcreateElementNS :\tfunction(namespaceURI,qualifiedName){\n\t\tvar node = new Element();\n\t\tvar pl = qualifiedName.split(':');\n\t\tvar attrs\t= node.attributes = new NamedNodeMap();\n\t\tnode.childNodes = new NodeList();\n\t\tnode.ownerDocument = this;\n\t\tnode.nodeName = qualifiedName;\n\t\tnode.tagName = qualifiedName;\n\t\tnode.namespaceURI = namespaceURI;\n\t\tif(pl.length == 2){\n\t\t\tnode.prefix = pl[0];\n\t\t\tnode.localName = pl[1];\n\t\t}else{\n\t\t\t//el.prefix = null;\n\t\t\tnode.localName = qualifiedName;\n\t\t}\n\t\tattrs._ownerElement = node;\n\t\treturn node;\n\t},\n\t// Introduced in DOM Level 2:\n\tcreateAttributeNS :\tfunction(namespaceURI,qualifiedName){\n\t\tvar node = new Attr();\n\t\tvar pl = qualifiedName.split(':');\n\t\tnode.ownerDocument = this;\n\t\tnode.nodeName = qualifiedName;\n\t\tnode.name = qualifiedName;\n\t\tnode.namespaceURI = namespaceURI;\n\t\tnode.specified = true;\n\t\tif(pl.length == 2){\n\t\t\tnode.prefix = pl[0];\n\t\t\tnode.localName = pl[1];\n\t\t}else{\n\t\t\t//el.prefix = null;\n\t\t\tnode.localName = qualifiedName;\n\t\t}\n\t\treturn node;\n\t}\n};\n_extends(Document,Node);\n\n\nfunction Element() {\n\tthis._nsMap = {};\n};\nElement.prototype = {\n\tnodeType : ELEMENT_NODE,\n\thasAttribute : function(name){\n\t\treturn this.getAttributeNode(name)!=null;\n\t},\n\tgetAttribute : function(name){\n\t\tvar attr = this.getAttributeNode(name);\n\t\treturn attr && attr.value || '';\n\t},\n\tgetAttributeNode : function(name){\n\t\treturn this.attributes.getNamedItem(name);\n\t},\n\tsetAttribute : function(name, value){\n\t\tvar attr = this.ownerDocument.createAttribute(name);\n\t\tattr.value = attr.nodeValue = \"\" + value;\n\t\tthis.setAttributeNode(attr)\n\t},\n\tremoveAttribute : function(name){\n\t\tvar attr = this.getAttributeNode(name)\n\t\tattr && this.removeAttributeNode(attr);\n\t},\n\t\n\t//four real opeartion method\n\tappendChild:function(newChild){\n\t\tif(newChild.nodeType === DOCUMENT_FRAGMENT_NODE){\n\t\t\treturn this.insertBefore(newChild,null);\n\t\t}else{\n\t\t\treturn _appendSingleChild(this,newChild);\n\t\t}\n\t},\n\tsetAttributeNode : function(newAttr){\n\t\treturn this.attributes.setNamedItem(newAttr);\n\t},\n\tsetAttributeNodeNS : function(newAttr){\n\t\treturn this.attributes.setNamedItemNS(newAttr);\n\t},\n\tremoveAttributeNode : function(oldAttr){\n\t\t//console.log(this == oldAttr.ownerElement)\n\t\treturn this.attributes.removeNamedItem(oldAttr.nodeName);\n\t},\n\t//get real attribute name,and remove it by removeAttributeNode\n\tremoveAttributeNS : function(namespaceURI, localName){\n\t\tvar old = this.getAttributeNodeNS(namespaceURI, localName);\n\t\told && this.removeAttributeNode(old);\n\t},\n\t\n\thasAttributeNS : function(namespaceURI, localName){\n\t\treturn this.getAttributeNodeNS(namespaceURI, localName)!=null;\n\t},\n\tgetAttributeNS : function(namespaceURI, localName){\n\t\tvar attr = this.getAttributeNodeNS(namespaceURI, localName);\n\t\treturn attr && attr.value || '';\n\t},\n\tsetAttributeNS : function(namespaceURI, qualifiedName, value){\n\t\tvar attr = this.ownerDocument.createAttributeNS(namespaceURI, qualifiedName);\n\t\tattr.value = attr.nodeValue = \"\" + value;\n\t\tthis.setAttributeNode(attr)\n\t},\n\tgetAttributeNodeNS : function(namespaceURI, localName){\n\t\treturn this.attributes.getNamedItemNS(namespaceURI, localName);\n\t},\n\t\n\tgetElementsByTagName : function(tagName){\n\t\treturn new LiveNodeList(this,function(base){\n\t\t\tvar ls = [];\n\t\t\t_visitNode(base,function(node){\n\t\t\t\tif(node !== base && node.nodeType == ELEMENT_NODE && (tagName === '*' || node.tagName == tagName)){\n\t\t\t\t\tls.push(node);\n\t\t\t\t}\n\t\t\t});\n\t\t\treturn ls;\n\t\t});\n\t},\n\tgetElementsByTagNameNS : function(namespaceURI, localName){\n\t\treturn new LiveNodeList(this,function(base){\n\t\t\tvar ls = [];\n\t\t\t_visitNode(base,function(node){\n\t\t\t\tif(node !== base && node.nodeType === ELEMENT_NODE && (namespaceURI === '*' || node.namespaceURI === namespaceURI) && (localName === '*' || node.localName == localName)){\n\t\t\t\t\tls.push(node);\n\t\t\t\t}\n\t\t\t});\n\t\t\treturn ls;\n\t\t\t\n\t\t});\n\t}\n};\nDocument.prototype.getElementsByTagName = Element.prototype.getElementsByTagName;\nDocument.prototype.getElementsByTagNameNS = Element.prototype.getElementsByTagNameNS;\n\n\n_extends(Element,Node);\nfunction Attr() {\n};\nAttr.prototype.nodeType = ATTRIBUTE_NODE;\n_extends(Attr,Node);\n\n\nfunction CharacterData() {\n};\nCharacterData.prototype = {\n\tdata : '',\n\tsubstringData : function(offset, count) {\n\t\treturn this.data.substring(offset, offset+count);\n\t},\n\tappendData: function(text) {\n\t\ttext = this.data+text;\n\t\tthis.nodeValue = this.data = text;\n\t\tthis.length = text.length;\n\t},\n\tinsertData: function(offset,text) {\n\t\tthis.replaceData(offset,0,text);\n\t\n\t},\n\tappendChild:function(newChild){\n\t\tthrow new Error(ExceptionMessage[HIERARCHY_REQUEST_ERR])\n\t},\n\tdeleteData: function(offset, count) {\n\t\tthis.replaceData(offset,count,\"\");\n\t},\n\treplaceData: function(offset, count, text) {\n\t\tvar start = this.data.substring(0,offset);\n\t\tvar end = this.data.substring(offset+count);\n\t\ttext = start + text + end;\n\t\tthis.nodeValue = this.data = text;\n\t\tthis.length = text.length;\n\t}\n}\n_extends(CharacterData,Node);\nfunction Text() {\n};\nText.prototype = {\n\tnodeName : \"#text\",\n\tnodeType : TEXT_NODE,\n\tsplitText : function(offset) {\n\t\tvar text = this.data;\n\t\tvar newText = text.substring(offset);\n\t\ttext = text.substring(0, offset);\n\t\tthis.data = this.nodeValue = text;\n\t\tthis.length = text.length;\n\t\tvar newNode = this.ownerDocument.createTextNode(newText);\n\t\tif(this.parentNode){\n\t\t\tthis.parentNode.insertBefore(newNode, this.nextSibling);\n\t\t}\n\t\treturn newNode;\n\t}\n}\n_extends(Text,CharacterData);\nfunction Comment() {\n};\nComment.prototype = {\n\tnodeName : \"#comment\",\n\tnodeType : COMMENT_NODE\n}\n_extends(Comment,CharacterData);\n\nfunction CDATASection() {\n};\nCDATASection.prototype = {\n\tnodeName : \"#cdata-section\",\n\tnodeType : CDATA_SECTION_NODE\n}\n_extends(CDATASection,CharacterData);\n\n\nfunction DocumentType() {\n};\nDocumentType.prototype.nodeType = DOCUMENT_TYPE_NODE;\n_extends(DocumentType,Node);\n\nfunction Notation() {\n};\nNotation.prototype.nodeType = NOTATION_NODE;\n_extends(Notation,Node);\n\nfunction Entity() {\n};\nEntity.prototype.nodeType = ENTITY_NODE;\n_extends(Entity,Node);\n\nfunction EntityReference() {\n};\nEntityReference.prototype.nodeType = ENTITY_REFERENCE_NODE;\n_extends(EntityReference,Node);\n\nfunction DocumentFragment() {\n};\nDocumentFragment.prototype.nodeName =\t\"#document-fragment\";\nDocumentFragment.prototype.nodeType =\tDOCUMENT_FRAGMENT_NODE;\n_extends(DocumentFragment,Node);\n\n\nfunction ProcessingInstruction() {\n}\nProcessingInstruction.prototype.nodeType = PROCESSING_INSTRUCTION_NODE;\n_extends(ProcessingInstruction,Node);\nfunction XMLSerializer(){}\nXMLSerializer.prototype.serializeToString = function(node,isHtml,nodeFilter){\n\treturn nodeSerializeToString.call(node,isHtml,nodeFilter);\n}\nNode.prototype.toString = nodeSerializeToString;\nfunction nodeSerializeToString(isHtml,nodeFilter){\n\tvar buf = [];\n\tvar refNode = this.nodeType == 9?this.documentElement:this;\n\tvar prefix = refNode.prefix;\n\tvar uri = refNode.namespaceURI;\n\t\n\tif(uri && prefix == null){\n\t\t//console.log(prefix)\n\t\tvar prefix = refNode.lookupPrefix(uri);\n\t\tif(prefix == null){\n\t\t\t//isHTML = true;\n\t\t\tvar visibleNamespaces=[\n\t\t\t{namespace:uri,prefix:null}\n\t\t\t//{namespace:uri,prefix:''}\n\t\t\t]\n\t\t}\n\t}\n\tserializeToString(this,buf,isHtml,nodeFilter,visibleNamespaces);\n\t//console.log('###',this.nodeType,uri,prefix,buf.join(''))\n\treturn buf.join('');\n}\nfunction needNamespaceDefine(node,isHTML, visibleNamespaces) {\n\tvar prefix = node.prefix||'';\n\tvar uri = node.namespaceURI;\n\tif (!prefix && !uri){\n\t\treturn false;\n\t}\n\tif (prefix === \"xml\" && uri === \"http://www.w3.org/XML/1998/namespace\" \n\t\t|| uri == 'http://www.w3.org/2000/xmlns/'){\n\t\treturn false;\n\t}\n\t\n\tvar i = visibleNamespaces.length \n\t//console.log('@@@@',node.tagName,prefix,uri,visibleNamespaces)\n\twhile (i--) {\n\t\tvar ns = visibleNamespaces[i];\n\t\t// get namespace prefix\n\t\t//console.log(node.nodeType,node.tagName,ns.prefix,prefix)\n\t\tif (ns.prefix == prefix){\n\t\t\treturn ns.namespace != uri;\n\t\t}\n\t}\n\t//console.log(isHTML,uri,prefix=='')\n\t//if(isHTML && prefix ==null && uri == 'http://www.w3.org/1999/xhtml'){\n\t//\treturn false;\n\t//}\n\t//node.flag = '11111'\n\t//console.error(3,true,node.flag,node.prefix,node.namespaceURI)\n\treturn true;\n}\nfunction serializeToString(node,buf,isHTML,nodeFilter,visibleNamespaces){\n\tif(nodeFilter){\n\t\tnode = nodeFilter(node);\n\t\tif(node){\n\t\t\tif(typeof node == 'string'){\n\t\t\t\tbuf.push(node);\n\t\t\t\treturn;\n\t\t\t}\n\t\t}else{\n\t\t\treturn;\n\t\t}\n\t\t//buf.sort.apply(attrs, attributeSorter);\n\t}\n\tswitch(node.nodeType){\n\tcase ELEMENT_NODE:\n\t\tif (!visibleNamespaces) visibleNamespaces = [];\n\t\tvar startVisibleNamespaces = visibleNamespaces.length;\n\t\tvar attrs = node.attributes;\n\t\tvar len = attrs.length;\n\t\tvar child = node.firstChild;\n\t\tvar nodeName = node.tagName;\n\t\t\n\t\tisHTML =  (htmlns === node.namespaceURI) ||isHTML \n\t\tbuf.push('<',nodeName);\n\t\t\n\t\t\n\t\t\n\t\tfor(var i=0;i<len;i++){\n\t\t\t// add namespaces for attributes\n\t\t\tvar attr = attrs.item(i);\n\t\t\tif (attr.prefix == 'xmlns') {\n\t\t\t\tvisibleNamespaces.push({ prefix: attr.localName, namespace: attr.value });\n\t\t\t}else if(attr.nodeName == 'xmlns'){\n\t\t\t\tvisibleNamespaces.push({ prefix: '', namespace: attr.value });\n\t\t\t}\n\t\t}\n\t\tfor(var i=0;i<len;i++){\n\t\t\tvar attr = attrs.item(i);\n\t\t\tif (needNamespaceDefine(attr,isHTML, visibleNamespaces)) {\n\t\t\t\tvar prefix = attr.prefix||'';\n\t\t\t\tvar uri = attr.namespaceURI;\n\t\t\t\tvar ns = prefix ? ' xmlns:' + prefix : \" xmlns\";\n\t\t\t\tbuf.push(ns, '=\"' , uri , '\"');\n\t\t\t\tvisibleNamespaces.push({ prefix: prefix, namespace:uri });\n\t\t\t}\n\t\t\tserializeToString(attr,buf,isHTML,nodeFilter,visibleNamespaces);\n\t\t}\n\t\t// add namespace for current node\t\t\n\t\tif (needNamespaceDefine(node,isHTML, visibleNamespaces)) {\n\t\t\tvar prefix = node.prefix||'';\n\t\t\tvar uri = node.namespaceURI;\n\t\t\tvar ns = prefix ? ' xmlns:' + prefix : \" xmlns\";\n\t\t\tbuf.push(ns, '=\"' , uri , '\"');\n\t\t\tvisibleNamespaces.push({ prefix: prefix, namespace:uri });\n\t\t}\n\t\t\n\t\tif(child || isHTML && !/^(?:meta|link|img|br|hr|input)$/i.test(nodeName)){\n\t\t\tbuf.push('>');\n\t\t\t//if is cdata child node\n\t\t\tif(isHTML && /^script$/i.test(nodeName)){\n\t\t\t\twhile(child){\n\t\t\t\t\tif(child.data){\n\t\t\t\t\t\tbuf.push(child.data);\n\t\t\t\t\t}else{\n\t\t\t\t\t\tserializeToString(child,buf,isHTML,nodeFilter,visibleNamespaces);\n\t\t\t\t\t}\n\t\t\t\t\tchild = child.nextSibling;\n\t\t\t\t}\n\t\t\t}else\n\t\t\t{\n\t\t\t\twhile(child){\n\t\t\t\t\tserializeToString(child,buf,isHTML,nodeFilter,visibleNamespaces);\n\t\t\t\t\tchild = child.nextSibling;\n\t\t\t\t}\n\t\t\t}\n\t\t\tbuf.push('</',nodeName,'>');\n\t\t}else{\n\t\t\tbuf.push('/>');\n\t\t}\n\t\t// remove added visible namespaces\n\t\t//visibleNamespaces.length = startVisibleNamespaces;\n\t\treturn;\n\tcase DOCUMENT_NODE:\n\tcase DOCUMENT_FRAGMENT_NODE:\n\t\tvar child = node.firstChild;\n\t\twhile(child){\n\t\t\tserializeToString(child,buf,isHTML,nodeFilter,visibleNamespaces);\n\t\t\tchild = child.nextSibling;\n\t\t}\n\t\treturn;\n\tcase ATTRIBUTE_NODE:\n\t\treturn buf.push(' ',node.name,'=\"',node.value.replace(/[<&\"]/g,_xmlEncoder),'\"');\n\tcase TEXT_NODE:\n\t\treturn buf.push(node.data.replace(/[<&]/g,_xmlEncoder));\n\tcase CDATA_SECTION_NODE:\n\t\treturn buf.push( '<![CDATA[',node.data,']]>');\n\tcase COMMENT_NODE:\n\t\treturn buf.push( \"<!--\",node.data,\"-->\");\n\tcase DOCUMENT_TYPE_NODE:\n\t\tvar pubid = node.publicId;\n\t\tvar sysid = node.systemId;\n\t\tbuf.push('<!DOCTYPE ',node.name);\n\t\tif(pubid){\n\t\t\tbuf.push(' PUBLIC \"',pubid);\n\t\t\tif (sysid && sysid!='.') {\n\t\t\t\tbuf.push( '\" \"',sysid);\n\t\t\t}\n\t\t\tbuf.push('\">');\n\t\t}else if(sysid && sysid!='.'){\n\t\t\tbuf.push(' SYSTEM \"',sysid,'\">');\n\t\t}else{\n\t\t\tvar sub = node.internalSubset;\n\t\t\tif(sub){\n\t\t\t\tbuf.push(\" [\",sub,\"]\");\n\t\t\t}\n\t\t\tbuf.push(\">\");\n\t\t}\n\t\treturn;\n\tcase PROCESSING_INSTRUCTION_NODE:\n\t\treturn buf.push( \"<?\",node.target,\" \",node.data,\"?>\");\n\tcase ENTITY_REFERENCE_NODE:\n\t\treturn buf.push( '&',node.nodeName,';');\n\t//case ENTITY_NODE:\n\t//case NOTATION_NODE:\n\tdefault:\n\t\tbuf.push('??',node.nodeName);\n\t}\n}\nfunction importNode(doc,node,deep){\n\tvar node2;\n\tswitch (node.nodeType) {\n\tcase ELEMENT_NODE:\n\t\tnode2 = node.cloneNode(false);\n\t\tnode2.ownerDocument = doc;\n\t\t//var attrs = node2.attributes;\n\t\t//var len = attrs.length;\n\t\t//for(var i=0;i<len;i++){\n\t\t\t//node2.setAttributeNodeNS(importNode(doc,attrs.item(i),deep));\n\t\t//}\n\tcase DOCUMENT_FRAGMENT_NODE:\n\t\tbreak;\n\tcase ATTRIBUTE_NODE:\n\t\tdeep = true;\n\t\tbreak;\n\t//case ENTITY_REFERENCE_NODE:\n\t//case PROCESSING_INSTRUCTION_NODE:\n\t////case TEXT_NODE:\n\t//case CDATA_SECTION_NODE:\n\t//case COMMENT_NODE:\n\t//\tdeep = false;\n\t//\tbreak;\n\t//case DOCUMENT_NODE:\n\t//case DOCUMENT_TYPE_NODE:\n\t//cannot be imported.\n\t//case ENTITY_NODE:\n\t//case NOTATION_NODE：\n\t//can not hit in level3\n\t//default:throw e;\n\t}\n\tif(!node2){\n\t\tnode2 = node.cloneNode(false);//false\n\t}\n\tnode2.ownerDocument = doc;\n\tnode2.parentNode = null;\n\tif(deep){\n\t\tvar child = node.firstChild;\n\t\twhile(child){\n\t\t\tnode2.appendChild(importNode(doc,child,deep));\n\t\t\tchild = child.nextSibling;\n\t\t}\n\t}\n\treturn node2;\n}\n//\n//var _relationMap = {firstChild:1,lastChild:1,previousSibling:1,nextSibling:1,\n//\t\t\t\t\tattributes:1,childNodes:1,parentNode:1,documentElement:1,doctype,};\nfunction cloneNode(doc,node,deep){\n\tvar node2 = new node.constructor();\n\tfor(var n in node){\n\t\tvar v = node[n];\n\t\tif(typeof v != 'object' ){\n\t\t\tif(v != node2[n]){\n\t\t\t\tnode2[n] = v;\n\t\t\t}\n\t\t}\n\t}\n\tif(node.childNodes){\n\t\tnode2.childNodes = new NodeList();\n\t}\n\tnode2.ownerDocument = doc;\n\tswitch (node2.nodeType) {\n\tcase ELEMENT_NODE:\n\t\tvar attrs\t= node.attributes;\n\t\tvar attrs2\t= node2.attributes = new NamedNodeMap();\n\t\tvar len = attrs.length\n\t\tattrs2._ownerElement = node2;\n\t\tfor(var i=0;i<len;i++){\n\t\t\tnode2.setAttributeNode(cloneNode(doc,attrs.item(i),true));\n\t\t}\n\t\tbreak;;\n\tcase ATTRIBUTE_NODE:\n\t\tdeep = true;\n\t}\n\tif(deep){\n\t\tvar child = node.firstChild;\n\t\twhile(child){\n\t\t\tnode2.appendChild(cloneNode(doc,child,deep));\n\t\t\tchild = child.nextSibling;\n\t\t}\n\t}\n\treturn node2;\n}\n\nfunction __set__(object,key,value){\n\tobject[key] = value\n}\n//do dynamic\ntry{\n\tif(Object.defineProperty){\n\t\tObject.defineProperty(LiveNodeList.prototype,'length',{\n\t\t\tget:function(){\n\t\t\t\t_updateLiveList(this);\n\t\t\t\treturn this.$$length;\n\t\t\t}\n\t\t});\n\t\tObject.defineProperty(Node.prototype,'textContent',{\n\t\t\tget:function(){\n\t\t\t\treturn getTextContent(this);\n\t\t\t},\n\t\t\tset:function(data){\n\t\t\t\tswitch(this.nodeType){\n\t\t\t\tcase ELEMENT_NODE:\n\t\t\t\tcase DOCUMENT_FRAGMENT_NODE:\n\t\t\t\t\twhile(this.firstChild){\n\t\t\t\t\t\tthis.removeChild(this.firstChild);\n\t\t\t\t\t}\n\t\t\t\t\tif(data || String(data)){\n\t\t\t\t\t\tthis.appendChild(this.ownerDocument.createTextNode(data));\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\t\t\t\tdefault:\n\t\t\t\t\t//TODO:\n\t\t\t\t\tthis.data = data;\n\t\t\t\t\tthis.value = data;\n\t\t\t\t\tthis.nodeValue = data;\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t\t\n\t\tfunction getTextContent(node){\n\t\t\tswitch(node.nodeType){\n\t\t\tcase ELEMENT_NODE:\n\t\t\tcase DOCUMENT_FRAGMENT_NODE:\n\t\t\t\tvar buf = [];\n\t\t\t\tnode = node.firstChild;\n\t\t\t\twhile(node){\n\t\t\t\t\tif(node.nodeType!==7 && node.nodeType !==8){\n\t\t\t\t\t\tbuf.push(getTextContent(node));\n\t\t\t\t\t}\n\t\t\t\t\tnode = node.nextSibling;\n\t\t\t\t}\n\t\t\t\treturn buf.join('');\n\t\t\tdefault:\n\t\t\t\treturn node.nodeValue;\n\t\t\t}\n\t\t}\n\t\t__set__ = function(object,key,value){\n\t\t\t//console.log(value)\n\t\t\tobject['$$'+key] = value\n\t\t}\n\t}\n}catch(e){//ie8\n}\n\n//if(typeof require == 'function'){\n\texports.DOMImplementation = DOMImplementation;\n\texports.XMLSerializer = XMLSerializer;\n//}\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,IAAIA,CAACC,GAAG,EAACC,IAAI,EAAC;EACtB,KAAI,IAAIC,CAAC,IAAIF,GAAG,EAAC;IAChBC,IAAI,CAACC,CAAC,CAAC,GAAGF,GAAG,CAACE,CAAC,CAAC;EACjB;AACD;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,KAAK,EAACC,KAAK,EAAC;EAC7B,IAAIC,EAAE,GAAGF,KAAK,CAACG,SAAS;EACxB,IAAGC,MAAM,CAACC,MAAM,EAAC;IAChB,IAAIC,GAAG,GAAGF,MAAM,CAACC,MAAM,CAACJ,KAAK,CAACE,SAAS,CAAC;IACxCD,EAAE,CAACK,SAAS,GAAGD,GAAG;EACnB;EACA,IAAG,EAAEJ,EAAE,YAAYD,KAAK,CAAC,EAAC;IAAA,IAChBO,CAAC,GAAV,SAASA,CAACA,CAAA,EAAE,CAAC,CAAC;IAAA;IACdA,CAAC,CAACL,SAAS,GAAGF,KAAK,CAACE,SAAS;IAC7BK,CAAC,GAAG,IAAIA,CAAC,CAAC,CAAC;IACXb,IAAI,CAACO,EAAE,EAACM,CAAC,CAAC;IACVR,KAAK,CAACG,SAAS,GAAGD,EAAE,GAAGM,CAAC;EACzB;EACA,IAAGN,EAAE,CAACO,WAAW,IAAIT,KAAK,EAAC;IAC1B,IAAG,OAAOA,KAAK,IAAI,UAAU,EAAC;MAC7BU,OAAO,CAACC,KAAK,CAAC,eAAe,GAACX,KAAK,CAAC;IACrC;IACAE,EAAE,CAACO,WAAW,GAAGT,KAAK;EACvB;AACD;AACA,IAAIY,MAAM,GAAG,8BAA8B;AAC3C;AACA,IAAIC,QAAQ,GAAG,CAAC,CAAC;AACjB,IAAIC,YAAY,GAAkBD,QAAQ,CAACC,YAAY,GAAkB,CAAC;AAC1E,IAAIC,cAAc,GAAgBF,QAAQ,CAACE,cAAc,GAAgB,CAAC;AAC1E,IAAIC,SAAS,GAAqBH,QAAQ,CAACG,SAAS,GAAqB,CAAC;AAC1E,IAAIC,kBAAkB,GAAYJ,QAAQ,CAACI,kBAAkB,GAAY,CAAC;AAC1E,IAAIC,qBAAqB,GAASL,QAAQ,CAACK,qBAAqB,GAAS,CAAC;AAC1E,IAAIC,WAAW,GAAmBN,QAAQ,CAACM,WAAW,GAAmB,CAAC;AAC1E,IAAIC,2BAA2B,GAAGP,QAAQ,CAACO,2BAA2B,GAAG,CAAC;AAC1E,IAAIC,YAAY,GAAkBR,QAAQ,CAACQ,YAAY,GAAkB,CAAC;AAC1E,IAAIC,aAAa,GAAiBT,QAAQ,CAACS,aAAa,GAAiB,CAAC;AAC1E,IAAIC,kBAAkB,GAAYV,QAAQ,CAACU,kBAAkB,GAAY,EAAE;AAC3E,IAAIC,sBAAsB,GAAQX,QAAQ,CAACW,sBAAsB,GAAQ,EAAE;AAC3E,IAAIC,aAAa,GAAiBZ,QAAQ,CAACY,aAAa,GAAiB,EAAE;;AAE3E;AACA,IAAIC,aAAa,GAAG,CAAC,CAAC;AACtB,IAAIC,gBAAgB,GAAG,CAAC,CAAC;AACzB,IAAIC,cAAc,GAAgBF,aAAa,CAACE,cAAc,IAAkBD,gBAAgB,CAAC,CAAC,CAAC,GAAC,kBAAkB,EAAE,CAAC,CAAC;AAC1H,IAAIE,kBAAkB,GAAYH,aAAa,CAACG,kBAAkB,IAAcF,gBAAgB,CAAC,CAAC,CAAC,GAAC,sBAAsB,EAAE,CAAC,CAAC;AAC9H,IAAIG,qBAAqB,GAASJ,aAAa,CAACI,qBAAqB,IAAWH,gBAAgB,CAAC,CAAC,CAAC,GAAC,yBAAyB,EAAE,CAAC,CAAC;AACjI,IAAII,kBAAkB,GAAYL,aAAa,CAACK,kBAAkB,IAAcJ,gBAAgB,CAAC,CAAC,CAAC,GAAC,gBAAgB,EAAE,CAAC,CAAC;AACxH,IAAIK,qBAAqB,GAASN,aAAa,CAACM,qBAAqB,IAAWL,gBAAgB,CAAC,CAAC,CAAC,GAAC,mBAAmB,EAAE,CAAC,CAAC;AAC3H,IAAIM,mBAAmB,GAAWP,aAAa,CAACO,mBAAmB,IAAaN,gBAAgB,CAAC,CAAC,CAAC,GAAC,iBAAiB,EAAE,CAAC,CAAC;AACzH,IAAIO,2BAA2B,GAAGR,aAAa,CAACQ,2BAA2B,IAAKP,gBAAgB,CAAC,CAAC,CAAC,GAAC,yBAAyB,EAAE,CAAC,CAAC;AACjI,IAAIQ,aAAa,GAAiBT,aAAa,CAACS,aAAa,IAAmBR,gBAAgB,CAAC,CAAC,CAAC,GAAC,WAAW,EAAE,CAAC,CAAC;AACnH,IAAIS,iBAAiB,GAAaV,aAAa,CAACU,iBAAiB,IAAeT,gBAAgB,CAAC,CAAC,CAAC,GAAC,eAAe,EAAE,CAAC,CAAC;AACvH,IAAIU,mBAAmB,GAAWX,aAAa,CAACW,mBAAmB,IAAaV,gBAAgB,CAAC,EAAE,CAAC,GAAC,kBAAkB,EAAE,EAAE,CAAC;AAC5H;AACA,IAAIW,iBAAiB,GAAWZ,aAAa,CAACY,iBAAiB,IAAaX,gBAAgB,CAAC,EAAE,CAAC,GAAC,eAAe,EAAE,EAAE,CAAC;AACrH,IAAIY,UAAU,GAAkBb,aAAa,CAACa,UAAU,IAAoBZ,gBAAgB,CAAC,EAAE,CAAC,GAAC,cAAc,EAAE,EAAE,CAAC;AACpH,IAAIa,wBAAwB,GAAId,aAAa,CAACc,wBAAwB,IAAMb,gBAAgB,CAAC,EAAE,CAAC,GAAC,sBAAsB,EAAE,EAAE,CAAC;AAC5H,IAAIc,aAAa,GAAef,aAAa,CAACe,aAAa,IAAgBd,gBAAgB,CAAC,EAAE,CAAC,GAAC,mBAAmB,EAAE,EAAE,CAAC;AACxH,IAAIe,kBAAkB,GAAUhB,aAAa,CAACgB,kBAAkB,IAAWf,gBAAgB,CAAC,EAAE,CAAC,GAAC,gBAAgB,EAAE,EAAE,CAAC;AAGrH,SAASgB,YAAYA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACpC,IAAGA,OAAO,YAAYC,KAAK,EAAC;IAC3B,IAAInC,KAAK,GAAGkC,OAAO;EACpB,CAAC,MAAI;IACJlC,KAAK,GAAG,IAAI;IACZmC,KAAK,CAACC,IAAI,CAAC,IAAI,EAAEpB,gBAAgB,CAACiB,IAAI,CAAC,CAAC;IACxC,IAAI,CAACC,OAAO,GAAGlB,gBAAgB,CAACiB,IAAI,CAAC;IACrC,IAAGE,KAAK,CAACE,iBAAiB,EAAEF,KAAK,CAACE,iBAAiB,CAAC,IAAI,EAAEL,YAAY,CAAC;EACxE;EACAhC,KAAK,CAACiC,IAAI,GAAGA,IAAI;EACjB,IAAGC,OAAO,EAAE,IAAI,CAACA,OAAO,GAAG,IAAI,CAACA,OAAO,GAAG,IAAI,GAAGA,OAAO;EACxD,OAAOlC,KAAK;AACb;AAAC;AACDgC,YAAY,CAACxC,SAAS,GAAG2C,KAAK,CAAC3C,SAAS;AACxCR,IAAI,CAAC+B,aAAa,EAACiB,YAAY,CAAC;AAChC;AACA;AACA;AACA;AACA;AACA,SAASM,QAAQA,CAAA,EAAG,CACpB;AAAC;AACDA,QAAQ,CAAC9C,SAAS,GAAG;EACpB;AACD;AACA;AACA;EACC+C,MAAM,EAAC,CAAC;EACR;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACCC,IAAI,EAAE,SAANA,IAAIA,CAAWC,KAAK,EAAE;IACrB,OAAO,IAAI,CAACA,KAAK,CAAC,IAAI,IAAI;EAC3B,CAAC;EACDC,QAAQ,EAAC,SAATA,QAAQA,CAAUC,MAAM,EAACC,UAAU,EAAC;IACnC,KAAI,IAAIC,GAAG,GAAG,EAAE,EAAEC,CAAC,GAAG,CAAC,EAACA,CAAC,GAAC,IAAI,CAACP,MAAM,EAACO,CAAC,EAAE,EAAC;MACzCC,iBAAiB,CAAC,IAAI,CAACD,CAAC,CAAC,EAACD,GAAG,EAACF,MAAM,EAACC,UAAU,CAAC;IACjD;IACA,OAAOC,GAAG,CAACG,IAAI,CAAC,EAAE,CAAC;EACpB;AACD,CAAC;AACD,SAASC,YAAYA,CAACC,IAAI,EAACC,OAAO,EAAC;EAClC,IAAI,CAACC,KAAK,GAAGF,IAAI;EACjB,IAAI,CAACG,QAAQ,GAAGF,OAAO;EACvBG,eAAe,CAAC,IAAI,CAAC;AACtB;AACA,SAASA,eAAeA,CAACC,IAAI,EAAC;EAC7B,IAAIC,GAAG,GAAGD,IAAI,CAACH,KAAK,CAACK,IAAI,IAAIF,IAAI,CAACH,KAAK,CAACM,aAAa,CAACD,IAAI;EAC1D,IAAGF,IAAI,CAACE,IAAI,IAAID,GAAG,EAAC;IACnB,IAAIG,EAAE,GAAGJ,IAAI,CAACF,QAAQ,CAACE,IAAI,CAACH,KAAK,CAAC;IAClC;IACAQ,OAAO,CAACL,IAAI,EAAC,QAAQ,EAACI,EAAE,CAACpB,MAAM,CAAC;IAChCvD,IAAI,CAAC2E,EAAE,EAACJ,IAAI,CAAC;IACbA,IAAI,CAACE,IAAI,GAAGD,GAAG;EAChB;AACD;AACAP,YAAY,CAACzD,SAAS,CAACgD,IAAI,GAAG,UAASM,CAAC,EAAC;EACxCQ,eAAe,CAAC,IAAI,CAAC;EACrB,OAAO,IAAI,CAACR,CAAC,CAAC;AACf,CAAC;AAED1D,QAAQ,CAAC6D,YAAY,EAACX,QAAQ,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,SAASuB,YAAYA,CAAA,EAAG,CACxB;AAAC;AAED,SAASC,cAAcA,CAACP,IAAI,EAACL,IAAI,EAAC;EACjC,IAAIJ,CAAC,GAAGS,IAAI,CAAChB,MAAM;EACnB,OAAMO,CAAC,EAAE,EAAC;IACT,IAAGS,IAAI,CAACT,CAAC,CAAC,KAAKI,IAAI,EAAC;MAAC,OAAOJ,CAAC;IAAA;EAC9B;AACD;AAEA,SAASiB,aAAaA,CAACC,EAAE,EAACT,IAAI,EAACU,OAAO,EAACC,OAAO,EAAC;EAC9C,IAAGA,OAAO,EAAC;IACVX,IAAI,CAACO,cAAc,CAACP,IAAI,EAACW,OAAO,CAAC,CAAC,GAAGD,OAAO;EAC7C,CAAC,MAAI;IACJV,IAAI,CAACA,IAAI,CAAChB,MAAM,EAAE,CAAC,GAAG0B,OAAO;EAC9B;EACA,IAAGD,EAAE,EAAC;IACLC,OAAO,CAACE,YAAY,GAAGH,EAAE;IACzB,IAAII,GAAG,GAAGJ,EAAE,CAACN,aAAa;IAC1B,IAAGU,GAAG,EAAC;MACNF,OAAO,IAAIG,kBAAkB,CAACD,GAAG,EAACJ,EAAE,EAACE,OAAO,CAAC;MAC7CI,eAAe,CAACF,GAAG,EAACJ,EAAE,EAACC,OAAO,CAAC;IAChC;EACD;AACD;AACA,SAASM,gBAAgBA,CAACP,EAAE,EAACT,IAAI,EAACiB,IAAI,EAAC;EACtC;EACA,IAAI1B,CAAC,GAAGgB,cAAc,CAACP,IAAI,EAACiB,IAAI,CAAC;EACjC,IAAG1B,CAAC,IAAE,CAAC,EAAC;IACP,IAAI2B,SAAS,GAAGlB,IAAI,CAAChB,MAAM,GAAC,CAAC;IAC7B,OAAMO,CAAC,GAAC2B,SAAS,EAAC;MACjBlB,IAAI,CAACT,CAAC,CAAC,GAAGS,IAAI,CAAC,EAAET,CAAC,CAAC;IACpB;IACAS,IAAI,CAAChB,MAAM,GAAGkC,SAAS;IACvB,IAAGT,EAAE,EAAC;MACL,IAAII,GAAG,GAAGJ,EAAE,CAACN,aAAa;MAC1B,IAAGU,GAAG,EAAC;QACNC,kBAAkB,CAACD,GAAG,EAACJ,EAAE,EAACQ,IAAI,CAAC;QAC/BA,IAAI,CAACL,YAAY,GAAG,IAAI;MACzB;IACD;EACD,CAAC,MAAI;IACJ,MAAMnC,YAAY,CAACR,aAAa,EAAC,IAAIW,KAAK,CAAC6B,EAAE,CAACU,OAAO,GAAC,GAAG,GAACF,IAAI,CAAC,CAAC;EACjE;AACD;AACAX,YAAY,CAACrE,SAAS,GAAG;EACxB+C,MAAM,EAAC,CAAC;EACRC,IAAI,EAACF,QAAQ,CAAC9C,SAAS,CAACgD,IAAI;EAC5BmC,YAAY,EAAE,SAAdA,YAAYA,CAAWC,GAAG,EAAE;IAC7B;IACA;IACA;IACE;IACA,IAAI9B,CAAC,GAAG,IAAI,CAACP,MAAM;IACnB,OAAMO,CAAC,EAAE,EAAC;MACT,IAAI0B,IAAI,GAAG,IAAI,CAAC1B,CAAC,CAAC;MAClB;MACA,IAAG0B,IAAI,CAACK,QAAQ,IAAID,GAAG,EAAC;QACvB,OAAOJ,IAAI;MACZ;IACD;EACD,CAAC;EACDM,YAAY,EAAE,SAAdA,YAAYA,CAAWN,IAAI,EAAE;IAC5B,IAAIR,EAAE,GAAGQ,IAAI,CAACL,YAAY;IAC1B,IAAGH,EAAE,IAAIA,EAAE,IAAE,IAAI,CAACe,aAAa,EAAC;MAC/B,MAAM,IAAI/C,YAAY,CAACN,mBAAmB,CAAC;IAC5C;IACA,IAAIwC,OAAO,GAAG,IAAI,CAACS,YAAY,CAACH,IAAI,CAACK,QAAQ,CAAC;IAC9Cd,aAAa,CAAC,IAAI,CAACgB,aAAa,EAAC,IAAI,EAACP,IAAI,EAACN,OAAO,CAAC;IACnD,OAAOA,OAAO;EACf,CAAC;EACD;EACAc,cAAc,EAAE,SAAhBA,cAAcA,CAAWR,IAAI,EAAE;IAAC;IAC/B,IAAIR,EAAE,GAAGQ,IAAI,CAACL,YAAY;MAAED,OAAO;IACnC,IAAGF,EAAE,IAAIA,EAAE,IAAE,IAAI,CAACe,aAAa,EAAC;MAC/B,MAAM,IAAI/C,YAAY,CAACN,mBAAmB,CAAC;IAC5C;IACAwC,OAAO,GAAG,IAAI,CAACe,cAAc,CAACT,IAAI,CAACU,YAAY,EAACV,IAAI,CAACW,SAAS,CAAC;IAC/DpB,aAAa,CAAC,IAAI,CAACgB,aAAa,EAAC,IAAI,EAACP,IAAI,EAACN,OAAO,CAAC;IACnD,OAAOA,OAAO;EACf,CAAC;EAED;EACAkB,eAAe,EAAE,SAAjBA,eAAeA,CAAWR,GAAG,EAAE;IAC9B,IAAIJ,IAAI,GAAG,IAAI,CAACG,YAAY,CAACC,GAAG,CAAC;IACjCL,gBAAgB,CAAC,IAAI,CAACQ,aAAa,EAAC,IAAI,EAACP,IAAI,CAAC;IAC9C,OAAOA,IAAI;EAGZ,CAAC;EAAC;;EAEF;EACAa,iBAAiB,EAAC,SAAlBA,iBAAiBA,CAAUH,YAAY,EAACC,SAAS,EAAC;IACjD,IAAIX,IAAI,GAAG,IAAI,CAACS,cAAc,CAACC,YAAY,EAACC,SAAS,CAAC;IACtDZ,gBAAgB,CAAC,IAAI,CAACQ,aAAa,EAAC,IAAI,EAACP,IAAI,CAAC;IAC9C,OAAOA,IAAI;EACZ,CAAC;EACDS,cAAc,EAAE,SAAhBA,cAAcA,CAAWC,YAAY,EAAEC,SAAS,EAAE;IACjD,IAAIrC,CAAC,GAAG,IAAI,CAACP,MAAM;IACnB,OAAMO,CAAC,EAAE,EAAC;MACT,IAAII,IAAI,GAAG,IAAI,CAACJ,CAAC,CAAC;MAClB,IAAGI,IAAI,CAACiC,SAAS,IAAIA,SAAS,IAAIjC,IAAI,CAACgC,YAAY,IAAIA,YAAY,EAAC;QACnE,OAAOhC,IAAI;MACZ;IACD;IACA,OAAO,IAAI;EACZ;AACD,CAAC;AACD;AACA;AACA;AACA,SAASoC,iBAAiBA,CAAC,YAAaC,QAAQ,EAAE;EACjD,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;EACnB,IAAID,QAAQ,EAAE;IACb,KAAK,IAAIE,OAAO,IAAIF,QAAQ,EAAE;MAC5B,IAAI,CAACC,SAAS,GAAGD,QAAQ,CAACE,OAAO,CAAC;IACpC;EACD;AACD;AAAC;AAEDH,iBAAiB,CAAC9F,SAAS,GAAG;EAC7BkG,UAAU,EAAE,SAAZA,UAAUA,CAAW,YAAaD,OAAO,EAAE,YAAaE,OAAO,EAAE;IAChE,IAAIC,QAAQ,GAAG,IAAI,CAACJ,SAAS,CAACC,OAAO,CAACI,WAAW,CAAC,CAAC,CAAC;IACpD,IAAID,QAAQ,KAAK,CAACD,OAAO,IAAIA,OAAO,IAAIC,QAAQ,CAAC,EAAE;MAClD,OAAO,IAAI;IACZ,CAAC,MAAM;MACN,OAAO,KAAK;IACb;EACD,CAAC;EACD;EACAE,cAAc,EAAC,SAAfA,cAAcA,CAAUZ,YAAY,EAAGa,aAAa,EAAEC,OAAO,EAAC;IAAC;IAC9D,IAAI5B,GAAG,GAAG,IAAI6B,QAAQ,CAAC,CAAC;IACxB7B,GAAG,CAAC8B,cAAc,GAAG,IAAI;IACzB9B,GAAG,CAAC+B,UAAU,GAAG,IAAI7D,QAAQ,CAAC,CAAC;IAC/B8B,GAAG,CAAC4B,OAAO,GAAGA,OAAO;IACrB,IAAGA,OAAO,EAAC;MACV5B,GAAG,CAACgC,WAAW,CAACJ,OAAO,CAAC;IACzB;IACA,IAAGD,aAAa,EAAC;MAChB,IAAIM,IAAI,GAAGjC,GAAG,CAACkC,eAAe,CAACpB,YAAY,EAACa,aAAa,CAAC;MAC1D3B,GAAG,CAACgC,WAAW,CAACC,IAAI,CAAC;IACtB;IACA,OAAOjC,GAAG;EACX,CAAC;EACD;EACAmC,kBAAkB,EAAC,SAAnBA,kBAAkBA,CAAUR,aAAa,EAAES,QAAQ,EAAEC,QAAQ,EAAC;IAAC;IAC9D,IAAIvD,IAAI,GAAG,IAAIwD,YAAY,CAAC,CAAC;IAC7BxD,IAAI,CAACyD,IAAI,GAAGZ,aAAa;IACzB7C,IAAI,CAAC2B,QAAQ,GAAGkB,aAAa;IAC7B7C,IAAI,CAACsD,QAAQ,GAAGA,QAAQ;IACxBtD,IAAI,CAACuD,QAAQ,GAAGA,QAAQ;IACxB;IACA;;IAEA;IACA;IACA;IACA,OAAOvD,IAAI;EACZ;AACD,CAAC;;AAGD;AACA;AACA;;AAEA,SAAS0D,IAAIA,CAAA,EAAG,CAChB;AAAC;AAEDA,IAAI,CAACpH,SAAS,GAAG;EAChBqH,UAAU,EAAG,IAAI;EACjBC,SAAS,EAAG,IAAI;EAChBC,eAAe,EAAG,IAAI;EACtBC,WAAW,EAAG,IAAI;EAClBC,UAAU,EAAG,IAAI;EACjBC,UAAU,EAAG,IAAI;EACjBf,UAAU,EAAG,IAAI;EACjBzC,aAAa,EAAG,IAAI;EACpByD,SAAS,EAAG,IAAI;EAChBjC,YAAY,EAAG,IAAI;EACnBkC,MAAM,EAAG,IAAI;EACbjC,SAAS,EAAG,IAAI;EAChB;EACAkC,YAAY,EAAC,SAAbA,YAAYA,CAAUC,QAAQ,EAAEC,QAAQ,EAAC;IAAC;IACzC,OAAOC,aAAa,CAAC,IAAI,EAACF,QAAQ,EAACC,QAAQ,CAAC;EAC7C,CAAC;EACDE,YAAY,EAAC,SAAbA,YAAYA,CAAUH,QAAQ,EAAEI,QAAQ,EAAC;IAAC;IACzC,IAAI,CAACL,YAAY,CAACC,QAAQ,EAACI,QAAQ,CAAC;IACpC,IAAGA,QAAQ,EAAC;MACX,IAAI,CAACC,WAAW,CAACD,QAAQ,CAAC;IAC3B;EACD,CAAC;EACDC,WAAW,EAAC,SAAZA,WAAWA,CAAUD,QAAQ,EAAC;IAC7B,OAAOE,YAAY,CAAC,IAAI,EAACF,QAAQ,CAAC;EACnC,CAAC;EACDtB,WAAW,EAAC,SAAZA,WAAWA,CAAUkB,QAAQ,EAAC;IAC7B,OAAO,IAAI,CAACD,YAAY,CAACC,QAAQ,EAAC,IAAI,CAAC;EACxC,CAAC;EACDO,aAAa,EAAC,SAAdA,aAAaA,CAAA,EAAW;IACvB,OAAO,IAAI,CAAChB,UAAU,IAAI,IAAI;EAC/B,CAAC;EACDiB,SAAS,EAAC,SAAVA,SAASA,CAAUC,IAAI,EAAC;IACvB,OAAOD,UAAS,CAAC,IAAI,CAACpE,aAAa,IAAE,IAAI,EAAC,IAAI,EAACqE,IAAI,CAAC;EACrD,CAAC;EACD;EACAC,SAAS,EAAC,SAAVA,SAASA,CAAA,EAAW;IACnB,IAAIC,KAAK,GAAG,IAAI,CAACpB,UAAU;IAC3B,OAAMoB,KAAK,EAAC;MACX,IAAIC,IAAI,GAAGD,KAAK,CAACjB,WAAW;MAC5B,IAAGkB,IAAI,IAAIA,IAAI,CAACC,QAAQ,IAAI9H,SAAS,IAAI4H,KAAK,CAACE,QAAQ,IAAI9H,SAAS,EAAC;QACpE,IAAI,CAACsH,WAAW,CAACO,IAAI,CAAC;QACtBD,KAAK,CAACG,UAAU,CAACF,IAAI,CAACG,IAAI,CAAC;MAC5B,CAAC,MAAI;QACJJ,KAAK,CAACD,SAAS,CAAC,CAAC;QACjBC,KAAK,GAAGC,IAAI;MACb;IACD;EACD,CAAC;EACC;EACFI,WAAW,EAAC,SAAZA,WAAWA,CAAU7C,OAAO,EAAEE,OAAO,EAAC;IACrC,OAAO,IAAI,CAACjC,aAAa,CAACwC,cAAc,CAACR,UAAU,CAACD,OAAO,EAACE,OAAO,CAAC;EACrE,CAAC;EACE;EACA4C,aAAa,EAAC,SAAdA,aAAaA,CAAA,EAAW;IACvB,OAAO,IAAI,CAACtB,UAAU,CAAC1E,MAAM,GAAC,CAAC;EAChC,CAAC;EACDiG,YAAY,EAAC,SAAbA,YAAYA,CAAUtD,YAAY,EAAC;IAClC,IAAIlB,EAAE,GAAG,IAAI;IACb,OAAMA,EAAE,EAAC;MACR,IAAIyE,GAAG,GAAGzE,EAAE,CAAC0E,MAAM;MACnB;MACA,IAAGD,GAAG,EAAC;QACN,KAAI,IAAIE,CAAC,IAAIF,GAAG,EAAC;UAChB,IAAGA,GAAG,CAACE,CAAC,CAAC,IAAIzD,YAAY,EAAC;YACzB,OAAOyD,CAAC;UACT;QACD;MACD;MACA3E,EAAE,GAAGA,EAAE,CAACmE,QAAQ,IAAI/H,cAAc,GAAC4D,EAAE,CAACN,aAAa,GAAGM,EAAE,CAACkD,UAAU;IACpE;IACA,OAAO,IAAI;EACZ,CAAC;EACD;EACA0B,kBAAkB,EAAC,SAAnBA,kBAAkBA,CAAUxB,MAAM,EAAC;IAClC,IAAIpD,EAAE,GAAG,IAAI;IACb,OAAMA,EAAE,EAAC;MACR,IAAIyE,GAAG,GAAGzE,EAAE,CAAC0E,MAAM;MACnB;MACA,IAAGD,GAAG,EAAC;QACN,IAAGrB,MAAM,IAAIqB,GAAG,EAAC;UAChB,OAAOA,GAAG,CAACrB,MAAM,CAAC;QACnB;MACD;MACApD,EAAE,GAAGA,EAAE,CAACmE,QAAQ,IAAI/H,cAAc,GAAC4D,EAAE,CAACN,aAAa,GAAGM,EAAE,CAACkD,UAAU;IACpE;IACA,OAAO,IAAI;EACZ,CAAC;EACD;EACA2B,kBAAkB,EAAC,SAAnBA,kBAAkBA,CAAU3D,YAAY,EAAC;IACxC,IAAIkC,MAAM,GAAG,IAAI,CAACoB,YAAY,CAACtD,YAAY,CAAC;IAC5C,OAAOkC,MAAM,IAAI,IAAI;EACtB;AACJ,CAAC;AAGD,SAAS0B,WAAWA,CAACC,CAAC,EAAC;EACtB,OAAOA,CAAC,IAAI,GAAG,IAAI,MAAM,IACjBA,CAAC,IAAI,GAAG,IAAI,MAAM,IAClBA,CAAC,IAAI,GAAG,IAAI,OAAO,IACnBA,CAAC,IAAI,GAAG,IAAI,QAAQ,IACpB,IAAI,GAACA,CAAC,CAACC,UAAU,CAAC,CAAC,GAAC,GAAG;AAChC;AAGAhK,IAAI,CAACkB,QAAQ,EAAC0G,IAAI,CAAC;AACnB5H,IAAI,CAACkB,QAAQ,EAAC0G,IAAI,CAACpH,SAAS,CAAC;;AAE7B;AACA;AACA;AACA;AACA,SAASyJ,UAAUA,CAAC/F,IAAI,EAACgG,QAAQ,EAAC;EACjC,IAAGA,QAAQ,CAAChG,IAAI,CAAC,EAAC;IACjB,OAAO,IAAI;EACZ;EACA,IAAGA,IAAI,GAAGA,IAAI,CAAC2D,UAAU,EAAC;IACzB,GAAE;MACD,IAAGoC,UAAU,CAAC/F,IAAI,EAACgG,QAAQ,CAAC,EAAC;QAAC,OAAO,IAAI;MAAA;IACpC,CAAC,QAAMhG,IAAI,GAACA,IAAI,CAAC8D,WAAW;EAChC;AACJ;AAIA,SAASf,QAAQA,CAAA,EAAE,CACnB;AACA,SAAS3B,eAAeA,CAACF,GAAG,EAACJ,EAAE,EAACC,OAAO,EAAC;EACvCG,GAAG,IAAIA,GAAG,CAACX,IAAI,EAAE;EACjB,IAAI0F,EAAE,GAAGlF,OAAO,CAACiB,YAAY;EAC7B,IAAGiE,EAAE,IAAI,+BAA+B,EAAC;IACxC;IACAnF,EAAE,CAAC0E,MAAM,CAACzE,OAAO,CAACmD,MAAM,GAACnD,OAAO,CAACkB,SAAS,GAAC,EAAE,CAAC,GAAGlB,OAAO,CAACmF,KAAK;EAC/D;AACD;AACA,SAAS/E,kBAAkBA,CAACD,GAAG,EAACJ,EAAE,EAACC,OAAO,EAACoF,MAAM,EAAC;EACjDjF,GAAG,IAAIA,GAAG,CAACX,IAAI,EAAE;EACjB,IAAI0F,EAAE,GAAGlF,OAAO,CAACiB,YAAY;EAC7B,IAAGiE,EAAE,IAAI,+BAA+B,EAAC;IACxC;IACA,OAAOnF,EAAE,CAAC0E,MAAM,CAACzE,OAAO,CAACmD,MAAM,GAACnD,OAAO,CAACkB,SAAS,GAAC,EAAE,CAAC;EACtD;AACD;AACA,SAASmE,cAAcA,CAAClF,GAAG,EAACJ,EAAE,EAACsD,QAAQ,EAAC;EACvC,IAAGlD,GAAG,IAAIA,GAAG,CAACX,IAAI,EAAC;IAClBW,GAAG,CAACX,IAAI,EAAE;IACV;IACA,IAAI8F,EAAE,GAAGvF,EAAE,CAACmC,UAAU;IACtB,IAAGmB,QAAQ,EAAC;MACXiC,EAAE,CAACA,EAAE,CAAChH,MAAM,EAAE,CAAC,GAAG+E,QAAQ;IAC3B,CAAC,MAAI;MACJ;MACA,IAAIW,KAAK,GAAGjE,EAAE,CAAC6C,UAAU;MACzB,IAAI/D,CAAC,GAAG,CAAC;MACT,OAAMmF,KAAK,EAAC;QACXsB,EAAE,CAACzG,CAAC,EAAE,CAAC,GAAGmF,KAAK;QACfA,KAAK,GAAEA,KAAK,CAACjB,WAAW;MACzB;MACAuC,EAAE,CAAChH,MAAM,GAAGO,CAAC;IACd;EACD;AACD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8E,YAAYA,CAACV,UAAU,EAACe,KAAK,EAAC;EACtC,IAAIuB,QAAQ,GAAGvB,KAAK,CAAClB,eAAe;EACpC,IAAImB,IAAI,GAAGD,KAAK,CAACjB,WAAW;EAC5B,IAAGwC,QAAQ,EAAC;IACXA,QAAQ,CAACxC,WAAW,GAAGkB,IAAI;EAC5B,CAAC,MAAI;IACJhB,UAAU,CAACL,UAAU,GAAGqB,IAAI;EAC7B;EACA,IAAGA,IAAI,EAAC;IACPA,IAAI,CAACnB,eAAe,GAAGyC,QAAQ;EAChC,CAAC,MAAI;IACJtC,UAAU,CAACJ,SAAS,GAAG0C,QAAQ;EAChC;EACAF,cAAc,CAACpC,UAAU,CAACxD,aAAa,EAACwD,UAAU,CAAC;EACnD,OAAOe,KAAK;AACb;AACA;AACA;AACA;AACA,SAAST,aAAaA,CAACN,UAAU,EAACI,QAAQ,EAACmC,SAAS,EAAC;EACpD,IAAIC,EAAE,GAAGpC,QAAQ,CAACJ,UAAU;EAC5B,IAAGwC,EAAE,EAAC;IACLA,EAAE,CAAC/B,WAAW,CAACL,QAAQ,CAAC,CAAC;EAC1B;EACA,IAAGA,QAAQ,CAACa,QAAQ,KAAKtH,sBAAsB,EAAC;IAC/C,IAAI8I,QAAQ,GAAGrC,QAAQ,CAACT,UAAU;IAClC,IAAI8C,QAAQ,IAAI,IAAI,EAAE;MACrB,OAAOrC,QAAQ;IAChB;IACA,IAAIsC,OAAO,GAAGtC,QAAQ,CAACR,SAAS;EACjC,CAAC,MAAI;IACJ6C,QAAQ,GAAGC,OAAO,GAAGtC,QAAQ;EAC9B;EACA,IAAIuC,GAAG,GAAGJ,SAAS,GAAGA,SAAS,CAAC1C,eAAe,GAAGG,UAAU,CAACJ,SAAS;EAEtE6C,QAAQ,CAAC5C,eAAe,GAAG8C,GAAG;EAC9BD,OAAO,CAAC5C,WAAW,GAAGyC,SAAS;EAG/B,IAAGI,GAAG,EAAC;IACNA,GAAG,CAAC7C,WAAW,GAAG2C,QAAQ;EAC3B,CAAC,MAAI;IACJzC,UAAU,CAACL,UAAU,GAAG8C,QAAQ;EACjC;EACA,IAAGF,SAAS,IAAI,IAAI,EAAC;IACpBvC,UAAU,CAACJ,SAAS,GAAG8C,OAAO;EAC/B,CAAC,MAAI;IACJH,SAAS,CAAC1C,eAAe,GAAG6C,OAAO;EACpC;EACA,GAAE;IACDD,QAAQ,CAACzC,UAAU,GAAGA,UAAU;EACjC,CAAC,QAAMyC,QAAQ,KAAKC,OAAO,KAAKD,QAAQ,GAAEA,QAAQ,CAAC3C,WAAW,CAAC;EAC/DsC,cAAc,CAACpC,UAAU,CAACxD,aAAa,IAAEwD,UAAU,EAACA,UAAU,CAAC;EAC/D;EACA,IAAII,QAAQ,CAACa,QAAQ,IAAItH,sBAAsB,EAAE;IAChDyG,QAAQ,CAACT,UAAU,GAAGS,QAAQ,CAACR,SAAS,GAAG,IAAI;EAChD;EACA,OAAOQ,QAAQ;AAChB;AACA,SAASwC,kBAAkBA,CAAC5C,UAAU,EAACI,QAAQ,EAAC;EAC/C,IAAIoC,EAAE,GAAGpC,QAAQ,CAACJ,UAAU;EAC5B,IAAGwC,EAAE,EAAC;IACL,IAAIG,GAAG,GAAG3C,UAAU,CAACJ,SAAS;IAC9B4C,EAAE,CAAC/B,WAAW,CAACL,QAAQ,CAAC,CAAC;IACzB,IAAIuC,GAAG,GAAG3C,UAAU,CAACJ,SAAS;EAC/B;EACA,IAAI+C,GAAG,GAAG3C,UAAU,CAACJ,SAAS;EAC9BQ,QAAQ,CAACJ,UAAU,GAAGA,UAAU;EAChCI,QAAQ,CAACP,eAAe,GAAG8C,GAAG;EAC9BvC,QAAQ,CAACN,WAAW,GAAG,IAAI;EAC3B,IAAG6C,GAAG,EAAC;IACNA,GAAG,CAAC7C,WAAW,GAAGM,QAAQ;EAC3B,CAAC,MAAI;IACJJ,UAAU,CAACL,UAAU,GAAGS,QAAQ;EACjC;EACAJ,UAAU,CAACJ,SAAS,GAAGQ,QAAQ;EAC/BgC,cAAc,CAACpC,UAAU,CAACxD,aAAa,EAACwD,UAAU,EAACI,QAAQ,CAAC;EAC5D,OAAOA,QAAQ;EACf;AACD;AACArB,QAAQ,CAACzG,SAAS,GAAG;EACpB;EACAqF,QAAQ,EAAI,WAAW;EACvBsD,QAAQ,EAAIxH,aAAa;EACzBqF,OAAO,EAAI,IAAI;EACf+D,eAAe,EAAI,IAAI;EACvBtG,IAAI,EAAG,CAAC;EAER4D,YAAY,EAAI,SAAhBA,YAAYA,CAAaC,QAAQ,EAAEC,QAAQ,EAAC;IAAC;IAC5C,IAAGD,QAAQ,CAACa,QAAQ,IAAItH,sBAAsB,EAAC;MAC9C,IAAIoH,KAAK,GAAGX,QAAQ,CAACT,UAAU;MAC/B,OAAMoB,KAAK,EAAC;QACX,IAAIC,IAAI,GAAGD,KAAK,CAACjB,WAAW;QAC5B,IAAI,CAACK,YAAY,CAACY,KAAK,EAACV,QAAQ,CAAC;QACjCU,KAAK,GAAGC,IAAI;MACb;MACA,OAAOZ,QAAQ;IAChB;IACA,IAAG,IAAI,CAACyC,eAAe,IAAI,IAAI,IAAIzC,QAAQ,CAACa,QAAQ,IAAIhI,YAAY,EAAC;MACpE,IAAI,CAAC4J,eAAe,GAAGzC,QAAQ;IAChC;IAEA,OAAOE,aAAa,CAAC,IAAI,EAACF,QAAQ,EAACC,QAAQ,CAAC,EAAED,QAAQ,CAAC5D,aAAa,GAAG,IAAI,EAAE4D,QAAQ;EACtF,CAAC;EACDK,WAAW,EAAI,SAAfA,WAAWA,CAAaD,QAAQ,EAAC;IAChC,IAAG,IAAI,CAACqC,eAAe,IAAIrC,QAAQ,EAAC;MACnC,IAAI,CAACqC,eAAe,GAAG,IAAI;IAC5B;IACA,OAAOnC,YAAY,CAAC,IAAI,EAACF,QAAQ,CAAC;EACnC,CAAC;EACD;EACAsC,UAAU,EAAG,SAAbA,UAAUA,CAAYC,YAAY,EAAClC,IAAI,EAAC;IACvC,OAAOiC,WAAU,CAAC,IAAI,EAACC,YAAY,EAAClC,IAAI,CAAC;EAC1C,CAAC;EACD;EACAmC,cAAc,EAAG,SAAjBA,cAAcA,CAAYC,EAAE,EAAC;IAC5B,IAAIC,GAAG,GAAG,IAAI;IACdnB,UAAU,CAAC,IAAI,CAACc,eAAe,EAAC,UAAS7G,IAAI,EAAC;MAC7C,IAAGA,IAAI,CAACiF,QAAQ,IAAIhI,YAAY,EAAC;QAChC,IAAG+C,IAAI,CAACmH,YAAY,CAAC,IAAI,CAAC,IAAIF,EAAE,EAAC;UAChCC,GAAG,GAAGlH,IAAI;UACV,OAAO,IAAI;QACZ;MACD;IACD,CAAC,CAAC;IACF,OAAOkH,GAAG;EACX,CAAC;EAED;EACAE,aAAa,EAAG,SAAhBA,aAAaA,CAAY5F,OAAO,EAAC;IAChC,IAAIxB,IAAI,GAAG,IAAIqH,OAAO,CAAC,CAAC;IACxBrH,IAAI,CAACQ,aAAa,GAAG,IAAI;IACzBR,IAAI,CAAC2B,QAAQ,GAAGH,OAAO;IACvBxB,IAAI,CAACwB,OAAO,GAAGA,OAAO;IACtBxB,IAAI,CAACiD,UAAU,GAAG,IAAI7D,QAAQ,CAAC,CAAC;IAChC,IAAIkI,KAAK,GAAGtH,IAAI,CAAC+D,UAAU,GAAG,IAAIpD,YAAY,CAAC,CAAC;IAChD2G,KAAK,CAACzF,aAAa,GAAG7B,IAAI;IAC1B,OAAOA,IAAI;EACZ,CAAC;EACDuH,sBAAsB,EAAG,SAAzBA,sBAAsBA,CAAA,EAAa;IAClC,IAAIvH,IAAI,GAAG,IAAIwH,gBAAgB,CAAC,CAAC;IACjCxH,IAAI,CAACQ,aAAa,GAAG,IAAI;IACzBR,IAAI,CAACiD,UAAU,GAAG,IAAI7D,QAAQ,CAAC,CAAC;IAChC,OAAOY,IAAI;EACZ,CAAC;EACDyH,cAAc,EAAG,SAAjBA,cAAcA,CAAYtC,IAAI,EAAC;IAC9B,IAAInF,IAAI,GAAG,IAAI0H,IAAI,CAAC,CAAC;IACrB1H,IAAI,CAACQ,aAAa,GAAG,IAAI;IACzBR,IAAI,CAACkF,UAAU,CAACC,IAAI,CAAC;IACrB,OAAOnF,IAAI;EACZ,CAAC;EACD2H,aAAa,EAAG,SAAhBA,aAAaA,CAAYxC,IAAI,EAAC;IAC7B,IAAInF,IAAI,GAAG,IAAI4H,OAAO,CAAC,CAAC;IACxB5H,IAAI,CAACQ,aAAa,GAAG,IAAI;IACzBR,IAAI,CAACkF,UAAU,CAACC,IAAI,CAAC;IACrB,OAAOnF,IAAI;EACZ,CAAC;EACD6H,kBAAkB,EAAG,SAArBA,kBAAkBA,CAAY1C,IAAI,EAAC;IAClC,IAAInF,IAAI,GAAG,IAAI8H,YAAY,CAAC,CAAC;IAC7B9H,IAAI,CAACQ,aAAa,GAAG,IAAI;IACzBR,IAAI,CAACkF,UAAU,CAACC,IAAI,CAAC;IACrB,OAAOnF,IAAI;EACZ,CAAC;EACD+H,2BAA2B,EAAG,SAA9BA,2BAA2BA,CAAYC,MAAM,EAAC7C,IAAI,EAAC;IAClD,IAAInF,IAAI,GAAG,IAAIiI,qBAAqB,CAAC,CAAC;IACtCjI,IAAI,CAACQ,aAAa,GAAG,IAAI;IACzBR,IAAI,CAACwB,OAAO,GAAGxB,IAAI,CAACgI,MAAM,GAAGA,MAAM;IACnChI,IAAI,CAACiE,SAAS,GAAEjE,IAAI,CAACmF,IAAI,GAAGA,IAAI;IAChC,OAAOnF,IAAI;EACZ,CAAC;EACDkI,eAAe,EAAG,SAAlBA,eAAeA,CAAYzE,IAAI,EAAC;IAC/B,IAAIzD,IAAI,GAAG,IAAImI,IAAI,CAAC,CAAC;IACrBnI,IAAI,CAACQ,aAAa,GAAG,IAAI;IACzBR,IAAI,CAACyD,IAAI,GAAGA,IAAI;IAChBzD,IAAI,CAAC2B,QAAQ,GAAG8B,IAAI;IACpBzD,IAAI,CAACiC,SAAS,GAAGwB,IAAI;IACrBzD,IAAI,CAACoI,SAAS,GAAG,IAAI;IACrB,OAAOpI,IAAI;EACZ,CAAC;EACDqI,qBAAqB,EAAG,SAAxBA,qBAAqBA,CAAY5E,IAAI,EAAC;IACrC,IAAIzD,IAAI,GAAG,IAAIsI,eAAe,CAAC,CAAC;IAChCtI,IAAI,CAACQ,aAAa,GAAG,IAAI;IACzBR,IAAI,CAAC2B,QAAQ,GAAG8B,IAAI;IACpB,OAAOzD,IAAI;EACZ,CAAC;EACD;EACAoD,eAAe,EAAG,SAAlBA,eAAeA,CAAYpB,YAAY,EAACa,aAAa,EAAC;IACrD,IAAI7C,IAAI,GAAG,IAAIqH,OAAO,CAAC,CAAC;IACxB,IAAIkB,EAAE,GAAG1F,aAAa,CAAC2F,KAAK,CAAC,GAAG,CAAC;IACjC,IAAIlB,KAAK,GAAGtH,IAAI,CAAC+D,UAAU,GAAG,IAAIpD,YAAY,CAAC,CAAC;IAChDX,IAAI,CAACiD,UAAU,GAAG,IAAI7D,QAAQ,CAAC,CAAC;IAChCY,IAAI,CAACQ,aAAa,GAAG,IAAI;IACzBR,IAAI,CAAC2B,QAAQ,GAAGkB,aAAa;IAC7B7C,IAAI,CAACwB,OAAO,GAAGqB,aAAa;IAC5B7C,IAAI,CAACgC,YAAY,GAAGA,YAAY;IAChC,IAAGuG,EAAE,CAAClJ,MAAM,IAAI,CAAC,EAAC;MACjBW,IAAI,CAACkE,MAAM,GAAGqE,EAAE,CAAC,CAAC,CAAC;MACnBvI,IAAI,CAACiC,SAAS,GAAGsG,EAAE,CAAC,CAAC,CAAC;IACvB,CAAC,MAAI;MACJ;MACAvI,IAAI,CAACiC,SAAS,GAAGY,aAAa;IAC/B;IACAyE,KAAK,CAACzF,aAAa,GAAG7B,IAAI;IAC1B,OAAOA,IAAI;EACZ,CAAC;EACD;EACAyI,iBAAiB,EAAG,SAApBA,iBAAiBA,CAAYzG,YAAY,EAACa,aAAa,EAAC;IACvD,IAAI7C,IAAI,GAAG,IAAImI,IAAI,CAAC,CAAC;IACrB,IAAII,EAAE,GAAG1F,aAAa,CAAC2F,KAAK,CAAC,GAAG,CAAC;IACjCxI,IAAI,CAACQ,aAAa,GAAG,IAAI;IACzBR,IAAI,CAAC2B,QAAQ,GAAGkB,aAAa;IAC7B7C,IAAI,CAACyD,IAAI,GAAGZ,aAAa;IACzB7C,IAAI,CAACgC,YAAY,GAAGA,YAAY;IAChChC,IAAI,CAACoI,SAAS,GAAG,IAAI;IACrB,IAAGG,EAAE,CAAClJ,MAAM,IAAI,CAAC,EAAC;MACjBW,IAAI,CAACkE,MAAM,GAAGqE,EAAE,CAAC,CAAC,CAAC;MACnBvI,IAAI,CAACiC,SAAS,GAAGsG,EAAE,CAAC,CAAC,CAAC;IACvB,CAAC,MAAI;MACJ;MACAvI,IAAI,CAACiC,SAAS,GAAGY,aAAa;IAC/B;IACA,OAAO7C,IAAI;EACZ;AACD,CAAC;AACD9D,QAAQ,CAAC6G,QAAQ,EAACW,IAAI,CAAC;AAGvB,SAAS2D,OAAOA,CAAA,EAAG;EAClB,IAAI,CAAC7B,MAAM,GAAG,CAAC,CAAC;AACjB;AAAC;AACD6B,OAAO,CAAC/K,SAAS,GAAG;EACnB2I,QAAQ,EAAGhI,YAAY;EACvByL,YAAY,EAAG,SAAfA,YAAYA,CAAYjF,IAAI,EAAC;IAC5B,OAAO,IAAI,CAACkF,gBAAgB,CAAClF,IAAI,CAAC,IAAE,IAAI;EACzC,CAAC;EACD0D,YAAY,EAAG,SAAfA,YAAYA,CAAY1D,IAAI,EAAC;IAC5B,IAAInC,IAAI,GAAG,IAAI,CAACqH,gBAAgB,CAAClF,IAAI,CAAC;IACtC,OAAOnC,IAAI,IAAIA,IAAI,CAAC4E,KAAK,IAAI,EAAE;EAChC,CAAC;EACDyC,gBAAgB,EAAG,SAAnBA,gBAAgBA,CAAYlF,IAAI,EAAC;IAChC,OAAO,IAAI,CAACM,UAAU,CAACtC,YAAY,CAACgC,IAAI,CAAC;EAC1C,CAAC;EACDmF,YAAY,EAAG,SAAfA,YAAYA,CAAYnF,IAAI,EAAEyC,KAAK,EAAC;IACnC,IAAI5E,IAAI,GAAG,IAAI,CAACd,aAAa,CAAC0H,eAAe,CAACzE,IAAI,CAAC;IACnDnC,IAAI,CAAC4E,KAAK,GAAG5E,IAAI,CAAC2C,SAAS,GAAG,EAAE,GAAGiC,KAAK;IACxC,IAAI,CAAC2C,gBAAgB,CAACvH,IAAI,CAAC;EAC5B,CAAC;EACDwH,eAAe,EAAG,SAAlBA,eAAeA,CAAYrF,IAAI,EAAC;IAC/B,IAAInC,IAAI,GAAG,IAAI,CAACqH,gBAAgB,CAAClF,IAAI,CAAC;IACtCnC,IAAI,IAAI,IAAI,CAACyH,mBAAmB,CAACzH,IAAI,CAAC;EACvC,CAAC;EAED;EACA4B,WAAW,EAAC,SAAZA,WAAWA,CAAUkB,QAAQ,EAAC;IAC7B,IAAGA,QAAQ,CAACa,QAAQ,KAAKtH,sBAAsB,EAAC;MAC/C,OAAO,IAAI,CAACwG,YAAY,CAACC,QAAQ,EAAC,IAAI,CAAC;IACxC,CAAC,MAAI;MACJ,OAAOwC,kBAAkB,CAAC,IAAI,EAACxC,QAAQ,CAAC;IACzC;EACD,CAAC;EACDyE,gBAAgB,EAAG,SAAnBA,gBAAgBA,CAAY9H,OAAO,EAAC;IACnC,OAAO,IAAI,CAACgD,UAAU,CAACnC,YAAY,CAACb,OAAO,CAAC;EAC7C,CAAC;EACDiI,kBAAkB,EAAG,SAArBA,kBAAkBA,CAAYjI,OAAO,EAAC;IACrC,OAAO,IAAI,CAACgD,UAAU,CAACjC,cAAc,CAACf,OAAO,CAAC;EAC/C,CAAC;EACDgI,mBAAmB,EAAG,SAAtBA,mBAAmBA,CAAY/H,OAAO,EAAC;IACtC;IACA,OAAO,IAAI,CAAC+C,UAAU,CAAC7B,eAAe,CAAClB,OAAO,CAACW,QAAQ,CAAC;EACzD,CAAC;EACD;EACAsH,iBAAiB,EAAG,SAApBA,iBAAiBA,CAAYjH,YAAY,EAAEC,SAAS,EAAC;IACpD,IAAIiH,GAAG,GAAG,IAAI,CAACC,kBAAkB,CAACnH,YAAY,EAAEC,SAAS,CAAC;IAC1DiH,GAAG,IAAI,IAAI,CAACH,mBAAmB,CAACG,GAAG,CAAC;EACrC,CAAC;EAEDE,cAAc,EAAG,SAAjBA,cAAcA,CAAYpH,YAAY,EAAEC,SAAS,EAAC;IACjD,OAAO,IAAI,CAACkH,kBAAkB,CAACnH,YAAY,EAAEC,SAAS,CAAC,IAAE,IAAI;EAC9D,CAAC;EACDoH,cAAc,EAAG,SAAjBA,cAAcA,CAAYrH,YAAY,EAAEC,SAAS,EAAC;IACjD,IAAIX,IAAI,GAAG,IAAI,CAAC6H,kBAAkB,CAACnH,YAAY,EAAEC,SAAS,CAAC;IAC3D,OAAOX,IAAI,IAAIA,IAAI,CAAC4E,KAAK,IAAI,EAAE;EAChC,CAAC;EACDoD,cAAc,EAAG,SAAjBA,cAAcA,CAAYtH,YAAY,EAAEa,aAAa,EAAEqD,KAAK,EAAC;IAC5D,IAAI5E,IAAI,GAAG,IAAI,CAACd,aAAa,CAACiI,iBAAiB,CAACzG,YAAY,EAAEa,aAAa,CAAC;IAC5EvB,IAAI,CAAC4E,KAAK,GAAG5E,IAAI,CAAC2C,SAAS,GAAG,EAAE,GAAGiC,KAAK;IACxC,IAAI,CAAC2C,gBAAgB,CAACvH,IAAI,CAAC;EAC5B,CAAC;EACD6H,kBAAkB,EAAG,SAArBA,kBAAkBA,CAAYnH,YAAY,EAAEC,SAAS,EAAC;IACrD,OAAO,IAAI,CAAC8B,UAAU,CAAChC,cAAc,CAACC,YAAY,EAAEC,SAAS,CAAC;EAC/D,CAAC;EAEDsH,oBAAoB,EAAG,SAAvBA,oBAAoBA,CAAY/H,OAAO,EAAC;IACvC,OAAO,IAAIzB,YAAY,CAAC,IAAI,EAAC,UAASyJ,IAAI,EAAC;MAC1C,IAAI/I,EAAE,GAAG,EAAE;MACXsF,UAAU,CAACyD,IAAI,EAAC,UAASxJ,IAAI,EAAC;QAC7B,IAAGA,IAAI,KAAKwJ,IAAI,IAAIxJ,IAAI,CAACiF,QAAQ,IAAIhI,YAAY,KAAKuE,OAAO,KAAK,GAAG,IAAIxB,IAAI,CAACwB,OAAO,IAAIA,OAAO,CAAC,EAAC;UACjGf,EAAE,CAACgJ,IAAI,CAACzJ,IAAI,CAAC;QACd;MACD,CAAC,CAAC;MACF,OAAOS,EAAE;IACV,CAAC,CAAC;EACH,CAAC;EACDiJ,sBAAsB,EAAG,SAAzBA,sBAAsBA,CAAY1H,YAAY,EAAEC,SAAS,EAAC;IACzD,OAAO,IAAIlC,YAAY,CAAC,IAAI,EAAC,UAASyJ,IAAI,EAAC;MAC1C,IAAI/I,EAAE,GAAG,EAAE;MACXsF,UAAU,CAACyD,IAAI,EAAC,UAASxJ,IAAI,EAAC;QAC7B,IAAGA,IAAI,KAAKwJ,IAAI,IAAIxJ,IAAI,CAACiF,QAAQ,KAAKhI,YAAY,KAAK+E,YAAY,KAAK,GAAG,IAAIhC,IAAI,CAACgC,YAAY,KAAKA,YAAY,CAAC,KAAKC,SAAS,KAAK,GAAG,IAAIjC,IAAI,CAACiC,SAAS,IAAIA,SAAS,CAAC,EAAC;UACxKxB,EAAE,CAACgJ,IAAI,CAACzJ,IAAI,CAAC;QACd;MACD,CAAC,CAAC;MACF,OAAOS,EAAE;IAEV,CAAC,CAAC;EACH;AACD,CAAC;AACDsC,QAAQ,CAACzG,SAAS,CAACiN,oBAAoB,GAAGlC,OAAO,CAAC/K,SAAS,CAACiN,oBAAoB;AAChFxG,QAAQ,CAACzG,SAAS,CAACoN,sBAAsB,GAAGrC,OAAO,CAAC/K,SAAS,CAACoN,sBAAsB;AAGpFxN,QAAQ,CAACmL,OAAO,EAAC3D,IAAI,CAAC;AACtB,SAASyE,IAAIA,CAAA,EAAG,CAChB;AAAC;AACDA,IAAI,CAAC7L,SAAS,CAAC2I,QAAQ,GAAG/H,cAAc;AACxChB,QAAQ,CAACiM,IAAI,EAACzE,IAAI,CAAC;AAGnB,SAASiG,aAAaA,CAAA,EAAG,CACzB;AAAC;AACDA,aAAa,CAACrN,SAAS,GAAG;EACzB6I,IAAI,EAAG,EAAE;EACTyE,aAAa,EAAG,SAAhBA,aAAaA,CAAYC,MAAM,EAAEC,KAAK,EAAE;IACvC,OAAO,IAAI,CAAC3E,IAAI,CAAC4E,SAAS,CAACF,MAAM,EAAEA,MAAM,GAACC,KAAK,CAAC;EACjD,CAAC;EACD5E,UAAU,EAAE,SAAZA,UAAUA,CAAW8E,IAAI,EAAE;IAC1BA,IAAI,GAAG,IAAI,CAAC7E,IAAI,GAAC6E,IAAI;IACrB,IAAI,CAAC/F,SAAS,GAAG,IAAI,CAACkB,IAAI,GAAG6E,IAAI;IACjC,IAAI,CAAC3K,MAAM,GAAG2K,IAAI,CAAC3K,MAAM;EAC1B,CAAC;EACD4K,UAAU,EAAE,SAAZA,UAAUA,CAAWJ,MAAM,EAACG,IAAI,EAAE;IACjC,IAAI,CAACE,WAAW,CAACL,MAAM,EAAC,CAAC,EAACG,IAAI,CAAC;EAEhC,CAAC;EACD9G,WAAW,EAAC,SAAZA,WAAWA,CAAUkB,QAAQ,EAAC;IAC7B,MAAM,IAAInF,KAAK,CAACnB,gBAAgB,CAACG,qBAAqB,CAAC,CAAC;EACzD,CAAC;EACDkM,UAAU,EAAE,SAAZA,UAAUA,CAAWN,MAAM,EAAEC,KAAK,EAAE;IACnC,IAAI,CAACI,WAAW,CAACL,MAAM,EAACC,KAAK,EAAC,EAAE,CAAC;EAClC,CAAC;EACDI,WAAW,EAAE,SAAbA,WAAWA,CAAWL,MAAM,EAAEC,KAAK,EAAEE,IAAI,EAAE;IAC1C,IAAII,KAAK,GAAG,IAAI,CAACjF,IAAI,CAAC4E,SAAS,CAAC,CAAC,EAACF,MAAM,CAAC;IACzC,IAAIQ,GAAG,GAAG,IAAI,CAAClF,IAAI,CAAC4E,SAAS,CAACF,MAAM,GAACC,KAAK,CAAC;IAC3CE,IAAI,GAAGI,KAAK,GAAGJ,IAAI,GAAGK,GAAG;IACzB,IAAI,CAACpG,SAAS,GAAG,IAAI,CAACkB,IAAI,GAAG6E,IAAI;IACjC,IAAI,CAAC3K,MAAM,GAAG2K,IAAI,CAAC3K,MAAM;EAC1B;AACD,CAAC;AACDnD,QAAQ,CAACyN,aAAa,EAACjG,IAAI,CAAC;AAC5B,SAASgE,IAAIA,CAAA,EAAG,CAChB;AAAC;AACDA,IAAI,CAACpL,SAAS,GAAG;EAChBqF,QAAQ,EAAG,OAAO;EAClBsD,QAAQ,EAAG9H,SAAS;EACpBmN,SAAS,EAAG,SAAZA,SAASA,CAAYT,MAAM,EAAE;IAC5B,IAAIG,IAAI,GAAG,IAAI,CAAC7E,IAAI;IACpB,IAAIoF,OAAO,GAAGP,IAAI,CAACD,SAAS,CAACF,MAAM,CAAC;IACpCG,IAAI,GAAGA,IAAI,CAACD,SAAS,CAAC,CAAC,EAAEF,MAAM,CAAC;IAChC,IAAI,CAAC1E,IAAI,GAAG,IAAI,CAAClB,SAAS,GAAG+F,IAAI;IACjC,IAAI,CAAC3K,MAAM,GAAG2K,IAAI,CAAC3K,MAAM;IACzB,IAAImL,OAAO,GAAG,IAAI,CAAChK,aAAa,CAACiH,cAAc,CAAC8C,OAAO,CAAC;IACxD,IAAG,IAAI,CAACvG,UAAU,EAAC;MAClB,IAAI,CAACA,UAAU,CAACG,YAAY,CAACqG,OAAO,EAAE,IAAI,CAAC1G,WAAW,CAAC;IACxD;IACA,OAAO0G,OAAO;EACf;AACD,CAAC;AACDtO,QAAQ,CAACwL,IAAI,EAACiC,aAAa,CAAC;AAC5B,SAAS/B,OAAOA,CAAA,EAAG,CACnB;AAAC;AACDA,OAAO,CAACtL,SAAS,GAAG;EACnBqF,QAAQ,EAAG,UAAU;EACrBsD,QAAQ,EAAGzH;AACZ,CAAC;AACDtB,QAAQ,CAAC0L,OAAO,EAAC+B,aAAa,CAAC;AAE/B,SAAS7B,YAAYA,CAAA,EAAG,CACxB;AAAC;AACDA,YAAY,CAACxL,SAAS,GAAG;EACxBqF,QAAQ,EAAG,gBAAgB;EAC3BsD,QAAQ,EAAG7H;AACZ,CAAC;AACDlB,QAAQ,CAAC4L,YAAY,EAAC6B,aAAa,CAAC;AAGpC,SAASnG,YAAYA,CAAA,EAAG,CACxB;AAAC;AACDA,YAAY,CAAClH,SAAS,CAAC2I,QAAQ,GAAGvH,kBAAkB;AACpDxB,QAAQ,CAACsH,YAAY,EAACE,IAAI,CAAC;AAE3B,SAAS+G,QAAQA,CAAA,EAAG,CACpB;AAAC;AACDA,QAAQ,CAACnO,SAAS,CAAC2I,QAAQ,GAAGrH,aAAa;AAC3C1B,QAAQ,CAACuO,QAAQ,EAAC/G,IAAI,CAAC;AAEvB,SAASgH,MAAMA,CAAA,EAAG,CAClB;AAAC;AACDA,MAAM,CAACpO,SAAS,CAAC2I,QAAQ,GAAG3H,WAAW;AACvCpB,QAAQ,CAACwO,MAAM,EAAChH,IAAI,CAAC;AAErB,SAAS4E,eAAeA,CAAA,EAAG,CAC3B;AAAC;AACDA,eAAe,CAAChM,SAAS,CAAC2I,QAAQ,GAAG5H,qBAAqB;AAC1DnB,QAAQ,CAACoM,eAAe,EAAC5E,IAAI,CAAC;AAE9B,SAAS8D,gBAAgBA,CAAA,EAAG,CAC5B;AAAC;AACDA,gBAAgB,CAAClL,SAAS,CAACqF,QAAQ,GAAG,oBAAoB;AAC1D6F,gBAAgB,CAAClL,SAAS,CAAC2I,QAAQ,GAAGtH,sBAAsB;AAC5DzB,QAAQ,CAACsL,gBAAgB,EAAC9D,IAAI,CAAC;AAG/B,SAASuE,qBAAqBA,CAAA,EAAG,CACjC;AACAA,qBAAqB,CAAC3L,SAAS,CAAC2I,QAAQ,GAAG1H,2BAA2B;AACtErB,QAAQ,CAAC+L,qBAAqB,EAACvE,IAAI,CAAC;AACpC,SAASiH,aAAaA,CAAA,EAAE,CAAC;AACzBA,aAAa,CAACrO,SAAS,CAACuD,iBAAiB,GAAG,UAASG,IAAI,EAAC4K,MAAM,EAAClL,UAAU,EAAC;EAC3E,OAAOmL,qBAAqB,CAAC3L,IAAI,CAACc,IAAI,EAAC4K,MAAM,EAAClL,UAAU,CAAC;AAC1D,CAAC;AACDgE,IAAI,CAACpH,SAAS,CAACkD,QAAQ,GAAGqL,qBAAqB;AAC/C,SAASA,qBAAqBA,CAACD,MAAM,EAAClL,UAAU,EAAC;EAChD,IAAIC,GAAG,GAAG,EAAE;EACZ,IAAImL,OAAO,GAAG,IAAI,CAAC7F,QAAQ,IAAI,CAAC,GAAC,IAAI,CAAC4B,eAAe,GAAC,IAAI;EAC1D,IAAI3C,MAAM,GAAG4G,OAAO,CAAC5G,MAAM;EAC3B,IAAI6G,GAAG,GAAGD,OAAO,CAAC9I,YAAY;EAE9B,IAAG+I,GAAG,IAAI7G,MAAM,IAAI,IAAI,EAAC;IACxB;IACA,IAAIA,MAAM,GAAG4G,OAAO,CAACxF,YAAY,CAACyF,GAAG,CAAC;IACtC,IAAG7G,MAAM,IAAI,IAAI,EAAC;MACjB;MACA,IAAI8G,iBAAiB,GAAC,CACtB;QAACC,SAAS,EAACF,GAAG;QAAC7G,MAAM,EAAC;MAAI;MAC1B;MAAA,CACC;IACF;EACD;EACArE,iBAAiB,CAAC,IAAI,EAACF,GAAG,EAACiL,MAAM,EAAClL,UAAU,EAACsL,iBAAiB,CAAC;EAC/D;EACA,OAAOrL,GAAG,CAACG,IAAI,CAAC,EAAE,CAAC;AACpB;AACA,SAASoL,mBAAmBA,CAAClL,IAAI,EAACP,MAAM,EAAEuL,iBAAiB,EAAE;EAC5D,IAAI9G,MAAM,GAAGlE,IAAI,CAACkE,MAAM,IAAE,EAAE;EAC5B,IAAI6G,GAAG,GAAG/K,IAAI,CAACgC,YAAY;EAC3B,IAAI,CAACkC,MAAM,IAAI,CAAC6G,GAAG,EAAC;IACnB,OAAO,KAAK;EACb;EACA,IAAI7G,MAAM,KAAK,KAAK,IAAI6G,GAAG,KAAK,sCAAsC,IAClEA,GAAG,IAAI,+BAA+B,EAAC;IAC1C,OAAO,KAAK;EACb;EAEA,IAAInL,CAAC,GAAGoL,iBAAiB,CAAC3L,MAAM;EAChC;EACA,OAAOO,CAAC,EAAE,EAAE;IACX,IAAIqG,EAAE,GAAG+E,iBAAiB,CAACpL,CAAC,CAAC;IAC7B;IACA;IACA,IAAIqG,EAAE,CAAC/B,MAAM,IAAIA,MAAM,EAAC;MACvB,OAAO+B,EAAE,CAACgF,SAAS,IAAIF,GAAG;IAC3B;EACD;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAAO,IAAI;AACZ;AACA,SAASlL,iBAAiBA,CAACG,IAAI,EAACL,GAAG,EAACF,MAAM,EAACC,UAAU,EAACsL,iBAAiB,EAAC;EACvE,IAAGtL,UAAU,EAAC;IACbM,IAAI,GAAGN,UAAU,CAACM,IAAI,CAAC;IACvB,IAAGA,IAAI,EAAC;MACP,IAAG,OAAOA,IAAI,IAAI,QAAQ,EAAC;QAC1BL,GAAG,CAAC8J,IAAI,CAACzJ,IAAI,CAAC;QACd;MACD;IACD,CAAC,MAAI;MACJ;IACD;IACA;EACD;EACA,QAAOA,IAAI,CAACiF,QAAQ;IACpB,KAAKhI,YAAY;MAChB,IAAI,CAAC+N,iBAAiB,EAAEA,iBAAiB,GAAG,EAAE;MAC9C,IAAIG,sBAAsB,GAAGH,iBAAiB,CAAC3L,MAAM;MACrD,IAAIiI,KAAK,GAAGtH,IAAI,CAAC+D,UAAU;MAC3B,IAAIqH,GAAG,GAAG9D,KAAK,CAACjI,MAAM;MACtB,IAAI0F,KAAK,GAAG/E,IAAI,CAAC2D,UAAU;MAC3B,IAAIhC,QAAQ,GAAG3B,IAAI,CAACwB,OAAO;MAE3B/B,MAAM,GAAK1C,MAAM,KAAKiD,IAAI,CAACgC,YAAY,IAAIvC,MAAM;MACjDE,GAAG,CAAC8J,IAAI,CAAC,GAAG,EAAC9H,QAAQ,CAAC;MAItB,KAAI,IAAI/B,CAAC,GAAC,CAAC,EAACA,CAAC,GAACwL,GAAG,EAACxL,CAAC,EAAE,EAAC;QACrB;QACA,IAAI0B,IAAI,GAAGgG,KAAK,CAAChI,IAAI,CAACM,CAAC,CAAC;QACxB,IAAI0B,IAAI,CAAC4C,MAAM,IAAI,OAAO,EAAE;UAC3B8G,iBAAiB,CAACvB,IAAI,CAAC;YAAEvF,MAAM,EAAE5C,IAAI,CAACW,SAAS;YAAEgJ,SAAS,EAAE3J,IAAI,CAAC4E;UAAM,CAAC,CAAC;QAC1E,CAAC,MAAK,IAAG5E,IAAI,CAACK,QAAQ,IAAI,OAAO,EAAC;UACjCqJ,iBAAiB,CAACvB,IAAI,CAAC;YAAEvF,MAAM,EAAE,EAAE;YAAE+G,SAAS,EAAE3J,IAAI,CAAC4E;UAAM,CAAC,CAAC;QAC9D;MACD;MACA,KAAI,IAAItG,CAAC,GAAC,CAAC,EAACA,CAAC,GAACwL,GAAG,EAACxL,CAAC,EAAE,EAAC;QACrB,IAAI0B,IAAI,GAAGgG,KAAK,CAAChI,IAAI,CAACM,CAAC,CAAC;QACxB,IAAIsL,mBAAmB,CAAC5J,IAAI,EAAC7B,MAAM,EAAEuL,iBAAiB,CAAC,EAAE;UACxD,IAAI9G,MAAM,GAAG5C,IAAI,CAAC4C,MAAM,IAAE,EAAE;UAC5B,IAAI6G,GAAG,GAAGzJ,IAAI,CAACU,YAAY;UAC3B,IAAIiE,EAAE,GAAG/B,MAAM,GAAG,SAAS,GAAGA,MAAM,GAAG,QAAQ;UAC/CvE,GAAG,CAAC8J,IAAI,CAACxD,EAAE,EAAE,IAAI,EAAG8E,GAAG,EAAG,GAAG,CAAC;UAC9BC,iBAAiB,CAACvB,IAAI,CAAC;YAAEvF,MAAM,EAAEA,MAAM;YAAE+G,SAAS,EAACF;UAAI,CAAC,CAAC;QAC1D;QACAlL,iBAAiB,CAACyB,IAAI,EAAC3B,GAAG,EAACF,MAAM,EAACC,UAAU,EAACsL,iBAAiB,CAAC;MAChE;MACA;MACA,IAAIE,mBAAmB,CAAClL,IAAI,EAACP,MAAM,EAAEuL,iBAAiB,CAAC,EAAE;QACxD,IAAI9G,MAAM,GAAGlE,IAAI,CAACkE,MAAM,IAAE,EAAE;QAC5B,IAAI6G,GAAG,GAAG/K,IAAI,CAACgC,YAAY;QAC3B,IAAIiE,EAAE,GAAG/B,MAAM,GAAG,SAAS,GAAGA,MAAM,GAAG,QAAQ;QAC/CvE,GAAG,CAAC8J,IAAI,CAACxD,EAAE,EAAE,IAAI,EAAG8E,GAAG,EAAG,GAAG,CAAC;QAC9BC,iBAAiB,CAACvB,IAAI,CAAC;UAAEvF,MAAM,EAAEA,MAAM;UAAE+G,SAAS,EAACF;QAAI,CAAC,CAAC;MAC1D;MAEA,IAAGhG,KAAK,IAAItF,MAAM,IAAI,CAAC,kCAAkC,CAAC4L,IAAI,CAAC1J,QAAQ,CAAC,EAAC;QACxEhC,GAAG,CAAC8J,IAAI,CAAC,GAAG,CAAC;QACb;QACA,IAAGhK,MAAM,IAAI,WAAW,CAAC4L,IAAI,CAAC1J,QAAQ,CAAC,EAAC;UACvC,OAAMoD,KAAK,EAAC;YACX,IAAGA,KAAK,CAACI,IAAI,EAAC;cACbxF,GAAG,CAAC8J,IAAI,CAAC1E,KAAK,CAACI,IAAI,CAAC;YACrB,CAAC,MAAI;cACJtF,iBAAiB,CAACkF,KAAK,EAACpF,GAAG,EAACF,MAAM,EAACC,UAAU,EAACsL,iBAAiB,CAAC;YACjE;YACAjG,KAAK,GAAGA,KAAK,CAACjB,WAAW;UAC1B;QACD,CAAC,MACD;UACC,OAAMiB,KAAK,EAAC;YACXlF,iBAAiB,CAACkF,KAAK,EAACpF,GAAG,EAACF,MAAM,EAACC,UAAU,EAACsL,iBAAiB,CAAC;YAChEjG,KAAK,GAAGA,KAAK,CAACjB,WAAW;UAC1B;QACD;QACAnE,GAAG,CAAC8J,IAAI,CAAC,IAAI,EAAC9H,QAAQ,EAAC,GAAG,CAAC;MAC5B,CAAC,MAAI;QACJhC,GAAG,CAAC8J,IAAI,CAAC,IAAI,CAAC;MACf;MACA;MACA;MACA;IACD,KAAKhM,aAAa;IAClB,KAAKE,sBAAsB;MAC1B,IAAIoH,KAAK,GAAG/E,IAAI,CAAC2D,UAAU;MAC3B,OAAMoB,KAAK,EAAC;QACXlF,iBAAiB,CAACkF,KAAK,EAACpF,GAAG,EAACF,MAAM,EAACC,UAAU,EAACsL,iBAAiB,CAAC;QAChEjG,KAAK,GAAGA,KAAK,CAACjB,WAAW;MAC1B;MACA;IACD,KAAK5G,cAAc;MAClB,OAAOyC,GAAG,CAAC8J,IAAI,CAAC,GAAG,EAACzJ,IAAI,CAACyD,IAAI,EAAC,IAAI,EAACzD,IAAI,CAACkG,KAAK,CAACoF,OAAO,CAAC,QAAQ,EAAC1F,WAAW,CAAC,EAAC,GAAG,CAAC;IACjF,KAAKzI,SAAS;MACb,OAAOwC,GAAG,CAAC8J,IAAI,CAACzJ,IAAI,CAACmF,IAAI,CAACmG,OAAO,CAAC,OAAO,EAAC1F,WAAW,CAAC,CAAC;IACxD,KAAKxI,kBAAkB;MACtB,OAAOuC,GAAG,CAAC8J,IAAI,CAAE,WAAW,EAACzJ,IAAI,CAACmF,IAAI,EAAC,KAAK,CAAC;IAC9C,KAAK3H,YAAY;MAChB,OAAOmC,GAAG,CAAC8J,IAAI,CAAE,MAAM,EAACzJ,IAAI,CAACmF,IAAI,EAAC,KAAK,CAAC;IACzC,KAAKzH,kBAAkB;MACtB,IAAI6N,KAAK,GAAGvL,IAAI,CAACsD,QAAQ;MACzB,IAAIkI,KAAK,GAAGxL,IAAI,CAACuD,QAAQ;MACzB5D,GAAG,CAAC8J,IAAI,CAAC,YAAY,EAACzJ,IAAI,CAACyD,IAAI,CAAC;MAChC,IAAG8H,KAAK,EAAC;QACR5L,GAAG,CAAC8J,IAAI,CAAC,WAAW,EAAC8B,KAAK,CAAC;QAC3B,IAAIC,KAAK,IAAIA,KAAK,IAAE,GAAG,EAAE;UACxB7L,GAAG,CAAC8J,IAAI,CAAE,KAAK,EAAC+B,KAAK,CAAC;QACvB;QACA7L,GAAG,CAAC8J,IAAI,CAAC,IAAI,CAAC;MACf,CAAC,MAAK,IAAG+B,KAAK,IAAIA,KAAK,IAAE,GAAG,EAAC;QAC5B7L,GAAG,CAAC8J,IAAI,CAAC,WAAW,EAAC+B,KAAK,EAAC,IAAI,CAAC;MACjC,CAAC,MAAI;QACJ,IAAIC,GAAG,GAAGzL,IAAI,CAAC0L,cAAc;QAC7B,IAAGD,GAAG,EAAC;UACN9L,GAAG,CAAC8J,IAAI,CAAC,IAAI,EAACgC,GAAG,EAAC,GAAG,CAAC;QACvB;QACA9L,GAAG,CAAC8J,IAAI,CAAC,GAAG,CAAC;MACd;MACA;IACD,KAAKlM,2BAA2B;MAC/B,OAAOoC,GAAG,CAAC8J,IAAI,CAAE,IAAI,EAACzJ,IAAI,CAACgI,MAAM,EAAC,GAAG,EAAChI,IAAI,CAACmF,IAAI,EAAC,IAAI,CAAC;IACtD,KAAK9H,qBAAqB;MACzB,OAAOsC,GAAG,CAAC8J,IAAI,CAAE,GAAG,EAACzJ,IAAI,CAAC2B,QAAQ,EAAC,GAAG,CAAC;IACxC;IACA;IACA;MACChC,GAAG,CAAC8J,IAAI,CAAC,IAAI,EAACzJ,IAAI,CAAC2B,QAAQ,CAAC;EAC7B;AACD;AACA,SAASmF,WAAUA,CAAC5F,GAAG,EAAClB,IAAI,EAAC6E,IAAI,EAAC;EACjC,IAAI8G,KAAK;EACT,QAAQ3L,IAAI,CAACiF,QAAQ;IACrB,KAAKhI,YAAY;MAChB0O,KAAK,GAAG3L,IAAI,CAAC4E,SAAS,CAAC,KAAK,CAAC;MAC7B+G,KAAK,CAACnL,aAAa,GAAGU,GAAG;IACzB;IACA;IACA;IACC;IACD;IACD,KAAKvD,sBAAsB;MAC1B;IACD,KAAKT,cAAc;MAClB2H,IAAI,GAAG,IAAI;MACX;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACA,IAAG,CAAC8G,KAAK,EAAC;IACTA,KAAK,GAAG3L,IAAI,CAAC4E,SAAS,CAAC,KAAK,CAAC,CAAC;EAC/B;EACA+G,KAAK,CAACnL,aAAa,GAAGU,GAAG;EACzByK,KAAK,CAAC3H,UAAU,GAAG,IAAI;EACvB,IAAGa,IAAI,EAAC;IACP,IAAIE,KAAK,GAAG/E,IAAI,CAAC2D,UAAU;IAC3B,OAAMoB,KAAK,EAAC;MACX4G,KAAK,CAACzI,WAAW,CAAC4D,WAAU,CAAC5F,GAAG,EAAC6D,KAAK,EAACF,IAAI,CAAC,CAAC;MAC7CE,KAAK,GAAGA,KAAK,CAACjB,WAAW;IAC1B;EACD;EACA,OAAO6H,KAAK;AACb;AACA;AACA;AACA;AACA,SAAS/G,UAASA,CAAC1D,GAAG,EAAClB,IAAI,EAAC6E,IAAI,EAAC;EAChC,IAAI8G,KAAK,GAAG,IAAI3L,IAAI,CAACpD,WAAW,CAAC,CAAC;EAClC,KAAI,IAAI6I,CAAC,IAAIzF,IAAI,EAAC;IACjB,IAAI4L,CAAC,GAAG5L,IAAI,CAACyF,CAAC,CAAC;IACf,IAAG,OAAOmG,CAAC,IAAI,QAAQ,EAAE;MACxB,IAAGA,CAAC,IAAID,KAAK,CAAClG,CAAC,CAAC,EAAC;QAChBkG,KAAK,CAAClG,CAAC,CAAC,GAAGmG,CAAC;MACb;IACD;EACD;EACA,IAAG5L,IAAI,CAACiD,UAAU,EAAC;IAClB0I,KAAK,CAAC1I,UAAU,GAAG,IAAI7D,QAAQ,CAAC,CAAC;EAClC;EACAuM,KAAK,CAACnL,aAAa,GAAGU,GAAG;EACzB,QAAQyK,KAAK,CAAC1G,QAAQ;IACtB,KAAKhI,YAAY;MAChB,IAAIqK,KAAK,GAAGtH,IAAI,CAAC+D,UAAU;MAC3B,IAAI8H,MAAM,GAAGF,KAAK,CAAC5H,UAAU,GAAG,IAAIpD,YAAY,CAAC,CAAC;MAClD,IAAIyK,GAAG,GAAG9D,KAAK,CAACjI,MAAM;MACtBwM,MAAM,CAAChK,aAAa,GAAG8J,KAAK;MAC5B,KAAI,IAAI/L,CAAC,GAAC,CAAC,EAACA,CAAC,GAACwL,GAAG,EAACxL,CAAC,EAAE,EAAC;QACrB+L,KAAK,CAAC9C,gBAAgB,CAACjE,UAAS,CAAC1D,GAAG,EAACoG,KAAK,CAAChI,IAAI,CAACM,CAAC,CAAC,EAAC,IAAI,CAAC,CAAC;MAC1D;MACA;MAAM;IACP,KAAK1C,cAAc;MAClB2H,IAAI,GAAG,IAAI;EACZ;EACA,IAAGA,IAAI,EAAC;IACP,IAAIE,KAAK,GAAG/E,IAAI,CAAC2D,UAAU;IAC3B,OAAMoB,KAAK,EAAC;MACX4G,KAAK,CAACzI,WAAW,CAAC0B,UAAS,CAAC1D,GAAG,EAAC6D,KAAK,EAACF,IAAI,CAAC,CAAC;MAC5CE,KAAK,GAAGA,KAAK,CAACjB,WAAW;IAC1B;EACD;EACA,OAAO6H,KAAK;AACb;AAEA,SAASjL,OAAOA,CAACoL,MAAM,EAACpK,GAAG,EAACwE,KAAK,EAAC;EACjC4F,MAAM,CAACpK,GAAG,CAAC,GAAGwE,KAAK;AACpB;AACA;AACA,IAAG;EACF,IAAG3J,MAAM,CAACwP,cAAc,EAAC;IAAA,IA+BfC,eAAc,GAAvB,SAASA,cAAcA,CAAChM,IAAI,EAAC;MAC5B,QAAOA,IAAI,CAACiF,QAAQ;QACpB,KAAKhI,YAAY;QACjB,KAAKU,sBAAsB;UAC1B,IAAIgC,GAAG,GAAG,EAAE;UACZK,IAAI,GAAGA,IAAI,CAAC2D,UAAU;UACtB,OAAM3D,IAAI,EAAC;YACV,IAAGA,IAAI,CAACiF,QAAQ,KAAG,CAAC,IAAIjF,IAAI,CAACiF,QAAQ,KAAI,CAAC,EAAC;cAC1CtF,GAAG,CAAC8J,IAAI,CAACuC,eAAc,CAAChM,IAAI,CAAC,CAAC;YAC/B;YACAA,IAAI,GAAGA,IAAI,CAAC8D,WAAW;UACxB;UACA,OAAOnE,GAAG,CAACG,IAAI,CAAC,EAAE,CAAC;QACpB;UACC,OAAOE,IAAI,CAACiE,SAAS;MACtB;IACD,CAAC;IA9CD1H,MAAM,CAACwP,cAAc,CAAChM,YAAY,CAACzD,SAAS,EAAC,QAAQ,EAAC;MACrD2P,GAAG,EAAC,SAAJA,GAAGA,CAAA,EAAW;QACb7L,eAAe,CAAC,IAAI,CAAC;QACrB,OAAO,IAAI,CAAC8L,QAAQ;MACrB;IACD,CAAC,CAAC;IACF3P,MAAM,CAACwP,cAAc,CAACrI,IAAI,CAACpH,SAAS,EAAC,aAAa,EAAC;MAClD2P,GAAG,EAAC,SAAJA,GAAGA,CAAA,EAAW;QACb,OAAOD,eAAc,CAAC,IAAI,CAAC;MAC5B,CAAC;MACDG,GAAG,EAAC,SAAJA,GAAGA,CAAUhH,IAAI,EAAC;QACjB,QAAO,IAAI,CAACF,QAAQ;UACpB,KAAKhI,YAAY;UACjB,KAAKU,sBAAsB;YAC1B,OAAM,IAAI,CAACgG,UAAU,EAAC;cACrB,IAAI,CAACc,WAAW,CAAC,IAAI,CAACd,UAAU,CAAC;YAClC;YACA,IAAGwB,IAAI,IAAIiH,MAAM,CAACjH,IAAI,CAAC,EAAC;cACvB,IAAI,CAACjC,WAAW,CAAC,IAAI,CAAC1C,aAAa,CAACiH,cAAc,CAACtC,IAAI,CAAC,CAAC;YAC1D;YACA;UACD;YACC;YACA,IAAI,CAACA,IAAI,GAAGA,IAAI;YAChB,IAAI,CAACe,KAAK,GAAGf,IAAI;YACjB,IAAI,CAAClB,SAAS,GAAGkB,IAAI;QACtB;MACD;IACD,CAAC,CAAC;IAmBFzE,OAAO,GAAG,SAAVA,OAAOA,CAAYoL,MAAM,EAACpK,GAAG,EAACwE,KAAK,EAAC;MACnC;MACA4F,MAAM,CAAC,IAAI,GAACpK,GAAG,CAAC,GAAGwE,KAAK;IACzB,CAAC;EACF;AACD,CAAC,QAAMmG,CAAC,EAAC,CAAC;AAAA;;AAGV;AACCC,OAAO,CAAClK,iBAAiB,GAAGA,iBAAiB;AAC7CkK,OAAO,CAAC3B,aAAa,GAAGA,aAAa;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}