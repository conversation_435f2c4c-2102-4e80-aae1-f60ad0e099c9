{"ast": null, "code": "import { renderSlot as _renderSlot, withModifiers as _withModifiers, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, Transition as _Transition, withCtx as _withCtx, createVNode as _createVNode } from \"vue\";\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return $setup.isShow ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"chat-popup-window\",\n    onClick: _withModifiers($setup.closeClick, [\"stop\"])\n  }, [_createVNode(_Transition, {\n    name: \"chat-popup-window-fade\"\n  }, {\n    default: _withCtx(function () {\n      return [$setup.bodyShow ? (_openBlock(), _createElementBlock(\"div\", {\n        key: 0,\n        class: \"chat-popup-window-body forbidSelect\",\n        onClick: _cache[0] || (_cache[0] = _withModifiers(function () {}, [\"stop\"]))\n      }, [_renderSlot(_ctx.$slots, \"default\")])) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 3 /* FORWARDED */\n  })])) : _createCommentVNode(\"v-if\", true);\n}", "map": {"version": 3, "names": ["$setup", "isShow", "_createElementBlock", "key", "class", "onClick", "_withModifiers", "closeClick", "_createVNode", "_Transition", "name", "default", "_withCtx", "bodyShow", "_cache", "_renderSlot", "_ctx", "$slots", "_createCommentVNode", "_"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalChat\\components\\chat-popup-window\\chat-popup-window.vue"], "sourcesContent": ["<template>\r\n  <div class=\"chat-popup-window\" @click.stop=\"closeClick\" v-if=\"isShow\">\r\n    <transition name=\"chat-popup-window-fade\">\r\n      <div class=\"chat-popup-window-body forbidSelect\" @click.stop v-if=\"bodyShow\">\r\n        <slot></slot>\r\n      </div>\r\n    </transition>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ChatPopupWindow' }\r\n</script>\r\n<script setup>\r\nimport { ref, watch, nextTick } from 'vue'\r\nconst props = defineProps({\r\n  modelValue: { type: Boolean, default: false },\r\n  beforeClose: Function\r\n})\r\nconst emit = defineEmits(['update:modelValue'])\r\n\r\nconst show = ref(props.modelValue)\r\nconst isShow = ref(false)\r\nconst bodyShow = ref(false)\r\nwatch(() => props.modelValue, () => {\r\n  show.value = props.modelValue\r\n  if (props.modelValue) {\r\n    isShow.value = true\r\n    nextTick(() => { bodyShow.value = true })\r\n  } else {\r\n    bodyShow.value = false\r\n    setTimeout(() => { isShow.value = false }, 99)\r\n  }\r\n})\r\nconst closeClick = () => {\r\n  if (typeof props.beforeClose === 'function') {\r\n    props.beforeClose(() => { emit('update:modelValue', false) })\r\n  } else {\r\n    emit('update:modelValue', false)\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.chat-popup-window {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  z-index: 998;\r\n  overflow: hidden;\r\n\r\n  .chat-popup-window-body {\r\n    min-width: 360px;\r\n    height: 68%;\r\n    background: #fff;\r\n    border-radius: 6px;\r\n    box-shadow: var(--zy-el-box-shadow-dark);\r\n  }\r\n\r\n  .chat-popup-window-fade-enter-active {\r\n    -webkit-animation: chat-popup-window-fade-in 0.3s;\r\n    animation: chat-popup-window-fade-in 0.3s;\r\n  }\r\n\r\n  .chat-popup-window-fade-leave-active {\r\n    -webkit-animation: chat-popup-window-fade-out 0.3s;\r\n    animation: chat-popup-window-fade-out 0.3s;\r\n  }\r\n\r\n  @keyframes chat-popup-window-fade-in {\r\n    0% {\r\n      transform: translate3d(0, -20px, 0);\r\n      opacity: 0;\r\n    }\r\n\r\n    100% {\r\n      transform: translate3d(0, 0, 0);\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  @keyframes chat-popup-window-fade-out {\r\n    0% {\r\n      transform: translate3d(0, 0, 0);\r\n      opacity: 1;\r\n    }\r\n\r\n    100% {\r\n      transform: translate3d(0, -20px, 0);\r\n      opacity: 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;SACgEA,MAAA,CAAAC,MAAM,I,cAApEC,mBAAA,CAMM;IAPRC,GAAA;IACOC,KAAK,EAAC,mBAAmB;IAAEC,OAAK,EADvCC,cAAA,CAC8CN,MAAA,CAAAO,UAAU;MACpDC,YAAA,CAIaC,WAAA;IAJDC,IAAI,EAAC;EAAwB;IAF7CC,OAAA,EAAAC,QAAA,CAEkG;MAAA,OAI/F,CAHsEZ,MAAA,CAAAa,QAAQ,I,cAA3EX,mBAAA,CAEM;QALZC,GAAA;QAGWC,KAAK,EAAC,qCAAqC;QAAEC,OAAK,EAAAS,MAAA,QAAAA,MAAA,MAH7DR,cAAA,CAGuD,cAAW;UAC1DS,WAAA,CAAaC,IAAA,CAAAC,MAAA,a,KAJrBC,mBAAA,e;;IAAAC,CAAA;UAAAD,mBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}