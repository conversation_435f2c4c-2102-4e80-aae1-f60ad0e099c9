{"ast": null, "code": "// GFM table, https://github.github.com/gfm/#tables-extension-\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\nfunction getLine(state, line) {\n  var pos = state.bMarks[line] + state.tShift[line],\n    max = state.eMarks[line];\n  return state.src.substr(pos, max - pos);\n}\nfunction escapedSplit(str) {\n  var result = [],\n    pos = 0,\n    max = str.length,\n    ch,\n    isEscaped = false,\n    lastPos = 0,\n    current = '';\n  ch = str.charCodeAt(pos);\n  while (pos < max) {\n    if (ch === 0x7c /* | */) {\n      if (!isEscaped) {\n        // pipe separating cells, '|'\n        result.push(current + str.substring(lastPos, pos));\n        current = '';\n        lastPos = pos + 1;\n      } else {\n        // escaped pipe, '\\|'\n        current += str.substring(lastPos, pos - 1);\n        lastPos = pos;\n      }\n    }\n    isEscaped = ch === 0x5c /* \\ */;\n    pos++;\n    ch = str.charCodeAt(pos);\n  }\n  result.push(current + str.substring(lastPos));\n  return result;\n}\nmodule.exports = function table(state, startLine, endLine, silent) {\n  var ch, lineText, pos, i, l, nextLine, columns, columnCount, token, aligns, t, tableLines, tbodyLines, oldParentType, terminate, terminatorRules, firstCh, secondCh;\n\n  // should have at least two lines\n  if (startLine + 2 > endLine) {\n    return false;\n  }\n  nextLine = startLine + 1;\n  if (state.sCount[nextLine] < state.blkIndent) {\n    return false;\n  }\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[nextLine] - state.blkIndent >= 4) {\n    return false;\n  }\n\n  // first character of the second line should be '|', '-', ':',\n  // and no other characters are allowed but spaces;\n  // basically, this is the equivalent of /^[-:|][-:|\\s]*$/ regexp\n\n  pos = state.bMarks[nextLine] + state.tShift[nextLine];\n  if (pos >= state.eMarks[nextLine]) {\n    return false;\n  }\n  firstCh = state.src.charCodeAt(pos++);\n  if (firstCh !== 0x7C /* | */ && firstCh !== 0x2D /* - */ && firstCh !== 0x3A /* : */) {\n    return false;\n  }\n  if (pos >= state.eMarks[nextLine]) {\n    return false;\n  }\n  secondCh = state.src.charCodeAt(pos++);\n  if (secondCh !== 0x7C /* | */ && secondCh !== 0x2D /* - */ && secondCh !== 0x3A /* : */ && !isSpace(secondCh)) {\n    return false;\n  }\n\n  // if first character is '-', then second character must not be a space\n  // (due to parsing ambiguity with list)\n  if (firstCh === 0x2D /* - */ && isSpace(secondCh)) {\n    return false;\n  }\n  while (pos < state.eMarks[nextLine]) {\n    ch = state.src.charCodeAt(pos);\n    if (ch !== 0x7C /* | */ && ch !== 0x2D /* - */ && ch !== 0x3A /* : */ && !isSpace(ch)) {\n      return false;\n    }\n    pos++;\n  }\n  lineText = getLine(state, startLine + 1);\n  columns = lineText.split('|');\n  aligns = [];\n  for (i = 0; i < columns.length; i++) {\n    t = columns[i].trim();\n    if (!t) {\n      // allow empty columns before and after table, but not in between columns;\n      // e.g. allow ` |---| `, disallow ` ---||--- `\n      if (i === 0 || i === columns.length - 1) {\n        continue;\n      } else {\n        return false;\n      }\n    }\n    if (!/^:?-+:?$/.test(t)) {\n      return false;\n    }\n    if (t.charCodeAt(t.length - 1) === 0x3A /* : */) {\n      aligns.push(t.charCodeAt(0) === 0x3A /* : */ ? 'center' : 'right');\n    } else if (t.charCodeAt(0) === 0x3A /* : */) {\n      aligns.push('left');\n    } else {\n      aligns.push('');\n    }\n  }\n  lineText = getLine(state, startLine).trim();\n  if (lineText.indexOf('|') === -1) {\n    return false;\n  }\n  if (state.sCount[startLine] - state.blkIndent >= 4) {\n    return false;\n  }\n  columns = escapedSplit(lineText);\n  if (columns.length && columns[0] === '') columns.shift();\n  if (columns.length && columns[columns.length - 1] === '') columns.pop();\n\n  // header row will define an amount of columns in the entire table,\n  // and align row should be exactly the same (the rest of the rows can differ)\n  columnCount = columns.length;\n  if (columnCount === 0 || columnCount !== aligns.length) {\n    return false;\n  }\n  if (silent) {\n    return true;\n  }\n  oldParentType = state.parentType;\n  state.parentType = 'table';\n\n  // use 'blockquote' lists for termination because it's\n  // the most similar to tables\n  terminatorRules = state.md.block.ruler.getRules('blockquote');\n  token = state.push('table_open', 'table', 1);\n  token.map = tableLines = [startLine, 0];\n  token = state.push('thead_open', 'thead', 1);\n  token.map = [startLine, startLine + 1];\n  token = state.push('tr_open', 'tr', 1);\n  token.map = [startLine, startLine + 1];\n  for (i = 0; i < columns.length; i++) {\n    token = state.push('th_open', 'th', 1);\n    if (aligns[i]) {\n      token.attrs = [['style', 'text-align:' + aligns[i]]];\n    }\n    token = state.push('inline', '', 0);\n    token.content = columns[i].trim();\n    token.children = [];\n    token = state.push('th_close', 'th', -1);\n  }\n  token = state.push('tr_close', 'tr', -1);\n  token = state.push('thead_close', 'thead', -1);\n  for (nextLine = startLine + 2; nextLine < endLine; nextLine++) {\n    if (state.sCount[nextLine] < state.blkIndent) {\n      break;\n    }\n    terminate = false;\n    for (i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true;\n        break;\n      }\n    }\n    if (terminate) {\n      break;\n    }\n    lineText = getLine(state, nextLine).trim();\n    if (!lineText) {\n      break;\n    }\n    if (state.sCount[nextLine] - state.blkIndent >= 4) {\n      break;\n    }\n    columns = escapedSplit(lineText);\n    if (columns.length && columns[0] === '') columns.shift();\n    if (columns.length && columns[columns.length - 1] === '') columns.pop();\n    if (nextLine === startLine + 2) {\n      token = state.push('tbody_open', 'tbody', 1);\n      token.map = tbodyLines = [startLine + 2, 0];\n    }\n    token = state.push('tr_open', 'tr', 1);\n    token.map = [nextLine, nextLine + 1];\n    for (i = 0; i < columnCount; i++) {\n      token = state.push('td_open', 'td', 1);\n      if (aligns[i]) {\n        token.attrs = [['style', 'text-align:' + aligns[i]]];\n      }\n      token = state.push('inline', '', 0);\n      token.content = columns[i] ? columns[i].trim() : '';\n      token.children = [];\n      token = state.push('td_close', 'td', -1);\n    }\n    token = state.push('tr_close', 'tr', -1);\n  }\n  if (tbodyLines) {\n    token = state.push('tbody_close', 'tbody', -1);\n    tbodyLines[1] = nextLine;\n  }\n  token = state.push('table_close', 'table', -1);\n  tableLines[1] = nextLine;\n  state.parentType = oldParentType;\n  state.line = nextLine;\n  return true;\n};", "map": {"version": 3, "names": ["isSpace", "require", "getLine", "state", "line", "pos", "bMarks", "tShift", "max", "eMarks", "src", "substr", "escapedSplit", "str", "result", "length", "ch", "isEscaped", "lastPos", "current", "charCodeAt", "push", "substring", "module", "exports", "table", "startLine", "endLine", "silent", "lineText", "i", "l", "nextLine", "columns", "columnCount", "token", "aligns", "t", "tableLines", "tbodyLines", "oldParentType", "terminate", "terminatorRules", "firstCh", "secondCh", "sCount", "blkIndent", "split", "trim", "test", "indexOf", "shift", "pop", "parentType", "md", "block", "ruler", "getRules", "map", "attrs", "content", "children"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_block/table.js"], "sourcesContent": ["// GFM table, https://github.github.com/gfm/#tables-extension-\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\n\n\nfunction getLine(state, line) {\n  var pos = state.bMarks[line] + state.tShift[line],\n      max = state.eMarks[line];\n\n  return state.src.substr(pos, max - pos);\n}\n\nfunction escapedSplit(str) {\n  var result = [],\n      pos = 0,\n      max = str.length,\n      ch,\n      isEscaped = false,\n      lastPos = 0,\n      current = '';\n\n  ch  = str.charCodeAt(pos);\n\n  while (pos < max) {\n    if (ch === 0x7c/* | */) {\n      if (!isEscaped) {\n        // pipe separating cells, '|'\n        result.push(current + str.substring(lastPos, pos));\n        current = '';\n        lastPos = pos + 1;\n      } else {\n        // escaped pipe, '\\|'\n        current += str.substring(lastPos, pos - 1);\n        lastPos = pos;\n      }\n    }\n\n    isEscaped = (ch === 0x5c/* \\ */);\n    pos++;\n\n    ch = str.charCodeAt(pos);\n  }\n\n  result.push(current + str.substring(lastPos));\n\n  return result;\n}\n\n\nmodule.exports = function table(state, startLine, endLine, silent) {\n  var ch, lineText, pos, i, l, nextLine, columns, columnCount, token,\n      aligns, t, tableLines, tbodyLines, oldParentType, terminate,\n      terminatorRules, firstCh, secondCh;\n\n  // should have at least two lines\n  if (startLine + 2 > endLine) { return false; }\n\n  nextLine = startLine + 1;\n\n  if (state.sCount[nextLine] < state.blkIndent) { return false; }\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[nextLine] - state.blkIndent >= 4) { return false; }\n\n  // first character of the second line should be '|', '-', ':',\n  // and no other characters are allowed but spaces;\n  // basically, this is the equivalent of /^[-:|][-:|\\s]*$/ regexp\n\n  pos = state.bMarks[nextLine] + state.tShift[nextLine];\n  if (pos >= state.eMarks[nextLine]) { return false; }\n\n  firstCh = state.src.charCodeAt(pos++);\n  if (firstCh !== 0x7C/* | */ && firstCh !== 0x2D/* - */ && firstCh !== 0x3A/* : */) { return false; }\n\n  if (pos >= state.eMarks[nextLine]) { return false; }\n\n  secondCh = state.src.charCodeAt(pos++);\n  if (secondCh !== 0x7C/* | */ && secondCh !== 0x2D/* - */ && secondCh !== 0x3A/* : */ && !isSpace(secondCh)) {\n    return false;\n  }\n\n  // if first character is '-', then second character must not be a space\n  // (due to parsing ambiguity with list)\n  if (firstCh === 0x2D/* - */ && isSpace(secondCh)) { return false; }\n\n  while (pos < state.eMarks[nextLine]) {\n    ch = state.src.charCodeAt(pos);\n\n    if (ch !== 0x7C/* | */ && ch !== 0x2D/* - */ && ch !== 0x3A/* : */ && !isSpace(ch)) { return false; }\n\n    pos++;\n  }\n\n  lineText = getLine(state, startLine + 1);\n\n  columns = lineText.split('|');\n  aligns = [];\n  for (i = 0; i < columns.length; i++) {\n    t = columns[i].trim();\n    if (!t) {\n      // allow empty columns before and after table, but not in between columns;\n      // e.g. allow ` |---| `, disallow ` ---||--- `\n      if (i === 0 || i === columns.length - 1) {\n        continue;\n      } else {\n        return false;\n      }\n    }\n\n    if (!/^:?-+:?$/.test(t)) { return false; }\n    if (t.charCodeAt(t.length - 1) === 0x3A/* : */) {\n      aligns.push(t.charCodeAt(0) === 0x3A/* : */ ? 'center' : 'right');\n    } else if (t.charCodeAt(0) === 0x3A/* : */) {\n      aligns.push('left');\n    } else {\n      aligns.push('');\n    }\n  }\n\n  lineText = getLine(state, startLine).trim();\n  if (lineText.indexOf('|') === -1) { return false; }\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false; }\n  columns = escapedSplit(lineText);\n  if (columns.length && columns[0] === '') columns.shift();\n  if (columns.length && columns[columns.length - 1] === '') columns.pop();\n\n  // header row will define an amount of columns in the entire table,\n  // and align row should be exactly the same (the rest of the rows can differ)\n  columnCount = columns.length;\n  if (columnCount === 0 || columnCount !== aligns.length) { return false; }\n\n  if (silent) { return true; }\n\n  oldParentType = state.parentType;\n  state.parentType = 'table';\n\n  // use 'blockquote' lists for termination because it's\n  // the most similar to tables\n  terminatorRules = state.md.block.ruler.getRules('blockquote');\n\n  token     = state.push('table_open', 'table', 1);\n  token.map = tableLines = [ startLine, 0 ];\n\n  token     = state.push('thead_open', 'thead', 1);\n  token.map = [ startLine, startLine + 1 ];\n\n  token     = state.push('tr_open', 'tr', 1);\n  token.map = [ startLine, startLine + 1 ];\n\n  for (i = 0; i < columns.length; i++) {\n    token          = state.push('th_open', 'th', 1);\n    if (aligns[i]) {\n      token.attrs  = [ [ 'style', 'text-align:' + aligns[i] ] ];\n    }\n\n    token          = state.push('inline', '', 0);\n    token.content  = columns[i].trim();\n    token.children = [];\n\n    token          = state.push('th_close', 'th', -1);\n  }\n\n  token     = state.push('tr_close', 'tr', -1);\n  token     = state.push('thead_close', 'thead', -1);\n\n  for (nextLine = startLine + 2; nextLine < endLine; nextLine++) {\n    if (state.sCount[nextLine] < state.blkIndent) { break; }\n\n    terminate = false;\n    for (i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true;\n        break;\n      }\n    }\n\n    if (terminate) { break; }\n    lineText = getLine(state, nextLine).trim();\n    if (!lineText) { break; }\n    if (state.sCount[nextLine] - state.blkIndent >= 4) { break; }\n    columns = escapedSplit(lineText);\n    if (columns.length && columns[0] === '') columns.shift();\n    if (columns.length && columns[columns.length - 1] === '') columns.pop();\n\n    if (nextLine === startLine + 2) {\n      token     = state.push('tbody_open', 'tbody', 1);\n      token.map = tbodyLines = [ startLine + 2, 0 ];\n    }\n\n    token     = state.push('tr_open', 'tr', 1);\n    token.map = [ nextLine, nextLine + 1 ];\n\n    for (i = 0; i < columnCount; i++) {\n      token          = state.push('td_open', 'td', 1);\n      if (aligns[i]) {\n        token.attrs  = [ [ 'style', 'text-align:' + aligns[i] ] ];\n      }\n\n      token          = state.push('inline', '', 0);\n      token.content  = columns[i] ? columns[i].trim() : '';\n      token.children = [];\n\n      token          = state.push('td_close', 'td', -1);\n    }\n    token = state.push('tr_close', 'tr', -1);\n  }\n\n  if (tbodyLines) {\n    token = state.push('tbody_close', 'tbody', -1);\n    tbodyLines[1] = nextLine;\n  }\n\n  token = state.push('table_close', 'table', -1);\n  tableLines[1] = nextLine;\n\n  state.parentType = oldParentType;\n  state.line = nextLine;\n  return true;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,iBAAiB,CAAC,CAACD,OAAO;AAGhD,SAASE,OAAOA,CAACC,KAAK,EAAEC,IAAI,EAAE;EAC5B,IAAIC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAACF,IAAI,CAAC,GAAGD,KAAK,CAACI,MAAM,CAACH,IAAI,CAAC;IAC7CI,GAAG,GAAGL,KAAK,CAACM,MAAM,CAACL,IAAI,CAAC;EAE5B,OAAOD,KAAK,CAACO,GAAG,CAACC,MAAM,CAACN,GAAG,EAAEG,GAAG,GAAGH,GAAG,CAAC;AACzC;AAEA,SAASO,YAAYA,CAACC,GAAG,EAAE;EACzB,IAAIC,MAAM,GAAG,EAAE;IACXT,GAAG,GAAG,CAAC;IACPG,GAAG,GAAGK,GAAG,CAACE,MAAM;IAChBC,EAAE;IACFC,SAAS,GAAG,KAAK;IACjBC,OAAO,GAAG,CAAC;IACXC,OAAO,GAAG,EAAE;EAEhBH,EAAE,GAAIH,GAAG,CAACO,UAAU,CAACf,GAAG,CAAC;EAEzB,OAAOA,GAAG,GAAGG,GAAG,EAAE;IAChB,IAAIQ,EAAE,KAAK,IAAI,UAAS;MACtB,IAAI,CAACC,SAAS,EAAE;QACd;QACAH,MAAM,CAACO,IAAI,CAACF,OAAO,GAAGN,GAAG,CAACS,SAAS,CAACJ,OAAO,EAAEb,GAAG,CAAC,CAAC;QAClDc,OAAO,GAAG,EAAE;QACZD,OAAO,GAAGb,GAAG,GAAG,CAAC;MACnB,CAAC,MAAM;QACL;QACAc,OAAO,IAAIN,GAAG,CAACS,SAAS,CAACJ,OAAO,EAAEb,GAAG,GAAG,CAAC,CAAC;QAC1Ca,OAAO,GAAGb,GAAG;MACf;IACF;IAEAY,SAAS,GAAID,EAAE,KAAK,IAAI,QAAQ;IAChCX,GAAG,EAAE;IAELW,EAAE,GAAGH,GAAG,CAACO,UAAU,CAACf,GAAG,CAAC;EAC1B;EAEAS,MAAM,CAACO,IAAI,CAACF,OAAO,GAAGN,GAAG,CAACS,SAAS,CAACJ,OAAO,CAAC,CAAC;EAE7C,OAAOJ,MAAM;AACf;AAGAS,MAAM,CAACC,OAAO,GAAG,SAASC,KAAKA,CAACtB,KAAK,EAAEuB,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAE;EACjE,IAAIZ,EAAE,EAAEa,QAAQ,EAAExB,GAAG,EAAEyB,CAAC,EAAEC,CAAC,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,KAAK,EAC9DC,MAAM,EAAEC,CAAC,EAAEC,UAAU,EAAEC,UAAU,EAAEC,aAAa,EAAEC,SAAS,EAC3DC,eAAe,EAAEC,OAAO,EAAEC,QAAQ;;EAEtC;EACA,IAAIlB,SAAS,GAAG,CAAC,GAAGC,OAAO,EAAE;IAAE,OAAO,KAAK;EAAE;EAE7CK,QAAQ,GAAGN,SAAS,GAAG,CAAC;EAExB,IAAIvB,KAAK,CAAC0C,MAAM,CAACb,QAAQ,CAAC,GAAG7B,KAAK,CAAC2C,SAAS,EAAE;IAAE,OAAO,KAAK;EAAE;;EAE9D;EACA,IAAI3C,KAAK,CAAC0C,MAAM,CAACb,QAAQ,CAAC,GAAG7B,KAAK,CAAC2C,SAAS,IAAI,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;;EAEnE;EACA;EACA;;EAEAzC,GAAG,GAAGF,KAAK,CAACG,MAAM,CAAC0B,QAAQ,CAAC,GAAG7B,KAAK,CAACI,MAAM,CAACyB,QAAQ,CAAC;EACrD,IAAI3B,GAAG,IAAIF,KAAK,CAACM,MAAM,CAACuB,QAAQ,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAEnDW,OAAO,GAAGxC,KAAK,CAACO,GAAG,CAACU,UAAU,CAACf,GAAG,EAAE,CAAC;EACrC,IAAIsC,OAAO,KAAK,IAAI,YAAWA,OAAO,KAAK,IAAI,YAAWA,OAAO,KAAK,IAAI,UAAS;IAAE,OAAO,KAAK;EAAE;EAEnG,IAAItC,GAAG,IAAIF,KAAK,CAACM,MAAM,CAACuB,QAAQ,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAEnDY,QAAQ,GAAGzC,KAAK,CAACO,GAAG,CAACU,UAAU,CAACf,GAAG,EAAE,CAAC;EACtC,IAAIuC,QAAQ,KAAK,IAAI,YAAWA,QAAQ,KAAK,IAAI,YAAWA,QAAQ,KAAK,IAAI,YAAW,CAAC5C,OAAO,CAAC4C,QAAQ,CAAC,EAAE;IAC1G,OAAO,KAAK;EACd;;EAEA;EACA;EACA,IAAID,OAAO,KAAK,IAAI,YAAW3C,OAAO,CAAC4C,QAAQ,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAElE,OAAOvC,GAAG,GAAGF,KAAK,CAACM,MAAM,CAACuB,QAAQ,CAAC,EAAE;IACnChB,EAAE,GAAGb,KAAK,CAACO,GAAG,CAACU,UAAU,CAACf,GAAG,CAAC;IAE9B,IAAIW,EAAE,KAAK,IAAI,YAAWA,EAAE,KAAK,IAAI,YAAWA,EAAE,KAAK,IAAI,YAAW,CAAChB,OAAO,CAACgB,EAAE,CAAC,EAAE;MAAE,OAAO,KAAK;IAAE;IAEpGX,GAAG,EAAE;EACP;EAEAwB,QAAQ,GAAG3B,OAAO,CAACC,KAAK,EAAEuB,SAAS,GAAG,CAAC,CAAC;EAExCO,OAAO,GAAGJ,QAAQ,CAACkB,KAAK,CAAC,GAAG,CAAC;EAC7BX,MAAM,GAAG,EAAE;EACX,KAAKN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,OAAO,CAAClB,MAAM,EAAEe,CAAC,EAAE,EAAE;IACnCO,CAAC,GAAGJ,OAAO,CAACH,CAAC,CAAC,CAACkB,IAAI,CAAC,CAAC;IACrB,IAAI,CAACX,CAAC,EAAE;MACN;MACA;MACA,IAAIP,CAAC,KAAK,CAAC,IAAIA,CAAC,KAAKG,OAAO,CAAClB,MAAM,GAAG,CAAC,EAAE;QACvC;MACF,CAAC,MAAM;QACL,OAAO,KAAK;MACd;IACF;IAEA,IAAI,CAAC,UAAU,CAACkC,IAAI,CAACZ,CAAC,CAAC,EAAE;MAAE,OAAO,KAAK;IAAE;IACzC,IAAIA,CAAC,CAACjB,UAAU,CAACiB,CAAC,CAACtB,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,UAAS;MAC9CqB,MAAM,CAACf,IAAI,CAACgB,CAAC,CAACjB,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,WAAU,QAAQ,GAAG,OAAO,CAAC;IACnE,CAAC,MAAM,IAAIiB,CAAC,CAACjB,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,UAAS;MAC1CgB,MAAM,CAACf,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC,MAAM;MACLe,MAAM,CAACf,IAAI,CAAC,EAAE,CAAC;IACjB;EACF;EAEAQ,QAAQ,GAAG3B,OAAO,CAACC,KAAK,EAAEuB,SAAS,CAAC,CAACsB,IAAI,CAAC,CAAC;EAC3C,IAAInB,QAAQ,CAACqB,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAClD,IAAI/C,KAAK,CAAC0C,MAAM,CAACnB,SAAS,CAAC,GAAGvB,KAAK,CAAC2C,SAAS,IAAI,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EACpEb,OAAO,GAAGrB,YAAY,CAACiB,QAAQ,CAAC;EAChC,IAAII,OAAO,CAAClB,MAAM,IAAIkB,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,EAAEA,OAAO,CAACkB,KAAK,CAAC,CAAC;EACxD,IAAIlB,OAAO,CAAClB,MAAM,IAAIkB,OAAO,CAACA,OAAO,CAAClB,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,EAAEkB,OAAO,CAACmB,GAAG,CAAC,CAAC;;EAEvE;EACA;EACAlB,WAAW,GAAGD,OAAO,CAAClB,MAAM;EAC5B,IAAImB,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAKE,MAAM,CAACrB,MAAM,EAAE;IAAE,OAAO,KAAK;EAAE;EAExE,IAAIa,MAAM,EAAE;IAAE,OAAO,IAAI;EAAE;EAE3BY,aAAa,GAAGrC,KAAK,CAACkD,UAAU;EAChClD,KAAK,CAACkD,UAAU,GAAG,OAAO;;EAE1B;EACA;EACAX,eAAe,GAAGvC,KAAK,CAACmD,EAAE,CAACC,KAAK,CAACC,KAAK,CAACC,QAAQ,CAAC,YAAY,CAAC;EAE7DtB,KAAK,GAAOhC,KAAK,CAACkB,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC;EAChDc,KAAK,CAACuB,GAAG,GAAGpB,UAAU,GAAG,CAAEZ,SAAS,EAAE,CAAC,CAAE;EAEzCS,KAAK,GAAOhC,KAAK,CAACkB,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC;EAChDc,KAAK,CAACuB,GAAG,GAAG,CAAEhC,SAAS,EAAEA,SAAS,GAAG,CAAC,CAAE;EAExCS,KAAK,GAAOhC,KAAK,CAACkB,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;EAC1Cc,KAAK,CAACuB,GAAG,GAAG,CAAEhC,SAAS,EAAEA,SAAS,GAAG,CAAC,CAAE;EAExC,KAAKI,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,OAAO,CAAClB,MAAM,EAAEe,CAAC,EAAE,EAAE;IACnCK,KAAK,GAAYhC,KAAK,CAACkB,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAC/C,IAAIe,MAAM,CAACN,CAAC,CAAC,EAAE;MACbK,KAAK,CAACwB,KAAK,GAAI,CAAE,CAAE,OAAO,EAAE,aAAa,GAAGvB,MAAM,CAACN,CAAC,CAAC,CAAE,CAAE;IAC3D;IAEAK,KAAK,GAAYhC,KAAK,CAACkB,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;IAC5Cc,KAAK,CAACyB,OAAO,GAAI3B,OAAO,CAACH,CAAC,CAAC,CAACkB,IAAI,CAAC,CAAC;IAClCb,KAAK,CAAC0B,QAAQ,GAAG,EAAE;IAEnB1B,KAAK,GAAYhC,KAAK,CAACkB,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EACnD;EAEAc,KAAK,GAAOhC,KAAK,CAACkB,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EAC5Cc,KAAK,GAAOhC,KAAK,CAACkB,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;EAElD,KAAKW,QAAQ,GAAGN,SAAS,GAAG,CAAC,EAAEM,QAAQ,GAAGL,OAAO,EAAEK,QAAQ,EAAE,EAAE;IAC7D,IAAI7B,KAAK,CAAC0C,MAAM,CAACb,QAAQ,CAAC,GAAG7B,KAAK,CAAC2C,SAAS,EAAE;MAAE;IAAO;IAEvDL,SAAS,GAAG,KAAK;IACjB,KAAKX,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGW,eAAe,CAAC3B,MAAM,EAAEe,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAClD,IAAIY,eAAe,CAACZ,CAAC,CAAC,CAAC3B,KAAK,EAAE6B,QAAQ,EAAEL,OAAO,EAAE,IAAI,CAAC,EAAE;QACtDc,SAAS,GAAG,IAAI;QAChB;MACF;IACF;IAEA,IAAIA,SAAS,EAAE;MAAE;IAAO;IACxBZ,QAAQ,GAAG3B,OAAO,CAACC,KAAK,EAAE6B,QAAQ,CAAC,CAACgB,IAAI,CAAC,CAAC;IAC1C,IAAI,CAACnB,QAAQ,EAAE;MAAE;IAAO;IACxB,IAAI1B,KAAK,CAAC0C,MAAM,CAACb,QAAQ,CAAC,GAAG7B,KAAK,CAAC2C,SAAS,IAAI,CAAC,EAAE;MAAE;IAAO;IAC5Db,OAAO,GAAGrB,YAAY,CAACiB,QAAQ,CAAC;IAChC,IAAII,OAAO,CAAClB,MAAM,IAAIkB,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,EAAEA,OAAO,CAACkB,KAAK,CAAC,CAAC;IACxD,IAAIlB,OAAO,CAAClB,MAAM,IAAIkB,OAAO,CAACA,OAAO,CAAClB,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,EAAEkB,OAAO,CAACmB,GAAG,CAAC,CAAC;IAEvE,IAAIpB,QAAQ,KAAKN,SAAS,GAAG,CAAC,EAAE;MAC9BS,KAAK,GAAOhC,KAAK,CAACkB,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,CAAC,CAAC;MAChDc,KAAK,CAACuB,GAAG,GAAGnB,UAAU,GAAG,CAAEb,SAAS,GAAG,CAAC,EAAE,CAAC,CAAE;IAC/C;IAEAS,KAAK,GAAOhC,KAAK,CAACkB,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1Cc,KAAK,CAACuB,GAAG,GAAG,CAAE1B,QAAQ,EAAEA,QAAQ,GAAG,CAAC,CAAE;IAEtC,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGI,WAAW,EAAEJ,CAAC,EAAE,EAAE;MAChCK,KAAK,GAAYhC,KAAK,CAACkB,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;MAC/C,IAAIe,MAAM,CAACN,CAAC,CAAC,EAAE;QACbK,KAAK,CAACwB,KAAK,GAAI,CAAE,CAAE,OAAO,EAAE,aAAa,GAAGvB,MAAM,CAACN,CAAC,CAAC,CAAE,CAAE;MAC3D;MAEAK,KAAK,GAAYhC,KAAK,CAACkB,IAAI,CAAC,QAAQ,EAAE,EAAE,EAAE,CAAC,CAAC;MAC5Cc,KAAK,CAACyB,OAAO,GAAI3B,OAAO,CAACH,CAAC,CAAC,GAAGG,OAAO,CAACH,CAAC,CAAC,CAACkB,IAAI,CAAC,CAAC,GAAG,EAAE;MACpDb,KAAK,CAAC0B,QAAQ,GAAG,EAAE;MAEnB1B,KAAK,GAAYhC,KAAK,CAACkB,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACnD;IACAc,KAAK,GAAGhC,KAAK,CAACkB,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EAC1C;EAEA,IAAIkB,UAAU,EAAE;IACdJ,KAAK,GAAGhC,KAAK,CAACkB,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;IAC9CkB,UAAU,CAAC,CAAC,CAAC,GAAGP,QAAQ;EAC1B;EAEAG,KAAK,GAAGhC,KAAK,CAACkB,IAAI,CAAC,aAAa,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC;EAC9CiB,UAAU,CAAC,CAAC,CAAC,GAAGN,QAAQ;EAExB7B,KAAK,CAACkD,UAAU,GAAGb,aAAa;EAChCrC,KAAK,CAACC,IAAI,GAAG4B,QAAQ;EACrB,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}