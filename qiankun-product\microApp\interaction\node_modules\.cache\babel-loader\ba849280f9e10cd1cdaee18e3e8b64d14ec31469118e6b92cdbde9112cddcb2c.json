{"ast": null, "code": "import { ref, onActivated } from 'vue';\nimport { format } from 'common/js/time.js';\nimport { GlobalTable } from 'common/js/GlobalTable.js';\nimport { qiankunMicro } from 'common/config/MicroGlobal';\n// import JoinVideoMeeting from './component/JoinVideoMeeting'\nimport LiveBroadcastDetails from './LiveBroadcastDetails.vue';\nvar __default__ = {\n  name: 'LiveBroadcast'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var buttonList = [\n      // { id: 'join', name: '手动入会', type: 'primary', has: 'join' }\n    ];\n    // const joinShow = ref(false)\n    var _GlobalTable = GlobalTable({\n        tableApi: 'videoConnectionList',\n        tableDataObj: {\n          query: {\n            isLive: 1\n          }\n        }\n      }),\n      keyword = _GlobalTable.keyword,\n      totals = _GlobalTable.totals,\n      pageNo = _GlobalTable.pageNo,\n      pageSize = _GlobalTable.pageSize,\n      pageSizes = _GlobalTable.pageSizes,\n      tableData = _GlobalTable.tableData,\n      handleQuery = _GlobalTable.handleQuery;\n    onActivated(function () {\n      handleQuery();\n    });\n    var handleButton = function handleButton(isType) {\n      switch (isType) {\n        case 'join':\n          // handleJoin()\n          break;\n        default:\n          break;\n      }\n    };\n    var handleReset = function handleReset() {\n      keyword.value = '';\n      handleQuery();\n    };\n    // const handleJoin = () => {\n    //   joinShow.value = true\n    // }\n    // const joinCallback = () => {\n    //   joinShow.value = false\n    // }\n    var detailsShow = ref(false);\n    var detailsId = ref('');\n    var handleDetails = function handleDetails(item) {\n      // detailsId.value = item.id\n      // detailsShow.value = true\n      qiankunMicro.setGlobalState({\n        openRoute: {\n          name: '视频会议详情',\n          path: '/interaction/LiveBroadcastDetails',\n          query: {\n            id: item.id\n          }\n        }\n      });\n    };\n    var callback = function callback() {\n      detailsShow.value = false;\n    };\n    var __returned__ = {\n      buttonList,\n      keyword,\n      totals,\n      pageNo,\n      pageSize,\n      pageSizes,\n      tableData,\n      handleQuery,\n      handleButton,\n      handleReset,\n      detailsShow,\n      detailsId,\n      handleDetails,\n      callback,\n      ref,\n      onActivated,\n      get format() {\n        return format;\n      },\n      get GlobalTable() {\n        return GlobalTable;\n      },\n      get qiankunMicro() {\n        return qiankunMicro;\n      },\n      LiveBroadcastDetails\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onActivated", "format", "GlobalTable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "LiveBroadcastDetails", "__default__", "name", "buttonList", "_GlobalTable", "tableApi", "tableDataObj", "query", "isLive", "keyword", "totals", "pageNo", "pageSize", "pageSizes", "tableData", "handleQuery", "handleButton", "isType", "handleReset", "value", "detailsShow", "detailsId", "handleDetails", "item", "setGlobalState", "openRoute", "path", "id", "callback"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/interaction/src/views/LiveManagement/LiveBroadcast.vue"], "sourcesContent": ["<template>\r\n  <div class=\"LiveBroadcast\">\r\n    <xyl-search-button @queryClick=\"handleQuery\" @resetClick=\"handleReset\" @handleButton=\"handleButton\"\r\n      :buttonList=\"buttonList\">\r\n      <template #search>\r\n        <el-input v-model=\"keyword\" placeholder=\"请输入关键词\" @keyup.enter=\"handleQuery\" clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <el-scrollbar always class=\"LiveBroadcastScrollbar\">\r\n      <el-empty description=\"暂无数据\" v-if=\"!tableData.length\" />\r\n      <div class=\"LiveBroadcastList\">\r\n        <div class=\"LiveBroadcastItem\" v-for=\"item in tableData\" :key=\"item.id\" @click=\"handleDetails(item)\">\r\n          <div class=\"LiveBroadcastType\">{{ item.meetingStatus }}</div>\r\n          <div class=\"LiveBroadcastName ellipsis\">{{ item.theme }}</div>\r\n          <div class=\"LiveBroadcastText\">会议号：<span>{{ item.meetingNumber }}</span></div>\r\n          <div class=\"LiveBroadcastDetailsTime\">\r\n            <div class=\"LiveBroadcastDetailsTimeLeft\">\r\n              <div class=\"LiveBroadcastDetailsTimeHours\">{{ format(item.startTime, 'HH:mm') }}</div>\r\n              <div class=\"LiveBroadcastDetailsTimeDate\">{{ format(item.startTime, 'YYYY年MM月DD日') }}</div>\r\n            </div>\r\n            <div class=\"LiveBroadcastDetailsTimeCenter\">\r\n              <div class=\"LiveBroadcastDetailsDuration\">{{ item.during }}分钟</div>\r\n            </div>\r\n            <div class=\"LiveBroadcastDetailsTimeRight\">\r\n              <div class=\"LiveBroadcastDetailsTimeHours\">{{ format(item.endTime, 'HH:mm') }}</div>\r\n              <div class=\"LiveBroadcastDetailsTimeDate\">{{ format(item.endTime, 'YYYY年MM月DD日') }}</div>\r\n            </div>\r\n          </div>\r\n          <div class=\"LiveBroadcastText\">发起人：<span>{{ item.createUserName }}</span></div>\r\n          <div class=\"LiveBroadcastText\">发起时间：<span>{{ format(item.createDate, 'YYYY年MM月DD日 HH:mm') }}</span></div>\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"pageNo\" v-model:page-size=\"pageSize\" :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\" @size-change=\"handleQuery\" @current-change=\"handleQuery\"\r\n        :total=\"totals\" background />\r\n    </div>\r\n    <LiveBroadcastDetails v-model=\"detailsShow\" :id=\"detailsId\" @callback=\"callback\" />\r\n    <!-- <xyl-popup-window v-model=\"joinShow\" name=\"手动入会\">\r\n      <JoinVideoMeeting @callback=\"joinCallback\"></JoinVideoMeeting>\r\n    </xyl-popup-window> -->\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'LiveBroadcast' }\r\n</script>\r\n<script setup>\r\nimport { ref, onActivated } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\nimport { qiankunMicro } from 'common/config/MicroGlobal'\r\n// import JoinVideoMeeting from './component/JoinVideoMeeting'\r\nimport LiveBroadcastDetails from './LiveBroadcastDetails.vue'\r\nconst buttonList = [\r\n  // { id: 'join', name: '手动入会', type: 'primary', has: 'join' }\r\n]\r\n// const joinShow = ref(false)\r\nconst {\r\n  keyword,\r\n  totals,\r\n  pageNo,\r\n  pageSize,\r\n  pageSizes,\r\n  tableData,\r\n  handleQuery\r\n} = GlobalTable({ tableApi: 'videoConnectionList', tableDataObj: { query: { isLive: 1 } } })\r\n\r\nonActivated(() => {\r\n  handleQuery()\r\n})\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'join':\r\n      // handleJoin()\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\nconst handleReset = () => {\r\n  keyword.value = ''\r\n  handleQuery()\r\n}\r\n// const handleJoin = () => {\r\n//   joinShow.value = true\r\n// }\r\n// const joinCallback = () => {\r\n//   joinShow.value = false\r\n// }\r\nconst detailsShow = ref(false)\r\nconst detailsId = ref('')\r\nconst handleDetails = (item) => {\r\n  // detailsId.value = item.id\r\n  // detailsShow.value = true\r\n  qiankunMicro.setGlobalState({ openRoute: { name: '视频会议详情', path: '/interaction/LiveBroadcastDetails', query: { id: item.id } } })\r\n}\r\nconst callback = () => {\r\n  detailsShow.value = false\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.LiveBroadcast {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .LiveBroadcastScrollbar {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n\r\n    .LiveBroadcastList {\r\n      width: 100%;\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      padding-left: 10px;\r\n\r\n      .LiveBroadcastItem {\r\n        width: calc(50% - 20px);\r\n        margin: 10px 0;\r\n        margin-right: 20px;\r\n        padding: 16px 20px;\r\n        background: #ffffff;\r\n        box-shadow: 0px 2px 10px 1px rgba(0, 0, 0, 0.1);\r\n\r\n        .LiveBroadcastType {\r\n          display: inline-block;\r\n          padding: 2px 12px;\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          color: var(--el-color-warning);\r\n          background-color: var(--el-color-warning-light-9);\r\n        }\r\n\r\n        .LiveBroadcastName {\r\n          width: 100%;\r\n          font-weight: bold;\r\n          font-size: var(--zy-name-font-size);\r\n          line-height: var(--zy-line-height);\r\n          padding: 8px 0;\r\n        }\r\n\r\n        .LiveBroadcastDetailsTime {\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: space-between;\r\n          padding: var(--zy-distance-two) 0;\r\n\r\n          .LiveBroadcastDetailsTimeLeft,\r\n          .LiveBroadcastDetailsTimeRight {\r\n            text-align: center;\r\n          }\r\n\r\n          .LiveBroadcastDetailsTimeCenter {\r\n            width: 88px;\r\n            height: 88px;\r\n            display: flex;\r\n            align-items: center;\r\n            flex-direction: column;\r\n            justify-content: center;\r\n            border-radius: 50%;\r\n            border: 1px solid var(--zy-el-color-primary);\r\n            background: var(--zy-el-color-primary-light-9);\r\n            position: relative;\r\n\r\n            &::after {\r\n              content: '';\r\n              position: absolute;\r\n              width: 88px;\r\n              height: 1px;\r\n              background: linear-gradient(90deg, var(--zy-el-color-primary), #fff);\r\n              top: 50%;\r\n              right: 0;\r\n              transform: translate(100%, -50%);\r\n            }\r\n\r\n            &::before {\r\n              content: '';\r\n              position: absolute;\r\n              width: 88px;\r\n              height: 1px;\r\n              background: linear-gradient(90deg, #fff, var(--zy-el-color-primary));\r\n              top: 50%;\r\n              left: 0;\r\n              transform: translate(-100%, -50%);\r\n            }\r\n\r\n            .LiveBroadcastDetailsDuration {\r\n              font-weight: bold;\r\n              color: var(--zy-el-color-primary);\r\n              font-size: var(--zy-name-font-size);\r\n            }\r\n          }\r\n\r\n          .LiveBroadcastDetailsTimeHours {\r\n            font-weight: bold;\r\n            font-size: calc(var(--zy-name-font-size) + 2px);\r\n          }\r\n\r\n          .LiveBroadcastDetailsTimeDate {\r\n            font-size: var(--zy-text-font-size);\r\n          }\r\n        }\r\n\r\n        .LiveBroadcastText {\r\n          font-weight: bold;\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          padding: 3px 0;\r\n\r\n          span {\r\n            font-weight: normal;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media screen and (min-width: 1660px) {\r\n  .LiveBroadcast {\r\n    .LiveBroadcastScrollbar {\r\n      .LiveBroadcastList {\r\n        .LiveBroadcastItem {\r\n          width: calc(33.3% - 20px);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAgDA,SAASA,GAAG,EAAEC,WAAW,QAAQ,KAAK;AACtC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,WAAW,QAAQ,0BAA0B;AACtD,SAASC,YAAY,QAAQ,2BAA2B;AACxD;AACA,OAAOC,oBAAoB,MAAM,4BAA4B;AAR7D,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAgB,CAAC;;;;;IASxC,IAAMC,UAAU,GAAG;MACjB;IAAA,CACD;IACD;IACA,IAAAC,YAAA,GAQIN,WAAW,CAAC;QAAEO,QAAQ,EAAE,qBAAqB;QAAEC,YAAY,EAAE;UAAEC,KAAK,EAAE;YAAEC,MAAM,EAAE;UAAE;QAAE;MAAE,CAAC,CAAC;MAP1FC,OAAO,GAAAL,YAAA,CAAPK,OAAO;MACPC,MAAM,GAAAN,YAAA,CAANM,MAAM;MACNC,MAAM,GAAAP,YAAA,CAANO,MAAM;MACNC,QAAQ,GAAAR,YAAA,CAARQ,QAAQ;MACRC,SAAS,GAAAT,YAAA,CAATS,SAAS;MACTC,SAAS,GAAAV,YAAA,CAATU,SAAS;MACTC,WAAW,GAAAX,YAAA,CAAXW,WAAW;IAGbnB,WAAW,CAAC,YAAM;MAChBmB,WAAW,CAAC,CAAC;IACf,CAAC,CAAC;IACF,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAIC,MAAM,EAAK;MAC/B,QAAQA,MAAM;QACZ,KAAK,MAAM;UACT;UACA;QACF;UACE;MACJ;IACF,CAAC;IACD,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBT,OAAO,CAACU,KAAK,GAAG,EAAE;MAClBJ,WAAW,CAAC,CAAC;IACf,CAAC;IACD;IACA;IACA;IACA;IACA;IACA;IACA,IAAMK,WAAW,GAAGzB,GAAG,CAAC,KAAK,CAAC;IAC9B,IAAM0B,SAAS,GAAG1B,GAAG,CAAC,EAAE,CAAC;IACzB,IAAM2B,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,IAAI,EAAK;MAC9B;MACA;MACAxB,YAAY,CAACyB,cAAc,CAAC;QAAEC,SAAS,EAAE;UAAEvB,IAAI,EAAE,QAAQ;UAAEwB,IAAI,EAAE,mCAAmC;UAAEnB,KAAK,EAAE;YAAEoB,EAAE,EAAEJ,IAAI,CAACI;UAAG;QAAE;MAAE,CAAC,CAAC;IACnI,CAAC;IACD,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAA,EAAS;MACrBR,WAAW,CAACD,KAAK,GAAG,KAAK;IAC3B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}