{"ast": null, "code": "/**\n * Module dependencies.\n */\n\nvar Transport = require('../transport');\nvar parser = require('engine.io-parser');\nvar parseqs = require('parseqs');\nvar inherit = require('component-inherit');\nvar yeast = require('yeast');\nvar debug = require('debug')('engine.io-client:websocket');\nvar BrowserWebSocket, NodeWebSocket;\nif (typeof WebSocket !== 'undefined') {\n  BrowserWebSocket = WebSocket;\n} else if (typeof self !== 'undefined') {\n  BrowserWebSocket = self.WebSocket || self.MozWebSocket;\n}\nif (typeof window === 'undefined') {\n  try {\n    NodeWebSocket = require('ws');\n  } catch (e) {}\n}\n\n/**\n * Get either the `WebSocket` or `MozWebSocket` globals\n * in the browser or try to resolve WebSocket-compatible\n * interface exposed by `ws` for Node-like environment.\n */\n\nvar WebSocketImpl = BrowserWebSocket || NodeWebSocket;\n\n/**\n * Module exports.\n */\n\nmodule.exports = WS;\n\n/**\n * WebSocket transport constructor.\n *\n * @api {Object} connection options\n * @api public\n */\n\nfunction WS(opts) {\n  var forceBase64 = opts && opts.forceBase64;\n  if (forceBase64) {\n    this.supportsBinary = false;\n  }\n  this.perMessageDeflate = opts.perMessageDeflate;\n  this.usingBrowserWebSocket = BrowserWebSocket && !opts.forceNode;\n  this.protocols = opts.protocols;\n  if (!this.usingBrowserWebSocket) {\n    WebSocketImpl = NodeWebSocket;\n  }\n  Transport.call(this, opts);\n}\n\n/**\n * Inherits from Transport.\n */\n\ninherit(WS, Transport);\n\n/**\n * Transport name.\n *\n * @api public\n */\n\nWS.prototype.name = 'websocket';\n\n/*\n * WebSockets support binary\n */\n\nWS.prototype.supportsBinary = true;\n\n/**\n * Opens socket.\n *\n * @api private\n */\n\nWS.prototype.doOpen = function () {\n  if (!this.check()) {\n    // let probe timeout\n    return;\n  }\n  var uri = this.uri();\n  var protocols = this.protocols;\n  var opts = {};\n  if (!this.isReactNative) {\n    opts.agent = this.agent;\n    opts.perMessageDeflate = this.perMessageDeflate;\n\n    // SSL options for Node.js client\n    opts.pfx = this.pfx;\n    opts.key = this.key;\n    opts.passphrase = this.passphrase;\n    opts.cert = this.cert;\n    opts.ca = this.ca;\n    opts.ciphers = this.ciphers;\n    opts.rejectUnauthorized = this.rejectUnauthorized;\n  }\n  if (this.extraHeaders) {\n    opts.headers = this.extraHeaders;\n  }\n  if (this.localAddress) {\n    opts.localAddress = this.localAddress;\n  }\n  try {\n    this.ws = this.usingBrowserWebSocket && !this.isReactNative ? protocols ? new WebSocketImpl(uri, protocols) : new WebSocketImpl(uri) : new WebSocketImpl(uri, protocols, opts);\n  } catch (err) {\n    return this.emit('error', err);\n  }\n  if (this.ws.binaryType === undefined) {\n    this.supportsBinary = false;\n  }\n  if (this.ws.supports && this.ws.supports.binary) {\n    this.supportsBinary = true;\n    this.ws.binaryType = 'nodebuffer';\n  } else {\n    this.ws.binaryType = 'arraybuffer';\n  }\n  this.addEventListeners();\n};\n\n/**\n * Adds event listeners to the socket\n *\n * @api private\n */\n\nWS.prototype.addEventListeners = function () {\n  var self = this;\n  this.ws.onopen = function () {\n    self.onOpen();\n  };\n  this.ws.onclose = function () {\n    self.onClose();\n  };\n  this.ws.onmessage = function (ev) {\n    self.onData(ev.data);\n  };\n  this.ws.onerror = function (e) {\n    self.onError('websocket error', e);\n  };\n};\n\n/**\n * Writes data to socket.\n *\n * @param {Array} array of packets.\n * @api private\n */\n\nWS.prototype.write = function (packets) {\n  var self = this;\n  this.writable = false;\n\n  // encodePacket efficient as it uses WS framing\n  // no need for encodePayload\n  var total = packets.length;\n  for (var i = 0, l = total; i < l; i++) {\n    (function (packet) {\n      parser.encodePacket(packet, self.supportsBinary, function (data) {\n        if (!self.usingBrowserWebSocket) {\n          // always create a new object (GH-437)\n          var opts = {};\n          if (packet.options) {\n            opts.compress = packet.options.compress;\n          }\n          if (self.perMessageDeflate) {\n            var len = 'string' === typeof data ? Buffer.byteLength(data) : data.length;\n            if (len < self.perMessageDeflate.threshold) {\n              opts.compress = false;\n            }\n          }\n        }\n\n        // Sometimes the websocket has already been closed but the browser didn't\n        // have a chance of informing us about it yet, in that case send will\n        // throw an error\n        try {\n          if (self.usingBrowserWebSocket) {\n            // TypeError is thrown when passing the second argument on Safari\n            self.ws.send(data);\n          } else {\n            self.ws.send(data, opts);\n          }\n        } catch (e) {\n          debug('websocket closed before onclose event');\n        }\n        --total || done();\n      });\n    })(packets[i]);\n  }\n  function done() {\n    self.emit('flush');\n\n    // fake drain\n    // defer to next tick to allow Socket to clear writeBuffer\n    setTimeout(function () {\n      self.writable = true;\n      self.emit('drain');\n    }, 0);\n  }\n};\n\n/**\n * Called upon close\n *\n * @api private\n */\n\nWS.prototype.onClose = function () {\n  Transport.prototype.onClose.call(this);\n};\n\n/**\n * Closes socket.\n *\n * @api private\n */\n\nWS.prototype.doClose = function () {\n  if (typeof this.ws !== 'undefined') {\n    this.ws.close();\n  }\n};\n\n/**\n * Generates uri for connection.\n *\n * @api private\n */\n\nWS.prototype.uri = function () {\n  var query = this.query || {};\n  var schema = this.secure ? 'wss' : 'ws';\n  var port = '';\n\n  // avoid port if default for schema\n  if (this.port && ('wss' === schema && Number(this.port) !== 443 || 'ws' === schema && Number(this.port) !== 80)) {\n    port = ':' + this.port;\n  }\n\n  // append timestamp to URI\n  if (this.timestampRequests) {\n    query[this.timestampParam] = yeast();\n  }\n\n  // communicate binary support capabilities\n  if (!this.supportsBinary) {\n    query.b64 = 1;\n  }\n  query = parseqs.encode(query);\n\n  // prepend ? to query\n  if (query.length) {\n    query = '?' + query;\n  }\n  var ipv6 = this.hostname.indexOf(':') !== -1;\n  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n};\n\n/**\n * Feature detection for WebSocket.\n *\n * @return {Boolean} whether this transport is available.\n * @api public\n */\n\nWS.prototype.check = function () {\n  return !!WebSocketImpl && !('__initialize' in WebSocketImpl && this.name === WS.prototype.name);\n};", "map": {"version": 3, "names": ["Transport", "require", "parser", "parseqs", "inherit", "yeast", "debug", "BrowserWebSocket", "NodeWebSocket", "WebSocket", "self", "MozWebSocket", "window", "e", "WebSocketImpl", "module", "exports", "WS", "opts", "forceBase64", "supportsBinary", "perMessageDeflate", "usingBrowserWebSocket", "forceNode", "protocols", "call", "prototype", "name", "doOpen", "check", "uri", "isReactNative", "agent", "pfx", "key", "passphrase", "cert", "ca", "ciphers", "rejectUnauthorized", "extraHeaders", "headers", "localAddress", "ws", "err", "emit", "binaryType", "undefined", "supports", "binary", "addEventListeners", "onopen", "onOpen", "onclose", "onClose", "onmessage", "ev", "onData", "data", "onerror", "onError", "write", "packets", "writable", "total", "length", "i", "l", "packet", "encodePacket", "options", "compress", "len", "<PERSON><PERSON><PERSON>", "byteLength", "threshold", "send", "done", "setTimeout", "doClose", "close", "query", "schema", "secure", "port", "Number", "timestampRequests", "timestampParam", "b64", "encode", "ipv6", "hostname", "indexOf", "path"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/engine.io-client@3.5.4/node_modules/engine.io-client/lib/transports/websocket.js"], "sourcesContent": ["/**\n * Module dependencies.\n */\n\nvar Transport = require('../transport');\nvar parser = require('engine.io-parser');\nvar parseqs = require('parseqs');\nvar inherit = require('component-inherit');\nvar yeast = require('yeast');\nvar debug = require('debug')('engine.io-client:websocket');\n\nvar BrowserWebSocket, NodeWebSocket;\n\nif (typeof WebSocket !== 'undefined') {\n  BrowserWebSocket = WebSocket;\n} else if (typeof self !== 'undefined') {\n  BrowserWebSocket = self.WebSocket || self.MozWebSocket;\n}\n\nif (typeof window === 'undefined') {\n  try {\n    NodeWebSocket = require('ws');\n  } catch (e) { }\n}\n\n/**\n * Get either the `WebSocket` or `MozWebSocket` globals\n * in the browser or try to resolve WebSocket-compatible\n * interface exposed by `ws` for Node-like environment.\n */\n\nvar WebSocketImpl = BrowserWebSocket || NodeWebSocket;\n\n/**\n * Module exports.\n */\n\nmodule.exports = WS;\n\n/**\n * WebSocket transport constructor.\n *\n * @api {Object} connection options\n * @api public\n */\n\nfunction WS (opts) {\n  var forceBase64 = (opts && opts.forceBase64);\n  if (forceBase64) {\n    this.supportsBinary = false;\n  }\n  this.perMessageDeflate = opts.perMessageDeflate;\n  this.usingBrowserWebSocket = BrowserWebSocket && !opts.forceNode;\n  this.protocols = opts.protocols;\n  if (!this.usingBrowserWebSocket) {\n    WebSocketImpl = NodeWebSocket;\n  }\n  Transport.call(this, opts);\n}\n\n/**\n * Inherits from Transport.\n */\n\ninherit(WS, Transport);\n\n/**\n * Transport name.\n *\n * @api public\n */\n\nWS.prototype.name = 'websocket';\n\n/*\n * WebSockets support binary\n */\n\nWS.prototype.supportsBinary = true;\n\n/**\n * Opens socket.\n *\n * @api private\n */\n\nWS.prototype.doOpen = function () {\n  if (!this.check()) {\n    // let probe timeout\n    return;\n  }\n\n  var uri = this.uri();\n  var protocols = this.protocols;\n\n  var opts = {};\n\n  if (!this.isReactNative) {\n    opts.agent = this.agent;\n    opts.perMessageDeflate = this.perMessageDeflate;\n\n    // SSL options for Node.js client\n    opts.pfx = this.pfx;\n    opts.key = this.key;\n    opts.passphrase = this.passphrase;\n    opts.cert = this.cert;\n    opts.ca = this.ca;\n    opts.ciphers = this.ciphers;\n    opts.rejectUnauthorized = this.rejectUnauthorized;\n  }\n\n  if (this.extraHeaders) {\n    opts.headers = this.extraHeaders;\n  }\n  if (this.localAddress) {\n    opts.localAddress = this.localAddress;\n  }\n\n  try {\n    this.ws =\n      this.usingBrowserWebSocket && !this.isReactNative\n        ? protocols\n          ? new WebSocketImpl(uri, protocols)\n          : new WebSocketImpl(uri)\n        : new WebSocketImpl(uri, protocols, opts);\n  } catch (err) {\n    return this.emit('error', err);\n  }\n\n  if (this.ws.binaryType === undefined) {\n    this.supportsBinary = false;\n  }\n\n  if (this.ws.supports && this.ws.supports.binary) {\n    this.supportsBinary = true;\n    this.ws.binaryType = 'nodebuffer';\n  } else {\n    this.ws.binaryType = 'arraybuffer';\n  }\n\n  this.addEventListeners();\n};\n\n/**\n * Adds event listeners to the socket\n *\n * @api private\n */\n\nWS.prototype.addEventListeners = function () {\n  var self = this;\n\n  this.ws.onopen = function () {\n    self.onOpen();\n  };\n  this.ws.onclose = function () {\n    self.onClose();\n  };\n  this.ws.onmessage = function (ev) {\n    self.onData(ev.data);\n  };\n  this.ws.onerror = function (e) {\n    self.onError('websocket error', e);\n  };\n};\n\n/**\n * Writes data to socket.\n *\n * @param {Array} array of packets.\n * @api private\n */\n\nWS.prototype.write = function (packets) {\n  var self = this;\n  this.writable = false;\n\n  // encodePacket efficient as it uses WS framing\n  // no need for encodePayload\n  var total = packets.length;\n  for (var i = 0, l = total; i < l; i++) {\n    (function (packet) {\n      parser.encodePacket(packet, self.supportsBinary, function (data) {\n        if (!self.usingBrowserWebSocket) {\n          // always create a new object (GH-437)\n          var opts = {};\n          if (packet.options) {\n            opts.compress = packet.options.compress;\n          }\n\n          if (self.perMessageDeflate) {\n            var len = 'string' === typeof data ? Buffer.byteLength(data) : data.length;\n            if (len < self.perMessageDeflate.threshold) {\n              opts.compress = false;\n            }\n          }\n        }\n\n        // Sometimes the websocket has already been closed but the browser didn't\n        // have a chance of informing us about it yet, in that case send will\n        // throw an error\n        try {\n          if (self.usingBrowserWebSocket) {\n            // TypeError is thrown when passing the second argument on Safari\n            self.ws.send(data);\n          } else {\n            self.ws.send(data, opts);\n          }\n        } catch (e) {\n          debug('websocket closed before onclose event');\n        }\n\n        --total || done();\n      });\n    })(packets[i]);\n  }\n\n  function done () {\n    self.emit('flush');\n\n    // fake drain\n    // defer to next tick to allow Socket to clear writeBuffer\n    setTimeout(function () {\n      self.writable = true;\n      self.emit('drain');\n    }, 0);\n  }\n};\n\n/**\n * Called upon close\n *\n * @api private\n */\n\nWS.prototype.onClose = function () {\n  Transport.prototype.onClose.call(this);\n};\n\n/**\n * Closes socket.\n *\n * @api private\n */\n\nWS.prototype.doClose = function () {\n  if (typeof this.ws !== 'undefined') {\n    this.ws.close();\n  }\n};\n\n/**\n * Generates uri for connection.\n *\n * @api private\n */\n\nWS.prototype.uri = function () {\n  var query = this.query || {};\n  var schema = this.secure ? 'wss' : 'ws';\n  var port = '';\n\n  // avoid port if default for schema\n  if (this.port && (('wss' === schema && Number(this.port) !== 443) ||\n    ('ws' === schema && Number(this.port) !== 80))) {\n    port = ':' + this.port;\n  }\n\n  // append timestamp to URI\n  if (this.timestampRequests) {\n    query[this.timestampParam] = yeast();\n  }\n\n  // communicate binary support capabilities\n  if (!this.supportsBinary) {\n    query.b64 = 1;\n  }\n\n  query = parseqs.encode(query);\n\n  // prepend ? to query\n  if (query.length) {\n    query = '?' + query;\n  }\n\n  var ipv6 = this.hostname.indexOf(':') !== -1;\n  return schema + '://' + (ipv6 ? '[' + this.hostname + ']' : this.hostname) + port + this.path + query;\n};\n\n/**\n * Feature detection for WebSocket.\n *\n * @return {Boolean} whether this transport is available.\n * @api public\n */\n\nWS.prototype.check = function () {\n  return !!WebSocketImpl && !('__initialize' in WebSocketImpl && this.name === WS.prototype.name);\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA,IAAIA,SAAS,GAAGC,OAAO,CAAC,cAAc,CAAC;AACvC,IAAIC,MAAM,GAAGD,OAAO,CAAC,kBAAkB,CAAC;AACxC,IAAIE,OAAO,GAAGF,OAAO,CAAC,SAAS,CAAC;AAChC,IAAIG,OAAO,GAAGH,OAAO,CAAC,mBAAmB,CAAC;AAC1C,IAAII,KAAK,GAAGJ,OAAO,CAAC,OAAO,CAAC;AAC5B,IAAIK,KAAK,GAAGL,OAAO,CAAC,OAAO,CAAC,CAAC,4BAA4B,CAAC;AAE1D,IAAIM,gBAAgB,EAAEC,aAAa;AAEnC,IAAI,OAAOC,SAAS,KAAK,WAAW,EAAE;EACpCF,gBAAgB,GAAGE,SAAS;AAC9B,CAAC,MAAM,IAAI,OAAOC,IAAI,KAAK,WAAW,EAAE;EACtCH,gBAAgB,GAAGG,IAAI,CAACD,SAAS,IAAIC,IAAI,CAACC,YAAY;AACxD;AAEA,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;EACjC,IAAI;IACFJ,aAAa,GAAGP,OAAO,CAAC,IAAI,CAAC;EAC/B,CAAC,CAAC,OAAOY,CAAC,EAAE,CAAE;AAChB;;AAEA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,aAAa,GAAGP,gBAAgB,IAAIC,aAAa;;AAErD;AACA;AACA;;AAEAO,MAAM,CAACC,OAAO,GAAGC,EAAE;;AAEnB;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASA,EAAEA,CAAEC,IAAI,EAAE;EACjB,IAAIC,WAAW,GAAID,IAAI,IAAIA,IAAI,CAACC,WAAY;EAC5C,IAAIA,WAAW,EAAE;IACf,IAAI,CAACC,cAAc,GAAG,KAAK;EAC7B;EACA,IAAI,CAACC,iBAAiB,GAAGH,IAAI,CAACG,iBAAiB;EAC/C,IAAI,CAACC,qBAAqB,GAAGf,gBAAgB,IAAI,CAACW,IAAI,CAACK,SAAS;EAChE,IAAI,CAACC,SAAS,GAAGN,IAAI,CAACM,SAAS;EAC/B,IAAI,CAAC,IAAI,CAACF,qBAAqB,EAAE;IAC/BR,aAAa,GAAGN,aAAa;EAC/B;EACAR,SAAS,CAACyB,IAAI,CAAC,IAAI,EAAEP,IAAI,CAAC;AAC5B;;AAEA;AACA;AACA;;AAEAd,OAAO,CAACa,EAAE,EAAEjB,SAAS,CAAC;;AAEtB;AACA;AACA;AACA;AACA;;AAEAiB,EAAE,CAACS,SAAS,CAACC,IAAI,GAAG,WAAW;;AAE/B;AACA;AACA;;AAEAV,EAAE,CAACS,SAAS,CAACN,cAAc,GAAG,IAAI;;AAElC;AACA;AACA;AACA;AACA;;AAEAH,EAAE,CAACS,SAAS,CAACE,MAAM,GAAG,YAAY;EAChC,IAAI,CAAC,IAAI,CAACC,KAAK,CAAC,CAAC,EAAE;IACjB;IACA;EACF;EAEA,IAAIC,GAAG,GAAG,IAAI,CAACA,GAAG,CAAC,CAAC;EACpB,IAAIN,SAAS,GAAG,IAAI,CAACA,SAAS;EAE9B,IAAIN,IAAI,GAAG,CAAC,CAAC;EAEb,IAAI,CAAC,IAAI,CAACa,aAAa,EAAE;IACvBb,IAAI,CAACc,KAAK,GAAG,IAAI,CAACA,KAAK;IACvBd,IAAI,CAACG,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;;IAE/C;IACAH,IAAI,CAACe,GAAG,GAAG,IAAI,CAACA,GAAG;IACnBf,IAAI,CAACgB,GAAG,GAAG,IAAI,CAACA,GAAG;IACnBhB,IAAI,CAACiB,UAAU,GAAG,IAAI,CAACA,UAAU;IACjCjB,IAAI,CAACkB,IAAI,GAAG,IAAI,CAACA,IAAI;IACrBlB,IAAI,CAACmB,EAAE,GAAG,IAAI,CAACA,EAAE;IACjBnB,IAAI,CAACoB,OAAO,GAAG,IAAI,CAACA,OAAO;IAC3BpB,IAAI,CAACqB,kBAAkB,GAAG,IAAI,CAACA,kBAAkB;EACnD;EAEA,IAAI,IAAI,CAACC,YAAY,EAAE;IACrBtB,IAAI,CAACuB,OAAO,GAAG,IAAI,CAACD,YAAY;EAClC;EACA,IAAI,IAAI,CAACE,YAAY,EAAE;IACrBxB,IAAI,CAACwB,YAAY,GAAG,IAAI,CAACA,YAAY;EACvC;EAEA,IAAI;IACF,IAAI,CAACC,EAAE,GACL,IAAI,CAACrB,qBAAqB,IAAI,CAAC,IAAI,CAACS,aAAa,GAC7CP,SAAS,GACP,IAAIV,aAAa,CAACgB,GAAG,EAAEN,SAAS,CAAC,GACjC,IAAIV,aAAa,CAACgB,GAAG,CAAC,GACxB,IAAIhB,aAAa,CAACgB,GAAG,EAAEN,SAAS,EAAEN,IAAI,CAAC;EAC/C,CAAC,CAAC,OAAO0B,GAAG,EAAE;IACZ,OAAO,IAAI,CAACC,IAAI,CAAC,OAAO,EAAED,GAAG,CAAC;EAChC;EAEA,IAAI,IAAI,CAACD,EAAE,CAACG,UAAU,KAAKC,SAAS,EAAE;IACpC,IAAI,CAAC3B,cAAc,GAAG,KAAK;EAC7B;EAEA,IAAI,IAAI,CAACuB,EAAE,CAACK,QAAQ,IAAI,IAAI,CAACL,EAAE,CAACK,QAAQ,CAACC,MAAM,EAAE;IAC/C,IAAI,CAAC7B,cAAc,GAAG,IAAI;IAC1B,IAAI,CAACuB,EAAE,CAACG,UAAU,GAAG,YAAY;EACnC,CAAC,MAAM;IACL,IAAI,CAACH,EAAE,CAACG,UAAU,GAAG,aAAa;EACpC;EAEA,IAAI,CAACI,iBAAiB,CAAC,CAAC;AAC1B,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEAjC,EAAE,CAACS,SAAS,CAACwB,iBAAiB,GAAG,YAAY;EAC3C,IAAIxC,IAAI,GAAG,IAAI;EAEf,IAAI,CAACiC,EAAE,CAACQ,MAAM,GAAG,YAAY;IAC3BzC,IAAI,CAAC0C,MAAM,CAAC,CAAC;EACf,CAAC;EACD,IAAI,CAACT,EAAE,CAACU,OAAO,GAAG,YAAY;IAC5B3C,IAAI,CAAC4C,OAAO,CAAC,CAAC;EAChB,CAAC;EACD,IAAI,CAACX,EAAE,CAACY,SAAS,GAAG,UAAUC,EAAE,EAAE;IAChC9C,IAAI,CAAC+C,MAAM,CAACD,EAAE,CAACE,IAAI,CAAC;EACtB,CAAC;EACD,IAAI,CAACf,EAAE,CAACgB,OAAO,GAAG,UAAU9C,CAAC,EAAE;IAC7BH,IAAI,CAACkD,OAAO,CAAC,iBAAiB,EAAE/C,CAAC,CAAC;EACpC,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEAI,EAAE,CAACS,SAAS,CAACmC,KAAK,GAAG,UAAUC,OAAO,EAAE;EACtC,IAAIpD,IAAI,GAAG,IAAI;EACf,IAAI,CAACqD,QAAQ,GAAG,KAAK;;EAErB;EACA;EACA,IAAIC,KAAK,GAAGF,OAAO,CAACG,MAAM;EAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,KAAK,EAAEE,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;IACrC,CAAC,UAAUE,MAAM,EAAE;MACjBlE,MAAM,CAACmE,YAAY,CAACD,MAAM,EAAE1D,IAAI,CAACU,cAAc,EAAE,UAAUsC,IAAI,EAAE;QAC/D,IAAI,CAAChD,IAAI,CAACY,qBAAqB,EAAE;UAC/B;UACA,IAAIJ,IAAI,GAAG,CAAC,CAAC;UACb,IAAIkD,MAAM,CAACE,OAAO,EAAE;YAClBpD,IAAI,CAACqD,QAAQ,GAAGH,MAAM,CAACE,OAAO,CAACC,QAAQ;UACzC;UAEA,IAAI7D,IAAI,CAACW,iBAAiB,EAAE;YAC1B,IAAImD,GAAG,GAAG,QAAQ,KAAK,OAAOd,IAAI,GAAGe,MAAM,CAACC,UAAU,CAAChB,IAAI,CAAC,GAAGA,IAAI,CAACO,MAAM;YAC1E,IAAIO,GAAG,GAAG9D,IAAI,CAACW,iBAAiB,CAACsD,SAAS,EAAE;cAC1CzD,IAAI,CAACqD,QAAQ,GAAG,KAAK;YACvB;UACF;QACF;;QAEA;QACA;QACA;QACA,IAAI;UACF,IAAI7D,IAAI,CAACY,qBAAqB,EAAE;YAC9B;YACAZ,IAAI,CAACiC,EAAE,CAACiC,IAAI,CAAClB,IAAI,CAAC;UACpB,CAAC,MAAM;YACLhD,IAAI,CAACiC,EAAE,CAACiC,IAAI,CAAClB,IAAI,EAAExC,IAAI,CAAC;UAC1B;QACF,CAAC,CAAC,OAAOL,CAAC,EAAE;UACVP,KAAK,CAAC,uCAAuC,CAAC;QAChD;QAEA,EAAE0D,KAAK,IAAIa,IAAI,CAAC,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC,EAAEf,OAAO,CAACI,CAAC,CAAC,CAAC;EAChB;EAEA,SAASW,IAAIA,CAAA,EAAI;IACfnE,IAAI,CAACmC,IAAI,CAAC,OAAO,CAAC;;IAElB;IACA;IACAiC,UAAU,CAAC,YAAY;MACrBpE,IAAI,CAACqD,QAAQ,GAAG,IAAI;MACpBrD,IAAI,CAACmC,IAAI,CAAC,OAAO,CAAC;IACpB,CAAC,EAAE,CAAC,CAAC;EACP;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA5B,EAAE,CAACS,SAAS,CAAC4B,OAAO,GAAG,YAAY;EACjCtD,SAAS,CAAC0B,SAAS,CAAC4B,OAAO,CAAC7B,IAAI,CAAC,IAAI,CAAC;AACxC,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEAR,EAAE,CAACS,SAAS,CAACqD,OAAO,GAAG,YAAY;EACjC,IAAI,OAAO,IAAI,CAACpC,EAAE,KAAK,WAAW,EAAE;IAClC,IAAI,CAACA,EAAE,CAACqC,KAAK,CAAC,CAAC;EACjB;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA/D,EAAE,CAACS,SAAS,CAACI,GAAG,GAAG,YAAY;EAC7B,IAAImD,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,CAAC,CAAC;EAC5B,IAAIC,MAAM,GAAG,IAAI,CAACC,MAAM,GAAG,KAAK,GAAG,IAAI;EACvC,IAAIC,IAAI,GAAG,EAAE;;EAEb;EACA,IAAI,IAAI,CAACA,IAAI,KAAM,KAAK,KAAKF,MAAM,IAAIG,MAAM,CAAC,IAAI,CAACD,IAAI,CAAC,KAAK,GAAG,IAC7D,IAAI,KAAKF,MAAM,IAAIG,MAAM,CAAC,IAAI,CAACD,IAAI,CAAC,KAAK,EAAG,CAAC,EAAE;IAChDA,IAAI,GAAG,GAAG,GAAG,IAAI,CAACA,IAAI;EACxB;;EAEA;EACA,IAAI,IAAI,CAACE,iBAAiB,EAAE;IAC1BL,KAAK,CAAC,IAAI,CAACM,cAAc,CAAC,GAAGlF,KAAK,CAAC,CAAC;EACtC;;EAEA;EACA,IAAI,CAAC,IAAI,CAACe,cAAc,EAAE;IACxB6D,KAAK,CAACO,GAAG,GAAG,CAAC;EACf;EAEAP,KAAK,GAAG9E,OAAO,CAACsF,MAAM,CAACR,KAAK,CAAC;;EAE7B;EACA,IAAIA,KAAK,CAAChB,MAAM,EAAE;IAChBgB,KAAK,GAAG,GAAG,GAAGA,KAAK;EACrB;EAEA,IAAIS,IAAI,GAAG,IAAI,CAACC,QAAQ,CAACC,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;EAC5C,OAAOV,MAAM,GAAG,KAAK,IAAIQ,IAAI,GAAG,GAAG,GAAG,IAAI,CAACC,QAAQ,GAAG,GAAG,GAAG,IAAI,CAACA,QAAQ,CAAC,GAAGP,IAAI,GAAG,IAAI,CAACS,IAAI,GAAGZ,KAAK;AACvG,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAEAhE,EAAE,CAACS,SAAS,CAACG,KAAK,GAAG,YAAY;EAC/B,OAAO,CAAC,CAACf,aAAa,IAAI,EAAE,cAAc,IAAIA,aAAa,IAAI,IAAI,CAACa,IAAI,KAAKV,EAAE,CAACS,SAAS,CAACC,IAAI,CAAC;AACjG,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}