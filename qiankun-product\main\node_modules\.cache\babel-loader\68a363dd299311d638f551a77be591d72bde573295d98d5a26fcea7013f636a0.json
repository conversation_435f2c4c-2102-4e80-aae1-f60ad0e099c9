{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalGroupQr\"\n};\nvar _hoisted_2 = {\n  class: \"GlobalGroupQrInfo\"\n};\nvar _hoisted_3 = {\n  class: \"GlobalGroupQrName\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_image, {\n    src: $setup.imgUrl($setup.groupInfo.groupImg),\n    fit: \"cover\",\n    draggable: \"false\"\n  }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_3, _toDisplayString($setup.groupInfo.groupName), 1 /* TEXT */)]), _createVNode($setup[\"QrcodeVue\"], {\n    value: $setup.groupQrCode,\n    size: 220\n  }, null, 8 /* PROPS */, [\"value\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_image", "src", "$setup", "imgUrl", "groupInfo", "groupImg", "fit", "draggable", "_hoisted_3", "_toDisplayString", "groupName", "value", "groupQrCode", "size"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalChat\\components\\GlobalGroupQr\\GlobalGroupQr.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalGroupQr\">\r\n    <div class=\"GlobalGroupQrInfo\">\r\n      <el-image :src=\"imgUrl(groupInfo.groupImg)\" fit=\"cover\" draggable=\"false\" />\r\n      <div class=\"GlobalGroupQrName\">{{ groupInfo.groupName }}</div>\r\n    </div>\r\n    <qrcode-vue :value=\"groupQrCode\" :size=\"220\" />\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalGroupQr' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, computed } from 'vue'\r\nimport { user, readConfig } from 'common/js/system_var.js'\r\nimport QrcodeVue from 'qrcode.vue'\r\nconst props = defineProps({ infoId: { type: String, default: '' } })\r\nconst groupInfo = ref({})\r\nconst groupQrCode = computed(() => `${readConfig.value.appOnlyHeader}|groupQr|${props.infoId}|${user.value.areaId}`)\r\nconst imgUrl = url => url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg')\r\n\r\nconst chatGroupInfo = async () => {\r\n  const { data } = await api.chatGroupInfo({ detailId: props.infoId })\r\n  groupInfo.value = data\r\n}\r\nonMounted(() => {\r\n  chatGroupInfo()\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalGroupQr {\r\n  width: 420px;\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  flex-direction: column;\r\n  padding: 40px;\r\n\r\n  .GlobalGroupQrInfo {\r\n    width: 252px;\r\n    display: flex;\r\n    align-items: center;\r\n    flex-direction: column;\r\n    padding: 0 0 20px 0;\r\n\r\n    .zy-el-image {\r\n      width: 52px;\r\n      height: 52px;\r\n    }\r\n\r\n    .GlobalGroupQrName {\r\n      width: 100%;\r\n      font-size: 16px;\r\n      line-height: 24px;\r\n      font-weight: bold;\r\n      text-align: center;\r\n      padding-top: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAmB;;EAEvBA,KAAK,EAAC;AAAmB;;;uBAHlCC,mBAAA,CAMM,OANNC,UAMM,GALJC,mBAAA,CAGM,OAHNC,UAGM,GAFJC,YAAA,CAA4EC,mBAAA;IAAjEC,GAAG,EAAEC,MAAA,CAAAC,MAAM,CAACD,MAAA,CAAAE,SAAS,CAACC,QAAQ;IAAGC,GAAG,EAAC,OAAO;IAACC,SAAS,EAAC;oCAClEV,mBAAA,CAA8D,OAA9DW,UAA8D,EAAAC,gBAAA,CAA5BP,MAAA,CAAAE,SAAS,CAACM,SAAS,iB,GAEvDX,YAAA,CAA+CG,MAAA;IAAlCS,KAAK,EAAET,MAAA,CAAAU,WAAW;IAAGC,IAAI,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}