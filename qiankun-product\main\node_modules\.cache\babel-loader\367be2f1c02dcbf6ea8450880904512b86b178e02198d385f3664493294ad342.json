{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"EditPassWord\"\n};\nvar _hoisted_2 = {\n  key: 0,\n  class: \"EditPassWordTips\"\n};\nvar _hoisted_3 = {\n  key: 1,\n  class: \"globalFormButton\"\n};\nvar _hoisted_4 = {\n  key: 2,\n  class: \"globalFormButtonConstraint\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_WarningFilled = _resolveComponent(\"WarningFilled\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [$setup.props.type ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_WarningFilled)];\n        }),\n        _: 1 /* STABLE */\n      }), _createTextVNode(\"系统检测到您的账号密码是弱密码，存在安全隐患，\" + _toDisplayString($setup.props.type === 'yes' ? '请您修改密码后再使用！' : '建议您修改密码！'), 1 /* TEXT */)])) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_form_item, {\n        label: \"旧密码\",\n        prop: \"oldPassword\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.oldPassword,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.oldPassword = $event;\n            }),\n            placeholder: \"请输入旧密码\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_form_item, {\n        label: `新密码（${$setup.regexName}）`,\n        prop: \"newPassword\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.newPassword,\n            \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n              return $setup.form.newPassword = $event;\n            }),\n            placeholder: \"请输入新密码\",\n            \"show-password\": \"\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"label\"]), _createVNode(_component_el_form_item, {\n        label: \"确认新密码\",\n        prop: \"verifyPassword\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            type: \"verifyPassword\",\n            modelValue: $setup.form.verifyPassword,\n            \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n              return $setup.form.verifyPassword = $event;\n            }),\n            placeholder: \"确认新密码\",\n            \"show-password\": \"\",\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])];\n        }),\n        _: 1 /* STABLE */\n      }), !$setup.props.type || $setup.props.type === 'no' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[3] || (_cache[3] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[5] || (_cache[5] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[6] || (_cache[6] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])) : _createCommentVNode(\"v-if\", true), $setup.props.type === 'yes' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[4] || (_cache[4] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[7] || (_cache[7] = [_createTextVNode(\"提交\")]);\n        }),\n        _: 1 /* STABLE */\n      })])) : _createCommentVNode(\"v-if\", true)];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "props", "type", "_hoisted_2", "_component_el_icon", "_component_WarningFilled", "_", "_createTextVNode", "_toDisplayString", "_createCommentVNode", "_component_el_form_item", "label", "prop", "_component_el_input", "modelValue", "oldPassword", "_cache", "$event", "placeholder", "clearable", "regexName", "newPassword", "verifyPassword", "_hoisted_3", "_component_el_button", "onClick", "submitForm", "formRef", "resetForm", "_hoisted_4"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LayoutContainer\\components\\EditPassWord.vue"], "sourcesContent": ["<template>\r\n  <div class=\"EditPassWord\">\r\n    <el-form ref=\"formRef\" :model=\"form\" :rules=\"rules\" inline label-position=\"top\" class=\"globalForm\">\r\n      <div class=\"EditPassWordTips\" v-if=\"props.type\">\r\n        <el-icon>\r\n          <WarningFilled />\r\n        </el-icon>系统检测到您的账号密码是弱密码，存在安全隐患，{{ props.type === 'yes' ? '请您修改密码后再使用！' : '建议您修改密码！' }}\r\n      </div>\r\n      <el-form-item label=\"旧密码\" prop=\"oldPassword\" class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.oldPassword\" placeholder=\"请输入旧密码\" clearable />\r\n      </el-form-item>\r\n      <el-form-item :label=\"`新密码（${regexName}）`\" prop=\"newPassword\" class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.newPassword\" placeholder=\"请输入新密码\" show-password clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"确认新密码\" prop=\"verifyPassword\" class=\"globalFormTitle\">\r\n        <el-input type=\"verifyPassword\" v-model=\"form.verifyPassword\" placeholder=\"确认新密码\" show-password clearable />\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\" v-if=\"!props.type || props.type === 'no'\">\r\n        <el-button type=\"primary\" @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n      <div class=\"globalFormButtonConstraint\" v-if=\"props.type === 'yes'\">\r\n        <el-button type=\"primary\" @click=\"submitForm(formRef)\">提交</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'EditPassWord' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport utils from 'common/js/utils.js'\r\nimport { user } from 'common/js/system_var.js'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ type: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst regexName = ref('')\r\nconst form = reactive({ oldPassword: '', newPassword: '', verifyPassword: '' })\r\n\r\nconst verifyPassword = (rule, value, callback) => {\r\n  if (value === '') {\r\n    callback(new Error('请再次输入新密码'))\r\n  } else if (value !== form.newPassword) {\r\n    callback(new Error('两次输入密码不一致!'))\r\n  } else {\r\n    callback()\r\n  }\r\n}\r\nconst rules = reactive({\r\n  oldPassword: [{ required: true, message: '请输入旧密码', trigger: ['blur', 'change'] }],\r\n  newPassword: [{ required: true, message: '请输入新密码', trigger: ['blur', 'change'] }],\r\n  verifyPassword: [{ validator: verifyPassword, required: true, trigger: ['blur', 'change'] }]\r\n})\r\n\r\nonMounted(() => {\r\n  if (props.type === 'no') { sessionStorage.removeItem('verify') }\r\n  passwordStrengthMessage()\r\n})\r\n\r\nconst passwordStrengthMessage = async () => {\r\n  const { data } = await api.passwordStrengthMessage()\r\n  regexName.value = data\r\n}\r\n\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { passwordStrengthChecker() } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\n\r\nconst passwordStrengthChecker = async () => {\r\n  const { code } = await api.passwordStrengthChecker({ password: utils.encrypt(form.newPassword, new Date().getTime(), '1') })\r\n  if (code === 200) { globalJson() }\r\n}\r\n\r\nconst globalJson = async () => {\r\n  const { code } = await api.globalJson('/userAccount/editPwd', {\r\n    accountId: user.value.accountId,\r\n    oldPassword: utils.encrypt(form.oldPassword, new Date().getTime(), '1'),\r\n    newPassword: utils.encrypt(form.newPassword, new Date().getTime(), '1')\r\n  })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '修改成功，请重新登录！' })\r\n    emit('callback', true)\r\n  }\r\n}\r\nconst resetForm = () => { emit('callback', false) }\r\n</script>\r\n<style lang=\"scss\">\r\n.EditPassWord {\r\n  width: 680px;\r\n\r\n  .EditPassWordTips {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    font-size: var(--zy-name-font-size);\r\n    line-height: var(--zy-name-line-height);\r\n    padding-bottom: var(--zy-distance-one);\r\n\r\n    .zy-el-icon {\r\n      font-size: 18px;\r\n      margin-right: 6px;\r\n      color: var(--el-color-warning);\r\n    }\r\n  }\r\n\r\n  .globalFormButtonConstraint {\r\n\r\n    width: 100%;\r\n    padding-right: var(--zy-distance-two);\r\n\r\n    .zy-el-button {\r\n      width: 100%;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAc;;EAD3BC,GAAA;EAGWD,KAAK,EAAC;;;EAHjBC,GAAA;EAiBWD,KAAK,EAAC;;;EAjBjBC,GAAA;EAqBWD,KAAK,EAAC;;;;;;;;;uBApBfE,mBAAA,CAwBM,OAxBNC,UAwBM,GAvBJC,YAAA,CAsBUC,kBAAA;IAtBDC,GAAG,EAAC,SAAS;IAAEC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IAAGC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IAAEC,MAAM,EAAN,EAAM;IAAC,gBAAc,EAAC,KAAK;IAACX,KAAK,EAAC;;IAF1FY,OAAA,EAAAC,QAAA,CAGM;MAAA,OAIM,CAJ8BL,MAAA,CAAAM,KAAK,CAACC,IAAI,I,cAA9Cb,mBAAA,CAIM,OAJNc,UAIM,GAHJZ,YAAA,CAEUa,kBAAA;QANlBL,OAAA,EAAAC,QAAA,CAKU;UAAA,OAAiB,CAAjBT,YAAA,CAAiBc,wBAAA,E;;QAL3BC,CAAA;UAAAC,gBAAA,CAMkB,yBAAuB,GAAAC,gBAAA,CAAGb,MAAA,CAAAM,KAAK,CAACC,IAAI,wD,KANtDO,mBAAA,gBAQMlB,YAAA,CAEemB,uBAAA;QAFDC,KAAK,EAAC,KAAK;QAACC,IAAI,EAAC,aAAa;QAACzB,KAAK,EAAC;;QARzDY,OAAA,EAAAC,QAAA,CASQ;UAAA,OAAsE,CAAtET,YAAA,CAAsEsB,mBAAA;YAT9EC,UAAA,EAS2BnB,MAAA,CAAAC,IAAI,CAACmB,WAAW;YAT3C,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAS2BtB,MAAA,CAAAC,IAAI,CAACmB,WAAW,GAAAE,MAAA;YAAA;YAAEC,WAAW,EAAC,QAAQ;YAACC,SAAS,EAAT;;;QATlEb,CAAA;UAWMf,YAAA,CAEemB,uBAAA;QAFAC,KAAK,SAAShB,MAAA,CAAAyB,SAAS;QAAKR,IAAI,EAAC,aAAa;QAACzB,KAAK,EAAC;;QAX1EY,OAAA,EAAAC,QAAA,CAYQ;UAAA,OAAoF,CAApFT,YAAA,CAAoFsB,mBAAA;YAZ5FC,UAAA,EAY2BnB,MAAA,CAAAC,IAAI,CAACyB,WAAW;YAZ3C,uBAAAL,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAY2BtB,MAAA,CAAAC,IAAI,CAACyB,WAAW,GAAAJ,MAAA;YAAA;YAAEC,WAAW,EAAC,QAAQ;YAAC,eAAa,EAAb,EAAa;YAACC,SAAS,EAAT;;;QAZhFb,CAAA;oCAcMf,YAAA,CAEemB,uBAAA;QAFDC,KAAK,EAAC,OAAO;QAACC,IAAI,EAAC,gBAAgB;QAACzB,KAAK,EAAC;;QAd9DY,OAAA,EAAAC,QAAA,CAeQ;UAAA,OAA4G,CAA5GT,YAAA,CAA4GsB,mBAAA;YAAlGX,IAAI,EAAC,gBAAgB;YAfvCY,UAAA,EAeiDnB,MAAA,CAAAC,IAAI,CAAC0B,cAAc;YAfpE,uBAAAN,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAeiDtB,MAAA,CAAAC,IAAI,CAAC0B,cAAc,GAAAL,MAAA;YAAA;YAAEC,WAAW,EAAC,OAAO;YAAC,eAAa,EAAb,EAAa;YAACC,SAAS,EAAT;;;QAfxGb,CAAA;WAiB2CX,MAAA,CAAAM,KAAK,CAACC,IAAI,IAAIP,MAAA,CAAAM,KAAK,CAACC,IAAI,a,cAA7Db,mBAAA,CAGM,OAHNkC,UAGM,GAFJhC,YAAA,CAAqEiC,oBAAA;QAA1DtB,IAAI,EAAC,SAAS;QAAEuB,OAAK,EAAAT,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEtB,MAAA,CAAA+B,UAAU,CAAC/B,MAAA,CAAAgC,OAAO;QAAA;;QAlB5D5B,OAAA,EAAAC,QAAA,CAkB+D;UAAA,OAAEgB,MAAA,QAAAA,MAAA,OAlBjET,gBAAA,CAkB+D,IAAE,E;;QAlBjED,CAAA;UAmBQf,YAAA,CAA4CiC,oBAAA;QAAhCC,OAAK,EAAE9B,MAAA,CAAAiC;MAAS;QAnBpC7B,OAAA,EAAAC,QAAA,CAmBsC;UAAA,OAAEgB,MAAA,QAAAA,MAAA,OAnBxCT,gBAAA,CAmBsC,IAAE,E;;QAnBxCD,CAAA;cAAAG,mBAAA,gBAqBoDd,MAAA,CAAAM,KAAK,CAACC,IAAI,c,cAAxDb,mBAAA,CAEM,OAFNwC,UAEM,GADJtC,YAAA,CAAqEiC,oBAAA;QAA1DtB,IAAI,EAAC,SAAS;QAAEuB,OAAK,EAAAT,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEtB,MAAA,CAAA+B,UAAU,CAAC/B,MAAA,CAAAgC,OAAO;QAAA;;QAtB5D5B,OAAA,EAAAC,QAAA,CAsB+D;UAAA,OAAEgB,MAAA,QAAAA,MAAA,OAtBjET,gBAAA,CAsB+D,IAAE,E;;QAtBjED,CAAA;cAAAG,mBAAA,e;;IAAAH,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}