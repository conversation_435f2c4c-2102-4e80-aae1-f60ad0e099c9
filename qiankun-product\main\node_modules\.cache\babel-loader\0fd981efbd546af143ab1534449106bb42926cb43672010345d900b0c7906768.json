{"ast": null, "code": "// Process *this* and _that_\n//\n'use strict';\n\n// Insert each marker as a separate text token, and add it to delimiter list\n//\nmodule.exports.tokenize = function emphasis(state, silent) {\n  var i,\n    scanned,\n    token,\n    start = state.pos,\n    marker = state.src.charCodeAt(start);\n  if (silent) {\n    return false;\n  }\n  if (marker !== 0x5F /* _ */ && marker !== 0x2A /* * */) {\n    return false;\n  }\n  scanned = state.scanDelims(state.pos, marker === 0x2A);\n  for (i = 0; i < scanned.length; i++) {\n    token = state.push('text', '', 0);\n    token.content = String.fromCharCode(marker);\n    state.delimiters.push({\n      // Char code of the starting marker (number).\n      //\n      marker: marker,\n      // Total length of these series of delimiters.\n      //\n      length: scanned.length,\n      // A position of the token this delimiter corresponds to.\n      //\n      token: state.tokens.length - 1,\n      // If this delimiter is matched as a valid opener, `end` will be\n      // equal to its position, otherwise it's `-1`.\n      //\n      end: -1,\n      // Boolean flags that determine if this delimiter could open or close\n      // an emphasis.\n      //\n      open: scanned.can_open,\n      close: scanned.can_close\n    });\n  }\n  state.pos += scanned.length;\n  return true;\n};\nfunction postProcess(state, delimiters) {\n  var i,\n    startDelim,\n    endDelim,\n    token,\n    ch,\n    isStrong,\n    max = delimiters.length;\n  for (i = max - 1; i >= 0; i--) {\n    startDelim = delimiters[i];\n    if (startDelim.marker !== 0x5F /* _ */ && startDelim.marker !== 0x2A /* * */) {\n      continue;\n    }\n\n    // Process only opening markers\n    if (startDelim.end === -1) {\n      continue;\n    }\n    endDelim = delimiters[startDelim.end];\n\n    // If the previous delimiter has the same marker and is adjacent to this one,\n    // merge those into one strong delimiter.\n    //\n    // `<em><em>whatever</em></em>` -> `<strong>whatever</strong>`\n    //\n    isStrong = i > 0 && delimiters[i - 1].end === startDelim.end + 1 &&\n    // check that first two markers match and adjacent\n    delimiters[i - 1].marker === startDelim.marker && delimiters[i - 1].token === startDelim.token - 1 &&\n    // check that last two markers are adjacent (we can safely assume they match)\n    delimiters[startDelim.end + 1].token === endDelim.token + 1;\n    ch = String.fromCharCode(startDelim.marker);\n    token = state.tokens[startDelim.token];\n    token.type = isStrong ? 'strong_open' : 'em_open';\n    token.tag = isStrong ? 'strong' : 'em';\n    token.nesting = 1;\n    token.markup = isStrong ? ch + ch : ch;\n    token.content = '';\n    token = state.tokens[endDelim.token];\n    token.type = isStrong ? 'strong_close' : 'em_close';\n    token.tag = isStrong ? 'strong' : 'em';\n    token.nesting = -1;\n    token.markup = isStrong ? ch + ch : ch;\n    token.content = '';\n    if (isStrong) {\n      state.tokens[delimiters[i - 1].token].content = '';\n      state.tokens[delimiters[startDelim.end + 1].token].content = '';\n      i--;\n    }\n  }\n}\n\n// Walk through delimiter list and replace text tokens with tags\n//\nmodule.exports.postProcess = function emphasis(state) {\n  var curr,\n    tokens_meta = state.tokens_meta,\n    max = state.tokens_meta.length;\n  postProcess(state, state.delimiters);\n  for (curr = 0; curr < max; curr++) {\n    if (tokens_meta[curr] && tokens_meta[curr].delimiters) {\n      postProcess(state, tokens_meta[curr].delimiters);\n    }\n  }\n};", "map": {"version": 3, "names": ["module", "exports", "tokenize", "emphasis", "state", "silent", "i", "scanned", "token", "start", "pos", "marker", "src", "charCodeAt", "scanDelims", "length", "push", "content", "String", "fromCharCode", "delimiters", "tokens", "end", "open", "can_open", "close", "can_close", "postProcess", "start<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "ch", "isStrong", "max", "type", "tag", "nesting", "markup", "curr", "tokens_meta"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_inline/emphasis.js"], "sourcesContent": ["// Process *this* and _that_\n//\n'use strict';\n\n\n// Insert each marker as a separate text token, and add it to delimiter list\n//\nmodule.exports.tokenize = function emphasis(state, silent) {\n  var i, scanned, token,\n      start = state.pos,\n      marker = state.src.charCodeAt(start);\n\n  if (silent) { return false; }\n\n  if (marker !== 0x5F /* _ */ && marker !== 0x2A /* * */) { return false; }\n\n  scanned = state.scanDelims(state.pos, marker === 0x2A);\n\n  for (i = 0; i < scanned.length; i++) {\n    token         = state.push('text', '', 0);\n    token.content = String.fromCharCode(marker);\n\n    state.delimiters.push({\n      // Char code of the starting marker (number).\n      //\n      marker: marker,\n\n      // Total length of these series of delimiters.\n      //\n      length: scanned.length,\n\n      // A position of the token this delimiter corresponds to.\n      //\n      token:  state.tokens.length - 1,\n\n      // If this delimiter is matched as a valid opener, `end` will be\n      // equal to its position, otherwise it's `-1`.\n      //\n      end:    -1,\n\n      // Boolean flags that determine if this delimiter could open or close\n      // an emphasis.\n      //\n      open:   scanned.can_open,\n      close:  scanned.can_close\n    });\n  }\n\n  state.pos += scanned.length;\n\n  return true;\n};\n\n\nfunction postProcess(state, delimiters) {\n  var i,\n      startDelim,\n      endDelim,\n      token,\n      ch,\n      isStrong,\n      max = delimiters.length;\n\n  for (i = max - 1; i >= 0; i--) {\n    startDelim = delimiters[i];\n\n    if (startDelim.marker !== 0x5F/* _ */ && startDelim.marker !== 0x2A/* * */) {\n      continue;\n    }\n\n    // Process only opening markers\n    if (startDelim.end === -1) {\n      continue;\n    }\n\n    endDelim = delimiters[startDelim.end];\n\n    // If the previous delimiter has the same marker and is adjacent to this one,\n    // merge those into one strong delimiter.\n    //\n    // `<em><em>whatever</em></em>` -> `<strong>whatever</strong>`\n    //\n    isStrong = i > 0 &&\n               delimiters[i - 1].end === startDelim.end + 1 &&\n               // check that first two markers match and adjacent\n               delimiters[i - 1].marker === startDelim.marker &&\n               delimiters[i - 1].token === startDelim.token - 1 &&\n               // check that last two markers are adjacent (we can safely assume they match)\n               delimiters[startDelim.end + 1].token === endDelim.token + 1;\n\n    ch = String.fromCharCode(startDelim.marker);\n\n    token         = state.tokens[startDelim.token];\n    token.type    = isStrong ? 'strong_open' : 'em_open';\n    token.tag     = isStrong ? 'strong' : 'em';\n    token.nesting = 1;\n    token.markup  = isStrong ? ch + ch : ch;\n    token.content = '';\n\n    token         = state.tokens[endDelim.token];\n    token.type    = isStrong ? 'strong_close' : 'em_close';\n    token.tag     = isStrong ? 'strong' : 'em';\n    token.nesting = -1;\n    token.markup  = isStrong ? ch + ch : ch;\n    token.content = '';\n\n    if (isStrong) {\n      state.tokens[delimiters[i - 1].token].content = '';\n      state.tokens[delimiters[startDelim.end + 1].token].content = '';\n      i--;\n    }\n  }\n}\n\n\n// Walk through delimiter list and replace text tokens with tags\n//\nmodule.exports.postProcess = function emphasis(state) {\n  var curr,\n      tokens_meta = state.tokens_meta,\n      max = state.tokens_meta.length;\n\n  postProcess(state, state.delimiters);\n\n  for (curr = 0; curr < max; curr++) {\n    if (tokens_meta[curr] && tokens_meta[curr].delimiters) {\n      postProcess(state, tokens_meta[curr].delimiters);\n    }\n  }\n};\n"], "mappings": "AAAA;AACA;AACA,YAAY;;AAGZ;AACA;AACAA,MAAM,CAACC,OAAO,CAACC,QAAQ,GAAG,SAASC,QAAQA,CAACC,KAAK,EAAEC,MAAM,EAAE;EACzD,IAAIC,CAAC;IAAEC,OAAO;IAAEC,KAAK;IACjBC,KAAK,GAAGL,KAAK,CAACM,GAAG;IACjBC,MAAM,GAAGP,KAAK,CAACQ,GAAG,CAACC,UAAU,CAACJ,KAAK,CAAC;EAExC,IAAIJ,MAAM,EAAE;IAAE,OAAO,KAAK;EAAE;EAE5B,IAAIM,MAAM,KAAK,IAAI,CAAC,WAAWA,MAAM,KAAK,IAAI,CAAC,SAAS;IAAE,OAAO,KAAK;EAAE;EAExEJ,OAAO,GAAGH,KAAK,CAACU,UAAU,CAACV,KAAK,CAACM,GAAG,EAAEC,MAAM,KAAK,IAAI,CAAC;EAEtD,KAAKL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,OAAO,CAACQ,MAAM,EAAET,CAAC,EAAE,EAAE;IACnCE,KAAK,GAAWJ,KAAK,CAACY,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;IACzCR,KAAK,CAACS,OAAO,GAAGC,MAAM,CAACC,YAAY,CAACR,MAAM,CAAC;IAE3CP,KAAK,CAACgB,UAAU,CAACJ,IAAI,CAAC;MACpB;MACA;MACAL,MAAM,EAAEA,MAAM;MAEd;MACA;MACAI,MAAM,EAAER,OAAO,CAACQ,MAAM;MAEtB;MACA;MACAP,KAAK,EAAGJ,KAAK,CAACiB,MAAM,CAACN,MAAM,GAAG,CAAC;MAE/B;MACA;MACA;MACAO,GAAG,EAAK,CAAC,CAAC;MAEV;MACA;MACA;MACAC,IAAI,EAAIhB,OAAO,CAACiB,QAAQ;MACxBC,KAAK,EAAGlB,OAAO,CAACmB;IAClB,CAAC,CAAC;EACJ;EAEAtB,KAAK,CAACM,GAAG,IAAIH,OAAO,CAACQ,MAAM;EAE3B,OAAO,IAAI;AACb,CAAC;AAGD,SAASY,WAAWA,CAACvB,KAAK,EAAEgB,UAAU,EAAE;EACtC,IAAId,CAAC;IACDsB,UAAU;IACVC,QAAQ;IACRrB,KAAK;IACLsB,EAAE;IACFC,QAAQ;IACRC,GAAG,GAAGZ,UAAU,CAACL,MAAM;EAE3B,KAAKT,CAAC,GAAG0B,GAAG,GAAG,CAAC,EAAE1B,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC7BsB,UAAU,GAAGR,UAAU,CAACd,CAAC,CAAC;IAE1B,IAAIsB,UAAU,CAACjB,MAAM,KAAK,IAAI,YAAWiB,UAAU,CAACjB,MAAM,KAAK,IAAI,UAAS;MAC1E;IACF;;IAEA;IACA,IAAIiB,UAAU,CAACN,GAAG,KAAK,CAAC,CAAC,EAAE;MACzB;IACF;IAEAO,QAAQ,GAAGT,UAAU,CAACQ,UAAU,CAACN,GAAG,CAAC;;IAErC;IACA;IACA;IACA;IACA;IACAS,QAAQ,GAAGzB,CAAC,GAAG,CAAC,IACLc,UAAU,CAACd,CAAC,GAAG,CAAC,CAAC,CAACgB,GAAG,KAAKM,UAAU,CAACN,GAAG,GAAG,CAAC;IAC5C;IACAF,UAAU,CAACd,CAAC,GAAG,CAAC,CAAC,CAACK,MAAM,KAAKiB,UAAU,CAACjB,MAAM,IAC9CS,UAAU,CAACd,CAAC,GAAG,CAAC,CAAC,CAACE,KAAK,KAAKoB,UAAU,CAACpB,KAAK,GAAG,CAAC;IAChD;IACAY,UAAU,CAACQ,UAAU,CAACN,GAAG,GAAG,CAAC,CAAC,CAACd,KAAK,KAAKqB,QAAQ,CAACrB,KAAK,GAAG,CAAC;IAEtEsB,EAAE,GAAGZ,MAAM,CAACC,YAAY,CAACS,UAAU,CAACjB,MAAM,CAAC;IAE3CH,KAAK,GAAWJ,KAAK,CAACiB,MAAM,CAACO,UAAU,CAACpB,KAAK,CAAC;IAC9CA,KAAK,CAACyB,IAAI,GAAMF,QAAQ,GAAG,aAAa,GAAG,SAAS;IACpDvB,KAAK,CAAC0B,GAAG,GAAOH,QAAQ,GAAG,QAAQ,GAAG,IAAI;IAC1CvB,KAAK,CAAC2B,OAAO,GAAG,CAAC;IACjB3B,KAAK,CAAC4B,MAAM,GAAIL,QAAQ,GAAGD,EAAE,GAAGA,EAAE,GAAGA,EAAE;IACvCtB,KAAK,CAACS,OAAO,GAAG,EAAE;IAElBT,KAAK,GAAWJ,KAAK,CAACiB,MAAM,CAACQ,QAAQ,CAACrB,KAAK,CAAC;IAC5CA,KAAK,CAACyB,IAAI,GAAMF,QAAQ,GAAG,cAAc,GAAG,UAAU;IACtDvB,KAAK,CAAC0B,GAAG,GAAOH,QAAQ,GAAG,QAAQ,GAAG,IAAI;IAC1CvB,KAAK,CAAC2B,OAAO,GAAG,CAAC,CAAC;IAClB3B,KAAK,CAAC4B,MAAM,GAAIL,QAAQ,GAAGD,EAAE,GAAGA,EAAE,GAAGA,EAAE;IACvCtB,KAAK,CAACS,OAAO,GAAG,EAAE;IAElB,IAAIc,QAAQ,EAAE;MACZ3B,KAAK,CAACiB,MAAM,CAACD,UAAU,CAACd,CAAC,GAAG,CAAC,CAAC,CAACE,KAAK,CAAC,CAACS,OAAO,GAAG,EAAE;MAClDb,KAAK,CAACiB,MAAM,CAACD,UAAU,CAACQ,UAAU,CAACN,GAAG,GAAG,CAAC,CAAC,CAACd,KAAK,CAAC,CAACS,OAAO,GAAG,EAAE;MAC/DX,CAAC,EAAE;IACL;EACF;AACF;;AAGA;AACA;AACAN,MAAM,CAACC,OAAO,CAAC0B,WAAW,GAAG,SAASxB,QAAQA,CAACC,KAAK,EAAE;EACpD,IAAIiC,IAAI;IACJC,WAAW,GAAGlC,KAAK,CAACkC,WAAW;IAC/BN,GAAG,GAAG5B,KAAK,CAACkC,WAAW,CAACvB,MAAM;EAElCY,WAAW,CAACvB,KAAK,EAAEA,KAAK,CAACgB,UAAU,CAAC;EAEpC,KAAKiB,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,GAAG,EAAEK,IAAI,EAAE,EAAE;IACjC,IAAIC,WAAW,CAACD,IAAI,CAAC,IAAIC,WAAW,CAACD,IAAI,CAAC,CAACjB,UAAU,EAAE;MACrDO,WAAW,CAACvB,KAAK,EAAEkC,WAAW,CAACD,IAAI,CAAC,CAACjB,UAAU,CAAC;IAClD;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}