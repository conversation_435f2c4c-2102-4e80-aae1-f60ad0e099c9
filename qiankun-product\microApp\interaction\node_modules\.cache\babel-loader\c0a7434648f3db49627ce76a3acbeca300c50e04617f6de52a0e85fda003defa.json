{"ast": null, "code": "import { resolveComponent as _resolveComponent, with<PERSON><PERSON>s as _withKeys, createVNode as _createVNode, withCtx as _withCtx, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"CommunicationPartner\"\n};\nvar _hoisted_2 = {\n  class: \"globalTable\"\n};\nvar _hoisted_3 = {\n  class: \"globalPagination\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_date_picker = _resolveComponent(\"el-date-picker\");\n  var _component_xyl_search_button = _resolveComponent(\"xyl-search-button\");\n  var _component_el_table_column = _resolveComponent(\"el-table-column\");\n  var _component_el_table = _resolveComponent(\"el-table\");\n  var _component_el_pagination = _resolveComponent(\"el-pagination\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_xyl_search_button, {\n    onQueryClick: $setup.handleQuery,\n    onResetClick: $setup.handleReset,\n    onHandleButton: $setup.handleButton,\n    buttonList: $setup.buttonList\n  }, {\n    search: _withCtx(function () {\n      return [_createVNode(_component_el_input, {\n        modelValue: $setup.searchForm.object,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.searchForm.object = $event;\n        }),\n        placeholder: \"请输入交流对象\",\n        onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_input, {\n        modelValue: $setup.searchForm.way,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n          return $setup.searchForm.way = $event;\n        }),\n        placeholder: \"请输入交流方式\",\n        onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_input, {\n        modelValue: $setup.searchForm.content,\n        \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n          return $setup.searchForm.content = $event;\n        }),\n        placeholder: \"请输入交流内容\",\n        onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_date_picker, {\n        modelValue: $setup.searchForm.time,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n          return $setup.searchForm.time = $event;\n        }),\n        type: \"datetime\",\n        placeholder: \"请选择交流时间\",\n        format: \"YYYY-MM-DD HH:mm:ss\",\n        \"value-format\": \"YYYY-MM-DD HH:mm:ss\",\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"]), _createVNode(_component_el_input, {\n        modelValue: $setup.searchForm.poster,\n        \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n          return $setup.searchForm.poster = $event;\n        }),\n        placeholder: \"请输入发帖人\",\n        onKeyup: _withKeys($setup.handleQuery, [\"enter\"]),\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])];\n    }),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_table, {\n    ref: \"tableRef\",\n    \"row-key\": \"id\",\n    data: $setup.paginatedTableData,\n    onSelect: $setup.handleTableSelect,\n    onSelectAll: $setup.handleTableSelect\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_table_column, {\n        type: \"selection\",\n        \"reserve-selection\": \"\",\n        width: \"60\",\n        fixed: \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"交流对象\",\n        prop: \"object\",\n        \"min-width\": \"120\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"交流方式\",\n        prop: \"way\",\n        \"min-width\": \"120\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"交流内容\",\n        prop: \"content\",\n        \"min-width\": \"300\",\n        \"show-overflow-tooltip\": \"\"\n      }), _createVNode(_component_el_table_column, {\n        label: \"交流时间\",\n        \"min-width\": \"120\"\n      }, {\n        default: _withCtx(function (scope) {\n          return [_createTextVNode(_toDisplayString($setup.format(scope.row.time)), 1 /* TEXT */)];\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_table_column, {\n        label: \"发帖人\",\n        prop: \"poster\",\n        \"min-width\": \"120\",\n        \"show-overflow-tooltip\": \"\"\n      })];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"data\", \"onSelect\", \"onSelectAll\"])]), _createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_pagination, {\n    currentPage: $setup.currentPage,\n    \"onUpdate:currentPage\": _cache[5] || (_cache[5] = function ($event) {\n      return $setup.currentPage = $event;\n    }),\n    \"page-size\": $setup.pageSize,\n    \"onUpdate:pageSize\": _cache[6] || (_cache[6] = function ($event) {\n      return $setup.pageSize = $event;\n    }),\n    \"page-sizes\": $setup.pageSizes,\n    layout: \"total, sizes, prev, pager, next, jumper\",\n    onSizeChange: $setup.handleSizeChange,\n    onCurrentChange: $setup.handleCurrentChange,\n    total: $setup.filteredTableData.length,\n    background: \"\"\n  }, null, 8 /* PROPS */, [\"currentPage\", \"page-size\", \"total\"])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_xyl_search_button", "onQueryClick", "$setup", "handleQuery", "onResetClick", "handleReset", "onHandleButton", "handleButton", "buttonList", "search", "_withCtx", "_component_el_input", "modelValue", "searchForm", "object", "_cache", "$event", "placeholder", "onKeyup", "_with<PERSON><PERSON><PERSON>", "clearable", "way", "content", "_component_el_date_picker", "time", "type", "format", "poster", "_", "_createElementVNode", "_hoisted_2", "_component_el_table", "ref", "data", "paginatedTableData", "onSelect", "handleTableSelect", "onSelectAll", "default", "_component_el_table_column", "width", "fixed", "label", "prop", "scope", "_createTextVNode", "_toDisplayString", "row", "_hoisted_3", "_component_el_pagination", "currentPage", "pageSize", "pageSizes", "layout", "onSizeChange", "handleSizeChange", "onCurrentChange", "handleCurrentChange", "total", "filteredTableData", "length", "background"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\CommunicationPartner\\CommunicationPartner.vue"], "sourcesContent": ["<template>\r\n  <div class=\"CommunicationPartner\">\r\n    <xyl-search-button @queryClick=\"handleQuery\" @resetClick=\"handleReset\" @handleButton=\"handleButton\"\r\n      :buttonList=\"buttonList\">\r\n      <template #search>\r\n        <el-input v-model=\"searchForm.object\" placeholder=\"请输入交流对象\" @keyup.enter=\"handleQuery\" clearable />\r\n        <el-input v-model=\"searchForm.way\" placeholder=\"请输入交流方式\" @keyup.enter=\"handleQuery\" clearable />\r\n        <el-input v-model=\"searchForm.content\" placeholder=\"请输入交流内容\" @keyup.enter=\"handleQuery\" clearable />\r\n        <el-date-picker v-model=\"searchForm.time\" type=\"datetime\" placeholder=\"请选择交流时间\" format=\"YYYY-MM-DD HH:mm:ss\"\r\n          value-format=\"YYYY-MM-DD HH:mm:ss\" clearable />\r\n        <el-input v-model=\"searchForm.poster\" placeholder=\"请输入发帖人\" @keyup.enter=\"handleQuery\" clearable />\r\n      </template>\r\n    </xyl-search-button>\r\n    <div class=\"globalTable\">\r\n      <el-table ref=\"tableRef\" row-key=\"id\" :data=\"paginatedTableData\" @select=\"handleTableSelect\"\r\n        @select-all=\"handleTableSelect\">\r\n        <el-table-column type=\"selection\" reserve-selection width=\"60\" fixed />\r\n        <el-table-column label=\"交流对象\" prop=\"object\" min-width=\"120\" show-overflow-tooltip />\r\n        <el-table-column label=\"交流方式\" prop=\"way\" min-width=\"120\" show-overflow-tooltip></el-table-column>\r\n        <el-table-column label=\"交流内容\" prop=\"content\" min-width=\"300\" show-overflow-tooltip />\r\n        <el-table-column label=\"交流时间\" min-width=\"120\">\r\n          <template #default=\"scope\">{{ format(scope.row.time) }}</template>\r\n        </el-table-column>\r\n        <el-table-column label=\"发帖人\" prop=\"poster\" min-width=\"120\" show-overflow-tooltip />\r\n      </el-table>\r\n    </div>\r\n    <div class=\"globalPagination\">\r\n      <el-pagination v-model:currentPage=\"currentPage\" v-model:page-size=\"pageSize\" :page-sizes=\"pageSizes\"\r\n        layout=\"total, sizes, prev, pager, next, jumper\" @size-change=\"handleSizeChange\"\r\n        @current-change=\"handleCurrentChange\" :total=\"filteredTableData.length\" background />\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'CommunicationPartner' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onActivated, computed } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { GlobalTable } from 'common/js/GlobalTable.js'\r\n\r\nconst buttonList = [\r\n  // { id: 'del', name: '移出收藏', type: 'primary', has: '' },\r\n  // { id: 'move', name: '移动', type: 'primary', has: '' }\r\n]\r\n\r\n// 筛选表单数据\r\nconst searchForm = ref({\r\n  object: '',\r\n  way: '',\r\n  content: '',\r\n  time: '',\r\n  poster: ''\r\n})\r\n\r\n// 分页相关\r\nconst currentPage = ref(1)\r\nconst pageSize = ref(10)\r\nconst pageSizes = [10, 20, 50, 100]\r\n\r\nconst collectSource = ref([])\r\nconst collectSourceObj = ref({})\r\nconst {\r\n  // keyword,\r\n  tableRef,\r\n  // totals,\r\n  // pageNo,\r\n  // pageSize,\r\n  // pageSizes,\r\n  // tableData,\r\n  handleTableSelect,\r\n  handleDel\r\n} = GlobalTable({ tableApi: 'favoriteList', delApi: 'favoriteDel' })\r\n\r\nconst tableData = ref([\r\n  { id: '1', object: '张弛', way: '个人', content: '关于 \"关于提升西安夜间旅游经济的建议\" 的提案是否可以约个时间交流下', time: 1749616222000, poster: '市文化和旅游局' },\r\n  { id: '2', object: '市文化和旅游局', way: '个人', content: '好的，您看下午方便我给您电话联系', time: 1749619222000, poster: '张弛' },\r\n  { id: '3', object: '张弛', way: '个人', content: '好的，我给你打电话', time: 1749621162000, poster: '市文化和旅游局' },\r\n  { id: '4', object: '【40】提案办理协商群', way: '群组', content: '关于\"关于西安市环境保护问题\"提案的建议策略不是很明确，可否帮忙解答下', time: 1750139562000, poster: '环境局' }\r\n])\r\n\r\n// 过滤后的表格数据\r\nconst filteredTableData = computed(() => {\r\n  return tableData.value.filter(item => {\r\n    // 交流对象筛选\r\n    if (searchForm.value.object && !item.object.includes(searchForm.value.object)) {\r\n      return false\r\n    }\r\n\r\n    // 交流方式筛选\r\n    if (searchForm.value.way && !item.way.includes(searchForm.value.way)) {\r\n      return false\r\n    }\r\n\r\n    // 交流内容筛选\r\n    if (searchForm.value.content && !item.content.includes(searchForm.value.content)) {\r\n      return false\r\n    }\r\n\r\n    // 交流时间筛选\r\n    if (searchForm.value.time) {\r\n      const searchTime = new Date(searchForm.value.time).getTime()\r\n      const itemTime = item.time\r\n      // 比较日期部分（忽略时分秒）\r\n      const searchDate = new Date(searchTime).toDateString()\r\n      const itemDate = new Date(itemTime).toDateString()\r\n      if (searchDate !== itemDate) {\r\n        return false\r\n      }\r\n    }\r\n\r\n    // 发帖人筛选\r\n    if (searchForm.value.poster && !item.poster.includes(searchForm.value.poster)) {\r\n      return false\r\n    }\r\n\r\n    return true\r\n  })\r\n})\r\n\r\n// 分页后的表格数据\r\nconst paginatedTableData = computed(() => {\r\n  const start = (currentPage.value - 1) * pageSize.value\r\n  const end = start + pageSize.value\r\n  return filteredTableData.value.slice(start, end)\r\n})\r\n\r\nonActivated(() => {\r\n  dictionaryData()\r\n})\r\n\r\nconst dictionaryData = async () => {\r\n  const res = await api.dictionaryData({ dictCodes: ['collect_source'] })\r\n  var { data } = res\r\n  var collect_source = {}\r\n  for (let index = 0; index < data.collect_source.length; index++) {\r\n    const item = data.collect_source[index]\r\n    collect_source[item.key] = item.name\r\n  }\r\n  collectSource.value = data.collect_source\r\n  collectSourceObj.value = collect_source\r\n}\r\n\r\nconst handleReset = () => {\r\n  // 重置所有筛选条件\r\n  searchForm.value = {\r\n    object: '',\r\n    way: '',\r\n    content: '',\r\n    time: '',\r\n    poster: ''\r\n  }\r\n  // 重置分页\r\n  currentPage.value = 1\r\n  handleQuery()\r\n}\r\n\r\n// 分页事件处理\r\nconst handleSizeChange = (size) => {\r\n  pageSize.value = size\r\n  currentPage.value = 1\r\n}\r\n\r\nconst handleCurrentChange = (page) => {\r\n  currentPage.value = page\r\n}\r\n\r\n// 自定义查询方法\r\nconst handleQuery = () => {\r\n  // 重置到第一页\r\n  currentPage.value = 1\r\n}\r\n\r\nconst handleButton = (isType) => {\r\n  switch (isType) {\r\n    case 'del':\r\n      handleDel('收藏', '移出')\r\n      break\r\n    case 'move':\r\n      // if (tableDataArray.value.length) { moveShow.value = true } else { ElMessage({ type: 'warning', message: '请至少选择一条数据' }) }\r\n      break\r\n    default:\r\n      break\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.CommunicationPartner {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n\r\n  .xyl-search-button {\r\n    .xyl-button {\r\n      width: calc(100% - 1200px);\r\n    }\r\n\r\n    .xyl-search {\r\n      width: 1200px;\r\n\r\n      .zy-el-input {\r\n        margin-left: 20px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .globalTable {\r\n    width: 100%;\r\n    height: calc(100% - (var(--zy-height) + (var(--zy-distance-four) * 2) + 42px));\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAsB;;EAY1BA,KAAK,EAAC;AAAa;;EAanBA,KAAK,EAAC;AAAkB;;;;;;;;uBAzB/BC,mBAAA,CA8BM,OA9BNC,UA8BM,GA7BJC,YAAA,CAUoBC,4BAAA;IAVAC,YAAU,EAAEC,MAAA,CAAAC,WAAW;IAAGC,YAAU,EAAEF,MAAA,CAAAG,WAAW;IAAGC,cAAY,EAAEJ,MAAA,CAAAK,YAAY;IAC/FC,UAAU,EAAEN,MAAA,CAAAM;;IACFC,MAAM,EAAAC,QAAA,CACf;MAAA,OAAmG,CAAnGX,YAAA,CAAmGY,mBAAA;QAL3GC,UAAA,EAK2BV,MAAA,CAAAW,UAAU,CAACC,MAAM;QAL5C,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAK2Bd,MAAA,CAAAW,UAAU,CAACC,MAAM,GAAAE,MAAA;QAAA;QAAEC,WAAW,EAAC,SAAS;QAAEC,OAAK,EAL1EC,SAAA,CAKkFjB,MAAA,CAAAC,WAAW;QAAEiB,SAAS,EAAT;+CACvFrB,YAAA,CAAgGY,mBAAA;QANxGC,UAAA,EAM2BV,MAAA,CAAAW,UAAU,CAACQ,GAAG;QANzC,uBAAAN,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAM2Bd,MAAA,CAAAW,UAAU,CAACQ,GAAG,GAAAL,MAAA;QAAA;QAAEC,WAAW,EAAC,SAAS;QAAEC,OAAK,EANvEC,SAAA,CAM+EjB,MAAA,CAAAC,WAAW;QAAEiB,SAAS,EAAT;+CACpFrB,YAAA,CAAoGY,mBAAA;QAP5GC,UAAA,EAO2BV,MAAA,CAAAW,UAAU,CAACS,OAAO;QAP7C,uBAAAP,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAO2Bd,MAAA,CAAAW,UAAU,CAACS,OAAO,GAAAN,MAAA;QAAA;QAAEC,WAAW,EAAC,SAAS;QAAEC,OAAK,EAP3EC,SAAA,CAOmFjB,MAAA,CAAAC,WAAW;QAAEiB,SAAS,EAAT;+CACxFrB,YAAA,CACiDwB,yBAAA;QATzDX,UAAA,EAQiCV,MAAA,CAAAW,UAAU,CAACW,IAAI;QARhD,uBAAAT,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAQiCd,MAAA,CAAAW,UAAU,CAACW,IAAI,GAAAR,MAAA;QAAA;QAAES,IAAI,EAAC,UAAU;QAACR,WAAW,EAAC,SAAS;QAACS,MAAM,EAAC,qBAAqB;QAC1G,cAAY,EAAC,qBAAqB;QAACN,SAAS,EAAT;+CACrCrB,YAAA,CAAkGY,mBAAA;QAV1GC,UAAA,EAU2BV,MAAA,CAAAW,UAAU,CAACc,MAAM;QAV5C,uBAAAZ,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAU2Bd,MAAA,CAAAW,UAAU,CAACc,MAAM,GAAAX,MAAA;QAAA;QAAEC,WAAW,EAAC,QAAQ;QAAEC,OAAK,EAVzEC,SAAA,CAUiFjB,MAAA,CAAAC,WAAW;QAAEiB,SAAS,EAAT;;;IAV9FQ,CAAA;MAaIC,mBAAA,CAYM,OAZNC,UAYM,GAXJ/B,YAAA,CAUWgC,mBAAA;IAVDC,GAAG,EAAC,UAAU;IAAC,SAAO,EAAC,IAAI;IAAEC,IAAI,EAAE/B,MAAA,CAAAgC,kBAAkB;IAAGC,QAAM,EAAEjC,MAAA,CAAAkC,iBAAiB;IACxFC,WAAU,EAAEnC,MAAA,CAAAkC;;IAfrBE,OAAA,EAAA5B,QAAA,CAgBQ;MAAA,OAAuE,CAAvEX,YAAA,CAAuEwC,0BAAA;QAAtDd,IAAI,EAAC,WAAW;QAAC,mBAAiB,EAAjB,EAAiB;QAACe,KAAK,EAAC,IAAI;QAACC,KAAK,EAAL;UAC/D1C,YAAA,CAAoFwC,0BAAA;QAAnEG,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC,QAAQ;QAAC,WAAS,EAAC,KAAK;QAAC,uBAAqB,EAArB;UAC5D5C,YAAA,CAAiGwC,0BAAA;QAAhFG,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC,KAAK;QAAC,WAAS,EAAC,KAAK;QAAC,uBAAqB,EAArB;UACzD5C,YAAA,CAAqFwC,0BAAA;QAApEG,KAAK,EAAC,MAAM;QAACC,IAAI,EAAC,SAAS;QAAC,WAAS,EAAC,KAAK;QAAC,uBAAqB,EAArB;UAC7D5C,YAAA,CAEkBwC,0BAAA;QAFDG,KAAK,EAAC,MAAM;QAAC,WAAS,EAAC;;QAC3BJ,OAAO,EAAA5B,QAAA,CAAS,UAA4BkC,KAA9B;UAAA,QArBnCC,gBAAA,CAAAC,gBAAA,CAqBwC5C,MAAA,CAAAwB,MAAM,CAACkB,KAAK,CAACG,GAAG,CAACvB,IAAI,kB;;QArB7DI,CAAA;UAuBQ7B,YAAA,CAAmFwC,0BAAA;QAAlEG,KAAK,EAAC,KAAK;QAACC,IAAI,EAAC,QAAQ;QAAC,WAAS,EAAC,KAAK;QAAC,uBAAqB,EAArB;;;IAvBnEf,CAAA;4DA0BIC,mBAAA,CAIM,OAJNmB,UAIM,GAHJjD,YAAA,CAEuFkD,wBAAA;IAFhEC,WAAW,EAAEhD,MAAA,CAAAgD,WAAW;IA3BrD,wBAAAnC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA2B0Cd,MAAA,CAAAgD,WAAW,GAAAlC,MAAA;IAAA;IAAU,WAAS,EAAEd,MAAA,CAAAiD,QAAQ;IA3BlF,qBAAApC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OA2B0Ed,MAAA,CAAAiD,QAAQ,GAAAnC,MAAA;IAAA;IAAG,YAAU,EAAEd,MAAA,CAAAkD,SAAS;IAClGC,MAAM,EAAC,yCAAyC;IAAEC,YAAW,EAAEpD,MAAA,CAAAqD,gBAAgB;IAC9EC,eAAc,EAAEtD,MAAA,CAAAuD,mBAAmB;IAAGC,KAAK,EAAExD,MAAA,CAAAyD,iBAAiB,CAACC,MAAM;IAAEC,UAAU,EAAV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}