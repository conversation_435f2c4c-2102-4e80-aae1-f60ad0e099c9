{"ast": null, "code": "var _excluded = [\"onMessage\", \"onOpen\", \"onClose\", \"onError\", \"timeout\", \"retryOnError\", \"retryDelay\", \"retryMax\"];\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _objectWithoutProperties(e, t) { if (null == e) return {}; var o, r, i = _objectWithoutPropertiesLoose(e, t); if (Object.getOwnPropertySymbols) { var s = Object.getOwnPropertySymbols(e); for (r = 0; r < s.length; r++) o = s[r], t.includes(o) || {}.propertyIsEnumerable.call(e, o) && (i[o] = e[o]); } return i; }\nfunction _objectWithoutPropertiesLoose(r, e) { if (null == r) return {}; var t = {}; for (var n in r) if ({}.hasOwnProperty.call(r, n)) { if (e.includes(n)) continue; t[n] = r[n]; } return t; }\nimport config from '../config';\nimport router from '@/router';\nimport store from '@/store';\nimport { fetchEventSource } from '@microsoft/fetch-event-source';\nvar headersConfig = function headersConfig() {\n  var _currentRoute$meta, _store$getters$getRea;\n  var headers = {\n    'Content-Type': 'application/json'\n  };\n  var token = sessionStorage.getItem('token') || 'basic enlzb2Z0Onp5c29mdCo2MDc5'; // 用户token\n  var microAppToken = sessionStorage.getItem('microAppToken') || 'basic YW5vbnltb3VzOmFub255bW91cyo2MDc5'; // 用户token\n  var AreaId = sessionStorage.getItem('AreaId') || ''; // 用户地区\n  headers['u-login-areaId'] = AreaId;\n  var currentRoute = router.currentRoute.value;\n  if (((_currentRoute$meta = currentRoute.meta) === null || _currentRoute$meta === void 0 ? void 0 : _currentRoute$meta.moduleName) === 'PUBLIC') {\n    headers.Authorization = microAppToken;\n    headers['u-terminal'] = 'PUBLIC';\n  } else {\n    headers.Authorization = token;\n    headers['u-terminal'] = 'PC';\n  }\n  headers['u-system-name'] = (_store$getters$getRea = store.getters.getReadOpenConfig) === null || _store$getters$getRea === void 0 ? void 0 : _store$getters$getRea.systemPlatform;\n  return headers;\n};\nvar http_stream = function http_stream(url) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var onMessage = options.onMessage,\n    onOpen = options.onOpen,\n    onClose = options.onClose,\n    onError = options.onError,\n    _options$timeout = options.timeout,\n    timeout = _options$timeout === void 0 ? 0 : _options$timeout,\n    _options$retryOnError = options.retryOnError,\n    retryOnError = _options$retryOnError === void 0 ? true : _options$retryOnError,\n    _options$retryDelay = options.retryDelay,\n    retryDelay = _options$retryDelay === void 0 ? 1000 : _options$retryDelay,\n    _options$retryMax = options.retryMax,\n    retryMax = _options$retryMax === void 0 ? 0 : _options$retryMax,\n    fetchOptions = _objectWithoutProperties(options, _excluded);\n  var finalOptions = _objectSpread({\n    method: 'POST',\n    headers: headersConfig()\n  }, fetchOptions);\n  var controller = new AbortController();\n  var timeoutId = null;\n  var retryCount = 0;\n  // 启动流式请求\n  var _executeRequest = function executeRequest() {\n    return new Promise(function (resolve, reject) {\n      // 设置请求超时\n      if (timeout > 0) {\n        timeoutId = setTimeout(function () {\n          reject(new Error(`请求超时，超过 ${timeout}ms`));\n          controller.abort();\n        }, timeout);\n      }\n      fetchEventSource(config.API_URL + url, _objectSpread(_objectSpread({}, finalOptions), {}, {\n        signal: controller.signal,\n        openWhenHidden: true,\n        retryOnError,\n        retryDelay,\n        retryMax,\n        onopen(response) {\n          return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n            var contentType, data;\n            return _regeneratorRuntime().wrap(function _callee$(_context) {\n              while (1) switch (_context.prev = _context.next) {\n                case 0:\n                  // 检查是否是JSON响应\n                  contentType = response.headers.get('content-type');\n                  if (!(contentType && contentType.includes('application/json'))) {\n                    _context.next = 9;\n                    break;\n                  }\n                  _context.next = 4;\n                  return response.json();\n                case 4:\n                  data = _context.sent;\n                  if (!(data.code && data.code !== 200)) {\n                    _context.next = 7;\n                    break;\n                  }\n                  throw new Error('收到JSON格式的错误响应');\n                case 7:\n                  _context.next = 13;\n                  break;\n                case 9:\n                  if (onOpen) onOpen(response);\n                  if (!response.ok) {\n                    _context.next = 12;\n                    break;\n                  }\n                  return _context.abrupt(\"return\");\n                case 12:\n                  throw new Error(`流式接口请求失败: ${response.statusText}`);\n                case 13:\n                case \"end\":\n                  return _context.stop();\n              }\n            }, _callee);\n          }))();\n        },\n        onmessage(event) {\n          try {\n            if (onMessage) onMessage(event);\n          } catch (err) {\n            console.error('消息处理错误:', err);\n            if (onError) onError(err);\n          }\n        },\n        onclose() {\n          if (timeoutId) clearTimeout(timeoutId);\n          if (onClose) onClose();\n          resolve();\n        },\n        onerror(err) {\n          if (timeoutId) clearTimeout(timeoutId);\n          if (onError) onError(err);\n          if (retryCount < retryMax && retryOnError) {\n            retryCount++;\n            console.log(`正在进行第 ${retryCount} 次重试...`);\n            setTimeout(function () {\n              _executeRequest();\n            }, retryDelay);\n          } else {\n            throw reject(err);\n          }\n        }\n      }));\n    });\n  };\n  var request = {\n    abort: function abort() {\n      return controller.abort();\n    },\n    retry: function retry() {\n      retryCount++;\n      console.log(`手动触发第 ${retryCount} 次重试...`);\n      return _executeRequest();\n    }\n  };\n  request.promise = _executeRequest();\n  return request;\n};\nexport default http_stream;", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "ownKeys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "_objectSpread", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_toPrimitive", "toPrimitive", "String", "Number", "_objectWithoutProperties", "_objectWithoutPropertiesLoose", "includes", "propertyIsEnumerable", "config", "router", "store", "fetchEventSource", "headersConfig", "_currentRoute$meta", "_store$getters$getRea", "headers", "token", "sessionStorage", "getItem", "microAppToken", "AreaId", "currentRoute", "meta", "moduleName", "Authorization", "getters", "getReadOpenConfig", "systemPlatform", "http_stream", "url", "options", "undefined", "onMessage", "onOpen", "onClose", "onError", "_options$timeout", "timeout", "_options$retryOnError", "retryOnError", "_options$retryDelay", "retry<PERSON><PERSON><PERSON>", "_options$retryMax", "retryMax", "fetchOptions", "_excluded", "finalOptions", "controller", "AbortController", "timeoutId", "retryCount", "executeRequest", "reject", "setTimeout", "abort", "API_URL", "signal", "openWhenHidden", "onopen", "response", "_callee", "contentType", "data", "_callee$", "_context", "get", "json", "code", "ok", "statusText", "onmessage", "event", "err", "console", "error", "onclose", "clearTimeout", "onerror", "log", "request", "retry", "promise"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/http/stream.js"], "sourcesContent": ["import config from '../config'\r\nimport router from '@/router'\r\nimport store from '@/store'\r\nimport { fetchEventSource } from '@microsoft/fetch-event-source'\r\n\r\nconst headersConfig = () => {\r\n  const headers = { 'Content-Type': 'application/json' }\r\n  const token = sessionStorage.getItem('token') || 'basic enlzb2Z0Onp5c29mdCo2MDc5' // 用户token\r\n  const microAppToken = sessionStorage.getItem('microAppToken') || 'basic YW5vbnltb3VzOmFub255bW91cyo2MDc5' // 用户token\r\n  const AreaId = sessionStorage.getItem('AreaId') || '' // 用户地区\r\n  headers['u-login-areaId'] = AreaId\r\n  const currentRoute = router.currentRoute.value\r\n  if (currentRoute.meta?.moduleName === 'PUBLIC') {\r\n    headers.Authorization = microAppToken\r\n    headers['u-terminal'] = 'PUBLIC'\r\n  } else {\r\n    headers.Authorization = token\r\n    headers['u-terminal'] = 'PC'\r\n  }\r\n  headers['u-system-name'] = store.getters.getReadOpenConfig?.systemPlatform\r\n  return headers\r\n}\r\nconst http_stream = (url, options = {}) => {\r\n  const {\r\n    onMessage,\r\n    onOpen,\r\n    onClose,\r\n    onError,\r\n    timeout = 0,\r\n    retryOnError = true,\r\n    retryDelay = 1000,\r\n    retryMax = 0,\r\n    ...fetchOptions\r\n  } = options\r\n\r\n  const finalOptions = {\r\n    method: 'POST',\r\n    headers: headersConfig(),\r\n    ...fetchOptions\r\n  }\r\n\r\n  const controller = new AbortController()\r\n  let timeoutId = null\r\n  let retryCount = 0\r\n  // 启动流式请求\r\n  const executeRequest = () =>\r\n    new Promise((resolve, reject) => {\r\n      // 设置请求超时\r\n      if (timeout > 0) {\r\n        timeoutId = setTimeout(() => {\r\n          reject(new Error(`请求超时，超过 ${timeout}ms`))\r\n          controller.abort()\r\n        }, timeout)\r\n      }\r\n      fetchEventSource(config.API_URL + url, {\r\n        ...finalOptions,\r\n        signal: controller.signal,\r\n        openWhenHidden: true,\r\n        retryOnError,\r\n        retryDelay,\r\n        retryMax,\r\n        async onopen(response) {\r\n          // 检查是否是JSON响应\r\n          const contentType = response.headers.get('content-type')\r\n          if (contentType && contentType.includes('application/json')) {\r\n            const data = await response.json()\r\n            if (data.code && data.code !== 200) {\r\n              throw new Error('收到JSON格式的错误响应')\r\n            }\r\n          } else {\r\n            if (onOpen) onOpen(response)\r\n            if (response.ok) return\r\n            throw new Error(`流式接口请求失败: ${response.statusText}`)\r\n          }\r\n        },\r\n        onmessage(event) {\r\n          try {\r\n            if (onMessage) onMessage(event)\r\n          } catch (err) {\r\n            console.error('消息处理错误:', err)\r\n            if (onError) onError(err)\r\n          }\r\n        },\r\n        onclose() {\r\n          if (timeoutId) clearTimeout(timeoutId)\r\n          if (onClose) onClose()\r\n          resolve()\r\n        },\r\n        onerror(err) {\r\n          if (timeoutId) clearTimeout(timeoutId)\r\n          if (onError) onError(err)\r\n          if (retryCount < retryMax && retryOnError) {\r\n            retryCount++\r\n            console.log(`正在进行第 ${retryCount} 次重试...`)\r\n            setTimeout(() => {\r\n              executeRequest()\r\n            }, retryDelay)\r\n          } else {\r\n            throw reject(err)\r\n          }\r\n        }\r\n      })\r\n    })\r\n  const request = {\r\n    abort: () => controller.abort(),\r\n    retry: () => {\r\n      retryCount++\r\n      console.log(`手动触发第 ${retryCount} 次重试...`)\r\n      return executeRequest()\r\n    }\r\n  }\r\n  request.promise = executeRequest()\r\n  return request\r\n}\r\nexport default http_stream\r\n"], "mappings": ";+CACA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AAAA,SAAAE,QAAA7G,CAAA,EAAAE,CAAA,QAAAD,CAAA,GAAAE,MAAA,CAAAsF,IAAA,CAAAzF,CAAA,OAAAG,MAAA,CAAA2G,qBAAA,QAAAvG,CAAA,GAAAJ,MAAA,CAAA2G,qBAAA,CAAA9G,CAAA,GAAAE,CAAA,KAAAK,CAAA,GAAAA,CAAA,CAAAwG,MAAA,WAAA7G,CAAA,WAAAC,MAAA,CAAA6G,wBAAA,CAAAhH,CAAA,EAAAE,CAAA,EAAAiB,UAAA,OAAAlB,CAAA,CAAAwE,IAAA,CAAAiC,KAAA,CAAAzG,CAAA,EAAAM,CAAA,YAAAN,CAAA;AAAA,SAAAgH,cAAAjH,CAAA,aAAAE,CAAA,MAAAA,CAAA,GAAAuG,SAAA,CAAA3B,MAAA,EAAA5E,CAAA,UAAAD,CAAA,WAAAwG,SAAA,CAAAvG,CAAA,IAAAuG,SAAA,CAAAvG,CAAA,QAAAA,CAAA,OAAA2G,OAAA,CAAA1G,MAAA,CAAAF,CAAA,OAAA4C,OAAA,WAAA3C,CAAA,IAAAgH,eAAA,CAAAlH,CAAA,EAAAE,CAAA,EAAAD,CAAA,CAAAC,CAAA,SAAAC,MAAA,CAAAgH,yBAAA,GAAAhH,MAAA,CAAAiH,gBAAA,CAAApH,CAAA,EAAAG,MAAA,CAAAgH,yBAAA,CAAAlH,CAAA,KAAA4G,OAAA,CAAA1G,MAAA,CAAAF,CAAA,GAAA4C,OAAA,WAAA3C,CAAA,IAAAC,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,EAAAC,MAAA,CAAA6G,wBAAA,CAAA/G,CAAA,EAAAC,CAAA,iBAAAF,CAAA;AAAA,SAAAkH,gBAAAlH,CAAA,EAAAE,CAAA,EAAAD,CAAA,YAAAC,CAAA,GAAAmH,cAAA,CAAAnH,CAAA,MAAAF,CAAA,GAAAG,MAAA,CAAAK,cAAA,CAAAR,CAAA,EAAAE,CAAA,IAAAO,KAAA,EAAAR,CAAA,EAAAkB,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAArB,CAAA,CAAAE,CAAA,IAAAD,CAAA,EAAAD,CAAA;AAAA,SAAAqH,eAAApH,CAAA,QAAAS,CAAA,GAAA4G,YAAA,CAAArH,CAAA,uCAAAS,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAA4G,aAAArH,CAAA,EAAAC,CAAA,2BAAAD,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAD,CAAA,GAAAC,CAAA,CAAAU,MAAA,CAAA4G,WAAA,kBAAAvH,CAAA,QAAAU,CAAA,GAAAV,CAAA,CAAA8B,IAAA,CAAA7B,CAAA,EAAAC,CAAA,uCAAAQ,CAAA,SAAAA,CAAA,YAAAqD,SAAA,yEAAA7D,CAAA,GAAAsH,MAAA,GAAAC,MAAA,EAAAxH,CAAA;AAAA,SAAAyH,yBAAA1H,CAAA,EAAAC,CAAA,gBAAAD,CAAA,iBAAAO,CAAA,EAAAL,CAAA,EAAAQ,CAAA,GAAAiH,6BAAA,CAAA3H,CAAA,EAAAC,CAAA,OAAAE,MAAA,CAAA2G,qBAAA,QAAA5E,CAAA,GAAA/B,MAAA,CAAA2G,qBAAA,CAAA9G,CAAA,QAAAE,CAAA,MAAAA,CAAA,GAAAgC,CAAA,CAAA4C,MAAA,EAAA5E,CAAA,IAAAK,CAAA,GAAA2B,CAAA,CAAAhC,CAAA,GAAAD,CAAA,CAAA2H,QAAA,CAAArH,CAAA,QAAAsH,oBAAA,CAAA/F,IAAA,CAAA9B,CAAA,EAAAO,CAAA,MAAAG,CAAA,CAAAH,CAAA,IAAAP,CAAA,CAAAO,CAAA,aAAAG,CAAA;AAAA,SAAAiH,8BAAAzH,CAAA,EAAAF,CAAA,gBAAAE,CAAA,iBAAAD,CAAA,gBAAAI,CAAA,IAAAH,CAAA,SAAAI,cAAA,CAAAwB,IAAA,CAAA5B,CAAA,EAAAG,CAAA,SAAAL,CAAA,CAAA4H,QAAA,CAAAvH,CAAA,aAAAJ,CAAA,CAAAI,CAAA,IAAAH,CAAA,CAAAG,CAAA,YAAAJ,CAAA;AADA,OAAO6H,MAAM,MAAM,WAAW;AAC9B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,gBAAgB,QAAQ,+BAA+B;AAEhE,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;EAAA,IAAAC,kBAAA,EAAAC,qBAAA;EAC1B,IAAMC,OAAO,GAAG;IAAE,cAAc,EAAE;EAAmB,CAAC;EACtD,IAAMC,KAAK,GAAGC,cAAc,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,gCAAgC,EAAC;EAClF,IAAMC,aAAa,GAAGF,cAAc,CAACC,OAAO,CAAC,eAAe,CAAC,IAAI,wCAAwC,EAAC;EAC1G,IAAME,MAAM,GAAGH,cAAc,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAC;EACtDH,OAAO,CAAC,gBAAgB,CAAC,GAAGK,MAAM;EAClC,IAAMC,YAAY,GAAGZ,MAAM,CAACY,YAAY,CAAClI,KAAK;EAC9C,IAAI,EAAA0H,kBAAA,GAAAQ,YAAY,CAACC,IAAI,cAAAT,kBAAA,uBAAjBA,kBAAA,CAAmBU,UAAU,MAAK,QAAQ,EAAE;IAC9CR,OAAO,CAACS,aAAa,GAAGL,aAAa;IACrCJ,OAAO,CAAC,YAAY,CAAC,GAAG,QAAQ;EAClC,CAAC,MAAM;IACLA,OAAO,CAACS,aAAa,GAAGR,KAAK;IAC7BD,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI;EAC9B;EACAA,OAAO,CAAC,eAAe,CAAC,IAAAD,qBAAA,GAAGJ,KAAK,CAACe,OAAO,CAACC,iBAAiB,cAAAZ,qBAAA,uBAA/BA,qBAAA,CAAiCa,cAAc;EAC1E,OAAOZ,OAAO;AAChB,CAAC;AACD,IAAMa,WAAW,GAAG,SAAdA,WAAWA,CAAIC,GAAG,EAAmB;EAAA,IAAjBC,OAAO,GAAA3C,SAAA,CAAA3B,MAAA,QAAA2B,SAAA,QAAA4C,SAAA,GAAA5C,SAAA,MAAG,CAAC,CAAC;EACpC,IACE6C,SAAS,GASPF,OAAO,CATTE,SAAS;IACTC,MAAM,GAQJH,OAAO,CARTG,MAAM;IACNC,OAAO,GAOLJ,OAAO,CAPTI,OAAO;IACPC,OAAO,GAMLL,OAAO,CANTK,OAAO;IAAAC,gBAAA,GAMLN,OAAO,CALTO,OAAO;IAAPA,OAAO,GAAAD,gBAAA,cAAG,CAAC,GAAAA,gBAAA;IAAAE,qBAAA,GAKTR,OAAO,CAJTS,YAAY;IAAZA,YAAY,GAAAD,qBAAA,cAAG,IAAI,GAAAA,qBAAA;IAAAE,mBAAA,GAIjBV,OAAO,CAHTW,UAAU;IAAVA,UAAU,GAAAD,mBAAA,cAAG,IAAI,GAAAA,mBAAA;IAAAE,iBAAA,GAGfZ,OAAO,CAFTa,QAAQ;IAARA,QAAQ,GAAAD,iBAAA,cAAG,CAAC,GAAAA,iBAAA;IACTE,YAAY,GAAAxC,wBAAA,CACb0B,OAAO,EAAAe,SAAA;EAEX,IAAMC,YAAY,GAAAnD,aAAA;IAChB1D,MAAM,EAAE,MAAM;IACd8E,OAAO,EAAEH,aAAa,CAAC;EAAC,GACrBgC,YAAY,CAChB;EAED,IAAMG,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;EACxC,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAIC,UAAU,GAAG,CAAC;EAClB;EACA,IAAMC,eAAc,GAAG,SAAjBA,cAAcA,CAAA;IAAA,OAClB,IAAIjF,OAAO,CAAC,UAACvC,OAAO,EAAEyH,MAAM,EAAK;MAC/B;MACA,IAAIf,OAAO,GAAG,CAAC,EAAE;QACfY,SAAS,GAAGI,UAAU,CAAC,YAAM;UAC3BD,MAAM,CAAC,IAAIrH,KAAK,CAAC,WAAWsG,OAAO,IAAI,CAAC,CAAC;UACzCU,UAAU,CAACO,KAAK,CAAC,CAAC;QACpB,CAAC,EAAEjB,OAAO,CAAC;MACb;MACA1B,gBAAgB,CAACH,MAAM,CAAC+C,OAAO,GAAG1B,GAAG,EAAAlC,aAAA,CAAAA,aAAA,KAChCmD,YAAY;QACfU,MAAM,EAAET,UAAU,CAACS,MAAM;QACzBC,cAAc,EAAE,IAAI;QACpBlB,YAAY;QACZE,UAAU;QACVE,QAAQ;QACFe,MAAMA,CAACC,QAAQ,EAAE;UAAA,OAAAzE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,UAAA+F,QAAA;YAAA,IAAAC,WAAA,EAAAC,IAAA;YAAA,OAAArL,mBAAA,GAAAuB,IAAA,UAAA+J,SAAAC,QAAA;cAAA,kBAAAA,QAAA,CAAA1F,IAAA,GAAA0F,QAAA,CAAArH,IAAA;gBAAA;kBACrB;kBACMkH,WAAW,GAAGF,QAAQ,CAAC5C,OAAO,CAACkD,GAAG,CAAC,cAAc,CAAC;kBAAA,MACpDJ,WAAW,IAAIA,WAAW,CAACvD,QAAQ,CAAC,kBAAkB,CAAC;oBAAA0D,QAAA,CAAArH,IAAA;oBAAA;kBAAA;kBAAAqH,QAAA,CAAArH,IAAA;kBAAA,OACtCgH,QAAQ,CAACO,IAAI,CAAC,CAAC;gBAAA;kBAA5BJ,IAAI,GAAAE,QAAA,CAAA5H,IAAA;kBAAA,MACN0H,IAAI,CAACK,IAAI,IAAIL,IAAI,CAACK,IAAI,KAAK,GAAG;oBAAAH,QAAA,CAAArH,IAAA;oBAAA;kBAAA;kBAAA,MAC1B,IAAIZ,KAAK,CAAC,eAAe,CAAC;gBAAA;kBAAAiI,QAAA,CAAArH,IAAA;kBAAA;gBAAA;kBAGlC,IAAIsF,MAAM,EAAEA,MAAM,CAAC0B,QAAQ,CAAC;kBAAA,KACxBA,QAAQ,CAACS,EAAE;oBAAAJ,QAAA,CAAArH,IAAA;oBAAA;kBAAA;kBAAA,OAAAqH,QAAA,CAAAzH,MAAA;gBAAA;kBAAA,MACT,IAAIR,KAAK,CAAC,aAAa4H,QAAQ,CAACU,UAAU,EAAE,CAAC;gBAAA;gBAAA;kBAAA,OAAAL,QAAA,CAAAvF,IAAA;cAAA;YAAA,GAAAmF,OAAA;UAAA;QAEvD,CAAC;QACDU,SAASA,CAACC,KAAK,EAAE;UACf,IAAI;YACF,IAAIvC,SAAS,EAAEA,SAAS,CAACuC,KAAK,CAAC;UACjC,CAAC,CAAC,OAAOC,GAAG,EAAE;YACZC,OAAO,CAACC,KAAK,CAAC,SAAS,EAAEF,GAAG,CAAC;YAC7B,IAAIrC,OAAO,EAAEA,OAAO,CAACqC,GAAG,CAAC;UAC3B;QACF,CAAC;QACDG,OAAOA,CAAA,EAAG;UACR,IAAI1B,SAAS,EAAE2B,YAAY,CAAC3B,SAAS,CAAC;UACtC,IAAIf,OAAO,EAAEA,OAAO,CAAC,CAAC;UACtBvG,OAAO,CAAC,CAAC;QACX,CAAC;QACDkJ,OAAOA,CAACL,GAAG,EAAE;UACX,IAAIvB,SAAS,EAAE2B,YAAY,CAAC3B,SAAS,CAAC;UACtC,IAAId,OAAO,EAAEA,OAAO,CAACqC,GAAG,CAAC;UACzB,IAAItB,UAAU,GAAGP,QAAQ,IAAIJ,YAAY,EAAE;YACzCW,UAAU,EAAE;YACZuB,OAAO,CAACK,GAAG,CAAC,SAAS5B,UAAU,SAAS,CAAC;YACzCG,UAAU,CAAC,YAAM;cACfF,eAAc,CAAC,CAAC;YAClB,CAAC,EAAEV,UAAU,CAAC;UAChB,CAAC,MAAM;YACL,MAAMW,MAAM,CAACoB,GAAG,CAAC;UACnB;QACF;MAAC,EACF,CAAC;IACJ,CAAC,CAAC;EAAA;EACJ,IAAMO,OAAO,GAAG;IACdzB,KAAK,EAAE,SAAPA,KAAKA,CAAA;MAAA,OAAQP,UAAU,CAACO,KAAK,CAAC,CAAC;IAAA;IAC/B0B,KAAK,EAAE,SAAPA,KAAKA,CAAA,EAAQ;MACX9B,UAAU,EAAE;MACZuB,OAAO,CAACK,GAAG,CAAC,SAAS5B,UAAU,SAAS,CAAC;MACzC,OAAOC,eAAc,CAAC,CAAC;IACzB;EACF,CAAC;EACD4B,OAAO,CAACE,OAAO,GAAG9B,eAAc,CAAC,CAAC;EAClC,OAAO4B,OAAO;AAChB,CAAC;AACD,eAAenD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}