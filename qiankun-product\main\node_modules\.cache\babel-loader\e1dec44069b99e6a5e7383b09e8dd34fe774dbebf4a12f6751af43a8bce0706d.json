{"ast": null, "code": "// Emoticons -> Emoji mapping.\n//\n// (!) Some patterns skipped, to avoid collisions\n// without increase matcher complicity. Than can change in future.\n//\n// Places to look for more emoticons info:\n//\n// - http://en.wikipedia.org/wiki/List_of_emoticons#Western\n// - https://github.com/wooorm/emoticon/blob/master/Support.md\n// - http://factoryjoe.com/projects/emoticons/\n//\n'use strict';\n\nmodule.exports = {\n  angry: ['>:(', '>:-('],\n  blush: [':\")', ':-\")'],\n  broken_heart: ['</3', '<\\\\3'],\n  // :\\ and :-\\ not used because of conflict with markdown escaping\n  confused: [':/', ':-/'],\n  // twemoji shows question\n  cry: [\":'(\", \":'-(\", ':,(', ':,-('],\n  frowning: [':(', ':-('],\n  heart: ['<3'],\n  imp: [']:(', ']:-('],\n  innocent: ['o:)', 'O:)', 'o:-)', 'O:-)', '0:)', '0:-)'],\n  joy: [\":')\", \":'-)\", ':,)', ':,-)', \":'D\", \":'-D\", ':,D', ':,-D'],\n  kissing: [':*', ':-*'],\n  laughing: ['x-)', 'X-)'],\n  neutral_face: [':|', ':-|'],\n  open_mouth: [':o', ':-o', ':O', ':-O'],\n  rage: [':@', ':-@'],\n  smile: [':D', ':-D'],\n  smiley: [':)', ':-)'],\n  smiling_imp: [']:)', ']:-)'],\n  sob: [\":,'(\", \":,'-(\", ';(', ';-('],\n  stuck_out_tongue: [':P', ':-P'],\n  sunglasses: ['8-)', 'B-)'],\n  sweat: [',:(', ',:-('],\n  sweat_smile: [',:)', ',:-)'],\n  unamused: [':s', ':-S', ':z', ':-Z', ':$', ':-$'],\n  wink: [';)', ';-)']\n};", "map": {"version": 3, "names": ["module", "exports", "angry", "blush", "broken_heart", "confused", "cry", "frowning", "heart", "imp", "innocent", "joy", "kissing", "laughing", "neutral_face", "open_mouth", "rage", "smile", "smiley", "smiling_imp", "sob", "stuck_out_tongue", "sunglasses", "sweat", "sweat_smile", "unamused", "wink"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it-emoji@2.0.2/node_modules/markdown-it-emoji/lib/data/shortcuts.js"], "sourcesContent": ["// Emoticons -> Emoji mapping.\n//\n// (!) Some patterns skipped, to avoid collisions\n// without increase matcher complicity. Than can change in future.\n//\n// Places to look for more emoticons info:\n//\n// - http://en.wikipedia.org/wiki/List_of_emoticons#Western\n// - https://github.com/wooorm/emoticon/blob/master/Support.md\n// - http://factoryjoe.com/projects/emoticons/\n//\n'use strict';\n\nmodule.exports = {\n  angry:            [ '>:(', '>:-(' ],\n  blush:            [ ':\")', ':-\")' ],\n  broken_heart:     [ '</3', '<\\\\3' ],\n  // :\\ and :-\\ not used because of conflict with markdown escaping\n  confused:         [ ':/', ':-/' ], // twemoji shows question\n  cry:              [ \":'(\", \":'-(\", ':,(', ':,-(' ],\n  frowning:         [ ':(', ':-(' ],\n  heart:            [ '<3' ],\n  imp:              [ ']:(', ']:-(' ],\n  innocent:         [ 'o:)', 'O:)', 'o:-)', 'O:-)', '0:)', '0:-)' ],\n  joy:              [ \":')\", \":'-)\", ':,)', ':,-)', \":'D\", \":'-D\", ':,D', ':,-D' ],\n  kissing:          [ ':*', ':-*' ],\n  laughing:         [ 'x-)', 'X-)' ],\n  neutral_face:     [ ':|', ':-|' ],\n  open_mouth:       [ ':o', ':-o', ':O', ':-O' ],\n  rage:             [ ':@', ':-@' ],\n  smile:            [ ':D', ':-D' ],\n  smiley:           [ ':)', ':-)' ],\n  smiling_imp:      [ ']:)', ']:-)' ],\n  sob:              [ \":,'(\", \":,'-(\", ';(', ';-(' ],\n  stuck_out_tongue: [ ':P', ':-P' ],\n  sunglasses:       [ '8-)', 'B-)' ],\n  sweat:            [ ',:(', ',:-(' ],\n  sweat_smile:      [ ',:)', ',:-)' ],\n  unamused:         [ ':s', ':-S', ':z', ':-Z', ':$', ':-$' ],\n  wink:             [ ';)', ';-)' ]\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG;EACfC,KAAK,EAAa,CAAE,KAAK,EAAE,MAAM,CAAE;EACnCC,KAAK,EAAa,CAAE,KAAK,EAAE,MAAM,CAAE;EACnCC,YAAY,EAAM,CAAE,KAAK,EAAE,MAAM,CAAE;EACnC;EACAC,QAAQ,EAAU,CAAE,IAAI,EAAE,KAAK,CAAE;EAAE;EACnCC,GAAG,EAAe,CAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAE;EAClDC,QAAQ,EAAU,CAAE,IAAI,EAAE,KAAK,CAAE;EACjCC,KAAK,EAAa,CAAE,IAAI,CAAE;EAC1BC,GAAG,EAAe,CAAE,KAAK,EAAE,MAAM,CAAE;EACnCC,QAAQ,EAAU,CAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAE;EACjEC,GAAG,EAAe,CAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,CAAE;EAChFC,OAAO,EAAW,CAAE,IAAI,EAAE,KAAK,CAAE;EACjCC,QAAQ,EAAU,CAAE,KAAK,EAAE,KAAK,CAAE;EAClCC,YAAY,EAAM,CAAE,IAAI,EAAE,KAAK,CAAE;EACjCC,UAAU,EAAQ,CAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAE;EAC9CC,IAAI,EAAc,CAAE,IAAI,EAAE,KAAK,CAAE;EACjCC,KAAK,EAAa,CAAE,IAAI,EAAE,KAAK,CAAE;EACjCC,MAAM,EAAY,CAAE,IAAI,EAAE,KAAK,CAAE;EACjCC,WAAW,EAAO,CAAE,KAAK,EAAE,MAAM,CAAE;EACnCC,GAAG,EAAe,CAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,CAAE;EAClDC,gBAAgB,EAAE,CAAE,IAAI,EAAE,KAAK,CAAE;EACjCC,UAAU,EAAQ,CAAE,KAAK,EAAE,KAAK,CAAE;EAClCC,KAAK,EAAa,CAAE,KAAK,EAAE,MAAM,CAAE;EACnCC,WAAW,EAAO,CAAE,KAAK,EAAE,MAAM,CAAE;EACnCC,QAAQ,EAAU,CAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,CAAE;EAC3DC,IAAI,EAAc,CAAE,IAAI,EAAE,KAAK;AACjC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}