{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, normalizeStyle as _normalizeStyle, withCtx as _withCtx, createBlock as _createBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"WorkBenchOneList\"\n};\nvar _hoisted_2 = [\"onClick\"];\nvar _hoisted_3 = {\n  class: \"WorkBenchOneItemBox\"\n};\nvar _hoisted_4 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createBlock(_component_el_scrollbar, {\n    always: \"\",\n    class: \"WorkBenchOne\",\n    style: _normalizeStyle(`background: url('${$setup.WorkBenchBgImg}') no-repeat;background-size: 100% 100%;`)\n  }, {\n    default: _withCtx(function () {\n      return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.WorkBench, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"WorkBenchOneBox\",\n          key: item.id\n        }, [_createElementVNode(\"div\", {\n          class: _normalizeClass([\"WorkBenchOneColumn\", {\n            WorkBenchOneColumnOther: item.id === 'other' && !item.name\n          }])\n        }, _toDisplayString(item.name), 3 /* TEXT, CLASS */), _createElementVNode(\"div\", _hoisted_1, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList(item.data, function (row) {\n          return _openBlock(), _createElementBlock(\"div\", {\n            class: \"WorkBenchOneItem\",\n            key: row.id,\n            onClick: function onClick($event) {\n              return $setup.handleWorkBench(row);\n            }\n          }, [_createVNode(_component_el_image, {\n            src: row.icon,\n            fit: \"cover\"\n          }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", {\n            class: \"WorkBenchOneItemName\",\n            innerHTML: row.name\n          }, null, 8 /* PROPS */, _hoisted_4)])], 8 /* PROPS */, _hoisted_2);\n        }), 128 /* KEYED_FRAGMENT */))])]);\n      }), 128 /* KEYED_FRAGMENT */))];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"style\"]);\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_el_scrollbar", "always", "style", "_normalizeStyle", "$setup", "WorkBenchBgImg", "default", "_withCtx", "_createElementBlock", "_Fragment", "_renderList", "WorkBench", "item", "key", "id", "_createElementVNode", "_normalizeClass", "WorkBenchOneColumnOther", "name", "_hoisted_1", "data", "row", "onClick", "$event", "handleWorkBench", "_createVNode", "_component_el_image", "src", "icon", "fit", "_hoisted_3", "innerHTML", "_hoisted_4", "_hoisted_2", "_"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\WorkBench\\components\\WorkBenchOne.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar always class=\"WorkBenchOne\"\r\n    :style=\"`background: url('${WorkBenchBgImg}') no-repeat;background-size: 100% 100%;`\">\r\n    <div class=\"WorkBenchOneBox\" v-for=\"item in WorkBench\" :key=\"item.id\">\r\n      <div class=\"WorkBenchOneColumn\" :class=\"{ WorkBenchOneColumnOther: item.id === 'other' && !item.name }\">\r\n        {{ item.name }}\r\n      </div>\r\n      <div class=\"WorkBenchOneList\">\r\n        <div class=\"WorkBenchOneItem\" v-for=\"row in item.data\" :key=\"row.id\" @click=\"handleWorkBench(row)\">\r\n          <el-image :src=\"row.icon\" fit=\"cover\" />\r\n          <div class=\"WorkBenchOneItemBox\">\r\n            <div class=\"WorkBenchOneItemName\" v-html=\"row.name\"></div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nexport default { name: 'WorkBench' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, inject, watch, onMounted } from 'vue'\r\nimport { WorkBenchBgImg } from 'common/js/system_var.js'\r\n\r\nconst menuFunction = ref([])\r\nconst WorkBench = ref([])\r\nconst WorkBenchList = inject('WorkBenchList')\r\nconst leftMenuData = inject('leftMenuData')\r\n\r\nonMounted(() => {\r\n  dictionaryData()\r\n})\r\n\r\nconst dictionaryData = async () => {\r\n  const res = await api.dictionaryData({ dictCodes: ['menu_function'] })\r\n  var { data } = res\r\n  menuFunction.value = data.menu_function || []\r\n  if (WorkBenchList && WorkBenchList.value && WorkBenchList.value.length) {\r\n    handleWorkBenchData(menuFunction.value.map((v) => ({ id: v.key, name: v.name, data: [] })))\r\n  }\r\n}\r\nconst handleWorkBenchData = (arr) => {\r\n  if (!WorkBenchList || !WorkBenchList.value) return\r\n\r\n  var other = ''\r\n  var arrData = arr\r\n  var arrIndex = arr.map((v) => v.id)\r\n  for (let i = 0, len = WorkBenchList.value.length; i < len; i++) {\r\n    const item = WorkBenchList.value[i]\r\n    if (item.menuFunction?.value) {\r\n      if (arrIndex.includes(item.menuFunction.value)) {\r\n        arrData.forEach((row) => {\r\n          if (item.menuFunction.value === row.id) {\r\n            row.data.push(item)\r\n          }\r\n        })\r\n      } else {\r\n        arrIndex.push(item.menuFunction.value)\r\n        arrData.push({ id: item.menuFunction.value, name: item.menuFunction.label, data: [item] })\r\n      }\r\n    } else {\r\n      if (other) {\r\n        other.data.push(item)\r\n      } else {\r\n        other = { id: 'other', data: [item] }\r\n      }\r\n    }\r\n  }\r\n  WorkBench.value = arrData.filter((v) => v.data.length)\r\n  if (other) {\r\n    WorkBench.value.push({ ...other, name: WorkBench.value.length ? '其他' : '' })\r\n  }\r\n}\r\nconst handleWorkBench = (item) => {\r\n  leftMenuData(item)\r\n}\r\n\r\nwatch(\r\n  () => WorkBenchList.value,\r\n  () => {\r\n    if (WorkBenchList && WorkBenchList.value && WorkBenchList.value.length) {\r\n      handleWorkBenchData(menuFunction.value.map((v) => ({ id: v.key, name: v.name, data: [] })))\r\n    }\r\n  },\r\n  { deep: true }\r\n)\r\n</script>\r\n<style lang=\"scss\">\r\n.WorkBenchOne {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .WorkBenchOneBox {\r\n    width: 1140px;\r\n    margin: auto;\r\n    padding-left: 40px;\r\n\r\n    .WorkBenchOneColumn {\r\n      padding: 30px 0;\r\n      padding-left: 24px;\r\n      font-size: 20px;\r\n      font-weight: bold;\r\n      color: #333333;\r\n      position: relative;\r\n\r\n      &::after {\r\n        content: '';\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 0;\r\n        width: 0;\r\n        height: 0;\r\n        border-top: 6px solid transparent;\r\n        border-right: 6px solid transparent;\r\n        border-bottom: 6px solid transparent;\r\n        border-left: 6px solid var(--zy-el-color-primary);\r\n        transform: translateY(-50%);\r\n      }\r\n\r\n      &::before {\r\n        content: '';\r\n        position: absolute;\r\n        top: 50%;\r\n        left: 10px;\r\n        width: 0;\r\n        height: 0;\r\n        border-top: 6px solid transparent;\r\n        border-right: 6px solid transparent;\r\n        border-bottom: 6px solid transparent;\r\n        border-left: 6px solid var(--zy-el-color-primary);\r\n        transform: translateY(-50%);\r\n      }\r\n    }\r\n\r\n    .WorkBenchOneColumnOther {\r\n      &::after {\r\n        border-left: 6px solid transparent;\r\n      }\r\n\r\n      &::before {\r\n        border-left: 6px solid transparent;\r\n      }\r\n    }\r\n\r\n    .WorkBenchOneList {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n\r\n      .WorkBenchOneItem {\r\n        width: 248px;\r\n        background: #ffffff;\r\n        box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.1);\r\n        border-radius: 4px;\r\n        margin: 0 26px 26px 0;\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        align-items: center;\r\n        justify-content: center;\r\n        padding: 20px 22px;\r\n        cursor: pointer;\r\n\r\n        &:hover {\r\n          box-shadow: 0px 2px 6px 2px rgba(0, 0, 0, 0.2);\r\n        }\r\n\r\n        .zy-el-image {\r\n          width: 48px;\r\n          height: 48px;\r\n        }\r\n\r\n        .WorkBenchOneItemBox {\r\n          width: calc(100% - 48px);\r\n          height: calc((var(--zy-name-font-size) * var(--zy-line-height)) * 2);\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n          align-items: center;\r\n        }\r\n\r\n        .WorkBenchOneItemName {\r\n          width: 100%;\r\n          line-height: var(--zy-line-height);\r\n          font-size: var(--zy-name-font-size);\r\n          padding-left: 20px;\r\n          font-weight: bold;\r\n          overflow: hidden;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAOWA,KAAK,EAAC;AAAkB;iBAPnC;;EAUeA,KAAK,EAAC;AAAqB;iBAV1C;;;;uBACEC,YAAA,CAeeC,uBAAA;IAfDC,MAAM,EAAN,EAAM;IAACH,KAAK,EAAC,cAAc;IACtCI,KAAK,EAFVC,eAAA,qBAEgCC,MAAA,CAAAC,cAAc;;IAF9CC,OAAA,EAAAC,QAAA,CAGiC;MAAA,OAAyB,E,kBAAtDC,mBAAA,CAYMC,SAAA,QAfVC,WAAA,CAGgDN,MAAA,CAAAO,SAAS,EAHzD,UAGwCC,IAAI;6BAAxCJ,mBAAA,CAYM;UAZDV,KAAK,EAAC,iBAAiB;UAA4Be,GAAG,EAAED,IAAI,CAACE;YAChEC,mBAAA,CAEM;UAFDjB,KAAK,EAJhBkB,eAAA,EAIiB,oBAAoB;YAAAC,uBAAA,EAAoCL,IAAI,CAACE,EAAE,iBAAiBF,IAAI,CAACM;UAAI;4BAC/FN,IAAI,CAACM,IAAI,yBAEdH,mBAAA,CAOM,OAPNI,UAOM,I,kBANJX,mBAAA,CAKMC,SAAA,QAbdC,WAAA,CAQoDE,IAAI,CAACQ,IAAI,EAR7D,UAQ6CC,GAAG;+BAAxCb,mBAAA,CAKM;YALDV,KAAK,EAAC,kBAAkB;YAA2Be,GAAG,EAAEQ,GAAG,CAACP,EAAE;YAAGQ,OAAK,WAALA,OAAKA,CAAAC,MAAA;cAAA,OAAEnB,MAAA,CAAAoB,eAAe,CAACH,GAAG;YAAA;cAC9FI,YAAA,CAAwCC,mBAAA;YAA7BC,GAAG,EAAEN,GAAG,CAACO,IAAI;YAAEC,GAAG,EAAC;4CAC9Bd,mBAAA,CAEM,OAFNe,UAEM,GADJf,mBAAA,CAA0D;YAArDjB,KAAK,EAAC,sBAAsB;YAACiC,SAAiB,EAATV,GAAG,CAACH;kCAX1Dc,UAAA,E,mBAAAC,UAAA;;;;IAAAC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}