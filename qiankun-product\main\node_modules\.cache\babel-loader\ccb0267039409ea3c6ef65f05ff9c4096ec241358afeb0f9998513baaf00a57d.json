{"ast": null, "code": "/** internal\n * class ParserInline\n *\n * Tokenizes paragraph content.\n **/\n'use strict';\n\nvar Ruler = require('./ruler');\n\n////////////////////////////////////////////////////////////////////////////////\n// Parser rules\n\nvar _rules = [['text', require('./rules_inline/text')], ['newline', require('./rules_inline/newline')], ['escape', require('./rules_inline/escape')], ['backticks', require('./rules_inline/backticks')], ['strikethrough', require('./rules_inline/strikethrough').tokenize], ['emphasis', require('./rules_inline/emphasis').tokenize], ['link', require('./rules_inline/link')], ['image', require('./rules_inline/image')], ['autolink', require('./rules_inline/autolink')], ['html_inline', require('./rules_inline/html_inline')], ['entity', require('./rules_inline/entity')]];\nvar _rules2 = [['balance_pairs', require('./rules_inline/balance_pairs')], ['strikethrough', require('./rules_inline/strikethrough').postProcess], ['emphasis', require('./rules_inline/emphasis').postProcess], ['text_collapse', require('./rules_inline/text_collapse')]];\n\n/**\n * new ParserInline()\n **/\nfunction ParserInline() {\n  var i;\n\n  /**\n   * ParserInline#ruler -> Ruler\n   *\n   * [[Ruler]] instance. Keep configuration of inline rules.\n   **/\n  this.ruler = new Ruler();\n  for (i = 0; i < _rules.length; i++) {\n    this.ruler.push(_rules[i][0], _rules[i][1]);\n  }\n\n  /**\n   * ParserInline#ruler2 -> Ruler\n   *\n   * [[Ruler]] instance. Second ruler used for post-processing\n   * (e.g. in emphasis-like rules).\n   **/\n  this.ruler2 = new Ruler();\n  for (i = 0; i < _rules2.length; i++) {\n    this.ruler2.push(_rules2[i][0], _rules2[i][1]);\n  }\n}\n\n// Skip single token by running all rules in validation mode;\n// returns `true` if any rule reported success\n//\nParserInline.prototype.skipToken = function (state) {\n  var ok,\n    i,\n    pos = state.pos,\n    rules = this.ruler.getRules(''),\n    len = rules.length,\n    maxNesting = state.md.options.maxNesting,\n    cache = state.cache;\n  if (typeof cache[pos] !== 'undefined') {\n    state.pos = cache[pos];\n    return;\n  }\n  if (state.level < maxNesting) {\n    for (i = 0; i < len; i++) {\n      // Increment state.level and decrement it later to limit recursion.\n      // It's harmless to do here, because no tokens are created. But ideally,\n      // we'd need a separate private state variable for this purpose.\n      //\n      state.level++;\n      ok = rules[i](state, true);\n      state.level--;\n      if (ok) {\n        break;\n      }\n    }\n  } else {\n    // Too much nesting, just skip until the end of the paragraph.\n    //\n    // NOTE: this will cause links to behave incorrectly in the following case,\n    //       when an amount of `[` is exactly equal to `maxNesting + 1`:\n    //\n    //       [[[[[[[[[[[[[[[[[[[[[foo]()\n    //\n    // TODO: remove this workaround when CM standard will allow nested links\n    //       (we can replace it by preventing links from being parsed in\n    //       validation mode)\n    //\n    state.pos = state.posMax;\n  }\n  if (!ok) {\n    state.pos++;\n  }\n  cache[pos] = state.pos;\n};\n\n// Generate tokens for input range\n//\nParserInline.prototype.tokenize = function (state) {\n  var ok,\n    i,\n    rules = this.ruler.getRules(''),\n    len = rules.length,\n    end = state.posMax,\n    maxNesting = state.md.options.maxNesting;\n  while (state.pos < end) {\n    // Try all possible rules.\n    // On success, rule should:\n    //\n    // - update `state.pos`\n    // - update `state.tokens`\n    // - return true\n\n    if (state.level < maxNesting) {\n      for (i = 0; i < len; i++) {\n        ok = rules[i](state, false);\n        if (ok) {\n          break;\n        }\n      }\n    }\n    if (ok) {\n      if (state.pos >= end) {\n        break;\n      }\n      continue;\n    }\n    state.pending += state.src[state.pos++];\n  }\n  if (state.pending) {\n    state.pushPending();\n  }\n};\n\n/**\n * ParserInline.parse(str, md, env, outTokens)\n *\n * Process input string and push inline tokens into `outTokens`\n **/\nParserInline.prototype.parse = function (str, md, env, outTokens) {\n  var i, rules, len;\n  var state = new this.State(str, md, env, outTokens);\n  this.tokenize(state);\n  rules = this.ruler2.getRules('');\n  len = rules.length;\n  for (i = 0; i < len; i++) {\n    rules[i](state);\n  }\n};\nParserInline.prototype.State = require('./rules_inline/state_inline');\nmodule.exports = ParserInline;", "map": {"version": 3, "names": ["Ruler", "require", "_rules", "tokenize", "_rules2", "postProcess", "ParserInline", "i", "ruler", "length", "push", "ruler2", "prototype", "skipToken", "state", "ok", "pos", "rules", "getRules", "len", "maxNesting", "md", "options", "cache", "level", "posMax", "end", "pending", "src", "pushPending", "parse", "str", "env", "outTokens", "State", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/parser_inline.js"], "sourcesContent": ["/** internal\n * class ParserInline\n *\n * Tokenizes paragraph content.\n **/\n'use strict';\n\n\nvar Ruler           = require('./ruler');\n\n\n////////////////////////////////////////////////////////////////////////////////\n// Parser rules\n\nvar _rules = [\n  [ 'text',            require('./rules_inline/text') ],\n  [ 'newline',         require('./rules_inline/newline') ],\n  [ 'escape',          require('./rules_inline/escape') ],\n  [ 'backticks',       require('./rules_inline/backticks') ],\n  [ 'strikethrough',   require('./rules_inline/strikethrough').tokenize ],\n  [ 'emphasis',        require('./rules_inline/emphasis').tokenize ],\n  [ 'link',            require('./rules_inline/link') ],\n  [ 'image',           require('./rules_inline/image') ],\n  [ 'autolink',        require('./rules_inline/autolink') ],\n  [ 'html_inline',     require('./rules_inline/html_inline') ],\n  [ 'entity',          require('./rules_inline/entity') ]\n];\n\nvar _rules2 = [\n  [ 'balance_pairs',   require('./rules_inline/balance_pairs') ],\n  [ 'strikethrough',   require('./rules_inline/strikethrough').postProcess ],\n  [ 'emphasis',        require('./rules_inline/emphasis').postProcess ],\n  [ 'text_collapse',   require('./rules_inline/text_collapse') ]\n];\n\n\n/**\n * new ParserInline()\n **/\nfunction ParserInline() {\n  var i;\n\n  /**\n   * ParserInline#ruler -> Ruler\n   *\n   * [[Ruler]] instance. Keep configuration of inline rules.\n   **/\n  this.ruler = new Ruler();\n\n  for (i = 0; i < _rules.length; i++) {\n    this.ruler.push(_rules[i][0], _rules[i][1]);\n  }\n\n  /**\n   * ParserInline#ruler2 -> Ruler\n   *\n   * [[Ruler]] instance. Second ruler used for post-processing\n   * (e.g. in emphasis-like rules).\n   **/\n  this.ruler2 = new Ruler();\n\n  for (i = 0; i < _rules2.length; i++) {\n    this.ruler2.push(_rules2[i][0], _rules2[i][1]);\n  }\n}\n\n\n// Skip single token by running all rules in validation mode;\n// returns `true` if any rule reported success\n//\nParserInline.prototype.skipToken = function (state) {\n  var ok, i, pos = state.pos,\n      rules = this.ruler.getRules(''),\n      len = rules.length,\n      maxNesting = state.md.options.maxNesting,\n      cache = state.cache;\n\n\n  if (typeof cache[pos] !== 'undefined') {\n    state.pos = cache[pos];\n    return;\n  }\n\n  if (state.level < maxNesting) {\n    for (i = 0; i < len; i++) {\n      // Increment state.level and decrement it later to limit recursion.\n      // It's harmless to do here, because no tokens are created. But ideally,\n      // we'd need a separate private state variable for this purpose.\n      //\n      state.level++;\n      ok = rules[i](state, true);\n      state.level--;\n\n      if (ok) { break; }\n    }\n  } else {\n    // Too much nesting, just skip until the end of the paragraph.\n    //\n    // NOTE: this will cause links to behave incorrectly in the following case,\n    //       when an amount of `[` is exactly equal to `maxNesting + 1`:\n    //\n    //       [[[[[[[[[[[[[[[[[[[[[foo]()\n    //\n    // TODO: remove this workaround when CM standard will allow nested links\n    //       (we can replace it by preventing links from being parsed in\n    //       validation mode)\n    //\n    state.pos = state.posMax;\n  }\n\n  if (!ok) { state.pos++; }\n  cache[pos] = state.pos;\n};\n\n\n// Generate tokens for input range\n//\nParserInline.prototype.tokenize = function (state) {\n  var ok, i,\n      rules = this.ruler.getRules(''),\n      len = rules.length,\n      end = state.posMax,\n      maxNesting = state.md.options.maxNesting;\n\n  while (state.pos < end) {\n    // Try all possible rules.\n    // On success, rule should:\n    //\n    // - update `state.pos`\n    // - update `state.tokens`\n    // - return true\n\n    if (state.level < maxNesting) {\n      for (i = 0; i < len; i++) {\n        ok = rules[i](state, false);\n        if (ok) { break; }\n      }\n    }\n\n    if (ok) {\n      if (state.pos >= end) { break; }\n      continue;\n    }\n\n    state.pending += state.src[state.pos++];\n  }\n\n  if (state.pending) {\n    state.pushPending();\n  }\n};\n\n\n/**\n * ParserInline.parse(str, md, env, outTokens)\n *\n * Process input string and push inline tokens into `outTokens`\n **/\nParserInline.prototype.parse = function (str, md, env, outTokens) {\n  var i, rules, len;\n  var state = new this.State(str, md, env, outTokens);\n\n  this.tokenize(state);\n\n  rules = this.ruler2.getRules('');\n  len = rules.length;\n\n  for (i = 0; i < len; i++) {\n    rules[i](state);\n  }\n};\n\n\nParserInline.prototype.State = require('./rules_inline/state_inline');\n\n\nmodule.exports = ParserInline;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAGZ,IAAIA,KAAK,GAAaC,OAAO,CAAC,SAAS,CAAC;;AAGxC;AACA;;AAEA,IAAIC,MAAM,GAAG,CACX,CAAE,MAAM,EAAaD,OAAO,CAAC,qBAAqB,CAAC,CAAE,EACrD,CAAE,SAAS,EAAUA,OAAO,CAAC,wBAAwB,CAAC,CAAE,EACxD,CAAE,QAAQ,EAAWA,OAAO,CAAC,uBAAuB,CAAC,CAAE,EACvD,CAAE,WAAW,EAAQA,OAAO,CAAC,0BAA0B,CAAC,CAAE,EAC1D,CAAE,eAAe,EAAIA,OAAO,CAAC,8BAA8B,CAAC,CAACE,QAAQ,CAAE,EACvE,CAAE,UAAU,EAASF,OAAO,CAAC,yBAAyB,CAAC,CAACE,QAAQ,CAAE,EAClE,CAAE,MAAM,EAAaF,OAAO,CAAC,qBAAqB,CAAC,CAAE,EACrD,CAAE,OAAO,EAAYA,OAAO,CAAC,sBAAsB,CAAC,CAAE,EACtD,CAAE,UAAU,EAASA,OAAO,CAAC,yBAAyB,CAAC,CAAE,EACzD,CAAE,aAAa,EAAMA,OAAO,CAAC,4BAA4B,CAAC,CAAE,EAC5D,CAAE,QAAQ,EAAWA,OAAO,CAAC,uBAAuB,CAAC,CAAE,CACxD;AAED,IAAIG,OAAO,GAAG,CACZ,CAAE,eAAe,EAAIH,OAAO,CAAC,8BAA8B,CAAC,CAAE,EAC9D,CAAE,eAAe,EAAIA,OAAO,CAAC,8BAA8B,CAAC,CAACI,WAAW,CAAE,EAC1E,CAAE,UAAU,EAASJ,OAAO,CAAC,yBAAyB,CAAC,CAACI,WAAW,CAAE,EACrE,CAAE,eAAe,EAAIJ,OAAO,CAAC,8BAA8B,CAAC,CAAE,CAC/D;;AAGD;AACA;AACA;AACA,SAASK,YAAYA,CAAA,EAAG;EACtB,IAAIC,CAAC;;EAEL;AACF;AACA;AACA;AACA;EACE,IAAI,CAACC,KAAK,GAAG,IAAIR,KAAK,CAAC,CAAC;EAExB,KAAKO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,MAAM,CAACO,MAAM,EAAEF,CAAC,EAAE,EAAE;IAClC,IAAI,CAACC,KAAK,CAACE,IAAI,CAACR,MAAM,CAACK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEL,MAAM,CAACK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7C;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAI,CAACI,MAAM,GAAG,IAAIX,KAAK,CAAC,CAAC;EAEzB,KAAKO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,OAAO,CAACK,MAAM,EAAEF,CAAC,EAAE,EAAE;IACnC,IAAI,CAACI,MAAM,CAACD,IAAI,CAACN,OAAO,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEH,OAAO,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChD;AACF;;AAGA;AACA;AACA;AACAD,YAAY,CAACM,SAAS,CAACC,SAAS,GAAG,UAAUC,KAAK,EAAE;EAClD,IAAIC,EAAE;IAAER,CAAC;IAAES,GAAG,GAAGF,KAAK,CAACE,GAAG;IACtBC,KAAK,GAAG,IAAI,CAACT,KAAK,CAACU,QAAQ,CAAC,EAAE,CAAC;IAC/BC,GAAG,GAAGF,KAAK,CAACR,MAAM;IAClBW,UAAU,GAAGN,KAAK,CAACO,EAAE,CAACC,OAAO,CAACF,UAAU;IACxCG,KAAK,GAAGT,KAAK,CAACS,KAAK;EAGvB,IAAI,OAAOA,KAAK,CAACP,GAAG,CAAC,KAAK,WAAW,EAAE;IACrCF,KAAK,CAACE,GAAG,GAAGO,KAAK,CAACP,GAAG,CAAC;IACtB;EACF;EAEA,IAAIF,KAAK,CAACU,KAAK,GAAGJ,UAAU,EAAE;IAC5B,KAAKb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,GAAG,EAAEZ,CAAC,EAAE,EAAE;MACxB;MACA;MACA;MACA;MACAO,KAAK,CAACU,KAAK,EAAE;MACbT,EAAE,GAAGE,KAAK,CAACV,CAAC,CAAC,CAACO,KAAK,EAAE,IAAI,CAAC;MAC1BA,KAAK,CAACU,KAAK,EAAE;MAEb,IAAIT,EAAE,EAAE;QAAE;MAAO;IACnB;EACF,CAAC,MAAM;IACL;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAD,KAAK,CAACE,GAAG,GAAGF,KAAK,CAACW,MAAM;EAC1B;EAEA,IAAI,CAACV,EAAE,EAAE;IAAED,KAAK,CAACE,GAAG,EAAE;EAAE;EACxBO,KAAK,CAACP,GAAG,CAAC,GAAGF,KAAK,CAACE,GAAG;AACxB,CAAC;;AAGD;AACA;AACAV,YAAY,CAACM,SAAS,CAACT,QAAQ,GAAG,UAAUW,KAAK,EAAE;EACjD,IAAIC,EAAE;IAAER,CAAC;IACLU,KAAK,GAAG,IAAI,CAACT,KAAK,CAACU,QAAQ,CAAC,EAAE,CAAC;IAC/BC,GAAG,GAAGF,KAAK,CAACR,MAAM;IAClBiB,GAAG,GAAGZ,KAAK,CAACW,MAAM;IAClBL,UAAU,GAAGN,KAAK,CAACO,EAAE,CAACC,OAAO,CAACF,UAAU;EAE5C,OAAON,KAAK,CAACE,GAAG,GAAGU,GAAG,EAAE;IACtB;IACA;IACA;IACA;IACA;IACA;;IAEA,IAAIZ,KAAK,CAACU,KAAK,GAAGJ,UAAU,EAAE;MAC5B,KAAKb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,GAAG,EAAEZ,CAAC,EAAE,EAAE;QACxBQ,EAAE,GAAGE,KAAK,CAACV,CAAC,CAAC,CAACO,KAAK,EAAE,KAAK,CAAC;QAC3B,IAAIC,EAAE,EAAE;UAAE;QAAO;MACnB;IACF;IAEA,IAAIA,EAAE,EAAE;MACN,IAAID,KAAK,CAACE,GAAG,IAAIU,GAAG,EAAE;QAAE;MAAO;MAC/B;IACF;IAEAZ,KAAK,CAACa,OAAO,IAAIb,KAAK,CAACc,GAAG,CAACd,KAAK,CAACE,GAAG,EAAE,CAAC;EACzC;EAEA,IAAIF,KAAK,CAACa,OAAO,EAAE;IACjBb,KAAK,CAACe,WAAW,CAAC,CAAC;EACrB;AACF,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACAvB,YAAY,CAACM,SAAS,CAACkB,KAAK,GAAG,UAAUC,GAAG,EAAEV,EAAE,EAAEW,GAAG,EAAEC,SAAS,EAAE;EAChE,IAAI1B,CAAC,EAAEU,KAAK,EAAEE,GAAG;EACjB,IAAIL,KAAK,GAAG,IAAI,IAAI,CAACoB,KAAK,CAACH,GAAG,EAAEV,EAAE,EAAEW,GAAG,EAAEC,SAAS,CAAC;EAEnD,IAAI,CAAC9B,QAAQ,CAACW,KAAK,CAAC;EAEpBG,KAAK,GAAG,IAAI,CAACN,MAAM,CAACO,QAAQ,CAAC,EAAE,CAAC;EAChCC,GAAG,GAAGF,KAAK,CAACR,MAAM;EAElB,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,GAAG,EAAEZ,CAAC,EAAE,EAAE;IACxBU,KAAK,CAACV,CAAC,CAAC,CAACO,KAAK,CAAC;EACjB;AACF,CAAC;AAGDR,YAAY,CAACM,SAAS,CAACsB,KAAK,GAAGjC,OAAO,CAAC,6BAA6B,CAAC;AAGrEkC,MAAM,CAACC,OAAO,GAAG9B,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}