{"ast": null, "code": "/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Initializes an array clone.\n *\n * @private\n * @param {Array} array The array to clone.\n * @returns {Array} Returns the initialized clone.\n */\nfunction initCloneArray(array) {\n  var length = array.length,\n    result = new array.constructor(length);\n\n  // Add properties assigned by `RegExp#exec`.\n  if (length && typeof array[0] == 'string' && hasOwnProperty.call(array, 'index')) {\n    result.index = array.index;\n    result.input = array.input;\n  }\n  return result;\n}\nmodule.exports = initCloneArray;", "map": {"version": 3, "names": ["objectProto", "Object", "prototype", "hasOwnProperty", "initCloneArray", "array", "length", "result", "constructor", "call", "index", "input", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_initCloneArray.js"], "sourcesContent": ["/** Used for built-in method references. */\nvar objectProto = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/**\n * Initializes an array clone.\n *\n * @private\n * @param {Array} array The array to clone.\n * @returns {Array} Returns the initialized clone.\n */\nfunction initCloneArray(array) {\n  var length = array.length,\n      result = new array.constructor(length);\n\n  // Add properties assigned by `RegExp#exec`.\n  if (length && typeof array[0] == 'string' && hasOwnProperty.call(array, 'index')) {\n    result.index = array.index;\n    result.input = array.input;\n  }\n  return result;\n}\n\nmodule.exports = initCloneArray;\n"], "mappings": "AAAA;AACA,IAAIA,WAAW,GAAGC,MAAM,CAACC,SAAS;;AAElC;AACA,IAAIC,cAAc,GAAGH,WAAW,CAACG,cAAc;;AAE/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IACrBC,MAAM,GAAG,IAAIF,KAAK,CAACG,WAAW,CAACF,MAAM,CAAC;;EAE1C;EACA,IAAIA,MAAM,IAAI,OAAOD,KAAK,CAAC,CAAC,CAAC,IAAI,QAAQ,IAAIF,cAAc,CAACM,IAAI,CAACJ,KAAK,EAAE,OAAO,CAAC,EAAE;IAChFE,MAAM,CAACG,KAAK,GAAGL,KAAK,CAACK,KAAK;IAC1BH,MAAM,CAACI,KAAK,GAAGN,KAAK,CAACM,KAAK;EAC5B;EACA,OAAOJ,MAAM;AACf;AAEAK,MAAM,CAACC,OAAO,GAAGT,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}