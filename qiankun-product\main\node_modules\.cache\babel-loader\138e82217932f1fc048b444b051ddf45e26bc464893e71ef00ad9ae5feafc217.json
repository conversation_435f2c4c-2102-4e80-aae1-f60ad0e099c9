{"ast": null, "code": "import { createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, normalizeClass as _normalizeClass, withCtx as _withCtx, createBlock as _createBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalGroupVote\"\n};\nvar _hoisted_2 = {\n  class: \"GlobalGroupVoteTitleBody\"\n};\nvar _hoisted_3 = [\"innerHTML\"];\nvar _hoisted_4 = {\n  class: \"GlobalGroupVoteScroll\"\n};\nvar _hoisted_5 = [\"onClick\"];\nvar _hoisted_6 = [\"innerHTML\"];\nvar _hoisted_7 = {\n  class: \"GlobalGroupVoteItemTitle\"\n};\nvar _hoisted_8 = [\"innerHTML\"];\nvar _hoisted_9 = {\n  class: \"GlobalGroupVoteItemTitleTime\"\n};\nvar _hoisted_10 = {\n  key: 0,\n  class: \"is-primary\"\n};\nvar _hoisted_11 = {\n  key: 1,\n  class: \"is-warning\"\n};\nvar _hoisted_12 = {\n  key: 2,\n  class: \"is-info\"\n};\nvar _hoisted_13 = {\n  class: \"GlobalGroupVoteItemName\"\n};\nvar _hoisted_14 = {\n  key: 0,\n  class: \"GlobalGroupVoteItemImg\"\n};\nvar _hoisted_15 = {\n  key: 0,\n  class: \"GlobalGroupVoteLoadingText\"\n};\nvar _hoisted_16 = {\n  key: 1,\n  class: \"GlobalGroupVoteLoadingText\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", {\n    class: _normalizeClass([\"GlobalGroupVoteBody\", {\n      'GlobalGroupVoteBodyActive': $setup.id\n    }])\n  }, [_createElementVNode(\"div\", _hoisted_2, [_cache[0] || (_cache[0] = _createElementVNode(\"div\", {\n    class: \"GlobalGroupVoteTitle\"\n  }, \"群投票\", -1 /* HOISTED */)), _createElementVNode(\"div\", {\n    class: \"GlobalGroupVoteTitleIcon\",\n    innerHTML: $setup.initGroupChatIcon,\n    onClick: $setup.handleCreateVote\n  }, null, 8 /* PROPS */, _hoisted_3)]), _createVNode(_component_el_scrollbar, {\n    ref: \"scrollRef\",\n    class: \"GlobalGroupVoteScrollbar\",\n    onScroll: $setup.handleScroll\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_4, [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.tableData, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: _normalizeClass([\"GlobalGroupVoteItem\", {\n            'GlobalGroupVoteItemHasVote': item.hasVote\n          }]),\n          key: item.id,\n          onClick: function onClick($event) {\n            return $setup.handleClick(item);\n          }\n        }, [_createElementVNode(\"div\", {\n          class: \"GlobalGroupVoteItemHasIcon\",\n          innerHTML: $setup.hasVoteIcon\n        }, null, 8 /* PROPS */, _hoisted_6), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", {\n          class: \"GlobalGroupVoteItemTitleIcon\",\n          innerHTML: $setup.voteListIcon\n        }, null, 8 /* PROPS */, _hoisted_8), _createElementVNode(\"div\", _hoisted_9, _toDisplayString($setup.format(item.createDate)), 1 /* TEXT */), item.voteStatus === '未开始' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_10, _toDisplayString(item.voteStatus), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), item.voteStatus === '进行中' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_11, _toDisplayString(item.voteStatus), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true), item.voteStatus === '已结束' ? (_openBlock(), _createElementBlock(\"span\", _hoisted_12, _toDisplayString(item.voteStatus), 1 /* TEXT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_13, _toDisplayString(item.topic), 1 /* TEXT */), item.topicImg ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [_createVNode(_component_el_image, {\n          src: $setup.imgUrl(item.topicImg),\n          fit: \"cover\",\n          draggable: \"false\"\n        }, null, 8 /* PROPS */, [\"src\"])])) : _createCommentVNode(\"v-if\", true)], 10 /* CLASS, PROPS */, _hoisted_5);\n      }), 128 /* KEYED_FRAGMENT */)), $setup.loading ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, \"加载中...\")) : _createCommentVNode(\"v-if\", true), $setup.isShow ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16, \"没有更多了\")) : _createCommentVNode(\"v-if\", true)])];\n    }),\n    _: 1 /* STABLE */\n  }, 512 /* NEED_PATCH */)], 2 /* CLASS */), $setup.id ? (_openBlock(), _createBlock($setup[\"GlobalVoteDetails\"], {\n    key: 0,\n    id: $setup.id,\n    onCallback: $setup.callback,\n    onSendMessage: $setup.handleSendMessage\n  }, null, 8 /* PROPS */, [\"id\"])) : _createCommentVNode(\"v-if\", true)]);\n}", "map": {"version": 3, "names": ["class", "key", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_normalizeClass", "$setup", "id", "_hoisted_2", "innerHTML", "initGroupChatIcon", "onClick", "handleCreateVote", "_hoisted_3", "_createVNode", "_component_el_scrollbar", "ref", "onScroll", "handleScroll", "default", "_withCtx", "_hoisted_4", "_Fragment", "_renderList", "tableData", "item", "hasVote", "$event", "handleClick", "hasVoteIcon", "_hoisted_6", "_hoisted_7", "voteListIcon", "_hoisted_8", "_hoisted_9", "_toDisplayString", "format", "createDate", "voteStatus", "_hoisted_10", "_createCommentVNode", "_hoisted_11", "_hoisted_12", "_hoisted_13", "topic", "topicImg", "_hoisted_14", "_component_el_image", "src", "imgUrl", "fit", "draggable", "_hoisted_5", "loading", "_hoisted_15", "isShow", "_hoisted_16", "_", "_createBlock", "onCallback", "callback", "onSendMessage", "handleSendMessage"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalChat\\components\\GlobalGroupVote\\GlobalGroupVote.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalGroupVote\">\r\n    <div class=\"GlobalGroupVoteBody\" :class=\"{ 'GlobalGroupVoteBodyActive': id }\">\r\n      <div class=\"GlobalGroupVoteTitleBody\">\r\n        <div class=\"GlobalGroupVoteTitle\">群投票</div>\r\n        <div class=\"GlobalGroupVoteTitleIcon\" v-html=\"initGroupChatIcon\" @click=\"handleCreateVote\"></div>\r\n      </div>\r\n      <el-scrollbar ref=\"scrollRef\" class=\"GlobalGroupVoteScrollbar\" @scroll=\"handleScroll\">\r\n        <div class=\"GlobalGroupVoteScroll\">\r\n          <div class=\"GlobalGroupVoteItem\" :class=\"{ 'GlobalGroupVoteItemHasVote': item.hasVote }\"\r\n            v-for=\"item in tableData\" :key=\"item.id\" @click=\"handleClick(item)\">\r\n            <div class=\"GlobalGroupVoteItemHasIcon\" v-html=\"hasVoteIcon\"></div>\r\n            <div class=\"GlobalGroupVoteItemTitle\">\r\n              <div class=\"GlobalGroupVoteItemTitleIcon\" v-html=\"voteListIcon\"></div>\r\n              <div class=\"GlobalGroupVoteItemTitleTime\">{{ format(item.createDate) }}</div>\r\n              <span class=\"is-primary\" v-if=\"item.voteStatus === '未开始'\">{{ item.voteStatus }}</span>\r\n              <span class=\"is-warning\" v-if=\"item.voteStatus === '进行中'\">{{ item.voteStatus }}</span>\r\n              <span class=\"is-info\" v-if=\"item.voteStatus === '已结束'\">{{ item.voteStatus }}</span>\r\n            </div>\r\n            <div class=\"GlobalGroupVoteItemName\">{{ item.topic }}</div>\r\n            <div class=\"GlobalGroupVoteItemImg\" v-if=\"item.topicImg\">\r\n              <el-image :src=\"imgUrl(item.topicImg)\" fit=\"cover\" draggable=\"false\" />\r\n            </div>\r\n          </div>\r\n          <div class=\"GlobalGroupVoteLoadingText\" v-if=\"loading\">加载中...</div>\r\n          <div class=\"GlobalGroupVoteLoadingText\" v-if=\"isShow\">没有更多了</div>\r\n        </div>\r\n      </el-scrollbar>\r\n    </div>\r\n    <GlobalVoteDetails :id=\"id\" v-if=\"id\" @callback=\"callback\" @sendMessage=\"handleSendMessage\"></GlobalVoteDetails>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalGroupVote' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, watch } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { initGroupChatIcon, voteListIcon, hasVoteIcon } from '../../js/icon.js'\r\nimport GlobalVoteDetails from './GlobalVoteDetails.vue'\r\nconst props = defineProps({\r\n  id: { type: String, default: '' },\r\n  refresh: { type: String, default: '' }\r\n})\r\nconst emit = defineEmits(['callback', 'sendMessage'])\r\nconst scrollRef = ref()\r\nconst loadingScroll = ref(false)\r\nconst pageNo = ref(1)\r\nconst pageSize = ref(10)\r\nconst totals = ref(0)\r\nconst isShow = ref(false)\r\nconst loading = ref(true)\r\nconst tableData = ref([])\r\nconst id = ref('')\r\nconst imgUrl = url => url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg')\r\nconst handleScroll = ({ scrollTop }) => {\r\n  if (!scrollRef.value) return\r\n  const { scrollHeight, clientHeight } = scrollRef.value.wrapRef\r\n  if (scrollHeight - scrollTop <= clientHeight + 50 && !loadingScroll.value) {\r\n    load()\r\n  }\r\n}\r\nconst load = () => {\r\n  if (pageNo.value * pageSize.value >= totals.value) return\r\n  loadingScroll.value = true\r\n  pageNo.value += 1\r\n  VoteList()\r\n}\r\nconst VoteList = async (type) => {\r\n  const { data, total } = await api.VoteList({\r\n    pageNo: type ? 1 : pageNo.value, pageSize: type && tableData.value.length ? tableData.value.length : pageSize.value,\r\n    query: { businessId: props.id, businessType: 'chatGroup' }\r\n  })\r\n  tableData.value = type ? data : [...tableData.value, ...data]\r\n  totals.value = total\r\n  loading.value = pageNo.value * pageSize.value < totals.value\r\n  isShow.value = pageNo.value * pageSize.value >= totals.value\r\n  loadingScroll.value = false\r\n}\r\nconst handleClick = (item) => {\r\n  id.value = item.id\r\n}\r\nconst callback = (type) => {\r\n  id.value = ''\r\n  if (type === 'del') VoteList(true)\r\n}\r\nconst handleSendMessage = (data) => {\r\n  emit('sendMessage', data)\r\n}\r\nconst handleCreateVote = () => {\r\n  emit('callback')\r\n}\r\nwatch(() => props.refresh, () => {\r\n  VoteList(true)\r\n}, { immediate: true })\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalGroupVote {\r\n  height: 100%;\r\n  display: flex;\r\n  background: var(--zy-el-color-info-light-9);\r\n\r\n  .GlobalGroupVoteBody {\r\n    width: 380px;\r\n    height: 100%;\r\n\r\n    &.GlobalGroupVoteBodyActive {\r\n      width: 320px;\r\n    }\r\n\r\n    .GlobalGroupVoteTitleBody {\r\n      width: 100%;\r\n      height: 56px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      background: #fff;\r\n      padding: 0 20px;\r\n\r\n      .GlobalGroupVoteTitle {\r\n        font-size: var(--zy-name-font-size);\r\n        line-height: var(--zy-line-height);\r\n      }\r\n\r\n      .GlobalGroupVoteTitleIcon {\r\n        width: 32px;\r\n        height: 32px;\r\n        cursor: pointer;\r\n      }\r\n    }\r\n\r\n    .GlobalGroupVoteScrollbar {\r\n      width: 100%;\r\n      height: calc(100% - 56px);\r\n\r\n      .GlobalGroupVoteScroll {\r\n        width: 100%;\r\n        padding: 16px;\r\n      }\r\n    }\r\n\r\n    .GlobalGroupVoteItem {\r\n      padding: 20px;\r\n      background: #fff;\r\n      border-radius: 6px;\r\n      box-shadow: var(--zy-el-box-shadow);\r\n      margin-bottom: 16px;\r\n      cursor: pointer;\r\n      position: relative;\r\n      overflow: hidden;\r\n\r\n      &.GlobalGroupVoteItemHasVote {\r\n        .GlobalGroupVoteItemHasIcon {\r\n          display: flex;\r\n        }\r\n\r\n        .GlobalGroupVoteItemTitle {\r\n          .GlobalGroupVoteItemTitleIcon {\r\n            display: none;\r\n          }\r\n        }\r\n      }\r\n\r\n      .GlobalGroupVoteItemHasIcon {\r\n        width: 42px;\r\n        height: 42px;\r\n        display: none;\r\n        align-items: center;\r\n        justify-content: center;\r\n        position: absolute;\r\n        top: 0;\r\n        left: 2px;\r\n        z-index: 9;\r\n      }\r\n\r\n      .GlobalGroupVoteItemTitle {\r\n        width: 100%;\r\n        display: flex;\r\n        align-items: flex-end;\r\n        justify-content: space-between;\r\n        position: relative;\r\n\r\n        .GlobalGroupVoteItemTitleIcon {\r\n          position: absolute;\r\n          top: 50%;\r\n          left: 0;\r\n          transform: translateY(-50%);\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          margin-right: 6px;\r\n\r\n          path {\r\n            fill: var(--zy-el-color-warning);\r\n          }\r\n        }\r\n\r\n        .GlobalGroupVoteItemTitleTime {\r\n          padding-left: 22px;\r\n          font-size: var(--zy-text-font-size);\r\n          color: var(--zy-el-text-color-secondary);\r\n        }\r\n\r\n        .is-primary,\r\n        .is-warning,\r\n        .is-info {\r\n          font-size: 12px;\r\n          padding: 2px 6px;\r\n          border-radius: 2px;\r\n        }\r\n\r\n        .is-primary {\r\n          color: var(--zy-el-color-primary);\r\n          background: var(--zy-el-color-primary-light-9);\r\n        }\r\n\r\n        .is-warning {\r\n          color: var(--zy-el-color-warning);\r\n          background: var(--zy-el-color-warning-light-9);\r\n        }\r\n\r\n        .is-info {\r\n          color: var(--zy-el-color-info);\r\n          background: var(--zy-el-color-info-light-9);\r\n        }\r\n      }\r\n\r\n      .GlobalGroupVoteItemName {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        padding-top: 6px;\r\n      }\r\n\r\n      .GlobalGroupVoteItemImg {\r\n        width: 100%;\r\n        padding-top: 6px;\r\n\r\n        .zy-el-image {\r\n          width: 100%;\r\n          height: 122px;\r\n          border-radius: 6px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalGroupVoteLoadingText {\r\n      width: 100%;\r\n      text-align: center;\r\n      color: var(--zy-el-text-color-secondary);\r\n      font-size: var(--zy-text-font-size);\r\n      line-height: var(--zy-line-height);\r\n    }\r\n  }\r\n\r\n  .GlobalVoteDetails {\r\n    position: relative;\r\n    background: #fff;\r\n\r\n    &::after {\r\n      content: \"\";\r\n      width: 1px;\r\n      height: 100%;\r\n      position: absolute;\r\n      left: 0;\r\n      bottom: 0;\r\n      background: var(--zy-el-border-color-lighter);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EAEnBA,KAAK,EAAC;AAA0B;iBAH3C;;EAQaA,KAAK,EAAC;AAAuB;iBAR1C;iBAAA;;EAYiBA,KAAK,EAAC;AAA0B;iBAZjD;;EAcmBA,KAAK,EAAC;AAA8B;;EAdvDC,GAAA;EAeoBD,KAAK,EAAC;;;EAf1BC,GAAA;EAgBoBD,KAAK,EAAC;;;EAhB1BC,GAAA;EAiBoBD,KAAK,EAAC;;;EAETA,KAAK,EAAC;AAAyB;;EAnBhDC,GAAA;EAoBiBD,KAAK,EAAC;;;EApBvBC,GAAA;EAwBeD,KAAK,EAAC;;;EAxBrBC,GAAA;EAyBeD,KAAK,EAAC;;;;;uBAxBnBE,mBAAA,CA6BM,OA7BNC,UA6BM,GA5BJC,mBAAA,CA0BM;IA1BDJ,KAAK,EAFdK,eAAA,EAEe,qBAAqB;MAAA,6BAAwCC,MAAA,CAAAC;IAAE;MACxEH,mBAAA,CAGM,OAHNI,UAGM,G,0BAFJJ,mBAAA,CAA2C;IAAtCJ,KAAK,EAAC;EAAsB,GAAC,KAAG,sBACrCI,mBAAA,CAAiG;IAA5FJ,KAAK,EAAC,0BAA0B;IAACS,SAA0B,EAAlBH,MAAA,CAAAI,iBAAiB;IAAGC,OAAK,EAAEL,MAAA,CAAAM;0BALjFC,UAAA,E,GAOMC,YAAA,CAoBeC,uBAAA;IApBDC,GAAG,EAAC,WAAW;IAAChB,KAAK,EAAC,0BAA0B;IAAEiB,QAAM,EAAEX,MAAA,CAAAY;;IAP9EC,OAAA,EAAAC,QAAA,CAQQ;MAAA,OAkBM,CAlBNhB,mBAAA,CAkBM,OAlBNiB,UAkBM,I,kBAjBJnB,mBAAA,CAcMoB,SAAA,QAvBhBC,WAAA,CAU2BjB,MAAA,CAAAkB,SAAS,EAVpC,UAUmBC,IAAI;6BADbvB,mBAAA,CAcM;UAdDF,KAAK,EATpBK,eAAA,EASqB,qBAAqB;YAAA,8BAAyCoB,IAAI,CAACC;UAAO;UACxDzB,GAAG,EAAEwB,IAAI,CAAClB,EAAE;UAAGI,OAAK,WAALA,OAAKA,CAAAgB,MAAA;YAAA,OAAErB,MAAA,CAAAsB,WAAW,CAACH,IAAI;UAAA;YACjErB,mBAAA,CAAmE;UAA9DJ,KAAK,EAAC,4BAA4B;UAACS,SAAoB,EAAZH,MAAA,CAAAuB;gCAX5DC,UAAA,GAYY1B,mBAAA,CAMM,OANN2B,UAMM,GALJ3B,mBAAA,CAAsE;UAAjEJ,KAAK,EAAC,8BAA8B;UAACS,SAAqB,EAAbH,MAAA,CAAA0B;gCAbhEC,UAAA,GAcc7B,mBAAA,CAA6E,OAA7E8B,UAA6E,EAAAC,gBAAA,CAAhC7B,MAAA,CAAA8B,MAAM,CAACX,IAAI,CAACY,UAAU,mBACpCZ,IAAI,CAACa,UAAU,c,cAA9CpC,mBAAA,CAAsF,QAAtFqC,WAAsF,EAAAJ,gBAAA,CAAzBV,IAAI,CAACa,UAAU,oBAf1FE,mBAAA,gBAgB6Cf,IAAI,CAACa,UAAU,c,cAA9CpC,mBAAA,CAAsF,QAAtFuC,WAAsF,EAAAN,gBAAA,CAAzBV,IAAI,CAACa,UAAU,oBAhB1FE,mBAAA,gBAiB0Cf,IAAI,CAACa,UAAU,c,cAA3CpC,mBAAA,CAAmF,QAAnFwC,WAAmF,EAAAP,gBAAA,CAAzBV,IAAI,CAACa,UAAU,oBAjBvFE,mBAAA,e,GAmBYpC,mBAAA,CAA2D,OAA3DuC,WAA2D,EAAAR,gBAAA,CAAnBV,IAAI,CAACmB,KAAK,kBACRnB,IAAI,CAACoB,QAAQ,I,cAAvD3C,mBAAA,CAEM,OAFN4C,WAEM,GADJhC,YAAA,CAAuEiC,mBAAA;UAA5DC,GAAG,EAAE1C,MAAA,CAAA2C,MAAM,CAACxB,IAAI,CAACoB,QAAQ;UAAGK,GAAG,EAAC,OAAO;UAACC,SAAS,EAAC;8CArB3EX,mBAAA,e,yBAAAY,UAAA;sCAwBwD9C,MAAA,CAAA+C,OAAO,I,cAArDnD,mBAAA,CAAmE,OAAnEoD,WAAmE,EAAZ,QAAM,KAxBvEd,mBAAA,gBAyBwDlC,MAAA,CAAAiD,MAAM,I,cAApDrD,mBAAA,CAAiE,OAAjEsD,WAAiE,EAAX,OAAK,KAzBrEhB,mBAAA,e;;IAAAiB,CAAA;6CA6BsCnD,MAAA,CAAAC,EAAE,I,cAApCmD,YAAA,CAAgHpD,MAAA;IA7BpHL,GAAA;IA6BwBM,EAAE,EAAED,MAAA,CAAAC,EAAE;IAAaoD,UAAQ,EAAErD,MAAA,CAAAsD,QAAQ;IAAGC,aAAW,EAAEvD,MAAA,CAAAwD;qCA7B7EtB,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}