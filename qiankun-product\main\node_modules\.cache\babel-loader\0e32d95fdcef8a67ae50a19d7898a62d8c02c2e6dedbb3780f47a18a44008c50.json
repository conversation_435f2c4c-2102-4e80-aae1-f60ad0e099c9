{"ast": null, "code": "\"use strict\";\n\nfunction getMinFromArrays(arrays, state) {\n  var minIndex = -1;\n  for (var i = 0, l = arrays.length; i < l; i++) {\n    if (state[i] >= arrays[i].length) {\n      continue;\n    }\n    if (minIndex === -1 || arrays[i][state[i]].offset < arrays[minIndex][state[minIndex]].offset) {\n      minIndex = i;\n    }\n  }\n  return minIndex;\n}\nmodule.exports = function (arrays) {\n  var totalLength = arrays.reduce(function (sum, array) {\n    return sum + array.length;\n  }, 0);\n  arrays = arrays.filter(function (array) {\n    return array.length > 0;\n  });\n  var resultArray = new Array(totalLength);\n  var state = arrays.map(function () {\n    return 0;\n  });\n  for (var i = 0; i < totalLength; i++) {\n    var arrayIndex = getMinFromArrays(arrays, state);\n    resultArray[i] = arrays[arrayIndex][state[arrayIndex]];\n    state[arrayIndex]++;\n  }\n  return resultArray;\n};", "map": {"version": 3, "names": ["getMinFromArrays", "arrays", "state", "minIndex", "i", "l", "length", "offset", "module", "exports", "totalLength", "reduce", "sum", "array", "filter", "resultArray", "Array", "map", "arrayIndex"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/docxtemplater@3.52.0/node_modules/docxtemplater/js/merge-sort.js"], "sourcesContent": ["\"use strict\";\n\nfunction getMinFromArrays(arrays, state) {\n  var minIndex = -1;\n  for (var i = 0, l = arrays.length; i < l; i++) {\n    if (state[i] >= arrays[i].length) {\n      continue;\n    }\n    if (minIndex === -1 || arrays[i][state[i]].offset < arrays[minIndex][state[minIndex]].offset) {\n      minIndex = i;\n    }\n  }\n  return minIndex;\n}\nmodule.exports = function (arrays) {\n  var totalLength = arrays.reduce(function (sum, array) {\n    return sum + array.length;\n  }, 0);\n  arrays = arrays.filter(function (array) {\n    return array.length > 0;\n  });\n  var resultArray = new Array(totalLength);\n  var state = arrays.map(function () {\n    return 0;\n  });\n  for (var i = 0; i < totalLength; i++) {\n    var arrayIndex = getMinFromArrays(arrays, state);\n    resultArray[i] = arrays[arrayIndex][state[arrayIndex]];\n    state[arrayIndex]++;\n  }\n  return resultArray;\n};"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,gBAAgBA,CAACC,MAAM,EAAEC,KAAK,EAAE;EACvC,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGJ,MAAM,CAACK,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;IAC7C,IAAIF,KAAK,CAACE,CAAC,CAAC,IAAIH,MAAM,CAACG,CAAC,CAAC,CAACE,MAAM,EAAE;MAChC;IACF;IACA,IAAIH,QAAQ,KAAK,CAAC,CAAC,IAAIF,MAAM,CAACG,CAAC,CAAC,CAACF,KAAK,CAACE,CAAC,CAAC,CAAC,CAACG,MAAM,GAAGN,MAAM,CAACE,QAAQ,CAAC,CAACD,KAAK,CAACC,QAAQ,CAAC,CAAC,CAACI,MAAM,EAAE;MAC5FJ,QAAQ,GAAGC,CAAC;IACd;EACF;EACA,OAAOD,QAAQ;AACjB;AACAK,MAAM,CAACC,OAAO,GAAG,UAAUR,MAAM,EAAE;EACjC,IAAIS,WAAW,GAAGT,MAAM,CAACU,MAAM,CAAC,UAAUC,GAAG,EAAEC,KAAK,EAAE;IACpD,OAAOD,GAAG,GAAGC,KAAK,CAACP,MAAM;EAC3B,CAAC,EAAE,CAAC,CAAC;EACLL,MAAM,GAAGA,MAAM,CAACa,MAAM,CAAC,UAAUD,KAAK,EAAE;IACtC,OAAOA,KAAK,CAACP,MAAM,GAAG,CAAC;EACzB,CAAC,CAAC;EACF,IAAIS,WAAW,GAAG,IAAIC,KAAK,CAACN,WAAW,CAAC;EACxC,IAAIR,KAAK,GAAGD,MAAM,CAACgB,GAAG,CAAC,YAAY;IACjC,OAAO,CAAC;EACV,CAAC,CAAC;EACF,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGM,WAAW,EAAEN,CAAC,EAAE,EAAE;IACpC,IAAIc,UAAU,GAAGlB,gBAAgB,CAACC,MAAM,EAAEC,KAAK,CAAC;IAChDa,WAAW,CAACX,CAAC,CAAC,GAAGH,MAAM,CAACiB,UAAU,CAAC,CAAChB,KAAK,CAACgB,UAAU,CAAC,CAAC;IACtDhB,KAAK,CAACgB,UAAU,CAAC,EAAE;EACrB;EACA,OAAOH,WAAW;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}