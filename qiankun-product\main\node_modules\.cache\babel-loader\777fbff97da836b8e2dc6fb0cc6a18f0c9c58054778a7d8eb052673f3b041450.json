{"ast": null, "code": "import '../../../utils/index.mjs';\nimport { buildProps } from '../../../utils/vue/props/runtime.mjs';\nimport { iconPropType } from '../../../utils/vue/icon.mjs';\nvar timelineItemProps = buildProps({\n  timestamp: {\n    type: String,\n    default: \"\"\n  },\n  hideTimestamp: {\n    type: Boolean,\n    default: false\n  },\n  center: {\n    type: Boolean,\n    default: false\n  },\n  placement: {\n    type: String,\n    values: [\"top\", \"bottom\"],\n    default: \"bottom\"\n  },\n  type: {\n    type: String,\n    values: [\"primary\", \"success\", \"warning\", \"danger\", \"info\"],\n    default: \"\"\n  },\n  color: {\n    type: String,\n    default: \"\"\n  },\n  size: {\n    type: String,\n    values: [\"normal\", \"large\"],\n    default: \"normal\"\n  },\n  icon: {\n    type: iconPropType\n  },\n  hollow: {\n    type: Boolean,\n    default: false\n  }\n});\nexport { timelineItemProps };", "map": {"version": 3, "names": ["timelineItemProps", "buildProps", "timestamp", "type", "String", "default", "hideTimestamp", "Boolean", "center", "placement", "values", "color", "size", "icon", "iconPropType", "hollow"], "sources": ["../../../../../../packages/components/timeline/src/timeline-item.ts"], "sourcesContent": ["import { buildProps, iconPropType } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type TimelineItem from './timeline-item.vue'\n\nexport const timelineItemProps = buildProps({\n  /**\n   * @description timestamp content\n   */\n  timestamp: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description whether to show timestamp\n   */\n  hideTimestamp: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description whether vertically centered\n   */\n  center: {\n    type: Boolean,\n    default: false,\n  },\n  /**\n   * @description position of timestamp\n   */\n  placement: {\n    type: String,\n    values: ['top', 'bottom'],\n    default: 'bottom',\n  },\n  /**\n   * @description node type\n   */\n  type: {\n    type: String,\n    values: ['primary', 'success', 'warning', 'danger', 'info'],\n    default: '',\n  },\n  /**\n   * @description background color of node\n   */\n  color: {\n    type: String,\n    default: '',\n  },\n  /**\n   * @description node size\n   */\n  size: {\n    type: String,\n    values: ['normal', 'large'],\n    default: 'normal',\n  },\n  /**\n   * @description icon component\n   */\n  icon: {\n    type: iconPropType,\n  },\n  /**\n   * @description icon is hollow\n   */\n  hollow: {\n    type: Boolean,\n    default: false,\n  },\n} as const)\nexport type TimelineItemProps = ExtractPropTypes<typeof timelineItemProps>\n\nexport type TimelineItemInstance = InstanceType<typeof TimelineItem>\n"], "mappings": ";;;AACY,IAACA,iBAAiB,GAAGC,UAAU,CAAC;EAC1CC,SAAS,EAAE;IACTC,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDC,aAAa,EAAE;IACbH,IAAI,EAAEI,OAAO;IACbF,OAAO,EAAE;EACb,CAAG;EACDG,MAAM,EAAE;IACNL,IAAI,EAAEI,OAAO;IACbF,OAAO,EAAE;EACb,CAAG;EACDI,SAAS,EAAE;IACTN,IAAI,EAAEC,MAAM;IACZM,MAAM,EAAE,CAAC,KAAK,EAAE,QAAQ,CAAC;IACzBL,OAAO,EAAE;EACb,CAAG;EACDF,IAAI,EAAE;IACJA,IAAI,EAAEC,MAAM;IACZM,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,MAAM,CAAC;IAC3DL,OAAO,EAAE;EACb,CAAG;EACDM,KAAK,EAAE;IACLR,IAAI,EAAEC,MAAM;IACZC,OAAO,EAAE;EACb,CAAG;EACDO,IAAI,EAAE;IACJT,IAAI,EAAEC,MAAM;IACZM,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;IAC3BL,OAAO,EAAE;EACb,CAAG;EACDQ,IAAI,EAAE;IACJV,IAAI,EAAEW;EACV,CAAG;EACDC,MAAM,EAAE;IACNZ,IAAI,EAAEI,OAAO;IACbF,OAAO,EAAE;EACb;AACA,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}