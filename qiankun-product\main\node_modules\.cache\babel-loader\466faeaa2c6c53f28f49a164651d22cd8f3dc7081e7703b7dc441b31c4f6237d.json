{"ast": null, "code": "\"use strict\";\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nvar nbspRegex = new RegExp(String.fromCharCode(160), \"g\");\nfunction replaceNbsps(str) {\n  return str.replace(nbspRegex, \" \");\n}\nfunction match(condition, placeHolderContent) {\n  var type = _typeof(condition);\n  if (type === \"string\") {\n    return replaceNbsps(placeHolderContent.substr(0, condition.length)) === condition;\n  }\n  if (condition instanceof RegExp) {\n    return condition.test(replaceNbsps(placeHolderContent));\n  }\n  if (type === \"function\") {\n    return !!condition(placeHolderContent);\n  }\n}\nfunction getValue(condition, placeHolderContent) {\n  var type = _typeof(condition);\n  if (type === \"string\") {\n    return replaceNbsps(placeHolderContent).substr(condition.length);\n  }\n  if (condition instanceof RegExp) {\n    return replaceNbsps(placeHolderContent).match(condition)[1];\n  }\n  if (type === \"function\") {\n    return condition(placeHolderContent);\n  }\n}\nfunction getValues(condition, placeHolderContent) {\n  var type = _typeof(condition);\n  if (type === \"string\") {\n    return [placeHolderContent, replaceNbsps(placeHolderContent).substr(condition.length)];\n  }\n  if (condition instanceof RegExp) {\n    return replaceNbsps(placeHolderContent).match(condition);\n  }\n  if (type === \"function\") {\n    return [placeHolderContent, condition(placeHolderContent)];\n  }\n}\nmodule.exports = {\n  match: match,\n  getValue: getValue,\n  getValues: getValues\n};", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "nbspRegex", "RegExp", "String", "fromCharCode", "replaceNbsps", "str", "replace", "match", "condition", "placeHolderContent", "type", "substr", "length", "test", "getValue", "getV<PERSON>ues", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/docxtemplater@3.52.0/node_modules/docxtemplater/js/prefix-matcher.js"], "sourcesContent": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nvar nbspRegex = new RegExp(String.fromCharCode(160), \"g\");\nfunction replaceNbsps(str) {\n  return str.replace(nbspRegex, \" \");\n}\nfunction match(condition, placeHolderContent) {\n  var type = _typeof(condition);\n  if (type === \"string\") {\n    return replaceNbsps(placeHolderContent.substr(0, condition.length)) === condition;\n  }\n  if (condition instanceof RegExp) {\n    return condition.test(replaceNbsps(placeHolderContent));\n  }\n  if (type === \"function\") {\n    return !!condition(placeHolderContent);\n  }\n}\nfunction getValue(condition, placeHolderContent) {\n  var type = _typeof(condition);\n  if (type === \"string\") {\n    return replaceNbsps(placeHolderContent).substr(condition.length);\n  }\n  if (condition instanceof RegExp) {\n    return replaceNbsps(placeHolderContent).match(condition)[1];\n  }\n  if (type === \"function\") {\n    return condition(placeHolderContent);\n  }\n}\nfunction getValues(condition, placeHolderContent) {\n  var type = _typeof(condition);\n  if (type === \"string\") {\n    return [placeHolderContent, replaceNbsps(placeHolderContent).substr(condition.length)];\n  }\n  if (condition instanceof RegExp) {\n    return replaceNbsps(placeHolderContent).match(condition);\n  }\n  if (type === \"function\") {\n    return [placeHolderContent, condition(placeHolderContent)];\n  }\n}\nmodule.exports = {\n  match: match,\n  getValue: getValue,\n  getValues: getValues\n};"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,IAAIK,SAAS,GAAG,IAAIC,MAAM,CAACC,MAAM,CAACC,YAAY,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;AACzD,SAASC,YAAYA,CAACC,GAAG,EAAE;EACzB,OAAOA,GAAG,CAACC,OAAO,CAACN,SAAS,EAAE,GAAG,CAAC;AACpC;AACA,SAASO,KAAKA,CAACC,SAAS,EAAEC,kBAAkB,EAAE;EAC5C,IAAIC,IAAI,GAAGhB,OAAO,CAACc,SAAS,CAAC;EAC7B,IAAIE,IAAI,KAAK,QAAQ,EAAE;IACrB,OAAON,YAAY,CAACK,kBAAkB,CAACE,MAAM,CAAC,CAAC,EAAEH,SAAS,CAACI,MAAM,CAAC,CAAC,KAAKJ,SAAS;EACnF;EACA,IAAIA,SAAS,YAAYP,MAAM,EAAE;IAC/B,OAAOO,SAAS,CAACK,IAAI,CAACT,YAAY,CAACK,kBAAkB,CAAC,CAAC;EACzD;EACA,IAAIC,IAAI,KAAK,UAAU,EAAE;IACvB,OAAO,CAAC,CAACF,SAAS,CAACC,kBAAkB,CAAC;EACxC;AACF;AACA,SAASK,QAAQA,CAACN,SAAS,EAAEC,kBAAkB,EAAE;EAC/C,IAAIC,IAAI,GAAGhB,OAAO,CAACc,SAAS,CAAC;EAC7B,IAAIE,IAAI,KAAK,QAAQ,EAAE;IACrB,OAAON,YAAY,CAACK,kBAAkB,CAAC,CAACE,MAAM,CAACH,SAAS,CAACI,MAAM,CAAC;EAClE;EACA,IAAIJ,SAAS,YAAYP,MAAM,EAAE;IAC/B,OAAOG,YAAY,CAACK,kBAAkB,CAAC,CAACF,KAAK,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC;EAC7D;EACA,IAAIE,IAAI,KAAK,UAAU,EAAE;IACvB,OAAOF,SAAS,CAACC,kBAAkB,CAAC;EACtC;AACF;AACA,SAASM,SAASA,CAACP,SAAS,EAAEC,kBAAkB,EAAE;EAChD,IAAIC,IAAI,GAAGhB,OAAO,CAACc,SAAS,CAAC;EAC7B,IAAIE,IAAI,KAAK,QAAQ,EAAE;IACrB,OAAO,CAACD,kBAAkB,EAAEL,YAAY,CAACK,kBAAkB,CAAC,CAACE,MAAM,CAACH,SAAS,CAACI,MAAM,CAAC,CAAC;EACxF;EACA,IAAIJ,SAAS,YAAYP,MAAM,EAAE;IAC/B,OAAOG,YAAY,CAACK,kBAAkB,CAAC,CAACF,KAAK,CAACC,SAAS,CAAC;EAC1D;EACA,IAAIE,IAAI,KAAK,UAAU,EAAE;IACvB,OAAO,CAACD,kBAAkB,EAAED,SAAS,CAACC,kBAAkB,CAAC,CAAC;EAC5D;AACF;AACAO,MAAM,CAACC,OAAO,GAAG;EACfV,KAAK,EAAEA,KAAK;EACZO,QAAQ,EAAEA,QAAQ;EAClBC,SAAS,EAAEA;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}