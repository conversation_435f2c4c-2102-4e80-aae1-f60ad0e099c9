{"ast": null, "code": "// Utilities\n//\n'use strict';\n\nfunction _class(obj) {\n  return Object.prototype.toString.call(obj);\n}\nfunction isString(obj) {\n  return _class(obj) === '[object String]';\n}\nvar _hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction has(object, key) {\n  return _hasOwnProperty.call(object, key);\n}\n\n// Merge objects\n//\nfunction assign(obj /*from1, from2, from3, ...*/) {\n  var sources = Array.prototype.slice.call(arguments, 1);\n  sources.forEach(function (source) {\n    if (!source) {\n      return;\n    }\n    if (typeof source !== 'object') {\n      throw new TypeError(source + 'must be object');\n    }\n    Object.keys(source).forEach(function (key) {\n      obj[key] = source[key];\n    });\n  });\n  return obj;\n}\n\n// Remove element from array and put another array at those position.\n// Useful for some operations with tokens\nfunction arrayReplaceAt(src, pos, newElements) {\n  return [].concat(src.slice(0, pos), newElements, src.slice(pos + 1));\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\nfunction isValidEntityCode(c) {\n  /*eslint no-bitwise:0*/\n  // broken sequence\n  if (c >= 0xD800 && c <= 0xDFFF) {\n    return false;\n  }\n  // never used\n  if (c >= 0xFDD0 && c <= 0xFDEF) {\n    return false;\n  }\n  if ((c & 0xFFFF) === 0xFFFF || (c & 0xFFFF) === 0xFFFE) {\n    return false;\n  }\n  // control codes\n  if (c >= 0x00 && c <= 0x08) {\n    return false;\n  }\n  if (c === 0x0B) {\n    return false;\n  }\n  if (c >= 0x0E && c <= 0x1F) {\n    return false;\n  }\n  if (c >= 0x7F && c <= 0x9F) {\n    return false;\n  }\n  // out of range\n  if (c > 0x10FFFF) {\n    return false;\n  }\n  return true;\n}\nfunction fromCodePoint(c) {\n  /*eslint no-bitwise:0*/\n  if (c > 0xffff) {\n    c -= 0x10000;\n    var surrogate1 = 0xd800 + (c >> 10),\n      surrogate2 = 0xdc00 + (c & 0x3ff);\n    return String.fromCharCode(surrogate1, surrogate2);\n  }\n  return String.fromCharCode(c);\n}\nvar UNESCAPE_MD_RE = /\\\\([!\"#$%&'()*+,\\-.\\/:;<=>?@[\\\\\\]^_`{|}~])/g;\nvar ENTITY_RE = /&([a-z#][a-z0-9]{1,31});/gi;\nvar UNESCAPE_ALL_RE = new RegExp(UNESCAPE_MD_RE.source + '|' + ENTITY_RE.source, 'gi');\nvar DIGITAL_ENTITY_TEST_RE = /^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))/i;\nvar entities = require('./entities');\nfunction replaceEntityPattern(match, name) {\n  var code = 0;\n  if (has(entities, name)) {\n    return entities[name];\n  }\n  if (name.charCodeAt(0) === 0x23 /* # */ && DIGITAL_ENTITY_TEST_RE.test(name)) {\n    code = name[1].toLowerCase() === 'x' ? parseInt(name.slice(2), 16) : parseInt(name.slice(1), 10);\n    if (isValidEntityCode(code)) {\n      return fromCodePoint(code);\n    }\n  }\n  return match;\n}\n\n/*function replaceEntities(str) {\n  if (str.indexOf('&') < 0) { return str; }\n\n  return str.replace(ENTITY_RE, replaceEntityPattern);\n}*/\n\nfunction unescapeMd(str) {\n  if (str.indexOf('\\\\') < 0) {\n    return str;\n  }\n  return str.replace(UNESCAPE_MD_RE, '$1');\n}\nfunction unescapeAll(str) {\n  if (str.indexOf('\\\\') < 0 && str.indexOf('&') < 0) {\n    return str;\n  }\n  return str.replace(UNESCAPE_ALL_RE, function (match, escaped, entity) {\n    if (escaped) {\n      return escaped;\n    }\n    return replaceEntityPattern(match, entity);\n  });\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\nvar HTML_ESCAPE_TEST_RE = /[&<>\"]/;\nvar HTML_ESCAPE_REPLACE_RE = /[&<>\"]/g;\nvar HTML_REPLACEMENTS = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;'\n};\nfunction replaceUnsafeChar(ch) {\n  return HTML_REPLACEMENTS[ch];\n}\nfunction escapeHtml(str) {\n  if (HTML_ESCAPE_TEST_RE.test(str)) {\n    return str.replace(HTML_ESCAPE_REPLACE_RE, replaceUnsafeChar);\n  }\n  return str;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\nvar REGEXP_ESCAPE_RE = /[.?*+^$[\\]\\\\(){}|-]/g;\nfunction escapeRE(str) {\n  return str.replace(REGEXP_ESCAPE_RE, '\\\\$&');\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\nfunction isSpace(code) {\n  switch (code) {\n    case 0x09:\n    case 0x20:\n      return true;\n  }\n  return false;\n}\n\n// Zs (unicode class) || [\\t\\f\\v\\r\\n]\nfunction isWhiteSpace(code) {\n  if (code >= 0x2000 && code <= 0x200A) {\n    return true;\n  }\n  switch (code) {\n    case 0x09: // \\t\n    case 0x0A: // \\n\n    case 0x0B: // \\v\n    case 0x0C: // \\f\n    case 0x0D: // \\r\n    case 0x20:\n    case 0xA0:\n    case 0x1680:\n    case 0x202F:\n    case 0x205F:\n    case 0x3000:\n      return true;\n  }\n  return false;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\n/*eslint-disable max-len*/\nvar UNICODE_PUNCT_RE = require('uc.micro/categories/P/regex');\n\n// Currently without astral characters support.\nfunction isPunctChar(ch) {\n  return UNICODE_PUNCT_RE.test(ch);\n}\n\n// Markdown ASCII punctuation characters.\n//\n// !, \", #, $, %, &, ', (, ), *, +, ,, -, ., /, :, ;, <, =, >, ?, @, [, \\, ], ^, _, `, {, |, }, or ~\n// http://spec.commonmark.org/0.15/#ascii-punctuation-character\n//\n// Don't confuse with unicode punctuation !!! It lacks some chars in ascii range.\n//\nfunction isMdAsciiPunct(ch) {\n  switch (ch) {\n    case 0x21 /* ! */:\n    case 0x22 /* \" */:\n    case 0x23 /* # */:\n    case 0x24 /* $ */:\n    case 0x25 /* % */:\n    case 0x26 /* & */:\n    case 0x27 /* ' */:\n    case 0x28 /* ( */:\n    case 0x29 /* ) */:\n    case 0x2A /* * */:\n    case 0x2B /* + */:\n    case 0x2C /* , */:\n    case 0x2D /* - */:\n    case 0x2E /* . */:\n    case 0x2F /* / */:\n    case 0x3A /* : */:\n    case 0x3B /* ; */:\n    case 0x3C /* < */:\n    case 0x3D /* = */:\n    case 0x3E /* > */:\n    case 0x3F /* ? */:\n    case 0x40 /* @ */:\n    case 0x5B /* [ */:\n    case 0x5C /* \\ */:\n    case 0x5D /* ] */:\n    case 0x5E /* ^ */:\n    case 0x5F /* _ */:\n    case 0x60 /* ` */:\n    case 0x7B /* { */:\n    case 0x7C /* | */:\n    case 0x7D /* } */:\n    case 0x7E /* ~ */:\n      return true;\n    default:\n      return false;\n  }\n}\n\n// Hepler to unify [reference labels].\n//\nfunction normalizeReference(str) {\n  // Trim and collapse whitespace\n  //\n  str = str.trim().replace(/\\s+/g, ' ');\n\n  // In node v10 'ẞ'.toLowerCase() === 'Ṿ', which is presumed to be a bug\n  // fixed in v12 (couldn't find any details).\n  //\n  // So treat this one as a special case\n  // (remove this when node v10 is no longer supported).\n  //\n  if ('ẞ'.toLowerCase() === 'Ṿ') {\n    str = str.replace(/ẞ/g, 'ß');\n  }\n\n  // .toLowerCase().toUpperCase() should get rid of all differences\n  // between letter variants.\n  //\n  // Simple .toLowerCase() doesn't normalize 125 code points correctly,\n  // and .toUpperCase doesn't normalize 6 of them (list of exceptions:\n  // İ, ϴ, ẞ, Ω, K, Å - those are already uppercased, but have differently\n  // uppercased versions).\n  //\n  // Here's an example showing how it happens. Lets take greek letter omega:\n  // uppercase U+0398 (Θ), U+03f4 (ϴ) and lowercase U+03b8 (θ), U+03d1 (ϑ)\n  //\n  // Unicode entries:\n  // 0398;GREEK CAPITAL LETTER THETA;Lu;0;L;;;;;N;;;;03B8;\n  // 03B8;GREEK SMALL LETTER THETA;Ll;0;L;;;;;N;;;0398;;0398\n  // 03D1;GREEK THETA SYMBOL;Ll;0;L;<compat> 03B8;;;;N;GREEK SMALL LETTER SCRIPT THETA;;0398;;0398\n  // 03F4;GREEK CAPITAL THETA SYMBOL;Lu;0;L;<compat> 0398;;;;N;;;;03B8;\n  //\n  // Case-insensitive comparison should treat all of them as equivalent.\n  //\n  // But .toLowerCase() doesn't change ϑ (it's already lowercase),\n  // and .toUpperCase() doesn't change ϴ (already uppercase).\n  //\n  // Applying first lower then upper case normalizes any character:\n  // '\\u0398\\u03f4\\u03b8\\u03d1'.toLowerCase().toUpperCase() === '\\u0398\\u0398\\u0398\\u0398'\n  //\n  // Note: this is equivalent to unicode case folding; unicode normalization\n  // is a different step that is not required here.\n  //\n  // Final result should be uppercased, because it's later stored in an object\n  // (this avoid a conflict with Object.prototype members,\n  // most notably, `__proto__`)\n  //\n  return str.toLowerCase().toUpperCase();\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\n// Re-export libraries commonly used in both markdown-it and its plugins,\n// so plugins won't have to depend on them explicitly, which reduces their\n// bundled size (e.g. a browser build).\n//\nexports.lib = {};\nexports.lib.mdurl = require('mdurl');\nexports.lib.ucmicro = require('uc.micro');\nexports.assign = assign;\nexports.isString = isString;\nexports.has = has;\nexports.unescapeMd = unescapeMd;\nexports.unescapeAll = unescapeAll;\nexports.isValidEntityCode = isValidEntityCode;\nexports.fromCodePoint = fromCodePoint;\n// exports.replaceEntities     = replaceEntities;\nexports.escapeHtml = escapeHtml;\nexports.arrayReplaceAt = arrayReplaceAt;\nexports.isSpace = isSpace;\nexports.isWhiteSpace = isWhiteSpace;\nexports.isMdAsciiPunct = isMdAsciiPunct;\nexports.isPunctChar = isPunctChar;\nexports.escapeRE = escapeRE;\nexports.normalizeReference = normalizeReference;", "map": {"version": 3, "names": ["_class", "obj", "Object", "prototype", "toString", "call", "isString", "_hasOwnProperty", "hasOwnProperty", "has", "object", "key", "assign", "sources", "Array", "slice", "arguments", "for<PERSON>ach", "source", "TypeError", "keys", "arrayReplaceAt", "src", "pos", "newElements", "concat", "isValidEntityCode", "c", "fromCodePoint", "surrogate1", "surrogate2", "String", "fromCharCode", "UNESCAPE_MD_RE", "ENTITY_RE", "UNESCAPE_ALL_RE", "RegExp", "DIGITAL_ENTITY_TEST_RE", "entities", "require", "replaceEntityPattern", "match", "name", "code", "charCodeAt", "test", "toLowerCase", "parseInt", "unescapeMd", "str", "indexOf", "replace", "unescapeAll", "escaped", "entity", "HTML_ESCAPE_TEST_RE", "HTML_ESCAPE_REPLACE_RE", "HTML_REPLACEMENTS", "replaceUnsafeChar", "ch", "escapeHtml", "REGEXP_ESCAPE_RE", "escapeRE", "isSpace", "isWhiteSpace", "UNICODE_PUNCT_RE", "isPunctChar", "isMdAsciiPunct", "normalizeReference", "trim", "toUpperCase", "exports", "lib", "mdurl", "ucmicro"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/common/utils.js"], "sourcesContent": ["// Utilities\n//\n'use strict';\n\n\nfunction _class(obj) { return Object.prototype.toString.call(obj); }\n\nfunction isString(obj) { return _class(obj) === '[object String]'; }\n\nvar _hasOwnProperty = Object.prototype.hasOwnProperty;\n\nfunction has(object, key) {\n  return _hasOwnProperty.call(object, key);\n}\n\n// Merge objects\n//\nfunction assign(obj /*from1, from2, from3, ...*/) {\n  var sources = Array.prototype.slice.call(arguments, 1);\n\n  sources.forEach(function (source) {\n    if (!source) { return; }\n\n    if (typeof source !== 'object') {\n      throw new TypeError(source + 'must be object');\n    }\n\n    Object.keys(source).forEach(function (key) {\n      obj[key] = source[key];\n    });\n  });\n\n  return obj;\n}\n\n// Remove element from array and put another array at those position.\n// Useful for some operations with tokens\nfunction arrayReplaceAt(src, pos, newElements) {\n  return [].concat(src.slice(0, pos), newElements, src.slice(pos + 1));\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\nfunction isValidEntityCode(c) {\n  /*eslint no-bitwise:0*/\n  // broken sequence\n  if (c >= 0xD800 && c <= 0xDFFF) { return false; }\n  // never used\n  if (c >= 0xFDD0 && c <= 0xFDEF) { return false; }\n  if ((c & 0xFFFF) === 0xFFFF || (c & 0xFFFF) === 0xFFFE) { return false; }\n  // control codes\n  if (c >= 0x00 && c <= 0x08) { return false; }\n  if (c === 0x0B) { return false; }\n  if (c >= 0x0E && c <= 0x1F) { return false; }\n  if (c >= 0x7F && c <= 0x9F) { return false; }\n  // out of range\n  if (c > 0x10FFFF) { return false; }\n  return true;\n}\n\nfunction fromCodePoint(c) {\n  /*eslint no-bitwise:0*/\n  if (c > 0xffff) {\n    c -= 0x10000;\n    var surrogate1 = 0xd800 + (c >> 10),\n        surrogate2 = 0xdc00 + (c & 0x3ff);\n\n    return String.fromCharCode(surrogate1, surrogate2);\n  }\n  return String.fromCharCode(c);\n}\n\n\nvar UNESCAPE_MD_RE  = /\\\\([!\"#$%&'()*+,\\-.\\/:;<=>?@[\\\\\\]^_`{|}~])/g;\nvar ENTITY_RE       = /&([a-z#][a-z0-9]{1,31});/gi;\nvar UNESCAPE_ALL_RE = new RegExp(UNESCAPE_MD_RE.source + '|' + ENTITY_RE.source, 'gi');\n\nvar DIGITAL_ENTITY_TEST_RE = /^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))/i;\n\nvar entities = require('./entities');\n\nfunction replaceEntityPattern(match, name) {\n  var code = 0;\n\n  if (has(entities, name)) {\n    return entities[name];\n  }\n\n  if (name.charCodeAt(0) === 0x23/* # */ && DIGITAL_ENTITY_TEST_RE.test(name)) {\n    code = name[1].toLowerCase() === 'x' ?\n      parseInt(name.slice(2), 16) : parseInt(name.slice(1), 10);\n\n    if (isValidEntityCode(code)) {\n      return fromCodePoint(code);\n    }\n  }\n\n  return match;\n}\n\n/*function replaceEntities(str) {\n  if (str.indexOf('&') < 0) { return str; }\n\n  return str.replace(ENTITY_RE, replaceEntityPattern);\n}*/\n\nfunction unescapeMd(str) {\n  if (str.indexOf('\\\\') < 0) { return str; }\n  return str.replace(UNESCAPE_MD_RE, '$1');\n}\n\nfunction unescapeAll(str) {\n  if (str.indexOf('\\\\') < 0 && str.indexOf('&') < 0) { return str; }\n\n  return str.replace(UNESCAPE_ALL_RE, function (match, escaped, entity) {\n    if (escaped) { return escaped; }\n    return replaceEntityPattern(match, entity);\n  });\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\nvar HTML_ESCAPE_TEST_RE = /[&<>\"]/;\nvar HTML_ESCAPE_REPLACE_RE = /[&<>\"]/g;\nvar HTML_REPLACEMENTS = {\n  '&': '&amp;',\n  '<': '&lt;',\n  '>': '&gt;',\n  '\"': '&quot;'\n};\n\nfunction replaceUnsafeChar(ch) {\n  return HTML_REPLACEMENTS[ch];\n}\n\nfunction escapeHtml(str) {\n  if (HTML_ESCAPE_TEST_RE.test(str)) {\n    return str.replace(HTML_ESCAPE_REPLACE_RE, replaceUnsafeChar);\n  }\n  return str;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\nvar REGEXP_ESCAPE_RE = /[.?*+^$[\\]\\\\(){}|-]/g;\n\nfunction escapeRE(str) {\n  return str.replace(REGEXP_ESCAPE_RE, '\\\\$&');\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\nfunction isSpace(code) {\n  switch (code) {\n    case 0x09:\n    case 0x20:\n      return true;\n  }\n  return false;\n}\n\n// Zs (unicode class) || [\\t\\f\\v\\r\\n]\nfunction isWhiteSpace(code) {\n  if (code >= 0x2000 && code <= 0x200A) { return true; }\n  switch (code) {\n    case 0x09: // \\t\n    case 0x0A: // \\n\n    case 0x0B: // \\v\n    case 0x0C: // \\f\n    case 0x0D: // \\r\n    case 0x20:\n    case 0xA0:\n    case 0x1680:\n    case 0x202F:\n    case 0x205F:\n    case 0x3000:\n      return true;\n  }\n  return false;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\n/*eslint-disable max-len*/\nvar UNICODE_PUNCT_RE = require('uc.micro/categories/P/regex');\n\n// Currently without astral characters support.\nfunction isPunctChar(ch) {\n  return UNICODE_PUNCT_RE.test(ch);\n}\n\n\n// Markdown ASCII punctuation characters.\n//\n// !, \", #, $, %, &, ', (, ), *, +, ,, -, ., /, :, ;, <, =, >, ?, @, [, \\, ], ^, _, `, {, |, }, or ~\n// http://spec.commonmark.org/0.15/#ascii-punctuation-character\n//\n// Don't confuse with unicode punctuation !!! It lacks some chars in ascii range.\n//\nfunction isMdAsciiPunct(ch) {\n  switch (ch) {\n    case 0x21/* ! */:\n    case 0x22/* \" */:\n    case 0x23/* # */:\n    case 0x24/* $ */:\n    case 0x25/* % */:\n    case 0x26/* & */:\n    case 0x27/* ' */:\n    case 0x28/* ( */:\n    case 0x29/* ) */:\n    case 0x2A/* * */:\n    case 0x2B/* + */:\n    case 0x2C/* , */:\n    case 0x2D/* - */:\n    case 0x2E/* . */:\n    case 0x2F/* / */:\n    case 0x3A/* : */:\n    case 0x3B/* ; */:\n    case 0x3C/* < */:\n    case 0x3D/* = */:\n    case 0x3E/* > */:\n    case 0x3F/* ? */:\n    case 0x40/* @ */:\n    case 0x5B/* [ */:\n    case 0x5C/* \\ */:\n    case 0x5D/* ] */:\n    case 0x5E/* ^ */:\n    case 0x5F/* _ */:\n    case 0x60/* ` */:\n    case 0x7B/* { */:\n    case 0x7C/* | */:\n    case 0x7D/* } */:\n    case 0x7E/* ~ */:\n      return true;\n    default:\n      return false;\n  }\n}\n\n// Hepler to unify [reference labels].\n//\nfunction normalizeReference(str) {\n  // Trim and collapse whitespace\n  //\n  str = str.trim().replace(/\\s+/g, ' ');\n\n  // In node v10 'ẞ'.toLowerCase() === 'Ṿ', which is presumed to be a bug\n  // fixed in v12 (couldn't find any details).\n  //\n  // So treat this one as a special case\n  // (remove this when node v10 is no longer supported).\n  //\n  if ('ẞ'.toLowerCase() === 'Ṿ') {\n    str = str.replace(/ẞ/g, 'ß');\n  }\n\n  // .toLowerCase().toUpperCase() should get rid of all differences\n  // between letter variants.\n  //\n  // Simple .toLowerCase() doesn't normalize 125 code points correctly,\n  // and .toUpperCase doesn't normalize 6 of them (list of exceptions:\n  // İ, ϴ, ẞ, Ω, K, Å - those are already uppercased, but have differently\n  // uppercased versions).\n  //\n  // Here's an example showing how it happens. Lets take greek letter omega:\n  // uppercase U+0398 (Θ), U+03f4 (ϴ) and lowercase U+03b8 (θ), U+03d1 (ϑ)\n  //\n  // Unicode entries:\n  // 0398;GREEK CAPITAL LETTER THETA;Lu;0;L;;;;;N;;;;03B8;\n  // 03B8;GREEK SMALL LETTER THETA;Ll;0;L;;;;;N;;;0398;;0398\n  // 03D1;GREEK THETA SYMBOL;Ll;0;L;<compat> 03B8;;;;N;GREEK SMALL LETTER SCRIPT THETA;;0398;;0398\n  // 03F4;GREEK CAPITAL THETA SYMBOL;Lu;0;L;<compat> 0398;;;;N;;;;03B8;\n  //\n  // Case-insensitive comparison should treat all of them as equivalent.\n  //\n  // But .toLowerCase() doesn't change ϑ (it's already lowercase),\n  // and .toUpperCase() doesn't change ϴ (already uppercase).\n  //\n  // Applying first lower then upper case normalizes any character:\n  // '\\u0398\\u03f4\\u03b8\\u03d1'.toLowerCase().toUpperCase() === '\\u0398\\u0398\\u0398\\u0398'\n  //\n  // Note: this is equivalent to unicode case folding; unicode normalization\n  // is a different step that is not required here.\n  //\n  // Final result should be uppercased, because it's later stored in an object\n  // (this avoid a conflict with Object.prototype members,\n  // most notably, `__proto__`)\n  //\n  return str.toLowerCase().toUpperCase();\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\n// Re-export libraries commonly used in both markdown-it and its plugins,\n// so plugins won't have to depend on them explicitly, which reduces their\n// bundled size (e.g. a browser build).\n//\nexports.lib                 = {};\nexports.lib.mdurl           = require('mdurl');\nexports.lib.ucmicro         = require('uc.micro');\n\nexports.assign              = assign;\nexports.isString            = isString;\nexports.has                 = has;\nexports.unescapeMd          = unescapeMd;\nexports.unescapeAll         = unescapeAll;\nexports.isValidEntityCode   = isValidEntityCode;\nexports.fromCodePoint       = fromCodePoint;\n// exports.replaceEntities     = replaceEntities;\nexports.escapeHtml          = escapeHtml;\nexports.arrayReplaceAt      = arrayReplaceAt;\nexports.isSpace             = isSpace;\nexports.isWhiteSpace        = isWhiteSpace;\nexports.isMdAsciiPunct      = isMdAsciiPunct;\nexports.isPunctChar         = isPunctChar;\nexports.escapeRE            = escapeRE;\nexports.normalizeReference  = normalizeReference;\n"], "mappings": "AAAA;AACA;AACA,YAAY;;AAGZ,SAASA,MAAMA,CAACC,GAAG,EAAE;EAAE,OAAOC,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACJ,GAAG,CAAC;AAAE;AAEnE,SAASK,QAAQA,CAACL,GAAG,EAAE;EAAE,OAAOD,MAAM,CAACC,GAAG,CAAC,KAAK,iBAAiB;AAAE;AAEnE,IAAIM,eAAe,GAAGL,MAAM,CAACC,SAAS,CAACK,cAAc;AAErD,SAASC,GAAGA,CAACC,MAAM,EAAEC,GAAG,EAAE;EACxB,OAAOJ,eAAe,CAACF,IAAI,CAACK,MAAM,EAAEC,GAAG,CAAC;AAC1C;;AAEA;AACA;AACA,SAASC,MAAMA,CAACX,GAAG,CAAC,8BAA8B;EAChD,IAAIY,OAAO,GAAGC,KAAK,CAACX,SAAS,CAACY,KAAK,CAACV,IAAI,CAACW,SAAS,EAAE,CAAC,CAAC;EAEtDH,OAAO,CAACI,OAAO,CAAC,UAAUC,MAAM,EAAE;IAChC,IAAI,CAACA,MAAM,EAAE;MAAE;IAAQ;IAEvB,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;MAC9B,MAAM,IAAIC,SAAS,CAACD,MAAM,GAAG,gBAAgB,CAAC;IAChD;IAEAhB,MAAM,CAACkB,IAAI,CAACF,MAAM,CAAC,CAACD,OAAO,CAAC,UAAUN,GAAG,EAAE;MACzCV,GAAG,CAACU,GAAG,CAAC,GAAGO,MAAM,CAACP,GAAG,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC,CAAC;EAEF,OAAOV,GAAG;AACZ;;AAEA;AACA;AACA,SAASoB,cAAcA,CAACC,GAAG,EAAEC,GAAG,EAAEC,WAAW,EAAE;EAC7C,OAAO,EAAE,CAACC,MAAM,CAACH,GAAG,CAACP,KAAK,CAAC,CAAC,EAAEQ,GAAG,CAAC,EAAEC,WAAW,EAAEF,GAAG,CAACP,KAAK,CAACQ,GAAG,GAAG,CAAC,CAAC,CAAC;AACtE;;AAEA;;AAEA,SAASG,iBAAiBA,CAACC,CAAC,EAAE;EAC5B;EACA;EACA,IAAIA,CAAC,IAAI,MAAM,IAAIA,CAAC,IAAI,MAAM,EAAE;IAAE,OAAO,KAAK;EAAE;EAChD;EACA,IAAIA,CAAC,IAAI,MAAM,IAAIA,CAAC,IAAI,MAAM,EAAE;IAAE,OAAO,KAAK;EAAE;EAChD,IAAI,CAACA,CAAC,GAAG,MAAM,MAAM,MAAM,IAAI,CAACA,CAAC,GAAG,MAAM,MAAM,MAAM,EAAE;IAAE,OAAO,KAAK;EAAE;EACxE;EACA,IAAIA,CAAC,IAAI,IAAI,IAAIA,CAAC,IAAI,IAAI,EAAE;IAAE,OAAO,KAAK;EAAE;EAC5C,IAAIA,CAAC,KAAK,IAAI,EAAE;IAAE,OAAO,KAAK;EAAE;EAChC,IAAIA,CAAC,IAAI,IAAI,IAAIA,CAAC,IAAI,IAAI,EAAE;IAAE,OAAO,KAAK;EAAE;EAC5C,IAAIA,CAAC,IAAI,IAAI,IAAIA,CAAC,IAAI,IAAI,EAAE;IAAE,OAAO,KAAK;EAAE;EAC5C;EACA,IAAIA,CAAC,GAAG,QAAQ,EAAE;IAAE,OAAO,KAAK;EAAE;EAClC,OAAO,IAAI;AACb;AAEA,SAASC,aAAaA,CAACD,CAAC,EAAE;EACxB;EACA,IAAIA,CAAC,GAAG,MAAM,EAAE;IACdA,CAAC,IAAI,OAAO;IACZ,IAAIE,UAAU,GAAG,MAAM,IAAIF,CAAC,IAAI,EAAE,CAAC;MAC/BG,UAAU,GAAG,MAAM,IAAIH,CAAC,GAAG,KAAK,CAAC;IAErC,OAAOI,MAAM,CAACC,YAAY,CAACH,UAAU,EAAEC,UAAU,CAAC;EACpD;EACA,OAAOC,MAAM,CAACC,YAAY,CAACL,CAAC,CAAC;AAC/B;AAGA,IAAIM,cAAc,GAAI,6CAA6C;AACnE,IAAIC,SAAS,GAAS,4BAA4B;AAClD,IAAIC,eAAe,GAAG,IAAIC,MAAM,CAACH,cAAc,CAACf,MAAM,GAAG,GAAG,GAAGgB,SAAS,CAAChB,MAAM,EAAE,IAAI,CAAC;AAEtF,IAAImB,sBAAsB,GAAG,oCAAoC;AAEjE,IAAIC,QAAQ,GAAGC,OAAO,CAAC,YAAY,CAAC;AAEpC,SAASC,oBAAoBA,CAACC,KAAK,EAAEC,IAAI,EAAE;EACzC,IAAIC,IAAI,GAAG,CAAC;EAEZ,IAAIlC,GAAG,CAAC6B,QAAQ,EAAEI,IAAI,CAAC,EAAE;IACvB,OAAOJ,QAAQ,CAACI,IAAI,CAAC;EACvB;EAEA,IAAIA,IAAI,CAACE,UAAU,CAAC,CAAC,CAAC,KAAK,IAAI,YAAWP,sBAAsB,CAACQ,IAAI,CAACH,IAAI,CAAC,EAAE;IAC3EC,IAAI,GAAGD,IAAI,CAAC,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,KAAK,GAAG,GAClCC,QAAQ,CAACL,IAAI,CAAC3B,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGgC,QAAQ,CAACL,IAAI,CAAC3B,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAE3D,IAAIW,iBAAiB,CAACiB,IAAI,CAAC,EAAE;MAC3B,OAAOf,aAAa,CAACe,IAAI,CAAC;IAC5B;EACF;EAEA,OAAOF,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;;AAEA,SAASO,UAAUA,CAACC,GAAG,EAAE;EACvB,IAAIA,GAAG,CAACC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;IAAE,OAAOD,GAAG;EAAE;EACzC,OAAOA,GAAG,CAACE,OAAO,CAAClB,cAAc,EAAE,IAAI,CAAC;AAC1C;AAEA,SAASmB,WAAWA,CAACH,GAAG,EAAE;EACxB,IAAIA,GAAG,CAACC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,IAAID,GAAG,CAACC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;IAAE,OAAOD,GAAG;EAAE;EAEjE,OAAOA,GAAG,CAACE,OAAO,CAAChB,eAAe,EAAE,UAAUM,KAAK,EAAEY,OAAO,EAAEC,MAAM,EAAE;IACpE,IAAID,OAAO,EAAE;MAAE,OAAOA,OAAO;IAAE;IAC/B,OAAOb,oBAAoB,CAACC,KAAK,EAAEa,MAAM,CAAC;EAC5C,CAAC,CAAC;AACJ;;AAEA;;AAEA,IAAIC,mBAAmB,GAAG,QAAQ;AAClC,IAAIC,sBAAsB,GAAG,SAAS;AACtC,IAAIC,iBAAiB,GAAG;EACtB,GAAG,EAAE,OAAO;EACZ,GAAG,EAAE,MAAM;EACX,GAAG,EAAE,MAAM;EACX,GAAG,EAAE;AACP,CAAC;AAED,SAASC,iBAAiBA,CAACC,EAAE,EAAE;EAC7B,OAAOF,iBAAiB,CAACE,EAAE,CAAC;AAC9B;AAEA,SAASC,UAAUA,CAACX,GAAG,EAAE;EACvB,IAAIM,mBAAmB,CAACV,IAAI,CAACI,GAAG,CAAC,EAAE;IACjC,OAAOA,GAAG,CAACE,OAAO,CAACK,sBAAsB,EAAEE,iBAAiB,CAAC;EAC/D;EACA,OAAOT,GAAG;AACZ;;AAEA;;AAEA,IAAIY,gBAAgB,GAAG,sBAAsB;AAE7C,SAASC,QAAQA,CAACb,GAAG,EAAE;EACrB,OAAOA,GAAG,CAACE,OAAO,CAACU,gBAAgB,EAAE,MAAM,CAAC;AAC9C;;AAEA;;AAEA,SAASE,OAAOA,CAACpB,IAAI,EAAE;EACrB,QAAQA,IAAI;IACV,KAAK,IAAI;IACT,KAAK,IAAI;MACP,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AACd;;AAEA;AACA,SAASqB,YAAYA,CAACrB,IAAI,EAAE;EAC1B,IAAIA,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,EAAE;IAAE,OAAO,IAAI;EAAE;EACrD,QAAQA,IAAI;IACV,KAAK,IAAI,CAAC,CAAC;IACX,KAAK,IAAI,CAAC,CAAC;IACX,KAAK,IAAI,CAAC,CAAC;IACX,KAAK,IAAI,CAAC,CAAC;IACX,KAAK,IAAI,CAAC,CAAC;IACX,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;IACX,KAAK,MAAM;MACT,OAAO,IAAI;EACf;EACA,OAAO,KAAK;AACd;;AAEA;;AAEA;AACA,IAAIsB,gBAAgB,GAAG1B,OAAO,CAAC,6BAA6B,CAAC;;AAE7D;AACA,SAAS2B,WAAWA,CAACP,EAAE,EAAE;EACvB,OAAOM,gBAAgB,CAACpB,IAAI,CAACc,EAAE,CAAC;AAClC;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,cAAcA,CAACR,EAAE,EAAE;EAC1B,QAAQA,EAAE;IACR,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;MACP,OAAO,IAAI;IACb;MACE,OAAO,KAAK;EAChB;AACF;;AAEA;AACA;AACA,SAASS,kBAAkBA,CAACnB,GAAG,EAAE;EAC/B;EACA;EACAA,GAAG,GAAGA,GAAG,CAACoB,IAAI,CAAC,CAAC,CAAClB,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;;EAErC;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,GAAG,CAACL,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;IAC7BG,GAAG,GAAGA,GAAG,CAACE,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;EAC9B;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAAOF,GAAG,CAACH,WAAW,CAAC,CAAC,CAACwB,WAAW,CAAC,CAAC;AACxC;;AAEA;;AAEA;AACA;AACA;AACA;AACAC,OAAO,CAACC,GAAG,GAAmB,CAAC,CAAC;AAChCD,OAAO,CAACC,GAAG,CAACC,KAAK,GAAalC,OAAO,CAAC,OAAO,CAAC;AAC9CgC,OAAO,CAACC,GAAG,CAACE,OAAO,GAAWnC,OAAO,CAAC,UAAU,CAAC;AAEjDgC,OAAO,CAAC3D,MAAM,GAAgBA,MAAM;AACpC2D,OAAO,CAACjE,QAAQ,GAAcA,QAAQ;AACtCiE,OAAO,CAAC9D,GAAG,GAAmBA,GAAG;AACjC8D,OAAO,CAACvB,UAAU,GAAYA,UAAU;AACxCuB,OAAO,CAACnB,WAAW,GAAWA,WAAW;AACzCmB,OAAO,CAAC7C,iBAAiB,GAAKA,iBAAiB;AAC/C6C,OAAO,CAAC3C,aAAa,GAASA,aAAa;AAC3C;AACA2C,OAAO,CAACX,UAAU,GAAYA,UAAU;AACxCW,OAAO,CAAClD,cAAc,GAAQA,cAAc;AAC5CkD,OAAO,CAACR,OAAO,GAAeA,OAAO;AACrCQ,OAAO,CAACP,YAAY,GAAUA,YAAY;AAC1CO,OAAO,CAACJ,cAAc,GAAQA,cAAc;AAC5CI,OAAO,CAACL,WAAW,GAAWA,WAAW;AACzCK,OAAO,CAACT,QAAQ,GAAcA,QAAQ;AACtCS,OAAO,CAACH,kBAAkB,GAAIA,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}