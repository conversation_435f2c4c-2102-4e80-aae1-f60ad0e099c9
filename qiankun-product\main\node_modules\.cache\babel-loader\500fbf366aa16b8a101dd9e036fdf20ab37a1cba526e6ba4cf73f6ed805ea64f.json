{"ast": null, "code": "/*\nLanguage: Shell Session\nRequires: bash.js\nAuthor: T<PERSON><PERSON><PERSON><PERSON><PERSON>e <<EMAIL>>\nCategory: common\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction shell(hljs) {\n  return {\n    name: 'Shell Session',\n    aliases: ['console', 'shellsession'],\n    contains: [{\n      className: 'meta.prompt',\n      // We cannot add \\s (spaces) in the regular expression otherwise it will be too broad and produce unexpected result.\n      // For instance, in the following example, it would match \"echo /path/to/home >\" as a prompt:\n      // echo /path/to/home > t.exe\n      begin: /^\\s{0,3}[/~\\w\\d[\\]()@-]*[>%$#][ ]?/,\n      starts: {\n        end: /[^\\\\](?=\\s*$)/,\n        subLanguage: 'bash'\n      }\n    }]\n  };\n}\nexport { shell as default };", "map": {"version": 3, "names": ["shell", "hljs", "name", "aliases", "contains", "className", "begin", "starts", "end", "subLanguage", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/highlight.js@11.11.1/node_modules/highlight.js/es/languages/shell.js"], "sourcesContent": ["/*\nLanguage: Shell Session\nRequires: bash.js\nAuthor: T<PERSON><PERSON><PERSON><PERSON><PERSON>e <<EMAIL>>\nCategory: common\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction shell(hljs) {\n  return {\n    name: 'Shell Session',\n    aliases: [\n      'console',\n      'shellsession'\n    ],\n    contains: [\n      {\n        className: 'meta.prompt',\n        // We cannot add \\s (spaces) in the regular expression otherwise it will be too broad and produce unexpected result.\n        // For instance, in the following example, it would match \"echo /path/to/home >\" as a prompt:\n        // echo /path/to/home > t.exe\n        begin: /^\\s{0,3}[/~\\w\\d[\\]()@-]*[>%$#][ ]?/,\n        starts: {\n          end: /[^\\\\](?=\\s*$)/,\n          subLanguage: 'bash'\n        }\n      }\n    ]\n  };\n}\n\nexport { shell as default };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,KAAKA,CAACC,IAAI,EAAE;EACnB,OAAO;IACLC,IAAI,EAAE,eAAe;IACrBC,OAAO,EAAE,CACP,SAAS,EACT,cAAc,CACf;IACDC,QAAQ,EAAE,CACR;MACEC,SAAS,EAAE,aAAa;MACxB;MACA;MACA;MACAC,KAAK,EAAE,oCAAoC;MAC3CC,MAAM,EAAE;QACNC,GAAG,EAAE,eAAe;QACpBC,WAAW,EAAE;MACf;IACF,CAAC;EAEL,CAAC;AACH;AAEA,SAAST,KAAK,IAAIU,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}