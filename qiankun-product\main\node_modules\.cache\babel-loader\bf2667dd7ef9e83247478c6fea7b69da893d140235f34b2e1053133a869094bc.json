{"ast": null, "code": "/**\n * <AUTHOR>\n * @since 2020-10-13\n */\nimport { checkActivityFunctions } from 'single-spa';\nimport { calcAppCount, isAllAppsUnmounted, patchHTMLDynamicAppendPrototypeFunctions, rebuildCSSRules, recordStyledComponentsCSSRules } from './common';\n/**\n * Just hijack dynamic head append, that could avoid accidentally hijacking the insertion of elements except in head.\n * Such a case: ReactDOM.createPortal(<style>.test{color:blue}</style>, container),\n * this could make we append the style element into app wrapper but it will cause an error while the react portal unmounting, as ReactDOM could not find the style in body children list.\n * @param appName\n * @param appWrapperGetter\n * @param sandbox\n * @param mounting\n * @param scopedCSS\n * @param excludeAssetFilter\n */\nexport function patchLooseSandbox(appName, appWrapperGetter, sandbox) {\n  var mounting = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true;\n  var scopedCSS = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n  var excludeAssetFilter = arguments.length > 5 ? arguments[5] : undefined;\n  var proxy = sandbox.proxy;\n  var dynamicStyleSheetElements = [];\n  var unpatchDynamicAppendPrototypeFunctions = patchHTMLDynamicAppendPrototypeFunctions(\n  /*\n    check if the currently specified application is active\n    While we switch page from qiankun app to a normal react routing page, the normal one may load stylesheet dynamically while page rendering,\n    but the url change listener must wait until the current call stack is flushed.\n    This scenario may cause we record the stylesheet from react routing page dynamic injection,\n    and remove them after the url change triggered and qiankun app is unmounting\n    see https://github.com/ReactTraining/history/blob/master/modules/createHashHistory.js#L222-L230\n   */\n  function () {\n    return checkActivityFunctions(window.location).some(function (name) {\n      return name === appName;\n    });\n  }, function () {\n    return {\n      appName: appName,\n      appWrapperGetter: appWrapperGetter,\n      proxy: proxy,\n      strictGlobal: false,\n      speedySandbox: false,\n      scopedCSS: scopedCSS,\n      dynamicStyleSheetElements: dynamicStyleSheetElements,\n      excludeAssetFilter: excludeAssetFilter\n    };\n  });\n  if (!mounting) calcAppCount(appName, 'increase', 'bootstrapping');\n  if (mounting) calcAppCount(appName, 'increase', 'mounting');\n  return function free() {\n    if (!mounting) calcAppCount(appName, 'decrease', 'bootstrapping');\n    if (mounting) calcAppCount(appName, 'decrease', 'mounting');\n    // release the overwrite prototype after all the micro apps unmounted\n    if (isAllAppsUnmounted()) unpatchDynamicAppendPrototypeFunctions();\n    recordStyledComponentsCSSRules(dynamicStyleSheetElements);\n    // As now the sub app content all wrapped with a special id container,\n    // the dynamic style sheet would be removed automatically while unmounting\n    return function rebuild() {\n      rebuildCSSRules(dynamicStyleSheetElements, function (stylesheetElement) {\n        var appWrapper = appWrapperGetter();\n        if (!appWrapper.contains(stylesheetElement)) {\n          // Using document.head.appendChild ensures that appendChild invocation can also directly use the HTMLHeadElement.prototype.appendChild method which is overwritten at mounting phase\n          document.head.appendChild.call(appWrapper, stylesheetElement);\n          return true;\n        }\n        return false;\n      });\n      // As the patcher will be invoked every mounting phase, we could release the cache for gc after rebuilding\n      if (mounting) {\n        dynamicStyleSheetElements = [];\n      }\n    };\n  };\n}", "map": {"version": 3, "names": ["checkActivityFunctions", "calcAppCount", "isAllAppsUnmounted", "patchHTMLDynamicAppendPrototypeFunctions", "rebuildCSSRules", "recordStyledComponentsCSSRules", "patchLooseSandbox", "appName", "appWrapperGetter", "sandbox", "mounting", "arguments", "length", "undefined", "scopedCSS", "excludeAssetFilter", "proxy", "dynamicStyleSheetElements", "unpatchDynamicAppendPrototypeFunctions", "window", "location", "some", "name", "strictGlobal", "speedySandbox", "free", "rebuild", "stylesheetElement", "appWrapper", "contains", "document", "head", "append<PERSON><PERSON><PERSON>", "call"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/qiankun@2.10.16/node_modules/qiankun/es/sandbox/patchers/dynamicAppend/forLooseSandbox.js"], "sourcesContent": ["/**\n * <AUTHOR>\n * @since 2020-10-13\n */\nimport { checkActivityFunctions } from 'single-spa';\nimport { calcAppCount, isAllAppsUnmounted, patchHTMLDynamicAppendPrototypeFunctions, rebuildCSSRules, recordStyledComponentsCSSRules } from './common';\n/**\n * Just hijack dynamic head append, that could avoid accidentally hijacking the insertion of elements except in head.\n * Such a case: ReactDOM.createPortal(<style>.test{color:blue}</style>, container),\n * this could make we append the style element into app wrapper but it will cause an error while the react portal unmounting, as ReactDOM could not find the style in body children list.\n * @param appName\n * @param appWrapperGetter\n * @param sandbox\n * @param mounting\n * @param scopedCSS\n * @param excludeAssetFilter\n */\nexport function patchLooseSandbox(appName, appWrapperGetter, sandbox) {\n  var mounting = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true;\n  var scopedCSS = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n  var excludeAssetFilter = arguments.length > 5 ? arguments[5] : undefined;\n  var proxy = sandbox.proxy;\n  var dynamicStyleSheetElements = [];\n  var unpatchDynamicAppendPrototypeFunctions = patchHTMLDynamicAppendPrototypeFunctions(\n  /*\n    check if the currently specified application is active\n    While we switch page from qiankun app to a normal react routing page, the normal one may load stylesheet dynamically while page rendering,\n    but the url change listener must wait until the current call stack is flushed.\n    This scenario may cause we record the stylesheet from react routing page dynamic injection,\n    and remove them after the url change triggered and qiankun app is unmounting\n    see https://github.com/ReactTraining/history/blob/master/modules/createHashHistory.js#L222-L230\n   */\n  function () {\n    return checkActivityFunctions(window.location).some(function (name) {\n      return name === appName;\n    });\n  }, function () {\n    return {\n      appName: appName,\n      appWrapperGetter: appWrapperGetter,\n      proxy: proxy,\n      strictGlobal: false,\n      speedySandbox: false,\n      scopedCSS: scopedCSS,\n      dynamicStyleSheetElements: dynamicStyleSheetElements,\n      excludeAssetFilter: excludeAssetFilter\n    };\n  });\n  if (!mounting) calcAppCount(appName, 'increase', 'bootstrapping');\n  if (mounting) calcAppCount(appName, 'increase', 'mounting');\n  return function free() {\n    if (!mounting) calcAppCount(appName, 'decrease', 'bootstrapping');\n    if (mounting) calcAppCount(appName, 'decrease', 'mounting');\n    // release the overwrite prototype after all the micro apps unmounted\n    if (isAllAppsUnmounted()) unpatchDynamicAppendPrototypeFunctions();\n    recordStyledComponentsCSSRules(dynamicStyleSheetElements);\n    // As now the sub app content all wrapped with a special id container,\n    // the dynamic style sheet would be removed automatically while unmounting\n    return function rebuild() {\n      rebuildCSSRules(dynamicStyleSheetElements, function (stylesheetElement) {\n        var appWrapper = appWrapperGetter();\n        if (!appWrapper.contains(stylesheetElement)) {\n          // Using document.head.appendChild ensures that appendChild invocation can also directly use the HTMLHeadElement.prototype.appendChild method which is overwritten at mounting phase\n          document.head.appendChild.call(appWrapper, stylesheetElement);\n          return true;\n        }\n        return false;\n      });\n      // As the patcher will be invoked every mounting phase, we could release the cache for gc after rebuilding\n      if (mounting) {\n        dynamicStyleSheetElements = [];\n      }\n    };\n  };\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA,SAASA,sBAAsB,QAAQ,YAAY;AACnD,SAASC,YAAY,EAAEC,kBAAkB,EAAEC,wCAAwC,EAAEC,eAAe,EAAEC,8BAA8B,QAAQ,UAAU;AACtJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,OAAO,EAAEC,gBAAgB,EAAEC,OAAO,EAAE;EACpE,IAAIC,QAAQ,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;EACvF,IAAIG,SAAS,GAAGH,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EACzF,IAAII,kBAAkB,GAAGJ,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,SAAS;EACxE,IAAIG,KAAK,GAAGP,OAAO,CAACO,KAAK;EACzB,IAAIC,yBAAyB,GAAG,EAAE;EAClC,IAAIC,sCAAsC,GAAGf,wCAAwC;EACrF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,YAAY;IACV,OAAOH,sBAAsB,CAACmB,MAAM,CAACC,QAAQ,CAAC,CAACC,IAAI,CAAC,UAAUC,IAAI,EAAE;MAClE,OAAOA,IAAI,KAAKf,OAAO;IACzB,CAAC,CAAC;EACJ,CAAC,EAAE,YAAY;IACb,OAAO;MACLA,OAAO,EAAEA,OAAO;MAChBC,gBAAgB,EAAEA,gBAAgB;MAClCQ,KAAK,EAAEA,KAAK;MACZO,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpBV,SAAS,EAAEA,SAAS;MACpBG,yBAAyB,EAAEA,yBAAyB;MACpDF,kBAAkB,EAAEA;IACtB,CAAC;EACH,CAAC,CAAC;EACF,IAAI,CAACL,QAAQ,EAAET,YAAY,CAACM,OAAO,EAAE,UAAU,EAAE,eAAe,CAAC;EACjE,IAAIG,QAAQ,EAAET,YAAY,CAACM,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC;EAC3D,OAAO,SAASkB,IAAIA,CAAA,EAAG;IACrB,IAAI,CAACf,QAAQ,EAAET,YAAY,CAACM,OAAO,EAAE,UAAU,EAAE,eAAe,CAAC;IACjE,IAAIG,QAAQ,EAAET,YAAY,CAACM,OAAO,EAAE,UAAU,EAAE,UAAU,CAAC;IAC3D;IACA,IAAIL,kBAAkB,CAAC,CAAC,EAAEgB,sCAAsC,CAAC,CAAC;IAClEb,8BAA8B,CAACY,yBAAyB,CAAC;IACzD;IACA;IACA,OAAO,SAASS,OAAOA,CAAA,EAAG;MACxBtB,eAAe,CAACa,yBAAyB,EAAE,UAAUU,iBAAiB,EAAE;QACtE,IAAIC,UAAU,GAAGpB,gBAAgB,CAAC,CAAC;QACnC,IAAI,CAACoB,UAAU,CAACC,QAAQ,CAACF,iBAAiB,CAAC,EAAE;UAC3C;UACAG,QAAQ,CAACC,IAAI,CAACC,WAAW,CAACC,IAAI,CAACL,UAAU,EAAED,iBAAiB,CAAC;UAC7D,OAAO,IAAI;QACb;QACA,OAAO,KAAK;MACd,CAAC,CAAC;MACF;MACA,IAAIjB,QAAQ,EAAE;QACZO,yBAAyB,GAAG,EAAE;MAChC;IACF,CAAC;EACH,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}