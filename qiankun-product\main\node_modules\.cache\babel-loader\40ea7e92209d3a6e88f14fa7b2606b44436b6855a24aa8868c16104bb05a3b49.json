{"ast": null, "code": "// HTML block\n\n'use strict';\n\nvar block_names = require('../common/html_blocks');\nvar HTML_OPEN_CLOSE_TAG_RE = require('../common/html_re').HTML_OPEN_CLOSE_TAG_RE;\n\n// An array of opening and corresponding closing sequences for html tags,\n// last argument defines whether it can terminate a paragraph or not\n//\nvar HTML_SEQUENCES = [[/^<(script|pre|style|textarea)(?=(\\s|>|$))/i, /<\\/(script|pre|style|textarea)>/i, true], [/^<!--/, /-->/, true], [/^<\\?/, /\\?>/, true], [/^<![A-Z]/, />/, true], [/^<!\\[CDATA\\[/, /\\]\\]>/, true], [new RegExp('^</?(' + block_names.join('|') + ')(?=(\\\\s|/?>|$))', 'i'), /^$/, true], [new RegExp(HTML_OPEN_CLOSE_TAG_RE.source + '\\\\s*$'), /^$/, false]];\nmodule.exports = function html_block(state, startLine, endLine, silent) {\n  var i,\n    nextLine,\n    token,\n    lineText,\n    pos = state.bMarks[startLine] + state.tShift[startLine],\n    max = state.eMarks[startLine];\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) {\n    return false;\n  }\n  if (!state.md.options.html) {\n    return false;\n  }\n  if (state.src.charCodeAt(pos) !== 0x3C /* < */) {\n    return false;\n  }\n  lineText = state.src.slice(pos, max);\n  for (i = 0; i < HTML_SEQUENCES.length; i++) {\n    if (HTML_SEQUENCES[i][0].test(lineText)) {\n      break;\n    }\n  }\n  if (i === HTML_SEQUENCES.length) {\n    return false;\n  }\n  if (silent) {\n    // true if this sequence can be a terminator, false otherwise\n    return HTML_SEQUENCES[i][2];\n  }\n  nextLine = startLine + 1;\n\n  // If we are here - we detected HTML block.\n  // Let's roll down till block end.\n  if (!HTML_SEQUENCES[i][1].test(lineText)) {\n    for (; nextLine < endLine; nextLine++) {\n      if (state.sCount[nextLine] < state.blkIndent) {\n        break;\n      }\n      pos = state.bMarks[nextLine] + state.tShift[nextLine];\n      max = state.eMarks[nextLine];\n      lineText = state.src.slice(pos, max);\n      if (HTML_SEQUENCES[i][1].test(lineText)) {\n        if (lineText.length !== 0) {\n          nextLine++;\n        }\n        break;\n      }\n    }\n  }\n  state.line = nextLine;\n  token = state.push('html_block', '', 0);\n  token.map = [startLine, nextLine];\n  token.content = state.getLines(startLine, nextLine, state.blkIndent, true);\n  return true;\n};", "map": {"version": 3, "names": ["block_names", "require", "HTML_OPEN_CLOSE_TAG_RE", "HTML_SEQUENCES", "RegExp", "join", "source", "module", "exports", "html_block", "state", "startLine", "endLine", "silent", "i", "nextLine", "token", "lineText", "pos", "bMarks", "tShift", "max", "eMarks", "sCount", "blkIndent", "md", "options", "html", "src", "charCodeAt", "slice", "length", "test", "line", "push", "map", "content", "getLines"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_block/html_block.js"], "sourcesContent": ["// HTML block\n\n'use strict';\n\n\nvar block_names = require('../common/html_blocks');\nvar HTML_OPEN_CLOSE_TAG_RE = require('../common/html_re').HTML_OPEN_CLOSE_TAG_RE;\n\n// An array of opening and corresponding closing sequences for html tags,\n// last argument defines whether it can terminate a paragraph or not\n//\nvar HTML_SEQUENCES = [\n  [ /^<(script|pre|style|textarea)(?=(\\s|>|$))/i, /<\\/(script|pre|style|textarea)>/i, true ],\n  [ /^<!--/,        /-->/,   true ],\n  [ /^<\\?/,         /\\?>/,   true ],\n  [ /^<![A-Z]/,     />/,     true ],\n  [ /^<!\\[CDATA\\[/, /\\]\\]>/, true ],\n  [ new RegExp('^</?(' + block_names.join('|') + ')(?=(\\\\s|/?>|$))', 'i'), /^$/, true ],\n  [ new RegExp(HTML_OPEN_CLOSE_TAG_RE.source + '\\\\s*$'),  /^$/, false ]\n];\n\n\nmodule.exports = function html_block(state, startLine, endLine, silent) {\n  var i, nextLine, token, lineText,\n      pos = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine];\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false; }\n\n  if (!state.md.options.html) { return false; }\n\n  if (state.src.charCodeAt(pos) !== 0x3C/* < */) { return false; }\n\n  lineText = state.src.slice(pos, max);\n\n  for (i = 0; i < HTML_SEQUENCES.length; i++) {\n    if (HTML_SEQUENCES[i][0].test(lineText)) { break; }\n  }\n\n  if (i === HTML_SEQUENCES.length) { return false; }\n\n  if (silent) {\n    // true if this sequence can be a terminator, false otherwise\n    return HTML_SEQUENCES[i][2];\n  }\n\n  nextLine = startLine + 1;\n\n  // If we are here - we detected HTML block.\n  // Let's roll down till block end.\n  if (!HTML_SEQUENCES[i][1].test(lineText)) {\n    for (; nextLine < endLine; nextLine++) {\n      if (state.sCount[nextLine] < state.blkIndent) { break; }\n\n      pos = state.bMarks[nextLine] + state.tShift[nextLine];\n      max = state.eMarks[nextLine];\n      lineText = state.src.slice(pos, max);\n\n      if (HTML_SEQUENCES[i][1].test(lineText)) {\n        if (lineText.length !== 0) { nextLine++; }\n        break;\n      }\n    }\n  }\n\n  state.line = nextLine;\n\n  token         = state.push('html_block', '', 0);\n  token.map     = [ startLine, nextLine ];\n  token.content = state.getLines(startLine, nextLine, state.blkIndent, true);\n\n  return true;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAGZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AAClD,IAAIC,sBAAsB,GAAGD,OAAO,CAAC,mBAAmB,CAAC,CAACC,sBAAsB;;AAEhF;AACA;AACA;AACA,IAAIC,cAAc,GAAG,CACnB,CAAE,4CAA4C,EAAE,kCAAkC,EAAE,IAAI,CAAE,EAC1F,CAAE,OAAO,EAAS,KAAK,EAAI,IAAI,CAAE,EACjC,CAAE,MAAM,EAAU,KAAK,EAAI,IAAI,CAAE,EACjC,CAAE,UAAU,EAAM,GAAG,EAAM,IAAI,CAAE,EACjC,CAAE,cAAc,EAAE,OAAO,EAAE,IAAI,CAAE,EACjC,CAAE,IAAIC,MAAM,CAAC,OAAO,GAAGJ,WAAW,CAACK,IAAI,CAAC,GAAG,CAAC,GAAG,kBAAkB,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE,IAAI,CAAE,EACrF,CAAE,IAAID,MAAM,CAACF,sBAAsB,CAACI,MAAM,GAAG,OAAO,CAAC,EAAG,IAAI,EAAE,KAAK,CAAE,CACtE;AAGDC,MAAM,CAACC,OAAO,GAAG,SAASC,UAAUA,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAE;EACtE,IAAIC,CAAC;IAAEC,QAAQ;IAAEC,KAAK;IAAEC,QAAQ;IAC5BC,GAAG,GAAGR,KAAK,CAACS,MAAM,CAACR,SAAS,CAAC,GAAGD,KAAK,CAACU,MAAM,CAACT,SAAS,CAAC;IACvDU,GAAG,GAAGX,KAAK,CAACY,MAAM,CAACX,SAAS,CAAC;;EAEjC;EACA,IAAID,KAAK,CAACa,MAAM,CAACZ,SAAS,CAAC,GAAGD,KAAK,CAACc,SAAS,IAAI,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAEpE,IAAI,CAACd,KAAK,CAACe,EAAE,CAACC,OAAO,CAACC,IAAI,EAAE;IAAE,OAAO,KAAK;EAAE;EAE5C,IAAIjB,KAAK,CAACkB,GAAG,CAACC,UAAU,CAACX,GAAG,CAAC,KAAK,IAAI,UAAS;IAAE,OAAO,KAAK;EAAE;EAE/DD,QAAQ,GAAGP,KAAK,CAACkB,GAAG,CAACE,KAAK,CAACZ,GAAG,EAAEG,GAAG,CAAC;EAEpC,KAAKP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGX,cAAc,CAAC4B,MAAM,EAAEjB,CAAC,EAAE,EAAE;IAC1C,IAAIX,cAAc,CAACW,CAAC,CAAC,CAAC,CAAC,CAAC,CAACkB,IAAI,CAACf,QAAQ,CAAC,EAAE;MAAE;IAAO;EACpD;EAEA,IAAIH,CAAC,KAAKX,cAAc,CAAC4B,MAAM,EAAE;IAAE,OAAO,KAAK;EAAE;EAEjD,IAAIlB,MAAM,EAAE;IACV;IACA,OAAOV,cAAc,CAACW,CAAC,CAAC,CAAC,CAAC,CAAC;EAC7B;EAEAC,QAAQ,GAAGJ,SAAS,GAAG,CAAC;;EAExB;EACA;EACA,IAAI,CAACR,cAAc,CAACW,CAAC,CAAC,CAAC,CAAC,CAAC,CAACkB,IAAI,CAACf,QAAQ,CAAC,EAAE;IACxC,OAAOF,QAAQ,GAAGH,OAAO,EAAEG,QAAQ,EAAE,EAAE;MACrC,IAAIL,KAAK,CAACa,MAAM,CAACR,QAAQ,CAAC,GAAGL,KAAK,CAACc,SAAS,EAAE;QAAE;MAAO;MAEvDN,GAAG,GAAGR,KAAK,CAACS,MAAM,CAACJ,QAAQ,CAAC,GAAGL,KAAK,CAACU,MAAM,CAACL,QAAQ,CAAC;MACrDM,GAAG,GAAGX,KAAK,CAACY,MAAM,CAACP,QAAQ,CAAC;MAC5BE,QAAQ,GAAGP,KAAK,CAACkB,GAAG,CAACE,KAAK,CAACZ,GAAG,EAAEG,GAAG,CAAC;MAEpC,IAAIlB,cAAc,CAACW,CAAC,CAAC,CAAC,CAAC,CAAC,CAACkB,IAAI,CAACf,QAAQ,CAAC,EAAE;QACvC,IAAIA,QAAQ,CAACc,MAAM,KAAK,CAAC,EAAE;UAAEhB,QAAQ,EAAE;QAAE;QACzC;MACF;IACF;EACF;EAEAL,KAAK,CAACuB,IAAI,GAAGlB,QAAQ;EAErBC,KAAK,GAAWN,KAAK,CAACwB,IAAI,CAAC,YAAY,EAAE,EAAE,EAAE,CAAC,CAAC;EAC/ClB,KAAK,CAACmB,GAAG,GAAO,CAAExB,SAAS,EAAEI,QAAQ,CAAE;EACvCC,KAAK,CAACoB,OAAO,GAAG1B,KAAK,CAAC2B,QAAQ,CAAC1B,SAAS,EAAEI,QAAQ,EAAEL,KAAK,CAACc,SAAS,EAAE,IAAI,CAAC;EAE1E,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}