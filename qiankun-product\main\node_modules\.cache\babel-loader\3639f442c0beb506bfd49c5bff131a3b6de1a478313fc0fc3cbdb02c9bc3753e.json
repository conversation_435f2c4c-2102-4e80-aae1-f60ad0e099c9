{"ast": null, "code": "var baseRest = require('./_baseRest'),\n  isIterateeCall = require('./_isIterateeCall');\n\n/**\n * Creates a function like `_.assign`.\n *\n * @private\n * @param {Function} assigner The function to assign values.\n * @returns {Function} Returns the new assigner function.\n */\nfunction createAssigner(assigner) {\n  return baseRest(function (object, sources) {\n    var index = -1,\n      length = sources.length,\n      customizer = length > 1 ? sources[length - 1] : undefined,\n      guard = length > 2 ? sources[2] : undefined;\n    customizer = assigner.length > 3 && typeof customizer == 'function' ? (length--, customizer) : undefined;\n    if (guard && isIterateeCall(sources[0], sources[1], guard)) {\n      customizer = length < 3 ? undefined : customizer;\n      length = 1;\n    }\n    object = Object(object);\n    while (++index < length) {\n      var source = sources[index];\n      if (source) {\n        assigner(object, source, index, customizer);\n      }\n    }\n    return object;\n  });\n}\nmodule.exports = createAssigner;", "map": {"version": 3, "names": ["baseRest", "require", "isIterateeCall", "createAssigner", "assigner", "object", "sources", "index", "length", "customizer", "undefined", "guard", "Object", "source", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/_createAssigner.js"], "sourcesContent": ["var baseRest = require('./_baseRest'),\n    isIterateeCall = require('./_isIterateeCall');\n\n/**\n * Creates a function like `_.assign`.\n *\n * @private\n * @param {Function} assigner The function to assign values.\n * @returns {Function} Returns the new assigner function.\n */\nfunction createAssigner(assigner) {\n  return baseRest(function(object, sources) {\n    var index = -1,\n        length = sources.length,\n        customizer = length > 1 ? sources[length - 1] : undefined,\n        guard = length > 2 ? sources[2] : undefined;\n\n    customizer = (assigner.length > 3 && typeof customizer == 'function')\n      ? (length--, customizer)\n      : undefined;\n\n    if (guard && isIterateeCall(sources[0], sources[1], guard)) {\n      customizer = length < 3 ? undefined : customizer;\n      length = 1;\n    }\n    object = Object(object);\n    while (++index < length) {\n      var source = sources[index];\n      if (source) {\n        assigner(object, source, index, customizer);\n      }\n    }\n    return object;\n  });\n}\n\nmodule.exports = createAssigner;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,aAAa,CAAC;EACjCC,cAAc,GAAGD,OAAO,CAAC,mBAAmB,CAAC;;AAEjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,cAAcA,CAACC,QAAQ,EAAE;EAChC,OAAOJ,QAAQ,CAAC,UAASK,MAAM,EAAEC,OAAO,EAAE;IACxC,IAAIC,KAAK,GAAG,CAAC,CAAC;MACVC,MAAM,GAAGF,OAAO,CAACE,MAAM;MACvBC,UAAU,GAAGD,MAAM,GAAG,CAAC,GAAGF,OAAO,CAACE,MAAM,GAAG,CAAC,CAAC,GAAGE,SAAS;MACzDC,KAAK,GAAGH,MAAM,GAAG,CAAC,GAAGF,OAAO,CAAC,CAAC,CAAC,GAAGI,SAAS;IAE/CD,UAAU,GAAIL,QAAQ,CAACI,MAAM,GAAG,CAAC,IAAI,OAAOC,UAAU,IAAI,UAAU,IAC/DD,MAAM,EAAE,EAAEC,UAAU,IACrBC,SAAS;IAEb,IAAIC,KAAK,IAAIT,cAAc,CAACI,OAAO,CAAC,CAAC,CAAC,EAAEA,OAAO,CAAC,CAAC,CAAC,EAAEK,KAAK,CAAC,EAAE;MAC1DF,UAAU,GAAGD,MAAM,GAAG,CAAC,GAAGE,SAAS,GAAGD,UAAU;MAChDD,MAAM,GAAG,CAAC;IACZ;IACAH,MAAM,GAAGO,MAAM,CAACP,MAAM,CAAC;IACvB,OAAO,EAAEE,KAAK,GAAGC,MAAM,EAAE;MACvB,IAAIK,MAAM,GAAGP,OAAO,CAACC,KAAK,CAAC;MAC3B,IAAIM,MAAM,EAAE;QACVT,QAAQ,CAACC,MAAM,EAAEQ,MAAM,EAAEN,KAAK,EAAEE,UAAU,CAAC;MAC7C;IACF;IACA,OAAOJ,MAAM;EACf,CAAC,CAAC;AACJ;AAEAS,MAAM,CAACC,OAAO,GAAGZ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}