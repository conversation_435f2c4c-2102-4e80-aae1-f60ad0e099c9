{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, normalizeClass as _normalizeClass, withCtx as _withCtx, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createCommentVNode as _createCommentVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalChatUser\"\n};\nvar _hoisted_2 = [\"innerHTML\"];\nvar _hoisted_3 = [\"onClick\"];\nvar _hoisted_4 = [\"innerHTML\"];\nvar _hoisted_5 = {\n  class: \"GlobalChatNavText forbidSelect\"\n};\nvar _hoisted_6 = [\"innerHTML\"];\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_badge = _resolveComponent(\"el-badge\");\n  return _openBlock(), _createElementBlock(\"div\", {\n    class: _normalizeClass([\"GlobalChatNav\", {\n      GlobalChatMacNav: $setup.isMac\n    }])\n  }, [_createElementVNode(\"div\", _hoisted_1, [_createVNode(_component_el_image, {\n    src: $setup.user.image,\n    fit: \"cover\",\n    draggable: \"false\"\n  }, null, 8 /* PROPS */, [\"src\"])]), _createVNode(_component_el_badge, {\n    value: $setup.chatTotal,\n    hidden: !$setup.chatTotal,\n    offset: [-16, 0]\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", {\n        class: _normalizeClass(['GlobalChatNavItem', {\n          'is-active': $setup.navId === '1'\n        }]),\n        onClick: _cache[0] || (_cache[0] = function ($event) {\n          return $setup.handleNavClick({\n            id: '1'\n          });\n        })\n      }, [_createElementVNode(\"div\", {\n        class: \"GlobalChatNavIcon\",\n        innerHTML: $setup.messageIcon\n      }, null, 8 /* PROPS */, _hoisted_2), _cache[1] || (_cache[1] = _createElementVNode(\"div\", {\n        class: \"GlobalChatNavText forbidSelect\"\n      }, \"消息\", -1 /* HOISTED */))], 2 /* CLASS */)];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"value\", \"hidden\"]), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.navData, function (item) {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: item.id,\n      class: _normalizeClass(['GlobalChatNavItem', {\n        'is-active': $setup.navId === item.id\n      }]),\n      onClick: function onClick($event) {\n        return $setup.handleNavClick(item);\n      }\n    }, [_createElementVNode(\"div\", {\n      class: \"GlobalChatNavIcon\",\n      innerHTML: item.icon\n    }, null, 8 /* PROPS */, _hoisted_4), _createElementVNode(\"div\", _hoisted_5, _toDisplayString(item.name), 1 /* TEXT */)], 10 /* CLASS, PROPS */, _hoisted_3);\n  }), 128 /* KEYED_FRAGMENT */)), $setup.props.exit ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"GlobalChatNavItem GlobalChatNavExit\",\n    onClick: $setup.handleExit\n  }, [_createElementVNode(\"div\", {\n    class: \"GlobalChatNavIcon\",\n    innerHTML: $setup.exitIcon\n  }, null, 8 /* PROPS */, _hoisted_6)])) : _createCommentVNode(\"v-if\", true)], 2 /* CLASS */);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_normalizeClass", "GlobalChatMacNav", "$setup", "isMac", "_createElementVNode", "_hoisted_1", "_createVNode", "_component_el_image", "src", "user", "image", "fit", "draggable", "_component_el_badge", "value", "chatTotal", "hidden", "offset", "default", "_withCtx", "navId", "onClick", "_cache", "$event", "handleNavClick", "id", "innerHTML", "messageIcon", "_hoisted_2", "_", "_Fragment", "_renderList", "navData", "item", "key", "icon", "_hoisted_4", "_hoisted_5", "_toDisplayString", "name", "_hoisted_3", "props", "exit", "handleExit", "exitIcon", "_hoisted_6", "_createCommentVNode"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalChat\\components\\GlobalChatNav.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalChatNav\" :class=\"{ GlobalChatMacNav: isMac }\">\r\n    <div class=\"GlobalChatUser\">\r\n      <el-image :src=\"user.image\" fit=\"cover\" draggable=\"false\" />\r\n    </div>\r\n    <el-badge :value=\"chatTotal\" :hidden=\"!chatTotal\" :offset=\"[-16, 0]\">\r\n      <div :class=\"['GlobalChatNavItem', { 'is-active': navId === '1' }]\" @click=\"handleNavClick({ id: '1' })\">\r\n        <div class=\"GlobalChatNavIcon\" v-html=\"messageIcon\"></div>\r\n        <div class=\"GlobalChatNavText forbidSelect\">消息</div>\r\n      </div>\r\n    </el-badge>\r\n    <div v-for=\"item in navData\" :key=\"item.id\" :class=\"['GlobalChatNavItem', { 'is-active': navId === item.id }]\"\r\n      @click=\"handleNavClick(item)\">\r\n      <div class=\"GlobalChatNavIcon\" v-html=\"item.icon\"></div>\r\n      <div class=\"GlobalChatNavText forbidSelect\">{{ item.name }}</div>\r\n    </div>\r\n    <div class=\"GlobalChatNavItem GlobalChatNavExit\" @click=\"handleExit\" v-if=\"props.exit\">\r\n      <div class=\"GlobalChatNavIcon\" v-html=\"exitIcon\"></div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalChatNav' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed } from 'vue'\r\nimport { useRouter } from 'vue-router'\r\nimport { useStore } from 'vuex'\r\nimport { user } from 'common/js/system_var.js'\r\nimport { globalReadOpenConfig } from 'common/js/GlobalMethod'\r\nimport { messageIcon, addressBookIcon, groupIcon, exitIcon } from '../js/icon.js'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nconst router = useRouter()\r\nconst store = useStore()\r\nconst props = defineProps({\r\n  modelValue: [String, Number],\r\n  chatTotal: { type: Number, default: 0 },\r\n  exit: { type: Boolean, default: false }\r\n})\r\nconst emit = defineEmits(['update:modelValue', 'change'])\r\nconst isMac = window.electron?.isMac\r\nconst navId = computed({\r\n  get () {\r\n    return props.modelValue\r\n  },\r\n  set (value) {\r\n    emit('update:modelValue', value)\r\n  }\r\n})\r\nconst chatTotal = computed(() => props.chatTotal)\r\nconst navData = ref([\r\n  // { id: '1', name: '消息', icon: messageIcon },\r\n  { id: '2', name: '通讯录', icon: addressBookIcon },\r\n  { id: '3', name: '群组', icon: groupIcon }\r\n])\r\nconst handleNavClick = (item) => {\r\n  if (navId.value === item.id) return\r\n  navId.value = item.id\r\n  emit('change', item.id)\r\n}\r\nconst handleExit = () => {\r\n  ElMessageBox.confirm('此操作将退出当前系统, 是否继续?', '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  })\r\n    .then(() => {\r\n      loginOut('已安全退出！')\r\n    })\r\n    .catch(() => {\r\n      ElMessage({ type: 'info', message: '已取消退出' })\r\n    })\r\n}\r\nconst loginOut = async (text) => {\r\n  const { code } = await api.loginOut()\r\n  if (code === 200) {\r\n    sessionStorage.clear()\r\n    const goal_login_router_path = localStorage.getItem('goal_login_router_path')\r\n    if (goal_login_router_path) {\r\n      const goal_login_router_query = localStorage.getItem('goal_login_router_query') || ''\r\n      router.push({\r\n        path: goal_login_router_path,\r\n        query: goal_login_router_query ? JSON.parse(goal_login_router_query) : {}\r\n      })\r\n    } else {\r\n      router.push({ path: '/LoginView' })\r\n    }\r\n    store.commit('setUser', {})\r\n    store.commit('setMenu', [])\r\n    store.commit('setArea', [])\r\n    store.commit('setRole', [])\r\n    store.commit('setReadConfig', {})\r\n    store.commit('setReadOpenConfig', {})\r\n    store.commit('setRongCloudToken', '')\r\n    globalReadOpenConfig()\r\n    // store.state.socket.disconnect()\r\n    // store.state.socket = null\r\n    ElMessage({ message: text, showClose: true, type: 'success' })\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalChatNav {\r\n  width: 72px;\r\n  height: 100%;\r\n  padding: 20px 0 60px 0;\r\n  background: #f8f8f8;\r\n  position: relative;\r\n  -webkit-app-region: drag;\r\n\r\n  &.GlobalChatMacNav {\r\n    padding-top: 52px;\r\n  }\r\n\r\n  .GlobalChatUser {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    cursor: pointer;\r\n    padding-bottom: 30px;\r\n    -webkit-app-region: no-drag;\r\n\r\n    .zy-el-image {\r\n      width: 46px;\r\n      height: 46px;\r\n      border-radius: 50%;\r\n      overflow: hidden;\r\n    }\r\n  }\r\n\r\n  .zy-el-badge {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    margin-bottom: 20px;\r\n    -webkit-app-region: no-drag;\r\n\r\n    .GlobalChatNavItem {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n\r\n  .GlobalChatNavItem {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    flex-direction: column;\r\n    margin-bottom: 20px;\r\n    cursor: pointer;\r\n    -webkit-app-region: no-drag;\r\n\r\n    .GlobalChatNavIcon {\r\n      width: 30px;\r\n      height: 30px;\r\n\r\n      .b {\r\n        fill: var(--zy-el-text-color-primary);\r\n      }\r\n    }\r\n\r\n    .GlobalChatNavText {\r\n      font-size: 14px;\r\n    }\r\n\r\n    &.is-active {\r\n      .GlobalChatNavIcon {\r\n        .b {\r\n          fill: var(--zy-el-color-primary);\r\n        }\r\n      }\r\n\r\n      .GlobalChatNavText {\r\n        color: var(--zy-el-color-primary);\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalChatNavExit {\r\n    position: absolute;\r\n    left: 0;\r\n    bottom: 0;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAESA,KAAK,EAAC;AAAgB;iBAF/B;iBAAA;iBAAA;;EAcWA,KAAK,EAAC;AAAgC;iBAdjD;;;;uBACEC,mBAAA,CAkBM;IAlBDD,KAAK,EADZE,eAAA,EACa,eAAe;MAAAC,gBAAA,EAA6BC,MAAA,CAAAC;IAAK;MAC1DC,mBAAA,CAEM,OAFNC,UAEM,GADJC,YAAA,CAA4DC,mBAAA;IAAjDC,GAAG,EAAEN,MAAA,CAAAO,IAAI,CAACC,KAAK;IAAEC,GAAG,EAAC,OAAO;IAACC,SAAS,EAAC;sCAEpDN,YAAA,CAKWO,mBAAA;IALAC,KAAK,EAAEZ,MAAA,CAAAa,SAAS;IAAGC,MAAM,GAAGd,MAAA,CAAAa,SAAS;IAAGE,MAAM,EAAE;;IAL/DC,OAAA,EAAAC,QAAA,CAMM;MAAA,OAGM,CAHNf,mBAAA,CAGM;QAHAN,KAAK,EANjBE,eAAA;UAAA,aAMwDE,MAAA,CAAAkB,KAAK;QAAA;QAAcC,OAAK,EAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAErB,MAAA,CAAAsB,cAAc;YAAAC,EAAA;UAAA;QAAA;UACxFrB,mBAAA,CAA0D;QAArDN,KAAK,EAAC,mBAAmB;QAAC4B,SAAoB,EAAZxB,MAAA,CAAAyB;8BAP/CC,UAAA,G,0BAQQxB,mBAAA,CAAoD;QAA/CN,KAAK,EAAC;MAAgC,GAAC,IAAE,qB;;IARtD+B,CAAA;6DAWI9B,mBAAA,CAIM+B,SAAA,QAfVC,WAAA,CAWwB7B,MAAA,CAAA8B,OAAO,EAX/B,UAWgBC,IAAI;yBAAhBlC,mBAAA,CAIM;MAJwBmC,GAAG,EAAED,IAAI,CAACR,EAAE;MAAG3B,KAAK,EAXtDE,eAAA;QAAA,aAW6FE,MAAA,CAAAkB,KAAK,KAAKa,IAAI,CAACR;MAAE;MACvGJ,OAAK,WAALA,OAAKA,CAAAE,MAAA;QAAA,OAAErB,MAAA,CAAAsB,cAAc,CAACS,IAAI;MAAA;QAC3B7B,mBAAA,CAAwD;MAAnDN,KAAK,EAAC,mBAAmB;MAAC4B,SAAkB,EAAVO,IAAI,CAACE;4BAblDC,UAAA,GAcMhC,mBAAA,CAAiE,OAAjEiC,UAAiE,EAAAC,gBAAA,CAAlBL,IAAI,CAACM,IAAI,iB,yBAd9DC,UAAA;kCAgB+EtC,MAAA,CAAAuC,KAAK,CAACC,IAAI,I,cAArF3C,mBAAA,CAEM;IAlBVmC,GAAA;IAgBSpC,KAAK,EAAC,qCAAqC;IAAEuB,OAAK,EAAEnB,MAAA,CAAAyC;MACvDvC,mBAAA,CAAuD;IAAlDN,KAAK,EAAC,mBAAmB;IAAC4B,SAAiB,EAATxB,MAAA,CAAA0C;0BAjB7CC,UAAA,E,KAAAC,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}