{"ast": null, "code": "var conventions = require(\"./conventions\");\nvar find = conventions.find;\nvar NAMESPACE = conventions.NAMESPACE;\n\n/**\n * A prerequisite for `[].filter`, to drop elements that are empty\n * @param {string} input\n * @returns {boolean}\n */\nfunction notEmptyString(input) {\n  return input !== '';\n}\n/**\n * @see https://infra.spec.whatwg.org/#split-on-ascii-whitespace\n * @see https://infra.spec.whatwg.org/#ascii-whitespace\n *\n * @param {string} input\n * @returns {string[]} (can be empty)\n */\nfunction splitOnASCIIWhitespace(input) {\n  // U+0009 TAB, U+000A LF, U+000C FF, U+000D CR, U+0020 SPACE\n  return input ? input.split(/[\\t\\n\\f\\r ]+/).filter(notEmptyString) : [];\n}\n\n/**\n * Adds element as a key to current if it is not already present.\n *\n * @param {Record<string, boolean | undefined>} current\n * @param {string} element\n * @returns {Record<string, boolean | undefined>}\n */\nfunction orderedSetReducer(current, element) {\n  if (!current.hasOwnProperty(element)) {\n    current[element] = true;\n  }\n  return current;\n}\n\n/**\n * @see https://infra.spec.whatwg.org/#ordered-set\n * @param {string} input\n * @returns {string[]}\n */\nfunction toOrderedSet(input) {\n  if (!input) return [];\n  var list = splitOnASCIIWhitespace(input);\n  return Object.keys(list.reduce(orderedSetReducer, {}));\n}\n\n/**\n * Uses `list.indexOf` to implement something like `Array.prototype.includes`,\n * which we can not rely on being available.\n *\n * @param {any[]} list\n * @returns {function(any): boolean}\n */\nfunction arrayIncludes(list) {\n  return function (element) {\n    return list && list.indexOf(element) !== -1;\n  };\n}\nfunction copy(src, dest) {\n  for (var p in src) {\n    if (Object.prototype.hasOwnProperty.call(src, p)) {\n      dest[p] = src[p];\n    }\n  }\n}\n\n/**\n^\\w+\\.prototype\\.([_\\w]+)\\s*=\\s*((?:.*\\{\\s*?[\\r\\n][\\s\\S]*?^})|\\S.*?(?=[;\\r\\n]));?\n^\\w+\\.prototype\\.([_\\w]+)\\s*=\\s*(\\S.*?(?=[;\\r\\n]));?\n */\nfunction _extends(Class, Super) {\n  var pt = Class.prototype;\n  if (!(pt instanceof Super)) {\n    var t = function t() {};\n    ;\n    t.prototype = Super.prototype;\n    t = new t();\n    copy(pt, t);\n    Class.prototype = pt = t;\n  }\n  if (pt.constructor != Class) {\n    if (typeof Class != 'function') {\n      console.error(\"unknown Class:\" + Class);\n    }\n    pt.constructor = Class;\n  }\n}\n\n// Node Types\nvar NodeType = {};\nvar ELEMENT_NODE = NodeType.ELEMENT_NODE = 1;\nvar ATTRIBUTE_NODE = NodeType.ATTRIBUTE_NODE = 2;\nvar TEXT_NODE = NodeType.TEXT_NODE = 3;\nvar CDATA_SECTION_NODE = NodeType.CDATA_SECTION_NODE = 4;\nvar ENTITY_REFERENCE_NODE = NodeType.ENTITY_REFERENCE_NODE = 5;\nvar ENTITY_NODE = NodeType.ENTITY_NODE = 6;\nvar PROCESSING_INSTRUCTION_NODE = NodeType.PROCESSING_INSTRUCTION_NODE = 7;\nvar COMMENT_NODE = NodeType.COMMENT_NODE = 8;\nvar DOCUMENT_NODE = NodeType.DOCUMENT_NODE = 9;\nvar DOCUMENT_TYPE_NODE = NodeType.DOCUMENT_TYPE_NODE = 10;\nvar DOCUMENT_FRAGMENT_NODE = NodeType.DOCUMENT_FRAGMENT_NODE = 11;\nvar NOTATION_NODE = NodeType.NOTATION_NODE = 12;\n\n// ExceptionCode\nvar ExceptionCode = {};\nvar ExceptionMessage = {};\nvar INDEX_SIZE_ERR = ExceptionCode.INDEX_SIZE_ERR = (ExceptionMessage[1] = \"Index size error\", 1);\nvar DOMSTRING_SIZE_ERR = ExceptionCode.DOMSTRING_SIZE_ERR = (ExceptionMessage[2] = \"DOMString size error\", 2);\nvar HIERARCHY_REQUEST_ERR = ExceptionCode.HIERARCHY_REQUEST_ERR = (ExceptionMessage[3] = \"Hierarchy request error\", 3);\nvar WRONG_DOCUMENT_ERR = ExceptionCode.WRONG_DOCUMENT_ERR = (ExceptionMessage[4] = \"Wrong document\", 4);\nvar INVALID_CHARACTER_ERR = ExceptionCode.INVALID_CHARACTER_ERR = (ExceptionMessage[5] = \"Invalid character\", 5);\nvar NO_DATA_ALLOWED_ERR = ExceptionCode.NO_DATA_ALLOWED_ERR = (ExceptionMessage[6] = \"No data allowed\", 6);\nvar NO_MODIFICATION_ALLOWED_ERR = ExceptionCode.NO_MODIFICATION_ALLOWED_ERR = (ExceptionMessage[7] = \"No modification allowed\", 7);\nvar NOT_FOUND_ERR = ExceptionCode.NOT_FOUND_ERR = (ExceptionMessage[8] = \"Not found\", 8);\nvar NOT_SUPPORTED_ERR = ExceptionCode.NOT_SUPPORTED_ERR = (ExceptionMessage[9] = \"Not supported\", 9);\nvar INUSE_ATTRIBUTE_ERR = ExceptionCode.INUSE_ATTRIBUTE_ERR = (ExceptionMessage[10] = \"Attribute in use\", 10);\n//level2\nvar INVALID_STATE_ERR = ExceptionCode.INVALID_STATE_ERR = (ExceptionMessage[11] = \"Invalid state\", 11);\nvar SYNTAX_ERR = ExceptionCode.SYNTAX_ERR = (ExceptionMessage[12] = \"Syntax error\", 12);\nvar INVALID_MODIFICATION_ERR = ExceptionCode.INVALID_MODIFICATION_ERR = (ExceptionMessage[13] = \"Invalid modification\", 13);\nvar NAMESPACE_ERR = ExceptionCode.NAMESPACE_ERR = (ExceptionMessage[14] = \"Invalid namespace\", 14);\nvar INVALID_ACCESS_ERR = ExceptionCode.INVALID_ACCESS_ERR = (ExceptionMessage[15] = \"Invalid access\", 15);\n\n/**\n * DOM Level 2\n * Object DOMException\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/ecma-script-binding.html\n * @see http://www.w3.org/TR/REC-DOM-Level-1/ecma-script-language-binding.html\n */\nfunction DOMException(code, message) {\n  if (message instanceof Error) {\n    var error = message;\n  } else {\n    error = this;\n    Error.call(this, ExceptionMessage[code]);\n    this.message = ExceptionMessage[code];\n    if (Error.captureStackTrace) Error.captureStackTrace(this, DOMException);\n  }\n  error.code = code;\n  if (message) this.message = this.message + \": \" + message;\n  return error;\n}\n;\nDOMException.prototype = Error.prototype;\ncopy(ExceptionCode, DOMException);\n\n/**\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/core.html#ID-536297177\n * The NodeList interface provides the abstraction of an ordered collection of nodes, without defining or constraining how this collection is implemented. NodeList objects in the DOM are live.\n * The items in the NodeList are accessible via an integral index, starting from 0.\n */\nfunction NodeList() {}\n;\nNodeList.prototype = {\n  /**\n   * The number of nodes in the list. The range of valid child node indices is 0 to length-1 inclusive.\n   * @standard level1\n   */\n  length: 0,\n  /**\n   * Returns the indexth item in the collection. If index is greater than or equal to the number of nodes in the list, this returns null.\n   * @standard level1\n   * @param index  unsigned long\n   *   Index into the collection.\n   * @return Node\n   * \tThe node at the indexth position in the NodeList, or null if that is not a valid index.\n   */\n  item: function item(index) {\n    return index >= 0 && index < this.length ? this[index] : null;\n  },\n  toString: function toString(isHTML, nodeFilter) {\n    for (var buf = [], i = 0; i < this.length; i++) {\n      serializeToString(this[i], buf, isHTML, nodeFilter);\n    }\n    return buf.join('');\n  },\n  /**\n   * @private\n   * @param {function (Node):boolean} predicate\n   * @returns {Node[]}\n   */\n  filter: function filter(predicate) {\n    return Array.prototype.filter.call(this, predicate);\n  },\n  /**\n   * @private\n   * @param {Node} item\n   * @returns {number}\n   */\n  indexOf: function indexOf(item) {\n    return Array.prototype.indexOf.call(this, item);\n  }\n};\nfunction LiveNodeList(node, refresh) {\n  this._node = node;\n  this._refresh = refresh;\n  _updateLiveList(this);\n}\nfunction _updateLiveList(list) {\n  var inc = list._node._inc || list._node.ownerDocument._inc;\n  if (list._inc !== inc) {\n    var ls = list._refresh(list._node);\n    __set__(list, 'length', ls.length);\n    if (!list.$$length || ls.length < list.$$length) {\n      for (var i = ls.length; i in list; i++) {\n        if (Object.prototype.hasOwnProperty.call(list, i)) {\n          delete list[i];\n        }\n      }\n    }\n    copy(ls, list);\n    list._inc = inc;\n  }\n}\nLiveNodeList.prototype.item = function (i) {\n  _updateLiveList(this);\n  return this[i] || null;\n};\n_extends(LiveNodeList, NodeList);\n\n/**\n * Objects implementing the NamedNodeMap interface are used\n * to represent collections of nodes that can be accessed by name.\n * Note that NamedNodeMap does not inherit from NodeList;\n * NamedNodeMaps are not maintained in any particular order.\n * Objects contained in an object implementing NamedNodeMap may also be accessed by an ordinal index,\n * but this is simply to allow convenient enumeration of the contents of a NamedNodeMap,\n * and does not imply that the DOM specifies an order to these Nodes.\n * NamedNodeMap objects in the DOM are live.\n * used for attributes or DocumentType entities\n */\nfunction NamedNodeMap() {}\n;\nfunction _findNodeIndex(list, node) {\n  var i = list.length;\n  while (i--) {\n    if (list[i] === node) {\n      return i;\n    }\n  }\n}\nfunction _addNamedNode(el, list, newAttr, oldAttr) {\n  if (oldAttr) {\n    list[_findNodeIndex(list, oldAttr)] = newAttr;\n  } else {\n    list[list.length++] = newAttr;\n  }\n  if (el) {\n    newAttr.ownerElement = el;\n    var doc = el.ownerDocument;\n    if (doc) {\n      oldAttr && _onRemoveAttribute(doc, el, oldAttr);\n      _onAddAttribute(doc, el, newAttr);\n    }\n  }\n}\nfunction _removeNamedNode(el, list, attr) {\n  //console.log('remove attr:'+attr)\n  var i = _findNodeIndex(list, attr);\n  if (i >= 0) {\n    var lastIndex = list.length - 1;\n    while (i < lastIndex) {\n      list[i] = list[++i];\n    }\n    list.length = lastIndex;\n    if (el) {\n      var doc = el.ownerDocument;\n      if (doc) {\n        _onRemoveAttribute(doc, el, attr);\n        attr.ownerElement = null;\n      }\n    }\n  } else {\n    throw new DOMException(NOT_FOUND_ERR, new Error(el.tagName + '@' + attr));\n  }\n}\nNamedNodeMap.prototype = {\n  length: 0,\n  item: NodeList.prototype.item,\n  getNamedItem: function getNamedItem(key) {\n    //\t\tif(key.indexOf(':')>0 || key == 'xmlns'){\n    //\t\t\treturn null;\n    //\t\t}\n    //console.log()\n    var i = this.length;\n    while (i--) {\n      var attr = this[i];\n      //console.log(attr.nodeName,key)\n      if (attr.nodeName == key) {\n        return attr;\n      }\n    }\n  },\n  setNamedItem: function setNamedItem(attr) {\n    var el = attr.ownerElement;\n    if (el && el != this._ownerElement) {\n      throw new DOMException(INUSE_ATTRIBUTE_ERR);\n    }\n    var oldAttr = this.getNamedItem(attr.nodeName);\n    _addNamedNode(this._ownerElement, this, attr, oldAttr);\n    return oldAttr;\n  },\n  /* returns Node */\n  setNamedItemNS: function setNamedItemNS(attr) {\n    // raises: WRONG_DOCUMENT_ERR,NO_MODIFICATION_ALLOWED_ERR,INUSE_ATTRIBUTE_ERR\n    var el = attr.ownerElement,\n      oldAttr;\n    if (el && el != this._ownerElement) {\n      throw new DOMException(INUSE_ATTRIBUTE_ERR);\n    }\n    oldAttr = this.getNamedItemNS(attr.namespaceURI, attr.localName);\n    _addNamedNode(this._ownerElement, this, attr, oldAttr);\n    return oldAttr;\n  },\n  /* returns Node */\n  removeNamedItem: function removeNamedItem(key) {\n    var attr = this.getNamedItem(key);\n    _removeNamedNode(this._ownerElement, this, attr);\n    return attr;\n  },\n  // raises: NOT_FOUND_ERR,NO_MODIFICATION_ALLOWED_ERR\n\n  //for level2\n  removeNamedItemNS: function removeNamedItemNS(namespaceURI, localName) {\n    var attr = this.getNamedItemNS(namespaceURI, localName);\n    _removeNamedNode(this._ownerElement, this, attr);\n    return attr;\n  },\n  getNamedItemNS: function getNamedItemNS(namespaceURI, localName) {\n    var i = this.length;\n    while (i--) {\n      var node = this[i];\n      if (node.localName == localName && node.namespaceURI == namespaceURI) {\n        return node;\n      }\n    }\n    return null;\n  }\n};\n\n/**\n * The DOMImplementation interface represents an object providing methods\n * which are not dependent on any particular document.\n * Such an object is returned by the `Document.implementation` property.\n *\n * __The individual methods describe the differences compared to the specs.__\n *\n * @constructor\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation MDN\n * @see https://www.w3.org/TR/REC-DOM-Level-1/level-one-core.html#ID-102161490 DOM Level 1 Core (Initial)\n * @see https://www.w3.org/TR/DOM-Level-2-Core/core.html#ID-102161490 DOM Level 2 Core\n * @see https://www.w3.org/TR/DOM-Level-3-Core/core.html#ID-102161490 DOM Level 3 Core\n * @see https://dom.spec.whatwg.org/#domimplementation DOM Living Standard\n */\nfunction DOMImplementation() {}\nDOMImplementation.prototype = {\n  /**\n   * The DOMImplementation.hasFeature() method returns a Boolean flag indicating if a given feature is supported.\n   * The different implementations fairly diverged in what kind of features were reported.\n   * The latest version of the spec settled to force this method to always return true, where the functionality was accurate and in use.\n   *\n   * @deprecated It is deprecated and modern browsers return true in all cases.\n   *\n   * @param {string} feature\n   * @param {string} [version]\n   * @returns {boolean} always true\n   *\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation/hasFeature MDN\n   * @see https://www.w3.org/TR/REC-DOM-Level-1/level-one-core.html#ID-5CED94D7 DOM Level 1 Core\n   * @see https://dom.spec.whatwg.org/#dom-domimplementation-hasfeature DOM Living Standard\n   */\n  hasFeature: function hasFeature(feature, version) {\n    return true;\n  },\n  /**\n   * Creates an XML Document object of the specified type with its document element.\n   *\n   * __It behaves slightly different from the description in the living standard__:\n   * - There is no interface/class `XMLDocument`, it returns a `Document` instance.\n   * - `contentType`, `encoding`, `mode`, `origin`, `url` fields are currently not declared.\n   * - this implementation is not validating names or qualified names\n   *   (when parsing XML strings, the SAX parser takes care of that)\n   *\n   * @param {string|null} namespaceURI\n   * @param {string} qualifiedName\n   * @param {DocumentType=null} doctype\n   * @returns {Document}\n   *\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation/createDocument MDN\n   * @see https://www.w3.org/TR/DOM-Level-2-Core/core.html#Level-2-Core-DOM-createDocument DOM Level 2 Core (initial)\n   * @see https://dom.spec.whatwg.org/#dom-domimplementation-createdocument  DOM Level 2 Core\n   *\n   * @see https://dom.spec.whatwg.org/#validate-and-extract DOM: Validate and extract\n   * @see https://www.w3.org/TR/xml/#NT-NameStartChar XML Spec: Names\n   * @see https://www.w3.org/TR/xml-names/#ns-qualnames XML Namespaces: Qualified names\n   */\n  createDocument: function createDocument(namespaceURI, qualifiedName, doctype) {\n    var doc = new Document();\n    doc.implementation = this;\n    doc.childNodes = new NodeList();\n    doc.doctype = doctype || null;\n    if (doctype) {\n      doc.appendChild(doctype);\n    }\n    if (qualifiedName) {\n      var root = doc.createElementNS(namespaceURI, qualifiedName);\n      doc.appendChild(root);\n    }\n    return doc;\n  },\n  /**\n   * Returns a doctype, with the given `qualifiedName`, `publicId`, and `systemId`.\n   *\n   * __This behavior is slightly different from the in the specs__:\n   * - this implementation is not validating names or qualified names\n   *   (when parsing XML strings, the SAX parser takes care of that)\n   *\n   * @param {string} qualifiedName\n   * @param {string} [publicId]\n   * @param {string} [systemId]\n   * @returns {DocumentType} which can either be used with `DOMImplementation.createDocument` upon document creation\n   * \t\t\t\t  or can be put into the document via methods like `Node.insertBefore()` or `Node.replaceChild()`\n   *\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation/createDocumentType MDN\n   * @see https://www.w3.org/TR/DOM-Level-2-Core/core.html#Level-2-Core-DOM-createDocType DOM Level 2 Core\n   * @see https://dom.spec.whatwg.org/#dom-domimplementation-createdocumenttype DOM Living Standard\n   *\n   * @see https://dom.spec.whatwg.org/#validate-and-extract DOM: Validate and extract\n   * @see https://www.w3.org/TR/xml/#NT-NameStartChar XML Spec: Names\n   * @see https://www.w3.org/TR/xml-names/#ns-qualnames XML Namespaces: Qualified names\n   */\n  createDocumentType: function createDocumentType(qualifiedName, publicId, systemId) {\n    var node = new DocumentType();\n    node.name = qualifiedName;\n    node.nodeName = qualifiedName;\n    node.publicId = publicId || '';\n    node.systemId = systemId || '';\n    return node;\n  }\n};\n\n/**\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/core.html#ID-1950641247\n */\n\nfunction Node() {}\n;\nNode.prototype = {\n  firstChild: null,\n  lastChild: null,\n  previousSibling: null,\n  nextSibling: null,\n  attributes: null,\n  parentNode: null,\n  childNodes: null,\n  ownerDocument: null,\n  nodeValue: null,\n  namespaceURI: null,\n  prefix: null,\n  localName: null,\n  // Modified in DOM Level 2:\n  insertBefore: function insertBefore(newChild, refChild) {\n    //raises\n    return _insertBefore(this, newChild, refChild);\n  },\n  replaceChild: function replaceChild(newChild, oldChild) {\n    //raises\n    _insertBefore(this, newChild, oldChild, assertPreReplacementValidityInDocument);\n    if (oldChild) {\n      this.removeChild(oldChild);\n    }\n  },\n  removeChild: function removeChild(oldChild) {\n    return _removeChild(this, oldChild);\n  },\n  appendChild: function appendChild(newChild) {\n    return this.insertBefore(newChild, null);\n  },\n  hasChildNodes: function hasChildNodes() {\n    return this.firstChild != null;\n  },\n  cloneNode: function cloneNode(deep) {\n    return _cloneNode(this.ownerDocument || this, this, deep);\n  },\n  // Modified in DOM Level 2:\n  normalize: function normalize() {\n    var child = this.firstChild;\n    while (child) {\n      var next = child.nextSibling;\n      if (next && next.nodeType == TEXT_NODE && child.nodeType == TEXT_NODE) {\n        this.removeChild(next);\n        child.appendData(next.data);\n      } else {\n        child.normalize();\n        child = next;\n      }\n    }\n  },\n  // Introduced in DOM Level 2:\n  isSupported: function isSupported(feature, version) {\n    return this.ownerDocument.implementation.hasFeature(feature, version);\n  },\n  // Introduced in DOM Level 2:\n  hasAttributes: function hasAttributes() {\n    return this.attributes.length > 0;\n  },\n  /**\n   * Look up the prefix associated to the given namespace URI, starting from this node.\n   * **The default namespace declarations are ignored by this method.**\n   * See Namespace Prefix Lookup for details on the algorithm used by this method.\n   *\n   * _Note: The implementation seems to be incomplete when compared to the algorithm described in the specs._\n   *\n   * @param {string | null} namespaceURI\n   * @returns {string | null}\n   * @see https://www.w3.org/TR/DOM-Level-3-Core/core.html#Node3-lookupNamespacePrefix\n   * @see https://www.w3.org/TR/DOM-Level-3-Core/namespaces-algorithms.html#lookupNamespacePrefixAlgo\n   * @see https://dom.spec.whatwg.org/#dom-node-lookupprefix\n   * @see https://github.com/xmldom/xmldom/issues/322\n   */\n  lookupPrefix: function lookupPrefix(namespaceURI) {\n    var el = this;\n    while (el) {\n      var map = el._nsMap;\n      //console.dir(map)\n      if (map) {\n        for (var n in map) {\n          if (Object.prototype.hasOwnProperty.call(map, n) && map[n] === namespaceURI) {\n            return n;\n          }\n        }\n      }\n      el = el.nodeType == ATTRIBUTE_NODE ? el.ownerDocument : el.parentNode;\n    }\n    return null;\n  },\n  // Introduced in DOM Level 3:\n  lookupNamespaceURI: function lookupNamespaceURI(prefix) {\n    var el = this;\n    while (el) {\n      var map = el._nsMap;\n      //console.dir(map)\n      if (map) {\n        if (Object.prototype.hasOwnProperty.call(map, prefix)) {\n          return map[prefix];\n        }\n      }\n      el = el.nodeType == ATTRIBUTE_NODE ? el.ownerDocument : el.parentNode;\n    }\n    return null;\n  },\n  // Introduced in DOM Level 3:\n  isDefaultNamespace: function isDefaultNamespace(namespaceURI) {\n    var prefix = this.lookupPrefix(namespaceURI);\n    return prefix == null;\n  }\n};\nfunction _xmlEncoder(c) {\n  return c == '<' && '&lt;' || c == '>' && '&gt;' || c == '&' && '&amp;' || c == '\"' && '&quot;' || '&#' + c.charCodeAt() + ';';\n}\ncopy(NodeType, Node);\ncopy(NodeType, Node.prototype);\n\n/**\n * @param callback return true for continue,false for break\n * @return boolean true: break visit;\n */\nfunction _visitNode(node, callback) {\n  if (callback(node)) {\n    return true;\n  }\n  if (node = node.firstChild) {\n    do {\n      if (_visitNode(node, callback)) {\n        return true;\n      }\n    } while (node = node.nextSibling);\n  }\n}\nfunction Document() {\n  this.ownerDocument = this;\n}\nfunction _onAddAttribute(doc, el, newAttr) {\n  doc && doc._inc++;\n  var ns = newAttr.namespaceURI;\n  if (ns === NAMESPACE.XMLNS) {\n    //update namespace\n    el._nsMap[newAttr.prefix ? newAttr.localName : ''] = newAttr.value;\n  }\n}\nfunction _onRemoveAttribute(doc, el, newAttr, remove) {\n  doc && doc._inc++;\n  var ns = newAttr.namespaceURI;\n  if (ns === NAMESPACE.XMLNS) {\n    //update namespace\n    delete el._nsMap[newAttr.prefix ? newAttr.localName : ''];\n  }\n}\n\n/**\n * Updates `el.childNodes`, updating the indexed items and it's `length`.\n * Passing `newChild` means it will be appended.\n * Otherwise it's assumed that an item has been removed,\n * and `el.firstNode` and it's `.nextSibling` are used\n * to walk the current list of child nodes.\n *\n * @param {Document} doc\n * @param {Node} el\n * @param {Node} [newChild]\n * @private\n */\nfunction _onUpdateChild(doc, el, newChild) {\n  if (doc && doc._inc) {\n    doc._inc++;\n    //update childNodes\n    var cs = el.childNodes;\n    if (newChild) {\n      cs[cs.length++] = newChild;\n    } else {\n      var child = el.firstChild;\n      var i = 0;\n      while (child) {\n        cs[i++] = child;\n        child = child.nextSibling;\n      }\n      cs.length = i;\n      delete cs[cs.length];\n    }\n  }\n}\n\n/**\n * Removes the connections between `parentNode` and `child`\n * and any existing `child.previousSibling` or `child.nextSibling`.\n *\n * @see https://github.com/xmldom/xmldom/issues/135\n * @see https://github.com/xmldom/xmldom/issues/145\n *\n * @param {Node} parentNode\n * @param {Node} child\n * @returns {Node} the child that was removed.\n * @private\n */\nfunction _removeChild(parentNode, child) {\n  var previous = child.previousSibling;\n  var next = child.nextSibling;\n  if (previous) {\n    previous.nextSibling = next;\n  } else {\n    parentNode.firstChild = next;\n  }\n  if (next) {\n    next.previousSibling = previous;\n  } else {\n    parentNode.lastChild = previous;\n  }\n  child.parentNode = null;\n  child.previousSibling = null;\n  child.nextSibling = null;\n  _onUpdateChild(parentNode.ownerDocument, parentNode);\n  return child;\n}\n\n/**\n * Returns `true` if `node` can be a parent for insertion.\n * @param {Node} node\n * @returns {boolean}\n */\nfunction hasValidParentNodeType(node) {\n  return node && (node.nodeType === Node.DOCUMENT_NODE || node.nodeType === Node.DOCUMENT_FRAGMENT_NODE || node.nodeType === Node.ELEMENT_NODE);\n}\n\n/**\n * Returns `true` if `node` can be inserted according to it's `nodeType`.\n * @param {Node} node\n * @returns {boolean}\n */\nfunction hasInsertableNodeType(node) {\n  return node && (isElementNode(node) || isTextNode(node) || isDocTypeNode(node) || node.nodeType === Node.DOCUMENT_FRAGMENT_NODE || node.nodeType === Node.COMMENT_NODE || node.nodeType === Node.PROCESSING_INSTRUCTION_NODE);\n}\n\n/**\n * Returns true if `node` is a DOCTYPE node\n * @param {Node} node\n * @returns {boolean}\n */\nfunction isDocTypeNode(node) {\n  return node && node.nodeType === Node.DOCUMENT_TYPE_NODE;\n}\n\n/**\n * Returns true if the node is an element\n * @param {Node} node\n * @returns {boolean}\n */\nfunction isElementNode(node) {\n  return node && node.nodeType === Node.ELEMENT_NODE;\n}\n/**\n * Returns true if `node` is a text node\n * @param {Node} node\n * @returns {boolean}\n */\nfunction isTextNode(node) {\n  return node && node.nodeType === Node.TEXT_NODE;\n}\n\n/**\n * Check if en element node can be inserted before `child`, or at the end if child is falsy,\n * according to the presence and position of a doctype node on the same level.\n *\n * @param {Document} doc The document node\n * @param {Node} child the node that would become the nextSibling if the element would be inserted\n * @returns {boolean} `true` if an element can be inserted before child\n * @private\n * https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n */\nfunction isElementInsertionPossible(doc, child) {\n  var parentChildNodes = doc.childNodes || [];\n  if (find(parentChildNodes, isElementNode) || isDocTypeNode(child)) {\n    return false;\n  }\n  var docTypeNode = find(parentChildNodes, isDocTypeNode);\n  return !(child && docTypeNode && parentChildNodes.indexOf(docTypeNode) > parentChildNodes.indexOf(child));\n}\n\n/**\n * Check if en element node can be inserted before `child`, or at the end if child is falsy,\n * according to the presence and position of a doctype node on the same level.\n *\n * @param {Node} doc The document node\n * @param {Node} child the node that would become the nextSibling if the element would be inserted\n * @returns {boolean} `true` if an element can be inserted before child\n * @private\n * https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n */\nfunction isElementReplacementPossible(doc, child) {\n  var parentChildNodes = doc.childNodes || [];\n  function hasElementChildThatIsNotChild(node) {\n    return isElementNode(node) && node !== child;\n  }\n  if (find(parentChildNodes, hasElementChildThatIsNotChild)) {\n    return false;\n  }\n  var docTypeNode = find(parentChildNodes, isDocTypeNode);\n  return !(child && docTypeNode && parentChildNodes.indexOf(docTypeNode) > parentChildNodes.indexOf(child));\n}\n\n/**\n * @private\n * Steps 1-5 of the checks before inserting and before replacing a child are the same.\n *\n * @param {Node} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node=} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n * @see https://dom.spec.whatwg.org/#concept-node-replace\n */\nfunction assertPreInsertionValidity1to5(parent, node, child) {\n  // 1. If `parent` is not a Document, DocumentFragment, or Element node, then throw a \"HierarchyRequestError\" DOMException.\n  if (!hasValidParentNodeType(parent)) {\n    throw new DOMException(HIERARCHY_REQUEST_ERR, 'Unexpected parent node type ' + parent.nodeType);\n  }\n  // 2. If `node` is a host-including inclusive ancestor of `parent`, then throw a \"HierarchyRequestError\" DOMException.\n  // not implemented!\n  // 3. If `child` is non-null and its parent is not `parent`, then throw a \"NotFoundError\" DOMException.\n  if (child && child.parentNode !== parent) {\n    throw new DOMException(NOT_FOUND_ERR, 'child not in parent');\n  }\n  if (\n  // 4. If `node` is not a DocumentFragment, DocumentType, Element, or CharacterData node, then throw a \"HierarchyRequestError\" DOMException.\n  !hasInsertableNodeType(node) ||\n  // 5. If either `node` is a Text node and `parent` is a document,\n  // the sax parser currently adds top level text nodes, this will be fixed in 0.9.0\n  // || (node.nodeType === Node.TEXT_NODE && parent.nodeType === Node.DOCUMENT_NODE)\n  // or `node` is a doctype and `parent` is not a document, then throw a \"HierarchyRequestError\" DOMException.\n  isDocTypeNode(node) && parent.nodeType !== Node.DOCUMENT_NODE) {\n    throw new DOMException(HIERARCHY_REQUEST_ERR, 'Unexpected node type ' + node.nodeType + ' for parent node type ' + parent.nodeType);\n  }\n}\n\n/**\n * @private\n * Step 6 of the checks before inserting and before replacing a child are different.\n *\n * @param {Document} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node | undefined} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n * @see https://dom.spec.whatwg.org/#concept-node-replace\n */\nfunction assertPreInsertionValidityInDocument(parent, node, child) {\n  var parentChildNodes = parent.childNodes || [];\n  var nodeChildNodes = node.childNodes || [];\n\n  // DocumentFragment\n  if (node.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {\n    var nodeChildElements = nodeChildNodes.filter(isElementNode);\n    // If node has more than one element child or has a Text node child.\n    if (nodeChildElements.length > 1 || find(nodeChildNodes, isTextNode)) {\n      throw new DOMException(HIERARCHY_REQUEST_ERR, 'More than one element or text in fragment');\n    }\n    // Otherwise, if `node` has one element child and either `parent` has an element child,\n    // `child` is a doctype, or `child` is non-null and a doctype is following `child`.\n    if (nodeChildElements.length === 1 && !isElementInsertionPossible(parent, child)) {\n      throw new DOMException(HIERARCHY_REQUEST_ERR, 'Element in fragment can not be inserted before doctype');\n    }\n  }\n  // Element\n  if (isElementNode(node)) {\n    // `parent` has an element child, `child` is a doctype,\n    // or `child` is non-null and a doctype is following `child`.\n    if (!isElementInsertionPossible(parent, child)) {\n      throw new DOMException(HIERARCHY_REQUEST_ERR, 'Only one element can be added and only after doctype');\n    }\n  }\n  // DocumentType\n  if (isDocTypeNode(node)) {\n    // `parent` has a doctype child,\n    if (find(parentChildNodes, isDocTypeNode)) {\n      throw new DOMException(HIERARCHY_REQUEST_ERR, 'Only one doctype is allowed');\n    }\n    var parentElementChild = find(parentChildNodes, isElementNode);\n    // `child` is non-null and an element is preceding `child`,\n    if (child && parentChildNodes.indexOf(parentElementChild) < parentChildNodes.indexOf(child)) {\n      throw new DOMException(HIERARCHY_REQUEST_ERR, 'Doctype can only be inserted before an element');\n    }\n    // or `child` is null and `parent` has an element child.\n    if (!child && parentElementChild) {\n      throw new DOMException(HIERARCHY_REQUEST_ERR, 'Doctype can not be appended since element is present');\n    }\n  }\n}\n\n/**\n * @private\n * Step 6 of the checks before inserting and before replacing a child are different.\n *\n * @param {Document} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node | undefined} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n * @see https://dom.spec.whatwg.org/#concept-node-replace\n */\nfunction assertPreReplacementValidityInDocument(parent, node, child) {\n  var parentChildNodes = parent.childNodes || [];\n  var nodeChildNodes = node.childNodes || [];\n\n  // DocumentFragment\n  if (node.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {\n    var nodeChildElements = nodeChildNodes.filter(isElementNode);\n    // If `node` has more than one element child or has a Text node child.\n    if (nodeChildElements.length > 1 || find(nodeChildNodes, isTextNode)) {\n      throw new DOMException(HIERARCHY_REQUEST_ERR, 'More than one element or text in fragment');\n    }\n    // Otherwise, if `node` has one element child and either `parent` has an element child that is not `child` or a doctype is following `child`.\n    if (nodeChildElements.length === 1 && !isElementReplacementPossible(parent, child)) {\n      throw new DOMException(HIERARCHY_REQUEST_ERR, 'Element in fragment can not be inserted before doctype');\n    }\n  }\n  // Element\n  if (isElementNode(node)) {\n    // `parent` has an element child that is not `child` or a doctype is following `child`.\n    if (!isElementReplacementPossible(parent, child)) {\n      throw new DOMException(HIERARCHY_REQUEST_ERR, 'Only one element can be added and only after doctype');\n    }\n  }\n  // DocumentType\n  if (isDocTypeNode(node)) {\n    var hasDoctypeChildThatIsNotChild = function hasDoctypeChildThatIsNotChild(node) {\n      return isDocTypeNode(node) && node !== child;\n    }; // `parent` has a doctype child that is not `child`,\n    if (find(parentChildNodes, hasDoctypeChildThatIsNotChild)) {\n      throw new DOMException(HIERARCHY_REQUEST_ERR, 'Only one doctype is allowed');\n    }\n    var parentElementChild = find(parentChildNodes, isElementNode);\n    // or an element is preceding `child`.\n    if (child && parentChildNodes.indexOf(parentElementChild) < parentChildNodes.indexOf(child)) {\n      throw new DOMException(HIERARCHY_REQUEST_ERR, 'Doctype can only be inserted before an element');\n    }\n  }\n}\n\n/**\n * @private\n * @param {Node} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node=} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n */\nfunction _insertBefore(parent, node, child, _inDocumentAssertion) {\n  // To ensure pre-insertion validity of a node into a parent before a child, run these steps:\n  assertPreInsertionValidity1to5(parent, node, child);\n\n  // If parent is a document, and any of the statements below, switched on the interface node implements,\n  // are true, then throw a \"HierarchyRequestError\" DOMException.\n  if (parent.nodeType === Node.DOCUMENT_NODE) {\n    (_inDocumentAssertion || assertPreInsertionValidityInDocument)(parent, node, child);\n  }\n  var cp = node.parentNode;\n  if (cp) {\n    cp.removeChild(node); //remove and update\n  }\n  if (node.nodeType === DOCUMENT_FRAGMENT_NODE) {\n    var newFirst = node.firstChild;\n    if (newFirst == null) {\n      return node;\n    }\n    var newLast = node.lastChild;\n  } else {\n    newFirst = newLast = node;\n  }\n  var pre = child ? child.previousSibling : parent.lastChild;\n  newFirst.previousSibling = pre;\n  newLast.nextSibling = child;\n  if (pre) {\n    pre.nextSibling = newFirst;\n  } else {\n    parent.firstChild = newFirst;\n  }\n  if (child == null) {\n    parent.lastChild = newLast;\n  } else {\n    child.previousSibling = newLast;\n  }\n  do {\n    newFirst.parentNode = parent;\n  } while (newFirst !== newLast && (newFirst = newFirst.nextSibling));\n  _onUpdateChild(parent.ownerDocument || parent, parent);\n  //console.log(parent.lastChild.nextSibling == null)\n  if (node.nodeType == DOCUMENT_FRAGMENT_NODE) {\n    node.firstChild = node.lastChild = null;\n  }\n  return node;\n}\n\n/**\n * Appends `newChild` to `parentNode`.\n * If `newChild` is already connected to a `parentNode` it is first removed from it.\n *\n * @see https://github.com/xmldom/xmldom/issues/135\n * @see https://github.com/xmldom/xmldom/issues/145\n * @param {Node} parentNode\n * @param {Node} newChild\n * @returns {Node}\n * @private\n */\nfunction _appendSingleChild(parentNode, newChild) {\n  if (newChild.parentNode) {\n    newChild.parentNode.removeChild(newChild);\n  }\n  newChild.parentNode = parentNode;\n  newChild.previousSibling = parentNode.lastChild;\n  newChild.nextSibling = null;\n  if (newChild.previousSibling) {\n    newChild.previousSibling.nextSibling = newChild;\n  } else {\n    parentNode.firstChild = newChild;\n  }\n  parentNode.lastChild = newChild;\n  _onUpdateChild(parentNode.ownerDocument, parentNode, newChild);\n  return newChild;\n}\nDocument.prototype = {\n  //implementation : null,\n  nodeName: '#document',\n  nodeType: DOCUMENT_NODE,\n  /**\n   * The DocumentType node of the document.\n   *\n   * @readonly\n   * @type DocumentType\n   */\n  doctype: null,\n  documentElement: null,\n  _inc: 1,\n  insertBefore: function insertBefore(newChild, refChild) {\n    //raises\n    if (newChild.nodeType == DOCUMENT_FRAGMENT_NODE) {\n      var child = newChild.firstChild;\n      while (child) {\n        var next = child.nextSibling;\n        this.insertBefore(child, refChild);\n        child = next;\n      }\n      return newChild;\n    }\n    _insertBefore(this, newChild, refChild);\n    newChild.ownerDocument = this;\n    if (this.documentElement === null && newChild.nodeType === ELEMENT_NODE) {\n      this.documentElement = newChild;\n    }\n    return newChild;\n  },\n  removeChild: function removeChild(oldChild) {\n    if (this.documentElement == oldChild) {\n      this.documentElement = null;\n    }\n    return _removeChild(this, oldChild);\n  },\n  replaceChild: function replaceChild(newChild, oldChild) {\n    //raises\n    _insertBefore(this, newChild, oldChild, assertPreReplacementValidityInDocument);\n    newChild.ownerDocument = this;\n    if (oldChild) {\n      this.removeChild(oldChild);\n    }\n    if (isElementNode(newChild)) {\n      this.documentElement = newChild;\n    }\n  },\n  // Introduced in DOM Level 2:\n  importNode: function importNode(importedNode, deep) {\n    return _importNode(this, importedNode, deep);\n  },\n  // Introduced in DOM Level 2:\n  getElementById: function getElementById(id) {\n    var rtv = null;\n    _visitNode(this.documentElement, function (node) {\n      if (node.nodeType == ELEMENT_NODE) {\n        if (node.getAttribute('id') == id) {\n          rtv = node;\n          return true;\n        }\n      }\n    });\n    return rtv;\n  },\n  /**\n   * The `getElementsByClassName` method of `Document` interface returns an array-like object\n   * of all child elements which have **all** of the given class name(s).\n   *\n   * Returns an empty list if `classeNames` is an empty string or only contains HTML white space characters.\n   *\n   *\n   * Warning: This is a live LiveNodeList.\n   * Changes in the DOM will reflect in the array as the changes occur.\n   * If an element selected by this array no longer qualifies for the selector,\n   * it will automatically be removed. Be aware of this for iteration purposes.\n   *\n   * @param {string} classNames is a string representing the class name(s) to match; multiple class names are separated by (ASCII-)whitespace\n   *\n   * @see https://developer.mozilla.org/en-US/docs/Web/API/Document/getElementsByClassName\n   * @see https://dom.spec.whatwg.org/#concept-getelementsbyclassname\n   */\n  getElementsByClassName: function getElementsByClassName(classNames) {\n    var classNamesSet = toOrderedSet(classNames);\n    return new LiveNodeList(this, function (base) {\n      var ls = [];\n      if (classNamesSet.length > 0) {\n        _visitNode(base.documentElement, function (node) {\n          if (node !== base && node.nodeType === ELEMENT_NODE) {\n            var nodeClassNames = node.getAttribute('class');\n            // can be null if the attribute does not exist\n            if (nodeClassNames) {\n              // before splitting and iterating just compare them for the most common case\n              var matches = classNames === nodeClassNames;\n              if (!matches) {\n                var nodeClassNamesSet = toOrderedSet(nodeClassNames);\n                matches = classNamesSet.every(arrayIncludes(nodeClassNamesSet));\n              }\n              if (matches) {\n                ls.push(node);\n              }\n            }\n          }\n        });\n      }\n      return ls;\n    });\n  },\n  //document factory method:\n  createElement: function createElement(tagName) {\n    var node = new Element();\n    node.ownerDocument = this;\n    node.nodeName = tagName;\n    node.tagName = tagName;\n    node.localName = tagName;\n    node.childNodes = new NodeList();\n    var attrs = node.attributes = new NamedNodeMap();\n    attrs._ownerElement = node;\n    return node;\n  },\n  createDocumentFragment: function createDocumentFragment() {\n    var node = new DocumentFragment();\n    node.ownerDocument = this;\n    node.childNodes = new NodeList();\n    return node;\n  },\n  createTextNode: function createTextNode(data) {\n    var node = new Text();\n    node.ownerDocument = this;\n    node.appendData(data);\n    return node;\n  },\n  createComment: function createComment(data) {\n    var node = new Comment();\n    node.ownerDocument = this;\n    node.appendData(data);\n    return node;\n  },\n  createCDATASection: function createCDATASection(data) {\n    var node = new CDATASection();\n    node.ownerDocument = this;\n    node.appendData(data);\n    return node;\n  },\n  createProcessingInstruction: function createProcessingInstruction(target, data) {\n    var node = new ProcessingInstruction();\n    node.ownerDocument = this;\n    node.tagName = node.nodeName = node.target = target;\n    node.nodeValue = node.data = data;\n    return node;\n  },\n  createAttribute: function createAttribute(name) {\n    var node = new Attr();\n    node.ownerDocument = this;\n    node.name = name;\n    node.nodeName = name;\n    node.localName = name;\n    node.specified = true;\n    return node;\n  },\n  createEntityReference: function createEntityReference(name) {\n    var node = new EntityReference();\n    node.ownerDocument = this;\n    node.nodeName = name;\n    return node;\n  },\n  // Introduced in DOM Level 2:\n  createElementNS: function createElementNS(namespaceURI, qualifiedName) {\n    var node = new Element();\n    var pl = qualifiedName.split(':');\n    var attrs = node.attributes = new NamedNodeMap();\n    node.childNodes = new NodeList();\n    node.ownerDocument = this;\n    node.nodeName = qualifiedName;\n    node.tagName = qualifiedName;\n    node.namespaceURI = namespaceURI;\n    if (pl.length == 2) {\n      node.prefix = pl[0];\n      node.localName = pl[1];\n    } else {\n      //el.prefix = null;\n      node.localName = qualifiedName;\n    }\n    attrs._ownerElement = node;\n    return node;\n  },\n  // Introduced in DOM Level 2:\n  createAttributeNS: function createAttributeNS(namespaceURI, qualifiedName) {\n    var node = new Attr();\n    var pl = qualifiedName.split(':');\n    node.ownerDocument = this;\n    node.nodeName = qualifiedName;\n    node.name = qualifiedName;\n    node.namespaceURI = namespaceURI;\n    node.specified = true;\n    if (pl.length == 2) {\n      node.prefix = pl[0];\n      node.localName = pl[1];\n    } else {\n      //el.prefix = null;\n      node.localName = qualifiedName;\n    }\n    return node;\n  }\n};\n_extends(Document, Node);\nfunction Element() {\n  this._nsMap = {};\n}\n;\nElement.prototype = {\n  nodeType: ELEMENT_NODE,\n  hasAttribute: function hasAttribute(name) {\n    return this.getAttributeNode(name) != null;\n  },\n  getAttribute: function getAttribute(name) {\n    var attr = this.getAttributeNode(name);\n    return attr && attr.value || '';\n  },\n  getAttributeNode: function getAttributeNode(name) {\n    return this.attributes.getNamedItem(name);\n  },\n  setAttribute: function setAttribute(name, value) {\n    var attr = this.ownerDocument.createAttribute(name);\n    attr.value = attr.nodeValue = \"\" + value;\n    this.setAttributeNode(attr);\n  },\n  removeAttribute: function removeAttribute(name) {\n    var attr = this.getAttributeNode(name);\n    attr && this.removeAttributeNode(attr);\n  },\n  //four real opeartion method\n  appendChild: function appendChild(newChild) {\n    if (newChild.nodeType === DOCUMENT_FRAGMENT_NODE) {\n      return this.insertBefore(newChild, null);\n    } else {\n      return _appendSingleChild(this, newChild);\n    }\n  },\n  setAttributeNode: function setAttributeNode(newAttr) {\n    return this.attributes.setNamedItem(newAttr);\n  },\n  setAttributeNodeNS: function setAttributeNodeNS(newAttr) {\n    return this.attributes.setNamedItemNS(newAttr);\n  },\n  removeAttributeNode: function removeAttributeNode(oldAttr) {\n    //console.log(this == oldAttr.ownerElement)\n    return this.attributes.removeNamedItem(oldAttr.nodeName);\n  },\n  //get real attribute name,and remove it by removeAttributeNode\n  removeAttributeNS: function removeAttributeNS(namespaceURI, localName) {\n    var old = this.getAttributeNodeNS(namespaceURI, localName);\n    old && this.removeAttributeNode(old);\n  },\n  hasAttributeNS: function hasAttributeNS(namespaceURI, localName) {\n    return this.getAttributeNodeNS(namespaceURI, localName) != null;\n  },\n  getAttributeNS: function getAttributeNS(namespaceURI, localName) {\n    var attr = this.getAttributeNodeNS(namespaceURI, localName);\n    return attr && attr.value || '';\n  },\n  setAttributeNS: function setAttributeNS(namespaceURI, qualifiedName, value) {\n    var attr = this.ownerDocument.createAttributeNS(namespaceURI, qualifiedName);\n    attr.value = attr.nodeValue = \"\" + value;\n    this.setAttributeNode(attr);\n  },\n  getAttributeNodeNS: function getAttributeNodeNS(namespaceURI, localName) {\n    return this.attributes.getNamedItemNS(namespaceURI, localName);\n  },\n  getElementsByTagName: function getElementsByTagName(tagName) {\n    return new LiveNodeList(this, function (base) {\n      var ls = [];\n      _visitNode(base, function (node) {\n        if (node !== base && node.nodeType == ELEMENT_NODE && (tagName === '*' || node.tagName == tagName)) {\n          ls.push(node);\n        }\n      });\n      return ls;\n    });\n  },\n  getElementsByTagNameNS: function getElementsByTagNameNS(namespaceURI, localName) {\n    return new LiveNodeList(this, function (base) {\n      var ls = [];\n      _visitNode(base, function (node) {\n        if (node !== base && node.nodeType === ELEMENT_NODE && (namespaceURI === '*' || node.namespaceURI === namespaceURI) && (localName === '*' || node.localName == localName)) {\n          ls.push(node);\n        }\n      });\n      return ls;\n    });\n  }\n};\nDocument.prototype.getElementsByTagName = Element.prototype.getElementsByTagName;\nDocument.prototype.getElementsByTagNameNS = Element.prototype.getElementsByTagNameNS;\n_extends(Element, Node);\nfunction Attr() {}\n;\nAttr.prototype.nodeType = ATTRIBUTE_NODE;\n_extends(Attr, Node);\nfunction CharacterData() {}\n;\nCharacterData.prototype = {\n  data: '',\n  substringData: function substringData(offset, count) {\n    return this.data.substring(offset, offset + count);\n  },\n  appendData: function appendData(text) {\n    text = this.data + text;\n    this.nodeValue = this.data = text;\n    this.length = text.length;\n  },\n  insertData: function insertData(offset, text) {\n    this.replaceData(offset, 0, text);\n  },\n  appendChild: function appendChild(newChild) {\n    throw new Error(ExceptionMessage[HIERARCHY_REQUEST_ERR]);\n  },\n  deleteData: function deleteData(offset, count) {\n    this.replaceData(offset, count, \"\");\n  },\n  replaceData: function replaceData(offset, count, text) {\n    var start = this.data.substring(0, offset);\n    var end = this.data.substring(offset + count);\n    text = start + text + end;\n    this.nodeValue = this.data = text;\n    this.length = text.length;\n  }\n};\n_extends(CharacterData, Node);\nfunction Text() {}\n;\nText.prototype = {\n  nodeName: \"#text\",\n  nodeType: TEXT_NODE,\n  splitText: function splitText(offset) {\n    var text = this.data;\n    var newText = text.substring(offset);\n    text = text.substring(0, offset);\n    this.data = this.nodeValue = text;\n    this.length = text.length;\n    var newNode = this.ownerDocument.createTextNode(newText);\n    if (this.parentNode) {\n      this.parentNode.insertBefore(newNode, this.nextSibling);\n    }\n    return newNode;\n  }\n};\n_extends(Text, CharacterData);\nfunction Comment() {}\n;\nComment.prototype = {\n  nodeName: \"#comment\",\n  nodeType: COMMENT_NODE\n};\n_extends(Comment, CharacterData);\nfunction CDATASection() {}\n;\nCDATASection.prototype = {\n  nodeName: \"#cdata-section\",\n  nodeType: CDATA_SECTION_NODE\n};\n_extends(CDATASection, CharacterData);\nfunction DocumentType() {}\n;\nDocumentType.prototype.nodeType = DOCUMENT_TYPE_NODE;\n_extends(DocumentType, Node);\nfunction Notation() {}\n;\nNotation.prototype.nodeType = NOTATION_NODE;\n_extends(Notation, Node);\nfunction Entity() {}\n;\nEntity.prototype.nodeType = ENTITY_NODE;\n_extends(Entity, Node);\nfunction EntityReference() {}\n;\nEntityReference.prototype.nodeType = ENTITY_REFERENCE_NODE;\n_extends(EntityReference, Node);\nfunction DocumentFragment() {}\n;\nDocumentFragment.prototype.nodeName = \"#document-fragment\";\nDocumentFragment.prototype.nodeType = DOCUMENT_FRAGMENT_NODE;\n_extends(DocumentFragment, Node);\nfunction ProcessingInstruction() {}\nProcessingInstruction.prototype.nodeType = PROCESSING_INSTRUCTION_NODE;\n_extends(ProcessingInstruction, Node);\nfunction XMLSerializer() {}\nXMLSerializer.prototype.serializeToString = function (node, isHtml, nodeFilter) {\n  return nodeSerializeToString.call(node, isHtml, nodeFilter);\n};\nNode.prototype.toString = nodeSerializeToString;\nfunction nodeSerializeToString(isHtml, nodeFilter) {\n  var buf = [];\n  var refNode = this.nodeType == 9 && this.documentElement || this;\n  var prefix = refNode.prefix;\n  var uri = refNode.namespaceURI;\n  if (uri && prefix == null) {\n    //console.log(prefix)\n    var prefix = refNode.lookupPrefix(uri);\n    if (prefix == null) {\n      //isHTML = true;\n      var visibleNamespaces = [{\n        namespace: uri,\n        prefix: null\n      }\n      //{namespace:uri,prefix:''}\n      ];\n    }\n  }\n  serializeToString(this, buf, isHtml, nodeFilter, visibleNamespaces);\n  //console.log('###',this.nodeType,uri,prefix,buf.join(''))\n  return buf.join('');\n}\nfunction needNamespaceDefine(node, isHTML, visibleNamespaces) {\n  var prefix = node.prefix || '';\n  var uri = node.namespaceURI;\n  // According to [Namespaces in XML 1.0](https://www.w3.org/TR/REC-xml-names/#ns-using) ,\n  // and more specifically https://www.w3.org/TR/REC-xml-names/#nsc-NoPrefixUndecl :\n  // > In a namespace declaration for a prefix [...], the attribute value MUST NOT be empty.\n  // in a similar manner [Namespaces in XML 1.1](https://www.w3.org/TR/xml-names11/#ns-using)\n  // and more specifically https://www.w3.org/TR/xml-names11/#nsc-NSDeclared :\n  // > [...] Furthermore, the attribute value [...] must not be an empty string.\n  // so serializing empty namespace value like xmlns:ds=\"\" would produce an invalid XML document.\n  if (!uri) {\n    return false;\n  }\n  if (prefix === \"xml\" && uri === NAMESPACE.XML || uri === NAMESPACE.XMLNS) {\n    return false;\n  }\n  var i = visibleNamespaces.length;\n  while (i--) {\n    var ns = visibleNamespaces[i];\n    // get namespace prefix\n    if (ns.prefix === prefix) {\n      return ns.namespace !== uri;\n    }\n  }\n  return true;\n}\n/**\n * Well-formed constraint: No < in Attribute Values\n * > The replacement text of any entity referred to directly or indirectly\n * > in an attribute value must not contain a <.\n * @see https://www.w3.org/TR/xml11/#CleanAttrVals\n * @see https://www.w3.org/TR/xml11/#NT-AttValue\n *\n * Literal whitespace other than space that appear in attribute values\n * are serialized as their entity references, so they will be preserved.\n * (In contrast to whitespace literals in the input which are normalized to spaces)\n * @see https://www.w3.org/TR/xml11/#AVNormalize\n * @see https://w3c.github.io/DOM-Parsing/#serializing-an-element-s-attributes\n */\nfunction addSerializedAttribute(buf, qualifiedName, value) {\n  buf.push(' ', qualifiedName, '=\"', value.replace(/[<>&\"\\t\\n\\r]/g, _xmlEncoder), '\"');\n}\nfunction serializeToString(node, buf, isHTML, nodeFilter, visibleNamespaces) {\n  if (!visibleNamespaces) {\n    visibleNamespaces = [];\n  }\n  if (nodeFilter) {\n    node = nodeFilter(node);\n    if (node) {\n      if (typeof node == 'string') {\n        buf.push(node);\n        return;\n      }\n    } else {\n      return;\n    }\n    //buf.sort.apply(attrs, attributeSorter);\n  }\n  switch (node.nodeType) {\n    case ELEMENT_NODE:\n      var attrs = node.attributes;\n      var len = attrs.length;\n      var child = node.firstChild;\n      var nodeName = node.tagName;\n      isHTML = NAMESPACE.isHTML(node.namespaceURI) || isHTML;\n      var prefixedNodeName = nodeName;\n      if (!isHTML && !node.prefix && node.namespaceURI) {\n        var defaultNS;\n        // lookup current default ns from `xmlns` attribute\n        for (var ai = 0; ai < attrs.length; ai++) {\n          if (attrs.item(ai).name === 'xmlns') {\n            defaultNS = attrs.item(ai).value;\n            break;\n          }\n        }\n        if (!defaultNS) {\n          // lookup current default ns in visibleNamespaces\n          for (var nsi = visibleNamespaces.length - 1; nsi >= 0; nsi--) {\n            var namespace = visibleNamespaces[nsi];\n            if (namespace.prefix === '' && namespace.namespace === node.namespaceURI) {\n              defaultNS = namespace.namespace;\n              break;\n            }\n          }\n        }\n        if (defaultNS !== node.namespaceURI) {\n          for (var nsi = visibleNamespaces.length - 1; nsi >= 0; nsi--) {\n            var namespace = visibleNamespaces[nsi];\n            if (namespace.namespace === node.namespaceURI) {\n              if (namespace.prefix) {\n                prefixedNodeName = namespace.prefix + ':' + nodeName;\n              }\n              break;\n            }\n          }\n        }\n      }\n      buf.push('<', prefixedNodeName);\n      for (var i = 0; i < len; i++) {\n        // add namespaces for attributes\n        var attr = attrs.item(i);\n        if (attr.prefix == 'xmlns') {\n          visibleNamespaces.push({\n            prefix: attr.localName,\n            namespace: attr.value\n          });\n        } else if (attr.nodeName == 'xmlns') {\n          visibleNamespaces.push({\n            prefix: '',\n            namespace: attr.value\n          });\n        }\n      }\n      for (var i = 0; i < len; i++) {\n        var attr = attrs.item(i);\n        if (needNamespaceDefine(attr, isHTML, visibleNamespaces)) {\n          var prefix = attr.prefix || '';\n          var uri = attr.namespaceURI;\n          addSerializedAttribute(buf, prefix ? 'xmlns:' + prefix : \"xmlns\", uri);\n          visibleNamespaces.push({\n            prefix: prefix,\n            namespace: uri\n          });\n        }\n        serializeToString(attr, buf, isHTML, nodeFilter, visibleNamespaces);\n      }\n\n      // add namespace for current node\n      if (nodeName === prefixedNodeName && needNamespaceDefine(node, isHTML, visibleNamespaces)) {\n        var prefix = node.prefix || '';\n        var uri = node.namespaceURI;\n        addSerializedAttribute(buf, prefix ? 'xmlns:' + prefix : \"xmlns\", uri);\n        visibleNamespaces.push({\n          prefix: prefix,\n          namespace: uri\n        });\n      }\n      if (child || isHTML && !/^(?:meta|link|img|br|hr|input)$/i.test(nodeName)) {\n        buf.push('>');\n        //if is cdata child node\n        if (isHTML && /^script$/i.test(nodeName)) {\n          while (child) {\n            if (child.data) {\n              buf.push(child.data);\n            } else {\n              serializeToString(child, buf, isHTML, nodeFilter, visibleNamespaces.slice());\n            }\n            child = child.nextSibling;\n          }\n        } else {\n          while (child) {\n            serializeToString(child, buf, isHTML, nodeFilter, visibleNamespaces.slice());\n            child = child.nextSibling;\n          }\n        }\n        buf.push('</', prefixedNodeName, '>');\n      } else {\n        buf.push('/>');\n      }\n      // remove added visible namespaces\n      //visibleNamespaces.length = startVisibleNamespaces;\n      return;\n    case DOCUMENT_NODE:\n    case DOCUMENT_FRAGMENT_NODE:\n      var child = node.firstChild;\n      while (child) {\n        serializeToString(child, buf, isHTML, nodeFilter, visibleNamespaces.slice());\n        child = child.nextSibling;\n      }\n      return;\n    case ATTRIBUTE_NODE:\n      return addSerializedAttribute(buf, node.name, node.value);\n    case TEXT_NODE:\n      /**\n       * The ampersand character (&) and the left angle bracket (<) must not appear in their literal form,\n       * except when used as markup delimiters, or within a comment, a processing instruction, or a CDATA section.\n       * If they are needed elsewhere, they must be escaped using either numeric character references or the strings\n       * `&amp;` and `&lt;` respectively.\n       * The right angle bracket (>) may be represented using the string \" &gt; \", and must, for compatibility,\n       * be escaped using either `&gt;` or a character reference when it appears in the string `]]>` in content,\n       * when that string is not marking the end of a CDATA section.\n       *\n       * In the content of elements, character data is any string of characters\n       * which does not contain the start-delimiter of any markup\n       * and does not include the CDATA-section-close delimiter, `]]>`.\n       *\n       * @see https://www.w3.org/TR/xml/#NT-CharData\n       * @see https://w3c.github.io/DOM-Parsing/#xml-serializing-a-text-node\n       */\n      return buf.push(node.data.replace(/[<&>]/g, _xmlEncoder));\n    case CDATA_SECTION_NODE:\n      return buf.push('<![CDATA[', node.data, ']]>');\n    case COMMENT_NODE:\n      return buf.push(\"<!--\", node.data, \"-->\");\n    case DOCUMENT_TYPE_NODE:\n      var pubid = node.publicId;\n      var sysid = node.systemId;\n      buf.push('<!DOCTYPE ', node.name);\n      if (pubid) {\n        buf.push(' PUBLIC ', pubid);\n        if (sysid && sysid != '.') {\n          buf.push(' ', sysid);\n        }\n        buf.push('>');\n      } else if (sysid && sysid != '.') {\n        buf.push(' SYSTEM ', sysid, '>');\n      } else {\n        var sub = node.internalSubset;\n        if (sub) {\n          buf.push(\" [\", sub, \"]\");\n        }\n        buf.push(\">\");\n      }\n      return;\n    case PROCESSING_INSTRUCTION_NODE:\n      return buf.push(\"<?\", node.target, \" \", node.data, \"?>\");\n    case ENTITY_REFERENCE_NODE:\n      return buf.push('&', node.nodeName, ';');\n    //case ENTITY_NODE:\n    //case NOTATION_NODE:\n    default:\n      buf.push('??', node.nodeName);\n  }\n}\nfunction _importNode(doc, node, deep) {\n  var node2;\n  switch (node.nodeType) {\n    case ELEMENT_NODE:\n      node2 = node.cloneNode(false);\n      node2.ownerDocument = doc;\n    //var attrs = node2.attributes;\n    //var len = attrs.length;\n    //for(var i=0;i<len;i++){\n    //node2.setAttributeNodeNS(importNode(doc,attrs.item(i),deep));\n    //}\n    case DOCUMENT_FRAGMENT_NODE:\n      break;\n    case ATTRIBUTE_NODE:\n      deep = true;\n      break;\n    //case ENTITY_REFERENCE_NODE:\n    //case PROCESSING_INSTRUCTION_NODE:\n    ////case TEXT_NODE:\n    //case CDATA_SECTION_NODE:\n    //case COMMENT_NODE:\n    //\tdeep = false;\n    //\tbreak;\n    //case DOCUMENT_NODE:\n    //case DOCUMENT_TYPE_NODE:\n    //cannot be imported.\n    //case ENTITY_NODE:\n    //case NOTATION_NODE：\n    //can not hit in level3\n    //default:throw e;\n  }\n  if (!node2) {\n    node2 = node.cloneNode(false); //false\n  }\n  node2.ownerDocument = doc;\n  node2.parentNode = null;\n  if (deep) {\n    var child = node.firstChild;\n    while (child) {\n      node2.appendChild(_importNode(doc, child, deep));\n      child = child.nextSibling;\n    }\n  }\n  return node2;\n}\n//\n//var _relationMap = {firstChild:1,lastChild:1,previousSibling:1,nextSibling:1,\n//\t\t\t\t\tattributes:1,childNodes:1,parentNode:1,documentElement:1,doctype,};\nfunction _cloneNode(doc, node, deep) {\n  var node2 = new node.constructor();\n  for (var n in node) {\n    if (Object.prototype.hasOwnProperty.call(node, n)) {\n      var v = node[n];\n      if (typeof v != \"object\") {\n        if (v != node2[n]) {\n          node2[n] = v;\n        }\n      }\n    }\n  }\n  if (node.childNodes) {\n    node2.childNodes = new NodeList();\n  }\n  node2.ownerDocument = doc;\n  switch (node2.nodeType) {\n    case ELEMENT_NODE:\n      var attrs = node.attributes;\n      var attrs2 = node2.attributes = new NamedNodeMap();\n      var len = attrs.length;\n      attrs2._ownerElement = node2;\n      for (var i = 0; i < len; i++) {\n        node2.setAttributeNode(_cloneNode(doc, attrs.item(i), true));\n      }\n      break;\n      ;\n    case ATTRIBUTE_NODE:\n      deep = true;\n  }\n  if (deep) {\n    var child = node.firstChild;\n    while (child) {\n      node2.appendChild(_cloneNode(doc, child, deep));\n      child = child.nextSibling;\n    }\n  }\n  return node2;\n}\nfunction __set__(object, key, value) {\n  object[key] = value;\n}\n//do dynamic\ntry {\n  if (Object.defineProperty) {\n    var _getTextContent = function getTextContent(node) {\n      switch (node.nodeType) {\n        case ELEMENT_NODE:\n        case DOCUMENT_FRAGMENT_NODE:\n          var buf = [];\n          node = node.firstChild;\n          while (node) {\n            if (node.nodeType !== 7 && node.nodeType !== 8) {\n              buf.push(_getTextContent(node));\n            }\n            node = node.nextSibling;\n          }\n          return buf.join('');\n        default:\n          return node.nodeValue;\n      }\n    };\n    Object.defineProperty(LiveNodeList.prototype, 'length', {\n      get: function get() {\n        _updateLiveList(this);\n        return this.$$length;\n      }\n    });\n    Object.defineProperty(Node.prototype, 'textContent', {\n      get: function get() {\n        return _getTextContent(this);\n      },\n      set: function set(data) {\n        switch (this.nodeType) {\n          case ELEMENT_NODE:\n          case DOCUMENT_FRAGMENT_NODE:\n            while (this.firstChild) {\n              this.removeChild(this.firstChild);\n            }\n            if (data || String(data)) {\n              this.appendChild(this.ownerDocument.createTextNode(data));\n            }\n            break;\n          default:\n            this.data = data;\n            this.value = data;\n            this.nodeValue = data;\n        }\n      }\n    });\n    __set__ = function __set__(object, key, value) {\n      //console.log(value)\n      object['$$' + key] = value;\n    };\n  }\n} catch (e) {//ie8\n}\n\n//if(typeof require == 'function'){\nexports.DocumentType = DocumentType;\nexports.DOMException = DOMException;\nexports.DOMImplementation = DOMImplementation;\nexports.Element = Element;\nexports.Node = Node;\nexports.NodeList = NodeList;\nexports.XMLSerializer = XMLSerializer;\n//}", "map": {"version": 3, "names": ["conventions", "require", "find", "NAMESPACE", "notEmptyString", "input", "splitOnASCIIWhitespace", "split", "filter", "orderedSetReducer", "current", "element", "hasOwnProperty", "toOrderedSet", "list", "Object", "keys", "reduce", "arrayIncludes", "indexOf", "copy", "src", "dest", "p", "prototype", "call", "_extends", "Class", "Super", "pt", "t", "constructor", "console", "error", "NodeType", "ELEMENT_NODE", "ATTRIBUTE_NODE", "TEXT_NODE", "CDATA_SECTION_NODE", "ENTITY_REFERENCE_NODE", "ENTITY_NODE", "PROCESSING_INSTRUCTION_NODE", "COMMENT_NODE", "DOCUMENT_NODE", "DOCUMENT_TYPE_NODE", "DOCUMENT_FRAGMENT_NODE", "NOTATION_NODE", "ExceptionCode", "ExceptionMessage", "INDEX_SIZE_ERR", "DOMSTRING_SIZE_ERR", "HIERARCHY_REQUEST_ERR", "WRONG_DOCUMENT_ERR", "INVALID_CHARACTER_ERR", "NO_DATA_ALLOWED_ERR", "NO_MODIFICATION_ALLOWED_ERR", "NOT_FOUND_ERR", "NOT_SUPPORTED_ERR", "INUSE_ATTRIBUTE_ERR", "INVALID_STATE_ERR", "SYNTAX_ERR", "INVALID_MODIFICATION_ERR", "NAMESPACE_ERR", "INVALID_ACCESS_ERR", "DOMException", "code", "message", "Error", "captureStackTrace", "NodeList", "length", "item", "index", "toString", "isHTML", "nodeFilter", "buf", "i", "serializeToString", "join", "predicate", "Array", "LiveNodeList", "node", "refresh", "_node", "_refresh", "_updateLiveList", "inc", "_inc", "ownerDocument", "ls", "__set__", "$$length", "NamedNodeMap", "_findNodeIndex", "_addNamedNode", "el", "newAttr", "oldAttr", "ownerElement", "doc", "_onRemoveAttribute", "_onAddAttribute", "_removeNamedNode", "attr", "lastIndex", "tagName", "getNamedItem", "key", "nodeName", "setNamedItem", "_ownerElement", "setNamedItemNS", "getNamedItemNS", "namespaceURI", "localName", "removeNamedItem", "removeNamedItemNS", "DOMImplementation", "hasFeature", "feature", "version", "createDocument", "qualifiedName", "doctype", "Document", "implementation", "childNodes", "append<PERSON><PERSON><PERSON>", "root", "createElementNS", "createDocumentType", "publicId", "systemId", "DocumentType", "name", "Node", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "previousSibling", "nextS<PERSON>ling", "attributes", "parentNode", "nodeValue", "prefix", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "refChild", "_insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "assertPreReplacementValidityInDocument", "<PERSON><PERSON><PERSON><PERSON>", "_remove<PERSON><PERSON>d", "hasChildNodes", "cloneNode", "deep", "normalize", "child", "next", "nodeType", "appendData", "data", "isSupported", "hasAttributes", "lookupPrefix", "map", "_nsMap", "n", "lookupNamespaceURI", "isDefaultNamespace", "_xmlEncoder", "c", "charCodeAt", "_visitNode", "callback", "ns", "XMLNS", "value", "remove", "_onUpdateChild", "cs", "previous", "hasValidParentNodeType", "hasInsertableNodeType", "isElementNode", "isTextNode", "isDocTypeNode", "isElementInsertionPossible", "parentChildNodes", "docTypeNode", "isElementReplacementPossible", "hasElementChildThatIsNotChild", "assertPreInsertionValidity1to5", "parent", "assertPreInsertionValidityInDocument", "nodeChildNodes", "nodeChildElements", "parent<PERSON><PERSON><PERSON><PERSON><PERSON>", "hasDoctypeChildThatIsNotChild", "_inDocumentAssertion", "cp", "newFirst", "newLast", "pre", "_appendSingleChild", "documentElement", "importNode", "importedNode", "getElementById", "id", "rtv", "getAttribute", "getElementsByClassName", "classNames", "classNamesSet", "base", "nodeClassNames", "matches", "nodeClassNamesSet", "every", "push", "createElement", "Element", "attrs", "createDocumentFragment", "DocumentFragment", "createTextNode", "Text", "createComment", "Comment", "createCDATASection", "CDATASection", "createProcessingInstruction", "target", "ProcessingInstruction", "createAttribute", "Attr", "specified", "createEntityReference", "EntityReference", "pl", "createAttributeNS", "hasAttribute", "getAttributeNode", "setAttribute", "setAttributeNode", "removeAttribute", "removeAttributeNode", "setAttributeNodeNS", "removeAttributeNS", "old", "getAttributeNodeNS", "hasAttributeNS", "getAttributeNS", "setAttributeNS", "getElementsByTagName", "getElementsByTagNameNS", "CharacterData", "substringData", "offset", "count", "substring", "text", "insertData", "replaceData", "deleteData", "start", "end", "splitText", "newText", "newNode", "Notation", "Entity", "XMLSerializer", "isHtml", "nodeSerializeToString", "refNode", "uri", "visibleNamespaces", "namespace", "needNamespaceDefine", "XML", "addSerializedAttribute", "replace", "len", "prefixedNodeName", "defaultNS", "ai", "nsi", "test", "slice", "pubid", "sysid", "sub", "internalSubset", "node2", "v", "attrs2", "object", "defineProperty", "getTextContent", "get", "set", "String", "e", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/@xmldom+xmldom@0.8.10/node_modules/@xmldom/xmldom/lib/dom.js"], "sourcesContent": ["var conventions = require(\"./conventions\");\n\nvar find = conventions.find;\nvar NAMESPACE = conventions.NAMESPACE;\n\n/**\n * A prerequisite for `[].filter`, to drop elements that are empty\n * @param {string} input\n * @returns {boolean}\n */\nfunction notEmptyString (input) {\n\treturn input !== ''\n}\n/**\n * @see https://infra.spec.whatwg.org/#split-on-ascii-whitespace\n * @see https://infra.spec.whatwg.org/#ascii-whitespace\n *\n * @param {string} input\n * @returns {string[]} (can be empty)\n */\nfunction splitOnASCIIWhitespace(input) {\n\t// U+0009 TAB, U+000A LF, U+000C FF, U+000D CR, U+0020 SPACE\n\treturn input ? input.split(/[\\t\\n\\f\\r ]+/).filter(notEmptyString) : []\n}\n\n/**\n * Adds element as a key to current if it is not already present.\n *\n * @param {Record<string, boolean | undefined>} current\n * @param {string} element\n * @returns {Record<string, boolean | undefined>}\n */\nfunction orderedSetReducer (current, element) {\n\tif (!current.hasOwnProperty(element)) {\n\t\tcurrent[element] = true;\n\t}\n\treturn current;\n}\n\n/**\n * @see https://infra.spec.whatwg.org/#ordered-set\n * @param {string} input\n * @returns {string[]}\n */\nfunction toOrderedSet(input) {\n\tif (!input) return [];\n\tvar list = splitOnASCIIWhitespace(input);\n\treturn Object.keys(list.reduce(orderedSetReducer, {}))\n}\n\n/**\n * Uses `list.indexOf` to implement something like `Array.prototype.includes`,\n * which we can not rely on being available.\n *\n * @param {any[]} list\n * @returns {function(any): boolean}\n */\nfunction arrayIncludes (list) {\n\treturn function(element) {\n\t\treturn list && list.indexOf(element) !== -1;\n\t}\n}\n\nfunction copy(src,dest){\n\tfor(var p in src){\n\t\tif (Object.prototype.hasOwnProperty.call(src, p)) {\n\t\t\tdest[p] = src[p];\n\t\t}\n\t}\n}\n\n/**\n^\\w+\\.prototype\\.([_\\w]+)\\s*=\\s*((?:.*\\{\\s*?[\\r\\n][\\s\\S]*?^})|\\S.*?(?=[;\\r\\n]));?\n^\\w+\\.prototype\\.([_\\w]+)\\s*=\\s*(\\S.*?(?=[;\\r\\n]));?\n */\nfunction _extends(Class,Super){\n\tvar pt = Class.prototype;\n\tif(!(pt instanceof Super)){\n\t\tfunction t(){};\n\t\tt.prototype = Super.prototype;\n\t\tt = new t();\n\t\tcopy(pt,t);\n\t\tClass.prototype = pt = t;\n\t}\n\tif(pt.constructor != Class){\n\t\tif(typeof Class != 'function'){\n\t\t\tconsole.error(\"unknown Class:\"+Class)\n\t\t}\n\t\tpt.constructor = Class\n\t}\n}\n\n// Node Types\nvar NodeType = {}\nvar ELEMENT_NODE                = NodeType.ELEMENT_NODE                = 1;\nvar ATTRIBUTE_NODE              = NodeType.ATTRIBUTE_NODE              = 2;\nvar TEXT_NODE                   = NodeType.TEXT_NODE                   = 3;\nvar CDATA_SECTION_NODE          = NodeType.CDATA_SECTION_NODE          = 4;\nvar ENTITY_REFERENCE_NODE       = NodeType.ENTITY_REFERENCE_NODE       = 5;\nvar ENTITY_NODE                 = NodeType.ENTITY_NODE                 = 6;\nvar PROCESSING_INSTRUCTION_NODE = NodeType.PROCESSING_INSTRUCTION_NODE = 7;\nvar COMMENT_NODE                = NodeType.COMMENT_NODE                = 8;\nvar DOCUMENT_NODE               = NodeType.DOCUMENT_NODE               = 9;\nvar DOCUMENT_TYPE_NODE          = NodeType.DOCUMENT_TYPE_NODE          = 10;\nvar DOCUMENT_FRAGMENT_NODE      = NodeType.DOCUMENT_FRAGMENT_NODE      = 11;\nvar NOTATION_NODE               = NodeType.NOTATION_NODE               = 12;\n\n// ExceptionCode\nvar ExceptionCode = {}\nvar ExceptionMessage = {};\nvar INDEX_SIZE_ERR              = ExceptionCode.INDEX_SIZE_ERR              = ((ExceptionMessage[1]=\"Index size error\"),1);\nvar DOMSTRING_SIZE_ERR          = ExceptionCode.DOMSTRING_SIZE_ERR          = ((ExceptionMessage[2]=\"DOMString size error\"),2);\nvar HIERARCHY_REQUEST_ERR       = ExceptionCode.HIERARCHY_REQUEST_ERR       = ((ExceptionMessage[3]=\"Hierarchy request error\"),3);\nvar WRONG_DOCUMENT_ERR          = ExceptionCode.WRONG_DOCUMENT_ERR          = ((ExceptionMessage[4]=\"Wrong document\"),4);\nvar INVALID_CHARACTER_ERR       = ExceptionCode.INVALID_CHARACTER_ERR       = ((ExceptionMessage[5]=\"Invalid character\"),5);\nvar NO_DATA_ALLOWED_ERR         = ExceptionCode.NO_DATA_ALLOWED_ERR         = ((ExceptionMessage[6]=\"No data allowed\"),6);\nvar NO_MODIFICATION_ALLOWED_ERR = ExceptionCode.NO_MODIFICATION_ALLOWED_ERR = ((ExceptionMessage[7]=\"No modification allowed\"),7);\nvar NOT_FOUND_ERR               = ExceptionCode.NOT_FOUND_ERR               = ((ExceptionMessage[8]=\"Not found\"),8);\nvar NOT_SUPPORTED_ERR           = ExceptionCode.NOT_SUPPORTED_ERR           = ((ExceptionMessage[9]=\"Not supported\"),9);\nvar INUSE_ATTRIBUTE_ERR         = ExceptionCode.INUSE_ATTRIBUTE_ERR         = ((ExceptionMessage[10]=\"Attribute in use\"),10);\n//level2\nvar INVALID_STATE_ERR        \t= ExceptionCode.INVALID_STATE_ERR        \t= ((ExceptionMessage[11]=\"Invalid state\"),11);\nvar SYNTAX_ERR               \t= ExceptionCode.SYNTAX_ERR               \t= ((ExceptionMessage[12]=\"Syntax error\"),12);\nvar INVALID_MODIFICATION_ERR \t= ExceptionCode.INVALID_MODIFICATION_ERR \t= ((ExceptionMessage[13]=\"Invalid modification\"),13);\nvar NAMESPACE_ERR            \t= ExceptionCode.NAMESPACE_ERR           \t= ((ExceptionMessage[14]=\"Invalid namespace\"),14);\nvar INVALID_ACCESS_ERR       \t= ExceptionCode.INVALID_ACCESS_ERR      \t= ((ExceptionMessage[15]=\"Invalid access\"),15);\n\n/**\n * DOM Level 2\n * Object DOMException\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/ecma-script-binding.html\n * @see http://www.w3.org/TR/REC-DOM-Level-1/ecma-script-language-binding.html\n */\nfunction DOMException(code, message) {\n\tif(message instanceof Error){\n\t\tvar error = message;\n\t}else{\n\t\terror = this;\n\t\tError.call(this, ExceptionMessage[code]);\n\t\tthis.message = ExceptionMessage[code];\n\t\tif(Error.captureStackTrace) Error.captureStackTrace(this, DOMException);\n\t}\n\terror.code = code;\n\tif(message) this.message = this.message + \": \" + message;\n\treturn error;\n};\nDOMException.prototype = Error.prototype;\ncopy(ExceptionCode,DOMException)\n\n/**\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/core.html#ID-536297177\n * The NodeList interface provides the abstraction of an ordered collection of nodes, without defining or constraining how this collection is implemented. NodeList objects in the DOM are live.\n * The items in the NodeList are accessible via an integral index, starting from 0.\n */\nfunction NodeList() {\n};\nNodeList.prototype = {\n\t/**\n\t * The number of nodes in the list. The range of valid child node indices is 0 to length-1 inclusive.\n\t * @standard level1\n\t */\n\tlength:0,\n\t/**\n\t * Returns the indexth item in the collection. If index is greater than or equal to the number of nodes in the list, this returns null.\n\t * @standard level1\n\t * @param index  unsigned long\n\t *   Index into the collection.\n\t * @return Node\n\t * \tThe node at the indexth position in the NodeList, or null if that is not a valid index.\n\t */\n\titem: function(index) {\n\t\treturn index >= 0 && index < this.length ? this[index] : null;\n\t},\n\ttoString:function(isHTML,nodeFilter){\n\t\tfor(var buf = [], i = 0;i<this.length;i++){\n\t\t\tserializeToString(this[i],buf,isHTML,nodeFilter);\n\t\t}\n\t\treturn buf.join('');\n\t},\n\t/**\n\t * @private\n\t * @param {function (Node):boolean} predicate\n\t * @returns {Node[]}\n\t */\n\tfilter: function (predicate) {\n\t\treturn Array.prototype.filter.call(this, predicate);\n\t},\n\t/**\n\t * @private\n\t * @param {Node} item\n\t * @returns {number}\n\t */\n\tindexOf: function (item) {\n\t\treturn Array.prototype.indexOf.call(this, item);\n\t},\n};\n\nfunction LiveNodeList(node,refresh){\n\tthis._node = node;\n\tthis._refresh = refresh\n\t_updateLiveList(this);\n}\nfunction _updateLiveList(list){\n\tvar inc = list._node._inc || list._node.ownerDocument._inc;\n\tif (list._inc !== inc) {\n\t\tvar ls = list._refresh(list._node);\n\t\t__set__(list,'length',ls.length);\n\t\tif (!list.$$length || ls.length < list.$$length) {\n\t\t\tfor (var i = ls.length; i in list; i++) {\n\t\t\t\tif (Object.prototype.hasOwnProperty.call(list, i)) {\n\t\t\t\t\tdelete list[i];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\tcopy(ls,list);\n\t\tlist._inc = inc;\n\t}\n}\nLiveNodeList.prototype.item = function(i){\n\t_updateLiveList(this);\n\treturn this[i] || null;\n}\n\n_extends(LiveNodeList,NodeList);\n\n/**\n * Objects implementing the NamedNodeMap interface are used\n * to represent collections of nodes that can be accessed by name.\n * Note that NamedNodeMap does not inherit from NodeList;\n * NamedNodeMaps are not maintained in any particular order.\n * Objects contained in an object implementing NamedNodeMap may also be accessed by an ordinal index,\n * but this is simply to allow convenient enumeration of the contents of a NamedNodeMap,\n * and does not imply that the DOM specifies an order to these Nodes.\n * NamedNodeMap objects in the DOM are live.\n * used for attributes or DocumentType entities\n */\nfunction NamedNodeMap() {\n};\n\nfunction _findNodeIndex(list,node){\n\tvar i = list.length;\n\twhile(i--){\n\t\tif(list[i] === node){return i}\n\t}\n}\n\nfunction _addNamedNode(el,list,newAttr,oldAttr){\n\tif(oldAttr){\n\t\tlist[_findNodeIndex(list,oldAttr)] = newAttr;\n\t}else{\n\t\tlist[list.length++] = newAttr;\n\t}\n\tif(el){\n\t\tnewAttr.ownerElement = el;\n\t\tvar doc = el.ownerDocument;\n\t\tif(doc){\n\t\t\toldAttr && _onRemoveAttribute(doc,el,oldAttr);\n\t\t\t_onAddAttribute(doc,el,newAttr);\n\t\t}\n\t}\n}\nfunction _removeNamedNode(el,list,attr){\n\t//console.log('remove attr:'+attr)\n\tvar i = _findNodeIndex(list,attr);\n\tif(i>=0){\n\t\tvar lastIndex = list.length-1\n\t\twhile(i<lastIndex){\n\t\t\tlist[i] = list[++i]\n\t\t}\n\t\tlist.length = lastIndex;\n\t\tif(el){\n\t\t\tvar doc = el.ownerDocument;\n\t\t\tif(doc){\n\t\t\t\t_onRemoveAttribute(doc,el,attr);\n\t\t\t\tattr.ownerElement = null;\n\t\t\t}\n\t\t}\n\t}else{\n\t\tthrow new DOMException(NOT_FOUND_ERR,new Error(el.tagName+'@'+attr))\n\t}\n}\nNamedNodeMap.prototype = {\n\tlength:0,\n\titem:NodeList.prototype.item,\n\tgetNamedItem: function(key) {\n//\t\tif(key.indexOf(':')>0 || key == 'xmlns'){\n//\t\t\treturn null;\n//\t\t}\n\t\t//console.log()\n\t\tvar i = this.length;\n\t\twhile(i--){\n\t\t\tvar attr = this[i];\n\t\t\t//console.log(attr.nodeName,key)\n\t\t\tif(attr.nodeName == key){\n\t\t\t\treturn attr;\n\t\t\t}\n\t\t}\n\t},\n\tsetNamedItem: function(attr) {\n\t\tvar el = attr.ownerElement;\n\t\tif(el && el!=this._ownerElement){\n\t\t\tthrow new DOMException(INUSE_ATTRIBUTE_ERR);\n\t\t}\n\t\tvar oldAttr = this.getNamedItem(attr.nodeName);\n\t\t_addNamedNode(this._ownerElement,this,attr,oldAttr);\n\t\treturn oldAttr;\n\t},\n\t/* returns Node */\n\tsetNamedItemNS: function(attr) {// raises: WRONG_DOCUMENT_ERR,NO_MODIFICATION_ALLOWED_ERR,INUSE_ATTRIBUTE_ERR\n\t\tvar el = attr.ownerElement, oldAttr;\n\t\tif(el && el!=this._ownerElement){\n\t\t\tthrow new DOMException(INUSE_ATTRIBUTE_ERR);\n\t\t}\n\t\toldAttr = this.getNamedItemNS(attr.namespaceURI,attr.localName);\n\t\t_addNamedNode(this._ownerElement,this,attr,oldAttr);\n\t\treturn oldAttr;\n\t},\n\n\t/* returns Node */\n\tremoveNamedItem: function(key) {\n\t\tvar attr = this.getNamedItem(key);\n\t\t_removeNamedNode(this._ownerElement,this,attr);\n\t\treturn attr;\n\n\n\t},// raises: NOT_FOUND_ERR,NO_MODIFICATION_ALLOWED_ERR\n\n\t//for level2\n\tremoveNamedItemNS:function(namespaceURI,localName){\n\t\tvar attr = this.getNamedItemNS(namespaceURI,localName);\n\t\t_removeNamedNode(this._ownerElement,this,attr);\n\t\treturn attr;\n\t},\n\tgetNamedItemNS: function(namespaceURI, localName) {\n\t\tvar i = this.length;\n\t\twhile(i--){\n\t\t\tvar node = this[i];\n\t\t\tif(node.localName == localName && node.namespaceURI == namespaceURI){\n\t\t\t\treturn node;\n\t\t\t}\n\t\t}\n\t\treturn null;\n\t}\n};\n\n/**\n * The DOMImplementation interface represents an object providing methods\n * which are not dependent on any particular document.\n * Such an object is returned by the `Document.implementation` property.\n *\n * __The individual methods describe the differences compared to the specs.__\n *\n * @constructor\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation MDN\n * @see https://www.w3.org/TR/REC-DOM-Level-1/level-one-core.html#ID-102161490 DOM Level 1 Core (Initial)\n * @see https://www.w3.org/TR/DOM-Level-2-Core/core.html#ID-102161490 DOM Level 2 Core\n * @see https://www.w3.org/TR/DOM-Level-3-Core/core.html#ID-102161490 DOM Level 3 Core\n * @see https://dom.spec.whatwg.org/#domimplementation DOM Living Standard\n */\nfunction DOMImplementation() {\n}\n\nDOMImplementation.prototype = {\n\t/**\n\t * The DOMImplementation.hasFeature() method returns a Boolean flag indicating if a given feature is supported.\n\t * The different implementations fairly diverged in what kind of features were reported.\n\t * The latest version of the spec settled to force this method to always return true, where the functionality was accurate and in use.\n\t *\n\t * @deprecated It is deprecated and modern browsers return true in all cases.\n\t *\n\t * @param {string} feature\n\t * @param {string} [version]\n\t * @returns {boolean} always true\n\t *\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation/hasFeature MDN\n\t * @see https://www.w3.org/TR/REC-DOM-Level-1/level-one-core.html#ID-5CED94D7 DOM Level 1 Core\n\t * @see https://dom.spec.whatwg.org/#dom-domimplementation-hasfeature DOM Living Standard\n\t */\n\thasFeature: function(feature, version) {\n\t\t\treturn true;\n\t},\n\t/**\n\t * Creates an XML Document object of the specified type with its document element.\n\t *\n\t * __It behaves slightly different from the description in the living standard__:\n\t * - There is no interface/class `XMLDocument`, it returns a `Document` instance.\n\t * - `contentType`, `encoding`, `mode`, `origin`, `url` fields are currently not declared.\n\t * - this implementation is not validating names or qualified names\n\t *   (when parsing XML strings, the SAX parser takes care of that)\n\t *\n\t * @param {string|null} namespaceURI\n\t * @param {string} qualifiedName\n\t * @param {DocumentType=null} doctype\n\t * @returns {Document}\n\t *\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation/createDocument MDN\n\t * @see https://www.w3.org/TR/DOM-Level-2-Core/core.html#Level-2-Core-DOM-createDocument DOM Level 2 Core (initial)\n\t * @see https://dom.spec.whatwg.org/#dom-domimplementation-createdocument  DOM Level 2 Core\n\t *\n\t * @see https://dom.spec.whatwg.org/#validate-and-extract DOM: Validate and extract\n\t * @see https://www.w3.org/TR/xml/#NT-NameStartChar XML Spec: Names\n\t * @see https://www.w3.org/TR/xml-names/#ns-qualnames XML Namespaces: Qualified names\n\t */\n\tcreateDocument: function(namespaceURI,  qualifiedName, doctype){\n\t\tvar doc = new Document();\n\t\tdoc.implementation = this;\n\t\tdoc.childNodes = new NodeList();\n\t\tdoc.doctype = doctype || null;\n\t\tif (doctype){\n\t\t\tdoc.appendChild(doctype);\n\t\t}\n\t\tif (qualifiedName){\n\t\t\tvar root = doc.createElementNS(namespaceURI, qualifiedName);\n\t\t\tdoc.appendChild(root);\n\t\t}\n\t\treturn doc;\n\t},\n\t/**\n\t * Returns a doctype, with the given `qualifiedName`, `publicId`, and `systemId`.\n\t *\n\t * __This behavior is slightly different from the in the specs__:\n\t * - this implementation is not validating names or qualified names\n\t *   (when parsing XML strings, the SAX parser takes care of that)\n\t *\n\t * @param {string} qualifiedName\n\t * @param {string} [publicId]\n\t * @param {string} [systemId]\n\t * @returns {DocumentType} which can either be used with `DOMImplementation.createDocument` upon document creation\n\t * \t\t\t\t  or can be put into the document via methods like `Node.insertBefore()` or `Node.replaceChild()`\n\t *\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/DOMImplementation/createDocumentType MDN\n\t * @see https://www.w3.org/TR/DOM-Level-2-Core/core.html#Level-2-Core-DOM-createDocType DOM Level 2 Core\n\t * @see https://dom.spec.whatwg.org/#dom-domimplementation-createdocumenttype DOM Living Standard\n\t *\n\t * @see https://dom.spec.whatwg.org/#validate-and-extract DOM: Validate and extract\n\t * @see https://www.w3.org/TR/xml/#NT-NameStartChar XML Spec: Names\n\t * @see https://www.w3.org/TR/xml-names/#ns-qualnames XML Namespaces: Qualified names\n\t */\n\tcreateDocumentType: function(qualifiedName, publicId, systemId){\n\t\tvar node = new DocumentType();\n\t\tnode.name = qualifiedName;\n\t\tnode.nodeName = qualifiedName;\n\t\tnode.publicId = publicId || '';\n\t\tnode.systemId = systemId || '';\n\n\t\treturn node;\n\t}\n};\n\n\n/**\n * @see http://www.w3.org/TR/2000/REC-DOM-Level-2-Core-20001113/core.html#ID-1950641247\n */\n\nfunction Node() {\n};\n\nNode.prototype = {\n\tfirstChild : null,\n\tlastChild : null,\n\tpreviousSibling : null,\n\tnextSibling : null,\n\tattributes : null,\n\tparentNode : null,\n\tchildNodes : null,\n\townerDocument : null,\n\tnodeValue : null,\n\tnamespaceURI : null,\n\tprefix : null,\n\tlocalName : null,\n\t// Modified in DOM Level 2:\n\tinsertBefore:function(newChild, refChild){//raises\n\t\treturn _insertBefore(this,newChild,refChild);\n\t},\n\treplaceChild:function(newChild, oldChild){//raises\n\t\t_insertBefore(this, newChild,oldChild, assertPreReplacementValidityInDocument);\n\t\tif(oldChild){\n\t\t\tthis.removeChild(oldChild);\n\t\t}\n\t},\n\tremoveChild:function(oldChild){\n\t\treturn _removeChild(this,oldChild);\n\t},\n\tappendChild:function(newChild){\n\t\treturn this.insertBefore(newChild,null);\n\t},\n\thasChildNodes:function(){\n\t\treturn this.firstChild != null;\n\t},\n\tcloneNode:function(deep){\n\t\treturn cloneNode(this.ownerDocument||this,this,deep);\n\t},\n\t// Modified in DOM Level 2:\n\tnormalize:function(){\n\t\tvar child = this.firstChild;\n\t\twhile(child){\n\t\t\tvar next = child.nextSibling;\n\t\t\tif(next && next.nodeType == TEXT_NODE && child.nodeType == TEXT_NODE){\n\t\t\t\tthis.removeChild(next);\n\t\t\t\tchild.appendData(next.data);\n\t\t\t}else{\n\t\t\t\tchild.normalize();\n\t\t\t\tchild = next;\n\t\t\t}\n\t\t}\n\t},\n  \t// Introduced in DOM Level 2:\n\tisSupported:function(feature, version){\n\t\treturn this.ownerDocument.implementation.hasFeature(feature,version);\n\t},\n    // Introduced in DOM Level 2:\n    hasAttributes:function(){\n    \treturn this.attributes.length>0;\n    },\n\t/**\n\t * Look up the prefix associated to the given namespace URI, starting from this node.\n\t * **The default namespace declarations are ignored by this method.**\n\t * See Namespace Prefix Lookup for details on the algorithm used by this method.\n\t *\n\t * _Note: The implementation seems to be incomplete when compared to the algorithm described in the specs._\n\t *\n\t * @param {string | null} namespaceURI\n\t * @returns {string | null}\n\t * @see https://www.w3.org/TR/DOM-Level-3-Core/core.html#Node3-lookupNamespacePrefix\n\t * @see https://www.w3.org/TR/DOM-Level-3-Core/namespaces-algorithms.html#lookupNamespacePrefixAlgo\n\t * @see https://dom.spec.whatwg.org/#dom-node-lookupprefix\n\t * @see https://github.com/xmldom/xmldom/issues/322\n\t */\n    lookupPrefix:function(namespaceURI){\n    \tvar el = this;\n    \twhile(el){\n    \t\tvar map = el._nsMap;\n    \t\t//console.dir(map)\n    \t\tif(map){\n    \t\t\tfor(var n in map){\n\t\t\t\t\t\tif (Object.prototype.hasOwnProperty.call(map, n) && map[n] === namespaceURI) {\n\t\t\t\t\t\t\treturn n;\n\t\t\t\t\t\t}\n    \t\t\t}\n    \t\t}\n    \t\tel = el.nodeType == ATTRIBUTE_NODE?el.ownerDocument : el.parentNode;\n    \t}\n    \treturn null;\n    },\n    // Introduced in DOM Level 3:\n    lookupNamespaceURI:function(prefix){\n    \tvar el = this;\n    \twhile(el){\n    \t\tvar map = el._nsMap;\n    \t\t//console.dir(map)\n    \t\tif(map){\n    \t\t\tif(Object.prototype.hasOwnProperty.call(map, prefix)){\n    \t\t\t\treturn map[prefix] ;\n    \t\t\t}\n    \t\t}\n    \t\tel = el.nodeType == ATTRIBUTE_NODE?el.ownerDocument : el.parentNode;\n    \t}\n    \treturn null;\n    },\n    // Introduced in DOM Level 3:\n    isDefaultNamespace:function(namespaceURI){\n    \tvar prefix = this.lookupPrefix(namespaceURI);\n    \treturn prefix == null;\n    }\n};\n\n\nfunction _xmlEncoder(c){\n\treturn c == '<' && '&lt;' ||\n         c == '>' && '&gt;' ||\n         c == '&' && '&amp;' ||\n         c == '\"' && '&quot;' ||\n         '&#'+c.charCodeAt()+';'\n}\n\n\ncopy(NodeType,Node);\ncopy(NodeType,Node.prototype);\n\n/**\n * @param callback return true for continue,false for break\n * @return boolean true: break visit;\n */\nfunction _visitNode(node,callback){\n\tif(callback(node)){\n\t\treturn true;\n\t}\n\tif(node = node.firstChild){\n\t\tdo{\n\t\t\tif(_visitNode(node,callback)){return true}\n        }while(node=node.nextSibling)\n    }\n}\n\n\n\nfunction Document(){\n\tthis.ownerDocument = this;\n}\n\nfunction _onAddAttribute(doc,el,newAttr){\n\tdoc && doc._inc++;\n\tvar ns = newAttr.namespaceURI ;\n\tif(ns === NAMESPACE.XMLNS){\n\t\t//update namespace\n\t\tel._nsMap[newAttr.prefix?newAttr.localName:''] = newAttr.value\n\t}\n}\n\nfunction _onRemoveAttribute(doc,el,newAttr,remove){\n\tdoc && doc._inc++;\n\tvar ns = newAttr.namespaceURI ;\n\tif(ns === NAMESPACE.XMLNS){\n\t\t//update namespace\n\t\tdelete el._nsMap[newAttr.prefix?newAttr.localName:'']\n\t}\n}\n\n/**\n * Updates `el.childNodes`, updating the indexed items and it's `length`.\n * Passing `newChild` means it will be appended.\n * Otherwise it's assumed that an item has been removed,\n * and `el.firstNode` and it's `.nextSibling` are used\n * to walk the current list of child nodes.\n *\n * @param {Document} doc\n * @param {Node} el\n * @param {Node} [newChild]\n * @private\n */\nfunction _onUpdateChild (doc, el, newChild) {\n\tif(doc && doc._inc){\n\t\tdoc._inc++;\n\t\t//update childNodes\n\t\tvar cs = el.childNodes;\n\t\tif (newChild) {\n\t\t\tcs[cs.length++] = newChild;\n\t\t} else {\n\t\t\tvar child = el.firstChild;\n\t\t\tvar i = 0;\n\t\t\twhile (child) {\n\t\t\t\tcs[i++] = child;\n\t\t\t\tchild = child.nextSibling;\n\t\t\t}\n\t\t\tcs.length = i;\n\t\t\tdelete cs[cs.length];\n\t\t}\n\t}\n}\n\n/**\n * Removes the connections between `parentNode` and `child`\n * and any existing `child.previousSibling` or `child.nextSibling`.\n *\n * @see https://github.com/xmldom/xmldom/issues/135\n * @see https://github.com/xmldom/xmldom/issues/145\n *\n * @param {Node} parentNode\n * @param {Node} child\n * @returns {Node} the child that was removed.\n * @private\n */\nfunction _removeChild (parentNode, child) {\n\tvar previous = child.previousSibling;\n\tvar next = child.nextSibling;\n\tif (previous) {\n\t\tprevious.nextSibling = next;\n\t} else {\n\t\tparentNode.firstChild = next;\n\t}\n\tif (next) {\n\t\tnext.previousSibling = previous;\n\t} else {\n\t\tparentNode.lastChild = previous;\n\t}\n\tchild.parentNode = null;\n\tchild.previousSibling = null;\n\tchild.nextSibling = null;\n\t_onUpdateChild(parentNode.ownerDocument, parentNode);\n\treturn child;\n}\n\n/**\n * Returns `true` if `node` can be a parent for insertion.\n * @param {Node} node\n * @returns {boolean}\n */\nfunction hasValidParentNodeType(node) {\n\treturn (\n\t\tnode &&\n\t\t(node.nodeType === Node.DOCUMENT_NODE || node.nodeType === Node.DOCUMENT_FRAGMENT_NODE || node.nodeType === Node.ELEMENT_NODE)\n\t);\n}\n\n/**\n * Returns `true` if `node` can be inserted according to it's `nodeType`.\n * @param {Node} node\n * @returns {boolean}\n */\nfunction hasInsertableNodeType(node) {\n\treturn (\n\t\tnode &&\n\t\t(isElementNode(node) ||\n\t\t\tisTextNode(node) ||\n\t\t\tisDocTypeNode(node) ||\n\t\t\tnode.nodeType === Node.DOCUMENT_FRAGMENT_NODE ||\n\t\t\tnode.nodeType === Node.COMMENT_NODE ||\n\t\t\tnode.nodeType === Node.PROCESSING_INSTRUCTION_NODE)\n\t);\n}\n\n/**\n * Returns true if `node` is a DOCTYPE node\n * @param {Node} node\n * @returns {boolean}\n */\nfunction isDocTypeNode(node) {\n\treturn node && node.nodeType === Node.DOCUMENT_TYPE_NODE;\n}\n\n/**\n * Returns true if the node is an element\n * @param {Node} node\n * @returns {boolean}\n */\nfunction isElementNode(node) {\n\treturn node && node.nodeType === Node.ELEMENT_NODE;\n}\n/**\n * Returns true if `node` is a text node\n * @param {Node} node\n * @returns {boolean}\n */\nfunction isTextNode(node) {\n\treturn node && node.nodeType === Node.TEXT_NODE;\n}\n\n/**\n * Check if en element node can be inserted before `child`, or at the end if child is falsy,\n * according to the presence and position of a doctype node on the same level.\n *\n * @param {Document} doc The document node\n * @param {Node} child the node that would become the nextSibling if the element would be inserted\n * @returns {boolean} `true` if an element can be inserted before child\n * @private\n * https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n */\nfunction isElementInsertionPossible(doc, child) {\n\tvar parentChildNodes = doc.childNodes || [];\n\tif (find(parentChildNodes, isElementNode) || isDocTypeNode(child)) {\n\t\treturn false;\n\t}\n\tvar docTypeNode = find(parentChildNodes, isDocTypeNode);\n\treturn !(child && docTypeNode && parentChildNodes.indexOf(docTypeNode) > parentChildNodes.indexOf(child));\n}\n\n/**\n * Check if en element node can be inserted before `child`, or at the end if child is falsy,\n * according to the presence and position of a doctype node on the same level.\n *\n * @param {Node} doc The document node\n * @param {Node} child the node that would become the nextSibling if the element would be inserted\n * @returns {boolean} `true` if an element can be inserted before child\n * @private\n * https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n */\nfunction isElementReplacementPossible(doc, child) {\n\tvar parentChildNodes = doc.childNodes || [];\n\n\tfunction hasElementChildThatIsNotChild(node) {\n\t\treturn isElementNode(node) && node !== child;\n\t}\n\n\tif (find(parentChildNodes, hasElementChildThatIsNotChild)) {\n\t\treturn false;\n\t}\n\tvar docTypeNode = find(parentChildNodes, isDocTypeNode);\n\treturn !(child && docTypeNode && parentChildNodes.indexOf(docTypeNode) > parentChildNodes.indexOf(child));\n}\n\n/**\n * @private\n * Steps 1-5 of the checks before inserting and before replacing a child are the same.\n *\n * @param {Node} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node=} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n * @see https://dom.spec.whatwg.org/#concept-node-replace\n */\nfunction assertPreInsertionValidity1to5(parent, node, child) {\n\t// 1. If `parent` is not a Document, DocumentFragment, or Element node, then throw a \"HierarchyRequestError\" DOMException.\n\tif (!hasValidParentNodeType(parent)) {\n\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Unexpected parent node type ' + parent.nodeType);\n\t}\n\t// 2. If `node` is a host-including inclusive ancestor of `parent`, then throw a \"HierarchyRequestError\" DOMException.\n\t// not implemented!\n\t// 3. If `child` is non-null and its parent is not `parent`, then throw a \"NotFoundError\" DOMException.\n\tif (child && child.parentNode !== parent) {\n\t\tthrow new DOMException(NOT_FOUND_ERR, 'child not in parent');\n\t}\n\tif (\n\t\t// 4. If `node` is not a DocumentFragment, DocumentType, Element, or CharacterData node, then throw a \"HierarchyRequestError\" DOMException.\n\t\t!hasInsertableNodeType(node) ||\n\t\t// 5. If either `node` is a Text node and `parent` is a document,\n\t\t// the sax parser currently adds top level text nodes, this will be fixed in 0.9.0\n\t\t// || (node.nodeType === Node.TEXT_NODE && parent.nodeType === Node.DOCUMENT_NODE)\n\t\t// or `node` is a doctype and `parent` is not a document, then throw a \"HierarchyRequestError\" DOMException.\n\t\t(isDocTypeNode(node) && parent.nodeType !== Node.DOCUMENT_NODE)\n\t) {\n\t\tthrow new DOMException(\n\t\t\tHIERARCHY_REQUEST_ERR,\n\t\t\t'Unexpected node type ' + node.nodeType + ' for parent node type ' + parent.nodeType\n\t\t);\n\t}\n}\n\n/**\n * @private\n * Step 6 of the checks before inserting and before replacing a child are different.\n *\n * @param {Document} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node | undefined} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n * @see https://dom.spec.whatwg.org/#concept-node-replace\n */\nfunction assertPreInsertionValidityInDocument(parent, node, child) {\n\tvar parentChildNodes = parent.childNodes || [];\n\tvar nodeChildNodes = node.childNodes || [];\n\n\t// DocumentFragment\n\tif (node.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {\n\t\tvar nodeChildElements = nodeChildNodes.filter(isElementNode);\n\t\t// If node has more than one element child or has a Text node child.\n\t\tif (nodeChildElements.length > 1 || find(nodeChildNodes, isTextNode)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'More than one element or text in fragment');\n\t\t}\n\t\t// Otherwise, if `node` has one element child and either `parent` has an element child,\n\t\t// `child` is a doctype, or `child` is non-null and a doctype is following `child`.\n\t\tif (nodeChildElements.length === 1 && !isElementInsertionPossible(parent, child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Element in fragment can not be inserted before doctype');\n\t\t}\n\t}\n\t// Element\n\tif (isElementNode(node)) {\n\t\t// `parent` has an element child, `child` is a doctype,\n\t\t// or `child` is non-null and a doctype is following `child`.\n\t\tif (!isElementInsertionPossible(parent, child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Only one element can be added and only after doctype');\n\t\t}\n\t}\n\t// DocumentType\n\tif (isDocTypeNode(node)) {\n\t\t// `parent` has a doctype child,\n\t\tif (find(parentChildNodes, isDocTypeNode)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Only one doctype is allowed');\n\t\t}\n\t\tvar parentElementChild = find(parentChildNodes, isElementNode);\n\t\t// `child` is non-null and an element is preceding `child`,\n\t\tif (child && parentChildNodes.indexOf(parentElementChild) < parentChildNodes.indexOf(child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Doctype can only be inserted before an element');\n\t\t}\n\t\t// or `child` is null and `parent` has an element child.\n\t\tif (!child && parentElementChild) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Doctype can not be appended since element is present');\n\t\t}\n\t}\n}\n\n/**\n * @private\n * Step 6 of the checks before inserting and before replacing a child are different.\n *\n * @param {Document} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node | undefined} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n * @see https://dom.spec.whatwg.org/#concept-node-replace\n */\nfunction assertPreReplacementValidityInDocument(parent, node, child) {\n\tvar parentChildNodes = parent.childNodes || [];\n\tvar nodeChildNodes = node.childNodes || [];\n\n\t// DocumentFragment\n\tif (node.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {\n\t\tvar nodeChildElements = nodeChildNodes.filter(isElementNode);\n\t\t// If `node` has more than one element child or has a Text node child.\n\t\tif (nodeChildElements.length > 1 || find(nodeChildNodes, isTextNode)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'More than one element or text in fragment');\n\t\t}\n\t\t// Otherwise, if `node` has one element child and either `parent` has an element child that is not `child` or a doctype is following `child`.\n\t\tif (nodeChildElements.length === 1 && !isElementReplacementPossible(parent, child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Element in fragment can not be inserted before doctype');\n\t\t}\n\t}\n\t// Element\n\tif (isElementNode(node)) {\n\t\t// `parent` has an element child that is not `child` or a doctype is following `child`.\n\t\tif (!isElementReplacementPossible(parent, child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Only one element can be added and only after doctype');\n\t\t}\n\t}\n\t// DocumentType\n\tif (isDocTypeNode(node)) {\n\t\tfunction hasDoctypeChildThatIsNotChild(node) {\n\t\t\treturn isDocTypeNode(node) && node !== child;\n\t\t}\n\n\t\t// `parent` has a doctype child that is not `child`,\n\t\tif (find(parentChildNodes, hasDoctypeChildThatIsNotChild)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Only one doctype is allowed');\n\t\t}\n\t\tvar parentElementChild = find(parentChildNodes, isElementNode);\n\t\t// or an element is preceding `child`.\n\t\tif (child && parentChildNodes.indexOf(parentElementChild) < parentChildNodes.indexOf(child)) {\n\t\t\tthrow new DOMException(HIERARCHY_REQUEST_ERR, 'Doctype can only be inserted before an element');\n\t\t}\n\t}\n}\n\n/**\n * @private\n * @param {Node} parent the parent node to insert `node` into\n * @param {Node} node the node to insert\n * @param {Node=} child the node that should become the `nextSibling` of `node`\n * @returns {Node}\n * @throws DOMException for several node combinations that would create a DOM that is not well-formed.\n * @throws DOMException if `child` is provided but is not a child of `parent`.\n * @see https://dom.spec.whatwg.org/#concept-node-ensure-pre-insertion-validity\n */\nfunction _insertBefore(parent, node, child, _inDocumentAssertion) {\n\t// To ensure pre-insertion validity of a node into a parent before a child, run these steps:\n\tassertPreInsertionValidity1to5(parent, node, child);\n\n\t// If parent is a document, and any of the statements below, switched on the interface node implements,\n\t// are true, then throw a \"HierarchyRequestError\" DOMException.\n\tif (parent.nodeType === Node.DOCUMENT_NODE) {\n\t\t(_inDocumentAssertion || assertPreInsertionValidityInDocument)(parent, node, child);\n\t}\n\n\tvar cp = node.parentNode;\n\tif(cp){\n\t\tcp.removeChild(node);//remove and update\n\t}\n\tif(node.nodeType === DOCUMENT_FRAGMENT_NODE){\n\t\tvar newFirst = node.firstChild;\n\t\tif (newFirst == null) {\n\t\t\treturn node;\n\t\t}\n\t\tvar newLast = node.lastChild;\n\t}else{\n\t\tnewFirst = newLast = node;\n\t}\n\tvar pre = child ? child.previousSibling : parent.lastChild;\n\n\tnewFirst.previousSibling = pre;\n\tnewLast.nextSibling = child;\n\n\n\tif(pre){\n\t\tpre.nextSibling = newFirst;\n\t}else{\n\t\tparent.firstChild = newFirst;\n\t}\n\tif(child == null){\n\t\tparent.lastChild = newLast;\n\t}else{\n\t\tchild.previousSibling = newLast;\n\t}\n\tdo{\n\t\tnewFirst.parentNode = parent;\n\t}while(newFirst !== newLast && (newFirst= newFirst.nextSibling))\n\t_onUpdateChild(parent.ownerDocument||parent, parent);\n\t//console.log(parent.lastChild.nextSibling == null)\n\tif (node.nodeType == DOCUMENT_FRAGMENT_NODE) {\n\t\tnode.firstChild = node.lastChild = null;\n\t}\n\treturn node;\n}\n\n/**\n * Appends `newChild` to `parentNode`.\n * If `newChild` is already connected to a `parentNode` it is first removed from it.\n *\n * @see https://github.com/xmldom/xmldom/issues/135\n * @see https://github.com/xmldom/xmldom/issues/145\n * @param {Node} parentNode\n * @param {Node} newChild\n * @returns {Node}\n * @private\n */\nfunction _appendSingleChild (parentNode, newChild) {\n\tif (newChild.parentNode) {\n\t\tnewChild.parentNode.removeChild(newChild);\n\t}\n\tnewChild.parentNode = parentNode;\n\tnewChild.previousSibling = parentNode.lastChild;\n\tnewChild.nextSibling = null;\n\tif (newChild.previousSibling) {\n\t\tnewChild.previousSibling.nextSibling = newChild;\n\t} else {\n\t\tparentNode.firstChild = newChild;\n\t}\n\tparentNode.lastChild = newChild;\n\t_onUpdateChild(parentNode.ownerDocument, parentNode, newChild);\n\treturn newChild;\n}\n\nDocument.prototype = {\n\t//implementation : null,\n\tnodeName :  '#document',\n\tnodeType :  DOCUMENT_NODE,\n\t/**\n\t * The DocumentType node of the document.\n\t *\n\t * @readonly\n\t * @type DocumentType\n\t */\n\tdoctype :  null,\n\tdocumentElement :  null,\n\t_inc : 1,\n\n\tinsertBefore :  function(newChild, refChild){//raises\n\t\tif(newChild.nodeType == DOCUMENT_FRAGMENT_NODE){\n\t\t\tvar child = newChild.firstChild;\n\t\t\twhile(child){\n\t\t\t\tvar next = child.nextSibling;\n\t\t\t\tthis.insertBefore(child,refChild);\n\t\t\t\tchild = next;\n\t\t\t}\n\t\t\treturn newChild;\n\t\t}\n\t\t_insertBefore(this, newChild, refChild);\n\t\tnewChild.ownerDocument = this;\n\t\tif (this.documentElement === null && newChild.nodeType === ELEMENT_NODE) {\n\t\t\tthis.documentElement = newChild;\n\t\t}\n\n\t\treturn newChild;\n\t},\n\tremoveChild :  function(oldChild){\n\t\tif(this.documentElement == oldChild){\n\t\t\tthis.documentElement = null;\n\t\t}\n\t\treturn _removeChild(this,oldChild);\n\t},\n\treplaceChild: function (newChild, oldChild) {\n\t\t//raises\n\t\t_insertBefore(this, newChild, oldChild, assertPreReplacementValidityInDocument);\n\t\tnewChild.ownerDocument = this;\n\t\tif (oldChild) {\n\t\t\tthis.removeChild(oldChild);\n\t\t}\n\t\tif (isElementNode(newChild)) {\n\t\t\tthis.documentElement = newChild;\n\t\t}\n\t},\n\t// Introduced in DOM Level 2:\n\timportNode : function(importedNode,deep){\n\t\treturn importNode(this,importedNode,deep);\n\t},\n\t// Introduced in DOM Level 2:\n\tgetElementById :\tfunction(id){\n\t\tvar rtv = null;\n\t\t_visitNode(this.documentElement,function(node){\n\t\t\tif(node.nodeType == ELEMENT_NODE){\n\t\t\t\tif(node.getAttribute('id') == id){\n\t\t\t\t\trtv = node;\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\t\treturn rtv;\n\t},\n\n\t/**\n\t * The `getElementsByClassName` method of `Document` interface returns an array-like object\n\t * of all child elements which have **all** of the given class name(s).\n\t *\n\t * Returns an empty list if `classeNames` is an empty string or only contains HTML white space characters.\n\t *\n\t *\n\t * Warning: This is a live LiveNodeList.\n\t * Changes in the DOM will reflect in the array as the changes occur.\n\t * If an element selected by this array no longer qualifies for the selector,\n\t * it will automatically be removed. Be aware of this for iteration purposes.\n\t *\n\t * @param {string} classNames is a string representing the class name(s) to match; multiple class names are separated by (ASCII-)whitespace\n\t *\n\t * @see https://developer.mozilla.org/en-US/docs/Web/API/Document/getElementsByClassName\n\t * @see https://dom.spec.whatwg.org/#concept-getelementsbyclassname\n\t */\n\tgetElementsByClassName: function(classNames) {\n\t\tvar classNamesSet = toOrderedSet(classNames)\n\t\treturn new LiveNodeList(this, function(base) {\n\t\t\tvar ls = [];\n\t\t\tif (classNamesSet.length > 0) {\n\t\t\t\t_visitNode(base.documentElement, function(node) {\n\t\t\t\t\tif(node !== base && node.nodeType === ELEMENT_NODE) {\n\t\t\t\t\t\tvar nodeClassNames = node.getAttribute('class')\n\t\t\t\t\t\t// can be null if the attribute does not exist\n\t\t\t\t\t\tif (nodeClassNames) {\n\t\t\t\t\t\t\t// before splitting and iterating just compare them for the most common case\n\t\t\t\t\t\t\tvar matches = classNames === nodeClassNames;\n\t\t\t\t\t\t\tif (!matches) {\n\t\t\t\t\t\t\t\tvar nodeClassNamesSet = toOrderedSet(nodeClassNames)\n\t\t\t\t\t\t\t\tmatches = classNamesSet.every(arrayIncludes(nodeClassNamesSet))\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tif(matches) {\n\t\t\t\t\t\t\t\tls.push(node);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t}\n\t\t\treturn ls;\n\t\t});\n\t},\n\n\t//document factory method:\n\tcreateElement :\tfunction(tagName){\n\t\tvar node = new Element();\n\t\tnode.ownerDocument = this;\n\t\tnode.nodeName = tagName;\n\t\tnode.tagName = tagName;\n\t\tnode.localName = tagName;\n\t\tnode.childNodes = new NodeList();\n\t\tvar attrs\t= node.attributes = new NamedNodeMap();\n\t\tattrs._ownerElement = node;\n\t\treturn node;\n\t},\n\tcreateDocumentFragment :\tfunction(){\n\t\tvar node = new DocumentFragment();\n\t\tnode.ownerDocument = this;\n\t\tnode.childNodes = new NodeList();\n\t\treturn node;\n\t},\n\tcreateTextNode :\tfunction(data){\n\t\tvar node = new Text();\n\t\tnode.ownerDocument = this;\n\t\tnode.appendData(data)\n\t\treturn node;\n\t},\n\tcreateComment :\tfunction(data){\n\t\tvar node = new Comment();\n\t\tnode.ownerDocument = this;\n\t\tnode.appendData(data)\n\t\treturn node;\n\t},\n\tcreateCDATASection :\tfunction(data){\n\t\tvar node = new CDATASection();\n\t\tnode.ownerDocument = this;\n\t\tnode.appendData(data)\n\t\treturn node;\n\t},\n\tcreateProcessingInstruction :\tfunction(target,data){\n\t\tvar node = new ProcessingInstruction();\n\t\tnode.ownerDocument = this;\n\t\tnode.tagName = node.nodeName = node.target = target;\n\t\tnode.nodeValue = node.data = data;\n\t\treturn node;\n\t},\n\tcreateAttribute :\tfunction(name){\n\t\tvar node = new Attr();\n\t\tnode.ownerDocument\t= this;\n\t\tnode.name = name;\n\t\tnode.nodeName\t= name;\n\t\tnode.localName = name;\n\t\tnode.specified = true;\n\t\treturn node;\n\t},\n\tcreateEntityReference :\tfunction(name){\n\t\tvar node = new EntityReference();\n\t\tnode.ownerDocument\t= this;\n\t\tnode.nodeName\t= name;\n\t\treturn node;\n\t},\n\t// Introduced in DOM Level 2:\n\tcreateElementNS :\tfunction(namespaceURI,qualifiedName){\n\t\tvar node = new Element();\n\t\tvar pl = qualifiedName.split(':');\n\t\tvar attrs\t= node.attributes = new NamedNodeMap();\n\t\tnode.childNodes = new NodeList();\n\t\tnode.ownerDocument = this;\n\t\tnode.nodeName = qualifiedName;\n\t\tnode.tagName = qualifiedName;\n\t\tnode.namespaceURI = namespaceURI;\n\t\tif(pl.length == 2){\n\t\t\tnode.prefix = pl[0];\n\t\t\tnode.localName = pl[1];\n\t\t}else{\n\t\t\t//el.prefix = null;\n\t\t\tnode.localName = qualifiedName;\n\t\t}\n\t\tattrs._ownerElement = node;\n\t\treturn node;\n\t},\n\t// Introduced in DOM Level 2:\n\tcreateAttributeNS :\tfunction(namespaceURI,qualifiedName){\n\t\tvar node = new Attr();\n\t\tvar pl = qualifiedName.split(':');\n\t\tnode.ownerDocument = this;\n\t\tnode.nodeName = qualifiedName;\n\t\tnode.name = qualifiedName;\n\t\tnode.namespaceURI = namespaceURI;\n\t\tnode.specified = true;\n\t\tif(pl.length == 2){\n\t\t\tnode.prefix = pl[0];\n\t\t\tnode.localName = pl[1];\n\t\t}else{\n\t\t\t//el.prefix = null;\n\t\t\tnode.localName = qualifiedName;\n\t\t}\n\t\treturn node;\n\t}\n};\n_extends(Document,Node);\n\n\nfunction Element() {\n\tthis._nsMap = {};\n};\nElement.prototype = {\n\tnodeType : ELEMENT_NODE,\n\thasAttribute : function(name){\n\t\treturn this.getAttributeNode(name)!=null;\n\t},\n\tgetAttribute : function(name){\n\t\tvar attr = this.getAttributeNode(name);\n\t\treturn attr && attr.value || '';\n\t},\n\tgetAttributeNode : function(name){\n\t\treturn this.attributes.getNamedItem(name);\n\t},\n\tsetAttribute : function(name, value){\n\t\tvar attr = this.ownerDocument.createAttribute(name);\n\t\tattr.value = attr.nodeValue = \"\" + value;\n\t\tthis.setAttributeNode(attr)\n\t},\n\tremoveAttribute : function(name){\n\t\tvar attr = this.getAttributeNode(name)\n\t\tattr && this.removeAttributeNode(attr);\n\t},\n\n\t//four real opeartion method\n\tappendChild:function(newChild){\n\t\tif(newChild.nodeType === DOCUMENT_FRAGMENT_NODE){\n\t\t\treturn this.insertBefore(newChild,null);\n\t\t}else{\n\t\t\treturn _appendSingleChild(this,newChild);\n\t\t}\n\t},\n\tsetAttributeNode : function(newAttr){\n\t\treturn this.attributes.setNamedItem(newAttr);\n\t},\n\tsetAttributeNodeNS : function(newAttr){\n\t\treturn this.attributes.setNamedItemNS(newAttr);\n\t},\n\tremoveAttributeNode : function(oldAttr){\n\t\t//console.log(this == oldAttr.ownerElement)\n\t\treturn this.attributes.removeNamedItem(oldAttr.nodeName);\n\t},\n\t//get real attribute name,and remove it by removeAttributeNode\n\tremoveAttributeNS : function(namespaceURI, localName){\n\t\tvar old = this.getAttributeNodeNS(namespaceURI, localName);\n\t\told && this.removeAttributeNode(old);\n\t},\n\n\thasAttributeNS : function(namespaceURI, localName){\n\t\treturn this.getAttributeNodeNS(namespaceURI, localName)!=null;\n\t},\n\tgetAttributeNS : function(namespaceURI, localName){\n\t\tvar attr = this.getAttributeNodeNS(namespaceURI, localName);\n\t\treturn attr && attr.value || '';\n\t},\n\tsetAttributeNS : function(namespaceURI, qualifiedName, value){\n\t\tvar attr = this.ownerDocument.createAttributeNS(namespaceURI, qualifiedName);\n\t\tattr.value = attr.nodeValue = \"\" + value;\n\t\tthis.setAttributeNode(attr)\n\t},\n\tgetAttributeNodeNS : function(namespaceURI, localName){\n\t\treturn this.attributes.getNamedItemNS(namespaceURI, localName);\n\t},\n\n\tgetElementsByTagName : function(tagName){\n\t\treturn new LiveNodeList(this,function(base){\n\t\t\tvar ls = [];\n\t\t\t_visitNode(base,function(node){\n\t\t\t\tif(node !== base && node.nodeType == ELEMENT_NODE && (tagName === '*' || node.tagName == tagName)){\n\t\t\t\t\tls.push(node);\n\t\t\t\t}\n\t\t\t});\n\t\t\treturn ls;\n\t\t});\n\t},\n\tgetElementsByTagNameNS : function(namespaceURI, localName){\n\t\treturn new LiveNodeList(this,function(base){\n\t\t\tvar ls = [];\n\t\t\t_visitNode(base,function(node){\n\t\t\t\tif(node !== base && node.nodeType === ELEMENT_NODE && (namespaceURI === '*' || node.namespaceURI === namespaceURI) && (localName === '*' || node.localName == localName)){\n\t\t\t\t\tls.push(node);\n\t\t\t\t}\n\t\t\t});\n\t\t\treturn ls;\n\n\t\t});\n\t}\n};\nDocument.prototype.getElementsByTagName = Element.prototype.getElementsByTagName;\nDocument.prototype.getElementsByTagNameNS = Element.prototype.getElementsByTagNameNS;\n\n\n_extends(Element,Node);\nfunction Attr() {\n};\nAttr.prototype.nodeType = ATTRIBUTE_NODE;\n_extends(Attr,Node);\n\n\nfunction CharacterData() {\n};\nCharacterData.prototype = {\n\tdata : '',\n\tsubstringData : function(offset, count) {\n\t\treturn this.data.substring(offset, offset+count);\n\t},\n\tappendData: function(text) {\n\t\ttext = this.data+text;\n\t\tthis.nodeValue = this.data = text;\n\t\tthis.length = text.length;\n\t},\n\tinsertData: function(offset,text) {\n\t\tthis.replaceData(offset,0,text);\n\n\t},\n\tappendChild:function(newChild){\n\t\tthrow new Error(ExceptionMessage[HIERARCHY_REQUEST_ERR])\n\t},\n\tdeleteData: function(offset, count) {\n\t\tthis.replaceData(offset,count,\"\");\n\t},\n\treplaceData: function(offset, count, text) {\n\t\tvar start = this.data.substring(0,offset);\n\t\tvar end = this.data.substring(offset+count);\n\t\ttext = start + text + end;\n\t\tthis.nodeValue = this.data = text;\n\t\tthis.length = text.length;\n\t}\n}\n_extends(CharacterData,Node);\nfunction Text() {\n};\nText.prototype = {\n\tnodeName : \"#text\",\n\tnodeType : TEXT_NODE,\n\tsplitText : function(offset) {\n\t\tvar text = this.data;\n\t\tvar newText = text.substring(offset);\n\t\ttext = text.substring(0, offset);\n\t\tthis.data = this.nodeValue = text;\n\t\tthis.length = text.length;\n\t\tvar newNode = this.ownerDocument.createTextNode(newText);\n\t\tif(this.parentNode){\n\t\t\tthis.parentNode.insertBefore(newNode, this.nextSibling);\n\t\t}\n\t\treturn newNode;\n\t}\n}\n_extends(Text,CharacterData);\nfunction Comment() {\n};\nComment.prototype = {\n\tnodeName : \"#comment\",\n\tnodeType : COMMENT_NODE\n}\n_extends(Comment,CharacterData);\n\nfunction CDATASection() {\n};\nCDATASection.prototype = {\n\tnodeName : \"#cdata-section\",\n\tnodeType : CDATA_SECTION_NODE\n}\n_extends(CDATASection,CharacterData);\n\n\nfunction DocumentType() {\n};\nDocumentType.prototype.nodeType = DOCUMENT_TYPE_NODE;\n_extends(DocumentType,Node);\n\nfunction Notation() {\n};\nNotation.prototype.nodeType = NOTATION_NODE;\n_extends(Notation,Node);\n\nfunction Entity() {\n};\nEntity.prototype.nodeType = ENTITY_NODE;\n_extends(Entity,Node);\n\nfunction EntityReference() {\n};\nEntityReference.prototype.nodeType = ENTITY_REFERENCE_NODE;\n_extends(EntityReference,Node);\n\nfunction DocumentFragment() {\n};\nDocumentFragment.prototype.nodeName =\t\"#document-fragment\";\nDocumentFragment.prototype.nodeType =\tDOCUMENT_FRAGMENT_NODE;\n_extends(DocumentFragment,Node);\n\n\nfunction ProcessingInstruction() {\n}\nProcessingInstruction.prototype.nodeType = PROCESSING_INSTRUCTION_NODE;\n_extends(ProcessingInstruction,Node);\nfunction XMLSerializer(){}\nXMLSerializer.prototype.serializeToString = function(node,isHtml,nodeFilter){\n\treturn nodeSerializeToString.call(node,isHtml,nodeFilter);\n}\nNode.prototype.toString = nodeSerializeToString;\nfunction nodeSerializeToString(isHtml,nodeFilter){\n\tvar buf = [];\n\tvar refNode = this.nodeType == 9 && this.documentElement || this;\n\tvar prefix = refNode.prefix;\n\tvar uri = refNode.namespaceURI;\n\n\tif(uri && prefix == null){\n\t\t//console.log(prefix)\n\t\tvar prefix = refNode.lookupPrefix(uri);\n\t\tif(prefix == null){\n\t\t\t//isHTML = true;\n\t\t\tvar visibleNamespaces=[\n\t\t\t{namespace:uri,prefix:null}\n\t\t\t//{namespace:uri,prefix:''}\n\t\t\t]\n\t\t}\n\t}\n\tserializeToString(this,buf,isHtml,nodeFilter,visibleNamespaces);\n\t//console.log('###',this.nodeType,uri,prefix,buf.join(''))\n\treturn buf.join('');\n}\n\nfunction needNamespaceDefine(node, isHTML, visibleNamespaces) {\n\tvar prefix = node.prefix || '';\n\tvar uri = node.namespaceURI;\n\t// According to [Namespaces in XML 1.0](https://www.w3.org/TR/REC-xml-names/#ns-using) ,\n\t// and more specifically https://www.w3.org/TR/REC-xml-names/#nsc-NoPrefixUndecl :\n\t// > In a namespace declaration for a prefix [...], the attribute value MUST NOT be empty.\n\t// in a similar manner [Namespaces in XML 1.1](https://www.w3.org/TR/xml-names11/#ns-using)\n\t// and more specifically https://www.w3.org/TR/xml-names11/#nsc-NSDeclared :\n\t// > [...] Furthermore, the attribute value [...] must not be an empty string.\n\t// so serializing empty namespace value like xmlns:ds=\"\" would produce an invalid XML document.\n\tif (!uri) {\n\t\treturn false;\n\t}\n\tif (prefix === \"xml\" && uri === NAMESPACE.XML || uri === NAMESPACE.XMLNS) {\n\t\treturn false;\n\t}\n\n\tvar i = visibleNamespaces.length\n\twhile (i--) {\n\t\tvar ns = visibleNamespaces[i];\n\t\t// get namespace prefix\n\t\tif (ns.prefix === prefix) {\n\t\t\treturn ns.namespace !== uri;\n\t\t}\n\t}\n\treturn true;\n}\n/**\n * Well-formed constraint: No < in Attribute Values\n * > The replacement text of any entity referred to directly or indirectly\n * > in an attribute value must not contain a <.\n * @see https://www.w3.org/TR/xml11/#CleanAttrVals\n * @see https://www.w3.org/TR/xml11/#NT-AttValue\n *\n * Literal whitespace other than space that appear in attribute values\n * are serialized as their entity references, so they will be preserved.\n * (In contrast to whitespace literals in the input which are normalized to spaces)\n * @see https://www.w3.org/TR/xml11/#AVNormalize\n * @see https://w3c.github.io/DOM-Parsing/#serializing-an-element-s-attributes\n */\nfunction addSerializedAttribute(buf, qualifiedName, value) {\n\tbuf.push(' ', qualifiedName, '=\"', value.replace(/[<>&\"\\t\\n\\r]/g, _xmlEncoder), '\"')\n}\n\nfunction serializeToString(node,buf,isHTML,nodeFilter,visibleNamespaces){\n\tif (!visibleNamespaces) {\n\t\tvisibleNamespaces = [];\n\t}\n\n\tif(nodeFilter){\n\t\tnode = nodeFilter(node);\n\t\tif(node){\n\t\t\tif(typeof node == 'string'){\n\t\t\t\tbuf.push(node);\n\t\t\t\treturn;\n\t\t\t}\n\t\t}else{\n\t\t\treturn;\n\t\t}\n\t\t//buf.sort.apply(attrs, attributeSorter);\n\t}\n\n\tswitch(node.nodeType){\n\tcase ELEMENT_NODE:\n\t\tvar attrs = node.attributes;\n\t\tvar len = attrs.length;\n\t\tvar child = node.firstChild;\n\t\tvar nodeName = node.tagName;\n\n\t\tisHTML = NAMESPACE.isHTML(node.namespaceURI) || isHTML\n\n\t\tvar prefixedNodeName = nodeName\n\t\tif (!isHTML && !node.prefix && node.namespaceURI) {\n\t\t\tvar defaultNS\n\t\t\t// lookup current default ns from `xmlns` attribute\n\t\t\tfor (var ai = 0; ai < attrs.length; ai++) {\n\t\t\t\tif (attrs.item(ai).name === 'xmlns') {\n\t\t\t\t\tdefaultNS = attrs.item(ai).value\n\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (!defaultNS) {\n\t\t\t\t// lookup current default ns in visibleNamespaces\n\t\t\t\tfor (var nsi = visibleNamespaces.length - 1; nsi >= 0; nsi--) {\n\t\t\t\t\tvar namespace = visibleNamespaces[nsi]\n\t\t\t\t\tif (namespace.prefix === '' && namespace.namespace === node.namespaceURI) {\n\t\t\t\t\t\tdefaultNS = namespace.namespace\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\tif (defaultNS !== node.namespaceURI) {\n\t\t\t\tfor (var nsi = visibleNamespaces.length - 1; nsi >= 0; nsi--) {\n\t\t\t\t\tvar namespace = visibleNamespaces[nsi]\n\t\t\t\t\tif (namespace.namespace === node.namespaceURI) {\n\t\t\t\t\t\tif (namespace.prefix) {\n\t\t\t\t\t\t\tprefixedNodeName = namespace.prefix + ':' + nodeName\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tbuf.push('<', prefixedNodeName);\n\n\t\tfor(var i=0;i<len;i++){\n\t\t\t// add namespaces for attributes\n\t\t\tvar attr = attrs.item(i);\n\t\t\tif (attr.prefix == 'xmlns') {\n\t\t\t\tvisibleNamespaces.push({ prefix: attr.localName, namespace: attr.value });\n\t\t\t}else if(attr.nodeName == 'xmlns'){\n\t\t\t\tvisibleNamespaces.push({ prefix: '', namespace: attr.value });\n\t\t\t}\n\t\t}\n\n\t\tfor(var i=0;i<len;i++){\n\t\t\tvar attr = attrs.item(i);\n\t\t\tif (needNamespaceDefine(attr,isHTML, visibleNamespaces)) {\n\t\t\t\tvar prefix = attr.prefix||'';\n\t\t\t\tvar uri = attr.namespaceURI;\n\t\t\t\taddSerializedAttribute(buf, prefix ? 'xmlns:' + prefix : \"xmlns\", uri);\n\t\t\t\tvisibleNamespaces.push({ prefix: prefix, namespace:uri });\n\t\t\t}\n\t\t\tserializeToString(attr,buf,isHTML,nodeFilter,visibleNamespaces);\n\t\t}\n\n\t\t// add namespace for current node\n\t\tif (nodeName === prefixedNodeName && needNamespaceDefine(node, isHTML, visibleNamespaces)) {\n\t\t\tvar prefix = node.prefix||'';\n\t\t\tvar uri = node.namespaceURI;\n\t\t\taddSerializedAttribute(buf, prefix ? 'xmlns:' + prefix : \"xmlns\", uri);\n\t\t\tvisibleNamespaces.push({ prefix: prefix, namespace:uri });\n\t\t}\n\n\t\tif(child || isHTML && !/^(?:meta|link|img|br|hr|input)$/i.test(nodeName)){\n\t\t\tbuf.push('>');\n\t\t\t//if is cdata child node\n\t\t\tif(isHTML && /^script$/i.test(nodeName)){\n\t\t\t\twhile(child){\n\t\t\t\t\tif(child.data){\n\t\t\t\t\t\tbuf.push(child.data);\n\t\t\t\t\t}else{\n\t\t\t\t\t\tserializeToString(child, buf, isHTML, nodeFilter, visibleNamespaces.slice());\n\t\t\t\t\t}\n\t\t\t\t\tchild = child.nextSibling;\n\t\t\t\t}\n\t\t\t}else\n\t\t\t{\n\t\t\t\twhile(child){\n\t\t\t\t\tserializeToString(child, buf, isHTML, nodeFilter, visibleNamespaces.slice());\n\t\t\t\t\tchild = child.nextSibling;\n\t\t\t\t}\n\t\t\t}\n\t\t\tbuf.push('</',prefixedNodeName,'>');\n\t\t}else{\n\t\t\tbuf.push('/>');\n\t\t}\n\t\t// remove added visible namespaces\n\t\t//visibleNamespaces.length = startVisibleNamespaces;\n\t\treturn;\n\tcase DOCUMENT_NODE:\n\tcase DOCUMENT_FRAGMENT_NODE:\n\t\tvar child = node.firstChild;\n\t\twhile(child){\n\t\t\tserializeToString(child, buf, isHTML, nodeFilter, visibleNamespaces.slice());\n\t\t\tchild = child.nextSibling;\n\t\t}\n\t\treturn;\n\tcase ATTRIBUTE_NODE:\n\t\treturn addSerializedAttribute(buf, node.name, node.value);\n\tcase TEXT_NODE:\n\t\t/**\n\t\t * The ampersand character (&) and the left angle bracket (<) must not appear in their literal form,\n\t\t * except when used as markup delimiters, or within a comment, a processing instruction, or a CDATA section.\n\t\t * If they are needed elsewhere, they must be escaped using either numeric character references or the strings\n\t\t * `&amp;` and `&lt;` respectively.\n\t\t * The right angle bracket (>) may be represented using the string \" &gt; \", and must, for compatibility,\n\t\t * be escaped using either `&gt;` or a character reference when it appears in the string `]]>` in content,\n\t\t * when that string is not marking the end of a CDATA section.\n\t\t *\n\t\t * In the content of elements, character data is any string of characters\n\t\t * which does not contain the start-delimiter of any markup\n\t\t * and does not include the CDATA-section-close delimiter, `]]>`.\n\t\t *\n\t\t * @see https://www.w3.org/TR/xml/#NT-CharData\n\t\t * @see https://w3c.github.io/DOM-Parsing/#xml-serializing-a-text-node\n\t\t */\n\t\treturn buf.push(node.data\n\t\t\t.replace(/[<&>]/g,_xmlEncoder)\n\t\t);\n\tcase CDATA_SECTION_NODE:\n\t\treturn buf.push( '<![CDATA[',node.data,']]>');\n\tcase COMMENT_NODE:\n\t\treturn buf.push( \"<!--\",node.data,\"-->\");\n\tcase DOCUMENT_TYPE_NODE:\n\t\tvar pubid = node.publicId;\n\t\tvar sysid = node.systemId;\n\t\tbuf.push('<!DOCTYPE ',node.name);\n\t\tif(pubid){\n\t\t\tbuf.push(' PUBLIC ', pubid);\n\t\t\tif (sysid && sysid!='.') {\n\t\t\t\tbuf.push(' ', sysid);\n\t\t\t}\n\t\t\tbuf.push('>');\n\t\t}else if(sysid && sysid!='.'){\n\t\t\tbuf.push(' SYSTEM ', sysid, '>');\n\t\t}else{\n\t\t\tvar sub = node.internalSubset;\n\t\t\tif(sub){\n\t\t\t\tbuf.push(\" [\",sub,\"]\");\n\t\t\t}\n\t\t\tbuf.push(\">\");\n\t\t}\n\t\treturn;\n\tcase PROCESSING_INSTRUCTION_NODE:\n\t\treturn buf.push( \"<?\",node.target,\" \",node.data,\"?>\");\n\tcase ENTITY_REFERENCE_NODE:\n\t\treturn buf.push( '&',node.nodeName,';');\n\t//case ENTITY_NODE:\n\t//case NOTATION_NODE:\n\tdefault:\n\t\tbuf.push('??',node.nodeName);\n\t}\n}\nfunction importNode(doc,node,deep){\n\tvar node2;\n\tswitch (node.nodeType) {\n\tcase ELEMENT_NODE:\n\t\tnode2 = node.cloneNode(false);\n\t\tnode2.ownerDocument = doc;\n\t\t//var attrs = node2.attributes;\n\t\t//var len = attrs.length;\n\t\t//for(var i=0;i<len;i++){\n\t\t\t//node2.setAttributeNodeNS(importNode(doc,attrs.item(i),deep));\n\t\t//}\n\tcase DOCUMENT_FRAGMENT_NODE:\n\t\tbreak;\n\tcase ATTRIBUTE_NODE:\n\t\tdeep = true;\n\t\tbreak;\n\t//case ENTITY_REFERENCE_NODE:\n\t//case PROCESSING_INSTRUCTION_NODE:\n\t////case TEXT_NODE:\n\t//case CDATA_SECTION_NODE:\n\t//case COMMENT_NODE:\n\t//\tdeep = false;\n\t//\tbreak;\n\t//case DOCUMENT_NODE:\n\t//case DOCUMENT_TYPE_NODE:\n\t//cannot be imported.\n\t//case ENTITY_NODE:\n\t//case NOTATION_NODE：\n\t//can not hit in level3\n\t//default:throw e;\n\t}\n\tif(!node2){\n\t\tnode2 = node.cloneNode(false);//false\n\t}\n\tnode2.ownerDocument = doc;\n\tnode2.parentNode = null;\n\tif(deep){\n\t\tvar child = node.firstChild;\n\t\twhile(child){\n\t\t\tnode2.appendChild(importNode(doc,child,deep));\n\t\t\tchild = child.nextSibling;\n\t\t}\n\t}\n\treturn node2;\n}\n//\n//var _relationMap = {firstChild:1,lastChild:1,previousSibling:1,nextSibling:1,\n//\t\t\t\t\tattributes:1,childNodes:1,parentNode:1,documentElement:1,doctype,};\nfunction cloneNode(doc,node,deep){\n\tvar node2 = new node.constructor();\n\tfor (var n in node) {\n\t\tif (Object.prototype.hasOwnProperty.call(node, n)) {\n\t\t\tvar v = node[n];\n\t\t\tif (typeof v != \"object\") {\n\t\t\t\tif (v != node2[n]) {\n\t\t\t\t\tnode2[n] = v;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\tif(node.childNodes){\n\t\tnode2.childNodes = new NodeList();\n\t}\n\tnode2.ownerDocument = doc;\n\tswitch (node2.nodeType) {\n\tcase ELEMENT_NODE:\n\t\tvar attrs\t= node.attributes;\n\t\tvar attrs2\t= node2.attributes = new NamedNodeMap();\n\t\tvar len = attrs.length\n\t\tattrs2._ownerElement = node2;\n\t\tfor(var i=0;i<len;i++){\n\t\t\tnode2.setAttributeNode(cloneNode(doc,attrs.item(i),true));\n\t\t}\n\t\tbreak;;\n\tcase ATTRIBUTE_NODE:\n\t\tdeep = true;\n\t}\n\tif(deep){\n\t\tvar child = node.firstChild;\n\t\twhile(child){\n\t\t\tnode2.appendChild(cloneNode(doc,child,deep));\n\t\t\tchild = child.nextSibling;\n\t\t}\n\t}\n\treturn node2;\n}\n\nfunction __set__(object,key,value){\n\tobject[key] = value\n}\n//do dynamic\ntry{\n\tif(Object.defineProperty){\n\t\tObject.defineProperty(LiveNodeList.prototype,'length',{\n\t\t\tget:function(){\n\t\t\t\t_updateLiveList(this);\n\t\t\t\treturn this.$$length;\n\t\t\t}\n\t\t});\n\n\t\tObject.defineProperty(Node.prototype,'textContent',{\n\t\t\tget:function(){\n\t\t\t\treturn getTextContent(this);\n\t\t\t},\n\n\t\t\tset:function(data){\n\t\t\t\tswitch(this.nodeType){\n\t\t\t\tcase ELEMENT_NODE:\n\t\t\t\tcase DOCUMENT_FRAGMENT_NODE:\n\t\t\t\t\twhile(this.firstChild){\n\t\t\t\t\t\tthis.removeChild(this.firstChild);\n\t\t\t\t\t}\n\t\t\t\t\tif(data || String(data)){\n\t\t\t\t\t\tthis.appendChild(this.ownerDocument.createTextNode(data));\n\t\t\t\t\t}\n\t\t\t\t\tbreak;\n\n\t\t\t\tdefault:\n\t\t\t\t\tthis.data = data;\n\t\t\t\t\tthis.value = data;\n\t\t\t\t\tthis.nodeValue = data;\n\t\t\t\t}\n\t\t\t}\n\t\t})\n\n\t\tfunction getTextContent(node){\n\t\t\tswitch(node.nodeType){\n\t\t\tcase ELEMENT_NODE:\n\t\t\tcase DOCUMENT_FRAGMENT_NODE:\n\t\t\t\tvar buf = [];\n\t\t\t\tnode = node.firstChild;\n\t\t\t\twhile(node){\n\t\t\t\t\tif(node.nodeType!==7 && node.nodeType !==8){\n\t\t\t\t\t\tbuf.push(getTextContent(node));\n\t\t\t\t\t}\n\t\t\t\t\tnode = node.nextSibling;\n\t\t\t\t}\n\t\t\t\treturn buf.join('');\n\t\t\tdefault:\n\t\t\t\treturn node.nodeValue;\n\t\t\t}\n\t\t}\n\n\t\t__set__ = function(object,key,value){\n\t\t\t//console.log(value)\n\t\t\tobject['$$'+key] = value\n\t\t}\n\t}\n}catch(e){//ie8\n}\n\n//if(typeof require == 'function'){\n\texports.DocumentType = DocumentType;\n\texports.DOMException = DOMException;\n\texports.DOMImplementation = DOMImplementation;\n\texports.Element = Element;\n\texports.Node = Node;\n\texports.NodeList = NodeList;\n\texports.XMLSerializer = XMLSerializer;\n//}\n"], "mappings": "AAAA,IAAIA,WAAW,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE1C,IAAIC,IAAI,GAAGF,WAAW,CAACE,IAAI;AAC3B,IAAIC,SAAS,GAAGH,WAAW,CAACG,SAAS;;AAErC;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAAEC,KAAK,EAAE;EAC/B,OAAOA,KAAK,KAAK,EAAE;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,sBAAsBA,CAACD,KAAK,EAAE;EACtC;EACA,OAAOA,KAAK,GAAGA,KAAK,CAACE,KAAK,CAAC,cAAc,CAAC,CAACC,MAAM,CAACJ,cAAc,CAAC,GAAG,EAAE;AACvE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,iBAAiBA,CAAEC,OAAO,EAAEC,OAAO,EAAE;EAC7C,IAAI,CAACD,OAAO,CAACE,cAAc,CAACD,OAAO,CAAC,EAAE;IACrCD,OAAO,CAACC,OAAO,CAAC,GAAG,IAAI;EACxB;EACA,OAAOD,OAAO;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASG,YAAYA,CAACR,KAAK,EAAE;EAC5B,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;EACrB,IAAIS,IAAI,GAAGR,sBAAsB,CAACD,KAAK,CAAC;EACxC,OAAOU,MAAM,CAACC,IAAI,CAACF,IAAI,CAACG,MAAM,CAACR,iBAAiB,EAAE,CAAC,CAAC,CAAC,CAAC;AACvD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,aAAaA,CAAEJ,IAAI,EAAE;EAC7B,OAAO,UAASH,OAAO,EAAE;IACxB,OAAOG,IAAI,IAAIA,IAAI,CAACK,OAAO,CAACR,OAAO,CAAC,KAAK,CAAC,CAAC;EAC5C,CAAC;AACF;AAEA,SAASS,IAAIA,CAACC,GAAG,EAACC,IAAI,EAAC;EACtB,KAAI,IAAIC,CAAC,IAAIF,GAAG,EAAC;IAChB,IAAIN,MAAM,CAACS,SAAS,CAACZ,cAAc,CAACa,IAAI,CAACJ,GAAG,EAAEE,CAAC,CAAC,EAAE;MACjDD,IAAI,CAACC,CAAC,CAAC,GAAGF,GAAG,CAACE,CAAC,CAAC;IACjB;EACD;AACD;;AAEA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACC,KAAK,EAACC,KAAK,EAAC;EAC7B,IAAIC,EAAE,GAAGF,KAAK,CAACH,SAAS;EACxB,IAAG,EAAEK,EAAE,YAAYD,KAAK,CAAC,EAAC;IAAA,IAChBE,CAAC,GAAV,SAASA,CAACA,CAAA,EAAE,CAAC,CAAC;IAAA;IACdA,CAAC,CAACN,SAAS,GAAGI,KAAK,CAACJ,SAAS;IAC7BM,CAAC,GAAG,IAAIA,CAAC,CAAC,CAAC;IACXV,IAAI,CAACS,EAAE,EAACC,CAAC,CAAC;IACVH,KAAK,CAACH,SAAS,GAAGK,EAAE,GAAGC,CAAC;EACzB;EACA,IAAGD,EAAE,CAACE,WAAW,IAAIJ,KAAK,EAAC;IAC1B,IAAG,OAAOA,KAAK,IAAI,UAAU,EAAC;MAC7BK,OAAO,CAACC,KAAK,CAAC,gBAAgB,GAACN,KAAK,CAAC;IACtC;IACAE,EAAE,CAACE,WAAW,GAAGJ,KAAK;EACvB;AACD;;AAEA;AACA,IAAIO,QAAQ,GAAG,CAAC,CAAC;AACjB,IAAIC,YAAY,GAAkBD,QAAQ,CAACC,YAAY,GAAkB,CAAC;AAC1E,IAAIC,cAAc,GAAgBF,QAAQ,CAACE,cAAc,GAAgB,CAAC;AAC1E,IAAIC,SAAS,GAAqBH,QAAQ,CAACG,SAAS,GAAqB,CAAC;AAC1E,IAAIC,kBAAkB,GAAYJ,QAAQ,CAACI,kBAAkB,GAAY,CAAC;AAC1E,IAAIC,qBAAqB,GAASL,QAAQ,CAACK,qBAAqB,GAAS,CAAC;AAC1E,IAAIC,WAAW,GAAmBN,QAAQ,CAACM,WAAW,GAAmB,CAAC;AAC1E,IAAIC,2BAA2B,GAAGP,QAAQ,CAACO,2BAA2B,GAAG,CAAC;AAC1E,IAAIC,YAAY,GAAkBR,QAAQ,CAACQ,YAAY,GAAkB,CAAC;AAC1E,IAAIC,aAAa,GAAiBT,QAAQ,CAACS,aAAa,GAAiB,CAAC;AAC1E,IAAIC,kBAAkB,GAAYV,QAAQ,CAACU,kBAAkB,GAAY,EAAE;AAC3E,IAAIC,sBAAsB,GAAQX,QAAQ,CAACW,sBAAsB,GAAQ,EAAE;AAC3E,IAAIC,aAAa,GAAiBZ,QAAQ,CAACY,aAAa,GAAiB,EAAE;;AAE3E;AACA,IAAIC,aAAa,GAAG,CAAC,CAAC;AACtB,IAAIC,gBAAgB,GAAG,CAAC,CAAC;AACzB,IAAIC,cAAc,GAAgBF,aAAa,CAACE,cAAc,IAAkBD,gBAAgB,CAAC,CAAC,CAAC,GAAC,kBAAkB,EAAE,CAAC,CAAC;AAC1H,IAAIE,kBAAkB,GAAYH,aAAa,CAACG,kBAAkB,IAAcF,gBAAgB,CAAC,CAAC,CAAC,GAAC,sBAAsB,EAAE,CAAC,CAAC;AAC9H,IAAIG,qBAAqB,GAASJ,aAAa,CAACI,qBAAqB,IAAWH,gBAAgB,CAAC,CAAC,CAAC,GAAC,yBAAyB,EAAE,CAAC,CAAC;AACjI,IAAII,kBAAkB,GAAYL,aAAa,CAACK,kBAAkB,IAAcJ,gBAAgB,CAAC,CAAC,CAAC,GAAC,gBAAgB,EAAE,CAAC,CAAC;AACxH,IAAIK,qBAAqB,GAASN,aAAa,CAACM,qBAAqB,IAAWL,gBAAgB,CAAC,CAAC,CAAC,GAAC,mBAAmB,EAAE,CAAC,CAAC;AAC3H,IAAIM,mBAAmB,GAAWP,aAAa,CAACO,mBAAmB,IAAaN,gBAAgB,CAAC,CAAC,CAAC,GAAC,iBAAiB,EAAE,CAAC,CAAC;AACzH,IAAIO,2BAA2B,GAAGR,aAAa,CAACQ,2BAA2B,IAAKP,gBAAgB,CAAC,CAAC,CAAC,GAAC,yBAAyB,EAAE,CAAC,CAAC;AACjI,IAAIQ,aAAa,GAAiBT,aAAa,CAACS,aAAa,IAAmBR,gBAAgB,CAAC,CAAC,CAAC,GAAC,WAAW,EAAE,CAAC,CAAC;AACnH,IAAIS,iBAAiB,GAAaV,aAAa,CAACU,iBAAiB,IAAeT,gBAAgB,CAAC,CAAC,CAAC,GAAC,eAAe,EAAE,CAAC,CAAC;AACvH,IAAIU,mBAAmB,GAAWX,aAAa,CAACW,mBAAmB,IAAaV,gBAAgB,CAAC,EAAE,CAAC,GAAC,kBAAkB,EAAE,EAAE,CAAC;AAC5H;AACA,IAAIW,iBAAiB,GAAWZ,aAAa,CAACY,iBAAiB,IAAaX,gBAAgB,CAAC,EAAE,CAAC,GAAC,eAAe,EAAE,EAAE,CAAC;AACrH,IAAIY,UAAU,GAAkBb,aAAa,CAACa,UAAU,IAAoBZ,gBAAgB,CAAC,EAAE,CAAC,GAAC,cAAc,EAAE,EAAE,CAAC;AACpH,IAAIa,wBAAwB,GAAId,aAAa,CAACc,wBAAwB,IAAMb,gBAAgB,CAAC,EAAE,CAAC,GAAC,sBAAsB,EAAE,EAAE,CAAC;AAC5H,IAAIc,aAAa,GAAef,aAAa,CAACe,aAAa,IAAgBd,gBAAgB,CAAC,EAAE,CAAC,GAAC,mBAAmB,EAAE,EAAE,CAAC;AACxH,IAAIe,kBAAkB,GAAUhB,aAAa,CAACgB,kBAAkB,IAAWf,gBAAgB,CAAC,EAAE,CAAC,GAAC,gBAAgB,EAAE,EAAE,CAAC;;AAErH;AACA;AACA;AACA;AACA;AACA;AACA,SAASgB,YAAYA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACpC,IAAGA,OAAO,YAAYC,KAAK,EAAC;IAC3B,IAAIlC,KAAK,GAAGiC,OAAO;EACpB,CAAC,MAAI;IACJjC,KAAK,GAAG,IAAI;IACZkC,KAAK,CAAC1C,IAAI,CAAC,IAAI,EAAEuB,gBAAgB,CAACiB,IAAI,CAAC,CAAC;IACxC,IAAI,CAACC,OAAO,GAAGlB,gBAAgB,CAACiB,IAAI,CAAC;IACrC,IAAGE,KAAK,CAACC,iBAAiB,EAAED,KAAK,CAACC,iBAAiB,CAAC,IAAI,EAAEJ,YAAY,CAAC;EACxE;EACA/B,KAAK,CAACgC,IAAI,GAAGA,IAAI;EACjB,IAAGC,OAAO,EAAE,IAAI,CAACA,OAAO,GAAG,IAAI,CAACA,OAAO,GAAG,IAAI,GAAGA,OAAO;EACxD,OAAOjC,KAAK;AACb;AAAC;AACD+B,YAAY,CAACxC,SAAS,GAAG2C,KAAK,CAAC3C,SAAS;AACxCJ,IAAI,CAAC2B,aAAa,EAACiB,YAAY,CAAC;;AAEhC;AACA;AACA;AACA;AACA;AACA,SAASK,QAAQA,CAAA,EAAG,CACpB;AAAC;AACDA,QAAQ,CAAC7C,SAAS,GAAG;EACpB;AACD;AACA;AACA;EACC8C,MAAM,EAAC,CAAC;EACR;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACCC,IAAI,EAAE,SAANA,IAAIA,CAAWC,KAAK,EAAE;IACrB,OAAOA,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAG,IAAI,CAACF,MAAM,GAAG,IAAI,CAACE,KAAK,CAAC,GAAG,IAAI;EAC9D,CAAC;EACDC,QAAQ,EAAC,SAATA,QAAQA,CAAUC,MAAM,EAACC,UAAU,EAAC;IACnC,KAAI,IAAIC,GAAG,GAAG,EAAE,EAAEC,CAAC,GAAG,CAAC,EAACA,CAAC,GAAC,IAAI,CAACP,MAAM,EAACO,CAAC,EAAE,EAAC;MACzCC,iBAAiB,CAAC,IAAI,CAACD,CAAC,CAAC,EAACD,GAAG,EAACF,MAAM,EAACC,UAAU,CAAC;IACjD;IACA,OAAOC,GAAG,CAACG,IAAI,CAAC,EAAE,CAAC;EACpB,CAAC;EACD;AACD;AACA;AACA;AACA;EACCvE,MAAM,EAAE,SAARA,MAAMA,CAAYwE,SAAS,EAAE;IAC5B,OAAOC,KAAK,CAACzD,SAAS,CAAChB,MAAM,CAACiB,IAAI,CAAC,IAAI,EAAEuD,SAAS,CAAC;EACpD,CAAC;EACD;AACD;AACA;AACA;AACA;EACC7D,OAAO,EAAE,SAATA,OAAOA,CAAYoD,IAAI,EAAE;IACxB,OAAOU,KAAK,CAACzD,SAAS,CAACL,OAAO,CAACM,IAAI,CAAC,IAAI,EAAE8C,IAAI,CAAC;EAChD;AACD,CAAC;AAED,SAASW,YAAYA,CAACC,IAAI,EAACC,OAAO,EAAC;EAClC,IAAI,CAACC,KAAK,GAAGF,IAAI;EACjB,IAAI,CAACG,QAAQ,GAAGF,OAAO;EACvBG,eAAe,CAAC,IAAI,CAAC;AACtB;AACA,SAASA,eAAeA,CAACzE,IAAI,EAAC;EAC7B,IAAI0E,GAAG,GAAG1E,IAAI,CAACuE,KAAK,CAACI,IAAI,IAAI3E,IAAI,CAACuE,KAAK,CAACK,aAAa,CAACD,IAAI;EAC1D,IAAI3E,IAAI,CAAC2E,IAAI,KAAKD,GAAG,EAAE;IACtB,IAAIG,EAAE,GAAG7E,IAAI,CAACwE,QAAQ,CAACxE,IAAI,CAACuE,KAAK,CAAC;IAClCO,OAAO,CAAC9E,IAAI,EAAC,QAAQ,EAAC6E,EAAE,CAACrB,MAAM,CAAC;IAChC,IAAI,CAACxD,IAAI,CAAC+E,QAAQ,IAAIF,EAAE,CAACrB,MAAM,GAAGxD,IAAI,CAAC+E,QAAQ,EAAE;MAChD,KAAK,IAAIhB,CAAC,GAAGc,EAAE,CAACrB,MAAM,EAAEO,CAAC,IAAI/D,IAAI,EAAE+D,CAAC,EAAE,EAAE;QACvC,IAAI9D,MAAM,CAACS,SAAS,CAACZ,cAAc,CAACa,IAAI,CAACX,IAAI,EAAE+D,CAAC,CAAC,EAAE;UAClD,OAAO/D,IAAI,CAAC+D,CAAC,CAAC;QACf;MACD;IACD;IACAzD,IAAI,CAACuE,EAAE,EAAC7E,IAAI,CAAC;IACbA,IAAI,CAAC2E,IAAI,GAAGD,GAAG;EAChB;AACD;AACAN,YAAY,CAAC1D,SAAS,CAAC+C,IAAI,GAAG,UAASM,CAAC,EAAC;EACxCU,eAAe,CAAC,IAAI,CAAC;EACrB,OAAO,IAAI,CAACV,CAAC,CAAC,IAAI,IAAI;AACvB,CAAC;AAEDnD,QAAQ,CAACwD,YAAY,EAACb,QAAQ,CAAC;;AAE/B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASyB,YAAYA,CAAA,EAAG,CACxB;AAAC;AAED,SAASC,cAAcA,CAACjF,IAAI,EAACqE,IAAI,EAAC;EACjC,IAAIN,CAAC,GAAG/D,IAAI,CAACwD,MAAM;EACnB,OAAMO,CAAC,EAAE,EAAC;IACT,IAAG/D,IAAI,CAAC+D,CAAC,CAAC,KAAKM,IAAI,EAAC;MAAC,OAAON,CAAC;IAAA;EAC9B;AACD;AAEA,SAASmB,aAAaA,CAACC,EAAE,EAACnF,IAAI,EAACoF,OAAO,EAACC,OAAO,EAAC;EAC9C,IAAGA,OAAO,EAAC;IACVrF,IAAI,CAACiF,cAAc,CAACjF,IAAI,EAACqF,OAAO,CAAC,CAAC,GAAGD,OAAO;EAC7C,CAAC,MAAI;IACJpF,IAAI,CAACA,IAAI,CAACwD,MAAM,EAAE,CAAC,GAAG4B,OAAO;EAC9B;EACA,IAAGD,EAAE,EAAC;IACLC,OAAO,CAACE,YAAY,GAAGH,EAAE;IACzB,IAAII,GAAG,GAAGJ,EAAE,CAACP,aAAa;IAC1B,IAAGW,GAAG,EAAC;MACNF,OAAO,IAAIG,kBAAkB,CAACD,GAAG,EAACJ,EAAE,EAACE,OAAO,CAAC;MAC7CI,eAAe,CAACF,GAAG,EAACJ,EAAE,EAACC,OAAO,CAAC;IAChC;EACD;AACD;AACA,SAASM,gBAAgBA,CAACP,EAAE,EAACnF,IAAI,EAAC2F,IAAI,EAAC;EACtC;EACA,IAAI5B,CAAC,GAAGkB,cAAc,CAACjF,IAAI,EAAC2F,IAAI,CAAC;EACjC,IAAG5B,CAAC,IAAE,CAAC,EAAC;IACP,IAAI6B,SAAS,GAAG5F,IAAI,CAACwD,MAAM,GAAC,CAAC;IAC7B,OAAMO,CAAC,GAAC6B,SAAS,EAAC;MACjB5F,IAAI,CAAC+D,CAAC,CAAC,GAAG/D,IAAI,CAAC,EAAE+D,CAAC,CAAC;IACpB;IACA/D,IAAI,CAACwD,MAAM,GAAGoC,SAAS;IACvB,IAAGT,EAAE,EAAC;MACL,IAAII,GAAG,GAAGJ,EAAE,CAACP,aAAa;MAC1B,IAAGW,GAAG,EAAC;QACNC,kBAAkB,CAACD,GAAG,EAACJ,EAAE,EAACQ,IAAI,CAAC;QAC/BA,IAAI,CAACL,YAAY,GAAG,IAAI;MACzB;IACD;EACD,CAAC,MAAI;IACJ,MAAM,IAAIpC,YAAY,CAACR,aAAa,EAAC,IAAIW,KAAK,CAAC8B,EAAE,CAACU,OAAO,GAAC,GAAG,GAACF,IAAI,CAAC,CAAC;EACrE;AACD;AACAX,YAAY,CAACtE,SAAS,GAAG;EACxB8C,MAAM,EAAC,CAAC;EACRC,IAAI,EAACF,QAAQ,CAAC7C,SAAS,CAAC+C,IAAI;EAC5BqC,YAAY,EAAE,SAAdA,YAAYA,CAAWC,GAAG,EAAE;IAC7B;IACA;IACA;IACE;IACA,IAAIhC,CAAC,GAAG,IAAI,CAACP,MAAM;IACnB,OAAMO,CAAC,EAAE,EAAC;MACT,IAAI4B,IAAI,GAAG,IAAI,CAAC5B,CAAC,CAAC;MAClB;MACA,IAAG4B,IAAI,CAACK,QAAQ,IAAID,GAAG,EAAC;QACvB,OAAOJ,IAAI;MACZ;IACD;EACD,CAAC;EACDM,YAAY,EAAE,SAAdA,YAAYA,CAAWN,IAAI,EAAE;IAC5B,IAAIR,EAAE,GAAGQ,IAAI,CAACL,YAAY;IAC1B,IAAGH,EAAE,IAAIA,EAAE,IAAE,IAAI,CAACe,aAAa,EAAC;MAC/B,MAAM,IAAIhD,YAAY,CAACN,mBAAmB,CAAC;IAC5C;IACA,IAAIyC,OAAO,GAAG,IAAI,CAACS,YAAY,CAACH,IAAI,CAACK,QAAQ,CAAC;IAC9Cd,aAAa,CAAC,IAAI,CAACgB,aAAa,EAAC,IAAI,EAACP,IAAI,EAACN,OAAO,CAAC;IACnD,OAAOA,OAAO;EACf,CAAC;EACD;EACAc,cAAc,EAAE,SAAhBA,cAAcA,CAAWR,IAAI,EAAE;IAAC;IAC/B,IAAIR,EAAE,GAAGQ,IAAI,CAACL,YAAY;MAAED,OAAO;IACnC,IAAGF,EAAE,IAAIA,EAAE,IAAE,IAAI,CAACe,aAAa,EAAC;MAC/B,MAAM,IAAIhD,YAAY,CAACN,mBAAmB,CAAC;IAC5C;IACAyC,OAAO,GAAG,IAAI,CAACe,cAAc,CAACT,IAAI,CAACU,YAAY,EAACV,IAAI,CAACW,SAAS,CAAC;IAC/DpB,aAAa,CAAC,IAAI,CAACgB,aAAa,EAAC,IAAI,EAACP,IAAI,EAACN,OAAO,CAAC;IACnD,OAAOA,OAAO;EACf,CAAC;EAED;EACAkB,eAAe,EAAE,SAAjBA,eAAeA,CAAWR,GAAG,EAAE;IAC9B,IAAIJ,IAAI,GAAG,IAAI,CAACG,YAAY,CAACC,GAAG,CAAC;IACjCL,gBAAgB,CAAC,IAAI,CAACQ,aAAa,EAAC,IAAI,EAACP,IAAI,CAAC;IAC9C,OAAOA,IAAI;EAGZ,CAAC;EAAC;;EAEF;EACAa,iBAAiB,EAAC,SAAlBA,iBAAiBA,CAAUH,YAAY,EAACC,SAAS,EAAC;IACjD,IAAIX,IAAI,GAAG,IAAI,CAACS,cAAc,CAACC,YAAY,EAACC,SAAS,CAAC;IACtDZ,gBAAgB,CAAC,IAAI,CAACQ,aAAa,EAAC,IAAI,EAACP,IAAI,CAAC;IAC9C,OAAOA,IAAI;EACZ,CAAC;EACDS,cAAc,EAAE,SAAhBA,cAAcA,CAAWC,YAAY,EAAEC,SAAS,EAAE;IACjD,IAAIvC,CAAC,GAAG,IAAI,CAACP,MAAM;IACnB,OAAMO,CAAC,EAAE,EAAC;MACT,IAAIM,IAAI,GAAG,IAAI,CAACN,CAAC,CAAC;MAClB,IAAGM,IAAI,CAACiC,SAAS,IAAIA,SAAS,IAAIjC,IAAI,CAACgC,YAAY,IAAIA,YAAY,EAAC;QACnE,OAAOhC,IAAI;MACZ;IACD;IACA,OAAO,IAAI;EACZ;AACD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoC,iBAAiBA,CAAA,EAAG,CAC7B;AAEAA,iBAAiB,CAAC/F,SAAS,GAAG;EAC7B;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACCgG,UAAU,EAAE,SAAZA,UAAUA,CAAWC,OAAO,EAAEC,OAAO,EAAE;IACrC,OAAO,IAAI;EACb,CAAC;EACD;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACCC,cAAc,EAAE,SAAhBA,cAAcA,CAAWR,YAAY,EAAGS,aAAa,EAAEC,OAAO,EAAC;IAC9D,IAAIxB,GAAG,GAAG,IAAIyB,QAAQ,CAAC,CAAC;IACxBzB,GAAG,CAAC0B,cAAc,GAAG,IAAI;IACzB1B,GAAG,CAAC2B,UAAU,GAAG,IAAI3D,QAAQ,CAAC,CAAC;IAC/BgC,GAAG,CAACwB,OAAO,GAAGA,OAAO,IAAI,IAAI;IAC7B,IAAIA,OAAO,EAAC;MACXxB,GAAG,CAAC4B,WAAW,CAACJ,OAAO,CAAC;IACzB;IACA,IAAID,aAAa,EAAC;MACjB,IAAIM,IAAI,GAAG7B,GAAG,CAAC8B,eAAe,CAAChB,YAAY,EAAES,aAAa,CAAC;MAC3DvB,GAAG,CAAC4B,WAAW,CAACC,IAAI,CAAC;IACtB;IACA,OAAO7B,GAAG;EACX,CAAC;EACD;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC+B,kBAAkB,EAAE,SAApBA,kBAAkBA,CAAWR,aAAa,EAAES,QAAQ,EAAEC,QAAQ,EAAC;IAC9D,IAAInD,IAAI,GAAG,IAAIoD,YAAY,CAAC,CAAC;IAC7BpD,IAAI,CAACqD,IAAI,GAAGZ,aAAa;IACzBzC,IAAI,CAAC2B,QAAQ,GAAGc,aAAa;IAC7BzC,IAAI,CAACkD,QAAQ,GAAGA,QAAQ,IAAI,EAAE;IAC9BlD,IAAI,CAACmD,QAAQ,GAAGA,QAAQ,IAAI,EAAE;IAE9B,OAAOnD,IAAI;EACZ;AACD,CAAC;;AAGD;AACA;AACA;;AAEA,SAASsD,IAAIA,CAAA,EAAG,CAChB;AAAC;AAEDA,IAAI,CAACjH,SAAS,GAAG;EAChBkH,UAAU,EAAG,IAAI;EACjBC,SAAS,EAAG,IAAI;EAChBC,eAAe,EAAG,IAAI;EACtBC,WAAW,EAAG,IAAI;EAClBC,UAAU,EAAG,IAAI;EACjBC,UAAU,EAAG,IAAI;EACjBf,UAAU,EAAG,IAAI;EACjBtC,aAAa,EAAG,IAAI;EACpBsD,SAAS,EAAG,IAAI;EAChB7B,YAAY,EAAG,IAAI;EACnB8B,MAAM,EAAG,IAAI;EACb7B,SAAS,EAAG,IAAI;EAChB;EACA8B,YAAY,EAAC,SAAbA,YAAYA,CAAUC,QAAQ,EAAEC,QAAQ,EAAC;IAAC;IACzC,OAAOC,aAAa,CAAC,IAAI,EAACF,QAAQ,EAACC,QAAQ,CAAC;EAC7C,CAAC;EACDE,YAAY,EAAC,SAAbA,YAAYA,CAAUH,QAAQ,EAAEI,QAAQ,EAAC;IAAC;IACzCF,aAAa,CAAC,IAAI,EAAEF,QAAQ,EAACI,QAAQ,EAAEC,sCAAsC,CAAC;IAC9E,IAAGD,QAAQ,EAAC;MACX,IAAI,CAACE,WAAW,CAACF,QAAQ,CAAC;IAC3B;EACD,CAAC;EACDE,WAAW,EAAC,SAAZA,WAAWA,CAAUF,QAAQ,EAAC;IAC7B,OAAOG,YAAY,CAAC,IAAI,EAACH,QAAQ,CAAC;EACnC,CAAC;EACDtB,WAAW,EAAC,SAAZA,WAAWA,CAAUkB,QAAQ,EAAC;IAC7B,OAAO,IAAI,CAACD,YAAY,CAACC,QAAQ,EAAC,IAAI,CAAC;EACxC,CAAC;EACDQ,aAAa,EAAC,SAAdA,aAAaA,CAAA,EAAW;IACvB,OAAO,IAAI,CAACjB,UAAU,IAAI,IAAI;EAC/B,CAAC;EACDkB,SAAS,EAAC,SAAVA,SAASA,CAAUC,IAAI,EAAC;IACvB,OAAOD,UAAS,CAAC,IAAI,CAAClE,aAAa,IAAE,IAAI,EAAC,IAAI,EAACmE,IAAI,CAAC;EACrD,CAAC;EACD;EACAC,SAAS,EAAC,SAAVA,SAASA,CAAA,EAAW;IACnB,IAAIC,KAAK,GAAG,IAAI,CAACrB,UAAU;IAC3B,OAAMqB,KAAK,EAAC;MACX,IAAIC,IAAI,GAAGD,KAAK,CAAClB,WAAW;MAC5B,IAAGmB,IAAI,IAAIA,IAAI,CAACC,QAAQ,IAAI5H,SAAS,IAAI0H,KAAK,CAACE,QAAQ,IAAI5H,SAAS,EAAC;QACpE,IAAI,CAACoH,WAAW,CAACO,IAAI,CAAC;QACtBD,KAAK,CAACG,UAAU,CAACF,IAAI,CAACG,IAAI,CAAC;MAC5B,CAAC,MAAI;QACJJ,KAAK,CAACD,SAAS,CAAC,CAAC;QACjBC,KAAK,GAAGC,IAAI;MACb;IACD;EACD,CAAC;EACC;EACFI,WAAW,EAAC,SAAZA,WAAWA,CAAU3C,OAAO,EAAEC,OAAO,EAAC;IACrC,OAAO,IAAI,CAAChC,aAAa,CAACqC,cAAc,CAACP,UAAU,CAACC,OAAO,EAACC,OAAO,CAAC;EACrE,CAAC;EACE;EACA2C,aAAa,EAAC,SAAdA,aAAaA,CAAA,EAAW;IACvB,OAAO,IAAI,CAACvB,UAAU,CAACxE,MAAM,GAAC,CAAC;EAChC,CAAC;EACJ;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIgG,YAAY,EAAC,SAAbA,YAAYA,CAAUnD,YAAY,EAAC;IAClC,IAAIlB,EAAE,GAAG,IAAI;IACb,OAAMA,EAAE,EAAC;MACR,IAAIsE,GAAG,GAAGtE,EAAE,CAACuE,MAAM;MACnB;MACA,IAAGD,GAAG,EAAC;QACN,KAAI,IAAIE,CAAC,IAAIF,GAAG,EAAC;UAClB,IAAIxJ,MAAM,CAACS,SAAS,CAACZ,cAAc,CAACa,IAAI,CAAC8I,GAAG,EAAEE,CAAC,CAAC,IAAIF,GAAG,CAACE,CAAC,CAAC,KAAKtD,YAAY,EAAE;YAC5E,OAAOsD,CAAC;UACT;QACC;MACD;MACAxE,EAAE,GAAGA,EAAE,CAACgE,QAAQ,IAAI7H,cAAc,GAAC6D,EAAE,CAACP,aAAa,GAAGO,EAAE,CAAC8C,UAAU;IACpE;IACA,OAAO,IAAI;EACZ,CAAC;EACD;EACA2B,kBAAkB,EAAC,SAAnBA,kBAAkBA,CAAUzB,MAAM,EAAC;IAClC,IAAIhD,EAAE,GAAG,IAAI;IACb,OAAMA,EAAE,EAAC;MACR,IAAIsE,GAAG,GAAGtE,EAAE,CAACuE,MAAM;MACnB;MACA,IAAGD,GAAG,EAAC;QACN,IAAGxJ,MAAM,CAACS,SAAS,CAACZ,cAAc,CAACa,IAAI,CAAC8I,GAAG,EAAEtB,MAAM,CAAC,EAAC;UACpD,OAAOsB,GAAG,CAACtB,MAAM,CAAC;QACnB;MACD;MACAhD,EAAE,GAAGA,EAAE,CAACgE,QAAQ,IAAI7H,cAAc,GAAC6D,EAAE,CAACP,aAAa,GAAGO,EAAE,CAAC8C,UAAU;IACpE;IACA,OAAO,IAAI;EACZ,CAAC;EACD;EACA4B,kBAAkB,EAAC,SAAnBA,kBAAkBA,CAAUxD,YAAY,EAAC;IACxC,IAAI8B,MAAM,GAAG,IAAI,CAACqB,YAAY,CAACnD,YAAY,CAAC;IAC5C,OAAO8B,MAAM,IAAI,IAAI;EACtB;AACJ,CAAC;AAGD,SAAS2B,WAAWA,CAACC,CAAC,EAAC;EACtB,OAAOA,CAAC,IAAI,GAAG,IAAI,MAAM,IACjBA,CAAC,IAAI,GAAG,IAAI,MAAM,IAClBA,CAAC,IAAI,GAAG,IAAI,OAAO,IACnBA,CAAC,IAAI,GAAG,IAAI,QAAQ,IACpB,IAAI,GAACA,CAAC,CAACC,UAAU,CAAC,CAAC,GAAC,GAAG;AAChC;AAGA1J,IAAI,CAACc,QAAQ,EAACuG,IAAI,CAAC;AACnBrH,IAAI,CAACc,QAAQ,EAACuG,IAAI,CAACjH,SAAS,CAAC;;AAE7B;AACA;AACA;AACA;AACA,SAASuJ,UAAUA,CAAC5F,IAAI,EAAC6F,QAAQ,EAAC;EACjC,IAAGA,QAAQ,CAAC7F,IAAI,CAAC,EAAC;IACjB,OAAO,IAAI;EACZ;EACA,IAAGA,IAAI,GAAGA,IAAI,CAACuD,UAAU,EAAC;IACzB,GAAE;MACD,IAAGqC,UAAU,CAAC5F,IAAI,EAAC6F,QAAQ,CAAC,EAAC;QAAC,OAAO,IAAI;MAAA;IACpC,CAAC,QAAM7F,IAAI,GAACA,IAAI,CAAC0D,WAAW;EAChC;AACJ;AAIA,SAASf,QAAQA,CAAA,EAAE;EAClB,IAAI,CAACpC,aAAa,GAAG,IAAI;AAC1B;AAEA,SAASa,eAAeA,CAACF,GAAG,EAACJ,EAAE,EAACC,OAAO,EAAC;EACvCG,GAAG,IAAIA,GAAG,CAACZ,IAAI,EAAE;EACjB,IAAIwF,EAAE,GAAG/E,OAAO,CAACiB,YAAY;EAC7B,IAAG8D,EAAE,KAAK9K,SAAS,CAAC+K,KAAK,EAAC;IACzB;IACAjF,EAAE,CAACuE,MAAM,CAACtE,OAAO,CAAC+C,MAAM,GAAC/C,OAAO,CAACkB,SAAS,GAAC,EAAE,CAAC,GAAGlB,OAAO,CAACiF,KAAK;EAC/D;AACD;AAEA,SAAS7E,kBAAkBA,CAACD,GAAG,EAACJ,EAAE,EAACC,OAAO,EAACkF,MAAM,EAAC;EACjD/E,GAAG,IAAIA,GAAG,CAACZ,IAAI,EAAE;EACjB,IAAIwF,EAAE,GAAG/E,OAAO,CAACiB,YAAY;EAC7B,IAAG8D,EAAE,KAAK9K,SAAS,CAAC+K,KAAK,EAAC;IACzB;IACA,OAAOjF,EAAE,CAACuE,MAAM,CAACtE,OAAO,CAAC+C,MAAM,GAAC/C,OAAO,CAACkB,SAAS,GAAC,EAAE,CAAC;EACtD;AACD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiE,cAAcA,CAAEhF,GAAG,EAAEJ,EAAE,EAAEkD,QAAQ,EAAE;EAC3C,IAAG9C,GAAG,IAAIA,GAAG,CAACZ,IAAI,EAAC;IAClBY,GAAG,CAACZ,IAAI,EAAE;IACV;IACA,IAAI6F,EAAE,GAAGrF,EAAE,CAAC+B,UAAU;IACtB,IAAImB,QAAQ,EAAE;MACbmC,EAAE,CAACA,EAAE,CAAChH,MAAM,EAAE,CAAC,GAAG6E,QAAQ;IAC3B,CAAC,MAAM;MACN,IAAIY,KAAK,GAAG9D,EAAE,CAACyC,UAAU;MACzB,IAAI7D,CAAC,GAAG,CAAC;MACT,OAAOkF,KAAK,EAAE;QACbuB,EAAE,CAACzG,CAAC,EAAE,CAAC,GAAGkF,KAAK;QACfA,KAAK,GAAGA,KAAK,CAAClB,WAAW;MAC1B;MACAyC,EAAE,CAAChH,MAAM,GAAGO,CAAC;MACb,OAAOyG,EAAE,CAACA,EAAE,CAAChH,MAAM,CAAC;IACrB;EACD;AACD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoF,YAAYA,CAAEX,UAAU,EAAEgB,KAAK,EAAE;EACzC,IAAIwB,QAAQ,GAAGxB,KAAK,CAACnB,eAAe;EACpC,IAAIoB,IAAI,GAAGD,KAAK,CAAClB,WAAW;EAC5B,IAAI0C,QAAQ,EAAE;IACbA,QAAQ,CAAC1C,WAAW,GAAGmB,IAAI;EAC5B,CAAC,MAAM;IACNjB,UAAU,CAACL,UAAU,GAAGsB,IAAI;EAC7B;EACA,IAAIA,IAAI,EAAE;IACTA,IAAI,CAACpB,eAAe,GAAG2C,QAAQ;EAChC,CAAC,MAAM;IACNxC,UAAU,CAACJ,SAAS,GAAG4C,QAAQ;EAChC;EACAxB,KAAK,CAAChB,UAAU,GAAG,IAAI;EACvBgB,KAAK,CAACnB,eAAe,GAAG,IAAI;EAC5BmB,KAAK,CAAClB,WAAW,GAAG,IAAI;EACxBwC,cAAc,CAACtC,UAAU,CAACrD,aAAa,EAAEqD,UAAU,CAAC;EACpD,OAAOgB,KAAK;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASyB,sBAAsBA,CAACrG,IAAI,EAAE;EACrC,OACCA,IAAI,KACHA,IAAI,CAAC8E,QAAQ,KAAKxB,IAAI,CAAC9F,aAAa,IAAIwC,IAAI,CAAC8E,QAAQ,KAAKxB,IAAI,CAAC5F,sBAAsB,IAAIsC,IAAI,CAAC8E,QAAQ,KAAKxB,IAAI,CAACtG,YAAY,CAAC;AAEhI;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASsJ,qBAAqBA,CAACtG,IAAI,EAAE;EACpC,OACCA,IAAI,KACHuG,aAAa,CAACvG,IAAI,CAAC,IACnBwG,UAAU,CAACxG,IAAI,CAAC,IAChByG,aAAa,CAACzG,IAAI,CAAC,IACnBA,IAAI,CAAC8E,QAAQ,KAAKxB,IAAI,CAAC5F,sBAAsB,IAC7CsC,IAAI,CAAC8E,QAAQ,KAAKxB,IAAI,CAAC/F,YAAY,IACnCyC,IAAI,CAAC8E,QAAQ,KAAKxB,IAAI,CAAChG,2BAA2B,CAAC;AAEtD;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASmJ,aAAaA,CAACzG,IAAI,EAAE;EAC5B,OAAOA,IAAI,IAAIA,IAAI,CAAC8E,QAAQ,KAAKxB,IAAI,CAAC7F,kBAAkB;AACzD;;AAEA;AACA;AACA;AACA;AACA;AACA,SAAS8I,aAAaA,CAACvG,IAAI,EAAE;EAC5B,OAAOA,IAAI,IAAIA,IAAI,CAAC8E,QAAQ,KAAKxB,IAAI,CAACtG,YAAY;AACnD;AACA;AACA;AACA;AACA;AACA;AACA,SAASwJ,UAAUA,CAACxG,IAAI,EAAE;EACzB,OAAOA,IAAI,IAAIA,IAAI,CAAC8E,QAAQ,KAAKxB,IAAI,CAACpG,SAAS;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASwJ,0BAA0BA,CAACxF,GAAG,EAAE0D,KAAK,EAAE;EAC/C,IAAI+B,gBAAgB,GAAGzF,GAAG,CAAC2B,UAAU,IAAI,EAAE;EAC3C,IAAI9H,IAAI,CAAC4L,gBAAgB,EAAEJ,aAAa,CAAC,IAAIE,aAAa,CAAC7B,KAAK,CAAC,EAAE;IAClE,OAAO,KAAK;EACb;EACA,IAAIgC,WAAW,GAAG7L,IAAI,CAAC4L,gBAAgB,EAAEF,aAAa,CAAC;EACvD,OAAO,EAAE7B,KAAK,IAAIgC,WAAW,IAAID,gBAAgB,CAAC3K,OAAO,CAAC4K,WAAW,CAAC,GAAGD,gBAAgB,CAAC3K,OAAO,CAAC4I,KAAK,CAAC,CAAC;AAC1G;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiC,4BAA4BA,CAAC3F,GAAG,EAAE0D,KAAK,EAAE;EACjD,IAAI+B,gBAAgB,GAAGzF,GAAG,CAAC2B,UAAU,IAAI,EAAE;EAE3C,SAASiE,6BAA6BA,CAAC9G,IAAI,EAAE;IAC5C,OAAOuG,aAAa,CAACvG,IAAI,CAAC,IAAIA,IAAI,KAAK4E,KAAK;EAC7C;EAEA,IAAI7J,IAAI,CAAC4L,gBAAgB,EAAEG,6BAA6B,CAAC,EAAE;IAC1D,OAAO,KAAK;EACb;EACA,IAAIF,WAAW,GAAG7L,IAAI,CAAC4L,gBAAgB,EAAEF,aAAa,CAAC;EACvD,OAAO,EAAE7B,KAAK,IAAIgC,WAAW,IAAID,gBAAgB,CAAC3K,OAAO,CAAC4K,WAAW,CAAC,GAAGD,gBAAgB,CAAC3K,OAAO,CAAC4I,KAAK,CAAC,CAAC;AAC1G;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmC,8BAA8BA,CAACC,MAAM,EAAEhH,IAAI,EAAE4E,KAAK,EAAE;EAC5D;EACA,IAAI,CAACyB,sBAAsB,CAACW,MAAM,CAAC,EAAE;IACpC,MAAM,IAAInI,YAAY,CAACb,qBAAqB,EAAE,8BAA8B,GAAGgJ,MAAM,CAAClC,QAAQ,CAAC;EAChG;EACA;EACA;EACA;EACA,IAAIF,KAAK,IAAIA,KAAK,CAAChB,UAAU,KAAKoD,MAAM,EAAE;IACzC,MAAM,IAAInI,YAAY,CAACR,aAAa,EAAE,qBAAqB,CAAC;EAC7D;EACA;EACC;EACA,CAACiI,qBAAqB,CAACtG,IAAI,CAAC;EAC5B;EACA;EACA;EACA;EACCyG,aAAa,CAACzG,IAAI,CAAC,IAAIgH,MAAM,CAAClC,QAAQ,KAAKxB,IAAI,CAAC9F,aAAc,EAC9D;IACD,MAAM,IAAIqB,YAAY,CACrBb,qBAAqB,EACrB,uBAAuB,GAAGgC,IAAI,CAAC8E,QAAQ,GAAG,wBAAwB,GAAGkC,MAAM,CAAClC,QAC7E,CAAC;EACF;AACD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmC,oCAAoCA,CAACD,MAAM,EAAEhH,IAAI,EAAE4E,KAAK,EAAE;EAClE,IAAI+B,gBAAgB,GAAGK,MAAM,CAACnE,UAAU,IAAI,EAAE;EAC9C,IAAIqE,cAAc,GAAGlH,IAAI,CAAC6C,UAAU,IAAI,EAAE;;EAE1C;EACA,IAAI7C,IAAI,CAAC8E,QAAQ,KAAKxB,IAAI,CAAC5F,sBAAsB,EAAE;IAClD,IAAIyJ,iBAAiB,GAAGD,cAAc,CAAC7L,MAAM,CAACkL,aAAa,CAAC;IAC5D;IACA,IAAIY,iBAAiB,CAAChI,MAAM,GAAG,CAAC,IAAIpE,IAAI,CAACmM,cAAc,EAAEV,UAAU,CAAC,EAAE;MACrE,MAAM,IAAI3H,YAAY,CAACb,qBAAqB,EAAE,2CAA2C,CAAC;IAC3F;IACA;IACA;IACA,IAAImJ,iBAAiB,CAAChI,MAAM,KAAK,CAAC,IAAI,CAACuH,0BAA0B,CAACM,MAAM,EAAEpC,KAAK,CAAC,EAAE;MACjF,MAAM,IAAI/F,YAAY,CAACb,qBAAqB,EAAE,wDAAwD,CAAC;IACxG;EACD;EACA;EACA,IAAIuI,aAAa,CAACvG,IAAI,CAAC,EAAE;IACxB;IACA;IACA,IAAI,CAAC0G,0BAA0B,CAACM,MAAM,EAAEpC,KAAK,CAAC,EAAE;MAC/C,MAAM,IAAI/F,YAAY,CAACb,qBAAqB,EAAE,sDAAsD,CAAC;IACtG;EACD;EACA;EACA,IAAIyI,aAAa,CAACzG,IAAI,CAAC,EAAE;IACxB;IACA,IAAIjF,IAAI,CAAC4L,gBAAgB,EAAEF,aAAa,CAAC,EAAE;MAC1C,MAAM,IAAI5H,YAAY,CAACb,qBAAqB,EAAE,6BAA6B,CAAC;IAC7E;IACA,IAAIoJ,kBAAkB,GAAGrM,IAAI,CAAC4L,gBAAgB,EAAEJ,aAAa,CAAC;IAC9D;IACA,IAAI3B,KAAK,IAAI+B,gBAAgB,CAAC3K,OAAO,CAACoL,kBAAkB,CAAC,GAAGT,gBAAgB,CAAC3K,OAAO,CAAC4I,KAAK,CAAC,EAAE;MAC5F,MAAM,IAAI/F,YAAY,CAACb,qBAAqB,EAAE,gDAAgD,CAAC;IAChG;IACA;IACA,IAAI,CAAC4G,KAAK,IAAIwC,kBAAkB,EAAE;MACjC,MAAM,IAAIvI,YAAY,CAACb,qBAAqB,EAAE,sDAAsD,CAAC;IACtG;EACD;AACD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqG,sCAAsCA,CAAC2C,MAAM,EAAEhH,IAAI,EAAE4E,KAAK,EAAE;EACpE,IAAI+B,gBAAgB,GAAGK,MAAM,CAACnE,UAAU,IAAI,EAAE;EAC9C,IAAIqE,cAAc,GAAGlH,IAAI,CAAC6C,UAAU,IAAI,EAAE;;EAE1C;EACA,IAAI7C,IAAI,CAAC8E,QAAQ,KAAKxB,IAAI,CAAC5F,sBAAsB,EAAE;IAClD,IAAIyJ,iBAAiB,GAAGD,cAAc,CAAC7L,MAAM,CAACkL,aAAa,CAAC;IAC5D;IACA,IAAIY,iBAAiB,CAAChI,MAAM,GAAG,CAAC,IAAIpE,IAAI,CAACmM,cAAc,EAAEV,UAAU,CAAC,EAAE;MACrE,MAAM,IAAI3H,YAAY,CAACb,qBAAqB,EAAE,2CAA2C,CAAC;IAC3F;IACA;IACA,IAAImJ,iBAAiB,CAAChI,MAAM,KAAK,CAAC,IAAI,CAAC0H,4BAA4B,CAACG,MAAM,EAAEpC,KAAK,CAAC,EAAE;MACnF,MAAM,IAAI/F,YAAY,CAACb,qBAAqB,EAAE,wDAAwD,CAAC;IACxG;EACD;EACA;EACA,IAAIuI,aAAa,CAACvG,IAAI,CAAC,EAAE;IACxB;IACA,IAAI,CAAC6G,4BAA4B,CAACG,MAAM,EAAEpC,KAAK,CAAC,EAAE;MACjD,MAAM,IAAI/F,YAAY,CAACb,qBAAqB,EAAE,sDAAsD,CAAC;IACtG;EACD;EACA;EACA,IAAIyI,aAAa,CAACzG,IAAI,CAAC,EAAE;IAAA,IACfqH,6BAA6B,GAAtC,SAASA,6BAA6BA,CAACrH,IAAI,EAAE;MAC5C,OAAOyG,aAAa,CAACzG,IAAI,CAAC,IAAIA,IAAI,KAAK4E,KAAK;IAC7C,CAAC,EAED;IACA,IAAI7J,IAAI,CAAC4L,gBAAgB,EAAEU,6BAA6B,CAAC,EAAE;MAC1D,MAAM,IAAIxI,YAAY,CAACb,qBAAqB,EAAE,6BAA6B,CAAC;IAC7E;IACA,IAAIoJ,kBAAkB,GAAGrM,IAAI,CAAC4L,gBAAgB,EAAEJ,aAAa,CAAC;IAC9D;IACA,IAAI3B,KAAK,IAAI+B,gBAAgB,CAAC3K,OAAO,CAACoL,kBAAkB,CAAC,GAAGT,gBAAgB,CAAC3K,OAAO,CAAC4I,KAAK,CAAC,EAAE;MAC5F,MAAM,IAAI/F,YAAY,CAACb,qBAAqB,EAAE,gDAAgD,CAAC;IAChG;EACD;AACD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkG,aAAaA,CAAC8C,MAAM,EAAEhH,IAAI,EAAE4E,KAAK,EAAE0C,oBAAoB,EAAE;EACjE;EACAP,8BAA8B,CAACC,MAAM,EAAEhH,IAAI,EAAE4E,KAAK,CAAC;;EAEnD;EACA;EACA,IAAIoC,MAAM,CAAClC,QAAQ,KAAKxB,IAAI,CAAC9F,aAAa,EAAE;IAC3C,CAAC8J,oBAAoB,IAAIL,oCAAoC,EAAED,MAAM,EAAEhH,IAAI,EAAE4E,KAAK,CAAC;EACpF;EAEA,IAAI2C,EAAE,GAAGvH,IAAI,CAAC4D,UAAU;EACxB,IAAG2D,EAAE,EAAC;IACLA,EAAE,CAACjD,WAAW,CAACtE,IAAI,CAAC,CAAC;EACtB;EACA,IAAGA,IAAI,CAAC8E,QAAQ,KAAKpH,sBAAsB,EAAC;IAC3C,IAAI8J,QAAQ,GAAGxH,IAAI,CAACuD,UAAU;IAC9B,IAAIiE,QAAQ,IAAI,IAAI,EAAE;MACrB,OAAOxH,IAAI;IACZ;IACA,IAAIyH,OAAO,GAAGzH,IAAI,CAACwD,SAAS;EAC7B,CAAC,MAAI;IACJgE,QAAQ,GAAGC,OAAO,GAAGzH,IAAI;EAC1B;EACA,IAAI0H,GAAG,GAAG9C,KAAK,GAAGA,KAAK,CAACnB,eAAe,GAAGuD,MAAM,CAACxD,SAAS;EAE1DgE,QAAQ,CAAC/D,eAAe,GAAGiE,GAAG;EAC9BD,OAAO,CAAC/D,WAAW,GAAGkB,KAAK;EAG3B,IAAG8C,GAAG,EAAC;IACNA,GAAG,CAAChE,WAAW,GAAG8D,QAAQ;EAC3B,CAAC,MAAI;IACJR,MAAM,CAACzD,UAAU,GAAGiE,QAAQ;EAC7B;EACA,IAAG5C,KAAK,IAAI,IAAI,EAAC;IAChBoC,MAAM,CAACxD,SAAS,GAAGiE,OAAO;EAC3B,CAAC,MAAI;IACJ7C,KAAK,CAACnB,eAAe,GAAGgE,OAAO;EAChC;EACA,GAAE;IACDD,QAAQ,CAAC5D,UAAU,GAAGoD,MAAM;EAC7B,CAAC,QAAMQ,QAAQ,KAAKC,OAAO,KAAKD,QAAQ,GAAEA,QAAQ,CAAC9D,WAAW,CAAC;EAC/DwC,cAAc,CAACc,MAAM,CAACzG,aAAa,IAAEyG,MAAM,EAAEA,MAAM,CAAC;EACpD;EACA,IAAIhH,IAAI,CAAC8E,QAAQ,IAAIpH,sBAAsB,EAAE;IAC5CsC,IAAI,CAACuD,UAAU,GAAGvD,IAAI,CAACwD,SAAS,GAAG,IAAI;EACxC;EACA,OAAOxD,IAAI;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2H,kBAAkBA,CAAE/D,UAAU,EAAEI,QAAQ,EAAE;EAClD,IAAIA,QAAQ,CAACJ,UAAU,EAAE;IACxBI,QAAQ,CAACJ,UAAU,CAACU,WAAW,CAACN,QAAQ,CAAC;EAC1C;EACAA,QAAQ,CAACJ,UAAU,GAAGA,UAAU;EAChCI,QAAQ,CAACP,eAAe,GAAGG,UAAU,CAACJ,SAAS;EAC/CQ,QAAQ,CAACN,WAAW,GAAG,IAAI;EAC3B,IAAIM,QAAQ,CAACP,eAAe,EAAE;IAC7BO,QAAQ,CAACP,eAAe,CAACC,WAAW,GAAGM,QAAQ;EAChD,CAAC,MAAM;IACNJ,UAAU,CAACL,UAAU,GAAGS,QAAQ;EACjC;EACAJ,UAAU,CAACJ,SAAS,GAAGQ,QAAQ;EAC/BkC,cAAc,CAACtC,UAAU,CAACrD,aAAa,EAAEqD,UAAU,EAAEI,QAAQ,CAAC;EAC9D,OAAOA,QAAQ;AAChB;AAEArB,QAAQ,CAACtG,SAAS,GAAG;EACpB;EACAsF,QAAQ,EAAI,WAAW;EACvBmD,QAAQ,EAAItH,aAAa;EACzB;AACD;AACA;AACA;AACA;AACA;EACCkF,OAAO,EAAI,IAAI;EACfkF,eAAe,EAAI,IAAI;EACvBtH,IAAI,EAAG,CAAC;EAERyD,YAAY,EAAI,SAAhBA,YAAYA,CAAaC,QAAQ,EAAEC,QAAQ,EAAC;IAAC;IAC5C,IAAGD,QAAQ,CAACc,QAAQ,IAAIpH,sBAAsB,EAAC;MAC9C,IAAIkH,KAAK,GAAGZ,QAAQ,CAACT,UAAU;MAC/B,OAAMqB,KAAK,EAAC;QACX,IAAIC,IAAI,GAAGD,KAAK,CAAClB,WAAW;QAC5B,IAAI,CAACK,YAAY,CAACa,KAAK,EAACX,QAAQ,CAAC;QACjCW,KAAK,GAAGC,IAAI;MACb;MACA,OAAOb,QAAQ;IAChB;IACAE,aAAa,CAAC,IAAI,EAAEF,QAAQ,EAAEC,QAAQ,CAAC;IACvCD,QAAQ,CAACzD,aAAa,GAAG,IAAI;IAC7B,IAAI,IAAI,CAACqH,eAAe,KAAK,IAAI,IAAI5D,QAAQ,CAACc,QAAQ,KAAK9H,YAAY,EAAE;MACxE,IAAI,CAAC4K,eAAe,GAAG5D,QAAQ;IAChC;IAEA,OAAOA,QAAQ;EAChB,CAAC;EACDM,WAAW,EAAI,SAAfA,WAAWA,CAAaF,QAAQ,EAAC;IAChC,IAAG,IAAI,CAACwD,eAAe,IAAIxD,QAAQ,EAAC;MACnC,IAAI,CAACwD,eAAe,GAAG,IAAI;IAC5B;IACA,OAAOrD,YAAY,CAAC,IAAI,EAACH,QAAQ,CAAC;EACnC,CAAC;EACDD,YAAY,EAAE,SAAdA,YAAYA,CAAYH,QAAQ,EAAEI,QAAQ,EAAE;IAC3C;IACAF,aAAa,CAAC,IAAI,EAAEF,QAAQ,EAAEI,QAAQ,EAAEC,sCAAsC,CAAC;IAC/EL,QAAQ,CAACzD,aAAa,GAAG,IAAI;IAC7B,IAAI6D,QAAQ,EAAE;MACb,IAAI,CAACE,WAAW,CAACF,QAAQ,CAAC;IAC3B;IACA,IAAImC,aAAa,CAACvC,QAAQ,CAAC,EAAE;MAC5B,IAAI,CAAC4D,eAAe,GAAG5D,QAAQ;IAChC;EACD,CAAC;EACD;EACA6D,UAAU,EAAG,SAAbA,UAAUA,CAAYC,YAAY,EAACpD,IAAI,EAAC;IACvC,OAAOmD,WAAU,CAAC,IAAI,EAACC,YAAY,EAACpD,IAAI,CAAC;EAC1C,CAAC;EACD;EACAqD,cAAc,EAAG,SAAjBA,cAAcA,CAAYC,EAAE,EAAC;IAC5B,IAAIC,GAAG,GAAG,IAAI;IACdrC,UAAU,CAAC,IAAI,CAACgC,eAAe,EAAC,UAAS5H,IAAI,EAAC;MAC7C,IAAGA,IAAI,CAAC8E,QAAQ,IAAI9H,YAAY,EAAC;QAChC,IAAGgD,IAAI,CAACkI,YAAY,CAAC,IAAI,CAAC,IAAIF,EAAE,EAAC;UAChCC,GAAG,GAAGjI,IAAI;UACV,OAAO,IAAI;QACZ;MACD;IACD,CAAC,CAAC;IACF,OAAOiI,GAAG;EACX,CAAC;EAED;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACCE,sBAAsB,EAAE,SAAxBA,sBAAsBA,CAAWC,UAAU,EAAE;IAC5C,IAAIC,aAAa,GAAG3M,YAAY,CAAC0M,UAAU,CAAC;IAC5C,OAAO,IAAIrI,YAAY,CAAC,IAAI,EAAE,UAASuI,IAAI,EAAE;MAC5C,IAAI9H,EAAE,GAAG,EAAE;MACX,IAAI6H,aAAa,CAAClJ,MAAM,GAAG,CAAC,EAAE;QAC7ByG,UAAU,CAAC0C,IAAI,CAACV,eAAe,EAAE,UAAS5H,IAAI,EAAE;UAC/C,IAAGA,IAAI,KAAKsI,IAAI,IAAItI,IAAI,CAAC8E,QAAQ,KAAK9H,YAAY,EAAE;YACnD,IAAIuL,cAAc,GAAGvI,IAAI,CAACkI,YAAY,CAAC,OAAO,CAAC;YAC/C;YACA,IAAIK,cAAc,EAAE;cACnB;cACA,IAAIC,OAAO,GAAGJ,UAAU,KAAKG,cAAc;cAC3C,IAAI,CAACC,OAAO,EAAE;gBACb,IAAIC,iBAAiB,GAAG/M,YAAY,CAAC6M,cAAc,CAAC;gBACpDC,OAAO,GAAGH,aAAa,CAACK,KAAK,CAAC3M,aAAa,CAAC0M,iBAAiB,CAAC,CAAC;cAChE;cACA,IAAGD,OAAO,EAAE;gBACXhI,EAAE,CAACmI,IAAI,CAAC3I,IAAI,CAAC;cACd;YACD;UACD;QACD,CAAC,CAAC;MACH;MACA,OAAOQ,EAAE;IACV,CAAC,CAAC;EACH,CAAC;EAED;EACAoI,aAAa,EAAG,SAAhBA,aAAaA,CAAYpH,OAAO,EAAC;IAChC,IAAIxB,IAAI,GAAG,IAAI6I,OAAO,CAAC,CAAC;IACxB7I,IAAI,CAACO,aAAa,GAAG,IAAI;IACzBP,IAAI,CAAC2B,QAAQ,GAAGH,OAAO;IACvBxB,IAAI,CAACwB,OAAO,GAAGA,OAAO;IACtBxB,IAAI,CAACiC,SAAS,GAAGT,OAAO;IACxBxB,IAAI,CAAC6C,UAAU,GAAG,IAAI3D,QAAQ,CAAC,CAAC;IAChC,IAAI4J,KAAK,GAAG9I,IAAI,CAAC2D,UAAU,GAAG,IAAIhD,YAAY,CAAC,CAAC;IAChDmI,KAAK,CAACjH,aAAa,GAAG7B,IAAI;IAC1B,OAAOA,IAAI;EACZ,CAAC;EACD+I,sBAAsB,EAAG,SAAzBA,sBAAsBA,CAAA,EAAa;IAClC,IAAI/I,IAAI,GAAG,IAAIgJ,gBAAgB,CAAC,CAAC;IACjChJ,IAAI,CAACO,aAAa,GAAG,IAAI;IACzBP,IAAI,CAAC6C,UAAU,GAAG,IAAI3D,QAAQ,CAAC,CAAC;IAChC,OAAOc,IAAI;EACZ,CAAC;EACDiJ,cAAc,EAAG,SAAjBA,cAAcA,CAAYjE,IAAI,EAAC;IAC9B,IAAIhF,IAAI,GAAG,IAAIkJ,IAAI,CAAC,CAAC;IACrBlJ,IAAI,CAACO,aAAa,GAAG,IAAI;IACzBP,IAAI,CAAC+E,UAAU,CAACC,IAAI,CAAC;IACrB,OAAOhF,IAAI;EACZ,CAAC;EACDmJ,aAAa,EAAG,SAAhBA,aAAaA,CAAYnE,IAAI,EAAC;IAC7B,IAAIhF,IAAI,GAAG,IAAIoJ,OAAO,CAAC,CAAC;IACxBpJ,IAAI,CAACO,aAAa,GAAG,IAAI;IACzBP,IAAI,CAAC+E,UAAU,CAACC,IAAI,CAAC;IACrB,OAAOhF,IAAI;EACZ,CAAC;EACDqJ,kBAAkB,EAAG,SAArBA,kBAAkBA,CAAYrE,IAAI,EAAC;IAClC,IAAIhF,IAAI,GAAG,IAAIsJ,YAAY,CAAC,CAAC;IAC7BtJ,IAAI,CAACO,aAAa,GAAG,IAAI;IACzBP,IAAI,CAAC+E,UAAU,CAACC,IAAI,CAAC;IACrB,OAAOhF,IAAI;EACZ,CAAC;EACDuJ,2BAA2B,EAAG,SAA9BA,2BAA2BA,CAAYC,MAAM,EAACxE,IAAI,EAAC;IAClD,IAAIhF,IAAI,GAAG,IAAIyJ,qBAAqB,CAAC,CAAC;IACtCzJ,IAAI,CAACO,aAAa,GAAG,IAAI;IACzBP,IAAI,CAACwB,OAAO,GAAGxB,IAAI,CAAC2B,QAAQ,GAAG3B,IAAI,CAACwJ,MAAM,GAAGA,MAAM;IACnDxJ,IAAI,CAAC6D,SAAS,GAAG7D,IAAI,CAACgF,IAAI,GAAGA,IAAI;IACjC,OAAOhF,IAAI;EACZ,CAAC;EACD0J,eAAe,EAAG,SAAlBA,eAAeA,CAAYrG,IAAI,EAAC;IAC/B,IAAIrD,IAAI,GAAG,IAAI2J,IAAI,CAAC,CAAC;IACrB3J,IAAI,CAACO,aAAa,GAAG,IAAI;IACzBP,IAAI,CAACqD,IAAI,GAAGA,IAAI;IAChBrD,IAAI,CAAC2B,QAAQ,GAAG0B,IAAI;IACpBrD,IAAI,CAACiC,SAAS,GAAGoB,IAAI;IACrBrD,IAAI,CAAC4J,SAAS,GAAG,IAAI;IACrB,OAAO5J,IAAI;EACZ,CAAC;EACD6J,qBAAqB,EAAG,SAAxBA,qBAAqBA,CAAYxG,IAAI,EAAC;IACrC,IAAIrD,IAAI,GAAG,IAAI8J,eAAe,CAAC,CAAC;IAChC9J,IAAI,CAACO,aAAa,GAAG,IAAI;IACzBP,IAAI,CAAC2B,QAAQ,GAAG0B,IAAI;IACpB,OAAOrD,IAAI;EACZ,CAAC;EACD;EACAgD,eAAe,EAAG,SAAlBA,eAAeA,CAAYhB,YAAY,EAACS,aAAa,EAAC;IACrD,IAAIzC,IAAI,GAAG,IAAI6I,OAAO,CAAC,CAAC;IACxB,IAAIkB,EAAE,GAAGtH,aAAa,CAACrH,KAAK,CAAC,GAAG,CAAC;IACjC,IAAI0N,KAAK,GAAG9I,IAAI,CAAC2D,UAAU,GAAG,IAAIhD,YAAY,CAAC,CAAC;IAChDX,IAAI,CAAC6C,UAAU,GAAG,IAAI3D,QAAQ,CAAC,CAAC;IAChCc,IAAI,CAACO,aAAa,GAAG,IAAI;IACzBP,IAAI,CAAC2B,QAAQ,GAAGc,aAAa;IAC7BzC,IAAI,CAACwB,OAAO,GAAGiB,aAAa;IAC5BzC,IAAI,CAACgC,YAAY,GAAGA,YAAY;IAChC,IAAG+H,EAAE,CAAC5K,MAAM,IAAI,CAAC,EAAC;MACjBa,IAAI,CAAC8D,MAAM,GAAGiG,EAAE,CAAC,CAAC,CAAC;MACnB/J,IAAI,CAACiC,SAAS,GAAG8H,EAAE,CAAC,CAAC,CAAC;IACvB,CAAC,MAAI;MACJ;MACA/J,IAAI,CAACiC,SAAS,GAAGQ,aAAa;IAC/B;IACAqG,KAAK,CAACjH,aAAa,GAAG7B,IAAI;IAC1B,OAAOA,IAAI;EACZ,CAAC;EACD;EACAgK,iBAAiB,EAAG,SAApBA,iBAAiBA,CAAYhI,YAAY,EAACS,aAAa,EAAC;IACvD,IAAIzC,IAAI,GAAG,IAAI2J,IAAI,CAAC,CAAC;IACrB,IAAII,EAAE,GAAGtH,aAAa,CAACrH,KAAK,CAAC,GAAG,CAAC;IACjC4E,IAAI,CAACO,aAAa,GAAG,IAAI;IACzBP,IAAI,CAAC2B,QAAQ,GAAGc,aAAa;IAC7BzC,IAAI,CAACqD,IAAI,GAAGZ,aAAa;IACzBzC,IAAI,CAACgC,YAAY,GAAGA,YAAY;IAChChC,IAAI,CAAC4J,SAAS,GAAG,IAAI;IACrB,IAAGG,EAAE,CAAC5K,MAAM,IAAI,CAAC,EAAC;MACjBa,IAAI,CAAC8D,MAAM,GAAGiG,EAAE,CAAC,CAAC,CAAC;MACnB/J,IAAI,CAACiC,SAAS,GAAG8H,EAAE,CAAC,CAAC,CAAC;IACvB,CAAC,MAAI;MACJ;MACA/J,IAAI,CAACiC,SAAS,GAAGQ,aAAa;IAC/B;IACA,OAAOzC,IAAI;EACZ;AACD,CAAC;AACDzD,QAAQ,CAACoG,QAAQ,EAACW,IAAI,CAAC;AAGvB,SAASuF,OAAOA,CAAA,EAAG;EAClB,IAAI,CAACxD,MAAM,GAAG,CAAC,CAAC;AACjB;AAAC;AACDwD,OAAO,CAACxM,SAAS,GAAG;EACnByI,QAAQ,EAAG9H,YAAY;EACvBiN,YAAY,EAAG,SAAfA,YAAYA,CAAY5G,IAAI,EAAC;IAC5B,OAAO,IAAI,CAAC6G,gBAAgB,CAAC7G,IAAI,CAAC,IAAE,IAAI;EACzC,CAAC;EACD6E,YAAY,EAAG,SAAfA,YAAYA,CAAY7E,IAAI,EAAC;IAC5B,IAAI/B,IAAI,GAAG,IAAI,CAAC4I,gBAAgB,CAAC7G,IAAI,CAAC;IACtC,OAAO/B,IAAI,IAAIA,IAAI,CAAC0E,KAAK,IAAI,EAAE;EAChC,CAAC;EACDkE,gBAAgB,EAAG,SAAnBA,gBAAgBA,CAAY7G,IAAI,EAAC;IAChC,OAAO,IAAI,CAACM,UAAU,CAAClC,YAAY,CAAC4B,IAAI,CAAC;EAC1C,CAAC;EACD8G,YAAY,EAAG,SAAfA,YAAYA,CAAY9G,IAAI,EAAE2C,KAAK,EAAC;IACnC,IAAI1E,IAAI,GAAG,IAAI,CAACf,aAAa,CAACmJ,eAAe,CAACrG,IAAI,CAAC;IACnD/B,IAAI,CAAC0E,KAAK,GAAG1E,IAAI,CAACuC,SAAS,GAAG,EAAE,GAAGmC,KAAK;IACxC,IAAI,CAACoE,gBAAgB,CAAC9I,IAAI,CAAC;EAC5B,CAAC;EACD+I,eAAe,EAAG,SAAlBA,eAAeA,CAAYhH,IAAI,EAAC;IAC/B,IAAI/B,IAAI,GAAG,IAAI,CAAC4I,gBAAgB,CAAC7G,IAAI,CAAC;IACtC/B,IAAI,IAAI,IAAI,CAACgJ,mBAAmB,CAAChJ,IAAI,CAAC;EACvC,CAAC;EAED;EACAwB,WAAW,EAAC,SAAZA,WAAWA,CAAUkB,QAAQ,EAAC;IAC7B,IAAGA,QAAQ,CAACc,QAAQ,KAAKpH,sBAAsB,EAAC;MAC/C,OAAO,IAAI,CAACqG,YAAY,CAACC,QAAQ,EAAC,IAAI,CAAC;IACxC,CAAC,MAAI;MACJ,OAAO2D,kBAAkB,CAAC,IAAI,EAAC3D,QAAQ,CAAC;IACzC;EACD,CAAC;EACDoG,gBAAgB,EAAG,SAAnBA,gBAAgBA,CAAYrJ,OAAO,EAAC;IACnC,OAAO,IAAI,CAAC4C,UAAU,CAAC/B,YAAY,CAACb,OAAO,CAAC;EAC7C,CAAC;EACDwJ,kBAAkB,EAAG,SAArBA,kBAAkBA,CAAYxJ,OAAO,EAAC;IACrC,OAAO,IAAI,CAAC4C,UAAU,CAAC7B,cAAc,CAACf,OAAO,CAAC;EAC/C,CAAC;EACDuJ,mBAAmB,EAAG,SAAtBA,mBAAmBA,CAAYtJ,OAAO,EAAC;IACtC;IACA,OAAO,IAAI,CAAC2C,UAAU,CAACzB,eAAe,CAAClB,OAAO,CAACW,QAAQ,CAAC;EACzD,CAAC;EACD;EACA6I,iBAAiB,EAAG,SAApBA,iBAAiBA,CAAYxI,YAAY,EAAEC,SAAS,EAAC;IACpD,IAAIwI,GAAG,GAAG,IAAI,CAACC,kBAAkB,CAAC1I,YAAY,EAAEC,SAAS,CAAC;IAC1DwI,GAAG,IAAI,IAAI,CAACH,mBAAmB,CAACG,GAAG,CAAC;EACrC,CAAC;EAEDE,cAAc,EAAG,SAAjBA,cAAcA,CAAY3I,YAAY,EAAEC,SAAS,EAAC;IACjD,OAAO,IAAI,CAACyI,kBAAkB,CAAC1I,YAAY,EAAEC,SAAS,CAAC,IAAE,IAAI;EAC9D,CAAC;EACD2I,cAAc,EAAG,SAAjBA,cAAcA,CAAY5I,YAAY,EAAEC,SAAS,EAAC;IACjD,IAAIX,IAAI,GAAG,IAAI,CAACoJ,kBAAkB,CAAC1I,YAAY,EAAEC,SAAS,CAAC;IAC3D,OAAOX,IAAI,IAAIA,IAAI,CAAC0E,KAAK,IAAI,EAAE;EAChC,CAAC;EACD6E,cAAc,EAAG,SAAjBA,cAAcA,CAAY7I,YAAY,EAAES,aAAa,EAAEuD,KAAK,EAAC;IAC5D,IAAI1E,IAAI,GAAG,IAAI,CAACf,aAAa,CAACyJ,iBAAiB,CAAChI,YAAY,EAAES,aAAa,CAAC;IAC5EnB,IAAI,CAAC0E,KAAK,GAAG1E,IAAI,CAACuC,SAAS,GAAG,EAAE,GAAGmC,KAAK;IACxC,IAAI,CAACoE,gBAAgB,CAAC9I,IAAI,CAAC;EAC5B,CAAC;EACDoJ,kBAAkB,EAAG,SAArBA,kBAAkBA,CAAY1I,YAAY,EAAEC,SAAS,EAAC;IACrD,OAAO,IAAI,CAAC0B,UAAU,CAAC5B,cAAc,CAACC,YAAY,EAAEC,SAAS,CAAC;EAC/D,CAAC;EAED6I,oBAAoB,EAAG,SAAvBA,oBAAoBA,CAAYtJ,OAAO,EAAC;IACvC,OAAO,IAAIzB,YAAY,CAAC,IAAI,EAAC,UAASuI,IAAI,EAAC;MAC1C,IAAI9H,EAAE,GAAG,EAAE;MACXoF,UAAU,CAAC0C,IAAI,EAAC,UAAStI,IAAI,EAAC;QAC7B,IAAGA,IAAI,KAAKsI,IAAI,IAAItI,IAAI,CAAC8E,QAAQ,IAAI9H,YAAY,KAAKwE,OAAO,KAAK,GAAG,IAAIxB,IAAI,CAACwB,OAAO,IAAIA,OAAO,CAAC,EAAC;UACjGhB,EAAE,CAACmI,IAAI,CAAC3I,IAAI,CAAC;QACd;MACD,CAAC,CAAC;MACF,OAAOQ,EAAE;IACV,CAAC,CAAC;EACH,CAAC;EACDuK,sBAAsB,EAAG,SAAzBA,sBAAsBA,CAAY/I,YAAY,EAAEC,SAAS,EAAC;IACzD,OAAO,IAAIlC,YAAY,CAAC,IAAI,EAAC,UAASuI,IAAI,EAAC;MAC1C,IAAI9H,EAAE,GAAG,EAAE;MACXoF,UAAU,CAAC0C,IAAI,EAAC,UAAStI,IAAI,EAAC;QAC7B,IAAGA,IAAI,KAAKsI,IAAI,IAAItI,IAAI,CAAC8E,QAAQ,KAAK9H,YAAY,KAAKgF,YAAY,KAAK,GAAG,IAAIhC,IAAI,CAACgC,YAAY,KAAKA,YAAY,CAAC,KAAKC,SAAS,KAAK,GAAG,IAAIjC,IAAI,CAACiC,SAAS,IAAIA,SAAS,CAAC,EAAC;UACxKzB,EAAE,CAACmI,IAAI,CAAC3I,IAAI,CAAC;QACd;MACD,CAAC,CAAC;MACF,OAAOQ,EAAE;IAEV,CAAC,CAAC;EACH;AACD,CAAC;AACDmC,QAAQ,CAACtG,SAAS,CAACyO,oBAAoB,GAAGjC,OAAO,CAACxM,SAAS,CAACyO,oBAAoB;AAChFnI,QAAQ,CAACtG,SAAS,CAAC0O,sBAAsB,GAAGlC,OAAO,CAACxM,SAAS,CAAC0O,sBAAsB;AAGpFxO,QAAQ,CAACsM,OAAO,EAACvF,IAAI,CAAC;AACtB,SAASqG,IAAIA,CAAA,EAAG,CAChB;AAAC;AACDA,IAAI,CAACtN,SAAS,CAACyI,QAAQ,GAAG7H,cAAc;AACxCV,QAAQ,CAACoN,IAAI,EAACrG,IAAI,CAAC;AAGnB,SAAS0H,aAAaA,CAAA,EAAG,CACzB;AAAC;AACDA,aAAa,CAAC3O,SAAS,GAAG;EACzB2I,IAAI,EAAG,EAAE;EACTiG,aAAa,EAAG,SAAhBA,aAAaA,CAAYC,MAAM,EAAEC,KAAK,EAAE;IACvC,OAAO,IAAI,CAACnG,IAAI,CAACoG,SAAS,CAACF,MAAM,EAAEA,MAAM,GAACC,KAAK,CAAC;EACjD,CAAC;EACDpG,UAAU,EAAE,SAAZA,UAAUA,CAAWsG,IAAI,EAAE;IAC1BA,IAAI,GAAG,IAAI,CAACrG,IAAI,GAACqG,IAAI;IACrB,IAAI,CAACxH,SAAS,GAAG,IAAI,CAACmB,IAAI,GAAGqG,IAAI;IACjC,IAAI,CAAClM,MAAM,GAAGkM,IAAI,CAAClM,MAAM;EAC1B,CAAC;EACDmM,UAAU,EAAE,SAAZA,UAAUA,CAAWJ,MAAM,EAACG,IAAI,EAAE;IACjC,IAAI,CAACE,WAAW,CAACL,MAAM,EAAC,CAAC,EAACG,IAAI,CAAC;EAEhC,CAAC;EACDvI,WAAW,EAAC,SAAZA,WAAWA,CAAUkB,QAAQ,EAAC;IAC7B,MAAM,IAAIhF,KAAK,CAACnB,gBAAgB,CAACG,qBAAqB,CAAC,CAAC;EACzD,CAAC;EACDwN,UAAU,EAAE,SAAZA,UAAUA,CAAWN,MAAM,EAAEC,KAAK,EAAE;IACnC,IAAI,CAACI,WAAW,CAACL,MAAM,EAACC,KAAK,EAAC,EAAE,CAAC;EAClC,CAAC;EACDI,WAAW,EAAE,SAAbA,WAAWA,CAAWL,MAAM,EAAEC,KAAK,EAAEE,IAAI,EAAE;IAC1C,IAAII,KAAK,GAAG,IAAI,CAACzG,IAAI,CAACoG,SAAS,CAAC,CAAC,EAACF,MAAM,CAAC;IACzC,IAAIQ,GAAG,GAAG,IAAI,CAAC1G,IAAI,CAACoG,SAAS,CAACF,MAAM,GAACC,KAAK,CAAC;IAC3CE,IAAI,GAAGI,KAAK,GAAGJ,IAAI,GAAGK,GAAG;IACzB,IAAI,CAAC7H,SAAS,GAAG,IAAI,CAACmB,IAAI,GAAGqG,IAAI;IACjC,IAAI,CAAClM,MAAM,GAAGkM,IAAI,CAAClM,MAAM;EAC1B;AACD,CAAC;AACD5C,QAAQ,CAACyO,aAAa,EAAC1H,IAAI,CAAC;AAC5B,SAAS4F,IAAIA,CAAA,EAAG,CAChB;AAAC;AACDA,IAAI,CAAC7M,SAAS,GAAG;EAChBsF,QAAQ,EAAG,OAAO;EAClBmD,QAAQ,EAAG5H,SAAS;EACpByO,SAAS,EAAG,SAAZA,SAASA,CAAYT,MAAM,EAAE;IAC5B,IAAIG,IAAI,GAAG,IAAI,CAACrG,IAAI;IACpB,IAAI4G,OAAO,GAAGP,IAAI,CAACD,SAAS,CAACF,MAAM,CAAC;IACpCG,IAAI,GAAGA,IAAI,CAACD,SAAS,CAAC,CAAC,EAAEF,MAAM,CAAC;IAChC,IAAI,CAAClG,IAAI,GAAG,IAAI,CAACnB,SAAS,GAAGwH,IAAI;IACjC,IAAI,CAAClM,MAAM,GAAGkM,IAAI,CAAClM,MAAM;IACzB,IAAI0M,OAAO,GAAG,IAAI,CAACtL,aAAa,CAAC0I,cAAc,CAAC2C,OAAO,CAAC;IACxD,IAAG,IAAI,CAAChI,UAAU,EAAC;MAClB,IAAI,CAACA,UAAU,CAACG,YAAY,CAAC8H,OAAO,EAAE,IAAI,CAACnI,WAAW,CAAC;IACxD;IACA,OAAOmI,OAAO;EACf;AACD,CAAC;AACDtP,QAAQ,CAAC2M,IAAI,EAAC8B,aAAa,CAAC;AAC5B,SAAS5B,OAAOA,CAAA,EAAG,CACnB;AAAC;AACDA,OAAO,CAAC/M,SAAS,GAAG;EACnBsF,QAAQ,EAAG,UAAU;EACrBmD,QAAQ,EAAGvH;AACZ,CAAC;AACDhB,QAAQ,CAAC6M,OAAO,EAAC4B,aAAa,CAAC;AAE/B,SAAS1B,YAAYA,CAAA,EAAG,CACxB;AAAC;AACDA,YAAY,CAACjN,SAAS,GAAG;EACxBsF,QAAQ,EAAG,gBAAgB;EAC3BmD,QAAQ,EAAG3H;AACZ,CAAC;AACDZ,QAAQ,CAAC+M,YAAY,EAAC0B,aAAa,CAAC;AAGpC,SAAS5H,YAAYA,CAAA,EAAG,CACxB;AAAC;AACDA,YAAY,CAAC/G,SAAS,CAACyI,QAAQ,GAAGrH,kBAAkB;AACpDlB,QAAQ,CAAC6G,YAAY,EAACE,IAAI,CAAC;AAE3B,SAASwI,QAAQA,CAAA,EAAG,CACpB;AAAC;AACDA,QAAQ,CAACzP,SAAS,CAACyI,QAAQ,GAAGnH,aAAa;AAC3CpB,QAAQ,CAACuP,QAAQ,EAACxI,IAAI,CAAC;AAEvB,SAASyI,MAAMA,CAAA,EAAG,CAClB;AAAC;AACDA,MAAM,CAAC1P,SAAS,CAACyI,QAAQ,GAAGzH,WAAW;AACvCd,QAAQ,CAACwP,MAAM,EAACzI,IAAI,CAAC;AAErB,SAASwG,eAAeA,CAAA,EAAG,CAC3B;AAAC;AACDA,eAAe,CAACzN,SAAS,CAACyI,QAAQ,GAAG1H,qBAAqB;AAC1Db,QAAQ,CAACuN,eAAe,EAACxG,IAAI,CAAC;AAE9B,SAAS0F,gBAAgBA,CAAA,EAAG,CAC5B;AAAC;AACDA,gBAAgB,CAAC3M,SAAS,CAACsF,QAAQ,GAAG,oBAAoB;AAC1DqH,gBAAgB,CAAC3M,SAAS,CAACyI,QAAQ,GAAGpH,sBAAsB;AAC5DnB,QAAQ,CAACyM,gBAAgB,EAAC1F,IAAI,CAAC;AAG/B,SAASmG,qBAAqBA,CAAA,EAAG,CACjC;AACAA,qBAAqB,CAACpN,SAAS,CAACyI,QAAQ,GAAGxH,2BAA2B;AACtEf,QAAQ,CAACkN,qBAAqB,EAACnG,IAAI,CAAC;AACpC,SAAS0I,aAAaA,CAAA,EAAE,CAAC;AACzBA,aAAa,CAAC3P,SAAS,CAACsD,iBAAiB,GAAG,UAASK,IAAI,EAACiM,MAAM,EAACzM,UAAU,EAAC;EAC3E,OAAO0M,qBAAqB,CAAC5P,IAAI,CAAC0D,IAAI,EAACiM,MAAM,EAACzM,UAAU,CAAC;AAC1D,CAAC;AACD8D,IAAI,CAACjH,SAAS,CAACiD,QAAQ,GAAG4M,qBAAqB;AAC/C,SAASA,qBAAqBA,CAACD,MAAM,EAACzM,UAAU,EAAC;EAChD,IAAIC,GAAG,GAAG,EAAE;EACZ,IAAI0M,OAAO,GAAG,IAAI,CAACrH,QAAQ,IAAI,CAAC,IAAI,IAAI,CAAC8C,eAAe,IAAI,IAAI;EAChE,IAAI9D,MAAM,GAAGqI,OAAO,CAACrI,MAAM;EAC3B,IAAIsI,GAAG,GAAGD,OAAO,CAACnK,YAAY;EAE9B,IAAGoK,GAAG,IAAItI,MAAM,IAAI,IAAI,EAAC;IACxB;IACA,IAAIA,MAAM,GAAGqI,OAAO,CAAChH,YAAY,CAACiH,GAAG,CAAC;IACtC,IAAGtI,MAAM,IAAI,IAAI,EAAC;MACjB;MACA,IAAIuI,iBAAiB,GAAC,CACtB;QAACC,SAAS,EAACF,GAAG;QAACtI,MAAM,EAAC;MAAI;MAC1B;MAAA,CACC;IACF;EACD;EACAnE,iBAAiB,CAAC,IAAI,EAACF,GAAG,EAACwM,MAAM,EAACzM,UAAU,EAAC6M,iBAAiB,CAAC;EAC/D;EACA,OAAO5M,GAAG,CAACG,IAAI,CAAC,EAAE,CAAC;AACpB;AAEA,SAAS2M,mBAAmBA,CAACvM,IAAI,EAAET,MAAM,EAAE8M,iBAAiB,EAAE;EAC7D,IAAIvI,MAAM,GAAG9D,IAAI,CAAC8D,MAAM,IAAI,EAAE;EAC9B,IAAIsI,GAAG,GAAGpM,IAAI,CAACgC,YAAY;EAC3B;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI,CAACoK,GAAG,EAAE;IACT,OAAO,KAAK;EACb;EACA,IAAItI,MAAM,KAAK,KAAK,IAAIsI,GAAG,KAAKpR,SAAS,CAACwR,GAAG,IAAIJ,GAAG,KAAKpR,SAAS,CAAC+K,KAAK,EAAE;IACzE,OAAO,KAAK;EACb;EAEA,IAAIrG,CAAC,GAAG2M,iBAAiB,CAAClN,MAAM;EAChC,OAAOO,CAAC,EAAE,EAAE;IACX,IAAIoG,EAAE,GAAGuG,iBAAiB,CAAC3M,CAAC,CAAC;IAC7B;IACA,IAAIoG,EAAE,CAAChC,MAAM,KAAKA,MAAM,EAAE;MACzB,OAAOgC,EAAE,CAACwG,SAAS,KAAKF,GAAG;IAC5B;EACD;EACA,OAAO,IAAI;AACZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,sBAAsBA,CAAChN,GAAG,EAAEgD,aAAa,EAAEuD,KAAK,EAAE;EAC1DvG,GAAG,CAACkJ,IAAI,CAAC,GAAG,EAAElG,aAAa,EAAE,IAAI,EAAEuD,KAAK,CAAC0G,OAAO,CAAC,eAAe,EAAEjH,WAAW,CAAC,EAAE,GAAG,CAAC;AACrF;AAEA,SAAS9F,iBAAiBA,CAACK,IAAI,EAACP,GAAG,EAACF,MAAM,EAACC,UAAU,EAAC6M,iBAAiB,EAAC;EACvE,IAAI,CAACA,iBAAiB,EAAE;IACvBA,iBAAiB,GAAG,EAAE;EACvB;EAEA,IAAG7M,UAAU,EAAC;IACbQ,IAAI,GAAGR,UAAU,CAACQ,IAAI,CAAC;IACvB,IAAGA,IAAI,EAAC;MACP,IAAG,OAAOA,IAAI,IAAI,QAAQ,EAAC;QAC1BP,GAAG,CAACkJ,IAAI,CAAC3I,IAAI,CAAC;QACd;MACD;IACD,CAAC,MAAI;MACJ;IACD;IACA;EACD;EAEA,QAAOA,IAAI,CAAC8E,QAAQ;IACpB,KAAK9H,YAAY;MAChB,IAAI8L,KAAK,GAAG9I,IAAI,CAAC2D,UAAU;MAC3B,IAAIgJ,GAAG,GAAG7D,KAAK,CAAC3J,MAAM;MACtB,IAAIyF,KAAK,GAAG5E,IAAI,CAACuD,UAAU;MAC3B,IAAI5B,QAAQ,GAAG3B,IAAI,CAACwB,OAAO;MAE3BjC,MAAM,GAAGvE,SAAS,CAACuE,MAAM,CAACS,IAAI,CAACgC,YAAY,CAAC,IAAIzC,MAAM;MAEtD,IAAIqN,gBAAgB,GAAGjL,QAAQ;MAC/B,IAAI,CAACpC,MAAM,IAAI,CAACS,IAAI,CAAC8D,MAAM,IAAI9D,IAAI,CAACgC,YAAY,EAAE;QACjD,IAAI6K,SAAS;QACb;QACA,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGhE,KAAK,CAAC3J,MAAM,EAAE2N,EAAE,EAAE,EAAE;UACzC,IAAIhE,KAAK,CAAC1J,IAAI,CAAC0N,EAAE,CAAC,CAACzJ,IAAI,KAAK,OAAO,EAAE;YACpCwJ,SAAS,GAAG/D,KAAK,CAAC1J,IAAI,CAAC0N,EAAE,CAAC,CAAC9G,KAAK;YAChC;UACD;QACD;QACA,IAAI,CAAC6G,SAAS,EAAE;UACf;UACA,KAAK,IAAIE,GAAG,GAAGV,iBAAiB,CAAClN,MAAM,GAAG,CAAC,EAAE4N,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;YAC7D,IAAIT,SAAS,GAAGD,iBAAiB,CAACU,GAAG,CAAC;YACtC,IAAIT,SAAS,CAACxI,MAAM,KAAK,EAAE,IAAIwI,SAAS,CAACA,SAAS,KAAKtM,IAAI,CAACgC,YAAY,EAAE;cACzE6K,SAAS,GAAGP,SAAS,CAACA,SAAS;cAC/B;YACD;UACD;QACD;QACA,IAAIO,SAAS,KAAK7M,IAAI,CAACgC,YAAY,EAAE;UACpC,KAAK,IAAI+K,GAAG,GAAGV,iBAAiB,CAAClN,MAAM,GAAG,CAAC,EAAE4N,GAAG,IAAI,CAAC,EAAEA,GAAG,EAAE,EAAE;YAC7D,IAAIT,SAAS,GAAGD,iBAAiB,CAACU,GAAG,CAAC;YACtC,IAAIT,SAAS,CAACA,SAAS,KAAKtM,IAAI,CAACgC,YAAY,EAAE;cAC9C,IAAIsK,SAAS,CAACxI,MAAM,EAAE;gBACrB8I,gBAAgB,GAAGN,SAAS,CAACxI,MAAM,GAAG,GAAG,GAAGnC,QAAQ;cACrD;cACA;YACD;UACD;QACD;MACD;MAEAlC,GAAG,CAACkJ,IAAI,CAAC,GAAG,EAAEiE,gBAAgB,CAAC;MAE/B,KAAI,IAAIlN,CAAC,GAAC,CAAC,EAACA,CAAC,GAACiN,GAAG,EAACjN,CAAC,EAAE,EAAC;QACrB;QACA,IAAI4B,IAAI,GAAGwH,KAAK,CAAC1J,IAAI,CAACM,CAAC,CAAC;QACxB,IAAI4B,IAAI,CAACwC,MAAM,IAAI,OAAO,EAAE;UAC3BuI,iBAAiB,CAAC1D,IAAI,CAAC;YAAE7E,MAAM,EAAExC,IAAI,CAACW,SAAS;YAAEqK,SAAS,EAAEhL,IAAI,CAAC0E;UAAM,CAAC,CAAC;QAC1E,CAAC,MAAK,IAAG1E,IAAI,CAACK,QAAQ,IAAI,OAAO,EAAC;UACjC0K,iBAAiB,CAAC1D,IAAI,CAAC;YAAE7E,MAAM,EAAE,EAAE;YAAEwI,SAAS,EAAEhL,IAAI,CAAC0E;UAAM,CAAC,CAAC;QAC9D;MACD;MAEA,KAAI,IAAItG,CAAC,GAAC,CAAC,EAACA,CAAC,GAACiN,GAAG,EAACjN,CAAC,EAAE,EAAC;QACrB,IAAI4B,IAAI,GAAGwH,KAAK,CAAC1J,IAAI,CAACM,CAAC,CAAC;QACxB,IAAI6M,mBAAmB,CAACjL,IAAI,EAAC/B,MAAM,EAAE8M,iBAAiB,CAAC,EAAE;UACxD,IAAIvI,MAAM,GAAGxC,IAAI,CAACwC,MAAM,IAAE,EAAE;UAC5B,IAAIsI,GAAG,GAAG9K,IAAI,CAACU,YAAY;UAC3ByK,sBAAsB,CAAChN,GAAG,EAAEqE,MAAM,GAAG,QAAQ,GAAGA,MAAM,GAAG,OAAO,EAAEsI,GAAG,CAAC;UACtEC,iBAAiB,CAAC1D,IAAI,CAAC;YAAE7E,MAAM,EAAEA,MAAM;YAAEwI,SAAS,EAACF;UAAI,CAAC,CAAC;QAC1D;QACAzM,iBAAiB,CAAC2B,IAAI,EAAC7B,GAAG,EAACF,MAAM,EAACC,UAAU,EAAC6M,iBAAiB,CAAC;MAChE;;MAEA;MACA,IAAI1K,QAAQ,KAAKiL,gBAAgB,IAAIL,mBAAmB,CAACvM,IAAI,EAAET,MAAM,EAAE8M,iBAAiB,CAAC,EAAE;QAC1F,IAAIvI,MAAM,GAAG9D,IAAI,CAAC8D,MAAM,IAAE,EAAE;QAC5B,IAAIsI,GAAG,GAAGpM,IAAI,CAACgC,YAAY;QAC3ByK,sBAAsB,CAAChN,GAAG,EAAEqE,MAAM,GAAG,QAAQ,GAAGA,MAAM,GAAG,OAAO,EAAEsI,GAAG,CAAC;QACtEC,iBAAiB,CAAC1D,IAAI,CAAC;UAAE7E,MAAM,EAAEA,MAAM;UAAEwI,SAAS,EAACF;QAAI,CAAC,CAAC;MAC1D;MAEA,IAAGxH,KAAK,IAAIrF,MAAM,IAAI,CAAC,kCAAkC,CAACyN,IAAI,CAACrL,QAAQ,CAAC,EAAC;QACxElC,GAAG,CAACkJ,IAAI,CAAC,GAAG,CAAC;QACb;QACA,IAAGpJ,MAAM,IAAI,WAAW,CAACyN,IAAI,CAACrL,QAAQ,CAAC,EAAC;UACvC,OAAMiD,KAAK,EAAC;YACX,IAAGA,KAAK,CAACI,IAAI,EAAC;cACbvF,GAAG,CAACkJ,IAAI,CAAC/D,KAAK,CAACI,IAAI,CAAC;YACrB,CAAC,MAAI;cACJrF,iBAAiB,CAACiF,KAAK,EAAEnF,GAAG,EAAEF,MAAM,EAAEC,UAAU,EAAE6M,iBAAiB,CAACY,KAAK,CAAC,CAAC,CAAC;YAC7E;YACArI,KAAK,GAAGA,KAAK,CAAClB,WAAW;UAC1B;QACD,CAAC,MACD;UACC,OAAMkB,KAAK,EAAC;YACXjF,iBAAiB,CAACiF,KAAK,EAAEnF,GAAG,EAAEF,MAAM,EAAEC,UAAU,EAAE6M,iBAAiB,CAACY,KAAK,CAAC,CAAC,CAAC;YAC5ErI,KAAK,GAAGA,KAAK,CAAClB,WAAW;UAC1B;QACD;QACAjE,GAAG,CAACkJ,IAAI,CAAC,IAAI,EAACiE,gBAAgB,EAAC,GAAG,CAAC;MACpC,CAAC,MAAI;QACJnN,GAAG,CAACkJ,IAAI,CAAC,IAAI,CAAC;MACf;MACA;MACA;MACA;IACD,KAAKnL,aAAa;IAClB,KAAKE,sBAAsB;MAC1B,IAAIkH,KAAK,GAAG5E,IAAI,CAACuD,UAAU;MAC3B,OAAMqB,KAAK,EAAC;QACXjF,iBAAiB,CAACiF,KAAK,EAAEnF,GAAG,EAAEF,MAAM,EAAEC,UAAU,EAAE6M,iBAAiB,CAACY,KAAK,CAAC,CAAC,CAAC;QAC5ErI,KAAK,GAAGA,KAAK,CAAClB,WAAW;MAC1B;MACA;IACD,KAAKzG,cAAc;MAClB,OAAOwP,sBAAsB,CAAChN,GAAG,EAAEO,IAAI,CAACqD,IAAI,EAAErD,IAAI,CAACgG,KAAK,CAAC;IAC1D,KAAK9I,SAAS;MACb;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;MACE,OAAOuC,GAAG,CAACkJ,IAAI,CAAC3I,IAAI,CAACgF,IAAI,CACvB0H,OAAO,CAAC,QAAQ,EAACjH,WAAW,CAC9B,CAAC;IACF,KAAKtI,kBAAkB;MACtB,OAAOsC,GAAG,CAACkJ,IAAI,CAAE,WAAW,EAAC3I,IAAI,CAACgF,IAAI,EAAC,KAAK,CAAC;IAC9C,KAAKzH,YAAY;MAChB,OAAOkC,GAAG,CAACkJ,IAAI,CAAE,MAAM,EAAC3I,IAAI,CAACgF,IAAI,EAAC,KAAK,CAAC;IACzC,KAAKvH,kBAAkB;MACtB,IAAIyP,KAAK,GAAGlN,IAAI,CAACkD,QAAQ;MACzB,IAAIiK,KAAK,GAAGnN,IAAI,CAACmD,QAAQ;MACzB1D,GAAG,CAACkJ,IAAI,CAAC,YAAY,EAAC3I,IAAI,CAACqD,IAAI,CAAC;MAChC,IAAG6J,KAAK,EAAC;QACRzN,GAAG,CAACkJ,IAAI,CAAC,UAAU,EAAEuE,KAAK,CAAC;QAC3B,IAAIC,KAAK,IAAIA,KAAK,IAAE,GAAG,EAAE;UACxB1N,GAAG,CAACkJ,IAAI,CAAC,GAAG,EAAEwE,KAAK,CAAC;QACrB;QACA1N,GAAG,CAACkJ,IAAI,CAAC,GAAG,CAAC;MACd,CAAC,MAAK,IAAGwE,KAAK,IAAIA,KAAK,IAAE,GAAG,EAAC;QAC5B1N,GAAG,CAACkJ,IAAI,CAAC,UAAU,EAAEwE,KAAK,EAAE,GAAG,CAAC;MACjC,CAAC,MAAI;QACJ,IAAIC,GAAG,GAAGpN,IAAI,CAACqN,cAAc;QAC7B,IAAGD,GAAG,EAAC;UACN3N,GAAG,CAACkJ,IAAI,CAAC,IAAI,EAACyE,GAAG,EAAC,GAAG,CAAC;QACvB;QACA3N,GAAG,CAACkJ,IAAI,CAAC,GAAG,CAAC;MACd;MACA;IACD,KAAKrL,2BAA2B;MAC/B,OAAOmC,GAAG,CAACkJ,IAAI,CAAE,IAAI,EAAC3I,IAAI,CAACwJ,MAAM,EAAC,GAAG,EAACxJ,IAAI,CAACgF,IAAI,EAAC,IAAI,CAAC;IACtD,KAAK5H,qBAAqB;MACzB,OAAOqC,GAAG,CAACkJ,IAAI,CAAE,GAAG,EAAC3I,IAAI,CAAC2B,QAAQ,EAAC,GAAG,CAAC;IACxC;IACA;IACA;MACClC,GAAG,CAACkJ,IAAI,CAAC,IAAI,EAAC3I,IAAI,CAAC2B,QAAQ,CAAC;EAC7B;AACD;AACA,SAASkG,WAAUA,CAAC3G,GAAG,EAAClB,IAAI,EAAC0E,IAAI,EAAC;EACjC,IAAI4I,KAAK;EACT,QAAQtN,IAAI,CAAC8E,QAAQ;IACrB,KAAK9H,YAAY;MAChBsQ,KAAK,GAAGtN,IAAI,CAACyE,SAAS,CAAC,KAAK,CAAC;MAC7B6I,KAAK,CAAC/M,aAAa,GAAGW,GAAG;IACzB;IACA;IACA;IACC;IACD;IACD,KAAKxD,sBAAsB;MAC1B;IACD,KAAKT,cAAc;MAClByH,IAAI,GAAG,IAAI;MACX;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACA,IAAG,CAAC4I,KAAK,EAAC;IACTA,KAAK,GAAGtN,IAAI,CAACyE,SAAS,CAAC,KAAK,CAAC,CAAC;EAC/B;EACA6I,KAAK,CAAC/M,aAAa,GAAGW,GAAG;EACzBoM,KAAK,CAAC1J,UAAU,GAAG,IAAI;EACvB,IAAGc,IAAI,EAAC;IACP,IAAIE,KAAK,GAAG5E,IAAI,CAACuD,UAAU;IAC3B,OAAMqB,KAAK,EAAC;MACX0I,KAAK,CAACxK,WAAW,CAAC+E,WAAU,CAAC3G,GAAG,EAAC0D,KAAK,EAACF,IAAI,CAAC,CAAC;MAC7CE,KAAK,GAAGA,KAAK,CAAClB,WAAW;IAC1B;EACD;EACA,OAAO4J,KAAK;AACb;AACA;AACA;AACA;AACA,SAAS7I,UAASA,CAACvD,GAAG,EAAClB,IAAI,EAAC0E,IAAI,EAAC;EAChC,IAAI4I,KAAK,GAAG,IAAItN,IAAI,CAACpD,WAAW,CAAC,CAAC;EAClC,KAAK,IAAI0I,CAAC,IAAItF,IAAI,EAAE;IACnB,IAAIpE,MAAM,CAACS,SAAS,CAACZ,cAAc,CAACa,IAAI,CAAC0D,IAAI,EAAEsF,CAAC,CAAC,EAAE;MAClD,IAAIiI,CAAC,GAAGvN,IAAI,CAACsF,CAAC,CAAC;MACf,IAAI,OAAOiI,CAAC,IAAI,QAAQ,EAAE;QACzB,IAAIA,CAAC,IAAID,KAAK,CAAChI,CAAC,CAAC,EAAE;UAClBgI,KAAK,CAAChI,CAAC,CAAC,GAAGiI,CAAC;QACb;MACD;IACD;EACD;EACA,IAAGvN,IAAI,CAAC6C,UAAU,EAAC;IAClByK,KAAK,CAACzK,UAAU,GAAG,IAAI3D,QAAQ,CAAC,CAAC;EAClC;EACAoO,KAAK,CAAC/M,aAAa,GAAGW,GAAG;EACzB,QAAQoM,KAAK,CAACxI,QAAQ;IACtB,KAAK9H,YAAY;MAChB,IAAI8L,KAAK,GAAG9I,IAAI,CAAC2D,UAAU;MAC3B,IAAI6J,MAAM,GAAGF,KAAK,CAAC3J,UAAU,GAAG,IAAIhD,YAAY,CAAC,CAAC;MAClD,IAAIgM,GAAG,GAAG7D,KAAK,CAAC3J,MAAM;MACtBqO,MAAM,CAAC3L,aAAa,GAAGyL,KAAK;MAC5B,KAAI,IAAI5N,CAAC,GAAC,CAAC,EAACA,CAAC,GAACiN,GAAG,EAACjN,CAAC,EAAE,EAAC;QACrB4N,KAAK,CAAClD,gBAAgB,CAAC3F,UAAS,CAACvD,GAAG,EAAC4H,KAAK,CAAC1J,IAAI,CAACM,CAAC,CAAC,EAAC,IAAI,CAAC,CAAC;MAC1D;MACA;MAAM;IACP,KAAKzC,cAAc;MAClByH,IAAI,GAAG,IAAI;EACZ;EACA,IAAGA,IAAI,EAAC;IACP,IAAIE,KAAK,GAAG5E,IAAI,CAACuD,UAAU;IAC3B,OAAMqB,KAAK,EAAC;MACX0I,KAAK,CAACxK,WAAW,CAAC2B,UAAS,CAACvD,GAAG,EAAC0D,KAAK,EAACF,IAAI,CAAC,CAAC;MAC5CE,KAAK,GAAGA,KAAK,CAAClB,WAAW;IAC1B;EACD;EACA,OAAO4J,KAAK;AACb;AAEA,SAAS7M,OAAOA,CAACgN,MAAM,EAAC/L,GAAG,EAACsE,KAAK,EAAC;EACjCyH,MAAM,CAAC/L,GAAG,CAAC,GAAGsE,KAAK;AACpB;AACA;AACA,IAAG;EACF,IAAGpK,MAAM,CAAC8R,cAAc,EAAC;IAAA,IAiCfC,eAAc,GAAvB,SAASA,cAAcA,CAAC3N,IAAI,EAAC;MAC5B,QAAOA,IAAI,CAAC8E,QAAQ;QACpB,KAAK9H,YAAY;QACjB,KAAKU,sBAAsB;UAC1B,IAAI+B,GAAG,GAAG,EAAE;UACZO,IAAI,GAAGA,IAAI,CAACuD,UAAU;UACtB,OAAMvD,IAAI,EAAC;YACV,IAAGA,IAAI,CAAC8E,QAAQ,KAAG,CAAC,IAAI9E,IAAI,CAAC8E,QAAQ,KAAI,CAAC,EAAC;cAC1CrF,GAAG,CAACkJ,IAAI,CAACgF,eAAc,CAAC3N,IAAI,CAAC,CAAC;YAC/B;YACAA,IAAI,GAAGA,IAAI,CAAC0D,WAAW;UACxB;UACA,OAAOjE,GAAG,CAACG,IAAI,CAAC,EAAE,CAAC;QACpB;UACC,OAAOI,IAAI,CAAC6D,SAAS;MACtB;IACD,CAAC;IAhDDjI,MAAM,CAAC8R,cAAc,CAAC3N,YAAY,CAAC1D,SAAS,EAAC,QAAQ,EAAC;MACrDuR,GAAG,EAAC,SAAJA,GAAGA,CAAA,EAAW;QACbxN,eAAe,CAAC,IAAI,CAAC;QACrB,OAAO,IAAI,CAACM,QAAQ;MACrB;IACD,CAAC,CAAC;IAEF9E,MAAM,CAAC8R,cAAc,CAACpK,IAAI,CAACjH,SAAS,EAAC,aAAa,EAAC;MAClDuR,GAAG,EAAC,SAAJA,GAAGA,CAAA,EAAW;QACb,OAAOD,eAAc,CAAC,IAAI,CAAC;MAC5B,CAAC;MAEDE,GAAG,EAAC,SAAJA,GAAGA,CAAU7I,IAAI,EAAC;QACjB,QAAO,IAAI,CAACF,QAAQ;UACpB,KAAK9H,YAAY;UACjB,KAAKU,sBAAsB;YAC1B,OAAM,IAAI,CAAC6F,UAAU,EAAC;cACrB,IAAI,CAACe,WAAW,CAAC,IAAI,CAACf,UAAU,CAAC;YAClC;YACA,IAAGyB,IAAI,IAAI8I,MAAM,CAAC9I,IAAI,CAAC,EAAC;cACvB,IAAI,CAAClC,WAAW,CAAC,IAAI,CAACvC,aAAa,CAAC0I,cAAc,CAACjE,IAAI,CAAC,CAAC;YAC1D;YACA;UAED;YACC,IAAI,CAACA,IAAI,GAAGA,IAAI;YAChB,IAAI,CAACgB,KAAK,GAAGhB,IAAI;YACjB,IAAI,CAACnB,SAAS,GAAGmB,IAAI;QACtB;MACD;IACD,CAAC,CAAC;IAoBFvE,OAAO,GAAG,SAAVA,OAAOA,CAAYgN,MAAM,EAAC/L,GAAG,EAACsE,KAAK,EAAC;MACnC;MACAyH,MAAM,CAAC,IAAI,GAAC/L,GAAG,CAAC,GAAGsE,KAAK;IACzB,CAAC;EACF;AACD,CAAC,QAAM+H,CAAC,EAAC,CAAC;AAAA;;AAGV;AACCC,OAAO,CAAC5K,YAAY,GAAGA,YAAY;AACnC4K,OAAO,CAACnP,YAAY,GAAGA,YAAY;AACnCmP,OAAO,CAAC5L,iBAAiB,GAAGA,iBAAiB;AAC7C4L,OAAO,CAACnF,OAAO,GAAGA,OAAO;AACzBmF,OAAO,CAAC1K,IAAI,GAAGA,IAAI;AACnB0K,OAAO,CAAC9O,QAAQ,GAAGA,QAAQ;AAC3B8O,OAAO,CAAChC,aAAa,GAAGA,aAAa;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}