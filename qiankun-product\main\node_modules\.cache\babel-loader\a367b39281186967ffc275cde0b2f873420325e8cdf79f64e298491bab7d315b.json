{"ast": null, "code": "var baseGetTag = require('./_baseGetTag'),\n  getPrototype = require('./_getPrototype'),\n  isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar objectTag = '[object Object]';\n\n/** Used for built-in method references. */\nvar funcProto = Function.prototype,\n  objectProto = Object.prototype;\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/** Used to infer the `Object` constructor. */\nvar objectCtorString = funcToString.call(Object);\n\n/**\n * Checks if `value` is a plain object, that is, an object created by the\n * `Object` constructor or one with a `[[Prototype]]` of `null`.\n *\n * @static\n * @memberOf _\n * @since 0.8.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a plain object, else `false`.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n * }\n *\n * _.isPlainObject(new Foo);\n * // => false\n *\n * _.isPlainObject([1, 2, 3]);\n * // => false\n *\n * _.isPlainObject({ 'x': 0, 'y': 0 });\n * // => true\n *\n * _.isPlainObject(Object.create(null));\n * // => true\n */\nfunction isPlainObject(value) {\n  if (!isObjectLike(value) || baseGetTag(value) != objectTag) {\n    return false;\n  }\n  var proto = getPrototype(value);\n  if (proto === null) {\n    return true;\n  }\n  var Ctor = hasOwnProperty.call(proto, 'constructor') && proto.constructor;\n  return typeof Ctor == 'function' && Ctor instanceof Ctor && funcToString.call(Ctor) == objectCtorString;\n}\nmodule.exports = isPlainObject;", "map": {"version": 3, "names": ["baseGetTag", "require", "getPrototype", "isObjectLike", "objectTag", "funcProto", "Function", "prototype", "objectProto", "Object", "funcToString", "toString", "hasOwnProperty", "objectCtorString", "call", "isPlainObject", "value", "proto", "Ctor", "constructor", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isPlainObject.js"], "sourcesContent": ["var baseGetTag = require('./_baseGetTag'),\n    getPrototype = require('./_getPrototype'),\n    isObjectLike = require('./isObjectLike');\n\n/** `Object#toString` result references. */\nvar objectTag = '[object Object]';\n\n/** Used for built-in method references. */\nvar funcProto = Function.prototype,\n    objectProto = Object.prototype;\n\n/** Used to resolve the decompiled source of functions. */\nvar funcToString = funcProto.toString;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto.hasOwnProperty;\n\n/** Used to infer the `Object` constructor. */\nvar objectCtorString = funcToString.call(Object);\n\n/**\n * Checks if `value` is a plain object, that is, an object created by the\n * `Object` constructor or one with a `[[Prototype]]` of `null`.\n *\n * @static\n * @memberOf _\n * @since 0.8.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a plain object, else `false`.\n * @example\n *\n * function Foo() {\n *   this.a = 1;\n * }\n *\n * _.isPlainObject(new Foo);\n * // => false\n *\n * _.isPlainObject([1, 2, 3]);\n * // => false\n *\n * _.isPlainObject({ 'x': 0, 'y': 0 });\n * // => true\n *\n * _.isPlainObject(Object.create(null));\n * // => true\n */\nfunction isPlainObject(value) {\n  if (!isObjectLike(value) || baseGetTag(value) != objectTag) {\n    return false;\n  }\n  var proto = getPrototype(value);\n  if (proto === null) {\n    return true;\n  }\n  var Ctor = hasOwnProperty.call(proto, 'constructor') && proto.constructor;\n  return typeof Ctor == 'function' && Ctor instanceof Ctor &&\n    funcToString.call(Ctor) == objectCtorString;\n}\n\nmodule.exports = isPlainObject;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;EACrCC,YAAY,GAAGD,OAAO,CAAC,iBAAiB,CAAC;EACzCE,YAAY,GAAGF,OAAO,CAAC,gBAAgB,CAAC;;AAE5C;AACA,IAAIG,SAAS,GAAG,iBAAiB;;AAEjC;AACA,IAAIC,SAAS,GAAGC,QAAQ,CAACC,SAAS;EAC9BC,WAAW,GAAGC,MAAM,CAACF,SAAS;;AAElC;AACA,IAAIG,YAAY,GAAGL,SAAS,CAACM,QAAQ;;AAErC;AACA,IAAIC,cAAc,GAAGJ,WAAW,CAACI,cAAc;;AAE/C;AACA,IAAIC,gBAAgB,GAAGH,YAAY,CAACI,IAAI,CAACL,MAAM,CAAC;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,aAAaA,CAACC,KAAK,EAAE;EAC5B,IAAI,CAACb,YAAY,CAACa,KAAK,CAAC,IAAIhB,UAAU,CAACgB,KAAK,CAAC,IAAIZ,SAAS,EAAE;IAC1D,OAAO,KAAK;EACd;EACA,IAAIa,KAAK,GAAGf,YAAY,CAACc,KAAK,CAAC;EAC/B,IAAIC,KAAK,KAAK,IAAI,EAAE;IAClB,OAAO,IAAI;EACb;EACA,IAAIC,IAAI,GAAGN,cAAc,CAACE,IAAI,CAACG,KAAK,EAAE,aAAa,CAAC,IAAIA,KAAK,CAACE,WAAW;EACzE,OAAO,OAAOD,IAAI,IAAI,UAAU,IAAIA,IAAI,YAAYA,IAAI,IACtDR,YAAY,CAACI,IAAI,CAACI,IAAI,CAAC,IAAIL,gBAAgB;AAC/C;AAEAO,MAAM,CAACC,OAAO,GAAGN,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}