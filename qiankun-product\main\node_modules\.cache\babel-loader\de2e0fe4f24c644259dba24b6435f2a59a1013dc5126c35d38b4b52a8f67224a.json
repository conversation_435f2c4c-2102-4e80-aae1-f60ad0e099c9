{"ast": null, "code": "/*\nLanguage: Matlab\nAuthor: <PERSON> <bar<PERSON><PERSON><PERSON><PERSON>@gmail.com>\nContributors: <PERSON> <ni<PERSON><PERSON><PERSON>@ya.ru>, <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.mathworks.com/products/matlab.html\nCategory: scientific\n*/\n\n/*\n  Formal syntax is not published, helpful link:\n  https://github.com/kornilova-l/matlab-IntelliJ-plugin/blob/master/src/main/grammar/Matlab.bnf\n*/\nfunction matlab(hljs) {\n  var TRANSPOSE_RE = '(\\'|\\\\.\\')+';\n  var TRANSPOSE = {\n    relevance: 0,\n    contains: [{\n      begin: TRANSPOSE_RE\n    }]\n  };\n  return {\n    name: 'Matlab',\n    keywords: {\n      keyword: 'arguments break case catch classdef continue else elseif end enumeration events for function ' + 'global if methods otherwise parfor persistent properties return spmd switch try while',\n      built_in: 'sin sind sinh asin asind asinh cos cosd cosh acos acosd acosh tan tand tanh atan ' + 'atand atan2 atanh sec secd sech asec asecd asech csc cscd csch acsc acscd acsch cot ' + 'cotd coth acot acotd acoth hypot exp expm1 log log1p log10 log2 pow2 realpow reallog ' + 'realsqrt sqrt nthroot nextpow2 abs angle complex conj imag real unwrap isreal ' + 'cplxpair fix floor ceil round mod rem sign airy besselj bessely besselh besseli ' + 'besselk beta betainc betaln ellipj ellipke erf erfc erfcx erfinv expint gamma ' + 'gammainc gammaln psi legendre cross dot factor isprime primes gcd lcm rat rats perms ' + 'nchoosek factorial cart2sph cart2pol pol2cart sph2cart hsv2rgb rgb2hsv zeros ones ' + 'eye repmat rand randn linspace logspace freqspace meshgrid accumarray size length ' + 'ndims numel disp isempty isequal isequalwithequalnans cat reshape diag blkdiag tril ' + 'triu fliplr flipud flipdim rot90 find sub2ind ind2sub bsxfun ndgrid permute ipermute ' + 'shiftdim circshift squeeze isscalar isvector ans eps realmax realmin pi i|0 inf nan ' + 'isnan isinf isfinite j|0 why compan gallery hadamard hankel hilb invhilb magic pascal ' + 'rosser toeplitz vander wilkinson max min nanmax nanmin mean nanmean type table ' + 'readtable writetable sortrows sort figure plot plot3 scatter scatter3 cellfun ' + 'legend intersect ismember procrustes hold num2cell '\n    },\n    illegal: '(//|\"|#|/\\\\*|\\\\s+/\\\\w+)',\n    contains: [{\n      className: 'function',\n      beginKeywords: 'function',\n      end: '$',\n      contains: [hljs.UNDERSCORE_TITLE_MODE, {\n        className: 'params',\n        variants: [{\n          begin: '\\\\(',\n          end: '\\\\)'\n        }, {\n          begin: '\\\\[',\n          end: '\\\\]'\n        }]\n      }]\n    }, {\n      className: 'built_in',\n      begin: /true|false/,\n      relevance: 0,\n      starts: TRANSPOSE\n    }, {\n      begin: '[a-zA-Z][a-zA-Z_0-9]*' + TRANSPOSE_RE,\n      relevance: 0\n    }, {\n      className: 'number',\n      begin: hljs.C_NUMBER_RE,\n      relevance: 0,\n      starts: TRANSPOSE\n    }, {\n      className: 'string',\n      begin: '\\'',\n      end: '\\'',\n      contains: [{\n        begin: '\\'\\''\n      }]\n    }, {\n      begin: /\\]|\\}|\\)/,\n      relevance: 0,\n      starts: TRANSPOSE\n    }, {\n      className: 'string',\n      begin: '\"',\n      end: '\"',\n      contains: [{\n        begin: '\"\"'\n      }],\n      starts: TRANSPOSE\n    }, hljs.COMMENT('^\\\\s*%\\\\{\\\\s*$', '^\\\\s*%\\\\}\\\\s*$'), hljs.COMMENT('%', '$')]\n  };\n}\nexport { matlab as default };", "map": {"version": 3, "names": ["matlab", "hljs", "TRANSPOSE_RE", "TRANSPOSE", "relevance", "contains", "begin", "name", "keywords", "keyword", "built_in", "illegal", "className", "beginKeywords", "end", "UNDERSCORE_TITLE_MODE", "variants", "starts", "C_NUMBER_RE", "COMMENT", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/highlight.js@11.11.1/node_modules/highlight.js/es/languages/matlab.js"], "sourcesContent": ["/*\nLanguage: Matlab\nAuthor: <PERSON> <bar<PERSON><PERSON><PERSON><PERSON>@gmail.com>\nContributors: <PERSON> <ni<PERSON><PERSON><PERSON>@ya.ru>, <PERSON><PERSON> <<EMAIL>>\nWebsite: https://www.mathworks.com/products/matlab.html\nCategory: scientific\n*/\n\n/*\n  Formal syntax is not published, helpful link:\n  https://github.com/kornilova-l/matlab-IntelliJ-plugin/blob/master/src/main/grammar/Matlab.bnf\n*/\nfunction matlab(hljs) {\n  const TRANSPOSE_RE = '(\\'|\\\\.\\')+';\n  const TRANSPOSE = {\n    relevance: 0,\n    contains: [ { begin: TRANSPOSE_RE } ]\n  };\n\n  return {\n    name: 'Matlab',\n    keywords: {\n      keyword:\n        'arguments break case catch classdef continue else elseif end enumeration events for function '\n        + 'global if methods otherwise parfor persistent properties return spmd switch try while',\n      built_in:\n        'sin sind sinh asin asind asinh cos cosd cosh acos acosd acosh tan tand tanh atan '\n        + 'atand atan2 atanh sec secd sech asec asecd asech csc cscd csch acsc acscd acsch cot '\n        + 'cotd coth acot acotd acoth hypot exp expm1 log log1p log10 log2 pow2 realpow reallog '\n        + 'realsqrt sqrt nthroot nextpow2 abs angle complex conj imag real unwrap isreal '\n        + 'cplxpair fix floor ceil round mod rem sign airy besselj bessely besselh besseli '\n        + 'besselk beta betainc betaln ellipj ellipke erf erfc erfcx erfinv expint gamma '\n        + 'gammainc gammaln psi legendre cross dot factor isprime primes gcd lcm rat rats perms '\n        + 'nchoosek factorial cart2sph cart2pol pol2cart sph2cart hsv2rgb rgb2hsv zeros ones '\n        + 'eye repmat rand randn linspace logspace freqspace meshgrid accumarray size length '\n        + 'ndims numel disp isempty isequal isequalwithequalnans cat reshape diag blkdiag tril '\n        + 'triu fliplr flipud flipdim rot90 find sub2ind ind2sub bsxfun ndgrid permute ipermute '\n        + 'shiftdim circshift squeeze isscalar isvector ans eps realmax realmin pi i|0 inf nan '\n        + 'isnan isinf isfinite j|0 why compan gallery hadamard hankel hilb invhilb magic pascal '\n        + 'rosser toeplitz vander wilkinson max min nanmax nanmin mean nanmean type table '\n        + 'readtable writetable sortrows sort figure plot plot3 scatter scatter3 cellfun '\n        + 'legend intersect ismember procrustes hold num2cell '\n    },\n    illegal: '(//|\"|#|/\\\\*|\\\\s+/\\\\w+)',\n    contains: [\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: '$',\n        contains: [\n          hljs.UNDERSCORE_TITLE_MODE,\n          {\n            className: 'params',\n            variants: [\n              {\n                begin: '\\\\(',\n                end: '\\\\)'\n              },\n              {\n                begin: '\\\\[',\n                end: '\\\\]'\n              }\n            ]\n          }\n        ]\n      },\n      {\n        className: 'built_in',\n        begin: /true|false/,\n        relevance: 0,\n        starts: TRANSPOSE\n      },\n      {\n        begin: '[a-zA-Z][a-zA-Z_0-9]*' + TRANSPOSE_RE,\n        relevance: 0\n      },\n      {\n        className: 'number',\n        begin: hljs.C_NUMBER_RE,\n        relevance: 0,\n        starts: TRANSPOSE\n      },\n      {\n        className: 'string',\n        begin: '\\'',\n        end: '\\'',\n        contains: [ { begin: '\\'\\'' } ]\n      },\n      {\n        begin: /\\]|\\}|\\)/,\n        relevance: 0,\n        starts: TRANSPOSE\n      },\n      {\n        className: 'string',\n        begin: '\"',\n        end: '\"',\n        contains: [ { begin: '\"\"' } ],\n        starts: TRANSPOSE\n      },\n      hljs.COMMENT('^\\\\s*%\\\\{\\\\s*$', '^\\\\s*%\\\\}\\\\s*$'),\n      hljs.COMMENT('%', '$')\n    ]\n  };\n}\n\nexport { matlab as default };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA,SAASA,MAAMA,CAACC,IAAI,EAAE;EACpB,IAAMC,YAAY,GAAG,aAAa;EAClC,IAAMC,SAAS,GAAG;IAChBC,SAAS,EAAE,CAAC;IACZC,QAAQ,EAAE,CAAE;MAAEC,KAAK,EAAEJ;IAAa,CAAC;EACrC,CAAC;EAED,OAAO;IACLK,IAAI,EAAE,QAAQ;IACdC,QAAQ,EAAE;MACRC,OAAO,EACL,+FAA+F,GAC7F,uFAAuF;MAC3FC,QAAQ,EACN,mFAAmF,GACjF,sFAAsF,GACtF,uFAAuF,GACvF,gFAAgF,GAChF,kFAAkF,GAClF,gFAAgF,GAChF,uFAAuF,GACvF,oFAAoF,GACpF,oFAAoF,GACpF,sFAAsF,GACtF,uFAAuF,GACvF,sFAAsF,GACtF,wFAAwF,GACxF,iFAAiF,GACjF,gFAAgF,GAChF;IACN,CAAC;IACDC,OAAO,EAAE,yBAAyB;IAClCN,QAAQ,EAAE,CACR;MACEO,SAAS,EAAE,UAAU;MACrBC,aAAa,EAAE,UAAU;MACzBC,GAAG,EAAE,GAAG;MACRT,QAAQ,EAAE,CACRJ,IAAI,CAACc,qBAAqB,EAC1B;QACEH,SAAS,EAAE,QAAQ;QACnBI,QAAQ,EAAE,CACR;UACEV,KAAK,EAAE,KAAK;UACZQ,GAAG,EAAE;QACP,CAAC,EACD;UACER,KAAK,EAAE,KAAK;UACZQ,GAAG,EAAE;QACP,CAAC;MAEL,CAAC;IAEL,CAAC,EACD;MACEF,SAAS,EAAE,UAAU;MACrBN,KAAK,EAAE,YAAY;MACnBF,SAAS,EAAE,CAAC;MACZa,MAAM,EAAEd;IACV,CAAC,EACD;MACEG,KAAK,EAAE,uBAAuB,GAAGJ,YAAY;MAC7CE,SAAS,EAAE;IACb,CAAC,EACD;MACEQ,SAAS,EAAE,QAAQ;MACnBN,KAAK,EAAEL,IAAI,CAACiB,WAAW;MACvBd,SAAS,EAAE,CAAC;MACZa,MAAM,EAAEd;IACV,CAAC,EACD;MACES,SAAS,EAAE,QAAQ;MACnBN,KAAK,EAAE,IAAI;MACXQ,GAAG,EAAE,IAAI;MACTT,QAAQ,EAAE,CAAE;QAAEC,KAAK,EAAE;MAAO,CAAC;IAC/B,CAAC,EACD;MACEA,KAAK,EAAE,UAAU;MACjBF,SAAS,EAAE,CAAC;MACZa,MAAM,EAAEd;IACV,CAAC,EACD;MACES,SAAS,EAAE,QAAQ;MACnBN,KAAK,EAAE,GAAG;MACVQ,GAAG,EAAE,GAAG;MACRT,QAAQ,EAAE,CAAE;QAAEC,KAAK,EAAE;MAAK,CAAC,CAAE;MAC7BW,MAAM,EAAEd;IACV,CAAC,EACDF,IAAI,CAACkB,OAAO,CAAC,gBAAgB,EAAE,gBAAgB,CAAC,EAChDlB,IAAI,CAACkB,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;EAE1B,CAAC;AACH;AAEA,SAASnB,MAAM,IAAIoB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}