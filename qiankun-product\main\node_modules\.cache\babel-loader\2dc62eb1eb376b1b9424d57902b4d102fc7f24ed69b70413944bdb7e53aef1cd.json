{"ast": null, "code": "\"use strict\";\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nvar wrapper = require(\"../module-wrapper.js\");\nvar _require = require(\"../errors.js\"),\n  getScopeCompilationError = _require.getScopeCompilationError;\nvar _require2 = require(\"../doc-utils.js\"),\n  utf8ToWord = _require2.utf8ToWord,\n  hasCorruptCharacters = _require2.hasCorruptCharacters;\nvar _require3 = require(\"../errors.js\"),\n  getCorruptCharactersException = _require3.getCorruptCharactersException;\nvar _require4 = require(\"../content-types.js\"),\n  settingsContentType = _require4.settingsContentType,\n  coreContentType = _require4.coreContentType,\n  appContentType = _require4.appContentType,\n  customContentType = _require4.customContentType;\nvar ftprefix = {\n  docx: \"w\",\n  pptx: \"a\"\n};\nvar Render = /*#__PURE__*/function () {\n  function Render() {\n    _classCallCheck(this, Render);\n    this.name = \"Render\";\n    this.recordRun = false;\n    this.recordedRun = [];\n  }\n  return _createClass(Render, [{\n    key: \"optionsTransformer\",\n    value: function optionsTransformer(options, docxtemplater) {\n      this.parser = docxtemplater.parser;\n      this.fileType = docxtemplater.fileType;\n      return options;\n    }\n  }, {\n    key: \"set\",\n    value: function set(obj) {\n      if (obj.compiled) {\n        this.compiled = obj.compiled;\n      }\n      if (obj.data != null) {\n        this.data = obj.data;\n      }\n    }\n  }, {\n    key: \"getRenderedMap\",\n    value: function getRenderedMap(mapper) {\n      var _this = this;\n      return Object.keys(this.compiled).reduce(function (mapper, from) {\n        mapper[from] = {\n          from: from,\n          data: _this.data\n        };\n        return mapper;\n      }, mapper);\n    }\n  }, {\n    key: \"postparse\",\n    value: function postparse(postparsed, options) {\n      var _this2 = this;\n      var errors = [];\n      postparsed.forEach(function (p) {\n        if (p.type === \"placeholder\") {\n          var tag = p.value;\n          try {\n            options.cachedParsers[p.lIndex] = _this2.parser(tag, {\n              tag: p\n            });\n          } catch (rootError) {\n            errors.push(getScopeCompilationError({\n              tag: tag,\n              rootError: rootError,\n              offset: p.offset\n            }));\n          }\n        }\n      });\n      return {\n        postparsed: postparsed,\n        errors: errors\n      };\n    }\n  }, {\n    key: \"render\",\n    value: function render(part, _ref) {\n      var contentType = _ref.contentType,\n        scopeManager = _ref.scopeManager,\n        linebreaks = _ref.linebreaks,\n        nullGetter = _ref.nullGetter,\n        fileType = _ref.fileType;\n      if (linebreaks && [settingsContentType, coreContentType, appContentType, customContentType].indexOf(contentType) !== -1) {\n        // Fixes issue tested in #docprops-linebreak\n        linebreaks = false;\n      }\n      if (linebreaks) {\n        this.recordRuns(part);\n      }\n      if (part.type !== \"placeholder\" || part.module) {\n        return;\n      }\n      var value;\n      try {\n        value = scopeManager.getValue(part.value, {\n          part: part\n        });\n      } catch (e) {\n        return {\n          errors: [e]\n        };\n      }\n      if (value == null) {\n        value = nullGetter(part);\n      }\n      if (hasCorruptCharacters(value)) {\n        return {\n          errors: [getCorruptCharactersException({\n            tag: part.value,\n            value: value,\n            offset: part.offset\n          })]\n        };\n      }\n      if (fileType === \"text\") {\n        return {\n          value: value\n        };\n      }\n      return {\n        value: linebreaks && typeof value === \"string\" ? this.renderLineBreaks(value) : utf8ToWord(value)\n      };\n    }\n  }, {\n    key: \"recordRuns\",\n    value: function recordRuns(part) {\n      if (part.tag === \"\".concat(ftprefix[this.fileType], \":r\")) {\n        this.recordedRun = [];\n      } else if (part.tag === \"\".concat(ftprefix[this.fileType], \":rPr\")) {\n        if (part.position === \"start\") {\n          this.recordRun = true;\n          this.recordedRun = [part.value];\n        }\n        if (part.position === \"end\" || part.position === \"selfclosing\") {\n          this.recordedRun.push(part.value);\n          this.recordRun = false;\n        }\n      } else if (this.recordRun) {\n        this.recordedRun.push(part.value);\n      }\n    }\n  }, {\n    key: \"renderLineBreaks\",\n    value: function renderLineBreaks(value) {\n      var _this3 = this;\n      var p = ftprefix[this.fileType];\n      var br = this.fileType === \"docx\" ? \"<w:r><w:br/></w:r>\" : \"<a:br/>\";\n      var lines = value.split(\"\\n\");\n      var runprops = this.recordedRun.join(\"\");\n      return lines.map(function (line) {\n        return utf8ToWord(line);\n      }).reduce(function (result, line, i) {\n        result.push(line);\n        if (i < lines.length - 1) {\n          result.push(\"</\".concat(p, \":t></\").concat(p, \":r>\").concat(br, \"<\").concat(p, \":r>\").concat(runprops, \"<\").concat(p, \":t\").concat(_this3.fileType === \"docx\" ? ' xml:space=\"preserve\"' : \"\", \">\"));\n        }\n        return result;\n      }, []);\n    }\n  }]);\n}();\nmodule.exports = function () {\n  return wrapper(new Render());\n};", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_classCallCheck", "a", "n", "TypeError", "_defineProperties", "e", "r", "t", "length", "enumerable", "configurable", "writable", "Object", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "i", "_toPrimitive", "toPrimitive", "call", "String", "Number", "wrapper", "require", "_require", "getScopeCompilationError", "_require2", "utf8ToWord", "hasCorruptCharacters", "_require3", "getCorruptCharactersException", "_require4", "settingsContentType", "coreContentType", "appContentType", "customContentType", "ftprefix", "docx", "pptx", "Render", "name", "recordRun", "recordedRun", "value", "optionsTransformer", "options", "docxtemplater", "parser", "fileType", "set", "obj", "compiled", "data", "getRenderedMap", "mapper", "_this", "keys", "reduce", "from", "postparse", "postparsed", "_this2", "errors", "for<PERSON>ach", "p", "type", "tag", "cachedParsers", "lIndex", "rootError", "push", "offset", "render", "part", "_ref", "contentType", "scopeManager", "linebreaks", "nullGetter", "indexOf", "recordRuns", "module", "getValue", "renderLineBreaks", "concat", "position", "_this3", "br", "lines", "split", "runprops", "join", "map", "line", "result", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/docxtemplater@3.52.0/node_modules/docxtemplater/js/modules/render.js"], "sourcesContent": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nvar wrapper = require(\"../module-wrapper.js\");\nvar _require = require(\"../errors.js\"),\n  getScopeCompilationError = _require.getScopeCompilationError;\nvar _require2 = require(\"../doc-utils.js\"),\n  utf8ToWord = _require2.utf8ToWord,\n  hasCorruptCharacters = _require2.hasCorruptCharacters;\nvar _require3 = require(\"../errors.js\"),\n  getCorruptCharactersException = _require3.getCorruptCharactersException;\nvar _require4 = require(\"../content-types.js\"),\n  settingsContentType = _require4.settingsContentType,\n  coreContentType = _require4.coreContentType,\n  appContentType = _require4.appContentType,\n  customContentType = _require4.customContentType;\nvar ftprefix = {\n  docx: \"w\",\n  pptx: \"a\"\n};\nvar Render = /*#__PURE__*/function () {\n  function Render() {\n    _classCallCheck(this, Render);\n    this.name = \"Render\";\n    this.recordRun = false;\n    this.recordedRun = [];\n  }\n  return _createClass(Render, [{\n    key: \"optionsTransformer\",\n    value: function optionsTransformer(options, docxtemplater) {\n      this.parser = docxtemplater.parser;\n      this.fileType = docxtemplater.fileType;\n      return options;\n    }\n  }, {\n    key: \"set\",\n    value: function set(obj) {\n      if (obj.compiled) {\n        this.compiled = obj.compiled;\n      }\n      if (obj.data != null) {\n        this.data = obj.data;\n      }\n    }\n  }, {\n    key: \"getRenderedMap\",\n    value: function getRenderedMap(mapper) {\n      var _this = this;\n      return Object.keys(this.compiled).reduce(function (mapper, from) {\n        mapper[from] = {\n          from: from,\n          data: _this.data\n        };\n        return mapper;\n      }, mapper);\n    }\n  }, {\n    key: \"postparse\",\n    value: function postparse(postparsed, options) {\n      var _this2 = this;\n      var errors = [];\n      postparsed.forEach(function (p) {\n        if (p.type === \"placeholder\") {\n          var tag = p.value;\n          try {\n            options.cachedParsers[p.lIndex] = _this2.parser(tag, {\n              tag: p\n            });\n          } catch (rootError) {\n            errors.push(getScopeCompilationError({\n              tag: tag,\n              rootError: rootError,\n              offset: p.offset\n            }));\n          }\n        }\n      });\n      return {\n        postparsed: postparsed,\n        errors: errors\n      };\n    }\n  }, {\n    key: \"render\",\n    value: function render(part, _ref) {\n      var contentType = _ref.contentType,\n        scopeManager = _ref.scopeManager,\n        linebreaks = _ref.linebreaks,\n        nullGetter = _ref.nullGetter,\n        fileType = _ref.fileType;\n      if (linebreaks && [settingsContentType, coreContentType, appContentType, customContentType].indexOf(contentType) !== -1) {\n        // Fixes issue tested in #docprops-linebreak\n        linebreaks = false;\n      }\n      if (linebreaks) {\n        this.recordRuns(part);\n      }\n      if (part.type !== \"placeholder\" || part.module) {\n        return;\n      }\n      var value;\n      try {\n        value = scopeManager.getValue(part.value, {\n          part: part\n        });\n      } catch (e) {\n        return {\n          errors: [e]\n        };\n      }\n      if (value == null) {\n        value = nullGetter(part);\n      }\n      if (hasCorruptCharacters(value)) {\n        return {\n          errors: [getCorruptCharactersException({\n            tag: part.value,\n            value: value,\n            offset: part.offset\n          })]\n        };\n      }\n      if (fileType === \"text\") {\n        return {\n          value: value\n        };\n      }\n      return {\n        value: linebreaks && typeof value === \"string\" ? this.renderLineBreaks(value) : utf8ToWord(value)\n      };\n    }\n  }, {\n    key: \"recordRuns\",\n    value: function recordRuns(part) {\n      if (part.tag === \"\".concat(ftprefix[this.fileType], \":r\")) {\n        this.recordedRun = [];\n      } else if (part.tag === \"\".concat(ftprefix[this.fileType], \":rPr\")) {\n        if (part.position === \"start\") {\n          this.recordRun = true;\n          this.recordedRun = [part.value];\n        }\n        if (part.position === \"end\" || part.position === \"selfclosing\") {\n          this.recordedRun.push(part.value);\n          this.recordRun = false;\n        }\n      } else if (this.recordRun) {\n        this.recordedRun.push(part.value);\n      }\n    }\n  }, {\n    key: \"renderLineBreaks\",\n    value: function renderLineBreaks(value) {\n      var _this3 = this;\n      var p = ftprefix[this.fileType];\n      var br = this.fileType === \"docx\" ? \"<w:r><w:br/></w:r>\" : \"<a:br/>\";\n      var lines = value.split(\"\\n\");\n      var runprops = this.recordedRun.join(\"\");\n      return lines.map(function (line) {\n        return utf8ToWord(line);\n      }).reduce(function (result, line, i) {\n        result.push(line);\n        if (i < lines.length - 1) {\n          result.push(\"</\".concat(p, \":t></\").concat(p, \":r>\").concat(br, \"<\").concat(p, \":r>\").concat(runprops, \"<\").concat(p, \":t\").concat(_this3.fileType === \"docx\" ? ' xml:space=\"preserve\"' : \"\", \">\"));\n        }\n        return result;\n      }, []);\n    }\n  }]);\n}();\nmodule.exports = function () {\n  return wrapper(new Render());\n};"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,EAAED,CAAC,YAAYC,CAAC,CAAC,EAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;AAAE;AAClH,SAASC,iBAAiBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIZ,CAAC,GAAGW,CAAC,CAACC,CAAC,CAAC;IAAEZ,CAAC,CAACc,UAAU,GAAGd,CAAC,CAACc,UAAU,IAAI,CAAC,CAAC,EAAEd,CAAC,CAACe,YAAY,GAAG,CAAC,CAAC,EAAE,OAAO,IAAIf,CAAC,KAAKA,CAAC,CAACgB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAEC,MAAM,CAACC,cAAc,CAACR,CAAC,EAAES,cAAc,CAACnB,CAAC,CAACoB,GAAG,CAAC,EAAEpB,CAAC,CAAC;EAAE;AAAE;AACvO,SAASqB,YAAYA,CAACX,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAOD,CAAC,IAAIF,iBAAiB,CAACC,CAAC,CAACN,SAAS,EAAEO,CAAC,CAAC,EAAEC,CAAC,IAAIH,iBAAiB,CAACC,CAAC,EAAEE,CAAC,CAAC,EAAEK,MAAM,CAACC,cAAc,CAACR,CAAC,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,EAAEN,CAAC;AAAE;AAC1K,SAASS,cAAcA,CAACP,CAAC,EAAE;EAAE,IAAIU,CAAC,GAAGC,YAAY,CAACX,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAIb,OAAO,CAACuB,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASC,YAAYA,CAACX,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIZ,OAAO,CAACa,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACX,MAAM,CAACuB,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKd,CAAC,EAAE;IAAE,IAAIY,CAAC,GAAGZ,CAAC,CAACe,IAAI,CAACb,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIZ,OAAO,CAACuB,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAId,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKG,CAAC,GAAGe,MAAM,GAAGC,MAAM,EAAEf,CAAC,CAAC;AAAE;AAC3T,IAAIgB,OAAO,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC7C,IAAIC,QAAQ,GAAGD,OAAO,CAAC,cAAc,CAAC;EACpCE,wBAAwB,GAAGD,QAAQ,CAACC,wBAAwB;AAC9D,IAAIC,SAAS,GAAGH,OAAO,CAAC,iBAAiB,CAAC;EACxCI,UAAU,GAAGD,SAAS,CAACC,UAAU;EACjCC,oBAAoB,GAAGF,SAAS,CAACE,oBAAoB;AACvD,IAAIC,SAAS,GAAGN,OAAO,CAAC,cAAc,CAAC;EACrCO,6BAA6B,GAAGD,SAAS,CAACC,6BAA6B;AACzE,IAAIC,SAAS,GAAGR,OAAO,CAAC,qBAAqB,CAAC;EAC5CS,mBAAmB,GAAGD,SAAS,CAACC,mBAAmB;EACnDC,eAAe,GAAGF,SAAS,CAACE,eAAe;EAC3CC,cAAc,GAAGH,SAAS,CAACG,cAAc;EACzCC,iBAAiB,GAAGJ,SAAS,CAACI,iBAAiB;AACjD,IAAIC,QAAQ,GAAG;EACbC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE;AACR,CAAC;AACD,IAAIC,MAAM,GAAG,aAAa,YAAY;EACpC,SAASA,MAAMA,CAAA,EAAG;IAChBxC,eAAe,CAAC,IAAI,EAAEwC,MAAM,CAAC;IAC7B,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,WAAW,GAAG,EAAE;EACvB;EACA,OAAO3B,YAAY,CAACwB,MAAM,EAAE,CAAC;IAC3BzB,GAAG,EAAE,oBAAoB;IACzB6B,KAAK,EAAE,SAASC,kBAAkBA,CAACC,OAAO,EAAEC,aAAa,EAAE;MACzD,IAAI,CAACC,MAAM,GAAGD,aAAa,CAACC,MAAM;MAClC,IAAI,CAACC,QAAQ,GAAGF,aAAa,CAACE,QAAQ;MACtC,OAAOH,OAAO;IAChB;EACF,CAAC,EAAE;IACD/B,GAAG,EAAE,KAAK;IACV6B,KAAK,EAAE,SAASM,GAAGA,CAACC,GAAG,EAAE;MACvB,IAAIA,GAAG,CAACC,QAAQ,EAAE;QAChB,IAAI,CAACA,QAAQ,GAAGD,GAAG,CAACC,QAAQ;MAC9B;MACA,IAAID,GAAG,CAACE,IAAI,IAAI,IAAI,EAAE;QACpB,IAAI,CAACA,IAAI,GAAGF,GAAG,CAACE,IAAI;MACtB;IACF;EACF,CAAC,EAAE;IACDtC,GAAG,EAAE,gBAAgB;IACrB6B,KAAK,EAAE,SAASU,cAAcA,CAACC,MAAM,EAAE;MACrC,IAAIC,KAAK,GAAG,IAAI;MAChB,OAAO5C,MAAM,CAAC6C,IAAI,CAAC,IAAI,CAACL,QAAQ,CAAC,CAACM,MAAM,CAAC,UAAUH,MAAM,EAAEI,IAAI,EAAE;QAC/DJ,MAAM,CAACI,IAAI,CAAC,GAAG;UACbA,IAAI,EAAEA,IAAI;UACVN,IAAI,EAAEG,KAAK,CAACH;QACd,CAAC;QACD,OAAOE,MAAM;MACf,CAAC,EAAEA,MAAM,CAAC;IACZ;EACF,CAAC,EAAE;IACDxC,GAAG,EAAE,WAAW;IAChB6B,KAAK,EAAE,SAASgB,SAASA,CAACC,UAAU,EAAEf,OAAO,EAAE;MAC7C,IAAIgB,MAAM,GAAG,IAAI;MACjB,IAAIC,MAAM,GAAG,EAAE;MACfF,UAAU,CAACG,OAAO,CAAC,UAAUC,CAAC,EAAE;QAC9B,IAAIA,CAAC,CAACC,IAAI,KAAK,aAAa,EAAE;UAC5B,IAAIC,GAAG,GAAGF,CAAC,CAACrB,KAAK;UACjB,IAAI;YACFE,OAAO,CAACsB,aAAa,CAACH,CAAC,CAACI,MAAM,CAAC,GAAGP,MAAM,CAACd,MAAM,CAACmB,GAAG,EAAE;cACnDA,GAAG,EAAEF;YACP,CAAC,CAAC;UACJ,CAAC,CAAC,OAAOK,SAAS,EAAE;YAClBP,MAAM,CAACQ,IAAI,CAAC7C,wBAAwB,CAAC;cACnCyC,GAAG,EAAEA,GAAG;cACRG,SAAS,EAAEA,SAAS;cACpBE,MAAM,EAAEP,CAAC,CAACO;YACZ,CAAC,CAAC,CAAC;UACL;QACF;MACF,CAAC,CAAC;MACF,OAAO;QACLX,UAAU,EAAEA,UAAU;QACtBE,MAAM,EAAEA;MACV,CAAC;IACH;EACF,CAAC,EAAE;IACDhD,GAAG,EAAE,QAAQ;IACb6B,KAAK,EAAE,SAAS6B,MAAMA,CAACC,IAAI,EAAEC,IAAI,EAAE;MACjC,IAAIC,WAAW,GAAGD,IAAI,CAACC,WAAW;QAChCC,YAAY,GAAGF,IAAI,CAACE,YAAY;QAChCC,UAAU,GAAGH,IAAI,CAACG,UAAU;QAC5BC,UAAU,GAAGJ,IAAI,CAACI,UAAU;QAC5B9B,QAAQ,GAAG0B,IAAI,CAAC1B,QAAQ;MAC1B,IAAI6B,UAAU,IAAI,CAAC7C,mBAAmB,EAAEC,eAAe,EAAEC,cAAc,EAAEC,iBAAiB,CAAC,CAAC4C,OAAO,CAACJ,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;QACvH;QACAE,UAAU,GAAG,KAAK;MACpB;MACA,IAAIA,UAAU,EAAE;QACd,IAAI,CAACG,UAAU,CAACP,IAAI,CAAC;MACvB;MACA,IAAIA,IAAI,CAACR,IAAI,KAAK,aAAa,IAAIQ,IAAI,CAACQ,MAAM,EAAE;QAC9C;MACF;MACA,IAAItC,KAAK;MACT,IAAI;QACFA,KAAK,GAAGiC,YAAY,CAACM,QAAQ,CAACT,IAAI,CAAC9B,KAAK,EAAE;UACxC8B,IAAI,EAAEA;QACR,CAAC,CAAC;MACJ,CAAC,CAAC,OAAOrE,CAAC,EAAE;QACV,OAAO;UACL0D,MAAM,EAAE,CAAC1D,CAAC;QACZ,CAAC;MACH;MACA,IAAIuC,KAAK,IAAI,IAAI,EAAE;QACjBA,KAAK,GAAGmC,UAAU,CAACL,IAAI,CAAC;MAC1B;MACA,IAAI7C,oBAAoB,CAACe,KAAK,CAAC,EAAE;QAC/B,OAAO;UACLmB,MAAM,EAAE,CAAChC,6BAA6B,CAAC;YACrCoC,GAAG,EAAEO,IAAI,CAAC9B,KAAK;YACfA,KAAK,EAAEA,KAAK;YACZ4B,MAAM,EAAEE,IAAI,CAACF;UACf,CAAC,CAAC;QACJ,CAAC;MACH;MACA,IAAIvB,QAAQ,KAAK,MAAM,EAAE;QACvB,OAAO;UACLL,KAAK,EAAEA;QACT,CAAC;MACH;MACA,OAAO;QACLA,KAAK,EAAEkC,UAAU,IAAI,OAAOlC,KAAK,KAAK,QAAQ,GAAG,IAAI,CAACwC,gBAAgB,CAACxC,KAAK,CAAC,GAAGhB,UAAU,CAACgB,KAAK;MAClG,CAAC;IACH;EACF,CAAC,EAAE;IACD7B,GAAG,EAAE,YAAY;IACjB6B,KAAK,EAAE,SAASqC,UAAUA,CAACP,IAAI,EAAE;MAC/B,IAAIA,IAAI,CAACP,GAAG,KAAK,EAAE,CAACkB,MAAM,CAAChD,QAAQ,CAAC,IAAI,CAACY,QAAQ,CAAC,EAAE,IAAI,CAAC,EAAE;QACzD,IAAI,CAACN,WAAW,GAAG,EAAE;MACvB,CAAC,MAAM,IAAI+B,IAAI,CAACP,GAAG,KAAK,EAAE,CAACkB,MAAM,CAAChD,QAAQ,CAAC,IAAI,CAACY,QAAQ,CAAC,EAAE,MAAM,CAAC,EAAE;QAClE,IAAIyB,IAAI,CAACY,QAAQ,KAAK,OAAO,EAAE;UAC7B,IAAI,CAAC5C,SAAS,GAAG,IAAI;UACrB,IAAI,CAACC,WAAW,GAAG,CAAC+B,IAAI,CAAC9B,KAAK,CAAC;QACjC;QACA,IAAI8B,IAAI,CAACY,QAAQ,KAAK,KAAK,IAAIZ,IAAI,CAACY,QAAQ,KAAK,aAAa,EAAE;UAC9D,IAAI,CAAC3C,WAAW,CAAC4B,IAAI,CAACG,IAAI,CAAC9B,KAAK,CAAC;UACjC,IAAI,CAACF,SAAS,GAAG,KAAK;QACxB;MACF,CAAC,MAAM,IAAI,IAAI,CAACA,SAAS,EAAE;QACzB,IAAI,CAACC,WAAW,CAAC4B,IAAI,CAACG,IAAI,CAAC9B,KAAK,CAAC;MACnC;IACF;EACF,CAAC,EAAE;IACD7B,GAAG,EAAE,kBAAkB;IACvB6B,KAAK,EAAE,SAASwC,gBAAgBA,CAACxC,KAAK,EAAE;MACtC,IAAI2C,MAAM,GAAG,IAAI;MACjB,IAAItB,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAACY,QAAQ,CAAC;MAC/B,IAAIuC,EAAE,GAAG,IAAI,CAACvC,QAAQ,KAAK,MAAM,GAAG,oBAAoB,GAAG,SAAS;MACpE,IAAIwC,KAAK,GAAG7C,KAAK,CAAC8C,KAAK,CAAC,IAAI,CAAC;MAC7B,IAAIC,QAAQ,GAAG,IAAI,CAAChD,WAAW,CAACiD,IAAI,CAAC,EAAE,CAAC;MACxC,OAAOH,KAAK,CAACI,GAAG,CAAC,UAAUC,IAAI,EAAE;QAC/B,OAAOlE,UAAU,CAACkE,IAAI,CAAC;MACzB,CAAC,CAAC,CAACpC,MAAM,CAAC,UAAUqC,MAAM,EAAED,IAAI,EAAE7E,CAAC,EAAE;QACnC8E,MAAM,CAACxB,IAAI,CAACuB,IAAI,CAAC;QACjB,IAAI7E,CAAC,GAAGwE,KAAK,CAACjF,MAAM,GAAG,CAAC,EAAE;UACxBuF,MAAM,CAACxB,IAAI,CAAC,IAAI,CAACc,MAAM,CAACpB,CAAC,EAAE,OAAO,CAAC,CAACoB,MAAM,CAACpB,CAAC,EAAE,KAAK,CAAC,CAACoB,MAAM,CAACG,EAAE,EAAE,GAAG,CAAC,CAACH,MAAM,CAACpB,CAAC,EAAE,KAAK,CAAC,CAACoB,MAAM,CAACM,QAAQ,EAAE,GAAG,CAAC,CAACN,MAAM,CAACpB,CAAC,EAAE,IAAI,CAAC,CAACoB,MAAM,CAACE,MAAM,CAACtC,QAAQ,KAAK,MAAM,GAAG,uBAAuB,GAAG,EAAE,EAAE,GAAG,CAAC,CAAC;QACrM;QACA,OAAO8C,MAAM;MACf,CAAC,EAAE,EAAE,CAAC;IACR;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACHb,MAAM,CAACc,OAAO,GAAG,YAAY;EAC3B,OAAOzE,OAAO,CAAC,IAAIiB,MAAM,CAAC,CAAC,CAAC;AAC9B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}