{"ast": null, "code": "// browser shim for xmlhttprequest module\n\nvar hasCORS = require('has-cors');\nvar globalThis = require('../globalThis');\nmodule.exports = function (opts) {\n  var xdomain = opts.xdomain;\n\n  // scheme must be same when usign XDomainRequest\n  // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n  var xscheme = opts.xscheme;\n\n  // XDomainRequest has a flow of not sending cookie, therefore it should be disabled as a default.\n  // https://github.com/Automattic/engine.io-client/pull/217\n  var enablesXDR = opts.enablesXDR;\n\n  // XMLHttpRequest can be disabled on IE\n  try {\n    if ('undefined' !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n      return new XMLHttpRequest();\n    }\n  } catch (e) {}\n\n  // Use XDomainRequest for IE8 if enablesXDR is true\n  // because loading bar keeps flashing when using jsonp-polling\n  // https://github.com/yujiosaka/socke.io-ie8-loading-example\n  try {\n    if ('undefined' !== typeof XDomainRequest && !xscheme && enablesXDR) {\n      return new XDomainRequest();\n    }\n  } catch (e) {}\n  if (!xdomain) {\n    try {\n      return new globalThis[['Active'].concat('Object').join('X')]('Microsoft.XMLHTTP');\n    } catch (e) {}\n  }\n};", "map": {"version": 3, "names": ["hasCORS", "require", "globalThis", "module", "exports", "opts", "xdomain", "xscheme", "enablesXDR", "XMLHttpRequest", "e", "XDomainRequest", "concat", "join"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/engine.io-client@3.5.4/node_modules/engine.io-client/lib/transports/xmlhttprequest.browser.js"], "sourcesContent": ["// browser shim for xmlhttprequest module\n\nvar hasCORS = require('has-cors');\nvar globalThis = require('../globalThis');\n\nmodule.exports = function (opts) {\n  var xdomain = opts.xdomain;\n\n  // scheme must be same when usign XDomainRequest\n  // http://blogs.msdn.com/b/ieinternals/archive/2010/05/13/xdomainrequest-restrictions-limitations-and-workarounds.aspx\n  var xscheme = opts.xscheme;\n\n  // XDomainRequest has a flow of not sending cookie, therefore it should be disabled as a default.\n  // https://github.com/Automattic/engine.io-client/pull/217\n  var enablesXDR = opts.enablesXDR;\n\n  // XMLHttpRequest can be disabled on IE\n  try {\n    if ('undefined' !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n      return new XMLHttpRequest();\n    }\n  } catch (e) { }\n\n  // Use XDomainRequest for IE8 if enablesXDR is true\n  // because loading bar keeps flashing when using jsonp-polling\n  // https://github.com/yujiosaka/socke.io-ie8-loading-example\n  try {\n    if ('undefined' !== typeof XDomainRequest && !xscheme && enablesXDR) {\n      return new XDomainRequest();\n    }\n  } catch (e) { }\n\n  if (!xdomain) {\n    try {\n      return new globalThis[['Active'].concat('Object').join('X')]('Microsoft.XMLHTTP');\n    } catch (e) { }\n  }\n};\n"], "mappings": "AAAA;;AAEA,IAAIA,OAAO,GAAGC,OAAO,CAAC,UAAU,CAAC;AACjC,IAAIC,UAAU,GAAGD,OAAO,CAAC,eAAe,CAAC;AAEzCE,MAAM,CAACC,OAAO,GAAG,UAAUC,IAAI,EAAE;EAC/B,IAAIC,OAAO,GAAGD,IAAI,CAACC,OAAO;;EAE1B;EACA;EACA,IAAIC,OAAO,GAAGF,IAAI,CAACE,OAAO;;EAE1B;EACA;EACA,IAAIC,UAAU,GAAGH,IAAI,CAACG,UAAU;;EAEhC;EACA,IAAI;IACF,IAAI,WAAW,KAAK,OAAOC,cAAc,KAAK,CAACH,OAAO,IAAIN,OAAO,CAAC,EAAE;MAClE,OAAO,IAAIS,cAAc,CAAC,CAAC;IAC7B;EACF,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAE;;EAEd;EACA;EACA;EACA,IAAI;IACF,IAAI,WAAW,KAAK,OAAOC,cAAc,IAAI,CAACJ,OAAO,IAAIC,UAAU,EAAE;MACnE,OAAO,IAAIG,cAAc,CAAC,CAAC;IAC7B;EACF,CAAC,CAAC,OAAOD,CAAC,EAAE,CAAE;EAEd,IAAI,CAACJ,OAAO,EAAE;IACZ,IAAI;MACF,OAAO,IAAIJ,UAAU,CAAC,CAAC,QAAQ,CAAC,CAACU,MAAM,CAAC,QAAQ,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,mBAAmB,CAAC;IACnF,CAAC,CAAC,OAAOH,CAAC,EAAE,CAAE;EAChB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}