{"ast": null, "code": "/*\nLanguage: C\nCategory: common, system\nWebsite: https://en.wikipedia.org/wiki/C_(programming_language)\n*/\n\n/** @type LanguageFn */\nfunction c(hljs) {\n  var regex = hljs.regex;\n  // added for historic reasons because `hljs.C_LINE_COMMENT_MODE` does\n  // not include such support nor can we be sure all the grammars depending\n  // on it would desire this behavior\n  var C_LINE_COMMENT_MODE = hljs.COMMENT('//', '$', {\n    contains: [{\n      begin: /\\\\\\n/\n    }]\n  });\n  var DECLTYPE_AUTO_RE = 'decltype\\\\(auto\\\\)';\n  var NAMESPACE_RE = '[a-zA-Z_]\\\\w*::';\n  var TEMPLATE_ARGUMENT_RE = '<[^<>]+>';\n  var FUNCTION_TYPE_RE = '(' + DECLTYPE_AUTO_RE + '|' + regex.optional(NAMESPACE_RE) + '[a-zA-Z_]\\\\w*' + regex.optional(TEMPLATE_ARGUMENT_RE) + ')';\n  var TYPES = {\n    className: 'type',\n    variants: [{\n      begin: '\\\\b[a-z\\\\d_]*_t\\\\b'\n    }, {\n      match: /\\batomic_[a-z]{3,6}\\b/\n    }]\n  };\n\n  // https://en.cppreference.com/w/cpp/language/escape\n  // \\\\ \\x \\xFF \\u2837 \\u00323747 \\374\n  var CHARACTER_ESCAPES = '\\\\\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\\\S)';\n  var STRINGS = {\n    className: 'string',\n    variants: [{\n      begin: '(u8?|U|L)?\"',\n      end: '\"',\n      illegal: '\\\\n',\n      contains: [hljs.BACKSLASH_ESCAPE]\n    }, {\n      begin: '(u8?|U|L)?\\'(' + CHARACTER_ESCAPES + \"|.)\",\n      end: '\\'',\n      illegal: '.'\n    }, hljs.END_SAME_AS_BEGIN({\n      begin: /(?:u8?|U|L)?R\"([^()\\\\ ]{0,16})\\(/,\n      end: /\\)([^()\\\\ ]{0,16})\"/\n    })]\n  };\n  var NUMBERS = {\n    className: 'number',\n    variants: [{\n      match: /\\b(0b[01']+)/\n    }, {\n      match: /(-?)\\b([\\d']+(\\.[\\d']*)?|\\.[\\d']+)((ll|LL|l|L)(u|U)?|(u|U)(ll|LL|l|L)?|f|F|b|B)/\n    }, {\n      match: /(-?)\\b(0[xX][a-fA-F0-9]+(?:'[a-fA-F0-9]+)*(?:\\.[a-fA-F0-9]*(?:'[a-fA-F0-9]*)*)?(?:[pP][-+]?[0-9]+)?(l|L)?(u|U)?)/\n    }, {\n      match: /(-?)\\b\\d+(?:'\\d+)*(?:\\.\\d*(?:'\\d*)*)?(?:[eE][-+]?\\d+)?/\n    }],\n    relevance: 0\n  };\n  var PREPROCESSOR = {\n    className: 'meta',\n    begin: /#\\s*[a-z]+\\b/,\n    end: /$/,\n    keywords: {\n      keyword: 'if else elif endif define undef warning error line ' + 'pragma _Pragma ifdef ifndef elifdef elifndef include'\n    },\n    contains: [{\n      begin: /\\\\\\n/,\n      relevance: 0\n    }, hljs.inherit(STRINGS, {\n      className: 'string'\n    }), {\n      className: 'string',\n      begin: /<.*?>/\n    }, C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n  };\n  var TITLE_MODE = {\n    className: 'title',\n    begin: regex.optional(NAMESPACE_RE) + hljs.IDENT_RE,\n    relevance: 0\n  };\n  var FUNCTION_TITLE = regex.optional(NAMESPACE_RE) + hljs.IDENT_RE + '\\\\s*\\\\(';\n  var C_KEYWORDS = [\"asm\", \"auto\", \"break\", \"case\", \"continue\", \"default\", \"do\", \"else\", \"enum\", \"extern\", \"for\", \"fortran\", \"goto\", \"if\", \"inline\", \"register\", \"restrict\", \"return\", \"sizeof\", \"typeof\", \"typeof_unqual\", \"struct\", \"switch\", \"typedef\", \"union\", \"volatile\", \"while\", \"_Alignas\", \"_Alignof\", \"_Atomic\", \"_Generic\", \"_Noreturn\", \"_Static_assert\", \"_Thread_local\",\n  // aliases\n  \"alignas\", \"alignof\", \"noreturn\", \"static_assert\", \"thread_local\",\n  // not a C keyword but is, for all intents and purposes, treated exactly like one.\n  \"_Pragma\"];\n  var C_TYPES = [\"float\", \"double\", \"signed\", \"unsigned\", \"int\", \"short\", \"long\", \"char\", \"void\", \"_Bool\", \"_BitInt\", \"_Complex\", \"_Imaginary\", \"_Decimal32\", \"_Decimal64\", \"_Decimal96\", \"_Decimal128\", \"_Decimal64x\", \"_Decimal128x\", \"_Float16\", \"_Float32\", \"_Float64\", \"_Float128\", \"_Float32x\", \"_Float64x\", \"_Float128x\",\n  // modifiers\n  \"const\", \"static\", \"constexpr\",\n  // aliases\n  \"complex\", \"bool\", \"imaginary\"];\n  var KEYWORDS = {\n    keyword: C_KEYWORDS,\n    type: C_TYPES,\n    literal: 'true false NULL',\n    // TODO: apply hinting work similar to what was done in cpp.js\n    built_in: 'std string wstring cin cout cerr clog stdin stdout stderr stringstream istringstream ostringstream ' + 'auto_ptr deque list queue stack vector map set pair bitset multiset multimap unordered_set ' + 'unordered_map unordered_multiset unordered_multimap priority_queue make_pair array shared_ptr abort terminate abs acos ' + 'asin atan2 atan calloc ceil cosh cos exit exp fabs floor fmod fprintf fputs free frexp ' + 'fscanf future isalnum isalpha iscntrl isdigit isgraph islower isprint ispunct isspace isupper ' + 'isxdigit tolower toupper labs ldexp log10 log malloc realloc memchr memcmp memcpy memset modf pow ' + 'printf putchar puts scanf sinh sin snprintf sprintf sqrt sscanf strcat strchr strcmp ' + 'strcpy strcspn strlen strncat strncmp strncpy strpbrk strrchr strspn strstr tanh tan ' + 'vfprintf vprintf vsprintf endl initializer_list unique_ptr'\n  };\n  var EXPRESSION_CONTAINS = [PREPROCESSOR, TYPES, C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, NUMBERS, STRINGS];\n  var EXPRESSION_CONTEXT = {\n    // This mode covers expression context where we can't expect a function\n    // definition and shouldn't highlight anything that looks like one:\n    // `return some()`, `else if()`, `(x*sum(1, 2))`\n    variants: [{\n      begin: /=/,\n      end: /;/\n    }, {\n      begin: /\\(/,\n      end: /\\)/\n    }, {\n      beginKeywords: 'new throw return else',\n      end: /;/\n    }],\n    keywords: KEYWORDS,\n    contains: EXPRESSION_CONTAINS.concat([{\n      begin: /\\(/,\n      end: /\\)/,\n      keywords: KEYWORDS,\n      contains: EXPRESSION_CONTAINS.concat(['self']),\n      relevance: 0\n    }]),\n    relevance: 0\n  };\n  var FUNCTION_DECLARATION = {\n    begin: '(' + FUNCTION_TYPE_RE + '[\\\\*&\\\\s]+)+' + FUNCTION_TITLE,\n    returnBegin: true,\n    end: /[{;=]/,\n    excludeEnd: true,\n    keywords: KEYWORDS,\n    illegal: /[^\\w\\s\\*&:<>.]/,\n    contains: [{\n      // to prevent it from being confused as the function title\n      begin: DECLTYPE_AUTO_RE,\n      keywords: KEYWORDS,\n      relevance: 0\n    }, {\n      begin: FUNCTION_TITLE,\n      returnBegin: true,\n      contains: [hljs.inherit(TITLE_MODE, {\n        className: \"title.function\"\n      })],\n      relevance: 0\n    },\n    // allow for multiple declarations, e.g.:\n    // extern void f(int), g(char);\n    {\n      relevance: 0,\n      match: /,/\n    }, {\n      className: 'params',\n      begin: /\\(/,\n      end: /\\)/,\n      keywords: KEYWORDS,\n      relevance: 0,\n      contains: [C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, STRINGS, NUMBERS, TYPES,\n      // Count matching parentheses.\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: KEYWORDS,\n        relevance: 0,\n        contains: ['self', C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, STRINGS, NUMBERS, TYPES]\n      }]\n    }, TYPES, C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, PREPROCESSOR]\n  };\n  return {\n    name: \"C\",\n    aliases: ['h'],\n    keywords: KEYWORDS,\n    // Until differentiations are added between `c` and `cpp`, `c` will\n    // not be auto-detected to avoid auto-detect conflicts between C and C++\n    disableAutodetect: true,\n    illegal: '</',\n    contains: [].concat(EXPRESSION_CONTEXT, FUNCTION_DECLARATION, EXPRESSION_CONTAINS, [PREPROCESSOR, {\n      begin: hljs.IDENT_RE + '::',\n      keywords: KEYWORDS\n    }, {\n      className: 'class',\n      beginKeywords: 'enum class struct union',\n      end: /[{;:<>=]/,\n      contains: [{\n        beginKeywords: \"final class struct\"\n      }, hljs.TITLE_MODE]\n    }]),\n    exports: {\n      preprocessor: PREPROCESSOR,\n      strings: STRINGS,\n      keywords: KEYWORDS\n    }\n  };\n}\nexport { c as default };", "map": {"version": 3, "names": ["c", "hljs", "regex", "C_LINE_COMMENT_MODE", "COMMENT", "contains", "begin", "DECLTYPE_AUTO_RE", "NAMESPACE_RE", "TEMPLATE_ARGUMENT_RE", "FUNCTION_TYPE_RE", "optional", "TYPES", "className", "variants", "match", "CHARACTER_ESCAPES", "STRINGS", "end", "illegal", "BACKSLASH_ESCAPE", "END_SAME_AS_BEGIN", "NUMBERS", "relevance", "PREPROCESSOR", "keywords", "keyword", "inherit", "C_BLOCK_COMMENT_MODE", "TITLE_MODE", "IDENT_RE", "FUNCTION_TITLE", "C_KEYWORDS", "C_TYPES", "KEYWORDS", "type", "literal", "built_in", "EXPRESSION_CONTAINS", "EXPRESSION_CONTEXT", "beginKeywords", "concat", "FUNCTION_DECLARATION", "returnBegin", "excludeEnd", "name", "aliases", "disableAutodetect", "exports", "preprocessor", "strings", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/highlight.js@11.11.1/node_modules/highlight.js/es/languages/c.js"], "sourcesContent": ["/*\nLanguage: C\nCategory: common, system\nWebsite: https://en.wikipedia.org/wiki/C_(programming_language)\n*/\n\n/** @type LanguageFn */\nfunction c(hljs) {\n  const regex = hljs.regex;\n  // added for historic reasons because `hljs.C_LINE_COMMENT_MODE` does\n  // not include such support nor can we be sure all the grammars depending\n  // on it would desire this behavior\n  const C_LINE_COMMENT_MODE = hljs.COMMENT('//', '$', { contains: [ { begin: /\\\\\\n/ } ] });\n  const DECLTYPE_AUTO_RE = 'decltype\\\\(auto\\\\)';\n  const NAMESPACE_RE = '[a-zA-Z_]\\\\w*::';\n  const TEMPLATE_ARGUMENT_RE = '<[^<>]+>';\n  const FUNCTION_TYPE_RE = '('\n    + DECLTYPE_AUTO_RE + '|'\n    + regex.optional(NAMESPACE_RE)\n    + '[a-zA-Z_]\\\\w*' + regex.optional(TEMPLATE_ARGUMENT_RE)\n  + ')';\n\n\n  const TYPES = {\n    className: 'type',\n    variants: [\n      { begin: '\\\\b[a-z\\\\d_]*_t\\\\b' },\n      { match: /\\batomic_[a-z]{3,6}\\b/ }\n    ]\n\n  };\n\n  // https://en.cppreference.com/w/cpp/language/escape\n  // \\\\ \\x \\xFF \\u2837 \\u00323747 \\374\n  const CHARACTER_ESCAPES = '\\\\\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\\\S)';\n  const STRINGS = {\n    className: 'string',\n    variants: [\n      {\n        begin: '(u8?|U|L)?\"',\n        end: '\"',\n        illegal: '\\\\n',\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        begin: '(u8?|U|L)?\\'(' + CHARACTER_ESCAPES + \"|.)\",\n        end: '\\'',\n        illegal: '.'\n      },\n      hljs.END_SAME_AS_BEGIN({\n        begin: /(?:u8?|U|L)?R\"([^()\\\\ ]{0,16})\\(/,\n        end: /\\)([^()\\\\ ]{0,16})\"/\n      })\n    ]\n  };\n\n  const NUMBERS = {\n    className: 'number',\n    variants: [\n      { match: /\\b(0b[01']+)/ },  \n      { match: /(-?)\\b([\\d']+(\\.[\\d']*)?|\\.[\\d']+)((ll|LL|l|L)(u|U)?|(u|U)(ll|LL|l|L)?|f|F|b|B)/ },  \n      { match: /(-?)\\b(0[xX][a-fA-F0-9]+(?:'[a-fA-F0-9]+)*(?:\\.[a-fA-F0-9]*(?:'[a-fA-F0-9]*)*)?(?:[pP][-+]?[0-9]+)?(l|L)?(u|U)?)/ },  \n      { match: /(-?)\\b\\d+(?:'\\d+)*(?:\\.\\d*(?:'\\d*)*)?(?:[eE][-+]?\\d+)?/ }  \n  ],\n    relevance: 0\n  };  \n  \n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: /#\\s*[a-z]+\\b/,\n    end: /$/,\n    keywords: { keyword:\n        'if else elif endif define undef warning error line '\n        + 'pragma _Pragma ifdef ifndef elifdef elifndef include' },\n    contains: [\n      {\n        begin: /\\\\\\n/,\n        relevance: 0\n      },\n      hljs.inherit(STRINGS, { className: 'string' }),\n      {\n        className: 'string',\n        begin: /<.*?>/\n      },\n      C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ]\n  };\n\n  const TITLE_MODE = {\n    className: 'title',\n    begin: regex.optional(NAMESPACE_RE) + hljs.IDENT_RE,\n    relevance: 0\n  };\n\n  const FUNCTION_TITLE = regex.optional(NAMESPACE_RE) + hljs.IDENT_RE + '\\\\s*\\\\(';\n\n  const C_KEYWORDS = [\n    \"asm\",\n    \"auto\",\n    \"break\",\n    \"case\",\n    \"continue\",\n    \"default\",\n    \"do\",\n    \"else\",\n    \"enum\",\n    \"extern\",\n    \"for\",\n    \"fortran\",\n    \"goto\",\n    \"if\",\n    \"inline\",\n    \"register\",\n    \"restrict\",\n    \"return\",\n    \"sizeof\",\n    \"typeof\",\n    \"typeof_unqual\",\n    \"struct\",\n    \"switch\",\n    \"typedef\",\n    \"union\",\n    \"volatile\",\n    \"while\",\n    \"_Alignas\",\n    \"_Alignof\",\n    \"_Atomic\",\n    \"_Generic\",\n    \"_Noreturn\",\n    \"_Static_assert\",\n    \"_Thread_local\",\n    // aliases\n    \"alignas\",\n    \"alignof\",\n    \"noreturn\",\n    \"static_assert\",\n    \"thread_local\",\n    // not a C keyword but is, for all intents and purposes, treated exactly like one.\n    \"_Pragma\"\n  ];\n\n  const C_TYPES = [\n    \"float\",\n    \"double\",\n    \"signed\",\n    \"unsigned\",\n    \"int\",\n    \"short\",\n    \"long\",\n    \"char\",\n    \"void\",\n    \"_Bool\",\n    \"_BitInt\",\n    \"_Complex\",\n    \"_Imaginary\",\n    \"_Decimal32\",\n    \"_Decimal64\",\n    \"_Decimal96\",\n    \"_Decimal128\",\n    \"_Decimal64x\",\n    \"_Decimal128x\",\n    \"_Float16\",\n    \"_Float32\",\n    \"_Float64\",\n    \"_Float128\",\n    \"_Float32x\",\n    \"_Float64x\",\n    \"_Float128x\",\n    // modifiers\n    \"const\",\n    \"static\",\n    \"constexpr\",\n    // aliases\n    \"complex\",\n    \"bool\",\n    \"imaginary\"\n  ];\n\n  const KEYWORDS = {\n    keyword: C_KEYWORDS,\n    type: C_TYPES,\n    literal: 'true false NULL',\n    // TODO: apply hinting work similar to what was done in cpp.js\n    built_in: 'std string wstring cin cout cerr clog stdin stdout stderr stringstream istringstream ostringstream '\n      + 'auto_ptr deque list queue stack vector map set pair bitset multiset multimap unordered_set '\n      + 'unordered_map unordered_multiset unordered_multimap priority_queue make_pair array shared_ptr abort terminate abs acos '\n      + 'asin atan2 atan calloc ceil cosh cos exit exp fabs floor fmod fprintf fputs free frexp '\n      + 'fscanf future isalnum isalpha iscntrl isdigit isgraph islower isprint ispunct isspace isupper '\n      + 'isxdigit tolower toupper labs ldexp log10 log malloc realloc memchr memcmp memcpy memset modf pow '\n      + 'printf putchar puts scanf sinh sin snprintf sprintf sqrt sscanf strcat strchr strcmp '\n      + 'strcpy strcspn strlen strncat strncmp strncpy strpbrk strrchr strspn strstr tanh tan '\n      + 'vfprintf vprintf vsprintf endl initializer_list unique_ptr',\n  };\n\n  const EXPRESSION_CONTAINS = [\n    PREPROCESSOR,\n    TYPES,\n    C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    NUMBERS,\n    STRINGS\n  ];\n\n  const EXPRESSION_CONTEXT = {\n    // This mode covers expression context where we can't expect a function\n    // definition and shouldn't highlight anything that looks like one:\n    // `return some()`, `else if()`, `(x*sum(1, 2))`\n    variants: [\n      {\n        begin: /=/,\n        end: /;/\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/\n      },\n      {\n        beginKeywords: 'new throw return else',\n        end: /;/\n      }\n    ],\n    keywords: KEYWORDS,\n    contains: EXPRESSION_CONTAINS.concat([\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: KEYWORDS,\n        contains: EXPRESSION_CONTAINS.concat([ 'self' ]),\n        relevance: 0\n      }\n    ]),\n    relevance: 0\n  };\n\n  const FUNCTION_DECLARATION = {\n    begin: '(' + FUNCTION_TYPE_RE + '[\\\\*&\\\\s]+)+' + FUNCTION_TITLE,\n    returnBegin: true,\n    end: /[{;=]/,\n    excludeEnd: true,\n    keywords: KEYWORDS,\n    illegal: /[^\\w\\s\\*&:<>.]/,\n    contains: [\n      { // to prevent it from being confused as the function title\n        begin: DECLTYPE_AUTO_RE,\n        keywords: KEYWORDS,\n        relevance: 0\n      },\n      {\n        begin: FUNCTION_TITLE,\n        returnBegin: true,\n        contains: [ hljs.inherit(TITLE_MODE, { className: \"title.function\" }) ],\n        relevance: 0\n      },\n      // allow for multiple declarations, e.g.:\n      // extern void f(int), g(char);\n      {\n        relevance: 0,\n        match: /,/\n      },\n      {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: KEYWORDS,\n        relevance: 0,\n        contains: [\n          C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE,\n          STRINGS,\n          NUMBERS,\n          TYPES,\n          // Count matching parentheses.\n          {\n            begin: /\\(/,\n            end: /\\)/,\n            keywords: KEYWORDS,\n            relevance: 0,\n            contains: [\n              'self',\n              C_LINE_COMMENT_MODE,\n              hljs.C_BLOCK_COMMENT_MODE,\n              STRINGS,\n              NUMBERS,\n              TYPES\n            ]\n          }\n        ]\n      },\n      TYPES,\n      C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      PREPROCESSOR\n    ]\n  };\n\n  return {\n    name: \"C\",\n    aliases: [ 'h' ],\n    keywords: KEYWORDS,\n    // Until differentiations are added between `c` and `cpp`, `c` will\n    // not be auto-detected to avoid auto-detect conflicts between C and C++\n    disableAutodetect: true,\n    illegal: '</',\n    contains: [].concat(\n      EXPRESSION_CONTEXT,\n      FUNCTION_DECLARATION,\n      EXPRESSION_CONTAINS,\n      [\n        PREPROCESSOR,\n        {\n          begin: hljs.IDENT_RE + '::',\n          keywords: KEYWORDS\n        },\n        {\n          className: 'class',\n          beginKeywords: 'enum class struct union',\n          end: /[{;:<>=]/,\n          contains: [\n            { beginKeywords: \"final class struct\" },\n            hljs.TITLE_MODE\n          ]\n        }\n      ]),\n    exports: {\n      preprocessor: PREPROCESSOR,\n      strings: STRINGS,\n      keywords: KEYWORDS\n    }\n  };\n}\n\nexport { c as default };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,CAACA,CAACC,IAAI,EAAE;EACf,IAAMC,KAAK,GAAGD,IAAI,CAACC,KAAK;EACxB;EACA;EACA;EACA,IAAMC,mBAAmB,GAAGF,IAAI,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE;IAAEC,QAAQ,EAAE,CAAE;MAAEC,KAAK,EAAE;IAAO,CAAC;EAAG,CAAC,CAAC;EACxF,IAAMC,gBAAgB,GAAG,oBAAoB;EAC7C,IAAMC,YAAY,GAAG,iBAAiB;EACtC,IAAMC,oBAAoB,GAAG,UAAU;EACvC,IAAMC,gBAAgB,GAAG,GAAG,GACxBH,gBAAgB,GAAG,GAAG,GACtBL,KAAK,CAACS,QAAQ,CAACH,YAAY,CAAC,GAC5B,eAAe,GAAGN,KAAK,CAACS,QAAQ,CAACF,oBAAoB,CAAC,GACxD,GAAG;EAGL,IAAMG,KAAK,GAAG;IACZC,SAAS,EAAE,MAAM;IACjBC,QAAQ,EAAE,CACR;MAAER,KAAK,EAAE;IAAqB,CAAC,EAC/B;MAAES,KAAK,EAAE;IAAwB,CAAC;EAGtC,CAAC;;EAED;EACA;EACA,IAAMC,iBAAiB,GAAG,sDAAsD;EAChF,IAAMC,OAAO,GAAG;IACdJ,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,CACR;MACER,KAAK,EAAE,aAAa;MACpBY,GAAG,EAAE,GAAG;MACRC,OAAO,EAAE,KAAK;MACdd,QAAQ,EAAE,CAAEJ,IAAI,CAACmB,gBAAgB;IACnC,CAAC,EACD;MACEd,KAAK,EAAE,eAAe,GAAGU,iBAAiB,GAAG,KAAK;MAClDE,GAAG,EAAE,IAAI;MACTC,OAAO,EAAE;IACX,CAAC,EACDlB,IAAI,CAACoB,iBAAiB,CAAC;MACrBf,KAAK,EAAE,kCAAkC;MACzCY,GAAG,EAAE;IACP,CAAC,CAAC;EAEN,CAAC;EAED,IAAMI,OAAO,GAAG;IACdT,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,CACR;MAAEC,KAAK,EAAE;IAAe,CAAC,EACzB;MAAEA,KAAK,EAAE;IAAkF,CAAC,EAC5F;MAAEA,KAAK,EAAE;IAAmH,CAAC,EAC7H;MAAEA,KAAK,EAAE;IAAyD,CAAC,CACtE;IACCQ,SAAS,EAAE;EACb,CAAC;EAED,IAAMC,YAAY,GAAG;IACnBX,SAAS,EAAE,MAAM;IACjBP,KAAK,EAAE,cAAc;IACrBY,GAAG,EAAE,GAAG;IACRO,QAAQ,EAAE;MAAEC,OAAO,EACf,qDAAqD,GACnD;IAAuD,CAAC;IAC9DrB,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,MAAM;MACbiB,SAAS,EAAE;IACb,CAAC,EACDtB,IAAI,CAAC0B,OAAO,CAACV,OAAO,EAAE;MAAEJ,SAAS,EAAE;IAAS,CAAC,CAAC,EAC9C;MACEA,SAAS,EAAE,QAAQ;MACnBP,KAAK,EAAE;IACT,CAAC,EACDH,mBAAmB,EACnBF,IAAI,CAAC2B,oBAAoB;EAE7B,CAAC;EAED,IAAMC,UAAU,GAAG;IACjBhB,SAAS,EAAE,OAAO;IAClBP,KAAK,EAAEJ,KAAK,CAACS,QAAQ,CAACH,YAAY,CAAC,GAAGP,IAAI,CAAC6B,QAAQ;IACnDP,SAAS,EAAE;EACb,CAAC;EAED,IAAMQ,cAAc,GAAG7B,KAAK,CAACS,QAAQ,CAACH,YAAY,CAAC,GAAGP,IAAI,CAAC6B,QAAQ,GAAG,SAAS;EAE/E,IAAME,UAAU,GAAG,CACjB,KAAK,EACL,MAAM,EACN,OAAO,EACP,MAAM,EACN,UAAU,EACV,SAAS,EACT,IAAI,EACJ,MAAM,EACN,MAAM,EACN,QAAQ,EACR,KAAK,EACL,SAAS,EACT,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,UAAU,EACV,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,eAAe,EACf,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,OAAO,EACP,UAAU,EACV,OAAO,EACP,UAAU,EACV,UAAU,EACV,SAAS,EACT,UAAU,EACV,WAAW,EACX,gBAAgB,EAChB,eAAe;EACf;EACA,SAAS,EACT,SAAS,EACT,UAAU,EACV,eAAe,EACf,cAAc;EACd;EACA,SAAS,CACV;EAED,IAAMC,OAAO,GAAG,CACd,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,UAAU,EACV,KAAK,EACL,OAAO,EACP,MAAM,EACN,MAAM,EACN,MAAM,EACN,OAAO,EACP,SAAS,EACT,UAAU,EACV,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,aAAa,EACb,cAAc,EACd,UAAU,EACV,UAAU,EACV,UAAU,EACV,WAAW,EACX,WAAW,EACX,WAAW,EACX,YAAY;EACZ;EACA,OAAO,EACP,QAAQ,EACR,WAAW;EACX;EACA,SAAS,EACT,MAAM,EACN,WAAW,CACZ;EAED,IAAMC,QAAQ,GAAG;IACfR,OAAO,EAAEM,UAAU;IACnBG,IAAI,EAAEF,OAAO;IACbG,OAAO,EAAE,iBAAiB;IAC1B;IACAC,QAAQ,EAAE,qGAAqG,GAC3G,6FAA6F,GAC7F,yHAAyH,GACzH,yFAAyF,GACzF,gGAAgG,GAChG,oGAAoG,GACpG,uFAAuF,GACvF,uFAAuF,GACvF;EACN,CAAC;EAED,IAAMC,mBAAmB,GAAG,CAC1Bd,YAAY,EACZZ,KAAK,EACLT,mBAAmB,EACnBF,IAAI,CAAC2B,oBAAoB,EACzBN,OAAO,EACPL,OAAO,CACR;EAED,IAAMsB,kBAAkB,GAAG;IACzB;IACA;IACA;IACAzB,QAAQ,EAAE,CACR;MACER,KAAK,EAAE,GAAG;MACVY,GAAG,EAAE;IACP,CAAC,EACD;MACEZ,KAAK,EAAE,IAAI;MACXY,GAAG,EAAE;IACP,CAAC,EACD;MACEsB,aAAa,EAAE,uBAAuB;MACtCtB,GAAG,EAAE;IACP,CAAC,CACF;IACDO,QAAQ,EAAES,QAAQ;IAClB7B,QAAQ,EAAEiC,mBAAmB,CAACG,MAAM,CAAC,CACnC;MACEnC,KAAK,EAAE,IAAI;MACXY,GAAG,EAAE,IAAI;MACTO,QAAQ,EAAES,QAAQ;MAClB7B,QAAQ,EAAEiC,mBAAmB,CAACG,MAAM,CAAC,CAAE,MAAM,CAAE,CAAC;MAChDlB,SAAS,EAAE;IACb,CAAC,CACF,CAAC;IACFA,SAAS,EAAE;EACb,CAAC;EAED,IAAMmB,oBAAoB,GAAG;IAC3BpC,KAAK,EAAE,GAAG,GAAGI,gBAAgB,GAAG,cAAc,GAAGqB,cAAc;IAC/DY,WAAW,EAAE,IAAI;IACjBzB,GAAG,EAAE,OAAO;IACZ0B,UAAU,EAAE,IAAI;IAChBnB,QAAQ,EAAES,QAAQ;IAClBf,OAAO,EAAE,gBAAgB;IACzBd,QAAQ,EAAE,CACR;MAAE;MACAC,KAAK,EAAEC,gBAAgB;MACvBkB,QAAQ,EAAES,QAAQ;MAClBX,SAAS,EAAE;IACb,CAAC,EACD;MACEjB,KAAK,EAAEyB,cAAc;MACrBY,WAAW,EAAE,IAAI;MACjBtC,QAAQ,EAAE,CAAEJ,IAAI,CAAC0B,OAAO,CAACE,UAAU,EAAE;QAAEhB,SAAS,EAAE;MAAiB,CAAC,CAAC,CAAE;MACvEU,SAAS,EAAE;IACb,CAAC;IACD;IACA;IACA;MACEA,SAAS,EAAE,CAAC;MACZR,KAAK,EAAE;IACT,CAAC,EACD;MACEF,SAAS,EAAE,QAAQ;MACnBP,KAAK,EAAE,IAAI;MACXY,GAAG,EAAE,IAAI;MACTO,QAAQ,EAAES,QAAQ;MAClBX,SAAS,EAAE,CAAC;MACZlB,QAAQ,EAAE,CACRF,mBAAmB,EACnBF,IAAI,CAAC2B,oBAAoB,EACzBX,OAAO,EACPK,OAAO,EACPV,KAAK;MACL;MACA;QACEN,KAAK,EAAE,IAAI;QACXY,GAAG,EAAE,IAAI;QACTO,QAAQ,EAAES,QAAQ;QAClBX,SAAS,EAAE,CAAC;QACZlB,QAAQ,EAAE,CACR,MAAM,EACNF,mBAAmB,EACnBF,IAAI,CAAC2B,oBAAoB,EACzBX,OAAO,EACPK,OAAO,EACPV,KAAK;MAET,CAAC;IAEL,CAAC,EACDA,KAAK,EACLT,mBAAmB,EACnBF,IAAI,CAAC2B,oBAAoB,EACzBJ,YAAY;EAEhB,CAAC;EAED,OAAO;IACLqB,IAAI,EAAE,GAAG;IACTC,OAAO,EAAE,CAAE,GAAG,CAAE;IAChBrB,QAAQ,EAAES,QAAQ;IAClB;IACA;IACAa,iBAAiB,EAAE,IAAI;IACvB5B,OAAO,EAAE,IAAI;IACbd,QAAQ,EAAE,EAAE,CAACoC,MAAM,CACjBF,kBAAkB,EAClBG,oBAAoB,EACpBJ,mBAAmB,EACnB,CACEd,YAAY,EACZ;MACElB,KAAK,EAAEL,IAAI,CAAC6B,QAAQ,GAAG,IAAI;MAC3BL,QAAQ,EAAES;IACZ,CAAC,EACD;MACErB,SAAS,EAAE,OAAO;MAClB2B,aAAa,EAAE,yBAAyB;MACxCtB,GAAG,EAAE,UAAU;MACfb,QAAQ,EAAE,CACR;QAAEmC,aAAa,EAAE;MAAqB,CAAC,EACvCvC,IAAI,CAAC4B,UAAU;IAEnB,CAAC,CACF,CAAC;IACJmB,OAAO,EAAE;MACPC,YAAY,EAAEzB,YAAY;MAC1B0B,OAAO,EAAEjC,OAAO;MAChBQ,QAAQ,EAAES;IACZ;EACF,CAAC;AACH;AAEA,SAASlC,CAAC,IAAImD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}