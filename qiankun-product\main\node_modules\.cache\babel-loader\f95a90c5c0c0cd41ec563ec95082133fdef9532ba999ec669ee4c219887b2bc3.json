{"ast": null, "code": "\"use strict\";\n\nvar coreContentType = \"application/vnd.openxmlformats-package.core-properties+xml\";\nvar appContentType = \"application/vnd.openxmlformats-officedocument.extended-properties+xml\";\nvar customContentType = \"application/vnd.openxmlformats-officedocument.custom-properties+xml\";\nvar settingsContentType = \"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml\";\nmodule.exports = {\n  settingsContentType: settingsContentType,\n  coreContentType: coreContentType,\n  appContentType: appContentType,\n  customContentType: customContentType\n};", "map": {"version": 3, "names": ["coreContentType", "appContentType", "customContentType", "settingsContentType", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/docxtemplater@3.52.0/node_modules/docxtemplater/js/content-types.js"], "sourcesContent": ["\"use strict\";\n\nvar coreContentType = \"application/vnd.openxmlformats-package.core-properties+xml\";\nvar appContentType = \"application/vnd.openxmlformats-officedocument.extended-properties+xml\";\nvar customContentType = \"application/vnd.openxmlformats-officedocument.custom-properties+xml\";\nvar settingsContentType = \"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml\";\nmodule.exports = {\n  settingsContentType: settingsContentType,\n  coreContentType: coreContentType,\n  appContentType: appContentType,\n  customContentType: customContentType\n};"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,eAAe,GAAG,4DAA4D;AAClF,IAAIC,cAAc,GAAG,uEAAuE;AAC5F,IAAIC,iBAAiB,GAAG,qEAAqE;AAC7F,IAAIC,mBAAmB,GAAG,6EAA6E;AACvGC,MAAM,CAACC,OAAO,GAAG;EACfF,mBAAmB,EAAEA,mBAAmB;EACxCH,eAAe,EAAEA,eAAe;EAChCC,cAAc,EAAEA,cAAc;EAC9BC,iBAAiB,EAAEA;AACrB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}