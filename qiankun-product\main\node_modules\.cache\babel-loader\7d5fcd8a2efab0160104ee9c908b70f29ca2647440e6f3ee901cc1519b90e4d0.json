{"ast": null, "code": "\"use strict\";\n\nvar Uint8ArrayReader = require(\"./uint8ArrayReader.js\");\nfunction NodeBufferReader(data) {\n  this.data = data;\n  this.length = this.data.length;\n  this.index = 0;\n  this.zero = 0;\n}\nNodeBufferReader.prototype = new Uint8ArrayReader();\n\n/**\n * @see DataReader.readData\n */\nNodeBufferReader.prototype.readData = function (size) {\n  this.checkOffset(size);\n  var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n  this.index += size;\n  return result;\n};\nmodule.exports = NodeBufferReader;", "map": {"version": 3, "names": ["Uint8ArrayReader", "require", "NodeBufferReader", "data", "length", "index", "zero", "prototype", "readData", "size", "checkOffset", "result", "slice", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/pizzip@3.1.7/node_modules/pizzip/js/nodeBufferReader.js"], "sourcesContent": ["\"use strict\";\n\nvar Uint8ArrayReader = require(\"./uint8ArrayReader.js\");\nfunction NodeBufferReader(data) {\n  this.data = data;\n  this.length = this.data.length;\n  this.index = 0;\n  this.zero = 0;\n}\nNodeBufferReader.prototype = new Uint8ArrayReader();\n\n/**\n * @see DataReader.readData\n */\nNodeBufferReader.prototype.readData = function (size) {\n  this.checkOffset(size);\n  var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n  this.index += size;\n  return result;\n};\nmodule.exports = NodeBufferReader;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,gBAAgB,GAAGC,OAAO,CAAC,uBAAuB,CAAC;AACvD,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,IAAI,CAACA,IAAI,GAAGA,IAAI;EAChB,IAAI,CAACC,MAAM,GAAG,IAAI,CAACD,IAAI,CAACC,MAAM;EAC9B,IAAI,CAACC,KAAK,GAAG,CAAC;EACd,IAAI,CAACC,IAAI,GAAG,CAAC;AACf;AACAJ,gBAAgB,CAACK,SAAS,GAAG,IAAIP,gBAAgB,CAAC,CAAC;;AAEnD;AACA;AACA;AACAE,gBAAgB,CAACK,SAAS,CAACC,QAAQ,GAAG,UAAUC,IAAI,EAAE;EACpD,IAAI,CAACC,WAAW,CAACD,IAAI,CAAC;EACtB,IAAIE,MAAM,GAAG,IAAI,CAACR,IAAI,CAACS,KAAK,CAAC,IAAI,CAACN,IAAI,GAAG,IAAI,CAACD,KAAK,EAAE,IAAI,CAACC,IAAI,GAAG,IAAI,CAACD,KAAK,GAAGI,IAAI,CAAC;EACnF,IAAI,CAACJ,KAAK,IAAII,IAAI;EAClB,OAAOE,MAAM;AACf,CAAC;AACDE,MAAM,CAACC,OAAO,GAAGZ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}