{"ast": null, "code": "// Block quotes\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\nmodule.exports = function blockquote(state, startLine, endLine, silent) {\n  var adjustTab,\n    ch,\n    i,\n    initial,\n    l,\n    lastLineEmpty,\n    lines,\n    nextLine,\n    offset,\n    oldBMarks,\n    oldBSCount,\n    oldIndent,\n    oldParentType,\n    oldSCount,\n    oldTShift,\n    spaceAfterMarker,\n    terminate,\n    terminatorRules,\n    token,\n    isOutdented,\n    oldLineMax = state.lineMax,\n    pos = state.bMarks[startLine] + state.tShift[startLine],\n    max = state.eMarks[startLine];\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) {\n    return false;\n  }\n\n  // check the block quote marker\n  if (state.src.charCodeAt(pos++) !== 0x3E /* > */) {\n    return false;\n  }\n\n  // we know that it's going to be a valid blockquote,\n  // so no point trying to find the end of it in silent mode\n  if (silent) {\n    return true;\n  }\n\n  // set offset past spaces and \">\"\n  initial = offset = state.sCount[startLine] + 1;\n\n  // skip one optional space after '>'\n  if (state.src.charCodeAt(pos) === 0x20 /* space */) {\n    // ' >   test '\n    //     ^ -- position start of line here:\n    pos++;\n    initial++;\n    offset++;\n    adjustTab = false;\n    spaceAfterMarker = true;\n  } else if (state.src.charCodeAt(pos) === 0x09 /* tab */) {\n    spaceAfterMarker = true;\n    if ((state.bsCount[startLine] + offset) % 4 === 3) {\n      // '  >\\t  test '\n      //       ^ -- position start of line here (tab has width===1)\n      pos++;\n      initial++;\n      offset++;\n      adjustTab = false;\n    } else {\n      // ' >\\t  test '\n      //    ^ -- position start of line here + shift bsCount slightly\n      //         to make extra space appear\n      adjustTab = true;\n    }\n  } else {\n    spaceAfterMarker = false;\n  }\n  oldBMarks = [state.bMarks[startLine]];\n  state.bMarks[startLine] = pos;\n  while (pos < max) {\n    ch = state.src.charCodeAt(pos);\n    if (isSpace(ch)) {\n      if (ch === 0x09) {\n        offset += 4 - (offset + state.bsCount[startLine] + (adjustTab ? 1 : 0)) % 4;\n      } else {\n        offset++;\n      }\n    } else {\n      break;\n    }\n    pos++;\n  }\n  oldBSCount = [state.bsCount[startLine]];\n  state.bsCount[startLine] = state.sCount[startLine] + 1 + (spaceAfterMarker ? 1 : 0);\n  lastLineEmpty = pos >= max;\n  oldSCount = [state.sCount[startLine]];\n  state.sCount[startLine] = offset - initial;\n  oldTShift = [state.tShift[startLine]];\n  state.tShift[startLine] = pos - state.bMarks[startLine];\n  terminatorRules = state.md.block.ruler.getRules('blockquote');\n  oldParentType = state.parentType;\n  state.parentType = 'blockquote';\n\n  // Search the end of the block\n  //\n  // Block ends with either:\n  //  1. an empty line outside:\n  //     ```\n  //     > test\n  //\n  //     ```\n  //  2. an empty line inside:\n  //     ```\n  //     >\n  //     test\n  //     ```\n  //  3. another tag:\n  //     ```\n  //     > test\n  //      - - -\n  //     ```\n  for (nextLine = startLine + 1; nextLine < endLine; nextLine++) {\n    // check if it's outdented, i.e. it's inside list item and indented\n    // less than said list item:\n    //\n    // ```\n    // 1. anything\n    //    > current blockquote\n    // 2. checking this line\n    // ```\n    isOutdented = state.sCount[nextLine] < state.blkIndent;\n    pos = state.bMarks[nextLine] + state.tShift[nextLine];\n    max = state.eMarks[nextLine];\n    if (pos >= max) {\n      // Case 1: line is not inside the blockquote, and this line is empty.\n      break;\n    }\n    if (state.src.charCodeAt(pos++) === 0x3E /* > */ && !isOutdented) {\n      // This line is inside the blockquote.\n\n      // set offset past spaces and \">\"\n      initial = offset = state.sCount[nextLine] + 1;\n\n      // skip one optional space after '>'\n      if (state.src.charCodeAt(pos) === 0x20 /* space */) {\n        // ' >   test '\n        //     ^ -- position start of line here:\n        pos++;\n        initial++;\n        offset++;\n        adjustTab = false;\n        spaceAfterMarker = true;\n      } else if (state.src.charCodeAt(pos) === 0x09 /* tab */) {\n        spaceAfterMarker = true;\n        if ((state.bsCount[nextLine] + offset) % 4 === 3) {\n          // '  >\\t  test '\n          //       ^ -- position start of line here (tab has width===1)\n          pos++;\n          initial++;\n          offset++;\n          adjustTab = false;\n        } else {\n          // ' >\\t  test '\n          //    ^ -- position start of line here + shift bsCount slightly\n          //         to make extra space appear\n          adjustTab = true;\n        }\n      } else {\n        spaceAfterMarker = false;\n      }\n      oldBMarks.push(state.bMarks[nextLine]);\n      state.bMarks[nextLine] = pos;\n      while (pos < max) {\n        ch = state.src.charCodeAt(pos);\n        if (isSpace(ch)) {\n          if (ch === 0x09) {\n            offset += 4 - (offset + state.bsCount[nextLine] + (adjustTab ? 1 : 0)) % 4;\n          } else {\n            offset++;\n          }\n        } else {\n          break;\n        }\n        pos++;\n      }\n      lastLineEmpty = pos >= max;\n      oldBSCount.push(state.bsCount[nextLine]);\n      state.bsCount[nextLine] = state.sCount[nextLine] + 1 + (spaceAfterMarker ? 1 : 0);\n      oldSCount.push(state.sCount[nextLine]);\n      state.sCount[nextLine] = offset - initial;\n      oldTShift.push(state.tShift[nextLine]);\n      state.tShift[nextLine] = pos - state.bMarks[nextLine];\n      continue;\n    }\n\n    // Case 2: line is not inside the blockquote, and the last line was empty.\n    if (lastLineEmpty) {\n      break;\n    }\n\n    // Case 3: another tag found.\n    terminate = false;\n    for (i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true;\n        break;\n      }\n    }\n    if (terminate) {\n      // Quirk to enforce \"hard termination mode\" for paragraphs;\n      // normally if you call `tokenize(state, startLine, nextLine)`,\n      // paragraphs will look below nextLine for paragraph continuation,\n      // but if blockquote is terminated by another tag, they shouldn't\n      state.lineMax = nextLine;\n      if (state.blkIndent !== 0) {\n        // state.blkIndent was non-zero, we now set it to zero,\n        // so we need to re-calculate all offsets to appear as\n        // if indent wasn't changed\n        oldBMarks.push(state.bMarks[nextLine]);\n        oldBSCount.push(state.bsCount[nextLine]);\n        oldTShift.push(state.tShift[nextLine]);\n        oldSCount.push(state.sCount[nextLine]);\n        state.sCount[nextLine] -= state.blkIndent;\n      }\n      break;\n    }\n    oldBMarks.push(state.bMarks[nextLine]);\n    oldBSCount.push(state.bsCount[nextLine]);\n    oldTShift.push(state.tShift[nextLine]);\n    oldSCount.push(state.sCount[nextLine]);\n\n    // A negative indentation means that this is a paragraph continuation\n    //\n    state.sCount[nextLine] = -1;\n  }\n  oldIndent = state.blkIndent;\n  state.blkIndent = 0;\n  token = state.push('blockquote_open', 'blockquote', 1);\n  token.markup = '>';\n  token.map = lines = [startLine, 0];\n  state.md.block.tokenize(state, startLine, nextLine);\n  token = state.push('blockquote_close', 'blockquote', -1);\n  token.markup = '>';\n  state.lineMax = oldLineMax;\n  state.parentType = oldParentType;\n  lines[1] = state.line;\n\n  // Restore original tShift; this might not be necessary since the parser\n  // has already been here, but just to make sure we can do that.\n  for (i = 0; i < oldTShift.length; i++) {\n    state.bMarks[i + startLine] = oldBMarks[i];\n    state.tShift[i + startLine] = oldTShift[i];\n    state.sCount[i + startLine] = oldSCount[i];\n    state.bsCount[i + startLine] = oldBSCount[i];\n  }\n  state.blkIndent = oldIndent;\n  return true;\n};", "map": {"version": 3, "names": ["isSpace", "require", "module", "exports", "blockquote", "state", "startLine", "endLine", "silent", "adjustTab", "ch", "i", "initial", "l", "lastLineEmpty", "lines", "nextLine", "offset", "oldBMarks", "oldBSCount", "oldIndent", "oldParentType", "oldSCount", "oldTShift", "spaceAfterMarker", "terminate", "terminatorRules", "token", "isOutdented", "oldLineMax", "lineMax", "pos", "bMarks", "tShift", "max", "eMarks", "sCount", "blkIndent", "src", "charCodeAt", "bsCount", "md", "block", "ruler", "getRules", "parentType", "push", "length", "markup", "map", "tokenize", "line"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_block/blockquote.js"], "sourcesContent": ["// Block quotes\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\n\n\nmodule.exports = function blockquote(state, startLine, endLine, silent) {\n  var adjustTab,\n      ch,\n      i,\n      initial,\n      l,\n      lastLineEmpty,\n      lines,\n      nextLine,\n      offset,\n      oldBMarks,\n      oldBSCount,\n      oldIndent,\n      oldParentType,\n      oldSCount,\n      oldTShift,\n      spaceAfterMarker,\n      terminate,\n      terminatorRules,\n      token,\n      isOutdented,\n      oldLineMax = state.lineMax,\n      pos = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine];\n\n  // if it's indented more than 3 spaces, it should be a code block\n  if (state.sCount[startLine] - state.blkIndent >= 4) { return false; }\n\n  // check the block quote marker\n  if (state.src.charCodeAt(pos++) !== 0x3E/* > */) { return false; }\n\n  // we know that it's going to be a valid blockquote,\n  // so no point trying to find the end of it in silent mode\n  if (silent) { return true; }\n\n  // set offset past spaces and \">\"\n  initial = offset = state.sCount[startLine] + 1;\n\n  // skip one optional space after '>'\n  if (state.src.charCodeAt(pos) === 0x20 /* space */) {\n    // ' >   test '\n    //     ^ -- position start of line here:\n    pos++;\n    initial++;\n    offset++;\n    adjustTab = false;\n    spaceAfterMarker = true;\n  } else if (state.src.charCodeAt(pos) === 0x09 /* tab */) {\n    spaceAfterMarker = true;\n\n    if ((state.bsCount[startLine] + offset) % 4 === 3) {\n      // '  >\\t  test '\n      //       ^ -- position start of line here (tab has width===1)\n      pos++;\n      initial++;\n      offset++;\n      adjustTab = false;\n    } else {\n      // ' >\\t  test '\n      //    ^ -- position start of line here + shift bsCount slightly\n      //         to make extra space appear\n      adjustTab = true;\n    }\n  } else {\n    spaceAfterMarker = false;\n  }\n\n  oldBMarks = [ state.bMarks[startLine] ];\n  state.bMarks[startLine] = pos;\n\n  while (pos < max) {\n    ch = state.src.charCodeAt(pos);\n\n    if (isSpace(ch)) {\n      if (ch === 0x09) {\n        offset += 4 - (offset + state.bsCount[startLine] + (adjustTab ? 1 : 0)) % 4;\n      } else {\n        offset++;\n      }\n    } else {\n      break;\n    }\n\n    pos++;\n  }\n\n  oldBSCount = [ state.bsCount[startLine] ];\n  state.bsCount[startLine] = state.sCount[startLine] + 1 + (spaceAfterMarker ? 1 : 0);\n\n  lastLineEmpty = pos >= max;\n\n  oldSCount = [ state.sCount[startLine] ];\n  state.sCount[startLine] = offset - initial;\n\n  oldTShift = [ state.tShift[startLine] ];\n  state.tShift[startLine] = pos - state.bMarks[startLine];\n\n  terminatorRules = state.md.block.ruler.getRules('blockquote');\n\n  oldParentType = state.parentType;\n  state.parentType = 'blockquote';\n\n  // Search the end of the block\n  //\n  // Block ends with either:\n  //  1. an empty line outside:\n  //     ```\n  //     > test\n  //\n  //     ```\n  //  2. an empty line inside:\n  //     ```\n  //     >\n  //     test\n  //     ```\n  //  3. another tag:\n  //     ```\n  //     > test\n  //      - - -\n  //     ```\n  for (nextLine = startLine + 1; nextLine < endLine; nextLine++) {\n    // check if it's outdented, i.e. it's inside list item and indented\n    // less than said list item:\n    //\n    // ```\n    // 1. anything\n    //    > current blockquote\n    // 2. checking this line\n    // ```\n    isOutdented = state.sCount[nextLine] < state.blkIndent;\n\n    pos = state.bMarks[nextLine] + state.tShift[nextLine];\n    max = state.eMarks[nextLine];\n\n    if (pos >= max) {\n      // Case 1: line is not inside the blockquote, and this line is empty.\n      break;\n    }\n\n    if (state.src.charCodeAt(pos++) === 0x3E/* > */ && !isOutdented) {\n      // This line is inside the blockquote.\n\n      // set offset past spaces and \">\"\n      initial = offset = state.sCount[nextLine] + 1;\n\n      // skip one optional space after '>'\n      if (state.src.charCodeAt(pos) === 0x20 /* space */) {\n        // ' >   test '\n        //     ^ -- position start of line here:\n        pos++;\n        initial++;\n        offset++;\n        adjustTab = false;\n        spaceAfterMarker = true;\n      } else if (state.src.charCodeAt(pos) === 0x09 /* tab */) {\n        spaceAfterMarker = true;\n\n        if ((state.bsCount[nextLine] + offset) % 4 === 3) {\n          // '  >\\t  test '\n          //       ^ -- position start of line here (tab has width===1)\n          pos++;\n          initial++;\n          offset++;\n          adjustTab = false;\n        } else {\n          // ' >\\t  test '\n          //    ^ -- position start of line here + shift bsCount slightly\n          //         to make extra space appear\n          adjustTab = true;\n        }\n      } else {\n        spaceAfterMarker = false;\n      }\n\n      oldBMarks.push(state.bMarks[nextLine]);\n      state.bMarks[nextLine] = pos;\n\n      while (pos < max) {\n        ch = state.src.charCodeAt(pos);\n\n        if (isSpace(ch)) {\n          if (ch === 0x09) {\n            offset += 4 - (offset + state.bsCount[nextLine] + (adjustTab ? 1 : 0)) % 4;\n          } else {\n            offset++;\n          }\n        } else {\n          break;\n        }\n\n        pos++;\n      }\n\n      lastLineEmpty = pos >= max;\n\n      oldBSCount.push(state.bsCount[nextLine]);\n      state.bsCount[nextLine] = state.sCount[nextLine] + 1 + (spaceAfterMarker ? 1 : 0);\n\n      oldSCount.push(state.sCount[nextLine]);\n      state.sCount[nextLine] = offset - initial;\n\n      oldTShift.push(state.tShift[nextLine]);\n      state.tShift[nextLine] = pos - state.bMarks[nextLine];\n      continue;\n    }\n\n    // Case 2: line is not inside the blockquote, and the last line was empty.\n    if (lastLineEmpty) { break; }\n\n    // Case 3: another tag found.\n    terminate = false;\n    for (i = 0, l = terminatorRules.length; i < l; i++) {\n      if (terminatorRules[i](state, nextLine, endLine, true)) {\n        terminate = true;\n        break;\n      }\n    }\n\n    if (terminate) {\n      // Quirk to enforce \"hard termination mode\" for paragraphs;\n      // normally if you call `tokenize(state, startLine, nextLine)`,\n      // paragraphs will look below nextLine for paragraph continuation,\n      // but if blockquote is terminated by another tag, they shouldn't\n      state.lineMax = nextLine;\n\n      if (state.blkIndent !== 0) {\n        // state.blkIndent was non-zero, we now set it to zero,\n        // so we need to re-calculate all offsets to appear as\n        // if indent wasn't changed\n        oldBMarks.push(state.bMarks[nextLine]);\n        oldBSCount.push(state.bsCount[nextLine]);\n        oldTShift.push(state.tShift[nextLine]);\n        oldSCount.push(state.sCount[nextLine]);\n        state.sCount[nextLine] -= state.blkIndent;\n      }\n\n      break;\n    }\n\n    oldBMarks.push(state.bMarks[nextLine]);\n    oldBSCount.push(state.bsCount[nextLine]);\n    oldTShift.push(state.tShift[nextLine]);\n    oldSCount.push(state.sCount[nextLine]);\n\n    // A negative indentation means that this is a paragraph continuation\n    //\n    state.sCount[nextLine] = -1;\n  }\n\n  oldIndent = state.blkIndent;\n  state.blkIndent = 0;\n\n  token        = state.push('blockquote_open', 'blockquote', 1);\n  token.markup = '>';\n  token.map    = lines = [ startLine, 0 ];\n\n  state.md.block.tokenize(state, startLine, nextLine);\n\n  token        = state.push('blockquote_close', 'blockquote', -1);\n  token.markup = '>';\n\n  state.lineMax = oldLineMax;\n  state.parentType = oldParentType;\n  lines[1] = state.line;\n\n  // Restore original tShift; this might not be necessary since the parser\n  // has already been here, but just to make sure we can do that.\n  for (i = 0; i < oldTShift.length; i++) {\n    state.bMarks[i + startLine] = oldBMarks[i];\n    state.tShift[i + startLine] = oldTShift[i];\n    state.sCount[i + startLine] = oldSCount[i];\n    state.bsCount[i + startLine] = oldBSCount[i];\n  }\n  state.blkIndent = oldIndent;\n\n  return true;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,iBAAiB,CAAC,CAACD,OAAO;AAGhDE,MAAM,CAACC,OAAO,GAAG,SAASC,UAAUA,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAE;EACtE,IAAIC,SAAS;IACTC,EAAE;IACFC,CAAC;IACDC,OAAO;IACPC,CAAC;IACDC,aAAa;IACbC,KAAK;IACLC,QAAQ;IACRC,MAAM;IACNC,SAAS;IACTC,UAAU;IACVC,SAAS;IACTC,aAAa;IACbC,SAAS;IACTC,SAAS;IACTC,gBAAgB;IAChBC,SAAS;IACTC,eAAe;IACfC,KAAK;IACLC,WAAW;IACXC,UAAU,GAAGxB,KAAK,CAACyB,OAAO;IAC1BC,GAAG,GAAG1B,KAAK,CAAC2B,MAAM,CAAC1B,SAAS,CAAC,GAAGD,KAAK,CAAC4B,MAAM,CAAC3B,SAAS,CAAC;IACvD4B,GAAG,GAAG7B,KAAK,CAAC8B,MAAM,CAAC7B,SAAS,CAAC;;EAEjC;EACA,IAAID,KAAK,CAAC+B,MAAM,CAAC9B,SAAS,CAAC,GAAGD,KAAK,CAACgC,SAAS,IAAI,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;;EAEpE;EACA,IAAIhC,KAAK,CAACiC,GAAG,CAACC,UAAU,CAACR,GAAG,EAAE,CAAC,KAAK,IAAI,UAAS;IAAE,OAAO,KAAK;EAAE;;EAEjE;EACA;EACA,IAAIvB,MAAM,EAAE;IAAE,OAAO,IAAI;EAAE;;EAE3B;EACAI,OAAO,GAAGK,MAAM,GAAGZ,KAAK,CAAC+B,MAAM,CAAC9B,SAAS,CAAC,GAAG,CAAC;;EAE9C;EACA,IAAID,KAAK,CAACiC,GAAG,CAACC,UAAU,CAACR,GAAG,CAAC,KAAK,IAAI,CAAC,aAAa;IAClD;IACA;IACAA,GAAG,EAAE;IACLnB,OAAO,EAAE;IACTK,MAAM,EAAE;IACRR,SAAS,GAAG,KAAK;IACjBe,gBAAgB,GAAG,IAAI;EACzB,CAAC,MAAM,IAAInB,KAAK,CAACiC,GAAG,CAACC,UAAU,CAACR,GAAG,CAAC,KAAK,IAAI,CAAC,WAAW;IACvDP,gBAAgB,GAAG,IAAI;IAEvB,IAAI,CAACnB,KAAK,CAACmC,OAAO,CAAClC,SAAS,CAAC,GAAGW,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;MACjD;MACA;MACAc,GAAG,EAAE;MACLnB,OAAO,EAAE;MACTK,MAAM,EAAE;MACRR,SAAS,GAAG,KAAK;IACnB,CAAC,MAAM;MACL;MACA;MACA;MACAA,SAAS,GAAG,IAAI;IAClB;EACF,CAAC,MAAM;IACLe,gBAAgB,GAAG,KAAK;EAC1B;EAEAN,SAAS,GAAG,CAAEb,KAAK,CAAC2B,MAAM,CAAC1B,SAAS,CAAC,CAAE;EACvCD,KAAK,CAAC2B,MAAM,CAAC1B,SAAS,CAAC,GAAGyB,GAAG;EAE7B,OAAOA,GAAG,GAAGG,GAAG,EAAE;IAChBxB,EAAE,GAAGL,KAAK,CAACiC,GAAG,CAACC,UAAU,CAACR,GAAG,CAAC;IAE9B,IAAI/B,OAAO,CAACU,EAAE,CAAC,EAAE;MACf,IAAIA,EAAE,KAAK,IAAI,EAAE;QACfO,MAAM,IAAI,CAAC,GAAG,CAACA,MAAM,GAAGZ,KAAK,CAACmC,OAAO,CAAClC,SAAS,CAAC,IAAIG,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;MAC7E,CAAC,MAAM;QACLQ,MAAM,EAAE;MACV;IACF,CAAC,MAAM;MACL;IACF;IAEAc,GAAG,EAAE;EACP;EAEAZ,UAAU,GAAG,CAAEd,KAAK,CAACmC,OAAO,CAAClC,SAAS,CAAC,CAAE;EACzCD,KAAK,CAACmC,OAAO,CAAClC,SAAS,CAAC,GAAGD,KAAK,CAAC+B,MAAM,CAAC9B,SAAS,CAAC,GAAG,CAAC,IAAIkB,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAAC;EAEnFV,aAAa,GAAGiB,GAAG,IAAIG,GAAG;EAE1BZ,SAAS,GAAG,CAAEjB,KAAK,CAAC+B,MAAM,CAAC9B,SAAS,CAAC,CAAE;EACvCD,KAAK,CAAC+B,MAAM,CAAC9B,SAAS,CAAC,GAAGW,MAAM,GAAGL,OAAO;EAE1CW,SAAS,GAAG,CAAElB,KAAK,CAAC4B,MAAM,CAAC3B,SAAS,CAAC,CAAE;EACvCD,KAAK,CAAC4B,MAAM,CAAC3B,SAAS,CAAC,GAAGyB,GAAG,GAAG1B,KAAK,CAAC2B,MAAM,CAAC1B,SAAS,CAAC;EAEvDoB,eAAe,GAAGrB,KAAK,CAACoC,EAAE,CAACC,KAAK,CAACC,KAAK,CAACC,QAAQ,CAAC,YAAY,CAAC;EAE7DvB,aAAa,GAAGhB,KAAK,CAACwC,UAAU;EAChCxC,KAAK,CAACwC,UAAU,GAAG,YAAY;;EAE/B;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,KAAK7B,QAAQ,GAAGV,SAAS,GAAG,CAAC,EAAEU,QAAQ,GAAGT,OAAO,EAAES,QAAQ,EAAE,EAAE;IAC7D;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAY,WAAW,GAAGvB,KAAK,CAAC+B,MAAM,CAACpB,QAAQ,CAAC,GAAGX,KAAK,CAACgC,SAAS;IAEtDN,GAAG,GAAG1B,KAAK,CAAC2B,MAAM,CAAChB,QAAQ,CAAC,GAAGX,KAAK,CAAC4B,MAAM,CAACjB,QAAQ,CAAC;IACrDkB,GAAG,GAAG7B,KAAK,CAAC8B,MAAM,CAACnB,QAAQ,CAAC;IAE5B,IAAIe,GAAG,IAAIG,GAAG,EAAE;MACd;MACA;IACF;IAEA,IAAI7B,KAAK,CAACiC,GAAG,CAACC,UAAU,CAACR,GAAG,EAAE,CAAC,KAAK,IAAI,YAAW,CAACH,WAAW,EAAE;MAC/D;;MAEA;MACAhB,OAAO,GAAGK,MAAM,GAAGZ,KAAK,CAAC+B,MAAM,CAACpB,QAAQ,CAAC,GAAG,CAAC;;MAE7C;MACA,IAAIX,KAAK,CAACiC,GAAG,CAACC,UAAU,CAACR,GAAG,CAAC,KAAK,IAAI,CAAC,aAAa;QAClD;QACA;QACAA,GAAG,EAAE;QACLnB,OAAO,EAAE;QACTK,MAAM,EAAE;QACRR,SAAS,GAAG,KAAK;QACjBe,gBAAgB,GAAG,IAAI;MACzB,CAAC,MAAM,IAAInB,KAAK,CAACiC,GAAG,CAACC,UAAU,CAACR,GAAG,CAAC,KAAK,IAAI,CAAC,WAAW;QACvDP,gBAAgB,GAAG,IAAI;QAEvB,IAAI,CAACnB,KAAK,CAACmC,OAAO,CAACxB,QAAQ,CAAC,GAAGC,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;UAChD;UACA;UACAc,GAAG,EAAE;UACLnB,OAAO,EAAE;UACTK,MAAM,EAAE;UACRR,SAAS,GAAG,KAAK;QACnB,CAAC,MAAM;UACL;UACA;UACA;UACAA,SAAS,GAAG,IAAI;QAClB;MACF,CAAC,MAAM;QACLe,gBAAgB,GAAG,KAAK;MAC1B;MAEAN,SAAS,CAAC4B,IAAI,CAACzC,KAAK,CAAC2B,MAAM,CAAChB,QAAQ,CAAC,CAAC;MACtCX,KAAK,CAAC2B,MAAM,CAAChB,QAAQ,CAAC,GAAGe,GAAG;MAE5B,OAAOA,GAAG,GAAGG,GAAG,EAAE;QAChBxB,EAAE,GAAGL,KAAK,CAACiC,GAAG,CAACC,UAAU,CAACR,GAAG,CAAC;QAE9B,IAAI/B,OAAO,CAACU,EAAE,CAAC,EAAE;UACf,IAAIA,EAAE,KAAK,IAAI,EAAE;YACfO,MAAM,IAAI,CAAC,GAAG,CAACA,MAAM,GAAGZ,KAAK,CAACmC,OAAO,CAACxB,QAAQ,CAAC,IAAIP,SAAS,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;UAC5E,CAAC,MAAM;YACLQ,MAAM,EAAE;UACV;QACF,CAAC,MAAM;UACL;QACF;QAEAc,GAAG,EAAE;MACP;MAEAjB,aAAa,GAAGiB,GAAG,IAAIG,GAAG;MAE1Bf,UAAU,CAAC2B,IAAI,CAACzC,KAAK,CAACmC,OAAO,CAACxB,QAAQ,CAAC,CAAC;MACxCX,KAAK,CAACmC,OAAO,CAACxB,QAAQ,CAAC,GAAGX,KAAK,CAAC+B,MAAM,CAACpB,QAAQ,CAAC,GAAG,CAAC,IAAIQ,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAAC;MAEjFF,SAAS,CAACwB,IAAI,CAACzC,KAAK,CAAC+B,MAAM,CAACpB,QAAQ,CAAC,CAAC;MACtCX,KAAK,CAAC+B,MAAM,CAACpB,QAAQ,CAAC,GAAGC,MAAM,GAAGL,OAAO;MAEzCW,SAAS,CAACuB,IAAI,CAACzC,KAAK,CAAC4B,MAAM,CAACjB,QAAQ,CAAC,CAAC;MACtCX,KAAK,CAAC4B,MAAM,CAACjB,QAAQ,CAAC,GAAGe,GAAG,GAAG1B,KAAK,CAAC2B,MAAM,CAAChB,QAAQ,CAAC;MACrD;IACF;;IAEA;IACA,IAAIF,aAAa,EAAE;MAAE;IAAO;;IAE5B;IACAW,SAAS,GAAG,KAAK;IACjB,KAAKd,CAAC,GAAG,CAAC,EAAEE,CAAC,GAAGa,eAAe,CAACqB,MAAM,EAAEpC,CAAC,GAAGE,CAAC,EAAEF,CAAC,EAAE,EAAE;MAClD,IAAIe,eAAe,CAACf,CAAC,CAAC,CAACN,KAAK,EAAEW,QAAQ,EAAET,OAAO,EAAE,IAAI,CAAC,EAAE;QACtDkB,SAAS,GAAG,IAAI;QAChB;MACF;IACF;IAEA,IAAIA,SAAS,EAAE;MACb;MACA;MACA;MACA;MACApB,KAAK,CAACyB,OAAO,GAAGd,QAAQ;MAExB,IAAIX,KAAK,CAACgC,SAAS,KAAK,CAAC,EAAE;QACzB;QACA;QACA;QACAnB,SAAS,CAAC4B,IAAI,CAACzC,KAAK,CAAC2B,MAAM,CAAChB,QAAQ,CAAC,CAAC;QACtCG,UAAU,CAAC2B,IAAI,CAACzC,KAAK,CAACmC,OAAO,CAACxB,QAAQ,CAAC,CAAC;QACxCO,SAAS,CAACuB,IAAI,CAACzC,KAAK,CAAC4B,MAAM,CAACjB,QAAQ,CAAC,CAAC;QACtCM,SAAS,CAACwB,IAAI,CAACzC,KAAK,CAAC+B,MAAM,CAACpB,QAAQ,CAAC,CAAC;QACtCX,KAAK,CAAC+B,MAAM,CAACpB,QAAQ,CAAC,IAAIX,KAAK,CAACgC,SAAS;MAC3C;MAEA;IACF;IAEAnB,SAAS,CAAC4B,IAAI,CAACzC,KAAK,CAAC2B,MAAM,CAAChB,QAAQ,CAAC,CAAC;IACtCG,UAAU,CAAC2B,IAAI,CAACzC,KAAK,CAACmC,OAAO,CAACxB,QAAQ,CAAC,CAAC;IACxCO,SAAS,CAACuB,IAAI,CAACzC,KAAK,CAAC4B,MAAM,CAACjB,QAAQ,CAAC,CAAC;IACtCM,SAAS,CAACwB,IAAI,CAACzC,KAAK,CAAC+B,MAAM,CAACpB,QAAQ,CAAC,CAAC;;IAEtC;IACA;IACAX,KAAK,CAAC+B,MAAM,CAACpB,QAAQ,CAAC,GAAG,CAAC,CAAC;EAC7B;EAEAI,SAAS,GAAGf,KAAK,CAACgC,SAAS;EAC3BhC,KAAK,CAACgC,SAAS,GAAG,CAAC;EAEnBV,KAAK,GAAUtB,KAAK,CAACyC,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC,CAAC;EAC7DnB,KAAK,CAACqB,MAAM,GAAG,GAAG;EAClBrB,KAAK,CAACsB,GAAG,GAAMlC,KAAK,GAAG,CAAET,SAAS,EAAE,CAAC,CAAE;EAEvCD,KAAK,CAACoC,EAAE,CAACC,KAAK,CAACQ,QAAQ,CAAC7C,KAAK,EAAEC,SAAS,EAAEU,QAAQ,CAAC;EAEnDW,KAAK,GAAUtB,KAAK,CAACyC,IAAI,CAAC,kBAAkB,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC;EAC/DnB,KAAK,CAACqB,MAAM,GAAG,GAAG;EAElB3C,KAAK,CAACyB,OAAO,GAAGD,UAAU;EAC1BxB,KAAK,CAACwC,UAAU,GAAGxB,aAAa;EAChCN,KAAK,CAAC,CAAC,CAAC,GAAGV,KAAK,CAAC8C,IAAI;;EAErB;EACA;EACA,KAAKxC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGY,SAAS,CAACwB,MAAM,EAAEpC,CAAC,EAAE,EAAE;IACrCN,KAAK,CAAC2B,MAAM,CAACrB,CAAC,GAAGL,SAAS,CAAC,GAAGY,SAAS,CAACP,CAAC,CAAC;IAC1CN,KAAK,CAAC4B,MAAM,CAACtB,CAAC,GAAGL,SAAS,CAAC,GAAGiB,SAAS,CAACZ,CAAC,CAAC;IAC1CN,KAAK,CAAC+B,MAAM,CAACzB,CAAC,GAAGL,SAAS,CAAC,GAAGgB,SAAS,CAACX,CAAC,CAAC;IAC1CN,KAAK,CAACmC,OAAO,CAAC7B,CAAC,GAAGL,SAAS,CAAC,GAAGa,UAAU,CAACR,CAAC,CAAC;EAC9C;EACAN,KAAK,CAACgC,SAAS,GAAGjB,SAAS;EAE3B,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}