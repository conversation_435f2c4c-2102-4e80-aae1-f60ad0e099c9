{"ast": null, "code": "import { createVNode as _createVNode, resolveDirective as _resolveDirective, openBlock as _openBlock, createElementBlock as _createElementBlock, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = {\n  class: \"preview-pdf\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode($setup[\"VueOfficePdf\"], {\n    src: $setup.fileUrl,\n    onRendered: $setup.renderedHandler,\n    onError: $setup.errorHandler\n  }, null, 8 /* PROPS */, [\"src\"])])), [[_directive_loading, $setup.loading]]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "$setup", "src", "fileUrl", "onRendered", "<PERSON><PERSON><PERSON><PERSON>", "onError", "<PERSON><PERSON><PERSON><PERSON>", "loading"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\main\\src\\components\\global-file-preview\\components\\preview-pdf.vue"], "sourcesContent": ["<template>\r\n  <div class=\"preview-pdf\" v-loading=\"loading\">\r\n    <vue-office-pdf :src=\"fileUrl\" @rendered=\"renderedHandler\" @error=\"errorHandler\" />\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'PreviewPdf' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport VueOfficePdf from '@vue-office/pdf'\r\nconst props = defineProps({ id: { type: String, default: '' }, type: { type: String, default: '' } })\r\nconst fileUrl = ref('')\r\nconst loading = ref(false)\r\nonMounted(() => {\r\n  globalDownload()\r\n})\r\n\r\nconst wordTopdf = async (file) => {\r\n  const param = new FormData()\r\n  param.append('file', file)\r\n  const res = await api.wordTopdf(param)\r\n  fileUrl.value = URL.createObjectURL(res)\r\n}\r\nconst ofdTopdf = async (file) => {\r\n  const param = new FormData()\r\n  param.append('file', file)\r\n  const res = await api.ofdTopdf(param)\r\n  fileUrl.value = URL.createObjectURL(res)\r\n}\r\nconst globalDownload = async () => {\r\n  loading.value = true\r\n  const res = await api.globalDownload(props.id)\r\n  if (['doc', 'docx', 'wps'].includes(props.type)) {\r\n    wordTopdf(res)\r\n  } else if (props.type === 'ofd') {\r\n    ofdTopdf(res)\r\n  } else {\r\n    fileUrl.value = URL.createObjectURL(res)\r\n  }\r\n}\r\nconst renderedHandler = () => {\r\n  loading.value = false\r\n  console.log('渲染完成')\r\n}\r\nconst errorHandler = () => {\r\n  loading.value = false\r\n  console.log('渲染失败')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.preview-pdf {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .vue-office-pdf {\r\n    width: 100%;\r\n    height: 100%;\r\n\r\n    .vue-office-pdf-wrapper {\r\n      background: transparent !important;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAa;;;wCAAxBC,mBAAA,CAEM,OAFNC,UAEM,GADJC,YAAA,CAAmFC,MAAA;IAAlEC,GAAG,EAAED,MAAA,CAAAE,OAAO;IAAGC,UAAQ,EAAEH,MAAA,CAAAI,eAAe;IAAGC,OAAK,EAAEL,MAAA,CAAAM;6DADjCN,MAAA,CAAAO,OAAO,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}