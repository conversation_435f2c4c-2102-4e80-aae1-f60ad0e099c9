{"ast": null, "code": "import api from '@/api';\nimport { ref, onMounted, watch } from 'vue';\nimport flvjs from 'flv.js';\nvar __default__ = {\n  name: 'XylVideoItem'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    id: {\n      type: String,\n      default: ''\n    }\n  },\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var props = __props;\n    var videoRef = ref();\n    var flvPlayer = ref();\n    onMounted(function () {\n      if (props.id) {\n        createdPlay();\n      }\n    });\n\n    // 检测浏览器是否支持 flv.js\n    var createdPlay = function createdPlay() {\n      if (flvjs.isSupported()) {\n        // 创建一个播放器实例\n        var Expression = /http(s)?:\\/\\/([\\w-]+\\.)+[\\w-]+(\\/[\\w- .\\/?%&=]*)?/;\n        var pathExp = new RegExp(Expression);\n        flvPlayer.value = flvjs.createPlayer({\n          type: 'mp4',\n          // 媒体类型，默认是 flv\n          isLive: true,\n          // 是否是直播流\n          hasAudio: true,\n          // 是否有音频\n          hanVideo: true,\n          // 是否有视频\n          url: pathExp.test(props.id) ? props.id : api.filePreview(props.id)\n        }, {\n          autoCleanupMinBackwardDuration: true\n        });\n        flvPlayer.value.attachMediaElement(videoRef.value);\n        flvPlayer.value.load();\n      }\n    };\n    watch(function () {\n      return props.id;\n    }, function () {\n      if (props.id) {\n        createdPlay();\n      }\n    });\n    var __returned__ = {\n      props,\n      videoRef,\n      flvPlayer,\n      createdPlay,\n      get api() {\n        return api;\n      },\n      ref,\n      onMounted,\n      watch,\n      get flvjs() {\n        return flvjs;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["api", "ref", "onMounted", "watch", "flvjs", "__default__", "name", "props", "__props", "videoRef", "flvPlayer", "id", "createdPlay", "isSupported", "Expression", "pathExp", "RegExp", "value", "createPlayer", "type", "isLive", "hasAudio", "han<PERSON>ideo", "url", "test", "filePreview", "autoCleanupMinBackwardDuration", "attachMediaElement", "load"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/components/xyl-upload-video/xyl-video-item.vue"], "sourcesContent": ["<!--\r\n * @FileDescription: 该文件的描述信息\r\n * @Author: 作者信息\r\n * @Date: 文件创建时间\r\n * @LastEditors: 最后更新作者\r\n * @LastEditTime: 最后更新时间\r\n -->\r\n<template>\r\n  <video class=\"xyl-video-item\" controlslist=\"nodownload\" ref=\"videoRef\" controls></video>\r\n</template>\r\n<script>\r\nexport default { name: 'XylVideoItem' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, watch } from 'vue'\r\nimport flvjs from 'flv.js'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst videoRef = ref()\r\nconst flvPlayer = ref()\r\n\r\nonMounted(() => {\r\n  if (props.id) {\r\n    createdPlay()\r\n  }\r\n})\r\n\r\n// 检测浏览器是否支持 flv.js\r\nconst createdPlay = () => {\r\n  if (flvjs.isSupported()) {\r\n    // 创建一个播放器实例\r\n    const Expression = /http(s)?:\\/\\/([\\w-]+\\.)+[\\w-]+(\\/[\\w- .\\/?%&=]*)?/\r\n    const pathExp = new RegExp(Expression)\r\n    flvPlayer.value = flvjs.createPlayer(\r\n      {\r\n        type: 'mp4', // 媒体类型，默认是 flv\r\n        isLive: true, // 是否是直播流\r\n        hasAudio: true, // 是否有音频\r\n        hanVideo: true, // 是否有视频\r\n        url: pathExp.test(props.id) ? props.id : api.filePreview(props.id)\r\n      },\r\n      { autoCleanupMinBackwardDuration: true }\r\n    )\r\n    flvPlayer.value.attachMediaElement(videoRef.value)\r\n    flvPlayer.value.load()\r\n  }\r\n}\r\nwatch(\r\n  () => props.id,\r\n  () => {\r\n    if (props.id) {\r\n      createdPlay()\r\n    }\r\n  }\r\n)\r\n</script>\r\n<style lang=\"scss\">\r\n.xyl-video-item {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: block;\r\n}\r\n</style>\r\n"], "mappings": "AAcA,OAAOA,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,SAAS,EAAEC,KAAK,QAAQ,KAAK;AAC3C,OAAOC,KAAK,MAAM,QAAQ;AAL1B,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAe,CAAC;;;;;;;;;;;IAMvC,IAAMC,KAAK,GAAGC,OAAkD;IAChE,IAAMC,QAAQ,GAAGR,GAAG,CAAC,CAAC;IACtB,IAAMS,SAAS,GAAGT,GAAG,CAAC,CAAC;IAEvBC,SAAS,CAAC,YAAM;MACd,IAAIK,KAAK,CAACI,EAAE,EAAE;QACZC,WAAW,CAAC,CAAC;MACf;IACF,CAAC,CAAC;;IAEF;IACA,IAAMA,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB,IAAIR,KAAK,CAACS,WAAW,CAAC,CAAC,EAAE;QACvB;QACA,IAAMC,UAAU,GAAG,mDAAmD;QACtE,IAAMC,OAAO,GAAG,IAAIC,MAAM,CAACF,UAAU,CAAC;QACtCJ,SAAS,CAACO,KAAK,GAAGb,KAAK,CAACc,YAAY,CAClC;UACEC,IAAI,EAAE,KAAK;UAAE;UACbC,MAAM,EAAE,IAAI;UAAE;UACdC,QAAQ,EAAE,IAAI;UAAE;UAChBC,QAAQ,EAAE,IAAI;UAAE;UAChBC,GAAG,EAAER,OAAO,CAACS,IAAI,CAACjB,KAAK,CAACI,EAAE,CAAC,GAAGJ,KAAK,CAACI,EAAE,GAAGX,GAAG,CAACyB,WAAW,CAAClB,KAAK,CAACI,EAAE;QACnE,CAAC,EACD;UAAEe,8BAA8B,EAAE;QAAK,CACzC,CAAC;QACDhB,SAAS,CAACO,KAAK,CAACU,kBAAkB,CAAClB,QAAQ,CAACQ,KAAK,CAAC;QAClDP,SAAS,CAACO,KAAK,CAACW,IAAI,CAAC,CAAC;MACxB;IACF,CAAC;IACDzB,KAAK,CACH;MAAA,OAAMI,KAAK,CAACI,EAAE;IAAA,GACd,YAAM;MACJ,IAAIJ,KAAK,CAACI,EAAE,EAAE;QACZC,WAAW,CAAC,CAAC;MACf;IACF,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}