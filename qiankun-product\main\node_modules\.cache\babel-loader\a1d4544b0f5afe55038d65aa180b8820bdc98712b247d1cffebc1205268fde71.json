{"ast": null, "code": "/**\n * Gets the number of `placeholder` occurrences in `array`.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} placeholder The placeholder to search for.\n * @returns {number} Returns the placeholder count.\n */\nfunction countHolders(array, placeholder) {\n  var length = array.length,\n    result = 0;\n  while (length--) {\n    if (array[length] === placeholder) {\n      ++result;\n    }\n  }\n  return result;\n}\nexport default countHolders;", "map": {"version": 3, "names": ["countHolders", "array", "placeholder", "length", "result"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_countHolders.js"], "sourcesContent": ["/**\n * Gets the number of `placeholder` occurrences in `array`.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} placeholder The placeholder to search for.\n * @returns {number} Returns the placeholder count.\n */\nfunction countHolders(array, placeholder) {\n  var length = array.length,\n      result = 0;\n\n  while (length--) {\n    if (array[length] === placeholder) {\n      ++result;\n    }\n  }\n  return result;\n}\n\nexport default countHolders;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,YAAYA,CAACC,KAAK,EAAEC,WAAW,EAAE;EACxC,IAAIC,MAAM,GAAGF,KAAK,CAACE,MAAM;IACrBC,MAAM,GAAG,CAAC;EAEd,OAAOD,MAAM,EAAE,EAAE;IACf,IAAIF,KAAK,CAACE,MAAM,CAAC,KAAKD,WAAW,EAAE;MACjC,EAAEE,MAAM;IACV;EACF;EACA,OAAOA,MAAM;AACf;AAEA,eAAeJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}