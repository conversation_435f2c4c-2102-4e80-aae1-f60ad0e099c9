{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _regeneratorRuntime from \"@babel/runtime/regenerator\";\n/**\n * <AUTHOR>\n * @since 2020-05-15\n */\nexport default function getAddOn(global) {\n  return {\n    beforeLoad: function beforeLoad() {\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              // eslint-disable-next-line no-param-reassign\n              global.__POWERED_BY_QIANKUN__ = true;\n            case 1:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }))();\n    },\n    beforeMount: function beforeMount() {\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              // eslint-disable-next-line no-param-reassign\n              global.__POWERED_BY_QIANKUN__ = true;\n            case 1:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }))();\n    },\n    beforeUnmount: function beforeUnmount() {\n      return _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              // eslint-disable-next-line no-param-reassign\n              delete global.__POWERED_BY_QIANKUN__;\n            case 1:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }))();\n    }\n  };\n}", "map": {"version": 3, "names": ["_asyncToGenerator", "_regeneratorRuntime", "getAddOn", "global", "beforeLoad", "mark", "_callee", "wrap", "_callee$", "_context", "prev", "next", "__POWERED_BY_QIANKUN__", "stop", "beforeMount", "_callee2", "_callee2$", "_context2", "beforeUnmount", "_callee3", "_callee3$", "_context3"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/qiankun@2.10.16/node_modules/qiankun/es/addons/engineFlag.js"], "sourcesContent": ["import _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _regeneratorRuntime from \"@babel/runtime/regenerator\";\n/**\n * <AUTHOR>\n * @since 2020-05-15\n */\nexport default function getAddOn(global) {\n  return {\n    beforeLoad: function beforeLoad() {\n      return _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee() {\n        return _regeneratorRuntime.wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              // eslint-disable-next-line no-param-reassign\n              global.__POWERED_BY_QIANKUN__ = true;\n            case 1:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }))();\n    },\n    beforeMount: function beforeMount() {\n      return _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee2() {\n        return _regeneratorRuntime.wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              // eslint-disable-next-line no-param-reassign\n              global.__POWERED_BY_QIANKUN__ = true;\n            case 1:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }))();\n    },\n    beforeUnmount: function beforeUnmount() {\n      return _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime.mark(function _callee3() {\n        return _regeneratorRuntime.wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              // eslint-disable-next-line no-param-reassign\n              delete global.__POWERED_BY_QIANKUN__;\n            case 1:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }))();\n    }\n  };\n}"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,mBAAmB,MAAM,4BAA4B;AAC5D;AACA;AACA;AACA;AACA,eAAe,SAASC,QAAQA,CAACC,MAAM,EAAE;EACvC,OAAO;IACLC,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;MAChC,OAAOJ,iBAAiB,CAAE,aAAaC,mBAAmB,CAACI,IAAI,CAAC,SAASC,OAAOA,CAAA,EAAG;QACjF,OAAOL,mBAAmB,CAACM,IAAI,CAAC,SAASC,QAAQA,CAACC,QAAQ,EAAE;UAC1D,OAAO,CAAC,EAAE,QAAQA,QAAQ,CAACC,IAAI,GAAGD,QAAQ,CAACE,IAAI;YAC7C,KAAK,CAAC;cACJ;cACAR,MAAM,CAACS,sBAAsB,GAAG,IAAI;YACtC,KAAK,CAAC;YACN,KAAK,KAAK;cACR,OAAOH,QAAQ,CAACI,IAAI,CAAC,CAAC;UAC1B;QACF,CAAC,EAAEP,OAAO,CAAC;MACb,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC;IACDQ,WAAW,EAAE,SAASA,WAAWA,CAAA,EAAG;MAClC,OAAOd,iBAAiB,CAAE,aAAaC,mBAAmB,CAACI,IAAI,CAAC,SAASU,QAAQA,CAAA,EAAG;QAClF,OAAOd,mBAAmB,CAACM,IAAI,CAAC,SAASS,SAASA,CAACC,SAAS,EAAE;UAC5D,OAAO,CAAC,EAAE,QAAQA,SAAS,CAACP,IAAI,GAAGO,SAAS,CAACN,IAAI;YAC/C,KAAK,CAAC;cACJ;cACAR,MAAM,CAACS,sBAAsB,GAAG,IAAI;YACtC,KAAK,CAAC;YACN,KAAK,KAAK;cACR,OAAOK,SAAS,CAACJ,IAAI,CAAC,CAAC;UAC3B;QACF,CAAC,EAAEE,QAAQ,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC;IACP,CAAC;IACDG,aAAa,EAAE,SAASA,aAAaA,CAAA,EAAG;MACtC,OAAOlB,iBAAiB,CAAE,aAAaC,mBAAmB,CAACI,IAAI,CAAC,SAASc,QAAQA,CAAA,EAAG;QAClF,OAAOlB,mBAAmB,CAACM,IAAI,CAAC,SAASa,SAASA,CAACC,SAAS,EAAE;UAC5D,OAAO,CAAC,EAAE,QAAQA,SAAS,CAACX,IAAI,GAAGW,SAAS,CAACV,IAAI;YAC/C,KAAK,CAAC;cACJ;cACA,OAAOR,MAAM,CAACS,sBAAsB;YACtC,KAAK,CAAC;YACN,KAAK,KAAK;cACR,OAAOS,SAAS,CAACR,IAAI,CAAC,CAAC;UAC3B;QACF,CAAC,EAAEM,QAAQ,CAAC;MACd,CAAC,CAAC,CAAC,CAAC,CAAC;IACP;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}