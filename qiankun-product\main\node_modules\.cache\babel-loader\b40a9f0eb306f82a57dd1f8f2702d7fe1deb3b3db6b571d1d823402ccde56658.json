{"ast": null, "code": "// Parse backticks\n\n'use strict';\n\nmodule.exports = function backtick(state, silent) {\n  var start,\n    max,\n    marker,\n    token,\n    matchStart,\n    matchEnd,\n    openerLength,\n    closerLength,\n    pos = state.pos,\n    ch = state.src.charCodeAt(pos);\n  if (ch !== 0x60 /* ` */) {\n    return false;\n  }\n  start = pos;\n  pos++;\n  max = state.posMax;\n\n  // scan marker length\n  while (pos < max && state.src.charCodeAt(pos) === 0x60 /* ` */) {\n    pos++;\n  }\n  marker = state.src.slice(start, pos);\n  openerLength = marker.length;\n  if (state.backticksScanned && (state.backticks[openerLength] || 0) <= start) {\n    if (!silent) state.pending += marker;\n    state.pos += openerLength;\n    return true;\n  }\n  matchStart = matchEnd = pos;\n\n  // Nothing found in the cache, scan until the end of the line (or until marker is found)\n  while ((matchStart = state.src.indexOf('`', matchEnd)) !== -1) {\n    matchEnd = matchStart + 1;\n\n    // scan marker length\n    while (matchEnd < max && state.src.charCodeAt(matchEnd) === 0x60 /* ` */) {\n      matchEnd++;\n    }\n    closerLength = matchEnd - matchStart;\n    if (closerLength === openerLength) {\n      // Found matching closer length.\n      if (!silent) {\n        token = state.push('code_inline', 'code', 0);\n        token.markup = marker;\n        token.content = state.src.slice(pos, matchStart).replace(/\\n/g, ' ').replace(/^ (.+) $/, '$1');\n      }\n      state.pos = matchEnd;\n      return true;\n    }\n\n    // Some different length found, put it in cache as upper limit of where closer can be found\n    state.backticks[closerLength] = matchStart;\n  }\n\n  // Scanned through the end, didn't find anything\n  state.backticksScanned = true;\n  if (!silent) state.pending += marker;\n  state.pos += openerLength;\n  return true;\n};", "map": {"version": 3, "names": ["module", "exports", "backtick", "state", "silent", "start", "max", "marker", "token", "matchStart", "matchEnd", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "pos", "ch", "src", "charCodeAt", "posMax", "slice", "length", "backticksScanned", "backticks", "pending", "indexOf", "push", "markup", "content", "replace"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_inline/backticks.js"], "sourcesContent": ["// Parse backticks\n\n'use strict';\n\n\nmodule.exports = function backtick(state, silent) {\n  var start, max, marker, token, matchStart, matchEnd, openerLength, closerLength,\n      pos = state.pos,\n      ch = state.src.charCodeAt(pos);\n\n  if (ch !== 0x60/* ` */) { return false; }\n\n  start = pos;\n  pos++;\n  max = state.posMax;\n\n  // scan marker length\n  while (pos < max && state.src.charCodeAt(pos) === 0x60/* ` */) { pos++; }\n\n  marker = state.src.slice(start, pos);\n  openerLength = marker.length;\n\n  if (state.backticksScanned && (state.backticks[openerLength] || 0) <= start) {\n    if (!silent) state.pending += marker;\n    state.pos += openerLength;\n    return true;\n  }\n\n  matchStart = matchEnd = pos;\n\n  // Nothing found in the cache, scan until the end of the line (or until marker is found)\n  while ((matchStart = state.src.indexOf('`', matchEnd)) !== -1) {\n    matchEnd = matchStart + 1;\n\n    // scan marker length\n    while (matchEnd < max && state.src.charCodeAt(matchEnd) === 0x60/* ` */) { matchEnd++; }\n\n    closerLength = matchEnd - matchStart;\n\n    if (closerLength === openerLength) {\n      // Found matching closer length.\n      if (!silent) {\n        token     = state.push('code_inline', 'code', 0);\n        token.markup  = marker;\n        token.content = state.src.slice(pos, matchStart)\n          .replace(/\\n/g, ' ')\n          .replace(/^ (.+) $/, '$1');\n      }\n      state.pos = matchEnd;\n      return true;\n    }\n\n    // Some different length found, put it in cache as upper limit of where closer can be found\n    state.backticks[closerLength] = matchStart;\n  }\n\n  // Scanned through the end, didn't find anything\n  state.backticksScanned = true;\n\n  if (!silent) state.pending += marker;\n  state.pos += openerLength;\n  return true;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAGZA,MAAM,CAACC,OAAO,GAAG,SAASC,QAAQA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAChD,IAAIC,KAAK;IAAEC,GAAG;IAAEC,MAAM;IAAEC,KAAK;IAAEC,UAAU;IAAEC,QAAQ;IAAEC,YAAY;IAAEC,YAAY;IAC3EC,GAAG,GAAGV,KAAK,CAACU,GAAG;IACfC,EAAE,GAAGX,KAAK,CAACY,GAAG,CAACC,UAAU,CAACH,GAAG,CAAC;EAElC,IAAIC,EAAE,KAAK,IAAI,UAAS;IAAE,OAAO,KAAK;EAAE;EAExCT,KAAK,GAAGQ,GAAG;EACXA,GAAG,EAAE;EACLP,GAAG,GAAGH,KAAK,CAACc,MAAM;;EAElB;EACA,OAAOJ,GAAG,GAAGP,GAAG,IAAIH,KAAK,CAACY,GAAG,CAACC,UAAU,CAACH,GAAG,CAAC,KAAK,IAAI,UAAS;IAAEA,GAAG,EAAE;EAAE;EAExEN,MAAM,GAAGJ,KAAK,CAACY,GAAG,CAACG,KAAK,CAACb,KAAK,EAAEQ,GAAG,CAAC;EACpCF,YAAY,GAAGJ,MAAM,CAACY,MAAM;EAE5B,IAAIhB,KAAK,CAACiB,gBAAgB,IAAI,CAACjB,KAAK,CAACkB,SAAS,CAACV,YAAY,CAAC,IAAI,CAAC,KAAKN,KAAK,EAAE;IAC3E,IAAI,CAACD,MAAM,EAAED,KAAK,CAACmB,OAAO,IAAIf,MAAM;IACpCJ,KAAK,CAACU,GAAG,IAAIF,YAAY;IACzB,OAAO,IAAI;EACb;EAEAF,UAAU,GAAGC,QAAQ,GAAGG,GAAG;;EAE3B;EACA,OAAO,CAACJ,UAAU,GAAGN,KAAK,CAACY,GAAG,CAACQ,OAAO,CAAC,GAAG,EAAEb,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE;IAC7DA,QAAQ,GAAGD,UAAU,GAAG,CAAC;;IAEzB;IACA,OAAOC,QAAQ,GAAGJ,GAAG,IAAIH,KAAK,CAACY,GAAG,CAACC,UAAU,CAACN,QAAQ,CAAC,KAAK,IAAI,UAAS;MAAEA,QAAQ,EAAE;IAAE;IAEvFE,YAAY,GAAGF,QAAQ,GAAGD,UAAU;IAEpC,IAAIG,YAAY,KAAKD,YAAY,EAAE;MACjC;MACA,IAAI,CAACP,MAAM,EAAE;QACXI,KAAK,GAAOL,KAAK,CAACqB,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,CAAC,CAAC;QAChDhB,KAAK,CAACiB,MAAM,GAAIlB,MAAM;QACtBC,KAAK,CAACkB,OAAO,GAAGvB,KAAK,CAACY,GAAG,CAACG,KAAK,CAACL,GAAG,EAAEJ,UAAU,CAAC,CAC7CkB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CACnBA,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC;MAC9B;MACAxB,KAAK,CAACU,GAAG,GAAGH,QAAQ;MACpB,OAAO,IAAI;IACb;;IAEA;IACAP,KAAK,CAACkB,SAAS,CAACT,YAAY,CAAC,GAAGH,UAAU;EAC5C;;EAEA;EACAN,KAAK,CAACiB,gBAAgB,GAAG,IAAI;EAE7B,IAAI,CAAChB,MAAM,EAAED,KAAK,CAACmB,OAAO,IAAIf,MAAM;EACpCJ,KAAK,CAACU,GAAG,IAAIF,YAAY;EACzB,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}