{"ast": null, "code": "import 'highlight.js/styles/atom-one-dark.css';\nimport hljs from 'highlight.js/lib/core';\nimport javascript from 'highlight.js/lib/languages/javascript';\nimport vbscript from 'highlight.js/lib/languages/vbscript';\nimport python from 'highlight.js/lib/languages/python';\nimport matlab from 'highlight.js/lib/languages/matlab';\nimport csharp from 'highlight.js/lib/languages/csharp';\nimport shell from 'highlight.js/lib/languages/shell';\nimport vhdl from 'highlight.js/lib/languages/vhdl';\nimport java from 'highlight.js/lib/languages/java';\nimport css from 'highlight.js/lib/languages/css';\nimport xml from 'highlight.js/lib/languages/xml';\nimport sql from 'highlight.js/lib/languages/sql';\nimport cpp from 'highlight.js/lib/languages/cpp';\nimport c from 'highlight.js/lib/languages/c';\nimport ClipboardJS from 'clipboard';\nhljs.registerLanguage('javascript', javascript);\nhljs.registerLanguage('vbscript', vbscript);\nhljs.registerLanguage('python', python);\nhljs.registerLanguage('matlab', matlab);\nhljs.registerLanguage('csharp', csharp);\nhljs.registerLanguage('shell', shell);\nhljs.registerLanguage('vhdl', vhdl);\nhljs.registerLanguage('java', java);\nhljs.registerLanguage('html', xml);\nhljs.registerLanguage('xml', xml);\nhljs.registerLanguage('css', css);\nhljs.registerLanguage('sql', sql);\nhljs.registerLanguage('cpp', cpp);\nhljs.registerLanguage('c', c);\nhljs.configure({\n  ignoreUnescapedHTML: true\n});\n\n/**\r\n * 高亮代码块\r\n * @param {Element} element 包含 pre code 代码块的元素\r\n */\nfunction highlightCode(element) {\n  var codeEls = element.querySelectorAll('pre code');\n  codeEls.forEach(function (el) {\n    hljs.highlightElement(el);\n  });\n}\n\n/**\r\n * 给代码块添加复制按钮\r\n * @param {Element} element 包含 pre code 代码块的元素\r\n */\nfunction buildCopyButton(element) {\n  // 获取所有的 pre 元素\n  var pres = element.querySelectorAll('pre');\n  if (!pres.length) return;\n  pres.forEach(function (pre) {\n    // 获取 code 元素中的文本内容\n    var code = pre.querySelector('code');\n    if (!code) return;\n    var text = code.innerText;\n    // 创建按钮\n    var btn = document.createElement('span');\n    btn.className = 'copy';\n    btn.textContent = '复制';\n    btn.setAttribute('data-clipboard-text', text);\n    // 将按钮插入到 pre 元素的最前面\n    pre.insertBefore(btn, pre.firstChild);\n    // 初始化 ClipboardJS\n    var clipboard = new ClipboardJS('.copy');\n    clipboard.on('success', function () {\n      btn.classList.add('copyed');\n      btn.textContent = '复制成功';\n      setTimeout(function () {\n        btn.textContent = '复制';\n        btn.classList.remove('copyed');\n      }, 1000);\n    });\n    clipboard.on('error', function () {\n      btn.textContent = '复制失败';\n    });\n  });\n}\n// function buildCopyButton (element) {\n//   let $pres = $(element).find('pre')\n//   if (!$pres.length) return\n//   $pres.each(function () {\n//     var t = $(this).children('code').text()\n//     // 创建按钮\n//     var btn = $('<span class=\"copy\">复制</span>').attr('data-clipboard-text', t)\n//     $(this).prepend(btn)\n//     var c = new ClipboardJS(btn[0])\n//     c.on('success', function () {\n//       btn.addClass('copyed').text('复制成功')\n//       setTimeout(function () {\n//         btn.text('复制').removeClass('copyed')\n//       }, 1000)\n//     })\n//     c.on('error', function () {\n//       btn.text('复制失败')\n//     })\n//   })\n// }\n\n/** 构建生成中的 markdown 的内容 */\nexport function buildCodeBlock(element) {\n  highlightCode(element);\n  buildCopyButton(element);\n}\n\n/** 核心函数, 对比节点的内容 实现动态更新 markdown 的 div 而不是用 innerHTML 的属性全部刷新 */\nexport function deepCloneAndUpdate(div1, div2) {\n  // 递归比较和更新 div1 和 div2 的子节点\n  function compareAndUpdate(node1, node2) {\n    // 情况 1：node1 是文本节点，更新文本内容\n    if (node1 && node1.nodeType === Node.TEXT_NODE && node2.nodeType === Node.TEXT_NODE) {\n      if (node1.nodeValue !== node2.nodeValue) {\n        // 更新文本内容\n        node1.nodeValue = node2.nodeValue;\n      }\n      return;\n    }\n\n    // 情况 2：node1 和 node2 的标签名不同，替换整个节点\n    if (!node1 || node1.tagName !== node2.tagName) {\n      // 克隆 node2 节点\n      var newNode = node2.cloneNode(true);\n      if (node1) {\n        // 替换旧节点\n        node1.parentNode.replaceChild(newNode, node1);\n      } else {\n        // 如果 node1 不存在，直接新增\n        node2.parentNode.appendChild(newNode);\n      }\n      return;\n    }\n    // 情况 3：节点的 class 或其他属性更新, 注意对root节点的保护\n    if (node1.className !== 'global-markdown' && node1.className !== node2.className) {\n      // 3.1 更新 className\n      node1.className = node2.className;\n    }\n    // 3.2 对 id 的更新 注意对root节点的保护\n    if (node1.id !== 'global-markdown' && node1.id !== node2.id) {\n      node1.id = node2.id;\n    }\n    //  3.3 对 style 的更新\n    if (node1.style.cssText !== node2.style.cssText) {\n      node1.style.cssText = node2.style.cssText;\n    }\n    // 情况 4：递归对比和更新子节点\n    var children1 = Array.from(node1.childNodes); // node1 的所有子节点\n    var children2 = Array.from(node2.childNodes); // node2 的所有子节点\n    // 遍历 node2 的子节点，逐个与 node1 的对应子节点比较\n    children2.forEach(function (child2, index) {\n      var child1 = children1[index];\n      if (!child1) {\n        // 如果 child1 不存在，直接克隆 child2 并添加到 node1\n        var newChild = child2.cloneNode(true);\n        node1.appendChild(newChild);\n      } else {\n        // 如果 child1 存在，递归比较和更新\n        compareAndUpdate(child1, child2);\n      }\n    });\n    // 删除 node1 中多余的子节点\n    if (children1.length > children2.length) {\n      for (var i = children2.length; i < children1.length; i++) {\n        node1.removeChild(children1[i]);\n      }\n    }\n  }\n  // 从 div2 根节点开始与 div1 比较\n  compareAndUpdate(div1, div2);\n}", "map": {"version": 3, "names": ["hljs", "javascript", "vbscript", "python", "matlab", "csharp", "shell", "vhdl", "java", "css", "xml", "sql", "cpp", "c", "ClipboardJS", "registerLanguage", "configure", "ignoreUnescapedHTML", "highlightCode", "element", "codeEls", "querySelectorAll", "for<PERSON>ach", "el", "highlightElement", "buildCopyButton", "pres", "length", "pre", "code", "querySelector", "text", "innerText", "btn", "document", "createElement", "className", "textContent", "setAttribute", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "clipboard", "on", "classList", "add", "setTimeout", "remove", "buildCodeBlock", "deepCloneAndUpdate", "div1", "div2", "compareAndUpdate", "node1", "node2", "nodeType", "Node", "TEXT_NODE", "nodeValue", "tagName", "newNode", "cloneNode", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON><PERSON>", "id", "style", "cssText", "children1", "Array", "from", "childNodes", "children2", "child2", "index", "child1", "<PERSON><PERSON><PERSON><PERSON>", "i", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/components/global-markdown/code-block.js"], "sourcesContent": ["import 'highlight.js/styles/atom-one-dark.css'\r\nimport hljs from 'highlight.js/lib/core'\r\nimport javascript from 'highlight.js/lib/languages/javascript'\r\nimport vbscript from 'highlight.js/lib/languages/vbscript'\r\nimport python from 'highlight.js/lib/languages/python'\r\nimport matlab from 'highlight.js/lib/languages/matlab'\r\nimport csharp from 'highlight.js/lib/languages/csharp'\r\nimport shell from 'highlight.js/lib/languages/shell'\r\nimport vhdl from 'highlight.js/lib/languages/vhdl'\r\nimport java from 'highlight.js/lib/languages/java'\r\nimport css from 'highlight.js/lib/languages/css'\r\nimport xml from 'highlight.js/lib/languages/xml'\r\nimport sql from 'highlight.js/lib/languages/sql'\r\nimport cpp from 'highlight.js/lib/languages/cpp'\r\nimport c from 'highlight.js/lib/languages/c'\r\nimport ClipboardJS from 'clipboard'\r\n\r\nhljs.registerLanguage('javascript', javascript)\r\nhljs.registerLanguage('vbscript', vbscript)\r\nhljs.registerLanguage('python', python)\r\nhljs.registerLanguage('matlab', matlab)\r\nhljs.registerLanguage('csharp', csharp)\r\nhljs.registerLanguage('shell', shell)\r\nhljs.registerLanguage('vhdl', vhdl)\r\nhljs.registerLanguage('java', java)\r\nhljs.registerLanguage('html', xml)\r\nhljs.registerLanguage('xml', xml)\r\nhljs.registerLanguage('css', css)\r\nhljs.registerLanguage('sql', sql)\r\nhljs.registerLanguage('cpp', cpp)\r\nhljs.registerLanguage('c', c)\r\n\r\nhljs.configure({ ignoreUnescapedHTML: true })\r\n\r\n/**\r\n * 高亮代码块\r\n * @param {Element} element 包含 pre code 代码块的元素\r\n */\r\nfunction highlightCode (element) {\r\n  const codeEls = element.querySelectorAll('pre code')\r\n  codeEls.forEach((el) => {\r\n    hljs.highlightElement(el)\r\n  })\r\n}\r\n\r\n/**\r\n * 给代码块添加复制按钮\r\n * @param {Element} element 包含 pre code 代码块的元素\r\n */\r\nfunction buildCopyButton (element) {\r\n  // 获取所有的 pre 元素\r\n  let pres = element.querySelectorAll('pre')\r\n  if (!pres.length) return\r\n  pres.forEach((pre) => {\r\n    // 获取 code 元素中的文本内容\r\n    let code = pre.querySelector('code')\r\n    if (!code) return\r\n    let text = code.innerText\r\n    // 创建按钮\r\n    let btn = document.createElement('span')\r\n    btn.className = 'copy'\r\n    btn.textContent = '复制'\r\n    btn.setAttribute('data-clipboard-text', text)\r\n    // 将按钮插入到 pre 元素的最前面\r\n    pre.insertBefore(btn, pre.firstChild)\r\n    // 初始化 ClipboardJS\r\n    let clipboard = new ClipboardJS('.copy')\r\n    clipboard.on('success', () => {\r\n      btn.classList.add('copyed')\r\n      btn.textContent = '复制成功'\r\n      setTimeout(() => {\r\n        btn.textContent = '复制'\r\n        btn.classList.remove('copyed')\r\n      }, 1000)\r\n    })\r\n    clipboard.on('error', () => {\r\n      btn.textContent = '复制失败'\r\n    })\r\n  })\r\n}\r\n// function buildCopyButton (element) {\r\n//   let $pres = $(element).find('pre')\r\n//   if (!$pres.length) return\r\n//   $pres.each(function () {\r\n//     var t = $(this).children('code').text()\r\n//     // 创建按钮\r\n//     var btn = $('<span class=\"copy\">复制</span>').attr('data-clipboard-text', t)\r\n//     $(this).prepend(btn)\r\n//     var c = new ClipboardJS(btn[0])\r\n//     c.on('success', function () {\r\n//       btn.addClass('copyed').text('复制成功')\r\n//       setTimeout(function () {\r\n//         btn.text('复制').removeClass('copyed')\r\n//       }, 1000)\r\n//     })\r\n//     c.on('error', function () {\r\n//       btn.text('复制失败')\r\n//     })\r\n//   })\r\n// }\r\n\r\n/** 构建生成中的 markdown 的内容 */\r\nexport function buildCodeBlock (element) {\r\n  highlightCode(element)\r\n  buildCopyButton(element)\r\n}\r\n\r\n/** 核心函数, 对比节点的内容 实现动态更新 markdown 的 div 而不是用 innerHTML 的属性全部刷新 */\r\nexport function deepCloneAndUpdate (div1, div2) {\r\n  // 递归比较和更新 div1 和 div2 的子节点\r\n  function compareAndUpdate (node1, node2) {\r\n    // 情况 1：node1 是文本节点，更新文本内容\r\n    if (node1 && node1.nodeType === Node.TEXT_NODE && node2.nodeType === Node.TEXT_NODE) {\r\n      if (node1.nodeValue !== node2.nodeValue) {\r\n        // 更新文本内容\r\n        node1.nodeValue = node2.nodeValue\r\n      }\r\n      return\r\n    }\r\n\r\n    // 情况 2：node1 和 node2 的标签名不同，替换整个节点\r\n    if (!node1 || node1.tagName !== node2.tagName) {\r\n      // 克隆 node2 节点\r\n      const newNode = node2.cloneNode(true)\r\n      if (node1) {\r\n        // 替换旧节点\r\n        node1.parentNode.replaceChild(newNode, node1)\r\n      } else {\r\n        // 如果 node1 不存在，直接新增\r\n        node2.parentNode.appendChild(newNode)\r\n      }\r\n      return\r\n    }\r\n    // 情况 3：节点的 class 或其他属性更新, 注意对root节点的保护\r\n    if (node1.className !== 'global-markdown' && node1.className !== node2.className) {\r\n      // 3.1 更新 className\r\n      node1.className = node2.className\r\n    }\r\n    // 3.2 对 id 的更新 注意对root节点的保护\r\n    if (node1.id !== 'global-markdown' && node1.id !== node2.id) {\r\n      node1.id = node2.id\r\n    }\r\n    //  3.3 对 style 的更新\r\n    if (node1.style.cssText !== node2.style.cssText) {\r\n      node1.style.cssText = node2.style.cssText\r\n    }\r\n    // 情况 4：递归对比和更新子节点\r\n    const children1 = Array.from(node1.childNodes) // node1 的所有子节点\r\n    const children2 = Array.from(node2.childNodes) // node2 的所有子节点\r\n    // 遍历 node2 的子节点，逐个与 node1 的对应子节点比较\r\n    children2.forEach((child2, index) => {\r\n      const child1 = children1[index]\r\n      if (!child1) {\r\n        // 如果 child1 不存在，直接克隆 child2 并添加到 node1\r\n        const newChild = child2.cloneNode(true)\r\n        node1.appendChild(newChild)\r\n      } else {\r\n        // 如果 child1 存在，递归比较和更新\r\n        compareAndUpdate(child1, child2)\r\n      }\r\n    })\r\n    // 删除 node1 中多余的子节点\r\n    if (children1.length > children2.length) {\r\n      for (let i = children2.length; i < children1.length; i++) {\r\n        node1.removeChild(children1[i])\r\n      }\r\n    }\r\n  }\r\n  // 从 div2 根节点开始与 div1 比较\r\n  compareAndUpdate(div1, div2)\r\n}"], "mappings": "AAAA,OAAO,uCAAuC;AAC9C,OAAOA,IAAI,MAAM,uBAAuB;AACxC,OAAOC,UAAU,MAAM,uCAAuC;AAC9D,OAAOC,QAAQ,MAAM,qCAAqC;AAC1D,OAAOC,MAAM,MAAM,mCAAmC;AACtD,OAAOC,MAAM,MAAM,mCAAmC;AACtD,OAAOC,MAAM,MAAM,mCAAmC;AACtD,OAAOC,KAAK,MAAM,kCAAkC;AACpD,OAAOC,IAAI,MAAM,iCAAiC;AAClD,OAAOC,IAAI,MAAM,iCAAiC;AAClD,OAAOC,GAAG,MAAM,gCAAgC;AAChD,OAAOC,GAAG,MAAM,gCAAgC;AAChD,OAAOC,GAAG,MAAM,gCAAgC;AAChD,OAAOC,GAAG,MAAM,gCAAgC;AAChD,OAAOC,CAAC,MAAM,8BAA8B;AAC5C,OAAOC,WAAW,MAAM,WAAW;AAEnCd,IAAI,CAACe,gBAAgB,CAAC,YAAY,EAAEd,UAAU,CAAC;AAC/CD,IAAI,CAACe,gBAAgB,CAAC,UAAU,EAAEb,QAAQ,CAAC;AAC3CF,IAAI,CAACe,gBAAgB,CAAC,QAAQ,EAAEZ,MAAM,CAAC;AACvCH,IAAI,CAACe,gBAAgB,CAAC,QAAQ,EAAEX,MAAM,CAAC;AACvCJ,IAAI,CAACe,gBAAgB,CAAC,QAAQ,EAAEV,MAAM,CAAC;AACvCL,IAAI,CAACe,gBAAgB,CAAC,OAAO,EAAET,KAAK,CAAC;AACrCN,IAAI,CAACe,gBAAgB,CAAC,MAAM,EAAER,IAAI,CAAC;AACnCP,IAAI,CAACe,gBAAgB,CAAC,MAAM,EAAEP,IAAI,CAAC;AACnCR,IAAI,CAACe,gBAAgB,CAAC,MAAM,EAAEL,GAAG,CAAC;AAClCV,IAAI,CAACe,gBAAgB,CAAC,KAAK,EAAEL,GAAG,CAAC;AACjCV,IAAI,CAACe,gBAAgB,CAAC,KAAK,EAAEN,GAAG,CAAC;AACjCT,IAAI,CAACe,gBAAgB,CAAC,KAAK,EAAEJ,GAAG,CAAC;AACjCX,IAAI,CAACe,gBAAgB,CAAC,KAAK,EAAEH,GAAG,CAAC;AACjCZ,IAAI,CAACe,gBAAgB,CAAC,GAAG,EAAEF,CAAC,CAAC;AAE7Bb,IAAI,CAACgB,SAAS,CAAC;EAAEC,mBAAmB,EAAE;AAAK,CAAC,CAAC;;AAE7C;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAAEC,OAAO,EAAE;EAC/B,IAAMC,OAAO,GAAGD,OAAO,CAACE,gBAAgB,CAAC,UAAU,CAAC;EACpDD,OAAO,CAACE,OAAO,CAAC,UAACC,EAAE,EAAK;IACtBvB,IAAI,CAACwB,gBAAgB,CAACD,EAAE,CAAC;EAC3B,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA,SAASE,eAAeA,CAAEN,OAAO,EAAE;EACjC;EACA,IAAIO,IAAI,GAAGP,OAAO,CAACE,gBAAgB,CAAC,KAAK,CAAC;EAC1C,IAAI,CAACK,IAAI,CAACC,MAAM,EAAE;EAClBD,IAAI,CAACJ,OAAO,CAAC,UAACM,GAAG,EAAK;IACpB;IACA,IAAIC,IAAI,GAAGD,GAAG,CAACE,aAAa,CAAC,MAAM,CAAC;IACpC,IAAI,CAACD,IAAI,EAAE;IACX,IAAIE,IAAI,GAAGF,IAAI,CAACG,SAAS;IACzB;IACA,IAAIC,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;IACxCF,GAAG,CAACG,SAAS,GAAG,MAAM;IACtBH,GAAG,CAACI,WAAW,GAAG,IAAI;IACtBJ,GAAG,CAACK,YAAY,CAAC,qBAAqB,EAAEP,IAAI,CAAC;IAC7C;IACAH,GAAG,CAACW,YAAY,CAACN,GAAG,EAAEL,GAAG,CAACY,UAAU,CAAC;IACrC;IACA,IAAIC,SAAS,GAAG,IAAI3B,WAAW,CAAC,OAAO,CAAC;IACxC2B,SAAS,CAACC,EAAE,CAAC,SAAS,EAAE,YAAM;MAC5BT,GAAG,CAACU,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;MAC3BX,GAAG,CAACI,WAAW,GAAG,MAAM;MACxBQ,UAAU,CAAC,YAAM;QACfZ,GAAG,CAACI,WAAW,GAAG,IAAI;QACtBJ,GAAG,CAACU,SAAS,CAACG,MAAM,CAAC,QAAQ,CAAC;MAChC,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,CAAC;IACFL,SAAS,CAACC,EAAE,CAAC,OAAO,EAAE,YAAM;MAC1BT,GAAG,CAACI,WAAW,GAAG,MAAM;IAC1B,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,OAAO,SAASU,cAAcA,CAAE5B,OAAO,EAAE;EACvCD,aAAa,CAACC,OAAO,CAAC;EACtBM,eAAe,CAACN,OAAO,CAAC;AAC1B;;AAEA;AACA,OAAO,SAAS6B,kBAAkBA,CAAEC,IAAI,EAAEC,IAAI,EAAE;EAC9C;EACA,SAASC,gBAAgBA,CAAEC,KAAK,EAAEC,KAAK,EAAE;IACvC;IACA,IAAID,KAAK,IAAIA,KAAK,CAACE,QAAQ,KAAKC,IAAI,CAACC,SAAS,IAAIH,KAAK,CAACC,QAAQ,KAAKC,IAAI,CAACC,SAAS,EAAE;MACnF,IAAIJ,KAAK,CAACK,SAAS,KAAKJ,KAAK,CAACI,SAAS,EAAE;QACvC;QACAL,KAAK,CAACK,SAAS,GAAGJ,KAAK,CAACI,SAAS;MACnC;MACA;IACF;;IAEA;IACA,IAAI,CAACL,KAAK,IAAIA,KAAK,CAACM,OAAO,KAAKL,KAAK,CAACK,OAAO,EAAE;MAC7C;MACA,IAAMC,OAAO,GAAGN,KAAK,CAACO,SAAS,CAAC,IAAI,CAAC;MACrC,IAAIR,KAAK,EAAE;QACT;QACAA,KAAK,CAACS,UAAU,CAACC,YAAY,CAACH,OAAO,EAAEP,KAAK,CAAC;MAC/C,CAAC,MAAM;QACL;QACAC,KAAK,CAACQ,UAAU,CAACE,WAAW,CAACJ,OAAO,CAAC;MACvC;MACA;IACF;IACA;IACA,IAAIP,KAAK,CAAChB,SAAS,KAAK,iBAAiB,IAAIgB,KAAK,CAAChB,SAAS,KAAKiB,KAAK,CAACjB,SAAS,EAAE;MAChF;MACAgB,KAAK,CAAChB,SAAS,GAAGiB,KAAK,CAACjB,SAAS;IACnC;IACA;IACA,IAAIgB,KAAK,CAACY,EAAE,KAAK,iBAAiB,IAAIZ,KAAK,CAACY,EAAE,KAAKX,KAAK,CAACW,EAAE,EAAE;MAC3DZ,KAAK,CAACY,EAAE,GAAGX,KAAK,CAACW,EAAE;IACrB;IACA;IACA,IAAIZ,KAAK,CAACa,KAAK,CAACC,OAAO,KAAKb,KAAK,CAACY,KAAK,CAACC,OAAO,EAAE;MAC/Cd,KAAK,CAACa,KAAK,CAACC,OAAO,GAAGb,KAAK,CAACY,KAAK,CAACC,OAAO;IAC3C;IACA;IACA,IAAMC,SAAS,GAAGC,KAAK,CAACC,IAAI,CAACjB,KAAK,CAACkB,UAAU,CAAC,EAAC;IAC/C,IAAMC,SAAS,GAAGH,KAAK,CAACC,IAAI,CAAChB,KAAK,CAACiB,UAAU,CAAC,EAAC;IAC/C;IACAC,SAAS,CAACjD,OAAO,CAAC,UAACkD,MAAM,EAAEC,KAAK,EAAK;MACnC,IAAMC,MAAM,GAAGP,SAAS,CAACM,KAAK,CAAC;MAC/B,IAAI,CAACC,MAAM,EAAE;QACX;QACA,IAAMC,QAAQ,GAAGH,MAAM,CAACZ,SAAS,CAAC,IAAI,CAAC;QACvCR,KAAK,CAACW,WAAW,CAACY,QAAQ,CAAC;MAC7B,CAAC,MAAM;QACL;QACAxB,gBAAgB,CAACuB,MAAM,EAAEF,MAAM,CAAC;MAClC;IACF,CAAC,CAAC;IACF;IACA,IAAIL,SAAS,CAACxC,MAAM,GAAG4C,SAAS,CAAC5C,MAAM,EAAE;MACvC,KAAK,IAAIiD,CAAC,GAAGL,SAAS,CAAC5C,MAAM,EAAEiD,CAAC,GAAGT,SAAS,CAACxC,MAAM,EAAEiD,CAAC,EAAE,EAAE;QACxDxB,KAAK,CAACyB,WAAW,CAACV,SAAS,CAACS,CAAC,CAAC,CAAC;MACjC;IACF;EACF;EACA;EACAzB,gBAAgB,CAACF,IAAI,EAAEC,IAAI,CAAC;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}