{"ast": null, "code": "\"use strict\";\n\n// convert string to array (typed, when possible)\n// <PERSON><PERSON><PERSON> disable all : because this is a utility function that was copied\n// from\n// https://github.com/open-xml-templating/pizzip/blob/34a840553c604980859dc6d0dcd1f89b6e5527b3/es6/utf8.js#L33\n// eslint-disable-next-line complexity\nfunction string2buf(str) {\n  var c,\n    c2,\n    mPos,\n    i,\n    bufLen = 0;\n  var strLen = str.length;\n\n  // count binary size\n  for (mPos = 0; mPos < strLen; mPos++) {\n    c = str.charCodeAt(mPos);\n    if ((c & 0xfc00) === 0xd800 && mPos + 1 < strLen) {\n      c2 = str.charCodeAt(mPos + 1);\n      if ((c2 & 0xfc00) === 0xdc00) {\n        c = 0x10000 + (c - 0xd800 << 10) + (c2 - 0xdc00);\n        mPos++;\n      }\n    }\n    bufLen += c < 0x80 ? 1 : c < 0x800 ? 2 : c < 0x10000 ? 3 : 4;\n  }\n\n  // allocate buffer\n  var buf = new Uint8Array(bufLen);\n\n  // convert\n  for (i = 0, mPos = 0; i < bufLen; mPos++) {\n    c = str.charCodeAt(mPos);\n    if ((c & 0xfc00) === 0xd800 && mPos + 1 < strLen) {\n      c2 = str.charCodeAt(mPos + 1);\n      if ((c2 & 0xfc00) === 0xdc00) {\n        c = 0x10000 + (c - 0xd800 << 10) + (c2 - 0xdc00);\n        mPos++;\n      }\n    }\n    if (c < 0x80) {\n      /* one byte */\n      buf[i++] = c;\n    } else if (c < 0x800) {\n      /* two bytes */\n      buf[i++] = 0xc0 | c >>> 6;\n      buf[i++] = 0x80 | c & 0x3f;\n    } else if (c < 0x10000) {\n      /* three bytes */\n      buf[i++] = 0xe0 | c >>> 12;\n      buf[i++] = 0x80 | c >>> 6 & 0x3f;\n      buf[i++] = 0x80 | c & 0x3f;\n    } else {\n      /* four bytes */\n      buf[i++] = 0xf0 | c >>> 18;\n      buf[i++] = 0x80 | c >>> 12 & 0x3f;\n      buf[i++] = 0x80 | c >>> 6 & 0x3f;\n      buf[i++] = 0x80 | c & 0x3f;\n    }\n  }\n  return buf;\n}\n// Stryker restore all\n\nfunction postrender(parts, options) {\n  for (var i = 0, l = options.modules.length; i < l; i++) {\n    var _module = options.modules[i];\n    parts = _module.postrender(parts, options);\n  }\n  var fullLength = 0;\n  var newParts = options.joinUncorrupt(parts, options);\n  var longStr = \"\";\n  var lenStr = 0;\n  var maxCompact = 65536;\n  var uintArrays = [];\n  for (var _i = 0, len = newParts.length; _i < len; _i++) {\n    var part = newParts[_i];\n\n    // This condition should be hit in the integration test at :\n    // it(\"should not regress with long file (hit maxCompact value of 65536)\", function () {\n    // Stryker disable all : because this is an optimisation that won't make any tests fail\n    if (part.length + lenStr > maxCompact) {\n      var _arr = string2buf(longStr);\n      fullLength += _arr.length;\n      uintArrays.push(_arr);\n      longStr = \"\";\n    }\n    // Stryker restore all\n\n    longStr += part;\n    lenStr += part.length;\n    delete newParts[_i];\n  }\n  var arr = string2buf(longStr);\n  fullLength += arr.length;\n  uintArrays.push(arr);\n  var array = new Uint8Array(fullLength);\n  var j = 0;\n\n  // Stryker disable all : because this is an optimisation that won't make any tests fail\n  uintArrays.forEach(function (buf) {\n    for (var _i2 = 0; _i2 < buf.length; ++_i2) {\n      array[_i2 + j] = buf[_i2];\n    }\n    j += buf.length;\n  });\n  // Stryker restore all\n  return array;\n}\nmodule.exports = postrender;", "map": {"version": 3, "names": ["string2buf", "str", "c", "c2", "mPos", "i", "bufLen", "strLen", "length", "charCodeAt", "buf", "Uint8Array", "postrender", "parts", "options", "l", "modules", "_module", "full<PERSON>ength", "newParts", "joinUncorrupt", "longStr", "lenStr", "maxCompact", "uintArrays", "_i", "len", "part", "_arr", "push", "arr", "array", "j", "for<PERSON>ach", "_i2", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/docxtemplater@3.52.0/node_modules/docxtemplater/js/postrender.js"], "sourcesContent": ["\"use strict\";\n\n// convert string to array (typed, when possible)\n// <PERSON><PERSON><PERSON> disable all : because this is a utility function that was copied\n// from\n// https://github.com/open-xml-templating/pizzip/blob/34a840553c604980859dc6d0dcd1f89b6e5527b3/es6/utf8.js#L33\n// eslint-disable-next-line complexity\nfunction string2buf(str) {\n  var c,\n    c2,\n    mPos,\n    i,\n    bufLen = 0;\n  var strLen = str.length;\n\n  // count binary size\n  for (mPos = 0; mPos < strLen; mPos++) {\n    c = str.charCodeAt(mPos);\n    if ((c & 0xfc00) === 0xd800 && mPos + 1 < strLen) {\n      c2 = str.charCodeAt(mPos + 1);\n      if ((c2 & 0xfc00) === 0xdc00) {\n        c = 0x10000 + (c - 0xd800 << 10) + (c2 - 0xdc00);\n        mPos++;\n      }\n    }\n    bufLen += c < 0x80 ? 1 : c < 0x800 ? 2 : c < 0x10000 ? 3 : 4;\n  }\n\n  // allocate buffer\n  var buf = new Uint8Array(bufLen);\n\n  // convert\n  for (i = 0, mPos = 0; i < bufLen; mPos++) {\n    c = str.charCodeAt(mPos);\n    if ((c & 0xfc00) === 0xd800 && mPos + 1 < strLen) {\n      c2 = str.charCodeAt(mPos + 1);\n      if ((c2 & 0xfc00) === 0xdc00) {\n        c = 0x10000 + (c - 0xd800 << 10) + (c2 - 0xdc00);\n        mPos++;\n      }\n    }\n    if (c < 0x80) {\n      /* one byte */\n      buf[i++] = c;\n    } else if (c < 0x800) {\n      /* two bytes */\n      buf[i++] = 0xc0 | c >>> 6;\n      buf[i++] = 0x80 | c & 0x3f;\n    } else if (c < 0x10000) {\n      /* three bytes */\n      buf[i++] = 0xe0 | c >>> 12;\n      buf[i++] = 0x80 | c >>> 6 & 0x3f;\n      buf[i++] = 0x80 | c & 0x3f;\n    } else {\n      /* four bytes */\n      buf[i++] = 0xf0 | c >>> 18;\n      buf[i++] = 0x80 | c >>> 12 & 0x3f;\n      buf[i++] = 0x80 | c >>> 6 & 0x3f;\n      buf[i++] = 0x80 | c & 0x3f;\n    }\n  }\n  return buf;\n}\n// Stryker restore all\n\nfunction postrender(parts, options) {\n  for (var i = 0, l = options.modules.length; i < l; i++) {\n    var _module = options.modules[i];\n    parts = _module.postrender(parts, options);\n  }\n  var fullLength = 0;\n  var newParts = options.joinUncorrupt(parts, options);\n  var longStr = \"\";\n  var lenStr = 0;\n  var maxCompact = 65536;\n  var uintArrays = [];\n  for (var _i = 0, len = newParts.length; _i < len; _i++) {\n    var part = newParts[_i];\n\n    // This condition should be hit in the integration test at :\n    // it(\"should not regress with long file (hit maxCompact value of 65536)\", function () {\n    // Stryker disable all : because this is an optimisation that won't make any tests fail\n    if (part.length + lenStr > maxCompact) {\n      var _arr = string2buf(longStr);\n      fullLength += _arr.length;\n      uintArrays.push(_arr);\n      longStr = \"\";\n    }\n    // Stryker restore all\n\n    longStr += part;\n    lenStr += part.length;\n    delete newParts[_i];\n  }\n  var arr = string2buf(longStr);\n  fullLength += arr.length;\n  uintArrays.push(arr);\n  var array = new Uint8Array(fullLength);\n  var j = 0;\n\n  // Stryker disable all : because this is an optimisation that won't make any tests fail\n  uintArrays.forEach(function (buf) {\n    for (var _i2 = 0; _i2 < buf.length; ++_i2) {\n      array[_i2 + j] = buf[_i2];\n    }\n    j += buf.length;\n  });\n  // Stryker restore all\n  return array;\n}\nmodule.exports = postrender;"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA,SAASA,UAAUA,CAACC,GAAG,EAAE;EACvB,IAAIC,CAAC;IACHC,EAAE;IACFC,IAAI;IACJC,CAAC;IACDC,MAAM,GAAG,CAAC;EACZ,IAAIC,MAAM,GAAGN,GAAG,CAACO,MAAM;;EAEvB;EACA,KAAKJ,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGG,MAAM,EAAEH,IAAI,EAAE,EAAE;IACpCF,CAAC,GAAGD,GAAG,CAACQ,UAAU,CAACL,IAAI,CAAC;IACxB,IAAI,CAACF,CAAC,GAAG,MAAM,MAAM,MAAM,IAAIE,IAAI,GAAG,CAAC,GAAGG,MAAM,EAAE;MAChDJ,EAAE,GAAGF,GAAG,CAACQ,UAAU,CAACL,IAAI,GAAG,CAAC,CAAC;MAC7B,IAAI,CAACD,EAAE,GAAG,MAAM,MAAM,MAAM,EAAE;QAC5BD,CAAC,GAAG,OAAO,IAAIA,CAAC,GAAG,MAAM,IAAI,EAAE,CAAC,IAAIC,EAAE,GAAG,MAAM,CAAC;QAChDC,IAAI,EAAE;MACR;IACF;IACAE,MAAM,IAAIJ,CAAC,GAAG,IAAI,GAAG,CAAC,GAAGA,CAAC,GAAG,KAAK,GAAG,CAAC,GAAGA,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,CAAC;EAC9D;;EAEA;EACA,IAAIQ,GAAG,GAAG,IAAIC,UAAU,CAACL,MAAM,CAAC;;EAEhC;EACA,KAAKD,CAAC,GAAG,CAAC,EAAED,IAAI,GAAG,CAAC,EAAEC,CAAC,GAAGC,MAAM,EAAEF,IAAI,EAAE,EAAE;IACxCF,CAAC,GAAGD,GAAG,CAACQ,UAAU,CAACL,IAAI,CAAC;IACxB,IAAI,CAACF,CAAC,GAAG,MAAM,MAAM,MAAM,IAAIE,IAAI,GAAG,CAAC,GAAGG,MAAM,EAAE;MAChDJ,EAAE,GAAGF,GAAG,CAACQ,UAAU,CAACL,IAAI,GAAG,CAAC,CAAC;MAC7B,IAAI,CAACD,EAAE,GAAG,MAAM,MAAM,MAAM,EAAE;QAC5BD,CAAC,GAAG,OAAO,IAAIA,CAAC,GAAG,MAAM,IAAI,EAAE,CAAC,IAAIC,EAAE,GAAG,MAAM,CAAC;QAChDC,IAAI,EAAE;MACR;IACF;IACA,IAAIF,CAAC,GAAG,IAAI,EAAE;MACZ;MACAQ,GAAG,CAACL,CAAC,EAAE,CAAC,GAAGH,CAAC;IACd,CAAC,MAAM,IAAIA,CAAC,GAAG,KAAK,EAAE;MACpB;MACAQ,GAAG,CAACL,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGH,CAAC,KAAK,CAAC;MACzBQ,GAAG,CAACL,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGH,CAAC,GAAG,IAAI;IAC5B,CAAC,MAAM,IAAIA,CAAC,GAAG,OAAO,EAAE;MACtB;MACAQ,GAAG,CAACL,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGH,CAAC,KAAK,EAAE;MAC1BQ,GAAG,CAACL,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGH,CAAC,KAAK,CAAC,GAAG,IAAI;MAChCQ,GAAG,CAACL,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGH,CAAC,GAAG,IAAI;IAC5B,CAAC,MAAM;MACL;MACAQ,GAAG,CAACL,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGH,CAAC,KAAK,EAAE;MAC1BQ,GAAG,CAACL,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGH,CAAC,KAAK,EAAE,GAAG,IAAI;MACjCQ,GAAG,CAACL,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGH,CAAC,KAAK,CAAC,GAAG,IAAI;MAChCQ,GAAG,CAACL,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGH,CAAC,GAAG,IAAI;IAC5B;EACF;EACA,OAAOQ,GAAG;AACZ;AACA;;AAEA,SAASE,UAAUA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAClC,KAAK,IAAIT,CAAC,GAAG,CAAC,EAAEU,CAAC,GAAGD,OAAO,CAACE,OAAO,CAACR,MAAM,EAAEH,CAAC,GAAGU,CAAC,EAAEV,CAAC,EAAE,EAAE;IACtD,IAAIY,OAAO,GAAGH,OAAO,CAACE,OAAO,CAACX,CAAC,CAAC;IAChCQ,KAAK,GAAGI,OAAO,CAACL,UAAU,CAACC,KAAK,EAAEC,OAAO,CAAC;EAC5C;EACA,IAAII,UAAU,GAAG,CAAC;EAClB,IAAIC,QAAQ,GAAGL,OAAO,CAACM,aAAa,CAACP,KAAK,EAAEC,OAAO,CAAC;EACpD,IAAIO,OAAO,GAAG,EAAE;EAChB,IAAIC,MAAM,GAAG,CAAC;EACd,IAAIC,UAAU,GAAG,KAAK;EACtB,IAAIC,UAAU,GAAG,EAAE;EACnB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEC,GAAG,GAAGP,QAAQ,CAACX,MAAM,EAAEiB,EAAE,GAAGC,GAAG,EAAED,EAAE,EAAE,EAAE;IACtD,IAAIE,IAAI,GAAGR,QAAQ,CAACM,EAAE,CAAC;;IAEvB;IACA;IACA;IACA,IAAIE,IAAI,CAACnB,MAAM,GAAGc,MAAM,GAAGC,UAAU,EAAE;MACrC,IAAIK,IAAI,GAAG5B,UAAU,CAACqB,OAAO,CAAC;MAC9BH,UAAU,IAAIU,IAAI,CAACpB,MAAM;MACzBgB,UAAU,CAACK,IAAI,CAACD,IAAI,CAAC;MACrBP,OAAO,GAAG,EAAE;IACd;IACA;;IAEAA,OAAO,IAAIM,IAAI;IACfL,MAAM,IAAIK,IAAI,CAACnB,MAAM;IACrB,OAAOW,QAAQ,CAACM,EAAE,CAAC;EACrB;EACA,IAAIK,GAAG,GAAG9B,UAAU,CAACqB,OAAO,CAAC;EAC7BH,UAAU,IAAIY,GAAG,CAACtB,MAAM;EACxBgB,UAAU,CAACK,IAAI,CAACC,GAAG,CAAC;EACpB,IAAIC,KAAK,GAAG,IAAIpB,UAAU,CAACO,UAAU,CAAC;EACtC,IAAIc,CAAC,GAAG,CAAC;;EAET;EACAR,UAAU,CAACS,OAAO,CAAC,UAAUvB,GAAG,EAAE;IAChC,KAAK,IAAIwB,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGxB,GAAG,CAACF,MAAM,EAAE,EAAE0B,GAAG,EAAE;MACzCH,KAAK,CAACG,GAAG,GAAGF,CAAC,CAAC,GAAGtB,GAAG,CAACwB,GAAG,CAAC;IAC3B;IACAF,CAAC,IAAItB,GAAG,CAACF,MAAM;EACjB,CAAC,CAAC;EACF;EACA,OAAOuB,KAAK;AACd;AACAI,MAAM,CAACC,OAAO,GAAGxB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}