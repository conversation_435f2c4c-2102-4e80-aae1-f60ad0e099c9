{"ast": null, "code": "function _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { ref, onBeforeUnmount, watch, onMounted, computed, nextTick } from 'vue';\nimport { format } from 'common/js/time.js';\nimport { Close } from '@element-plus/icons-vue';\nimport Hls from 'hls.js';\n// 使用 hls.js 播放 HLS 流\n\nvar __default__ = {\n  name: 'LiveBroadcastDetails'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    modelValue: {\n      type: Boolean,\n      default: false\n    },\n    id: {\n      type: String,\n      default: ''\n    }\n  },\n  emits: ['callback', 'update:modelValue'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var emit = __emit;\n    var details = ref({});\n    var logoSrc = ref('https://xazx.cszysoft.com:8131/lzt/pageImg/open/logo?areaId=610100');\n    var activeTab = ref('details');\n    var imgUrl = computed(function () {\n      return details.value.coverImg ? api.fileURL(details.value.coverImg) : '';\n    });\n    var countdownText = ref('');\n    var countdownTimeOnly = ref('');\n\n    // 视频播放器相关\n    var videoPlayer = ref(null);\n    var player = ref(null);\n    var hls = ref(null);\n    var isPlayerInitialized = ref(false);\n    onMounted(function () {\n      // 在 onMounted 中，如果组件已经显示且有 id，则加载数据\n      if (props.modelValue && props.id) {\n        getInfo();\n      }\n    });\n\n    // 监听 modelValue 变化 - 当组件从隐藏变为显示时加载数据\n    watch(function () {\n      return props.modelValue;\n    }, function (newVal, oldVal) {\n      if (newVal && props.id && !oldVal) {\n        // 只有从 false 变为 true 且有 id 时才加载数据\n        getInfo();\n      }\n    });\n\n    // 监听 id 变化 - 当 id 改变且组件显示时加载数据\n    watch(function () {\n      return props.id;\n    }, function (newVal, oldVal) {\n      if (newVal && props.modelValue && newVal !== oldVal) {\n        getInfo();\n      }\n    });\n    var getInfo = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var res, data;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return api.videoConnectionInfo({\n                detailId: props.id\n              });\n            case 2:\n              res = _context.sent;\n              data = res.data;\n              details.value = data;\n              applyStatusFromProps();\n            case 6:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function getInfo() {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n\n    // 监听状态变化\n    watch(function () {\n      return details.value.meetingStatus;\n    }, function (newStatus, oldStatus) {\n      if (newStatus === '进行中' && oldStatus !== '进行中') {\n        // 从未开始变为进行中，初始化播放器\n        nextTick(function () {\n          _initVideoPlayer();\n        });\n      } else if (newStatus !== '进行中' && oldStatus === '进行中') {\n        // 从进行中变为其他状态，销毁播放器\n        destroyVideoPlayer();\n      }\n    });\n    var tickCountdown = function tickCountdown() {\n      if (!details.value.startTime) return;\n      var start = new Date(details.value.startTime).getTime();\n      var now = Date.now();\n      var diff = Math.max(0, start - now);\n      var sec = Math.floor(diff / 1000) % 60;\n      var min = Math.floor(diff / (1000 * 60)) % 60;\n      var hour = Math.floor(diff / (1000 * 60 * 60)) % 24;\n      var day = Math.floor(diff / (1000 * 60 * 60 * 24));\n      var timeText = '';\n      if (day > 0) {\n        // 大于1天：显示 X天X时X分\n        timeText = `${day} 天 ${hour} 时 ${min} 分`;\n      } else if (hour > 0) {\n        // 大于1小时但小于1天：显示 X时X分X秒\n        timeText = `${hour} 时 ${min} 分 ${sec} 秒`;\n      } else if (min > 0) {\n        // 大于1分钟但小于1小时：显示 X分X秒\n        timeText = `${min} 分 ${sec} 秒`;\n      } else {\n        // 小于1分钟：显示 X秒\n        timeText = `${sec} 秒`;\n      }\n\n      // 设置完整的倒计时文本（保留原有逻辑）\n      countdownText.value = `距离 ${format(details.value.startTime, 'MM/DD HH:mm')} 直播开始还有 ${timeText}`;\n      // 设置只包含时间的文本（用于两行显示）\n      countdownTimeOnly.value = timeText;\n    };\n    var timer = null;\n    var applyStatusFromProps = function applyStatusFromProps() {\n      if (timer) clearInterval(timer);\n      if (details.value.meetingStatus === '未开始') {\n        tickCountdown();\n        timer = setInterval(tickCountdown, 1000);\n      } else if (details.value.meetingStatus === '进行中') {\n        // 初始化播放器\n        nextTick(function () {\n          _initVideoPlayer();\n        });\n      }\n    };\n    var _initVideoPlayer = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2() {\n        var video, hlsUrl;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              if (!(isPlayerInitialized.value || !videoPlayer.value)) {\n                _context2.next = 2;\n                break;\n              }\n              return _context2.abrupt(\"return\");\n            case 2:\n              // 销毁现有播放器\n              destroyVideoPlayer();\n              video = videoPlayer.value;\n              player.value = video;\n              isPlayerInitialized.value = true;\n              // HLS视频流地址 - 使用您提供的地址\n              hlsUrl = 'http://prdpulllive.xylink.com/prdnemo/9681c99c9088f6520198def4ad835a39.m3u8?auth_key=0cd581927cc20104121e61dae8be17eb-1756182609-c60224498c3e4842a0034bb0575b00fb-'; // 检查浏览器是否原生支持HLS\n              if (video.canPlayType('application/vnd.apple.mpegurl')) {\n                // 原生支持HLS\n                video.src = hlsUrl;\n                setupVideoEvents();\n              } else if (Hls.isSupported()) {\n                // 使用HLS.js库\n                hls.value = new Hls({\n                  maxBufferLength: 30,\n                  maxMaxBufferLength: 60,\n                  startLevel: -1,\n                  // 自动选择适合的初始清晰度\n                  maxBufferHole: 0.5,\n                  highLatencyMode: false\n                });\n                // 加载视频流\n                hls.value.loadSource(hlsUrl);\n                hls.value.attachMedia(video);\n                // HLS事件监听\n                hls.value.on(Hls.Events.MANIFEST_PARSED, function () {\n                  console.log('视频流准备就绪，点击播放按钮开始');\n                });\n                // 错误处理\n                hls.value.on(Hls.Events.ERROR, function (_, data) {\n                  console.error('HLS错误:', data);\n                  switch (data.type) {\n                    case Hls.ErrorTypes.NETWORK_ERROR:\n                      hls.value.startLoad(); // 尝试重新加载\n                      break;\n                    case Hls.ErrorTypes.MEDIA_ERROR:\n                      hls.value.recoverMediaError(); // 尝试恢复媒体错误\n                      break;\n                    default:\n                      // 无法恢复的错误，尝试重新初始化\n                      setTimeout(_initVideoPlayer, 3000);\n                      break;\n                  }\n                });\n                setupVideoEvents();\n              }\n            case 8:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function initVideoPlayer() {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n\n    // 设置视频事件监听\n    var setupVideoEvents = function setupVideoEvents() {\n      var video = player.value;\n      if (!video) return;\n\n      // 视频可以播放时\n      video.addEventListener('canplay', function () {\n        console.log('视频准备就绪，点击播放按钮开始');\n      });\n\n      // 播放事件\n      video.addEventListener('play', function () {\n        console.log('正在播放HLS视频流');\n      });\n\n      // 暂停事件\n      video.addEventListener('pause', function () {\n        console.log('HLS视频流已暂停');\n      });\n\n      // 视频结束事件\n      video.addEventListener('ended', function () {\n        console.log('视频播放已结束');\n      });\n\n      // 音量变化事件\n      video.addEventListener('volumechange', function () {\n        console.log('音量');\n      });\n    };\n    // 销毁视频播放器\n    var destroyVideoPlayer = function destroyVideoPlayer() {\n      // 销毁 hls.js 实例\n      if (hls.value) {\n        try {\n          hls.value.destroy();\n        } catch (error) {\n          console.error('销毁HLS实例错误:', error);\n        }\n        hls.value = null;\n      }\n      if (player.value) {\n        try {\n          player.value.pause();\n          player.value.src = '';\n          player.value.load();\n        } catch (error) {\n          console.error('销毁播放器错误:', error);\n        }\n        player.value = null;\n        isPlayerInitialized.value = false;\n      }\n    };\n    onBeforeUnmount(function () {\n      if (timer) clearInterval(timer);\n      destroyVideoPlayer();\n    });\n    var handleClose = function handleClose() {\n      emit('update:modelValue', false);\n      emit('callback');\n    };\n    var __returned__ = {\n      props,\n      emit,\n      details,\n      logoSrc,\n      activeTab,\n      imgUrl,\n      countdownText,\n      countdownTimeOnly,\n      videoPlayer,\n      player,\n      hls,\n      isPlayerInitialized,\n      getInfo,\n      tickCountdown,\n      get timer() {\n        return timer;\n      },\n      set timer(v) {\n        timer = v;\n      },\n      applyStatusFromProps,\n      initVideoPlayer: _initVideoPlayer,\n      setupVideoEvents,\n      destroyVideoPlayer,\n      handleClose,\n      get api() {\n        return api;\n      },\n      ref,\n      onBeforeUnmount,\n      watch,\n      onMounted,\n      computed,\n      nextTick,\n      get format() {\n        return format;\n      },\n      get Close() {\n        return Close;\n      },\n      get Hls() {\n        return Hls;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "ref", "onBeforeUnmount", "watch", "onMounted", "computed", "nextTick", "format", "Close", "Hls", "__default__", "props", "__props", "emit", "__emit", "details", "logoSrc", "activeTab", "imgUrl", "coverImg", "fileURL", "countdownText", "countdownTimeOnly", "videoPlayer", "player", "hls", "isPlayerInitialized", "modelValue", "id", "getInfo", "newVal", "oldVal", "_ref2", "_callee", "res", "data", "_callee$", "_context", "videoConnectionInfo", "detailId", "applyStatusFromProps", "meetingStatus", "newStatus", "oldStatus", "initVideoPlayer", "destroyVideoPlayer", "tickCountdown", "startTime", "start", "Date", "getTime", "now", "diff", "Math", "max", "sec", "floor", "min", "hour", "day", "timeText", "timer", "clearInterval", "setInterval", "_ref3", "_callee2", "video", "hlsUrl", "_callee2$", "_context2", "canPlayType", "src", "setupVideoEvents", "isSupported", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maxMax<PERSON><PERSON>er<PERSON><PERSON><PERSON>", "startLevel", "maxBufferHole", "highLatencyMode", "loadSource", "attachMedia", "on", "Events", "MANIFEST_PARSED", "console", "log", "ERROR", "_", "error", "ErrorTypes", "NETWORK_ERROR", "startLoad", "MEDIA_ERROR", "recoverMediaError", "setTimeout", "addEventListener", "destroy", "pause", "load", "handleClose"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/microApp/interaction/src/views/LiveManagement/LiveBroadcastDetails.vue"], "sourcesContent": ["<template>\r\n  <transition name=\"details-fade\">\r\n    <div v-show=\"modelValue\" class=\"LiveBroadcastDetails\">\r\n      <div class=\"LiveBroadcastDetailsHeader\">\r\n        <img v-if=\"logoSrc\" class=\"LiveBroadcastDetailsEmblem\" :src=\"logoSrc\" alt=\"emblem\" />\r\n        <div class=\"LiveBroadcastDetailsHeadInfo\">\r\n          <div class=\"LiveBroadcastDetailsTitle\">{{ details.theme }}</div>\r\n          <div class=\"LiveBroadcastDetailsTime\">直播时间：{{ format(details.startTime) }} 到 {{\r\n            format(details.endTime) }}</div>\r\n        </div>\r\n        <el-icon class=\"LiveBroadcastDetailsClose\" @click=\"handleClose\">\r\n          <Close />\r\n        </el-icon>\r\n      </div>\r\n      <div class=\"LiveBroadcastDetailsBody\">\r\n        <div class=\"LiveBroadcastDetailsCanvas\">\r\n          <!-- 未开始：显示封面图 + 倒计时 -->\r\n          <div v-if=\"details.meetingStatus === '未开始'\" class=\"LiveBroadcastDetailsPoster\">\r\n            <img :src=\"imgUrl\" alt=\"海报/播放画面区域\" style=\"width: 100%;height: 100%;object-fit: contain;\">\r\n            <div class=\"LiveBroadcastDetailsCountdown\">\r\n              <div class=\"countdown-line1\">距离 {{ format(details.startTime, 'MM/DD HH:mm') }} 直播开始还有</div>\r\n              <div class=\"countdown-line2\">{{ countdownTimeOnly }}</div>\r\n            </div>\r\n          </div>\r\n          <!-- 进行中：直播播放器 -->\r\n          <div v-else-if=\"details.meetingStatus === '进行中'\" class=\"LiveBroadcastDetailsLiveOverlay\">\r\n            <div class=\"player-container\">\r\n              <video ref=\"videoPlayer\" id=\"video-player\" controls></video>\r\n            </div>\r\n          </div>\r\n          <!-- 已结束：遮罩 + 回放按钮 -->\r\n          <div v-else-if=\"details.meetingStatus === '已结束'\" class=\"LiveBroadcastDetailsEnded\">\r\n            <div class=\"LiveBroadcastDetailsEndedWrap\">\r\n              <div class=\"endedTitle\">直播已结束</div>\r\n              <el-button type=\"primary\" class=\"replayBtn\" v-if=\"details.isReplay == 1\">观看回放</el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"LiveBroadcastDetailsSidebar\">\r\n          <div class=\"LiveBroadcastDetailsTabs\">\r\n            <div :class=\"['LiveBroadcastDetailsTab', { active: activeTab === 'details' }]\"\r\n              @click=\"activeTab = 'details'\">\r\n              直播详情</div>\r\n            <div :class=\"['LiveBroadcastDetailsTab', { active: activeTab === 'interact' }]\"\r\n              @click=\"activeTab = 'interact'\">\r\n              互动</div>\r\n          </div>\r\n          <div class=\"LiveBroadcastDetailsTabPane\" v-if=\"activeTab === 'details'\">\r\n            <div class=\"detailsTitle\">{{ details?.theme || '-' }}</div>\r\n            <div class=\"detailsTime\">时间：{{ format(details?.startTime) }} - {{ format(details?.endTime) }}</div>\r\n            <div class=\"detailsDesc\">{{ details.liveDescribes || '暂无简介' }}</div>\r\n          </div>\r\n          <div class=\"LiveBroadcastDetailsTabPane\" v-else>\r\n            <div class=\"LiveBroadcastDetailsPanelTitle\">互动</div>\r\n            <div class=\"LiveBroadcastDetailsPanelText\">暂无互动内容</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </transition>\r\n\r\n</template>\r\n<script>\r\nexport default { name: 'LiveBroadcastDetails' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onBeforeUnmount, watch, onMounted, computed, nextTick } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { Close } from '@element-plus/icons-vue'\r\nimport Hls from 'hls.js'\r\n// 使用 hls.js 播放 HLS 流\r\nconst props = defineProps({\r\n  modelValue: { type: Boolean, default: false },\r\n  id: { type: String, default: '' }\r\n})\r\nconst emit = defineEmits(['callback', 'update:modelValue'])\r\nconst details = ref({})\r\nconst logoSrc = ref('https://xazx.cszysoft.com:8131/lzt/pageImg/open/logo?areaId=610100')\r\n\r\nconst activeTab = ref('details')\r\nconst imgUrl = computed(() => details.value.coverImg ? api.fileURL(details.value.coverImg) : '')\r\nconst countdownText = ref('')\r\nconst countdownTimeOnly = ref('')\r\n\r\n// 视频播放器相关\r\nconst videoPlayer = ref(null)\r\nconst player = ref(null)\r\nconst hls = ref(null)\r\nconst isPlayerInitialized = ref(false)\r\nonMounted(() => {\r\n  // 在 onMounted 中，如果组件已经显示且有 id，则加载数据\r\n  if (props.modelValue && props.id) {\r\n    getInfo()\r\n  }\r\n})\r\n\r\n// 监听 modelValue 变化 - 当组件从隐藏变为显示时加载数据\r\nwatch(() => props.modelValue, (newVal, oldVal) => {\r\n  if (newVal && props.id && !oldVal) {\r\n    // 只有从 false 变为 true 且有 id 时才加载数据\r\n    getInfo()\r\n  }\r\n})\r\n\r\n// 监听 id 变化 - 当 id 改变且组件显示时加载数据\r\nwatch(() => props.id, (newVal, oldVal) => {\r\n  if (newVal && props.modelValue && newVal !== oldVal) {\r\n    getInfo()\r\n  }\r\n})\r\n\r\nconst getInfo = async () => {\r\n  const res = await api.videoConnectionInfo({ detailId: props.id })\r\n  var { data } = res\r\n  details.value = data\r\n  applyStatusFromProps()\r\n}\r\n\r\n// 监听状态变化\r\nwatch(() => details.value.meetingStatus, (newStatus, oldStatus) => {\r\n  if (newStatus === '进行中' && oldStatus !== '进行中') {\r\n    // 从未开始变为进行中，初始化播放器\r\n    nextTick(() => {\r\n      initVideoPlayer()\r\n    })\r\n  } else if (newStatus !== '进行中' && oldStatus === '进行中') {\r\n    // 从进行中变为其他状态，销毁播放器\r\n    destroyVideoPlayer()\r\n  }\r\n})\r\n\r\nconst tickCountdown = () => {\r\n  if (!details.value.startTime) return\r\n  const start = new Date(details.value.startTime).getTime()\r\n  const now = Date.now()\r\n  let diff = Math.max(0, start - now)\r\n\r\n  const sec = Math.floor(diff / 1000) % 60\r\n  const min = Math.floor(diff / (1000 * 60)) % 60\r\n  const hour = Math.floor(diff / (1000 * 60 * 60)) % 24\r\n  const day = Math.floor(diff / (1000 * 60 * 60 * 24))\r\n\r\n  let timeText = ''\r\n\r\n  if (day > 0) {\r\n    // 大于1天：显示 X天X时X分\r\n    timeText = `${day} 天 ${hour} 时 ${min} 分`\r\n  } else if (hour > 0) {\r\n    // 大于1小时但小于1天：显示 X时X分X秒\r\n    timeText = `${hour} 时 ${min} 分 ${sec} 秒`\r\n  } else if (min > 0) {\r\n    // 大于1分钟但小于1小时：显示 X分X秒\r\n    timeText = `${min} 分 ${sec} 秒`\r\n  } else {\r\n    // 小于1分钟：显示 X秒\r\n    timeText = `${sec} 秒`\r\n  }\r\n\r\n  // 设置完整的倒计时文本（保留原有逻辑）\r\n  countdownText.value = `距离 ${format(details.value.startTime, 'MM/DD HH:mm')} 直播开始还有 ${timeText}`\r\n  // 设置只包含时间的文本（用于两行显示）\r\n  countdownTimeOnly.value = timeText\r\n}\r\n\r\nlet timer = null\r\nconst applyStatusFromProps = () => {\r\n  if (timer) clearInterval(timer)\r\n  if (details.value.meetingStatus === '未开始') {\r\n    tickCountdown()\r\n    timer = setInterval(tickCountdown, 1000)\r\n  } else if (details.value.meetingStatus === '进行中') {\r\n    // 初始化播放器\r\n    nextTick(() => {\r\n      initVideoPlayer()\r\n    })\r\n  }\r\n}\r\n\r\nconst initVideoPlayer = async () => {\r\n  if (isPlayerInitialized.value || !videoPlayer.value) return\r\n  // 销毁现有播放器\r\n  destroyVideoPlayer()\r\n  const video = videoPlayer.value\r\n  player.value = video\r\n  isPlayerInitialized.value = true\r\n  // HLS视频流地址 - 使用您提供的地址\r\n  const hlsUrl =\r\n    'http://prdpulllive.xylink.com/prdnemo/9681c99c9088f6520198def4ad835a39.m3u8?auth_key=0cd581927cc20104121e61dae8be17eb-1756182609-c60224498c3e4842a0034bb0575b00fb-'\r\n  // 检查浏览器是否原生支持HLS\r\n  if (video.canPlayType('application/vnd.apple.mpegurl')) {\r\n    // 原生支持HLS\r\n    video.src = hlsUrl\r\n    setupVideoEvents()\r\n  } else if (Hls.isSupported()) {\r\n    // 使用HLS.js库\r\n    hls.value = new Hls({\r\n      maxBufferLength: 30,\r\n      maxMaxBufferLength: 60,\r\n      startLevel: -1, // 自动选择适合的初始清晰度\r\n      maxBufferHole: 0.5,\r\n      highLatencyMode: false\r\n    })\r\n    // 加载视频流\r\n    hls.value.loadSource(hlsUrl)\r\n    hls.value.attachMedia(video)\r\n    // HLS事件监听\r\n    hls.value.on(Hls.Events.MANIFEST_PARSED, function () {\r\n      console.log('视频流准备就绪，点击播放按钮开始')\r\n    })\r\n    // 错误处理\r\n    hls.value.on(Hls.Events.ERROR, function (_, data) {\r\n      console.error('HLS错误:', data)\r\n      switch (data.type) {\r\n        case Hls.ErrorTypes.NETWORK_ERROR:\r\n          hls.value.startLoad() // 尝试重新加载\r\n          break\r\n        case Hls.ErrorTypes.MEDIA_ERROR:\r\n          hls.value.recoverMediaError() // 尝试恢复媒体错误\r\n          break\r\n        default:\r\n          // 无法恢复的错误，尝试重新初始化\r\n          setTimeout(initVideoPlayer, 3000)\r\n          break\r\n      }\r\n    })\r\n    setupVideoEvents()\r\n  }\r\n}\r\n\r\n// 设置视频事件监听\r\nconst setupVideoEvents = () => {\r\n  const video = player.value\r\n  if (!video) return\r\n\r\n  // 视频可以播放时\r\n  video.addEventListener('canplay', function () {\r\n    console.log('视频准备就绪，点击播放按钮开始')\r\n  })\r\n\r\n  // 播放事件\r\n  video.addEventListener('play', function () {\r\n    console.log('正在播放HLS视频流')\r\n  })\r\n\r\n  // 暂停事件\r\n  video.addEventListener('pause', function () {\r\n    console.log('HLS视频流已暂停')\r\n  })\r\n\r\n  // 视频结束事件\r\n  video.addEventListener('ended', function () {\r\n    console.log('视频播放已结束')\r\n  })\r\n\r\n  // 音量变化事件\r\n  video.addEventListener('volumechange', function () {\r\n    console.log('音量')\r\n  })\r\n}\r\n// 销毁视频播放器\r\nconst destroyVideoPlayer = () => {\r\n  // 销毁 hls.js 实例\r\n  if (hls.value) {\r\n    try {\r\n      hls.value.destroy()\r\n    } catch (error) {\r\n      console.error('销毁HLS实例错误:', error)\r\n    }\r\n    hls.value = null\r\n  }\r\n\r\n  if (player.value) {\r\n    try {\r\n      player.value.pause()\r\n      player.value.src = ''\r\n      player.value.load()\r\n    } catch (error) {\r\n      console.error('销毁播放器错误:', error)\r\n    }\r\n    player.value = null\r\n    isPlayerInitialized.value = false\r\n  }\r\n}\r\n\r\nonBeforeUnmount(() => {\r\n  if (timer) clearInterval(timer)\r\n  destroyVideoPlayer()\r\n})\r\n\r\nconst handleClose = () => {\r\n  emit('update:modelValue', false)\r\n  emit('callback')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.LiveBroadcastDetails {\r\n  position: fixed;\r\n  inset: 0;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  background: #0F0F0F;\r\n  display: flex;\r\n  flex-direction: column;\r\n  z-index: 9999;\r\n\r\n  .LiveBroadcastDetailsHeader {\r\n    position: relative;\r\n    height: 80px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 16px;\r\n    color: #fff;\r\n    background: #2B2B2B;\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.08);\r\n\r\n    .LiveBroadcastDetailsEmblem {\r\n      width: 58px;\r\n      height: 58px;\r\n      margin-right: 14px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsHeadInfo {\r\n      display: flex;\r\n      flex-direction: column;\r\n    }\r\n\r\n    .LiveBroadcastDetailsTitle {\r\n      font-family: Microsoft YaHei, Microsoft YaHei;\r\n      font-weight: bold;\r\n      font-size: 20px;\r\n      color: #FFFFFF;\r\n      margin-bottom: 12px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsTime {\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #D9D9D9;\r\n    }\r\n\r\n    .LiveBroadcastDetailsClose {\r\n      position: absolute;\r\n      right: 16px;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      color: #fff;\r\n      cursor: pointer;\r\n      font-size: 18px;\r\n      opacity: .85;\r\n    }\r\n  }\r\n\r\n  .LiveBroadcastDetailsBody {\r\n    flex: 1;\r\n    display: grid;\r\n    grid-template-columns: 1fr 360px;\r\n    gap: 16px;\r\n    padding: 16px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .LiveBroadcastDetailsCanvas {\r\n    position: relative;\r\n    height: 100%;\r\n    background: #111;\r\n    overflow: hidden;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .LiveBroadcastDetailsPoster {\r\n      width: 100%;\r\n      height: 100%;\r\n      background: #000;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      color: #d23a2e;\r\n      font-weight: bold;\r\n      font-size: 22px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsCountdown {\r\n      position: absolute;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      height: 95px;\r\n      background: rgba(0, 0, 0, 0.8);\r\n      color: #fff;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n\r\n      .countdown-line1 {\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        margin-bottom: 8px;\r\n        opacity: 0.9;\r\n      }\r\n\r\n      .countdown-line2 {\r\n        font-size: 24px;\r\n        font-weight: 600;\r\n        letter-spacing: 2px;\r\n      }\r\n    }\r\n\r\n    .LiveBroadcastDetailsLiveOverlay {\r\n      position: absolute;\r\n      left: 0;\r\n      right: 0;\r\n      top: 0;\r\n      bottom: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n\r\n      .player-container {\r\n        width: 100%;\r\n        height: 100%;\r\n\r\n        #video-player {\r\n          width: 100%;\r\n          height: 100%;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LiveBroadcastDetailsEnded {\r\n      position: absolute;\r\n      inset: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      background: rgba(0, 0, 0, .45);\r\n\r\n      .LiveBroadcastDetailsEndedWrap {\r\n        text-align: center;\r\n\r\n        .endedTitle {\r\n          color: #fff;\r\n          font-size: 18px;\r\n          margin-bottom: 12px;\r\n        }\r\n\r\n        .replayBtn {\r\n          background: linear-gradient(90deg, #5bc0ff, #5f7cff);\r\n          border: none;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .LiveBroadcastDetailsSidebar {\r\n    height: 100%;\r\n    background: #191919;\r\n    border-left: 1px solid rgba(255, 255, 255, 0.05);\r\n    color: #e8e8e8;\r\n    // padding: 14px 16px 16px 16px;\r\n    overflow: auto;\r\n\r\n    .LiveBroadcastDetailsTabs {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-around;\r\n      gap: 40px;\r\n      margin-bottom: 10px;\r\n      background: #2B2B2B;\r\n      padding: 14px 16px;\r\n\r\n      .LiveBroadcastDetailsTab {\r\n        cursor: pointer;\r\n        color: #999999;\r\n        position: relative;\r\n        font-size: 16px;\r\n        line-height: 1;\r\n        transition: color .2s ease;\r\n        font-weight: 500;\r\n\r\n        &.active {\r\n          color: #ffffff;\r\n          font-weight: 700;\r\n        }\r\n\r\n        &.active::after {\r\n          content: '';\r\n          position: absolute;\r\n          left: 0;\r\n          right: 0;\r\n          bottom: -8px;\r\n          height: 3px;\r\n          background: #54BDFF;\r\n          border-radius: 3px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LiveBroadcastDetailsTabPane {\r\n      padding: 12px 15px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsPanelTitle {\r\n      font-weight: bold;\r\n      margin-bottom: 10px;\r\n      font-size: 15px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsPanelText {\r\n      font-size: 13px;\r\n      line-height: 1.8;\r\n      color: #cfcfcf;\r\n    }\r\n\r\n    /* 详情tab内样式 */\r\n    .detailsTitle {\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #FFFFFF;\r\n      margin-bottom: 14px;\r\n    }\r\n\r\n    .detailsTime {\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #666666;\r\n      margin-bottom: 12px;\r\n    }\r\n\r\n    .detailsDesc {\r\n      font-size: 13px;\r\n      color: #d9d9d9;\r\n      line-height: 1.8;\r\n    }\r\n  }\r\n}\r\n\r\n.details-fade-enter-active,\r\n.details-fade-leave-active {\r\n  transition: opacity .2s ease;\r\n}\r\n\r\n.details-fade-enter-from,\r\n.details-fade-leave-to {\r\n  opacity: 0;\r\n}\r\n</style>\r\n"], "mappings": "+CAmEA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AADA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,GAAG,EAAEC,eAAe,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,KAAK;AAChF,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,KAAK,QAAQ,yBAAyB;AAC/C,OAAOC,GAAG,MAAM,QAAQ;AACxB;;AARA,IAAAC,WAAA,GAAe;EAAErC,IAAI,EAAE;AAAuB,CAAC;;;;;;;;;;;;;;;;;IAS/C,IAAMsC,KAAK,GAAGC,OAGZ;IACF,IAAMC,IAAI,GAAGC,MAA8C;IAC3D,IAAMC,OAAO,GAAGd,GAAG,CAAC,CAAC,CAAC,CAAC;IACvB,IAAMe,OAAO,GAAGf,GAAG,CAAC,oEAAoE,CAAC;IAEzF,IAAMgB,SAAS,GAAGhB,GAAG,CAAC,SAAS,CAAC;IAChC,IAAMiB,MAAM,GAAGb,QAAQ,CAAC;MAAA,OAAMU,OAAO,CAACnH,KAAK,CAACuH,QAAQ,GAAGnB,GAAG,CAACoB,OAAO,CAACL,OAAO,CAACnH,KAAK,CAACuH,QAAQ,CAAC,GAAG,EAAE;IAAA,EAAC;IAChG,IAAME,aAAa,GAAGpB,GAAG,CAAC,EAAE,CAAC;IAC7B,IAAMqB,iBAAiB,GAAGrB,GAAG,CAAC,EAAE,CAAC;;IAEjC;IACA,IAAMsB,WAAW,GAAGtB,GAAG,CAAC,IAAI,CAAC;IAC7B,IAAMuB,MAAM,GAAGvB,GAAG,CAAC,IAAI,CAAC;IACxB,IAAMwB,GAAG,GAAGxB,GAAG,CAAC,IAAI,CAAC;IACrB,IAAMyB,mBAAmB,GAAGzB,GAAG,CAAC,KAAK,CAAC;IACtCG,SAAS,CAAC,YAAM;MACd;MACA,IAAIO,KAAK,CAACgB,UAAU,IAAIhB,KAAK,CAACiB,EAAE,EAAE;QAChCC,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;;IAEF;IACA1B,KAAK,CAAC;MAAA,OAAMQ,KAAK,CAACgB,UAAU;IAAA,GAAE,UAACG,MAAM,EAAEC,MAAM,EAAK;MAChD,IAAID,MAAM,IAAInB,KAAK,CAACiB,EAAE,IAAI,CAACG,MAAM,EAAE;QACjC;QACAF,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;;IAEF;IACA1B,KAAK,CAAC;MAAA,OAAMQ,KAAK,CAACiB,EAAE;IAAA,GAAE,UAACE,MAAM,EAAEC,MAAM,EAAK;MACxC,IAAID,MAAM,IAAInB,KAAK,CAACgB,UAAU,IAAIG,MAAM,KAAKC,MAAM,EAAE;QACnDF,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC;IAEF,IAAMA,OAAO;MAAA,IAAAG,KAAA,GAAArC,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2D,QAAA;QAAA,IAAAC,GAAA,EAAAC,IAAA;QAAA,OAAAjJ,mBAAA,GAAAuB,IAAA,UAAA2H,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAtD,IAAA,GAAAsD,QAAA,CAAAjF,IAAA;YAAA;cAAAiF,QAAA,CAAAjF,IAAA;cAAA,OACI4C,GAAG,CAACsC,mBAAmB,CAAC;gBAAEC,QAAQ,EAAE5B,KAAK,CAACiB;cAAG,CAAC,CAAC;YAAA;cAA3DM,GAAG,GAAAG,QAAA,CAAAxF,IAAA;cACHsF,IAAI,GAAKD,GAAG,CAAZC,IAAI;cACVpB,OAAO,CAACnH,KAAK,GAAGuI,IAAI;cACpBK,oBAAoB,CAAC,CAAC;YAAA;YAAA;cAAA,OAAAH,QAAA,CAAAnD,IAAA;UAAA;QAAA,GAAA+C,OAAA;MAAA,CACvB;MAAA,gBALKJ,OAAOA,CAAA;QAAA,OAAAG,KAAA,CAAAnC,KAAA,OAAAD,SAAA;MAAA;IAAA,GAKZ;;IAED;IACAO,KAAK,CAAC;MAAA,OAAMY,OAAO,CAACnH,KAAK,CAAC6I,aAAa;IAAA,GAAE,UAACC,SAAS,EAAEC,SAAS,EAAK;MACjE,IAAID,SAAS,KAAK,KAAK,IAAIC,SAAS,KAAK,KAAK,EAAE;QAC9C;QACArC,QAAQ,CAAC,YAAM;UACbsC,gBAAe,CAAC,CAAC;QACnB,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIF,SAAS,KAAK,KAAK,IAAIC,SAAS,KAAK,KAAK,EAAE;QACrD;QACAE,kBAAkB,CAAC,CAAC;MACtB;IACF,CAAC,CAAC;IAEF,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1B,IAAI,CAAC/B,OAAO,CAACnH,KAAK,CAACmJ,SAAS,EAAE;MAC9B,IAAMC,KAAK,GAAG,IAAIC,IAAI,CAAClC,OAAO,CAACnH,KAAK,CAACmJ,SAAS,CAAC,CAACG,OAAO,CAAC,CAAC;MACzD,IAAMC,GAAG,GAAGF,IAAI,CAACE,GAAG,CAAC,CAAC;MACtB,IAAIC,IAAI,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEN,KAAK,GAAGG,GAAG,CAAC;MAEnC,IAAMI,GAAG,GAAGF,IAAI,CAACG,KAAK,CAACJ,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE;MACxC,IAAMK,GAAG,GAAGJ,IAAI,CAACG,KAAK,CAACJ,IAAI,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;MAC/C,IAAMM,IAAI,GAAGL,IAAI,CAACG,KAAK,CAACJ,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,GAAG,EAAE;MACrD,IAAMO,GAAG,GAAGN,IAAI,CAACG,KAAK,CAACJ,IAAI,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;MAEpD,IAAIQ,QAAQ,GAAG,EAAE;MAEjB,IAAID,GAAG,GAAG,CAAC,EAAE;QACX;QACAC,QAAQ,GAAG,GAAGD,GAAG,MAAMD,IAAI,MAAMD,GAAG,IAAI;MAC1C,CAAC,MAAM,IAAIC,IAAI,GAAG,CAAC,EAAE;QACnB;QACAE,QAAQ,GAAG,GAAGF,IAAI,MAAMD,GAAG,MAAMF,GAAG,IAAI;MAC1C,CAAC,MAAM,IAAIE,GAAG,GAAG,CAAC,EAAE;QAClB;QACAG,QAAQ,GAAG,GAAGH,GAAG,MAAMF,GAAG,IAAI;MAChC,CAAC,MAAM;QACL;QACAK,QAAQ,GAAG,GAAGL,GAAG,IAAI;MACvB;;MAEA;MACAlC,aAAa,CAACzH,KAAK,GAAG,MAAM2G,MAAM,CAACQ,OAAO,CAACnH,KAAK,CAACmJ,SAAS,EAAE,aAAa,CAAC,WAAWa,QAAQ,EAAE;MAC/F;MACAtC,iBAAiB,CAAC1H,KAAK,GAAGgK,QAAQ;IACpC,CAAC;IAED,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAMrB,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA,EAAS;MACjC,IAAIqB,KAAK,EAAEC,aAAa,CAACD,KAAK,CAAC;MAC/B,IAAI9C,OAAO,CAACnH,KAAK,CAAC6I,aAAa,KAAK,KAAK,EAAE;QACzCK,aAAa,CAAC,CAAC;QACfe,KAAK,GAAGE,WAAW,CAACjB,aAAa,EAAE,IAAI,CAAC;MAC1C,CAAC,MAAM,IAAI/B,OAAO,CAACnH,KAAK,CAAC6I,aAAa,KAAK,KAAK,EAAE;QAChD;QACAnC,QAAQ,CAAC,YAAM;UACbsC,gBAAe,CAAC,CAAC;QACnB,CAAC,CAAC;MACJ;IACF,CAAC;IAED,IAAMA,gBAAe;MAAA,IAAAoB,KAAA,GAAArE,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2F,SAAA;QAAA,IAAAC,KAAA,EAAAC,MAAA;QAAA,OAAAjL,mBAAA,GAAAuB,IAAA,UAAA2J,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAtF,IAAA,GAAAsF,SAAA,CAAAjH,IAAA;YAAA;cAAA,MAClBsE,mBAAmB,CAAC9H,KAAK,IAAI,CAAC2H,WAAW,CAAC3H,KAAK;gBAAAyK,SAAA,CAAAjH,IAAA;gBAAA;cAAA;cAAA,OAAAiH,SAAA,CAAArH,MAAA;YAAA;cACnD;cACA6F,kBAAkB,CAAC,CAAC;cACdqB,KAAK,GAAG3C,WAAW,CAAC3H,KAAK;cAC/B4H,MAAM,CAAC5H,KAAK,GAAGsK,KAAK;cACpBxC,mBAAmB,CAAC9H,KAAK,GAAG,IAAI;cAChC;cACMuK,MAAM,GACV,oKAAoK,EACtK;cACA,IAAID,KAAK,CAACI,WAAW,CAAC,+BAA+B,CAAC,EAAE;gBACtD;gBACAJ,KAAK,CAACK,GAAG,GAAGJ,MAAM;gBAClBK,gBAAgB,CAAC,CAAC;cACpB,CAAC,MAAM,IAAI/D,GAAG,CAACgE,WAAW,CAAC,CAAC,EAAE;gBAC5B;gBACAhD,GAAG,CAAC7H,KAAK,GAAG,IAAI6G,GAAG,CAAC;kBAClBiE,eAAe,EAAE,EAAE;kBACnBC,kBAAkB,EAAE,EAAE;kBACtBC,UAAU,EAAE,CAAC,CAAC;kBAAE;kBAChBC,aAAa,EAAE,GAAG;kBAClBC,eAAe,EAAE;gBACnB,CAAC,CAAC;gBACF;gBACArD,GAAG,CAAC7H,KAAK,CAACmL,UAAU,CAACZ,MAAM,CAAC;gBAC5B1C,GAAG,CAAC7H,KAAK,CAACoL,WAAW,CAACd,KAAK,CAAC;gBAC5B;gBACAzC,GAAG,CAAC7H,KAAK,CAACqL,EAAE,CAACxE,GAAG,CAACyE,MAAM,CAACC,eAAe,EAAE,YAAY;kBACnDC,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;gBACjC,CAAC,CAAC;gBACF;gBACA5D,GAAG,CAAC7H,KAAK,CAACqL,EAAE,CAACxE,GAAG,CAACyE,MAAM,CAACI,KAAK,EAAE,UAAUC,CAAC,EAAEpD,IAAI,EAAE;kBAChDiD,OAAO,CAACI,KAAK,CAAC,QAAQ,EAAErD,IAAI,CAAC;kBAC7B,QAAQA,IAAI,CAACpH,IAAI;oBACf,KAAK0F,GAAG,CAACgF,UAAU,CAACC,aAAa;sBAC/BjE,GAAG,CAAC7H,KAAK,CAAC+L,SAAS,CAAC,CAAC,EAAC;sBACtB;oBACF,KAAKlF,GAAG,CAACgF,UAAU,CAACG,WAAW;sBAC7BnE,GAAG,CAAC7H,KAAK,CAACiM,iBAAiB,CAAC,CAAC,EAAC;sBAC9B;oBACF;sBACE;sBACAC,UAAU,CAAClD,gBAAe,EAAE,IAAI,CAAC;sBACjC;kBACJ;gBACF,CAAC,CAAC;gBACF4B,gBAAgB,CAAC,CAAC;cACpB;YAAC;YAAA;cAAA,OAAAH,SAAA,CAAAnF,IAAA;UAAA;QAAA,GAAA+E,QAAA;MAAA,CACF;MAAA,gBAjDKrB,eAAeA,CAAA;QAAA,OAAAoB,KAAA,CAAAnE,KAAA,OAAAD,SAAA;MAAA;IAAA,GAiDpB;;IAED;IACA,IAAM4E,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;MAC7B,IAAMN,KAAK,GAAG1C,MAAM,CAAC5H,KAAK;MAC1B,IAAI,CAACsK,KAAK,EAAE;;MAEZ;MACAA,KAAK,CAAC6B,gBAAgB,CAAC,SAAS,EAAE,YAAY;QAC5CX,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAChC,CAAC,CAAC;;MAEF;MACAnB,KAAK,CAAC6B,gBAAgB,CAAC,MAAM,EAAE,YAAY;QACzCX,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;MAC3B,CAAC,CAAC;;MAEF;MACAnB,KAAK,CAAC6B,gBAAgB,CAAC,OAAO,EAAE,YAAY;QAC1CX,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;MAC1B,CAAC,CAAC;;MAEF;MACAnB,KAAK,CAAC6B,gBAAgB,CAAC,OAAO,EAAE,YAAY;QAC1CX,OAAO,CAACC,GAAG,CAAC,SAAS,CAAC;MACxB,CAAC,CAAC;;MAEF;MACAnB,KAAK,CAAC6B,gBAAgB,CAAC,cAAc,EAAE,YAAY;QACjDX,OAAO,CAACC,GAAG,CAAC,IAAI,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC;IACD;IACA,IAAMxC,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA,EAAS;MAC/B;MACA,IAAIpB,GAAG,CAAC7H,KAAK,EAAE;QACb,IAAI;UACF6H,GAAG,CAAC7H,KAAK,CAACoM,OAAO,CAAC,CAAC;QACrB,CAAC,CAAC,OAAOR,KAAK,EAAE;UACdJ,OAAO,CAACI,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;QACpC;QACA/D,GAAG,CAAC7H,KAAK,GAAG,IAAI;MAClB;MAEA,IAAI4H,MAAM,CAAC5H,KAAK,EAAE;QAChB,IAAI;UACF4H,MAAM,CAAC5H,KAAK,CAACqM,KAAK,CAAC,CAAC;UACpBzE,MAAM,CAAC5H,KAAK,CAAC2K,GAAG,GAAG,EAAE;UACrB/C,MAAM,CAAC5H,KAAK,CAACsM,IAAI,CAAC,CAAC;QACrB,CAAC,CAAC,OAAOV,KAAK,EAAE;UACdJ,OAAO,CAACI,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;QAClC;QACAhE,MAAM,CAAC5H,KAAK,GAAG,IAAI;QACnB8H,mBAAmB,CAAC9H,KAAK,GAAG,KAAK;MACnC;IACF,CAAC;IAEDsG,eAAe,CAAC,YAAM;MACpB,IAAI2D,KAAK,EAAEC,aAAa,CAACD,KAAK,CAAC;MAC/BhB,kBAAkB,CAAC,CAAC;IACtB,CAAC,CAAC;IAEF,IAAMsD,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxBtF,IAAI,CAAC,mBAAmB,EAAE,KAAK,CAAC;MAChCA,IAAI,CAAC,UAAU,CAAC;IAClB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}