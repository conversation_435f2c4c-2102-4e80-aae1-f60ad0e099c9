{"ast": null, "code": "import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inherits(t, e) {\n  if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n  t.prototype = Object.create(e && e.prototype, {\n    constructor: {\n      value: t,\n      writable: !0,\n      configurable: !0\n    }\n  }), Object.defineProperty(t, \"prototype\", {\n    writable: !1\n  }), e && setPrototypeOf(t, e);\n}\nexport { _inherits as default };", "map": {"version": 3, "names": ["setPrototypeOf", "_inherits", "t", "e", "TypeError", "prototype", "Object", "create", "constructor", "value", "writable", "configurable", "defineProperty", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/@babel+runtime@7.26.0/node_modules/@babel/runtime/helpers/esm/inherits.js"], "sourcesContent": ["import setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _inherits(t, e) {\n  if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\");\n  t.prototype = Object.create(e && e.prototype, {\n    constructor: {\n      value: t,\n      writable: !0,\n      configurable: !0\n    }\n  }), Object.defineProperty(t, \"prototype\", {\n    writable: !1\n  }), e && setPrototypeOf(t, e);\n}\nexport { _inherits as default };"], "mappings": "AAAA,OAAOA,cAAc,MAAM,qBAAqB;AAChD,SAASC,SAASA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACvB,IAAI,UAAU,IAAI,OAAOA,CAAC,IAAI,IAAI,KAAKA,CAAC,EAAE,MAAM,IAAIC,SAAS,CAAC,oDAAoD,CAAC;EACnHF,CAAC,CAACG,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACJ,CAAC,IAAIA,CAAC,CAACE,SAAS,EAAE;IAC5CG,WAAW,EAAE;MACXC,KAAK,EAAEP,CAAC;MACRQ,QAAQ,EAAE,CAAC,CAAC;MACZC,YAAY,EAAE,CAAC;IACjB;EACF,CAAC,CAAC,EAAEL,MAAM,CAACM,cAAc,CAACV,CAAC,EAAE,WAAW,EAAE;IACxCQ,QAAQ,EAAE,CAAC;EACb,CAAC,CAAC,EAAEP,CAAC,IAAIH,cAAc,CAACE,CAAC,EAAEC,CAAC,CAAC;AAC/B;AACA,SAASF,SAAS,IAAIY,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}