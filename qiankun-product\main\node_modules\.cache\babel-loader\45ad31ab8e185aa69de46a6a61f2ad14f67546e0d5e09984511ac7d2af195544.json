{"ast": null, "code": "import { defineAsyncComponent, ref, onMounted, nextTick } from 'vue';\nimport { useRoute, useRouter } from 'vue-router';\nimport { qiankun, LayoutView, ChatMethod, AiChatMethod, refreshIcon, loginHintMethod } from './LayoutView.js';\nimport { systemLogo, systemName, whetherAiChat, systemNameAreaPrefix, layoutNameBg, layoutChildBg, layoutChildNameBg } from 'common/js/system_var.js';\nimport { ArrowRight } from '@element-plus/icons-vue';\nvar __default__ = {\n  name: 'LayoutView'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var HelpDocument = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/HelpDocument');\n    });\n    var EditPassWord = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/EditPassWord');\n    });\n    // const LayoutBoxMessage = defineAsyncComponent(() => import('../LayoutContainer/components/LayoutBoxMessage'))\n    // const LayoutPersonalDoList = defineAsyncComponent(() => import('../LayoutContainer/components/LayoutPersonalDoList'))\n    var GlobalRegionSelect = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/GlobalRegionSelect');\n    });\n    var GlobalChatFloating = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/GlobalChatFloating');\n    });\n    var GlobalFloatingWindow = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/GlobalFloatingWindow');\n    });\n    var GlobalAiControls = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/GlobalAiControls');\n    });\n    var GlobalAiChat = defineAsyncComponent(function () {\n      return import('../GlobalAiChat/GlobalAiChat');\n    });\n    var LayoutMenu = defineAsyncComponent(function () {\n      return import('./component/LayoutMenu/LayoutMenu.vue');\n    });\n    var suggestPop = defineAsyncComponent(function () {\n      return import('./component/suggestPop');\n    });\n    var qusetionAnswering = defineAsyncComponent(function () {\n      return import('./component/question-answering.vue');\n    });\n    var SubAppViewport = {\n      name: 'SubAppViewport',\n      props: ['name'],\n      template: `<div :id=\"name\" class=\"subApp-viewport\"></div>`\n    };\n    var _qiankun = qiankun(useRoute()),\n      isMain = _qiankun.isMain;\n    var route = useRoute();\n    // const currentFontSizeLabel = ref('切换字号')\n    var isSys = ref(false);\n    // const loginSystemName = computed(() => {\n    //   const name = (platformAreaName.value || '') + systemName.value\n    //   const num = Number(loginNameLineFeedPosition.value || '0') || 0\n    //   return num ? name.substring(0, num) + '\\n' + name.substring(num) : name\n    // })\n    var _LayoutView = LayoutView(useRoute(), useRouter()),\n      user = _LayoutView.user,\n      area = _LayoutView.area,\n      role = _LayoutView.role,\n      left = _LayoutView.left,\n      width = _LayoutView.width,\n      LayoutViewBox = _LayoutView.LayoutViewBox,\n      LayoutViewInfo = _LayoutView.LayoutViewInfo,\n      helpShow = _LayoutView.helpShow,\n      handleCommand = _LayoutView.handleCommand,\n      editPassWordShow = _LayoutView.editPassWordShow,\n      verifyEditPassWord = _LayoutView.verifyEditPassWord,\n      verifyEditPassWordShow = _LayoutView.verifyEditPassWordShow,\n      editPassWordCallback = _LayoutView.editPassWordCallback,\n      regionId = _LayoutView.regionId,\n      regionName = _LayoutView.regionName,\n      regionSelect = _LayoutView.regionSelect,\n      isRegionSelectShow = _LayoutView.isRegionSelectShow,\n      isView = _LayoutView.isView,\n      isChildView = _LayoutView.isChildView,\n      tabMenu = _LayoutView.tabMenu,\n      tabMenuData = _LayoutView.tabMenuData,\n      handleClick = _LayoutView.handleClick,\n      menuId = _LayoutView.menuId,\n      menuData = _LayoutView.menuData,\n      menuClick = _LayoutView.menuClick,\n      handleBreadcrumb = _LayoutView.handleBreadcrumb,\n      WorkBenchObj = _LayoutView.WorkBenchObj,\n      childData = _LayoutView.childData,\n      WorkBenchReturn = _LayoutView.WorkBenchReturn,\n      isRefresh = _LayoutView.isRefresh,\n      keepAliveRoute = _LayoutView.keepAliveRoute,\n      tabData = _LayoutView.tabData,\n      tabClick = _LayoutView.tabClick,\n      handleRefresh = _LayoutView.handleRefresh,\n      handleClose = _LayoutView.handleClose,\n      handleCloseOther = _LayoutView.handleCloseOther,\n      isMicroApp = _LayoutView.isMicroApp,\n      MicroApp = _LayoutView.MicroApp,\n      openPage = _LayoutView.openPage,\n      leftMenuData = _LayoutView.leftMenuData,\n      WorkBenchList = _LayoutView.WorkBenchList;\n    onMounted(function () {\n      nextTick(function () {\n        setTimeout(function () {\n          var roleList = JSON.parse(sessionStorage.getItem('role'));\n          console.log('当前角色===>', roleList);\n          if (roleList) {\n            isSys.value = roleList === null || roleList === void 0 ? void 0 : roleList.includes('管理员');\n          }\n        }, 1000);\n      });\n    });\n    var _ChatMethod = ChatMethod(),\n      rongCloudToken = _ChatMethod.rongCloudToken;\n    var _AiChatMethod = AiChatMethod(),\n      AiChatTargetWidth = _AiChatMethod.AiChatTargetWidth,\n      AiChatViewType = _AiChatMethod.AiChatViewType,\n      AiChatWindowShow = _AiChatMethod.AiChatWindowShow;\n\n    // const handleFontSizeChange = (command) => {\n    //   if (command === 'default') currentFontSizeLabel.value = '默认字号'\n    //   if (command === 'large') currentFontSizeLabel.value = '大字号'\n    //   if (command === 'xlarge') currentFontSizeLabel.value = '超大字号'\n    //   // 这里可以加实际字号切换逻辑\n    // }\n    var returnHome = function returnHome() {\n      if (isChildView.value) {\n        WorkBenchReturn();\n      } else {\n        openPage({\n          key: 'routePath',\n          value: '/homePage'\n        });\n      }\n    };\n\n    // 切换新版本\n    var newVersion = function newVersion() {\n      console.log('切换到新版本');\n      openPage({\n        key: 'routePath',\n        value: '/WorkBenchCopy'\n      });\n    };\n    // 资讯\n    var openInformation = function openInformation() {\n      openPage({\n        key: 'routePath',\n        value: '/information/AllInformation?moduleId=1&moduleName=资讯'\n      });\n      // const filtered = (WorkBenchList.value || []).filter(item => item.routePath !== '/homePage')\n      // const systemOperation = filtered.flatMap(item => item.children).find(child => child.name === '新闻资讯')\n      // leftMenuData(systemOperation)\n    };\n    // 系统管理\n    var sysManagement = function sysManagement() {\n      var filtered = (WorkBenchList.value || []).filter(function (item) {\n        return item.routePath !== '/homePage';\n      });\n      var systemOperation = filtered.flatMap(function (item) {\n        return item.children;\n      }).find(function (child) {\n        return child.name === '系统运维';\n      });\n      leftMenuData(systemOperation);\n    };\n    // 跳转到我的\n    var sysUser = function sysUser() {\n      var filtered = (WorkBenchList.value || []).filter(function (item) {\n        return item.routePath !== '/homePage';\n      });\n      var myOperation = filtered.flatMap(function (item) {\n        return item.children;\n      }).find(function (child) {\n        return child.name === '我的';\n      });\n      leftMenuData(myOperation);\n    };\n    var _loginHintMethod = loginHintMethod(),\n      loginHintShow = _loginHintMethod.loginHintShow;\n    var __returned__ = {\n      HelpDocument,\n      EditPassWord,\n      GlobalRegionSelect,\n      GlobalChatFloating,\n      GlobalFloatingWindow,\n      GlobalAiControls,\n      GlobalAiChat,\n      LayoutMenu,\n      suggestPop,\n      qusetionAnswering,\n      SubAppViewport,\n      isMain,\n      route,\n      isSys,\n      user,\n      area,\n      role,\n      left,\n      width,\n      LayoutViewBox,\n      LayoutViewInfo,\n      helpShow,\n      handleCommand,\n      editPassWordShow,\n      verifyEditPassWord,\n      verifyEditPassWordShow,\n      editPassWordCallback,\n      regionId,\n      regionName,\n      regionSelect,\n      isRegionSelectShow,\n      isView,\n      isChildView,\n      tabMenu,\n      tabMenuData,\n      handleClick,\n      menuId,\n      menuData,\n      menuClick,\n      handleBreadcrumb,\n      WorkBenchObj,\n      childData,\n      WorkBenchReturn,\n      isRefresh,\n      keepAliveRoute,\n      tabData,\n      tabClick,\n      handleRefresh,\n      handleClose,\n      handleCloseOther,\n      isMicroApp,\n      MicroApp,\n      openPage,\n      leftMenuData,\n      WorkBenchList,\n      rongCloudToken,\n      AiChatTargetWidth,\n      AiChatViewType,\n      AiChatWindowShow,\n      returnHome,\n      newVersion,\n      openInformation,\n      sysManagement,\n      sysUser,\n      loginHintShow,\n      defineAsyncComponent,\n      ref,\n      onMounted,\n      nextTick,\n      get useRoute() {\n        return useRoute;\n      },\n      get useRouter() {\n        return useRouter;\n      },\n      get qiankun() {\n        return qiankun;\n      },\n      get LayoutView() {\n        return LayoutView;\n      },\n      get ChatMethod() {\n        return ChatMethod;\n      },\n      get AiChatMethod() {\n        return AiChatMethod;\n      },\n      get refreshIcon() {\n        return refreshIcon;\n      },\n      get loginHintMethod() {\n        return loginHintMethod;\n      },\n      get systemLogo() {\n        return systemLogo;\n      },\n      get systemName() {\n        return systemName;\n      },\n      get whetherAiChat() {\n        return whetherAiChat;\n      },\n      get systemNameAreaPrefix() {\n        return systemNameAreaPrefix;\n      },\n      get layoutNameBg() {\n        return layoutNameBg;\n      },\n      get layoutChildBg() {\n        return layoutChildBg;\n      },\n      get layoutChildNameBg() {\n        return layoutChildNameBg;\n      },\n      get ArrowRight() {\n        return ArrowRight;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["defineAsyncComponent", "ref", "onMounted", "nextTick", "useRoute", "useRouter", "qiankun", "LayoutView", "ChatMethod", "AiChatMethod", "refreshIcon", "loginHintMethod", "systemLogo", "systemName", "whetherAiChat", "systemNameAreaPrefix", "layoutNameBg", "layoutChildBg", "layoutChildNameBg", "ArrowRight", "__default__", "name", "HelpDocument", "EditPassWord", "GlobalRegionSelect", "GlobalChatFloating", "GlobalFloatingWindow", "GlobalAiControls", "GlobalAiChat", "LayoutMenu", "suggestPop", "qusetionAnswering", "SubAppViewport", "props", "template", "_qiankun", "is<PERSON><PERSON>", "route", "isSys", "_LayoutView", "user", "area", "role", "left", "width", "LayoutViewBox", "LayoutViewInfo", "helpShow", "handleCommand", "editPassWordShow", "verifyEditPassWord", "verifyEditPassWordShow", "editPassWordCallback", "regionId", "regionName", "regionSelect", "isRegionSelectShow", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "tabMenu", "tabMenuData", "handleClick", "menuId", "menuData", "menuClick", "handleBreadcrumb", "WorkBenchObj", "childData", "WorkBenchReturn", "isRefresh", "keepAliveRoute", "tabData", "tabClick", "handleRefresh", "handleClose", "handleCloseOther", "isMicroApp", "MicroApp", "openPage", "leftMenuData", "WorkBenchList", "setTimeout", "roleList", "JSON", "parse", "sessionStorage", "getItem", "console", "log", "value", "includes", "_ChatMethod", "rongCloudToken", "_AiChatMethod", "AiChatTargetWidth", "AiChatViewType", "AiChatWindowShow", "returnHome", "key", "newVersion", "openInformation", "sysManagement", "filtered", "filter", "item", "routePath", "systemOperation", "flatMap", "children", "find", "child", "sysUser", "myOperation", "_loginHintMethod", "loginHintShow"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/LayoutView/LayoutView.vue"], "sourcesContent": ["<template>\r\n  <el-container class=\"LayoutView\">\r\n    <el-header class=\"LayoutViewHeader\" v-if=\"route.name != 'WorkBenchCopy'\">\r\n      <div class=\"LayoutViewBox\">\r\n        <el-image class=\"LayoutViewLogo\" :src=\"systemLogo\" fit=\"contain\" />\r\n        <div class=\"LayoutViewName\" v-html=\"systemName\"></div>\r\n      </div>\r\n      <div class=\"LayoutViewInfo\" ref=\"LayoutViewInfo\">\r\n        <!-- <el-dropdown @command=\"handleFontSizeChange\">\r\n          <span class=\"LayoutViewFontSize\">\r\n            {{ currentFontSizeLabel }} <el-icon><arrow-down /></el-icon>\r\n          </span>\r\n          <template #dropdown>\r\n            <el-dropdown-menu>\r\n              <el-dropdown-item command=\"default\">默认字号</el-dropdown-item>\r\n              <el-dropdown-item command=\"large\">大字号</el-dropdown-item>\r\n              <el-dropdown-item command=\"xlarge\">超大字号</el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </template>\r\n</el-dropdown> -->\r\n        <xyl-region v-model=\"regionId\" :data=\"area\" @select=\"regionSelect\" v-show=\"!isChildView\"\r\n          :props=\"{ label: 'name', children: 'children' }\"></xyl-region>\r\n        <button v-if=\"route.name == 'homePage'\" class=\"new_version\" @click=\"newVersion\">\r\n          <img src=\"../img/login_btn_bg.png\" alt=\"\" />\r\n          <span>新版本</span>\r\n        </button>\r\n        <div class=\"LayoutViewMenuItem\" @click=\"returnHome\">\r\n          <img src=\"../img/icon_layout_home.png\" alt=\"首页\" />\r\n          <span>首页</span>\r\n        </div>\r\n        <div class=\"LayoutViewMenuItem\" @click=\"openInformation\" v-if=\"false\">\r\n          <img src=\"../img/icon_layout_news.png\" alt=\"资讯\" />\r\n          <span>资讯</span>\r\n        </div>\r\n        <div class=\"LayoutViewMenuItem\" @click=\"sysManagement\" v-if=\"isSys\">\r\n          <img src=\"../img/icon_layout_system.png\" alt=\"系统\" />\r\n          <span>系统</span>\r\n        </div>\r\n        <el-tooltip placement=\"top\" effect=\"light\" :offset=\"6\" :disabled=\"!role.length\">\r\n          <template #content>\r\n            <div class=\"LayoutViewRoleItem\" v-for=\"(item, index) in role\" :key=\"index\">{{ item }}</div>\r\n          </template>\r\n          <div class=\"LayoutViewMenuItem\" @click=\"sysUser\">\r\n            <img :src=\"user.image\" alt=\"\" class=\"avatar\" />\r\n            <span>{{ user.userName }}</span>\r\n          </div>\r\n        </el-tooltip>\r\n        <div class=\"LayoutViewMenuItem\" @click=\"handleCommand('exit')\">\r\n          <span>退出</span>\r\n        </div>\r\n      </div>\r\n    </el-header>\r\n    <el-container class=\"LayoutViewContainer\"\r\n      :style=\"route.name != 'WorkBenchCopy' ? 'height: calc(100% - 120px);' : 'height: 100%;'\">\r\n      <el-aside class=\"LayoutViewAside\" v-show=\"isView\">\r\n        <LayoutMenu v-model=\"menuId\" :menuData=\"menuData\" @select=\"menuClick\"></LayoutMenu>\r\n      </el-aside>\r\n      <el-main class=\"LayoutViewMain\"\r\n        :class=\"{ LayoutViewMainView: !isView, LayoutViewMainBreadcrumb: (!isView && (tabData.length > 1)) }\">\r\n        <xyl-tab v-model=\"menuId\" @tab-click=\"tabClick\" @refresh=\"handleRefresh\" @close=\"handleClose\" feature\r\n          @closeOther=\"handleCloseOther\" v-show=\"isView\">\r\n          <xyl-tab-item v-for=\"item in tabData\" :key=\"item.id\" :value=\"item.id\">{{ item.name }}</xyl-tab-item>\r\n        </xyl-tab>\r\n        <div class=\"LayoutViewBreadcrumb\" v-if=\"!isView && tabData.length > 1\">\r\n          <el-breadcrumb :separator-icon=\"ArrowRight\">\r\n            <el-breadcrumb-item v-for=\"(item, index) in tabData\" :key=\"`key-${item.id}`\"\r\n              @click=\"handleBreadcrumb(item, index)\">\r\n              {{ item.name }}\r\n            </el-breadcrumb-item>\r\n          </el-breadcrumb>\r\n        </div>\r\n        <div class=\"LayoutViewBody\">\r\n          <router-view v-slot=\"{ Component }\">\r\n            <keep-alive :include=\"keepAliveRoute\">\r\n              <component v-if=\"isMain && isRefresh\" :key=\"$route.fullPath\" :is=\"Component\"></component>\r\n            </keep-alive>\r\n          </router-view>\r\n          <SubAppViewport v-for=\"item in MicroApp\" :key=\"item\" v-show=\"!isMain && isMicroApp === item\" :name=\"item\">\r\n          </SubAppViewport>\r\n        </div>\r\n      </el-main>\r\n      <el-aside class=\"LayoutViewFloatingWindow\" v-if=\"whetherAiChat\">\r\n        <transition name=\"width-animation\">\r\n          <div class=\"LayoutViewFloatingWindowBody\" :style=\"{ '--ai-chat-target-width': AiChatTargetWidth }\"\r\n            v-if=\"AiChatViewType\" v-show=\"AiChatWindowShow\">\r\n            <GlobalAiChat v-model=\"AiChatWindowShow\"></GlobalAiChat>\r\n          </div>\r\n        </transition>\r\n      </el-aside>\r\n    </el-container>\r\n    <xyl-popup-window v-model=\"helpShow\" name=\"帮助文档\">\r\n      <HelpDocument></HelpDocument>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"editPassWordShow\" name=\"修改密码\">\r\n      <EditPassWord :type=\"verifyEditPassWord\" @callback=\"editPassWordCallback\"></EditPassWord>\r\n    </xyl-popup-window>\r\n    <div class=\"ConstraintEditPassWord\" v-if=\"verifyEditPassWordShow\">\r\n      <EditPassWord :type=\"verifyEditPassWord\" @callback=\"editPassWordCallback\"></EditPassWord>\r\n    </div>\r\n    <GlobalRegionSelect v-if=\"isRegionSelectShow\" @callback=\"regionSelect\"></GlobalRegionSelect>\r\n  </el-container>\r\n  <qusetionAnswering></qusetionAnswering>\r\n  <GlobalChatFloating v-if=\"rongCloudToken\"></GlobalChatFloating>\r\n  <GlobalFloatingWindow v-model=\"AiChatWindowShow\" :disabled=\"AiChatViewType\" v-if=\"whetherAiChat\" />\r\n  <GlobalAiControls v-if=\"whetherAiChat\" />\r\n  <suggestPop v-if=\"isMain && loginHintShow\" />\r\n</template>\r\n<script>\r\nexport default { name: 'LayoutView' }\r\n</script>\r\n<script setup>\r\nimport { defineAsyncComponent, ref, onMounted, nextTick } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport { qiankun, LayoutView, ChatMethod, AiChatMethod, refreshIcon, loginHintMethod } from './LayoutView.js'\r\nimport {\r\n  systemLogo,\r\n  systemName,\r\n  whetherAiChat,\r\n  systemNameAreaPrefix,\r\n  layoutNameBg,\r\n  layoutChildBg,\r\n  layoutChildNameBg\r\n} from 'common/js/system_var.js'\r\nimport { ArrowRight } from '@element-plus/icons-vue'\r\nconst HelpDocument = defineAsyncComponent(() => import('../LayoutContainer/components/HelpDocument'))\r\nconst EditPassWord = defineAsyncComponent(() => import('../LayoutContainer/components/EditPassWord'))\r\n// const LayoutBoxMessage = defineAsyncComponent(() => import('../LayoutContainer/components/LayoutBoxMessage'))\r\n// const LayoutPersonalDoList = defineAsyncComponent(() => import('../LayoutContainer/components/LayoutPersonalDoList'))\r\nconst GlobalRegionSelect = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalRegionSelect'))\r\nconst GlobalChatFloating = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalChatFloating'))\r\nconst GlobalFloatingWindow = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalFloatingWindow'))\r\nconst GlobalAiControls = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalAiControls'))\r\nconst GlobalAiChat = defineAsyncComponent(() => import('../GlobalAiChat/GlobalAiChat'))\r\nconst LayoutMenu = defineAsyncComponent(() => import('./component/LayoutMenu/LayoutMenu.vue'))\r\nconst suggestPop = defineAsyncComponent(() => import('./component/suggestPop'))\r\nconst qusetionAnswering = defineAsyncComponent(() => import('./component/question-answering.vue'))\r\nconst SubAppViewport = {\r\n  name: 'SubAppViewport',\r\n  props: ['name'],\r\n  template: `<div :id=\"name\" class=\"subApp-viewport\"></div>`\r\n}\r\nconst { isMain } = qiankun(useRoute())\r\nconst route = useRoute()\r\n// const currentFontSizeLabel = ref('切换字号')\r\nconst isSys = ref(false)\r\n// const loginSystemName = computed(() => {\r\n//   const name = (platformAreaName.value || '') + systemName.value\r\n//   const num = Number(loginNameLineFeedPosition.value || '0') || 0\r\n//   return num ? name.substring(0, num) + '\\n' + name.substring(num) : name\r\n// })\r\nconst {\r\n  user, area, role, left, width, LayoutViewBox, LayoutViewInfo, helpShow, handleCommand,\r\n  editPassWordShow, verifyEditPassWord, verifyEditPassWordShow, editPassWordCallback,\r\n  regionId, regionName, regionSelect, isRegionSelectShow, isView, isChildView, tabMenu, tabMenuData, handleClick,\r\n  menuId, menuData, menuClick, handleBreadcrumb, WorkBenchObj, childData, WorkBenchReturn,\r\n  isRefresh, keepAliveRoute, tabData, tabClick, handleRefresh, handleClose, handleCloseOther,\r\n  isMicroApp, MicroApp, openPage, leftMenuData, WorkBenchList\r\n} = LayoutView(useRoute(), useRouter())\r\nonMounted(() => {\r\n  nextTick(() => {\r\n    setTimeout(() => {\r\n      const roleList = JSON.parse(sessionStorage.getItem('role'))\r\n      console.log('当前角色===>', roleList)\r\n      if (roleList) { isSys.value = roleList?.includes('管理员') }\r\n    }, 1000)\r\n  })\r\n})\r\nconst { rongCloudToken } = ChatMethod()\r\nconst { AiChatTargetWidth, AiChatViewType, AiChatWindowShow } = AiChatMethod()\r\n\r\n// const handleFontSizeChange = (command) => {\r\n//   if (command === 'default') currentFontSizeLabel.value = '默认字号'\r\n//   if (command === 'large') currentFontSizeLabel.value = '大字号'\r\n//   if (command === 'xlarge') currentFontSizeLabel.value = '超大字号'\r\n//   // 这里可以加实际字号切换逻辑\r\n// }\r\nconst returnHome = () => {\r\n  if (isChildView.value) {\r\n    WorkBenchReturn()\r\n  } else {\r\n    openPage({ key: 'routePath', value: '/homePage' })\r\n  }\r\n}\r\n\r\n// 切换新版本\r\nconst newVersion = () => {\r\n  console.log('切换到新版本')\r\n  openPage({ key: 'routePath', value: '/WorkBenchCopy' })\r\n}\r\n// 资讯\r\nconst openInformation = () => {\r\n  openPage({ key: 'routePath', value: '/information/AllInformation?moduleId=1&moduleName=资讯' })\r\n  // const filtered = (WorkBenchList.value || []).filter(item => item.routePath !== '/homePage')\r\n  // const systemOperation = filtered.flatMap(item => item.children).find(child => child.name === '新闻资讯')\r\n  // leftMenuData(systemOperation)\r\n}\r\n// 系统管理\r\nconst sysManagement = () => {\r\n  const filtered = (WorkBenchList.value || []).filter(item => item.routePath !== '/homePage')\r\n  const systemOperation = filtered.flatMap(item => item.children).find(child => child.name === '系统运维')\r\n  leftMenuData(systemOperation)\r\n}\r\n// 跳转到我的\r\nconst sysUser = () => {\r\n  const filtered = (WorkBenchList.value || []).filter(item => item.routePath !== '/homePage')\r\n  const myOperation = filtered.flatMap(item => item.children).find(child => child.name === '我的')\r\n  leftMenuData(myOperation)\r\n}\r\n\r\nconst { loginHintShow } = loginHintMethod()\r\n</script>\r\n<style lang=\"scss\">\r\n.LayoutView {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .LayoutViewHeader {\r\n    height: 120px;\r\n    background: url(\"../img/layout_top_bg1.png\") no-repeat;\r\n    background-size: 100% 100%;\r\n    position: relative;\r\n    display: flex;\r\n    justify-content: space-between;\r\n\r\n    .LayoutViewBox {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      box-sizing: border-box;\r\n      position: relative;\r\n      z-index: 2;\r\n      flex-shrink: 0;\r\n\r\n      .LayoutViewLogo {\r\n        height: 75px;\r\n        width: 75px;\r\n\r\n        .zy-el-image {\r\n          width: 100%;\r\n          display: block;\r\n        }\r\n      }\r\n\r\n      .LayoutViewName {\r\n        font-size: 37px;\r\n        color: #fff;\r\n        font-weight: bold;\r\n        margin-left: 15px;\r\n        letter-spacing: 5px;\r\n      }\r\n    }\r\n\r\n    .LayoutViewInfo {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 32px;\r\n\r\n      .new_version {\r\n        background: none;\r\n        border: none;\r\n        position: relative;\r\n        display: flex;\r\n        align-items: center;\r\n        padding: 0;\r\n        cursor: pointer;\r\n\r\n        img {\r\n          height: 39px;\r\n        }\r\n\r\n        span {\r\n          position: absolute;\r\n          left: 0;\r\n          width: 100%;\r\n          text-align: center;\r\n          color: rgb(0, 51, 152);\r\n          font-size: 14px;\r\n          line-height: 39px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n        }\r\n      }\r\n\r\n      .LayoutViewFontSize {\r\n        color: #fff;\r\n        font-size: 18px;\r\n        cursor: pointer;\r\n        display: flex;\r\n        align-items: center;\r\n        margin-right: 24px;\r\n\r\n        .el-icon {\r\n          margin-left: 4px;\r\n        }\r\n      }\r\n\r\n      .LayoutViewMenuItem {\r\n        display: flex;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        color: #fff;\r\n        font-size: 18px;\r\n        cursor: pointer;\r\n        margin-right: 24px;\r\n\r\n        img {\r\n          width: 35px;\r\n          height: 35px;\r\n        }\r\n\r\n        .avatar {\r\n          width: 35px;\r\n          height: 35px;\r\n          border-radius: 50%;\r\n          margin: 0 8px;\r\n          object-fit: cover;\r\n          border: 2px solid #fff;\r\n          background: #eee;\r\n        }\r\n\r\n        span {\r\n          font-size: 16px;\r\n          margin-top: 4px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .LayoutViewContainer {\r\n    width: 100%;\r\n    // height: 100%;\r\n    //  height: calc(100% - 62px);\r\n    background: var(--zy-el-color-info-light-9);\r\n\r\n    .LayoutViewFloatingWindow {\r\n      width: auto;\r\n      height: 100%;\r\n\r\n      .LayoutViewFloatingWindowBody {\r\n        width: var(--ai-chat-target-width);\r\n        height: 100%;\r\n        background: #fff;\r\n        box-sizing: border-box;\r\n        transform-origin: left center;\r\n        border-left: 1px solid var(--zy-el-border-color-lighter);\r\n      }\r\n\r\n      /* 进入动画 */\r\n      .width-animation-enter-active {\r\n        animation: widen 0.2s ease-in-out forwards;\r\n      }\r\n\r\n      /* 离开动画 */\r\n      .width-animation-leave-active {\r\n        animation: narrow 0.2s ease-in-out forwards;\r\n      }\r\n\r\n      /* 定义进入动画 */\r\n      @keyframes widen {\r\n        from {\r\n          width: 0;\r\n        }\r\n\r\n        to {\r\n          width: var(--ai-chat-target-width);\r\n        }\r\n      }\r\n\r\n      /* 定义离开动画 */\r\n      @keyframes narrow {\r\n        from {\r\n          width: var(--ai-chat-target-width);\r\n        }\r\n\r\n        to {\r\n          width: 0;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LayoutViewAside {\r\n      width: auto;\r\n    }\r\n\r\n    .LayoutViewMain {\r\n      height: 100%;\r\n      padding: var(--zy-distance-three) var(--zy-distance-three) 0 0;\r\n\r\n      .LayoutViewBreadcrumb {\r\n        width: 100%;\r\n        height: calc((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px);\r\n        display: flex;\r\n        align-items: center;\r\n        background-color: #fff;\r\n        padding: 0 var(--zy-distance-two);\r\n        border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n        .zy-el-breadcrumb {\r\n          font-size: var(--zy-name-font-size);\r\n\r\n          .zy-el-breadcrumb__inner {\r\n            cursor: pointer;\r\n            font-weight: bold;\r\n            color: var(--zy-el-color-primary);\r\n          }\r\n\r\n          .zy-el-breadcrumb__item {\r\n            &:last-child {\r\n              .zy-el-breadcrumb__inner {\r\n                cursor: text;\r\n                font-weight: normal;\r\n                color: var(--zy-el-text-color-regular);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .LayoutViewBody {\r\n        width: 100%;\r\n        height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px));\r\n        background-color: #fff;\r\n\r\n        .subApp-viewport {\r\n          width: 100%;\r\n          height: 100%;\r\n\r\n          >div {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .LayoutViewMainView {\r\n      width: 100%;\r\n      padding: 0;\r\n      background: #f8f8f8;\r\n\r\n      .LayoutViewBody {\r\n        height: 100%;\r\n      }\r\n    }\r\n\r\n    .LayoutViewMainBreadcrumb {\r\n      width: 100%;\r\n\r\n      .LayoutViewBody {\r\n        position: relative;\r\n        height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px));\r\n      }\r\n    }\r\n  }\r\n\r\n  .ConstraintEditPassWord {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    z-index: 999;\r\n    background-color: #fff;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .EditPassWord {\r\n      box-shadow: 0px 2px 40px rgba(0, 0, 0, 0.1);\r\n    }\r\n  }\r\n}\r\n\r\n.LayoutViewRoleItem {\r\n  font-size: var(--zy-text-font-size);\r\n  line-height: var(--zy-line-height);\r\n}\r\n</style>\r\n"], "mappings": "AA+GA,SAASA,oBAAoB,EAAEC,GAAG,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,KAAK;AACpE,SAASC,QAAQ,EAAEC,SAAS,QAAQ,YAAY;AAChD,SAASC,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAEC,YAAY,EAAEC,WAAW,EAAEC,eAAe,QAAQ,iBAAiB;AAC7G,SACEC,UAAU,EACVC,UAAU,EACVC,aAAa,EACbC,oBAAoB,EACpBC,YAAY,EACZC,aAAa,EACbC,iBAAiB,QACZ,yBAAyB;AAChC,SAASC,UAAU,QAAQ,yBAAyB;AAfpD,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAa,CAAC;;;;;IAgBrC,IAAMC,YAAY,GAAGtB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,4CAA4C,CAAC;IAAA,EAAC;IACrG,IAAMuB,YAAY,GAAGvB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,4CAA4C,CAAC;IAAA,EAAC;IACrG;IACA;IACA,IAAMwB,kBAAkB,GAAGxB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,kDAAkD,CAAC;IAAA,EAAC;IACjH,IAAMyB,kBAAkB,GAAGzB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,kDAAkD,CAAC;IAAA,EAAC;IACjH,IAAM0B,oBAAoB,GAAG1B,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,oDAAoD,CAAC;IAAA,EAAC;IACrH,IAAM2B,gBAAgB,GAAG3B,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,gDAAgD,CAAC;IAAA,EAAC;IAC7G,IAAM4B,YAAY,GAAG5B,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,8BAA8B,CAAC;IAAA,EAAC;IACvF,IAAM6B,UAAU,GAAG7B,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,uCAAuC,CAAC;IAAA,EAAC;IAC9F,IAAM8B,UAAU,GAAG9B,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,wBAAwB,CAAC;IAAA,EAAC;IAC/E,IAAM+B,iBAAiB,GAAG/B,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,oCAAoC,CAAC;IAAA,EAAC;IAClG,IAAMgC,cAAc,GAAG;MACrBX,IAAI,EAAE,gBAAgB;MACtBY,KAAK,EAAE,CAAC,MAAM,CAAC;MACfC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAAC,QAAA,GAAmB7B,OAAO,CAACF,QAAQ,CAAC,CAAC,CAAC;MAA9BgC,MAAM,GAAAD,QAAA,CAANC,MAAM;IACd,IAAMC,KAAK,GAAGjC,QAAQ,CAAC,CAAC;IACxB;IACA,IAAMkC,KAAK,GAAGrC,GAAG,CAAC,KAAK,CAAC;IACxB;IACA;IACA;IACA;IACA;IACA,IAAAsC,WAAA,GAOIhC,UAAU,CAACH,QAAQ,CAAC,CAAC,EAAEC,SAAS,CAAC,CAAC,CAAC;MANrCmC,IAAI,GAAAD,WAAA,CAAJC,IAAI;MAAEC,IAAI,GAAAF,WAAA,CAAJE,IAAI;MAAEC,IAAI,GAAAH,WAAA,CAAJG,IAAI;MAAEC,IAAI,GAAAJ,WAAA,CAAJI,IAAI;MAAEC,KAAK,GAAAL,WAAA,CAALK,KAAK;MAAEC,aAAa,GAAAN,WAAA,CAAbM,aAAa;MAAEC,cAAc,GAAAP,WAAA,CAAdO,cAAc;MAAEC,QAAQ,GAAAR,WAAA,CAARQ,QAAQ;MAAEC,aAAa,GAAAT,WAAA,CAAbS,aAAa;MACrFC,gBAAgB,GAAAV,WAAA,CAAhBU,gBAAgB;MAAEC,kBAAkB,GAAAX,WAAA,CAAlBW,kBAAkB;MAAEC,sBAAsB,GAAAZ,WAAA,CAAtBY,sBAAsB;MAAEC,oBAAoB,GAAAb,WAAA,CAApBa,oBAAoB;MAClFC,QAAQ,GAAAd,WAAA,CAARc,QAAQ;MAAEC,UAAU,GAAAf,WAAA,CAAVe,UAAU;MAAEC,YAAY,GAAAhB,WAAA,CAAZgB,YAAY;MAAEC,kBAAkB,GAAAjB,WAAA,CAAlBiB,kBAAkB;MAAEC,MAAM,GAAAlB,WAAA,CAANkB,MAAM;MAAEC,WAAW,GAAAnB,WAAA,CAAXmB,WAAW;MAAEC,OAAO,GAAApB,WAAA,CAAPoB,OAAO;MAAEC,WAAW,GAAArB,WAAA,CAAXqB,WAAW;MAAEC,WAAW,GAAAtB,WAAA,CAAXsB,WAAW;MAC9GC,MAAM,GAAAvB,WAAA,CAANuB,MAAM;MAAEC,QAAQ,GAAAxB,WAAA,CAARwB,QAAQ;MAAEC,SAAS,GAAAzB,WAAA,CAATyB,SAAS;MAAEC,gBAAgB,GAAA1B,WAAA,CAAhB0B,gBAAgB;MAAEC,YAAY,GAAA3B,WAAA,CAAZ2B,YAAY;MAAEC,SAAS,GAAA5B,WAAA,CAAT4B,SAAS;MAAEC,eAAe,GAAA7B,WAAA,CAAf6B,eAAe;MACvFC,SAAS,GAAA9B,WAAA,CAAT8B,SAAS;MAAEC,cAAc,GAAA/B,WAAA,CAAd+B,cAAc;MAAEC,OAAO,GAAAhC,WAAA,CAAPgC,OAAO;MAAEC,QAAQ,GAAAjC,WAAA,CAARiC,QAAQ;MAAEC,aAAa,GAAAlC,WAAA,CAAbkC,aAAa;MAAEC,WAAW,GAAAnC,WAAA,CAAXmC,WAAW;MAAEC,gBAAgB,GAAApC,WAAA,CAAhBoC,gBAAgB;MAC1FC,UAAU,GAAArC,WAAA,CAAVqC,UAAU;MAAEC,QAAQ,GAAAtC,WAAA,CAARsC,QAAQ;MAAEC,QAAQ,GAAAvC,WAAA,CAARuC,QAAQ;MAAEC,YAAY,GAAAxC,WAAA,CAAZwC,YAAY;MAAEC,aAAa,GAAAzC,WAAA,CAAbyC,aAAa;IAE7D9E,SAAS,CAAC,YAAM;MACdC,QAAQ,CAAC,YAAM;QACb8E,UAAU,CAAC,YAAM;UACf,IAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACC,cAAc,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC;UAC3DC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEN,QAAQ,CAAC;UACjC,IAAIA,QAAQ,EAAE;YAAE5C,KAAK,CAACmD,KAAK,GAAGP,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEQ,QAAQ,CAAC,KAAK,CAAC;UAAC;QAC1D,CAAC,EAAE,IAAI,CAAC;MACV,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,IAAAC,WAAA,GAA2BnF,UAAU,CAAC,CAAC;MAA/BoF,cAAc,GAAAD,WAAA,CAAdC,cAAc;IACtB,IAAAC,aAAA,GAAgEpF,YAAY,CAAC,CAAC;MAAtEqF,iBAAiB,GAAAD,aAAA,CAAjBC,iBAAiB;MAAEC,cAAc,GAAAF,aAAA,CAAdE,cAAc;MAAEC,gBAAgB,GAAAH,aAAA,CAAhBG,gBAAgB;;IAE3D;IACA;IACA;IACA;IACA;IACA;IACA,IAAMC,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvB,IAAIvC,WAAW,CAAC+B,KAAK,EAAE;QACrBrB,eAAe,CAAC,CAAC;MACnB,CAAC,MAAM;QACLU,QAAQ,CAAC;UAAEoB,GAAG,EAAE,WAAW;UAAET,KAAK,EAAE;QAAY,CAAC,CAAC;MACpD;IACF,CAAC;;IAED;IACA,IAAMU,UAAU,GAAG,SAAbA,UAAUA,CAAA,EAAS;MACvBZ,OAAO,CAACC,GAAG,CAAC,QAAQ,CAAC;MACrBV,QAAQ,CAAC;QAAEoB,GAAG,EAAE,WAAW;QAAET,KAAK,EAAE;MAAiB,CAAC,CAAC;IACzD,CAAC;IACD;IACA,IAAMW,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;MAC5BtB,QAAQ,CAAC;QAAEoB,GAAG,EAAE,WAAW;QAAET,KAAK,EAAE;MAAuD,CAAC,CAAC;MAC7F;MACA;MACA;IACF,CAAC;IACD;IACA,IAAMY,aAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1B,IAAMC,QAAQ,GAAG,CAACtB,aAAa,CAACS,KAAK,IAAI,EAAE,EAAEc,MAAM,CAAC,UAAAC,IAAI;QAAA,OAAIA,IAAI,CAACC,SAAS,KAAK,WAAW;MAAA,EAAC;MAC3F,IAAMC,eAAe,GAAGJ,QAAQ,CAACK,OAAO,CAAC,UAAAH,IAAI;QAAA,OAAIA,IAAI,CAACI,QAAQ;MAAA,EAAC,CAACC,IAAI,CAAC,UAAAC,KAAK;QAAA,OAAIA,KAAK,CAACzF,IAAI,KAAK,MAAM;MAAA,EAAC;MACpG0D,YAAY,CAAC2B,eAAe,CAAC;IAC/B,CAAC;IACD;IACA,IAAMK,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAS;MACpB,IAAMT,QAAQ,GAAG,CAACtB,aAAa,CAACS,KAAK,IAAI,EAAE,EAAEc,MAAM,CAAC,UAAAC,IAAI;QAAA,OAAIA,IAAI,CAACC,SAAS,KAAK,WAAW;MAAA,EAAC;MAC3F,IAAMO,WAAW,GAAGV,QAAQ,CAACK,OAAO,CAAC,UAAAH,IAAI;QAAA,OAAIA,IAAI,CAACI,QAAQ;MAAA,EAAC,CAACC,IAAI,CAAC,UAAAC,KAAK;QAAA,OAAIA,KAAK,CAACzF,IAAI,KAAK,IAAI;MAAA,EAAC;MAC9F0D,YAAY,CAACiC,WAAW,CAAC;IAC3B,CAAC;IAED,IAAAC,gBAAA,GAA0BtG,eAAe,CAAC,CAAC;MAAnCuG,aAAa,GAAAD,gBAAA,CAAbC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}