{"ast": null, "code": "import { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, normalizeStyle as _normalizeStyle, vShow as _vShow, withDirectives as _withDirectives, createBlock as _createBlock, Fragment as _Fragment } from \"vue\";\nimport _imports_0 from '../../img/answer.png';\nimport _imports_1 from '../../img/intelligence.gif';\nvar _hoisted_1 = {\n  key: 0,\n  class: \"intelligence\"\n};\nvar _hoisted_2 = {\n  class: \"intelligence-centent\"\n};\nvar _hoisted_3 = {\n  key: 0,\n  class: \"message\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(_Fragment, null, [$setup.initShow == '1' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [$setup.cententTopShow ? (_openBlock(), _createElementBlock(\"div\", {\n    key: 0,\n    class: \"centent-top\",\n    style: _normalizeStyle(`right:${$setup.showMessage ? '-50px' : '-130px'}`),\n    onMouseover: $setup.mouseoverFun,\n    onMouseleave: $setup.mouseleaveFun,\n    onClick: $setup.checkShow\n  }, [$setup.showMessage ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, _cache[0] || (_cache[0] = [_createTextVNode(\"您有问题需要解答吗？\"), _createElementVNode(\"img\", {\n    src: _imports_0,\n    alt: \"\",\n    style: {\n      \"width\": \"11px\",\n      \"height\": \"11px\"\n    }\n  }, null, -1 /* HOISTED */), _createElementVNode(\"div\", {\n    class: \"arrow\"\n  }, null, -1 /* HOISTED */)]))) : _createCommentVNode(\"v-if\", true), _cache[1] || (_cache[1] = _createElementVNode(\"img\", {\n    src: _imports_1,\n    alt: \"\"\n  }, null, -1 /* HOISTED */)), _cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n    class: \"textContent\"\n  }, null, -1 /* HOISTED */))], 36 /* STYLE, NEED_HYDRATION */)) : _createCommentVNode(\"v-if\", true)])])) : _createCommentVNode(\"v-if\", true), $setup.initShow == '1' ? _withDirectives((_openBlock(), _createBlock($setup[\"DouBaoIntelligentize\"], {\n    key: 1,\n    class: \"intelligenceCentent\",\n    typeShow: false,\n    showMessage: $setup.initShow,\n    ref: \"DouBaoIntelligentizeRef\",\n    onCheckOutFun: $setup.checkOutFun\n  }, null, 8 /* PROPS */, [\"showMessage\"])), [[_vShow, $setup.showChat]]) : _createCommentVNode(\"v-if\", true)], 64 /* STABLE_FRAGMENT */);\n}", "map": {"version": 3, "names": ["_imports_0", "_imports_1", "key", "class", "_createElementBlock", "_Fragment", "$setup", "initShow", "_hoisted_1", "_createElementVNode", "_hoisted_2", "cententTopShow", "style", "_normalizeStyle", "showMessage", "onMouseover", "mouseoverFun", "onMouseleave", "mouseleaveFun", "onClick", "checkShow", "_hoisted_3", "_cache", "_createTextVNode", "src", "alt", "_createCommentVNode", "_createBlock", "typeShow", "ref", "onCheckOutFun", "checkOutFun", "showChat"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\LayoutView\\component\\question-answering.vue"], "sourcesContent": ["<template>\r\n  <div class=\"intelligence\"\r\n       v-if=\"initShow == '1'\">\r\n    <div class=\"intelligence-centent\">\r\n      <div class=\"centent-top\"\r\n           :style=\"`right:${showMessage ? '-50px' : '-130px'}`\"\r\n           v-if=\"cententTopShow\"\r\n           @mouseover=\"mouseoverFun\"\r\n           @mouseleave=\"mouseleaveFun\"\r\n           @click=\"checkShow\">\r\n        <div v-if=\"showMessage\"\r\n             class=\"message\">您有问题需要解答吗？<img src=\"../../img/answer.png\"\r\n               alt=\"\"\r\n               style=\"width: 11px;height: 11px;\">\r\n          <div class=\"arrow\"></div>\r\n        </div>\r\n        <img src=\"../../img/intelligence.gif\"\r\n             alt=\"\">\r\n        <div class=\"textContent\"></div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n  <DouBaoIntelligentize class=\"intelligenceCentent\"\r\n                        v-if=\"initShow == '1'\"\r\n                        v-show=\"showChat\"\r\n                        :typeShow=\"false\"\r\n                        :showMessage=\"initShow\"\r\n                        ref=\"DouBaoIntelligentizeRef\"\r\n                        @checkOutFun=\"checkOutFun\"></DouBaoIntelligentize>\r\n</template>\r\n\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport DouBaoIntelligentize from '../../Intelligentize/Intelligentize/DouBaoIntelligentize.vue'\r\nconst initShow = ref('0')\r\nconst showChat = ref(false)\r\nconst showMessage = ref(false)\r\nconst cententTopShow = ref(true)\r\nconst mouseoverFun = () => {\r\n  showMessage.value = true\r\n}\r\nonMounted(() => {\r\n  globalReadOpenConfigFun()\r\n})\r\nconst mouseleaveFun = () => {\r\n  showMessage.value = false\r\n}\r\nconst checkOutFun = () => {\r\n  showChat.value = false\r\n  cententTopShow.value = true\r\n}\r\nconst globalReadOpenConfigFun = async () => {\r\n  const { data } = await api.globalReadOpenConfig({ codes: ['question_answering'], is: '1' })\r\n  initShow.value = data.question_answering\r\n  console.log(data, '############')\r\n}\r\n// const { data } = await api.globalReadOpenConfig({ codes: ['appShareAddress'] })\r\n// shareUrl.value = `${data.appShareAddress}pages/index/index.html?%7B%22n%22:%22mo_notice_list%22,%22u%22:%22../mo_notice_list/mo_notice_list.stml%22,%22p%22:%7B%22headTheme%22:%22#FFF%22,%22appTheme%22:%22#BC1D1D%22,%22areaId%22:%22${user.value.areaId}%22,%22v%22:%221%22%7D%7D`\r\n// qrShow.value = true\r\nconst checkShow = () => {\r\n  showChat.value = true\r\n  showMessage.value = false\r\n  cententTopShow.value = false\r\n}\r\nconst TimeMessage = () => {\r\n  showMessage.value = true\r\n  setTimeout(() => {\r\n    showMessage.value = false\r\n  }, 3000);\r\n}\r\nsetInterval(() => {\r\n  TimeMessage()\r\n}, 60000)\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.intelligence {\r\n  position: relative;\r\n  transform: translateY(-200px);\r\n  z-index: 999;\r\n  width: 100%;\r\n\r\n  .intelligence-centent {\r\n    position: absolute;\r\n    right: 0px;\r\n    height: 130px;\r\n    user-select: none;\r\n    overflow: hidden;\r\n\r\n\r\n    .centent-top {\r\n      transition: all 0.3s;\r\n      position: absolute;\r\n      width: 180px;\r\n      border-radius: 50px 0 0 50px;\r\n      box-shadow: 0px 0px 5px 0px rgba(0, 0, 0, 0.2);\r\n      display: flex;\r\n      padding: 2px 1px;\r\n      right: -140px;\r\n      position: relative;\r\n      margin-top: 80px;\r\n      cursor: pointer;\r\n      background: url('../../img/suspension.png') no-repeat;\r\n      background-position: -20px -20px;\r\n\r\n      .message {\r\n        background-color: #fff;\r\n        width: 95px;\r\n        line-height: 20px;\r\n        position: absolute;\r\n        top: -50px;\r\n        border: 1px solid rgba(0, 0, 0, 0.1);\r\n        right: 0px;\r\n        left: 0;\r\n        font-family: Alibaba PuHuiTi 2.0, Alibaba PuHuiTi 20;\r\n        font-weight: normal;\r\n        font-size: 12px;\r\n        color: #333333;\r\n        line-height: 16px;\r\n        padding: 5px;\r\n        border-radius: 5px;\r\n        user-select: none;\r\n\r\n        .arrow {\r\n          width: 0px;\r\n          height: 0pxl;\r\n          border: 5px solid #dadada;\r\n          position: absolute;\r\n          bottom: -10px;\r\n          border-right-color: transparent;\r\n          border-left-color: transparent;\r\n          border-bottom-color: transparent;\r\n        }\r\n      }\r\n\r\n      img {\r\n        width: 40px;\r\n        height: 40px;\r\n        margin-left: 5px;\r\n      }\r\n\r\n      div {\r\n        line-height: 30px;\r\n        margin-left: 10px;\r\n        color: #333333;\r\n        font-family: Microsoft YaHei, Microsoft YaHei;\r\n        font-weight: bold;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.intelligenceCentent {\r\n  position: fixed;\r\n  // width: 460px;\r\n  // height: 800px;\r\n  width: 30%;\r\n  height: 90%;\r\n  right: 0px;\r\n  bottom: 0px;\r\n  z-index: 999;\r\n}\r\n</style>"], "mappings": ";OAW4CA,UAA0B;OAKzDC,UAAgC;;EAhB7CC,GAAA;EACOC,KAAK,EAAC;;;EAEJA,KAAK,EAAC;AAAsB;;EAHrCD,GAAA;EAWaC,KAAK,EAAC;;;uBAXnBC,mBAAA,CAAAC,SAAA,SAEaC,MAAA,CAAAC,QAAQ,W,cADnBH,mBAAA,CAoBM,OApBNI,UAoBM,GAlBJC,mBAAA,CAiBM,OAjBNC,UAiBM,GAdOJ,MAAA,CAAAK,cAAc,I,cAFzBP,mBAAA,CAeM;IAnBZF,GAAA;IAIWC,KAAK,EAAC,aAAa;IAClBS,KAAK,EALjBC,eAAA,UAK4BP,MAAA,CAAAQ,WAAW;IAE3BC,WAAS,EAAET,MAAA,CAAAU,YAAY;IACvBC,YAAU,EAAEX,MAAA,CAAAY,aAAa;IACzBC,OAAK,EAAEb,MAAA,CAAAc;MACAd,MAAA,CAAAQ,WAAW,I,cAAtBV,mBAAA,CAKM,OALNiB,UAKM,EAAAC,MAAA,QAAAA,MAAA,OAfdC,gBAAA,CAW6B,YAAU,GAAAd,mBAAA,CAEU;IAFLe,GAA0B,EAA1BxB,UAA0B;IACvDyB,GAAG,EAAC,EAAE;IACNb,KAAiC,EAAjC;MAAA;MAAA;IAAA;8BACLH,mBAAA,CAAyB;IAApBN,KAAK,EAAC;EAAO,2B,MAd5BuB,mBAAA,gB,0BAgBQjB,mBAAA,CACY;IADPe,GAAgC,EAAhCvB,UAAgC;IAChCwB,GAAG,EAAC;yDACThB,mBAAA,CAA+B;IAA1BN,KAAK,EAAC;EAAa,4B,qCAlBhCuB,mBAAA,e,OAAAA,mBAAA,gBAuB8BpB,MAAA,CAAAC,QAAQ,U,+BADpCoB,YAAA,CAMwErB,MAAA;IA5B1EJ,GAAA;IAsBwBC,KAAK,EAAC,qBAAqB;IAG1ByB,QAAQ,EAAE,KAAK;IACfd,WAAW,EAAER,MAAA,CAAAC,QAAQ;IACtBsB,GAAG,EAAC,yBAAyB;IAC5BC,aAAW,EAAExB,MAAA,CAAAyB;uDAJNzB,MAAA,CAAA0B,QAAQ,E,IAxBxCN,mBAAA,e", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}