{"ast": null, "code": "import '../../../utils/index.mjs';\nimport { buildProps, definePropType } from '../../../utils/vue/props/runtime.mjs';\nvar tourStrategies = [\"absolute\", \"fixed\"];\nvar tourPlacements = [\"top-start\", \"top-end\", \"top\", \"bottom-start\", \"bottom-end\", \"bottom\", \"left-start\", \"left-end\", \"left\", \"right-start\", \"right-end\", \"right\"];\nvar tourContentProps = buildProps({\n  placement: {\n    type: definePropType(String),\n    values: tourPlacements,\n    default: \"bottom\"\n  },\n  reference: {\n    type: definePropType(Object),\n    default: null\n  },\n  strategy: {\n    type: definePropType(String),\n    values: tourStrategies,\n    default: \"absolute\"\n  },\n  offset: {\n    type: Number,\n    default: 10\n  },\n  showArrow: Boolean,\n  zIndex: {\n    type: Number,\n    default: 2001\n  }\n});\nvar tourContentEmits = {\n  close: function close() {\n    return true;\n  }\n};\nexport { tourContentEmits, tourContentProps, tourPlacements, tourStrategies };", "map": {"version": 3, "names": ["tourStrategies", "tourPlacements", "tourContentProps", "buildProps", "placement", "type", "definePropType", "String", "values", "default", "reference", "Object", "strategy", "offset", "Number", "showArrow", "Boolean", "zIndex", "tourContentEmits", "close"], "sources": ["../../../../../../packages/components/tour/src/content.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type { Placement, Strategy, VirtualElement } from '@floating-ui/dom'\n\nexport const tourStrategies = ['absolute', 'fixed'] as const\n\nexport const tourPlacements = [\n  'top-start',\n  'top-end',\n  'top',\n  'bottom-start',\n  'bottom-end',\n  'bottom',\n  'left-start',\n  'left-end',\n  'left',\n  'right-start',\n  'right-end',\n  'right',\n] as const\n\nexport const tourContentProps = buildProps({\n  /**\n   * @description position of the guide card relative to the target element\n   */\n  placement: {\n    type: definePropType<Placement>(String),\n    values: tourPlacements,\n    default: 'bottom',\n  },\n  /**\n   * @description the reference dom\n   */\n  reference: {\n    type: definePropType<HTMLElement | VirtualElement | null>(Object),\n    default: null,\n  },\n  /**\n   * @description position strategy of the content\n   */\n  strategy: {\n    type: definePropType<Strategy>(String),\n    values: tourStrategies,\n    default: 'absolute',\n  },\n  /**\n   * @description offset of the arrow\n   */\n  offset: {\n    type: Number,\n    default: 10,\n  },\n  /**\n   * @description @description whether to show the arrow\n   */\n  showArrow: Boolean,\n  /**\n   * @description content's zIndex\n   */\n  zIndex: {\n    type: Number,\n    default: 2001,\n  },\n})\n\nexport type TourContentProps = ExtractPropTypes<typeof tourContentProps>\n\nexport const tourContentEmits = {\n  close: () => true,\n}\nexport type TourContentEmits = typeof tourContentEmits\n"], "mappings": ";;AACY,IAACA,cAAc,GAAG,CAAC,UAAU,EAAE,OAAO;AACtC,IAACC,cAAc,GAAG,CAC5B,WAAW,EACX,SAAS,EACT,KAAK,EACL,cAAc,EACd,YAAY,EACZ,QAAQ,EACR,YAAY,EACZ,UAAU,EACV,MAAM,EACN,aAAa,EACb,WAAW,EACX,OAAO,CACT;AACY,IAACC,gBAAgB,GAAGC,UAAU,CAAC;EACzCC,SAAS,EAAE;IACTC,IAAI,EAAEC,cAAc,CAACC,MAAM,CAAC;IAC5BC,MAAM,EAAEP,cAAc;IACtBQ,OAAO,EAAE;EACb,CAAG;EACDC,SAAS,EAAE;IACTL,IAAI,EAAEC,cAAc,CAACK,MAAM,CAAC;IAC5BF,OAAO,EAAE;EACb,CAAG;EACDG,QAAQ,EAAE;IACRP,IAAI,EAAEC,cAAc,CAACC,MAAM,CAAC;IAC5BC,MAAM,EAAER,cAAc;IACtBS,OAAO,EAAE;EACb,CAAG;EACDI,MAAM,EAAE;IACNR,IAAI,EAAES,MAAM;IACZL,OAAO,EAAE;EACb,CAAG;EACDM,SAAS,EAAEC,OAAO;EAClBC,MAAM,EAAE;IACNZ,IAAI,EAAES,MAAM;IACZL,OAAO,EAAE;EACb;AACA,CAAC;AACW,IAACS,gBAAgB,GAAG;EAC9BC,KAAK,EAAE,SAAPA,KAAKA,CAAA;IAAA,OAAQ,IAAI;EAAA;AACnB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}