{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ProposalAuxiliaryWriting\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, \"文本识别\");\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiToolBoxFunction\\ProposalAuxiliaryWriting\\ProposalAuxiliaryWriting.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ProposalAuxiliaryWriting\">文本识别</div>\r\n</template>\r\n<script>\r\nexport default { name: 'ProposalAuxiliaryWriting' }\r\n</script>\r\n\r\n<script setup></script>\r\n<style lang=\"scss\">\r\n.ProposalAuxiliaryWriting {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n  background: #f3f5f7;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAA0B;;uBAArCC,mBAAA,CAAgD,OAAhDC,UAAgD,EAAV,MAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}