{"ast": null, "code": "import { ref, onMounted, onUnmounted } from 'vue';\nimport { systemChatIcon } from 'common/js/system_var.js';\nimport GlobalChat from '../../GlobalChat/GlobalChat.vue';\nvar __default__ = {\n  name: 'GlobalChatFloating'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var elContainer = ref();\n    var isDragging = ref(false);\n    var positionX = ref(window.innerWidth - 100);\n    var positionY = ref(168);\n    var startPosX = ref(0);\n    var startPosY = ref(0);\n    var elShow = ref(false);\n    var isActive = ref(false);\n    var offset = ref([0, 0]);\n    var chatTotal = ref(0);\n    var isActiveType = ref(false);\n    var callback = function callback(value) {\n      if (value < 10) {\n        offset.value = [0, 0];\n      } else if (value < 100) {\n        offset.value = [-4, 0];\n      } else {\n        offset.value = [-8, 0];\n      }\n      chatTotal.value = value;\n    };\n    var handleClick = function handleClick() {\n      if (!isDragging.value && !isActive.value) elShow.value = !elShow.value;\n      isActive.value = false;\n    };\n    var handleMousedown = function handleMousedown(event) {\n      isDragging.value = true;\n      startPosX.value = event.clientX - positionX.value;\n      startPosY.value = event.clientY - positionY.value;\n      document.addEventListener('mousemove', handleMousemove);\n      document.addEventListener('mouseup', _handleMouseup);\n    };\n    var _handleMouseup = function handleMouseup() {\n      if (isDragging.value) {\n        isDragging.value = false;\n        document.removeEventListener('mousemove', handleMousemove);\n        document.removeEventListener('mouseup', _handleMouseup);\n        handleAutoSnap();\n      }\n    };\n    var handleMousemove = function handleMousemove(event) {\n      if (isDragging.value) {\n        elShow.value = false;\n        isActive.value = true;\n        // requestAnimationFrame(() => {    \n        var newX = event.clientX - startPosX.value;\n        var newY = event.clientY - startPosY.value;\n        var windowWidth = window.innerWidth;\n        var windowHeight = window.innerHeight;\n        var elWidth = elContainer.value.offsetWidth;\n        var elHeight = elContainer.value.offsetHeight;\n        positionX.value = Math.max(0, Math.min(windowWidth - elWidth, newX));\n        positionY.value = Math.max(0, Math.min(windowHeight - elHeight, newY));\n        elContainer.value.style.top = positionY.value + 'px';\n        elContainer.value.style.left = positionX.value + 'px';\n        // })\n      }\n    };\n    // 自动吸附到最近的侧边\n    var handleAutoSnap = function handleAutoSnap() {\n      var windowWidth = window.innerWidth;\n      var elWidth = elContainer.value.offsetWidth;\n      if (positionX.value + elWidth / 2 < windowWidth / 2) {\n        positionX.value = 0;\n        isActiveType.value = true;\n      } else {\n        isActiveType.value = false;\n        positionX.value = windowWidth - elWidth;\n      }\n      elContainer.value.style.top = positionY.value + 'px';\n      elContainer.value.style.left = positionX.value + 'px';\n    };\n    onMounted(function () {\n      positionX.value = window.innerWidth - elContainer.value.offsetWidth;\n      elContainer.value.style.top = positionY.value + 'px';\n      elContainer.value.style.left = positionX.value + 'px';\n      window.addEventListener('resize', handleAutoSnap);\n    });\n    onUnmounted(function () {\n      window.removeEventListener('resize', handleAutoSnap);\n    });\n    var __returned__ = {\n      elContainer,\n      isDragging,\n      positionX,\n      positionY,\n      startPosX,\n      startPosY,\n      elShow,\n      isActive,\n      offset,\n      chatTotal,\n      isActiveType,\n      callback,\n      handleClick,\n      handleMousedown,\n      handleMouseup: _handleMouseup,\n      handleMousemove,\n      handleAutoSnap,\n      ref,\n      onMounted,\n      onUnmounted,\n      get systemChatIcon() {\n        return systemChatIcon;\n      },\n      GlobalChat\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["ref", "onMounted", "onUnmounted", "systemChatIcon", "GlobalChat", "__default__", "name", "<PERSON><PERSON><PERSON><PERSON>", "isDragging", "positionX", "window", "innerWidth", "positionY", "startPosX", "startPosY", "elShow", "isActive", "offset", "chatTotal", "isActiveType", "callback", "value", "handleClick", "handleMousedown", "event", "clientX", "clientY", "document", "addEventListener", "handleMousemove", "handleMouseup", "removeEventListener", "handleAutoSnap", "newX", "newY", "windowWidth", "windowHeight", "innerHeight", "<PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "elHeight", "offsetHeight", "Math", "max", "min", "style", "top", "left"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/LayoutContainer/components/GlobalChatFloating.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalChatFloating forbidSelect\" ref=\"elContainer\" @mousedown=\"handleMousedown\" @mouseup=\"handleMouseup\">\r\n    <el-popover :visible=\"elShow\" placement=\"left\" popper-class=\"GlobalChatFloatingPopover\">\r\n      <template #reference>\r\n        <div :class=\"['GlobalChatFloatingBody', isActiveType ? 'is-left ' : 'is-right', { 'is-active': isActive }]\"\r\n          @click=\"handleClick\">\r\n          <el-badge :value=\"chatTotal\" :max=\"99\" :hidden=\"!chatTotal\" :offset=\"offset\">\r\n            <el-image :src=\"systemChatIcon\" loading=\"lazy\" fit=\"cover\" draggable=\"false\" />\r\n          </el-badge>\r\n        </div>\r\n      </template>\r\n      <GlobalChat @callback=\"callback\"></GlobalChat>\r\n    </el-popover>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalChatFloating' }\r\n</script>\r\n<script setup>\r\nimport { ref, onMounted, onUnmounted } from 'vue'\r\nimport { systemChatIcon } from 'common/js/system_var.js'\r\nimport GlobalChat from '../../GlobalChat/GlobalChat.vue'\r\nconst elContainer = ref()\r\nconst isDragging = ref(false)\r\nconst positionX = ref(window.innerWidth - 100)\r\nconst positionY = ref(168)\r\nconst startPosX = ref(0)\r\nconst startPosY = ref(0)\r\nconst elShow = ref(false)\r\nconst isActive = ref(false)\r\nconst offset = ref([0, 0])\r\nconst chatTotal = ref(0)\r\nconst isActiveType = ref(false)\r\nconst callback = (value) => {\r\n  if (value < 10) {\r\n    offset.value = [0, 0]\r\n  } else if (value < 100) {\r\n    offset.value = [-4, 0]\r\n  } else {\r\n    offset.value = [-8, 0]\r\n  }\r\n  chatTotal.value = value\r\n}\r\nconst handleClick = () => {\r\n  if (!isDragging.value && !isActive.value) elShow.value = !elShow.value\r\n  isActive.value = false\r\n}\r\nconst handleMousedown = (event) => {\r\n  isDragging.value = true\r\n  startPosX.value = event.clientX - positionX.value\r\n  startPosY.value = event.clientY - positionY.value\r\n  document.addEventListener('mousemove', handleMousemove)\r\n  document.addEventListener('mouseup', handleMouseup)\r\n}\r\nconst handleMouseup = () => {\r\n  if (isDragging.value) {\r\n    isDragging.value = false\r\n    document.removeEventListener('mousemove', handleMousemove)\r\n    document.removeEventListener('mouseup', handleMouseup)\r\n    handleAutoSnap()\r\n  }\r\n}\r\nconst handleMousemove = (event) => {\r\n  if (isDragging.value) {\r\n    elShow.value = false\r\n    isActive.value = true\r\n    // requestAnimationFrame(() => {    \r\n    const newX = event.clientX - startPosX.value\r\n    const newY = event.clientY - startPosY.value\r\n    const windowWidth = window.innerWidth\r\n    const windowHeight = window.innerHeight\r\n    const elWidth = elContainer.value.offsetWidth\r\n    const elHeight = elContainer.value.offsetHeight\r\n    positionX.value = Math.max(0, Math.min(windowWidth - elWidth, newX))\r\n    positionY.value = Math.max(0, Math.min(windowHeight - elHeight, newY))\r\n    elContainer.value.style.top = positionY.value + 'px'\r\n    elContainer.value.style.left = positionX.value + 'px'\r\n    // })\r\n  }\r\n}\r\n// 自动吸附到最近的侧边\r\nconst handleAutoSnap = () => {\r\n  const windowWidth = window.innerWidth\r\n  const elWidth = elContainer.value.offsetWidth\r\n  if (positionX.value + elWidth / 2 < windowWidth / 2) {\r\n    positionX.value = 0\r\n    isActiveType.value = true\r\n  } else {\r\n    isActiveType.value = false\r\n    positionX.value = windowWidth - elWidth\r\n  }\r\n  elContainer.value.style.top = positionY.value + 'px'\r\n  elContainer.value.style.left = positionX.value + 'px'\r\n}\r\nonMounted(() => {\r\n  positionX.value = window.innerWidth - elContainer.value.offsetWidth\r\n  elContainer.value.style.top = positionY.value + 'px'\r\n  elContainer.value.style.left = positionX.value + 'px'\r\n  window.addEventListener('resize', handleAutoSnap)\r\n})\r\nonUnmounted(() => {\r\n  window.removeEventListener('resize', handleAutoSnap)\r\n})\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.GlobalChatFloating {\r\n  width: 66px;\r\n  height: 52px;\r\n  position: fixed;\r\n  cursor: pointer;\r\n  z-index: 99;\r\n\r\n  .GlobalChatFloatingBody {\r\n    width: 66px;\r\n    height: 52px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding-top: 6px;\r\n    padding-left: 12px;\r\n    background: #fff;\r\n    box-shadow: var(--zy-el-box-shadow-light);\r\n    overflow: hidden;\r\n\r\n    &.is-left {\r\n      padding-left: 2px;\r\n      border-radius: 0 26px 26px 0;\r\n    }\r\n\r\n    &.is-right {\r\n      border-radius: 26px 0 0 26px;\r\n    }\r\n\r\n    &.is-active {\r\n      padding: 0;\r\n      padding-top: 6px;\r\n      padding-left: 12px;\r\n      border-radius: 28px;\r\n    }\r\n\r\n    .zy-el-badge {\r\n      width: 38px;\r\n      height: 38px;\r\n\r\n      .zy-el-image {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.GlobalChatFloatingPopover {\r\n  width: 880px !important;\r\n  height: 580px !important;\r\n  padding: 0 !important;\r\n\r\n  .GlobalChatBox {\r\n\r\n    .GlobalChat {\r\n      width: 880px;\r\n    }\r\n  }\r\n}\r\n</style>"], "mappings": "AAmBA,SAASA,GAAG,EAAEC,SAAS,EAAEC,WAAW,QAAQ,KAAK;AACjD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,OAAOC,UAAU,MAAM,iCAAiC;AALxD,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAqB,CAAC;;;;;IAM7C,IAAMC,WAAW,GAAGP,GAAG,CAAC,CAAC;IACzB,IAAMQ,UAAU,GAAGR,GAAG,CAAC,KAAK,CAAC;IAC7B,IAAMS,SAAS,GAAGT,GAAG,CAACU,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;IAC9C,IAAMC,SAAS,GAAGZ,GAAG,CAAC,GAAG,CAAC;IAC1B,IAAMa,SAAS,GAAGb,GAAG,CAAC,CAAC,CAAC;IACxB,IAAMc,SAAS,GAAGd,GAAG,CAAC,CAAC,CAAC;IACxB,IAAMe,MAAM,GAAGf,GAAG,CAAC,KAAK,CAAC;IACzB,IAAMgB,QAAQ,GAAGhB,GAAG,CAAC,KAAK,CAAC;IAC3B,IAAMiB,MAAM,GAAGjB,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC1B,IAAMkB,SAAS,GAAGlB,GAAG,CAAC,CAAC,CAAC;IACxB,IAAMmB,YAAY,GAAGnB,GAAG,CAAC,KAAK,CAAC;IAC/B,IAAMoB,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,KAAK,EAAK;MAC1B,IAAIA,KAAK,GAAG,EAAE,EAAE;QACdJ,MAAM,CAACI,KAAK,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;MACvB,CAAC,MAAM,IAAIA,KAAK,GAAG,GAAG,EAAE;QACtBJ,MAAM,CAACI,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACxB,CAAC,MAAM;QACLJ,MAAM,CAACI,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;MACxB;MACAH,SAAS,CAACG,KAAK,GAAGA,KAAK;IACzB,CAAC;IACD,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MACxB,IAAI,CAACd,UAAU,CAACa,KAAK,IAAI,CAACL,QAAQ,CAACK,KAAK,EAAEN,MAAM,CAACM,KAAK,GAAG,CAACN,MAAM,CAACM,KAAK;MACtEL,QAAQ,CAACK,KAAK,GAAG,KAAK;IACxB,CAAC;IACD,IAAME,eAAe,GAAG,SAAlBA,eAAeA,CAAIC,KAAK,EAAK;MACjChB,UAAU,CAACa,KAAK,GAAG,IAAI;MACvBR,SAAS,CAACQ,KAAK,GAAGG,KAAK,CAACC,OAAO,GAAGhB,SAAS,CAACY,KAAK;MACjDP,SAAS,CAACO,KAAK,GAAGG,KAAK,CAACE,OAAO,GAAGd,SAAS,CAACS,KAAK;MACjDM,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEC,eAAe,CAAC;MACvDF,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEE,cAAa,CAAC;IACrD,CAAC;IACD,IAAMA,cAAa,GAAG,SAAhBA,aAAaA,CAAA,EAAS;MAC1B,IAAItB,UAAU,CAACa,KAAK,EAAE;QACpBb,UAAU,CAACa,KAAK,GAAG,KAAK;QACxBM,QAAQ,CAACI,mBAAmB,CAAC,WAAW,EAAEF,eAAe,CAAC;QAC1DF,QAAQ,CAACI,mBAAmB,CAAC,SAAS,EAAED,cAAa,CAAC;QACtDE,cAAc,CAAC,CAAC;MAClB;IACF,CAAC;IACD,IAAMH,eAAe,GAAG,SAAlBA,eAAeA,CAAIL,KAAK,EAAK;MACjC,IAAIhB,UAAU,CAACa,KAAK,EAAE;QACpBN,MAAM,CAACM,KAAK,GAAG,KAAK;QACpBL,QAAQ,CAACK,KAAK,GAAG,IAAI;QACrB;QACA,IAAMY,IAAI,GAAGT,KAAK,CAACC,OAAO,GAAGZ,SAAS,CAACQ,KAAK;QAC5C,IAAMa,IAAI,GAAGV,KAAK,CAACE,OAAO,GAAGZ,SAAS,CAACO,KAAK;QAC5C,IAAMc,WAAW,GAAGzB,MAAM,CAACC,UAAU;QACrC,IAAMyB,YAAY,GAAG1B,MAAM,CAAC2B,WAAW;QACvC,IAAMC,OAAO,GAAG/B,WAAW,CAACc,KAAK,CAACkB,WAAW;QAC7C,IAAMC,QAAQ,GAAGjC,WAAW,CAACc,KAAK,CAACoB,YAAY;QAC/ChC,SAAS,CAACY,KAAK,GAAGqB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACT,WAAW,GAAGG,OAAO,EAAEL,IAAI,CAAC,CAAC;QACpErB,SAAS,CAACS,KAAK,GAAGqB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACE,GAAG,CAACR,YAAY,GAAGI,QAAQ,EAAEN,IAAI,CAAC,CAAC;QACtE3B,WAAW,CAACc,KAAK,CAACwB,KAAK,CAACC,GAAG,GAAGlC,SAAS,CAACS,KAAK,GAAG,IAAI;QACpDd,WAAW,CAACc,KAAK,CAACwB,KAAK,CAACE,IAAI,GAAGtC,SAAS,CAACY,KAAK,GAAG,IAAI;QACrD;MACF;IACF,CAAC;IACD;IACA,IAAMW,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;MAC3B,IAAMG,WAAW,GAAGzB,MAAM,CAACC,UAAU;MACrC,IAAM2B,OAAO,GAAG/B,WAAW,CAACc,KAAK,CAACkB,WAAW;MAC7C,IAAI9B,SAAS,CAACY,KAAK,GAAGiB,OAAO,GAAG,CAAC,GAAGH,WAAW,GAAG,CAAC,EAAE;QACnD1B,SAAS,CAACY,KAAK,GAAG,CAAC;QACnBF,YAAY,CAACE,KAAK,GAAG,IAAI;MAC3B,CAAC,MAAM;QACLF,YAAY,CAACE,KAAK,GAAG,KAAK;QAC1BZ,SAAS,CAACY,KAAK,GAAGc,WAAW,GAAGG,OAAO;MACzC;MACA/B,WAAW,CAACc,KAAK,CAACwB,KAAK,CAACC,GAAG,GAAGlC,SAAS,CAACS,KAAK,GAAG,IAAI;MACpDd,WAAW,CAACc,KAAK,CAACwB,KAAK,CAACE,IAAI,GAAGtC,SAAS,CAACY,KAAK,GAAG,IAAI;IACvD,CAAC;IACDpB,SAAS,CAAC,YAAM;MACdQ,SAAS,CAACY,KAAK,GAAGX,MAAM,CAACC,UAAU,GAAGJ,WAAW,CAACc,KAAK,CAACkB,WAAW;MACnEhC,WAAW,CAACc,KAAK,CAACwB,KAAK,CAACC,GAAG,GAAGlC,SAAS,CAACS,KAAK,GAAG,IAAI;MACpDd,WAAW,CAACc,KAAK,CAACwB,KAAK,CAACE,IAAI,GAAGtC,SAAS,CAACY,KAAK,GAAG,IAAI;MACrDX,MAAM,CAACkB,gBAAgB,CAAC,QAAQ,EAAEI,cAAc,CAAC;IACnD,CAAC,CAAC;IACF9B,WAAW,CAAC,YAAM;MAChBQ,MAAM,CAACqB,mBAAmB,CAAC,QAAQ,EAAEC,cAAc,CAAC;IACtD,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}