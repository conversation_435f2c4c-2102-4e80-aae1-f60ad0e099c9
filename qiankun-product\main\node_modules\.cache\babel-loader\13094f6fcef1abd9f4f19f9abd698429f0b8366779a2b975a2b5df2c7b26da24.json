{"ast": null, "code": "import api from '@/api';\nimport { computed } from 'vue';\nvar __default__ = {\n  name: 'ChatSendImg'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    chatInfo: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    },\n    fileImg: {\n      type: Object,\n      default: function _default() {\n        return {};\n      }\n    }\n  },\n  emits: ['callback'],\n  setup(__props, _ref) {\n    var __expose = _ref.expose,\n      __emit = _ref.emit;\n    __expose();\n    var props = __props;\n    var emit = __emit;\n    var chatInfo = computed(function () {\n      return props.chatInfo;\n    });\n    var fileImg = computed(function () {\n      return props.fileImg;\n    });\n    // 图片地址拼接组合\n    var imgUrl = function imgUrl(url) {\n      return url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg');\n    };\n    var handleSubmit = function handleSubmit() {\n      emit('callback', true);\n    };\n    var handleReset = function handleReset() {\n      emit('callback', false);\n    };\n    var __returned__ = {\n      props,\n      emit,\n      chatInfo,\n      fileImg,\n      imgUrl,\n      handleSubmit,\n      handleReset,\n      get api() {\n        return api;\n      },\n      computed\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["api", "computed", "__default__", "name", "props", "__props", "emit", "__emit", "chatInfo", "fileImg", "imgUrl", "url", "fileURL", "defaultImgURL", "handleSubmit", "handleReset"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/GlobalChat/components/ChatSendImg/ChatSendImg.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ChatSendImg\">\r\n    <div class=\"ChatSendImgObject\">发送给：</div>\r\n    <div class=\"ChatSendImgUser\">\r\n      <el-image :src=\"imgUrl(chatInfo.chatObjectInfo.img)\" fit=\"cover\" draggable=\"false\" />\r\n      <div class=\"ChatSendImgUserName ellipsis\">{{ chatInfo.chatObjectInfo.name }}</div>\r\n    </div>\r\n    <div class=\"ChatSendImgBody\">\r\n      <el-image :src=\"fileImg.url\" fit=\"cover\" draggable=\"false\" />\r\n    </div>\r\n    <div class=\"ChatSendImgButton\">\r\n      <el-button @click=\"handleReset\">取消</el-button>\r\n      <el-button type=\"primary\" @click=\"handleSubmit\">发送</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ChatSendImg' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { computed } from 'vue'\r\nconst props = defineProps({\r\n  chatInfo: { type: Object, default: () => ({}) },\r\n  fileImg: { type: Object, default: () => ({}) }\r\n})\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst chatInfo = computed(() => props.chatInfo)\r\nconst fileImg = computed(() => props.fileImg)\r\n// 图片地址拼接组合\r\nconst imgUrl = url => url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg')\r\nconst handleSubmit = () => { emit('callback', true) }\r\nconst handleReset = () => { emit('callback', false) }\r\n</script>\r\n<style lang=\"scss\">\r\n.ChatSendImg {\r\n  width: 360px;\r\n  height: 100%;\r\n  padding: 20px 0;\r\n\r\n  .ChatSendImgObject {\r\n    width: 100%;\r\n    height: 26px;\r\n    line-height: 26px;\r\n    font-size: 16px;\r\n    padding: 0 20px;\r\n  }\r\n\r\n  .ChatSendImgUser {\r\n    width: 100%;\r\n    height: 68px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    position: relative;\r\n    padding: 0 20px 10px 20px;\r\n\r\n    &::after {\r\n      content: \"\";\r\n      width: calc(100% - 40px);\r\n      border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n      position: absolute;\r\n      left: 20px;\r\n      bottom: 10px;\r\n    }\r\n\r\n    .zy-el-image {\r\n      width: 38px;\r\n      height: 38px;\r\n      border-radius: 50%;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .ChatSendImgUserName {\r\n      width: calc(100% - 52px);\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  .ChatSendImgBody {\r\n    width: 100%;\r\n    height: calc(100% - 140px);\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .zy-el-image {\r\n      max-width: 80%;\r\n      height: 80%;\r\n    }\r\n  }\r\n\r\n  .ChatSendImgButton {\r\n    width: 100%;\r\n    height: 46px;\r\n    display: flex;\r\n    align-items: flex-end;\r\n    justify-content: center;\r\n\r\n    .zy-el-button {\r\n      width: 120px;\r\n      height: var(--zy-height-secondary);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAoBA,OAAOA,GAAG,MAAM,OAAO;AACvB,SAASC,QAAQ,QAAQ,KAAK;AAJ9B,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAc,CAAC;;;;;;;;;;;;;;;;;;;;;IAKtC,IAAMC,KAAK,GAAGC,OAGZ;IACF,IAAMC,IAAI,GAAGC,MAAyB;IAEtC,IAAMC,QAAQ,GAAGP,QAAQ,CAAC;MAAA,OAAMG,KAAK,CAACI,QAAQ;IAAA,EAAC;IAC/C,IAAMC,OAAO,GAAGR,QAAQ,CAAC;MAAA,OAAMG,KAAK,CAACK,OAAO;IAAA,EAAC;IAC7C;IACA,IAAMC,MAAM,GAAG,SAATA,MAAMA,CAAGC,GAAG;MAAA,OAAIA,GAAG,GAAGX,GAAG,CAACY,OAAO,CAACD,GAAG,CAAC,GAAGX,GAAG,CAACa,aAAa,CAAC,uBAAuB,CAAC;IAAA;IACzF,IAAMC,YAAY,GAAG,SAAfA,YAAYA,CAAA,EAAS;MAAER,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAAC,CAAC;IACrD,IAAMS,WAAW,GAAG,SAAdA,WAAWA,CAAA,EAAS;MAAET,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC;IAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}