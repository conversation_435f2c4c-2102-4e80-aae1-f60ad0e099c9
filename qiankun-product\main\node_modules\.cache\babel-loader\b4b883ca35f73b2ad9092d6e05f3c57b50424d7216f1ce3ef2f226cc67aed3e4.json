{"ast": null, "code": "import { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, resolveDirective as _resolveDirective, openBlock as _openBlock, createElement<PERSON>lock as _createElementBlock, withDirectives as _withDirectives } from \"vue\";\nvar _hoisted_1 = {\n  class: \"preview-text\"\n};\nvar _hoisted_2 = {\n  class: \"preview-text-text\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _directive_loading = _resolveDirective(\"loading\");\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_scrollbar, {\n    class: \"preview-text-box\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"pre\", _hoisted_2, _toDisplayString($setup.text), 1 /* TEXT */)];\n    }),\n    _: 1 /* STABLE */\n  })])), [[_directive_loading, $setup.loading]]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_scrollbar", "default", "_withCtx", "_createElementVNode", "_hoisted_2", "_toDisplayString", "$setup", "text", "_", "loading"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\main\\src\\components\\global-file-preview\\components\\preview-text.vue"], "sourcesContent": ["<template>\r\n  <div class=\"preview-text\" v-loading=\"loading\">\r\n    <el-scrollbar class=\"preview-text-box\">\r\n      <pre class=\"preview-text-text\">{{ text }}</pre>\r\n    </el-scrollbar>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'PreviewText' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst text = ref('')\r\nconst loading = ref(false)\r\nonMounted(() => { globalDownload() })\r\n\r\nconst globalDownload = async () => {\r\n  loading.value = true\r\n  const res = await api.globalDownload(props.id)\r\n  const arrayBuffer = await readBuffer(res)\r\n  text.value = await readText(arrayBuffer)\r\n  loading.value = false\r\n}\r\nconst readBuffer = async (file) => {\r\n  return new Promise((resolve, reject) => {\r\n    const reader = new FileReader()\r\n    reader.onload = loadEvent => resolve(loadEvent.target.result)\r\n    reader.onerror = e => reject(e)\r\n    reader.readAsArrayBuffer(file)\r\n  })\r\n}\r\nconst readText = async (buffer) => {\r\n  return new Promise((resolve, reject) => {\r\n    const reader = new FileReader()\r\n    reader.onload = loadEvent => resolve(loadEvent.target.result)\r\n    reader.onerror = e => reject(e)\r\n    reader.readAsText(new Blob([buffer]), 'utf-8')\r\n  })\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.preview-text {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .preview-text-box {\r\n    width: 100%;\r\n    height: 100%;\r\n\r\n    .zy-el-scrollbar__wrap {\r\n      overflow-x: hidden;\r\n    }\r\n\r\n    .is-vertical {\r\n      width: 9px;\r\n      border-radius: var(--el-border-radius-base);\r\n\r\n      .zy-el-scrollbar__thumb {\r\n        border-radius: var(--el-border-radius-base);\r\n      }\r\n    }\r\n  }\r\n\r\n  .preview-text-text {\r\n    width: 990px;\r\n    padding: 22px;\r\n    display: block;\r\n    font-size: 16px;\r\n    line-height: 24px;\r\n    word-break: break-word;\r\n    white-space: break-spaces;\r\n    background-color: #fff;\r\n    margin: 20px auto;\r\n    box-shadow: 0px 2px 4px 1px rgba(0, 0, 0, 0.05);\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAc;;EAEhBA,KAAK,EAAC;AAAmB;;;;wCAFlCC,mBAAA,CAIM,OAJNC,UAIM,GAHJC,YAAA,CAEeC,uBAAA;IAFDJ,KAAK,EAAC;EAAkB;IAF1CK,OAAA,EAAAC,QAAA,CAGM;MAAA,OAA+C,CAA/CC,mBAAA,CAA+C,OAA/CC,UAA+C,EAAAC,gBAAA,CAAbC,MAAA,CAAAC,IAAI,iB;;IAH5CC,CAAA;+BACuCF,MAAA,CAAAG,OAAO,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}