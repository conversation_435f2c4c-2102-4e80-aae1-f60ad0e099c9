{"ast": null, "code": "// Main parser class\n\n'use strict';\n\nvar utils = require('./common/utils');\nvar helpers = require('./helpers');\nvar Renderer = require('./renderer');\nvar ParserCore = require('./parser_core');\nvar ParserBlock = require('./parser_block');\nvar ParserInline = require('./parser_inline');\nvar LinkifyIt = require('linkify-it');\nvar mdurl = require('mdurl');\nvar punycode = require('punycode');\nvar config = {\n  default: require('./presets/default'),\n  zero: require('./presets/zero'),\n  commonmark: require('./presets/commonmark')\n};\n\n////////////////////////////////////////////////////////////////////////////////\n//\n// This validator can prohibit more than really needed to prevent XSS. It's a\n// tradeoff to keep code simple and to be secure by default.\n//\n// If you need different setup - override validator method as you wish. Or\n// replace it with dummy function and use external sanitizer.\n//\n\nvar BAD_PROTO_RE = /^(vbscript|javascript|file|data):/;\nvar GOOD_DATA_RE = /^data:image\\/(gif|png|jpeg|webp);/;\nfunction validateLink(url) {\n  // url should be normalized at this point, and existing entities are decoded\n  var str = url.trim().toLowerCase();\n  return BAD_PROTO_RE.test(str) ? GOOD_DATA_RE.test(str) ? true : false : true;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\nvar RECODE_HOSTNAME_FOR = ['http:', 'https:', 'mailto:'];\nfunction normalizeLink(url) {\n  var parsed = mdurl.parse(url, true);\n  if (parsed.hostname) {\n    // Encode hostnames in urls like:\n    // `http://host/`, `https://host/`, `mailto:user@host`, `//host/`\n    //\n    // We don't encode unknown schemas, because it's likely that we encode\n    // something we shouldn't (e.g. `skype:name` treated as `skype:host`)\n    //\n    if (!parsed.protocol || RECODE_HOSTNAME_FOR.indexOf(parsed.protocol) >= 0) {\n      try {\n        parsed.hostname = punycode.toASCII(parsed.hostname);\n      } catch (er) {/**/}\n    }\n  }\n  return mdurl.encode(mdurl.format(parsed));\n}\nfunction normalizeLinkText(url) {\n  var parsed = mdurl.parse(url, true);\n  if (parsed.hostname) {\n    // Encode hostnames in urls like:\n    // `http://host/`, `https://host/`, `mailto:user@host`, `//host/`\n    //\n    // We don't encode unknown schemas, because it's likely that we encode\n    // something we shouldn't (e.g. `skype:name` treated as `skype:host`)\n    //\n    if (!parsed.protocol || RECODE_HOSTNAME_FOR.indexOf(parsed.protocol) >= 0) {\n      try {\n        parsed.hostname = punycode.toUnicode(parsed.hostname);\n      } catch (er) {/**/}\n    }\n  }\n\n  // add '%' to exclude list because of https://github.com/markdown-it/markdown-it/issues/720\n  return mdurl.decode(mdurl.format(parsed), mdurl.decode.defaultChars + '%');\n}\n\n/**\n * class MarkdownIt\n *\n * Main parser/renderer class.\n *\n * ##### Usage\n *\n * ```javascript\n * // node.js, \"classic\" way:\n * var MarkdownIt = require('markdown-it'),\n *     md = new MarkdownIt();\n * var result = md.render('# markdown-it rulezz!');\n *\n * // node.js, the same, but with sugar:\n * var md = require('markdown-it')();\n * var result = md.render('# markdown-it rulezz!');\n *\n * // browser without AMD, added to \"window\" on script load\n * // Note, there are no dash.\n * var md = window.markdownit();\n * var result = md.render('# markdown-it rulezz!');\n * ```\n *\n * Single line rendering, without paragraph wrap:\n *\n * ```javascript\n * var md = require('markdown-it')();\n * var result = md.renderInline('__markdown-it__ rulezz!');\n * ```\n **/\n\n/**\n * new MarkdownIt([presetName, options])\n * - presetName (String): optional, `commonmark` / `zero`\n * - options (Object)\n *\n * Creates parser instanse with given config. Can be called without `new`.\n *\n * ##### presetName\n *\n * MarkdownIt provides named presets as a convenience to quickly\n * enable/disable active syntax rules and options for common use cases.\n *\n * - [\"commonmark\"](https://github.com/markdown-it/markdown-it/blob/master/lib/presets/commonmark.js) -\n *   configures parser to strict [CommonMark](http://commonmark.org/) mode.\n * - [default](https://github.com/markdown-it/markdown-it/blob/master/lib/presets/default.js) -\n *   similar to GFM, used when no preset name given. Enables all available rules,\n *   but still without html, typographer & autolinker.\n * - [\"zero\"](https://github.com/markdown-it/markdown-it/blob/master/lib/presets/zero.js) -\n *   all rules disabled. Useful to quickly setup your config via `.enable()`.\n *   For example, when you need only `bold` and `italic` markup and nothing else.\n *\n * ##### options:\n *\n * - __html__ - `false`. Set `true` to enable HTML tags in source. Be careful!\n *   That's not safe! You may need external sanitizer to protect output from XSS.\n *   It's better to extend features via plugins, instead of enabling HTML.\n * - __xhtmlOut__ - `false`. Set `true` to add '/' when closing single tags\n *   (`<br />`). This is needed only for full CommonMark compatibility. In real\n *   world you will need HTML output.\n * - __breaks__ - `false`. Set `true` to convert `\\n` in paragraphs into `<br>`.\n * - __langPrefix__ - `language-`. CSS language class prefix for fenced blocks.\n *   Can be useful for external highlighters.\n * - __linkify__ - `false`. Set `true` to autoconvert URL-like text to links.\n * - __typographer__  - `false`. Set `true` to enable [some language-neutral\n *   replacement](https://github.com/markdown-it/markdown-it/blob/master/lib/rules_core/replacements.js) +\n *   quotes beautification (smartquotes).\n * - __quotes__ - `“”‘’`, String or Array. Double + single quotes replacement\n *   pairs, when typographer enabled and smartquotes on. For example, you can\n *   use `'«»„“'` for Russian, `'„“‚‘'` for German, and\n *   `['«\\xA0', '\\xA0»', '‹\\xA0', '\\xA0›']` for French (including nbsp).\n * - __highlight__ - `null`. Highlighter function for fenced code blocks.\n *   Highlighter `function (str, lang)` should return escaped HTML. It can also\n *   return empty string if the source was not changed and should be escaped\n *   externaly. If result starts with <pre... internal wrapper is skipped.\n *\n * ##### Example\n *\n * ```javascript\n * // commonmark mode\n * var md = require('markdown-it')('commonmark');\n *\n * // default mode\n * var md = require('markdown-it')();\n *\n * // enable everything\n * var md = require('markdown-it')({\n *   html: true,\n *   linkify: true,\n *   typographer: true\n * });\n * ```\n *\n * ##### Syntax highlighting\n *\n * ```js\n * var hljs = require('highlight.js') // https://highlightjs.org/\n *\n * var md = require('markdown-it')({\n *   highlight: function (str, lang) {\n *     if (lang && hljs.getLanguage(lang)) {\n *       try {\n *         return hljs.highlight(str, { language: lang, ignoreIllegals: true }).value;\n *       } catch (__) {}\n *     }\n *\n *     return ''; // use external default escaping\n *   }\n * });\n * ```\n *\n * Or with full wrapper override (if you need assign class to `<pre>`):\n *\n * ```javascript\n * var hljs = require('highlight.js') // https://highlightjs.org/\n *\n * // Actual default values\n * var md = require('markdown-it')({\n *   highlight: function (str, lang) {\n *     if (lang && hljs.getLanguage(lang)) {\n *       try {\n *         return '<pre class=\"hljs\"><code>' +\n *                hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +\n *                '</code></pre>';\n *       } catch (__) {}\n *     }\n *\n *     return '<pre class=\"hljs\"><code>' + md.utils.escapeHtml(str) + '</code></pre>';\n *   }\n * });\n * ```\n *\n **/\nfunction MarkdownIt(presetName, options) {\n  if (!(this instanceof MarkdownIt)) {\n    return new MarkdownIt(presetName, options);\n  }\n  if (!options) {\n    if (!utils.isString(presetName)) {\n      options = presetName || {};\n      presetName = 'default';\n    }\n  }\n\n  /**\n   * MarkdownIt#inline -> ParserInline\n   *\n   * Instance of [[ParserInline]]. You may need it to add new rules when\n   * writing plugins. For simple rules control use [[MarkdownIt.disable]] and\n   * [[MarkdownIt.enable]].\n   **/\n  this.inline = new ParserInline();\n\n  /**\n   * MarkdownIt#block -> ParserBlock\n   *\n   * Instance of [[ParserBlock]]. You may need it to add new rules when\n   * writing plugins. For simple rules control use [[MarkdownIt.disable]] and\n   * [[MarkdownIt.enable]].\n   **/\n  this.block = new ParserBlock();\n\n  /**\n   * MarkdownIt#core -> Core\n   *\n   * Instance of [[Core]] chain executor. You may need it to add new rules when\n   * writing plugins. For simple rules control use [[MarkdownIt.disable]] and\n   * [[MarkdownIt.enable]].\n   **/\n  this.core = new ParserCore();\n\n  /**\n   * MarkdownIt#renderer -> Renderer\n   *\n   * Instance of [[Renderer]]. Use it to modify output look. Or to add rendering\n   * rules for new token types, generated by plugins.\n   *\n   * ##### Example\n   *\n   * ```javascript\n   * var md = require('markdown-it')();\n   *\n   * function myToken(tokens, idx, options, env, self) {\n   *   //...\n   *   return result;\n   * };\n   *\n   * md.renderer.rules['my_token'] = myToken\n   * ```\n   *\n   * See [[Renderer]] docs and [source code](https://github.com/markdown-it/markdown-it/blob/master/lib/renderer.js).\n   **/\n  this.renderer = new Renderer();\n\n  /**\n   * MarkdownIt#linkify -> LinkifyIt\n   *\n   * [linkify-it](https://github.com/markdown-it/linkify-it) instance.\n   * Used by [linkify](https://github.com/markdown-it/markdown-it/blob/master/lib/rules_core/linkify.js)\n   * rule.\n   **/\n  this.linkify = new LinkifyIt();\n\n  /**\n   * MarkdownIt#validateLink(url) -> Boolean\n   *\n   * Link validation function. CommonMark allows too much in links. By default\n   * we disable `javascript:`, `vbscript:`, `file:` schemas, and almost all `data:...` schemas\n   * except some embedded image types.\n   *\n   * You can change this behaviour:\n   *\n   * ```javascript\n   * var md = require('markdown-it')();\n   * // enable everything\n   * md.validateLink = function () { return true; }\n   * ```\n   **/\n  this.validateLink = validateLink;\n\n  /**\n   * MarkdownIt#normalizeLink(url) -> String\n   *\n   * Function used to encode link url to a machine-readable format,\n   * which includes url-encoding, punycode, etc.\n   **/\n  this.normalizeLink = normalizeLink;\n\n  /**\n   * MarkdownIt#normalizeLinkText(url) -> String\n   *\n   * Function used to decode link url to a human-readable format`\n   **/\n  this.normalizeLinkText = normalizeLinkText;\n\n  // Expose utils & helpers for easy acces from plugins\n\n  /**\n   * MarkdownIt#utils -> utils\n   *\n   * Assorted utility functions, useful to write plugins. See details\n   * [here](https://github.com/markdown-it/markdown-it/blob/master/lib/common/utils.js).\n   **/\n  this.utils = utils;\n\n  /**\n   * MarkdownIt#helpers -> helpers\n   *\n   * Link components parser functions, useful to write plugins. See details\n   * [here](https://github.com/markdown-it/markdown-it/blob/master/lib/helpers).\n   **/\n  this.helpers = utils.assign({}, helpers);\n  this.options = {};\n  this.configure(presetName);\n  if (options) {\n    this.set(options);\n  }\n}\n\n/** chainable\n * MarkdownIt.set(options)\n *\n * Set parser options (in the same format as in constructor). Probably, you\n * will never need it, but you can change options after constructor call.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')()\n *             .set({ html: true, breaks: true })\n *             .set({ typographer, true });\n * ```\n *\n * __Note:__ To achieve the best possible performance, don't modify a\n * `markdown-it` instance options on the fly. If you need multiple configurations\n * it's best to create multiple instances and initialize each with separate\n * config.\n **/\nMarkdownIt.prototype.set = function (options) {\n  utils.assign(this.options, options);\n  return this;\n};\n\n/** chainable, internal\n * MarkdownIt.configure(presets)\n *\n * Batch load of all options and compenent settings. This is internal method,\n * and you probably will not need it. But if you will - see available presets\n * and data structure [here](https://github.com/markdown-it/markdown-it/tree/master/lib/presets)\n *\n * We strongly recommend to use presets instead of direct config loads. That\n * will give better compatibility with next versions.\n **/\nMarkdownIt.prototype.configure = function (presets) {\n  var self = this,\n    presetName;\n  if (utils.isString(presets)) {\n    presetName = presets;\n    presets = config[presetName];\n    if (!presets) {\n      throw new Error('Wrong `markdown-it` preset \"' + presetName + '\", check name');\n    }\n  }\n  if (!presets) {\n    throw new Error('Wrong `markdown-it` preset, can\\'t be empty');\n  }\n  if (presets.options) {\n    self.set(presets.options);\n  }\n  if (presets.components) {\n    Object.keys(presets.components).forEach(function (name) {\n      if (presets.components[name].rules) {\n        self[name].ruler.enableOnly(presets.components[name].rules);\n      }\n      if (presets.components[name].rules2) {\n        self[name].ruler2.enableOnly(presets.components[name].rules2);\n      }\n    });\n  }\n  return this;\n};\n\n/** chainable\n * MarkdownIt.enable(list, ignoreInvalid)\n * - list (String|Array): rule name or list of rule names to enable\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Enable list or rules. It will automatically find appropriate components,\n * containing rules with given names. If rule not found, and `ignoreInvalid`\n * not set - throws exception.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')()\n *             .enable(['sub', 'sup'])\n *             .disable('smartquotes');\n * ```\n **/\nMarkdownIt.prototype.enable = function (list, ignoreInvalid) {\n  var result = [];\n  if (!Array.isArray(list)) {\n    list = [list];\n  }\n  ['core', 'block', 'inline'].forEach(function (chain) {\n    result = result.concat(this[chain].ruler.enable(list, true));\n  }, this);\n  result = result.concat(this.inline.ruler2.enable(list, true));\n  var missed = list.filter(function (name) {\n    return result.indexOf(name) < 0;\n  });\n  if (missed.length && !ignoreInvalid) {\n    throw new Error('MarkdownIt. Failed to enable unknown rule(s): ' + missed);\n  }\n  return this;\n};\n\n/** chainable\n * MarkdownIt.disable(list, ignoreInvalid)\n * - list (String|Array): rule name or list of rule names to disable.\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * The same as [[MarkdownIt.enable]], but turn specified rules off.\n **/\nMarkdownIt.prototype.disable = function (list, ignoreInvalid) {\n  var result = [];\n  if (!Array.isArray(list)) {\n    list = [list];\n  }\n  ['core', 'block', 'inline'].forEach(function (chain) {\n    result = result.concat(this[chain].ruler.disable(list, true));\n  }, this);\n  result = result.concat(this.inline.ruler2.disable(list, true));\n  var missed = list.filter(function (name) {\n    return result.indexOf(name) < 0;\n  });\n  if (missed.length && !ignoreInvalid) {\n    throw new Error('MarkdownIt. Failed to disable unknown rule(s): ' + missed);\n  }\n  return this;\n};\n\n/** chainable\n * MarkdownIt.use(plugin, params)\n *\n * Load specified plugin with given params into current parser instance.\n * It's just a sugar to call `plugin(md, params)` with curring.\n *\n * ##### Example\n *\n * ```javascript\n * var iterator = require('markdown-it-for-inline');\n * var md = require('markdown-it')()\n *             .use(iterator, 'foo_replace', 'text', function (tokens, idx) {\n *               tokens[idx].content = tokens[idx].content.replace(/foo/g, 'bar');\n *             });\n * ```\n **/\nMarkdownIt.prototype.use = function (plugin /*, params, ... */) {\n  var args = [this].concat(Array.prototype.slice.call(arguments, 1));\n  plugin.apply(plugin, args);\n  return this;\n};\n\n/** internal\n * MarkdownIt.parse(src, env) -> Array\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * Parse input string and return list of block tokens (special token type\n * \"inline\" will contain list of inline tokens). You should not call this\n * method directly, until you write custom renderer (for example, to produce\n * AST).\n *\n * `env` is used to pass data between \"distributed\" rules and return additional\n * metadata like reference info, needed for the renderer. It also can be used to\n * inject data in specific cases. Usually, you will be ok to pass `{}`,\n * and then pass updated object to renderer.\n **/\nMarkdownIt.prototype.parse = function (src, env) {\n  if (typeof src !== 'string') {\n    throw new Error('Input data should be a String');\n  }\n  var state = new this.core.State(src, this, env);\n  this.core.process(state);\n  return state.tokens;\n};\n\n/**\n * MarkdownIt.render(src [, env]) -> String\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * Render markdown string into html. It does all magic for you :).\n *\n * `env` can be used to inject additional metadata (`{}` by default).\n * But you will not need it with high probability. See also comment\n * in [[MarkdownIt.parse]].\n **/\nMarkdownIt.prototype.render = function (src, env) {\n  env = env || {};\n  return this.renderer.render(this.parse(src, env), this.options, env);\n};\n\n/** internal\n * MarkdownIt.parseInline(src, env) -> Array\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * The same as [[MarkdownIt.parse]] but skip all block rules. It returns the\n * block tokens list with the single `inline` element, containing parsed inline\n * tokens in `children` property. Also updates `env` object.\n **/\nMarkdownIt.prototype.parseInline = function (src, env) {\n  var state = new this.core.State(src, this, env);\n  state.inlineMode = true;\n  this.core.process(state);\n  return state.tokens;\n};\n\n/**\n * MarkdownIt.renderInline(src [, env]) -> String\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * Similar to [[MarkdownIt.render]] but for single paragraph content. Result\n * will NOT be wrapped into `<p>` tags.\n **/\nMarkdownIt.prototype.renderInline = function (src, env) {\n  env = env || {};\n  return this.renderer.render(this.parseInline(src, env), this.options, env);\n};\nmodule.exports = MarkdownIt;", "map": {"version": 3, "names": ["utils", "require", "helpers", "<PERSON><PERSON><PERSON>", "ParserCore", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ParserInline", "LinkifyIt", "mdurl", "punycode", "config", "default", "zero", "commonmark", "BAD_PROTO_RE", "GOOD_DATA_RE", "validateLink", "url", "str", "trim", "toLowerCase", "test", "RECODE_HOSTNAME_FOR", "normalizeLink", "parsed", "parse", "hostname", "protocol", "indexOf", "toASCII", "er", "encode", "format", "normalizeLinkText", "toUnicode", "decode", "defaultChars", "MarkdownIt", "presetName", "options", "isString", "inline", "block", "core", "renderer", "linkify", "assign", "configure", "set", "prototype", "presets", "self", "Error", "components", "Object", "keys", "for<PERSON>ach", "name", "rules", "ruler", "enableOnly", "rules2", "ruler2", "enable", "list", "ignoreInvalid", "result", "Array", "isArray", "chain", "concat", "missed", "filter", "length", "disable", "use", "plugin", "args", "slice", "call", "arguments", "apply", "src", "env", "state", "State", "process", "tokens", "render", "parseInline", "inlineMode", "renderInline", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/index.js"], "sourcesContent": ["// Main parser class\n\n'use strict';\n\n\nvar utils        = require('./common/utils');\nvar helpers      = require('./helpers');\nvar Renderer     = require('./renderer');\nvar ParserCore   = require('./parser_core');\nvar ParserBlock  = require('./parser_block');\nvar ParserInline = require('./parser_inline');\nvar LinkifyIt    = require('linkify-it');\nvar mdurl        = require('mdurl');\nvar punycode     = require('punycode');\n\n\nvar config = {\n  default: require('./presets/default'),\n  zero: require('./presets/zero'),\n  commonmark: require('./presets/commonmark')\n};\n\n////////////////////////////////////////////////////////////////////////////////\n//\n// This validator can prohibit more than really needed to prevent XSS. It's a\n// tradeoff to keep code simple and to be secure by default.\n//\n// If you need different setup - override validator method as you wish. Or\n// replace it with dummy function and use external sanitizer.\n//\n\nvar BAD_PROTO_RE = /^(vbscript|javascript|file|data):/;\nvar GOOD_DATA_RE = /^data:image\\/(gif|png|jpeg|webp);/;\n\nfunction validateLink(url) {\n  // url should be normalized at this point, and existing entities are decoded\n  var str = url.trim().toLowerCase();\n\n  return BAD_PROTO_RE.test(str) ? (GOOD_DATA_RE.test(str) ? true : false) : true;\n}\n\n////////////////////////////////////////////////////////////////////////////////\n\n\nvar RECODE_HOSTNAME_FOR = [ 'http:', 'https:', 'mailto:' ];\n\nfunction normalizeLink(url) {\n  var parsed = mdurl.parse(url, true);\n\n  if (parsed.hostname) {\n    // Encode hostnames in urls like:\n    // `http://host/`, `https://host/`, `mailto:user@host`, `//host/`\n    //\n    // We don't encode unknown schemas, because it's likely that we encode\n    // something we shouldn't (e.g. `skype:name` treated as `skype:host`)\n    //\n    if (!parsed.protocol || RECODE_HOSTNAME_FOR.indexOf(parsed.protocol) >= 0) {\n      try {\n        parsed.hostname = punycode.toASCII(parsed.hostname);\n      } catch (er) { /**/ }\n    }\n  }\n\n  return mdurl.encode(mdurl.format(parsed));\n}\n\nfunction normalizeLinkText(url) {\n  var parsed = mdurl.parse(url, true);\n\n  if (parsed.hostname) {\n    // Encode hostnames in urls like:\n    // `http://host/`, `https://host/`, `mailto:user@host`, `//host/`\n    //\n    // We don't encode unknown schemas, because it's likely that we encode\n    // something we shouldn't (e.g. `skype:name` treated as `skype:host`)\n    //\n    if (!parsed.protocol || RECODE_HOSTNAME_FOR.indexOf(parsed.protocol) >= 0) {\n      try {\n        parsed.hostname = punycode.toUnicode(parsed.hostname);\n      } catch (er) { /**/ }\n    }\n  }\n\n  // add '%' to exclude list because of https://github.com/markdown-it/markdown-it/issues/720\n  return mdurl.decode(mdurl.format(parsed), mdurl.decode.defaultChars + '%');\n}\n\n\n/**\n * class MarkdownIt\n *\n * Main parser/renderer class.\n *\n * ##### Usage\n *\n * ```javascript\n * // node.js, \"classic\" way:\n * var MarkdownIt = require('markdown-it'),\n *     md = new MarkdownIt();\n * var result = md.render('# markdown-it rulezz!');\n *\n * // node.js, the same, but with sugar:\n * var md = require('markdown-it')();\n * var result = md.render('# markdown-it rulezz!');\n *\n * // browser without AMD, added to \"window\" on script load\n * // Note, there are no dash.\n * var md = window.markdownit();\n * var result = md.render('# markdown-it rulezz!');\n * ```\n *\n * Single line rendering, without paragraph wrap:\n *\n * ```javascript\n * var md = require('markdown-it')();\n * var result = md.renderInline('__markdown-it__ rulezz!');\n * ```\n **/\n\n/**\n * new MarkdownIt([presetName, options])\n * - presetName (String): optional, `commonmark` / `zero`\n * - options (Object)\n *\n * Creates parser instanse with given config. Can be called without `new`.\n *\n * ##### presetName\n *\n * MarkdownIt provides named presets as a convenience to quickly\n * enable/disable active syntax rules and options for common use cases.\n *\n * - [\"commonmark\"](https://github.com/markdown-it/markdown-it/blob/master/lib/presets/commonmark.js) -\n *   configures parser to strict [CommonMark](http://commonmark.org/) mode.\n * - [default](https://github.com/markdown-it/markdown-it/blob/master/lib/presets/default.js) -\n *   similar to GFM, used when no preset name given. Enables all available rules,\n *   but still without html, typographer & autolinker.\n * - [\"zero\"](https://github.com/markdown-it/markdown-it/blob/master/lib/presets/zero.js) -\n *   all rules disabled. Useful to quickly setup your config via `.enable()`.\n *   For example, when you need only `bold` and `italic` markup and nothing else.\n *\n * ##### options:\n *\n * - __html__ - `false`. Set `true` to enable HTML tags in source. Be careful!\n *   That's not safe! You may need external sanitizer to protect output from XSS.\n *   It's better to extend features via plugins, instead of enabling HTML.\n * - __xhtmlOut__ - `false`. Set `true` to add '/' when closing single tags\n *   (`<br />`). This is needed only for full CommonMark compatibility. In real\n *   world you will need HTML output.\n * - __breaks__ - `false`. Set `true` to convert `\\n` in paragraphs into `<br>`.\n * - __langPrefix__ - `language-`. CSS language class prefix for fenced blocks.\n *   Can be useful for external highlighters.\n * - __linkify__ - `false`. Set `true` to autoconvert URL-like text to links.\n * - __typographer__  - `false`. Set `true` to enable [some language-neutral\n *   replacement](https://github.com/markdown-it/markdown-it/blob/master/lib/rules_core/replacements.js) +\n *   quotes beautification (smartquotes).\n * - __quotes__ - `“”‘’`, String or Array. Double + single quotes replacement\n *   pairs, when typographer enabled and smartquotes on. For example, you can\n *   use `'«»„“'` for Russian, `'„“‚‘'` for German, and\n *   `['«\\xA0', '\\xA0»', '‹\\xA0', '\\xA0›']` for French (including nbsp).\n * - __highlight__ - `null`. Highlighter function for fenced code blocks.\n *   Highlighter `function (str, lang)` should return escaped HTML. It can also\n *   return empty string if the source was not changed and should be escaped\n *   externaly. If result starts with <pre... internal wrapper is skipped.\n *\n * ##### Example\n *\n * ```javascript\n * // commonmark mode\n * var md = require('markdown-it')('commonmark');\n *\n * // default mode\n * var md = require('markdown-it')();\n *\n * // enable everything\n * var md = require('markdown-it')({\n *   html: true,\n *   linkify: true,\n *   typographer: true\n * });\n * ```\n *\n * ##### Syntax highlighting\n *\n * ```js\n * var hljs = require('highlight.js') // https://highlightjs.org/\n *\n * var md = require('markdown-it')({\n *   highlight: function (str, lang) {\n *     if (lang && hljs.getLanguage(lang)) {\n *       try {\n *         return hljs.highlight(str, { language: lang, ignoreIllegals: true }).value;\n *       } catch (__) {}\n *     }\n *\n *     return ''; // use external default escaping\n *   }\n * });\n * ```\n *\n * Or with full wrapper override (if you need assign class to `<pre>`):\n *\n * ```javascript\n * var hljs = require('highlight.js') // https://highlightjs.org/\n *\n * // Actual default values\n * var md = require('markdown-it')({\n *   highlight: function (str, lang) {\n *     if (lang && hljs.getLanguage(lang)) {\n *       try {\n *         return '<pre class=\"hljs\"><code>' +\n *                hljs.highlight(str, { language: lang, ignoreIllegals: true }).value +\n *                '</code></pre>';\n *       } catch (__) {}\n *     }\n *\n *     return '<pre class=\"hljs\"><code>' + md.utils.escapeHtml(str) + '</code></pre>';\n *   }\n * });\n * ```\n *\n **/\nfunction MarkdownIt(presetName, options) {\n  if (!(this instanceof MarkdownIt)) {\n    return new MarkdownIt(presetName, options);\n  }\n\n  if (!options) {\n    if (!utils.isString(presetName)) {\n      options = presetName || {};\n      presetName = 'default';\n    }\n  }\n\n  /**\n   * MarkdownIt#inline -> ParserInline\n   *\n   * Instance of [[ParserInline]]. You may need it to add new rules when\n   * writing plugins. For simple rules control use [[MarkdownIt.disable]] and\n   * [[MarkdownIt.enable]].\n   **/\n  this.inline = new ParserInline();\n\n  /**\n   * MarkdownIt#block -> ParserBlock\n   *\n   * Instance of [[ParserBlock]]. You may need it to add new rules when\n   * writing plugins. For simple rules control use [[MarkdownIt.disable]] and\n   * [[MarkdownIt.enable]].\n   **/\n  this.block = new ParserBlock();\n\n  /**\n   * MarkdownIt#core -> Core\n   *\n   * Instance of [[Core]] chain executor. You may need it to add new rules when\n   * writing plugins. For simple rules control use [[MarkdownIt.disable]] and\n   * [[MarkdownIt.enable]].\n   **/\n  this.core = new ParserCore();\n\n  /**\n   * MarkdownIt#renderer -> Renderer\n   *\n   * Instance of [[Renderer]]. Use it to modify output look. Or to add rendering\n   * rules for new token types, generated by plugins.\n   *\n   * ##### Example\n   *\n   * ```javascript\n   * var md = require('markdown-it')();\n   *\n   * function myToken(tokens, idx, options, env, self) {\n   *   //...\n   *   return result;\n   * };\n   *\n   * md.renderer.rules['my_token'] = myToken\n   * ```\n   *\n   * See [[Renderer]] docs and [source code](https://github.com/markdown-it/markdown-it/blob/master/lib/renderer.js).\n   **/\n  this.renderer = new Renderer();\n\n  /**\n   * MarkdownIt#linkify -> LinkifyIt\n   *\n   * [linkify-it](https://github.com/markdown-it/linkify-it) instance.\n   * Used by [linkify](https://github.com/markdown-it/markdown-it/blob/master/lib/rules_core/linkify.js)\n   * rule.\n   **/\n  this.linkify = new LinkifyIt();\n\n  /**\n   * MarkdownIt#validateLink(url) -> Boolean\n   *\n   * Link validation function. CommonMark allows too much in links. By default\n   * we disable `javascript:`, `vbscript:`, `file:` schemas, and almost all `data:...` schemas\n   * except some embedded image types.\n   *\n   * You can change this behaviour:\n   *\n   * ```javascript\n   * var md = require('markdown-it')();\n   * // enable everything\n   * md.validateLink = function () { return true; }\n   * ```\n   **/\n  this.validateLink = validateLink;\n\n  /**\n   * MarkdownIt#normalizeLink(url) -> String\n   *\n   * Function used to encode link url to a machine-readable format,\n   * which includes url-encoding, punycode, etc.\n   **/\n  this.normalizeLink = normalizeLink;\n\n  /**\n   * MarkdownIt#normalizeLinkText(url) -> String\n   *\n   * Function used to decode link url to a human-readable format`\n   **/\n  this.normalizeLinkText = normalizeLinkText;\n\n\n  // Expose utils & helpers for easy acces from plugins\n\n  /**\n   * MarkdownIt#utils -> utils\n   *\n   * Assorted utility functions, useful to write plugins. See details\n   * [here](https://github.com/markdown-it/markdown-it/blob/master/lib/common/utils.js).\n   **/\n  this.utils = utils;\n\n  /**\n   * MarkdownIt#helpers -> helpers\n   *\n   * Link components parser functions, useful to write plugins. See details\n   * [here](https://github.com/markdown-it/markdown-it/blob/master/lib/helpers).\n   **/\n  this.helpers = utils.assign({}, helpers);\n\n\n  this.options = {};\n  this.configure(presetName);\n\n  if (options) { this.set(options); }\n}\n\n\n/** chainable\n * MarkdownIt.set(options)\n *\n * Set parser options (in the same format as in constructor). Probably, you\n * will never need it, but you can change options after constructor call.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')()\n *             .set({ html: true, breaks: true })\n *             .set({ typographer, true });\n * ```\n *\n * __Note:__ To achieve the best possible performance, don't modify a\n * `markdown-it` instance options on the fly. If you need multiple configurations\n * it's best to create multiple instances and initialize each with separate\n * config.\n **/\nMarkdownIt.prototype.set = function (options) {\n  utils.assign(this.options, options);\n  return this;\n};\n\n\n/** chainable, internal\n * MarkdownIt.configure(presets)\n *\n * Batch load of all options and compenent settings. This is internal method,\n * and you probably will not need it. But if you will - see available presets\n * and data structure [here](https://github.com/markdown-it/markdown-it/tree/master/lib/presets)\n *\n * We strongly recommend to use presets instead of direct config loads. That\n * will give better compatibility with next versions.\n **/\nMarkdownIt.prototype.configure = function (presets) {\n  var self = this, presetName;\n\n  if (utils.isString(presets)) {\n    presetName = presets;\n    presets = config[presetName];\n    if (!presets) { throw new Error('Wrong `markdown-it` preset \"' + presetName + '\", check name'); }\n  }\n\n  if (!presets) { throw new Error('Wrong `markdown-it` preset, can\\'t be empty'); }\n\n  if (presets.options) { self.set(presets.options); }\n\n  if (presets.components) {\n    Object.keys(presets.components).forEach(function (name) {\n      if (presets.components[name].rules) {\n        self[name].ruler.enableOnly(presets.components[name].rules);\n      }\n      if (presets.components[name].rules2) {\n        self[name].ruler2.enableOnly(presets.components[name].rules2);\n      }\n    });\n  }\n  return this;\n};\n\n\n/** chainable\n * MarkdownIt.enable(list, ignoreInvalid)\n * - list (String|Array): rule name or list of rule names to enable\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * Enable list or rules. It will automatically find appropriate components,\n * containing rules with given names. If rule not found, and `ignoreInvalid`\n * not set - throws exception.\n *\n * ##### Example\n *\n * ```javascript\n * var md = require('markdown-it')()\n *             .enable(['sub', 'sup'])\n *             .disable('smartquotes');\n * ```\n **/\nMarkdownIt.prototype.enable = function (list, ignoreInvalid) {\n  var result = [];\n\n  if (!Array.isArray(list)) { list = [ list ]; }\n\n  [ 'core', 'block', 'inline' ].forEach(function (chain) {\n    result = result.concat(this[chain].ruler.enable(list, true));\n  }, this);\n\n  result = result.concat(this.inline.ruler2.enable(list, true));\n\n  var missed = list.filter(function (name) { return result.indexOf(name) < 0; });\n\n  if (missed.length && !ignoreInvalid) {\n    throw new Error('MarkdownIt. Failed to enable unknown rule(s): ' + missed);\n  }\n\n  return this;\n};\n\n\n/** chainable\n * MarkdownIt.disable(list, ignoreInvalid)\n * - list (String|Array): rule name or list of rule names to disable.\n * - ignoreInvalid (Boolean): set `true` to ignore errors when rule not found.\n *\n * The same as [[MarkdownIt.enable]], but turn specified rules off.\n **/\nMarkdownIt.prototype.disable = function (list, ignoreInvalid) {\n  var result = [];\n\n  if (!Array.isArray(list)) { list = [ list ]; }\n\n  [ 'core', 'block', 'inline' ].forEach(function (chain) {\n    result = result.concat(this[chain].ruler.disable(list, true));\n  }, this);\n\n  result = result.concat(this.inline.ruler2.disable(list, true));\n\n  var missed = list.filter(function (name) { return result.indexOf(name) < 0; });\n\n  if (missed.length && !ignoreInvalid) {\n    throw new Error('MarkdownIt. Failed to disable unknown rule(s): ' + missed);\n  }\n  return this;\n};\n\n\n/** chainable\n * MarkdownIt.use(plugin, params)\n *\n * Load specified plugin with given params into current parser instance.\n * It's just a sugar to call `plugin(md, params)` with curring.\n *\n * ##### Example\n *\n * ```javascript\n * var iterator = require('markdown-it-for-inline');\n * var md = require('markdown-it')()\n *             .use(iterator, 'foo_replace', 'text', function (tokens, idx) {\n *               tokens[idx].content = tokens[idx].content.replace(/foo/g, 'bar');\n *             });\n * ```\n **/\nMarkdownIt.prototype.use = function (plugin /*, params, ... */) {\n  var args = [ this ].concat(Array.prototype.slice.call(arguments, 1));\n  plugin.apply(plugin, args);\n  return this;\n};\n\n\n/** internal\n * MarkdownIt.parse(src, env) -> Array\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * Parse input string and return list of block tokens (special token type\n * \"inline\" will contain list of inline tokens). You should not call this\n * method directly, until you write custom renderer (for example, to produce\n * AST).\n *\n * `env` is used to pass data between \"distributed\" rules and return additional\n * metadata like reference info, needed for the renderer. It also can be used to\n * inject data in specific cases. Usually, you will be ok to pass `{}`,\n * and then pass updated object to renderer.\n **/\nMarkdownIt.prototype.parse = function (src, env) {\n  if (typeof src !== 'string') {\n    throw new Error('Input data should be a String');\n  }\n\n  var state = new this.core.State(src, this, env);\n\n  this.core.process(state);\n\n  return state.tokens;\n};\n\n\n/**\n * MarkdownIt.render(src [, env]) -> String\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * Render markdown string into html. It does all magic for you :).\n *\n * `env` can be used to inject additional metadata (`{}` by default).\n * But you will not need it with high probability. See also comment\n * in [[MarkdownIt.parse]].\n **/\nMarkdownIt.prototype.render = function (src, env) {\n  env = env || {};\n\n  return this.renderer.render(this.parse(src, env), this.options, env);\n};\n\n\n/** internal\n * MarkdownIt.parseInline(src, env) -> Array\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * The same as [[MarkdownIt.parse]] but skip all block rules. It returns the\n * block tokens list with the single `inline` element, containing parsed inline\n * tokens in `children` property. Also updates `env` object.\n **/\nMarkdownIt.prototype.parseInline = function (src, env) {\n  var state = new this.core.State(src, this, env);\n\n  state.inlineMode = true;\n  this.core.process(state);\n\n  return state.tokens;\n};\n\n\n/**\n * MarkdownIt.renderInline(src [, env]) -> String\n * - src (String): source string\n * - env (Object): environment sandbox\n *\n * Similar to [[MarkdownIt.render]] but for single paragraph content. Result\n * will NOT be wrapped into `<p>` tags.\n **/\nMarkdownIt.prototype.renderInline = function (src, env) {\n  env = env || {};\n\n  return this.renderer.render(this.parseInline(src, env), this.options, env);\n};\n\n\nmodule.exports = MarkdownIt;\n"], "mappings": "AAAA;;AAEA,YAAY;;AAGZ,IAAIA,KAAK,GAAUC,OAAO,CAAC,gBAAgB,CAAC;AAC5C,IAAIC,OAAO,GAAQD,OAAO,CAAC,WAAW,CAAC;AACvC,IAAIE,QAAQ,GAAOF,OAAO,CAAC,YAAY,CAAC;AACxC,IAAIG,UAAU,GAAKH,OAAO,CAAC,eAAe,CAAC;AAC3C,IAAII,WAAW,GAAIJ,OAAO,CAAC,gBAAgB,CAAC;AAC5C,IAAIK,YAAY,GAAGL,OAAO,CAAC,iBAAiB,CAAC;AAC7C,IAAIM,SAAS,GAAMN,OAAO,CAAC,YAAY,CAAC;AACxC,IAAIO,KAAK,GAAUP,OAAO,CAAC,OAAO,CAAC;AACnC,IAAIQ,QAAQ,GAAOR,OAAO,CAAC,UAAU,CAAC;AAGtC,IAAIS,MAAM,GAAG;EACXC,OAAO,EAAEV,OAAO,CAAC,mBAAmB,CAAC;EACrCW,IAAI,EAAEX,OAAO,CAAC,gBAAgB,CAAC;EAC/BY,UAAU,EAAEZ,OAAO,CAAC,sBAAsB;AAC5C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIa,YAAY,GAAG,mCAAmC;AACtD,IAAIC,YAAY,GAAG,mCAAmC;AAEtD,SAASC,YAAYA,CAACC,GAAG,EAAE;EACzB;EACA,IAAIC,GAAG,GAAGD,GAAG,CAACE,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAElC,OAAON,YAAY,CAACO,IAAI,CAACH,GAAG,CAAC,GAAIH,YAAY,CAACM,IAAI,CAACH,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK,GAAI,IAAI;AAChF;;AAEA;;AAGA,IAAII,mBAAmB,GAAG,CAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,CAAE;AAE1D,SAASC,aAAaA,CAACN,GAAG,EAAE;EAC1B,IAAIO,MAAM,GAAGhB,KAAK,CAACiB,KAAK,CAACR,GAAG,EAAE,IAAI,CAAC;EAEnC,IAAIO,MAAM,CAACE,QAAQ,EAAE;IACnB;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACF,MAAM,CAACG,QAAQ,IAAIL,mBAAmB,CAACM,OAAO,CAACJ,MAAM,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAE;MACzE,IAAI;QACFH,MAAM,CAACE,QAAQ,GAAGjB,QAAQ,CAACoB,OAAO,CAACL,MAAM,CAACE,QAAQ,CAAC;MACrD,CAAC,CAAC,OAAOI,EAAE,EAAE,CAAE;IACjB;EACF;EAEA,OAAOtB,KAAK,CAACuB,MAAM,CAACvB,KAAK,CAACwB,MAAM,CAACR,MAAM,CAAC,CAAC;AAC3C;AAEA,SAASS,iBAAiBA,CAAChB,GAAG,EAAE;EAC9B,IAAIO,MAAM,GAAGhB,KAAK,CAACiB,KAAK,CAACR,GAAG,EAAE,IAAI,CAAC;EAEnC,IAAIO,MAAM,CAACE,QAAQ,EAAE;IACnB;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACF,MAAM,CAACG,QAAQ,IAAIL,mBAAmB,CAACM,OAAO,CAACJ,MAAM,CAACG,QAAQ,CAAC,IAAI,CAAC,EAAE;MACzE,IAAI;QACFH,MAAM,CAACE,QAAQ,GAAGjB,QAAQ,CAACyB,SAAS,CAACV,MAAM,CAACE,QAAQ,CAAC;MACvD,CAAC,CAAC,OAAOI,EAAE,EAAE,CAAE;IACjB;EACF;;EAEA;EACA,OAAOtB,KAAK,CAAC2B,MAAM,CAAC3B,KAAK,CAACwB,MAAM,CAACR,MAAM,CAAC,EAAEhB,KAAK,CAAC2B,MAAM,CAACC,YAAY,GAAG,GAAG,CAAC;AAC5E;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,UAAUA,CAACC,UAAU,EAAEC,OAAO,EAAE;EACvC,IAAI,EAAE,IAAI,YAAYF,UAAU,CAAC,EAAE;IACjC,OAAO,IAAIA,UAAU,CAACC,UAAU,EAAEC,OAAO,CAAC;EAC5C;EAEA,IAAI,CAACA,OAAO,EAAE;IACZ,IAAI,CAACvC,KAAK,CAACwC,QAAQ,CAACF,UAAU,CAAC,EAAE;MAC/BC,OAAO,GAAGD,UAAU,IAAI,CAAC,CAAC;MAC1BA,UAAU,GAAG,SAAS;IACxB;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAI,CAACG,MAAM,GAAG,IAAInC,YAAY,CAAC,CAAC;;EAEhC;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAI,CAACoC,KAAK,GAAG,IAAIrC,WAAW,CAAC,CAAC;;EAE9B;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAI,CAACsC,IAAI,GAAG,IAAIvC,UAAU,CAAC,CAAC;;EAE5B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAI,CAACwC,QAAQ,GAAG,IAAIzC,QAAQ,CAAC,CAAC;;EAE9B;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAI,CAAC0C,OAAO,GAAG,IAAItC,SAAS,CAAC,CAAC;;EAE9B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAI,CAACS,YAAY,GAAGA,YAAY;;EAEhC;AACF;AACA;AACA;AACA;AACA;EACE,IAAI,CAACO,aAAa,GAAGA,aAAa;;EAElC;AACF;AACA;AACA;AACA;EACE,IAAI,CAACU,iBAAiB,GAAGA,iBAAiB;;EAG1C;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,IAAI,CAACjC,KAAK,GAAGA,KAAK;;EAElB;AACF;AACA;AACA;AACA;AACA;EACE,IAAI,CAACE,OAAO,GAAGF,KAAK,CAAC8C,MAAM,CAAC,CAAC,CAAC,EAAE5C,OAAO,CAAC;EAGxC,IAAI,CAACqC,OAAO,GAAG,CAAC,CAAC;EACjB,IAAI,CAACQ,SAAS,CAACT,UAAU,CAAC;EAE1B,IAAIC,OAAO,EAAE;IAAE,IAAI,CAACS,GAAG,CAACT,OAAO,CAAC;EAAE;AACpC;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAF,UAAU,CAACY,SAAS,CAACD,GAAG,GAAG,UAAUT,OAAO,EAAE;EAC5CvC,KAAK,CAAC8C,MAAM,CAAC,IAAI,CAACP,OAAO,EAAEA,OAAO,CAAC;EACnC,OAAO,IAAI;AACb,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAF,UAAU,CAACY,SAAS,CAACF,SAAS,GAAG,UAAUG,OAAO,EAAE;EAClD,IAAIC,IAAI,GAAG,IAAI;IAAEb,UAAU;EAE3B,IAAItC,KAAK,CAACwC,QAAQ,CAACU,OAAO,CAAC,EAAE;IAC3BZ,UAAU,GAAGY,OAAO;IACpBA,OAAO,GAAGxC,MAAM,CAAC4B,UAAU,CAAC;IAC5B,IAAI,CAACY,OAAO,EAAE;MAAE,MAAM,IAAIE,KAAK,CAAC,8BAA8B,GAAGd,UAAU,GAAG,eAAe,CAAC;IAAE;EAClG;EAEA,IAAI,CAACY,OAAO,EAAE;IAAE,MAAM,IAAIE,KAAK,CAAC,6CAA6C,CAAC;EAAE;EAEhF,IAAIF,OAAO,CAACX,OAAO,EAAE;IAAEY,IAAI,CAACH,GAAG,CAACE,OAAO,CAACX,OAAO,CAAC;EAAE;EAElD,IAAIW,OAAO,CAACG,UAAU,EAAE;IACtBC,MAAM,CAACC,IAAI,CAACL,OAAO,CAACG,UAAU,CAAC,CAACG,OAAO,CAAC,UAAUC,IAAI,EAAE;MACtD,IAAIP,OAAO,CAACG,UAAU,CAACI,IAAI,CAAC,CAACC,KAAK,EAAE;QAClCP,IAAI,CAACM,IAAI,CAAC,CAACE,KAAK,CAACC,UAAU,CAACV,OAAO,CAACG,UAAU,CAACI,IAAI,CAAC,CAACC,KAAK,CAAC;MAC7D;MACA,IAAIR,OAAO,CAACG,UAAU,CAACI,IAAI,CAAC,CAACI,MAAM,EAAE;QACnCV,IAAI,CAACM,IAAI,CAAC,CAACK,MAAM,CAACF,UAAU,CAACV,OAAO,CAACG,UAAU,CAACI,IAAI,CAAC,CAACI,MAAM,CAAC;MAC/D;IACF,CAAC,CAAC;EACJ;EACA,OAAO,IAAI;AACb,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAxB,UAAU,CAACY,SAAS,CAACc,MAAM,GAAG,UAAUC,IAAI,EAAEC,aAAa,EAAE;EAC3D,IAAIC,MAAM,GAAG,EAAE;EAEf,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,EAAE;IAAEA,IAAI,GAAG,CAAEA,IAAI,CAAE;EAAE;EAE7C,CAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAE,CAACR,OAAO,CAAC,UAAUa,KAAK,EAAE;IACrDH,MAAM,GAAGA,MAAM,CAACI,MAAM,CAAC,IAAI,CAACD,KAAK,CAAC,CAACV,KAAK,CAACI,MAAM,CAACC,IAAI,EAAE,IAAI,CAAC,CAAC;EAC9D,CAAC,EAAE,IAAI,CAAC;EAERE,MAAM,GAAGA,MAAM,CAACI,MAAM,CAAC,IAAI,CAAC7B,MAAM,CAACqB,MAAM,CAACC,MAAM,CAACC,IAAI,EAAE,IAAI,CAAC,CAAC;EAE7D,IAAIO,MAAM,GAAGP,IAAI,CAACQ,MAAM,CAAC,UAAUf,IAAI,EAAE;IAAE,OAAOS,MAAM,CAACtC,OAAO,CAAC6B,IAAI,CAAC,GAAG,CAAC;EAAE,CAAC,CAAC;EAE9E,IAAIc,MAAM,CAACE,MAAM,IAAI,CAACR,aAAa,EAAE;IACnC,MAAM,IAAIb,KAAK,CAAC,gDAAgD,GAAGmB,MAAM,CAAC;EAC5E;EAEA,OAAO,IAAI;AACb,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACAlC,UAAU,CAACY,SAAS,CAACyB,OAAO,GAAG,UAAUV,IAAI,EAAEC,aAAa,EAAE;EAC5D,IAAIC,MAAM,GAAG,EAAE;EAEf,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,EAAE;IAAEA,IAAI,GAAG,CAAEA,IAAI,CAAE;EAAE;EAE7C,CAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAE,CAACR,OAAO,CAAC,UAAUa,KAAK,EAAE;IACrDH,MAAM,GAAGA,MAAM,CAACI,MAAM,CAAC,IAAI,CAACD,KAAK,CAAC,CAACV,KAAK,CAACe,OAAO,CAACV,IAAI,EAAE,IAAI,CAAC,CAAC;EAC/D,CAAC,EAAE,IAAI,CAAC;EAERE,MAAM,GAAGA,MAAM,CAACI,MAAM,CAAC,IAAI,CAAC7B,MAAM,CAACqB,MAAM,CAACY,OAAO,CAACV,IAAI,EAAE,IAAI,CAAC,CAAC;EAE9D,IAAIO,MAAM,GAAGP,IAAI,CAACQ,MAAM,CAAC,UAAUf,IAAI,EAAE;IAAE,OAAOS,MAAM,CAACtC,OAAO,CAAC6B,IAAI,CAAC,GAAG,CAAC;EAAE,CAAC,CAAC;EAE9E,IAAIc,MAAM,CAACE,MAAM,IAAI,CAACR,aAAa,EAAE;IACnC,MAAM,IAAIb,KAAK,CAAC,iDAAiD,GAAGmB,MAAM,CAAC;EAC7E;EACA,OAAO,IAAI;AACb,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAlC,UAAU,CAACY,SAAS,CAAC0B,GAAG,GAAG,UAAUC,MAAM,CAAC,oBAAoB;EAC9D,IAAIC,IAAI,GAAG,CAAE,IAAI,CAAE,CAACP,MAAM,CAACH,KAAK,CAAClB,SAAS,CAAC6B,KAAK,CAACC,IAAI,CAACC,SAAS,EAAE,CAAC,CAAC,CAAC;EACpEJ,MAAM,CAACK,KAAK,CAACL,MAAM,EAAEC,IAAI,CAAC;EAC1B,OAAO,IAAI;AACb,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAxC,UAAU,CAACY,SAAS,CAACxB,KAAK,GAAG,UAAUyD,GAAG,EAAEC,GAAG,EAAE;EAC/C,IAAI,OAAOD,GAAG,KAAK,QAAQ,EAAE;IAC3B,MAAM,IAAI9B,KAAK,CAAC,+BAA+B,CAAC;EAClD;EAEA,IAAIgC,KAAK,GAAG,IAAI,IAAI,CAACzC,IAAI,CAAC0C,KAAK,CAACH,GAAG,EAAE,IAAI,EAAEC,GAAG,CAAC;EAE/C,IAAI,CAACxC,IAAI,CAAC2C,OAAO,CAACF,KAAK,CAAC;EAExB,OAAOA,KAAK,CAACG,MAAM;AACrB,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAlD,UAAU,CAACY,SAAS,CAACuC,MAAM,GAAG,UAAUN,GAAG,EAAEC,GAAG,EAAE;EAChDA,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;EAEf,OAAO,IAAI,CAACvC,QAAQ,CAAC4C,MAAM,CAAC,IAAI,CAAC/D,KAAK,CAACyD,GAAG,EAAEC,GAAG,CAAC,EAAE,IAAI,CAAC5C,OAAO,EAAE4C,GAAG,CAAC;AACtE,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA9C,UAAU,CAACY,SAAS,CAACwC,WAAW,GAAG,UAAUP,GAAG,EAAEC,GAAG,EAAE;EACrD,IAAIC,KAAK,GAAG,IAAI,IAAI,CAACzC,IAAI,CAAC0C,KAAK,CAACH,GAAG,EAAE,IAAI,EAAEC,GAAG,CAAC;EAE/CC,KAAK,CAACM,UAAU,GAAG,IAAI;EACvB,IAAI,CAAC/C,IAAI,CAAC2C,OAAO,CAACF,KAAK,CAAC;EAExB,OAAOA,KAAK,CAACG,MAAM;AACrB,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAlD,UAAU,CAACY,SAAS,CAAC0C,YAAY,GAAG,UAAUT,GAAG,EAAEC,GAAG,EAAE;EACtDA,GAAG,GAAGA,GAAG,IAAI,CAAC,CAAC;EAEf,OAAO,IAAI,CAACvC,QAAQ,CAAC4C,MAAM,CAAC,IAAI,CAACC,WAAW,CAACP,GAAG,EAAEC,GAAG,CAAC,EAAE,IAAI,CAAC5C,OAAO,EAAE4C,GAAG,CAAC;AAC5E,CAAC;AAGDS,MAAM,CAACC,OAAO,GAAGxD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}