{"ast": null, "code": "\"use strict\";\n\nvar DataReader = require(\"./dataReader.js\");\nvar utils = require(\"./utils.js\");\nfunction StringReader(data, optimizedBinaryString) {\n  this.data = data;\n  if (!optimizedBinaryString) {\n    this.data = utils.string2binary(this.data);\n  }\n  this.length = this.data.length;\n  this.index = 0;\n  this.zero = 0;\n}\nStringReader.prototype = new DataReader();\n/**\n * @see DataReader.byteAt\n */\nStringReader.prototype.byteAt = function (i) {\n  return this.data.charCodeAt(this.zero + i);\n};\n/**\n * @see DataReader.lastIndexOfSignature\n */\nStringReader.prototype.lastIndexOfSignature = function (sig) {\n  return this.data.lastIndexOf(sig) - this.zero;\n};\n/**\n * @see DataReader.readData\n */\nStringReader.prototype.readData = function (size) {\n  this.checkOffset(size);\n  // this will work because the constructor applied the \"& 0xff\" mask.\n  var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n  this.index += size;\n  return result;\n};\nmodule.exports = StringReader;", "map": {"version": 3, "names": ["DataReader", "require", "utils", "StringReader", "data", "optimizedBinaryString", "string2binary", "length", "index", "zero", "prototype", "byteAt", "i", "charCodeAt", "lastIndexOfSignature", "sig", "lastIndexOf", "readData", "size", "checkOffset", "result", "slice", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/pizzip@3.1.7/node_modules/pizzip/js/stringReader.js"], "sourcesContent": ["\"use strict\";\n\nvar DataReader = require(\"./dataReader.js\");\nvar utils = require(\"./utils.js\");\nfunction StringReader(data, optimizedBinaryString) {\n  this.data = data;\n  if (!optimizedBinaryString) {\n    this.data = utils.string2binary(this.data);\n  }\n  this.length = this.data.length;\n  this.index = 0;\n  this.zero = 0;\n}\nStringReader.prototype = new DataReader();\n/**\n * @see DataReader.byteAt\n */\nStringReader.prototype.byteAt = function (i) {\n  return this.data.charCodeAt(this.zero + i);\n};\n/**\n * @see DataReader.lastIndexOfSignature\n */\nStringReader.prototype.lastIndexOfSignature = function (sig) {\n  return this.data.lastIndexOf(sig) - this.zero;\n};\n/**\n * @see DataReader.readData\n */\nStringReader.prototype.readData = function (size) {\n  this.checkOffset(size);\n  // this will work because the constructor applied the \"& 0xff\" mask.\n  var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n  this.index += size;\n  return result;\n};\nmodule.exports = StringReader;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,UAAU,GAAGC,OAAO,CAAC,iBAAiB,CAAC;AAC3C,IAAIC,KAAK,GAAGD,OAAO,CAAC,YAAY,CAAC;AACjC,SAASE,YAAYA,CAACC,IAAI,EAAEC,qBAAqB,EAAE;EACjD,IAAI,CAACD,IAAI,GAAGA,IAAI;EAChB,IAAI,CAACC,qBAAqB,EAAE;IAC1B,IAAI,CAACD,IAAI,GAAGF,KAAK,CAACI,aAAa,CAAC,IAAI,CAACF,IAAI,CAAC;EAC5C;EACA,IAAI,CAACG,MAAM,GAAG,IAAI,CAACH,IAAI,CAACG,MAAM;EAC9B,IAAI,CAACC,KAAK,GAAG,CAAC;EACd,IAAI,CAACC,IAAI,GAAG,CAAC;AACf;AACAN,YAAY,CAACO,SAAS,GAAG,IAAIV,UAAU,CAAC,CAAC;AACzC;AACA;AACA;AACAG,YAAY,CAACO,SAAS,CAACC,MAAM,GAAG,UAAUC,CAAC,EAAE;EAC3C,OAAO,IAAI,CAACR,IAAI,CAACS,UAAU,CAAC,IAAI,CAACJ,IAAI,GAAGG,CAAC,CAAC;AAC5C,CAAC;AACD;AACA;AACA;AACAT,YAAY,CAACO,SAAS,CAACI,oBAAoB,GAAG,UAAUC,GAAG,EAAE;EAC3D,OAAO,IAAI,CAACX,IAAI,CAACY,WAAW,CAACD,GAAG,CAAC,GAAG,IAAI,CAACN,IAAI;AAC/C,CAAC;AACD;AACA;AACA;AACAN,YAAY,CAACO,SAAS,CAACO,QAAQ,GAAG,UAAUC,IAAI,EAAE;EAChD,IAAI,CAACC,WAAW,CAACD,IAAI,CAAC;EACtB;EACA,IAAIE,MAAM,GAAG,IAAI,CAAChB,IAAI,CAACiB,KAAK,CAAC,IAAI,CAACZ,IAAI,GAAG,IAAI,CAACD,KAAK,EAAE,IAAI,CAACC,IAAI,GAAG,IAAI,CAACD,KAAK,GAAGU,IAAI,CAAC;EACnF,IAAI,CAACV,KAAK,IAAIU,IAAI;EAClB,OAAOE,MAAM;AACf,CAAC;AACDE,MAAM,CAACC,OAAO,GAAGpB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}