{"ast": null, "code": "import { defineAsyncComponent } from 'vue';\nimport { use<PERSON>out<PERSON>, useRouter } from 'vue-router';\nimport { qiankun, LayoutView, ChatMethod, AiChatMethod, refreshIcon, loginHintMethod } from '../LayoutView/LayoutView.js';\nimport { systemLogo, systemName, layoutBg, whetherAiChat, systemNameAreaPrefix } from 'common/js/system_var.js';\nimport { ArrowRight } from '@element-plus/icons-vue';\nvar __default__ = {\n  name: 'LayoutViewOne'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var HelpDocument = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/HelpDocument');\n    });\n    var EditPassWord = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/EditPassWord');\n    });\n    var LayoutBoxMessage = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/LayoutBoxMessage');\n    });\n    var LayoutPersonalDoList = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/LayoutPersonalDoList');\n    });\n    var GlobalRegionSelect = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/GlobalRegionSelect');\n    });\n    var GlobalChatFloating = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/GlobalChatFloating');\n    });\n    var GlobalFloatingWindow = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/GlobalFloatingWindow');\n    });\n    var GlobalAiControls = defineAsyncComponent(function () {\n      return import('../LayoutContainer/components/GlobalAiControls');\n    });\n    var GlobalAiChat = defineAsyncComponent(function () {\n      return import('../GlobalAiChat/GlobalAiChat');\n    });\n    var LayoutViewOneWorkBench = defineAsyncComponent(function () {\n      return import('./component/LayoutViewOneWorkBench.vue');\n    });\n    var qusetionAnswering = defineAsyncComponent(function () {\n      return import('./component/question-answering.vue');\n    });\n    var suggestPop = defineAsyncComponent(function () {\n      return import('../LayoutView/component/suggestPop');\n    });\n    var SubAppViewport = {\n      name: 'SubAppViewport',\n      props: ['name'],\n      template: `<div :id=\"name\" class=\"subApp-viewport\"></div>`\n    };\n    var _qiankun = qiankun(useRoute()),\n      isMain = _qiankun.isMain;\n    var _LayoutView = LayoutView(useRoute(), useRouter()),\n      user = _LayoutView.user,\n      area = _LayoutView.area,\n      role = _LayoutView.role,\n      LayoutViewBox = _LayoutView.LayoutViewBox,\n      LayoutViewInfo = _LayoutView.LayoutViewInfo,\n      helpShow = _LayoutView.helpShow,\n      handleCommand = _LayoutView.handleCommand,\n      editPassWordShow = _LayoutView.editPassWordShow,\n      verifyEditPassWord = _LayoutView.verifyEditPassWord,\n      verifyEditPassWordShow = _LayoutView.verifyEditPassWordShow,\n      editPassWordCallback = _LayoutView.editPassWordCallback,\n      regionId = _LayoutView.regionId,\n      regionName = _LayoutView.regionName,\n      regionSelect = _LayoutView.regionSelect,\n      isRegionSelectShow = _LayoutView.isRegionSelectShow,\n      isView = _LayoutView.isView,\n      tabMenu = _LayoutView.tabMenu,\n      tabMenuData = _LayoutView.tabMenuData,\n      handleClick = _LayoutView.handleClick,\n      menuId = _LayoutView.menuId,\n      menuData = _LayoutView.menuData,\n      menuClick = _LayoutView.menuClick,\n      handleBreadcrumb = _LayoutView.handleBreadcrumb,\n      WorkBenchReturn = _LayoutView.WorkBenchReturn,\n      isRefresh = _LayoutView.isRefresh,\n      keepAliveRoute = _LayoutView.keepAliveRoute,\n      tabData = _LayoutView.tabData,\n      tabClick = _LayoutView.tabClick,\n      handleRefresh = _LayoutView.handleRefresh,\n      handleClose = _LayoutView.handleClose,\n      handleCloseOther = _LayoutView.handleCloseOther,\n      isMicroApp = _LayoutView.isMicroApp,\n      MicroApp = _LayoutView.MicroApp;\n    var _ChatMethod = ChatMethod(),\n      rongCloudToken = _ChatMethod.rongCloudToken;\n    var _loginHintMethod = loginHintMethod(),\n      loginHintShow = _loginHintMethod.loginHintShow;\n    var _AiChatMethod = AiChatMethod(),\n      AiChatTargetWidth = _AiChatMethod.AiChatTargetWidth,\n      AiChatViewType = _AiChatMethod.AiChatViewType,\n      AiChatWindowShow = _AiChatMethod.AiChatWindowShow;\n    var __returned__ = {\n      HelpDocument,\n      EditPassWord,\n      LayoutBoxMessage,\n      LayoutPersonalDoList,\n      GlobalRegionSelect,\n      GlobalChatFloating,\n      GlobalFloatingWindow,\n      GlobalAiControls,\n      GlobalAiChat,\n      LayoutViewOneWorkBench,\n      qusetionAnswering,\n      suggestPop,\n      SubAppViewport,\n      isMain,\n      user,\n      area,\n      role,\n      LayoutViewBox,\n      LayoutViewInfo,\n      helpShow,\n      handleCommand,\n      editPassWordShow,\n      verifyEditPassWord,\n      verifyEditPassWordShow,\n      editPassWordCallback,\n      regionId,\n      regionName,\n      regionSelect,\n      isRegionSelectShow,\n      isView,\n      tabMenu,\n      tabMenuData,\n      handleClick,\n      menuId,\n      menuData,\n      menuClick,\n      handleBreadcrumb,\n      WorkBenchReturn,\n      isRefresh,\n      keepAliveRoute,\n      tabData,\n      tabClick,\n      handleRefresh,\n      handleClose,\n      handleCloseOther,\n      isMicroApp,\n      MicroApp,\n      rongCloudToken,\n      loginHintShow,\n      AiChatTargetWidth,\n      AiChatViewType,\n      AiChatWindowShow,\n      defineAsyncComponent,\n      get useRoute() {\n        return useRoute;\n      },\n      get useRouter() {\n        return useRouter;\n      },\n      get qiankun() {\n        return qiankun;\n      },\n      get LayoutView() {\n        return LayoutView;\n      },\n      get ChatMethod() {\n        return ChatMethod;\n      },\n      get AiChatMethod() {\n        return AiChatMethod;\n      },\n      get refreshIcon() {\n        return refreshIcon;\n      },\n      get loginHintMethod() {\n        return loginHintMethod;\n      },\n      get systemLogo() {\n        return systemLogo;\n      },\n      get systemName() {\n        return systemName;\n      },\n      get layoutBg() {\n        return layoutBg;\n      },\n      get whetherAiChat() {\n        return whetherAiChat;\n      },\n      get systemNameAreaPrefix() {\n        return systemNameAreaPrefix;\n      },\n      get ArrowRight() {\n        return ArrowRight;\n      }\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["defineAsyncComponent", "useRoute", "useRouter", "qiankun", "LayoutView", "ChatMethod", "AiChatMethod", "refreshIcon", "loginHintMethod", "systemLogo", "systemName", "layoutBg", "whetherAiChat", "systemNameAreaPrefix", "ArrowRight", "__default__", "name", "HelpDocument", "EditPassWord", "LayoutBoxMessage", "LayoutPersonalDoList", "GlobalRegionSelect", "GlobalChatFloating", "GlobalFloatingWindow", "GlobalAiControls", "GlobalAiChat", "LayoutViewOneWorkBench", "qusetionAnswering", "suggestPop", "SubAppViewport", "props", "template", "_qiankun", "is<PERSON><PERSON>", "_LayoutView", "user", "area", "role", "LayoutViewBox", "LayoutViewInfo", "helpShow", "handleCommand", "editPassWordShow", "verifyEditPassWord", "verifyEditPassWordShow", "editPassWordCallback", "regionId", "regionName", "regionSelect", "isRegionSelectShow", "<PERSON><PERSON><PERSON><PERSON>", "tabMenu", "tabMenuData", "handleClick", "menuId", "menuData", "menuClick", "handleBreadcrumb", "WorkBenchReturn", "isRefresh", "keepAliveRoute", "tabData", "tabClick", "handleRefresh", "handleClose", "handleCloseOther", "isMicroApp", "MicroApp", "_ChatMethod", "rongCloudToken", "_loginHintMethod", "loginHintShow", "_AiChatMethod", "AiChatTargetWidth", "AiChatViewType", "AiChatWindowShow"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/LayoutViewOne/LayoutViewOne.vue"], "sourcesContent": ["<template>\r\n  <el-container class=\"LayoutViewOne\" :style=\"`background: url('${layoutBg}') no-repeat;background-size: 100% 100%;`\">\r\n    <el-header class=\"LayoutViewOneHeader\">\r\n      <div class=\"LayoutViewOneBox\" ref=\"LayoutViewBox\" @click=\"WorkBenchReturn\">\r\n        <div class=\"LayoutViewOneLogo\">\r\n          <el-image :src=\"systemLogo\" fit=\"cover\" />\r\n        </div>\r\n        <div class=\"LayoutViewOneName\">{{ systemNameAreaPrefix === 'true' ? regionName : '' }}{{ systemName }}</div>\r\n      </div>\r\n      <div class=\"LayoutViewOneHeaderMenu\">\r\n        <el-tabs v-model=\"tabMenu\" @tab-change=\"handleClick\">\r\n          <el-tab-pane v-for=\"item in tabMenuData\" :key=\"item.id\" :name=\"item.id\" :label=\"item.name\">\r\n            <template #label>\r\n              <div class=\"LayoutViewOneHeaderMenuItem\" v-if=\"item.routePath !== '/WorkBench'\">{{ item.name }}</div>\r\n              <el-popover trigger=\"hover\" popper-class=\"LayoutViewOneWorkBenchPopover\" transition=\"zy-el-zoom-in-top\"\r\n                v-if=\"item.routePath === '/WorkBench'\">\r\n                <template #reference>\r\n                  <div class=\"LayoutViewOneHeaderMenuItem\">{{ item.name }}</div>\r\n                </template>\r\n                <LayoutViewOneWorkBench :id=\"item.id\" :data=\"item.children\"></LayoutViewOneWorkBench>\r\n              </el-popover>\r\n            </template>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n      <div class=\"LayoutViewOneInfo\" ref=\"LayoutViewInfo\">\r\n        <xyl-region v-model=\"regionId\" :data=\"area\" @select=\"regionSelect\"\r\n          :props=\"{ label: 'name', children: 'children' }\"></xyl-region>\r\n        <el-tooltip placement=\"top\" effect=\"light\" :offset=\"6\" :disabled=\"!role.length\">\r\n          <template #content>\r\n            <div class=\"LayoutViewOneRoleItem\" v-for=\"(item, index) in role\" :key=\"index\">{{ item }}</div>\r\n          </template>\r\n          <div class=\"LayoutViewOneUser\">\r\n            <el-image :src=\"user.image\" fit=\"cover\" />\r\n            <span class=\"forbidSelect\">{{ user.userName }}</span>\r\n          </div>\r\n        </el-tooltip>\r\n        <LayoutPersonalDoList>待办</LayoutPersonalDoList>\r\n        <LayoutBoxMessage>消息</LayoutBoxMessage>\r\n        <div class=\"LayoutViewOneRefresh\" v-html=\"refreshIcon\" @click=\"handleCommand('refresh')\" title=\"重新加载平台\"></div>\r\n        <el-dropdown @command=\"handleCommand\">\r\n          <div class=\"LayoutOperation\"></div>\r\n          <template #dropdown>\r\n            <el-dropdown-menu>\r\n              <el-dropdown-item command=\"task\">系统任务管理器</el-dropdown-item>\r\n              <!-- <el-dropdown-item command=\"refresh\">重新加载平台</el-dropdown-item> -->\r\n              <!-- <el-dropdown-item command=\"locale\">简繁切换</el-dropdown-item>\r\n              <el-dropdown-item command=\"help\">帮助文档</el-dropdown-item> -->\r\n              <el-dropdown-item command=\"edit_password\">修改密码</el-dropdown-item>\r\n              <el-dropdown-item command=\"exit\">安全退出</el-dropdown-item>\r\n            </el-dropdown-menu>\r\n          </template>\r\n        </el-dropdown>\r\n      </div>\r\n    </el-header>\r\n    <el-container class=\"LayoutViewOneContainer\">\r\n      <el-aside class=\"LayoutViewOneAside\" v-show=\"isView\">\r\n        <xyl-menu v-model=\"menuId\" :menuData=\"menuData\" @select=\"menuClick\"></xyl-menu>\r\n      </el-aside>\r\n      <el-main class=\"LayoutViewOneMain\"\r\n        :class=\"{ LayoutViewOneMainView: !isView, LayoutViewOneMainBreadcrumb: !isView && tabData.length > 1 }\">\r\n        <xyl-tab v-model=\"menuId\" @tab-click=\"tabClick\" @refresh=\"handleRefresh\" @close=\"handleClose\"\r\n          @closeOther=\"handleCloseOther\" v-show=\"isView\">\r\n          <xyl-tab-item v-for=\"item in tabData\" :key=\"item.id\" :value=\"item.id\">{{ item.name }}</xyl-tab-item>\r\n        </xyl-tab>\r\n        <div class=\"LayoutViewOneBreadcrumb\" v-if=\"!isView && tabData.length > 1\">\r\n          <el-breadcrumb :separator-icon=\"ArrowRight\">\r\n            <el-breadcrumb-item v-for=\"(item, index) in tabData\" :key=\"`key-${item.id}`\"\r\n              @click=\"handleBreadcrumb(item, index)\">\r\n              {{ item.name }}\r\n            </el-breadcrumb-item>\r\n          </el-breadcrumb>\r\n        </div>\r\n        <div class=\"LayoutViewOneBody\">\r\n          <router-view v-slot=\"{ Component }\">\r\n            <keep-alive :include=\"keepAliveRoute\">\r\n              <component v-if=\"isMain && isRefresh\" :key=\"$route.fullPath\" :is=\"Component\"></component>\r\n            </keep-alive>\r\n          </router-view>\r\n          <SubAppViewport v-for=\"item in MicroApp\" :key=\"item\" v-show=\"!isMain && isMicroApp === item\" :name=\"item\">\r\n          </SubAppViewport>\r\n        </div>\r\n      </el-main>\r\n      <el-aside class=\"LayoutViewOneFloatingWindow\" v-if=\"whetherAiChat\">\r\n        <transition name=\"width-animation\">\r\n          <div class=\"LayoutViewOneFloatingWindowBody\" :style=\"{ '--ai-chat-target-width': AiChatTargetWidth }\"\r\n            v-if=\"AiChatViewType\" v-show=\"AiChatWindowShow\">\r\n            <GlobalAiChat v-model=\"AiChatWindowShow\"></GlobalAiChat>\r\n          </div>\r\n        </transition>\r\n      </el-aside>\r\n    </el-container>\r\n    <xyl-popup-window v-model=\"helpShow\" name=\"帮助文档\">\r\n      <HelpDocument></HelpDocument>\r\n    </xyl-popup-window>\r\n    <xyl-popup-window v-model=\"editPassWordShow\" name=\"修改密码\">\r\n      <EditPassWord :type=\"verifyEditPassWord\" @callback=\"editPassWordCallback\"></EditPassWord>\r\n    </xyl-popup-window>\r\n    <div class=\"ConstraintEditPassWord\" v-if=\"verifyEditPassWordShow\">\r\n      <EditPassWord :type=\"verifyEditPassWord\" @callback=\"editPassWordCallback\"></EditPassWord>\r\n    </div>\r\n    <GlobalRegionSelect v-if=\"isRegionSelectShow\" @callback=\"regionSelect\"></GlobalRegionSelect>\r\n  </el-container>\r\n  <qusetionAnswering></qusetionAnswering>\r\n  <GlobalChatFloating v-if=\"rongCloudToken\"></GlobalChatFloating>\r\n  <GlobalFloatingWindow v-model=\"AiChatWindowShow\" :disabled=\"AiChatViewType\" v-if=\"whetherAiChat\" />\r\n  <GlobalAiControls v-if=\"whetherAiChat\" />\r\n  <suggestPop v-if=\"isMain && loginHintShow\" />\r\n</template>\r\n<script>\r\nexport default { name: 'LayoutViewOne' }\r\n</script>\r\n<script setup>\r\nimport { defineAsyncComponent } from 'vue'\r\nimport { useRoute, useRouter } from 'vue-router'\r\nimport {\r\n  qiankun,\r\n  LayoutView,\r\n  ChatMethod,\r\n  AiChatMethod,\r\n  refreshIcon,\r\n  loginHintMethod\r\n} from '../LayoutView/LayoutView.js'\r\nimport { systemLogo, systemName, layoutBg, whetherAiChat, systemNameAreaPrefix } from 'common/js/system_var.js'\r\nimport { ArrowRight } from '@element-plus/icons-vue'\r\nconst HelpDocument = defineAsyncComponent(() => import('../LayoutContainer/components/HelpDocument'))\r\nconst EditPassWord = defineAsyncComponent(() => import('../LayoutContainer/components/EditPassWord'))\r\nconst LayoutBoxMessage = defineAsyncComponent(() => import('../LayoutContainer/components/LayoutBoxMessage'))\r\nconst LayoutPersonalDoList = defineAsyncComponent(() => import('../LayoutContainer/components/LayoutPersonalDoList'))\r\nconst GlobalRegionSelect = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalRegionSelect'))\r\nconst GlobalChatFloating = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalChatFloating'))\r\nconst GlobalFloatingWindow = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalFloatingWindow'))\r\nconst GlobalAiControls = defineAsyncComponent(() => import('../LayoutContainer/components/GlobalAiControls'))\r\nconst GlobalAiChat = defineAsyncComponent(() => import('../GlobalAiChat/GlobalAiChat'))\r\nconst LayoutViewOneWorkBench = defineAsyncComponent(() => import('./component/LayoutViewOneWorkBench.vue'))\r\nconst qusetionAnswering = defineAsyncComponent(() => import('./component/question-answering.vue'))\r\nconst suggestPop = defineAsyncComponent(() => import('../LayoutView/component/suggestPop'))\r\nconst SubAppViewport = {\r\n  name: 'SubAppViewport',\r\n  props: ['name'],\r\n  template: `<div :id=\"name\" class=\"subApp-viewport\"></div>`\r\n}\r\nconst { isMain } = qiankun(useRoute())\r\nconst {\r\n  user,\r\n  area,\r\n  role,\r\n  LayoutViewBox,\r\n  LayoutViewInfo,\r\n  helpShow,\r\n  handleCommand,\r\n  editPassWordShow,\r\n  verifyEditPassWord,\r\n  verifyEditPassWordShow,\r\n  editPassWordCallback,\r\n  regionId,\r\n  regionName,\r\n  regionSelect,\r\n  isRegionSelectShow,\r\n  isView,\r\n  tabMenu,\r\n  tabMenuData,\r\n  handleClick,\r\n  menuId,\r\n  menuData,\r\n  menuClick,\r\n  handleBreadcrumb,\r\n  WorkBenchReturn,\r\n  isRefresh,\r\n  keepAliveRoute,\r\n  tabData,\r\n  tabClick,\r\n  handleRefresh,\r\n  handleClose,\r\n  handleCloseOther,\r\n  isMicroApp,\r\n  MicroApp\r\n} = LayoutView(useRoute(), useRouter())\r\n\r\nconst { rongCloudToken } = ChatMethod()\r\nconst { loginHintShow } = loginHintMethod()\r\nconst { AiChatTargetWidth, AiChatViewType, AiChatWindowShow } = AiChatMethod()\r\n</script>\r\n<style lang=\"scss\">\r\n.LayoutViewOne {\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .LayoutViewOneHeader {\r\n    width: 100%;\r\n    height: 68px;\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .LayoutViewOneBox {\r\n      height: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 0 40px 0 20px;\r\n      cursor: pointer;\r\n\r\n      .LayoutViewOneLogo {\r\n        width: 52px;\r\n\r\n        .zy-el-image {\r\n          width: 100%;\r\n          display: block;\r\n        }\r\n      }\r\n\r\n      .LayoutViewOneName {\r\n        font-size: var(--zy-system-font-size);\r\n        line-height: var(--zy-line-height);\r\n        font-weight: bold;\r\n        color: var(--zy-el-color-primary);\r\n        padding-left: 12px;\r\n      }\r\n    }\r\n\r\n    .LayoutViewOneHeaderMenu {\r\n      flex: 1;\r\n      height: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      overflow: hidden;\r\n\r\n      .zy-el-tabs {\r\n        width: 100%;\r\n\r\n        .zy-el-tabs__header {\r\n          margin: 0;\r\n\r\n          .zy-el-tabs__nav-next,\r\n          .zy-el-tabs__nav-prev {\r\n            width: 26px;\r\n            font-size: calc(var(--zy-navigation-font-size) + 4px);\r\n            line-height: 48px;\r\n\r\n            .zy-el-icon {\r\n              color: var(--zy-el-text-color-primary);\r\n            }\r\n          }\r\n\r\n          .zy-el-tabs__nav-wrap {\r\n            padding: 0 calc(var(--zy-navigation-font-size) + 8px);\r\n\r\n            &::after {\r\n              background-color: transparent;\r\n            }\r\n\r\n            .zy-el-tabs__item {\r\n              height: 43px;\r\n              line-height: 43px;\r\n              font-weight: normal;\r\n              font-size: var(--zy-navigation-font-size);\r\n            }\r\n\r\n            .is-active {\r\n              font-weight: bold;\r\n            }\r\n\r\n            .zy-el-tabs__active-bar {\r\n              height: 3px;\r\n            }\r\n          }\r\n        }\r\n\r\n        .zy-el-tabs__content {\r\n          display: none;\r\n        }\r\n      }\r\n    }\r\n\r\n    .isLayoutViewHeaderMenu {\r\n      .zy-el-tabs {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    .LayoutViewOneInfo {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .xyl-region {\r\n        padding-left: 12px;\r\n        background: rgba(0, 0, 0, 0.1);\r\n        border-radius: calc(var(--zy-height-routine) / 2);\r\n        border: 1px solid var(--zy-el-border-color-lighter);\r\n        margin-left: 20px;\r\n\r\n        .xyl-region-view {\r\n          height: var(--zy-height-routine);\r\n\r\n          .xyl-region-img {\r\n            width: 20px;\r\n            height: 20px;\r\n          }\r\n\r\n          .xyl-region-name {\r\n            font-size: var(--zy-text-font-size);\r\n          }\r\n\r\n          .xyl-region-icon {\r\n            width: calc(var(--zy-text-font-size) + 6px);\r\n            font-size: var(--zy-text-font-size);\r\n          }\r\n        }\r\n      }\r\n\r\n      .LayoutViewOneUser {\r\n        display: flex;\r\n        align-items: center;\r\n        cursor: pointer;\r\n        height: var(--zy-height-routine);\r\n        background: rgba(0, 0, 0, 0.1);\r\n        border-radius: calc(var(--zy-height-routine) / 2);\r\n        border: 1px solid var(--zy-el-border-color-lighter);\r\n        margin-left: 10px;\r\n\r\n        .zy-el-image {\r\n          width: calc(var(--zy-height-routine) - 2px);\r\n          height: calc(var(--zy-height-routine) - 2px);\r\n          border-radius: 50%;\r\n          overflow: hidden;\r\n        }\r\n\r\n        span {\r\n          color: #fff;\r\n          margin-left: 6px;\r\n          padding-right: 12px;\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n        }\r\n      }\r\n\r\n      .zy-el-badge {\r\n        height: var(--zy-height-routine);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        background: rgba(0, 0, 0, 0.1);\r\n        border-radius: calc(var(--zy-height-routine) / 2);\r\n        border: 1px solid var(--zy-el-border-color-lighter);\r\n        margin-left: 10px;\r\n\r\n        &+.zy-el-badge {\r\n          margin-left: 20px;\r\n        }\r\n\r\n        .LayoutPersonalDoList,\r\n        .LayoutBoxMessage {\r\n          width: auto;\r\n          height: auto;\r\n          padding: 0 12px 0 36px;\r\n          background-size: 20px 20px;\r\n          background-position: 12px center;\r\n          font-size: var(--zy-text-font-size);\r\n          line-height: var(--zy-line-height);\r\n          color: #fff;\r\n        }\r\n      }\r\n\r\n      .zy-el-dropdown {\r\n        width: var(--zy-height-routine);\r\n        height: var(--zy-height-routine);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        background: rgba(0, 0, 0, 0.1);\r\n        border-radius: calc(var(--zy-height-routine) / 2);\r\n        border: 1px solid var(--zy-el-border-color-lighter);\r\n        margin-left: 10px;\r\n      }\r\n\r\n      .LayoutViewOneRefresh {\r\n        width: var(--zy-height-routine);\r\n        height: var(--zy-height-routine);\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        background: rgba(0, 0, 0, 0.1);\r\n        border-radius: calc(var(--zy-height-routine) / 2);\r\n        border: 1px solid var(--zy-el-border-color-lighter);\r\n        margin-left: 20px;\r\n        cursor: pointer;\r\n\r\n        svg {\r\n          width: 24px;\r\n          height: 24px;\r\n        }\r\n      }\r\n\r\n      .LayoutPersonalDoList {\r\n        width: 20px;\r\n        height: 20px;\r\n        cursor: pointer;\r\n        background: url('../img/layout_personal_do_list.png') no-repeat;\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .LayoutBoxMessage {\r\n        width: 20px;\r\n        height: 20px;\r\n        cursor: pointer;\r\n        background: url('../img/layout_box_message.png') no-repeat;\r\n        background-size: 100% 100%;\r\n      }\r\n\r\n      .LayoutOperation {\r\n        width: 20px;\r\n        height: 20px;\r\n        cursor: pointer;\r\n        background: url('../img/layout_operation.png') no-repeat;\r\n        background-size: 100% 100%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .LayoutViewOneContainer {\r\n    width: 100%;\r\n    height: calc(100% - 68px);\r\n\r\n    .LayoutViewOneFloatingWindow {\r\n      width: auto;\r\n      height: 100%;\r\n\r\n      .LayoutViewOneFloatingWindowBody {\r\n        width: var(--ai-chat-target-width);\r\n        height: 100%;\r\n        background: #fff;\r\n        box-sizing: border-box;\r\n        transform-origin: left center;\r\n        border-left: 1px solid var(--zy-el-border-color-lighter);\r\n      }\r\n\r\n      /* 进入动画 */\r\n      .width-animation-enter-active {\r\n        animation: widen 0.2s ease-in-out forwards;\r\n      }\r\n\r\n      /* 离开动画 */\r\n      .width-animation-leave-active {\r\n        animation: narrow 0.2s ease-in-out forwards;\r\n      }\r\n\r\n      /* 定义进入动画 */\r\n      @keyframes widen {\r\n        from {\r\n          width: 0;\r\n        }\r\n\r\n        to {\r\n          width: var(--ai-chat-target-width);\r\n        }\r\n      }\r\n\r\n      /* 定义离开动画 */\r\n      @keyframes narrow {\r\n        from {\r\n          width: var(--ai-chat-target-width);\r\n        }\r\n\r\n        to {\r\n          width: 0;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LayoutViewOneAside {\r\n      width: auto;\r\n    }\r\n\r\n    .LayoutViewOneMain {\r\n      height: 100%;\r\n      padding: 0 var(--zy-distance-three) 0 0;\r\n\r\n      .LayoutViewOneBreadcrumb {\r\n        width: 100%;\r\n        height: calc((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px);\r\n        display: flex;\r\n        align-items: center;\r\n        background-color: #fff;\r\n        padding: 0 var(--zy-distance-two);\r\n        border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n\r\n        .zy-el-breadcrumb {\r\n          font-size: var(--zy-name-font-size);\r\n\r\n          .zy-el-breadcrumb__inner {\r\n            cursor: pointer;\r\n            font-weight: bold;\r\n            color: var(--zy-el-color-primary);\r\n          }\r\n\r\n          .zy-el-breadcrumb__item {\r\n            &:last-child {\r\n              .zy-el-breadcrumb__inner {\r\n                cursor: text;\r\n                font-weight: normal;\r\n                color: var(--zy-el-text-color-regular);\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n\r\n      .LayoutViewOneBody {\r\n        width: 100%;\r\n        height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px));\r\n        background: #fff;\r\n\r\n        .subApp-viewport {\r\n          width: 100%;\r\n          height: 100%;\r\n\r\n          >div {\r\n            width: 100%;\r\n            height: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .LayoutViewOneMainView {\r\n      width: 100%;\r\n      padding: 0;\r\n\r\n      .LayoutViewOneBody {\r\n        height: 100%;\r\n        background: transparent;\r\n      }\r\n    }\r\n\r\n    .LayoutViewOneMainBreadcrumb {\r\n      width: 100%;\r\n\r\n      .LayoutViewOneBody {\r\n        height: calc(100% - ((var(--zy-name-font-size) * var(--zy-line-height)) + (var(--zy-distance-five) * 2) + 4px));\r\n      }\r\n    }\r\n  }\r\n\r\n  .ConstraintEditPassWord {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    z-index: 999;\r\n    background-color: #fff;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .EditPassWord {\r\n      box-shadow: 0px 2px 40px rgba(0, 0, 0, 0.1);\r\n    }\r\n  }\r\n}\r\n\r\n.LayoutViewOneRoleItem {\r\n  font-size: var(--zy-text-font-size);\r\n  line-height: var(--zy-line-height);\r\n}\r\n\r\n.LayoutViewOneWorkBenchPopover {\r\n  width: 680px !important;\r\n  padding: 0 !important;\r\n\r\n  .LayoutViewOneWorkBench {\r\n    width: 100%;\r\n    max-height: 480px;\r\n\r\n    .zy-el-scrollbar__wrap {\r\n      max-height: 480px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AAiHA,SAASA,oBAAoB,QAAQ,KAAK;AAC1C,SAASC,QAAQ,EAAEC,SAAS,QAAQ,YAAY;AAChD,SACEC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,YAAY,EACZC,WAAW,EACXC,eAAe,QACV,6BAA6B;AACpC,SAASC,UAAU,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,oBAAoB,QAAQ,yBAAyB;AAC/G,SAASC,UAAU,QAAQ,yBAAyB;AAdpD,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAgB,CAAC;;;;;IAexC,IAAMC,YAAY,GAAGjB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,4CAA4C,CAAC;IAAA,EAAC;IACrG,IAAMkB,YAAY,GAAGlB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,4CAA4C,CAAC;IAAA,EAAC;IACrG,IAAMmB,gBAAgB,GAAGnB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,gDAAgD,CAAC;IAAA,EAAC;IAC7G,IAAMoB,oBAAoB,GAAGpB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,oDAAoD,CAAC;IAAA,EAAC;IACrH,IAAMqB,kBAAkB,GAAGrB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,kDAAkD,CAAC;IAAA,EAAC;IACjH,IAAMsB,kBAAkB,GAAGtB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,kDAAkD,CAAC;IAAA,EAAC;IACjH,IAAMuB,oBAAoB,GAAGvB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,oDAAoD,CAAC;IAAA,EAAC;IACrH,IAAMwB,gBAAgB,GAAGxB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,gDAAgD,CAAC;IAAA,EAAC;IAC7G,IAAMyB,YAAY,GAAGzB,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,8BAA8B,CAAC;IAAA,EAAC;IACvF,IAAM0B,sBAAsB,GAAG1B,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,wCAAwC,CAAC;IAAA,EAAC;IAC3G,IAAM2B,iBAAiB,GAAG3B,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,oCAAoC,CAAC;IAAA,EAAC;IAClG,IAAM4B,UAAU,GAAG5B,oBAAoB,CAAC;MAAA,OAAM,MAAM,CAAC,oCAAoC,CAAC;IAAA,EAAC;IAC3F,IAAM6B,cAAc,GAAG;MACrBb,IAAI,EAAE,gBAAgB;MACtBc,KAAK,EAAE,CAAC,MAAM,CAAC;MACfC,QAAQ,EAAE;IACZ,CAAC;IACD,IAAAC,QAAA,GAAmB7B,OAAO,CAACF,QAAQ,CAAC,CAAC,CAAC;MAA9BgC,MAAM,GAAAD,QAAA,CAANC,MAAM;IACd,IAAAC,WAAA,GAkCI9B,UAAU,CAACH,QAAQ,CAAC,CAAC,EAAEC,SAAS,CAAC,CAAC,CAAC;MAjCrCiC,IAAI,GAAAD,WAAA,CAAJC,IAAI;MACJC,IAAI,GAAAF,WAAA,CAAJE,IAAI;MACJC,IAAI,GAAAH,WAAA,CAAJG,IAAI;MACJC,aAAa,GAAAJ,WAAA,CAAbI,aAAa;MACbC,cAAc,GAAAL,WAAA,CAAdK,cAAc;MACdC,QAAQ,GAAAN,WAAA,CAARM,QAAQ;MACRC,aAAa,GAAAP,WAAA,CAAbO,aAAa;MACbC,gBAAgB,GAAAR,WAAA,CAAhBQ,gBAAgB;MAChBC,kBAAkB,GAAAT,WAAA,CAAlBS,kBAAkB;MAClBC,sBAAsB,GAAAV,WAAA,CAAtBU,sBAAsB;MACtBC,oBAAoB,GAAAX,WAAA,CAApBW,oBAAoB;MACpBC,QAAQ,GAAAZ,WAAA,CAARY,QAAQ;MACRC,UAAU,GAAAb,WAAA,CAAVa,UAAU;MACVC,YAAY,GAAAd,WAAA,CAAZc,YAAY;MACZC,kBAAkB,GAAAf,WAAA,CAAlBe,kBAAkB;MAClBC,MAAM,GAAAhB,WAAA,CAANgB,MAAM;MACNC,OAAO,GAAAjB,WAAA,CAAPiB,OAAO;MACPC,WAAW,GAAAlB,WAAA,CAAXkB,WAAW;MACXC,WAAW,GAAAnB,WAAA,CAAXmB,WAAW;MACXC,MAAM,GAAApB,WAAA,CAANoB,MAAM;MACNC,QAAQ,GAAArB,WAAA,CAARqB,QAAQ;MACRC,SAAS,GAAAtB,WAAA,CAATsB,SAAS;MACTC,gBAAgB,GAAAvB,WAAA,CAAhBuB,gBAAgB;MAChBC,eAAe,GAAAxB,WAAA,CAAfwB,eAAe;MACfC,SAAS,GAAAzB,WAAA,CAATyB,SAAS;MACTC,cAAc,GAAA1B,WAAA,CAAd0B,cAAc;MACdC,OAAO,GAAA3B,WAAA,CAAP2B,OAAO;MACPC,QAAQ,GAAA5B,WAAA,CAAR4B,QAAQ;MACRC,aAAa,GAAA7B,WAAA,CAAb6B,aAAa;MACbC,WAAW,GAAA9B,WAAA,CAAX8B,WAAW;MACXC,gBAAgB,GAAA/B,WAAA,CAAhB+B,gBAAgB;MAChBC,UAAU,GAAAhC,WAAA,CAAVgC,UAAU;MACVC,QAAQ,GAAAjC,WAAA,CAARiC,QAAQ;IAGV,IAAAC,WAAA,GAA2B/D,UAAU,CAAC,CAAC;MAA/BgE,cAAc,GAAAD,WAAA,CAAdC,cAAc;IACtB,IAAAC,gBAAA,GAA0B9D,eAAe,CAAC,CAAC;MAAnC+D,aAAa,GAAAD,gBAAA,CAAbC,aAAa;IACrB,IAAAC,aAAA,GAAgElE,YAAY,CAAC,CAAC;MAAtEmE,iBAAiB,GAAAD,aAAA,CAAjBC,iBAAiB;MAAEC,cAAc,GAAAF,aAAA,CAAdE,cAAc;MAAEC,gBAAgB,GAAAH,aAAA,CAAhBG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}