{"ast": null, "code": "import _typeof from \"./typeof.js\";\nimport assertThisInitialized from \"./assertThisInitialized.js\";\nfunction _possibleConstructorReturn(t, e) {\n  if (e && (\"object\" == _typeof(e) || \"function\" == typeof e)) return e;\n  if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\");\n  return assertThisInitialized(t);\n}\nexport { _possibleConstructorReturn as default };", "map": {"version": 3, "names": ["_typeof", "assertThisInitialized", "_possibleConstructorReturn", "t", "e", "TypeError", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/@babel+runtime@7.26.0/node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js"], "sourcesContent": ["import _typeof from \"./typeof.js\";\nimport assertThisInitialized from \"./assertThisInitialized.js\";\nfunction _possibleConstructorReturn(t, e) {\n  if (e && (\"object\" == _typeof(e) || \"function\" == typeof e)) return e;\n  if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\");\n  return assertThisInitialized(t);\n}\nexport { _possibleConstructorReturn as default };"], "mappings": "AAAA,OAAOA,OAAO,MAAM,aAAa;AACjC,OAAOC,qBAAqB,MAAM,4BAA4B;AAC9D,SAASC,0BAA0BA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACxC,IAAIA,CAAC,KAAK,QAAQ,IAAIJ,OAAO,CAACI,CAAC,CAAC,IAAI,UAAU,IAAI,OAAOA,CAAC,CAAC,EAAE,OAAOA,CAAC;EACrE,IAAI,KAAK,CAAC,KAAKA,CAAC,EAAE,MAAM,IAAIC,SAAS,CAAC,0DAA0D,CAAC;EACjG,OAAOJ,qBAAqB,CAACE,CAAC,CAAC;AACjC;AACA,SAASD,0BAA0B,IAAII,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}