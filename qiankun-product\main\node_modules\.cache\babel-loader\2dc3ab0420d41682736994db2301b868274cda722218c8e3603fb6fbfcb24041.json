{"ast": null, "code": "// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\n//\n// Changes from joyent/node:\n//\n// 1. No leading slash in paths,\n//    e.g. in `url.parse('http://foo?bar')` pathname is ``, not `/`\n//\n// 2. Backslashes are not replaced with slashes,\n//    so `http:\\\\example.org\\` is treated like a relative path\n//\n// 3. Trailing colon is treated like a part of the path,\n//    i.e. in `http://example.org:foo` pathname is `:foo`\n//\n// 4. Nothing is URL-encoded in the resulting object,\n//    (in joyent/node some chars in auth and paths are encoded)\n//\n// 5. `url.parse()` does not have `parseQueryString` argument\n//\n// 6. Removed extraneous result properties: `host`, `path`, `query`, etc.,\n//    which can be constructed using other parts of the url.\n//\nfunction Url() {\n  this.protocol = null;\n  this.slashes = null;\n  this.auth = null;\n  this.port = null;\n  this.hostname = null;\n  this.hash = null;\n  this.search = null;\n  this.pathname = null;\n}\n\n// Reference: RFC 3986, RFC 1808, RFC 2396\n\n// define these here so at least they only have to be\n// compiled once on the first module load.\nvar protocolPattern = /^([a-z0-9.+-]+:)/i,\n  portPattern = /:[0-9]*$/,\n  // Special case for a simple path URL\n  simplePathPattern = /^(\\/\\/?(?!\\/)[^\\?\\s]*)(\\?[^\\s]*)?$/,\n  // RFC 2396: characters reserved for delimiting URLs.\n  // We actually just auto-escape these.\n  delims = ['<', '>', '\"', '`', ' ', '\\r', '\\n', '\\t'],\n  // RFC 2396: characters not allowed for various reasons.\n  unwise = ['{', '}', '|', '\\\\', '^', '`'].concat(delims),\n  // Allowed by RFCs, but cause of XSS attacks.  Always escape these.\n  autoEscape = ['\\''].concat(unwise),\n  // Characters that are never ever allowed in a hostname.\n  // Note that any invalid chars are also handled, but these\n  // are the ones that are *expected* to be seen, so we fast-path\n  // them.\n  nonHostChars = ['%', '/', '?', ';', '#'].concat(autoEscape),\n  hostEndingChars = ['/', '?', '#'],\n  hostnameMaxLen = 255,\n  hostnamePartPattern = /^[+a-z0-9A-Z_-]{0,63}$/,\n  hostnamePartStart = /^([+a-z0-9A-Z_-]{0,63})(.*)$/,\n  // protocols that can allow \"unsafe\" and \"unwise\" chars.\n  /* eslint-disable no-script-url */\n  // protocols that never have a hostname.\n  hostlessProtocol = {\n    'javascript': true,\n    'javascript:': true\n  },\n  // protocols that always contain a // bit.\n  slashedProtocol = {\n    'http': true,\n    'https': true,\n    'ftp': true,\n    'gopher': true,\n    'file': true,\n    'http:': true,\n    'https:': true,\n    'ftp:': true,\n    'gopher:': true,\n    'file:': true\n  };\n/* eslint-enable no-script-url */\n\nfunction urlParse(url, slashesDenoteHost) {\n  if (url && url instanceof Url) {\n    return url;\n  }\n  var u = new Url();\n  u.parse(url, slashesDenoteHost);\n  return u;\n}\nUrl.prototype.parse = function (url, slashesDenoteHost) {\n  var i,\n    l,\n    lowerProto,\n    hec,\n    slashes,\n    rest = url;\n\n  // trim before proceeding.\n  // This is to support parse stuff like \"  http://foo.com  \\n\"\n  rest = rest.trim();\n  if (!slashesDenoteHost && url.split('#').length === 1) {\n    // Try fast path regexp\n    var simplePath = simplePathPattern.exec(rest);\n    if (simplePath) {\n      this.pathname = simplePath[1];\n      if (simplePath[2]) {\n        this.search = simplePath[2];\n      }\n      return this;\n    }\n  }\n  var proto = protocolPattern.exec(rest);\n  if (proto) {\n    proto = proto[0];\n    lowerProto = proto.toLowerCase();\n    this.protocol = proto;\n    rest = rest.substr(proto.length);\n  }\n\n  // figure out if it's got a host\n  // user@server is *always* interpreted as a hostname, and url\n  // resolution will treat //foo/bar as host=foo,path=bar because that's\n  // how the browser resolves relative URLs.\n  if (slashesDenoteHost || proto || rest.match(/^\\/\\/[^@\\/]+@[^@\\/]+/)) {\n    slashes = rest.substr(0, 2) === '//';\n    if (slashes && !(proto && hostlessProtocol[proto])) {\n      rest = rest.substr(2);\n      this.slashes = true;\n    }\n  }\n  if (!hostlessProtocol[proto] && (slashes || proto && !slashedProtocol[proto])) {\n    // there's a hostname.\n    // the first instance of /, ?, ;, or # ends the host.\n    //\n    // If there is an @ in the hostname, then non-host chars *are* allowed\n    // to the left of the last @ sign, unless some host-ending character\n    // comes *before* the @-sign.\n    // URLs are obnoxious.\n    //\n    // ex:\n    // http://a@b@c/ => user:a@b host:c\n    // http://a@b?@c => user:a host:c path:/?@c\n\n    // v0.12 TODO(isaacs): This is not quite how Chrome does things.\n    // Review our test case against browsers more comprehensively.\n\n    // find the first instance of any hostEndingChars\n    var hostEnd = -1;\n    for (i = 0; i < hostEndingChars.length; i++) {\n      hec = rest.indexOf(hostEndingChars[i]);\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) {\n        hostEnd = hec;\n      }\n    }\n\n    // at this point, either we have an explicit point where the\n    // auth portion cannot go past, or the last @ char is the decider.\n    var auth, atSign;\n    if (hostEnd === -1) {\n      // atSign can be anywhere.\n      atSign = rest.lastIndexOf('@');\n    } else {\n      // atSign must be in auth portion.\n      // http://a@b/c@d => host:b auth:a path:/c@d\n      atSign = rest.lastIndexOf('@', hostEnd);\n    }\n\n    // Now we have a portion which is definitely the auth.\n    // Pull that off.\n    if (atSign !== -1) {\n      auth = rest.slice(0, atSign);\n      rest = rest.slice(atSign + 1);\n      this.auth = auth;\n    }\n\n    // the host is the remaining to the left of the first non-host char\n    hostEnd = -1;\n    for (i = 0; i < nonHostChars.length; i++) {\n      hec = rest.indexOf(nonHostChars[i]);\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) {\n        hostEnd = hec;\n      }\n    }\n    // if we still have not hit it, then the entire thing is a host.\n    if (hostEnd === -1) {\n      hostEnd = rest.length;\n    }\n    if (rest[hostEnd - 1] === ':') {\n      hostEnd--;\n    }\n    var host = rest.slice(0, hostEnd);\n    rest = rest.slice(hostEnd);\n\n    // pull out port.\n    this.parseHost(host);\n\n    // we've indicated that there is a hostname,\n    // so even if it's empty, it has to be present.\n    this.hostname = this.hostname || '';\n\n    // if hostname begins with [ and ends with ]\n    // assume that it's an IPv6 address.\n    var ipv6Hostname = this.hostname[0] === '[' && this.hostname[this.hostname.length - 1] === ']';\n\n    // validate a little.\n    if (!ipv6Hostname) {\n      var hostparts = this.hostname.split(/\\./);\n      for (i = 0, l = hostparts.length; i < l; i++) {\n        var part = hostparts[i];\n        if (!part) {\n          continue;\n        }\n        if (!part.match(hostnamePartPattern)) {\n          var newpart = '';\n          for (var j = 0, k = part.length; j < k; j++) {\n            if (part.charCodeAt(j) > 127) {\n              // we replace non-ASCII char with a temporary placeholder\n              // we need this to make sure size of hostname is not\n              // broken by replacing non-ASCII by nothing\n              newpart += 'x';\n            } else {\n              newpart += part[j];\n            }\n          }\n          // we test again with ASCII char only\n          if (!newpart.match(hostnamePartPattern)) {\n            var validParts = hostparts.slice(0, i);\n            var notHost = hostparts.slice(i + 1);\n            var bit = part.match(hostnamePartStart);\n            if (bit) {\n              validParts.push(bit[1]);\n              notHost.unshift(bit[2]);\n            }\n            if (notHost.length) {\n              rest = notHost.join('.') + rest;\n            }\n            this.hostname = validParts.join('.');\n            break;\n          }\n        }\n      }\n    }\n    if (this.hostname.length > hostnameMaxLen) {\n      this.hostname = '';\n    }\n\n    // strip [ and ] from the hostname\n    // the host field still retains them, though\n    if (ipv6Hostname) {\n      this.hostname = this.hostname.substr(1, this.hostname.length - 2);\n    }\n  }\n\n  // chop off from the tail first.\n  var hash = rest.indexOf('#');\n  if (hash !== -1) {\n    // got a fragment string.\n    this.hash = rest.substr(hash);\n    rest = rest.slice(0, hash);\n  }\n  var qm = rest.indexOf('?');\n  if (qm !== -1) {\n    this.search = rest.substr(qm);\n    rest = rest.slice(0, qm);\n  }\n  if (rest) {\n    this.pathname = rest;\n  }\n  if (slashedProtocol[lowerProto] && this.hostname && !this.pathname) {\n    this.pathname = '';\n  }\n  return this;\n};\nUrl.prototype.parseHost = function (host) {\n  var port = portPattern.exec(host);\n  if (port) {\n    port = port[0];\n    if (port !== ':') {\n      this.port = port.substr(1);\n    }\n    host = host.substr(0, host.length - port.length);\n  }\n  if (host) {\n    this.hostname = host;\n  }\n};\nmodule.exports = urlParse;", "map": {"version": 3, "names": ["Url", "protocol", "slashes", "auth", "port", "hostname", "hash", "search", "pathname", "protocolPattern", "portPattern", "simplePathPattern", "delims", "unwise", "concat", "autoEscape", "nonHostChars", "hostEndingChars", "hostnameMaxLen", "hostnamePartPattern", "hostnamePartStart", "hostlessProtocol", "slashedProtocol", "urlParse", "url", "slashesDenoteHost", "u", "parse", "prototype", "i", "l", "lowerProto", "hec", "rest", "trim", "split", "length", "simplePath", "exec", "proto", "toLowerCase", "substr", "match", "hostEnd", "indexOf", "atSign", "lastIndexOf", "slice", "host", "parseHost", "ipv6Hostname", "hostparts", "part", "newpart", "j", "k", "charCodeAt", "validParts", "notHost", "bit", "push", "unshift", "join", "qm", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/mdurl@1.0.1/node_modules/mdurl/parse.js"], "sourcesContent": ["// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n'use strict';\n\n//\n// Changes from joyent/node:\n//\n// 1. No leading slash in paths,\n//    e.g. in `url.parse('http://foo?bar')` pathname is ``, not `/`\n//\n// 2. Backslashes are not replaced with slashes,\n//    so `http:\\\\example.org\\` is treated like a relative path\n//\n// 3. Trailing colon is treated like a part of the path,\n//    i.e. in `http://example.org:foo` pathname is `:foo`\n//\n// 4. Nothing is URL-encoded in the resulting object,\n//    (in joyent/node some chars in auth and paths are encoded)\n//\n// 5. `url.parse()` does not have `parseQueryString` argument\n//\n// 6. Removed extraneous result properties: `host`, `path`, `query`, etc.,\n//    which can be constructed using other parts of the url.\n//\n\n\nfunction Url() {\n  this.protocol = null;\n  this.slashes = null;\n  this.auth = null;\n  this.port = null;\n  this.hostname = null;\n  this.hash = null;\n  this.search = null;\n  this.pathname = null;\n}\n\n// Reference: RFC 3986, RFC 1808, RFC 2396\n\n// define these here so at least they only have to be\n// compiled once on the first module load.\nvar protocolPattern = /^([a-z0-9.+-]+:)/i,\n    portPattern = /:[0-9]*$/,\n\n    // Special case for a simple path URL\n    simplePathPattern = /^(\\/\\/?(?!\\/)[^\\?\\s]*)(\\?[^\\s]*)?$/,\n\n    // RFC 2396: characters reserved for delimiting URLs.\n    // We actually just auto-escape these.\n    delims = [ '<', '>', '\"', '`', ' ', '\\r', '\\n', '\\t' ],\n\n    // RFC 2396: characters not allowed for various reasons.\n    unwise = [ '{', '}', '|', '\\\\', '^', '`' ].concat(delims),\n\n    // Allowed by RFCs, but cause of XSS attacks.  Always escape these.\n    autoEscape = [ '\\'' ].concat(unwise),\n    // Characters that are never ever allowed in a hostname.\n    // Note that any invalid chars are also handled, but these\n    // are the ones that are *expected* to be seen, so we fast-path\n    // them.\n    nonHostChars = [ '%', '/', '?', ';', '#' ].concat(autoEscape),\n    hostEndingChars = [ '/', '?', '#' ],\n    hostnameMaxLen = 255,\n    hostnamePartPattern = /^[+a-z0-9A-Z_-]{0,63}$/,\n    hostnamePartStart = /^([+a-z0-9A-Z_-]{0,63})(.*)$/,\n    // protocols that can allow \"unsafe\" and \"unwise\" chars.\n    /* eslint-disable no-script-url */\n    // protocols that never have a hostname.\n    hostlessProtocol = {\n      'javascript': true,\n      'javascript:': true\n    },\n    // protocols that always contain a // bit.\n    slashedProtocol = {\n      'http': true,\n      'https': true,\n      'ftp': true,\n      'gopher': true,\n      'file': true,\n      'http:': true,\n      'https:': true,\n      'ftp:': true,\n      'gopher:': true,\n      'file:': true\n    };\n    /* eslint-enable no-script-url */\n\nfunction urlParse(url, slashesDenoteHost) {\n  if (url && url instanceof Url) { return url; }\n\n  var u = new Url();\n  u.parse(url, slashesDenoteHost);\n  return u;\n}\n\nUrl.prototype.parse = function(url, slashesDenoteHost) {\n  var i, l, lowerProto, hec, slashes,\n      rest = url;\n\n  // trim before proceeding.\n  // This is to support parse stuff like \"  http://foo.com  \\n\"\n  rest = rest.trim();\n\n  if (!slashesDenoteHost && url.split('#').length === 1) {\n    // Try fast path regexp\n    var simplePath = simplePathPattern.exec(rest);\n    if (simplePath) {\n      this.pathname = simplePath[1];\n      if (simplePath[2]) {\n        this.search = simplePath[2];\n      }\n      return this;\n    }\n  }\n\n  var proto = protocolPattern.exec(rest);\n  if (proto) {\n    proto = proto[0];\n    lowerProto = proto.toLowerCase();\n    this.protocol = proto;\n    rest = rest.substr(proto.length);\n  }\n\n  // figure out if it's got a host\n  // user@server is *always* interpreted as a hostname, and url\n  // resolution will treat //foo/bar as host=foo,path=bar because that's\n  // how the browser resolves relative URLs.\n  if (slashesDenoteHost || proto || rest.match(/^\\/\\/[^@\\/]+@[^@\\/]+/)) {\n    slashes = rest.substr(0, 2) === '//';\n    if (slashes && !(proto && hostlessProtocol[proto])) {\n      rest = rest.substr(2);\n      this.slashes = true;\n    }\n  }\n\n  if (!hostlessProtocol[proto] &&\n      (slashes || (proto && !slashedProtocol[proto]))) {\n\n    // there's a hostname.\n    // the first instance of /, ?, ;, or # ends the host.\n    //\n    // If there is an @ in the hostname, then non-host chars *are* allowed\n    // to the left of the last @ sign, unless some host-ending character\n    // comes *before* the @-sign.\n    // URLs are obnoxious.\n    //\n    // ex:\n    // http://a@b@c/ => user:a@b host:c\n    // http://a@b?@c => user:a host:c path:/?@c\n\n    // v0.12 TODO(isaacs): This is not quite how Chrome does things.\n    // Review our test case against browsers more comprehensively.\n\n    // find the first instance of any hostEndingChars\n    var hostEnd = -1;\n    for (i = 0; i < hostEndingChars.length; i++) {\n      hec = rest.indexOf(hostEndingChars[i]);\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) {\n        hostEnd = hec;\n      }\n    }\n\n    // at this point, either we have an explicit point where the\n    // auth portion cannot go past, or the last @ char is the decider.\n    var auth, atSign;\n    if (hostEnd === -1) {\n      // atSign can be anywhere.\n      atSign = rest.lastIndexOf('@');\n    } else {\n      // atSign must be in auth portion.\n      // http://a@b/c@d => host:b auth:a path:/c@d\n      atSign = rest.lastIndexOf('@', hostEnd);\n    }\n\n    // Now we have a portion which is definitely the auth.\n    // Pull that off.\n    if (atSign !== -1) {\n      auth = rest.slice(0, atSign);\n      rest = rest.slice(atSign + 1);\n      this.auth = auth;\n    }\n\n    // the host is the remaining to the left of the first non-host char\n    hostEnd = -1;\n    for (i = 0; i < nonHostChars.length; i++) {\n      hec = rest.indexOf(nonHostChars[i]);\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) {\n        hostEnd = hec;\n      }\n    }\n    // if we still have not hit it, then the entire thing is a host.\n    if (hostEnd === -1) {\n      hostEnd = rest.length;\n    }\n\n    if (rest[hostEnd - 1] === ':') { hostEnd--; }\n    var host = rest.slice(0, hostEnd);\n    rest = rest.slice(hostEnd);\n\n    // pull out port.\n    this.parseHost(host);\n\n    // we've indicated that there is a hostname,\n    // so even if it's empty, it has to be present.\n    this.hostname = this.hostname || '';\n\n    // if hostname begins with [ and ends with ]\n    // assume that it's an IPv6 address.\n    var ipv6Hostname = this.hostname[0] === '[' &&\n        this.hostname[this.hostname.length - 1] === ']';\n\n    // validate a little.\n    if (!ipv6Hostname) {\n      var hostparts = this.hostname.split(/\\./);\n      for (i = 0, l = hostparts.length; i < l; i++) {\n        var part = hostparts[i];\n        if (!part) { continue; }\n        if (!part.match(hostnamePartPattern)) {\n          var newpart = '';\n          for (var j = 0, k = part.length; j < k; j++) {\n            if (part.charCodeAt(j) > 127) {\n              // we replace non-ASCII char with a temporary placeholder\n              // we need this to make sure size of hostname is not\n              // broken by replacing non-ASCII by nothing\n              newpart += 'x';\n            } else {\n              newpart += part[j];\n            }\n          }\n          // we test again with ASCII char only\n          if (!newpart.match(hostnamePartPattern)) {\n            var validParts = hostparts.slice(0, i);\n            var notHost = hostparts.slice(i + 1);\n            var bit = part.match(hostnamePartStart);\n            if (bit) {\n              validParts.push(bit[1]);\n              notHost.unshift(bit[2]);\n            }\n            if (notHost.length) {\n              rest = notHost.join('.') + rest;\n            }\n            this.hostname = validParts.join('.');\n            break;\n          }\n        }\n      }\n    }\n\n    if (this.hostname.length > hostnameMaxLen) {\n      this.hostname = '';\n    }\n\n    // strip [ and ] from the hostname\n    // the host field still retains them, though\n    if (ipv6Hostname) {\n      this.hostname = this.hostname.substr(1, this.hostname.length - 2);\n    }\n  }\n\n  // chop off from the tail first.\n  var hash = rest.indexOf('#');\n  if (hash !== -1) {\n    // got a fragment string.\n    this.hash = rest.substr(hash);\n    rest = rest.slice(0, hash);\n  }\n  var qm = rest.indexOf('?');\n  if (qm !== -1) {\n    this.search = rest.substr(qm);\n    rest = rest.slice(0, qm);\n  }\n  if (rest) { this.pathname = rest; }\n  if (slashedProtocol[lowerProto] &&\n      this.hostname && !this.pathname) {\n    this.pathname = '';\n  }\n\n  return this;\n};\n\nUrl.prototype.parseHost = function(host) {\n  var port = portPattern.exec(host);\n  if (port) {\n    port = port[0];\n    if (port !== ':') {\n      this.port = port.substr(1);\n    }\n    host = host.substr(0, host.length - port.length);\n  }\n  if (host) { this.hostname = host; }\n};\n\nmodule.exports = urlParse;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA,SAASA,GAAGA,CAAA,EAAG;EACb,IAAI,CAACC,QAAQ,GAAG,IAAI;EACpB,IAAI,CAACC,OAAO,GAAG,IAAI;EACnB,IAAI,CAACC,IAAI,GAAG,IAAI;EAChB,IAAI,CAACC,IAAI,GAAG,IAAI;EAChB,IAAI,CAACC,QAAQ,GAAG,IAAI;EACpB,IAAI,CAACC,IAAI,GAAG,IAAI;EAChB,IAAI,CAACC,MAAM,GAAG,IAAI;EAClB,IAAI,CAACC,QAAQ,GAAG,IAAI;AACtB;;AAEA;;AAEA;AACA;AACA,IAAIC,eAAe,GAAG,mBAAmB;EACrCC,WAAW,GAAG,UAAU;EAExB;EACAC,iBAAiB,GAAG,oCAAoC;EAExD;EACA;EACAC,MAAM,GAAG,CAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAE;EAEtD;EACAC,MAAM,GAAG,CAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAE,CAACC,MAAM,CAACF,MAAM,CAAC;EAEzD;EACAG,UAAU,GAAG,CAAE,IAAI,CAAE,CAACD,MAAM,CAACD,MAAM,CAAC;EACpC;EACA;EACA;EACA;EACAG,YAAY,GAAG,CAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE,CAACF,MAAM,CAACC,UAAU,CAAC;EAC7DE,eAAe,GAAG,CAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAE;EACnCC,cAAc,GAAG,GAAG;EACpBC,mBAAmB,GAAG,wBAAwB;EAC9CC,iBAAiB,GAAG,8BAA8B;EAClD;EACA;EACA;EACAC,gBAAgB,GAAG;IACjB,YAAY,EAAE,IAAI;IAClB,aAAa,EAAE;EACjB,CAAC;EACD;EACAC,eAAe,GAAG;IAChB,MAAM,EAAE,IAAI;IACZ,OAAO,EAAE,IAAI;IACb,KAAK,EAAE,IAAI;IACX,QAAQ,EAAE,IAAI;IACd,MAAM,EAAE,IAAI;IACZ,OAAO,EAAE,IAAI;IACb,QAAQ,EAAE,IAAI;IACd,MAAM,EAAE,IAAI;IACZ,SAAS,EAAE,IAAI;IACf,OAAO,EAAE;EACX,CAAC;AACD;;AAEJ,SAASC,QAAQA,CAACC,GAAG,EAAEC,iBAAiB,EAAE;EACxC,IAAID,GAAG,IAAIA,GAAG,YAAYxB,GAAG,EAAE;IAAE,OAAOwB,GAAG;EAAE;EAE7C,IAAIE,CAAC,GAAG,IAAI1B,GAAG,CAAC,CAAC;EACjB0B,CAAC,CAACC,KAAK,CAACH,GAAG,EAAEC,iBAAiB,CAAC;EAC/B,OAAOC,CAAC;AACV;AAEA1B,GAAG,CAAC4B,SAAS,CAACD,KAAK,GAAG,UAASH,GAAG,EAAEC,iBAAiB,EAAE;EACrD,IAAII,CAAC;IAAEC,CAAC;IAAEC,UAAU;IAAEC,GAAG;IAAE9B,OAAO;IAC9B+B,IAAI,GAAGT,GAAG;;EAEd;EACA;EACAS,IAAI,GAAGA,IAAI,CAACC,IAAI,CAAC,CAAC;EAElB,IAAI,CAACT,iBAAiB,IAAID,GAAG,CAACW,KAAK,CAAC,GAAG,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;IACrD;IACA,IAAIC,UAAU,GAAG1B,iBAAiB,CAAC2B,IAAI,CAACL,IAAI,CAAC;IAC7C,IAAII,UAAU,EAAE;MACd,IAAI,CAAC7B,QAAQ,GAAG6B,UAAU,CAAC,CAAC,CAAC;MAC7B,IAAIA,UAAU,CAAC,CAAC,CAAC,EAAE;QACjB,IAAI,CAAC9B,MAAM,GAAG8B,UAAU,CAAC,CAAC,CAAC;MAC7B;MACA,OAAO,IAAI;IACb;EACF;EAEA,IAAIE,KAAK,GAAG9B,eAAe,CAAC6B,IAAI,CAACL,IAAI,CAAC;EACtC,IAAIM,KAAK,EAAE;IACTA,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC;IAChBR,UAAU,GAAGQ,KAAK,CAACC,WAAW,CAAC,CAAC;IAChC,IAAI,CAACvC,QAAQ,GAAGsC,KAAK;IACrBN,IAAI,GAAGA,IAAI,CAACQ,MAAM,CAACF,KAAK,CAACH,MAAM,CAAC;EAClC;;EAEA;EACA;EACA;EACA;EACA,IAAIX,iBAAiB,IAAIc,KAAK,IAAIN,IAAI,CAACS,KAAK,CAAC,sBAAsB,CAAC,EAAE;IACpExC,OAAO,GAAG+B,IAAI,CAACQ,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,IAAI;IACpC,IAAIvC,OAAO,IAAI,EAAEqC,KAAK,IAAIlB,gBAAgB,CAACkB,KAAK,CAAC,CAAC,EAAE;MAClDN,IAAI,GAAGA,IAAI,CAACQ,MAAM,CAAC,CAAC,CAAC;MACrB,IAAI,CAACvC,OAAO,GAAG,IAAI;IACrB;EACF;EAEA,IAAI,CAACmB,gBAAgB,CAACkB,KAAK,CAAC,KACvBrC,OAAO,IAAKqC,KAAK,IAAI,CAACjB,eAAe,CAACiB,KAAK,CAAE,CAAC,EAAE;IAEnD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA;IACA;;IAEA;IACA,IAAII,OAAO,GAAG,CAAC,CAAC;IAChB,KAAKd,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,eAAe,CAACmB,MAAM,EAAEP,CAAC,EAAE,EAAE;MAC3CG,GAAG,GAAGC,IAAI,CAACW,OAAO,CAAC3B,eAAe,CAACY,CAAC,CAAC,CAAC;MACtC,IAAIG,GAAG,KAAK,CAAC,CAAC,KAAKW,OAAO,KAAK,CAAC,CAAC,IAAIX,GAAG,GAAGW,OAAO,CAAC,EAAE;QACnDA,OAAO,GAAGX,GAAG;MACf;IACF;;IAEA;IACA;IACA,IAAI7B,IAAI,EAAE0C,MAAM;IAChB,IAAIF,OAAO,KAAK,CAAC,CAAC,EAAE;MAClB;MACAE,MAAM,GAAGZ,IAAI,CAACa,WAAW,CAAC,GAAG,CAAC;IAChC,CAAC,MAAM;MACL;MACA;MACAD,MAAM,GAAGZ,IAAI,CAACa,WAAW,CAAC,GAAG,EAAEH,OAAO,CAAC;IACzC;;IAEA;IACA;IACA,IAAIE,MAAM,KAAK,CAAC,CAAC,EAAE;MACjB1C,IAAI,GAAG8B,IAAI,CAACc,KAAK,CAAC,CAAC,EAAEF,MAAM,CAAC;MAC5BZ,IAAI,GAAGA,IAAI,CAACc,KAAK,CAACF,MAAM,GAAG,CAAC,CAAC;MAC7B,IAAI,CAAC1C,IAAI,GAAGA,IAAI;IAClB;;IAEA;IACAwC,OAAO,GAAG,CAAC,CAAC;IACZ,KAAKd,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGb,YAAY,CAACoB,MAAM,EAAEP,CAAC,EAAE,EAAE;MACxCG,GAAG,GAAGC,IAAI,CAACW,OAAO,CAAC5B,YAAY,CAACa,CAAC,CAAC,CAAC;MACnC,IAAIG,GAAG,KAAK,CAAC,CAAC,KAAKW,OAAO,KAAK,CAAC,CAAC,IAAIX,GAAG,GAAGW,OAAO,CAAC,EAAE;QACnDA,OAAO,GAAGX,GAAG;MACf;IACF;IACA;IACA,IAAIW,OAAO,KAAK,CAAC,CAAC,EAAE;MAClBA,OAAO,GAAGV,IAAI,CAACG,MAAM;IACvB;IAEA,IAAIH,IAAI,CAACU,OAAO,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;MAAEA,OAAO,EAAE;IAAE;IAC5C,IAAIK,IAAI,GAAGf,IAAI,CAACc,KAAK,CAAC,CAAC,EAAEJ,OAAO,CAAC;IACjCV,IAAI,GAAGA,IAAI,CAACc,KAAK,CAACJ,OAAO,CAAC;;IAE1B;IACA,IAAI,CAACM,SAAS,CAACD,IAAI,CAAC;;IAEpB;IACA;IACA,IAAI,CAAC3C,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAI,EAAE;;IAEnC;IACA;IACA,IAAI6C,YAAY,GAAG,IAAI,CAAC7C,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,IACvC,IAAI,CAACA,QAAQ,CAAC,IAAI,CAACA,QAAQ,CAAC+B,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG;;IAEnD;IACA,IAAI,CAACc,YAAY,EAAE;MACjB,IAAIC,SAAS,GAAG,IAAI,CAAC9C,QAAQ,CAAC8B,KAAK,CAAC,IAAI,CAAC;MACzC,KAAKN,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGqB,SAAS,CAACf,MAAM,EAAEP,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;QAC5C,IAAIuB,IAAI,GAAGD,SAAS,CAACtB,CAAC,CAAC;QACvB,IAAI,CAACuB,IAAI,EAAE;UAAE;QAAU;QACvB,IAAI,CAACA,IAAI,CAACV,KAAK,CAACvB,mBAAmB,CAAC,EAAE;UACpC,IAAIkC,OAAO,GAAG,EAAE;UAChB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,IAAI,CAAChB,MAAM,EAAEkB,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;YAC3C,IAAIF,IAAI,CAACI,UAAU,CAACF,CAAC,CAAC,GAAG,GAAG,EAAE;cAC5B;cACA;cACA;cACAD,OAAO,IAAI,GAAG;YAChB,CAAC,MAAM;cACLA,OAAO,IAAID,IAAI,CAACE,CAAC,CAAC;YACpB;UACF;UACA;UACA,IAAI,CAACD,OAAO,CAACX,KAAK,CAACvB,mBAAmB,CAAC,EAAE;YACvC,IAAIsC,UAAU,GAAGN,SAAS,CAACJ,KAAK,CAAC,CAAC,EAAElB,CAAC,CAAC;YACtC,IAAI6B,OAAO,GAAGP,SAAS,CAACJ,KAAK,CAAClB,CAAC,GAAG,CAAC,CAAC;YACpC,IAAI8B,GAAG,GAAGP,IAAI,CAACV,KAAK,CAACtB,iBAAiB,CAAC;YACvC,IAAIuC,GAAG,EAAE;cACPF,UAAU,CAACG,IAAI,CAACD,GAAG,CAAC,CAAC,CAAC,CAAC;cACvBD,OAAO,CAACG,OAAO,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;YACzB;YACA,IAAID,OAAO,CAACtB,MAAM,EAAE;cAClBH,IAAI,GAAGyB,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC,GAAG7B,IAAI;YACjC;YACA,IAAI,CAAC5B,QAAQ,GAAGoD,UAAU,CAACK,IAAI,CAAC,GAAG,CAAC;YACpC;UACF;QACF;MACF;IACF;IAEA,IAAI,IAAI,CAACzD,QAAQ,CAAC+B,MAAM,GAAGlB,cAAc,EAAE;MACzC,IAAI,CAACb,QAAQ,GAAG,EAAE;IACpB;;IAEA;IACA;IACA,IAAI6C,YAAY,EAAE;MAChB,IAAI,CAAC7C,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACoC,MAAM,CAAC,CAAC,EAAE,IAAI,CAACpC,QAAQ,CAAC+B,MAAM,GAAG,CAAC,CAAC;IACnE;EACF;;EAEA;EACA,IAAI9B,IAAI,GAAG2B,IAAI,CAACW,OAAO,CAAC,GAAG,CAAC;EAC5B,IAAItC,IAAI,KAAK,CAAC,CAAC,EAAE;IACf;IACA,IAAI,CAACA,IAAI,GAAG2B,IAAI,CAACQ,MAAM,CAACnC,IAAI,CAAC;IAC7B2B,IAAI,GAAGA,IAAI,CAACc,KAAK,CAAC,CAAC,EAAEzC,IAAI,CAAC;EAC5B;EACA,IAAIyD,EAAE,GAAG9B,IAAI,CAACW,OAAO,CAAC,GAAG,CAAC;EAC1B,IAAImB,EAAE,KAAK,CAAC,CAAC,EAAE;IACb,IAAI,CAACxD,MAAM,GAAG0B,IAAI,CAACQ,MAAM,CAACsB,EAAE,CAAC;IAC7B9B,IAAI,GAAGA,IAAI,CAACc,KAAK,CAAC,CAAC,EAAEgB,EAAE,CAAC;EAC1B;EACA,IAAI9B,IAAI,EAAE;IAAE,IAAI,CAACzB,QAAQ,GAAGyB,IAAI;EAAE;EAClC,IAAIX,eAAe,CAACS,UAAU,CAAC,IAC3B,IAAI,CAAC1B,QAAQ,IAAI,CAAC,IAAI,CAACG,QAAQ,EAAE;IACnC,IAAI,CAACA,QAAQ,GAAG,EAAE;EACpB;EAEA,OAAO,IAAI;AACb,CAAC;AAEDR,GAAG,CAAC4B,SAAS,CAACqB,SAAS,GAAG,UAASD,IAAI,EAAE;EACvC,IAAI5C,IAAI,GAAGM,WAAW,CAAC4B,IAAI,CAACU,IAAI,CAAC;EACjC,IAAI5C,IAAI,EAAE;IACRA,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC;IACd,IAAIA,IAAI,KAAK,GAAG,EAAE;MAChB,IAAI,CAACA,IAAI,GAAGA,IAAI,CAACqC,MAAM,CAAC,CAAC,CAAC;IAC5B;IACAO,IAAI,GAAGA,IAAI,CAACP,MAAM,CAAC,CAAC,EAAEO,IAAI,CAACZ,MAAM,GAAGhC,IAAI,CAACgC,MAAM,CAAC;EAClD;EACA,IAAIY,IAAI,EAAE;IAAE,IAAI,CAAC3C,QAAQ,GAAG2C,IAAI;EAAE;AACpC,CAAC;AAEDgB,MAAM,CAACC,OAAO,GAAG1C,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}