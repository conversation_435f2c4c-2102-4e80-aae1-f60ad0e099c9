{"ast": null, "code": "// Process escaped chars and hardbreaks\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\nvar ESCAPED = [];\nfor (var i = 0; i < 256; i++) {\n  ESCAPED.push(0);\n}\n'\\\\!\"#$%&\\'()*+,./:;<=>?@[]^_`{|}~-'.split('').forEach(function (ch) {\n  ESCAPED[ch.charCodeAt(0)] = 1;\n});\nmodule.exports = function escape(state, silent) {\n  var ch,\n    pos = state.pos,\n    max = state.posMax;\n  if (state.src.charCodeAt(pos) !== 0x5C /* \\ */) {\n    return false;\n  }\n  pos++;\n  if (pos < max) {\n    ch = state.src.charCodeAt(pos);\n    if (ch < 256 && ESCAPED[ch] !== 0) {\n      if (!silent) {\n        state.pending += state.src[pos];\n      }\n      state.pos += 2;\n      return true;\n    }\n    if (ch === 0x0A) {\n      if (!silent) {\n        state.push('hardbreak', 'br', 0);\n      }\n      pos++;\n      // skip leading whitespaces from next line\n      while (pos < max) {\n        ch = state.src.charCodeAt(pos);\n        if (!isSpace(ch)) {\n          break;\n        }\n        pos++;\n      }\n      state.pos = pos;\n      return true;\n    }\n  }\n  if (!silent) {\n    state.pending += '\\\\';\n  }\n  state.pos++;\n  return true;\n};", "map": {"version": 3, "names": ["isSpace", "require", "ESCAPED", "i", "push", "split", "for<PERSON>ach", "ch", "charCodeAt", "module", "exports", "escape", "state", "silent", "pos", "max", "posMax", "src", "pending"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_inline/escape.js"], "sourcesContent": ["// Process escaped chars and hardbreaks\n\n'use strict';\n\nvar isSpace = require('../common/utils').isSpace;\n\nvar ESCAPED = [];\n\nfor (var i = 0; i < 256; i++) { ESCAPED.push(0); }\n\n'\\\\!\"#$%&\\'()*+,./:;<=>?@[]^_`{|}~-'\n  .split('').forEach(function (ch) { ESCAPED[ch.charCodeAt(0)] = 1; });\n\n\nmodule.exports = function escape(state, silent) {\n  var ch, pos = state.pos, max = state.posMax;\n\n  if (state.src.charCodeAt(pos) !== 0x5C/* \\ */) { return false; }\n\n  pos++;\n\n  if (pos < max) {\n    ch = state.src.charCodeAt(pos);\n\n    if (ch < 256 && ESCAPED[ch] !== 0) {\n      if (!silent) { state.pending += state.src[pos]; }\n      state.pos += 2;\n      return true;\n    }\n\n    if (ch === 0x0A) {\n      if (!silent) {\n        state.push('hardbreak', 'br', 0);\n      }\n\n      pos++;\n      // skip leading whitespaces from next line\n      while (pos < max) {\n        ch = state.src.charCodeAt(pos);\n        if (!isSpace(ch)) { break; }\n        pos++;\n      }\n\n      state.pos = pos;\n      return true;\n    }\n  }\n\n  if (!silent) { state.pending += '\\\\'; }\n  state.pos++;\n  return true;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAEZ,IAAIA,OAAO,GAAGC,OAAO,CAAC,iBAAiB,CAAC,CAACD,OAAO;AAEhD,IAAIE,OAAO,GAAG,EAAE;AAEhB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;EAAED,OAAO,CAACE,IAAI,CAAC,CAAC,CAAC;AAAE;AAEjD,oCAAoC,CACjCC,KAAK,CAAC,EAAE,CAAC,CAACC,OAAO,CAAC,UAAUC,EAAE,EAAE;EAAEL,OAAO,CAACK,EAAE,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;AAAE,CAAC,CAAC;AAGtEC,MAAM,CAACC,OAAO,GAAG,SAASC,MAAMA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC9C,IAAIN,EAAE;IAAEO,GAAG,GAAGF,KAAK,CAACE,GAAG;IAAEC,GAAG,GAAGH,KAAK,CAACI,MAAM;EAE3C,IAAIJ,KAAK,CAACK,GAAG,CAACT,UAAU,CAACM,GAAG,CAAC,KAAK,IAAI,UAAS;IAAE,OAAO,KAAK;EAAE;EAE/DA,GAAG,EAAE;EAEL,IAAIA,GAAG,GAAGC,GAAG,EAAE;IACbR,EAAE,GAAGK,KAAK,CAACK,GAAG,CAACT,UAAU,CAACM,GAAG,CAAC;IAE9B,IAAIP,EAAE,GAAG,GAAG,IAAIL,OAAO,CAACK,EAAE,CAAC,KAAK,CAAC,EAAE;MACjC,IAAI,CAACM,MAAM,EAAE;QAAED,KAAK,CAACM,OAAO,IAAIN,KAAK,CAACK,GAAG,CAACH,GAAG,CAAC;MAAE;MAChDF,KAAK,CAACE,GAAG,IAAI,CAAC;MACd,OAAO,IAAI;IACb;IAEA,IAAIP,EAAE,KAAK,IAAI,EAAE;MACf,IAAI,CAACM,MAAM,EAAE;QACXD,KAAK,CAACR,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,CAAC;MAClC;MAEAU,GAAG,EAAE;MACL;MACA,OAAOA,GAAG,GAAGC,GAAG,EAAE;QAChBR,EAAE,GAAGK,KAAK,CAACK,GAAG,CAACT,UAAU,CAACM,GAAG,CAAC;QAC9B,IAAI,CAACd,OAAO,CAACO,EAAE,CAAC,EAAE;UAAE;QAAO;QAC3BO,GAAG,EAAE;MACP;MAEAF,KAAK,CAACE,GAAG,GAAGA,GAAG;MACf,OAAO,IAAI;IACb;EACF;EAEA,IAAI,CAACD,MAAM,EAAE;IAAED,KAAK,CAACM,OAAO,IAAI,IAAI;EAAE;EACtCN,KAAK,CAACE,GAAG,EAAE;EACX,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}