{"ast": null, "code": "'use strict';\n\nmodule.exports = function ins_plugin(md) {\n  // Insert each marker as a separate text token, and add it to delimiter list\n  //\n  function tokenize(state, silent) {\n    var i,\n      scanned,\n      token,\n      len,\n      ch,\n      start = state.pos,\n      marker = state.src.charCodeAt(start);\n    if (silent) {\n      return false;\n    }\n    if (marker !== 0x2B /* + */) {\n      return false;\n    }\n    scanned = state.scanDelims(state.pos, true);\n    len = scanned.length;\n    ch = String.fromCharCode(marker);\n    if (len < 2) {\n      return false;\n    }\n    if (len % 2) {\n      token = state.push('text', '', 0);\n      token.content = ch;\n      len--;\n    }\n    for (i = 0; i < len; i += 2) {\n      token = state.push('text', '', 0);\n      token.content = ch + ch;\n      if (!scanned.can_open && !scanned.can_close) {\n        continue;\n      }\n      state.delimiters.push({\n        marker: marker,\n        length: 0,\n        // disable \"rule of 3\" length checks meant for emphasis\n        jump: i / 2,\n        // 1 delimiter = 2 characters\n        token: state.tokens.length - 1,\n        end: -1,\n        open: scanned.can_open,\n        close: scanned.can_close\n      });\n    }\n    state.pos += scanned.length;\n    return true;\n  }\n\n  // Walk through delimiter list and replace text tokens with tags\n  //\n  function postProcess(state, delimiters) {\n    var i,\n      j,\n      startDelim,\n      endDelim,\n      token,\n      loneMarkers = [],\n      max = delimiters.length;\n    for (i = 0; i < max; i++) {\n      startDelim = delimiters[i];\n      if (startDelim.marker !== 0x2B /* + */) {\n        continue;\n      }\n      if (startDelim.end === -1) {\n        continue;\n      }\n      endDelim = delimiters[startDelim.end];\n      token = state.tokens[startDelim.token];\n      token.type = 'ins_open';\n      token.tag = 'ins';\n      token.nesting = 1;\n      token.markup = '++';\n      token.content = '';\n      token = state.tokens[endDelim.token];\n      token.type = 'ins_close';\n      token.tag = 'ins';\n      token.nesting = -1;\n      token.markup = '++';\n      token.content = '';\n      if (state.tokens[endDelim.token - 1].type === 'text' && state.tokens[endDelim.token - 1].content === '+') {\n        loneMarkers.push(endDelim.token - 1);\n      }\n    }\n\n    // If a marker sequence has an odd number of characters, it's splitted\n    // like this: `~~~~~` -> `~` + `~~` + `~~`, leaving one marker at the\n    // start of the sequence.\n    //\n    // So, we have to move all those markers after subsequent s_close tags.\n    //\n    while (loneMarkers.length) {\n      i = loneMarkers.pop();\n      j = i + 1;\n      while (j < state.tokens.length && state.tokens[j].type === 'ins_close') {\n        j++;\n      }\n      j--;\n      if (i !== j) {\n        token = state.tokens[j];\n        state.tokens[j] = state.tokens[i];\n        state.tokens[i] = token;\n      }\n    }\n  }\n  md.inline.ruler.before('emphasis', 'ins', tokenize);\n  md.inline.ruler2.before('emphasis', 'ins', function (state) {\n    var curr,\n      tokens_meta = state.tokens_meta,\n      max = (state.tokens_meta || []).length;\n    postProcess(state, state.delimiters);\n    for (curr = 0; curr < max; curr++) {\n      if (tokens_meta[curr] && tokens_meta[curr].delimiters) {\n        postProcess(state, tokens_meta[curr].delimiters);\n      }\n    }\n  });\n};", "map": {"version": 3, "names": ["module", "exports", "ins_plugin", "md", "tokenize", "state", "silent", "i", "scanned", "token", "len", "ch", "start", "pos", "marker", "src", "charCodeAt", "scanDelims", "length", "String", "fromCharCode", "push", "content", "can_open", "can_close", "delimiters", "jump", "tokens", "end", "open", "close", "postProcess", "j", "start<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "loneMarkers", "max", "type", "tag", "nesting", "markup", "pop", "inline", "ruler", "before", "ruler2", "curr", "tokens_meta"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it-ins@3.0.1/node_modules/markdown-it-ins/index.js"], "sourcesContent": ["'use strict';\n\n\nmodule.exports = function ins_plugin(md) {\n  // Insert each marker as a separate text token, and add it to delimiter list\n  //\n  function tokenize(state, silent) {\n    var i, scanned, token, len, ch,\n        start = state.pos,\n        marker = state.src.charCodeAt(start);\n\n    if (silent) { return false; }\n\n    if (marker !== 0x2B/* + */) { return false; }\n\n    scanned = state.scanDelims(state.pos, true);\n    len = scanned.length;\n    ch = String.fromCharCode(marker);\n\n    if (len < 2) { return false; }\n\n    if (len % 2) {\n      token         = state.push('text', '', 0);\n      token.content = ch;\n      len--;\n    }\n\n    for (i = 0; i < len; i += 2) {\n      token         = state.push('text', '', 0);\n      token.content = ch + ch;\n\n      if (!scanned.can_open && !scanned.can_close) { continue; }\n\n      state.delimiters.push({\n        marker: marker,\n        length: 0,     // disable \"rule of 3\" length checks meant for emphasis\n        jump:   i / 2, // 1 delimiter = 2 characters\n        token:  state.tokens.length - 1,\n        end:    -1,\n        open:   scanned.can_open,\n        close:  scanned.can_close\n      });\n    }\n\n    state.pos += scanned.length;\n\n    return true;\n  }\n\n\n  // Walk through delimiter list and replace text tokens with tags\n  //\n  function postProcess(state, delimiters) {\n    var i, j,\n        startDelim,\n        endDelim,\n        token,\n        loneMarkers = [],\n        max = delimiters.length;\n\n    for (i = 0; i < max; i++) {\n      startDelim = delimiters[i];\n\n      if (startDelim.marker !== 0x2B/* + */) {\n        continue;\n      }\n\n      if (startDelim.end === -1) {\n        continue;\n      }\n\n      endDelim = delimiters[startDelim.end];\n\n      token         = state.tokens[startDelim.token];\n      token.type    = 'ins_open';\n      token.tag     = 'ins';\n      token.nesting = 1;\n      token.markup  = '++';\n      token.content = '';\n\n      token         = state.tokens[endDelim.token];\n      token.type    = 'ins_close';\n      token.tag     = 'ins';\n      token.nesting = -1;\n      token.markup  = '++';\n      token.content = '';\n\n      if (state.tokens[endDelim.token - 1].type === 'text' &&\n          state.tokens[endDelim.token - 1].content === '+') {\n\n        loneMarkers.push(endDelim.token - 1);\n      }\n    }\n\n    // If a marker sequence has an odd number of characters, it's splitted\n    // like this: `~~~~~` -> `~` + `~~` + `~~`, leaving one marker at the\n    // start of the sequence.\n    //\n    // So, we have to move all those markers after subsequent s_close tags.\n    //\n    while (loneMarkers.length) {\n      i = loneMarkers.pop();\n      j = i + 1;\n\n      while (j < state.tokens.length && state.tokens[j].type === 'ins_close') {\n        j++;\n      }\n\n      j--;\n\n      if (i !== j) {\n        token = state.tokens[j];\n        state.tokens[j] = state.tokens[i];\n        state.tokens[i] = token;\n      }\n    }\n  }\n\n  md.inline.ruler.before('emphasis', 'ins', tokenize);\n  md.inline.ruler2.before('emphasis', 'ins', function (state) {\n    var curr,\n        tokens_meta = state.tokens_meta,\n        max = (state.tokens_meta || []).length;\n\n    postProcess(state, state.delimiters);\n\n    for (curr = 0; curr < max; curr++) {\n      if (tokens_meta[curr] && tokens_meta[curr].delimiters) {\n        postProcess(state, tokens_meta[curr].delimiters);\n      }\n    }\n  });\n};\n"], "mappings": "AAAA,YAAY;;AAGZA,MAAM,CAACC,OAAO,GAAG,SAASC,UAAUA,CAACC,EAAE,EAAE;EACvC;EACA;EACA,SAASC,QAAQA,CAACC,KAAK,EAAEC,MAAM,EAAE;IAC/B,IAAIC,CAAC;MAAEC,OAAO;MAAEC,KAAK;MAAEC,GAAG;MAAEC,EAAE;MAC1BC,KAAK,GAAGP,KAAK,CAACQ,GAAG;MACjBC,MAAM,GAAGT,KAAK,CAACU,GAAG,CAACC,UAAU,CAACJ,KAAK,CAAC;IAExC,IAAIN,MAAM,EAAE;MAAE,OAAO,KAAK;IAAE;IAE5B,IAAIQ,MAAM,KAAK,IAAI,UAAS;MAAE,OAAO,KAAK;IAAE;IAE5CN,OAAO,GAAGH,KAAK,CAACY,UAAU,CAACZ,KAAK,CAACQ,GAAG,EAAE,IAAI,CAAC;IAC3CH,GAAG,GAAGF,OAAO,CAACU,MAAM;IACpBP,EAAE,GAAGQ,MAAM,CAACC,YAAY,CAACN,MAAM,CAAC;IAEhC,IAAIJ,GAAG,GAAG,CAAC,EAAE;MAAE,OAAO,KAAK;IAAE;IAE7B,IAAIA,GAAG,GAAG,CAAC,EAAE;MACXD,KAAK,GAAWJ,KAAK,CAACgB,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;MACzCZ,KAAK,CAACa,OAAO,GAAGX,EAAE;MAClBD,GAAG,EAAE;IACP;IAEA,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGG,GAAG,EAAEH,CAAC,IAAI,CAAC,EAAE;MAC3BE,KAAK,GAAWJ,KAAK,CAACgB,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;MACzCZ,KAAK,CAACa,OAAO,GAAGX,EAAE,GAAGA,EAAE;MAEvB,IAAI,CAACH,OAAO,CAACe,QAAQ,IAAI,CAACf,OAAO,CAACgB,SAAS,EAAE;QAAE;MAAU;MAEzDnB,KAAK,CAACoB,UAAU,CAACJ,IAAI,CAAC;QACpBP,MAAM,EAAEA,MAAM;QACdI,MAAM,EAAE,CAAC;QAAM;QACfQ,IAAI,EAAInB,CAAC,GAAG,CAAC;QAAE;QACfE,KAAK,EAAGJ,KAAK,CAACsB,MAAM,CAACT,MAAM,GAAG,CAAC;QAC/BU,GAAG,EAAK,CAAC,CAAC;QACVC,IAAI,EAAIrB,OAAO,CAACe,QAAQ;QACxBO,KAAK,EAAGtB,OAAO,CAACgB;MAClB,CAAC,CAAC;IACJ;IAEAnB,KAAK,CAACQ,GAAG,IAAIL,OAAO,CAACU,MAAM;IAE3B,OAAO,IAAI;EACb;;EAGA;EACA;EACA,SAASa,WAAWA,CAAC1B,KAAK,EAAEoB,UAAU,EAAE;IACtC,IAAIlB,CAAC;MAAEyB,CAAC;MACJC,UAAU;MACVC,QAAQ;MACRzB,KAAK;MACL0B,WAAW,GAAG,EAAE;MAChBC,GAAG,GAAGX,UAAU,CAACP,MAAM;IAE3B,KAAKX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6B,GAAG,EAAE7B,CAAC,EAAE,EAAE;MACxB0B,UAAU,GAAGR,UAAU,CAAClB,CAAC,CAAC;MAE1B,IAAI0B,UAAU,CAACnB,MAAM,KAAK,IAAI,UAAS;QACrC;MACF;MAEA,IAAImB,UAAU,CAACL,GAAG,KAAK,CAAC,CAAC,EAAE;QACzB;MACF;MAEAM,QAAQ,GAAGT,UAAU,CAACQ,UAAU,CAACL,GAAG,CAAC;MAErCnB,KAAK,GAAWJ,KAAK,CAACsB,MAAM,CAACM,UAAU,CAACxB,KAAK,CAAC;MAC9CA,KAAK,CAAC4B,IAAI,GAAM,UAAU;MAC1B5B,KAAK,CAAC6B,GAAG,GAAO,KAAK;MACrB7B,KAAK,CAAC8B,OAAO,GAAG,CAAC;MACjB9B,KAAK,CAAC+B,MAAM,GAAI,IAAI;MACpB/B,KAAK,CAACa,OAAO,GAAG,EAAE;MAElBb,KAAK,GAAWJ,KAAK,CAACsB,MAAM,CAACO,QAAQ,CAACzB,KAAK,CAAC;MAC5CA,KAAK,CAAC4B,IAAI,GAAM,WAAW;MAC3B5B,KAAK,CAAC6B,GAAG,GAAO,KAAK;MACrB7B,KAAK,CAAC8B,OAAO,GAAG,CAAC,CAAC;MAClB9B,KAAK,CAAC+B,MAAM,GAAI,IAAI;MACpB/B,KAAK,CAACa,OAAO,GAAG,EAAE;MAElB,IAAIjB,KAAK,CAACsB,MAAM,CAACO,QAAQ,CAACzB,KAAK,GAAG,CAAC,CAAC,CAAC4B,IAAI,KAAK,MAAM,IAChDhC,KAAK,CAACsB,MAAM,CAACO,QAAQ,CAACzB,KAAK,GAAG,CAAC,CAAC,CAACa,OAAO,KAAK,GAAG,EAAE;QAEpDa,WAAW,CAACd,IAAI,CAACa,QAAQ,CAACzB,KAAK,GAAG,CAAC,CAAC;MACtC;IACF;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA,OAAO0B,WAAW,CAACjB,MAAM,EAAE;MACzBX,CAAC,GAAG4B,WAAW,CAACM,GAAG,CAAC,CAAC;MACrBT,CAAC,GAAGzB,CAAC,GAAG,CAAC;MAET,OAAOyB,CAAC,GAAG3B,KAAK,CAACsB,MAAM,CAACT,MAAM,IAAIb,KAAK,CAACsB,MAAM,CAACK,CAAC,CAAC,CAACK,IAAI,KAAK,WAAW,EAAE;QACtEL,CAAC,EAAE;MACL;MAEAA,CAAC,EAAE;MAEH,IAAIzB,CAAC,KAAKyB,CAAC,EAAE;QACXvB,KAAK,GAAGJ,KAAK,CAACsB,MAAM,CAACK,CAAC,CAAC;QACvB3B,KAAK,CAACsB,MAAM,CAACK,CAAC,CAAC,GAAG3B,KAAK,CAACsB,MAAM,CAACpB,CAAC,CAAC;QACjCF,KAAK,CAACsB,MAAM,CAACpB,CAAC,CAAC,GAAGE,KAAK;MACzB;IACF;EACF;EAEAN,EAAE,CAACuC,MAAM,CAACC,KAAK,CAACC,MAAM,CAAC,UAAU,EAAE,KAAK,EAAExC,QAAQ,CAAC;EACnDD,EAAE,CAACuC,MAAM,CAACG,MAAM,CAACD,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,UAAUvC,KAAK,EAAE;IAC1D,IAAIyC,IAAI;MACJC,WAAW,GAAG1C,KAAK,CAAC0C,WAAW;MAC/BX,GAAG,GAAG,CAAC/B,KAAK,CAAC0C,WAAW,IAAI,EAAE,EAAE7B,MAAM;IAE1Ca,WAAW,CAAC1B,KAAK,EAAEA,KAAK,CAACoB,UAAU,CAAC;IAEpC,KAAKqB,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGV,GAAG,EAAEU,IAAI,EAAE,EAAE;MACjC,IAAIC,WAAW,CAACD,IAAI,CAAC,IAAIC,WAAW,CAACD,IAAI,CAAC,CAACrB,UAAU,EAAE;QACrDM,WAAW,CAAC1B,KAAK,EAAE0C,WAAW,CAACD,IAAI,CAAC,CAACrB,UAAU,CAAC;MAClD;IACF;EACF,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}