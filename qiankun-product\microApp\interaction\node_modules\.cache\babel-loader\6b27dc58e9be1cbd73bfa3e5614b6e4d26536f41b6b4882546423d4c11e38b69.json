{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, Fragment as _Fragment, createTextVNode as _createTextVNode, createBlock as _createBlock, normalizeClass as _normalizeClass, vShow as _vShow, withDirectives as _withDirectives, Transition as _Transition } from \"vue\";\nvar _hoisted_1 = {\n  class: \"LiveBroadcastDetails\"\n};\nvar _hoisted_2 = {\n  class: \"LiveBroadcastDetailsHeader\"\n};\nvar _hoisted_3 = [\"src\"];\nvar _hoisted_4 = {\n  class: \"LiveBroadcastDetailsHeadInfo\"\n};\nvar _hoisted_5 = {\n  class: \"LiveBroadcastDetailsTitle\"\n};\nvar _hoisted_6 = {\n  class: \"LiveBroadcastDetailsTime\"\n};\nvar _hoisted_7 = {\n  class: \"LiveBroadcastDetailsBody\"\n};\nvar _hoisted_8 = {\n  class: \"LiveBroadcastDetailsCanvas\"\n};\nvar _hoisted_9 = {\n  key: 0,\n  class: \"LiveBroadcastDetailsPoster\"\n};\nvar _hoisted_10 = [\"src\"];\nvar _hoisted_11 = {\n  class: \"LiveBroadcastDetailsCountdown\"\n};\nvar _hoisted_12 = {\n  class: \"countdown-line1\"\n};\nvar _hoisted_13 = {\n  class: \"countdown-line2\"\n};\nvar _hoisted_14 = {\n  class: \"LiveBroadcastDetailsLiveOverlay\"\n};\nvar _hoisted_15 = {\n  ref: \"videoContainer\",\n  class: \"video-container\"\n};\nvar _hoisted_16 = {\n  class: \"LiveBroadcastDetailsEnded\"\n};\nvar _hoisted_17 = {\n  class: \"LiveBroadcastDetailsEndedWrap\"\n};\nvar _hoisted_18 = {\n  class: \"LiveBroadcastDetailsSidebar\"\n};\nvar _hoisted_19 = {\n  class: \"LiveBroadcastDetailsTabs\"\n};\nvar _hoisted_20 = {\n  key: 0,\n  class: \"LiveBroadcastDetailsTabPane\"\n};\nvar _hoisted_21 = {\n  class: \"detailsTitle\"\n};\nvar _hoisted_22 = {\n  class: \"detailsTime\"\n};\nvar _hoisted_23 = {\n  class: \"detailsDesc\"\n};\nvar _hoisted_24 = {\n  key: 1,\n  class: \"LiveBroadcastDetailsTabPane\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createBlock(_Transition, {\n    name: \"details-fade\",\n    persisted: \"\"\n  }, {\n    default: _withCtx(function () {\n      var _$setup$details, _$setup$details2, _$setup$details3;\n      return [_withDirectives(_createElementVNode(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [$setup.logoSrc ? (_openBlock(), _createElementBlock(\"img\", {\n        key: 0,\n        class: \"LiveBroadcastDetailsEmblem\",\n        src: $setup.logoSrc,\n        alt: \"emblem\"\n      }, null, 8 /* PROPS */, _hoisted_3)) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_4, [_createElementVNode(\"div\", _hoisted_5, _toDisplayString($setup.details.theme), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_6, \"直播时间：\" + _toDisplayString($setup.format($setup.details.startTime)) + \" 到 \" + _toDisplayString($setup.format($setup.details.endTime)), 1 /* TEXT */)]), _createVNode(_component_el_icon, {\n        class: \"LiveBroadcastDetailsClose\",\n        onClick: $setup.handleClose\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode($setup[\"Close\"])];\n        }),\n        _: 1 /* STABLE */\n      })]), _createElementVNode(\"div\", _hoisted_7, [_createElementVNode(\"div\", _hoisted_8, [_createCommentVNode(\" 未开始：显示封面图 + 倒计时 \"), $setup.details.meetingStatus === '未开始' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [_createElementVNode(\"img\", {\n        src: $setup.imgUrl,\n        alt: \"海报/播放画面区域\",\n        style: {\n          \"width\": \"100%\",\n          \"height\": \"100%\",\n          \"object-fit\": \"contain\"\n        }\n      }, null, 8 /* PROPS */, _hoisted_10), _createElementVNode(\"div\", _hoisted_11, [_createElementVNode(\"div\", _hoisted_12, \"距离 \" + _toDisplayString($setup.format($setup.details.startTime, 'MM/DD HH:mm')) + \" 直播开始还有\", 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_13, _toDisplayString($setup.countdownTimeOnly), 1 /* TEXT */)])])) : $setup.details.meetingStatus === '进行中' ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 1\n      }, [_createCommentVNode(\" 进行中：直播播放器 \"), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", _hoisted_15, null, 512 /* NEED_PATCH */)])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : $setup.details.meetingStatus === '已结束' ? (_openBlock(), _createElementBlock(_Fragment, {\n        key: 2\n      }, [_createCommentVNode(\" 已结束：遮罩 + 回放按钮 \"), _createElementVNode(\"div\", _hoisted_16, [_createElementVNode(\"div\", _hoisted_17, [_cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n        class: \"endedTitle\"\n      }, \"直播已结束\", -1 /* HOISTED */)), $setup.details.isReplay == 1 ? (_openBlock(), _createBlock(_component_el_button, {\n        key: 0,\n        type: \"primary\",\n        class: \"replayBtn\"\n      }, {\n        default: _withCtx(function () {\n          return _cache[2] || (_cache[2] = [_createTextVNode(\"观看回放\")]);\n        }),\n        _: 1 /* STABLE */\n      })) : _createCommentVNode(\"v-if\", true)])])], 2112 /* STABLE_FRAGMENT, DEV_ROOT_FRAGMENT */)) : _createCommentVNode(\"v-if\", true)]), _createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_createElementVNode(\"div\", {\n        class: _normalizeClass(['LiveBroadcastDetailsTab', {\n          active: $setup.activeTab === 'details'\n        }]),\n        onClick: _cache[0] || (_cache[0] = function ($event) {\n          return $setup.activeTab = 'details';\n        })\n      }, \" 直播详情\", 2 /* CLASS */), _createElementVNode(\"div\", {\n        class: _normalizeClass(['LiveBroadcastDetailsTab', {\n          active: $setup.activeTab === 'interact'\n        }]),\n        onClick: _cache[1] || (_cache[1] = function ($event) {\n          return $setup.activeTab = 'interact';\n        })\n      }, \" 互动\", 2 /* CLASS */)]), $setup.activeTab === 'details' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_createElementVNode(\"div\", _hoisted_21, _toDisplayString(((_$setup$details = $setup.details) === null || _$setup$details === void 0 ? void 0 : _$setup$details.theme) || '-'), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_22, \"时间：\" + _toDisplayString($setup.format((_$setup$details2 = $setup.details) === null || _$setup$details2 === void 0 ? void 0 : _$setup$details2.startTime)) + \" - \" + _toDisplayString($setup.format((_$setup$details3 = $setup.details) === null || _$setup$details3 === void 0 ? void 0 : _$setup$details3.endTime)), 1 /* TEXT */), _createElementVNode(\"div\", _hoisted_23, _toDisplayString($setup.details.liveDescribes || '暂无简介'), 1 /* TEXT */)])) : (_openBlock(), _createElementBlock(\"div\", _hoisted_24, _cache[4] || (_cache[4] = [_createElementVNode(\"div\", {\n        class: \"LiveBroadcastDetailsPanelTitle\"\n      }, \"互动\", -1 /* HOISTED */), _createElementVNode(\"div\", {\n        class: \"LiveBroadcastDetailsPanelText\"\n      }, \"暂无互动内容\", -1 /* HOISTED */)])))])])], 512 /* NEED_PATCH */), [[_vShow, $props.modelValue]])];\n    }),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["class", "key", "ref", "_createBlock", "_Transition", "name", "persisted", "default", "_withCtx", "_$setup$details", "_$setup$details2", "_$setup$details3", "_createElementVNode", "_hoisted_1", "_hoisted_2", "$setup", "logoSrc", "_createElementBlock", "src", "alt", "_hoisted_3", "_createCommentVNode", "_hoisted_4", "_hoisted_5", "_toDisplayString", "details", "theme", "_hoisted_6", "format", "startTime", "endTime", "_createVNode", "_component_el_icon", "onClick", "handleClose", "_", "_hoisted_7", "_hoisted_8", "meetingStatus", "_hoisted_9", "imgUrl", "style", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "countdownTimeOnly", "_Fragment", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "isReplay", "_component_el_button", "type", "_cache", "_createTextVNode", "_hoisted_18", "_hoisted_19", "_normalizeClass", "active", "activeTab", "$event", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "liveDescribes", "_hoisted_24", "$props", "modelValue"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\microApp\\interaction\\src\\views\\LiveManagement\\LiveBroadcastDetails.vue"], "sourcesContent": ["<template>\r\n  <transition name=\"details-fade\">\r\n    <div v-show=\"modelValue\" class=\"LiveBroadcastDetails\">\r\n      <div class=\"LiveBroadcastDetailsHeader\">\r\n        <img v-if=\"logoSrc\" class=\"LiveBroadcastDetailsEmblem\" :src=\"logoSrc\" alt=\"emblem\" />\r\n        <div class=\"LiveBroadcastDetailsHeadInfo\">\r\n          <div class=\"LiveBroadcastDetailsTitle\">{{ details.theme }}</div>\r\n          <div class=\"LiveBroadcastDetailsTime\">直播时间：{{ format(details.startTime) }} 到 {{\r\n            format(details.endTime) }}</div>\r\n        </div>\r\n        <el-icon class=\"LiveBroadcastDetailsClose\" @click=\"handleClose\">\r\n          <Close />\r\n        </el-icon>\r\n      </div>\r\n      <div class=\"LiveBroadcastDetailsBody\">\r\n        <div class=\"LiveBroadcastDetailsCanvas\">\r\n          <!-- 未开始：显示封面图 + 倒计时 -->\r\n          <div v-if=\"details.meetingStatus === '未开始'\" class=\"LiveBroadcastDetailsPoster\">\r\n            <img :src=\"imgUrl\" alt=\"海报/播放画面区域\" style=\"width: 100%;height: 100%;object-fit: contain;\">\r\n            <div class=\"LiveBroadcastDetailsCountdown\">\r\n              <div class=\"countdown-line1\">距离 {{ format(details.startTime, 'MM/DD HH:mm') }} 直播开始还有</div>\r\n              <div class=\"countdown-line2\">{{ countdownTimeOnly }}</div>\r\n            </div>\r\n          </div>\r\n          <!-- 进行中：直播播放器 -->\r\n          <div v-else-if=\"details.meetingStatus === '进行中'\" class=\"LiveBroadcastDetailsLiveOverlay\">\r\n            <div ref=\"videoContainer\" class=\"video-container\"></div>\r\n          </div>\r\n          <!-- 已结束：遮罩 + 回放按钮 -->\r\n          <div v-else-if=\"details.meetingStatus === '已结束'\" class=\"LiveBroadcastDetailsEnded\">\r\n            <div class=\"LiveBroadcastDetailsEndedWrap\">\r\n              <div class=\"endedTitle\">直播已结束</div>\r\n              <el-button type=\"primary\" class=\"replayBtn\" v-if=\"details.isReplay == 1\">观看回放</el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"LiveBroadcastDetailsSidebar\">\r\n          <div class=\"LiveBroadcastDetailsTabs\">\r\n            <div :class=\"['LiveBroadcastDetailsTab', { active: activeTab === 'details' }]\"\r\n              @click=\"activeTab = 'details'\">\r\n              直播详情</div>\r\n            <div :class=\"['LiveBroadcastDetailsTab', { active: activeTab === 'interact' }]\"\r\n              @click=\"activeTab = 'interact'\">\r\n              互动</div>\r\n          </div>\r\n          <div class=\"LiveBroadcastDetailsTabPane\" v-if=\"activeTab === 'details'\">\r\n            <div class=\"detailsTitle\">{{ details?.theme || '-' }}</div>\r\n            <div class=\"detailsTime\">时间：{{ format(details?.startTime) }} - {{ format(details?.endTime) }}</div>\r\n            <div class=\"detailsDesc\">{{ details.liveDescribes || '暂无简介' }}</div>\r\n          </div>\r\n          <div class=\"LiveBroadcastDetailsTabPane\" v-else>\r\n            <div class=\"LiveBroadcastDetailsPanelTitle\">互动</div>\r\n            <div class=\"LiveBroadcastDetailsPanelText\">暂无互动内容</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </transition>\r\n\r\n</template>\r\n<script>\r\nexport default { name: 'LiveBroadcastDetails' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onBeforeUnmount, watch, onMounted, computed, nextTick } from 'vue'\r\nimport { format } from 'common/js/time.js'\r\nimport { Close } from '@element-plus/icons-vue'\r\nimport Hls from 'hls.js'\r\n// 使用 hls.js 播放 HLS 流\r\nconst props = defineProps({\r\n  modelValue: { type: Boolean, default: false },\r\n  id: { type: String, default: '' }\r\n})\r\nconst emit = defineEmits(['callback', 'update:modelValue'])\r\nconst details = ref({})\r\nconst logoSrc = ref('https://xazx.cszysoft.com:8131/lzt/pageImg/open/logo?areaId=610100')\r\n\r\nconst activeTab = ref('details')\r\nconst imgUrl = computed(() => details.value.coverImg ? api.fileURL(details.value.coverImg) : '')\r\nconst countdownText = ref('')\r\nconst countdownTimeOnly = ref('')\r\n\r\n// 视频播放器相关\r\nconst videoContainer = ref(null)\r\nconst player = ref(null)\r\nconst hls = ref(null)\r\nconst isPlayerInitialized = ref(false)\r\n\r\nonMounted(() => {\r\n  // 在 onMounted 中，如果组件已经显示且有 id，则加载数据\r\n  if (props.modelValue && props.id) {\r\n    getInfo()\r\n  }\r\n})\r\n\r\n// 监听 modelValue 变化 - 当组件从隐藏变为显示时加载数据\r\nwatch(() => props.modelValue, (newVal, oldVal) => {\r\n  if (newVal && props.id && !oldVal) {\r\n    // 只有从 false 变为 true 且有 id 时才加载数据\r\n    getInfo()\r\n  }\r\n})\r\n\r\n// 监听 id 变化 - 当 id 改变且组件显示时加载数据\r\nwatch(() => props.id, (newVal, oldVal) => {\r\n  if (newVal && props.modelValue && newVal !== oldVal) {\r\n    getInfo()\r\n  }\r\n})\r\n\r\nconst getInfo = async () => {\r\n  const res = await api.videoConnectionInfo({ detailId: props.id })\r\n  var { data } = res\r\n  details.value = data\r\n  applyStatusFromProps()\r\n}\r\n\r\n// 监听状态变化\r\nwatch(() => details.value.meetingStatus, (newStatus, oldStatus) => {\r\n  if (newStatus === '进行中' && oldStatus !== '进行中') {\r\n    // 从未开始变为进行中，初始化播放器\r\n    nextTick(() => {\r\n      initVideoPlayer()\r\n    })\r\n  } else if (newStatus !== '进行中' && oldStatus === '进行中') {\r\n    // 从进行中变为其他状态，销毁播放器\r\n    destroyVideoPlayer()\r\n  }\r\n})\r\n\r\nconst tickCountdown = () => {\r\n  if (!details.value.startTime) return\r\n  const start = new Date(details.value.startTime).getTime()\r\n  const now = Date.now()\r\n  let diff = Math.max(0, start - now)\r\n\r\n  const sec = Math.floor(diff / 1000) % 60\r\n  const min = Math.floor(diff / (1000 * 60)) % 60\r\n  const hour = Math.floor(diff / (1000 * 60 * 60)) % 24\r\n  const day = Math.floor(diff / (1000 * 60 * 60 * 24))\r\n\r\n  let timeText = ''\r\n\r\n  if (day > 0) {\r\n    // 大于1天：显示 X天X时X分\r\n    timeText = `${day} 天 ${hour} 时 ${min} 分`\r\n  } else if (hour > 0) {\r\n    // 大于1小时但小于1天：显示 X时X分X秒\r\n    timeText = `${hour} 时 ${min} 分 ${sec} 秒`\r\n  } else if (min > 0) {\r\n    // 大于1分钟但小于1小时：显示 X分X秒\r\n    timeText = `${min} 分 ${sec} 秒`\r\n  } else {\r\n    // 小于1分钟：显示 X秒\r\n    timeText = `${sec} 秒`\r\n  }\r\n\r\n  // 设置完整的倒计时文本（保留原有逻辑）\r\n  countdownText.value = `距离 ${format(details.value.startTime, 'MM/DD HH:mm')} 直播开始还有 ${timeText}`\r\n  // 设置只包含时间的文本（用于两行显示）\r\n  countdownTimeOnly.value = timeText\r\n}\r\n\r\nlet timer = null\r\nconst applyStatusFromProps = () => {\r\n  if (timer) clearInterval(timer)\r\n  if (details.value.meetingStatus === '未开始') {\r\n    tickCountdown()\r\n    timer = setInterval(tickCountdown, 1000)\r\n  } else if (details.value.meetingStatus === '进行中') {\r\n    // 初始化播放器\r\n    nextTick(() => {\r\n      initVideoPlayer()\r\n    })\r\n  }\r\n}\r\n\r\n// 初始化视频播放器\r\nconst initVideoPlayer = async () => {\r\n  if (isPlayerInitialized.value || !videoContainer.value) return\r\n\r\n  console.log('初始化视频播放器')\r\n  console.log('推流地址:', details.value.liveUrl)\r\n\r\n  // 销毁现有播放器\r\n  destroyVideoPlayer()\r\n\r\n  // 创建原生 video 元素\r\n  const video = document.createElement('video')\r\n  video.className = 'live-video-player'\r\n  video.controls = true\r\n  video.autoplay = true\r\n  video.preload = 'auto'\r\n  video.playsInline = true\r\n  video.webkitPlaysinline = true\r\n  video.style.width = '100%'\r\n  video.style.height = '100%'\r\n  video.style.objectFit = 'contain'\r\n\r\n  // 添加到容器\r\n  videoContainer.value.appendChild(video)\r\n  player.value = video\r\n  isPlayerInitialized.value = true\r\n\r\n  // 设置直播流地址（使用HLS格式）\r\n  if (details.value.liveUrl) {\r\n    const hlsUrl = getHlsUrl(details.value.liveUrl)\r\n    console.log('解析后的HLS地址:', hlsUrl)\r\n\r\n    if (hlsUrl) {\r\n      // 测试HLS地址可访问性\r\n      const isAccessible = await testHlsUrl(hlsUrl)\r\n      if (!isAccessible) {\r\n        console.error('HLS地址无法访问，可能是CORS问题或地址无效')\r\n        alert('推流地址无法访问，请检查网络连接或联系管理员')\r\n        return\r\n      }\r\n\r\n      // 检查浏览器是否支持HLS\r\n      if (Hls.isSupported()) {\r\n        // 使用 hls.js 播放\r\n        hls.value = new Hls({\r\n          debug: false,\r\n          enableWorker: true,\r\n          lowLatencyMode: true,\r\n          backBufferLength: 90\r\n        })\r\n\r\n        hls.value.loadSource(hlsUrl)\r\n        hls.value.attachMedia(video)\r\n\r\n        hls.value.on(Hls.Events.MANIFEST_PARSED, () => {\r\n          console.log('HLS清单解析完成，开始播放')\r\n          video.play().catch(e => {\r\n            console.error('自动播放失败:', e)\r\n          })\r\n        })\r\n\r\n        hls.value.on(Hls.Events.ERROR, (event, data) => {\r\n          console.error('HLS错误:', data)\r\n          if (data.fatal) {\r\n            switch (data.type) {\r\n              case Hls.ErrorTypes.NETWORK_ERROR:\r\n                console.error('网络错误，尝试恢复...')\r\n                hls.value.startLoad()\r\n                break\r\n              case Hls.ErrorTypes.MEDIA_ERROR:\r\n                console.error('媒体错误，尝试恢复...')\r\n                hls.value.recoverMediaError()\r\n                break\r\n              default:\r\n                console.error('致命错误，无法恢复')\r\n                break\r\n            }\r\n          }\r\n        })\r\n      } else if (video.canPlayType('application/vnd.apple.mpegurl')) {\r\n        // 原生支持HLS（Safari）\r\n        video.src = hlsUrl\r\n        video.addEventListener('loadedmetadata', () => {\r\n          console.log('原生HLS支持，开始播放')\r\n          video.play().catch(e => {\r\n            console.error('自动播放失败:', e)\r\n          })\r\n        })\r\n      } else {\r\n        console.error('浏览器不支持HLS播放')\r\n        alert('您的浏览器不支持HLS播放，请使用Chrome、Firefox或Safari')\r\n      }\r\n    }\r\n  } else {\r\n    console.warn('没有推流地址')\r\n  }\r\n\r\n  // 添加错误处理\r\n  video.addEventListener('error', (error) => {\r\n    console.error('视频播放错误:', error)\r\n  })\r\n\r\n  // 添加加载事件\r\n  video.addEventListener('loadeddata', () => {\r\n    console.log('视频数据加载完成')\r\n  })\r\n\r\n  // 添加自定义控制按钮\r\n  addCustomControls(video)\r\n}\r\n\r\n// 销毁视频播放器\r\nconst destroyVideoPlayer = () => {\r\n  // 销毁 hls.js 实例\r\n  if (hls.value) {\r\n    try {\r\n      hls.value.destroy()\r\n    } catch (error) {\r\n      console.error('销毁HLS实例错误:', error)\r\n    }\r\n    hls.value = null\r\n  }\r\n\r\n  if (player.value) {\r\n    try {\r\n      player.value.pause()\r\n      player.value.src = ''\r\n      player.value.load()\r\n      if (videoContainer.value) {\r\n        videoContainer.value.innerHTML = ''\r\n      }\r\n    } catch (error) {\r\n      console.error('销毁播放器错误:', error)\r\n    }\r\n    player.value = null\r\n    isPlayerInitialized.value = false\r\n  }\r\n}\r\n\r\n// 从推流地址中获取HLS地址\r\nconst getHlsUrl = (liveUrl) => {\r\n  console.log('原始推流地址:', liveUrl)\r\n\r\n  // 如果liveUrl是JSON格式，解析出HLS地址\r\n  try {\r\n    const urlData = JSON.parse(liveUrl)\r\n    console.log('解析的JSON数据:', urlData)\r\n    const hlsUrl = urlData.hls || liveUrl\r\n    console.log('提取的HLS地址:', hlsUrl)\r\n    return hlsUrl\r\n  } catch (error) {\r\n    console.log('不是JSON格式，直接使用原地址')\r\n    // 如果不是JSON格式，直接返回原地址\r\n    return liveUrl\r\n  }\r\n}\r\n\r\n// 测试HLS地址是否可访问\r\nconst testHlsUrl = async (url) => {\r\n  try {\r\n    console.log('测试HLS地址可访问性:', url)\r\n    const response = await fetch(url, {\r\n      method: 'HEAD',\r\n      mode: 'cors'\r\n    })\r\n    console.log('HLS地址响应状态:', response.status)\r\n    return response.ok\r\n  } catch (error) {\r\n    console.error('HLS地址测试失败:', error)\r\n    return false\r\n  }\r\n}\r\n\r\n// 示例推流地址解析\r\n// 如果您的推流地址是字符串格式，可以这样处理：\r\n// const sampleUrl = {\r\n//   \"flv\": \"http://prdpulllive.xylink.com/prdnemo/9681c99c9088f6520198def4ad835a39.flv?auth_key=...\",\r\n//   \"hls\": \"http://prdpulllive.xylink.com/prdnemo/9681c99c9088f6520198def4ad835a39.m3u8?auth_key=...\",\r\n//   \"rtmp\": \"rtmp://prdpulllive.xylink.com/prdnemo/9681c99c9088f6520198def4ad835a39?auth_key=...\"\r\n// }\r\n\r\n// 添加自定义控制按钮\r\nconst addCustomControls = (video) => {\r\n  // 创建自定义控制栏\r\n  const controlsContainer = document.createElement('div')\r\n  controlsContainer.className = 'custom-video-controls'\r\n  controlsContainer.style.cssText = `\r\n    position: absolute;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    background: rgba(0, 0, 0, 0.7);\r\n    padding: 10px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    color: white;\r\n    font-size: 14px;\r\n  `\r\n\r\n  // 左侧控制按钮\r\n  const leftControls = document.createElement('div')\r\n  leftControls.style.display = 'flex'\r\n  leftControls.style.alignItems = 'center'\r\n  leftControls.style.gap = '15px'\r\n\r\n  // 播放/暂停按钮\r\n  const playBtn = document.createElement('button')\r\n  playBtn.innerHTML = '▶'\r\n  playBtn.style.cssText = 'background: none; border: none; color: white; cursor: pointer; font-size: 16px;'\r\n  playBtn.onclick = () => {\r\n    if (video.paused) {\r\n      video.play()\r\n      playBtn.innerHTML = '⏸'\r\n    } else {\r\n      video.pause()\r\n      playBtn.innerHTML = '▶'\r\n    }\r\n  }\r\n\r\n  // 刷新按钮\r\n  const refreshBtn = document.createElement('button')\r\n  refreshBtn.innerHTML = '🔄'\r\n  refreshBtn.style.cssText = 'background: none; border: none; color: white; cursor: pointer; font-size: 16px;'\r\n  refreshBtn.onclick = () => {\r\n    video.currentTime = 0\r\n    video.play()\r\n  }\r\n\r\n  leftControls.appendChild(playBtn)\r\n  leftControls.appendChild(refreshBtn)\r\n\r\n  // 右侧控制按钮\r\n  const rightControls = document.createElement('div')\r\n  rightControls.style.display = 'flex'\r\n  rightControls.style.alignItems = 'center'\r\n  rightControls.style.gap = '15px'\r\n\r\n  // 高清按钮\r\n  const hdBtn = document.createElement('button')\r\n  hdBtn.innerHTML = '高清'\r\n  hdBtn.style.cssText = 'background: none; border: none; color: white; cursor: pointer; font-size: 14px;'\r\n  hdBtn.onclick = () => {\r\n    console.log('高清设置')\r\n  }\r\n\r\n  // 声音按钮\r\n  const volumeBtn = document.createElement('button')\r\n  volumeBtn.innerHTML = '🔊'\r\n  volumeBtn.style.cssText = 'background: none; border: none; color: white; cursor: pointer; font-size: 16px;'\r\n  volumeBtn.onclick = () => {\r\n    video.muted = !video.muted\r\n    volumeBtn.innerHTML = video.muted ? '🔇' : '🔊'\r\n  }\r\n\r\n  // 弹幕按钮\r\n  const danmakuBtn = document.createElement('button')\r\n  danmakuBtn.innerHTML = '弹幕'\r\n  danmakuBtn.style.cssText = 'background: none; border: none; color: white; cursor: pointer; font-size: 14px;'\r\n  danmakuBtn.onclick = () => {\r\n    console.log('弹幕功能')\r\n  }\r\n\r\n  // 全屏按钮\r\n  const fullscreenBtn = document.createElement('button')\r\n  fullscreenBtn.innerHTML = '⛶'\r\n  fullscreenBtn.style.cssText = 'background: none; border: none; color: white; cursor: pointer; font-size: 16px;'\r\n  fullscreenBtn.onclick = () => {\r\n    if (document.fullscreenElement) {\r\n      document.exitFullscreen()\r\n    } else {\r\n      videoContainer.value.requestFullscreen()\r\n    }\r\n  }\r\n\r\n  rightControls.appendChild(hdBtn)\r\n  rightControls.appendChild(volumeBtn)\r\n  rightControls.appendChild(danmakuBtn)\r\n  rightControls.appendChild(fullscreenBtn)\r\n\r\n  controlsContainer.appendChild(leftControls)\r\n  controlsContainer.appendChild(rightControls)\r\n\r\n  // 添加到视频容器\r\n  videoContainer.value.appendChild(controlsContainer)\r\n\r\n  // 隐藏原生控制栏\r\n  video.controls = false\r\n}\r\nonBeforeUnmount(() => {\r\n  if (timer) clearInterval(timer)\r\n  destroyVideoPlayer()\r\n})\r\nconst handleClose = () => {\r\n  emit('update:modelValue', false)\r\n  emit('callback')\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.LiveBroadcastDetails {\r\n  position: fixed;\r\n  inset: 0;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  background: #0F0F0F;\r\n  display: flex;\r\n  flex-direction: column;\r\n  z-index: 9999;\r\n\r\n  .LiveBroadcastDetailsHeader {\r\n    position: relative;\r\n    height: 80px;\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 0 16px;\r\n    color: #fff;\r\n    background: #2B2B2B;\r\n    border-bottom: 1px solid rgba(255, 255, 255, 0.08);\r\n\r\n    .LiveBroadcastDetailsEmblem {\r\n      width: 58px;\r\n      height: 58px;\r\n      margin-right: 14px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsHeadInfo {\r\n      display: flex;\r\n      flex-direction: column;\r\n    }\r\n\r\n    .LiveBroadcastDetailsTitle {\r\n      font-family: Microsoft YaHei, Microsoft YaHei;\r\n      font-weight: bold;\r\n      font-size: 20px;\r\n      color: #FFFFFF;\r\n      margin-bottom: 12px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsTime {\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #D9D9D9;\r\n    }\r\n\r\n    .LiveBroadcastDetailsClose {\r\n      position: absolute;\r\n      right: 16px;\r\n      top: 50%;\r\n      transform: translateY(-50%);\r\n      color: #fff;\r\n      cursor: pointer;\r\n      font-size: 18px;\r\n      opacity: .85;\r\n    }\r\n  }\r\n\r\n  .LiveBroadcastDetailsBody {\r\n    flex: 1;\r\n    display: grid;\r\n    grid-template-columns: 1fr 360px;\r\n    gap: 16px;\r\n    padding: 16px;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .LiveBroadcastDetailsCanvas {\r\n    position: relative;\r\n    height: 100%;\r\n    background: #111;\r\n    overflow: hidden;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .LiveBroadcastDetailsPoster {\r\n      width: 100%;\r\n      height: 100%;\r\n      background: #000;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      color: #d23a2e;\r\n      font-weight: bold;\r\n      font-size: 22px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsCountdown {\r\n      position: absolute;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      height: 95px;\r\n      background: rgba(0, 0, 0, 0.8);\r\n      color: #fff;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      justify-content: center;\r\n      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);\r\n\r\n      .countdown-line1 {\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        margin-bottom: 8px;\r\n        opacity: 0.9;\r\n      }\r\n\r\n      .countdown-line2 {\r\n        font-size: 24px;\r\n        font-weight: 600;\r\n        letter-spacing: 2px;\r\n      }\r\n    }\r\n\r\n    .LiveBroadcastDetailsLiveOverlay {\r\n      position: absolute;\r\n      left: 0;\r\n      right: 0;\r\n      top: 0;\r\n      bottom: 0;\r\n      width: 100%;\r\n      height: 100%;\r\n\r\n      .video-container {\r\n        width: 100%;\r\n        height: 100%;\r\n        position: relative;\r\n\r\n        .live-video-player {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: contain;\r\n        }\r\n\r\n        .custom-video-controls {\r\n          position: absolute;\r\n          bottom: 0;\r\n          left: 0;\r\n          right: 0;\r\n          background: rgba(0, 0, 0, 0.7);\r\n          padding: 10px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: space-between;\r\n          color: white;\r\n          font-size: 14px;\r\n\r\n          button {\r\n            background: none;\r\n            border: none;\r\n            color: white;\r\n            cursor: pointer;\r\n            font-size: 14px;\r\n            padding: 5px;\r\n            border-radius: 3px;\r\n            transition: background-color 0.2s;\r\n\r\n            &:hover {\r\n              background: rgba(255, 255, 255, 0.1);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .LiveBroadcastDetailsEnded {\r\n      position: absolute;\r\n      inset: 0;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      background: rgba(0, 0, 0, .45);\r\n\r\n      .LiveBroadcastDetailsEndedWrap {\r\n        text-align: center;\r\n\r\n        .endedTitle {\r\n          color: #fff;\r\n          font-size: 18px;\r\n          margin-bottom: 12px;\r\n        }\r\n\r\n        .replayBtn {\r\n          background: linear-gradient(90deg, #5bc0ff, #5f7cff);\r\n          border: none;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .LiveBroadcastDetailsSidebar {\r\n    height: 100%;\r\n    background: #191919;\r\n    border-left: 1px solid rgba(255, 255, 255, 0.05);\r\n    color: #e8e8e8;\r\n    // padding: 14px 16px 16px 16px;\r\n    overflow: auto;\r\n\r\n    .LiveBroadcastDetailsTabs {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-around;\r\n      gap: 40px;\r\n      margin-bottom: 10px;\r\n      background: #2B2B2B;\r\n      padding: 14px 16px;\r\n\r\n      .LiveBroadcastDetailsTab {\r\n        cursor: pointer;\r\n        color: #999999;\r\n        position: relative;\r\n        font-size: 16px;\r\n        line-height: 1;\r\n        transition: color .2s ease;\r\n        font-weight: 500;\r\n\r\n        &.active {\r\n          color: #ffffff;\r\n          font-weight: 700;\r\n        }\r\n\r\n        &.active::after {\r\n          content: '';\r\n          position: absolute;\r\n          left: 0;\r\n          right: 0;\r\n          bottom: -8px;\r\n          height: 3px;\r\n          background: #54BDFF;\r\n          border-radius: 3px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .LiveBroadcastDetailsTabPane {\r\n      padding: 12px 15px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsPanelTitle {\r\n      font-weight: bold;\r\n      margin-bottom: 10px;\r\n      font-size: 15px;\r\n    }\r\n\r\n    .LiveBroadcastDetailsPanelText {\r\n      font-size: 13px;\r\n      line-height: 1.8;\r\n      color: #cfcfcf;\r\n    }\r\n\r\n    /* 详情tab内样式 */\r\n    .detailsTitle {\r\n      font-weight: 400;\r\n      font-size: 18px;\r\n      color: #FFFFFF;\r\n      margin-bottom: 14px;\r\n    }\r\n\r\n    .detailsTime {\r\n      font-weight: 400;\r\n      font-size: 14px;\r\n      color: #666666;\r\n      margin-bottom: 12px;\r\n    }\r\n\r\n    .detailsDesc {\r\n      font-size: 13px;\r\n      color: #d9d9d9;\r\n      line-height: 1.8;\r\n    }\r\n  }\r\n}\r\n\r\n.details-fade-enter-active,\r\n.details-fade-leave-active {\r\n  transition: opacity .2s ease;\r\n}\r\n\r\n.details-fade-enter-from,\r\n.details-fade-leave-to {\r\n  opacity: 0;\r\n}\r\n</style>\r\n"], "mappings": ";;EAE6BA,KAAK,EAAC;AAAsB;;EAC9CA,KAAK,EAAC;AAA4B;iBAH7C;;EAKaA,KAAK,EAAC;AAA8B;;EAClCA,KAAK,EAAC;AAA2B;;EACjCA,KAAK,EAAC;AAA0B;;EAOpCA,KAAK,EAAC;AAA0B;;EAC9BA,KAAK,EAAC;AAA4B;;EAf/CC,GAAA;EAiBsDD,KAAK,EAAC;;kBAjB5D;;EAmBiBA,KAAK,EAAC;AAA+B;;EACnCA,KAAK,EAAC;AAAiB;;EACvBA,KAAK,EAAC;AAAiB;;EAIiBA,KAAK,EAAC;AAAiC;;EACjFE,GAAG,EAAC,gBAAgB;EAACF,KAAK,EAAC;;;EAGeA,KAAK,EAAC;AAA2B;;EAC3EA,KAAK,EAAC;AAA+B;;EAMzCA,KAAK,EAAC;AAA6B;;EACjCA,KAAK,EAAC;AAA0B;;EArC/CC,GAAA;EA6CeD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAAc;;EACpBA,KAAK,EAAC;AAAa;;EACnBA,KAAK,EAAC;AAAa;;EAhDpCC,GAAA;EAkDeD,KAAK,EAAC;;;;;uBAjDnBG,YAAA,CAwDaC,WAAA;IAxDDC,IAAI,EAAC,cAAc;IAA/BC,SAwDa,EAxDb;;IADFC,OAAA,EAAAC,QAAA,CAEI;MAAA,IAAAC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA;MAAA,OAsDM,C,gBAtDNC,mBAAA,CAsDM,OAtDNC,UAsDM,GArDJD,mBAAA,CAUM,OAVNE,UAUM,GATOC,MAAA,CAAAC,OAAO,I,cAAlBC,mBAAA,CAAqF;QAJ7FhB,GAAA;QAI4BD,KAAK,EAAC,4BAA4B;QAAEkB,GAAG,EAAEH,MAAA,CAAAC,OAAO;QAAEG,GAAG,EAAC;8BAJlFC,UAAA,KAAAC,mBAAA,gBAKQT,mBAAA,CAIM,OAJNU,UAIM,GAHJV,mBAAA,CAAgE,OAAhEW,UAAgE,EAAAC,gBAAA,CAAtBT,MAAA,CAAAU,OAAO,CAACC,KAAK,kBACvDd,mBAAA,CACkC,OADlCe,UACkC,EADI,OAAK,GAAAH,gBAAA,CAAGT,MAAA,CAAAa,MAAM,CAACb,MAAA,CAAAU,OAAO,CAACI,SAAS,KAAI,KAAG,GAAAL,gBAAA,CAC3ET,MAAA,CAAAa,MAAM,CAACb,MAAA,CAAAU,OAAO,CAACK,OAAO,kB,GAE1BC,YAAA,CAEUC,kBAAA;QAFDhC,KAAK,EAAC,2BAA2B;QAAEiC,OAAK,EAAElB,MAAA,CAAAmB;;QAV3D3B,OAAA,EAAAC,QAAA,CAWU;UAAA,OAAS,CAATuB,YAAA,CAAShB,MAAA,W;;QAXnBoB,CAAA;YAcMvB,mBAAA,CAyCM,OAzCNwB,UAyCM,GAxCJxB,mBAAA,CAoBM,OApBNyB,UAoBM,GAnBJhB,mBAAA,qBAAwB,EACbN,MAAA,CAAAU,OAAO,CAACa,aAAa,c,cAAhCrB,mBAAA,CAMM,OANNsB,UAMM,GALJ3B,mBAAA,CAAyF;QAAnFM,GAAG,EAAEH,MAAA,CAAAyB,MAAM;QAAErB,GAAG,EAAC,WAAW;QAACsB,KAAqD,EAArD;UAAA;UAAA;UAAA;QAAA;8BAlB/CC,WAAA,GAmBY9B,mBAAA,CAGM,OAHN+B,WAGM,GAFJ/B,mBAAA,CAA2F,OAA3FgC,WAA2F,EAA9D,KAAG,GAAApB,gBAAA,CAAGT,MAAA,CAAAa,MAAM,CAACb,MAAA,CAAAU,OAAO,CAACI,SAAS,oBAAmB,SAAO,iBACrFjB,mBAAA,CAA0D,OAA1DiC,WAA0D,EAAArB,gBAAA,CAA1BT,MAAA,CAAA+B,iBAAiB,iB,OAIrC/B,MAAA,CAAAU,OAAO,CAACa,aAAa,c,cAArCrB,mBAAA,CAEM8B,SAAA;QA3BhB9C,GAAA;MAAA,IAwBUoB,mBAAA,eAAkB,EAClBT,mBAAA,CAEM,OAFNoC,WAEM,GADJpC,mBAAA,CAAwD,OAAxDqC,WAAwD,8B,sDAG1ClC,MAAA,CAAAU,OAAO,CAACa,aAAa,c,cAArCrB,mBAAA,CAKM8B,SAAA;QAlChB9C,GAAA;MAAA,IA4BUoB,mBAAA,mBAAsB,EACtBT,mBAAA,CAKM,OALNsC,WAKM,GAJJtC,mBAAA,CAGM,OAHNuC,WAGM,G,0BAFJvC,mBAAA,CAAmC;QAA9BZ,KAAK,EAAC;MAAY,GAAC,OAAK,sBACqBe,MAAA,CAAAU,OAAO,CAAC2B,QAAQ,S,cAAlEjD,YAAA,CAAyFkD,oBAAA;QAhCvGpD,GAAA;QAgCyBqD,IAAI,EAAC,SAAS;QAACtD,KAAK,EAAC;;QAhC9CO,OAAA,EAAAC,QAAA,CAgCuF;UAAA,OAAI+C,MAAA,QAAAA,MAAA,OAhC3FC,gBAAA,CAgCuF,MAAI,E;;QAhC3FrB,CAAA;YAAAd,mBAAA,e,wDAAAA,mBAAA,e,GAoCQT,mBAAA,CAkBM,OAlBN6C,WAkBM,GAjBJ7C,mBAAA,CAOM,OAPN8C,WAOM,GANJ9C,mBAAA,CAEY;QAFNZ,KAAK,EAtCvB2D,eAAA;UAAAC,MAAA,EAsC+D7C,MAAA,CAAA8C,SAAS;QAAA;QACzD5B,OAAK,EAAAsB,MAAA,QAAAA,MAAA,gBAAAO,MAAA;UAAA,OAAE/C,MAAA,CAAA8C,SAAS;QAAA;SAAc,OAC3B,kBACNjD,mBAAA,CAEU;QAFJZ,KAAK,EAzCvB2D,eAAA;UAAAC,MAAA,EAyC+D7C,MAAA,CAAA8C,SAAS;QAAA;QACzD5B,OAAK,EAAAsB,MAAA,QAAAA,MAAA,gBAAAO,MAAA;UAAA,OAAE/C,MAAA,CAAA8C,SAAS;QAAA;SAAe,KAC9B,iB,GAEyC9C,MAAA,CAAA8C,SAAS,kB,cAAxD5C,mBAAA,CAIM,OAJN8C,WAIM,GAHJnD,mBAAA,CAA2D,OAA3DoD,WAA2D,EAAAxC,gBAAA,CAA9B,EAAAf,eAAA,GAAAM,MAAA,CAAAU,OAAO,cAAAhB,eAAA,uBAAPA,eAAA,CAASiB,KAAK,0BAC3Cd,mBAAA,CAAmG,OAAnGqD,WAAmG,EAA1E,KAAG,GAAAzC,gBAAA,CAAGT,MAAA,CAAAa,MAAM,EAAAlB,gBAAA,GAACK,MAAA,CAAAU,OAAO,cAAAf,gBAAA,uBAAPA,gBAAA,CAASmB,SAAS,KAAI,KAAG,GAAAL,gBAAA,CAAGT,MAAA,CAAAa,MAAM,EAAAjB,gBAAA,GAACI,MAAA,CAAAU,OAAO,cAAAd,gBAAA,uBAAPA,gBAAA,CAASmB,OAAO,mBACzFlB,mBAAA,CAAoE,OAApEsD,WAAoE,EAAA1C,gBAAA,CAAxCT,MAAA,CAAAU,OAAO,CAAC0C,aAAa,2B,oBAEnDlD,mBAAA,CAGM,OAHNmD,WAGM,EAAAb,MAAA,QAAAA,MAAA,OAFJ3C,mBAAA,CAAoD;QAA/CZ,KAAK,EAAC;MAAgC,GAAC,IAAE,qBAC9CY,mBAAA,CAAuD;QAAlDZ,KAAK,EAAC;MAA+B,GAAC,QAAM,oB,2CAlD5CqE,MAAA,CAAAC,UAAU,E;;IAF3BnC,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}