{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"ChartFour\",\n  ref: \"elChartRef\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, null, 512 /* NEED_PATCH */);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\PublicSentimentInfo\\components\\ChartFour.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ChartFour\" ref=\"elChartRef\">\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'ChartFour' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onMounted, nextTick, onUnmounted } from 'vue'\r\nimport { useRoute } from 'vue-router'\r\nimport { useStore } from 'vuex'\r\nimport * as echarts from 'echarts'\r\nimport elementResizeDetectorMaker from 'element-resize-detector'\r\nconst route = useRoute()\r\nconst store = useStore()\r\nconst erd = elementResizeDetectorMaker()\r\nconst ticketType = ref('')\r\nlet elChart = null\r\nconst elChartRef = ref()\r\nconst endValue = ref(6)\r\nconst xAxisData = ref([])\r\nconst tableData = ref([])\r\nconst stateData = computed(() => store.getters.getPublicSentimentInfoFn)\r\nconst initChart = () => {\r\n  if (!elChart) { elChart = echarts.init(elChartRef.value) }\r\n  var colorListOne = [\r\n    'rgba(121, 240, 182, .1)',\r\n    'rgba(186, 206, 246, .1)',\r\n    'rgba(141, 204, 241, .1)',\r\n    'rgba(250, 220, 135, .1)',\r\n    'rgba(0, 254, 210, .1)',\r\n    'rgba(0, 124, 250, .1)',\r\n    'rgba(0, 179, 244, .1)'\r\n  ]\r\n  var colorListTow = [\r\n    'rgba(121, 240, 182, .2)',\r\n    'rgba(186, 206, 246, .2)',\r\n    'rgba(141, 204, 241, .2)',\r\n    'rgba(250, 220, 135, .2)',\r\n    'rgba(0, 254, 210, .2)',\r\n    'rgba(0, 124, 250, .2)',\r\n    'rgba(0, 179, 244, .2)'\r\n  ]\r\n  var colorLine = [\r\n    'rgb(121, 240, 182)',\r\n    'rgb(186, 206, 246)',\r\n    'rgb(141, 204, 241)',\r\n    'rgb(250, 220, 135)',\r\n    'rgb(0, 254, 210)',\r\n    'rgb(0, 124, 250)',\r\n    'rgb(0, 179, 244)'\r\n  ]\r\n  const seriesData = tableData.value.map((item, index) => {\r\n    return {\r\n      name: item.name,\r\n      type: \"line\",\r\n      symbol: 'circle', //设定为实心点\r\n      showAllSymbol: true,\r\n      symbolSize: 5,\r\n      smooth: true,\r\n      itemStyle: {\r\n        normal: {\r\n          color: colorLine[index],//线的颜色\r\n          lineStyle: { color: colorLine[index], width: 1.6 },\r\n          areaStyle: {\r\n            //新版渐变色实现\r\n            color: {\r\n              type: 'linear', x: 0, y: 0, x2: 0, y2: 1,\r\n              colorStops: [\r\n                { offset: 0, color: colorListOne[index] },\r\n                { offset: 1, color: colorListTow[index] }\r\n              ]\r\n            }\r\n          }\r\n        }\r\n      },\r\n      data: item.data\r\n    }\r\n  })\r\n\r\n  const setOption = {\r\n    tooltip: {\r\n      trigger: 'axis',\r\n      formatter (params) {\r\n        let html = ''\r\n        for (let index = 0; index < params.length; index++) {\r\n          const item = params[index]\r\n          html += `<div class=\"ColumnChartText\"><span>${item.marker}${item.seriesName}</span><span> ${item.value}</span></div>`\r\n        }\r\n        return `<div class=\"ColumnChartTooltip\">\r\n          <div class=\"ColumnChartName\">${params[0]?.name}</div>\r\n          ${html} \r\n        </div>`\r\n      },\r\n      axisPointer: {\r\n        type: 'shadow'\r\n      }\r\n    },\r\n    grid: {\r\n      top: '18%',\r\n      left: '2%',\r\n      right: '2%',\r\n      bottom: '8%',\r\n      containLabel: true\r\n    },\r\n    legend: {\r\n      icon: 'rect',\r\n      right: \"center\",\r\n      top: '5%',\r\n      itemWidth: 7, // 设置宽度\r\n      itemHeight: 7, // 设置高度\r\n      itemGap: 15, // 设置间距\r\n      textStyle: { //图例文字的样式 \r\n        fontSize: 12\r\n      }\r\n    },\r\n    xAxis: {\r\n      // boundaryGap: true,\r\n      data: xAxisData.value,\r\n      axisTick: {\r\n        show: false\r\n      }\r\n    },\r\n    yAxis: [{\r\n      inverse: false,\r\n      splitLine: {\r\n        show: true,\r\n        lineStyle: {\r\n          color: 'rgba(117, 168, 202, 0.3)',\r\n          type: 'dashed'\r\n        }\r\n      },\r\n      axisLine: {\r\n        show: false\r\n      },\r\n      axisLabel: {\r\n        formatter: \"{value}\",\r\n        textStyle: {\r\n          fontSize: 12\r\n        }\r\n      },\r\n      nameTextStyle: {\r\n        padding: [0, 0, 0, 15],\r\n        fontSize: 12\r\n      },\r\n      axisTick: {\r\n        show: false\r\n      }\r\n    }],\r\n    dataZoom: [\r\n      {\r\n        id: 'dataZoomY',\r\n        xAxisIndex: [0],\r\n        show: endValue.value + 1 < xAxisData.value.length, // 是否显示滑动条，不影响使用\r\n        type: 'slider', // 这个 dataZoom 组件是 slider 型 dataZoom 组件\r\n        endValue: endValue.value,\r\n        height: 12,\r\n        bottom: '2%',\r\n        zoomLock: true,\r\n        showDataShadow: false, // 是否显示数据阴影 默认auto\r\n        backgroundColor: '#fff',\r\n        showDetail: false, // 即拖拽时候是否显示详细数值信息 默认true\r\n        realtime: true, // 是否实时更新\r\n        filterMode: 'filter',\r\n        handleIcon: 'circle',\r\n        moveHandleSize: 0,\r\n        brushSelect: false\r\n      },\r\n      {\r\n        type: 'inside',\r\n        xAxisIndex: 0,\r\n        zoomOnMouseWheel: false,  // 滚轮是否触发缩放\r\n        moveOnMouseMove: true,  // 鼠标滚轮触发滚动\r\n        moveOnMouseWheel: true\r\n      }\r\n    ],\r\n    series: seriesData\r\n  }\r\n  elChart.setOption(setOption)\r\n}\r\nconst publicChartAnalysis = async () => {\r\n  if (stateData.value[`ChartFour-${ticketType.value}`]) {\r\n    xAxisData.value = stateData.value[`ChartFour-${ticketType.value}`].xAxisData\r\n    tableData.value = stateData.value[`ChartFour-${ticketType.value}`].tableData\r\n  } else {\r\n    const { data } = await api.publicChartAnalysis({ ticketType: ticketType.value, chartType: '4' })\r\n    let newTableKey = []\r\n    let newTableData = []\r\n    for (let index = 0; index < data?.charts?.length; index++) {\r\n      const item = data.charts[index]\r\n      let dataList = []\r\n      for (let i = 0; i < item.charts.length; i++) {\r\n        const row = item.charts[i]\r\n        dataList.push(row.num)\r\n        if (!newTableKey.includes(row.key)) {\r\n          newTableKey.push(row.key)\r\n        }\r\n      }\r\n      newTableData.push({ name: item.name, data: dataList })\r\n    }\r\n    xAxisData.value = newTableKey\r\n    tableData.value = newTableData\r\n    store.commit('setPublicSentimentInfo', { key: `ChartFour-${ticketType.value}`, params: { xAxisData: xAxisData.value, tableData: tableData.value } })\r\n  }\r\n  initChart()\r\n  nextTick(() => {\r\n    erd.listenTo(elChartRef.value, (element) => {\r\n      endValue.value = Math.trunc((element.offsetWidth / 99) - 2)\r\n      elChart.setOption({ dataZoom: [{ show: endValue.value + 1 < xAxisData.value.length, endValue: endValue.value }] })\r\n      elChart.resize()\r\n    })\r\n  })\r\n}\r\nonMounted(() => {\r\n  ticketType.value = route.query.ticketType || '2'\r\n  publicChartAnalysis()\r\n})\r\nonUnmounted(() => { erd.uninstall(elChartRef.value) })\r\n</script>\r\n<style lang=\"scss\">\r\n.ChartFour {\r\n  width: 100%;\r\n  height: 320px;\r\n\r\n  .ColumnChartTooltip {\r\n    min-width: 180px;\r\n\r\n    .ColumnChartName {\r\n      width: 100%;\r\n      font-weight: bold;\r\n      font-size: var(--zy-name-font-size);\r\n      line-height: var(--zy-line-height);\r\n      color: var(--zy-el-text-color-primary);\r\n    }\r\n\r\n    .ColumnChartText {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      padding-top: var(--zy-font-name-distance-five);\r\n\r\n      span+span {\r\n        margin-left: 20px;\r\n      }\r\n\r\n      span {\r\n        font-size: var(--zy-text-font-size);\r\n        line-height: var(--zy-line-height);\r\n        color: var(--zy-el-text-color-regular);\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC,WAAW;EAACC,GAAG,EAAC;;;uBAA3BC,mBAAA,CACM,OADNC,UACM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}