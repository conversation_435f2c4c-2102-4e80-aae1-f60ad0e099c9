{"ast": null, "code": "function _getPrototypeOf(t) {\n  return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) {\n    return t.__proto__ || Object.getPrototypeOf(t);\n  }, _getPrototypeOf(t);\n}\nexport { _getPrototypeOf as default };", "map": {"version": 3, "names": ["_getPrototypeOf", "t", "Object", "setPrototypeOf", "getPrototypeOf", "bind", "__proto__", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/@babel+runtime@7.26.0/node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js"], "sourcesContent": ["function _getPrototypeOf(t) {\n  return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) {\n    return t.__proto__ || Object.getPrototypeOf(t);\n  }, _getPrototypeOf(t);\n}\nexport { _getPrototypeOf as default };"], "mappings": "AAAA,SAASA,eAAeA,CAACC,CAAC,EAAE;EAC1B,OAAOD,eAAe,GAAGE,MAAM,CAACC,cAAc,GAAGD,MAAM,CAACE,cAAc,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUJ,CAAC,EAAE;IAC3F,OAAOA,CAAC,CAACK,SAAS,IAAIJ,MAAM,CAACE,cAAc,CAACH,CAAC,CAAC;EAChD,CAAC,EAAED,eAAe,CAACC,CAAC,CAAC;AACvB;AACA,SAASD,eAAe,IAAIO,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}