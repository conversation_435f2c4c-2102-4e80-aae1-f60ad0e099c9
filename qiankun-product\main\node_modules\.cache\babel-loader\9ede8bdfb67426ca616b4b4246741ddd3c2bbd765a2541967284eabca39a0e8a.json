{"ast": null, "code": "var IDENT_RE = '[A-Za-z$_][0-9A-Za-z$_]*';\nvar KEYWORDS = [\"as\",\n// for exports\n\"in\", \"of\", \"if\", \"for\", \"while\", \"finally\", \"var\", \"new\", \"function\", \"do\", \"return\", \"void\", \"else\", \"break\", \"catch\", \"instanceof\", \"with\", \"throw\", \"case\", \"default\", \"try\", \"switch\", \"continue\", \"typeof\", \"delete\", \"let\", \"yield\", \"const\", \"class\",\n// JS handles these with a special rule\n// \"get\",\n// \"set\",\n\"debugger\", \"async\", \"await\", \"static\", \"import\", \"from\", \"export\", \"extends\",\n// It's reached stage 3, which is \"recommended for implementation\":\n\"using\"];\nvar LITERALS = [\"true\", \"false\", \"null\", \"undefined\", \"NaN\", \"Infinity\"];\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects\nvar TYPES = [\n// Fundamental objects\n\"Object\", \"Function\", \"Boolean\", \"Symbol\",\n// numbers and dates\n\"Math\", \"Date\", \"Number\", \"BigInt\",\n// text\n\"String\", \"RegExp\",\n// Indexed collections\n\"Array\", \"Float32Array\", \"Float64Array\", \"Int8Array\", \"Uint8Array\", \"Uint8ClampedArray\", \"Int16Array\", \"Int32Array\", \"Uint16Array\", \"Uint32Array\", \"BigInt64Array\", \"BigUint64Array\",\n// Keyed collections\n\"Set\", \"Map\", \"WeakSet\", \"WeakMap\",\n// Structured data\n\"ArrayBuffer\", \"SharedArrayBuffer\", \"Atomics\", \"DataView\", \"JSON\",\n// Control abstraction objects\n\"Promise\", \"Generator\", \"GeneratorFunction\", \"AsyncFunction\",\n// Reflection\n\"Reflect\", \"Proxy\",\n// Internationalization\n\"Intl\",\n// WebAssembly\n\"WebAssembly\"];\nvar ERROR_TYPES = [\"Error\", \"EvalError\", \"InternalError\", \"RangeError\", \"ReferenceError\", \"SyntaxError\", \"TypeError\", \"URIError\"];\nvar BUILT_IN_GLOBALS = [\"setInterval\", \"setTimeout\", \"clearInterval\", \"clearTimeout\", \"require\", \"exports\", \"eval\", \"isFinite\", \"isNaN\", \"parseFloat\", \"parseInt\", \"decodeURI\", \"decodeURIComponent\", \"encodeURI\", \"encodeURIComponent\", \"escape\", \"unescape\"];\nvar BUILT_IN_VARIABLES = [\"arguments\", \"this\", \"super\", \"console\", \"window\", \"document\", \"localStorage\", \"sessionStorage\", \"module\", \"global\" // Node.js\n];\nvar BUILT_INS = [].concat(BUILT_IN_GLOBALS, TYPES, ERROR_TYPES);\n\n/*\nLanguage: JavaScript\nDescription: JavaScript (JS) is a lightweight, interpreted, or just-in-time compiled programming language with first-class functions.\nCategory: common, scripting, web\nWebsite: https://developer.mozilla.org/en-US/docs/Web/JavaScript\n*/\n\n/** @type LanguageFn */\nfunction javascript(hljs) {\n  var regex = hljs.regex;\n  /**\n   * Takes a string like \"<Booger\" and checks to see\n   * if we can find a matching \"</Booger\" later in the\n   * content.\n   * @param {RegExpMatchArray} match\n   * @param {{after:number}} param1\n   */\n  var hasClosingTag = function hasClosingTag(match, _ref) {\n    var after = _ref.after;\n    var tag = \"</\" + match[0].slice(1);\n    var pos = match.input.indexOf(tag, after);\n    return pos !== -1;\n  };\n  var IDENT_RE$1 = IDENT_RE;\n  var FRAGMENT = {\n    begin: '<>',\n    end: '</>'\n  };\n  // to avoid some special cases inside isTrulyOpeningTag\n  var XML_SELF_CLOSING = /<[A-Za-z0-9\\\\._:-]+\\s*\\/>/;\n  var XML_TAG = {\n    begin: /<[A-Za-z0-9\\\\._:-]+/,\n    end: /\\/[A-Za-z0-9\\\\._:-]+>|\\/>/,\n    /**\n     * @param {RegExpMatchArray} match\n     * @param {CallbackResponse} response\n     */\n    isTrulyOpeningTag: function isTrulyOpeningTag(match, response) {\n      var afterMatchIndex = match[0].length + match.index;\n      var nextChar = match.input[afterMatchIndex];\n      if (\n      // HTML should not include another raw `<` inside a tag\n      // nested type?\n      // `<Array<Array<number>>`, etc.\n      nextChar === \"<\" ||\n      // the , gives away that this is not HTML\n      // `<T, A extends keyof T, V>`\n      nextChar === \",\") {\n        response.ignoreMatch();\n        return;\n      }\n\n      // `<something>`\n      // Quite possibly a tag, lets look for a matching closing tag...\n      if (nextChar === \">\") {\n        // if we cannot find a matching closing tag, then we\n        // will ignore it\n        if (!hasClosingTag(match, {\n          after: afterMatchIndex\n        })) {\n          response.ignoreMatch();\n        }\n      }\n\n      // `<blah />` (self-closing)\n      // handled by simpleSelfClosing rule\n\n      var m;\n      var afterMatch = match.input.substring(afterMatchIndex);\n\n      // some more template typing stuff\n      //  <T = any>(key?: string) => Modify<\n      if (m = afterMatch.match(/^\\s*=/)) {\n        response.ignoreMatch();\n        return;\n      }\n\n      // `<From extends string>`\n      // technically this could be HTML, but it smells like a type\n      // NOTE: This is ugh, but added specifically for https://github.com/highlightjs/highlight.js/issues/3276\n      if (m = afterMatch.match(/^\\s+extends\\s+/)) {\n        if (m.index === 0) {\n          response.ignoreMatch();\n          // eslint-disable-next-line no-useless-return\n          return;\n        }\n      }\n    }\n  };\n  var KEYWORDS$1 = {\n    $pattern: IDENT_RE,\n    keyword: KEYWORDS,\n    literal: LITERALS,\n    built_in: BUILT_INS,\n    \"variable.language\": BUILT_IN_VARIABLES\n  };\n\n  // https://tc39.es/ecma262/#sec-literals-numeric-literals\n  var decimalDigits = '[0-9](_?[0-9])*';\n  var frac = `\\\\.(${decimalDigits})`;\n  // DecimalIntegerLiteral, including Annex B NonOctalDecimalIntegerLiteral\n  // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n  var decimalInteger = `0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*`;\n  var NUMBER = {\n    className: 'number',\n    variants: [\n    // DecimalLiteral\n    {\n      begin: `(\\\\b(${decimalInteger})((${frac})|\\\\.)?|(${frac}))` + `[eE][+-]?(${decimalDigits})\\\\b`\n    }, {\n      begin: `\\\\b(${decimalInteger})\\\\b((${frac})\\\\b|\\\\.)?|(${frac})\\\\b`\n    },\n    // DecimalBigIntegerLiteral\n    {\n      begin: `\\\\b(0|[1-9](_?[0-9])*)n\\\\b`\n    },\n    // NonDecimalIntegerLiteral\n    {\n      begin: \"\\\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\\\b\"\n    }, {\n      begin: \"\\\\b0[bB][0-1](_?[0-1])*n?\\\\b\"\n    }, {\n      begin: \"\\\\b0[oO][0-7](_?[0-7])*n?\\\\b\"\n    },\n    // LegacyOctalIntegerLiteral (does not include underscore separators)\n    // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n    {\n      begin: \"\\\\b0[0-7]+n?\\\\b\"\n    }],\n    relevance: 0\n  };\n  var SUBST = {\n    className: 'subst',\n    begin: '\\\\$\\\\{',\n    end: '\\\\}',\n    keywords: KEYWORDS$1,\n    contains: [] // defined later\n  };\n  var HTML_TEMPLATE = {\n    begin: '\\.?html`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [hljs.BACKSLASH_ESCAPE, SUBST],\n      subLanguage: 'xml'\n    }\n  };\n  var CSS_TEMPLATE = {\n    begin: '\\.?css`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [hljs.BACKSLASH_ESCAPE, SUBST],\n      subLanguage: 'css'\n    }\n  };\n  var GRAPHQL_TEMPLATE = {\n    begin: '\\.?gql`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [hljs.BACKSLASH_ESCAPE, SUBST],\n      subLanguage: 'graphql'\n    }\n  };\n  var TEMPLATE_STRING = {\n    className: 'string',\n    begin: '`',\n    end: '`',\n    contains: [hljs.BACKSLASH_ESCAPE, SUBST]\n  };\n  var JSDOC_COMMENT = hljs.COMMENT(/\\/\\*\\*(?!\\/)/, '\\\\*/', {\n    relevance: 0,\n    contains: [{\n      begin: '(?=@[A-Za-z]+)',\n      relevance: 0,\n      contains: [{\n        className: 'doctag',\n        begin: '@[A-Za-z]+'\n      }, {\n        className: 'type',\n        begin: '\\\\{',\n        end: '\\\\}',\n        excludeEnd: true,\n        excludeBegin: true,\n        relevance: 0\n      }, {\n        className: 'variable',\n        begin: IDENT_RE$1 + '(?=\\\\s*(-)|$)',\n        endsParent: true,\n        relevance: 0\n      },\n      // eat spaces (not newlines) so we can find\n      // types or variables\n      {\n        begin: /(?=[^\\n])\\s/,\n        relevance: 0\n      }]\n    }]\n  });\n  var COMMENT = {\n    className: \"comment\",\n    variants: [JSDOC_COMMENT, hljs.C_BLOCK_COMMENT_MODE, hljs.C_LINE_COMMENT_MODE]\n  };\n  var SUBST_INTERNALS = [hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, HTML_TEMPLATE, CSS_TEMPLATE, GRAPHQL_TEMPLATE, TEMPLATE_STRING,\n  // Skip numbers when they are part of a variable name\n  {\n    match: /\\$\\d+/\n  }, NUMBER\n  // This is intentional:\n  // See https://github.com/highlightjs/highlight.js/issues/3288\n  // hljs.REGEXP_MODE\n  ];\n  SUBST.contains = SUBST_INTERNALS.concat({\n    // we need to pair up {} inside our subst to prevent\n    // it from ending too early by matching another }\n    begin: /\\{/,\n    end: /\\}/,\n    keywords: KEYWORDS$1,\n    contains: [\"self\"].concat(SUBST_INTERNALS)\n  });\n  var SUBST_AND_COMMENTS = [].concat(COMMENT, SUBST.contains);\n  var PARAMS_CONTAINS = SUBST_AND_COMMENTS.concat([\n  // eat recursive parens in sub expressions\n  {\n    begin: /(\\s*)\\(/,\n    end: /\\)/,\n    keywords: KEYWORDS$1,\n    contains: [\"self\"].concat(SUBST_AND_COMMENTS)\n  }]);\n  var PARAMS = {\n    className: 'params',\n    // convert this to negative lookbehind in v12\n    begin: /(\\s*)\\(/,\n    // to match the parms with\n    end: /\\)/,\n    excludeBegin: true,\n    excludeEnd: true,\n    keywords: KEYWORDS$1,\n    contains: PARAMS_CONTAINS\n  };\n\n  // ES6 classes\n  var CLASS_OR_EXTENDS = {\n    variants: [\n    // class Car extends vehicle\n    {\n      match: [/class/, /\\s+/, IDENT_RE$1, /\\s+/, /extends/, /\\s+/, regex.concat(IDENT_RE$1, \"(\", regex.concat(/\\./, IDENT_RE$1), \")*\")],\n      scope: {\n        1: \"keyword\",\n        3: \"title.class\",\n        5: \"keyword\",\n        7: \"title.class.inherited\"\n      }\n    },\n    // class Car\n    {\n      match: [/class/, /\\s+/, IDENT_RE$1],\n      scope: {\n        1: \"keyword\",\n        3: \"title.class\"\n      }\n    }]\n  };\n  var CLASS_REFERENCE = {\n    relevance: 0,\n    match: regex.either(\n    // Hard coded exceptions\n    /\\bJSON/,\n    // Float32Array, OutT\n    /\\b[A-Z][a-z]+([A-Z][a-z]*|\\d)*/,\n    // CSSFactory, CSSFactoryT\n    /\\b[A-Z]{2,}([A-Z][a-z]+|\\d)+([A-Z][a-z]*)*/,\n    // FPs, FPsT\n    /\\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\\d)*([A-Z][a-z]*)*/\n    // P\n    // single letters are not highlighted\n    // BLAH\n    // this will be flagged as a UPPER_CASE_CONSTANT instead\n    ),\n    className: \"title.class\",\n    keywords: {\n      _: [].concat(TYPES, ERROR_TYPES)\n    }\n  };\n  var USE_STRICT = {\n    label: \"use_strict\",\n    className: 'meta',\n    relevance: 10,\n    begin: /^\\s*['\"]use (strict|asm)['\"]/\n  };\n  var FUNCTION_DEFINITION = {\n    variants: [{\n      match: [/function/, /\\s+/, IDENT_RE$1, /(?=\\s*\\()/]\n    },\n    // anonymous function\n    {\n      match: [/function/, /\\s*(?=\\()/]\n    }],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    label: \"func.def\",\n    contains: [PARAMS],\n    illegal: /%/\n  };\n  var UPPER_CASE_CONSTANT = {\n    relevance: 0,\n    match: /\\b[A-Z][A-Z_0-9]+\\b/,\n    className: \"variable.constant\"\n  };\n  function noneOf(list) {\n    return regex.concat(\"(?!\", list.join(\"|\"), \")\");\n  }\n  var FUNCTION_CALL = {\n    match: regex.concat(/\\b/, noneOf([].concat(BUILT_IN_GLOBALS, [\"super\", \"import\"]).map(function (x) {\n      return `${x}\\\\s*\\\\(`;\n    })), IDENT_RE$1, regex.lookahead(/\\s*\\(/)),\n    className: \"title.function\",\n    relevance: 0\n  };\n  var PROPERTY_ACCESS = {\n    begin: regex.concat(/\\./, regex.lookahead(regex.concat(IDENT_RE$1, /(?![0-9A-Za-z$_(])/))),\n    end: IDENT_RE$1,\n    excludeBegin: true,\n    keywords: \"prototype\",\n    className: \"property\",\n    relevance: 0\n  };\n  var GETTER_OR_SETTER = {\n    match: [/get|set/, /\\s+/, IDENT_RE$1, /(?=\\()/],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [{\n      // eat to avoid empty params\n      begin: /\\(\\)/\n    }, PARAMS]\n  };\n  var FUNC_LEAD_IN_RE = '(\\\\(' + '[^()]*(\\\\(' + '[^()]*(\\\\(' + '[^()]*' + '\\\\)[^()]*)*' + '\\\\)[^()]*)*' + '\\\\)|' + hljs.UNDERSCORE_IDENT_RE + ')\\\\s*=>';\n  var FUNCTION_VARIABLE = {\n    match: [/const|var|let/, /\\s+/, IDENT_RE$1, /\\s*/, /=\\s*/, /(async\\s*)?/,\n    // async is optional\n    regex.lookahead(FUNC_LEAD_IN_RE)],\n    keywords: \"async\",\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [PARAMS]\n  };\n  return {\n    name: 'JavaScript',\n    aliases: ['js', 'jsx', 'mjs', 'cjs'],\n    keywords: KEYWORDS$1,\n    // this will be extended by TypeScript\n    exports: {\n      PARAMS_CONTAINS,\n      CLASS_REFERENCE\n    },\n    illegal: /#(?![$_A-z])/,\n    contains: [hljs.SHEBANG({\n      label: \"shebang\",\n      binary: \"node\",\n      relevance: 5\n    }), USE_STRICT, hljs.APOS_STRING_MODE, hljs.QUOTE_STRING_MODE, HTML_TEMPLATE, CSS_TEMPLATE, GRAPHQL_TEMPLATE, TEMPLATE_STRING, COMMENT,\n    // Skip numbers when they are part of a variable name\n    {\n      match: /\\$\\d+/\n    }, NUMBER, CLASS_REFERENCE, {\n      scope: 'attr',\n      match: IDENT_RE$1 + regex.lookahead(':'),\n      relevance: 0\n    }, FUNCTION_VARIABLE, {\n      // \"value\" container\n      begin: '(' + hljs.RE_STARTERS_RE + '|\\\\b(case|return|throw)\\\\b)\\\\s*',\n      keywords: 'return throw case',\n      relevance: 0,\n      contains: [COMMENT, hljs.REGEXP_MODE, {\n        className: 'function',\n        // we have to count the parens to make sure we actually have the\n        // correct bounding ( ) before the =>.  There could be any number of\n        // sub-expressions inside also surrounded by parens.\n        begin: FUNC_LEAD_IN_RE,\n        returnBegin: true,\n        end: '\\\\s*=>',\n        contains: [{\n          className: 'params',\n          variants: [{\n            begin: hljs.UNDERSCORE_IDENT_RE,\n            relevance: 0\n          }, {\n            className: null,\n            begin: /\\(\\s*\\)/,\n            skip: true\n          }, {\n            begin: /(\\s*)\\(/,\n            end: /\\)/,\n            excludeBegin: true,\n            excludeEnd: true,\n            keywords: KEYWORDS$1,\n            contains: PARAMS_CONTAINS\n          }]\n        }]\n      }, {\n        // could be a comma delimited list of params to a function call\n        begin: /,/,\n        relevance: 0\n      }, {\n        match: /\\s+/,\n        relevance: 0\n      }, {\n        // JSX\n        variants: [{\n          begin: FRAGMENT.begin,\n          end: FRAGMENT.end\n        }, {\n          match: XML_SELF_CLOSING\n        }, {\n          begin: XML_TAG.begin,\n          // we carefully check the opening tag to see if it truly\n          // is a tag and not a false positive\n          'on:begin': XML_TAG.isTrulyOpeningTag,\n          end: XML_TAG.end\n        }],\n        subLanguage: 'xml',\n        contains: [{\n          begin: XML_TAG.begin,\n          end: XML_TAG.end,\n          skip: true,\n          contains: ['self']\n        }]\n      }]\n    }, FUNCTION_DEFINITION, {\n      // prevent this from getting swallowed up by function\n      // since they appear \"function like\"\n      beginKeywords: \"while if switch catch for\"\n    }, {\n      // we have to count the parens to make sure we actually have the correct\n      // bounding ( ).  There could be any number of sub-expressions inside\n      // also surrounded by parens.\n      begin: '\\\\b(?!function)' + hljs.UNDERSCORE_IDENT_RE + '\\\\(' +\n      // first parens\n      '[^()]*(\\\\(' + '[^()]*(\\\\(' + '[^()]*' + '\\\\)[^()]*)*' + '\\\\)[^()]*)*' + '\\\\)\\\\s*\\\\{',\n      // end parens\n      returnBegin: true,\n      label: \"func.def\",\n      contains: [PARAMS, hljs.inherit(hljs.TITLE_MODE, {\n        begin: IDENT_RE$1,\n        className: \"title.function\"\n      })]\n    },\n    // catch ... so it won't trigger the property rule below\n    {\n      match: /\\.\\.\\./,\n      relevance: 0\n    }, PROPERTY_ACCESS,\n    // hack: prevents detection of keywords in some circumstances\n    // .keyword()\n    // $keyword = x\n    {\n      match: '\\\\$' + IDENT_RE$1,\n      relevance: 0\n    }, {\n      match: [/\\bconstructor(?=\\s*\\()/],\n      className: {\n        1: \"title.function\"\n      },\n      contains: [PARAMS]\n    }, FUNCTION_CALL, UPPER_CASE_CONSTANT, CLASS_OR_EXTENDS, GETTER_OR_SETTER, {\n      match: /\\$[(.]/ // relevance booster for a pattern common to JS libs: `$(something)` and `$.something`\n    }]\n  };\n}\nexport { javascript as default };", "map": {"version": 3, "names": ["IDENT_RE", "KEYWORDS", "LITERALS", "TYPES", "ERROR_TYPES", "BUILT_IN_GLOBALS", "BUILT_IN_VARIABLES", "BUILT_INS", "concat", "javascript", "hljs", "regex", "hasClosingTag", "match", "_ref", "after", "tag", "slice", "pos", "input", "indexOf", "IDENT_RE$1", "FRAGMENT", "begin", "end", "XML_SELF_CLOSING", "XML_TAG", "isTrulyOpeningTag", "response", "afterMatchIndex", "length", "index", "nextChar", "ignoreMatch", "m", "afterMatch", "substring", "KEYWORDS$1", "$pattern", "keyword", "literal", "built_in", "decimalDigits", "frac", "decimalInteger", "NUMBER", "className", "variants", "relevance", "SUBST", "keywords", "contains", "HTML_TEMPLATE", "starts", "returnEnd", "BACKSLASH_ESCAPE", "subLanguage", "CSS_TEMPLATE", "GRAPHQL_TEMPLATE", "TEMPLATE_STRING", "JSDOC_COMMENT", "COMMENT", "excludeEnd", "excludeBegin", "endsParent", "C_BLOCK_COMMENT_MODE", "C_LINE_COMMENT_MODE", "SUBST_INTERNALS", "APOS_STRING_MODE", "QUOTE_STRING_MODE", "SUBST_AND_COMMENTS", "PARAMS_CONTAINS", "PARAMS", "CLASS_OR_EXTENDS", "scope", "CLASS_REFERENCE", "either", "_", "USE_STRICT", "label", "FUNCTION_DEFINITION", "illegal", "UPPER_CASE_CONSTANT", "noneOf", "list", "join", "FUNCTION_CALL", "map", "x", "<PERSON><PERSON><PERSON>", "PROPERTY_ACCESS", "GETTER_OR_SETTER", "FUNC_LEAD_IN_RE", "UNDERSCORE_IDENT_RE", "FUNCTION_VARIABLE", "name", "aliases", "exports", "SHEBANG", "binary", "RE_STARTERS_RE", "REGEXP_MODE", "returnBegin", "skip", "beginKeywords", "inherit", "TITLE_MODE", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/highlight.js@11.11.1/node_modules/highlight.js/es/languages/javascript.js"], "sourcesContent": ["const IDENT_RE = '[A-Za-z$_][0-9A-Za-z$_]*';\nconst KEYWORDS = [\n  \"as\", // for exports\n  \"in\",\n  \"of\",\n  \"if\",\n  \"for\",\n  \"while\",\n  \"finally\",\n  \"var\",\n  \"new\",\n  \"function\",\n  \"do\",\n  \"return\",\n  \"void\",\n  \"else\",\n  \"break\",\n  \"catch\",\n  \"instanceof\",\n  \"with\",\n  \"throw\",\n  \"case\",\n  \"default\",\n  \"try\",\n  \"switch\",\n  \"continue\",\n  \"typeof\",\n  \"delete\",\n  \"let\",\n  \"yield\",\n  \"const\",\n  \"class\",\n  // J<PERSON> handles these with a special rule\n  // \"get\",\n  // \"set\",\n  \"debugger\",\n  \"async\",\n  \"await\",\n  \"static\",\n  \"import\",\n  \"from\",\n  \"export\",\n  \"extends\",\n  // It's reached stage 3, which is \"recommended for implementation\":\n  \"using\"\n];\nconst LITERALS = [\n  \"true\",\n  \"false\",\n  \"null\",\n  \"undefined\",\n  \"NaN\",\n  \"Infinity\"\n];\n\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects\nconst TYPES = [\n  // Fundamental objects\n  \"Object\",\n  \"Function\",\n  \"Boolean\",\n  \"Symbol\",\n  // numbers and dates\n  \"Math\",\n  \"Date\",\n  \"Number\",\n  \"BigInt\",\n  // text\n  \"String\",\n  \"RegExp\",\n  // Indexed collections\n  \"Array\",\n  \"Float32Array\",\n  \"Float64Array\",\n  \"Int8Array\",\n  \"Uint8Array\",\n  \"Uint8ClampedArray\",\n  \"Int16Array\",\n  \"Int32Array\",\n  \"Uint16Array\",\n  \"Uint32Array\",\n  \"BigInt64Array\",\n  \"BigUint64Array\",\n  // Keyed collections\n  \"Set\",\n  \"Map\",\n  \"WeakSet\",\n  \"WeakMap\",\n  // Structured data\n  \"ArrayBuffer\",\n  \"SharedArrayBuffer\",\n  \"Atomics\",\n  \"DataView\",\n  \"JSON\",\n  // Control abstraction objects\n  \"Promise\",\n  \"Generator\",\n  \"GeneratorFunction\",\n  \"AsyncFunction\",\n  // Reflection\n  \"Reflect\",\n  \"Proxy\",\n  // Internationalization\n  \"Intl\",\n  // WebAssembly\n  \"WebAssembly\"\n];\n\nconst ERROR_TYPES = [\n  \"Error\",\n  \"EvalError\",\n  \"InternalError\",\n  \"RangeError\",\n  \"ReferenceError\",\n  \"SyntaxError\",\n  \"TypeError\",\n  \"URIError\"\n];\n\nconst BUILT_IN_GLOBALS = [\n  \"setInterval\",\n  \"setTimeout\",\n  \"clearInterval\",\n  \"clearTimeout\",\n\n  \"require\",\n  \"exports\",\n\n  \"eval\",\n  \"isFinite\",\n  \"isNaN\",\n  \"parseFloat\",\n  \"parseInt\",\n  \"decodeURI\",\n  \"decodeURIComponent\",\n  \"encodeURI\",\n  \"encodeURIComponent\",\n  \"escape\",\n  \"unescape\"\n];\n\nconst BUILT_IN_VARIABLES = [\n  \"arguments\",\n  \"this\",\n  \"super\",\n  \"console\",\n  \"window\",\n  \"document\",\n  \"localStorage\",\n  \"sessionStorage\",\n  \"module\",\n  \"global\" // Node.js\n];\n\nconst BUILT_INS = [].concat(\n  BUILT_IN_GLOBALS,\n  TYPES,\n  ERROR_TYPES\n);\n\n/*\nLanguage: JavaScript\nDescription: JavaScript (JS) is a lightweight, interpreted, or just-in-time compiled programming language with first-class functions.\nCategory: common, scripting, web\nWebsite: https://developer.mozilla.org/en-US/docs/Web/JavaScript\n*/\n\n\n/** @type LanguageFn */\nfunction javascript(hljs) {\n  const regex = hljs.regex;\n  /**\n   * Takes a string like \"<Booger\" and checks to see\n   * if we can find a matching \"</Booger\" later in the\n   * content.\n   * @param {RegExpMatchArray} match\n   * @param {{after:number}} param1\n   */\n  const hasClosingTag = (match, { after }) => {\n    const tag = \"</\" + match[0].slice(1);\n    const pos = match.input.indexOf(tag, after);\n    return pos !== -1;\n  };\n\n  const IDENT_RE$1 = IDENT_RE;\n  const FRAGMENT = {\n    begin: '<>',\n    end: '</>'\n  };\n  // to avoid some special cases inside isTrulyOpeningTag\n  const XML_SELF_CLOSING = /<[A-Za-z0-9\\\\._:-]+\\s*\\/>/;\n  const XML_TAG = {\n    begin: /<[A-Za-z0-9\\\\._:-]+/,\n    end: /\\/[A-Za-z0-9\\\\._:-]+>|\\/>/,\n    /**\n     * @param {RegExpMatchArray} match\n     * @param {CallbackResponse} response\n     */\n    isTrulyOpeningTag: (match, response) => {\n      const afterMatchIndex = match[0].length + match.index;\n      const nextChar = match.input[afterMatchIndex];\n      if (\n        // HTML should not include another raw `<` inside a tag\n        // nested type?\n        // `<Array<Array<number>>`, etc.\n        nextChar === \"<\" ||\n        // the , gives away that this is not HTML\n        // `<T, A extends keyof T, V>`\n        nextChar === \",\"\n        ) {\n        response.ignoreMatch();\n        return;\n      }\n\n      // `<something>`\n      // Quite possibly a tag, lets look for a matching closing tag...\n      if (nextChar === \">\") {\n        // if we cannot find a matching closing tag, then we\n        // will ignore it\n        if (!hasClosingTag(match, { after: afterMatchIndex })) {\n          response.ignoreMatch();\n        }\n      }\n\n      // `<blah />` (self-closing)\n      // handled by simpleSelfClosing rule\n\n      let m;\n      const afterMatch = match.input.substring(afterMatchIndex);\n\n      // some more template typing stuff\n      //  <T = any>(key?: string) => Modify<\n      if ((m = afterMatch.match(/^\\s*=/))) {\n        response.ignoreMatch();\n        return;\n      }\n\n      // `<From extends string>`\n      // technically this could be HTML, but it smells like a type\n      // NOTE: This is ugh, but added specifically for https://github.com/highlightjs/highlight.js/issues/3276\n      if ((m = afterMatch.match(/^\\s+extends\\s+/))) {\n        if (m.index === 0) {\n          response.ignoreMatch();\n          // eslint-disable-next-line no-useless-return\n          return;\n        }\n      }\n    }\n  };\n  const KEYWORDS$1 = {\n    $pattern: IDENT_RE,\n    keyword: KEYWORDS,\n    literal: LITERALS,\n    built_in: BUILT_INS,\n    \"variable.language\": BUILT_IN_VARIABLES\n  };\n\n  // https://tc39.es/ecma262/#sec-literals-numeric-literals\n  const decimalDigits = '[0-9](_?[0-9])*';\n  const frac = `\\\\.(${decimalDigits})`;\n  // DecimalIntegerLiteral, including Annex B NonOctalDecimalIntegerLiteral\n  // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n  const decimalInteger = `0|[1-9](_?[0-9])*|0[0-7]*[89][0-9]*`;\n  const NUMBER = {\n    className: 'number',\n    variants: [\n      // DecimalLiteral\n      { begin: `(\\\\b(${decimalInteger})((${frac})|\\\\.)?|(${frac}))` +\n        `[eE][+-]?(${decimalDigits})\\\\b` },\n      { begin: `\\\\b(${decimalInteger})\\\\b((${frac})\\\\b|\\\\.)?|(${frac})\\\\b` },\n\n      // DecimalBigIntegerLiteral\n      { begin: `\\\\b(0|[1-9](_?[0-9])*)n\\\\b` },\n\n      // NonDecimalIntegerLiteral\n      { begin: \"\\\\b0[xX][0-9a-fA-F](_?[0-9a-fA-F])*n?\\\\b\" },\n      { begin: \"\\\\b0[bB][0-1](_?[0-1])*n?\\\\b\" },\n      { begin: \"\\\\b0[oO][0-7](_?[0-7])*n?\\\\b\" },\n\n      // LegacyOctalIntegerLiteral (does not include underscore separators)\n      // https://tc39.es/ecma262/#sec-additional-syntax-numeric-literals\n      { begin: \"\\\\b0[0-7]+n?\\\\b\" },\n    ],\n    relevance: 0\n  };\n\n  const SUBST = {\n    className: 'subst',\n    begin: '\\\\$\\\\{',\n    end: '\\\\}',\n    keywords: KEYWORDS$1,\n    contains: [] // defined later\n  };\n  const HTML_TEMPLATE = {\n    begin: '\\.?html`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'xml'\n    }\n  };\n  const CSS_TEMPLATE = {\n    begin: '\\.?css`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'css'\n    }\n  };\n  const GRAPHQL_TEMPLATE = {\n    begin: '\\.?gql`',\n    end: '',\n    starts: {\n      end: '`',\n      returnEnd: false,\n      contains: [\n        hljs.BACKSLASH_ESCAPE,\n        SUBST\n      ],\n      subLanguage: 'graphql'\n    }\n  };\n  const TEMPLATE_STRING = {\n    className: 'string',\n    begin: '`',\n    end: '`',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ]\n  };\n  const JSDOC_COMMENT = hljs.COMMENT(\n    /\\/\\*\\*(?!\\/)/,\n    '\\\\*/',\n    {\n      relevance: 0,\n      contains: [\n        {\n          begin: '(?=@[A-Za-z]+)',\n          relevance: 0,\n          contains: [\n            {\n              className: 'doctag',\n              begin: '@[A-Za-z]+'\n            },\n            {\n              className: 'type',\n              begin: '\\\\{',\n              end: '\\\\}',\n              excludeEnd: true,\n              excludeBegin: true,\n              relevance: 0\n            },\n            {\n              className: 'variable',\n              begin: IDENT_RE$1 + '(?=\\\\s*(-)|$)',\n              endsParent: true,\n              relevance: 0\n            },\n            // eat spaces (not newlines) so we can find\n            // types or variables\n            {\n              begin: /(?=[^\\n])\\s/,\n              relevance: 0\n            }\n          ]\n        }\n      ]\n    }\n  );\n  const COMMENT = {\n    className: \"comment\",\n    variants: [\n      JSDOC_COMMENT,\n      hljs.C_BLOCK_COMMENT_MODE,\n      hljs.C_LINE_COMMENT_MODE\n    ]\n  };\n  const SUBST_INTERNALS = [\n    hljs.APOS_STRING_MODE,\n    hljs.QUOTE_STRING_MODE,\n    HTML_TEMPLATE,\n    CSS_TEMPLATE,\n    GRAPHQL_TEMPLATE,\n    TEMPLATE_STRING,\n    // Skip numbers when they are part of a variable name\n    { match: /\\$\\d+/ },\n    NUMBER,\n    // This is intentional:\n    // See https://github.com/highlightjs/highlight.js/issues/3288\n    // hljs.REGEXP_MODE\n  ];\n  SUBST.contains = SUBST_INTERNALS\n    .concat({\n      // we need to pair up {} inside our subst to prevent\n      // it from ending too early by matching another }\n      begin: /\\{/,\n      end: /\\}/,\n      keywords: KEYWORDS$1,\n      contains: [\n        \"self\"\n      ].concat(SUBST_INTERNALS)\n    });\n  const SUBST_AND_COMMENTS = [].concat(COMMENT, SUBST.contains);\n  const PARAMS_CONTAINS = SUBST_AND_COMMENTS.concat([\n    // eat recursive parens in sub expressions\n    {\n      begin: /(\\s*)\\(/,\n      end: /\\)/,\n      keywords: KEYWORDS$1,\n      contains: [\"self\"].concat(SUBST_AND_COMMENTS)\n    }\n  ]);\n  const PARAMS = {\n    className: 'params',\n    // convert this to negative lookbehind in v12\n    begin: /(\\s*)\\(/, // to match the parms with\n    end: /\\)/,\n    excludeBegin: true,\n    excludeEnd: true,\n    keywords: KEYWORDS$1,\n    contains: PARAMS_CONTAINS\n  };\n\n  // ES6 classes\n  const CLASS_OR_EXTENDS = {\n    variants: [\n      // class Car extends vehicle\n      {\n        match: [\n          /class/,\n          /\\s+/,\n          IDENT_RE$1,\n          /\\s+/,\n          /extends/,\n          /\\s+/,\n          regex.concat(IDENT_RE$1, \"(\", regex.concat(/\\./, IDENT_RE$1), \")*\")\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\",\n          5: \"keyword\",\n          7: \"title.class.inherited\"\n        }\n      },\n      // class Car\n      {\n        match: [\n          /class/,\n          /\\s+/,\n          IDENT_RE$1\n        ],\n        scope: {\n          1: \"keyword\",\n          3: \"title.class\"\n        }\n      },\n\n    ]\n  };\n\n  const CLASS_REFERENCE = {\n    relevance: 0,\n    match:\n    regex.either(\n      // Hard coded exceptions\n      /\\bJSON/,\n      // Float32Array, OutT\n      /\\b[A-Z][a-z]+([A-Z][a-z]*|\\d)*/,\n      // CSSFactory, CSSFactoryT\n      /\\b[A-Z]{2,}([A-Z][a-z]+|\\d)+([A-Z][a-z]*)*/,\n      // FPs, FPsT\n      /\\b[A-Z]{2,}[a-z]+([A-Z][a-z]+|\\d)*([A-Z][a-z]*)*/,\n      // P\n      // single letters are not highlighted\n      // BLAH\n      // this will be flagged as a UPPER_CASE_CONSTANT instead\n    ),\n    className: \"title.class\",\n    keywords: {\n      _: [\n        // se we still get relevance credit for JS library classes\n        ...TYPES,\n        ...ERROR_TYPES\n      ]\n    }\n  };\n\n  const USE_STRICT = {\n    label: \"use_strict\",\n    className: 'meta',\n    relevance: 10,\n    begin: /^\\s*['\"]use (strict|asm)['\"]/\n  };\n\n  const FUNCTION_DEFINITION = {\n    variants: [\n      {\n        match: [\n          /function/,\n          /\\s+/,\n          IDENT_RE$1,\n          /(?=\\s*\\()/\n        ]\n      },\n      // anonymous function\n      {\n        match: [\n          /function/,\n          /\\s*(?=\\()/\n        ]\n      }\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    label: \"func.def\",\n    contains: [ PARAMS ],\n    illegal: /%/\n  };\n\n  const UPPER_CASE_CONSTANT = {\n    relevance: 0,\n    match: /\\b[A-Z][A-Z_0-9]+\\b/,\n    className: \"variable.constant\"\n  };\n\n  function noneOf(list) {\n    return regex.concat(\"(?!\", list.join(\"|\"), \")\");\n  }\n\n  const FUNCTION_CALL = {\n    match: regex.concat(\n      /\\b/,\n      noneOf([\n        ...BUILT_IN_GLOBALS,\n        \"super\",\n        \"import\"\n      ].map(x => `${x}\\\\s*\\\\(`)),\n      IDENT_RE$1, regex.lookahead(/\\s*\\(/)),\n    className: \"title.function\",\n    relevance: 0\n  };\n\n  const PROPERTY_ACCESS = {\n    begin: regex.concat(/\\./, regex.lookahead(\n      regex.concat(IDENT_RE$1, /(?![0-9A-Za-z$_(])/)\n    )),\n    end: IDENT_RE$1,\n    excludeBegin: true,\n    keywords: \"prototype\",\n    className: \"property\",\n    relevance: 0\n  };\n\n  const GETTER_OR_SETTER = {\n    match: [\n      /get|set/,\n      /\\s+/,\n      IDENT_RE$1,\n      /(?=\\()/\n    ],\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      { // eat to avoid empty params\n        begin: /\\(\\)/\n      },\n      PARAMS\n    ]\n  };\n\n  const FUNC_LEAD_IN_RE = '(\\\\(' +\n    '[^()]*(\\\\(' +\n    '[^()]*(\\\\(' +\n    '[^()]*' +\n    '\\\\)[^()]*)*' +\n    '\\\\)[^()]*)*' +\n    '\\\\)|' + hljs.UNDERSCORE_IDENT_RE + ')\\\\s*=>';\n\n  const FUNCTION_VARIABLE = {\n    match: [\n      /const|var|let/, /\\s+/,\n      IDENT_RE$1, /\\s*/,\n      /=\\s*/,\n      /(async\\s*)?/, // async is optional\n      regex.lookahead(FUNC_LEAD_IN_RE)\n    ],\n    keywords: \"async\",\n    className: {\n      1: \"keyword\",\n      3: \"title.function\"\n    },\n    contains: [\n      PARAMS\n    ]\n  };\n\n  return {\n    name: 'JavaScript',\n    aliases: ['js', 'jsx', 'mjs', 'cjs'],\n    keywords: KEYWORDS$1,\n    // this will be extended by TypeScript\n    exports: { PARAMS_CONTAINS, CLASS_REFERENCE },\n    illegal: /#(?![$_A-z])/,\n    contains: [\n      hljs.SHEBANG({\n        label: \"shebang\",\n        binary: \"node\",\n        relevance: 5\n      }),\n      USE_STRICT,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      HTML_TEMPLATE,\n      CSS_TEMPLATE,\n      GRAPHQL_TEMPLATE,\n      TEMPLATE_STRING,\n      COMMENT,\n      // Skip numbers when they are part of a variable name\n      { match: /\\$\\d+/ },\n      NUMBER,\n      CLASS_REFERENCE,\n      {\n        scope: 'attr',\n        match: IDENT_RE$1 + regex.lookahead(':'),\n        relevance: 0\n      },\n      FUNCTION_VARIABLE,\n      { // \"value\" container\n        begin: '(' + hljs.RE_STARTERS_RE + '|\\\\b(case|return|throw)\\\\b)\\\\s*',\n        keywords: 'return throw case',\n        relevance: 0,\n        contains: [\n          COMMENT,\n          hljs.REGEXP_MODE,\n          {\n            className: 'function',\n            // we have to count the parens to make sure we actually have the\n            // correct bounding ( ) before the =>.  There could be any number of\n            // sub-expressions inside also surrounded by parens.\n            begin: FUNC_LEAD_IN_RE,\n            returnBegin: true,\n            end: '\\\\s*=>',\n            contains: [\n              {\n                className: 'params',\n                variants: [\n                  {\n                    begin: hljs.UNDERSCORE_IDENT_RE,\n                    relevance: 0\n                  },\n                  {\n                    className: null,\n                    begin: /\\(\\s*\\)/,\n                    skip: true\n                  },\n                  {\n                    begin: /(\\s*)\\(/,\n                    end: /\\)/,\n                    excludeBegin: true,\n                    excludeEnd: true,\n                    keywords: KEYWORDS$1,\n                    contains: PARAMS_CONTAINS\n                  }\n                ]\n              }\n            ]\n          },\n          { // could be a comma delimited list of params to a function call\n            begin: /,/,\n            relevance: 0\n          },\n          {\n            match: /\\s+/,\n            relevance: 0\n          },\n          { // JSX\n            variants: [\n              { begin: FRAGMENT.begin, end: FRAGMENT.end },\n              { match: XML_SELF_CLOSING },\n              {\n                begin: XML_TAG.begin,\n                // we carefully check the opening tag to see if it truly\n                // is a tag and not a false positive\n                'on:begin': XML_TAG.isTrulyOpeningTag,\n                end: XML_TAG.end\n              }\n            ],\n            subLanguage: 'xml',\n            contains: [\n              {\n                begin: XML_TAG.begin,\n                end: XML_TAG.end,\n                skip: true,\n                contains: ['self']\n              }\n            ]\n          }\n        ],\n      },\n      FUNCTION_DEFINITION,\n      {\n        // prevent this from getting swallowed up by function\n        // since they appear \"function like\"\n        beginKeywords: \"while if switch catch for\"\n      },\n      {\n        // we have to count the parens to make sure we actually have the correct\n        // bounding ( ).  There could be any number of sub-expressions inside\n        // also surrounded by parens.\n        begin: '\\\\b(?!function)' + hljs.UNDERSCORE_IDENT_RE +\n          '\\\\(' + // first parens\n          '[^()]*(\\\\(' +\n            '[^()]*(\\\\(' +\n              '[^()]*' +\n            '\\\\)[^()]*)*' +\n          '\\\\)[^()]*)*' +\n          '\\\\)\\\\s*\\\\{', // end parens\n        returnBegin:true,\n        label: \"func.def\",\n        contains: [\n          PARAMS,\n          hljs.inherit(hljs.TITLE_MODE, { begin: IDENT_RE$1, className: \"title.function\" })\n        ]\n      },\n      // catch ... so it won't trigger the property rule below\n      {\n        match: /\\.\\.\\./,\n        relevance: 0\n      },\n      PROPERTY_ACCESS,\n      // hack: prevents detection of keywords in some circumstances\n      // .keyword()\n      // $keyword = x\n      {\n        match: '\\\\$' + IDENT_RE$1,\n        relevance: 0\n      },\n      {\n        match: [ /\\bconstructor(?=\\s*\\()/ ],\n        className: { 1: \"title.function\" },\n        contains: [ PARAMS ]\n      },\n      FUNCTION_CALL,\n      UPPER_CASE_CONSTANT,\n      CLASS_OR_EXTENDS,\n      GETTER_OR_SETTER,\n      {\n        match: /\\$[(.]/ // relevance booster for a pattern common to JS libs: `$(something)` and `$.something`\n      }\n    ]\n  };\n}\n\nexport { javascript as default };\n"], "mappings": "AAAA,IAAMA,QAAQ,GAAG,0BAA0B;AAC3C,IAAMC,QAAQ,GAAG,CACf,IAAI;AAAE;AACN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,KAAK,EACL,OAAO,EACP,SAAS,EACT,KAAK,EACL,KAAK,EACL,UAAU,EACV,IAAI,EACJ,QAAQ,EACR,MAAM,EACN,MAAM,EACN,OAAO,EACP,OAAO,EACP,YAAY,EACZ,MAAM,EACN,OAAO,EACP,MAAM,EACN,SAAS,EACT,KAAK,EACL,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,KAAK,EACL,OAAO,EACP,OAAO,EACP,OAAO;AACP;AACA;AACA;AACA,UAAU,EACV,OAAO,EACP,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,SAAS;AACT;AACA,OAAO,CACR;AACD,IAAMC,QAAQ,GAAG,CACf,MAAM,EACN,OAAO,EACP,MAAM,EACN,WAAW,EACX,KAAK,EACL,UAAU,CACX;;AAED;AACA,IAAMC,KAAK,GAAG;AACZ;AACA,QAAQ,EACR,UAAU,EACV,SAAS,EACT,QAAQ;AACR;AACA,MAAM,EACN,MAAM,EACN,QAAQ,EACR,QAAQ;AACR;AACA,QAAQ,EACR,QAAQ;AACR;AACA,OAAO,EACP,cAAc,EACd,cAAc,EACd,WAAW,EACX,YAAY,EACZ,mBAAmB,EACnB,YAAY,EACZ,YAAY,EACZ,aAAa,EACb,aAAa,EACb,eAAe,EACf,gBAAgB;AAChB;AACA,KAAK,EACL,KAAK,EACL,SAAS,EACT,SAAS;AACT;AACA,aAAa,EACb,mBAAmB,EACnB,SAAS,EACT,UAAU,EACV,MAAM;AACN;AACA,SAAS,EACT,WAAW,EACX,mBAAmB,EACnB,eAAe;AACf;AACA,SAAS,EACT,OAAO;AACP;AACA,MAAM;AACN;AACA,aAAa,CACd;AAED,IAAMC,WAAW,GAAG,CAClB,OAAO,EACP,WAAW,EACX,eAAe,EACf,YAAY,EACZ,gBAAgB,EAChB,aAAa,EACb,WAAW,EACX,UAAU,CACX;AAED,IAAMC,gBAAgB,GAAG,CACvB,aAAa,EACb,YAAY,EACZ,eAAe,EACf,cAAc,EAEd,SAAS,EACT,SAAS,EAET,MAAM,EACN,UAAU,EACV,OAAO,EACP,YAAY,EACZ,UAAU,EACV,WAAW,EACX,oBAAoB,EACpB,WAAW,EACX,oBAAoB,EACpB,QAAQ,EACR,UAAU,CACX;AAED,IAAMC,kBAAkB,GAAG,CACzB,WAAW,EACX,MAAM,EACN,OAAO,EACP,SAAS,EACT,QAAQ,EACR,UAAU,EACV,cAAc,EACd,gBAAgB,EAChB,QAAQ,EACR,QAAQ,CAAC;AAAA,CACV;AAED,IAAMC,SAAS,GAAG,EAAE,CAACC,MAAM,CACzBH,gBAAgB,EAChBF,KAAK,EACLC,WACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;;AAGA;AACA,SAASK,UAAUA,CAACC,IAAI,EAAE;EACxB,IAAMC,KAAK,GAAGD,IAAI,CAACC,KAAK;EACxB;AACF;AACA;AACA;AACA;AACA;AACA;EACE,IAAMC,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,KAAK,EAAAC,IAAA,EAAgB;IAAA,IAAZC,KAAK,GAAAD,IAAA,CAALC,KAAK;IACnC,IAAMC,GAAG,GAAG,IAAI,GAAGH,KAAK,CAAC,CAAC,CAAC,CAACI,KAAK,CAAC,CAAC,CAAC;IACpC,IAAMC,GAAG,GAAGL,KAAK,CAACM,KAAK,CAACC,OAAO,CAACJ,GAAG,EAAED,KAAK,CAAC;IAC3C,OAAOG,GAAG,KAAK,CAAC,CAAC;EACnB,CAAC;EAED,IAAMG,UAAU,GAAGrB,QAAQ;EAC3B,IAAMsB,QAAQ,GAAG;IACfC,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE;EACP,CAAC;EACD;EACA,IAAMC,gBAAgB,GAAG,2BAA2B;EACpD,IAAMC,OAAO,GAAG;IACdH,KAAK,EAAE,qBAAqB;IAC5BC,GAAG,EAAE,2BAA2B;IAChC;AACJ;AACA;AACA;IACIG,iBAAiB,EAAE,SAAnBA,iBAAiBA,CAAGd,KAAK,EAAEe,QAAQ,EAAK;MACtC,IAAMC,eAAe,GAAGhB,KAAK,CAAC,CAAC,CAAC,CAACiB,MAAM,GAAGjB,KAAK,CAACkB,KAAK;MACrD,IAAMC,QAAQ,GAAGnB,KAAK,CAACM,KAAK,CAACU,eAAe,CAAC;MAC7C;MACE;MACA;MACA;MACAG,QAAQ,KAAK,GAAG;MAChB;MACA;MACAA,QAAQ,KAAK,GAAG,EACd;QACFJ,QAAQ,CAACK,WAAW,CAAC,CAAC;QACtB;MACF;;MAEA;MACA;MACA,IAAID,QAAQ,KAAK,GAAG,EAAE;QACpB;QACA;QACA,IAAI,CAACpB,aAAa,CAACC,KAAK,EAAE;UAAEE,KAAK,EAAEc;QAAgB,CAAC,CAAC,EAAE;UACrDD,QAAQ,CAACK,WAAW,CAAC,CAAC;QACxB;MACF;;MAEA;MACA;;MAEA,IAAIC,CAAC;MACL,IAAMC,UAAU,GAAGtB,KAAK,CAACM,KAAK,CAACiB,SAAS,CAACP,eAAe,CAAC;;MAEzD;MACA;MACA,IAAKK,CAAC,GAAGC,UAAU,CAACtB,KAAK,CAAC,OAAO,CAAC,EAAG;QACnCe,QAAQ,CAACK,WAAW,CAAC,CAAC;QACtB;MACF;;MAEA;MACA;MACA;MACA,IAAKC,CAAC,GAAGC,UAAU,CAACtB,KAAK,CAAC,gBAAgB,CAAC,EAAG;QAC5C,IAAIqB,CAAC,CAACH,KAAK,KAAK,CAAC,EAAE;UACjBH,QAAQ,CAACK,WAAW,CAAC,CAAC;UACtB;UACA;QACF;MACF;IACF;EACF,CAAC;EACD,IAAMI,UAAU,GAAG;IACjBC,QAAQ,EAAEtC,QAAQ;IAClBuC,OAAO,EAAEtC,QAAQ;IACjBuC,OAAO,EAAEtC,QAAQ;IACjBuC,QAAQ,EAAElC,SAAS;IACnB,mBAAmB,EAAED;EACvB,CAAC;;EAED;EACA,IAAMoC,aAAa,GAAG,iBAAiB;EACvC,IAAMC,IAAI,GAAG,OAAOD,aAAa,GAAG;EACpC;EACA;EACA,IAAME,cAAc,GAAG,qCAAqC;EAC5D,IAAMC,MAAM,GAAG;IACbC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE;IACR;IACA;MAAExB,KAAK,EAAE,QAAQqB,cAAc,MAAMD,IAAI,YAAYA,IAAI,IAAI,GAC3D,aAAaD,aAAa;IAAO,CAAC,EACpC;MAAEnB,KAAK,EAAE,OAAOqB,cAAc,SAASD,IAAI,eAAeA,IAAI;IAAO,CAAC;IAEtE;IACA;MAAEpB,KAAK,EAAE;IAA6B,CAAC;IAEvC;IACA;MAAEA,KAAK,EAAE;IAA2C,CAAC,EACrD;MAAEA,KAAK,EAAE;IAA+B,CAAC,EACzC;MAAEA,KAAK,EAAE;IAA+B,CAAC;IAEzC;IACA;IACA;MAAEA,KAAK,EAAE;IAAkB,CAAC,CAC7B;IACDyB,SAAS,EAAE;EACb,CAAC;EAED,IAAMC,KAAK,GAAG;IACZH,SAAS,EAAE,OAAO;IAClBvB,KAAK,EAAE,QAAQ;IACfC,GAAG,EAAE,KAAK;IACV0B,QAAQ,EAAEb,UAAU;IACpBc,QAAQ,EAAE,EAAE,CAAC;EACf,CAAC;EACD,IAAMC,aAAa,GAAG;IACpB7B,KAAK,EAAE,UAAU;IACjBC,GAAG,EAAE,EAAE;IACP6B,MAAM,EAAE;MACN7B,GAAG,EAAE,GAAG;MACR8B,SAAS,EAAE,KAAK;MAChBH,QAAQ,EAAE,CACRzC,IAAI,CAAC6C,gBAAgB,EACrBN,KAAK,CACN;MACDO,WAAW,EAAE;IACf;EACF,CAAC;EACD,IAAMC,YAAY,GAAG;IACnBlC,KAAK,EAAE,SAAS;IAChBC,GAAG,EAAE,EAAE;IACP6B,MAAM,EAAE;MACN7B,GAAG,EAAE,GAAG;MACR8B,SAAS,EAAE,KAAK;MAChBH,QAAQ,EAAE,CACRzC,IAAI,CAAC6C,gBAAgB,EACrBN,KAAK,CACN;MACDO,WAAW,EAAE;IACf;EACF,CAAC;EACD,IAAME,gBAAgB,GAAG;IACvBnC,KAAK,EAAE,SAAS;IAChBC,GAAG,EAAE,EAAE;IACP6B,MAAM,EAAE;MACN7B,GAAG,EAAE,GAAG;MACR8B,SAAS,EAAE,KAAK;MAChBH,QAAQ,EAAE,CACRzC,IAAI,CAAC6C,gBAAgB,EACrBN,KAAK,CACN;MACDO,WAAW,EAAE;IACf;EACF,CAAC;EACD,IAAMG,eAAe,GAAG;IACtBb,SAAS,EAAE,QAAQ;IACnBvB,KAAK,EAAE,GAAG;IACVC,GAAG,EAAE,GAAG;IACR2B,QAAQ,EAAE,CACRzC,IAAI,CAAC6C,gBAAgB,EACrBN,KAAK;EAET,CAAC;EACD,IAAMW,aAAa,GAAGlD,IAAI,CAACmD,OAAO,CAChC,cAAc,EACd,MAAM,EACN;IACEb,SAAS,EAAE,CAAC;IACZG,QAAQ,EAAE,CACR;MACE5B,KAAK,EAAE,gBAAgB;MACvByB,SAAS,EAAE,CAAC;MACZG,QAAQ,EAAE,CACR;QACEL,SAAS,EAAE,QAAQ;QACnBvB,KAAK,EAAE;MACT,CAAC,EACD;QACEuB,SAAS,EAAE,MAAM;QACjBvB,KAAK,EAAE,KAAK;QACZC,GAAG,EAAE,KAAK;QACVsC,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE,IAAI;QAClBf,SAAS,EAAE;MACb,CAAC,EACD;QACEF,SAAS,EAAE,UAAU;QACrBvB,KAAK,EAAEF,UAAU,GAAG,eAAe;QACnC2C,UAAU,EAAE,IAAI;QAChBhB,SAAS,EAAE;MACb,CAAC;MACD;MACA;MACA;QACEzB,KAAK,EAAE,aAAa;QACpByB,SAAS,EAAE;MACb,CAAC;IAEL,CAAC;EAEL,CACF,CAAC;EACD,IAAMa,OAAO,GAAG;IACdf,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAE,CACRa,aAAa,EACblD,IAAI,CAACuD,oBAAoB,EACzBvD,IAAI,CAACwD,mBAAmB;EAE5B,CAAC;EACD,IAAMC,eAAe,GAAG,CACtBzD,IAAI,CAAC0D,gBAAgB,EACrB1D,IAAI,CAAC2D,iBAAiB,EACtBjB,aAAa,EACbK,YAAY,EACZC,gBAAgB,EAChBC,eAAe;EACf;EACA;IAAE9C,KAAK,EAAE;EAAQ,CAAC,EAClBgC;EACA;EACA;EACA;EAAA,CACD;EACDI,KAAK,CAACE,QAAQ,GAAGgB,eAAe,CAC7B3D,MAAM,CAAC;IACN;IACA;IACAe,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACT0B,QAAQ,EAAEb,UAAU;IACpBc,QAAQ,EAAE,CACR,MAAM,CACP,CAAC3C,MAAM,CAAC2D,eAAe;EAC1B,CAAC,CAAC;EACJ,IAAMG,kBAAkB,GAAG,EAAE,CAAC9D,MAAM,CAACqD,OAAO,EAAEZ,KAAK,CAACE,QAAQ,CAAC;EAC7D,IAAMoB,eAAe,GAAGD,kBAAkB,CAAC9D,MAAM,CAAC;EAChD;EACA;IACEe,KAAK,EAAE,SAAS;IAChBC,GAAG,EAAE,IAAI;IACT0B,QAAQ,EAAEb,UAAU;IACpBc,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC3C,MAAM,CAAC8D,kBAAkB;EAC9C,CAAC,CACF,CAAC;EACF,IAAME,MAAM,GAAG;IACb1B,SAAS,EAAE,QAAQ;IACnB;IACAvB,KAAK,EAAE,SAAS;IAAE;IAClBC,GAAG,EAAE,IAAI;IACTuC,YAAY,EAAE,IAAI;IAClBD,UAAU,EAAE,IAAI;IAChBZ,QAAQ,EAAEb,UAAU;IACpBc,QAAQ,EAAEoB;EACZ,CAAC;;EAED;EACA,IAAME,gBAAgB,GAAG;IACvB1B,QAAQ,EAAE;IACR;IACA;MACElC,KAAK,EAAE,CACL,OAAO,EACP,KAAK,EACLQ,UAAU,EACV,KAAK,EACL,SAAS,EACT,KAAK,EACLV,KAAK,CAACH,MAAM,CAACa,UAAU,EAAE,GAAG,EAAEV,KAAK,CAACH,MAAM,CAAC,IAAI,EAAEa,UAAU,CAAC,EAAE,IAAI,CAAC,CACpE;MACDqD,KAAK,EAAE;QACL,CAAC,EAAE,SAAS;QACZ,CAAC,EAAE,aAAa;QAChB,CAAC,EAAE,SAAS;QACZ,CAAC,EAAE;MACL;IACF,CAAC;IACD;IACA;MACE7D,KAAK,EAAE,CACL,OAAO,EACP,KAAK,EACLQ,UAAU,CACX;MACDqD,KAAK,EAAE;QACL,CAAC,EAAE,SAAS;QACZ,CAAC,EAAE;MACL;IACF,CAAC;EAGL,CAAC;EAED,IAAMC,eAAe,GAAG;IACtB3B,SAAS,EAAE,CAAC;IACZnC,KAAK,EACLF,KAAK,CAACiE,MAAM;IACV;IACA,QAAQ;IACR;IACA,gCAAgC;IAChC;IACA,4CAA4C;IAC5C;IACA;IACA;IACA;IACA;IACA;IACF,CAAC;IACD9B,SAAS,EAAE,aAAa;IACxBI,QAAQ,EAAE;MACR2B,CAAC,KAAArE,MAAA,CAEIL,KAAK,EACLC,WAAW;IAElB;EACF,CAAC;EAED,IAAM0E,UAAU,GAAG;IACjBC,KAAK,EAAE,YAAY;IACnBjC,SAAS,EAAE,MAAM;IACjBE,SAAS,EAAE,EAAE;IACbzB,KAAK,EAAE;EACT,CAAC;EAED,IAAMyD,mBAAmB,GAAG;IAC1BjC,QAAQ,EAAE,CACR;MACElC,KAAK,EAAE,CACL,UAAU,EACV,KAAK,EACLQ,UAAU,EACV,WAAW;IAEf,CAAC;IACD;IACA;MACER,KAAK,EAAE,CACL,UAAU,EACV,WAAW;IAEf,CAAC,CACF;IACDiC,SAAS,EAAE;MACT,CAAC,EAAE,SAAS;MACZ,CAAC,EAAE;IACL,CAAC;IACDiC,KAAK,EAAE,UAAU;IACjB5B,QAAQ,EAAE,CAAEqB,MAAM,CAAE;IACpBS,OAAO,EAAE;EACX,CAAC;EAED,IAAMC,mBAAmB,GAAG;IAC1BlC,SAAS,EAAE,CAAC;IACZnC,KAAK,EAAE,qBAAqB;IAC5BiC,SAAS,EAAE;EACb,CAAC;EAED,SAASqC,MAAMA,CAACC,IAAI,EAAE;IACpB,OAAOzE,KAAK,CAACH,MAAM,CAAC,KAAK,EAAE4E,IAAI,CAACC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC;EACjD;EAEA,IAAMC,aAAa,GAAG;IACpBzE,KAAK,EAAEF,KAAK,CAACH,MAAM,CACjB,IAAI,EACJ2E,MAAM,CAAC,GAAA3E,MAAA,CACFH,gBAAgB,GACnB,OAAO,EACP,QAAQ,GACRkF,GAAG,CAAC,UAAAC,CAAC;MAAA,OAAI,GAAGA,CAAC,SAAS;IAAA,EAAC,CAAC,EAC1BnE,UAAU,EAAEV,KAAK,CAAC8E,SAAS,CAAC,OAAO,CAAC,CAAC;IACvC3C,SAAS,EAAE,gBAAgB;IAC3BE,SAAS,EAAE;EACb,CAAC;EAED,IAAM0C,eAAe,GAAG;IACtBnE,KAAK,EAAEZ,KAAK,CAACH,MAAM,CAAC,IAAI,EAAEG,KAAK,CAAC8E,SAAS,CACvC9E,KAAK,CAACH,MAAM,CAACa,UAAU,EAAE,oBAAoB,CAC/C,CAAC,CAAC;IACFG,GAAG,EAAEH,UAAU;IACf0C,YAAY,EAAE,IAAI;IAClBb,QAAQ,EAAE,WAAW;IACrBJ,SAAS,EAAE,UAAU;IACrBE,SAAS,EAAE;EACb,CAAC;EAED,IAAM2C,gBAAgB,GAAG;IACvB9E,KAAK,EAAE,CACL,SAAS,EACT,KAAK,EACLQ,UAAU,EACV,QAAQ,CACT;IACDyB,SAAS,EAAE;MACT,CAAC,EAAE,SAAS;MACZ,CAAC,EAAE;IACL,CAAC;IACDK,QAAQ,EAAE,CACR;MAAE;MACA5B,KAAK,EAAE;IACT,CAAC,EACDiD,MAAM;EAEV,CAAC;EAED,IAAMoB,eAAe,GAAG,MAAM,GAC5B,YAAY,GACZ,YAAY,GACZ,QAAQ,GACR,aAAa,GACb,aAAa,GACb,MAAM,GAAGlF,IAAI,CAACmF,mBAAmB,GAAG,SAAS;EAE/C,IAAMC,iBAAiB,GAAG;IACxBjF,KAAK,EAAE,CACL,eAAe,EAAE,KAAK,EACtBQ,UAAU,EAAE,KAAK,EACjB,MAAM,EACN,aAAa;IAAE;IACfV,KAAK,CAAC8E,SAAS,CAACG,eAAe,CAAC,CACjC;IACD1C,QAAQ,EAAE,OAAO;IACjBJ,SAAS,EAAE;MACT,CAAC,EAAE,SAAS;MACZ,CAAC,EAAE;IACL,CAAC;IACDK,QAAQ,EAAE,CACRqB,MAAM;EAEV,CAAC;EAED,OAAO;IACLuB,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE,CAAC,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC;IACpC9C,QAAQ,EAAEb,UAAU;IACpB;IACA4D,OAAO,EAAE;MAAE1B,eAAe;MAAEI;IAAgB,CAAC;IAC7CM,OAAO,EAAE,cAAc;IACvB9B,QAAQ,EAAE,CACRzC,IAAI,CAACwF,OAAO,CAAC;MACXnB,KAAK,EAAE,SAAS;MAChBoB,MAAM,EAAE,MAAM;MACdnD,SAAS,EAAE;IACb,CAAC,CAAC,EACF8B,UAAU,EACVpE,IAAI,CAAC0D,gBAAgB,EACrB1D,IAAI,CAAC2D,iBAAiB,EACtBjB,aAAa,EACbK,YAAY,EACZC,gBAAgB,EAChBC,eAAe,EACfE,OAAO;IACP;IACA;MAAEhD,KAAK,EAAE;IAAQ,CAAC,EAClBgC,MAAM,EACN8B,eAAe,EACf;MACED,KAAK,EAAE,MAAM;MACb7D,KAAK,EAAEQ,UAAU,GAAGV,KAAK,CAAC8E,SAAS,CAAC,GAAG,CAAC;MACxCzC,SAAS,EAAE;IACb,CAAC,EACD8C,iBAAiB,EACjB;MAAE;MACAvE,KAAK,EAAE,GAAG,GAAGb,IAAI,CAAC0F,cAAc,GAAG,iCAAiC;MACpElD,QAAQ,EAAE,mBAAmB;MAC7BF,SAAS,EAAE,CAAC;MACZG,QAAQ,EAAE,CACRU,OAAO,EACPnD,IAAI,CAAC2F,WAAW,EAChB;QACEvD,SAAS,EAAE,UAAU;QACrB;QACA;QACA;QACAvB,KAAK,EAAEqE,eAAe;QACtBU,WAAW,EAAE,IAAI;QACjB9E,GAAG,EAAE,QAAQ;QACb2B,QAAQ,EAAE,CACR;UACEL,SAAS,EAAE,QAAQ;UACnBC,QAAQ,EAAE,CACR;YACExB,KAAK,EAAEb,IAAI,CAACmF,mBAAmB;YAC/B7C,SAAS,EAAE;UACb,CAAC,EACD;YACEF,SAAS,EAAE,IAAI;YACfvB,KAAK,EAAE,SAAS;YAChBgF,IAAI,EAAE;UACR,CAAC,EACD;YACEhF,KAAK,EAAE,SAAS;YAChBC,GAAG,EAAE,IAAI;YACTuC,YAAY,EAAE,IAAI;YAClBD,UAAU,EAAE,IAAI;YAChBZ,QAAQ,EAAEb,UAAU;YACpBc,QAAQ,EAAEoB;UACZ,CAAC;QAEL,CAAC;MAEL,CAAC,EACD;QAAE;QACAhD,KAAK,EAAE,GAAG;QACVyB,SAAS,EAAE;MACb,CAAC,EACD;QACEnC,KAAK,EAAE,KAAK;QACZmC,SAAS,EAAE;MACb,CAAC,EACD;QAAE;QACAD,QAAQ,EAAE,CACR;UAAExB,KAAK,EAAED,QAAQ,CAACC,KAAK;UAAEC,GAAG,EAAEF,QAAQ,CAACE;QAAI,CAAC,EAC5C;UAAEX,KAAK,EAAEY;QAAiB,CAAC,EAC3B;UACEF,KAAK,EAAEG,OAAO,CAACH,KAAK;UACpB;UACA;UACA,UAAU,EAAEG,OAAO,CAACC,iBAAiB;UACrCH,GAAG,EAAEE,OAAO,CAACF;QACf,CAAC,CACF;QACDgC,WAAW,EAAE,KAAK;QAClBL,QAAQ,EAAE,CACR;UACE5B,KAAK,EAAEG,OAAO,CAACH,KAAK;UACpBC,GAAG,EAAEE,OAAO,CAACF,GAAG;UAChB+E,IAAI,EAAE,IAAI;UACVpD,QAAQ,EAAE,CAAC,MAAM;QACnB,CAAC;MAEL,CAAC;IAEL,CAAC,EACD6B,mBAAmB,EACnB;MACE;MACA;MACAwB,aAAa,EAAE;IACjB,CAAC,EACD;MACE;MACA;MACA;MACAjF,KAAK,EAAE,iBAAiB,GAAGb,IAAI,CAACmF,mBAAmB,GACjD,KAAK;MAAG;MACR,YAAY,GACV,YAAY,GACV,QAAQ,GACV,aAAa,GACf,aAAa,GACb,YAAY;MAAE;MAChBS,WAAW,EAAC,IAAI;MAChBvB,KAAK,EAAE,UAAU;MACjB5B,QAAQ,EAAE,CACRqB,MAAM,EACN9D,IAAI,CAAC+F,OAAO,CAAC/F,IAAI,CAACgG,UAAU,EAAE;QAAEnF,KAAK,EAAEF,UAAU;QAAEyB,SAAS,EAAE;MAAiB,CAAC,CAAC;IAErF,CAAC;IACD;IACA;MACEjC,KAAK,EAAE,QAAQ;MACfmC,SAAS,EAAE;IACb,CAAC,EACD0C,eAAe;IACf;IACA;IACA;IACA;MACE7E,KAAK,EAAE,KAAK,GAAGQ,UAAU;MACzB2B,SAAS,EAAE;IACb,CAAC,EACD;MACEnC,KAAK,EAAE,CAAE,wBAAwB,CAAE;MACnCiC,SAAS,EAAE;QAAE,CAAC,EAAE;MAAiB,CAAC;MAClCK,QAAQ,EAAE,CAAEqB,MAAM;IACpB,CAAC,EACDc,aAAa,EACbJ,mBAAmB,EACnBT,gBAAgB,EAChBkB,gBAAgB,EAChB;MACE9E,KAAK,EAAE,QAAQ,CAAC;IAClB,CAAC;EAEL,CAAC;AACH;AAEA,SAASJ,UAAU,IAAIkG,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}