{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _regeneratorRuntime() { \"use strict\"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return e; }; var t, e = {}, r = Object.prototype, n = r.hasOwnProperty, o = Object.defineProperty || function (t, e, r) { t[e] = r.value; }, i = \"function\" == typeof Symbol ? Symbol : {}, a = i.iterator || \"@@iterator\", c = i.asyncIterator || \"@@asyncIterator\", u = i.toStringTag || \"@@toStringTag\"; function define(t, e, r) { return Object.defineProperty(t, e, { value: r, enumerable: !0, configurable: !0, writable: !0 }), t[e]; } try { define({}, \"\"); } catch (t) { define = function define(t, e, r) { return t[e] = r; }; } function wrap(t, e, r, n) { var i = e && e.prototype instanceof Generator ? e : Generator, a = Object.create(i.prototype), c = new Context(n || []); return o(a, \"_invoke\", { value: makeInvokeMethod(t, r, c) }), a; } function tryCatch(t, e, r) { try { return { type: \"normal\", arg: t.call(e, r) }; } catch (t) { return { type: \"throw\", arg: t }; } } e.wrap = wrap; var h = \"suspendedStart\", l = \"suspendedYield\", f = \"executing\", s = \"completed\", y = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var p = {}; define(p, a, function () { return this; }); var d = Object.getPrototypeOf, v = d && d(d(values([]))); v && v !== r && n.call(v, a) && (p = v); var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p); function defineIteratorMethods(t) { [\"next\", \"throw\", \"return\"].forEach(function (e) { define(t, e, function (t) { return this._invoke(e, t); }); }); } function AsyncIterator(t, e) { function invoke(r, o, i, a) { var c = tryCatch(t[r], t, o); if (\"throw\" !== c.type) { var u = c.arg, h = u.value; return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) { invoke(\"next\", t, i, a); }, function (t) { invoke(\"throw\", t, i, a); }) : e.resolve(h).then(function (t) { u.value = t, i(u); }, function (t) { return invoke(\"throw\", t, i, a); }); } a(c.arg); } var r; o(this, \"_invoke\", { value: function value(t, n) { function callInvokeWithMethodAndArg() { return new e(function (e, r) { invoke(t, n, e, r); }); } return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(e, r, n) { var o = h; return function (i, a) { if (o === f) throw Error(\"Generator is already running\"); if (o === s) { if (\"throw\" === i) throw a; return { value: t, done: !0 }; } for (n.method = i, n.arg = a;;) { var c = n.delegate; if (c) { var u = maybeInvokeDelegate(c, n); if (u) { if (u === y) continue; return u; } } if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) { if (o === h) throw o = s, n.arg; n.dispatchException(n.arg); } else \"return\" === n.method && n.abrupt(\"return\", n.arg); o = f; var p = tryCatch(e, r, n); if (\"normal\" === p.type) { if (o = n.done ? s : l, p.arg === y) continue; return { value: p.arg, done: n.done }; } \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg); } }; } function maybeInvokeDelegate(e, r) { var n = r.method, o = e.iterator[n]; if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y; var i = tryCatch(o, e.iterator, r.arg); if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y; var a = i.arg; return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y); } function pushTryEntry(t) { var e = { tryLoc: t[0] }; 1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e); } function resetTryEntry(t) { var e = t.completion || {}; e.type = \"normal\", delete e.arg, t.completion = e; } function Context(t) { this.tryEntries = [{ tryLoc: \"root\" }], t.forEach(pushTryEntry, this), this.reset(!0); } function values(e) { if (e || \"\" === e) { var r = e[a]; if (r) return r.call(e); if (\"function\" == typeof e.next) return e; if (!isNaN(e.length)) { var o = -1, i = function next() { for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next; return next.value = t, next.done = !0, next; }; return i.next = i; } } throw new TypeError(typeof e + \" is not iterable\"); } return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", { value: GeneratorFunctionPrototype, configurable: !0 }), o(GeneratorFunctionPrototype, \"constructor\", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) { var e = \"function\" == typeof t && t.constructor; return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name)); }, e.mark = function (t) { return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t; }, e.awrap = function (t) { return { __await: t }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () { return this; }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) { void 0 === i && (i = Promise); var a = new AsyncIterator(wrap(t, r, n, o), i); return e.isGeneratorFunction(r) ? a : a.next().then(function (t) { return t.done ? t.value : a.next(); }); }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () { return this; }), define(g, \"toString\", function () { return \"[object Generator]\"; }), e.keys = function (t) { var e = Object(t), r = []; for (var n in e) r.push(n); return r.reverse(), function next() { for (; r.length;) { var t = r.pop(); if (t in e) return next.value = t, next.done = !1, next; } return next.done = !0, next; }; }, e.values = values, Context.prototype = { constructor: Context, reset: function reset(e) { if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t); }, stop: function stop() { this.done = !0; var t = this.tryEntries[0].completion; if (\"throw\" === t.type) throw t.arg; return this.rval; }, dispatchException: function dispatchException(e) { if (this.done) throw e; var r = this; function handle(n, o) { return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o; } for (var o = this.tryEntries.length - 1; o >= 0; --o) { var i = this.tryEntries[o], a = i.completion; if (\"root\" === i.tryLoc) return handle(\"end\"); if (i.tryLoc <= this.prev) { var c = n.call(i, \"catchLoc\"), u = n.call(i, \"finallyLoc\"); if (c && u) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } else if (c) { if (this.prev < i.catchLoc) return handle(i.catchLoc, !0); } else { if (!u) throw Error(\"try statement without catch or finally\"); if (this.prev < i.finallyLoc) return handle(i.finallyLoc); } } } }, abrupt: function abrupt(t, e) { for (var r = this.tryEntries.length - 1; r >= 0; --r) { var o = this.tryEntries[r]; if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) { var i = o; break; } } i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null); var a = i ? i.completion : {}; return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a); }, complete: function complete(t, e) { if (\"throw\" === t.type) throw t.arg; return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y; }, finish: function finish(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y; } }, catch: function _catch(t) { for (var e = this.tryEntries.length - 1; e >= 0; --e) { var r = this.tryEntries[e]; if (r.tryLoc === t) { var n = r.completion; if (\"throw\" === n.type) { var o = n.arg; resetTryEntry(r); } return o; } } throw Error(\"illegal catch attempt\"); }, delegateYield: function delegateYield(e, r, n) { return this.delegate = { iterator: values(e), resultName: r, nextLoc: n }, \"next\" === this.method && (this.arg = t), y; } }, e; }\nfunction asyncGeneratorStep(n, t, e, r, o, a, c) { try { var i = n[a](c), u = i.value; } catch (n) { return void e(n); } i.done ? t(u) : Promise.resolve(u).then(r, o); }\nfunction _asyncToGenerator(n) { return function () { var t = this, e = arguments; return new Promise(function (r, o) { var a = n.apply(t, e); function _next(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"next\", n); } function _throw(n) { asyncGeneratorStep(a, r, o, _next, _throw, \"throw\", n); } _next(void 0); }); }; }\nimport api from '@/api';\nimport { emotion } from './emotion.js';\nimport { format } from 'common/js/time.js';\nimport { user, appOnlyHeader } from 'common/js/system_var.js';\nexport var fileIcon = function fileIcon(fileType) {\n  var IconClass = {\n    docx: 'globalFileWord',\n    doc: 'globalFileWord',\n    wps: 'globalFileWPS',\n    xlsx: 'globalFileExcel',\n    xls: 'globalFileExcel',\n    pdf: 'globalFilePDF',\n    pptx: 'globalFilePPT',\n    ppt: 'globalFilePPT',\n    txt: 'globalFileTXT',\n    jpg: 'globalFilePicture',\n    png: 'globalFilePicture',\n    gif: 'globalFilePicture',\n    avi: 'globalFileVideo',\n    mp4: 'globalFileVideo',\n    zip: 'globalFileCompress',\n    rar: 'globalFileCompress'\n  };\n  return IconClass[fileType] || 'globalFileUnknown';\n};\nexport var handleTimeFormat = function handleTimeFormat(time) {\n  if (format(time, 'YYYY') !== format(new Date(), 'YYYY')) {\n    return format(time, 'YYYY-MM-DD');\n  } else if (format(time, 'YYYY-MM-DD') === format(new Date(), 'YYYY-MM-DD')) {\n    return format(time, 'HH:mm');\n  } else {\n    var givenDate = new Date(time);\n    var currentData = new Date();\n    var difference = currentData - givenDate;\n    // 将差值转换成天数\n    var daysDifference = difference / (1000 * 60 * 60 * 24);\n    // 返回是否相差七天\n    return daysDifference <= 7 && daysDifference >= -7 ? format(time, 'EEE') : format(time, 'MM-DD');\n  }\n};\nvar globalFileInfo = /*#__PURE__*/function () {\n  var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(fileId) {\n    var file, _yield$api$globalFile, data;\n    return _regeneratorRuntime().wrap(function _callee$(_context) {\n      while (1) switch (_context.prev = _context.next) {\n        case 0:\n          file = JSON.parse(localStorage.getItem(fileId)) || '';\n          if (!file) {\n            _context.next = 3;\n            break;\n          }\n          return _context.abrupt(\"return\", file);\n        case 3:\n          _context.next = 5;\n          return api.globalFileInfo(fileId);\n        case 5:\n          _yield$api$globalFile = _context.sent;\n          data = _yield$api$globalFile.data;\n          localStorage.setItem(fileId, JSON.stringify(data));\n          return _context.abrupt(\"return\", data);\n        case 9:\n        case \"end\":\n          return _context.stop();\n      }\n    }, _callee);\n  }));\n  return function globalFileInfo(_x) {\n    return _ref.apply(this, arguments);\n  };\n}();\nvar globalVoteInfo = /*#__PURE__*/function () {\n  var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(voteId) {\n    var vote, _yield$api$VoteInfo, data;\n    return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n      while (1) switch (_context2.prev = _context2.next) {\n        case 0:\n          vote = JSON.parse(localStorage.getItem(voteId)) || '';\n          if (!vote) {\n            _context2.next = 3;\n            break;\n          }\n          return _context2.abrupt(\"return\", vote);\n        case 3:\n          _context2.next = 5;\n          return api.VoteInfo({\n            detailId: voteId\n          });\n        case 5:\n          _yield$api$VoteInfo = _context2.sent;\n          data = _yield$api$VoteInfo.data;\n          localStorage.setItem(voteId, JSON.stringify(data));\n          return _context2.abrupt(\"return\", data);\n        case 9:\n        case \"end\":\n          return _context2.stop();\n      }\n    }, _callee2);\n  }));\n  return function globalVoteInfo(_x2) {\n    return _ref2.apply(this, arguments);\n  };\n}();\nvar getContentBetweenBrackets = function getContentBetweenBrackets(str) {\n  var regex = /\\[([^\\]]+)\\]/g;\n  var matches = [];\n  var match = '';\n  while (match = regex.exec(str)) {\n    matches.push(match[1]);\n  }\n  return matches;\n};\nvar replaceAllOccurrences = function replaceAllOccurrences(str, item, newValue) {\n  var escapedSearch = item.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n  return str.replace(new RegExp(escapedSearch, 'g'), newValue);\n};\nvar getEmotion = function getEmotion(name) {\n  var elEmotion = '';\n  for (var index = 0; index < emotion.length; index++) {\n    var item = emotion[index];\n    if (item.text === `[${name}]`) {\n      elEmotion = `<div class=\"GlobalChatEmotionItem\"><div class=\"${item.name}\"></div></div>`;\n    }\n  }\n  return elEmotion || name;\n};\nexport var handleHistoryMessages = /*#__PURE__*/function () {\n  var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3(data, userObj) {\n    var withdrawId, newMessages, index, item, chatObjectInfo, chatObjectInfoType, className, _item$content, _userObj$item$senderU, _item$content2, _item$content3, contents, htmlContent, _index, _item, elEmotion, contentArr, file, vote;\n    return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n      while (1) switch (_context3.prev = _context3.next) {\n        case 0:\n          withdrawId = [];\n          newMessages = [];\n          index = 0;\n        case 3:\n          if (!(index < data.length)) {\n            _context3.next = 51;\n            break;\n          }\n          item = data[index];\n          chatObjectInfo = userObj[item.senderUserId];\n          chatObjectInfoType = item.senderUserId === appOnlyHeader.value + user.value.accountId;\n          className = chatObjectInfoType ? 'GlobalChatSelfMessages' : 'GlobalChatMessages';\n          if (!(item.messageType === 'RC:RcCmd')) {\n            _context3.next = 13;\n            break;\n          }\n          withdrawId.push((_item$content = item.content) === null || _item$content === void 0 ? void 0 : _item$content.messageUId);\n          newMessages.push({\n            id: item.messageId,\n            uid: item.messageUId,\n            type: item.messageType,\n            direction: item.messageDirection,\n            chatObjectInfo,\n            chatObjectInfoType,\n            className: 'GlobalChatMessagesInform',\n            content: item.content,\n            sentTime: item.sentTime,\n            userName: (_userObj$item$senderU = userObj[item.senderUserId]) === null || _userObj$item$senderU === void 0 ? void 0 : _userObj$item$senderU.name\n          });\n          _context3.next = 48;\n          break;\n        case 13:\n          if (!(item.messageType === 'RC:CmdNtf')) {\n            _context3.next = 17;\n            break;\n          }\n          newMessages.push({\n            id: item.messageId,\n            uid: item.messageUId,\n            type: item.messageType,\n            direction: item.messageDirection,\n            chatObjectInfo,\n            chatObjectInfoType,\n            className: 'GlobalChatMessagesInform',\n            content: item.content,\n            sentTime: item.sentTime\n          });\n          _context3.next = 48;\n          break;\n        case 17:\n          if (!(item.messageType === 'RC:TxtMsg')) {\n            _context3.next = 24;\n            break;\n          }\n          contents = _toConsumableArray(new Set(getContentBetweenBrackets((_item$content2 = item.content) === null || _item$content2 === void 0 ? void 0 : _item$content2.content)));\n          htmlContent = (_item$content3 = item.content) === null || _item$content3 === void 0 ? void 0 : _item$content3.content.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\"/g, '&quot;').replace(/'/g, '&#039;');\n          for (_index = 0; _index < contents.length; _index++) {\n            _item = contents[_index];\n            elEmotion = getEmotion(_item);\n            htmlContent = replaceAllOccurrences(htmlContent, `[${_item}]`, elEmotion);\n          }\n          newMessages.push({\n            id: item.messageId,\n            uid: item.messageUId,\n            type: item.messageType,\n            direction: item.messageDirection,\n            chatObjectInfo,\n            chatObjectInfoType,\n            className,\n            content: _objectSpread(_objectSpread({}, item.content), {}, {\n              htmlContent\n            }),\n            sentTime: item.sentTime\n          });\n          _context3.next = 48;\n          break;\n        case 24:\n          if (!(item.messageType === 'RC:LBSMsg')) {\n            _context3.next = 28;\n            break;\n          }\n          newMessages.push({\n            id: item.messageId,\n            uid: item.messageUId,\n            type: item.messageType,\n            direction: item.messageDirection,\n            chatObjectInfo,\n            chatObjectInfoType,\n            className,\n            customType: 'unknown',\n            sentTime: item.sentTime\n          });\n          _context3.next = 48;\n          break;\n        case 28:\n          if (!(item.messageType === 'RC:ImgTextMsg')) {\n            _context3.next = 47;\n            break;\n          }\n          contentArr = item.content.content.split(',');\n          if (!(contentArr[0] === '[文件]')) {\n            _context3.next = 37;\n            break;\n          }\n          _context3.next = 33;\n          return globalFileInfo(contentArr[1]);\n        case 33:\n          file = _context3.sent;\n          newMessages.push({\n            id: item.messageId,\n            uid: item.messageUId,\n            type: item.messageType,\n            direction: item.messageDirection,\n            chatObjectInfo,\n            chatObjectInfoType,\n            className,\n            customType: 'file',\n            file,\n            sentTime: item.sentTime\n          });\n          _context3.next = 45;\n          break;\n        case 37:\n          if (!(contentArr[0] === '[投票]')) {\n            _context3.next = 44;\n            break;\n          }\n          _context3.next = 40;\n          return globalVoteInfo(contentArr[1]);\n        case 40:\n          vote = _context3.sent;\n          newMessages.push({\n            id: item.messageId,\n            uid: item.messageUId,\n            type: item.messageType,\n            direction: item.messageDirection,\n            chatObjectInfo,\n            chatObjectInfoType,\n            className,\n            customType: 'vote',\n            vote,\n            sentTime: item.sentTime\n          });\n          _context3.next = 45;\n          break;\n        case 44:\n          newMessages.push({\n            id: item.messageId,\n            uid: item.messageUId,\n            type: item.messageType,\n            direction: item.messageDirection,\n            chatObjectInfo,\n            chatObjectInfoType,\n            className,\n            customType: 'unknown',\n            sentTime: item.sentTime\n          });\n        case 45:\n          _context3.next = 48;\n          break;\n        case 47:\n          newMessages.push({\n            id: item.messageId,\n            uid: item.messageUId,\n            type: item.messageType,\n            direction: item.messageDirection,\n            chatObjectInfo,\n            chatObjectInfoType,\n            className,\n            content: item.content,\n            sentTime: item.sentTime\n          });\n        case 48:\n          index++;\n          _context3.next = 3;\n          break;\n        case 51:\n          return _context3.abrupt(\"return\", {\n            newMessages,\n            withdrawId\n          });\n        case 52:\n        case \"end\":\n          return _context3.stop();\n      }\n    }, _callee3);\n  }));\n  return function handleHistoryMessages(_x3, _x4) {\n    return _ref3.apply(this, arguments);\n  };\n}();\n/**\r\n * 获取文件扩展名\r\n * @param {string} fileName - 文件名\r\n * @returns {string} - 文件扩展名\r\n */\nvar getFileExtension = function getFileExtension(fileName) {\n  var lastDotIndex = fileName.lastIndexOf('.');\n  if (lastDotIndex === -1) return '';\n  return fileName.slice(lastDotIndex);\n};\n\n/**\r\n * 获取文件名（不含扩展名）\r\n * @param {string} fileName - 文件名\r\n * @returns {string} - 不含扩展名的文件名\r\n */\nvar getFileNameWithoutExtension = function getFileNameWithoutExtension(fileName) {\n  var lastDotIndex = fileName.lastIndexOf('.');\n  if (lastDotIndex === -1) return fileName;\n  return fileName.slice(0, lastDotIndex);\n};\n/**\r\n * 获取唯一的文件名\r\n * @param {string} fileName - 原始文件名\r\n * @param {string[]} existingNames - 已存在的文件名数组\r\n * @returns {string} - 处理后的唯一文件名\r\n */\nexport var getUniqueFileName = function getUniqueFileName(fileName, existingNames) {\n  // 如果没有重名，直接返回原文件名\n  if (!(existingNames !== null && existingNames !== void 0 && existingNames.includes(fileName))) return fileName;\n  // 获取文件名和扩展名\n  var ext = getFileExtension(fileName);\n  var baseName = getFileNameWithoutExtension(fileName);\n  var newName = fileName;\n  var counter = 1;\n  // 循环直到找到不重复的文件名\n  while (existingNames !== null && existingNames !== void 0 && existingNames.includes(newName)) {\n    newName = `${baseName}(${counter})${ext}`;\n    counter++;\n  }\n  return newName;\n};", "map": {"version": 3, "names": ["_regeneratorRuntime", "e", "t", "r", "Object", "prototype", "n", "hasOwnProperty", "o", "defineProperty", "value", "i", "Symbol", "a", "iterator", "c", "asyncIterator", "u", "toStringTag", "define", "enumerable", "configurable", "writable", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "type", "arg", "call", "h", "l", "f", "s", "y", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "for<PERSON>ach", "_invoke", "AsyncIterator", "invoke", "resolve", "__await", "then", "callInvokeWithMethodAndArg", "Error", "done", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "return", "TypeError", "resultName", "next", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "push", "resetTryEntry", "completion", "reset", "isNaN", "length", "displayName", "isGeneratorFunction", "constructor", "name", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "Promise", "keys", "reverse", "pop", "prev", "char<PERSON>t", "slice", "stop", "rval", "handle", "complete", "finish", "catch", "_catch", "<PERSON><PERSON><PERSON>", "asyncGeneratorStep", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "api", "emotion", "format", "user", "appOnly<PERSON>eader", "fileIcon", "fileType", "IconClass", "docx", "doc", "wps", "xlsx", "xls", "pdf", "pptx", "ppt", "txt", "jpg", "png", "gif", "avi", "mp4", "zip", "rar", "handleTimeFormat", "time", "Date", "givenDate", "currentData", "difference", "daysDifference", "globalFileInfo", "_ref", "_callee", "fileId", "file", "_yield$api$globalFile", "data", "_callee$", "_context", "JSON", "parse", "localStorage", "getItem", "setItem", "stringify", "_x", "globalVoteInfo", "_ref2", "_callee2", "voteId", "vote", "_yield$api$VoteInfo", "_callee2$", "_context2", "VoteInfo", "detailId", "_x2", "getContentBetweenBrackets", "str", "regex", "matches", "match", "exec", "replaceAllOccurrences", "item", "newValue", "escapedS<PERSON>ch", "replace", "RegExp", "getEmotion", "elEmotion", "index", "text", "handleHistoryMessages", "_ref3", "_callee3", "userObj", "withdrawId", "newMessages", "chatObjectInfo", "chatObjectInfoType", "className", "_item$content", "_userObj$item$senderU", "_item$content2", "_item$content3", "contents", "htmlContent", "_index", "_item", "contentArr", "_callee3$", "_context3", "senderUserId", "accountId", "messageType", "content", "messageUId", "id", "messageId", "uid", "direction", "messageDirection", "sentTime", "userName", "_toConsumableArray", "Set", "_objectSpread", "customType", "split", "_x3", "_x4", "getFileExtension", "fileName", "lastDotIndex", "lastIndexOf", "getFileNameWithoutExtension", "getUniqueFileName", "existingNames", "includes", "ext", "baseName", "newName", "counter"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/customize/GlobalChat/js/ChatViewMethod.js"], "sourcesContent": ["\r\nimport api from '@/api'\r\nimport { emotion } from './emotion.js'\r\nimport { format } from 'common/js/time.js'\r\nimport { user, appOnlyHeader } from 'common/js/system_var.js'\r\nexport const fileIcon = (fileType) => {\r\n  const IconClass = {\r\n    docx: 'globalFileWord',\r\n    doc: 'globalFileWord',\r\n    wps: 'globalFileWPS',\r\n    xlsx: 'globalFileExcel',\r\n    xls: 'globalFileExcel',\r\n    pdf: 'globalFilePDF',\r\n    pptx: 'globalFilePPT',\r\n    ppt: 'globalFilePPT',\r\n    txt: 'globalFileTXT',\r\n    jpg: 'globalFilePicture',\r\n    png: 'globalFilePicture',\r\n    gif: 'globalFilePicture',\r\n    avi: 'globalFileVideo',\r\n    mp4: 'globalFileVideo',\r\n    zip: 'globalFileCompress',\r\n    rar: 'globalFileCompress'\r\n  }\r\n  return IconClass[fileType] || 'globalFileUnknown'\r\n}\r\nexport const handleTimeFormat = (time) => {\r\n  if (format(time, 'YYYY') !== format(new Date(), 'YYYY')) {\r\n    return format(time, 'YYYY-MM-DD')\r\n  } else if (format(time, 'YYYY-MM-DD') === format(new Date(), 'YYYY-MM-DD')) {\r\n    return format(time, 'HH:mm')\r\n  } else {\r\n    const givenDate = new Date(time)\r\n    const currentData = new Date()\r\n    const difference = currentData - givenDate\r\n    // 将差值转换成天数\r\n    const daysDifference = difference / (1000 * 60 * 60 * 24)\r\n    // 返回是否相差七天\r\n    return daysDifference <= 7 && daysDifference >= -7 ? format(time, 'EEE') : format(time, 'MM-DD')\r\n  }\r\n}\r\nconst globalFileInfo = async (fileId) => {\r\n  const file = JSON.parse(localStorage.getItem(fileId)) || ''\r\n  if (file) return file\r\n  const { data } = await api.globalFileInfo(fileId)\r\n  localStorage.setItem(fileId, JSON.stringify(data))\r\n  return data\r\n}\r\nconst globalVoteInfo = async (voteId) => {\r\n  const vote = JSON.parse(localStorage.getItem(voteId)) || ''\r\n  if (vote) return vote\r\n  const { data } = await api.VoteInfo({ detailId: voteId })\r\n  localStorage.setItem(voteId, JSON.stringify(data))\r\n  return data\r\n}\r\nconst getContentBetweenBrackets = (str) => {\r\n  const regex = /\\[([^\\]]+)\\]/g\r\n  let matches = []\r\n  let match = ''\r\n  while ((match = regex.exec(str))) {\r\n    matches.push(match[1])\r\n  }\r\n  return matches\r\n}\r\nconst replaceAllOccurrences = (str, item, newValue) => {\r\n  const escapedSearch = item.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&')\r\n  return str.replace(new RegExp(escapedSearch, 'g'), newValue)\r\n}\r\nconst getEmotion = (name) => {\r\n  let elEmotion = ''\r\n  for (let index = 0; index < emotion.length; index++) {\r\n    const item = emotion[index]\r\n    if (item.text === `[${name}]`) {\r\n      elEmotion = `<div class=\"GlobalChatEmotionItem\"><div class=\"${item.name}\"></div></div>`\r\n    }\r\n  }\r\n  return elEmotion || name\r\n}\r\nexport const handleHistoryMessages = async (data, userObj) => {\r\n  const withdrawId = []\r\n  const newMessages = []\r\n  for (let index = 0; index < data.length; index++) {\r\n    const item = data[index]\r\n    const chatObjectInfo = userObj[item.senderUserId]\r\n    const chatObjectInfoType = item.senderUserId === appOnlyHeader.value + user.value.accountId\r\n    const className = chatObjectInfoType ? 'GlobalChatSelfMessages' : 'GlobalChatMessages'\r\n    if (item.messageType === 'RC:RcCmd') {\r\n      withdrawId.push(item.content?.messageUId)\r\n      newMessages.push({ id: item.messageId, uid: item.messageUId, type: item.messageType, direction: item.messageDirection, chatObjectInfo, chatObjectInfoType, className: 'GlobalChatMessagesInform', content: item.content, sentTime: item.sentTime, userName: userObj[item.senderUserId]?.name })\r\n    } else if (item.messageType === 'RC:CmdNtf') {\r\n      newMessages.push({ id: item.messageId, uid: item.messageUId, type: item.messageType, direction: item.messageDirection, chatObjectInfo, chatObjectInfoType, className: 'GlobalChatMessagesInform', content: item.content, sentTime: item.sentTime })\r\n    } else if (item.messageType === 'RC:TxtMsg') {\r\n      const contents = [...new Set(getContentBetweenBrackets(item.content?.content))]\r\n      let htmlContent = item.content?.content.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\"/g, '&quot;').replace(/'/g, '&#039;');\r\n      for (let index = 0; index < contents.length; index++) {\r\n        const item = contents[index]\r\n        const elEmotion = getEmotion(item)\r\n        htmlContent = replaceAllOccurrences(htmlContent, `[${item}]`, elEmotion)\r\n      }\r\n      newMessages.push({ id: item.messageId, uid: item.messageUId, type: item.messageType, direction: item.messageDirection, chatObjectInfo, chatObjectInfoType, className, content: { ...item.content, htmlContent }, sentTime: item.sentTime })\r\n    } else if (item.messageType === 'RC:LBSMsg') {\r\n      newMessages.push({ id: item.messageId, uid: item.messageUId, type: item.messageType, direction: item.messageDirection, chatObjectInfo, chatObjectInfoType, className, customType: 'unknown', sentTime: item.sentTime })\r\n    } else if (item.messageType === 'RC:ImgTextMsg') {\r\n      const contentArr = item.content.content.split(',')\r\n      if (contentArr[0] === '[文件]') {\r\n        const file = await globalFileInfo(contentArr[1])\r\n        newMessages.push({ id: item.messageId, uid: item.messageUId, type: item.messageType, direction: item.messageDirection, chatObjectInfo, chatObjectInfoType, className, customType: 'file', file, sentTime: item.sentTime })\r\n      } else if (contentArr[0] === '[投票]') {\r\n        const vote = await globalVoteInfo(contentArr[1])\r\n        newMessages.push({ id: item.messageId, uid: item.messageUId, type: item.messageType, direction: item.messageDirection, chatObjectInfo, chatObjectInfoType, className, customType: 'vote', vote, sentTime: item.sentTime })\r\n      } else {\r\n        newMessages.push({ id: item.messageId, uid: item.messageUId, type: item.messageType, direction: item.messageDirection, chatObjectInfo, chatObjectInfoType, className, customType: 'unknown', sentTime: item.sentTime })\r\n      }\r\n    } else {\r\n      newMessages.push({ id: item.messageId, uid: item.messageUId, type: item.messageType, direction: item.messageDirection, chatObjectInfo, chatObjectInfoType, className, content: item.content, sentTime: item.sentTime })\r\n    }\r\n  }\r\n  return { newMessages, withdrawId }\r\n}\r\n/**\r\n * 获取文件扩展名\r\n * @param {string} fileName - 文件名\r\n * @returns {string} - 文件扩展名\r\n */\r\nconst getFileExtension = (fileName) => {\r\n  const lastDotIndex = fileName.lastIndexOf('.')\r\n  if (lastDotIndex === -1) return ''\r\n  return fileName.slice(lastDotIndex)\r\n}\r\n\r\n/**\r\n * 获取文件名（不含扩展名）\r\n * @param {string} fileName - 文件名\r\n * @returns {string} - 不含扩展名的文件名\r\n */\r\nconst getFileNameWithoutExtension = (fileName) => {\r\n  const lastDotIndex = fileName.lastIndexOf('.')\r\n  if (lastDotIndex === -1) return fileName\r\n  return fileName.slice(0, lastDotIndex)\r\n}\r\n/**\r\n * 获取唯一的文件名\r\n * @param {string} fileName - 原始文件名\r\n * @param {string[]} existingNames - 已存在的文件名数组\r\n * @returns {string} - 处理后的唯一文件名\r\n */\r\nexport const getUniqueFileName = (fileName, existingNames) => {\r\n  // 如果没有重名，直接返回原文件名\r\n  if (!existingNames?.includes(fileName)) return fileName\r\n  // 获取文件名和扩展名\r\n  const ext = getFileExtension(fileName)\r\n  const baseName = getFileNameWithoutExtension(fileName)\r\n  let newName = fileName\r\n  let counter = 1\r\n  // 循环直到找到不重复的文件名\r\n  while (existingNames?.includes(newName)) {\r\n    newName = `${baseName}(${counter})${ext}`\r\n    counter++\r\n  }\r\n  return newName\r\n}\r\n"], "mappings": ";;;;;;;;;;;+CACA,qJAAAA,mBAAA,YAAAA,oBAAA,WAAAC,CAAA,SAAAC,CAAA,EAAAD,CAAA,OAAAE,CAAA,GAAAC,MAAA,CAAAC,SAAA,EAAAC,CAAA,GAAAH,CAAA,CAAAI,cAAA,EAAAC,CAAA,GAAAJ,MAAA,CAAAK,cAAA,cAAAP,CAAA,EAAAD,CAAA,EAAAE,CAAA,IAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,CAAAO,KAAA,KAAAC,CAAA,wBAAAC,MAAA,GAAAA,MAAA,OAAAC,CAAA,GAAAF,CAAA,CAAAG,QAAA,kBAAAC,CAAA,GAAAJ,CAAA,CAAAK,aAAA,uBAAAC,CAAA,GAAAN,CAAA,CAAAO,WAAA,8BAAAC,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAC,MAAA,CAAAK,cAAA,CAAAP,CAAA,EAAAD,CAAA,IAAAS,KAAA,EAAAP,CAAA,EAAAiB,UAAA,MAAAC,YAAA,MAAAC,QAAA,SAAApB,CAAA,CAAAD,CAAA,WAAAkB,MAAA,mBAAAjB,CAAA,IAAAiB,MAAA,YAAAA,OAAAjB,CAAA,EAAAD,CAAA,EAAAE,CAAA,WAAAD,CAAA,CAAAD,CAAA,IAAAE,CAAA,gBAAAoB,KAAArB,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAK,CAAA,GAAAV,CAAA,IAAAA,CAAA,CAAAI,SAAA,YAAAmB,SAAA,GAAAvB,CAAA,GAAAuB,SAAA,EAAAX,CAAA,GAAAT,MAAA,CAAAqB,MAAA,CAAAd,CAAA,CAAAN,SAAA,GAAAU,CAAA,OAAAW,OAAA,CAAApB,CAAA,gBAAAE,CAAA,CAAAK,CAAA,eAAAH,KAAA,EAAAiB,gBAAA,CAAAzB,CAAA,EAAAC,CAAA,EAAAY,CAAA,MAAAF,CAAA,aAAAe,SAAA1B,CAAA,EAAAD,CAAA,EAAAE,CAAA,mBAAA0B,IAAA,YAAAC,GAAA,EAAA5B,CAAA,CAAA6B,IAAA,CAAA9B,CAAA,EAAAE,CAAA,cAAAD,CAAA,aAAA2B,IAAA,WAAAC,GAAA,EAAA5B,CAAA,QAAAD,CAAA,CAAAsB,IAAA,GAAAA,IAAA,MAAAS,CAAA,qBAAAC,CAAA,qBAAAC,CAAA,gBAAAC,CAAA,gBAAAC,CAAA,gBAAAZ,UAAA,cAAAa,kBAAA,cAAAC,2BAAA,SAAAC,CAAA,OAAApB,MAAA,CAAAoB,CAAA,EAAA1B,CAAA,qCAAA2B,CAAA,GAAApC,MAAA,CAAAqC,cAAA,EAAAC,CAAA,GAAAF,CAAA,IAAAA,CAAA,CAAAA,CAAA,CAAAG,MAAA,QAAAD,CAAA,IAAAA,CAAA,KAAAvC,CAAA,IAAAG,CAAA,CAAAyB,IAAA,CAAAW,CAAA,EAAA7B,CAAA,MAAA0B,CAAA,GAAAG,CAAA,OAAAE,CAAA,GAAAN,0BAAA,CAAAjC,SAAA,GAAAmB,SAAA,CAAAnB,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAc,CAAA,YAAAM,sBAAA3C,CAAA,gCAAA4C,OAAA,WAAA7C,CAAA,IAAAkB,MAAA,CAAAjB,CAAA,EAAAD,CAAA,YAAAC,CAAA,gBAAA6C,OAAA,CAAA9C,CAAA,EAAAC,CAAA,sBAAA8C,cAAA9C,CAAA,EAAAD,CAAA,aAAAgD,OAAA9C,CAAA,EAAAK,CAAA,EAAAG,CAAA,EAAAE,CAAA,QAAAE,CAAA,GAAAa,QAAA,CAAA1B,CAAA,CAAAC,CAAA,GAAAD,CAAA,EAAAM,CAAA,mBAAAO,CAAA,CAAAc,IAAA,QAAAZ,CAAA,GAAAF,CAAA,CAAAe,GAAA,EAAAE,CAAA,GAAAf,CAAA,CAAAP,KAAA,SAAAsB,CAAA,uBAAAA,CAAA,IAAA1B,CAAA,CAAAyB,IAAA,CAAAC,CAAA,eAAA/B,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,CAAAmB,OAAA,EAAAC,IAAA,WAAAlD,CAAA,IAAA+C,MAAA,SAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,gBAAAX,CAAA,IAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,QAAAZ,CAAA,CAAAiD,OAAA,CAAAlB,CAAA,EAAAoB,IAAA,WAAAlD,CAAA,IAAAe,CAAA,CAAAP,KAAA,GAAAR,CAAA,EAAAS,CAAA,CAAAM,CAAA,gBAAAf,CAAA,WAAA+C,MAAA,UAAA/C,CAAA,EAAAS,CAAA,EAAAE,CAAA,SAAAA,CAAA,CAAAE,CAAA,CAAAe,GAAA,SAAA3B,CAAA,EAAAK,CAAA,oBAAAE,KAAA,WAAAA,MAAAR,CAAA,EAAAI,CAAA,aAAA+C,2BAAA,eAAApD,CAAA,WAAAA,CAAA,EAAAE,CAAA,IAAA8C,MAAA,CAAA/C,CAAA,EAAAI,CAAA,EAAAL,CAAA,EAAAE,CAAA,gBAAAA,CAAA,GAAAA,CAAA,GAAAA,CAAA,CAAAiD,IAAA,CAAAC,0BAAA,EAAAA,0BAAA,IAAAA,0BAAA,qBAAA1B,iBAAA1B,CAAA,EAAAE,CAAA,EAAAG,CAAA,QAAAE,CAAA,GAAAwB,CAAA,mBAAArB,CAAA,EAAAE,CAAA,QAAAL,CAAA,KAAA0B,CAAA,QAAAoB,KAAA,sCAAA9C,CAAA,KAAA2B,CAAA,oBAAAxB,CAAA,QAAAE,CAAA,WAAAH,KAAA,EAAAR,CAAA,EAAAqD,IAAA,eAAAjD,CAAA,CAAAkD,MAAA,GAAA7C,CAAA,EAAAL,CAAA,CAAAwB,GAAA,GAAAjB,CAAA,UAAAE,CAAA,GAAAT,CAAA,CAAAmD,QAAA,MAAA1C,CAAA,QAAAE,CAAA,GAAAyC,mBAAA,CAAA3C,CAAA,EAAAT,CAAA,OAAAW,CAAA,QAAAA,CAAA,KAAAmB,CAAA,mBAAAnB,CAAA,qBAAAX,CAAA,CAAAkD,MAAA,EAAAlD,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAsD,KAAA,GAAAtD,CAAA,CAAAwB,GAAA,sBAAAxB,CAAA,CAAAkD,MAAA,QAAAhD,CAAA,KAAAwB,CAAA,QAAAxB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAwB,GAAA,EAAAxB,CAAA,CAAAuD,iBAAA,CAAAvD,CAAA,CAAAwB,GAAA,uBAAAxB,CAAA,CAAAkD,MAAA,IAAAlD,CAAA,CAAAwD,MAAA,WAAAxD,CAAA,CAAAwB,GAAA,GAAAtB,CAAA,GAAA0B,CAAA,MAAAK,CAAA,GAAAX,QAAA,CAAA3B,CAAA,EAAAE,CAAA,EAAAG,CAAA,oBAAAiC,CAAA,CAAAV,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAiD,IAAA,GAAApB,CAAA,GAAAF,CAAA,EAAAM,CAAA,CAAAT,GAAA,KAAAM,CAAA,qBAAA1B,KAAA,EAAA6B,CAAA,CAAAT,GAAA,EAAAyB,IAAA,EAAAjD,CAAA,CAAAiD,IAAA,kBAAAhB,CAAA,CAAAV,IAAA,KAAArB,CAAA,GAAA2B,CAAA,EAAA7B,CAAA,CAAAkD,MAAA,YAAAlD,CAAA,CAAAwB,GAAA,GAAAS,CAAA,CAAAT,GAAA,mBAAA4B,oBAAAzD,CAAA,EAAAE,CAAA,QAAAG,CAAA,GAAAH,CAAA,CAAAqD,MAAA,EAAAhD,CAAA,GAAAP,CAAA,CAAAa,QAAA,CAAAR,CAAA,OAAAE,CAAA,KAAAN,CAAA,SAAAC,CAAA,CAAAsD,QAAA,qBAAAnD,CAAA,IAAAL,CAAA,CAAAa,QAAA,CAAAiD,MAAA,KAAA5D,CAAA,CAAAqD,MAAA,aAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,EAAAwD,mBAAA,CAAAzD,CAAA,EAAAE,CAAA,eAAAA,CAAA,CAAAqD,MAAA,kBAAAlD,CAAA,KAAAH,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,uCAAA1D,CAAA,iBAAA8B,CAAA,MAAAzB,CAAA,GAAAiB,QAAA,CAAApB,CAAA,EAAAP,CAAA,CAAAa,QAAA,EAAAX,CAAA,CAAA2B,GAAA,mBAAAnB,CAAA,CAAAkB,IAAA,SAAA1B,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,GAAAnB,CAAA,CAAAmB,GAAA,EAAA3B,CAAA,CAAAsD,QAAA,SAAArB,CAAA,MAAAvB,CAAA,GAAAF,CAAA,CAAAmB,GAAA,SAAAjB,CAAA,GAAAA,CAAA,CAAA0C,IAAA,IAAApD,CAAA,CAAAF,CAAA,CAAAgE,UAAA,IAAApD,CAAA,CAAAH,KAAA,EAAAP,CAAA,CAAA+D,IAAA,GAAAjE,CAAA,CAAAkE,OAAA,eAAAhE,CAAA,CAAAqD,MAAA,KAAArD,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,GAAAC,CAAA,CAAAsD,QAAA,SAAArB,CAAA,IAAAvB,CAAA,IAAAV,CAAA,CAAAqD,MAAA,YAAArD,CAAA,CAAA2B,GAAA,OAAAkC,SAAA,sCAAA7D,CAAA,CAAAsD,QAAA,SAAArB,CAAA,cAAAgC,aAAAlE,CAAA,QAAAD,CAAA,KAAAoE,MAAA,EAAAnE,CAAA,YAAAA,CAAA,KAAAD,CAAA,CAAAqE,QAAA,GAAApE,CAAA,WAAAA,CAAA,KAAAD,CAAA,CAAAsE,UAAA,GAAArE,CAAA,KAAAD,CAAA,CAAAuE,QAAA,GAAAtE,CAAA,WAAAuE,UAAA,CAAAC,IAAA,CAAAzE,CAAA,cAAA0E,cAAAzE,CAAA,QAAAD,CAAA,GAAAC,CAAA,CAAA0E,UAAA,QAAA3E,CAAA,CAAA4B,IAAA,oBAAA5B,CAAA,CAAA6B,GAAA,EAAA5B,CAAA,CAAA0E,UAAA,GAAA3E,CAAA,aAAAyB,QAAAxB,CAAA,SAAAuE,UAAA,MAAAJ,MAAA,aAAAnE,CAAA,CAAA4C,OAAA,CAAAsB,YAAA,cAAAS,KAAA,iBAAAlC,OAAA1C,CAAA,QAAAA,CAAA,WAAAA,CAAA,QAAAE,CAAA,GAAAF,CAAA,CAAAY,CAAA,OAAAV,CAAA,SAAAA,CAAA,CAAA4B,IAAA,CAAA9B,CAAA,4BAAAA,CAAA,CAAAiE,IAAA,SAAAjE,CAAA,OAAA6E,KAAA,CAAA7E,CAAA,CAAA8E,MAAA,SAAAvE,CAAA,OAAAG,CAAA,YAAAuD,KAAA,aAAA1D,CAAA,GAAAP,CAAA,CAAA8E,MAAA,OAAAzE,CAAA,CAAAyB,IAAA,CAAA9B,CAAA,EAAAO,CAAA,UAAA0D,IAAA,CAAAxD,KAAA,GAAAT,CAAA,CAAAO,CAAA,GAAA0D,IAAA,CAAAX,IAAA,OAAAW,IAAA,SAAAA,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,YAAAvD,CAAA,CAAAuD,IAAA,GAAAvD,CAAA,gBAAAqD,SAAA,QAAA/D,CAAA,iCAAAoC,iBAAA,CAAAhC,SAAA,GAAAiC,0BAAA,EAAA9B,CAAA,CAAAoC,CAAA,mBAAAlC,KAAA,EAAA4B,0BAAA,EAAAjB,YAAA,SAAAb,CAAA,CAAA8B,0BAAA,mBAAA5B,KAAA,EAAA2B,iBAAA,EAAAhB,YAAA,SAAAgB,iBAAA,CAAA2C,WAAA,GAAA7D,MAAA,CAAAmB,0BAAA,EAAArB,CAAA,wBAAAhB,CAAA,CAAAgF,mBAAA,aAAA/E,CAAA,QAAAD,CAAA,wBAAAC,CAAA,IAAAA,CAAA,CAAAgF,WAAA,WAAAjF,CAAA,KAAAA,CAAA,KAAAoC,iBAAA,6BAAApC,CAAA,CAAA+E,WAAA,IAAA/E,CAAA,CAAAkF,IAAA,OAAAlF,CAAA,CAAAmF,IAAA,aAAAlF,CAAA,WAAAE,MAAA,CAAAiF,cAAA,GAAAjF,MAAA,CAAAiF,cAAA,CAAAnF,CAAA,EAAAoC,0BAAA,KAAApC,CAAA,CAAAoF,SAAA,GAAAhD,0BAAA,EAAAnB,MAAA,CAAAjB,CAAA,EAAAe,CAAA,yBAAAf,CAAA,CAAAG,SAAA,GAAAD,MAAA,CAAAqB,MAAA,CAAAmB,CAAA,GAAA1C,CAAA,KAAAD,CAAA,CAAAsF,KAAA,aAAArF,CAAA,aAAAiD,OAAA,EAAAjD,CAAA,OAAA2C,qBAAA,CAAAG,aAAA,CAAA3C,SAAA,GAAAc,MAAA,CAAA6B,aAAA,CAAA3C,SAAA,EAAAU,CAAA,iCAAAd,CAAA,CAAA+C,aAAA,GAAAA,aAAA,EAAA/C,CAAA,CAAAuF,KAAA,aAAAtF,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,EAAAG,CAAA,eAAAA,CAAA,KAAAA,CAAA,GAAA8E,OAAA,OAAA5E,CAAA,OAAAmC,aAAA,CAAAzB,IAAA,CAAArB,CAAA,EAAAC,CAAA,EAAAG,CAAA,EAAAE,CAAA,GAAAG,CAAA,UAAAV,CAAA,CAAAgF,mBAAA,CAAA9E,CAAA,IAAAU,CAAA,GAAAA,CAAA,CAAAqD,IAAA,GAAAd,IAAA,WAAAlD,CAAA,WAAAA,CAAA,CAAAqD,IAAA,GAAArD,CAAA,CAAAQ,KAAA,GAAAG,CAAA,CAAAqD,IAAA,WAAArB,qBAAA,CAAAD,CAAA,GAAAzB,MAAA,CAAAyB,CAAA,EAAA3B,CAAA,gBAAAE,MAAA,CAAAyB,CAAA,EAAA/B,CAAA,iCAAAM,MAAA,CAAAyB,CAAA,6DAAA3C,CAAA,CAAAyF,IAAA,aAAAxF,CAAA,QAAAD,CAAA,GAAAG,MAAA,CAAAF,CAAA,GAAAC,CAAA,gBAAAG,CAAA,IAAAL,CAAA,EAAAE,CAAA,CAAAuE,IAAA,CAAApE,CAAA,UAAAH,CAAA,CAAAwF,OAAA,aAAAzB,KAAA,WAAA/D,CAAA,CAAA4E,MAAA,SAAA7E,CAAA,GAAAC,CAAA,CAAAyF,GAAA,QAAA1F,CAAA,IAAAD,CAAA,SAAAiE,IAAA,CAAAxD,KAAA,GAAAR,CAAA,EAAAgE,IAAA,CAAAX,IAAA,OAAAW,IAAA,WAAAA,IAAA,CAAAX,IAAA,OAAAW,IAAA,QAAAjE,CAAA,CAAA0C,MAAA,GAAAA,MAAA,EAAAjB,OAAA,CAAArB,SAAA,KAAA6E,WAAA,EAAAxD,OAAA,EAAAmD,KAAA,WAAAA,MAAA5E,CAAA,aAAA4F,IAAA,WAAA3B,IAAA,WAAAP,IAAA,QAAAC,KAAA,GAAA1D,CAAA,OAAAqD,IAAA,YAAAE,QAAA,cAAAD,MAAA,gBAAA1B,GAAA,GAAA5B,CAAA,OAAAuE,UAAA,CAAA3B,OAAA,CAAA6B,aAAA,IAAA1E,CAAA,WAAAE,CAAA,kBAAAA,CAAA,CAAA2F,MAAA,OAAAxF,CAAA,CAAAyB,IAAA,OAAA5B,CAAA,MAAA2E,KAAA,EAAA3E,CAAA,CAAA4F,KAAA,cAAA5F,CAAA,IAAAD,CAAA,MAAA8F,IAAA,WAAAA,KAAA,SAAAzC,IAAA,WAAArD,CAAA,QAAAuE,UAAA,IAAAG,UAAA,kBAAA1E,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,cAAAmE,IAAA,KAAApC,iBAAA,WAAAA,kBAAA5D,CAAA,aAAAsD,IAAA,QAAAtD,CAAA,MAAAE,CAAA,kBAAA+F,OAAA5F,CAAA,EAAAE,CAAA,WAAAK,CAAA,CAAAgB,IAAA,YAAAhB,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAE,CAAA,CAAA+D,IAAA,GAAA5D,CAAA,EAAAE,CAAA,KAAAL,CAAA,CAAAqD,MAAA,WAAArD,CAAA,CAAA2B,GAAA,GAAA5B,CAAA,KAAAM,CAAA,aAAAA,CAAA,QAAAiE,UAAA,CAAAM,MAAA,MAAAvE,CAAA,SAAAA,CAAA,QAAAG,CAAA,QAAA8D,UAAA,CAAAjE,CAAA,GAAAK,CAAA,GAAAF,CAAA,CAAAiE,UAAA,iBAAAjE,CAAA,CAAA0D,MAAA,SAAA6B,MAAA,aAAAvF,CAAA,CAAA0D,MAAA,SAAAwB,IAAA,QAAA9E,CAAA,GAAAT,CAAA,CAAAyB,IAAA,CAAApB,CAAA,eAAAM,CAAA,GAAAX,CAAA,CAAAyB,IAAA,CAAApB,CAAA,qBAAAI,CAAA,IAAAE,CAAA,aAAA4E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,gBAAAuB,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,cAAAxD,CAAA,aAAA8E,IAAA,GAAAlF,CAAA,CAAA2D,QAAA,SAAA4B,MAAA,CAAAvF,CAAA,CAAA2D,QAAA,qBAAArD,CAAA,QAAAqC,KAAA,qDAAAuC,IAAA,GAAAlF,CAAA,CAAA4D,UAAA,SAAA2B,MAAA,CAAAvF,CAAA,CAAA4D,UAAA,YAAAT,MAAA,WAAAA,OAAA5D,CAAA,EAAAD,CAAA,aAAAE,CAAA,QAAAsE,UAAA,CAAAM,MAAA,MAAA5E,CAAA,SAAAA,CAAA,QAAAK,CAAA,QAAAiE,UAAA,CAAAtE,CAAA,OAAAK,CAAA,CAAA6D,MAAA,SAAAwB,IAAA,IAAAvF,CAAA,CAAAyB,IAAA,CAAAvB,CAAA,wBAAAqF,IAAA,GAAArF,CAAA,CAAA+D,UAAA,QAAA5D,CAAA,GAAAH,CAAA,aAAAG,CAAA,iBAAAT,CAAA,mBAAAA,CAAA,KAAAS,CAAA,CAAA0D,MAAA,IAAApE,CAAA,IAAAA,CAAA,IAAAU,CAAA,CAAA4D,UAAA,KAAA5D,CAAA,cAAAE,CAAA,GAAAF,CAAA,GAAAA,CAAA,CAAAiE,UAAA,cAAA/D,CAAA,CAAAgB,IAAA,GAAA3B,CAAA,EAAAW,CAAA,CAAAiB,GAAA,GAAA7B,CAAA,EAAAU,CAAA,SAAA6C,MAAA,gBAAAU,IAAA,GAAAvD,CAAA,CAAA4D,UAAA,EAAAnC,CAAA,SAAA+D,QAAA,CAAAtF,CAAA,MAAAsF,QAAA,WAAAA,SAAAjG,CAAA,EAAAD,CAAA,oBAAAC,CAAA,CAAA2B,IAAA,QAAA3B,CAAA,CAAA4B,GAAA,qBAAA5B,CAAA,CAAA2B,IAAA,mBAAA3B,CAAA,CAAA2B,IAAA,QAAAqC,IAAA,GAAAhE,CAAA,CAAA4B,GAAA,gBAAA5B,CAAA,CAAA2B,IAAA,SAAAoE,IAAA,QAAAnE,GAAA,GAAA5B,CAAA,CAAA4B,GAAA,OAAA0B,MAAA,kBAAAU,IAAA,yBAAAhE,CAAA,CAAA2B,IAAA,IAAA5B,CAAA,UAAAiE,IAAA,GAAAjE,CAAA,GAAAmC,CAAA,KAAAgE,MAAA,WAAAA,OAAAlG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAoE,UAAA,KAAArE,CAAA,cAAAiG,QAAA,CAAAhG,CAAA,CAAAyE,UAAA,EAAAzE,CAAA,CAAAqE,QAAA,GAAAG,aAAA,CAAAxE,CAAA,GAAAiC,CAAA,OAAAiE,KAAA,WAAAC,OAAApG,CAAA,aAAAD,CAAA,QAAAwE,UAAA,CAAAM,MAAA,MAAA9E,CAAA,SAAAA,CAAA,QAAAE,CAAA,QAAAsE,UAAA,CAAAxE,CAAA,OAAAE,CAAA,CAAAkE,MAAA,KAAAnE,CAAA,QAAAI,CAAA,GAAAH,CAAA,CAAAyE,UAAA,kBAAAtE,CAAA,CAAAuB,IAAA,QAAArB,CAAA,GAAAF,CAAA,CAAAwB,GAAA,EAAA6C,aAAA,CAAAxE,CAAA,YAAAK,CAAA,YAAA8C,KAAA,8BAAAiD,aAAA,WAAAA,cAAAtG,CAAA,EAAAE,CAAA,EAAAG,CAAA,gBAAAmD,QAAA,KAAA3C,QAAA,EAAA6B,MAAA,CAAA1C,CAAA,GAAAgE,UAAA,EAAA9D,CAAA,EAAAgE,OAAA,EAAA7D,CAAA,oBAAAkD,MAAA,UAAA1B,GAAA,GAAA5B,CAAA,GAAAkC,CAAA,OAAAnC,CAAA;AAAA,SAAAuG,mBAAAlG,CAAA,EAAAJ,CAAA,EAAAD,CAAA,EAAAE,CAAA,EAAAK,CAAA,EAAAK,CAAA,EAAAE,CAAA,cAAAJ,CAAA,GAAAL,CAAA,CAAAO,CAAA,EAAAE,CAAA,GAAAE,CAAA,GAAAN,CAAA,CAAAD,KAAA,WAAAJ,CAAA,gBAAAL,CAAA,CAAAK,CAAA,KAAAK,CAAA,CAAA4C,IAAA,GAAArD,CAAA,CAAAe,CAAA,IAAAwE,OAAA,CAAAvC,OAAA,CAAAjC,CAAA,EAAAmC,IAAA,CAAAjD,CAAA,EAAAK,CAAA;AAAA,SAAAiG,kBAAAnG,CAAA,6BAAAJ,CAAA,SAAAD,CAAA,GAAAyG,SAAA,aAAAjB,OAAA,WAAAtF,CAAA,EAAAK,CAAA,QAAAK,CAAA,GAAAP,CAAA,CAAAqG,KAAA,CAAAzG,CAAA,EAAAD,CAAA,YAAA2G,MAAAtG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,UAAAvG,CAAA,cAAAuG,OAAAvG,CAAA,IAAAkG,kBAAA,CAAA3F,CAAA,EAAAV,CAAA,EAAAK,CAAA,EAAAoG,KAAA,EAAAC,MAAA,WAAAvG,CAAA,KAAAsG,KAAA;AAAA,OAAOE,GAAG,MAAM,OAAO;AACvB,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,IAAI,EAAEC,aAAa,QAAQ,yBAAyB;AAC7D,OAAO,IAAMC,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,QAAQ,EAAK;EACpC,IAAMC,SAAS,GAAG;IAChBC,IAAI,EAAE,gBAAgB;IACtBC,GAAG,EAAE,gBAAgB;IACrBC,GAAG,EAAE,eAAe;IACpBC,IAAI,EAAE,iBAAiB;IACvBC,GAAG,EAAE,iBAAiB;IACtBC,GAAG,EAAE,eAAe;IACpBC,IAAI,EAAE,eAAe;IACrBC,GAAG,EAAE,eAAe;IACpBC,GAAG,EAAE,eAAe;IACpBC,GAAG,EAAE,mBAAmB;IACxBC,GAAG,EAAE,mBAAmB;IACxBC,GAAG,EAAE,mBAAmB;IACxBC,GAAG,EAAE,iBAAiB;IACtBC,GAAG,EAAE,iBAAiB;IACtBC,GAAG,EAAE,oBAAoB;IACzBC,GAAG,EAAE;EACP,CAAC;EACD,OAAOhB,SAAS,CAACD,QAAQ,CAAC,IAAI,mBAAmB;AACnD,CAAC;AACD,OAAO,IAAMkB,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,IAAI,EAAK;EACxC,IAAIvB,MAAM,CAACuB,IAAI,EAAE,MAAM,CAAC,KAAKvB,MAAM,CAAC,IAAIwB,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE;IACvD,OAAOxB,MAAM,CAACuB,IAAI,EAAE,YAAY,CAAC;EACnC,CAAC,MAAM,IAAIvB,MAAM,CAACuB,IAAI,EAAE,YAAY,CAAC,KAAKvB,MAAM,CAAC,IAAIwB,IAAI,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE;IAC1E,OAAOxB,MAAM,CAACuB,IAAI,EAAE,OAAO,CAAC;EAC9B,CAAC,MAAM;IACL,IAAME,SAAS,GAAG,IAAID,IAAI,CAACD,IAAI,CAAC;IAChC,IAAMG,WAAW,GAAG,IAAIF,IAAI,CAAC,CAAC;IAC9B,IAAMG,UAAU,GAAGD,WAAW,GAAGD,SAAS;IAC1C;IACA,IAAMG,cAAc,GAAGD,UAAU,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;IACzD;IACA,OAAOC,cAAc,IAAI,CAAC,IAAIA,cAAc,IAAI,CAAC,CAAC,GAAG5B,MAAM,CAACuB,IAAI,EAAE,KAAK,CAAC,GAAGvB,MAAM,CAACuB,IAAI,EAAE,OAAO,CAAC;EAClG;AACF,CAAC;AACD,IAAMM,cAAc;EAAA,IAAAC,IAAA,GAAArC,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2D,QAAOC,MAAM;IAAA,IAAAC,IAAA,EAAAC,qBAAA,EAAAC,IAAA;IAAA,OAAAnJ,mBAAA,GAAAuB,IAAA,UAAA6H,SAAAC,QAAA;MAAA,kBAAAA,QAAA,CAAAxD,IAAA,GAAAwD,QAAA,CAAAnF,IAAA;QAAA;UAC5B+E,IAAI,GAAGK,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAACT,MAAM,CAAC,CAAC,IAAI,EAAE;UAAA,KACvDC,IAAI;YAAAI,QAAA,CAAAnF,IAAA;YAAA;UAAA;UAAA,OAAAmF,QAAA,CAAAvF,MAAA,WAASmF,IAAI;QAAA;UAAAI,QAAA,CAAAnF,IAAA;UAAA,OACE4C,GAAG,CAAC+B,cAAc,CAACG,MAAM,CAAC;QAAA;UAAAE,qBAAA,GAAAG,QAAA,CAAA1F,IAAA;UAAzCwF,IAAI,GAAAD,qBAAA,CAAJC,IAAI;UACZK,YAAY,CAACE,OAAO,CAACV,MAAM,EAAEM,IAAI,CAACK,SAAS,CAACR,IAAI,CAAC,CAAC;UAAA,OAAAE,QAAA,CAAAvF,MAAA,WAC3CqF,IAAI;QAAA;QAAA;UAAA,OAAAE,QAAA,CAAArD,IAAA;MAAA;IAAA,GAAA+C,OAAA;EAAA,CACZ;EAAA,gBANKF,cAAcA,CAAAe,EAAA;IAAA,OAAAd,IAAA,CAAAnC,KAAA,OAAAD,SAAA;EAAA;AAAA,GAMnB;AACD,IAAMmD,cAAc;EAAA,IAAAC,KAAA,GAAArD,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAA2E,SAAOC,MAAM;IAAA,IAAAC,IAAA,EAAAC,mBAAA,EAAAf,IAAA;IAAA,OAAAnJ,mBAAA,GAAAuB,IAAA,UAAA4I,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAAvE,IAAA,GAAAuE,SAAA,CAAAlG,IAAA;QAAA;UAC5B+F,IAAI,GAAGX,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAACO,MAAM,CAAC,CAAC,IAAI,EAAE;UAAA,KACvDC,IAAI;YAAAG,SAAA,CAAAlG,IAAA;YAAA;UAAA;UAAA,OAAAkG,SAAA,CAAAtG,MAAA,WAASmG,IAAI;QAAA;UAAAG,SAAA,CAAAlG,IAAA;UAAA,OACE4C,GAAG,CAACuD,QAAQ,CAAC;YAAEC,QAAQ,EAAEN;UAAO,CAAC,CAAC;QAAA;UAAAE,mBAAA,GAAAE,SAAA,CAAAzG,IAAA;UAAjDwF,IAAI,GAAAe,mBAAA,CAAJf,IAAI;UACZK,YAAY,CAACE,OAAO,CAACM,MAAM,EAAEV,IAAI,CAACK,SAAS,CAACR,IAAI,CAAC,CAAC;UAAA,OAAAiB,SAAA,CAAAtG,MAAA,WAC3CqF,IAAI;QAAA;QAAA;UAAA,OAAAiB,SAAA,CAAApE,IAAA;MAAA;IAAA,GAAA+D,QAAA;EAAA,CACZ;EAAA,gBANKF,cAAcA,CAAAU,GAAA;IAAA,OAAAT,KAAA,CAAAnD,KAAA,OAAAD,SAAA;EAAA;AAAA,GAMnB;AACD,IAAM8D,yBAAyB,GAAG,SAA5BA,yBAAyBA,CAAIC,GAAG,EAAK;EACzC,IAAMC,KAAK,GAAG,eAAe;EAC7B,IAAIC,OAAO,GAAG,EAAE;EAChB,IAAIC,KAAK,GAAG,EAAE;EACd,OAAQA,KAAK,GAAGF,KAAK,CAACG,IAAI,CAACJ,GAAG,CAAC,EAAG;IAChCE,OAAO,CAACjG,IAAI,CAACkG,KAAK,CAAC,CAAC,CAAC,CAAC;EACxB;EACA,OAAOD,OAAO;AAChB,CAAC;AACD,IAAMG,qBAAqB,GAAG,SAAxBA,qBAAqBA,CAAIL,GAAG,EAAEM,IAAI,EAAEC,QAAQ,EAAK;EACrD,IAAMC,aAAa,GAAGF,IAAI,CAACG,OAAO,CAAC,qBAAqB,EAAE,MAAM,CAAC;EACjE,OAAOT,GAAG,CAACS,OAAO,CAAC,IAAIC,MAAM,CAACF,aAAa,EAAE,GAAG,CAAC,EAAED,QAAQ,CAAC;AAC9D,CAAC;AACD,IAAMI,UAAU,GAAG,SAAbA,UAAUA,CAAIjG,IAAI,EAAK;EAC3B,IAAIkG,SAAS,GAAG,EAAE;EAClB,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGvE,OAAO,CAAChC,MAAM,EAAEuG,KAAK,EAAE,EAAE;IACnD,IAAMP,IAAI,GAAGhE,OAAO,CAACuE,KAAK,CAAC;IAC3B,IAAIP,IAAI,CAACQ,IAAI,KAAK,IAAIpG,IAAI,GAAG,EAAE;MAC7BkG,SAAS,GAAG,kDAAkDN,IAAI,CAAC5F,IAAI,gBAAgB;IACzF;EACF;EACA,OAAOkG,SAAS,IAAIlG,IAAI;AAC1B,CAAC;AACD,OAAO,IAAMqG,qBAAqB;EAAA,IAAAC,KAAA,GAAAhF,iBAAA,cAAAzG,mBAAA,GAAAoF,IAAA,CAAG,SAAAsG,SAAOvC,IAAI,EAAEwC,OAAO;IAAA,IAAAC,UAAA,EAAAC,WAAA,EAAAP,KAAA,EAAAP,IAAA,EAAAe,cAAA,EAAAC,kBAAA,EAAAC,SAAA,EAAAC,aAAA,EAAAC,qBAAA,EAAAC,cAAA,EAAAC,cAAA,EAAAC,QAAA,EAAAC,WAAA,EAAAC,MAAA,EAAAC,KAAA,EAAAnB,SAAA,EAAAoB,UAAA,EAAAxD,IAAA,EAAAgB,IAAA;IAAA,OAAAjK,mBAAA,GAAAuB,IAAA,UAAAmL,UAAAC,SAAA;MAAA,kBAAAA,SAAA,CAAA9G,IAAA,GAAA8G,SAAA,CAAAzI,IAAA;QAAA;UACjD0H,UAAU,GAAG,EAAE;UACfC,WAAW,GAAG,EAAE;UACbP,KAAK,GAAG,CAAC;QAAA;UAAA,MAAEA,KAAK,GAAGnC,IAAI,CAACpE,MAAM;YAAA4H,SAAA,CAAAzI,IAAA;YAAA;UAAA;UAC/B6G,IAAI,GAAG5B,IAAI,CAACmC,KAAK,CAAC;UAClBQ,cAAc,GAAGH,OAAO,CAACZ,IAAI,CAAC6B,YAAY,CAAC;UAC3Cb,kBAAkB,GAAGhB,IAAI,CAAC6B,YAAY,KAAK1F,aAAa,CAACxG,KAAK,GAAGuG,IAAI,CAACvG,KAAK,CAACmM,SAAS;UACrFb,SAAS,GAAGD,kBAAkB,GAAG,wBAAwB,GAAG,oBAAoB;UAAA,MAClFhB,IAAI,CAAC+B,WAAW,KAAK,UAAU;YAAAH,SAAA,CAAAzI,IAAA;YAAA;UAAA;UACjC0H,UAAU,CAAClH,IAAI,EAAAuH,aAAA,GAAClB,IAAI,CAACgC,OAAO,cAAAd,aAAA,uBAAZA,aAAA,CAAce,UAAU,CAAC;UACzCnB,WAAW,CAACnH,IAAI,CAAC;YAAEuI,EAAE,EAAElC,IAAI,CAACmC,SAAS;YAAEC,GAAG,EAAEpC,IAAI,CAACiC,UAAU;YAAEnL,IAAI,EAAEkJ,IAAI,CAAC+B,WAAW;YAAEM,SAAS,EAAErC,IAAI,CAACsC,gBAAgB;YAAEvB,cAAc;YAAEC,kBAAkB;YAAEC,SAAS,EAAE,0BAA0B;YAAEe,OAAO,EAAEhC,IAAI,CAACgC,OAAO;YAAEO,QAAQ,EAAEvC,IAAI,CAACuC,QAAQ;YAAEC,QAAQ,GAAArB,qBAAA,GAAEP,OAAO,CAACZ,IAAI,CAAC6B,YAAY,CAAC,cAAAV,qBAAA,uBAA1BA,qBAAA,CAA4B/G;UAAK,CAAC,CAAC;UAAAwH,SAAA,CAAAzI,IAAA;UAAA;QAAA;UAAA,MACtR6G,IAAI,CAAC+B,WAAW,KAAK,WAAW;YAAAH,SAAA,CAAAzI,IAAA;YAAA;UAAA;UACzC2H,WAAW,CAACnH,IAAI,CAAC;YAAEuI,EAAE,EAAElC,IAAI,CAACmC,SAAS;YAAEC,GAAG,EAAEpC,IAAI,CAACiC,UAAU;YAAEnL,IAAI,EAAEkJ,IAAI,CAAC+B,WAAW;YAAEM,SAAS,EAAErC,IAAI,CAACsC,gBAAgB;YAAEvB,cAAc;YAAEC,kBAAkB;YAAEC,SAAS,EAAE,0BAA0B;YAAEe,OAAO,EAAEhC,IAAI,CAACgC,OAAO;YAAEO,QAAQ,EAAEvC,IAAI,CAACuC;UAAS,CAAC,CAAC;UAAAX,SAAA,CAAAzI,IAAA;UAAA;QAAA;UAAA,MAC1O6G,IAAI,CAAC+B,WAAW,KAAK,WAAW;YAAAH,SAAA,CAAAzI,IAAA;YAAA;UAAA;UACnCmI,QAAQ,GAAAmB,kBAAA,CAAO,IAAIC,GAAG,CAACjD,yBAAyB,EAAA2B,cAAA,GAACpB,IAAI,CAACgC,OAAO,cAAAZ,cAAA,uBAAZA,cAAA,CAAcY,OAAO,CAAC,CAAC;UAC1ET,WAAW,IAAAF,cAAA,GAAGrB,IAAI,CAACgC,OAAO,cAAAX,cAAA,uBAAZA,cAAA,CAAcW,OAAO,CAAC7B,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;UAC1J,KAASI,MAAK,GAAG,CAAC,EAAEA,MAAK,GAAGe,QAAQ,CAACtH,MAAM,EAAEuG,MAAK,EAAE,EAAE;YAC9CP,KAAI,GAAGsB,QAAQ,CAACf,MAAK,CAAC;YACtBD,SAAS,GAAGD,UAAU,CAACL,KAAI,CAAC;YAClCuB,WAAW,GAAGxB,qBAAqB,CAACwB,WAAW,EAAE,IAAIvB,KAAI,GAAG,EAAEM,SAAS,CAAC;UAC1E;UACAQ,WAAW,CAACnH,IAAI,CAAC;YAAEuI,EAAE,EAAElC,IAAI,CAACmC,SAAS;YAAEC,GAAG,EAAEpC,IAAI,CAACiC,UAAU;YAAEnL,IAAI,EAAEkJ,IAAI,CAAC+B,WAAW;YAAEM,SAAS,EAAErC,IAAI,CAACsC,gBAAgB;YAAEvB,cAAc;YAAEC,kBAAkB;YAAEC,SAAS;YAAEe,OAAO,EAAAW,aAAA,CAAAA,aAAA,KAAO3C,IAAI,CAACgC,OAAO;cAAET;YAAW,EAAE;YAAEgB,QAAQ,EAAEvC,IAAI,CAACuC;UAAS,CAAC,CAAC;UAAAX,SAAA,CAAAzI,IAAA;UAAA;QAAA;UAAA,MAClO6G,IAAI,CAAC+B,WAAW,KAAK,WAAW;YAAAH,SAAA,CAAAzI,IAAA;YAAA;UAAA;UACzC2H,WAAW,CAACnH,IAAI,CAAC;YAAEuI,EAAE,EAAElC,IAAI,CAACmC,SAAS;YAAEC,GAAG,EAAEpC,IAAI,CAACiC,UAAU;YAAEnL,IAAI,EAAEkJ,IAAI,CAAC+B,WAAW;YAAEM,SAAS,EAAErC,IAAI,CAACsC,gBAAgB;YAAEvB,cAAc;YAAEC,kBAAkB;YAAEC,SAAS;YAAE2B,UAAU,EAAE,SAAS;YAAEL,QAAQ,EAAEvC,IAAI,CAACuC;UAAS,CAAC,CAAC;UAAAX,SAAA,CAAAzI,IAAA;UAAA;QAAA;UAAA,MAC9M6G,IAAI,CAAC+B,WAAW,KAAK,eAAe;YAAAH,SAAA,CAAAzI,IAAA;YAAA;UAAA;UACvCuI,UAAU,GAAG1B,IAAI,CAACgC,OAAO,CAACA,OAAO,CAACa,KAAK,CAAC,GAAG,CAAC;UAAA,MAC9CnB,UAAU,CAAC,CAAC,CAAC,KAAK,MAAM;YAAAE,SAAA,CAAAzI,IAAA;YAAA;UAAA;UAAAyI,SAAA,CAAAzI,IAAA;UAAA,OACP2E,cAAc,CAAC4D,UAAU,CAAC,CAAC,CAAC,CAAC;QAAA;UAA1CxD,IAAI,GAAA0D,SAAA,CAAAhJ,IAAA;UACVkI,WAAW,CAACnH,IAAI,CAAC;YAAEuI,EAAE,EAAElC,IAAI,CAACmC,SAAS;YAAEC,GAAG,EAAEpC,IAAI,CAACiC,UAAU;YAAEnL,IAAI,EAAEkJ,IAAI,CAAC+B,WAAW;YAAEM,SAAS,EAAErC,IAAI,CAACsC,gBAAgB;YAAEvB,cAAc;YAAEC,kBAAkB;YAAEC,SAAS;YAAE2B,UAAU,EAAE,MAAM;YAAE1E,IAAI;YAAEqE,QAAQ,EAAEvC,IAAI,CAACuC;UAAS,CAAC,CAAC;UAAAX,SAAA,CAAAzI,IAAA;UAAA;QAAA;UAAA,MACjNuI,UAAU,CAAC,CAAC,CAAC,KAAK,MAAM;YAAAE,SAAA,CAAAzI,IAAA;YAAA;UAAA;UAAAyI,SAAA,CAAAzI,IAAA;UAAA,OACd2F,cAAc,CAAC4C,UAAU,CAAC,CAAC,CAAC,CAAC;QAAA;UAA1CxC,IAAI,GAAA0C,SAAA,CAAAhJ,IAAA;UACVkI,WAAW,CAACnH,IAAI,CAAC;YAAEuI,EAAE,EAAElC,IAAI,CAACmC,SAAS;YAAEC,GAAG,EAAEpC,IAAI,CAACiC,UAAU;YAAEnL,IAAI,EAAEkJ,IAAI,CAAC+B,WAAW;YAAEM,SAAS,EAAErC,IAAI,CAACsC,gBAAgB;YAAEvB,cAAc;YAAEC,kBAAkB;YAAEC,SAAS;YAAE2B,UAAU,EAAE,MAAM;YAAE1D,IAAI;YAAEqD,QAAQ,EAAEvC,IAAI,CAACuC;UAAS,CAAC,CAAC;UAAAX,SAAA,CAAAzI,IAAA;UAAA;QAAA;UAE1N2H,WAAW,CAACnH,IAAI,CAAC;YAAEuI,EAAE,EAAElC,IAAI,CAACmC,SAAS;YAAEC,GAAG,EAAEpC,IAAI,CAACiC,UAAU;YAAEnL,IAAI,EAAEkJ,IAAI,CAAC+B,WAAW;YAAEM,SAAS,EAAErC,IAAI,CAACsC,gBAAgB;YAAEvB,cAAc;YAAEC,kBAAkB;YAAEC,SAAS;YAAE2B,UAAU,EAAE,SAAS;YAAEL,QAAQ,EAAEvC,IAAI,CAACuC;UAAS,CAAC,CAAC;QAAA;UAAAX,SAAA,CAAAzI,IAAA;UAAA;QAAA;UAGzN2H,WAAW,CAACnH,IAAI,CAAC;YAAEuI,EAAE,EAAElC,IAAI,CAACmC,SAAS;YAAEC,GAAG,EAAEpC,IAAI,CAACiC,UAAU;YAAEnL,IAAI,EAAEkJ,IAAI,CAAC+B,WAAW;YAAEM,SAAS,EAAErC,IAAI,CAACsC,gBAAgB;YAAEvB,cAAc;YAAEC,kBAAkB;YAAEC,SAAS;YAAEe,OAAO,EAAEhC,IAAI,CAACgC,OAAO;YAAEO,QAAQ,EAAEvC,IAAI,CAACuC;UAAS,CAAC,CAAC;QAAA;UAjClLhC,KAAK,EAAE;UAAAqB,SAAA,CAAAzI,IAAA;UAAA;QAAA;UAAA,OAAAyI,SAAA,CAAA7I,MAAA,WAoCzC;YAAE+H,WAAW;YAAED;UAAW,CAAC;QAAA;QAAA;UAAA,OAAAe,SAAA,CAAA3G,IAAA;MAAA;IAAA,GAAA0F,QAAA;EAAA,CACnC;EAAA,gBAxCYF,qBAAqBA,CAAAqC,GAAA,EAAAC,GAAA;IAAA,OAAArC,KAAA,CAAA9E,KAAA,OAAAD,SAAA;EAAA;AAAA,GAwCjC;AACD;AACA;AACA;AACA;AACA;AACA,IAAMqH,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,QAAQ,EAAK;EACrC,IAAMC,YAAY,GAAGD,QAAQ,CAACE,WAAW,CAAC,GAAG,CAAC;EAC9C,IAAID,YAAY,KAAK,CAAC,CAAC,EAAE,OAAO,EAAE;EAClC,OAAOD,QAAQ,CAACjI,KAAK,CAACkI,YAAY,CAAC;AACrC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,IAAME,2BAA2B,GAAG,SAA9BA,2BAA2BA,CAAIH,QAAQ,EAAK;EAChD,IAAMC,YAAY,GAAGD,QAAQ,CAACE,WAAW,CAAC,GAAG,CAAC;EAC9C,IAAID,YAAY,KAAK,CAAC,CAAC,EAAE,OAAOD,QAAQ;EACxC,OAAOA,QAAQ,CAACjI,KAAK,CAAC,CAAC,EAAEkI,YAAY,CAAC;AACxC,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,IAAMG,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIJ,QAAQ,EAAEK,aAAa,EAAK;EAC5D;EACA,IAAI,EAACA,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEC,QAAQ,CAACN,QAAQ,CAAC,GAAE,OAAOA,QAAQ;EACvD;EACA,IAAMO,GAAG,GAAGR,gBAAgB,CAACC,QAAQ,CAAC;EACtC,IAAMQ,QAAQ,GAAGL,2BAA2B,CAACH,QAAQ,CAAC;EACtD,IAAIS,OAAO,GAAGT,QAAQ;EACtB,IAAIU,OAAO,GAAG,CAAC;EACf;EACA,OAAOL,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAEC,QAAQ,CAACG,OAAO,CAAC,EAAE;IACvCA,OAAO,GAAG,GAAGD,QAAQ,IAAIE,OAAO,IAAIH,GAAG,EAAE;IACzCG,OAAO,EAAE;EACX;EACA,OAAOD,OAAO;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}