{"ast": null, "code": "// Process block-level custom containers\n//\n'use strict';\n\nmodule.exports = function container_plugin(md, name, options) {\n  // Second param may be useful if you decide\n  // to increase minimal allowed marker length\n  function validateDefault(params /*, markup*/) {\n    return params.trim().split(' ', 2)[0] === name;\n  }\n  function renderDefault(tokens, idx, _options, env, slf) {\n    // add a class to the opening tag\n    if (tokens[idx].nesting === 1) {\n      tokens[idx].attrJoin('class', name);\n    }\n    return slf.renderToken(tokens, idx, _options, env, slf);\n  }\n  options = options || {};\n  var min_markers = 3,\n    marker_str = options.marker || ':',\n    marker_char = marker_str.charCodeAt(0),\n    marker_len = marker_str.length,\n    validate = options.validate || validateDefault,\n    render = options.render || renderDefault;\n  function container(state, startLine, endLine, silent) {\n    var pos,\n      nextLine,\n      marker_count,\n      markup,\n      params,\n      token,\n      old_parent,\n      old_line_max,\n      auto_closed = false,\n      start = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine];\n\n    // Check out the first character quickly,\n    // this should filter out most of non-containers\n    //\n    if (marker_char !== state.src.charCodeAt(start)) {\n      return false;\n    }\n\n    // Check out the rest of the marker string\n    //\n    for (pos = start + 1; pos <= max; pos++) {\n      if (marker_str[(pos - start) % marker_len] !== state.src[pos]) {\n        break;\n      }\n    }\n    marker_count = Math.floor((pos - start) / marker_len);\n    if (marker_count < min_markers) {\n      return false;\n    }\n    pos -= (pos - start) % marker_len;\n    markup = state.src.slice(start, pos);\n    params = state.src.slice(pos, max);\n    if (!validate(params, markup)) {\n      return false;\n    }\n\n    // Since start is found, we can report success here in validation mode\n    //\n    if (silent) {\n      return true;\n    }\n\n    // Search for the end of the block\n    //\n    nextLine = startLine;\n    for (;;) {\n      nextLine++;\n      if (nextLine >= endLine) {\n        // unclosed block should be autoclosed by end of document.\n        // also block seems to be autoclosed by end of parent\n        break;\n      }\n      start = state.bMarks[nextLine] + state.tShift[nextLine];\n      max = state.eMarks[nextLine];\n      if (start < max && state.sCount[nextLine] < state.blkIndent) {\n        // non-empty line with negative indent should stop the list:\n        // - ```\n        //  test\n        break;\n      }\n      if (marker_char !== state.src.charCodeAt(start)) {\n        continue;\n      }\n      if (state.sCount[nextLine] - state.blkIndent >= 4) {\n        // closing fence should be indented less than 4 spaces\n        continue;\n      }\n      for (pos = start + 1; pos <= max; pos++) {\n        if (marker_str[(pos - start) % marker_len] !== state.src[pos]) {\n          break;\n        }\n      }\n\n      // closing code fence must be at least as long as the opening one\n      if (Math.floor((pos - start) / marker_len) < marker_count) {\n        continue;\n      }\n\n      // make sure tail has spaces only\n      pos -= (pos - start) % marker_len;\n      pos = state.skipSpaces(pos);\n      if (pos < max) {\n        continue;\n      }\n\n      // found!\n      auto_closed = true;\n      break;\n    }\n    old_parent = state.parentType;\n    old_line_max = state.lineMax;\n    state.parentType = 'container';\n\n    // this will prevent lazy continuations from ever going past our end marker\n    state.lineMax = nextLine;\n    token = state.push('container_' + name + '_open', 'div', 1);\n    token.markup = markup;\n    token.block = true;\n    token.info = params;\n    token.map = [startLine, nextLine];\n    state.md.block.tokenize(state, startLine + 1, nextLine);\n    token = state.push('container_' + name + '_close', 'div', -1);\n    token.markup = state.src.slice(start, pos);\n    token.block = true;\n    state.parentType = old_parent;\n    state.lineMax = old_line_max;\n    state.line = nextLine + (auto_closed ? 1 : 0);\n    return true;\n  }\n  md.block.ruler.before('fence', 'container_' + name, container, {\n    alt: ['paragraph', 'reference', 'blockquote', 'list']\n  });\n  md.renderer.rules['container_' + name + '_open'] = render;\n  md.renderer.rules['container_' + name + '_close'] = render;\n};", "map": {"version": 3, "names": ["module", "exports", "container_plugin", "md", "name", "options", "validateDefault", "params", "trim", "split", "renderDefault", "tokens", "idx", "_options", "env", "slf", "nesting", "attr<PERSON><PERSON>n", "renderToken", "min_markers", "marker_str", "marker", "marker_char", "charCodeAt", "marker_len", "length", "validate", "render", "container", "state", "startLine", "endLine", "silent", "pos", "nextLine", "marker_count", "markup", "token", "old_parent", "old_line_max", "auto_closed", "start", "bMarks", "tShift", "max", "eMarks", "src", "Math", "floor", "slice", "sCount", "blkIndent", "skipSpaces", "parentType", "lineMax", "push", "block", "info", "map", "tokenize", "line", "ruler", "before", "alt", "renderer", "rules"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it-container@3.0.0/node_modules/markdown-it-container/index.js"], "sourcesContent": ["// Process block-level custom containers\n//\n'use strict';\n\n\nmodule.exports = function container_plugin(md, name, options) {\n\n  // Second param may be useful if you decide\n  // to increase minimal allowed marker length\n  function validateDefault(params/*, markup*/) {\n    return params.trim().split(' ', 2)[0] === name;\n  }\n\n  function renderDefault(tokens, idx, _options, env, slf) {\n\n    // add a class to the opening tag\n    if (tokens[idx].nesting === 1) {\n      tokens[idx].attrJoin('class', name);\n    }\n\n    return slf.renderToken(tokens, idx, _options, env, slf);\n  }\n\n  options = options || {};\n\n  var min_markers = 3,\n      marker_str  = options.marker || ':',\n      marker_char = marker_str.charCodeAt(0),\n      marker_len  = marker_str.length,\n      validate    = options.validate || validateDefault,\n      render      = options.render || renderDefault;\n\n  function container(state, startLine, endLine, silent) {\n    var pos, nextLine, marker_count, markup, params, token,\n        old_parent, old_line_max,\n        auto_closed = false,\n        start = state.bMarks[startLine] + state.tShift[startLine],\n        max = state.eMarks[startLine];\n\n    // Check out the first character quickly,\n    // this should filter out most of non-containers\n    //\n    if (marker_char !== state.src.charCodeAt(start)) { return false; }\n\n    // Check out the rest of the marker string\n    //\n    for (pos = start + 1; pos <= max; pos++) {\n      if (marker_str[(pos - start) % marker_len] !== state.src[pos]) {\n        break;\n      }\n    }\n\n    marker_count = Math.floor((pos - start) / marker_len);\n    if (marker_count < min_markers) { return false; }\n    pos -= (pos - start) % marker_len;\n\n    markup = state.src.slice(start, pos);\n    params = state.src.slice(pos, max);\n    if (!validate(params, markup)) { return false; }\n\n    // Since start is found, we can report success here in validation mode\n    //\n    if (silent) { return true; }\n\n    // Search for the end of the block\n    //\n    nextLine = startLine;\n\n    for (;;) {\n      nextLine++;\n      if (nextLine >= endLine) {\n        // unclosed block should be autoclosed by end of document.\n        // also block seems to be autoclosed by end of parent\n        break;\n      }\n\n      start = state.bMarks[nextLine] + state.tShift[nextLine];\n      max = state.eMarks[nextLine];\n\n      if (start < max && state.sCount[nextLine] < state.blkIndent) {\n        // non-empty line with negative indent should stop the list:\n        // - ```\n        //  test\n        break;\n      }\n\n      if (marker_char !== state.src.charCodeAt(start)) { continue; }\n\n      if (state.sCount[nextLine] - state.blkIndent >= 4) {\n        // closing fence should be indented less than 4 spaces\n        continue;\n      }\n\n      for (pos = start + 1; pos <= max; pos++) {\n        if (marker_str[(pos - start) % marker_len] !== state.src[pos]) {\n          break;\n        }\n      }\n\n      // closing code fence must be at least as long as the opening one\n      if (Math.floor((pos - start) / marker_len) < marker_count) { continue; }\n\n      // make sure tail has spaces only\n      pos -= (pos - start) % marker_len;\n      pos = state.skipSpaces(pos);\n\n      if (pos < max) { continue; }\n\n      // found!\n      auto_closed = true;\n      break;\n    }\n\n    old_parent = state.parentType;\n    old_line_max = state.lineMax;\n    state.parentType = 'container';\n\n    // this will prevent lazy continuations from ever going past our end marker\n    state.lineMax = nextLine;\n\n    token        = state.push('container_' + name + '_open', 'div', 1);\n    token.markup = markup;\n    token.block  = true;\n    token.info   = params;\n    token.map    = [ startLine, nextLine ];\n\n    state.md.block.tokenize(state, startLine + 1, nextLine);\n\n    token        = state.push('container_' + name + '_close', 'div', -1);\n    token.markup = state.src.slice(start, pos);\n    token.block  = true;\n\n    state.parentType = old_parent;\n    state.lineMax = old_line_max;\n    state.line = nextLine + (auto_closed ? 1 : 0);\n\n    return true;\n  }\n\n  md.block.ruler.before('fence', 'container_' + name, container, {\n    alt: [ 'paragraph', 'reference', 'blockquote', 'list' ]\n  });\n  md.renderer.rules['container_' + name + '_open'] = render;\n  md.renderer.rules['container_' + name + '_close'] = render;\n};\n"], "mappings": "AAAA;AACA;AACA,YAAY;;AAGZA,MAAM,CAACC,OAAO,GAAG,SAASC,gBAAgBA,CAACC,EAAE,EAAEC,IAAI,EAAEC,OAAO,EAAE;EAE5D;EACA;EACA,SAASC,eAAeA,CAACC,MAAM,eAAc;IAC3C,OAAOA,MAAM,CAACC,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKL,IAAI;EAChD;EAEA,SAASM,aAAaA,CAACC,MAAM,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,GAAG,EAAE;IAEtD;IACA,IAAIJ,MAAM,CAACC,GAAG,CAAC,CAACI,OAAO,KAAK,CAAC,EAAE;MAC7BL,MAAM,CAACC,GAAG,CAAC,CAACK,QAAQ,CAAC,OAAO,EAAEb,IAAI,CAAC;IACrC;IAEA,OAAOW,GAAG,CAACG,WAAW,CAACP,MAAM,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,GAAG,CAAC;EACzD;EAEAV,OAAO,GAAGA,OAAO,IAAI,CAAC,CAAC;EAEvB,IAAIc,WAAW,GAAG,CAAC;IACfC,UAAU,GAAIf,OAAO,CAACgB,MAAM,IAAI,GAAG;IACnCC,WAAW,GAAGF,UAAU,CAACG,UAAU,CAAC,CAAC,CAAC;IACtCC,UAAU,GAAIJ,UAAU,CAACK,MAAM;IAC/BC,QAAQ,GAAMrB,OAAO,CAACqB,QAAQ,IAAIpB,eAAe;IACjDqB,MAAM,GAAQtB,OAAO,CAACsB,MAAM,IAAIjB,aAAa;EAEjD,SAASkB,SAASA,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAE;IACpD,IAAIC,GAAG;MAAEC,QAAQ;MAAEC,YAAY;MAAEC,MAAM;MAAE7B,MAAM;MAAE8B,KAAK;MAClDC,UAAU;MAAEC,YAAY;MACxBC,WAAW,GAAG,KAAK;MACnBC,KAAK,GAAGZ,KAAK,CAACa,MAAM,CAACZ,SAAS,CAAC,GAAGD,KAAK,CAACc,MAAM,CAACb,SAAS,CAAC;MACzDc,GAAG,GAAGf,KAAK,CAACgB,MAAM,CAACf,SAAS,CAAC;;IAEjC;IACA;IACA;IACA,IAAIR,WAAW,KAAKO,KAAK,CAACiB,GAAG,CAACvB,UAAU,CAACkB,KAAK,CAAC,EAAE;MAAE,OAAO,KAAK;IAAE;;IAEjE;IACA;IACA,KAAKR,GAAG,GAAGQ,KAAK,GAAG,CAAC,EAAER,GAAG,IAAIW,GAAG,EAAEX,GAAG,EAAE,EAAE;MACvC,IAAIb,UAAU,CAAC,CAACa,GAAG,GAAGQ,KAAK,IAAIjB,UAAU,CAAC,KAAKK,KAAK,CAACiB,GAAG,CAACb,GAAG,CAAC,EAAE;QAC7D;MACF;IACF;IAEAE,YAAY,GAAGY,IAAI,CAACC,KAAK,CAAC,CAACf,GAAG,GAAGQ,KAAK,IAAIjB,UAAU,CAAC;IACrD,IAAIW,YAAY,GAAGhB,WAAW,EAAE;MAAE,OAAO,KAAK;IAAE;IAChDc,GAAG,IAAI,CAACA,GAAG,GAAGQ,KAAK,IAAIjB,UAAU;IAEjCY,MAAM,GAAGP,KAAK,CAACiB,GAAG,CAACG,KAAK,CAACR,KAAK,EAAER,GAAG,CAAC;IACpC1B,MAAM,GAAGsB,KAAK,CAACiB,GAAG,CAACG,KAAK,CAAChB,GAAG,EAAEW,GAAG,CAAC;IAClC,IAAI,CAAClB,QAAQ,CAACnB,MAAM,EAAE6B,MAAM,CAAC,EAAE;MAAE,OAAO,KAAK;IAAE;;IAE/C;IACA;IACA,IAAIJ,MAAM,EAAE;MAAE,OAAO,IAAI;IAAE;;IAE3B;IACA;IACAE,QAAQ,GAAGJ,SAAS;IAEpB,SAAS;MACPI,QAAQ,EAAE;MACV,IAAIA,QAAQ,IAAIH,OAAO,EAAE;QACvB;QACA;QACA;MACF;MAEAU,KAAK,GAAGZ,KAAK,CAACa,MAAM,CAACR,QAAQ,CAAC,GAAGL,KAAK,CAACc,MAAM,CAACT,QAAQ,CAAC;MACvDU,GAAG,GAAGf,KAAK,CAACgB,MAAM,CAACX,QAAQ,CAAC;MAE5B,IAAIO,KAAK,GAAGG,GAAG,IAAIf,KAAK,CAACqB,MAAM,CAAChB,QAAQ,CAAC,GAAGL,KAAK,CAACsB,SAAS,EAAE;QAC3D;QACA;QACA;QACA;MACF;MAEA,IAAI7B,WAAW,KAAKO,KAAK,CAACiB,GAAG,CAACvB,UAAU,CAACkB,KAAK,CAAC,EAAE;QAAE;MAAU;MAE7D,IAAIZ,KAAK,CAACqB,MAAM,CAAChB,QAAQ,CAAC,GAAGL,KAAK,CAACsB,SAAS,IAAI,CAAC,EAAE;QACjD;QACA;MACF;MAEA,KAAKlB,GAAG,GAAGQ,KAAK,GAAG,CAAC,EAAER,GAAG,IAAIW,GAAG,EAAEX,GAAG,EAAE,EAAE;QACvC,IAAIb,UAAU,CAAC,CAACa,GAAG,GAAGQ,KAAK,IAAIjB,UAAU,CAAC,KAAKK,KAAK,CAACiB,GAAG,CAACb,GAAG,CAAC,EAAE;UAC7D;QACF;MACF;;MAEA;MACA,IAAIc,IAAI,CAACC,KAAK,CAAC,CAACf,GAAG,GAAGQ,KAAK,IAAIjB,UAAU,CAAC,GAAGW,YAAY,EAAE;QAAE;MAAU;;MAEvE;MACAF,GAAG,IAAI,CAACA,GAAG,GAAGQ,KAAK,IAAIjB,UAAU;MACjCS,GAAG,GAAGJ,KAAK,CAACuB,UAAU,CAACnB,GAAG,CAAC;MAE3B,IAAIA,GAAG,GAAGW,GAAG,EAAE;QAAE;MAAU;;MAE3B;MACAJ,WAAW,GAAG,IAAI;MAClB;IACF;IAEAF,UAAU,GAAGT,KAAK,CAACwB,UAAU;IAC7Bd,YAAY,GAAGV,KAAK,CAACyB,OAAO;IAC5BzB,KAAK,CAACwB,UAAU,GAAG,WAAW;;IAE9B;IACAxB,KAAK,CAACyB,OAAO,GAAGpB,QAAQ;IAExBG,KAAK,GAAUR,KAAK,CAAC0B,IAAI,CAAC,YAAY,GAAGnD,IAAI,GAAG,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;IAClEiC,KAAK,CAACD,MAAM,GAAGA,MAAM;IACrBC,KAAK,CAACmB,KAAK,GAAI,IAAI;IACnBnB,KAAK,CAACoB,IAAI,GAAKlD,MAAM;IACrB8B,KAAK,CAACqB,GAAG,GAAM,CAAE5B,SAAS,EAAEI,QAAQ,CAAE;IAEtCL,KAAK,CAAC1B,EAAE,CAACqD,KAAK,CAACG,QAAQ,CAAC9B,KAAK,EAAEC,SAAS,GAAG,CAAC,EAAEI,QAAQ,CAAC;IAEvDG,KAAK,GAAUR,KAAK,CAAC0B,IAAI,CAAC,YAAY,GAAGnD,IAAI,GAAG,QAAQ,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC;IACpEiC,KAAK,CAACD,MAAM,GAAGP,KAAK,CAACiB,GAAG,CAACG,KAAK,CAACR,KAAK,EAAER,GAAG,CAAC;IAC1CI,KAAK,CAACmB,KAAK,GAAI,IAAI;IAEnB3B,KAAK,CAACwB,UAAU,GAAGf,UAAU;IAC7BT,KAAK,CAACyB,OAAO,GAAGf,YAAY;IAC5BV,KAAK,CAAC+B,IAAI,GAAG1B,QAAQ,IAAIM,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC;IAE7C,OAAO,IAAI;EACb;EAEArC,EAAE,CAACqD,KAAK,CAACK,KAAK,CAACC,MAAM,CAAC,OAAO,EAAE,YAAY,GAAG1D,IAAI,EAAEwB,SAAS,EAAE;IAC7DmC,GAAG,EAAE,CAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM;EACvD,CAAC,CAAC;EACF5D,EAAE,CAAC6D,QAAQ,CAACC,KAAK,CAAC,YAAY,GAAG7D,IAAI,GAAG,OAAO,CAAC,GAAGuB,MAAM;EACzDxB,EAAE,CAAC6D,QAAQ,CAACC,KAAK,CAAC,YAAY,GAAG7D,IAAI,GAAG,QAAQ,CAAC,GAAGuB,MAAM;AAC5D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}