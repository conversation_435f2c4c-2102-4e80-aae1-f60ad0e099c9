{"ast": null, "code": "\"use strict\";\n\nvar docxContentType = \"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml\";\nvar docxmContentType = \"application/vnd.ms-word.document.macroEnabled.main+xml\";\nvar dotxContentType = \"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml\";\nvar dotmContentType = \"application/vnd.ms-word.template.macroEnabledTemplate.main+xml\";\nvar headerContentType = \"application/vnd.openxmlformats-officedocument.wordprocessingml.header+xml\";\nvar footnotesContentType = \"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml\";\nvar commentsContentType = \"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml\";\nvar footerContentType = \"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml\";\nvar pptxContentType = \"application/vnd.openxmlformats-officedocument.presentationml.slide+xml\";\nvar pptxSlideMaster = \"application/vnd.openxmlformats-officedocument.presentationml.slideMaster+xml\";\nvar pptxSlideLayout = \"application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml\";\nvar pptxPresentationContentType = \"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml\";\nvar main = [docxContentType, docxmContentType, dotxContentType, dotmContentType];\nvar filetypes = {\n  main: main,\n  docx: [headerContentType].concat(main, [footerContentType, footnotesContentType, commentsContentType]),\n  pptx: [pptxContentType, pptxSlideMaster, pptxSlideLayout, pptxPresentationContentType]\n};\nmodule.exports = filetypes;", "map": {"version": 3, "names": ["docxContentType", "docxmContentType", "dotxContentType", "dotmContentType", "headerContentType", "footnotesContentType", "commentsContentType", "footerContentType", "pptxContentType", "pptxSlideMaster", "pptxSlideLayout", "pptxPresentationContentType", "main", "filetypes", "docx", "concat", "pptx", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/docxtemplater@3.52.0/node_modules/docxtemplater/js/filetypes.js"], "sourcesContent": ["\"use strict\";\n\nvar docxContentType = \"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml\";\nvar docxmContentType = \"application/vnd.ms-word.document.macroEnabled.main+xml\";\nvar dotxContentType = \"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml\";\nvar dotmContentType = \"application/vnd.ms-word.template.macroEnabledTemplate.main+xml\";\nvar headerContentType = \"application/vnd.openxmlformats-officedocument.wordprocessingml.header+xml\";\nvar footnotesContentType = \"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml\";\nvar commentsContentType = \"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml\";\nvar footerContentType = \"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml\";\nvar pptxContentType = \"application/vnd.openxmlformats-officedocument.presentationml.slide+xml\";\nvar pptxSlideMaster = \"application/vnd.openxmlformats-officedocument.presentationml.slideMaster+xml\";\nvar pptxSlideLayout = \"application/vnd.openxmlformats-officedocument.presentationml.slideLayout+xml\";\nvar pptxPresentationContentType = \"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml\";\nvar main = [docxContentType, docxmContentType, dotxContentType, dotmContentType];\nvar filetypes = {\n  main: main,\n  docx: [headerContentType].concat(main, [footerContentType, footnotesContentType, commentsContentType]),\n  pptx: [pptxContentType, pptxSlideMaster, pptxSlideLayout, pptxPresentationContentType]\n};\nmodule.exports = filetypes;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,eAAe,GAAG,kFAAkF;AACxG,IAAIC,gBAAgB,GAAG,wDAAwD;AAC/E,IAAIC,eAAe,GAAG,kFAAkF;AACxG,IAAIC,eAAe,GAAG,gEAAgE;AACtF,IAAIC,iBAAiB,GAAG,2EAA2E;AACnG,IAAIC,oBAAoB,GAAG,8EAA8E;AACzG,IAAIC,mBAAmB,GAAG,6EAA6E;AACvG,IAAIC,iBAAiB,GAAG,2EAA2E;AACnG,IAAIC,eAAe,GAAG,wEAAwE;AAC9F,IAAIC,eAAe,GAAG,8EAA8E;AACpG,IAAIC,eAAe,GAAG,8EAA8E;AACpG,IAAIC,2BAA2B,GAAG,oFAAoF;AACtH,IAAIC,IAAI,GAAG,CAACZ,eAAe,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,eAAe,CAAC;AAChF,IAAIU,SAAS,GAAG;EACdD,IAAI,EAAEA,IAAI;EACVE,IAAI,EAAE,CAACV,iBAAiB,CAAC,CAACW,MAAM,CAACH,IAAI,EAAE,CAACL,iBAAiB,EAAEF,oBAAoB,EAAEC,mBAAmB,CAAC,CAAC;EACtGU,IAAI,EAAE,CAACR,eAAe,EAAEC,eAAe,EAAEC,eAAe,EAAEC,2BAA2B;AACvF,CAAC;AACDM,MAAM,CAACC,OAAO,GAAGL,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}