{"ast": null, "code": "var asciiWords = require('./_asciiWords'),\n  hasUnicodeWord = require('./_hasUnicodeWord'),\n  toString = require('./toString'),\n  unicodeWords = require('./_unicodeWords');\n\n/**\n * Splits `string` into an array of its words.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to inspect.\n * @param {RegExp|string} [pattern] The pattern to match words.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Array} Returns the words of `string`.\n * @example\n *\n * _.words('fred, barney, & pebbles');\n * // => ['fred', 'barney', 'pebbles']\n *\n * _.words('fred, barney, & pebbles', /[^, ]+/g);\n * // => ['fred', 'barney', '&', 'pebbles']\n */\nfunction words(string, pattern, guard) {\n  string = toString(string);\n  pattern = guard ? undefined : pattern;\n  if (pattern === undefined) {\n    return hasUnicodeWord(string) ? unicodeWords(string) : asciiWords(string);\n  }\n  return string.match(pattern) || [];\n}\nmodule.exports = words;", "map": {"version": 3, "names": ["<PERSON>cii<PERSON><PERSON><PERSON>", "require", "hasUnicodeWord", "toString", "unicodeWords", "words", "string", "pattern", "guard", "undefined", "match", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/words.js"], "sourcesContent": ["var asciiWords = require('./_asciiWords'),\n    hasUnicodeWord = require('./_hasUnicodeWord'),\n    toString = require('./toString'),\n    unicodeWords = require('./_unicodeWords');\n\n/**\n * Splits `string` into an array of its words.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to inspect.\n * @param {RegExp|string} [pattern] The pattern to match words.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Array} Returns the words of `string`.\n * @example\n *\n * _.words('fred, barney, & pebbles');\n * // => ['fred', 'barney', 'pebbles']\n *\n * _.words('fred, barney, & pebbles', /[^, ]+/g);\n * // => ['fred', 'barney', '&', 'pebbles']\n */\nfunction words(string, pattern, guard) {\n  string = toString(string);\n  pattern = guard ? undefined : pattern;\n\n  if (pattern === undefined) {\n    return hasUnicodeWord(string) ? unicodeWords(string) : asciiWords(string);\n  }\n  return string.match(pattern) || [];\n}\n\nmodule.exports = words;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;EACrCC,cAAc,GAAGD,OAAO,CAAC,mBAAmB,CAAC;EAC7CE,QAAQ,GAAGF,OAAO,CAAC,YAAY,CAAC;EAChCG,YAAY,GAAGH,OAAO,CAAC,iBAAiB,CAAC;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAE;EACrCF,MAAM,GAAGH,QAAQ,CAACG,MAAM,CAAC;EACzBC,OAAO,GAAGC,KAAK,GAAGC,SAAS,GAAGF,OAAO;EAErC,IAAIA,OAAO,KAAKE,SAAS,EAAE;IACzB,OAAOP,cAAc,CAACI,MAAM,CAAC,GAAGF,YAAY,CAACE,MAAM,CAAC,GAAGN,UAAU,CAACM,MAAM,CAAC;EAC3E;EACA,OAAOA,MAAM,CAACI,KAAK,CAACH,OAAO,CAAC,IAAI,EAAE;AACpC;AAEAI,MAAM,CAACC,OAAO,GAAGP,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}