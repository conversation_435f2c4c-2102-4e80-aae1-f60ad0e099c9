{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"DownloadFileName\"\n};\nvar _hoisted_2 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode(_component_el_form, {\n    ref: \"formRef\",\n    model: $setup.form,\n    rules: $setup.rules,\n    inline: \"\",\n    \"label-position\": \"top\",\n    class: \"globalForm\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form_item, {\n        label: $setup.props.name,\n        prop: \"fileName\",\n        class: \"globalFormTitle\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_input, {\n            modelValue: $setup.form.fileName,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.form.fileName = $event;\n            }),\n            placeholder: `请输入${$setup.props.name}`,\n            clearable: \"\"\n          }, null, 8 /* PROPS */, [\"modelValue\", \"placeholder\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"label\"]), _createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: _cache[1] || (_cache[1] = function ($event) {\n          return $setup.submitForm($setup.formRef);\n        })\n      }, {\n        default: _withCtx(function () {\n          return _cache[2] || (_cache[2] = [_createTextVNode(\"确定\")]);\n        }),\n        _: 1 /* STABLE */\n      }), _createVNode(_component_el_button, {\n        onClick: $setup.resetForm\n      }, {\n        default: _withCtx(function () {\n          return _cache[3] || (_cache[3] = [_createTextVNode(\"取消\")]);\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"model\", \"rules\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "_component_el_form", "ref", "model", "$setup", "form", "rules", "inline", "default", "_withCtx", "_component_el_form_item", "label", "props", "name", "prop", "_component_el_input", "modelValue", "fileName", "_cache", "$event", "placeholder", "clearable", "_", "_createElementVNode", "_hoisted_2", "_component_el_button", "type", "onClick", "submitForm", "formRef", "_createTextVNode", "resetForm"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\Intelligentize\\VersionComparison\\DownloadFileName\\DownloadFileName.vue"], "sourcesContent": ["<template>\r\n  <div class=\"DownloadFileName\">\r\n    <el-form ref=\"formRef\"\r\n             :model=\"form\"\r\n             :rules=\"rules\"\r\n             inline\r\n             label-position=\"top\"\r\n             class=\"globalForm\">\r\n      <el-form-item :label=\"props.name\"\r\n                    prop=\"fileName\"\r\n                    class=\"globalFormTitle\">\r\n        <el-input v-model=\"form.fileName\"\r\n                  :placeholder=\"`请输入${props.name}`\"\r\n                  clearable />\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\"\r\n                   @click=\"submitForm(formRef)\">确定</el-button>\r\n        <el-button @click=\"resetForm\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'DownloadFileName' }\r\n</script>\r\n<script setup>\r\nimport { reactive, ref, onMounted } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ name: { type: String, default: '文件名' }, fileName: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\n\r\nconst formRef = ref()\r\nconst form = reactive({ fileName: '' })\r\nconst rules = reactive({ fileName: [{ required: true, message: `请输入${props.name}`, trigger: ['blur', 'change'] }] })\r\n\r\nonMounted(() => { if (props.fileName) { form.fileName = props.fileName } })\r\n\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) { emit('callback', form.fileName) } else { ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' }) }\r\n  })\r\n}\r\nconst resetForm = () => { emit('callback', '') }\r\n</script>\r\n<style lang=\"scss\">\r\n.DownloadFileName {\r\n  width: 680px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;EAcpBA,KAAK,EAAC;AAAkB;;;;;;uBAdjCC,mBAAA,CAoBM,OApBNC,UAoBM,GAnBJC,YAAA,CAkBUC,kBAAA;IAlBDC,GAAG,EAAC,SAAS;IACZC,KAAK,EAAEC,MAAA,CAAAC,IAAI;IACXC,KAAK,EAAEF,MAAA,CAAAE,KAAK;IACbC,MAAM,EAAN,EAAM;IACN,gBAAc,EAAC,KAAK;IACpBV,KAAK,EAAC;;IAPnBW,OAAA,EAAAC,QAAA,CAQM;MAAA,OAMe,CANfT,YAAA,CAMeU,uBAAA;QANAC,KAAK,EAAEP,MAAA,CAAAQ,KAAK,CAACC,IAAI;QAClBC,IAAI,EAAC,UAAU;QACfjB,KAAK,EAAC;;QAV1BW,OAAA,EAAAC,QAAA,CAWQ;UAAA,OAEsB,CAFtBT,YAAA,CAEsBe,mBAAA;YAb9BC,UAAA,EAW2BZ,MAAA,CAAAC,IAAI,CAACY,QAAQ;YAXxC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAW2Bf,MAAA,CAAAC,IAAI,CAACY,QAAQ,GAAAE,MAAA;YAAA;YACrBC,WAAW,QAAQhB,MAAA,CAAAQ,KAAK,CAACC,IAAI;YAC9BQ,SAAS,EAAT;;;QAblBC,CAAA;oCAeMC,mBAAA,CAIM,OAJNC,UAIM,GAHJxB,YAAA,CACsDyB,oBAAA;QAD3CC,IAAI,EAAC,SAAS;QACbC,OAAK,EAAAT,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAEf,MAAA,CAAAwB,UAAU,CAACxB,MAAA,CAAAyB,OAAO;QAAA;;QAjB7CrB,OAAA,EAAAC,QAAA,CAiBgD;UAAA,OAAES,MAAA,QAAAA,MAAA,OAjBlDY,gBAAA,CAiBgD,IAAE,E;;QAjBlDR,CAAA;UAkBQtB,YAAA,CAA4CyB,oBAAA;QAAhCE,OAAK,EAAEvB,MAAA,CAAA2B;MAAS;QAlBpCvB,OAAA,EAAAC,QAAA,CAkBsC;UAAA,OAAES,MAAA,QAAAA,MAAA,OAlBxCY,gBAAA,CAkBsC,IAAE,E;;QAlBxCR,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}