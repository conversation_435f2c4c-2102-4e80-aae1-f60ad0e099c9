{"ast": null, "code": "import { openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"xyl-video-item\",\n  controlslist: \"nodownload\",\n  ref: \"videoRef\",\n  controls: \"\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"video\", _hoisted_1, null, 512 /* NEED_PATCH */);\n}", "map": {"version": 3, "names": ["class", "controlslist", "ref", "controls", "_createElementBlock", "_hoisted_1"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\common\\components\\xyl-upload-video\\xyl-video-item.vue"], "sourcesContent": ["<!--\r\n * @FileDescription: 该文件的描述信息\r\n * @Author: 作者信息\r\n * @Date: 文件创建时间\r\n * @LastEditors: 最后更新作者\r\n * @LastEditTime: 最后更新时间\r\n -->\r\n<template>\r\n  <video class=\"xyl-video-item\" controlslist=\"nodownload\" ref=\"videoRef\" controls></video>\r\n</template>\r\n<script>\r\nexport default { name: 'XylVideoItem' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted, watch } from 'vue'\r\nimport flvjs from 'flv.js'\r\nconst props = defineProps({ id: { type: String, default: '' } })\r\nconst videoRef = ref()\r\nconst flvPlayer = ref()\r\n\r\nonMounted(() => {\r\n  if (props.id) {\r\n    createdPlay()\r\n  }\r\n})\r\n\r\n// 检测浏览器是否支持 flv.js\r\nconst createdPlay = () => {\r\n  if (flvjs.isSupported()) {\r\n    // 创建一个播放器实例\r\n    const Expression = /http(s)?:\\/\\/([\\w-]+\\.)+[\\w-]+(\\/[\\w- .\\/?%&=]*)?/\r\n    const pathExp = new RegExp(Expression)\r\n    flvPlayer.value = flvjs.createPlayer(\r\n      {\r\n        type: 'mp4', // 媒体类型，默认是 flv\r\n        isLive: true, // 是否是直播流\r\n        hasAudio: true, // 是否有音频\r\n        hanVideo: true, // 是否有视频\r\n        url: pathExp.test(props.id) ? props.id : api.filePreview(props.id)\r\n      },\r\n      { autoCleanupMinBackwardDuration: true }\r\n    )\r\n    flvPlayer.value.attachMediaElement(videoRef.value)\r\n    flvPlayer.value.load()\r\n  }\r\n}\r\nwatch(\r\n  () => props.id,\r\n  () => {\r\n    if (props.id) {\r\n      createdPlay()\r\n    }\r\n  }\r\n)\r\n</script>\r\n<style lang=\"scss\">\r\n.xyl-video-item {\r\n  width: 100%;\r\n  height: 100%;\r\n  display: block;\r\n}\r\n</style>\r\n"], "mappings": ";;EAQSA,KAAK,EAAC,gBAAgB;EAACC,YAAY,EAAC,YAAY;EAACC,GAAG,EAAC,UAAU;EAACC,QAAQ,EAAR;;;uBAAvEC,mBAAA,CAAwF,SAAxFC,UAAwF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}