{"ast": null, "code": "// markdown-it default options\n\n'use strict';\n\nmodule.exports = {\n  options: {\n    html: false,\n    // Enable HTML tags in source\n    xhtmlOut: false,\n    // Use '/' to close single tags (<br />)\n    breaks: false,\n    // Convert '\\n' in paragraphs into <br>\n    langPrefix: 'language-',\n    // CSS language prefix for fenced blocks\n    linkify: false,\n    // autoconvert URL-like texts to links\n\n    // Enable some language-neutral replacements + quotes beautification\n    typographer: false,\n    // Double + single quotes replacement pairs, when typographer enabled,\n    // and smartquotes on. Could be either a String or an Array.\n    //\n    // For example, you can use '«»„“' for Russian, '„“‚‘' for German,\n    // and ['«\\xA0', '\\xA0»', '‹\\xA0', '\\xA0›'] for French (including nbsp).\n    quotes: \"\\u201C\\u201D\\u2018\\u2019\",\n    /* “”‘’ */\n\n    // Highlighter function. Should return escaped HTML,\n    // or '' if the source string is not changed and should be escaped externaly.\n    // If result starts with <pre... internal wrapper is skipped.\n    //\n    // function (/*str, lang*/) { return ''; }\n    //\n    highlight: null,\n    maxNesting: 100 // Internal protection, recursion limit\n  },\n  components: {\n    core: {},\n    block: {},\n    inline: {}\n  }\n};", "map": {"version": 3, "names": ["module", "exports", "options", "html", "xhtmlOut", "breaks", "langPrefix", "linkify", "typographer", "quotes", "highlight", "maxNesting", "components", "core", "block", "inline"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/presets/default.js"], "sourcesContent": ["// markdown-it default options\n\n'use strict';\n\n\nmodule.exports = {\n  options: {\n    html:         false,        // Enable HTML tags in source\n    xhtmlOut:     false,        // Use '/' to close single tags (<br />)\n    breaks:       false,        // Convert '\\n' in paragraphs into <br>\n    langPrefix:   'language-',  // CSS language prefix for fenced blocks\n    linkify:      false,        // autoconvert URL-like texts to links\n\n    // Enable some language-neutral replacements + quotes beautification\n    typographer:  false,\n\n    // Double + single quotes replacement pairs, when typographer enabled,\n    // and smartquotes on. Could be either a String or an Array.\n    //\n    // For example, you can use '«»„“' for Russian, '„“‚‘' for German,\n    // and ['«\\xA0', '\\xA0»', '‹\\xA0', '\\xA0›'] for French (including nbsp).\n    quotes: '\\u201c\\u201d\\u2018\\u2019', /* “”‘’ */\n\n    // Highlighter function. Should return escaped HTML,\n    // or '' if the source string is not changed and should be escaped externaly.\n    // If result starts with <pre... internal wrapper is skipped.\n    //\n    // function (/*str, lang*/) { return ''; }\n    //\n    highlight: null,\n\n    maxNesting:   100            // Internal protection, recursion limit\n  },\n\n  components: {\n\n    core: {},\n    block: {},\n    inline: {}\n  }\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAGZA,MAAM,CAACC,OAAO,GAAG;EACfC,OAAO,EAAE;IACPC,IAAI,EAAU,KAAK;IAAS;IAC5BC,QAAQ,EAAM,KAAK;IAAS;IAC5BC,MAAM,EAAQ,KAAK;IAAS;IAC5BC,UAAU,EAAI,WAAW;IAAG;IAC5BC,OAAO,EAAO,KAAK;IAAS;;IAE5B;IACAC,WAAW,EAAG,KAAK;IAEnB;IACA;IACA;IACA;IACA;IACAC,MAAM,EAAE,0BAA0B;IAAE;;IAEpC;IACA;IACA;IACA;IACA;IACA;IACAC,SAAS,EAAE,IAAI;IAEfC,UAAU,EAAI,GAAG,CAAY;EAC/B,CAAC;EAEDC,UAAU,EAAE;IAEVC,IAAI,EAAE,CAAC,CAAC;IACRC,KAAK,EAAE,CAAC,CAAC;IACTC,MAAM,EAAE,CAAC;EACX;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}