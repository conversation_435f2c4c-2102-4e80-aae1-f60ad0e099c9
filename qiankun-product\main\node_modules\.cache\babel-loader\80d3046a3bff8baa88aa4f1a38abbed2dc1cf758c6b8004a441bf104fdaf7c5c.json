{"ast": null, "code": "\"use strict\";\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _toConsumableArray(r) {\n  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return _arrayLikeToArray(r);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nfunction _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nvar _require = require(\"../doc-utils.js\"),\n  chunkBy = _require.chunkBy,\n  last = _require.last,\n  isParagraphStart = _require.isParagraphStart,\n  isModule = _require.isModule,\n  isParagraphEnd = _require.isParagraphEnd,\n  isContent = _require.isContent,\n  startsWith = _require.startsWith,\n  isTagEnd = _require.isTagEnd,\n  isTagStart = _require.isTagStart,\n  getSingleAttribute = _require.getSingleAttribute,\n  setSingleAttribute = _require.setSingleAttribute;\nvar filetypes = require(\"../filetypes.js\");\nvar wrapper = require(\"../module-wrapper.js\");\nvar moduleName = \"loop\";\nfunction hasContent(parts) {\n  return parts.some(function (part) {\n    return isContent(part);\n  });\n}\nfunction getFirstMeaningFulPart(parsed) {\n  for (var i = 0, len = parsed.length; i < len; i++) {\n    if (parsed[i].type !== \"content\") {\n      return parsed[i];\n    }\n  }\n  return null;\n}\nfunction isInsideParagraphLoop(part) {\n  var firstMeaningfulPart = getFirstMeaningFulPart(part.subparsed);\n  return firstMeaningfulPart != null && firstMeaningfulPart.tag !== \"w:t\";\n}\nfunction getPageBreakIfApplies(part) {\n  return part.hasPageBreak && isInsideParagraphLoop(part) ? '<w:p><w:r><w:br w:type=\"page\"/></w:r></w:p>' : \"\";\n}\nfunction isEnclosedByParagraphs(parsed) {\n  return parsed.length && isParagraphStart(parsed[0]) && isParagraphEnd(last(parsed));\n}\nfunction getOffset(chunk) {\n  return hasContent(chunk) ? 0 : chunk.length;\n}\nfunction addPageBreakAtEnd(subRendered) {\n  var j = subRendered.parts.length - 1;\n  if (subRendered.parts[j] === \"</w:p>\") {\n    subRendered.parts.splice(j, 0, '<w:r><w:br w:type=\"page\"/></w:r>');\n  } else {\n    subRendered.parts.push('<w:p><w:r><w:br w:type=\"page\"/></w:r></w:p>');\n  }\n}\nfunction addPageBreakAtBeginning(subRendered) {\n  subRendered.parts.unshift('<w:p><w:r><w:br w:type=\"page\"/></w:r></w:p>');\n}\nfunction isContinuous(parts) {\n  return parts.some(function (part) {\n    return isTagStart(\"w:type\", part) && part.value.indexOf(\"continuous\") !== -1;\n  });\n}\nfunction isNextPage(parts) {\n  return parts.some(function (part) {\n    return isTagStart(\"w:type\", part) && part.value.indexOf('w:val=\"nextPage\"') !== -1;\n  });\n}\nfunction addSectionBefore(parts, sect) {\n  return [\"<w:p><w:pPr>\".concat(sect.map(function (_ref) {\n    var value = _ref.value;\n    return value;\n  }).join(\"\"), \"</w:pPr></w:p>\")].concat(parts);\n}\nfunction addContinuousType(parts) {\n  var stop = false;\n  var inSectPr = false;\n  return parts.reduce(function (result, part) {\n    if (stop === false && startsWith(part, \"<w:sectPr\")) {\n      inSectPr = true;\n    }\n    if (inSectPr) {\n      if (startsWith(part, \"<w:type\")) {\n        stop = true;\n      }\n      if (stop === false && startsWith(part, \"</w:sectPr\")) {\n        result.push('<w:type w:val=\"continuous\"/>');\n      }\n    }\n    result.push(part);\n    return result;\n  }, []);\n}\nfunction dropHeaderFooterRefs(parts) {\n  return parts.filter(function (text) {\n    return !startsWith(text, \"<w:headerReference\") && !startsWith(text, \"<w:footerReference\");\n  });\n}\nfunction hasPageBreak(chunk) {\n  return chunk.some(function (part) {\n    return part.tag === \"w:br\" && part.value.indexOf('w:type=\"page\"') !== -1;\n  });\n}\nfunction hasImage(chunk) {\n  return chunk.some(function (_ref2) {\n    var tag = _ref2.tag;\n    return tag === \"w:drawing\";\n  });\n}\nfunction getSectPr(chunks) {\n  var collectSectPr = false;\n  var sectPrs = [];\n  chunks.forEach(function (part) {\n    if (isTagStart(\"w:sectPr\", part)) {\n      sectPrs.push([]);\n      collectSectPr = true;\n    }\n    if (collectSectPr) {\n      sectPrs[sectPrs.length - 1].push(part);\n    }\n    if (isTagEnd(\"w:sectPr\", part)) {\n      collectSectPr = false;\n    }\n  });\n  return sectPrs;\n}\nfunction getSectPrHeaderFooterChangeCount(chunks) {\n  var collectSectPr = false;\n  var sectPrCount = 0;\n  chunks.forEach(function (part) {\n    if (isTagStart(\"w:sectPr\", part)) {\n      collectSectPr = true;\n    }\n    if (collectSectPr) {\n      if (part.tag === \"w:headerReference\" || part.tag === \"w:footerReference\") {\n        sectPrCount++;\n        collectSectPr = false;\n      }\n    }\n    if (isTagEnd(\"w:sectPr\", part)) {\n      collectSectPr = false;\n    }\n  });\n  return sectPrCount;\n}\nfunction getLastSectPr(parsed) {\n  var sectPr = [];\n  var inSectPr = false;\n  for (var i = parsed.length - 1; i >= 0; i--) {\n    var part = parsed[i];\n    if (isTagEnd(\"w:sectPr\", part)) {\n      inSectPr = true;\n    }\n    if (isTagStart(\"w:sectPr\", part)) {\n      sectPr.unshift(part.value);\n      inSectPr = false;\n    }\n    if (inSectPr) {\n      sectPr.unshift(part.value);\n    }\n    if (isParagraphStart(part)) {\n      if (sectPr.length > 0) {\n        return sectPr.join(\"\");\n      }\n      break;\n    }\n  }\n  return \"\";\n}\nvar LoopModule = /*#__PURE__*/function () {\n  function LoopModule() {\n    _classCallCheck(this, LoopModule);\n    this.name = \"LoopModule\";\n    this.inXfrm = false;\n    this.totalSectPr = 0;\n    this.prefix = {\n      start: \"#\",\n      end: \"/\",\n      dash: /^-([^\\s]+)\\s(.+)/,\n      inverted: \"^\"\n    };\n  }\n  return _createClass(LoopModule, [{\n    key: \"optionsTransformer\",\n    value: function optionsTransformer(opts, docxtemplater) {\n      this.docxtemplater = docxtemplater;\n      return opts;\n    }\n  }, {\n    key: \"preparse\",\n    value: function preparse(parsed, _ref3) {\n      var contentType = _ref3.contentType;\n      if (filetypes.main.indexOf(contentType) !== -1) {\n        this.sects = getSectPr(parsed);\n      }\n    }\n  }, {\n    key: \"matchers\",\n    value: function matchers() {\n      var module = moduleName;\n      return [[this.prefix.start, module, {\n        expandTo: \"auto\",\n        location: \"start\",\n        inverted: false\n      }], [this.prefix.inverted, module, {\n        expandTo: \"auto\",\n        location: \"start\",\n        inverted: true\n      }], [this.prefix.end, module, {\n        location: \"end\"\n      }], [this.prefix.dash, module, function (_ref4) {\n        var _ref5 = _slicedToArray(_ref4, 3),\n          expandTo = _ref5[1],\n          value = _ref5[2];\n        return {\n          location: \"start\",\n          inverted: false,\n          expandTo: expandTo,\n          value: value\n        };\n      }]];\n    }\n  }, {\n    key: \"getTraits\",\n    value: function getTraits(traitName, parsed) {\n      // Stryker disable all : because getTraits should disappear in v4\n      if (traitName !== \"expandPair\") {\n        return;\n      }\n      // Stryker restore all\n\n      return parsed.reduce(function (tags, part, offset) {\n        if (isModule(part, moduleName) && part.subparsed == null) {\n          tags.push({\n            part: part,\n            offset: offset\n          });\n        }\n        return tags;\n      }, []);\n    }\n  }, {\n    key: \"postparse\",\n    value: function postparse(parsed, _ref6) {\n      var basePart = _ref6.basePart;\n      if (basePart && this.docxtemplater.fileType === \"docx\" && parsed.length > 0) {\n        basePart.sectPrCount = getSectPrHeaderFooterChangeCount(parsed);\n        this.totalSectPr += basePart.sectPrCount;\n        var sects = this.sects;\n        sects.some(function (sect, index) {\n          if (basePart.lIndex < sect[0].lIndex) {\n            if (index + 1 < sects.length && isContinuous(sects[index + 1])) {\n              basePart.addContinuousType = true;\n            }\n            return true;\n          }\n          if (parsed[0].lIndex < sect[0].lIndex && sect[0].lIndex < basePart.lIndex) {\n            if (isNextPage(sects[index])) {\n              basePart.addNextPage = {\n                index: index\n              };\n            }\n            return true;\n          }\n        });\n        basePart.lastParagrapSectPr = getLastSectPr(parsed);\n      }\n      if (!basePart || basePart.expandTo !== \"auto\" || basePart.module !== moduleName || !isEnclosedByParagraphs(parsed)) {\n        return parsed;\n      }\n      basePart.paragraphLoop = true;\n      var level = 0;\n      var chunks = chunkBy(parsed, function (p) {\n        if (isParagraphStart(p)) {\n          level++;\n          if (level === 1) {\n            return \"start\";\n          }\n        }\n        if (isParagraphEnd(p)) {\n          level--;\n          if (level === 0) {\n            return \"end\";\n          }\n        }\n        return null;\n      });\n      var firstChunk = chunks[0];\n      var lastChunk = last(chunks);\n      var firstOffset = getOffset(firstChunk);\n      var lastOffset = getOffset(lastChunk);\n      basePart.hasPageBreakBeginning = hasPageBreak(firstChunk);\n      basePart.hasPageBreak = hasPageBreak(lastChunk);\n      if (hasImage(firstChunk)) {\n        firstOffset = 0;\n      }\n      if (hasImage(lastChunk)) {\n        lastOffset = 0;\n      }\n      return parsed.slice(firstOffset, parsed.length - lastOffset);\n    }\n  }, {\n    key: \"resolve\",\n    value: function resolve(part, options) {\n      if (!isModule(part, moduleName)) {\n        return null;\n      }\n      var sm = options.scopeManager;\n      var promisedValue = sm.getValueAsync(part.value, {\n        part: part\n      });\n      var promises = [];\n      function loopOver(scope, i, length) {\n        var scopeManager = sm.createSubScopeManager(scope, part.value, i, part, length);\n        promises.push(options.resolve(_objectSpread(_objectSpread({}, options), {}, {\n          compiled: part.subparsed,\n          tags: {},\n          scopeManager: scopeManager\n        })));\n      }\n      var errorList = [];\n      return promisedValue.then(function (values) {\n        return new Promise(function (resolve) {\n          if (values instanceof Array) {\n            Promise.all(values).then(resolve);\n          } else {\n            resolve(values);\n          }\n        }).then(function (values) {\n          sm.loopOverValue(values, loopOver, part.inverted);\n          return Promise.all(promises).then(function (r) {\n            return r.map(function (_ref7) {\n              var resolved = _ref7.resolved,\n                errors = _ref7.errors;\n              errorList.push.apply(errorList, _toConsumableArray(errors));\n              return resolved;\n            });\n          }).then(function (value) {\n            if (errorList.length > 0) {\n              throw errorList;\n            }\n            return value;\n          });\n        });\n      });\n    }\n    // eslint-disable-next-line complexity\n  }, {\n    key: \"render\",\n    value: function render(part, options) {\n      if (part.tag === \"p:xfrm\") {\n        this.inXfrm = part.position === \"start\";\n      }\n      if (part.tag === \"a:ext\" && this.inXfrm) {\n        this.lastExt = part;\n        return part;\n      }\n      if (!isModule(part, moduleName)) {\n        return null;\n      }\n      var totalValue = [];\n      var errors = [];\n      var heightOffset = 0;\n      var self = this;\n      var firstTag = part.subparsed[0];\n      var tagHeight = 0;\n      if ((firstTag === null || firstTag === void 0 ? void 0 : firstTag.tag) === \"a:tr\") {\n        tagHeight = +getSingleAttribute(firstTag.value, \"h\");\n      }\n      heightOffset -= tagHeight;\n      var a16RowIdOffset = 0;\n      var insideParagraphLoop = isInsideParagraphLoop(part);\n\n      // eslint-disable-next-line complexity\n      function loopOver(scope, i, length) {\n        heightOffset += tagHeight;\n        var scopeManager = options.scopeManager.createSubScopeManager(scope, part.value, i, part, length);\n        part.subparsed.forEach(function (pp) {\n          if (isTagStart(\"a16:rowId\", pp)) {\n            var val = +getSingleAttribute(pp.value, \"val\") + a16RowIdOffset;\n            a16RowIdOffset = 1;\n            pp.value = setSingleAttribute(pp.value, \"val\", val);\n          }\n        });\n        var subRendered = options.render(_objectSpread(_objectSpread({}, options), {}, {\n          compiled: part.subparsed,\n          tags: {},\n          scopeManager: scopeManager\n        }));\n        if (part.hasPageBreak && i === length - 1 && insideParagraphLoop) {\n          addPageBreakAtEnd(subRendered);\n        }\n        var isNotFirst = scopeManager.scopePathItem.some(function (i) {\n          return i !== 0;\n        });\n        if (isNotFirst) {\n          if (part.sectPrCount === 1) {\n            subRendered.parts = dropHeaderFooterRefs(subRendered.parts);\n          }\n          if (part.addContinuousType) {\n            subRendered.parts = addContinuousType(subRendered.parts);\n          }\n        } else if (part.addNextPage) {\n          subRendered.parts = addSectionBefore(subRendered.parts, self.sects[part.addNextPage.index]);\n        }\n        if (part.addNextPage) {\n          addPageBreakAtEnd(subRendered);\n        }\n        if (part.hasPageBreakBeginning && insideParagraphLoop) {\n          addPageBreakAtBeginning(subRendered);\n        }\n        for (var _i = 0, len = subRendered.parts.length; _i < len; _i++) {\n          totalValue.push(subRendered.parts[_i]);\n        }\n        Array.prototype.push.apply(errors, subRendered.errors);\n      }\n      var result = options.scopeManager.loopOver(part.value, loopOver, part.inverted, {\n        part: part\n      });\n      // if the loop is showing empty content\n      if (result === false) {\n        if (part.lastParagrapSectPr) {\n          if (part.paragraphLoop) {\n            return {\n              value: \"<w:p><w:pPr>\".concat(part.lastParagrapSectPr, \"</w:pPr></w:p>\")\n            };\n          }\n          return {\n            value: \"</w:t></w:r></w:p><w:p><w:pPr>\".concat(part.lastParagrapSectPr, \"</w:pPr><w:r><w:t>\")\n          };\n        }\n        return {\n          value: getPageBreakIfApplies(part) || \"\",\n          errors: errors\n        };\n      }\n      if (heightOffset !== 0) {\n        var cy = +getSingleAttribute(this.lastExt.value, \"cy\");\n        this.lastExt.value = setSingleAttribute(this.lastExt.value, \"cy\", cy + heightOffset);\n      }\n      return {\n        value: options.joinUncorrupt(totalValue, _objectSpread(_objectSpread({}, options), {}, {\n          basePart: part\n        })),\n        errors: errors\n      };\n    }\n  }]);\n}();\nmodule.exports = function () {\n  return wrapper(new LoopModule());\n};", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_toConsumableArray", "r", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "TypeError", "Array", "from", "isArray", "_arrayLikeToArray", "ownKeys", "e", "t", "Object", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "_slicedToArray", "_arrayWithHoles", "_iterableToArrayLimit", "_nonIterableRest", "a", "toString", "call", "slice", "name", "test", "n", "l", "i", "u", "f", "next", "done", "_classCallCheck", "_defineProperties", "key", "_createClass", "_toPrimitive", "toPrimitive", "String", "Number", "_require", "require", "chunkBy", "last", "isParagraphStart", "isModule", "isParagraphEnd", "<PERSON><PERSON><PERSON><PERSON>", "startsWith", "isTagEnd", "isTagStart", "getSingleAttribute", "setSingleAttribute", "filetypes", "wrapper", "moduleName", "<PERSON><PERSON><PERSON><PERSON>", "parts", "some", "part", "getFirstMeaningFulPart", "parsed", "len", "type", "isInsideParagraphLoop", "firstMeaningfulPart", "subparsed", "tag", "getPageBreakIfApplies", "hasPageBreak", "isEnclosedByParagraphs", "getOffset", "chunk", "addPageBreakAtEnd", "subRendered", "j", "splice", "addPageBreakAtBeginning", "unshift", "isContinuous", "indexOf", "isNextPage", "addSectionBefore", "sect", "concat", "map", "_ref", "join", "addContinuousType", "stop", "inSectPr", "reduce", "result", "dropHeaderFooterRefs", "text", "hasImage", "_ref2", "getSectPr", "chunks", "collectSectPr", "sectPrs", "getSectPrHeaderFooterChangeCount", "sectPrCount", "getLastSectPr", "sectPr", "LoopModule", "inXfrm", "totalSectPr", "prefix", "start", "end", "dash", "inverted", "optionsTransformer", "opts", "docxtemplater", "preparse", "_ref3", "contentType", "main", "sects", "matchers", "module", "expandTo", "location", "_ref4", "_ref5", "getTraits", "traitName", "tags", "offset", "postparse", "_ref6", "basePart", "fileType", "index", "lIndex", "addNextPage", "lastParagrapSectPr", "paragraphLoop", "level", "p", "firstChunk", "lastChunk", "firstOffset", "lastOffset", "hasPageBreakBeginning", "resolve", "options", "sm", "scopeManager", "promisedValue", "getValueAsync", "promises", "loopOver", "scope", "createSubScopeManager", "compiled", "errorList", "then", "values", "Promise", "all", "loopOverValue", "_ref7", "resolved", "errors", "render", "position", "lastExt", "totalValue", "heightOffset", "self", "firstTag", "tagHeight", "a16RowIdOffset", "insideParagraphLoop", "pp", "val", "isNotFirst", "scopePathItem", "_i", "cy", "joinUncorrupt", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/docxtemplater@3.52.0/node_modules/docxtemplater/js/modules/loop.js"], "sourcesContent": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nvar _require = require(\"../doc-utils.js\"),\n  chunkBy = _require.chunkBy,\n  last = _require.last,\n  isParagraphStart = _require.isParagraphStart,\n  isModule = _require.isModule,\n  isParagraphEnd = _require.isParagraphEnd,\n  isContent = _require.isContent,\n  startsWith = _require.startsWith,\n  isTagEnd = _require.isTagEnd,\n  isTagStart = _require.isTagStart,\n  getSingleAttribute = _require.getSingleAttribute,\n  setSingleAttribute = _require.setSingleAttribute;\nvar filetypes = require(\"../filetypes.js\");\nvar wrapper = require(\"../module-wrapper.js\");\nvar moduleName = \"loop\";\nfunction hasContent(parts) {\n  return parts.some(function (part) {\n    return isContent(part);\n  });\n}\nfunction getFirstMeaningFulPart(parsed) {\n  for (var i = 0, len = parsed.length; i < len; i++) {\n    if (parsed[i].type !== \"content\") {\n      return parsed[i];\n    }\n  }\n  return null;\n}\nfunction isInsideParagraphLoop(part) {\n  var firstMeaningfulPart = getFirstMeaningFulPart(part.subparsed);\n  return firstMeaningfulPart != null && firstMeaningfulPart.tag !== \"w:t\";\n}\nfunction getPageBreakIfApplies(part) {\n  return part.hasPageBreak && isInsideParagraphLoop(part) ? '<w:p><w:r><w:br w:type=\"page\"/></w:r></w:p>' : \"\";\n}\nfunction isEnclosedByParagraphs(parsed) {\n  return parsed.length && isParagraphStart(parsed[0]) && isParagraphEnd(last(parsed));\n}\nfunction getOffset(chunk) {\n  return hasContent(chunk) ? 0 : chunk.length;\n}\nfunction addPageBreakAtEnd(subRendered) {\n  var j = subRendered.parts.length - 1;\n  if (subRendered.parts[j] === \"</w:p>\") {\n    subRendered.parts.splice(j, 0, '<w:r><w:br w:type=\"page\"/></w:r>');\n  } else {\n    subRendered.parts.push('<w:p><w:r><w:br w:type=\"page\"/></w:r></w:p>');\n  }\n}\nfunction addPageBreakAtBeginning(subRendered) {\n  subRendered.parts.unshift('<w:p><w:r><w:br w:type=\"page\"/></w:r></w:p>');\n}\nfunction isContinuous(parts) {\n  return parts.some(function (part) {\n    return isTagStart(\"w:type\", part) && part.value.indexOf(\"continuous\") !== -1;\n  });\n}\nfunction isNextPage(parts) {\n  return parts.some(function (part) {\n    return isTagStart(\"w:type\", part) && part.value.indexOf('w:val=\"nextPage\"') !== -1;\n  });\n}\nfunction addSectionBefore(parts, sect) {\n  return [\"<w:p><w:pPr>\".concat(sect.map(function (_ref) {\n    var value = _ref.value;\n    return value;\n  }).join(\"\"), \"</w:pPr></w:p>\")].concat(parts);\n}\nfunction addContinuousType(parts) {\n  var stop = false;\n  var inSectPr = false;\n  return parts.reduce(function (result, part) {\n    if (stop === false && startsWith(part, \"<w:sectPr\")) {\n      inSectPr = true;\n    }\n    if (inSectPr) {\n      if (startsWith(part, \"<w:type\")) {\n        stop = true;\n      }\n      if (stop === false && startsWith(part, \"</w:sectPr\")) {\n        result.push('<w:type w:val=\"continuous\"/>');\n      }\n    }\n    result.push(part);\n    return result;\n  }, []);\n}\nfunction dropHeaderFooterRefs(parts) {\n  return parts.filter(function (text) {\n    return !startsWith(text, \"<w:headerReference\") && !startsWith(text, \"<w:footerReference\");\n  });\n}\nfunction hasPageBreak(chunk) {\n  return chunk.some(function (part) {\n    return part.tag === \"w:br\" && part.value.indexOf('w:type=\"page\"') !== -1;\n  });\n}\nfunction hasImage(chunk) {\n  return chunk.some(function (_ref2) {\n    var tag = _ref2.tag;\n    return tag === \"w:drawing\";\n  });\n}\nfunction getSectPr(chunks) {\n  var collectSectPr = false;\n  var sectPrs = [];\n  chunks.forEach(function (part) {\n    if (isTagStart(\"w:sectPr\", part)) {\n      sectPrs.push([]);\n      collectSectPr = true;\n    }\n    if (collectSectPr) {\n      sectPrs[sectPrs.length - 1].push(part);\n    }\n    if (isTagEnd(\"w:sectPr\", part)) {\n      collectSectPr = false;\n    }\n  });\n  return sectPrs;\n}\nfunction getSectPrHeaderFooterChangeCount(chunks) {\n  var collectSectPr = false;\n  var sectPrCount = 0;\n  chunks.forEach(function (part) {\n    if (isTagStart(\"w:sectPr\", part)) {\n      collectSectPr = true;\n    }\n    if (collectSectPr) {\n      if (part.tag === \"w:headerReference\" || part.tag === \"w:footerReference\") {\n        sectPrCount++;\n        collectSectPr = false;\n      }\n    }\n    if (isTagEnd(\"w:sectPr\", part)) {\n      collectSectPr = false;\n    }\n  });\n  return sectPrCount;\n}\nfunction getLastSectPr(parsed) {\n  var sectPr = [];\n  var inSectPr = false;\n  for (var i = parsed.length - 1; i >= 0; i--) {\n    var part = parsed[i];\n    if (isTagEnd(\"w:sectPr\", part)) {\n      inSectPr = true;\n    }\n    if (isTagStart(\"w:sectPr\", part)) {\n      sectPr.unshift(part.value);\n      inSectPr = false;\n    }\n    if (inSectPr) {\n      sectPr.unshift(part.value);\n    }\n    if (isParagraphStart(part)) {\n      if (sectPr.length > 0) {\n        return sectPr.join(\"\");\n      }\n      break;\n    }\n  }\n  return \"\";\n}\nvar LoopModule = /*#__PURE__*/function () {\n  function LoopModule() {\n    _classCallCheck(this, LoopModule);\n    this.name = \"LoopModule\";\n    this.inXfrm = false;\n    this.totalSectPr = 0;\n    this.prefix = {\n      start: \"#\",\n      end: \"/\",\n      dash: /^-([^\\s]+)\\s(.+)/,\n      inverted: \"^\"\n    };\n  }\n  return _createClass(LoopModule, [{\n    key: \"optionsTransformer\",\n    value: function optionsTransformer(opts, docxtemplater) {\n      this.docxtemplater = docxtemplater;\n      return opts;\n    }\n  }, {\n    key: \"preparse\",\n    value: function preparse(parsed, _ref3) {\n      var contentType = _ref3.contentType;\n      if (filetypes.main.indexOf(contentType) !== -1) {\n        this.sects = getSectPr(parsed);\n      }\n    }\n  }, {\n    key: \"matchers\",\n    value: function matchers() {\n      var module = moduleName;\n      return [[this.prefix.start, module, {\n        expandTo: \"auto\",\n        location: \"start\",\n        inverted: false\n      }], [this.prefix.inverted, module, {\n        expandTo: \"auto\",\n        location: \"start\",\n        inverted: true\n      }], [this.prefix.end, module, {\n        location: \"end\"\n      }], [this.prefix.dash, module, function (_ref4) {\n        var _ref5 = _slicedToArray(_ref4, 3),\n          expandTo = _ref5[1],\n          value = _ref5[2];\n        return {\n          location: \"start\",\n          inverted: false,\n          expandTo: expandTo,\n          value: value\n        };\n      }]];\n    }\n  }, {\n    key: \"getTraits\",\n    value: function getTraits(traitName, parsed) {\n      // Stryker disable all : because getTraits should disappear in v4\n      if (traitName !== \"expandPair\") {\n        return;\n      }\n      // Stryker restore all\n\n      return parsed.reduce(function (tags, part, offset) {\n        if (isModule(part, moduleName) && part.subparsed == null) {\n          tags.push({\n            part: part,\n            offset: offset\n          });\n        }\n        return tags;\n      }, []);\n    }\n  }, {\n    key: \"postparse\",\n    value: function postparse(parsed, _ref6) {\n      var basePart = _ref6.basePart;\n      if (basePart && this.docxtemplater.fileType === \"docx\" && parsed.length > 0) {\n        basePart.sectPrCount = getSectPrHeaderFooterChangeCount(parsed);\n        this.totalSectPr += basePart.sectPrCount;\n        var sects = this.sects;\n        sects.some(function (sect, index) {\n          if (basePart.lIndex < sect[0].lIndex) {\n            if (index + 1 < sects.length && isContinuous(sects[index + 1])) {\n              basePart.addContinuousType = true;\n            }\n            return true;\n          }\n          if (parsed[0].lIndex < sect[0].lIndex && sect[0].lIndex < basePart.lIndex) {\n            if (isNextPage(sects[index])) {\n              basePart.addNextPage = {\n                index: index\n              };\n            }\n            return true;\n          }\n        });\n        basePart.lastParagrapSectPr = getLastSectPr(parsed);\n      }\n      if (!basePart || basePart.expandTo !== \"auto\" || basePart.module !== moduleName || !isEnclosedByParagraphs(parsed)) {\n        return parsed;\n      }\n      basePart.paragraphLoop = true;\n      var level = 0;\n      var chunks = chunkBy(parsed, function (p) {\n        if (isParagraphStart(p)) {\n          level++;\n          if (level === 1) {\n            return \"start\";\n          }\n        }\n        if (isParagraphEnd(p)) {\n          level--;\n          if (level === 0) {\n            return \"end\";\n          }\n        }\n        return null;\n      });\n      var firstChunk = chunks[0];\n      var lastChunk = last(chunks);\n      var firstOffset = getOffset(firstChunk);\n      var lastOffset = getOffset(lastChunk);\n      basePart.hasPageBreakBeginning = hasPageBreak(firstChunk);\n      basePart.hasPageBreak = hasPageBreak(lastChunk);\n      if (hasImage(firstChunk)) {\n        firstOffset = 0;\n      }\n      if (hasImage(lastChunk)) {\n        lastOffset = 0;\n      }\n      return parsed.slice(firstOffset, parsed.length - lastOffset);\n    }\n  }, {\n    key: \"resolve\",\n    value: function resolve(part, options) {\n      if (!isModule(part, moduleName)) {\n        return null;\n      }\n      var sm = options.scopeManager;\n      var promisedValue = sm.getValueAsync(part.value, {\n        part: part\n      });\n      var promises = [];\n      function loopOver(scope, i, length) {\n        var scopeManager = sm.createSubScopeManager(scope, part.value, i, part, length);\n        promises.push(options.resolve(_objectSpread(_objectSpread({}, options), {}, {\n          compiled: part.subparsed,\n          tags: {},\n          scopeManager: scopeManager\n        })));\n      }\n      var errorList = [];\n      return promisedValue.then(function (values) {\n        return new Promise(function (resolve) {\n          if (values instanceof Array) {\n            Promise.all(values).then(resolve);\n          } else {\n            resolve(values);\n          }\n        }).then(function (values) {\n          sm.loopOverValue(values, loopOver, part.inverted);\n          return Promise.all(promises).then(function (r) {\n            return r.map(function (_ref7) {\n              var resolved = _ref7.resolved,\n                errors = _ref7.errors;\n              errorList.push.apply(errorList, _toConsumableArray(errors));\n              return resolved;\n            });\n          }).then(function (value) {\n            if (errorList.length > 0) {\n              throw errorList;\n            }\n            return value;\n          });\n        });\n      });\n    }\n    // eslint-disable-next-line complexity\n  }, {\n    key: \"render\",\n    value: function render(part, options) {\n      if (part.tag === \"p:xfrm\") {\n        this.inXfrm = part.position === \"start\";\n      }\n      if (part.tag === \"a:ext\" && this.inXfrm) {\n        this.lastExt = part;\n        return part;\n      }\n      if (!isModule(part, moduleName)) {\n        return null;\n      }\n      var totalValue = [];\n      var errors = [];\n      var heightOffset = 0;\n      var self = this;\n      var firstTag = part.subparsed[0];\n      var tagHeight = 0;\n      if ((firstTag === null || firstTag === void 0 ? void 0 : firstTag.tag) === \"a:tr\") {\n        tagHeight = +getSingleAttribute(firstTag.value, \"h\");\n      }\n      heightOffset -= tagHeight;\n      var a16RowIdOffset = 0;\n      var insideParagraphLoop = isInsideParagraphLoop(part);\n\n      // eslint-disable-next-line complexity\n      function loopOver(scope, i, length) {\n        heightOffset += tagHeight;\n        var scopeManager = options.scopeManager.createSubScopeManager(scope, part.value, i, part, length);\n        part.subparsed.forEach(function (pp) {\n          if (isTagStart(\"a16:rowId\", pp)) {\n            var val = +getSingleAttribute(pp.value, \"val\") + a16RowIdOffset;\n            a16RowIdOffset = 1;\n            pp.value = setSingleAttribute(pp.value, \"val\", val);\n          }\n        });\n        var subRendered = options.render(_objectSpread(_objectSpread({}, options), {}, {\n          compiled: part.subparsed,\n          tags: {},\n          scopeManager: scopeManager\n        }));\n        if (part.hasPageBreak && i === length - 1 && insideParagraphLoop) {\n          addPageBreakAtEnd(subRendered);\n        }\n        var isNotFirst = scopeManager.scopePathItem.some(function (i) {\n          return i !== 0;\n        });\n        if (isNotFirst) {\n          if (part.sectPrCount === 1) {\n            subRendered.parts = dropHeaderFooterRefs(subRendered.parts);\n          }\n          if (part.addContinuousType) {\n            subRendered.parts = addContinuousType(subRendered.parts);\n          }\n        } else if (part.addNextPage) {\n          subRendered.parts = addSectionBefore(subRendered.parts, self.sects[part.addNextPage.index]);\n        }\n        if (part.addNextPage) {\n          addPageBreakAtEnd(subRendered);\n        }\n        if (part.hasPageBreakBeginning && insideParagraphLoop) {\n          addPageBreakAtBeginning(subRendered);\n        }\n        for (var _i = 0, len = subRendered.parts.length; _i < len; _i++) {\n          totalValue.push(subRendered.parts[_i]);\n        }\n        Array.prototype.push.apply(errors, subRendered.errors);\n      }\n      var result = options.scopeManager.loopOver(part.value, loopOver, part.inverted, {\n        part: part\n      });\n      // if the loop is showing empty content\n      if (result === false) {\n        if (part.lastParagrapSectPr) {\n          if (part.paragraphLoop) {\n            return {\n              value: \"<w:p><w:pPr>\".concat(part.lastParagrapSectPr, \"</w:pPr></w:p>\")\n            };\n          }\n          return {\n            value: \"</w:t></w:r></w:p><w:p><w:pPr>\".concat(part.lastParagrapSectPr, \"</w:pPr><w:r><w:t>\")\n          };\n        }\n        return {\n          value: getPageBreakIfApplies(part) || \"\",\n          errors: errors\n        };\n      }\n      if (heightOffset !== 0) {\n        var cy = +getSingleAttribute(this.lastExt.value, \"cy\");\n        this.lastExt.value = setSingleAttribute(this.lastExt.value, \"cy\", cy + heightOffset);\n      }\n      return {\n        value: options.joinUncorrupt(totalValue, _objectSpread(_objectSpread({}, options), {}, {\n          basePart: part\n        })),\n        errors: errors\n      };\n    }\n  }]);\n}();\nmodule.exports = function () {\n  return wrapper(new LoopModule());\n};"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,kBAAkBA,CAACC,CAAC,EAAE;EAAE,OAAOC,kBAAkB,CAACD,CAAC,CAAC,IAAIE,gBAAgB,CAACF,CAAC,CAAC,IAAIG,2BAA2B,CAACH,CAAC,CAAC,IAAII,kBAAkB,CAAC,CAAC;AAAE;AAChJ,SAASA,kBAAkBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;AAAE;AAC7L,SAASH,gBAAgBA,CAACF,CAAC,EAAE;EAAE,IAAI,WAAW,IAAI,OAAOL,MAAM,IAAI,IAAI,IAAIK,CAAC,CAACL,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAII,CAAC,CAAC,YAAY,CAAC,EAAE,OAAOM,KAAK,CAACC,IAAI,CAACP,CAAC,CAAC;AAAE;AAChJ,SAASC,kBAAkBA,CAACD,CAAC,EAAE;EAAE,IAAIM,KAAK,CAACE,OAAO,CAACR,CAAC,CAAC,EAAE,OAAOS,iBAAiB,CAACT,CAAC,CAAC;AAAE;AACpF,SAASU,OAAOA,CAACC,CAAC,EAAEX,CAAC,EAAE;EAAE,IAAIY,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACH,CAAC,CAAC;EAAE,IAAIE,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIrB,CAAC,GAAGmB,MAAM,CAACE,qBAAqB,CAACJ,CAAC,CAAC;IAAEX,CAAC,KAAKN,CAAC,GAAGA,CAAC,CAACsB,MAAM,CAAC,UAAUhB,CAAC,EAAE;MAAE,OAAOa,MAAM,CAACI,wBAAwB,CAACN,CAAC,EAAEX,CAAC,CAAC,CAACkB,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,CAAC,CAACO,IAAI,CAACC,KAAK,CAACR,CAAC,EAAElB,CAAC,CAAC;EAAE;EAAE,OAAOkB,CAAC;AAAE;AAC9P,SAASS,aAAaA,CAACV,CAAC,EAAE;EAAE,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,SAAS,CAACC,MAAM,EAAEvB,CAAC,EAAE,EAAE;IAAE,IAAIY,CAAC,GAAG,IAAI,IAAIU,SAAS,CAACtB,CAAC,CAAC,GAAGsB,SAAS,CAACtB,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGU,OAAO,CAACG,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,UAAUxB,CAAC,EAAE;MAAEyB,eAAe,CAACd,CAAC,EAAEX,CAAC,EAAEY,CAAC,CAACZ,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGa,MAAM,CAACa,yBAAyB,GAAGb,MAAM,CAACc,gBAAgB,CAAChB,CAAC,EAAEE,MAAM,CAACa,yBAAyB,CAACd,CAAC,CAAC,CAAC,GAAGF,OAAO,CAACG,MAAM,CAACD,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,UAAUxB,CAAC,EAAE;MAAEa,MAAM,CAACe,cAAc,CAACjB,CAAC,EAAEX,CAAC,EAAEa,MAAM,CAACI,wBAAwB,CAACL,CAAC,EAAEZ,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOW,CAAC;AAAE;AACtb,SAASc,eAAeA,CAACd,CAAC,EAAEX,CAAC,EAAEY,CAAC,EAAE;EAAE,OAAO,CAACZ,CAAC,GAAG6B,cAAc,CAAC7B,CAAC,CAAC,KAAKW,CAAC,GAAGE,MAAM,CAACe,cAAc,CAACjB,CAAC,EAAEX,CAAC,EAAE;IAAE8B,KAAK,EAAElB,CAAC;IAAEM,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGrB,CAAC,CAACX,CAAC,CAAC,GAAGY,CAAC,EAAED,CAAC;AAAE;AACnL,SAASsB,cAAcA,CAACjC,CAAC,EAAEW,CAAC,EAAE;EAAE,OAAOuB,eAAe,CAAClC,CAAC,CAAC,IAAImC,qBAAqB,CAACnC,CAAC,EAAEW,CAAC,CAAC,IAAIR,2BAA2B,CAACH,CAAC,EAAEW,CAAC,CAAC,IAAIyB,gBAAgB,CAAC,CAAC;AAAE;AACrJ,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAI/B,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAChM,SAASF,2BAA2BA,CAACH,CAAC,EAAEqC,CAAC,EAAE;EAAE,IAAIrC,CAAC,EAAE;IAAE,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE,OAAOS,iBAAiB,CAACT,CAAC,EAAEqC,CAAC,CAAC;IAAE,IAAIzB,CAAC,GAAG,CAAC,CAAC,CAAC0B,QAAQ,CAACC,IAAI,CAACvC,CAAC,CAAC,CAACwC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAAE,OAAO,QAAQ,KAAK5B,CAAC,IAAIZ,CAAC,CAACH,WAAW,KAAKe,CAAC,GAAGZ,CAAC,CAACH,WAAW,CAAC4C,IAAI,CAAC,EAAE,KAAK,KAAK7B,CAAC,IAAI,KAAK,KAAKA,CAAC,GAAGN,KAAK,CAACC,IAAI,CAACP,CAAC,CAAC,GAAG,WAAW,KAAKY,CAAC,IAAI,0CAA0C,CAAC8B,IAAI,CAAC9B,CAAC,CAAC,GAAGH,iBAAiB,CAACT,CAAC,EAAEqC,CAAC,CAAC,GAAG,KAAK,CAAC;EAAE;AAAE;AACzX,SAAS5B,iBAAiBA,CAACT,CAAC,EAAEqC,CAAC,EAAE;EAAE,CAAC,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAGrC,CAAC,CAACuB,MAAM,MAAMc,CAAC,GAAGrC,CAAC,CAACuB,MAAM,CAAC;EAAE,KAAK,IAAIZ,CAAC,GAAG,CAAC,EAAEgC,CAAC,GAAGrC,KAAK,CAAC+B,CAAC,CAAC,EAAE1B,CAAC,GAAG0B,CAAC,EAAE1B,CAAC,EAAE,EAAEgC,CAAC,CAAChC,CAAC,CAAC,GAAGX,CAAC,CAACW,CAAC,CAAC;EAAE,OAAOgC,CAAC;AAAE;AACnJ,SAASR,qBAAqBA,CAACnC,CAAC,EAAE4C,CAAC,EAAE;EAAE,IAAIhC,CAAC,GAAG,IAAI,IAAIZ,CAAC,GAAG,IAAI,GAAG,WAAW,IAAI,OAAOL,MAAM,IAAIK,CAAC,CAACL,MAAM,CAACC,QAAQ,CAAC,IAAII,CAAC,CAAC,YAAY,CAAC;EAAE,IAAI,IAAI,IAAIY,CAAC,EAAE;IAAE,IAAID,CAAC;MAAEgC,CAAC;MAAEE,CAAC;MAAEC,CAAC;MAAET,CAAC,GAAG,EAAE;MAAEU,CAAC,GAAG,CAAC,CAAC;MAAErD,CAAC,GAAG,CAAC,CAAC;IAAE,IAAI;MAAE,IAAImD,CAAC,GAAG,CAACjC,CAAC,GAAGA,CAAC,CAAC2B,IAAI,CAACvC,CAAC,CAAC,EAAEgD,IAAI,EAAE,CAAC,KAAKJ,CAAC,EAAE;QAAE,IAAI/B,MAAM,CAACD,CAAC,CAAC,KAAKA,CAAC,EAAE;QAAQmC,CAAC,GAAG,CAAC,CAAC;MAAE,CAAC,MAAM,OAAO,EAAEA,CAAC,GAAG,CAACpC,CAAC,GAAGkC,CAAC,CAACN,IAAI,CAAC3B,CAAC,CAAC,EAAEqC,IAAI,CAAC,KAAKZ,CAAC,CAAClB,IAAI,CAACR,CAAC,CAACmB,KAAK,CAAC,EAAEO,CAAC,CAACd,MAAM,KAAKqB,CAAC,CAAC,EAAEG,CAAC,GAAG,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,OAAO/C,CAAC,EAAE;MAAEN,CAAC,GAAG,CAAC,CAAC,EAAEiD,CAAC,GAAG3C,CAAC;IAAE,CAAC,SAAS;MAAE,IAAI;QAAE,IAAI,CAAC+C,CAAC,IAAI,IAAI,IAAInC,CAAC,CAAC,QAAQ,CAAC,KAAKkC,CAAC,GAAGlC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEC,MAAM,CAACiC,CAAC,CAAC,KAAKA,CAAC,CAAC,EAAE;MAAQ,CAAC,SAAS;QAAE,IAAIpD,CAAC,EAAE,MAAMiD,CAAC;MAAE;IAAE;IAAE,OAAON,CAAC;EAAE;AAAE;AACzhB,SAASH,eAAeA,CAAClC,CAAC,EAAE;EAAE,IAAIM,KAAK,CAACE,OAAO,CAACR,CAAC,CAAC,EAAE,OAAOA,CAAC;AAAE;AAC9D,SAASkD,eAAeA,CAACb,CAAC,EAAEM,CAAC,EAAE;EAAE,IAAI,EAAEN,CAAC,YAAYM,CAAC,CAAC,EAAE,MAAM,IAAItC,SAAS,CAAC,mCAAmC,CAAC;AAAE;AAClH,SAAS8C,iBAAiBA,CAACxC,CAAC,EAAEX,CAAC,EAAE;EAAE,KAAK,IAAIY,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,CAAC,CAACuB,MAAM,EAAEX,CAAC,EAAE,EAAE;IAAE,IAAIlB,CAAC,GAAGM,CAAC,CAACY,CAAC,CAAC;IAAElB,CAAC,CAACwB,UAAU,GAAGxB,CAAC,CAACwB,UAAU,IAAI,CAAC,CAAC,EAAExB,CAAC,CAACqC,YAAY,GAAG,CAAC,CAAC,EAAE,OAAO,IAAIrC,CAAC,KAAKA,CAAC,CAACsC,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAEnB,MAAM,CAACe,cAAc,CAACjB,CAAC,EAAEkB,cAAc,CAACnC,CAAC,CAAC0D,GAAG,CAAC,EAAE1D,CAAC,CAAC;EAAE;AAAE;AACvO,SAAS2D,YAAYA,CAAC1C,CAAC,EAAEX,CAAC,EAAEY,CAAC,EAAE;EAAE,OAAOZ,CAAC,IAAImD,iBAAiB,CAACxC,CAAC,CAACb,SAAS,EAAEE,CAAC,CAAC,EAAEY,CAAC,IAAIuC,iBAAiB,CAACxC,CAAC,EAAEC,CAAC,CAAC,EAAEC,MAAM,CAACe,cAAc,CAACjB,CAAC,EAAE,WAAW,EAAE;IAAEqB,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,EAAErB,CAAC;AAAE;AAC1K,SAASkB,cAAcA,CAACjB,CAAC,EAAE;EAAE,IAAIiC,CAAC,GAAGS,YAAY,CAAC1C,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAInB,OAAO,CAACoD,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASS,YAAYA,CAAC1C,CAAC,EAAEZ,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIP,OAAO,CAACmB,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAID,CAAC,GAAGC,CAAC,CAACjB,MAAM,CAAC4D,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAK5C,CAAC,EAAE;IAAE,IAAIkC,CAAC,GAAGlC,CAAC,CAAC4B,IAAI,CAAC3B,CAAC,EAAEZ,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIP,OAAO,CAACoD,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAIxC,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKL,CAAC,GAAGwD,MAAM,GAAGC,MAAM,EAAE7C,CAAC,CAAC;AAAE;AAC3T,IAAI8C,QAAQ,GAAGC,OAAO,CAAC,iBAAiB,CAAC;EACvCC,OAAO,GAAGF,QAAQ,CAACE,OAAO;EAC1BC,IAAI,GAAGH,QAAQ,CAACG,IAAI;EACpBC,gBAAgB,GAAGJ,QAAQ,CAACI,gBAAgB;EAC5CC,QAAQ,GAAGL,QAAQ,CAACK,QAAQ;EAC5BC,cAAc,GAAGN,QAAQ,CAACM,cAAc;EACxCC,SAAS,GAAGP,QAAQ,CAACO,SAAS;EAC9BC,UAAU,GAAGR,QAAQ,CAACQ,UAAU;EAChCC,QAAQ,GAAGT,QAAQ,CAACS,QAAQ;EAC5BC,UAAU,GAAGV,QAAQ,CAACU,UAAU;EAChCC,kBAAkB,GAAGX,QAAQ,CAACW,kBAAkB;EAChDC,kBAAkB,GAAGZ,QAAQ,CAACY,kBAAkB;AAClD,IAAIC,SAAS,GAAGZ,OAAO,CAAC,iBAAiB,CAAC;AAC1C,IAAIa,OAAO,GAAGb,OAAO,CAAC,sBAAsB,CAAC;AAC7C,IAAIc,UAAU,GAAG,MAAM;AACvB,SAASC,UAAUA,CAACC,KAAK,EAAE;EACzB,OAAOA,KAAK,CAACC,IAAI,CAAC,UAAUC,IAAI,EAAE;IAChC,OAAOZ,SAAS,CAACY,IAAI,CAAC;EACxB,CAAC,CAAC;AACJ;AACA,SAASC,sBAAsBA,CAACC,MAAM,EAAE;EACtC,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEmC,GAAG,GAAGD,MAAM,CAACxD,MAAM,EAAEsB,CAAC,GAAGmC,GAAG,EAAEnC,CAAC,EAAE,EAAE;IACjD,IAAIkC,MAAM,CAAClC,CAAC,CAAC,CAACoC,IAAI,KAAK,SAAS,EAAE;MAChC,OAAOF,MAAM,CAAClC,CAAC,CAAC;IAClB;EACF;EACA,OAAO,IAAI;AACb;AACA,SAASqC,qBAAqBA,CAACL,IAAI,EAAE;EACnC,IAAIM,mBAAmB,GAAGL,sBAAsB,CAACD,IAAI,CAACO,SAAS,CAAC;EAChE,OAAOD,mBAAmB,IAAI,IAAI,IAAIA,mBAAmB,CAACE,GAAG,KAAK,KAAK;AACzE;AACA,SAASC,qBAAqBA,CAACT,IAAI,EAAE;EACnC,OAAOA,IAAI,CAACU,YAAY,IAAIL,qBAAqB,CAACL,IAAI,CAAC,GAAG,6CAA6C,GAAG,EAAE;AAC9G;AACA,SAASW,sBAAsBA,CAACT,MAAM,EAAE;EACtC,OAAOA,MAAM,CAACxD,MAAM,IAAIuC,gBAAgB,CAACiB,MAAM,CAAC,CAAC,CAAC,CAAC,IAAIf,cAAc,CAACH,IAAI,CAACkB,MAAM,CAAC,CAAC;AACrF;AACA,SAASU,SAASA,CAACC,KAAK,EAAE;EACxB,OAAOhB,UAAU,CAACgB,KAAK,CAAC,GAAG,CAAC,GAAGA,KAAK,CAACnE,MAAM;AAC7C;AACA,SAASoE,iBAAiBA,CAACC,WAAW,EAAE;EACtC,IAAIC,CAAC,GAAGD,WAAW,CAACjB,KAAK,CAACpD,MAAM,GAAG,CAAC;EACpC,IAAIqE,WAAW,CAACjB,KAAK,CAACkB,CAAC,CAAC,KAAK,QAAQ,EAAE;IACrCD,WAAW,CAACjB,KAAK,CAACmB,MAAM,CAACD,CAAC,EAAE,CAAC,EAAE,kCAAkC,CAAC;EACpE,CAAC,MAAM;IACLD,WAAW,CAACjB,KAAK,CAACxD,IAAI,CAAC,6CAA6C,CAAC;EACvE;AACF;AACA,SAAS4E,uBAAuBA,CAACH,WAAW,EAAE;EAC5CA,WAAW,CAACjB,KAAK,CAACqB,OAAO,CAAC,6CAA6C,CAAC;AAC1E;AACA,SAASC,YAAYA,CAACtB,KAAK,EAAE;EAC3B,OAAOA,KAAK,CAACC,IAAI,CAAC,UAAUC,IAAI,EAAE;IAChC,OAAOT,UAAU,CAAC,QAAQ,EAAES,IAAI,CAAC,IAAIA,IAAI,CAAC/C,KAAK,CAACoE,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;EAC9E,CAAC,CAAC;AACJ;AACA,SAASC,UAAUA,CAACxB,KAAK,EAAE;EACzB,OAAOA,KAAK,CAACC,IAAI,CAAC,UAAUC,IAAI,EAAE;IAChC,OAAOT,UAAU,CAAC,QAAQ,EAAES,IAAI,CAAC,IAAIA,IAAI,CAAC/C,KAAK,CAACoE,OAAO,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;EACpF,CAAC,CAAC;AACJ;AACA,SAASE,gBAAgBA,CAACzB,KAAK,EAAE0B,IAAI,EAAE;EACrC,OAAO,CAAC,cAAc,CAACC,MAAM,CAACD,IAAI,CAACE,GAAG,CAAC,UAAUC,IAAI,EAAE;IACrD,IAAI1E,KAAK,GAAG0E,IAAI,CAAC1E,KAAK;IACtB,OAAOA,KAAK;EACd,CAAC,CAAC,CAAC2E,IAAI,CAAC,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,CAACH,MAAM,CAAC3B,KAAK,CAAC;AAC/C;AACA,SAAS+B,iBAAiBA,CAAC/B,KAAK,EAAE;EAChC,IAAIgC,IAAI,GAAG,KAAK;EAChB,IAAIC,QAAQ,GAAG,KAAK;EACpB,OAAOjC,KAAK,CAACkC,MAAM,CAAC,UAAUC,MAAM,EAAEjC,IAAI,EAAE;IAC1C,IAAI8B,IAAI,KAAK,KAAK,IAAIzC,UAAU,CAACW,IAAI,EAAE,WAAW,CAAC,EAAE;MACnD+B,QAAQ,GAAG,IAAI;IACjB;IACA,IAAIA,QAAQ,EAAE;MACZ,IAAI1C,UAAU,CAACW,IAAI,EAAE,SAAS,CAAC,EAAE;QAC/B8B,IAAI,GAAG,IAAI;MACb;MACA,IAAIA,IAAI,KAAK,KAAK,IAAIzC,UAAU,CAACW,IAAI,EAAE,YAAY,CAAC,EAAE;QACpDiC,MAAM,CAAC3F,IAAI,CAAC,8BAA8B,CAAC;MAC7C;IACF;IACA2F,MAAM,CAAC3F,IAAI,CAAC0D,IAAI,CAAC;IACjB,OAAOiC,MAAM;EACf,CAAC,EAAE,EAAE,CAAC;AACR;AACA,SAASC,oBAAoBA,CAACpC,KAAK,EAAE;EACnC,OAAOA,KAAK,CAAC3D,MAAM,CAAC,UAAUgG,IAAI,EAAE;IAClC,OAAO,CAAC9C,UAAU,CAAC8C,IAAI,EAAE,oBAAoB,CAAC,IAAI,CAAC9C,UAAU,CAAC8C,IAAI,EAAE,oBAAoB,CAAC;EAC3F,CAAC,CAAC;AACJ;AACA,SAASzB,YAAYA,CAACG,KAAK,EAAE;EAC3B,OAAOA,KAAK,CAACd,IAAI,CAAC,UAAUC,IAAI,EAAE;IAChC,OAAOA,IAAI,CAACQ,GAAG,KAAK,MAAM,IAAIR,IAAI,CAAC/C,KAAK,CAACoE,OAAO,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC;EAC1E,CAAC,CAAC;AACJ;AACA,SAASe,QAAQA,CAACvB,KAAK,EAAE;EACvB,OAAOA,KAAK,CAACd,IAAI,CAAC,UAAUsC,KAAK,EAAE;IACjC,IAAI7B,GAAG,GAAG6B,KAAK,CAAC7B,GAAG;IACnB,OAAOA,GAAG,KAAK,WAAW;EAC5B,CAAC,CAAC;AACJ;AACA,SAAS8B,SAASA,CAACC,MAAM,EAAE;EACzB,IAAIC,aAAa,GAAG,KAAK;EACzB,IAAIC,OAAO,GAAG,EAAE;EAChBF,MAAM,CAAC5F,OAAO,CAAC,UAAUqD,IAAI,EAAE;IAC7B,IAAIT,UAAU,CAAC,UAAU,EAAES,IAAI,CAAC,EAAE;MAChCyC,OAAO,CAACnG,IAAI,CAAC,EAAE,CAAC;MAChBkG,aAAa,GAAG,IAAI;IACtB;IACA,IAAIA,aAAa,EAAE;MACjBC,OAAO,CAACA,OAAO,CAAC/F,MAAM,GAAG,CAAC,CAAC,CAACJ,IAAI,CAAC0D,IAAI,CAAC;IACxC;IACA,IAAIV,QAAQ,CAAC,UAAU,EAAEU,IAAI,CAAC,EAAE;MAC9BwC,aAAa,GAAG,KAAK;IACvB;EACF,CAAC,CAAC;EACF,OAAOC,OAAO;AAChB;AACA,SAASC,gCAAgCA,CAACH,MAAM,EAAE;EAChD,IAAIC,aAAa,GAAG,KAAK;EACzB,IAAIG,WAAW,GAAG,CAAC;EACnBJ,MAAM,CAAC5F,OAAO,CAAC,UAAUqD,IAAI,EAAE;IAC7B,IAAIT,UAAU,CAAC,UAAU,EAAES,IAAI,CAAC,EAAE;MAChCwC,aAAa,GAAG,IAAI;IACtB;IACA,IAAIA,aAAa,EAAE;MACjB,IAAIxC,IAAI,CAACQ,GAAG,KAAK,mBAAmB,IAAIR,IAAI,CAACQ,GAAG,KAAK,mBAAmB,EAAE;QACxEmC,WAAW,EAAE;QACbH,aAAa,GAAG,KAAK;MACvB;IACF;IACA,IAAIlD,QAAQ,CAAC,UAAU,EAAEU,IAAI,CAAC,EAAE;MAC9BwC,aAAa,GAAG,KAAK;IACvB;EACF,CAAC,CAAC;EACF,OAAOG,WAAW;AACpB;AACA,SAASC,aAAaA,CAAC1C,MAAM,EAAE;EAC7B,IAAI2C,MAAM,GAAG,EAAE;EACf,IAAId,QAAQ,GAAG,KAAK;EACpB,KAAK,IAAI/D,CAAC,GAAGkC,MAAM,CAACxD,MAAM,GAAG,CAAC,EAAEsB,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC3C,IAAIgC,IAAI,GAAGE,MAAM,CAAClC,CAAC,CAAC;IACpB,IAAIsB,QAAQ,CAAC,UAAU,EAAEU,IAAI,CAAC,EAAE;MAC9B+B,QAAQ,GAAG,IAAI;IACjB;IACA,IAAIxC,UAAU,CAAC,UAAU,EAAES,IAAI,CAAC,EAAE;MAChC6C,MAAM,CAAC1B,OAAO,CAACnB,IAAI,CAAC/C,KAAK,CAAC;MAC1B8E,QAAQ,GAAG,KAAK;IAClB;IACA,IAAIA,QAAQ,EAAE;MACZc,MAAM,CAAC1B,OAAO,CAACnB,IAAI,CAAC/C,KAAK,CAAC;IAC5B;IACA,IAAIgC,gBAAgB,CAACe,IAAI,CAAC,EAAE;MAC1B,IAAI6C,MAAM,CAACnG,MAAM,GAAG,CAAC,EAAE;QACrB,OAAOmG,MAAM,CAACjB,IAAI,CAAC,EAAE,CAAC;MACxB;MACA;IACF;EACF;EACA,OAAO,EAAE;AACX;AACA,IAAIkB,UAAU,GAAG,aAAa,YAAY;EACxC,SAASA,UAAUA,CAAA,EAAG;IACpBzE,eAAe,CAAC,IAAI,EAAEyE,UAAU,CAAC;IACjC,IAAI,CAAClF,IAAI,GAAG,YAAY;IACxB,IAAI,CAACmF,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,MAAM,GAAG;MACZC,KAAK,EAAE,GAAG;MACVC,GAAG,EAAE,GAAG;MACRC,IAAI,EAAE,kBAAkB;MACxBC,QAAQ,EAAE;IACZ,CAAC;EACH;EACA,OAAO7E,YAAY,CAACsE,UAAU,EAAE,CAAC;IAC/BvE,GAAG,EAAE,oBAAoB;IACzBtB,KAAK,EAAE,SAASqG,kBAAkBA,CAACC,IAAI,EAAEC,aAAa,EAAE;MACtD,IAAI,CAACA,aAAa,GAAGA,aAAa;MAClC,OAAOD,IAAI;IACb;EACF,CAAC,EAAE;IACDhF,GAAG,EAAE,UAAU;IACftB,KAAK,EAAE,SAASwG,QAAQA,CAACvD,MAAM,EAAEwD,KAAK,EAAE;MACtC,IAAIC,WAAW,GAAGD,KAAK,CAACC,WAAW;MACnC,IAAIjE,SAAS,CAACkE,IAAI,CAACvC,OAAO,CAACsC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE;QAC9C,IAAI,CAACE,KAAK,GAAGvB,SAAS,CAACpC,MAAM,CAAC;MAChC;IACF;EACF,CAAC,EAAE;IACD3B,GAAG,EAAE,UAAU;IACftB,KAAK,EAAE,SAAS6G,QAAQA,CAAA,EAAG;MACzB,IAAIC,MAAM,GAAGnE,UAAU;MACvB,OAAO,CAAC,CAAC,IAAI,CAACqD,MAAM,CAACC,KAAK,EAAEa,MAAM,EAAE;QAClCC,QAAQ,EAAE,MAAM;QAChBC,QAAQ,EAAE,OAAO;QACjBZ,QAAQ,EAAE;MACZ,CAAC,CAAC,EAAE,CAAC,IAAI,CAACJ,MAAM,CAACI,QAAQ,EAAEU,MAAM,EAAE;QACjCC,QAAQ,EAAE,MAAM;QAChBC,QAAQ,EAAE,OAAO;QACjBZ,QAAQ,EAAE;MACZ,CAAC,CAAC,EAAE,CAAC,IAAI,CAACJ,MAAM,CAACE,GAAG,EAAEY,MAAM,EAAE;QAC5BE,QAAQ,EAAE;MACZ,CAAC,CAAC,EAAE,CAAC,IAAI,CAAChB,MAAM,CAACG,IAAI,EAAEW,MAAM,EAAE,UAAUG,KAAK,EAAE;QAC9C,IAAIC,KAAK,GAAG/G,cAAc,CAAC8G,KAAK,EAAE,CAAC,CAAC;UAClCF,QAAQ,GAAGG,KAAK,CAAC,CAAC,CAAC;UACnBlH,KAAK,GAAGkH,KAAK,CAAC,CAAC,CAAC;QAClB,OAAO;UACLF,QAAQ,EAAE,OAAO;UACjBZ,QAAQ,EAAE,KAAK;UACfW,QAAQ,EAAEA,QAAQ;UAClB/G,KAAK,EAAEA;QACT,CAAC;MACH,CAAC,CAAC,CAAC;IACL;EACF,CAAC,EAAE;IACDsB,GAAG,EAAE,WAAW;IAChBtB,KAAK,EAAE,SAASmH,SAASA,CAACC,SAAS,EAAEnE,MAAM,EAAE;MAC3C;MACA,IAAImE,SAAS,KAAK,YAAY,EAAE;QAC9B;MACF;MACA;;MAEA,OAAOnE,MAAM,CAAC8B,MAAM,CAAC,UAAUsC,IAAI,EAAEtE,IAAI,EAAEuE,MAAM,EAAE;QACjD,IAAIrF,QAAQ,CAACc,IAAI,EAAEJ,UAAU,CAAC,IAAII,IAAI,CAACO,SAAS,IAAI,IAAI,EAAE;UACxD+D,IAAI,CAAChI,IAAI,CAAC;YACR0D,IAAI,EAAEA,IAAI;YACVuE,MAAM,EAAEA;UACV,CAAC,CAAC;QACJ;QACA,OAAOD,IAAI;MACb,CAAC,EAAE,EAAE,CAAC;IACR;EACF,CAAC,EAAE;IACD/F,GAAG,EAAE,WAAW;IAChBtB,KAAK,EAAE,SAASuH,SAASA,CAACtE,MAAM,EAAEuE,KAAK,EAAE;MACvC,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;MAC7B,IAAIA,QAAQ,IAAI,IAAI,CAAClB,aAAa,CAACmB,QAAQ,KAAK,MAAM,IAAIzE,MAAM,CAACxD,MAAM,GAAG,CAAC,EAAE;QAC3EgI,QAAQ,CAAC/B,WAAW,GAAGD,gCAAgC,CAACxC,MAAM,CAAC;QAC/D,IAAI,CAAC8C,WAAW,IAAI0B,QAAQ,CAAC/B,WAAW;QACxC,IAAIkB,KAAK,GAAG,IAAI,CAACA,KAAK;QACtBA,KAAK,CAAC9D,IAAI,CAAC,UAAUyB,IAAI,EAAEoD,KAAK,EAAE;UAChC,IAAIF,QAAQ,CAACG,MAAM,GAAGrD,IAAI,CAAC,CAAC,CAAC,CAACqD,MAAM,EAAE;YACpC,IAAID,KAAK,GAAG,CAAC,GAAGf,KAAK,CAACnH,MAAM,IAAI0E,YAAY,CAACyC,KAAK,CAACe,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE;cAC9DF,QAAQ,CAAC7C,iBAAiB,GAAG,IAAI;YACnC;YACA,OAAO,IAAI;UACb;UACA,IAAI3B,MAAM,CAAC,CAAC,CAAC,CAAC2E,MAAM,GAAGrD,IAAI,CAAC,CAAC,CAAC,CAACqD,MAAM,IAAIrD,IAAI,CAAC,CAAC,CAAC,CAACqD,MAAM,GAAGH,QAAQ,CAACG,MAAM,EAAE;YACzE,IAAIvD,UAAU,CAACuC,KAAK,CAACe,KAAK,CAAC,CAAC,EAAE;cAC5BF,QAAQ,CAACI,WAAW,GAAG;gBACrBF,KAAK,EAAEA;cACT,CAAC;YACH;YACA,OAAO,IAAI;UACb;QACF,CAAC,CAAC;QACFF,QAAQ,CAACK,kBAAkB,GAAGnC,aAAa,CAAC1C,MAAM,CAAC;MACrD;MACA,IAAI,CAACwE,QAAQ,IAAIA,QAAQ,CAACV,QAAQ,KAAK,MAAM,IAAIU,QAAQ,CAACX,MAAM,KAAKnE,UAAU,IAAI,CAACe,sBAAsB,CAACT,MAAM,CAAC,EAAE;QAClH,OAAOA,MAAM;MACf;MACAwE,QAAQ,CAACM,aAAa,GAAG,IAAI;MAC7B,IAAIC,KAAK,GAAG,CAAC;MACb,IAAI1C,MAAM,GAAGxD,OAAO,CAACmB,MAAM,EAAE,UAAUgF,CAAC,EAAE;QACxC,IAAIjG,gBAAgB,CAACiG,CAAC,CAAC,EAAE;UACvBD,KAAK,EAAE;UACP,IAAIA,KAAK,KAAK,CAAC,EAAE;YACf,OAAO,OAAO;UAChB;QACF;QACA,IAAI9F,cAAc,CAAC+F,CAAC,CAAC,EAAE;UACrBD,KAAK,EAAE;UACP,IAAIA,KAAK,KAAK,CAAC,EAAE;YACf,OAAO,KAAK;UACd;QACF;QACA,OAAO,IAAI;MACb,CAAC,CAAC;MACF,IAAIE,UAAU,GAAG5C,MAAM,CAAC,CAAC,CAAC;MAC1B,IAAI6C,SAAS,GAAGpG,IAAI,CAACuD,MAAM,CAAC;MAC5B,IAAI8C,WAAW,GAAGzE,SAAS,CAACuE,UAAU,CAAC;MACvC,IAAIG,UAAU,GAAG1E,SAAS,CAACwE,SAAS,CAAC;MACrCV,QAAQ,CAACa,qBAAqB,GAAG7E,YAAY,CAACyE,UAAU,CAAC;MACzDT,QAAQ,CAAChE,YAAY,GAAGA,YAAY,CAAC0E,SAAS,CAAC;MAC/C,IAAIhD,QAAQ,CAAC+C,UAAU,CAAC,EAAE;QACxBE,WAAW,GAAG,CAAC;MACjB;MACA,IAAIjD,QAAQ,CAACgD,SAAS,CAAC,EAAE;QACvBE,UAAU,GAAG,CAAC;MAChB;MACA,OAAOpF,MAAM,CAACvC,KAAK,CAAC0H,WAAW,EAAEnF,MAAM,CAACxD,MAAM,GAAG4I,UAAU,CAAC;IAC9D;EACF,CAAC,EAAE;IACD/G,GAAG,EAAE,SAAS;IACdtB,KAAK,EAAE,SAASuI,OAAOA,CAACxF,IAAI,EAAEyF,OAAO,EAAE;MACrC,IAAI,CAACvG,QAAQ,CAACc,IAAI,EAAEJ,UAAU,CAAC,EAAE;QAC/B,OAAO,IAAI;MACb;MACA,IAAI8F,EAAE,GAAGD,OAAO,CAACE,YAAY;MAC7B,IAAIC,aAAa,GAAGF,EAAE,CAACG,aAAa,CAAC7F,IAAI,CAAC/C,KAAK,EAAE;QAC/C+C,IAAI,EAAEA;MACR,CAAC,CAAC;MACF,IAAI8F,QAAQ,GAAG,EAAE;MACjB,SAASC,QAAQA,CAACC,KAAK,EAAEhI,CAAC,EAAEtB,MAAM,EAAE;QAClC,IAAIiJ,YAAY,GAAGD,EAAE,CAACO,qBAAqB,CAACD,KAAK,EAAEhG,IAAI,CAAC/C,KAAK,EAAEe,CAAC,EAAEgC,IAAI,EAAEtD,MAAM,CAAC;QAC/EoJ,QAAQ,CAACxJ,IAAI,CAACmJ,OAAO,CAACD,OAAO,CAAChJ,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiJ,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;UAC1ES,QAAQ,EAAElG,IAAI,CAACO,SAAS;UACxB+D,IAAI,EAAE,CAAC,CAAC;UACRqB,YAAY,EAAEA;QAChB,CAAC,CAAC,CAAC,CAAC;MACN;MACA,IAAIQ,SAAS,GAAG,EAAE;MAClB,OAAOP,aAAa,CAACQ,IAAI,CAAC,UAAUC,MAAM,EAAE;QAC1C,OAAO,IAAIC,OAAO,CAAC,UAAUd,OAAO,EAAE;UACpC,IAAIa,MAAM,YAAY5K,KAAK,EAAE;YAC3B6K,OAAO,CAACC,GAAG,CAACF,MAAM,CAAC,CAACD,IAAI,CAACZ,OAAO,CAAC;UACnC,CAAC,MAAM;YACLA,OAAO,CAACa,MAAM,CAAC;UACjB;QACF,CAAC,CAAC,CAACD,IAAI,CAAC,UAAUC,MAAM,EAAE;UACxBX,EAAE,CAACc,aAAa,CAACH,MAAM,EAAEN,QAAQ,EAAE/F,IAAI,CAACqD,QAAQ,CAAC;UACjD,OAAOiD,OAAO,CAACC,GAAG,CAACT,QAAQ,CAAC,CAACM,IAAI,CAAC,UAAUjL,CAAC,EAAE;YAC7C,OAAOA,CAAC,CAACuG,GAAG,CAAC,UAAU+E,KAAK,EAAE;cAC5B,IAAIC,QAAQ,GAAGD,KAAK,CAACC,QAAQ;gBAC3BC,MAAM,GAAGF,KAAK,CAACE,MAAM;cACvBR,SAAS,CAAC7J,IAAI,CAACC,KAAK,CAAC4J,SAAS,EAAEjL,kBAAkB,CAACyL,MAAM,CAAC,CAAC;cAC3D,OAAOD,QAAQ;YACjB,CAAC,CAAC;UACJ,CAAC,CAAC,CAACN,IAAI,CAAC,UAAUnJ,KAAK,EAAE;YACvB,IAAIkJ,SAAS,CAACzJ,MAAM,GAAG,CAAC,EAAE;cACxB,MAAMyJ,SAAS;YACjB;YACA,OAAOlJ,KAAK;UACd,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACA;EACF,CAAC,EAAE;IACDsB,GAAG,EAAE,QAAQ;IACbtB,KAAK,EAAE,SAAS2J,MAAMA,CAAC5G,IAAI,EAAEyF,OAAO,EAAE;MACpC,IAAIzF,IAAI,CAACQ,GAAG,KAAK,QAAQ,EAAE;QACzB,IAAI,CAACuC,MAAM,GAAG/C,IAAI,CAAC6G,QAAQ,KAAK,OAAO;MACzC;MACA,IAAI7G,IAAI,CAACQ,GAAG,KAAK,OAAO,IAAI,IAAI,CAACuC,MAAM,EAAE;QACvC,IAAI,CAAC+D,OAAO,GAAG9G,IAAI;QACnB,OAAOA,IAAI;MACb;MACA,IAAI,CAACd,QAAQ,CAACc,IAAI,EAAEJ,UAAU,CAAC,EAAE;QAC/B,OAAO,IAAI;MACb;MACA,IAAImH,UAAU,GAAG,EAAE;MACnB,IAAIJ,MAAM,GAAG,EAAE;MACf,IAAIK,YAAY,GAAG,CAAC;MACpB,IAAIC,IAAI,GAAG,IAAI;MACf,IAAIC,QAAQ,GAAGlH,IAAI,CAACO,SAAS,CAAC,CAAC,CAAC;MAChC,IAAI4G,SAAS,GAAG,CAAC;MACjB,IAAI,CAACD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC1G,GAAG,MAAM,MAAM,EAAE;QACjF2G,SAAS,GAAG,CAAC3H,kBAAkB,CAAC0H,QAAQ,CAACjK,KAAK,EAAE,GAAG,CAAC;MACtD;MACA+J,YAAY,IAAIG,SAAS;MACzB,IAAIC,cAAc,GAAG,CAAC;MACtB,IAAIC,mBAAmB,GAAGhH,qBAAqB,CAACL,IAAI,CAAC;;MAErD;MACA,SAAS+F,QAAQA,CAACC,KAAK,EAAEhI,CAAC,EAAEtB,MAAM,EAAE;QAClCsK,YAAY,IAAIG,SAAS;QACzB,IAAIxB,YAAY,GAAGF,OAAO,CAACE,YAAY,CAACM,qBAAqB,CAACD,KAAK,EAAEhG,IAAI,CAAC/C,KAAK,EAAEe,CAAC,EAAEgC,IAAI,EAAEtD,MAAM,CAAC;QACjGsD,IAAI,CAACO,SAAS,CAAC5D,OAAO,CAAC,UAAU2K,EAAE,EAAE;UACnC,IAAI/H,UAAU,CAAC,WAAW,EAAE+H,EAAE,CAAC,EAAE;YAC/B,IAAIC,GAAG,GAAG,CAAC/H,kBAAkB,CAAC8H,EAAE,CAACrK,KAAK,EAAE,KAAK,CAAC,GAAGmK,cAAc;YAC/DA,cAAc,GAAG,CAAC;YAClBE,EAAE,CAACrK,KAAK,GAAGwC,kBAAkB,CAAC6H,EAAE,CAACrK,KAAK,EAAE,KAAK,EAAEsK,GAAG,CAAC;UACrD;QACF,CAAC,CAAC;QACF,IAAIxG,WAAW,GAAG0E,OAAO,CAACmB,MAAM,CAACpK,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiJ,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;UAC7ES,QAAQ,EAAElG,IAAI,CAACO,SAAS;UACxB+D,IAAI,EAAE,CAAC,CAAC;UACRqB,YAAY,EAAEA;QAChB,CAAC,CAAC,CAAC;QACH,IAAI3F,IAAI,CAACU,YAAY,IAAI1C,CAAC,KAAKtB,MAAM,GAAG,CAAC,IAAI2K,mBAAmB,EAAE;UAChEvG,iBAAiB,CAACC,WAAW,CAAC;QAChC;QACA,IAAIyG,UAAU,GAAG7B,YAAY,CAAC8B,aAAa,CAAC1H,IAAI,CAAC,UAAU/B,CAAC,EAAE;UAC5D,OAAOA,CAAC,KAAK,CAAC;QAChB,CAAC,CAAC;QACF,IAAIwJ,UAAU,EAAE;UACd,IAAIxH,IAAI,CAAC2C,WAAW,KAAK,CAAC,EAAE;YAC1B5B,WAAW,CAACjB,KAAK,GAAGoC,oBAAoB,CAACnB,WAAW,CAACjB,KAAK,CAAC;UAC7D;UACA,IAAIE,IAAI,CAAC6B,iBAAiB,EAAE;YAC1Bd,WAAW,CAACjB,KAAK,GAAG+B,iBAAiB,CAACd,WAAW,CAACjB,KAAK,CAAC;UAC1D;QACF,CAAC,MAAM,IAAIE,IAAI,CAAC8E,WAAW,EAAE;UAC3B/D,WAAW,CAACjB,KAAK,GAAGyB,gBAAgB,CAACR,WAAW,CAACjB,KAAK,EAAEmH,IAAI,CAACpD,KAAK,CAAC7D,IAAI,CAAC8E,WAAW,CAACF,KAAK,CAAC,CAAC;QAC7F;QACA,IAAI5E,IAAI,CAAC8E,WAAW,EAAE;UACpBhE,iBAAiB,CAACC,WAAW,CAAC;QAChC;QACA,IAAIf,IAAI,CAACuF,qBAAqB,IAAI8B,mBAAmB,EAAE;UACrDnG,uBAAuB,CAACH,WAAW,CAAC;QACtC;QACA,KAAK,IAAI2G,EAAE,GAAG,CAAC,EAAEvH,GAAG,GAAGY,WAAW,CAACjB,KAAK,CAACpD,MAAM,EAAEgL,EAAE,GAAGvH,GAAG,EAAEuH,EAAE,EAAE,EAAE;UAC/DX,UAAU,CAACzK,IAAI,CAACyE,WAAW,CAACjB,KAAK,CAAC4H,EAAE,CAAC,CAAC;QACxC;QACAjM,KAAK,CAACR,SAAS,CAACqB,IAAI,CAACC,KAAK,CAACoK,MAAM,EAAE5F,WAAW,CAAC4F,MAAM,CAAC;MACxD;MACA,IAAI1E,MAAM,GAAGwD,OAAO,CAACE,YAAY,CAACI,QAAQ,CAAC/F,IAAI,CAAC/C,KAAK,EAAE8I,QAAQ,EAAE/F,IAAI,CAACqD,QAAQ,EAAE;QAC9ErD,IAAI,EAAEA;MACR,CAAC,CAAC;MACF;MACA,IAAIiC,MAAM,KAAK,KAAK,EAAE;QACpB,IAAIjC,IAAI,CAAC+E,kBAAkB,EAAE;UAC3B,IAAI/E,IAAI,CAACgF,aAAa,EAAE;YACtB,OAAO;cACL/H,KAAK,EAAE,cAAc,CAACwE,MAAM,CAACzB,IAAI,CAAC+E,kBAAkB,EAAE,gBAAgB;YACxE,CAAC;UACH;UACA,OAAO;YACL9H,KAAK,EAAE,gCAAgC,CAACwE,MAAM,CAACzB,IAAI,CAAC+E,kBAAkB,EAAE,oBAAoB;UAC9F,CAAC;QACH;QACA,OAAO;UACL9H,KAAK,EAAEwD,qBAAqB,CAACT,IAAI,CAAC,IAAI,EAAE;UACxC2G,MAAM,EAAEA;QACV,CAAC;MACH;MACA,IAAIK,YAAY,KAAK,CAAC,EAAE;QACtB,IAAIW,EAAE,GAAG,CAACnI,kBAAkB,CAAC,IAAI,CAACsH,OAAO,CAAC7J,KAAK,EAAE,IAAI,CAAC;QACtD,IAAI,CAAC6J,OAAO,CAAC7J,KAAK,GAAGwC,kBAAkB,CAAC,IAAI,CAACqH,OAAO,CAAC7J,KAAK,EAAE,IAAI,EAAE0K,EAAE,GAAGX,YAAY,CAAC;MACtF;MACA,OAAO;QACL/J,KAAK,EAAEwI,OAAO,CAACmC,aAAa,CAACb,UAAU,EAAEvK,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEiJ,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;UACrFf,QAAQ,EAAE1E;QACZ,CAAC,CAAC,CAAC;QACH2G,MAAM,EAAEA;MACV,CAAC;IACH;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACH5C,MAAM,CAAC8D,OAAO,GAAG,YAAY;EAC3B,OAAOlI,OAAO,CAAC,IAAImD,UAAU,CAAC,CAAC,CAAC;AAClC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}