{"ast": null, "code": "// Enclose abbreviations in <abbr> tags\n//\n'use strict';\n\nmodule.exports = function sub_plugin(md) {\n  var escapeRE = md.utils.escapeRE,\n    arrayReplaceAt = md.utils.arrayReplaceAt;\n\n  // ASCII characters in Cc, Sc, Sm, Sk categories we should terminate on;\n  // you can check character classes here:\n  // http://www.unicode.org/Public/UNIDATA/UnicodeData.txt\n  var OTHER_CHARS = ' \\r\\n$+<=>^`|~';\n  var UNICODE_PUNCT_RE = md.utils.lib.ucmicro.P.source;\n  var UNICODE_SPACE_RE = md.utils.lib.ucmicro.Z.source;\n  function abbr_def(state, startLine, endLine, silent) {\n    var label,\n      title,\n      ch,\n      labelStart,\n      labelEnd,\n      pos = state.bMarks[startLine] + state.tShift[startLine],\n      max = state.eMarks[startLine];\n    if (pos + 2 >= max) {\n      return false;\n    }\n    if (state.src.charCodeAt(pos++) !== 0x2A /* * */) {\n      return false;\n    }\n    if (state.src.charCodeAt(pos++) !== 0x5B /* [ */) {\n      return false;\n    }\n    labelStart = pos;\n    for (; pos < max; pos++) {\n      ch = state.src.charCodeAt(pos);\n      if (ch === 0x5B /* [ */) {\n        return false;\n      } else if (ch === 0x5D /* ] */) {\n        labelEnd = pos;\n        break;\n      } else if (ch === 0x5C /* \\ */) {\n        pos++;\n      }\n    }\n    if (labelEnd < 0 || state.src.charCodeAt(labelEnd + 1) !== 0x3A /* : */) {\n      return false;\n    }\n    if (silent) {\n      return true;\n    }\n    label = state.src.slice(labelStart, labelEnd).replace(/\\\\(.)/g, '$1');\n    title = state.src.slice(labelEnd + 2, max).trim();\n    if (label.length === 0) {\n      return false;\n    }\n    if (title.length === 0) {\n      return false;\n    }\n    if (!state.env.abbreviations) {\n      state.env.abbreviations = {};\n    }\n    // prepend ':' to avoid conflict with Object.prototype members\n    if (typeof state.env.abbreviations[':' + label] === 'undefined') {\n      state.env.abbreviations[':' + label] = title;\n    }\n    state.line = startLine + 1;\n    return true;\n  }\n  function abbr_replace(state) {\n    var i,\n      j,\n      l,\n      tokens,\n      token,\n      text,\n      nodes,\n      pos,\n      reg,\n      m,\n      regText,\n      regSimple,\n      currentToken,\n      blockTokens = state.tokens;\n    if (!state.env.abbreviations) {\n      return;\n    }\n    regSimple = new RegExp('(?:' + Object.keys(state.env.abbreviations).map(function (x) {\n      return x.substr(1);\n    }).sort(function (a, b) {\n      return b.length - a.length;\n    }).map(escapeRE).join('|') + ')');\n    regText = '(^|' + UNICODE_PUNCT_RE + '|' + UNICODE_SPACE_RE + '|[' + OTHER_CHARS.split('').map(escapeRE).join('') + '])' + '(' + Object.keys(state.env.abbreviations).map(function (x) {\n      return x.substr(1);\n    }).sort(function (a, b) {\n      return b.length - a.length;\n    }).map(escapeRE).join('|') + ')' + '($|' + UNICODE_PUNCT_RE + '|' + UNICODE_SPACE_RE + '|[' + OTHER_CHARS.split('').map(escapeRE).join('') + '])';\n    reg = new RegExp(regText, 'g');\n    for (j = 0, l = blockTokens.length; j < l; j++) {\n      if (blockTokens[j].type !== 'inline') {\n        continue;\n      }\n      tokens = blockTokens[j].children;\n\n      // We scan from the end, to keep position when new tags added.\n      for (i = tokens.length - 1; i >= 0; i--) {\n        currentToken = tokens[i];\n        if (currentToken.type !== 'text') {\n          continue;\n        }\n        pos = 0;\n        text = currentToken.content;\n        reg.lastIndex = 0;\n        nodes = [];\n\n        // fast regexp run to determine whether there are any abbreviated words\n        // in the current token\n        if (!regSimple.test(text)) {\n          continue;\n        }\n        while (m = reg.exec(text)) {\n          if (m.index > 0 || m[1].length > 0) {\n            token = new state.Token('text', '', 0);\n            token.content = text.slice(pos, m.index + m[1].length);\n            nodes.push(token);\n          }\n          token = new state.Token('abbr_open', 'abbr', 1);\n          token.attrs = [['title', state.env.abbreviations[':' + m[2]]]];\n          nodes.push(token);\n          token = new state.Token('text', '', 0);\n          token.content = m[2];\n          nodes.push(token);\n          token = new state.Token('abbr_close', 'abbr', -1);\n          nodes.push(token);\n          reg.lastIndex -= m[3].length;\n          pos = reg.lastIndex;\n        }\n        if (!nodes.length) {\n          continue;\n        }\n        if (pos < text.length) {\n          token = new state.Token('text', '', 0);\n          token.content = text.slice(pos);\n          nodes.push(token);\n        }\n\n        // replace current node\n        blockTokens[j].children = tokens = arrayReplaceAt(tokens, i, nodes);\n      }\n    }\n  }\n  md.block.ruler.before('reference', 'abbr_def', abbr_def, {\n    alt: ['paragraph', 'reference']\n  });\n  md.core.ruler.after('linkify', 'abbr_replace', abbr_replace);\n};", "map": {"version": 3, "names": ["module", "exports", "sub_plugin", "md", "escapeRE", "utils", "arrayReplaceAt", "OTHER_CHARS", "UNICODE_PUNCT_RE", "lib", "ucmicro", "P", "source", "UNICODE_SPACE_RE", "Z", "abbr_def", "state", "startLine", "endLine", "silent", "label", "title", "ch", "labelStart", "labelEnd", "pos", "bMarks", "tShift", "max", "eMarks", "src", "charCodeAt", "slice", "replace", "trim", "length", "env", "abbreviations", "line", "abbr_replace", "i", "j", "l", "tokens", "token", "text", "nodes", "reg", "m", "regText", "regSimple", "currentToken", "blockTokens", "RegExp", "Object", "keys", "map", "x", "substr", "sort", "a", "b", "join", "split", "type", "children", "content", "lastIndex", "test", "exec", "index", "Token", "push", "attrs", "block", "ruler", "before", "alt", "core", "after"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it-abbr@1.0.4/node_modules/markdown-it-abbr/index.js"], "sourcesContent": ["// Enclose abbreviations in <abbr> tags\n//\n'use strict';\n\n\nmodule.exports = function sub_plugin(md) {\n  var escapeRE        = md.utils.escapeRE,\n      arrayReplaceAt  = md.utils.arrayReplaceAt;\n\n  // ASCII characters in Cc, Sc, Sm, Sk categories we should terminate on;\n  // you can check character classes here:\n  // http://www.unicode.org/Public/UNIDATA/UnicodeData.txt\n  var OTHER_CHARS      = ' \\r\\n$+<=>^`|~';\n\n  var UNICODE_PUNCT_RE = md.utils.lib.ucmicro.P.source;\n  var UNICODE_SPACE_RE = md.utils.lib.ucmicro.Z.source;\n\n\n  function abbr_def(state, startLine, endLine, silent) {\n    var label, title, ch, labelStart, labelEnd,\n        pos = state.bMarks[startLine] + state.tShift[startLine],\n        max = state.eMarks[startLine];\n\n    if (pos + 2 >= max) { return false; }\n\n    if (state.src.charCodeAt(pos++) !== 0x2A/* * */) { return false; }\n    if (state.src.charCodeAt(pos++) !== 0x5B/* [ */) { return false; }\n\n    labelStart = pos;\n\n    for (; pos < max; pos++) {\n      ch = state.src.charCodeAt(pos);\n      if (ch === 0x5B /* [ */) {\n        return false;\n      } else if (ch === 0x5D /* ] */) {\n        labelEnd = pos;\n        break;\n      } else if (ch === 0x5C /* \\ */) {\n        pos++;\n      }\n    }\n\n    if (labelEnd < 0 || state.src.charCodeAt(labelEnd + 1) !== 0x3A/* : */) {\n      return false;\n    }\n\n    if (silent) { return true; }\n\n    label = state.src.slice(labelStart, labelEnd).replace(/\\\\(.)/g, '$1');\n    title = state.src.slice(labelEnd + 2, max).trim();\n    if (label.length === 0) { return false; }\n    if (title.length === 0) { return false; }\n    if (!state.env.abbreviations) { state.env.abbreviations = {}; }\n    // prepend ':' to avoid conflict with Object.prototype members\n    if (typeof state.env.abbreviations[':' + label] === 'undefined') {\n      state.env.abbreviations[':' + label] = title;\n    }\n\n    state.line = startLine + 1;\n    return true;\n  }\n\n\n  function abbr_replace(state) {\n    var i, j, l, tokens, token, text, nodes, pos, reg, m, regText, regSimple,\n        currentToken,\n        blockTokens = state.tokens;\n\n    if (!state.env.abbreviations) { return; }\n\n    regSimple = new RegExp('(?:' +\n      Object.keys(state.env.abbreviations).map(function (x) {\n        return x.substr(1);\n      }).sort(function (a, b) {\n        return b.length - a.length;\n      }).map(escapeRE).join('|') +\n    ')');\n\n    regText = '(^|' + UNICODE_PUNCT_RE + '|' + UNICODE_SPACE_RE +\n                    '|[' + OTHER_CHARS.split('').map(escapeRE).join('') + '])'\n            + '(' + Object.keys(state.env.abbreviations).map(function (x) {\n                      return x.substr(1);\n                    }).sort(function (a, b) {\n                      return b.length - a.length;\n                    }).map(escapeRE).join('|') + ')'\n            + '($|' + UNICODE_PUNCT_RE + '|' + UNICODE_SPACE_RE +\n                    '|[' + OTHER_CHARS.split('').map(escapeRE).join('') + '])';\n\n    reg = new RegExp(regText, 'g');\n\n    for (j = 0, l = blockTokens.length; j < l; j++) {\n      if (blockTokens[j].type !== 'inline') { continue; }\n      tokens = blockTokens[j].children;\n\n      // We scan from the end, to keep position when new tags added.\n      for (i = tokens.length - 1; i >= 0; i--) {\n        currentToken = tokens[i];\n        if (currentToken.type !== 'text') { continue; }\n\n        pos = 0;\n        text = currentToken.content;\n        reg.lastIndex = 0;\n        nodes = [];\n\n        // fast regexp run to determine whether there are any abbreviated words\n        // in the current token\n        if (!regSimple.test(text)) { continue; }\n\n        while ((m = reg.exec(text))) {\n          if (m.index > 0 || m[1].length > 0) {\n            token         = new state.Token('text', '', 0);\n            token.content = text.slice(pos, m.index + m[1].length);\n            nodes.push(token);\n          }\n\n          token         = new state.Token('abbr_open', 'abbr', 1);\n          token.attrs   = [ [ 'title', state.env.abbreviations[':' + m[2]] ] ];\n          nodes.push(token);\n\n          token         = new state.Token('text', '', 0);\n          token.content = m[2];\n          nodes.push(token);\n\n          token         = new state.Token('abbr_close', 'abbr', -1);\n          nodes.push(token);\n\n          reg.lastIndex -= m[3].length;\n          pos = reg.lastIndex;\n        }\n\n        if (!nodes.length) { continue; }\n\n        if (pos < text.length) {\n          token         = new state.Token('text', '', 0);\n          token.content = text.slice(pos);\n          nodes.push(token);\n        }\n\n        // replace current node\n        blockTokens[j].children = tokens = arrayReplaceAt(tokens, i, nodes);\n      }\n    }\n  }\n\n  md.block.ruler.before('reference', 'abbr_def', abbr_def, { alt: [ 'paragraph', 'reference' ] });\n\n  md.core.ruler.after('linkify', 'abbr_replace', abbr_replace);\n};\n"], "mappings": "AAAA;AACA;AACA,YAAY;;AAGZA,MAAM,CAACC,OAAO,GAAG,SAASC,UAAUA,CAACC,EAAE,EAAE;EACvC,IAAIC,QAAQ,GAAUD,EAAE,CAACE,KAAK,CAACD,QAAQ;IACnCE,cAAc,GAAIH,EAAE,CAACE,KAAK,CAACC,cAAc;;EAE7C;EACA;EACA;EACA,IAAIC,WAAW,GAAQ,gBAAgB;EAEvC,IAAIC,gBAAgB,GAAGL,EAAE,CAACE,KAAK,CAACI,GAAG,CAACC,OAAO,CAACC,CAAC,CAACC,MAAM;EACpD,IAAIC,gBAAgB,GAAGV,EAAE,CAACE,KAAK,CAACI,GAAG,CAACC,OAAO,CAACI,CAAC,CAACF,MAAM;EAGpD,SAASG,QAAQA,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAEC,MAAM,EAAE;IACnD,IAAIC,KAAK;MAAEC,KAAK;MAAEC,EAAE;MAAEC,UAAU;MAAEC,QAAQ;MACtCC,GAAG,GAAGT,KAAK,CAACU,MAAM,CAACT,SAAS,CAAC,GAAGD,KAAK,CAACW,MAAM,CAACV,SAAS,CAAC;MACvDW,GAAG,GAAGZ,KAAK,CAACa,MAAM,CAACZ,SAAS,CAAC;IAEjC,IAAIQ,GAAG,GAAG,CAAC,IAAIG,GAAG,EAAE;MAAE,OAAO,KAAK;IAAE;IAEpC,IAAIZ,KAAK,CAACc,GAAG,CAACC,UAAU,CAACN,GAAG,EAAE,CAAC,KAAK,IAAI,UAAS;MAAE,OAAO,KAAK;IAAE;IACjE,IAAIT,KAAK,CAACc,GAAG,CAACC,UAAU,CAACN,GAAG,EAAE,CAAC,KAAK,IAAI,UAAS;MAAE,OAAO,KAAK;IAAE;IAEjEF,UAAU,GAAGE,GAAG;IAEhB,OAAOA,GAAG,GAAGG,GAAG,EAAEH,GAAG,EAAE,EAAE;MACvBH,EAAE,GAAGN,KAAK,CAACc,GAAG,CAACC,UAAU,CAACN,GAAG,CAAC;MAC9B,IAAIH,EAAE,KAAK,IAAI,CAAC,SAAS;QACvB,OAAO,KAAK;MACd,CAAC,MAAM,IAAIA,EAAE,KAAK,IAAI,CAAC,SAAS;QAC9BE,QAAQ,GAAGC,GAAG;QACd;MACF,CAAC,MAAM,IAAIH,EAAE,KAAK,IAAI,CAAC,SAAS;QAC9BG,GAAG,EAAE;MACP;IACF;IAEA,IAAID,QAAQ,GAAG,CAAC,IAAIR,KAAK,CAACc,GAAG,CAACC,UAAU,CAACP,QAAQ,GAAG,CAAC,CAAC,KAAK,IAAI,UAAS;MACtE,OAAO,KAAK;IACd;IAEA,IAAIL,MAAM,EAAE;MAAE,OAAO,IAAI;IAAE;IAE3BC,KAAK,GAAGJ,KAAK,CAACc,GAAG,CAACE,KAAK,CAACT,UAAU,EAAEC,QAAQ,CAAC,CAACS,OAAO,CAAC,QAAQ,EAAE,IAAI,CAAC;IACrEZ,KAAK,GAAGL,KAAK,CAACc,GAAG,CAACE,KAAK,CAACR,QAAQ,GAAG,CAAC,EAAEI,GAAG,CAAC,CAACM,IAAI,CAAC,CAAC;IACjD,IAAId,KAAK,CAACe,MAAM,KAAK,CAAC,EAAE;MAAE,OAAO,KAAK;IAAE;IACxC,IAAId,KAAK,CAACc,MAAM,KAAK,CAAC,EAAE;MAAE,OAAO,KAAK;IAAE;IACxC,IAAI,CAACnB,KAAK,CAACoB,GAAG,CAACC,aAAa,EAAE;MAAErB,KAAK,CAACoB,GAAG,CAACC,aAAa,GAAG,CAAC,CAAC;IAAE;IAC9D;IACA,IAAI,OAAOrB,KAAK,CAACoB,GAAG,CAACC,aAAa,CAAC,GAAG,GAAGjB,KAAK,CAAC,KAAK,WAAW,EAAE;MAC/DJ,KAAK,CAACoB,GAAG,CAACC,aAAa,CAAC,GAAG,GAAGjB,KAAK,CAAC,GAAGC,KAAK;IAC9C;IAEAL,KAAK,CAACsB,IAAI,GAAGrB,SAAS,GAAG,CAAC;IAC1B,OAAO,IAAI;EACb;EAGA,SAASsB,YAAYA,CAACvB,KAAK,EAAE;IAC3B,IAAIwB,CAAC;MAAEC,CAAC;MAAEC,CAAC;MAAEC,MAAM;MAAEC,KAAK;MAAEC,IAAI;MAAEC,KAAK;MAAErB,GAAG;MAAEsB,GAAG;MAAEC,CAAC;MAAEC,OAAO;MAAEC,SAAS;MACpEC,YAAY;MACZC,WAAW,GAAGpC,KAAK,CAAC2B,MAAM;IAE9B,IAAI,CAAC3B,KAAK,CAACoB,GAAG,CAACC,aAAa,EAAE;MAAE;IAAQ;IAExCa,SAAS,GAAG,IAAIG,MAAM,CAAC,KAAK,GAC1BC,MAAM,CAACC,IAAI,CAACvC,KAAK,CAACoB,GAAG,CAACC,aAAa,CAAC,CAACmB,GAAG,CAAC,UAAUC,CAAC,EAAE;MACpD,OAAOA,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAACC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACtB,OAAOA,CAAC,CAAC1B,MAAM,GAAGyB,CAAC,CAACzB,MAAM;IAC5B,CAAC,CAAC,CAACqB,GAAG,CAACpD,QAAQ,CAAC,CAAC0D,IAAI,CAAC,GAAG,CAAC,GAC5B,GAAG,CAAC;IAEJb,OAAO,GAAG,KAAK,GAAGzC,gBAAgB,GAAG,GAAG,GAAGK,gBAAgB,GAC3C,IAAI,GAAGN,WAAW,CAACwD,KAAK,CAAC,EAAE,CAAC,CAACP,GAAG,CAACpD,QAAQ,CAAC,CAAC0D,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,GAChE,GAAG,GAAGR,MAAM,CAACC,IAAI,CAACvC,KAAK,CAACoB,GAAG,CAACC,aAAa,CAAC,CAACmB,GAAG,CAAC,UAAUC,CAAC,EAAE;MACpD,OAAOA,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC;IACpB,CAAC,CAAC,CAACC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACtB,OAAOA,CAAC,CAAC1B,MAAM,GAAGyB,CAAC,CAACzB,MAAM;IAC5B,CAAC,CAAC,CAACqB,GAAG,CAACpD,QAAQ,CAAC,CAAC0D,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG,GACtC,KAAK,GAAGtD,gBAAgB,GAAG,GAAG,GAAGK,gBAAgB,GAC3C,IAAI,GAAGN,WAAW,CAACwD,KAAK,CAAC,EAAE,CAAC,CAACP,GAAG,CAACpD,QAAQ,CAAC,CAAC0D,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI;IAE1Ef,GAAG,GAAG,IAAIM,MAAM,CAACJ,OAAO,EAAE,GAAG,CAAC;IAE9B,KAAKR,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGU,WAAW,CAACjB,MAAM,EAAEM,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAC9C,IAAIW,WAAW,CAACX,CAAC,CAAC,CAACuB,IAAI,KAAK,QAAQ,EAAE;QAAE;MAAU;MAClDrB,MAAM,GAAGS,WAAW,CAACX,CAAC,CAAC,CAACwB,QAAQ;;MAEhC;MACA,KAAKzB,CAAC,GAAGG,MAAM,CAACR,MAAM,GAAG,CAAC,EAAEK,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;QACvCW,YAAY,GAAGR,MAAM,CAACH,CAAC,CAAC;QACxB,IAAIW,YAAY,CAACa,IAAI,KAAK,MAAM,EAAE;UAAE;QAAU;QAE9CvC,GAAG,GAAG,CAAC;QACPoB,IAAI,GAAGM,YAAY,CAACe,OAAO;QAC3BnB,GAAG,CAACoB,SAAS,GAAG,CAAC;QACjBrB,KAAK,GAAG,EAAE;;QAEV;QACA;QACA,IAAI,CAACI,SAAS,CAACkB,IAAI,CAACvB,IAAI,CAAC,EAAE;UAAE;QAAU;QAEvC,OAAQG,CAAC,GAAGD,GAAG,CAACsB,IAAI,CAACxB,IAAI,CAAC,EAAG;UAC3B,IAAIG,CAAC,CAACsB,KAAK,GAAG,CAAC,IAAItB,CAAC,CAAC,CAAC,CAAC,CAACb,MAAM,GAAG,CAAC,EAAE;YAClCS,KAAK,GAAW,IAAI5B,KAAK,CAACuD,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;YAC9C3B,KAAK,CAACsB,OAAO,GAAGrB,IAAI,CAACb,KAAK,CAACP,GAAG,EAAEuB,CAAC,CAACsB,KAAK,GAAGtB,CAAC,CAAC,CAAC,CAAC,CAACb,MAAM,CAAC;YACtDW,KAAK,CAAC0B,IAAI,CAAC5B,KAAK,CAAC;UACnB;UAEAA,KAAK,GAAW,IAAI5B,KAAK,CAACuD,KAAK,CAAC,WAAW,EAAE,MAAM,EAAE,CAAC,CAAC;UACvD3B,KAAK,CAAC6B,KAAK,GAAK,CAAE,CAAE,OAAO,EAAEzD,KAAK,CAACoB,GAAG,CAACC,aAAa,CAAC,GAAG,GAAGW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAE,CAAE;UACpEF,KAAK,CAAC0B,IAAI,CAAC5B,KAAK,CAAC;UAEjBA,KAAK,GAAW,IAAI5B,KAAK,CAACuD,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;UAC9C3B,KAAK,CAACsB,OAAO,GAAGlB,CAAC,CAAC,CAAC,CAAC;UACpBF,KAAK,CAAC0B,IAAI,CAAC5B,KAAK,CAAC;UAEjBA,KAAK,GAAW,IAAI5B,KAAK,CAACuD,KAAK,CAAC,YAAY,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;UACzDzB,KAAK,CAAC0B,IAAI,CAAC5B,KAAK,CAAC;UAEjBG,GAAG,CAACoB,SAAS,IAAInB,CAAC,CAAC,CAAC,CAAC,CAACb,MAAM;UAC5BV,GAAG,GAAGsB,GAAG,CAACoB,SAAS;QACrB;QAEA,IAAI,CAACrB,KAAK,CAACX,MAAM,EAAE;UAAE;QAAU;QAE/B,IAAIV,GAAG,GAAGoB,IAAI,CAACV,MAAM,EAAE;UACrBS,KAAK,GAAW,IAAI5B,KAAK,CAACuD,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;UAC9C3B,KAAK,CAACsB,OAAO,GAAGrB,IAAI,CAACb,KAAK,CAACP,GAAG,CAAC;UAC/BqB,KAAK,CAAC0B,IAAI,CAAC5B,KAAK,CAAC;QACnB;;QAEA;QACAQ,WAAW,CAACX,CAAC,CAAC,CAACwB,QAAQ,GAAGtB,MAAM,GAAGrC,cAAc,CAACqC,MAAM,EAAEH,CAAC,EAAEM,KAAK,CAAC;MACrE;IACF;EACF;EAEA3C,EAAE,CAACuE,KAAK,CAACC,KAAK,CAACC,MAAM,CAAC,WAAW,EAAE,UAAU,EAAE7D,QAAQ,EAAE;IAAE8D,GAAG,EAAE,CAAE,WAAW,EAAE,WAAW;EAAG,CAAC,CAAC;EAE/F1E,EAAE,CAAC2E,IAAI,CAACH,KAAK,CAACI,KAAK,CAAC,SAAS,EAAE,cAAc,EAAExC,YAAY,CAAC;AAC9D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}