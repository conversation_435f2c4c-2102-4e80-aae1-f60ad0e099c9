{"ast": null, "code": "\"use strict\";\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nfunction _slicedToArray(r, e) {\n  return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();\n}\nfunction _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _iterableToArrayLimit(r, l) {\n  var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n  if (null != t) {\n    var e,\n      n,\n      i,\n      u,\n      a = [],\n      f = !0,\n      o = !1;\n    try {\n      if (i = (t = t.call(r)).next, 0 === l) {\n        if (Object(t) !== t) return;\n        f = !1;\n      } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n    } catch (r) {\n      o = !0, n = r;\n    } finally {\n      try {\n        if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return;\n      } finally {\n        if (o) throw n;\n      }\n    }\n    return a;\n  }\n}\nfunction _arrayWithHoles(r) {\n  if (Array.isArray(r)) return r;\n}\nfunction _toConsumableArray(r) {\n  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\nfunction _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return _arrayLikeToArray(r);\n}\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nvar _require = require(\"./doc-utils.js\"),\n  wordToUtf8 = _require.wordToUtf8;\nvar _require2 = require(\"./prefix-matcher.js\"),\n  match = _require2.match,\n  getValue = _require2.getValue,\n  getValues = _require2.getValues;\nfunction getMatchers(modules, options) {\n  var matchers = [];\n  for (var i = 0, l = modules.length; i < l; i++) {\n    var _module = modules[i];\n    if (_module.matchers) {\n      var mmm = _module.matchers(options);\n      if (!(mmm instanceof Array)) {\n        throw new Error(\"module matcher returns a non array\");\n      }\n      matchers.push.apply(matchers, _toConsumableArray(mmm));\n    }\n  }\n  return matchers;\n}\nfunction getMatches(matchers, placeHolderContent, options) {\n  var matches = [];\n  for (var i = 0, len = matchers.length; i < len; i++) {\n    var matcher = matchers[i];\n    var _matcher = _slicedToArray(matcher, 2),\n      prefix = _matcher[0],\n      _module2 = _matcher[1];\n    var properties = matcher[2] || {};\n    if (options.match(prefix, placeHolderContent)) {\n      var values = options.getValues(prefix, placeHolderContent);\n      if (typeof properties === \"function\") {\n        properties = properties(values);\n      }\n      if (!properties.value) {\n        var _values = _slicedToArray(values, 2);\n        properties.value = _values[1];\n      }\n      matches.push(_objectSpread({\n        type: \"placeholder\",\n        prefix: prefix,\n        module: _module2,\n        onMatch: properties.onMatch,\n        priority: properties.priority\n      }, properties));\n    }\n  }\n  return matches;\n}\nfunction moduleParse(placeHolderContent, options) {\n  var modules = options.modules;\n  var startOffset = options.startOffset;\n  var endLindex = options.lIndex;\n  var moduleParsed;\n  options.offset = startOffset;\n  options.match = match;\n  options.getValue = getValue;\n  options.getValues = getValues;\n  var matchers = getMatchers(modules, options);\n  var matches = getMatches(matchers, placeHolderContent, options);\n  if (matches.length > 0) {\n    var bestMatch = null;\n    matches.forEach(function (match) {\n      match.priority = match.priority || -match.value.length;\n      if (!bestMatch || match.priority > bestMatch.priority) {\n        bestMatch = match;\n      }\n    });\n    bestMatch.offset = startOffset;\n    delete bestMatch.priority;\n    bestMatch.endLindex = endLindex;\n    bestMatch.lIndex = endLindex;\n    bestMatch.raw = placeHolderContent;\n    if (bestMatch.onMatch) {\n      bestMatch.onMatch(bestMatch);\n    }\n    delete bestMatch.onMatch;\n    delete bestMatch.prefix;\n    return bestMatch;\n  }\n  for (var i = 0, l = modules.length; i < l; i++) {\n    var _module3 = modules[i];\n    moduleParsed = _module3.parse(placeHolderContent, options);\n    if (moduleParsed) {\n      moduleParsed.offset = startOffset;\n      moduleParsed.endLindex = endLindex;\n      moduleParsed.lIndex = endLindex;\n      moduleParsed.raw = placeHolderContent;\n      return moduleParsed;\n    }\n  }\n  return {\n    type: \"placeholder\",\n    value: placeHolderContent,\n    offset: startOffset,\n    endLindex: endLindex,\n    lIndex: endLindex\n  };\n}\nvar parser = {\n  preparse: function preparse(parsed, modules, options) {\n    function preparse(parsed, options) {\n      return modules.forEach(function (module) {\n        module.preparse(parsed, options);\n      });\n    }\n    return {\n      preparsed: preparse(parsed, options)\n    };\n  },\n  parse: function parse(lexed, modules, options) {\n    var inPlaceHolder = false;\n    var placeHolderContent = \"\";\n    var startOffset;\n    var tailParts = [];\n    var droppedTags = options.fileTypeConfig.droppedTagsInsidePlaceholder || [];\n    return lexed.reduce(function lexedToParsed(parsed, token) {\n      if (token.type === \"delimiter\") {\n        inPlaceHolder = token.position === \"start\";\n        if (token.position === \"end\") {\n          options.parse = function (placeHolderContent) {\n            return moduleParse(placeHolderContent, _objectSpread(_objectSpread(_objectSpread({}, options), token), {}, {\n              startOffset: startOffset,\n              modules: modules\n            }));\n          };\n          parsed.push(options.parse(wordToUtf8(placeHolderContent)));\n          Array.prototype.push.apply(parsed, tailParts);\n          tailParts = [];\n        }\n        if (token.position === \"start\") {\n          tailParts = [];\n          startOffset = token.offset;\n        }\n        placeHolderContent = \"\";\n        return parsed;\n      }\n      if (!inPlaceHolder) {\n        parsed.push(token);\n        return parsed;\n      }\n      if (token.type !== \"content\" || token.position !== \"insidetag\") {\n        if (droppedTags.indexOf(token.tag) !== -1) {\n          return parsed;\n        }\n        tailParts.push(token);\n        return parsed;\n      }\n      placeHolderContent += token.value;\n      return parsed;\n    }, []);\n  },\n  postparse: function postparse(postparsed, modules, options) {\n    function getTraits(traitName, postparsed) {\n      return modules.map(function (module) {\n        return module.getTraits(traitName, postparsed);\n      });\n    }\n    var errors = [];\n    function _postparse(postparsed, options) {\n      return modules.reduce(function (postparsed, module) {\n        var r = module.postparse(postparsed, _objectSpread(_objectSpread({}, options), {}, {\n          postparse: function postparse(parsed, opts) {\n            return _postparse(parsed, _objectSpread(_objectSpread({}, options), opts));\n          },\n          getTraits: getTraits\n        }));\n        if (r == null) {\n          return postparsed;\n        }\n        if (r.errors) {\n          Array.prototype.push.apply(errors, r.errors);\n          return r.postparsed;\n        }\n        return r;\n      }, postparsed);\n    }\n    return {\n      postparsed: _postparse(postparsed, options),\n      errors: errors\n    };\n  }\n};\nmodule.exports = parser;", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "ownKeys", "e", "r", "t", "Object", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "length", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "toPrimitive", "call", "TypeError", "String", "Number", "_slicedToArray", "_arrayWithHoles", "_iterableToArrayLimit", "_unsupportedIterableToArray", "_nonIterableRest", "l", "n", "u", "a", "f", "next", "done", "Array", "isArray", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "_nonIterableSpread", "_arrayLikeToArray", "toString", "slice", "name", "from", "test", "_require", "require", "wordToUtf8", "_require2", "match", "getValue", "getV<PERSON>ues", "getMatchers", "modules", "options", "matchers", "_module", "mmm", "Error", "getMatches", "placeHolderContent", "matches", "len", "matcher", "_matcher", "prefix", "_module2", "properties", "values", "_values", "type", "module", "onMatch", "priority", "moduleParse", "startOffset", "endLindex", "lIndex", "moduleParsed", "offset", "bestMatch", "raw", "_module3", "parse", "parser", "preparse", "parsed", "preparsed", "lexed", "inPlaceHolder", "tailParts", "droppedTags", "fileTypeConfig", "droppedTagsInsidePlaceholder", "reduce", "lexedToParsed", "token", "position", "indexOf", "tag", "postparse", "postparsed", "getTraits", "traitName", "map", "errors", "_postparse", "opts", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/docxtemplater@3.52.0/node_modules/docxtemplater/js/parser.js"], "sourcesContent": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t[\"return\"] && (u = t[\"return\"](), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nvar _require = require(\"./doc-utils.js\"),\n  wordToUtf8 = _require.wordToUtf8;\nvar _require2 = require(\"./prefix-matcher.js\"),\n  match = _require2.match,\n  getValue = _require2.getValue,\n  getValues = _require2.getValues;\nfunction getMatchers(modules, options) {\n  var matchers = [];\n  for (var i = 0, l = modules.length; i < l; i++) {\n    var _module = modules[i];\n    if (_module.matchers) {\n      var mmm = _module.matchers(options);\n      if (!(mmm instanceof Array)) {\n        throw new Error(\"module matcher returns a non array\");\n      }\n      matchers.push.apply(matchers, _toConsumableArray(mmm));\n    }\n  }\n  return matchers;\n}\nfunction getMatches(matchers, placeHolderContent, options) {\n  var matches = [];\n  for (var i = 0, len = matchers.length; i < len; i++) {\n    var matcher = matchers[i];\n    var _matcher = _slicedToArray(matcher, 2),\n      prefix = _matcher[0],\n      _module2 = _matcher[1];\n    var properties = matcher[2] || {};\n    if (options.match(prefix, placeHolderContent)) {\n      var values = options.getValues(prefix, placeHolderContent);\n      if (typeof properties === \"function\") {\n        properties = properties(values);\n      }\n      if (!properties.value) {\n        var _values = _slicedToArray(values, 2);\n        properties.value = _values[1];\n      }\n      matches.push(_objectSpread({\n        type: \"placeholder\",\n        prefix: prefix,\n        module: _module2,\n        onMatch: properties.onMatch,\n        priority: properties.priority\n      }, properties));\n    }\n  }\n  return matches;\n}\nfunction moduleParse(placeHolderContent, options) {\n  var modules = options.modules;\n  var startOffset = options.startOffset;\n  var endLindex = options.lIndex;\n  var moduleParsed;\n  options.offset = startOffset;\n  options.match = match;\n  options.getValue = getValue;\n  options.getValues = getValues;\n  var matchers = getMatchers(modules, options);\n  var matches = getMatches(matchers, placeHolderContent, options);\n  if (matches.length > 0) {\n    var bestMatch = null;\n    matches.forEach(function (match) {\n      match.priority = match.priority || -match.value.length;\n      if (!bestMatch || match.priority > bestMatch.priority) {\n        bestMatch = match;\n      }\n    });\n    bestMatch.offset = startOffset;\n    delete bestMatch.priority;\n    bestMatch.endLindex = endLindex;\n    bestMatch.lIndex = endLindex;\n    bestMatch.raw = placeHolderContent;\n    if (bestMatch.onMatch) {\n      bestMatch.onMatch(bestMatch);\n    }\n    delete bestMatch.onMatch;\n    delete bestMatch.prefix;\n    return bestMatch;\n  }\n  for (var i = 0, l = modules.length; i < l; i++) {\n    var _module3 = modules[i];\n    moduleParsed = _module3.parse(placeHolderContent, options);\n    if (moduleParsed) {\n      moduleParsed.offset = startOffset;\n      moduleParsed.endLindex = endLindex;\n      moduleParsed.lIndex = endLindex;\n      moduleParsed.raw = placeHolderContent;\n      return moduleParsed;\n    }\n  }\n  return {\n    type: \"placeholder\",\n    value: placeHolderContent,\n    offset: startOffset,\n    endLindex: endLindex,\n    lIndex: endLindex\n  };\n}\nvar parser = {\n  preparse: function preparse(parsed, modules, options) {\n    function preparse(parsed, options) {\n      return modules.forEach(function (module) {\n        module.preparse(parsed, options);\n      });\n    }\n    return {\n      preparsed: preparse(parsed, options)\n    };\n  },\n  parse: function parse(lexed, modules, options) {\n    var inPlaceHolder = false;\n    var placeHolderContent = \"\";\n    var startOffset;\n    var tailParts = [];\n    var droppedTags = options.fileTypeConfig.droppedTagsInsidePlaceholder || [];\n    return lexed.reduce(function lexedToParsed(parsed, token) {\n      if (token.type === \"delimiter\") {\n        inPlaceHolder = token.position === \"start\";\n        if (token.position === \"end\") {\n          options.parse = function (placeHolderContent) {\n            return moduleParse(placeHolderContent, _objectSpread(_objectSpread(_objectSpread({}, options), token), {}, {\n              startOffset: startOffset,\n              modules: modules\n            }));\n          };\n          parsed.push(options.parse(wordToUtf8(placeHolderContent)));\n          Array.prototype.push.apply(parsed, tailParts);\n          tailParts = [];\n        }\n        if (token.position === \"start\") {\n          tailParts = [];\n          startOffset = token.offset;\n        }\n        placeHolderContent = \"\";\n        return parsed;\n      }\n      if (!inPlaceHolder) {\n        parsed.push(token);\n        return parsed;\n      }\n      if (token.type !== \"content\" || token.position !== \"insidetag\") {\n        if (droppedTags.indexOf(token.tag) !== -1) {\n          return parsed;\n        }\n        tailParts.push(token);\n        return parsed;\n      }\n      placeHolderContent += token.value;\n      return parsed;\n    }, []);\n  },\n  postparse: function postparse(postparsed, modules, options) {\n    function getTraits(traitName, postparsed) {\n      return modules.map(function (module) {\n        return module.getTraits(traitName, postparsed);\n      });\n    }\n    var errors = [];\n    function _postparse(postparsed, options) {\n      return modules.reduce(function (postparsed, module) {\n        var r = module.postparse(postparsed, _objectSpread(_objectSpread({}, options), {}, {\n          postparse: function postparse(parsed, opts) {\n            return _postparse(parsed, _objectSpread(_objectSpread({}, options), opts));\n          },\n          getTraits: getTraits\n        }));\n        if (r == null) {\n          return postparsed;\n        }\n        if (r.errors) {\n          Array.prototype.push.apply(errors, r.errors);\n          return r.postparsed;\n        }\n        return r;\n      }, postparsed);\n    }\n    return {\n      postparsed: _postparse(postparsed, options),\n      errors: errors\n    };\n  }\n};\nmodule.exports = parser;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,OAAOA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAIC,CAAC,GAAGC,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAIX,CAAC,GAAGS,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAEC,CAAC,KAAKP,CAAC,GAAGA,CAAC,CAACY,MAAM,CAAC,UAAUL,CAAC,EAAE;MAAE,OAAOE,MAAM,CAACI,wBAAwB,CAACP,CAAC,EAAEC,CAAC,CAAC,CAACO,UAAU;IAAE,CAAC,CAAC,CAAC,EAAEN,CAAC,CAACO,IAAI,CAACC,KAAK,CAACR,CAAC,EAAER,CAAC,CAAC;EAAE;EAAE,OAAOQ,CAAC;AAAE;AAC9P,SAASS,aAAaA,CAACX,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGW,SAAS,CAACC,MAAM,EAAEZ,CAAC,EAAE,EAAE;IAAE,IAAIC,CAAC,GAAG,IAAI,IAAIU,SAAS,CAACX,CAAC,CAAC,GAAGW,SAAS,CAACX,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGF,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEc,eAAe,CAACf,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGE,MAAM,CAACa,yBAAyB,GAAGb,MAAM,CAACc,gBAAgB,CAACjB,CAAC,EAAEG,MAAM,CAACa,yBAAyB,CAACd,CAAC,CAAC,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACD,CAAC,CAAC,CAAC,CAACY,OAAO,CAAC,UAAUb,CAAC,EAAE;MAAEE,MAAM,CAACe,cAAc,CAAClB,CAAC,EAAEC,CAAC,EAAEE,MAAM,CAACI,wBAAwB,CAACL,CAAC,EAAED,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOD,CAAC;AAAE;AACtb,SAASe,eAAeA,CAACf,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAO,CAACD,CAAC,GAAGkB,cAAc,CAAClB,CAAC,CAAC,KAAKD,CAAC,GAAGG,MAAM,CAACe,cAAc,CAAClB,CAAC,EAAEC,CAAC,EAAE;IAAEmB,KAAK,EAAElB,CAAC;IAAEM,UAAU,EAAE,CAAC,CAAC;IAAEa,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGtB,CAAC,CAACC,CAAC,CAAC,GAAGC,CAAC,EAAEF,CAAC;AAAE;AACnL,SAASmB,cAAcA,CAACjB,CAAC,EAAE;EAAE,IAAIqB,CAAC,GAAGC,YAAY,CAACtB,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAIT,OAAO,CAAC8B,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASC,YAAYA,CAACtB,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIR,OAAO,CAACS,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACP,MAAM,CAAC8B,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKzB,CAAC,EAAE;IAAE,IAAIuB,CAAC,GAAGvB,CAAC,CAAC0B,IAAI,CAACxB,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIR,OAAO,CAAC8B,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAII,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAK1B,CAAC,GAAG2B,MAAM,GAAGC,MAAM,EAAE3B,CAAC,CAAC;AAAE;AAC3T,SAAS4B,cAAcA,CAAC7B,CAAC,EAAED,CAAC,EAAE;EAAE,OAAO+B,eAAe,CAAC9B,CAAC,CAAC,IAAI+B,qBAAqB,CAAC/B,CAAC,EAAED,CAAC,CAAC,IAAIiC,2BAA2B,CAAChC,CAAC,EAAED,CAAC,CAAC,IAAIkC,gBAAgB,CAAC,CAAC;AAAE;AACrJ,SAASA,gBAAgBA,CAAA,EAAG;EAAE,MAAM,IAAIP,SAAS,CAAC,2IAA2I,CAAC;AAAE;AAChM,SAASK,qBAAqBA,CAAC/B,CAAC,EAAEkC,CAAC,EAAE;EAAE,IAAIjC,CAAC,GAAG,IAAI,IAAID,CAAC,GAAG,IAAI,GAAG,WAAW,IAAI,OAAON,MAAM,IAAIM,CAAC,CAACN,MAAM,CAACC,QAAQ,CAAC,IAAIK,CAAC,CAAC,YAAY,CAAC;EAAE,IAAI,IAAI,IAAIC,CAAC,EAAE;IAAE,IAAIF,CAAC;MAAEoC,CAAC;MAAEb,CAAC;MAAEc,CAAC;MAAEC,CAAC,GAAG,EAAE;MAAEC,CAAC,GAAG,CAAC,CAAC;MAAE7C,CAAC,GAAG,CAAC,CAAC;IAAE,IAAI;MAAE,IAAI6B,CAAC,GAAG,CAACrB,CAAC,GAAGA,CAAC,CAACwB,IAAI,CAACzB,CAAC,CAAC,EAAEuC,IAAI,EAAE,CAAC,KAAKL,CAAC,EAAE;QAAE,IAAIhC,MAAM,CAACD,CAAC,CAAC,KAAKA,CAAC,EAAE;QAAQqC,CAAC,GAAG,CAAC,CAAC;MAAE,CAAC,MAAM,OAAO,EAAEA,CAAC,GAAG,CAACvC,CAAC,GAAGuB,CAAC,CAACG,IAAI,CAACxB,CAAC,CAAC,EAAEuC,IAAI,CAAC,KAAKH,CAAC,CAAC7B,IAAI,CAACT,CAAC,CAACoB,KAAK,CAAC,EAAEkB,CAAC,CAACzB,MAAM,KAAKsB,CAAC,CAAC,EAAEI,CAAC,GAAG,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,OAAOtC,CAAC,EAAE;MAAEP,CAAC,GAAG,CAAC,CAAC,EAAE0C,CAAC,GAAGnC,CAAC;IAAE,CAAC,SAAS;MAAE,IAAI;QAAE,IAAI,CAACsC,CAAC,IAAI,IAAI,IAAIrC,CAAC,CAAC,QAAQ,CAAC,KAAKmC,CAAC,GAAGnC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,EAAEC,MAAM,CAACkC,CAAC,CAAC,KAAKA,CAAC,CAAC,EAAE;MAAQ,CAAC,SAAS;QAAE,IAAI3C,CAAC,EAAE,MAAM0C,CAAC;MAAE;IAAE;IAAE,OAAOE,CAAC;EAAE;AAAE;AACzhB,SAASP,eAAeA,CAAC9B,CAAC,EAAE;EAAE,IAAIyC,KAAK,CAACC,OAAO,CAAC1C,CAAC,CAAC,EAAE,OAAOA,CAAC;AAAE;AAC9D,SAAS2C,kBAAkBA,CAAC3C,CAAC,EAAE;EAAE,OAAO4C,kBAAkB,CAAC5C,CAAC,CAAC,IAAI6C,gBAAgB,CAAC7C,CAAC,CAAC,IAAIgC,2BAA2B,CAAChC,CAAC,CAAC,IAAI8C,kBAAkB,CAAC,CAAC;AAAE;AAChJ,SAASA,kBAAkBA,CAAA,EAAG;EAAE,MAAM,IAAIpB,SAAS,CAAC,sIAAsI,CAAC;AAAE;AAC7L,SAASM,2BAA2BA,CAAChC,CAAC,EAAEqC,CAAC,EAAE;EAAE,IAAIrC,CAAC,EAAE;IAAE,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE,OAAO+C,iBAAiB,CAAC/C,CAAC,EAAEqC,CAAC,CAAC;IAAE,IAAIpC,CAAC,GAAG,CAAC,CAAC,CAAC+C,QAAQ,CAACvB,IAAI,CAACzB,CAAC,CAAC,CAACiD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAAE,OAAO,QAAQ,KAAKhD,CAAC,IAAID,CAAC,CAACJ,WAAW,KAAKK,CAAC,GAAGD,CAAC,CAACJ,WAAW,CAACsD,IAAI,CAAC,EAAE,KAAK,KAAKjD,CAAC,IAAI,KAAK,KAAKA,CAAC,GAAGwC,KAAK,CAACU,IAAI,CAACnD,CAAC,CAAC,GAAG,WAAW,KAAKC,CAAC,IAAI,0CAA0C,CAACmD,IAAI,CAACnD,CAAC,CAAC,GAAG8C,iBAAiB,CAAC/C,CAAC,EAAEqC,CAAC,CAAC,GAAG,KAAK,CAAC;EAAE;AAAE;AACzX,SAASQ,gBAAgBA,CAAC7C,CAAC,EAAE;EAAE,IAAI,WAAW,IAAI,OAAON,MAAM,IAAI,IAAI,IAAIM,CAAC,CAACN,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAIK,CAAC,CAAC,YAAY,CAAC,EAAE,OAAOyC,KAAK,CAACU,IAAI,CAACnD,CAAC,CAAC;AAAE;AAChJ,SAAS4C,kBAAkBA,CAAC5C,CAAC,EAAE;EAAE,IAAIyC,KAAK,CAACC,OAAO,CAAC1C,CAAC,CAAC,EAAE,OAAO+C,iBAAiB,CAAC/C,CAAC,CAAC;AAAE;AACpF,SAAS+C,iBAAiBA,CAAC/C,CAAC,EAAEqC,CAAC,EAAE;EAAE,CAAC,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAGrC,CAAC,CAACY,MAAM,MAAMyB,CAAC,GAAGrC,CAAC,CAACY,MAAM,CAAC;EAAE,KAAK,IAAIb,CAAC,GAAG,CAAC,EAAEoC,CAAC,GAAGM,KAAK,CAACJ,CAAC,CAAC,EAAEtC,CAAC,GAAGsC,CAAC,EAAEtC,CAAC,EAAE,EAAEoC,CAAC,CAACpC,CAAC,CAAC,GAAGC,CAAC,CAACD,CAAC,CAAC;EAAE,OAAOoC,CAAC;AAAE;AACnJ,IAAIkB,QAAQ,GAAGC,OAAO,CAAC,gBAAgB,CAAC;EACtCC,UAAU,GAAGF,QAAQ,CAACE,UAAU;AAClC,IAAIC,SAAS,GAAGF,OAAO,CAAC,qBAAqB,CAAC;EAC5CG,KAAK,GAAGD,SAAS,CAACC,KAAK;EACvBC,QAAQ,GAAGF,SAAS,CAACE,QAAQ;EAC7BC,SAAS,GAAGH,SAAS,CAACG,SAAS;AACjC,SAASC,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAE;EACrC,IAAIC,QAAQ,GAAG,EAAE;EACjB,KAAK,IAAIzC,CAAC,GAAG,CAAC,EAAEY,CAAC,GAAG2B,OAAO,CAACjD,MAAM,EAAEU,CAAC,GAAGY,CAAC,EAAEZ,CAAC,EAAE,EAAE;IAC9C,IAAI0C,OAAO,GAAGH,OAAO,CAACvC,CAAC,CAAC;IACxB,IAAI0C,OAAO,CAACD,QAAQ,EAAE;MACpB,IAAIE,GAAG,GAAGD,OAAO,CAACD,QAAQ,CAACD,OAAO,CAAC;MACnC,IAAI,EAAEG,GAAG,YAAYxB,KAAK,CAAC,EAAE;QAC3B,MAAM,IAAIyB,KAAK,CAAC,oCAAoC,CAAC;MACvD;MACAH,QAAQ,CAACvD,IAAI,CAACC,KAAK,CAACsD,QAAQ,EAAEpB,kBAAkB,CAACsB,GAAG,CAAC,CAAC;IACxD;EACF;EACA,OAAOF,QAAQ;AACjB;AACA,SAASI,UAAUA,CAACJ,QAAQ,EAAEK,kBAAkB,EAAEN,OAAO,EAAE;EACzD,IAAIO,OAAO,GAAG,EAAE;EAChB,KAAK,IAAI/C,CAAC,GAAG,CAAC,EAAEgD,GAAG,GAAGP,QAAQ,CAACnD,MAAM,EAAEU,CAAC,GAAGgD,GAAG,EAAEhD,CAAC,EAAE,EAAE;IACnD,IAAIiD,OAAO,GAAGR,QAAQ,CAACzC,CAAC,CAAC;IACzB,IAAIkD,QAAQ,GAAG3C,cAAc,CAAC0C,OAAO,EAAE,CAAC,CAAC;MACvCE,MAAM,GAAGD,QAAQ,CAAC,CAAC,CAAC;MACpBE,QAAQ,GAAGF,QAAQ,CAAC,CAAC,CAAC;IACxB,IAAIG,UAAU,GAAGJ,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IACjC,IAAIT,OAAO,CAACL,KAAK,CAACgB,MAAM,EAAEL,kBAAkB,CAAC,EAAE;MAC7C,IAAIQ,MAAM,GAAGd,OAAO,CAACH,SAAS,CAACc,MAAM,EAAEL,kBAAkB,CAAC;MAC1D,IAAI,OAAOO,UAAU,KAAK,UAAU,EAAE;QACpCA,UAAU,GAAGA,UAAU,CAACC,MAAM,CAAC;MACjC;MACA,IAAI,CAACD,UAAU,CAACxD,KAAK,EAAE;QACrB,IAAI0D,OAAO,GAAGhD,cAAc,CAAC+C,MAAM,EAAE,CAAC,CAAC;QACvCD,UAAU,CAACxD,KAAK,GAAG0D,OAAO,CAAC,CAAC,CAAC;MAC/B;MACAR,OAAO,CAAC7D,IAAI,CAACE,aAAa,CAAC;QACzBoE,IAAI,EAAE,aAAa;QACnBL,MAAM,EAAEA,MAAM;QACdM,MAAM,EAAEL,QAAQ;QAChBM,OAAO,EAAEL,UAAU,CAACK,OAAO;QAC3BC,QAAQ,EAAEN,UAAU,CAACM;MACvB,CAAC,EAAEN,UAAU,CAAC,CAAC;IACjB;EACF;EACA,OAAON,OAAO;AAChB;AACA,SAASa,WAAWA,CAACd,kBAAkB,EAAEN,OAAO,EAAE;EAChD,IAAID,OAAO,GAAGC,OAAO,CAACD,OAAO;EAC7B,IAAIsB,WAAW,GAAGrB,OAAO,CAACqB,WAAW;EACrC,IAAIC,SAAS,GAAGtB,OAAO,CAACuB,MAAM;EAC9B,IAAIC,YAAY;EAChBxB,OAAO,CAACyB,MAAM,GAAGJ,WAAW;EAC5BrB,OAAO,CAACL,KAAK,GAAGA,KAAK;EACrBK,OAAO,CAACJ,QAAQ,GAAGA,QAAQ;EAC3BI,OAAO,CAACH,SAAS,GAAGA,SAAS;EAC7B,IAAII,QAAQ,GAAGH,WAAW,CAACC,OAAO,EAAEC,OAAO,CAAC;EAC5C,IAAIO,OAAO,GAAGF,UAAU,CAACJ,QAAQ,EAAEK,kBAAkB,EAAEN,OAAO,CAAC;EAC/D,IAAIO,OAAO,CAACzD,MAAM,GAAG,CAAC,EAAE;IACtB,IAAI4E,SAAS,GAAG,IAAI;IACpBnB,OAAO,CAACxD,OAAO,CAAC,UAAU4C,KAAK,EAAE;MAC/BA,KAAK,CAACwB,QAAQ,GAAGxB,KAAK,CAACwB,QAAQ,IAAI,CAACxB,KAAK,CAACtC,KAAK,CAACP,MAAM;MACtD,IAAI,CAAC4E,SAAS,IAAI/B,KAAK,CAACwB,QAAQ,GAAGO,SAAS,CAACP,QAAQ,EAAE;QACrDO,SAAS,GAAG/B,KAAK;MACnB;IACF,CAAC,CAAC;IACF+B,SAAS,CAACD,MAAM,GAAGJ,WAAW;IAC9B,OAAOK,SAAS,CAACP,QAAQ;IACzBO,SAAS,CAACJ,SAAS,GAAGA,SAAS;IAC/BI,SAAS,CAACH,MAAM,GAAGD,SAAS;IAC5BI,SAAS,CAACC,GAAG,GAAGrB,kBAAkB;IAClC,IAAIoB,SAAS,CAACR,OAAO,EAAE;MACrBQ,SAAS,CAACR,OAAO,CAACQ,SAAS,CAAC;IAC9B;IACA,OAAOA,SAAS,CAACR,OAAO;IACxB,OAAOQ,SAAS,CAACf,MAAM;IACvB,OAAOe,SAAS;EAClB;EACA,KAAK,IAAIlE,CAAC,GAAG,CAAC,EAAEY,CAAC,GAAG2B,OAAO,CAACjD,MAAM,EAAEU,CAAC,GAAGY,CAAC,EAAEZ,CAAC,EAAE,EAAE;IAC9C,IAAIoE,QAAQ,GAAG7B,OAAO,CAACvC,CAAC,CAAC;IACzBgE,YAAY,GAAGI,QAAQ,CAACC,KAAK,CAACvB,kBAAkB,EAAEN,OAAO,CAAC;IAC1D,IAAIwB,YAAY,EAAE;MAChBA,YAAY,CAACC,MAAM,GAAGJ,WAAW;MACjCG,YAAY,CAACF,SAAS,GAAGA,SAAS;MAClCE,YAAY,CAACD,MAAM,GAAGD,SAAS;MAC/BE,YAAY,CAACG,GAAG,GAAGrB,kBAAkB;MACrC,OAAOkB,YAAY;IACrB;EACF;EACA,OAAO;IACLR,IAAI,EAAE,aAAa;IACnB3D,KAAK,EAAEiD,kBAAkB;IACzBmB,MAAM,EAAEJ,WAAW;IACnBC,SAAS,EAAEA,SAAS;IACpBC,MAAM,EAAED;EACV,CAAC;AACH;AACA,IAAIQ,MAAM,GAAG;EACXC,QAAQ,EAAE,SAASA,QAAQA,CAACC,MAAM,EAAEjC,OAAO,EAAEC,OAAO,EAAE;IACpD,SAAS+B,QAAQA,CAACC,MAAM,EAAEhC,OAAO,EAAE;MACjC,OAAOD,OAAO,CAAChD,OAAO,CAAC,UAAUkE,MAAM,EAAE;QACvCA,MAAM,CAACc,QAAQ,CAACC,MAAM,EAAEhC,OAAO,CAAC;MAClC,CAAC,CAAC;IACJ;IACA,OAAO;MACLiC,SAAS,EAAEF,QAAQ,CAACC,MAAM,EAAEhC,OAAO;IACrC,CAAC;EACH,CAAC;EACD6B,KAAK,EAAE,SAASA,KAAKA,CAACK,KAAK,EAAEnC,OAAO,EAAEC,OAAO,EAAE;IAC7C,IAAImC,aAAa,GAAG,KAAK;IACzB,IAAI7B,kBAAkB,GAAG,EAAE;IAC3B,IAAIe,WAAW;IACf,IAAIe,SAAS,GAAG,EAAE;IAClB,IAAIC,WAAW,GAAGrC,OAAO,CAACsC,cAAc,CAACC,4BAA4B,IAAI,EAAE;IAC3E,OAAOL,KAAK,CAACM,MAAM,CAAC,SAASC,aAAaA,CAACT,MAAM,EAAEU,KAAK,EAAE;MACxD,IAAIA,KAAK,CAAC1B,IAAI,KAAK,WAAW,EAAE;QAC9BmB,aAAa,GAAGO,KAAK,CAACC,QAAQ,KAAK,OAAO;QAC1C,IAAID,KAAK,CAACC,QAAQ,KAAK,KAAK,EAAE;UAC5B3C,OAAO,CAAC6B,KAAK,GAAG,UAAUvB,kBAAkB,EAAE;YAC5C,OAAOc,WAAW,CAACd,kBAAkB,EAAE1D,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoD,OAAO,CAAC,EAAE0C,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;cACzGrB,WAAW,EAAEA,WAAW;cACxBtB,OAAO,EAAEA;YACX,CAAC,CAAC,CAAC;UACL,CAAC;UACDiC,MAAM,CAACtF,IAAI,CAACsD,OAAO,CAAC6B,KAAK,CAACpC,UAAU,CAACa,kBAAkB,CAAC,CAAC,CAAC;UAC1D3B,KAAK,CAAC5C,SAAS,CAACW,IAAI,CAACC,KAAK,CAACqF,MAAM,EAAEI,SAAS,CAAC;UAC7CA,SAAS,GAAG,EAAE;QAChB;QACA,IAAIM,KAAK,CAACC,QAAQ,KAAK,OAAO,EAAE;UAC9BP,SAAS,GAAG,EAAE;UACdf,WAAW,GAAGqB,KAAK,CAACjB,MAAM;QAC5B;QACAnB,kBAAkB,GAAG,EAAE;QACvB,OAAO0B,MAAM;MACf;MACA,IAAI,CAACG,aAAa,EAAE;QAClBH,MAAM,CAACtF,IAAI,CAACgG,KAAK,CAAC;QAClB,OAAOV,MAAM;MACf;MACA,IAAIU,KAAK,CAAC1B,IAAI,KAAK,SAAS,IAAI0B,KAAK,CAACC,QAAQ,KAAK,WAAW,EAAE;QAC9D,IAAIN,WAAW,CAACO,OAAO,CAACF,KAAK,CAACG,GAAG,CAAC,KAAK,CAAC,CAAC,EAAE;UACzC,OAAOb,MAAM;QACf;QACAI,SAAS,CAAC1F,IAAI,CAACgG,KAAK,CAAC;QACrB,OAAOV,MAAM;MACf;MACA1B,kBAAkB,IAAIoC,KAAK,CAACrF,KAAK;MACjC,OAAO2E,MAAM;IACf,CAAC,EAAE,EAAE,CAAC;EACR,CAAC;EACDc,SAAS,EAAE,SAASA,SAASA,CAACC,UAAU,EAAEhD,OAAO,EAAEC,OAAO,EAAE;IAC1D,SAASgD,SAASA,CAACC,SAAS,EAAEF,UAAU,EAAE;MACxC,OAAOhD,OAAO,CAACmD,GAAG,CAAC,UAAUjC,MAAM,EAAE;QACnC,OAAOA,MAAM,CAAC+B,SAAS,CAACC,SAAS,EAAEF,UAAU,CAAC;MAChD,CAAC,CAAC;IACJ;IACA,IAAII,MAAM,GAAG,EAAE;IACf,SAASC,UAAUA,CAACL,UAAU,EAAE/C,OAAO,EAAE;MACvC,OAAOD,OAAO,CAACyC,MAAM,CAAC,UAAUO,UAAU,EAAE9B,MAAM,EAAE;QAClD,IAAI/E,CAAC,GAAG+E,MAAM,CAAC6B,SAAS,CAACC,UAAU,EAAEnG,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoD,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;UACjF8C,SAAS,EAAE,SAASA,SAASA,CAACd,MAAM,EAAEqB,IAAI,EAAE;YAC1C,OAAOD,UAAU,CAACpB,MAAM,EAAEpF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoD,OAAO,CAAC,EAAEqD,IAAI,CAAC,CAAC;UAC5E,CAAC;UACDL,SAAS,EAAEA;QACb,CAAC,CAAC,CAAC;QACH,IAAI9G,CAAC,IAAI,IAAI,EAAE;UACb,OAAO6G,UAAU;QACnB;QACA,IAAI7G,CAAC,CAACiH,MAAM,EAAE;UACZxE,KAAK,CAAC5C,SAAS,CAACW,IAAI,CAACC,KAAK,CAACwG,MAAM,EAAEjH,CAAC,CAACiH,MAAM,CAAC;UAC5C,OAAOjH,CAAC,CAAC6G,UAAU;QACrB;QACA,OAAO7G,CAAC;MACV,CAAC,EAAE6G,UAAU,CAAC;IAChB;IACA,OAAO;MACLA,UAAU,EAAEK,UAAU,CAACL,UAAU,EAAE/C,OAAO,CAAC;MAC3CmD,MAAM,EAAEA;IACV,CAAC;EACH;AACF,CAAC;AACDlC,MAAM,CAACqC,OAAO,GAAGxB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}