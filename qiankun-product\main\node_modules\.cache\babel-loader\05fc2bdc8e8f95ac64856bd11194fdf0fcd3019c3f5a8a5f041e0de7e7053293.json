{"ast": null, "code": "import copyArray from './_copyArray.js';\nimport isIndex from './_isIndex.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMin = Math.min;\n\n/**\n * Reorder `array` according to the specified indexes where the element at\n * the first index is assigned as the first element, the element at\n * the second index is assigned as the second element, and so on.\n *\n * @private\n * @param {Array} array The array to reorder.\n * @param {Array} indexes The arranged array indexes.\n * @returns {Array} Returns `array`.\n */\nfunction reorder(array, indexes) {\n  var arrLength = array.length,\n    length = nativeMin(indexes.length, arrLength),\n    oldArray = copyArray(array);\n  while (length--) {\n    var index = indexes[length];\n    array[length] = isIndex(index, arrLength) ? oldArray[index] : undefined;\n  }\n  return array;\n}\nexport default reorder;", "map": {"version": 3, "names": ["copyArray", "isIndex", "nativeMin", "Math", "min", "reorder", "array", "indexes", "arr<PERSON><PERSON><PERSON>", "length", "oldArray", "index", "undefined"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/_reorder.js"], "sourcesContent": ["import copyArray from './_copyArray.js';\nimport isIndex from './_isIndex.js';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMin = Math.min;\n\n/**\n * Reorder `array` according to the specified indexes where the element at\n * the first index is assigned as the first element, the element at\n * the second index is assigned as the second element, and so on.\n *\n * @private\n * @param {Array} array The array to reorder.\n * @param {Array} indexes The arranged array indexes.\n * @returns {Array} Returns `array`.\n */\nfunction reorder(array, indexes) {\n  var arrLength = array.length,\n      length = nativeMin(indexes.length, arrLength),\n      oldArray = copyArray(array);\n\n  while (length--) {\n    var index = indexes[length];\n    array[length] = isIndex(index, arrLength) ? oldArray[index] : undefined;\n  }\n  return array;\n}\n\nexport default reorder;\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,iBAAiB;AACvC,OAAOC,OAAO,MAAM,eAAe;;AAEnC;AACA,IAAIC,SAAS,GAAGC,IAAI,CAACC,GAAG;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAC/B,IAAIC,SAAS,GAAGF,KAAK,CAACG,MAAM;IACxBA,MAAM,GAAGP,SAAS,CAACK,OAAO,CAACE,MAAM,EAAED,SAAS,CAAC;IAC7CE,QAAQ,GAAGV,SAAS,CAACM,KAAK,CAAC;EAE/B,OAAOG,MAAM,EAAE,EAAE;IACf,IAAIE,KAAK,GAAGJ,OAAO,CAACE,MAAM,CAAC;IAC3BH,KAAK,CAACG,MAAM,CAAC,GAAGR,OAAO,CAACU,KAAK,EAAEH,SAAS,CAAC,GAAGE,QAAQ,CAACC,KAAK,CAAC,GAAGC,SAAS;EACzE;EACA,OAAON,KAAK;AACd;AAEA,eAAeD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}