{"ast": null, "code": "import _isFunction from \"lodash/isFunction\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n/**\n * <AUTHOR>\n * @since 2019-10-21\n */\nimport { execScripts } from 'import-html-entry';\nimport { frameworkConfiguration } from '../../../apis';\nimport { qiankunHeadTagName } from '../../../utils';\nimport { cachedGlobals } from '../../proxySandbox';\nimport * as css from '../css';\nvar SCRIPT_TAG_NAME = 'SCRIPT';\nvar LINK_TAG_NAME = 'LINK';\nvar STYLE_TAG_NAME = 'STYLE';\nexport var styleElementTargetSymbol = Symbol('target');\nexport var styleElementRefNodeNo = Symbol('refNodeNo');\nvar overwrittenSymbol = Symbol('qiankun-overwritten');\nexport var getAppWrapperHeadElement = function getAppWrapperHeadElement(appWrapper) {\n  return appWrapper.querySelector(qiankunHeadTagName);\n};\nexport function isExecutableScriptType(script) {\n  return !script.type || ['text/javascript', 'module', 'application/javascript', 'text/ecmascript', 'application/ecmascript'].indexOf(script.type) !== -1;\n}\nexport function isHijackingTag(tagName) {\n  return (tagName === null || tagName === void 0 ? void 0 : tagName.toUpperCase()) === LINK_TAG_NAME || (tagName === null || tagName === void 0 ? void 0 : tagName.toUpperCase()) === STYLE_TAG_NAME || (tagName === null || tagName === void 0 ? void 0 : tagName.toUpperCase()) === SCRIPT_TAG_NAME;\n}\n/**\n * Check if a style element is a styled-component liked.\n * A styled-components liked element is which not have textContext but keep the rules in its styleSheet.cssRules.\n * Such as the style element generated by styled-components and emotion.\n * @param element\n */\nexport function isStyledComponentsLike(element) {\n  var _element$sheet, _getStyledElementCSSR;\n  return !element.textContent && (((_element$sheet = element.sheet) === null || _element$sheet === void 0 ? void 0 : _element$sheet.cssRules.length) || ((_getStyledElementCSSR = getStyledElementCSSRules(element)) === null || _getStyledElementCSSR === void 0 ? void 0 : _getStyledElementCSSR.length));\n}\nvar appsCounterMap = new Map();\nexport function calcAppCount(appName, calcType, status) {\n  var appCount = appsCounterMap.get(appName) || {\n    bootstrappingPatchCount: 0,\n    mountingPatchCount: 0\n  };\n  switch (calcType) {\n    case 'increase':\n      appCount[\"\".concat(status, \"PatchCount\")] += 1;\n      break;\n    case 'decrease':\n      // bootstrap patch just called once but its freer will be called multiple times\n      if (appCount[\"\".concat(status, \"PatchCount\")] > 0) {\n        appCount[\"\".concat(status, \"PatchCount\")] -= 1;\n      }\n      break;\n  }\n  appsCounterMap.set(appName, appCount);\n}\nexport function isAllAppsUnmounted() {\n  return Array.from(appsCounterMap.entries()).every(function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      _ref2$ = _ref2[1],\n      bpc = _ref2$.bootstrappingPatchCount,\n      mpc = _ref2$.mountingPatchCount;\n    return bpc === 0 && mpc === 0;\n  });\n}\nfunction patchCustomEvent(e, elementGetter) {\n  Object.defineProperties(e, {\n    srcElement: {\n      get: elementGetter\n    },\n    target: {\n      get: elementGetter\n    }\n  });\n  return e;\n}\nfunction manualInvokeElementOnLoad(element) {\n  // we need to invoke the onload event manually to notify the event listener that the script was completed\n  // here are the two typical ways of dynamic script loading\n  // 1. element.onload callback way, which webpack and loadjs used, see https://github.com/muicss/loadjs/blob/master/src/loadjs.js#L138\n  // 2. addEventListener way, which toast-loader used, see https://github.com/pyrsmk/toast/blob/master/src/Toast.ts#L64\n  var loadEvent = new CustomEvent('load');\n  var patchedEvent = patchCustomEvent(loadEvent, function () {\n    return element;\n  });\n  if (_isFunction(element.onload)) {\n    element.onload(patchedEvent);\n  } else {\n    element.dispatchEvent(patchedEvent);\n  }\n}\nfunction manualInvokeElementOnError(element) {\n  var errorEvent = new CustomEvent('error');\n  var patchedEvent = patchCustomEvent(errorEvent, function () {\n    return element;\n  });\n  if (_isFunction(element.onerror)) {\n    element.onerror(patchedEvent);\n  } else {\n    element.dispatchEvent(patchedEvent);\n  }\n}\nfunction convertLinkAsStyle(element, postProcess) {\n  var fetchFn = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : fetch;\n  var styleElement = document.createElement('style');\n  var href = element.href;\n  // add source link element href\n  styleElement.dataset.qiankunHref = href;\n  fetchFn(href).then(function (res) {\n    return res.text();\n  }).then(function (styleContext) {\n    styleElement.appendChild(document.createTextNode(styleContext));\n    postProcess(styleElement);\n    manualInvokeElementOnLoad(element);\n  }).catch(function () {\n    return manualInvokeElementOnError(element);\n  });\n  return styleElement;\n}\nvar defineNonEnumerableProperty = function defineNonEnumerableProperty(target, key, value) {\n  Object.defineProperty(target, key, {\n    configurable: true,\n    enumerable: false,\n    writable: true,\n    value: value\n  });\n};\nvar styledComponentCSSRulesMap = new WeakMap();\nvar dynamicScriptAttachedCommentMap = new WeakMap();\nvar dynamicLinkAttachedInlineStyleMap = new WeakMap();\nexport function recordStyledComponentsCSSRules(styleElements) {\n  styleElements.forEach(function (styleElement) {\n    /*\n     With a styled-components generated style element, we need to record its cssRules for restore next re-mounting time.\n     We're doing this because the sheet of style element is going to be cleaned automatically by browser after the style element dom removed from document.\n     see https://www.w3.org/TR/cssom-1/#associated-css-style-sheet\n     */\n    if (styleElement instanceof HTMLStyleElement && isStyledComponentsLike(styleElement)) {\n      if (styleElement.sheet) {\n        // record the original css rules of the style element for restore\n        styledComponentCSSRulesMap.set(styleElement, styleElement.sheet.cssRules);\n      }\n    }\n  });\n}\nexport function getStyledElementCSSRules(styledElement) {\n  return styledComponentCSSRulesMap.get(styledElement);\n}\nfunction getOverwrittenAppendChildOrInsertBefore(opts) {\n  function appendChildOrInsertBefore(newChild) {\n    var refChild = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    var element = newChild;\n    var rawDOMAppendOrInsertBefore = opts.rawDOMAppendOrInsertBefore,\n      isInvokedByMicroApp = opts.isInvokedByMicroApp,\n      containerConfigGetter = opts.containerConfigGetter,\n      _opts$target = opts.target,\n      target = _opts$target === void 0 ? 'body' : _opts$target;\n    if (!isHijackingTag(element.tagName) || !isInvokedByMicroApp(element)) {\n      return rawDOMAppendOrInsertBefore.call(this, element, refChild);\n    }\n    if (element.tagName) {\n      var containerConfig = containerConfigGetter(element);\n      var appName = containerConfig.appName,\n        appWrapperGetter = containerConfig.appWrapperGetter,\n        proxy = containerConfig.proxy,\n        strictGlobal = containerConfig.strictGlobal,\n        speedySandbox = containerConfig.speedySandbox,\n        dynamicStyleSheetElements = containerConfig.dynamicStyleSheetElements,\n        scopedCSS = containerConfig.scopedCSS,\n        excludeAssetFilter = containerConfig.excludeAssetFilter;\n      switch (element.tagName) {\n        case LINK_TAG_NAME:\n        case STYLE_TAG_NAME:\n          {\n            var stylesheetElement = newChild;\n            var _stylesheetElement = stylesheetElement,\n              href = _stylesheetElement.href;\n            if (excludeAssetFilter && href && excludeAssetFilter(href)) {\n              return rawDOMAppendOrInsertBefore.call(this, element, refChild);\n            }\n            defineNonEnumerableProperty(stylesheetElement, styleElementTargetSymbol, target);\n            var appWrapper = appWrapperGetter();\n            if (scopedCSS) {\n              var _element$tagName;\n              // exclude link elements like <link rel=\"icon\" href=\"favicon.ico\">\n              var linkElementUsingStylesheet = ((_element$tagName = element.tagName) === null || _element$tagName === void 0 ? void 0 : _element$tagName.toUpperCase()) === LINK_TAG_NAME && element.rel === 'stylesheet' && element.href;\n              if (linkElementUsingStylesheet) {\n                var _frameworkConfigurati;\n                var _fetch = typeof frameworkConfiguration.fetch === 'function' ? frameworkConfiguration.fetch : (_frameworkConfigurati = frameworkConfiguration.fetch) === null || _frameworkConfigurati === void 0 ? void 0 : _frameworkConfigurati.fn;\n                stylesheetElement = convertLinkAsStyle(element, function (styleElement) {\n                  return css.process(appWrapper, styleElement, appName);\n                }, _fetch);\n                dynamicLinkAttachedInlineStyleMap.set(element, stylesheetElement);\n              } else {\n                css.process(appWrapper, stylesheetElement, appName);\n              }\n            }\n            var mountDOM = target === 'head' ? getAppWrapperHeadElement(appWrapper) : appWrapper;\n            var referenceNode = mountDOM.contains(refChild) ? refChild : null;\n            var refNo;\n            if (referenceNode) {\n              refNo = Array.from(mountDOM.childNodes).indexOf(referenceNode);\n            }\n            var result = rawDOMAppendOrInsertBefore.call(mountDOM, stylesheetElement, referenceNode);\n            // record refNo thus we can keep order while remounting\n            if (typeof refNo === 'number' && refNo !== -1) {\n              defineNonEnumerableProperty(stylesheetElement, styleElementRefNodeNo, refNo);\n            }\n            // record dynamic style elements after insert succeed\n            dynamicStyleSheetElements.push(stylesheetElement);\n            return result;\n          }\n        case SCRIPT_TAG_NAME:\n          {\n            var _element = element,\n              src = _element.src,\n              text = _element.text;\n            // some script like jsonp maybe not support cors which shouldn't use execScripts\n            if (excludeAssetFilter && src && excludeAssetFilter(src) || !isExecutableScriptType(element)) {\n              return rawDOMAppendOrInsertBefore.call(this, element, refChild);\n            }\n            var _appWrapper = appWrapperGetter();\n            var _mountDOM = target === 'head' ? getAppWrapperHeadElement(_appWrapper) : _appWrapper;\n            var _fetch2 = frameworkConfiguration.fetch;\n            var _referenceNode = _mountDOM.contains(refChild) ? refChild : null;\n            var scopedGlobalVariables = speedySandbox ? cachedGlobals : [];\n            if (src) {\n              var isRedfinedCurrentScript = false;\n              execScripts(null, [src], proxy, {\n                fetch: _fetch2,\n                strictGlobal: strictGlobal,\n                scopedGlobalVariables: scopedGlobalVariables,\n                beforeExec: function beforeExec() {\n                  var isCurrentScriptConfigurable = function isCurrentScriptConfigurable() {\n                    var descriptor = Object.getOwnPropertyDescriptor(document, 'currentScript');\n                    return !descriptor || descriptor.configurable;\n                  };\n                  if (isCurrentScriptConfigurable()) {\n                    Object.defineProperty(document, 'currentScript', {\n                      get: function get() {\n                        return element;\n                      },\n                      configurable: true\n                    });\n                    isRedfinedCurrentScript = true;\n                  }\n                },\n                success: function success() {\n                  manualInvokeElementOnLoad(element);\n                  if (isRedfinedCurrentScript) {\n                    // @ts-ignore\n                    delete document.currentScript;\n                  }\n                  element = null;\n                },\n                error: function error() {\n                  manualInvokeElementOnError(element);\n                  if (isRedfinedCurrentScript) {\n                    // @ts-ignore\n                    delete document.currentScript;\n                  }\n                  element = null;\n                }\n              });\n              var dynamicScriptCommentElement = document.createComment(\"dynamic script \".concat(src, \" replaced by qiankun\"));\n              dynamicScriptAttachedCommentMap.set(element, dynamicScriptCommentElement);\n              return rawDOMAppendOrInsertBefore.call(_mountDOM, dynamicScriptCommentElement, _referenceNode);\n            }\n            // inline script never trigger the onload and onerror event\n            execScripts(null, [\"<script>\".concat(text, \"</script>\")], proxy, {\n              strictGlobal: strictGlobal,\n              scopedGlobalVariables: scopedGlobalVariables\n            });\n            var dynamicInlineScriptCommentElement = document.createComment('dynamic inline script replaced by qiankun');\n            dynamicScriptAttachedCommentMap.set(element, dynamicInlineScriptCommentElement);\n            return rawDOMAppendOrInsertBefore.call(_mountDOM, dynamicInlineScriptCommentElement, _referenceNode);\n          }\n        default:\n          break;\n      }\n    }\n    return rawDOMAppendOrInsertBefore.call(this, element, refChild);\n  }\n  appendChildOrInsertBefore[overwrittenSymbol] = true;\n  return appendChildOrInsertBefore;\n}\nfunction getNewRemoveChild(rawRemoveChild, containerConfigGetter, target, isInvokedByMicroApp) {\n  function removeChild(child) {\n    var tagName = child.tagName;\n    if (!isHijackingTag(tagName) || !isInvokedByMicroApp(child)) return rawRemoveChild.call(this, child);\n    try {\n      var attachedElement;\n      var _containerConfigGette = containerConfigGetter(child),\n        appWrapperGetter = _containerConfigGette.appWrapperGetter,\n        dynamicStyleSheetElements = _containerConfigGette.dynamicStyleSheetElements;\n      switch (tagName) {\n        case STYLE_TAG_NAME:\n        case LINK_TAG_NAME:\n          {\n            attachedElement = dynamicLinkAttachedInlineStyleMap.get(child) || child;\n            // try to remove the dynamic style sheet\n            var dynamicElementIndex = dynamicStyleSheetElements.indexOf(attachedElement);\n            if (dynamicElementIndex !== -1) {\n              dynamicStyleSheetElements.splice(dynamicElementIndex, 1);\n            }\n            break;\n          }\n        case SCRIPT_TAG_NAME:\n          {\n            attachedElement = dynamicScriptAttachedCommentMap.get(child) || child;\n            break;\n          }\n        default:\n          {\n            attachedElement = child;\n          }\n      }\n      var appWrapper = appWrapperGetter();\n      var container = target === 'head' ? getAppWrapperHeadElement(appWrapper) : appWrapper;\n      // container might have been removed while app unmounting if the removeChild action was async\n      if (container.contains(attachedElement)) {\n        return rawRemoveChild.call(attachedElement.parentNode, attachedElement);\n      }\n    } catch (e) {\n      console.warn(e);\n    }\n    return rawRemoveChild.call(this, child);\n  }\n  removeChild[overwrittenSymbol] = true;\n  return removeChild;\n}\nexport function patchHTMLDynamicAppendPrototypeFunctions(isInvokedByMicroApp, containerConfigGetter) {\n  var rawHeadAppendChild = HTMLHeadElement.prototype.appendChild;\n  var rawBodyAppendChild = HTMLBodyElement.prototype.appendChild;\n  var rawHeadInsertBefore = HTMLHeadElement.prototype.insertBefore;\n  // Just overwrite it while it have not been overwritten\n  if (rawHeadAppendChild[overwrittenSymbol] !== true && rawBodyAppendChild[overwrittenSymbol] !== true && rawHeadInsertBefore[overwrittenSymbol] !== true) {\n    HTMLHeadElement.prototype.appendChild = getOverwrittenAppendChildOrInsertBefore({\n      rawDOMAppendOrInsertBefore: rawHeadAppendChild,\n      containerConfigGetter: containerConfigGetter,\n      isInvokedByMicroApp: isInvokedByMicroApp,\n      target: 'head'\n    });\n    HTMLBodyElement.prototype.appendChild = getOverwrittenAppendChildOrInsertBefore({\n      rawDOMAppendOrInsertBefore: rawBodyAppendChild,\n      containerConfigGetter: containerConfigGetter,\n      isInvokedByMicroApp: isInvokedByMicroApp,\n      target: 'body'\n    });\n    HTMLHeadElement.prototype.insertBefore = getOverwrittenAppendChildOrInsertBefore({\n      rawDOMAppendOrInsertBefore: rawHeadInsertBefore,\n      containerConfigGetter: containerConfigGetter,\n      isInvokedByMicroApp: isInvokedByMicroApp,\n      target: 'head'\n    });\n  }\n  var rawHeadRemoveChild = HTMLHeadElement.prototype.removeChild;\n  var rawBodyRemoveChild = HTMLBodyElement.prototype.removeChild;\n  // Just overwrite it while it have not been overwritten\n  if (rawHeadRemoveChild[overwrittenSymbol] !== true && rawBodyRemoveChild[overwrittenSymbol] !== true) {\n    HTMLHeadElement.prototype.removeChild = getNewRemoveChild(rawHeadRemoveChild, containerConfigGetter, 'head', isInvokedByMicroApp);\n    HTMLBodyElement.prototype.removeChild = getNewRemoveChild(rawBodyRemoveChild, containerConfigGetter, 'body', isInvokedByMicroApp);\n  }\n  return function unpatch() {\n    HTMLHeadElement.prototype.appendChild = rawHeadAppendChild;\n    HTMLHeadElement.prototype.removeChild = rawHeadRemoveChild;\n    HTMLBodyElement.prototype.appendChild = rawBodyAppendChild;\n    HTMLBodyElement.prototype.removeChild = rawBodyRemoveChild;\n    HTMLHeadElement.prototype.insertBefore = rawHeadInsertBefore;\n  };\n}\nexport function rebuildCSSRules(styleSheetElements, reAppendElement) {\n  styleSheetElements.forEach(function (stylesheetElement) {\n    // re-append the dynamic stylesheet to sub-app container\n    var appendSuccess = reAppendElement(stylesheetElement);\n    if (appendSuccess) {\n      /*\n      get the stored css rules from styled-components generated element, and the re-insert rules for them.\n      note that we must do this after style element had been added to document, which stylesheet would be associated to the document automatically.\n      check the spec https://www.w3.org/TR/cssom-1/#associated-css-style-sheet\n       */\n      if (stylesheetElement instanceof HTMLStyleElement && isStyledComponentsLike(stylesheetElement)) {\n        var cssRules = getStyledElementCSSRules(stylesheetElement);\n        if (cssRules) {\n          // eslint-disable-next-line no-plusplus\n          for (var i = 0; i < cssRules.length; i++) {\n            var cssRule = cssRules[i];\n            var cssStyleSheetElement = stylesheetElement.sheet;\n            cssStyleSheetElement.insertRule(cssRule.cssText, cssStyleSheetElement.cssRules.length);\n          }\n        }\n      }\n    }\n  });\n}", "map": {"version": 3, "names": ["_isFunction", "_slicedToArray", "execScripts", "frameworkConfiguration", "qiankunHeadTagName", "cachedGlobals", "css", "SCRIPT_TAG_NAME", "LINK_TAG_NAME", "STYLE_TAG_NAME", "styleElementTargetSymbol", "Symbol", "styleElementRefNodeNo", "overwrittenSymbol", "getAppWrapperHeadElement", "appWrapper", "querySelector", "isExecutableScriptType", "script", "type", "indexOf", "isHijackingTag", "tagName", "toUpperCase", "isStyledComponentsLike", "element", "_element$sheet", "_getStyledElementCSSR", "textContent", "sheet", "cssRules", "length", "getStyledElementCSSRules", "appsCounterMap", "Map", "calcAppCount", "appName", "calcType", "status", "appCount", "get", "bootstrappingPatchCount", "mountingPatchCount", "concat", "set", "isAllAppsUnmounted", "Array", "from", "entries", "every", "_ref", "_ref2", "_ref2$", "bpc", "mpc", "patchCustomEvent", "e", "elementGetter", "Object", "defineProperties", "srcElement", "target", "manualInvokeElementOnLoad", "loadEvent", "CustomEvent", "patchedEvent", "onload", "dispatchEvent", "manualInvokeElementOnError", "errorEvent", "onerror", "convertLinkAsStyle", "postProcess", "fetchFn", "arguments", "undefined", "fetch", "styleElement", "document", "createElement", "href", "dataset", "qiankunHref", "then", "res", "text", "styleContext", "append<PERSON><PERSON><PERSON>", "createTextNode", "catch", "defineNonEnumerableProperty", "key", "value", "defineProperty", "configurable", "enumerable", "writable", "styledComponentCSSRulesMap", "WeakMap", "dynamicScriptAttachedCommentMap", "dynamicLinkAttachedInlineStyleMap", "recordStyledComponentsCSSRules", "styleElements", "for<PERSON>ach", "HTMLStyleElement", "styledElement", "getOverwrittenAppendChildOrInsertBefore", "opts", "appendChildOrInsertBefore", "<PERSON><PERSON><PERSON><PERSON>", "refChild", "rawDOMAppendOrInsertBefore", "isInvokedByMicroApp", "containerConfigGetter", "_opts$target", "call", "containerConfig", "appWrapperGetter", "proxy", "strictGlobal", "speedySandbox", "dynamicStyleSheetElements", "scopedCSS", "excludeAssetFilter", "stylesheetElement", "_stylesheetElement", "_element$tagName", "linkElementUsingStylesheet", "rel", "_frameworkConfigurati", "_fetch", "fn", "process", "mountDOM", "referenceNode", "contains", "refNo", "childNodes", "result", "push", "_element", "src", "_appWrapper", "_mountDOM", "_fetch2", "_referenceNode", "scopedGlobalVariables", "isRedfinedCurrentScript", "beforeExec", "isCurrentScriptConfigurable", "descriptor", "getOwnPropertyDescriptor", "success", "currentScript", "error", "dynamicScriptCommentElement", "createComment", "dynamicInlineScriptCommentElement", "getNewRemoveChild", "rawRemoveChild", "<PERSON><PERSON><PERSON><PERSON>", "child", "attachedElement", "_containerConfigGette", "dynamicElementIndex", "splice", "container", "parentNode", "console", "warn", "patchHTMLDynamicAppendPrototypeFunctions", "rawHeadAppendChild", "HTMLHeadElement", "prototype", "rawBodyAppendChild", "HTMLBodyElement", "rawHeadInsertBefore", "insertBefore", "rawHeadRemoveChild", "rawBodyRemoveChild", "unpatch", "rebuildCSSRules", "styleSheetElements", "reAppendElement", "appendSuccess", "i", "cssRule", "cssStyleSheetElement", "insertRule", "cssText"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/qiankun@2.10.16/node_modules/qiankun/es/sandbox/patchers/dynamicAppend/common.js"], "sourcesContent": ["import _isFunction from \"lodash/isFunction\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\n/**\n * <AUTHOR>\n * @since 2019-10-21\n */\nimport { execScripts } from 'import-html-entry';\nimport { frameworkConfiguration } from '../../../apis';\nimport { qiankunHeadTagName } from '../../../utils';\nimport { cachedGlobals } from '../../proxySandbox';\nimport * as css from '../css';\nvar SCRIPT_TAG_NAME = 'SCRIPT';\nvar LINK_TAG_NAME = 'LINK';\nvar STYLE_TAG_NAME = 'STYLE';\nexport var styleElementTargetSymbol = Symbol('target');\nexport var styleElementRefNodeNo = Symbol('refNodeNo');\nvar overwrittenSymbol = Symbol('qiankun-overwritten');\nexport var getAppWrapperHeadElement = function getAppWrapperHeadElement(appWrapper) {\n  return appWrapper.querySelector(qiankunHeadTagName);\n};\nexport function isExecutableScriptType(script) {\n  return !script.type || ['text/javascript', 'module', 'application/javascript', 'text/ecmascript', 'application/ecmascript'].indexOf(script.type) !== -1;\n}\nexport function isHijackingTag(tagName) {\n  return (tagName === null || tagName === void 0 ? void 0 : tagName.toUpperCase()) === LINK_TAG_NAME || (tagName === null || tagName === void 0 ? void 0 : tagName.toUpperCase()) === STYLE_TAG_NAME || (tagName === null || tagName === void 0 ? void 0 : tagName.toUpperCase()) === SCRIPT_TAG_NAME;\n}\n/**\n * Check if a style element is a styled-component liked.\n * A styled-components liked element is which not have textContext but keep the rules in its styleSheet.cssRules.\n * Such as the style element generated by styled-components and emotion.\n * @param element\n */\nexport function isStyledComponentsLike(element) {\n  var _element$sheet, _getStyledElementCSSR;\n  return !element.textContent && (((_element$sheet = element.sheet) === null || _element$sheet === void 0 ? void 0 : _element$sheet.cssRules.length) || ((_getStyledElementCSSR = getStyledElementCSSRules(element)) === null || _getStyledElementCSSR === void 0 ? void 0 : _getStyledElementCSSR.length));\n}\nvar appsCounterMap = new Map();\nexport function calcAppCount(appName, calcType, status) {\n  var appCount = appsCounterMap.get(appName) || {\n    bootstrappingPatchCount: 0,\n    mountingPatchCount: 0\n  };\n  switch (calcType) {\n    case 'increase':\n      appCount[\"\".concat(status, \"PatchCount\")] += 1;\n      break;\n    case 'decrease':\n      // bootstrap patch just called once but its freer will be called multiple times\n      if (appCount[\"\".concat(status, \"PatchCount\")] > 0) {\n        appCount[\"\".concat(status, \"PatchCount\")] -= 1;\n      }\n      break;\n  }\n  appsCounterMap.set(appName, appCount);\n}\nexport function isAllAppsUnmounted() {\n  return Array.from(appsCounterMap.entries()).every(function (_ref) {\n    var _ref2 = _slicedToArray(_ref, 2),\n      _ref2$ = _ref2[1],\n      bpc = _ref2$.bootstrappingPatchCount,\n      mpc = _ref2$.mountingPatchCount;\n    return bpc === 0 && mpc === 0;\n  });\n}\nfunction patchCustomEvent(e, elementGetter) {\n  Object.defineProperties(e, {\n    srcElement: {\n      get: elementGetter\n    },\n    target: {\n      get: elementGetter\n    }\n  });\n  return e;\n}\nfunction manualInvokeElementOnLoad(element) {\n  // we need to invoke the onload event manually to notify the event listener that the script was completed\n  // here are the two typical ways of dynamic script loading\n  // 1. element.onload callback way, which webpack and loadjs used, see https://github.com/muicss/loadjs/blob/master/src/loadjs.js#L138\n  // 2. addEventListener way, which toast-loader used, see https://github.com/pyrsmk/toast/blob/master/src/Toast.ts#L64\n  var loadEvent = new CustomEvent('load');\n  var patchedEvent = patchCustomEvent(loadEvent, function () {\n    return element;\n  });\n  if (_isFunction(element.onload)) {\n    element.onload(patchedEvent);\n  } else {\n    element.dispatchEvent(patchedEvent);\n  }\n}\nfunction manualInvokeElementOnError(element) {\n  var errorEvent = new CustomEvent('error');\n  var patchedEvent = patchCustomEvent(errorEvent, function () {\n    return element;\n  });\n  if (_isFunction(element.onerror)) {\n    element.onerror(patchedEvent);\n  } else {\n    element.dispatchEvent(patchedEvent);\n  }\n}\nfunction convertLinkAsStyle(element, postProcess) {\n  var fetchFn = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : fetch;\n  var styleElement = document.createElement('style');\n  var href = element.href;\n  // add source link element href\n  styleElement.dataset.qiankunHref = href;\n  fetchFn(href).then(function (res) {\n    return res.text();\n  }).then(function (styleContext) {\n    styleElement.appendChild(document.createTextNode(styleContext));\n    postProcess(styleElement);\n    manualInvokeElementOnLoad(element);\n  }).catch(function () {\n    return manualInvokeElementOnError(element);\n  });\n  return styleElement;\n}\nvar defineNonEnumerableProperty = function defineNonEnumerableProperty(target, key, value) {\n  Object.defineProperty(target, key, {\n    configurable: true,\n    enumerable: false,\n    writable: true,\n    value: value\n  });\n};\nvar styledComponentCSSRulesMap = new WeakMap();\nvar dynamicScriptAttachedCommentMap = new WeakMap();\nvar dynamicLinkAttachedInlineStyleMap = new WeakMap();\nexport function recordStyledComponentsCSSRules(styleElements) {\n  styleElements.forEach(function (styleElement) {\n    /*\n     With a styled-components generated style element, we need to record its cssRules for restore next re-mounting time.\n     We're doing this because the sheet of style element is going to be cleaned automatically by browser after the style element dom removed from document.\n     see https://www.w3.org/TR/cssom-1/#associated-css-style-sheet\n     */\n    if (styleElement instanceof HTMLStyleElement && isStyledComponentsLike(styleElement)) {\n      if (styleElement.sheet) {\n        // record the original css rules of the style element for restore\n        styledComponentCSSRulesMap.set(styleElement, styleElement.sheet.cssRules);\n      }\n    }\n  });\n}\nexport function getStyledElementCSSRules(styledElement) {\n  return styledComponentCSSRulesMap.get(styledElement);\n}\nfunction getOverwrittenAppendChildOrInsertBefore(opts) {\n  function appendChildOrInsertBefore(newChild) {\n    var refChild = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : null;\n    var element = newChild;\n    var rawDOMAppendOrInsertBefore = opts.rawDOMAppendOrInsertBefore,\n      isInvokedByMicroApp = opts.isInvokedByMicroApp,\n      containerConfigGetter = opts.containerConfigGetter,\n      _opts$target = opts.target,\n      target = _opts$target === void 0 ? 'body' : _opts$target;\n    if (!isHijackingTag(element.tagName) || !isInvokedByMicroApp(element)) {\n      return rawDOMAppendOrInsertBefore.call(this, element, refChild);\n    }\n    if (element.tagName) {\n      var containerConfig = containerConfigGetter(element);\n      var appName = containerConfig.appName,\n        appWrapperGetter = containerConfig.appWrapperGetter,\n        proxy = containerConfig.proxy,\n        strictGlobal = containerConfig.strictGlobal,\n        speedySandbox = containerConfig.speedySandbox,\n        dynamicStyleSheetElements = containerConfig.dynamicStyleSheetElements,\n        scopedCSS = containerConfig.scopedCSS,\n        excludeAssetFilter = containerConfig.excludeAssetFilter;\n      switch (element.tagName) {\n        case LINK_TAG_NAME:\n        case STYLE_TAG_NAME:\n          {\n            var stylesheetElement = newChild;\n            var _stylesheetElement = stylesheetElement,\n              href = _stylesheetElement.href;\n            if (excludeAssetFilter && href && excludeAssetFilter(href)) {\n              return rawDOMAppendOrInsertBefore.call(this, element, refChild);\n            }\n            defineNonEnumerableProperty(stylesheetElement, styleElementTargetSymbol, target);\n            var appWrapper = appWrapperGetter();\n            if (scopedCSS) {\n              var _element$tagName;\n              // exclude link elements like <link rel=\"icon\" href=\"favicon.ico\">\n              var linkElementUsingStylesheet = ((_element$tagName = element.tagName) === null || _element$tagName === void 0 ? void 0 : _element$tagName.toUpperCase()) === LINK_TAG_NAME && element.rel === 'stylesheet' && element.href;\n              if (linkElementUsingStylesheet) {\n                var _frameworkConfigurati;\n                var _fetch = typeof frameworkConfiguration.fetch === 'function' ? frameworkConfiguration.fetch : (_frameworkConfigurati = frameworkConfiguration.fetch) === null || _frameworkConfigurati === void 0 ? void 0 : _frameworkConfigurati.fn;\n                stylesheetElement = convertLinkAsStyle(element, function (styleElement) {\n                  return css.process(appWrapper, styleElement, appName);\n                }, _fetch);\n                dynamicLinkAttachedInlineStyleMap.set(element, stylesheetElement);\n              } else {\n                css.process(appWrapper, stylesheetElement, appName);\n              }\n            }\n            var mountDOM = target === 'head' ? getAppWrapperHeadElement(appWrapper) : appWrapper;\n            var referenceNode = mountDOM.contains(refChild) ? refChild : null;\n            var refNo;\n            if (referenceNode) {\n              refNo = Array.from(mountDOM.childNodes).indexOf(referenceNode);\n            }\n            var result = rawDOMAppendOrInsertBefore.call(mountDOM, stylesheetElement, referenceNode);\n            // record refNo thus we can keep order while remounting\n            if (typeof refNo === 'number' && refNo !== -1) {\n              defineNonEnumerableProperty(stylesheetElement, styleElementRefNodeNo, refNo);\n            }\n            // record dynamic style elements after insert succeed\n            dynamicStyleSheetElements.push(stylesheetElement);\n            return result;\n          }\n        case SCRIPT_TAG_NAME:\n          {\n            var _element = element,\n              src = _element.src,\n              text = _element.text;\n            // some script like jsonp maybe not support cors which shouldn't use execScripts\n            if (excludeAssetFilter && src && excludeAssetFilter(src) || !isExecutableScriptType(element)) {\n              return rawDOMAppendOrInsertBefore.call(this, element, refChild);\n            }\n            var _appWrapper = appWrapperGetter();\n            var _mountDOM = target === 'head' ? getAppWrapperHeadElement(_appWrapper) : _appWrapper;\n            var _fetch2 = frameworkConfiguration.fetch;\n            var _referenceNode = _mountDOM.contains(refChild) ? refChild : null;\n            var scopedGlobalVariables = speedySandbox ? cachedGlobals : [];\n            if (src) {\n              var isRedfinedCurrentScript = false;\n              execScripts(null, [src], proxy, {\n                fetch: _fetch2,\n                strictGlobal: strictGlobal,\n                scopedGlobalVariables: scopedGlobalVariables,\n                beforeExec: function beforeExec() {\n                  var isCurrentScriptConfigurable = function isCurrentScriptConfigurable() {\n                    var descriptor = Object.getOwnPropertyDescriptor(document, 'currentScript');\n                    return !descriptor || descriptor.configurable;\n                  };\n                  if (isCurrentScriptConfigurable()) {\n                    Object.defineProperty(document, 'currentScript', {\n                      get: function get() {\n                        return element;\n                      },\n                      configurable: true\n                    });\n                    isRedfinedCurrentScript = true;\n                  }\n                },\n                success: function success() {\n                  manualInvokeElementOnLoad(element);\n                  if (isRedfinedCurrentScript) {\n                    // @ts-ignore\n                    delete document.currentScript;\n                  }\n                  element = null;\n                },\n                error: function error() {\n                  manualInvokeElementOnError(element);\n                  if (isRedfinedCurrentScript) {\n                    // @ts-ignore\n                    delete document.currentScript;\n                  }\n                  element = null;\n                }\n              });\n              var dynamicScriptCommentElement = document.createComment(\"dynamic script \".concat(src, \" replaced by qiankun\"));\n              dynamicScriptAttachedCommentMap.set(element, dynamicScriptCommentElement);\n              return rawDOMAppendOrInsertBefore.call(_mountDOM, dynamicScriptCommentElement, _referenceNode);\n            }\n            // inline script never trigger the onload and onerror event\n            execScripts(null, [\"<script>\".concat(text, \"</script>\")], proxy, {\n              strictGlobal: strictGlobal,\n              scopedGlobalVariables: scopedGlobalVariables\n            });\n            var dynamicInlineScriptCommentElement = document.createComment('dynamic inline script replaced by qiankun');\n            dynamicScriptAttachedCommentMap.set(element, dynamicInlineScriptCommentElement);\n            return rawDOMAppendOrInsertBefore.call(_mountDOM, dynamicInlineScriptCommentElement, _referenceNode);\n          }\n        default:\n          break;\n      }\n    }\n    return rawDOMAppendOrInsertBefore.call(this, element, refChild);\n  }\n  appendChildOrInsertBefore[overwrittenSymbol] = true;\n  return appendChildOrInsertBefore;\n}\nfunction getNewRemoveChild(rawRemoveChild, containerConfigGetter, target, isInvokedByMicroApp) {\n  function removeChild(child) {\n    var tagName = child.tagName;\n    if (!isHijackingTag(tagName) || !isInvokedByMicroApp(child)) return rawRemoveChild.call(this, child);\n    try {\n      var attachedElement;\n      var _containerConfigGette = containerConfigGetter(child),\n        appWrapperGetter = _containerConfigGette.appWrapperGetter,\n        dynamicStyleSheetElements = _containerConfigGette.dynamicStyleSheetElements;\n      switch (tagName) {\n        case STYLE_TAG_NAME:\n        case LINK_TAG_NAME:\n          {\n            attachedElement = dynamicLinkAttachedInlineStyleMap.get(child) || child;\n            // try to remove the dynamic style sheet\n            var dynamicElementIndex = dynamicStyleSheetElements.indexOf(attachedElement);\n            if (dynamicElementIndex !== -1) {\n              dynamicStyleSheetElements.splice(dynamicElementIndex, 1);\n            }\n            break;\n          }\n        case SCRIPT_TAG_NAME:\n          {\n            attachedElement = dynamicScriptAttachedCommentMap.get(child) || child;\n            break;\n          }\n        default:\n          {\n            attachedElement = child;\n          }\n      }\n      var appWrapper = appWrapperGetter();\n      var container = target === 'head' ? getAppWrapperHeadElement(appWrapper) : appWrapper;\n      // container might have been removed while app unmounting if the removeChild action was async\n      if (container.contains(attachedElement)) {\n        return rawRemoveChild.call(attachedElement.parentNode, attachedElement);\n      }\n    } catch (e) {\n      console.warn(e);\n    }\n    return rawRemoveChild.call(this, child);\n  }\n  removeChild[overwrittenSymbol] = true;\n  return removeChild;\n}\nexport function patchHTMLDynamicAppendPrototypeFunctions(isInvokedByMicroApp, containerConfigGetter) {\n  var rawHeadAppendChild = HTMLHeadElement.prototype.appendChild;\n  var rawBodyAppendChild = HTMLBodyElement.prototype.appendChild;\n  var rawHeadInsertBefore = HTMLHeadElement.prototype.insertBefore;\n  // Just overwrite it while it have not been overwritten\n  if (rawHeadAppendChild[overwrittenSymbol] !== true && rawBodyAppendChild[overwrittenSymbol] !== true && rawHeadInsertBefore[overwrittenSymbol] !== true) {\n    HTMLHeadElement.prototype.appendChild = getOverwrittenAppendChildOrInsertBefore({\n      rawDOMAppendOrInsertBefore: rawHeadAppendChild,\n      containerConfigGetter: containerConfigGetter,\n      isInvokedByMicroApp: isInvokedByMicroApp,\n      target: 'head'\n    });\n    HTMLBodyElement.prototype.appendChild = getOverwrittenAppendChildOrInsertBefore({\n      rawDOMAppendOrInsertBefore: rawBodyAppendChild,\n      containerConfigGetter: containerConfigGetter,\n      isInvokedByMicroApp: isInvokedByMicroApp,\n      target: 'body'\n    });\n    HTMLHeadElement.prototype.insertBefore = getOverwrittenAppendChildOrInsertBefore({\n      rawDOMAppendOrInsertBefore: rawHeadInsertBefore,\n      containerConfigGetter: containerConfigGetter,\n      isInvokedByMicroApp: isInvokedByMicroApp,\n      target: 'head'\n    });\n  }\n  var rawHeadRemoveChild = HTMLHeadElement.prototype.removeChild;\n  var rawBodyRemoveChild = HTMLBodyElement.prototype.removeChild;\n  // Just overwrite it while it have not been overwritten\n  if (rawHeadRemoveChild[overwrittenSymbol] !== true && rawBodyRemoveChild[overwrittenSymbol] !== true) {\n    HTMLHeadElement.prototype.removeChild = getNewRemoveChild(rawHeadRemoveChild, containerConfigGetter, 'head', isInvokedByMicroApp);\n    HTMLBodyElement.prototype.removeChild = getNewRemoveChild(rawBodyRemoveChild, containerConfigGetter, 'body', isInvokedByMicroApp);\n  }\n  return function unpatch() {\n    HTMLHeadElement.prototype.appendChild = rawHeadAppendChild;\n    HTMLHeadElement.prototype.removeChild = rawHeadRemoveChild;\n    HTMLBodyElement.prototype.appendChild = rawBodyAppendChild;\n    HTMLBodyElement.prototype.removeChild = rawBodyRemoveChild;\n    HTMLHeadElement.prototype.insertBefore = rawHeadInsertBefore;\n  };\n}\nexport function rebuildCSSRules(styleSheetElements, reAppendElement) {\n  styleSheetElements.forEach(function (stylesheetElement) {\n    // re-append the dynamic stylesheet to sub-app container\n    var appendSuccess = reAppendElement(stylesheetElement);\n    if (appendSuccess) {\n      /*\n      get the stored css rules from styled-components generated element, and the re-insert rules for them.\n      note that we must do this after style element had been added to document, which stylesheet would be associated to the document automatically.\n      check the spec https://www.w3.org/TR/cssom-1/#associated-css-style-sheet\n       */\n      if (stylesheetElement instanceof HTMLStyleElement && isStyledComponentsLike(stylesheetElement)) {\n        var cssRules = getStyledElementCSSRules(stylesheetElement);\n        if (cssRules) {\n          // eslint-disable-next-line no-plusplus\n          for (var i = 0; i < cssRules.length; i++) {\n            var cssRule = cssRules[i];\n            var cssStyleSheetElement = stylesheetElement.sheet;\n            cssStyleSheetElement.insertRule(cssRule.cssText, cssStyleSheetElement.cssRules.length);\n          }\n        }\n      }\n    }\n  });\n}"], "mappings": "AAAA,OAAOA,WAAW,MAAM,mBAAmB;AAC3C,OAAOC,cAAc,MAAM,0CAA0C;AACrE;AACA;AACA;AACA;AACA,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,sBAAsB,QAAQ,eAAe;AACtD,SAASC,kBAAkB,QAAQ,gBAAgB;AACnD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAO,KAAKC,GAAG,MAAM,QAAQ;AAC7B,IAAIC,eAAe,GAAG,QAAQ;AAC9B,IAAIC,aAAa,GAAG,MAAM;AAC1B,IAAIC,cAAc,GAAG,OAAO;AAC5B,OAAO,IAAIC,wBAAwB,GAAGC,MAAM,CAAC,QAAQ,CAAC;AACtD,OAAO,IAAIC,qBAAqB,GAAGD,MAAM,CAAC,WAAW,CAAC;AACtD,IAAIE,iBAAiB,GAAGF,MAAM,CAAC,qBAAqB,CAAC;AACrD,OAAO,IAAIG,wBAAwB,GAAG,SAASA,wBAAwBA,CAACC,UAAU,EAAE;EAClF,OAAOA,UAAU,CAACC,aAAa,CAACZ,kBAAkB,CAAC;AACrD,CAAC;AACD,OAAO,SAASa,sBAAsBA,CAACC,MAAM,EAAE;EAC7C,OAAO,CAACA,MAAM,CAACC,IAAI,IAAI,CAAC,iBAAiB,EAAE,QAAQ,EAAE,wBAAwB,EAAE,iBAAiB,EAAE,wBAAwB,CAAC,CAACC,OAAO,CAACF,MAAM,CAACC,IAAI,CAAC,KAAK,CAAC,CAAC;AACzJ;AACA,OAAO,SAASE,cAAcA,CAACC,OAAO,EAAE;EACtC,OAAO,CAACA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,WAAW,CAAC,CAAC,MAAMf,aAAa,IAAI,CAACc,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,WAAW,CAAC,CAAC,MAAMd,cAAc,IAAI,CAACa,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,WAAW,CAAC,CAAC,MAAMhB,eAAe;AACrS;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASiB,sBAAsBA,CAACC,OAAO,EAAE;EAC9C,IAAIC,cAAc,EAAEC,qBAAqB;EACzC,OAAO,CAACF,OAAO,CAACG,WAAW,KAAK,CAAC,CAACF,cAAc,GAAGD,OAAO,CAACI,KAAK,MAAM,IAAI,IAAIH,cAAc,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACI,QAAQ,CAACC,MAAM,MAAM,CAACJ,qBAAqB,GAAGK,wBAAwB,CAACP,OAAO,CAAC,MAAM,IAAI,IAAIE,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACI,MAAM,CAAC,CAAC;AAC3S;AACA,IAAIE,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC9B,OAAO,SAASC,YAAYA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,EAAE;EACtD,IAAIC,QAAQ,GAAGN,cAAc,CAACO,GAAG,CAACJ,OAAO,CAAC,IAAI;IAC5CK,uBAAuB,EAAE,CAAC;IAC1BC,kBAAkB,EAAE;EACtB,CAAC;EACD,QAAQL,QAAQ;IACd,KAAK,UAAU;MACbE,QAAQ,CAAC,EAAE,CAACI,MAAM,CAACL,MAAM,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC;MAC9C;IACF,KAAK,UAAU;MACb;MACA,IAAIC,QAAQ,CAAC,EAAE,CAACI,MAAM,CAACL,MAAM,EAAE,YAAY,CAAC,CAAC,GAAG,CAAC,EAAE;QACjDC,QAAQ,CAAC,EAAE,CAACI,MAAM,CAACL,MAAM,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC;MAChD;MACA;EACJ;EACAL,cAAc,CAACW,GAAG,CAACR,OAAO,EAAEG,QAAQ,CAAC;AACvC;AACA,OAAO,SAASM,kBAAkBA,CAAA,EAAG;EACnC,OAAOC,KAAK,CAACC,IAAI,CAACd,cAAc,CAACe,OAAO,CAAC,CAAC,CAAC,CAACC,KAAK,CAAC,UAAUC,IAAI,EAAE;IAChE,IAAIC,KAAK,GAAGlD,cAAc,CAACiD,IAAI,EAAE,CAAC,CAAC;MACjCE,MAAM,GAAGD,KAAK,CAAC,CAAC,CAAC;MACjBE,GAAG,GAAGD,MAAM,CAACX,uBAAuB;MACpCa,GAAG,GAAGF,MAAM,CAACV,kBAAkB;IACjC,OAAOW,GAAG,KAAK,CAAC,IAAIC,GAAG,KAAK,CAAC;EAC/B,CAAC,CAAC;AACJ;AACA,SAASC,gBAAgBA,CAACC,CAAC,EAAEC,aAAa,EAAE;EAC1CC,MAAM,CAACC,gBAAgB,CAACH,CAAC,EAAE;IACzBI,UAAU,EAAE;MACVpB,GAAG,EAAEiB;IACP,CAAC;IACDI,MAAM,EAAE;MACNrB,GAAG,EAAEiB;IACP;EACF,CAAC,CAAC;EACF,OAAOD,CAAC;AACV;AACA,SAASM,yBAAyBA,CAACrC,OAAO,EAAE;EAC1C;EACA;EACA;EACA;EACA,IAAIsC,SAAS,GAAG,IAAIC,WAAW,CAAC,MAAM,CAAC;EACvC,IAAIC,YAAY,GAAGV,gBAAgB,CAACQ,SAAS,EAAE,YAAY;IACzD,OAAOtC,OAAO;EAChB,CAAC,CAAC;EACF,IAAIzB,WAAW,CAACyB,OAAO,CAACyC,MAAM,CAAC,EAAE;IAC/BzC,OAAO,CAACyC,MAAM,CAACD,YAAY,CAAC;EAC9B,CAAC,MAAM;IACLxC,OAAO,CAAC0C,aAAa,CAACF,YAAY,CAAC;EACrC;AACF;AACA,SAASG,0BAA0BA,CAAC3C,OAAO,EAAE;EAC3C,IAAI4C,UAAU,GAAG,IAAIL,WAAW,CAAC,OAAO,CAAC;EACzC,IAAIC,YAAY,GAAGV,gBAAgB,CAACc,UAAU,EAAE,YAAY;IAC1D,OAAO5C,OAAO;EAChB,CAAC,CAAC;EACF,IAAIzB,WAAW,CAACyB,OAAO,CAAC6C,OAAO,CAAC,EAAE;IAChC7C,OAAO,CAAC6C,OAAO,CAACL,YAAY,CAAC;EAC/B,CAAC,MAAM;IACLxC,OAAO,CAAC0C,aAAa,CAACF,YAAY,CAAC;EACrC;AACF;AACA,SAASM,kBAAkBA,CAAC9C,OAAO,EAAE+C,WAAW,EAAE;EAChD,IAAIC,OAAO,GAAGC,SAAS,CAAC3C,MAAM,GAAG,CAAC,IAAI2C,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAGE,KAAK;EACvF,IAAIC,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;EAClD,IAAIC,IAAI,GAAGvD,OAAO,CAACuD,IAAI;EACvB;EACAH,YAAY,CAACI,OAAO,CAACC,WAAW,GAAGF,IAAI;EACvCP,OAAO,CAACO,IAAI,CAAC,CAACG,IAAI,CAAC,UAAUC,GAAG,EAAE;IAChC,OAAOA,GAAG,CAACC,IAAI,CAAC,CAAC;EACnB,CAAC,CAAC,CAACF,IAAI,CAAC,UAAUG,YAAY,EAAE;IAC9BT,YAAY,CAACU,WAAW,CAACT,QAAQ,CAACU,cAAc,CAACF,YAAY,CAAC,CAAC;IAC/Dd,WAAW,CAACK,YAAY,CAAC;IACzBf,yBAAyB,CAACrC,OAAO,CAAC;EACpC,CAAC,CAAC,CAACgE,KAAK,CAAC,YAAY;IACnB,OAAOrB,0BAA0B,CAAC3C,OAAO,CAAC;EAC5C,CAAC,CAAC;EACF,OAAOoD,YAAY;AACrB;AACA,IAAIa,2BAA2B,GAAG,SAASA,2BAA2BA,CAAC7B,MAAM,EAAE8B,GAAG,EAAEC,KAAK,EAAE;EACzFlC,MAAM,CAACmC,cAAc,CAAChC,MAAM,EAAE8B,GAAG,EAAE;IACjCG,YAAY,EAAE,IAAI;IAClBC,UAAU,EAAE,KAAK;IACjBC,QAAQ,EAAE,IAAI;IACdJ,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ,CAAC;AACD,IAAIK,0BAA0B,GAAG,IAAIC,OAAO,CAAC,CAAC;AAC9C,IAAIC,+BAA+B,GAAG,IAAID,OAAO,CAAC,CAAC;AACnD,IAAIE,iCAAiC,GAAG,IAAIF,OAAO,CAAC,CAAC;AACrD,OAAO,SAASG,8BAA8BA,CAACC,aAAa,EAAE;EAC5DA,aAAa,CAACC,OAAO,CAAC,UAAU1B,YAAY,EAAE;IAC5C;AACJ;AACA;AACA;AACA;IACI,IAAIA,YAAY,YAAY2B,gBAAgB,IAAIhF,sBAAsB,CAACqD,YAAY,CAAC,EAAE;MACpF,IAAIA,YAAY,CAAChD,KAAK,EAAE;QACtB;QACAoE,0BAA0B,CAACrD,GAAG,CAACiC,YAAY,EAAEA,YAAY,CAAChD,KAAK,CAACC,QAAQ,CAAC;MAC3E;IACF;EACF,CAAC,CAAC;AACJ;AACA,OAAO,SAASE,wBAAwBA,CAACyE,aAAa,EAAE;EACtD,OAAOR,0BAA0B,CAACzD,GAAG,CAACiE,aAAa,CAAC;AACtD;AACA,SAASC,uCAAuCA,CAACC,IAAI,EAAE;EACrD,SAASC,yBAAyBA,CAACC,QAAQ,EAAE;IAC3C,IAAIC,QAAQ,GAAGpC,SAAS,CAAC3C,MAAM,GAAG,CAAC,IAAI2C,SAAS,CAAC,CAAC,CAAC,KAAKC,SAAS,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;IACvF,IAAIjD,OAAO,GAAGoF,QAAQ;IACtB,IAAIE,0BAA0B,GAAGJ,IAAI,CAACI,0BAA0B;MAC9DC,mBAAmB,GAAGL,IAAI,CAACK,mBAAmB;MAC9CC,qBAAqB,GAAGN,IAAI,CAACM,qBAAqB;MAClDC,YAAY,GAAGP,IAAI,CAAC9C,MAAM;MAC1BA,MAAM,GAAGqD,YAAY,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,YAAY;IAC1D,IAAI,CAAC7F,cAAc,CAACI,OAAO,CAACH,OAAO,CAAC,IAAI,CAAC0F,mBAAmB,CAACvF,OAAO,CAAC,EAAE;MACrE,OAAOsF,0BAA0B,CAACI,IAAI,CAAC,IAAI,EAAE1F,OAAO,EAAEqF,QAAQ,CAAC;IACjE;IACA,IAAIrF,OAAO,CAACH,OAAO,EAAE;MACnB,IAAI8F,eAAe,GAAGH,qBAAqB,CAACxF,OAAO,CAAC;MACpD,IAAIW,OAAO,GAAGgF,eAAe,CAAChF,OAAO;QACnCiF,gBAAgB,GAAGD,eAAe,CAACC,gBAAgB;QACnDC,KAAK,GAAGF,eAAe,CAACE,KAAK;QAC7BC,YAAY,GAAGH,eAAe,CAACG,YAAY;QAC3CC,aAAa,GAAGJ,eAAe,CAACI,aAAa;QAC7CC,yBAAyB,GAAGL,eAAe,CAACK,yBAAyB;QACrEC,SAAS,GAAGN,eAAe,CAACM,SAAS;QACrCC,kBAAkB,GAAGP,eAAe,CAACO,kBAAkB;MACzD,QAAQlG,OAAO,CAACH,OAAO;QACrB,KAAKd,aAAa;QAClB,KAAKC,cAAc;UACjB;YACE,IAAImH,iBAAiB,GAAGf,QAAQ;YAChC,IAAIgB,kBAAkB,GAAGD,iBAAiB;cACxC5C,IAAI,GAAG6C,kBAAkB,CAAC7C,IAAI;YAChC,IAAI2C,kBAAkB,IAAI3C,IAAI,IAAI2C,kBAAkB,CAAC3C,IAAI,CAAC,EAAE;cAC1D,OAAO+B,0BAA0B,CAACI,IAAI,CAAC,IAAI,EAAE1F,OAAO,EAAEqF,QAAQ,CAAC;YACjE;YACApB,2BAA2B,CAACkC,iBAAiB,EAAElH,wBAAwB,EAAEmD,MAAM,CAAC;YAChF,IAAI9C,UAAU,GAAGsG,gBAAgB,CAAC,CAAC;YACnC,IAAIK,SAAS,EAAE;cACb,IAAII,gBAAgB;cACpB;cACA,IAAIC,0BAA0B,GAAG,CAAC,CAACD,gBAAgB,GAAGrG,OAAO,CAACH,OAAO,MAAM,IAAI,IAAIwG,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,gBAAgB,CAACvG,WAAW,CAAC,CAAC,MAAMf,aAAa,IAAIiB,OAAO,CAACuG,GAAG,KAAK,YAAY,IAAIvG,OAAO,CAACuD,IAAI;cAC3N,IAAI+C,0BAA0B,EAAE;gBAC9B,IAAIE,qBAAqB;gBACzB,IAAIC,MAAM,GAAG,OAAO/H,sBAAsB,CAACyE,KAAK,KAAK,UAAU,GAAGzE,sBAAsB,CAACyE,KAAK,GAAG,CAACqD,qBAAqB,GAAG9H,sBAAsB,CAACyE,KAAK,MAAM,IAAI,IAAIqD,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACE,EAAE;gBACxOP,iBAAiB,GAAGrD,kBAAkB,CAAC9C,OAAO,EAAE,UAAUoD,YAAY,EAAE;kBACtE,OAAOvE,GAAG,CAAC8H,OAAO,CAACrH,UAAU,EAAE8D,YAAY,EAAEzC,OAAO,CAAC;gBACvD,CAAC,EAAE8F,MAAM,CAAC;gBACV9B,iCAAiC,CAACxD,GAAG,CAACnB,OAAO,EAAEmG,iBAAiB,CAAC;cACnE,CAAC,MAAM;gBACLtH,GAAG,CAAC8H,OAAO,CAACrH,UAAU,EAAE6G,iBAAiB,EAAExF,OAAO,CAAC;cACrD;YACF;YACA,IAAIiG,QAAQ,GAAGxE,MAAM,KAAK,MAAM,GAAG/C,wBAAwB,CAACC,UAAU,CAAC,GAAGA,UAAU;YACpF,IAAIuH,aAAa,GAAGD,QAAQ,CAACE,QAAQ,CAACzB,QAAQ,CAAC,GAAGA,QAAQ,GAAG,IAAI;YACjE,IAAI0B,KAAK;YACT,IAAIF,aAAa,EAAE;cACjBE,KAAK,GAAG1F,KAAK,CAACC,IAAI,CAACsF,QAAQ,CAACI,UAAU,CAAC,CAACrH,OAAO,CAACkH,aAAa,CAAC;YAChE;YACA,IAAII,MAAM,GAAG3B,0BAA0B,CAACI,IAAI,CAACkB,QAAQ,EAAET,iBAAiB,EAAEU,aAAa,CAAC;YACxF;YACA,IAAI,OAAOE,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,CAAC,CAAC,EAAE;cAC7C9C,2BAA2B,CAACkC,iBAAiB,EAAEhH,qBAAqB,EAAE4H,KAAK,CAAC;YAC9E;YACA;YACAf,yBAAyB,CAACkB,IAAI,CAACf,iBAAiB,CAAC;YACjD,OAAOc,MAAM;UACf;QACF,KAAKnI,eAAe;UAClB;YACE,IAAIqI,QAAQ,GAAGnH,OAAO;cACpBoH,GAAG,GAAGD,QAAQ,CAACC,GAAG;cAClBxD,IAAI,GAAGuD,QAAQ,CAACvD,IAAI;YACtB;YACA,IAAIsC,kBAAkB,IAAIkB,GAAG,IAAIlB,kBAAkB,CAACkB,GAAG,CAAC,IAAI,CAAC5H,sBAAsB,CAACQ,OAAO,CAAC,EAAE;cAC5F,OAAOsF,0BAA0B,CAACI,IAAI,CAAC,IAAI,EAAE1F,OAAO,EAAEqF,QAAQ,CAAC;YACjE;YACA,IAAIgC,WAAW,GAAGzB,gBAAgB,CAAC,CAAC;YACpC,IAAI0B,SAAS,GAAGlF,MAAM,KAAK,MAAM,GAAG/C,wBAAwB,CAACgI,WAAW,CAAC,GAAGA,WAAW;YACvF,IAAIE,OAAO,GAAG7I,sBAAsB,CAACyE,KAAK;YAC1C,IAAIqE,cAAc,GAAGF,SAAS,CAACR,QAAQ,CAACzB,QAAQ,CAAC,GAAGA,QAAQ,GAAG,IAAI;YACnE,IAAIoC,qBAAqB,GAAG1B,aAAa,GAAGnH,aAAa,GAAG,EAAE;YAC9D,IAAIwI,GAAG,EAAE;cACP,IAAIM,uBAAuB,GAAG,KAAK;cACnCjJ,WAAW,CAAC,IAAI,EAAE,CAAC2I,GAAG,CAAC,EAAEvB,KAAK,EAAE;gBAC9B1C,KAAK,EAAEoE,OAAO;gBACdzB,YAAY,EAAEA,YAAY;gBAC1B2B,qBAAqB,EAAEA,qBAAqB;gBAC5CE,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;kBAChC,IAAIC,2BAA2B,GAAG,SAASA,2BAA2BA,CAAA,EAAG;oBACvE,IAAIC,UAAU,GAAG5F,MAAM,CAAC6F,wBAAwB,CAACzE,QAAQ,EAAE,eAAe,CAAC;oBAC3E,OAAO,CAACwE,UAAU,IAAIA,UAAU,CAACxD,YAAY;kBAC/C,CAAC;kBACD,IAAIuD,2BAA2B,CAAC,CAAC,EAAE;oBACjC3F,MAAM,CAACmC,cAAc,CAACf,QAAQ,EAAE,eAAe,EAAE;sBAC/CtC,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;wBAClB,OAAOf,OAAO;sBAChB,CAAC;sBACDqE,YAAY,EAAE;oBAChB,CAAC,CAAC;oBACFqD,uBAAuB,GAAG,IAAI;kBAChC;gBACF,CAAC;gBACDK,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;kBAC1B1F,yBAAyB,CAACrC,OAAO,CAAC;kBAClC,IAAI0H,uBAAuB,EAAE;oBAC3B;oBACA,OAAOrE,QAAQ,CAAC2E,aAAa;kBAC/B;kBACAhI,OAAO,GAAG,IAAI;gBAChB,CAAC;gBACDiI,KAAK,EAAE,SAASA,KAAKA,CAAA,EAAG;kBACtBtF,0BAA0B,CAAC3C,OAAO,CAAC;kBACnC,IAAI0H,uBAAuB,EAAE;oBAC3B;oBACA,OAAOrE,QAAQ,CAAC2E,aAAa;kBAC/B;kBACAhI,OAAO,GAAG,IAAI;gBAChB;cACF,CAAC,CAAC;cACF,IAAIkI,2BAA2B,GAAG7E,QAAQ,CAAC8E,aAAa,CAAC,iBAAiB,CAACjH,MAAM,CAACkG,GAAG,EAAE,sBAAsB,CAAC,CAAC;cAC/G1C,+BAA+B,CAACvD,GAAG,CAACnB,OAAO,EAAEkI,2BAA2B,CAAC;cACzE,OAAO5C,0BAA0B,CAACI,IAAI,CAAC4B,SAAS,EAAEY,2BAA2B,EAAEV,cAAc,CAAC;YAChG;YACA;YACA/I,WAAW,CAAC,IAAI,EAAE,CAAC,UAAU,CAACyC,MAAM,CAAC0C,IAAI,EAAE,WAAW,CAAC,CAAC,EAAEiC,KAAK,EAAE;cAC/DC,YAAY,EAAEA,YAAY;cAC1B2B,qBAAqB,EAAEA;YACzB,CAAC,CAAC;YACF,IAAIW,iCAAiC,GAAG/E,QAAQ,CAAC8E,aAAa,CAAC,2CAA2C,CAAC;YAC3GzD,+BAA+B,CAACvD,GAAG,CAACnB,OAAO,EAAEoI,iCAAiC,CAAC;YAC/E,OAAO9C,0BAA0B,CAACI,IAAI,CAAC4B,SAAS,EAAEc,iCAAiC,EAAEZ,cAAc,CAAC;UACtG;QACF;UACE;MACJ;IACF;IACA,OAAOlC,0BAA0B,CAACI,IAAI,CAAC,IAAI,EAAE1F,OAAO,EAAEqF,QAAQ,CAAC;EACjE;EACAF,yBAAyB,CAAC/F,iBAAiB,CAAC,GAAG,IAAI;EACnD,OAAO+F,yBAAyB;AAClC;AACA,SAASkD,iBAAiBA,CAACC,cAAc,EAAE9C,qBAAqB,EAAEpD,MAAM,EAAEmD,mBAAmB,EAAE;EAC7F,SAASgD,WAAWA,CAACC,KAAK,EAAE;IAC1B,IAAI3I,OAAO,GAAG2I,KAAK,CAAC3I,OAAO;IAC3B,IAAI,CAACD,cAAc,CAACC,OAAO,CAAC,IAAI,CAAC0F,mBAAmB,CAACiD,KAAK,CAAC,EAAE,OAAOF,cAAc,CAAC5C,IAAI,CAAC,IAAI,EAAE8C,KAAK,CAAC;IACpG,IAAI;MACF,IAAIC,eAAe;MACnB,IAAIC,qBAAqB,GAAGlD,qBAAqB,CAACgD,KAAK,CAAC;QACtD5C,gBAAgB,GAAG8C,qBAAqB,CAAC9C,gBAAgB;QACzDI,yBAAyB,GAAG0C,qBAAqB,CAAC1C,yBAAyB;MAC7E,QAAQnG,OAAO;QACb,KAAKb,cAAc;QACnB,KAAKD,aAAa;UAChB;YACE0J,eAAe,GAAG9D,iCAAiC,CAAC5D,GAAG,CAACyH,KAAK,CAAC,IAAIA,KAAK;YACvE;YACA,IAAIG,mBAAmB,GAAG3C,yBAAyB,CAACrG,OAAO,CAAC8I,eAAe,CAAC;YAC5E,IAAIE,mBAAmB,KAAK,CAAC,CAAC,EAAE;cAC9B3C,yBAAyB,CAAC4C,MAAM,CAACD,mBAAmB,EAAE,CAAC,CAAC;YAC1D;YACA;UACF;QACF,KAAK7J,eAAe;UAClB;YACE2J,eAAe,GAAG/D,+BAA+B,CAAC3D,GAAG,CAACyH,KAAK,CAAC,IAAIA,KAAK;YACrE;UACF;QACF;UACE;YACEC,eAAe,GAAGD,KAAK;UACzB;MACJ;MACA,IAAIlJ,UAAU,GAAGsG,gBAAgB,CAAC,CAAC;MACnC,IAAIiD,SAAS,GAAGzG,MAAM,KAAK,MAAM,GAAG/C,wBAAwB,CAACC,UAAU,CAAC,GAAGA,UAAU;MACrF;MACA,IAAIuJ,SAAS,CAAC/B,QAAQ,CAAC2B,eAAe,CAAC,EAAE;QACvC,OAAOH,cAAc,CAAC5C,IAAI,CAAC+C,eAAe,CAACK,UAAU,EAAEL,eAAe,CAAC;MACzE;IACF,CAAC,CAAC,OAAO1G,CAAC,EAAE;MACVgH,OAAO,CAACC,IAAI,CAACjH,CAAC,CAAC;IACjB;IACA,OAAOuG,cAAc,CAAC5C,IAAI,CAAC,IAAI,EAAE8C,KAAK,CAAC;EACzC;EACAD,WAAW,CAACnJ,iBAAiB,CAAC,GAAG,IAAI;EACrC,OAAOmJ,WAAW;AACpB;AACA,OAAO,SAASU,wCAAwCA,CAAC1D,mBAAmB,EAAEC,qBAAqB,EAAE;EACnG,IAAI0D,kBAAkB,GAAGC,eAAe,CAACC,SAAS,CAACtF,WAAW;EAC9D,IAAIuF,kBAAkB,GAAGC,eAAe,CAACF,SAAS,CAACtF,WAAW;EAC9D,IAAIyF,mBAAmB,GAAGJ,eAAe,CAACC,SAAS,CAACI,YAAY;EAChE;EACA,IAAIN,kBAAkB,CAAC9J,iBAAiB,CAAC,KAAK,IAAI,IAAIiK,kBAAkB,CAACjK,iBAAiB,CAAC,KAAK,IAAI,IAAImK,mBAAmB,CAACnK,iBAAiB,CAAC,KAAK,IAAI,EAAE;IACvJ+J,eAAe,CAACC,SAAS,CAACtF,WAAW,GAAGmB,uCAAuC,CAAC;MAC9EK,0BAA0B,EAAE4D,kBAAkB;MAC9C1D,qBAAqB,EAAEA,qBAAqB;MAC5CD,mBAAmB,EAAEA,mBAAmB;MACxCnD,MAAM,EAAE;IACV,CAAC,CAAC;IACFkH,eAAe,CAACF,SAAS,CAACtF,WAAW,GAAGmB,uCAAuC,CAAC;MAC9EK,0BAA0B,EAAE+D,kBAAkB;MAC9C7D,qBAAqB,EAAEA,qBAAqB;MAC5CD,mBAAmB,EAAEA,mBAAmB;MACxCnD,MAAM,EAAE;IACV,CAAC,CAAC;IACF+G,eAAe,CAACC,SAAS,CAACI,YAAY,GAAGvE,uCAAuC,CAAC;MAC/EK,0BAA0B,EAAEiE,mBAAmB;MAC/C/D,qBAAqB,EAAEA,qBAAqB;MAC5CD,mBAAmB,EAAEA,mBAAmB;MACxCnD,MAAM,EAAE;IACV,CAAC,CAAC;EACJ;EACA,IAAIqH,kBAAkB,GAAGN,eAAe,CAACC,SAAS,CAACb,WAAW;EAC9D,IAAImB,kBAAkB,GAAGJ,eAAe,CAACF,SAAS,CAACb,WAAW;EAC9D;EACA,IAAIkB,kBAAkB,CAACrK,iBAAiB,CAAC,KAAK,IAAI,IAAIsK,kBAAkB,CAACtK,iBAAiB,CAAC,KAAK,IAAI,EAAE;IACpG+J,eAAe,CAACC,SAAS,CAACb,WAAW,GAAGF,iBAAiB,CAACoB,kBAAkB,EAAEjE,qBAAqB,EAAE,MAAM,EAAED,mBAAmB,CAAC;IACjI+D,eAAe,CAACF,SAAS,CAACb,WAAW,GAAGF,iBAAiB,CAACqB,kBAAkB,EAAElE,qBAAqB,EAAE,MAAM,EAAED,mBAAmB,CAAC;EACnI;EACA,OAAO,SAASoE,OAAOA,CAAA,EAAG;IACxBR,eAAe,CAACC,SAAS,CAACtF,WAAW,GAAGoF,kBAAkB;IAC1DC,eAAe,CAACC,SAAS,CAACb,WAAW,GAAGkB,kBAAkB;IAC1DH,eAAe,CAACF,SAAS,CAACtF,WAAW,GAAGuF,kBAAkB;IAC1DC,eAAe,CAACF,SAAS,CAACb,WAAW,GAAGmB,kBAAkB;IAC1DP,eAAe,CAACC,SAAS,CAACI,YAAY,GAAGD,mBAAmB;EAC9D,CAAC;AACH;AACA,OAAO,SAASK,eAAeA,CAACC,kBAAkB,EAAEC,eAAe,EAAE;EACnED,kBAAkB,CAAC/E,OAAO,CAAC,UAAUqB,iBAAiB,EAAE;IACtD;IACA,IAAI4D,aAAa,GAAGD,eAAe,CAAC3D,iBAAiB,CAAC;IACtD,IAAI4D,aAAa,EAAE;MACjB;AACN;AACA;AACA;AACA;MACM,IAAI5D,iBAAiB,YAAYpB,gBAAgB,IAAIhF,sBAAsB,CAACoG,iBAAiB,CAAC,EAAE;QAC9F,IAAI9F,QAAQ,GAAGE,wBAAwB,CAAC4F,iBAAiB,CAAC;QAC1D,IAAI9F,QAAQ,EAAE;UACZ;UACA,KAAK,IAAI2J,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG3J,QAAQ,CAACC,MAAM,EAAE0J,CAAC,EAAE,EAAE;YACxC,IAAIC,OAAO,GAAG5J,QAAQ,CAAC2J,CAAC,CAAC;YACzB,IAAIE,oBAAoB,GAAG/D,iBAAiB,CAAC/F,KAAK;YAClD8J,oBAAoB,CAACC,UAAU,CAACF,OAAO,CAACG,OAAO,EAAEF,oBAAoB,CAAC7J,QAAQ,CAACC,MAAM,CAAC;UACxF;QACF;MACF;IACF;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}