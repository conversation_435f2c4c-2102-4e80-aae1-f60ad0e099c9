{"ast": null, "code": "import { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createBlock as _createBlock, withCtx as _withCtx, createVNode as _createVNode, createCommentVNode as _createCommentVNode, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode } from \"vue\";\nvar _hoisted_1 = {\n  class: \"globalFormButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_option = _resolveComponent(\"el-option\");\n  var _component_el_select = _resolveComponent(\"el-select\");\n  var _component_el_form_item = _resolveComponent(\"el-form-item\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_xyl_date_picker = _resolveComponent(\"xyl-date-picker\");\n  var _component_el_switch = _resolveComponent(\"el-switch\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_form = _resolveComponent(\"el-form\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createBlock(_component_el_scrollbar, {\n    class: \"SiteHotWordNew\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_form, {\n        model: $setup.form,\n        rules: $setup.rules,\n        inline: \"\",\n        ref: \"formRef\",\n        \"label-position\": \"top\",\n        class: \"globalForm\"\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_form_item, {\n            label: \"业务线\",\n            prop: \"hottype\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_select, {\n                modelValue: $setup.form.hottype,\n                \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n                  return $setup.form.hottype = $event;\n                }),\n                placeholder: \"请选择业务线\"\n              }, {\n                default: _withCtx(function () {\n                  return [(_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.hottypeList, function (item) {\n                    return _openBlock(), _createBlock(_component_el_option, {\n                      key: item.id,\n                      label: item.name,\n                      value: item.id\n                    }, null, 8 /* PROPS */, [\"label\", \"value\"]);\n                  }), 128 /* KEYED_FRAGMENT */))];\n                }),\n                _: 1 /* STABLE */\n              }, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_form_item, {\n            label: \"出现次数\",\n            prop: \"appearTimes\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_input, {\n                modelValue: $setup.form.appearTimes,\n                \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n                  return $setup.form.appearTimes = $event;\n                }),\n                disabled: \"\",\n                placeholder: \"请输入出现次数\",\n                clearable: \"\"\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_form_item, {\n            label: \"热词\",\n            prop: \"hotWord\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_input, {\n                modelValue: $setup.form.hotWord,\n                \"onUpdate:modelValue\": _cache[2] || (_cache[2] = function ($event) {\n                  return $setup.form.hotWord = $event;\n                }),\n                maxlength: \"100\",\n                \"show-word-limit\": \"\",\n                placeholder: \"请输入热词\",\n                clearable: \"\"\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_form_item, {\n            label: \"年月\",\n            prop: \"recordDate\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_xyl_date_picker, {\n                modelValue: $setup.form.recordDate,\n                \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n                  return $setup.form.recordDate = $event;\n                }),\n                format: \"YYYY年MM月\",\n                type: \"month\",\n                \"value-format\": \"YYYY-M\",\n                placeholder: \"请选择年月\"\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }), _createCommentVNode(\" <el-form-item label=\\\"序号\\\"\\r\\n                    prop=\\\"sort\\\">\\r\\n        <el-input v-model=\\\"form.sort\\\"\\r\\n                  maxlength=\\\"10\\\"\\r\\n                  show-word-limit\\r\\n                  @input=\\\"form.sort = validNum(form.sort)\\\"\\r\\n                  placeholder=\\\"请输入序号\\\"\\r\\n                  clearable />\\r\\n      </el-form-item> \"), _createVNode(_component_el_form_item, {\n            label: \"调整次数\",\n            prop: \"appearAdjustTimes\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_input, {\n                modelValue: $setup.form.appearAdjustTimes,\n                \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n                  return $setup.form.appearAdjustTimes = $event;\n                }),\n                maxlength: \"10\",\n                \"show-word-limit\": \"\",\n                onInput: _cache[5] || (_cache[5] = function ($event) {\n                  return $setup.form.appearAdjustTimes = $setup.validNum($setup.form.appearAdjustTimes);\n                }),\n                placeholder: \"请输入调整次数\",\n                clearable: \"\"\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_form_item, {\n            label: \"是否启用\",\n            prop: \"isShow\"\n          }, {\n            default: _withCtx(function () {\n              return [_createVNode(_component_el_switch, {\n                modelValue: $setup.form.isShow,\n                \"onUpdate:modelValue\": _cache[6] || (_cache[6] = function ($event) {\n                  return $setup.form.isShow = $event;\n                }),\n                \"inline-prompt\": \"\",\n                \"active-value\": 1,\n                \"inactive-value\": 0\n              }, null, 8 /* PROPS */, [\"modelValue\"])];\n            }),\n            _: 1 /* STABLE */\n          }), _createElementVNode(\"div\", _hoisted_1, [_createVNode(_component_el_button, {\n            type: \"primary\",\n            onClick: _cache[7] || (_cache[7] = function ($event) {\n              return $setup.submitForm($setup.formRef);\n            })\n          }, {\n            default: _withCtx(function () {\n              return _cache[9] || (_cache[9] = [_createTextVNode(\"提交\")]);\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_button, {\n            onClick: _cache[8] || (_cache[8] = function ($event) {\n              return $setup.cancel($setup.formRef);\n            })\n          }, {\n            default: _withCtx(function () {\n              return _cache[10] || (_cache[10] = [_createTextVNode(\"取消\")]);\n            }),\n            _: 1 /* STABLE */\n          })])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"model\", \"rules\"])];\n    }),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_el_scrollbar", "default", "_withCtx", "_createVNode", "_component_el_form", "model", "$setup", "form", "rules", "inline", "ref", "_component_el_form_item", "label", "prop", "_component_el_select", "modelValue", "hottype", "_cache", "$event", "placeholder", "_createElementBlock", "_Fragment", "_renderList", "hottypeList", "item", "_component_el_option", "key", "id", "name", "value", "_", "_component_el_input", "appearTimes", "disabled", "clearable", "hotWord", "maxlength", "_component_xyl_date_picker", "recordDate", "format", "type", "_createCommentVNode", "appearAdjustTimes", "onInput", "validNum", "_component_el_switch", "isShow", "_createElementVNode", "_hoisted_1", "_component_el_button", "onClick", "submitForm", "formRef", "_createTextVNode", "cancel"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiUseStatistics\\AiHotWord\\AiHotWordNew.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar class=\"SiteHotWordNew\">\r\n    <el-form :model=\"form\" :rules=\"rules\" inline ref=\"formRef\" label-position=\"top\" class=\"globalForm\">\r\n      <el-form-item label=\"业务线\" prop=\"hottype\">\r\n        <el-select v-model=\"form.hottype\" placeholder=\"请选择业务线\">\r\n          <el-option v-for=\"item in hottypeList\" :key=\"item.id\" :label=\"item.name\" :value=\"item.id\" />\r\n        </el-select>\r\n      </el-form-item>\r\n      <el-form-item label=\"出现次数\" prop=\"appearTimes\">\r\n        <el-input v-model=\"form.appearTimes\" disabled placeholder=\"请输入出现次数\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"热词\" prop=\"hotWord\">\r\n        <el-input v-model=\"form.hotWord\" maxlength=\"100\" show-word-limit placeholder=\"请输入热词\" clearable />\r\n      </el-form-item>\r\n      <el-form-item label=\"年月\" prop=\"recordDate\">\r\n        <xyl-date-picker\r\n          v-model=\"form.recordDate\"\r\n          format=\"YYYY年MM月\"\r\n          type=\"month\"\r\n          value-format=\"YYYY-M\"\r\n          placeholder=\"请选择年月\" />\r\n      </el-form-item>\r\n      <!-- <el-form-item label=\"序号\"\r\n                    prop=\"sort\">\r\n        <el-input v-model=\"form.sort\"\r\n                  maxlength=\"10\"\r\n                  show-word-limit\r\n                  @input=\"form.sort = validNum(form.sort)\"\r\n                  placeholder=\"请输入序号\"\r\n                  clearable />\r\n      </el-form-item> -->\r\n      <el-form-item label=\"调整次数\" prop=\"appearAdjustTimes\">\r\n        <el-input\r\n          v-model=\"form.appearAdjustTimes\"\r\n          maxlength=\"10\"\r\n          show-word-limit\r\n          @input=\"form.appearAdjustTimes = validNum(form.appearAdjustTimes)\"\r\n          placeholder=\"请输入调整次数\"\r\n          clearable />\r\n      </el-form-item>\r\n\r\n      <el-form-item label=\"是否启用\" prop=\"isShow\">\r\n        <el-switch v-model=\"form.isShow\" inline-prompt :active-value=\"1\" :inactive-value=\"0\" />\r\n      </el-form-item>\r\n      <div class=\"globalFormButton\">\r\n        <el-button type=\"primary\" @click=\"submitForm(formRef)\">提交</el-button>\r\n        <el-button @click=\"cancel(formRef)\">取消</el-button>\r\n      </div>\r\n    </el-form>\r\n  </el-scrollbar>\r\n</template>\r\n\r\n<script>\r\nexport default { name: 'SiteHotWordNew' }\r\n</script>\r\n\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ElMessage } from 'element-plus'\r\nimport { format } from 'common/js/time.js'\r\nimport { ref, reactive, onMounted } from 'vue'\r\nimport { validNum } from 'common/js/utils.js'\r\nconst props = defineProps({\r\n  id: { type: String, default: '' }\r\n})\r\nconst emit = defineEmits(['callback'])\r\nconst formRef = ref()\r\nconst form = reactive({\r\n  months: '',\r\n  sort: '',\r\n  hotWord: '',\r\n  recordDate: '',\r\n  appearTimes: 0,\r\n  appearAdjustTimes: '',\r\n  isShow: 1,\r\n  hottype: ''\r\n})\r\nconst rules = reactive({\r\n  // months: [{ required: true, message: '请选择年月', trigger: ['blur', 'change'] }],\r\n  hotWord: [{ required: true, message: '请输入热词', trigger: ['blur', 'change'] }]\r\n})\r\n\r\nonMounted(() => {\r\n  getHottypeList()\r\n  if (props.id) {\r\n    hotWordPlusInfo()\r\n  }\r\n})\r\nconst hottypeList = ref([])\r\nconst getHottypeList = async () => {\r\n  const res = await api.globalJson('/aigptChatScene/selector')\r\n  hottypeList.value = res.data\r\n}\r\nconst submitForm = async (formEl) => {\r\n  if (!formEl) return\r\n  await formEl.validate((valid, fields) => {\r\n    if (valid) {\r\n      var params = {\r\n        form: {\r\n          ownerBusinessId: form.hottype,\r\n          id: props.id,\r\n          sort: form.sort,\r\n          hotWord: form.hotWord,\r\n          businessCode: 'aigptChat',\r\n          businessName: 'Ai热词',\r\n          appearAdjustTimes: form.appearAdjustTimes,\r\n          isShow: form.isShow,\r\n          appearTimes: form.appearTimes\r\n        },\r\n        recordYear: Number(format(form.recordDate, 'YYYY')),\r\n        recordMonth: Number(format(form.recordDate, 'MM'))\r\n        // recordMonth: Number(format(form.recordDate, 'DD')),\r\n      }\r\n      globalJson(params, formEl)\r\n    } else {\r\n      ElMessage({ type: 'warning', message: '请根据提示信息完善字段内容！' })\r\n    }\r\n  })\r\n}\r\n\r\nconst hotWordPlusInfo = async () => {\r\n  const { data, code } = await api.hotWordPlusInfo({ detailId: props.id })\r\n  if (code === 200) {\r\n    form.hottype = data.ownerBusinessId\r\n    form.sort = data.sort\r\n    form.hotWord = data.hotWord\r\n    form.appearTimes = data.appearTimes || 0\r\n    form.isShow = data.isShow\r\n    form.appearAdjustTimes = data.appearAdjustTimes\r\n    form.recordDate = data.recordYear + '-' + data.recordMonth\r\n  }\r\n}\r\n\r\nconst globalJson = async (params, formEl) => {\r\n  const { code } = await api.globalJson(props.id ? '/hotWordPlus/edit' : '/hotWordPlus/add', params)\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: props.id ? '编辑成功' : '新增成功' })\r\n    cancel()\r\n  }\r\n}\r\n\r\nconst cancel = (formEl) => {\r\n  emit('callback')\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.SiteHotWordNew {\r\n  width: 680px;\r\n\r\n  .globalForm {\r\n    margin: 20px auto;\r\n    background-color: #fff;\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EA4CWA,KAAK,EAAC;AAAkB;;;;;;;;;;;uBA3CjCC,YAAA,CAgDeC,uBAAA;IAhDDF,KAAK,EAAC;EAAgB;IADtCG,OAAA,EAAAC,QAAA,CAEI;MAAA,OA8CU,CA9CVC,YAAA,CA8CUC,kBAAA;QA9CAC,KAAK,EAAEC,MAAA,CAAAC,IAAI;QAAGC,KAAK,EAAEF,MAAA,CAAAE,KAAK;QAAEC,MAAM,EAAN,EAAM;QAACC,GAAG,EAAC,SAAS;QAAC,gBAAc,EAAC,KAAK;QAACZ,KAAK,EAAC;;QAF1FG,OAAA,EAAAC,QAAA,CAGM;UAAA,OAIe,CAJfC,YAAA,CAIeQ,uBAAA;YAJDC,KAAK,EAAC,KAAK;YAACC,IAAI,EAAC;;YAHrCZ,OAAA,EAAAC,QAAA,CAIQ;cAAA,OAEY,CAFZC,YAAA,CAEYW,oBAAA;gBANpBC,UAAA,EAI4BT,MAAA,CAAAC,IAAI,CAACS,OAAO;gBAJxC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAI4BZ,MAAA,CAAAC,IAAI,CAACS,OAAO,GAAAE,MAAA;gBAAA;gBAAEC,WAAW,EAAC;;gBAJtDlB,OAAA,EAAAC,QAAA,CAKqB;kBAAA,OAA2B,E,kBAAtCkB,mBAAA,CAA4FC,SAAA,QALtGC,WAAA,CAKoChB,MAAA,CAAAiB,WAAW,EAL/C,UAK4BC,IAAI;yCAAtBzB,YAAA,CAA4F0B,oBAAA;sBAApDC,GAAG,EAAEF,IAAI,CAACG,EAAE;sBAAGf,KAAK,EAAEY,IAAI,CAACI,IAAI;sBAAGC,KAAK,EAAEL,IAAI,CAACG;;;;gBALhGG,CAAA;;;YAAAA,CAAA;cAQM3B,YAAA,CAEeQ,uBAAA;YAFDC,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC;;YARtCZ,OAAA,EAAAC,QAAA,CASQ;cAAA,OAAgF,CAAhFC,YAAA,CAAgF4B,mBAAA;gBATxFhB,UAAA,EAS2BT,MAAA,CAAAC,IAAI,CAACyB,WAAW;gBAT3C,uBAAAf,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAS2BZ,MAAA,CAAAC,IAAI,CAACyB,WAAW,GAAAd,MAAA;gBAAA;gBAAEe,QAAQ,EAAR,EAAQ;gBAACd,WAAW,EAAC,SAAS;gBAACe,SAAS,EAAT;;;YAT5EJ,CAAA;cAWM3B,YAAA,CAEeQ,uBAAA;YAFDC,KAAK,EAAC,IAAI;YAACC,IAAI,EAAC;;YAXpCZ,OAAA,EAAAC,QAAA,CAYQ;cAAA,OAAiG,CAAjGC,YAAA,CAAiG4B,mBAAA;gBAZzGhB,UAAA,EAY2BT,MAAA,CAAAC,IAAI,CAAC4B,OAAO;gBAZvC,uBAAAlB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAY2BZ,MAAA,CAAAC,IAAI,CAAC4B,OAAO,GAAAjB,MAAA;gBAAA;gBAAEkB,SAAS,EAAC,KAAK;gBAAC,iBAAe,EAAf,EAAe;gBAACjB,WAAW,EAAC,OAAO;gBAACe,SAAS,EAAT;;;YAZ7FJ,CAAA;cAcM3B,YAAA,CAOeQ,uBAAA;YAPDC,KAAK,EAAC,IAAI;YAACC,IAAI,EAAC;;YAdpCZ,OAAA,EAAAC,QAAA,CAeQ;cAAA,OAKwB,CALxBC,YAAA,CAKwBkC,0BAAA;gBApBhCtB,UAAA,EAgBmBT,MAAA,CAAAC,IAAI,CAAC+B,UAAU;gBAhBlC,uBAAArB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAgBmBZ,MAAA,CAAAC,IAAI,CAAC+B,UAAU,GAAApB,MAAA;gBAAA;gBACxBqB,MAAM,EAAC,UAAU;gBACjBC,IAAI,EAAC,OAAO;gBACZ,cAAY,EAAC,QAAQ;gBACrBrB,WAAW,EAAC;;;YApBtBW,CAAA;cAsBMW,mBAAA,kWAQmB,EACnBtC,YAAA,CAQeQ,uBAAA;YARDC,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC;;YA/BtCZ,OAAA,EAAAC,QAAA,CAgCQ;cAAA,OAMc,CANdC,YAAA,CAMc4B,mBAAA;gBAtCtBhB,UAAA,EAiCmBT,MAAA,CAAAC,IAAI,CAACmC,iBAAiB;gBAjCzC,uBAAAzB,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAiCmBZ,MAAA,CAAAC,IAAI,CAACmC,iBAAiB,GAAAxB,MAAA;gBAAA;gBAC/BkB,SAAS,EAAC,IAAI;gBACd,iBAAe,EAAf,EAAe;gBACdO,OAAK,EAAA1B,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OAAEZ,MAAA,CAAAC,IAAI,CAACmC,iBAAiB,GAAGpC,MAAA,CAAAsC,QAAQ,CAACtC,MAAA,CAAAC,IAAI,CAACmC,iBAAiB;gBAAA;gBAChEvB,WAAW,EAAC,SAAS;gBACrBe,SAAS,EAAT;;;YAtCVJ,CAAA;cAyCM3B,YAAA,CAEeQ,uBAAA;YAFDC,KAAK,EAAC,MAAM;YAACC,IAAI,EAAC;;YAzCtCZ,OAAA,EAAAC,QAAA,CA0CQ;cAAA,OAAuF,CAAvFC,YAAA,CAAuF0C,oBAAA;gBA1C/F9B,UAAA,EA0C4BT,MAAA,CAAAC,IAAI,CAACuC,MAAM;gBA1CvC,uBAAA7B,MAAA,QAAAA,MAAA,gBAAAC,MAAA;kBAAA,OA0C4BZ,MAAA,CAAAC,IAAI,CAACuC,MAAM,GAAA5B,MAAA;gBAAA;gBAAE,eAAa,EAAb,EAAa;gBAAE,cAAY,EAAE,CAAC;gBAAG,gBAAc,EAAE;;;YA1C1FY,CAAA;cA4CMiB,mBAAA,CAGM,OAHNC,UAGM,GAFJ7C,YAAA,CAAqE8C,oBAAA;YAA1DT,IAAI,EAAC,SAAS;YAAEU,OAAK,EAAAjC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAAEZ,MAAA,CAAA6C,UAAU,CAAC7C,MAAA,CAAA8C,OAAO;YAAA;;YA7C5DnD,OAAA,EAAAC,QAAA,CA6C+D;cAAA,OAAEe,MAAA,QAAAA,MAAA,OA7CjEoC,gBAAA,CA6C+D,IAAE,E;;YA7CjEvB,CAAA;cA8CQ3B,YAAA,CAAkD8C,oBAAA;YAAtCC,OAAK,EAAAjC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAAEZ,MAAA,CAAAgD,MAAM,CAAChD,MAAA,CAAA8C,OAAO;YAAA;;YA9CzCnD,OAAA,EAAAC,QAAA,CA8C4C;cAAA,OAAEe,MAAA,SAAAA,MAAA,QA9C9CoC,gBAAA,CA8C4C,IAAE,E;;YA9C9CvB,CAAA;;;QAAAA,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}