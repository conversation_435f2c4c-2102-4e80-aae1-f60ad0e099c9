{"ast": null, "code": "// Process html entity - &#123;, &#xAF;, &quot;, ...\n\n'use strict';\n\nvar entities = require('../common/entities');\nvar has = require('../common/utils').has;\nvar isValidEntityCode = require('../common/utils').isValidEntityCode;\nvar fromCodePoint = require('../common/utils').fromCodePoint;\nvar DIGITAL_RE = /^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i;\nvar NAMED_RE = /^&([a-z][a-z0-9]{1,31});/i;\nmodule.exports = function entity(state, silent) {\n  var ch,\n    code,\n    match,\n    pos = state.pos,\n    max = state.posMax;\n  if (state.src.charCodeAt(pos) !== 0x26 /* & */) {\n    return false;\n  }\n  if (pos + 1 < max) {\n    ch = state.src.charCodeAt(pos + 1);\n    if (ch === 0x23 /* # */) {\n      match = state.src.slice(pos).match(DIGITAL_RE);\n      if (match) {\n        if (!silent) {\n          code = match[1][0].toLowerCase() === 'x' ? parseInt(match[1].slice(1), 16) : parseInt(match[1], 10);\n          state.pending += isValidEntityCode(code) ? fromCodePoint(code) : fromCodePoint(0xFFFD);\n        }\n        state.pos += match[0].length;\n        return true;\n      }\n    } else {\n      match = state.src.slice(pos).match(NAMED_RE);\n      if (match) {\n        if (has(entities, match[1])) {\n          if (!silent) {\n            state.pending += entities[match[1]];\n          }\n          state.pos += match[0].length;\n          return true;\n        }\n      }\n    }\n  }\n  if (!silent) {\n    state.pending += '&';\n  }\n  state.pos++;\n  return true;\n};", "map": {"version": 3, "names": ["entities", "require", "has", "isValidEntityCode", "fromCodePoint", "DIGITAL_RE", "NAMED_RE", "module", "exports", "entity", "state", "silent", "ch", "code", "match", "pos", "max", "posMax", "src", "charCodeAt", "slice", "toLowerCase", "parseInt", "pending", "length"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_inline/entity.js"], "sourcesContent": ["// Process html entity - &#123;, &#xAF;, &quot;, ...\n\n'use strict';\n\nvar entities          = require('../common/entities');\nvar has               = require('../common/utils').has;\nvar isValidEntityCode = require('../common/utils').isValidEntityCode;\nvar fromCodePoint     = require('../common/utils').fromCodePoint;\n\n\nvar DIGITAL_RE = /^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i;\nvar NAMED_RE   = /^&([a-z][a-z0-9]{1,31});/i;\n\n\nmodule.exports = function entity(state, silent) {\n  var ch, code, match, pos = state.pos, max = state.posMax;\n\n  if (state.src.charCodeAt(pos) !== 0x26/* & */) { return false; }\n\n  if (pos + 1 < max) {\n    ch = state.src.charCodeAt(pos + 1);\n\n    if (ch === 0x23 /* # */) {\n      match = state.src.slice(pos).match(DIGITAL_RE);\n      if (match) {\n        if (!silent) {\n          code = match[1][0].toLowerCase() === 'x' ? parseInt(match[1].slice(1), 16) : parseInt(match[1], 10);\n          state.pending += isValidEntityCode(code) ? fromCodePoint(code) : fromCodePoint(0xFFFD);\n        }\n        state.pos += match[0].length;\n        return true;\n      }\n    } else {\n      match = state.src.slice(pos).match(NAMED_RE);\n      if (match) {\n        if (has(entities, match[1])) {\n          if (!silent) { state.pending += entities[match[1]]; }\n          state.pos += match[0].length;\n          return true;\n        }\n      }\n    }\n  }\n\n  if (!silent) { state.pending += '&'; }\n  state.pos++;\n  return true;\n};\n"], "mappings": "AAAA;;AAEA,YAAY;;AAEZ,IAAIA,QAAQ,GAAYC,OAAO,CAAC,oBAAoB,CAAC;AACrD,IAAIC,GAAG,GAAiBD,OAAO,CAAC,iBAAiB,CAAC,CAACC,GAAG;AACtD,IAAIC,iBAAiB,GAAGF,OAAO,CAAC,iBAAiB,CAAC,CAACE,iBAAiB;AACpE,IAAIC,aAAa,GAAOH,OAAO,CAAC,iBAAiB,CAAC,CAACG,aAAa;AAGhE,IAAIC,UAAU,GAAG,sCAAsC;AACvD,IAAIC,QAAQ,GAAK,2BAA2B;AAG5CC,MAAM,CAACC,OAAO,GAAG,SAASC,MAAMA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC9C,IAAIC,EAAE;IAAEC,IAAI;IAAEC,KAAK;IAAEC,GAAG,GAAGL,KAAK,CAACK,GAAG;IAAEC,GAAG,GAAGN,KAAK,CAACO,MAAM;EAExD,IAAIP,KAAK,CAACQ,GAAG,CAACC,UAAU,CAACJ,GAAG,CAAC,KAAK,IAAI,UAAS;IAAE,OAAO,KAAK;EAAE;EAE/D,IAAIA,GAAG,GAAG,CAAC,GAAGC,GAAG,EAAE;IACjBJ,EAAE,GAAGF,KAAK,CAACQ,GAAG,CAACC,UAAU,CAACJ,GAAG,GAAG,CAAC,CAAC;IAElC,IAAIH,EAAE,KAAK,IAAI,CAAC,SAAS;MACvBE,KAAK,GAAGJ,KAAK,CAACQ,GAAG,CAACE,KAAK,CAACL,GAAG,CAAC,CAACD,KAAK,CAACT,UAAU,CAAC;MAC9C,IAAIS,KAAK,EAAE;QACT,IAAI,CAACH,MAAM,EAAE;UACXE,IAAI,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAACO,WAAW,CAAC,CAAC,KAAK,GAAG,GAAGC,QAAQ,CAACR,KAAK,CAAC,CAAC,CAAC,CAACM,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGE,QAAQ,CAACR,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;UACnGJ,KAAK,CAACa,OAAO,IAAIpB,iBAAiB,CAACU,IAAI,CAAC,GAAGT,aAAa,CAACS,IAAI,CAAC,GAAGT,aAAa,CAAC,MAAM,CAAC;QACxF;QACAM,KAAK,CAACK,GAAG,IAAID,KAAK,CAAC,CAAC,CAAC,CAACU,MAAM;QAC5B,OAAO,IAAI;MACb;IACF,CAAC,MAAM;MACLV,KAAK,GAAGJ,KAAK,CAACQ,GAAG,CAACE,KAAK,CAACL,GAAG,CAAC,CAACD,KAAK,CAACR,QAAQ,CAAC;MAC5C,IAAIQ,KAAK,EAAE;QACT,IAAIZ,GAAG,CAACF,QAAQ,EAAEc,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;UAC3B,IAAI,CAACH,MAAM,EAAE;YAAED,KAAK,CAACa,OAAO,IAAIvB,QAAQ,CAACc,KAAK,CAAC,CAAC,CAAC,CAAC;UAAE;UACpDJ,KAAK,CAACK,GAAG,IAAID,KAAK,CAAC,CAAC,CAAC,CAACU,MAAM;UAC5B,OAAO,IAAI;QACb;MACF;IACF;EACF;EAEA,IAAI,CAACb,MAAM,EAAE;IAAED,KAAK,CAACa,OAAO,IAAI,GAAG;EAAE;EACrCb,KAAK,CAACK,GAAG,EAAE;EACX,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}