{"ast": null, "code": "// For each opening emphasis-like marker find a matching closing one\n//\n'use strict';\n\nfunction processDelimiters(state, delimiters) {\n  var closerIdx,\n    openerIdx,\n    closer,\n    opener,\n    minOpenerIdx,\n    newMinOpenerIdx,\n    isOddMatch,\n    lastJump,\n    openersBottom = {},\n    max = delimiters.length;\n  if (!max) return;\n\n  // headerIdx is the first delimiter of the current (where closer is) delimiter run\n  var headerIdx = 0;\n  var lastTokenIdx = -2; // needs any value lower than -1\n  var jumps = [];\n  for (closerIdx = 0; closerIdx < max; closerIdx++) {\n    closer = delimiters[closerIdx];\n    jumps.push(0);\n\n    // markers belong to same delimiter run if:\n    //  - they have adjacent tokens\n    //  - AND markers are the same\n    //\n    if (delimiters[headerIdx].marker !== closer.marker || lastTokenIdx !== closer.token - 1) {\n      headerIdx = closerIdx;\n    }\n    lastTokenIdx = closer.token;\n\n    // Length is only used for emphasis-specific \"rule of 3\",\n    // if it's not defined (in strikethrough or 3rd party plugins),\n    // we can default it to 0 to disable those checks.\n    //\n    closer.length = closer.length || 0;\n    if (!closer.close) continue;\n\n    // Previously calculated lower bounds (previous fails)\n    // for each marker, each delimiter length modulo 3,\n    // and for whether this closer can be an opener;\n    // https://github.com/commonmark/cmark/commit/34250e12ccebdc6372b8b49c44fab57c72443460\n    if (!openersBottom.hasOwnProperty(closer.marker)) {\n      openersBottom[closer.marker] = [-1, -1, -1, -1, -1, -1];\n    }\n    minOpenerIdx = openersBottom[closer.marker][(closer.open ? 3 : 0) + closer.length % 3];\n    openerIdx = headerIdx - jumps[headerIdx] - 1;\n    newMinOpenerIdx = openerIdx;\n    for (; openerIdx > minOpenerIdx; openerIdx -= jumps[openerIdx] + 1) {\n      opener = delimiters[openerIdx];\n      if (opener.marker !== closer.marker) continue;\n      if (opener.open && opener.end < 0) {\n        isOddMatch = false;\n\n        // from spec:\n        //\n        // If one of the delimiters can both open and close emphasis, then the\n        // sum of the lengths of the delimiter runs containing the opening and\n        // closing delimiters must not be a multiple of 3 unless both lengths\n        // are multiples of 3.\n        //\n        if (opener.close || closer.open) {\n          if ((opener.length + closer.length) % 3 === 0) {\n            if (opener.length % 3 !== 0 || closer.length % 3 !== 0) {\n              isOddMatch = true;\n            }\n          }\n        }\n        if (!isOddMatch) {\n          // If previous delimiter cannot be an opener, we can safely skip\n          // the entire sequence in future checks. This is required to make\n          // sure algorithm has linear complexity (see *_*_*_*_*_... case).\n          //\n          lastJump = openerIdx > 0 && !delimiters[openerIdx - 1].open ? jumps[openerIdx - 1] + 1 : 0;\n          jumps[closerIdx] = closerIdx - openerIdx + lastJump;\n          jumps[openerIdx] = lastJump;\n          closer.open = false;\n          opener.end = closerIdx;\n          opener.close = false;\n          newMinOpenerIdx = -1;\n          // treat next token as start of run,\n          // it optimizes skips in **<...>**a**<...>** pathological case\n          lastTokenIdx = -2;\n          break;\n        }\n      }\n    }\n    if (newMinOpenerIdx !== -1) {\n      // If match for this delimiter run failed, we want to set lower bound for\n      // future lookups. This is required to make sure algorithm has linear\n      // complexity.\n      //\n      // See details here:\n      // https://github.com/commonmark/cmark/issues/178#issuecomment-270417442\n      //\n      openersBottom[closer.marker][(closer.open ? 3 : 0) + (closer.length || 0) % 3] = newMinOpenerIdx;\n    }\n  }\n}\nmodule.exports = function link_pairs(state) {\n  var curr,\n    tokens_meta = state.tokens_meta,\n    max = state.tokens_meta.length;\n  processDelimiters(state, state.delimiters);\n  for (curr = 0; curr < max; curr++) {\n    if (tokens_meta[curr] && tokens_meta[curr].delimiters) {\n      processDelimiters(state, tokens_meta[curr].delimiters);\n    }\n  }\n};", "map": {"version": 3, "names": ["processDelimiters", "state", "delimiters", "closerIdx", "openerIdx", "closer", "opener", "minOpenerIdx", "newMinOpenerIdx", "isOddMatch", "lastJump", "openers<PERSON><PERSON><PERSON>", "max", "length", "headerIdx", "lastTokenIdx", "jumps", "push", "marker", "token", "close", "hasOwnProperty", "open", "end", "module", "exports", "link_pairs", "curr", "tokens_meta"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_inline/balance_pairs.js"], "sourcesContent": ["// For each opening emphasis-like marker find a matching closing one\n//\n'use strict';\n\n\nfunction processDelimiters(state, delimiters) {\n  var closerIdx, openerIdx, closer, opener, minOpenerIdx, newMinOpenerIdx,\n      isOddMatch, lastJump,\n      openersBottom = {},\n      max = delimiters.length;\n\n  if (!max) return;\n\n  // headerIdx is the first delimiter of the current (where closer is) delimiter run\n  var headerIdx = 0;\n  var lastTokenIdx = -2; // needs any value lower than -1\n  var jumps = [];\n\n  for (closerIdx = 0; closerIdx < max; closerIdx++) {\n    closer = delimiters[closerIdx];\n\n    jumps.push(0);\n\n    // markers belong to same delimiter run if:\n    //  - they have adjacent tokens\n    //  - AND markers are the same\n    //\n    if (delimiters[headerIdx].marker !== closer.marker || lastTokenIdx !== closer.token - 1) {\n      headerIdx = closerIdx;\n    }\n\n    lastTokenIdx = closer.token;\n\n    // Length is only used for emphasis-specific \"rule of 3\",\n    // if it's not defined (in strikethrough or 3rd party plugins),\n    // we can default it to 0 to disable those checks.\n    //\n    closer.length = closer.length || 0;\n\n    if (!closer.close) continue;\n\n    // Previously calculated lower bounds (previous fails)\n    // for each marker, each delimiter length modulo 3,\n    // and for whether this closer can be an opener;\n    // https://github.com/commonmark/cmark/commit/34250e12ccebdc6372b8b49c44fab57c72443460\n    if (!openersBottom.hasOwnProperty(closer.marker)) {\n      openersBottom[closer.marker] = [ -1, -1, -1, -1, -1, -1 ];\n    }\n\n    minOpenerIdx = openersBottom[closer.marker][(closer.open ? 3 : 0) + (closer.length % 3)];\n\n    openerIdx = headerIdx - jumps[headerIdx] - 1;\n\n    newMinOpenerIdx = openerIdx;\n\n    for (; openerIdx > minOpenerIdx; openerIdx -= jumps[openerIdx] + 1) {\n      opener = delimiters[openerIdx];\n\n      if (opener.marker !== closer.marker) continue;\n\n      if (opener.open && opener.end < 0) {\n\n        isOddMatch = false;\n\n        // from spec:\n        //\n        // If one of the delimiters can both open and close emphasis, then the\n        // sum of the lengths of the delimiter runs containing the opening and\n        // closing delimiters must not be a multiple of 3 unless both lengths\n        // are multiples of 3.\n        //\n        if (opener.close || closer.open) {\n          if ((opener.length + closer.length) % 3 === 0) {\n            if (opener.length % 3 !== 0 || closer.length % 3 !== 0) {\n              isOddMatch = true;\n            }\n          }\n        }\n\n        if (!isOddMatch) {\n          // If previous delimiter cannot be an opener, we can safely skip\n          // the entire sequence in future checks. This is required to make\n          // sure algorithm has linear complexity (see *_*_*_*_*_... case).\n          //\n          lastJump = openerIdx > 0 && !delimiters[openerIdx - 1].open ?\n            jumps[openerIdx - 1] + 1 :\n            0;\n\n          jumps[closerIdx] = closerIdx - openerIdx + lastJump;\n          jumps[openerIdx] = lastJump;\n\n          closer.open  = false;\n          opener.end   = closerIdx;\n          opener.close = false;\n          newMinOpenerIdx = -1;\n          // treat next token as start of run,\n          // it optimizes skips in **<...>**a**<...>** pathological case\n          lastTokenIdx = -2;\n          break;\n        }\n      }\n    }\n\n    if (newMinOpenerIdx !== -1) {\n      // If match for this delimiter run failed, we want to set lower bound for\n      // future lookups. This is required to make sure algorithm has linear\n      // complexity.\n      //\n      // See details here:\n      // https://github.com/commonmark/cmark/issues/178#issuecomment-270417442\n      //\n      openersBottom[closer.marker][(closer.open ? 3 : 0) + ((closer.length || 0) % 3)] = newMinOpenerIdx;\n    }\n  }\n}\n\n\nmodule.exports = function link_pairs(state) {\n  var curr,\n      tokens_meta = state.tokens_meta,\n      max = state.tokens_meta.length;\n\n  processDelimiters(state, state.delimiters);\n\n  for (curr = 0; curr < max; curr++) {\n    if (tokens_meta[curr] && tokens_meta[curr].delimiters) {\n      processDelimiters(state, tokens_meta[curr].delimiters);\n    }\n  }\n};\n"], "mappings": "AAAA;AACA;AACA,YAAY;;AAGZ,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,UAAU,EAAE;EAC5C,IAAIC,SAAS;IAAEC,SAAS;IAAEC,MAAM;IAAEC,MAAM;IAAEC,YAAY;IAAEC,eAAe;IACnEC,UAAU;IAAEC,QAAQ;IACpBC,aAAa,GAAG,CAAC,CAAC;IAClBC,GAAG,GAAGV,UAAU,CAACW,MAAM;EAE3B,IAAI,CAACD,GAAG,EAAE;;EAEV;EACA,IAAIE,SAAS,GAAG,CAAC;EACjB,IAAIC,YAAY,GAAG,CAAC,CAAC,CAAC,CAAC;EACvB,IAAIC,KAAK,GAAG,EAAE;EAEd,KAAKb,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGS,GAAG,EAAET,SAAS,EAAE,EAAE;IAChDE,MAAM,GAAGH,UAAU,CAACC,SAAS,CAAC;IAE9Ba,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;;IAEb;IACA;IACA;IACA;IACA,IAAIf,UAAU,CAACY,SAAS,CAAC,CAACI,MAAM,KAAKb,MAAM,CAACa,MAAM,IAAIH,YAAY,KAAKV,MAAM,CAACc,KAAK,GAAG,CAAC,EAAE;MACvFL,SAAS,GAAGX,SAAS;IACvB;IAEAY,YAAY,GAAGV,MAAM,CAACc,KAAK;;IAE3B;IACA;IACA;IACA;IACAd,MAAM,CAACQ,MAAM,GAAGR,MAAM,CAACQ,MAAM,IAAI,CAAC;IAElC,IAAI,CAACR,MAAM,CAACe,KAAK,EAAE;;IAEnB;IACA;IACA;IACA;IACA,IAAI,CAACT,aAAa,CAACU,cAAc,CAAChB,MAAM,CAACa,MAAM,CAAC,EAAE;MAChDP,aAAa,CAACN,MAAM,CAACa,MAAM,CAAC,GAAG,CAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE;IAC3D;IAEAX,YAAY,GAAGI,aAAa,CAACN,MAAM,CAACa,MAAM,CAAC,CAAC,CAACb,MAAM,CAACiB,IAAI,GAAG,CAAC,GAAG,CAAC,IAAKjB,MAAM,CAACQ,MAAM,GAAG,CAAE,CAAC;IAExFT,SAAS,GAAGU,SAAS,GAAGE,KAAK,CAACF,SAAS,CAAC,GAAG,CAAC;IAE5CN,eAAe,GAAGJ,SAAS;IAE3B,OAAOA,SAAS,GAAGG,YAAY,EAAEH,SAAS,IAAIY,KAAK,CAACZ,SAAS,CAAC,GAAG,CAAC,EAAE;MAClEE,MAAM,GAAGJ,UAAU,CAACE,SAAS,CAAC;MAE9B,IAAIE,MAAM,CAACY,MAAM,KAAKb,MAAM,CAACa,MAAM,EAAE;MAErC,IAAIZ,MAAM,CAACgB,IAAI,IAAIhB,MAAM,CAACiB,GAAG,GAAG,CAAC,EAAE;QAEjCd,UAAU,GAAG,KAAK;;QAElB;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAIH,MAAM,CAACc,KAAK,IAAIf,MAAM,CAACiB,IAAI,EAAE;UAC/B,IAAI,CAAChB,MAAM,CAACO,MAAM,GAAGR,MAAM,CAACQ,MAAM,IAAI,CAAC,KAAK,CAAC,EAAE;YAC7C,IAAIP,MAAM,CAACO,MAAM,GAAG,CAAC,KAAK,CAAC,IAAIR,MAAM,CAACQ,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;cACtDJ,UAAU,GAAG,IAAI;YACnB;UACF;QACF;QAEA,IAAI,CAACA,UAAU,EAAE;UACf;UACA;UACA;UACA;UACAC,QAAQ,GAAGN,SAAS,GAAG,CAAC,IAAI,CAACF,UAAU,CAACE,SAAS,GAAG,CAAC,CAAC,CAACkB,IAAI,GACzDN,KAAK,CAACZ,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,GACxB,CAAC;UAEHY,KAAK,CAACb,SAAS,CAAC,GAAGA,SAAS,GAAGC,SAAS,GAAGM,QAAQ;UACnDM,KAAK,CAACZ,SAAS,CAAC,GAAGM,QAAQ;UAE3BL,MAAM,CAACiB,IAAI,GAAI,KAAK;UACpBhB,MAAM,CAACiB,GAAG,GAAKpB,SAAS;UACxBG,MAAM,CAACc,KAAK,GAAG,KAAK;UACpBZ,eAAe,GAAG,CAAC,CAAC;UACpB;UACA;UACAO,YAAY,GAAG,CAAC,CAAC;UACjB;QACF;MACF;IACF;IAEA,IAAIP,eAAe,KAAK,CAAC,CAAC,EAAE;MAC1B;MACA;MACA;MACA;MACA;MACA;MACA;MACAG,aAAa,CAACN,MAAM,CAACa,MAAM,CAAC,CAAC,CAACb,MAAM,CAACiB,IAAI,GAAG,CAAC,GAAG,CAAC,IAAK,CAACjB,MAAM,CAACQ,MAAM,IAAI,CAAC,IAAI,CAAE,CAAC,GAAGL,eAAe;IACpG;EACF;AACF;AAGAgB,MAAM,CAACC,OAAO,GAAG,SAASC,UAAUA,CAACzB,KAAK,EAAE;EAC1C,IAAI0B,IAAI;IACJC,WAAW,GAAG3B,KAAK,CAAC2B,WAAW;IAC/BhB,GAAG,GAAGX,KAAK,CAAC2B,WAAW,CAACf,MAAM;EAElCb,iBAAiB,CAACC,KAAK,EAAEA,KAAK,CAACC,UAAU,CAAC;EAE1C,KAAKyB,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGf,GAAG,EAAEe,IAAI,EAAE,EAAE;IACjC,IAAIC,WAAW,CAACD,IAAI,CAAC,IAAIC,WAAW,CAACD,IAAI,CAAC,CAACzB,UAAU,EAAE;MACrDF,iBAAiB,CAACC,KAAK,EAAE2B,WAAW,CAACD,IAAI,CAAC,CAACzB,UAAU,CAAC;IACxD;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}