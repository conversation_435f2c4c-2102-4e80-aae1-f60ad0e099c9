{"ast": null, "code": "import isNativeReflectConstruct from \"./isNativeReflectConstruct.js\";\nimport setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _construct(t, e, r) {\n  if (isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments);\n  var o = [null];\n  o.push.apply(o, e);\n  var p = new (t.bind.apply(t, o))();\n  return r && setPrototypeOf(p, r.prototype), p;\n}\nexport { _construct as default };", "map": {"version": 3, "names": ["isNativeReflectConstruct", "setPrototypeOf", "_construct", "t", "e", "r", "Reflect", "construct", "apply", "arguments", "o", "push", "p", "bind", "prototype", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/@babel+runtime@7.26.0/node_modules/@babel/runtime/helpers/esm/construct.js"], "sourcesContent": ["import isNativeReflectConstruct from \"./isNativeReflectConstruct.js\";\nimport setPrototypeOf from \"./setPrototypeOf.js\";\nfunction _construct(t, e, r) {\n  if (isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments);\n  var o = [null];\n  o.push.apply(o, e);\n  var p = new (t.bind.apply(t, o))();\n  return r && setPrototypeOf(p, r.prototype), p;\n}\nexport { _construct as default };"], "mappings": "AAAA,OAAOA,wBAAwB,MAAM,+BAA+B;AACpE,OAAOC,cAAc,MAAM,qBAAqB;AAChD,SAASC,UAAUA,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAC3B,IAAIL,wBAAwB,CAAC,CAAC,EAAE,OAAOM,OAAO,CAACC,SAAS,CAACC,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAC/E,IAAIC,CAAC,GAAG,CAAC,IAAI,CAAC;EACdA,CAAC,CAACC,IAAI,CAACH,KAAK,CAACE,CAAC,EAAEN,CAAC,CAAC;EAClB,IAAIQ,CAAC,GAAG,KAAKT,CAAC,CAACU,IAAI,CAACL,KAAK,CAACL,CAAC,EAAEO,CAAC,CAAC,EAAE,CAAC;EAClC,OAAOL,CAAC,IAAIJ,cAAc,CAACW,CAAC,EAAEP,CAAC,CAACS,SAAS,CAAC,EAAEF,CAAC;AAC/C;AACA,SAASV,UAAU,IAAIa,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}