{"ast": null, "code": "function _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nexport { _isNativeReflectConstruct as default };", "map": {"version": 3, "names": ["_isNativeReflectConstruct", "t", "Boolean", "prototype", "valueOf", "call", "Reflect", "construct", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/@babel+runtime@7.26.0/node_modules/@babel/runtime/helpers/esm/isNativeReflectConstruct.js"], "sourcesContent": ["function _isNativeReflectConstruct() {\n  try {\n    var t = !Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>, [], function () {}));\n  } catch (t) {}\n  return (_isNativeReflectConstruct = function _isNativeReflectConstruct() {\n    return !!t;\n  })();\n}\nexport { _isNativeReflectConstruct as default };"], "mappings": "AAAA,SAASA,yBAAyBA,CAAA,EAAG;EACnC,IAAI;IACF,IAAIC,CAAC,GAAG,CAACC,OAAO,CAACC,SAAS,CAACC,OAAO,CAACC,IAAI,CAACC,OAAO,CAACC,SAAS,CAACL,OAAO,EAAE,EAAE,EAAE,YAAY,CAAC,CAAC,CAAC,CAAC;EACzF,CAAC,CAAC,OAAOD,CAAC,EAAE,CAAC;EACb,OAAO,CAACD,yBAAyB,GAAG,SAASA,yBAAyBA,CAAA,EAAG;IACvE,OAAO,CAAC,CAACC,CAAC;EACZ,CAAC,EAAE,CAAC;AACN;AACA,SAASD,yBAAyB,IAAIQ,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}