{"ast": null, "code": "function _wrapNativeSuper(t) { var r = \"function\" == typeof Map ? new Map() : void 0; return _wrapNativeSuper = function _wrapNativeSuper(t) { if (null === t || !_isNativeFunction(t)) return t; if (\"function\" != typeof t) throw new TypeError(\"Super expression must either be null or a function\"); if (void 0 !== r) { if (r.has(t)) return r.get(t); r.set(t, Wrapper); } function Wrapper() { return _construct(t, arguments, _getPrototypeOf(this).constructor); } return Wrapper.prototype = Object.create(t.prototype, { constructor: { value: Wrapper, enumerable: !1, writable: !0, configurable: !0 } }), _setPrototypeOf(Wrapper, t); }, _wrapNativeSuper(t); }\nfunction _construct(t, e, r) { if (_isNativeReflectConstruct()) return Reflect.construct.apply(null, arguments); var o = [null]; o.push.apply(o, e); var p = new (t.bind.apply(t, o))(); return r && _setPrototypeOf(p, r.prototype), p; }\nfunction _isNativeFunction(t) { try { return -1 !== Function.toString.call(t).indexOf(\"[native code]\"); } catch (n) { return \"function\" == typeof t; } }\nfunction _slicedToArray(r, e) { return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest(); }\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _iterableToArrayLimit(r, l) { var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (null != t) { var e, n, i, u, a = [], f = !0, o = !1; try { if (i = (t = t.call(r)).next, 0 === l) { if (Object(t) !== t) return; f = !1; } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0); } catch (r) { o = !0, n = r; } finally { try { if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return; } finally { if (o) throw n; } } return a; } }\nfunction _arrayWithHoles(r) { if (Array.isArray(r)) return r; }\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _possibleConstructorReturn(t, e) { if (e && (\"object\" == typeof e || \"function\" == typeof e)) return e; if (void 0 !== e) throw new TypeError(\"Derived constructors may only return object or undefined\"); return _assertThisInitialized(t); }\nfunction _assertThisInitialized(e) { if (void 0 === e) throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); return e; }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _getPrototypeOf(t) { return _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function (t) { return t.__proto__ || Object.getPrototypeOf(t); }, _getPrototypeOf(t); }\nfunction _inherits(t, e) { if (\"function\" != typeof e && null !== e) throw new TypeError(\"Super expression must either be null or a function\"); t.prototype = Object.create(e && e.prototype, { constructor: { value: t, writable: !0, configurable: !0 } }), Object.defineProperty(t, \"prototype\", { writable: !1 }), e && _setPrototypeOf(t, e); }\nfunction _setPrototypeOf(t, e) { return _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function (t, e) { return t.__proto__ = e, t; }, _setPrototypeOf(t, e); }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\n/* eslint-disable no-multi-assign */\n\nfunction deepFreeze(obj) {\n  if (obj instanceof Map) {\n    obj.clear = obj.delete = obj.set = function () {\n      throw new Error('map is read-only');\n    };\n  } else if (obj instanceof Set) {\n    obj.add = obj.clear = obj.delete = function () {\n      throw new Error('set is read-only');\n    };\n  }\n\n  // Freeze self\n  Object.freeze(obj);\n  Object.getOwnPropertyNames(obj).forEach(function (name) {\n    var prop = obj[name];\n    var type = typeof prop;\n\n    // Freeze prop if it is an object or function and also not already frozen\n    if ((type === 'object' || type === 'function') && !Object.isFrozen(prop)) {\n      deepFreeze(prop);\n    }\n  });\n  return obj;\n}\n\n/** @typedef {import('highlight.js').CallbackResponse} CallbackResponse */\n/** @typedef {import('highlight.js').CompiledMode} CompiledMode */\n/** @implements CallbackResponse */\nvar Response = /*#__PURE__*/function () {\n  \"use strict\";\n\n  /**\n   * @param {CompiledMode} mode\n   */\n  function Response(mode) {\n    _classCallCheck(this, Response);\n    // eslint-disable-next-line no-undefined\n    if (mode.data === undefined) mode.data = {};\n    this.data = mode.data;\n    this.isMatchIgnored = false;\n  }\n  return _createClass(Response, [{\n    key: \"ignoreMatch\",\n    value: function ignoreMatch() {\n      this.isMatchIgnored = true;\n    }\n  }]);\n}();\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction escapeHTML(value) {\n  return value.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\"/g, '&quot;').replace(/'/g, '&#x27;');\n}\n\n/**\n * performs a shallow merge of multiple objects into one\n *\n * @template T\n * @param {T} original\n * @param {Record<string,any>[]} objects\n * @returns {T} a single new object\n */\nfunction inherit$1(original) {\n  /** @type Record<string,any> */\n  var result = Object.create(null);\n  for (var key in original) {\n    result[key] = original[key];\n  }\n  for (var _len = arguments.length, objects = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    objects[_key - 1] = arguments[_key];\n  }\n  objects.forEach(function (obj) {\n    for (var _key2 in obj) {\n      result[_key2] = obj[_key2];\n    }\n  });\n  return /** @type {T} */result;\n}\n\n/**\n * @typedef {object} Renderer\n * @property {(text: string) => void} addText\n * @property {(node: Node) => void} openNode\n * @property {(node: Node) => void} closeNode\n * @property {() => string} value\n */\n\n/** @typedef {{scope?: string, language?: string, sublanguage?: boolean}} Node */\n/** @typedef {{walk: (r: Renderer) => void}} Tree */\n/** */\n\nvar SPAN_CLOSE = '</span>';\n\n/**\n * Determines if a node needs to be wrapped in <span>\n *\n * @param {Node} node */\nvar emitsWrappingTags = function emitsWrappingTags(node) {\n  // rarely we can have a sublanguage where language is undefined\n  // TODO: track down why\n  return !!node.scope;\n};\n\n/**\n *\n * @param {string} name\n * @param {{prefix:string}} options\n */\nvar scopeToCSSClass = function scopeToCSSClass(name, _ref) {\n  var prefix = _ref.prefix;\n  // sub-language\n  if (name.startsWith(\"language:\")) {\n    return name.replace(\"language:\", \"language-\");\n  }\n  // tiered scope: comment.line\n  if (name.includes(\".\")) {\n    var pieces = name.split(\".\");\n    return [`${prefix}${pieces.shift()}`].concat(_toConsumableArray(pieces.map(function (x, i) {\n      return `${x}${\"_\".repeat(i + 1)}`;\n    }))).join(\" \");\n  }\n  // simple scope\n  return `${prefix}${name}`;\n};\n\n/** @type {Renderer} */\nvar HTMLRenderer = /*#__PURE__*/function () {\n  \"use strict\";\n\n  /**\n   * Creates a new HTMLRenderer\n   *\n   * @param {Tree} parseTree - the parse tree (must support `walk` API)\n   * @param {{classPrefix: string}} options\n   */\n  function HTMLRenderer(parseTree, options) {\n    _classCallCheck(this, HTMLRenderer);\n    this.buffer = \"\";\n    this.classPrefix = options.classPrefix;\n    parseTree.walk(this);\n  }\n\n  /**\n   * Adds texts to the output stream\n   *\n   * @param {string} text */\n  return _createClass(HTMLRenderer, [{\n    key: \"addText\",\n    value: function addText(text) {\n      this.buffer += escapeHTML(text);\n    }\n\n    /**\n     * Adds a node open to the output stream (if needed)\n     *\n     * @param {Node} node */\n  }, {\n    key: \"openNode\",\n    value: function openNode(node) {\n      if (!emitsWrappingTags(node)) return;\n      var className = scopeToCSSClass(node.scope, {\n        prefix: this.classPrefix\n      });\n      this.span(className);\n    }\n\n    /**\n     * Adds a node close to the output stream (if needed)\n     *\n     * @param {Node} node */\n  }, {\n    key: \"closeNode\",\n    value: function closeNode(node) {\n      if (!emitsWrappingTags(node)) return;\n      this.buffer += SPAN_CLOSE;\n    }\n\n    /**\n     * returns the accumulated buffer\n    */\n  }, {\n    key: \"value\",\n    value: function value() {\n      return this.buffer;\n    }\n\n    // helpers\n\n    /**\n     * Builds a span element\n     *\n     * @param {string} className */\n  }, {\n    key: \"span\",\n    value: function span(className) {\n      this.buffer += `<span class=\"${className}\">`;\n    }\n  }]);\n}();\n/** @typedef {{scope?: string, language?: string, children: Node[]} | string} Node */\n/** @typedef {{scope?: string, language?: string, children: Node[]} } DataNode */\n/** @typedef {import('highlight.js').Emitter} Emitter */\n/**  */\n/** @returns {DataNode} */\nvar newNode = function newNode() {\n  var opts = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  /** @type DataNode */\n  var result = {\n    children: []\n  };\n  Object.assign(result, opts);\n  return result;\n};\nvar TokenTree = /*#__PURE__*/function () {\n  \"use strict\";\n\n  function TokenTree() {\n    _classCallCheck(this, TokenTree);\n    /** @type DataNode */\n    this.rootNode = newNode();\n    this.stack = [this.rootNode];\n  }\n  return _createClass(TokenTree, [{\n    key: \"top\",\n    get: function get() {\n      return this.stack[this.stack.length - 1];\n    }\n  }, {\n    key: \"root\",\n    get: function get() {\n      return this.rootNode;\n    }\n\n    /** @param {Node} node */\n  }, {\n    key: \"add\",\n    value: function add(node) {\n      this.top.children.push(node);\n    }\n\n    /** @param {string} scope */\n  }, {\n    key: \"openNode\",\n    value: function openNode(scope) {\n      /** @type Node */\n      var node = newNode({\n        scope\n      });\n      this.add(node);\n      this.stack.push(node);\n    }\n  }, {\n    key: \"closeNode\",\n    value: function closeNode() {\n      if (this.stack.length > 1) {\n        return this.stack.pop();\n      }\n      // eslint-disable-next-line no-undefined\n      return undefined;\n    }\n  }, {\n    key: \"closeAllNodes\",\n    value: function closeAllNodes() {\n      while (this.closeNode());\n    }\n  }, {\n    key: \"toJSON\",\n    value: function toJSON() {\n      return JSON.stringify(this.rootNode, null, 4);\n    }\n\n    /**\n     * @typedef { import(\"./html_renderer\").Renderer } Renderer\n     * @param {Renderer} builder\n     */\n  }, {\n    key: \"walk\",\n    value: function walk(builder) {\n      // this does not\n      return this.constructor._walk(builder, this.rootNode);\n      // this works\n      // return TokenTree._walk(builder, this.rootNode);\n    }\n\n    /**\n     * @param {Renderer} builder\n     * @param {Node} node\n     */\n  }], [{\n    key: \"_walk\",\n    value: function _walk(builder, node) {\n      var _this = this;\n      if (typeof node === \"string\") {\n        builder.addText(node);\n      } else if (node.children) {\n        builder.openNode(node);\n        node.children.forEach(function (child) {\n          return _this._walk(builder, child);\n        });\n        builder.closeNode(node);\n      }\n      return builder;\n    }\n\n    /**\n     * @param {Node} node\n     */\n  }, {\n    key: \"_collapse\",\n    value: function _collapse(node) {\n      if (typeof node === \"string\") return;\n      if (!node.children) return;\n      if (node.children.every(function (el) {\n        return typeof el === \"string\";\n      })) {\n        // node.text = node.children.join(\"\");\n        // delete node.children;\n        node.children = [node.children.join(\"\")];\n      } else {\n        node.children.forEach(function (child) {\n          TokenTree._collapse(child);\n        });\n      }\n    }\n  }]);\n}();\n/**\n  Currently this is all private API, but this is the minimal API necessary\n  that an Emitter must implement to fully support the parser.\n\n  Minimal interface:\n\n  - addText(text)\n  - __addSublanguage(emitter, subLanguageName)\n  - startScope(scope)\n  - endScope()\n  - finalize()\n  - toHTML()\n\n*/\n/**\n * @implements {Emitter}\n */\nvar TokenTreeEmitter = /*#__PURE__*/function (_TokenTree) {\n  \"use strict\";\n\n  /**\n   * @param {*} options\n   */\n  function TokenTreeEmitter(options) {\n    var _this2;\n    _classCallCheck(this, TokenTreeEmitter);\n    _this2 = _callSuper(this, TokenTreeEmitter);\n    _this2.options = options;\n    return _this2;\n  }\n\n  /**\n   * @param {string} text\n   */\n  _inherits(TokenTreeEmitter, _TokenTree);\n  return _createClass(TokenTreeEmitter, [{\n    key: \"addText\",\n    value: function addText(text) {\n      if (text === \"\") {\n        return;\n      }\n      this.add(text);\n    }\n\n    /** @param {string} scope */\n  }, {\n    key: \"startScope\",\n    value: function startScope(scope) {\n      this.openNode(scope);\n    }\n  }, {\n    key: \"endScope\",\n    value: function endScope() {\n      this.closeNode();\n    }\n\n    /**\n     * @param {Emitter & {root: DataNode}} emitter\n     * @param {string} name\n     */\n  }, {\n    key: \"__addSublanguage\",\n    value: function __addSublanguage(emitter, name) {\n      /** @type DataNode */\n      var node = emitter.root;\n      if (name) node.scope = `language:${name}`;\n      this.add(node);\n    }\n  }, {\n    key: \"toHTML\",\n    value: function toHTML() {\n      var renderer = new HTMLRenderer(this, this.options);\n      return renderer.value();\n    }\n  }, {\n    key: \"finalize\",\n    value: function finalize() {\n      this.closeAllNodes();\n      return true;\n    }\n  }]);\n}(TokenTree);\n/**\n * @param {string} value\n * @returns {RegExp}\n * */\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction anyNumberOfTimes(re) {\n  return concat('(?:', re, ')*');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction optional(re) {\n  return concat('(?:', re, ')?');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat() {\n  for (var _len2 = arguments.length, args = new Array(_len2), _key3 = 0; _key3 < _len2; _key3++) {\n    args[_key3] = arguments[_key3];\n  }\n  var joined = args.map(function (x) {\n    return source(x);\n  }).join(\"\");\n  return joined;\n}\n\n/**\n * @param { Array<string | RegExp | Object> } args\n * @returns {object}\n */\nfunction stripOptionsFromArgs(args) {\n  var opts = args[args.length - 1];\n  if (typeof opts === 'object' && opts.constructor === Object) {\n    args.splice(args.length - 1, 1);\n    return opts;\n  } else {\n    return {};\n  }\n}\n\n/** @typedef { {capture?: boolean} } RegexEitherOptions */\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] | [...(RegExp | string)[], RegexEitherOptions]} args\n * @returns {string}\n */\nfunction either() {\n  for (var _len3 = arguments.length, args = new Array(_len3), _key4 = 0; _key4 < _len3; _key4++) {\n    args[_key4] = arguments[_key4];\n  }\n  /** @type { object & {capture?: boolean} }  */\n  var opts = stripOptionsFromArgs(args);\n  var joined = '(' + (opts.capture ? \"\" : \"?:\") + args.map(function (x) {\n    return source(x);\n  }).join(\"|\") + \")\";\n  return joined;\n}\n\n/**\n * @param {RegExp | string} re\n * @returns {number}\n */\nfunction countMatchGroups(re) {\n  return new RegExp(re.toString() + '|').exec('').length - 1;\n}\n\n/**\n * Does lexeme start with a regular expression match at the beginning\n * @param {RegExp} re\n * @param {string} lexeme\n */\nfunction startsWith(re, lexeme) {\n  var match = re && re.exec(lexeme);\n  return match && match.index === 0;\n}\n\n// BACKREF_RE matches an open parenthesis or backreference. To avoid\n// an incorrect parse, it additionally matches the following:\n// - [...] elements, where the meaning of parentheses and escapes change\n// - other escape sequences, so we do not misparse escape sequences as\n//   interesting elements\n// - non-matching or lookahead parentheses, which do not capture. These\n//   follow the '(' with a '?'.\nvar BACKREF_RE = /\\[(?:[^\\\\\\]]|\\\\.)*\\]|\\(\\??|\\\\([1-9][0-9]*)|\\\\./;\n\n// **INTERNAL** Not intended for outside usage\n// join logically computes regexps.join(separator), but fixes the\n// backreferences so they continue to match.\n// it also places each individual regular expression into it's own\n// match group, keeping track of the sequencing of those match groups\n// is currently an exercise for the caller. :-)\n/**\n * @param {(string | RegExp)[]} regexps\n * @param {{joinWith: string}} opts\n * @returns {string}\n */\nfunction _rewriteBackreferences(regexps, _ref2) {\n  var joinWith = _ref2.joinWith;\n  var numCaptures = 0;\n  return regexps.map(function (regex) {\n    numCaptures += 1;\n    var offset = numCaptures;\n    var re = source(regex);\n    var out = '';\n    while (re.length > 0) {\n      var match = BACKREF_RE.exec(re);\n      if (!match) {\n        out += re;\n        break;\n      }\n      out += re.substring(0, match.index);\n      re = re.substring(match.index + match[0].length);\n      if (match[0][0] === '\\\\' && match[1]) {\n        // Adjust the backreference.\n        out += '\\\\' + String(Number(match[1]) + offset);\n      } else {\n        out += match[0];\n        if (match[0] === '(') {\n          numCaptures++;\n        }\n      }\n    }\n    return out;\n  }).map(function (re) {\n    return `(${re})`;\n  }).join(joinWith);\n}\n\n/** @typedef {import('highlight.js').Mode} Mode */\n/** @typedef {import('highlight.js').ModeCallback} ModeCallback */\n\n// Common regexps\nvar MATCH_NOTHING_RE = /\\b\\B/;\nvar IDENT_RE = '[a-zA-Z]\\\\w*';\nvar UNDERSCORE_IDENT_RE = '[a-zA-Z_]\\\\w*';\nvar NUMBER_RE = '\\\\b\\\\d+(\\\\.\\\\d+)?';\nvar C_NUMBER_RE = '(-?)(\\\\b0[xX][a-fA-F0-9]+|(\\\\b\\\\d+(\\\\.\\\\d*)?|\\\\.\\\\d+)([eE][-+]?\\\\d+)?)'; // 0x..., 0..., decimal, float\nvar BINARY_NUMBER_RE = '\\\\b(0b[01]+)'; // 0b...\nvar RE_STARTERS_RE = '!|!=|!==|%|%=|&|&&|&=|\\\\*|\\\\*=|\\\\+|\\\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\\\?|\\\\[|\\\\{|\\\\(|\\\\^|\\\\^=|\\\\||\\\\|=|\\\\|\\\\||~';\n\n/**\n* @param { Partial<Mode> & {binary?: string | RegExp} } opts\n*/\nvar SHEBANG = function SHEBANG() {\n  var opts = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var beginShebang = /^#![ ]*\\//;\n  if (opts.binary) {\n    opts.begin = concat(beginShebang, /.*\\b/, opts.binary, /\\b.*/);\n  }\n  return inherit$1({\n    scope: 'meta',\n    begin: beginShebang,\n    end: /$/,\n    relevance: 0,\n    /** @type {ModeCallback} */\n    \"on:begin\": function onBegin(m, resp) {\n      if (m.index !== 0) resp.ignoreMatch();\n    }\n  }, opts);\n};\n\n// Common modes\nvar BACKSLASH_ESCAPE = {\n  begin: '\\\\\\\\[\\\\s\\\\S]',\n  relevance: 0\n};\nvar APOS_STRING_MODE = {\n  scope: 'string',\n  begin: '\\'',\n  end: '\\'',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nvar QUOTE_STRING_MODE = {\n  scope: 'string',\n  begin: '\"',\n  end: '\"',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nvar PHRASAL_WORDS_MODE = {\n  begin: /\\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\\b/\n};\n/**\n * Creates a comment mode\n *\n * @param {string | RegExp} begin\n * @param {string | RegExp} end\n * @param {Mode | {}} [modeOptions]\n * @returns {Partial<Mode>}\n */\nvar COMMENT = function COMMENT(begin, end) {\n  var modeOptions = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  var mode = inherit$1({\n    scope: 'comment',\n    begin,\n    end,\n    contains: []\n  }, modeOptions);\n  mode.contains.push({\n    scope: 'doctag',\n    // hack to avoid the space from being included. the space is necessary to\n    // match here to prevent the plain text rule below from gobbling up doctags\n    begin: '[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)',\n    end: /(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,\n    excludeBegin: true,\n    relevance: 0\n  });\n  var ENGLISH_WORD = either(\n  // list of common 1 and 2 letter words in English\n  \"I\", \"a\", \"is\", \"so\", \"us\", \"to\", \"at\", \"if\", \"in\", \"it\", \"on\",\n  // note: this is not an exhaustive list of contractions, just popular ones\n  /[A-Za-z]+['](d|ve|re|ll|t|s|n)/,\n  // contractions - can't we'd they're let's, etc\n  /[A-Za-z]+[-][a-z]+/,\n  // `no-way`, etc.\n  /[A-Za-z][a-z]{2,}/ // allow capitalized words at beginning of sentences\n  );\n  // looking like plain text, more likely to be a comment\n  mode.contains.push({\n    // TODO: how to include \", (, ) without breaking grammars that use these for\n    // comment delimiters?\n    // begin: /[ ]+([()\"]?([A-Za-z'-]{3,}|is|a|I|so|us|[tT][oO]|at|if|in|it|on)[.]?[()\":]?([.][ ]|[ ]|\\))){3}/\n    // ---\n\n    // this tries to find sequences of 3 english words in a row (without any\n    // \"programming\" type syntax) this gives us a strong signal that we've\n    // TRULY found a comment - vs perhaps scanning with the wrong language.\n    // It's possible to find something that LOOKS like the start of the\n    // comment - but then if there is no readable text - good chance it is a\n    // false match and not a comment.\n    //\n    // for a visual example please see:\n    // https://github.com/highlightjs/highlight.js/issues/2827\n\n    begin: concat(/[ ]+/,\n    // necessary to prevent us gobbling up doctags like /* <AUTHOR> Mcgill */\n    '(', ENGLISH_WORD, /[.]?[:]?([.][ ]|[ ])/, '){3}') // look for 3 words in a row\n  });\n  return mode;\n};\nvar C_LINE_COMMENT_MODE = COMMENT('//', '$');\nvar C_BLOCK_COMMENT_MODE = COMMENT('/\\\\*', '\\\\*/');\nvar HASH_COMMENT_MODE = COMMENT('#', '$');\nvar NUMBER_MODE = {\n  scope: 'number',\n  begin: NUMBER_RE,\n  relevance: 0\n};\nvar C_NUMBER_MODE = {\n  scope: 'number',\n  begin: C_NUMBER_RE,\n  relevance: 0\n};\nvar BINARY_NUMBER_MODE = {\n  scope: 'number',\n  begin: BINARY_NUMBER_RE,\n  relevance: 0\n};\nvar REGEXP_MODE = {\n  scope: \"regexp\",\n  begin: /\\/(?=[^/\\n]*\\/)/,\n  end: /\\/[gimuy]*/,\n  contains: [BACKSLASH_ESCAPE, {\n    begin: /\\[/,\n    end: /\\]/,\n    relevance: 0,\n    contains: [BACKSLASH_ESCAPE]\n  }]\n};\nvar TITLE_MODE = {\n  scope: 'title',\n  begin: IDENT_RE,\n  relevance: 0\n};\nvar UNDERSCORE_TITLE_MODE = {\n  scope: 'title',\n  begin: UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\nvar METHOD_GUARD = {\n  // excludes method names from keyword processing\n  begin: '\\\\.\\\\s*' + UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\n\n/**\n * Adds end same as begin mechanics to a mode\n *\n * Your mode must include at least a single () match group as that first match\n * group is what is used for comparison\n * @param {Partial<Mode>} mode\n */\nvar END_SAME_AS_BEGIN = function END_SAME_AS_BEGIN(mode) {\n  return Object.assign(mode, {\n    /** @type {ModeCallback} */\n    'on:begin': function onBegin(m, resp) {\n      resp.data._beginMatch = m[1];\n    },\n    /** @type {ModeCallback} */\n    'on:end': function onEnd(m, resp) {\n      if (resp.data._beginMatch !== m[1]) resp.ignoreMatch();\n    }\n  });\n};\nvar MODES = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  APOS_STRING_MODE: APOS_STRING_MODE,\n  BACKSLASH_ESCAPE: BACKSLASH_ESCAPE,\n  BINARY_NUMBER_MODE: BINARY_NUMBER_MODE,\n  BINARY_NUMBER_RE: BINARY_NUMBER_RE,\n  COMMENT: COMMENT,\n  C_BLOCK_COMMENT_MODE: C_BLOCK_COMMENT_MODE,\n  C_LINE_COMMENT_MODE: C_LINE_COMMENT_MODE,\n  C_NUMBER_MODE: C_NUMBER_MODE,\n  C_NUMBER_RE: C_NUMBER_RE,\n  END_SAME_AS_BEGIN: END_SAME_AS_BEGIN,\n  HASH_COMMENT_MODE: HASH_COMMENT_MODE,\n  IDENT_RE: IDENT_RE,\n  MATCH_NOTHING_RE: MATCH_NOTHING_RE,\n  METHOD_GUARD: METHOD_GUARD,\n  NUMBER_MODE: NUMBER_MODE,\n  NUMBER_RE: NUMBER_RE,\n  PHRASAL_WORDS_MODE: PHRASAL_WORDS_MODE,\n  QUOTE_STRING_MODE: QUOTE_STRING_MODE,\n  REGEXP_MODE: REGEXP_MODE,\n  RE_STARTERS_RE: RE_STARTERS_RE,\n  SHEBANG: SHEBANG,\n  TITLE_MODE: TITLE_MODE,\n  UNDERSCORE_IDENT_RE: UNDERSCORE_IDENT_RE,\n  UNDERSCORE_TITLE_MODE: UNDERSCORE_TITLE_MODE\n});\n\n/**\n@typedef {import('highlight.js').CallbackResponse} CallbackResponse\n@typedef {import('highlight.js').CompilerExt} CompilerExt\n*/\n\n// Grammar extensions / plugins\n// See: https://github.com/highlightjs/highlight.js/issues/2833\n\n// Grammar extensions allow \"syntactic sugar\" to be added to the grammar modes\n// without requiring any underlying changes to the compiler internals.\n\n// `compileMatch` being the perfect small example of now allowing a grammar\n// author to write `match` when they desire to match a single expression rather\n// than being forced to use `begin`.  The extension then just moves `match` into\n// `begin` when it runs.  Ie, no features have been added, but we've just made\n// the experience of writing (and reading grammars) a little bit nicer.\n\n// ------\n\n// TODO: We need negative look-behind support to do this properly\n/**\n * Skip a match if it has a preceding dot\n *\n * This is used for `beginKeywords` to prevent matching expressions such as\n * `bob.keyword.do()`. The mode compiler automatically wires this up as a\n * special _internal_ 'on:begin' callback for modes with `beginKeywords`\n * @param {RegExpMatchArray} match\n * @param {CallbackResponse} response\n */\nfunction skipIfHasPrecedingDot(match, response) {\n  var before = match.input[match.index - 1];\n  if (before === \".\") {\n    response.ignoreMatch();\n  }\n}\n\n/**\n *\n * @type {CompilerExt}\n */\nfunction scopeClassName(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.className !== undefined) {\n    mode.scope = mode.className;\n    delete mode.className;\n  }\n}\n\n/**\n * `beginKeywords` syntactic sugar\n * @type {CompilerExt}\n */\nfunction beginKeywords(mode, parent) {\n  if (!parent) return;\n  if (!mode.beginKeywords) return;\n\n  // for languages with keywords that include non-word characters checking for\n  // a word boundary is not sufficient, so instead we check for a word boundary\n  // or whitespace - this does no harm in any case since our keyword engine\n  // doesn't allow spaces in keywords anyways and we still check for the boundary\n  // first\n  mode.begin = '\\\\b(' + mode.beginKeywords.split(' ').join('|') + ')(?!\\\\.)(?=\\\\b|\\\\s)';\n  mode.__beforeBegin = skipIfHasPrecedingDot;\n  mode.keywords = mode.keywords || mode.beginKeywords;\n  delete mode.beginKeywords;\n\n  // prevents double relevance, the keywords themselves provide\n  // relevance, the mode doesn't need to double it\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 0;\n}\n\n/**\n * Allow `illegal` to contain an array of illegal values\n * @type {CompilerExt}\n */\nfunction compileIllegal(mode, _parent) {\n  if (!Array.isArray(mode.illegal)) return;\n  mode.illegal = either.apply(void 0, _toConsumableArray(mode.illegal));\n}\n\n/**\n * `match` to match a single expression for readability\n * @type {CompilerExt}\n */\nfunction compileMatch(mode, _parent) {\n  if (!mode.match) return;\n  if (mode.begin || mode.end) throw new Error(\"begin & end are not supported with match\");\n  mode.begin = mode.match;\n  delete mode.match;\n}\n\n/**\n * provides the default 1 relevance to all modes\n * @type {CompilerExt}\n */\nfunction compileRelevance(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 1;\n}\n\n// allow beforeMatch to act as a \"qualifier\" for the match\n// the full match begin must be [beforeMatch][begin]\nvar beforeMatchExt = function beforeMatchExt(mode, parent) {\n  if (!mode.beforeMatch) return;\n  // starts conflicts with endsParent which we need to make sure the child\n  // rule is not matched multiple times\n  if (mode.starts) throw new Error(\"beforeMatch cannot be used with starts\");\n  var originalMode = Object.assign({}, mode);\n  Object.keys(mode).forEach(function (key) {\n    delete mode[key];\n  });\n  mode.keywords = originalMode.keywords;\n  mode.begin = concat(originalMode.beforeMatch, lookahead(originalMode.begin));\n  mode.starts = {\n    relevance: 0,\n    contains: [Object.assign(originalMode, {\n      endsParent: true\n    })]\n  };\n  mode.relevance = 0;\n  delete originalMode.beforeMatch;\n};\n\n// keywords that should have no default relevance value\nvar COMMON_KEYWORDS = ['of', 'and', 'for', 'in', 'not', 'or', 'if', 'then', 'parent',\n// common variable name\n'list',\n// common variable name\n'value' // common variable name\n];\nvar DEFAULT_KEYWORD_SCOPE = \"keyword\";\n\n/**\n * Given raw keywords from a language definition, compile them.\n *\n * @param {string | Record<string,string|string[]> | Array<string>} rawKeywords\n * @param {boolean} caseInsensitive\n */\nfunction compileKeywords(rawKeywords, caseInsensitive) {\n  var scopeName = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : DEFAULT_KEYWORD_SCOPE;\n  /** @type {import(\"highlight.js/private\").KeywordDict} */\n  var compiledKeywords = Object.create(null);\n\n  // input can be a string of keywords, an array of keywords, or a object with\n  // named keys representing scopeName (which can then point to a string or array)\n  if (typeof rawKeywords === 'string') {\n    compileList(scopeName, rawKeywords.split(\" \"));\n  } else if (Array.isArray(rawKeywords)) {\n    compileList(scopeName, rawKeywords);\n  } else {\n    Object.keys(rawKeywords).forEach(function (scopeName) {\n      // collapse all our objects back into the parent object\n      Object.assign(compiledKeywords, compileKeywords(rawKeywords[scopeName], caseInsensitive, scopeName));\n    });\n  }\n  return compiledKeywords;\n\n  // ---\n\n  /**\n   * Compiles an individual list of keywords\n   *\n   * Ex: \"for if when while|5\"\n   *\n   * @param {string} scopeName\n   * @param {Array<string>} keywordList\n   */\n  function compileList(scopeName, keywordList) {\n    if (caseInsensitive) {\n      keywordList = keywordList.map(function (x) {\n        return x.toLowerCase();\n      });\n    }\n    keywordList.forEach(function (keyword) {\n      var pair = keyword.split('|');\n      compiledKeywords[pair[0]] = [scopeName, scoreForKeyword(pair[0], pair[1])];\n    });\n  }\n}\n\n/**\n * Returns the proper score for a given keyword\n *\n * Also takes into account comment keywords, which will be scored 0 UNLESS\n * another score has been manually assigned.\n * @param {string} keyword\n * @param {string} [providedScore]\n */\nfunction scoreForKeyword(keyword, providedScore) {\n  // manual scores always win over common keywords\n  // so you can force a score of 1 if you really insist\n  if (providedScore) {\n    return Number(providedScore);\n  }\n  return commonKeyword(keyword) ? 0 : 1;\n}\n\n/**\n * Determines if a given keyword is common or not\n *\n * @param {string} keyword */\nfunction commonKeyword(keyword) {\n  return COMMON_KEYWORDS.includes(keyword.toLowerCase());\n}\n\n/*\n\nFor the reasoning behind this please see:\nhttps://github.com/highlightjs/highlight.js/issues/2880#issuecomment-*********\n\n*/\n\n/**\n * @type {Record<string, boolean>}\n */\nvar seenDeprecations = {};\n\n/**\n * @param {string} message\n */\nvar error = function error(message) {\n  console.error(message);\n};\n\n/**\n * @param {string} message\n * @param {any} args\n */\nvar warn = function warn(message) {\n  var _console;\n  for (var _len4 = arguments.length, args = new Array(_len4 > 1 ? _len4 - 1 : 0), _key5 = 1; _key5 < _len4; _key5++) {\n    args[_key5 - 1] = arguments[_key5];\n  }\n  (_console = console).log.apply(_console, [`WARN: ${message}`].concat(args));\n};\n\n/**\n * @param {string} version\n * @param {string} message\n */\nvar deprecated = function deprecated(version, message) {\n  if (seenDeprecations[`${version}/${message}`]) return;\n  console.log(`Deprecated as of ${version}. ${message}`);\n  seenDeprecations[`${version}/${message}`] = true;\n};\n\n/* eslint-disable no-throw-literal */\n\n/**\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n*/\n\nvar MultiClassError = new Error();\n\n/**\n * Renumbers labeled scope names to account for additional inner match\n * groups that otherwise would break everything.\n *\n * Lets say we 3 match scopes:\n *\n *   { 1 => ..., 2 => ..., 3 => ... }\n *\n * So what we need is a clean match like this:\n *\n *   (a)(b)(c) => [ \"a\", \"b\", \"c\" ]\n *\n * But this falls apart with inner match groups:\n *\n * (a)(((b)))(c) => [\"a\", \"b\", \"b\", \"b\", \"c\" ]\n *\n * Our scopes are now \"out of alignment\" and we're repeating `b` 3 times.\n * What needs to happen is the numbers are remapped:\n *\n *   { 1 => ..., 2 => ..., 5 => ... }\n *\n * We also need to know that the ONLY groups that should be output\n * are 1, 2, and 5.  This function handles this behavior.\n *\n * @param {CompiledMode} mode\n * @param {Array<RegExp | string>} regexes\n * @param {{key: \"beginScope\"|\"endScope\"}} opts\n */\nfunction remapScopeNames(mode, regexes, _ref3) {\n  var key = _ref3.key;\n  var offset = 0;\n  var scopeNames = mode[key];\n  /** @type Record<number,boolean> */\n  var emit = {};\n  /** @type Record<number,string> */\n  var positions = {};\n  for (var i = 1; i <= regexes.length; i++) {\n    positions[i + offset] = scopeNames[i];\n    emit[i + offset] = true;\n    offset += countMatchGroups(regexes[i - 1]);\n  }\n  // we use _emit to keep track of which match groups are \"top-level\" to avoid double\n  // output from inside match groups\n  mode[key] = positions;\n  mode[key]._emit = emit;\n  mode[key]._multi = true;\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction beginMultiClass(mode) {\n  if (!Array.isArray(mode.begin)) return;\n  if (mode.skip || mode.excludeBegin || mode.returnBegin) {\n    error(\"skip, excludeBegin, returnBegin not compatible with beginScope: {}\");\n    throw MultiClassError;\n  }\n  if (typeof mode.beginScope !== \"object\" || mode.beginScope === null) {\n    error(\"beginScope must be object\");\n    throw MultiClassError;\n  }\n  remapScopeNames(mode, mode.begin, {\n    key: \"beginScope\"\n  });\n  mode.begin = _rewriteBackreferences(mode.begin, {\n    joinWith: \"\"\n  });\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction endMultiClass(mode) {\n  if (!Array.isArray(mode.end)) return;\n  if (mode.skip || mode.excludeEnd || mode.returnEnd) {\n    error(\"skip, excludeEnd, returnEnd not compatible with endScope: {}\");\n    throw MultiClassError;\n  }\n  if (typeof mode.endScope !== \"object\" || mode.endScope === null) {\n    error(\"endScope must be object\");\n    throw MultiClassError;\n  }\n  remapScopeNames(mode, mode.end, {\n    key: \"endScope\"\n  });\n  mode.end = _rewriteBackreferences(mode.end, {\n    joinWith: \"\"\n  });\n}\n\n/**\n * this exists only to allow `scope: {}` to be used beside `match:`\n * Otherwise `beginScope` would necessary and that would look weird\n\n  {\n    match: [ /def/, /\\w+/ ]\n    scope: { 1: \"keyword\" , 2: \"title\" }\n  }\n\n * @param {CompiledMode} mode\n */\nfunction scopeSugar(mode) {\n  if (mode.scope && typeof mode.scope === \"object\" && mode.scope !== null) {\n    mode.beginScope = mode.scope;\n    delete mode.scope;\n  }\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction MultiClass(mode) {\n  scopeSugar(mode);\n  if (typeof mode.beginScope === \"string\") {\n    mode.beginScope = {\n      _wrap: mode.beginScope\n    };\n  }\n  if (typeof mode.endScope === \"string\") {\n    mode.endScope = {\n      _wrap: mode.endScope\n    };\n  }\n  beginMultiClass(mode);\n  endMultiClass(mode);\n}\n\n/**\n@typedef {import('highlight.js').Mode} Mode\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n@typedef {import('highlight.js').Language} Language\n@typedef {import('highlight.js').HLJSPlugin} HLJSPlugin\n@typedef {import('highlight.js').CompiledLanguage} CompiledLanguage\n*/\n\n// compilation\n\n/**\n * Compiles a language definition result\n *\n * Given the raw result of a language definition (Language), compiles this so\n * that it is ready for highlighting code.\n * @param {Language} language\n * @returns {CompiledLanguage}\n */\nfunction compileLanguage(language) {\n  /**\n   * Builds a regex with the case sensitivity of the current language\n   *\n   * @param {RegExp | string} value\n   * @param {boolean} [global]\n   */\n  function langRe(value, global) {\n    return new RegExp(source(value), 'm' + (language.case_insensitive ? 'i' : '') + (language.unicodeRegex ? 'u' : '') + (global ? 'g' : ''));\n  }\n\n  /**\n    Stores multiple regular expressions and allows you to quickly search for\n    them all in a string simultaneously - returning the first match.  It does\n    this by creating a huge (a|b|c) regex - each individual item wrapped with ()\n    and joined by `|` - using match groups to track position.  When a match is\n    found checking which position in the array has content allows us to figure\n    out which of the original regexes / match groups triggered the match.\n     The match object itself (the result of `Regex.exec`) is returned but also\n    enhanced by merging in any meta-data that was registered with the regex.\n    This is how we keep track of which mode matched, and what type of rule\n    (`illegal`, `begin`, end, etc).\n  */\n  var MultiRegex = /*#__PURE__*/function () {\n    \"use strict\";\n\n    function MultiRegex() {\n      _classCallCheck(this, MultiRegex);\n      this.matchIndexes = {};\n      // @ts-ignore\n      this.regexes = [];\n      this.matchAt = 1;\n      this.position = 0;\n    }\n\n    // @ts-ignore\n    return _createClass(MultiRegex, [{\n      key: \"addRule\",\n      value: function addRule(re, opts) {\n        opts.position = this.position++;\n        // @ts-ignore\n        this.matchIndexes[this.matchAt] = opts;\n        this.regexes.push([opts, re]);\n        this.matchAt += countMatchGroups(re) + 1;\n      }\n    }, {\n      key: \"compile\",\n      value: function compile() {\n        if (this.regexes.length === 0) {\n          // avoids the need to check length every time exec is called\n          // @ts-ignore\n          this.exec = function () {\n            return null;\n          };\n        }\n        var terminators = this.regexes.map(function (el) {\n          return el[1];\n        });\n        this.matcherRe = langRe(_rewriteBackreferences(terminators, {\n          joinWith: '|'\n        }), true);\n        this.lastIndex = 0;\n      }\n\n      /** @param {string} s */\n    }, {\n      key: \"exec\",\n      value: function exec(s) {\n        this.matcherRe.lastIndex = this.lastIndex;\n        var match = this.matcherRe.exec(s);\n        if (!match) {\n          return null;\n        }\n\n        // eslint-disable-next-line no-undefined\n        var i = match.findIndex(function (el, i) {\n          return i > 0 && el !== undefined;\n        });\n        // @ts-ignore\n        var matchData = this.matchIndexes[i];\n        // trim off any earlier non-relevant match groups (ie, the other regex\n        // match groups that make up the multi-matcher)\n        match.splice(0, i);\n        return Object.assign(match, matchData);\n      }\n    }]);\n  }();\n  /*\n    Created to solve the key deficiently with MultiRegex - there is no way to\n    test for multiple matches at a single location.  Why would we need to do\n    that?  In the future a more dynamic engine will allow certain matches to be\n    ignored.  An example: if we matched say the 3rd regex in a large group but\n    decided to ignore it - we'd need to started testing again at the 4th\n    regex... but MultiRegex itself gives us no real way to do that.\n     So what this class creates MultiRegexs on the fly for whatever search\n    position they are needed.\n     NOTE: These additional MultiRegex objects are created dynamically.  For most\n    grammars most of the time we will never actually need anything more than the\n    first MultiRegex - so this shouldn't have too much overhead.\n     Say this is our search group, and we match regex3, but wish to ignore it.\n       regex1 | regex2 | regex3 | regex4 | regex5    ' ie, startAt = 0\n     What we need is a new MultiRegex that only includes the remaining\n    possibilities:\n       regex4 | regex5                               ' ie, startAt = 3\n     This class wraps all that complexity up in a simple API... `startAt` decides\n    where in the array of expressions to start doing the matching. It\n    auto-increments, so if a match is found at position 2, then startAt will be\n    set to 3.  If the end is reached startAt will return to 0.\n     MOST of the time the parser will be setting startAt manually to 0.\n  */\n  var ResumableMultiRegex = /*#__PURE__*/function () {\n    \"use strict\";\n\n    function ResumableMultiRegex() {\n      _classCallCheck(this, ResumableMultiRegex);\n      // @ts-ignore\n      this.rules = [];\n      // @ts-ignore\n      this.multiRegexes = [];\n      this.count = 0;\n      this.lastIndex = 0;\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    return _createClass(ResumableMultiRegex, [{\n      key: \"getMatcher\",\n      value: function getMatcher(index) {\n        if (this.multiRegexes[index]) return this.multiRegexes[index];\n        var matcher = new MultiRegex();\n        this.rules.slice(index).forEach(function (_ref4) {\n          var _ref5 = _slicedToArray(_ref4, 2),\n            re = _ref5[0],\n            opts = _ref5[1];\n          return matcher.addRule(re, opts);\n        });\n        matcher.compile();\n        this.multiRegexes[index] = matcher;\n        return matcher;\n      }\n    }, {\n      key: \"resumingScanAtSamePosition\",\n      value: function resumingScanAtSamePosition() {\n        return this.regexIndex !== 0;\n      }\n    }, {\n      key: \"considerAll\",\n      value: function considerAll() {\n        this.regexIndex = 0;\n      }\n\n      // @ts-ignore\n    }, {\n      key: \"addRule\",\n      value: function addRule(re, opts) {\n        this.rules.push([re, opts]);\n        if (opts.type === \"begin\") this.count++;\n      }\n\n      /** @param {string} s */\n    }, {\n      key: \"exec\",\n      value: function exec(s) {\n        var m = this.getMatcher(this.regexIndex);\n        m.lastIndex = this.lastIndex;\n        var result = m.exec(s);\n\n        // The following is because we have no easy way to say \"resume scanning at the\n        // existing position but also skip the current rule ONLY\". What happens is\n        // all prior rules are also skipped which can result in matching the wrong\n        // thing. Example of matching \"booger\":\n\n        // our matcher is [string, \"booger\", number]\n        //\n        // ....booger....\n\n        // if \"booger\" is ignored then we'd really need a regex to scan from the\n        // SAME position for only: [string, number] but ignoring \"booger\" (if it\n        // was the first match), a simple resume would scan ahead who knows how\n        // far looking only for \"number\", ignoring potential string matches (or\n        // future \"booger\" matches that might be valid.)\n\n        // So what we do: We execute two matchers, one resuming at the same\n        // position, but the second full matcher starting at the position after:\n\n        //     /--- resume first regex match here (for [number])\n        //     |/---- full match here for [string, \"booger\", number]\n        //     vv\n        // ....booger....\n\n        // Which ever results in a match first is then used. So this 3-4 step\n        // process essentially allows us to say \"match at this position, excluding\n        // a prior rule that was ignored\".\n        //\n        // 1. Match \"booger\" first, ignore. Also proves that [string] does non match.\n        // 2. Resume matching for [number]\n        // 3. Match at index + 1 for [string, \"booger\", number]\n        // 4. If #2 and #3 result in matches, which came first?\n        if (this.resumingScanAtSamePosition()) {\n          if (result && result.index === this.lastIndex) ;else {\n            // use the second matcher result\n            var m2 = this.getMatcher(0);\n            m2.lastIndex = this.lastIndex + 1;\n            result = m2.exec(s);\n          }\n        }\n        if (result) {\n          this.regexIndex += result.position + 1;\n          if (this.regexIndex === this.count) {\n            // wrap-around to considering all matches again\n            this.considerAll();\n          }\n        }\n        return result;\n      }\n    }]);\n  }();\n  /**\n   * Given a mode, builds a huge ResumableMultiRegex that can be used to walk\n   * the content and find matches.\n   *\n   * @param {CompiledMode} mode\n   * @returns {ResumableMultiRegex}\n   */\n  function buildModeRegex(mode) {\n    var mm = new ResumableMultiRegex();\n    mode.contains.forEach(function (term) {\n      return mm.addRule(term.begin, {\n        rule: term,\n        type: \"begin\"\n      });\n    });\n    if (mode.terminatorEnd) {\n      mm.addRule(mode.terminatorEnd, {\n        type: \"end\"\n      });\n    }\n    if (mode.illegal) {\n      mm.addRule(mode.illegal, {\n        type: \"illegal\"\n      });\n    }\n    return mm;\n  }\n\n  /** skip vs abort vs ignore\n   *\n   * @skip   - The mode is still entered and exited normally (and contains rules apply),\n   *           but all content is held and added to the parent buffer rather than being\n   *           output when the mode ends.  Mostly used with `sublanguage` to build up\n   *           a single large buffer than can be parsed by sublanguage.\n   *\n   *             - The mode begin ands ends normally.\n   *             - Content matched is added to the parent mode buffer.\n   *             - The parser cursor is moved forward normally.\n   *\n   * @abort  - A hack placeholder until we have ignore.  Aborts the mode (as if it\n   *           never matched) but DOES NOT continue to match subsequent `contains`\n   *           modes.  Abort is bad/suboptimal because it can result in modes\n   *           farther down not getting applied because an earlier rule eats the\n   *           content but then aborts.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is added to the mode buffer.\n   *             - The parser cursor is moved forward accordingly.\n   *\n   * @ignore - Ignores the mode (as if it never matched) and continues to match any\n   *           subsequent `contains` modes.  Ignore isn't technically possible with\n   *           the current parser implementation.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is ignored.\n   *             - The parser cursor is not moved forward.\n   */\n\n  /**\n   * Compiles an individual mode\n   *\n   * This can raise an error if the mode contains certain detectable known logic\n   * issues.\n   * @param {Mode} mode\n   * @param {CompiledMode | null} [parent]\n   * @returns {CompiledMode | never}\n   */\n  function compileMode(mode, parent) {\n    var _ref6;\n    var cmode = /** @type CompiledMode */mode;\n    if (mode.isCompiled) return cmode;\n    [scopeClassName,\n    // do this early so compiler extensions generally don't have to worry about\n    // the distinction between match/begin\n    compileMatch, MultiClass, beforeMatchExt].forEach(function (ext) {\n      return ext(mode, parent);\n    });\n    language.compilerExtensions.forEach(function (ext) {\n      return ext(mode, parent);\n    });\n\n    // __beforeBegin is considered private API, internal use only\n    mode.__beforeBegin = null;\n    [beginKeywords,\n    // do this later so compiler extensions that come earlier have access to the\n    // raw array if they wanted to perhaps manipulate it, etc.\n    compileIllegal,\n    // default to 1 relevance if not specified\n    compileRelevance].forEach(function (ext) {\n      return ext(mode, parent);\n    });\n    mode.isCompiled = true;\n    var keywordPattern = null;\n    if (typeof mode.keywords === \"object\" && mode.keywords.$pattern) {\n      // we need a copy because keywords might be compiled multiple times\n      // so we can't go deleting $pattern from the original on the first\n      // pass\n      mode.keywords = Object.assign({}, mode.keywords);\n      keywordPattern = mode.keywords.$pattern;\n      delete mode.keywords.$pattern;\n    }\n    keywordPattern = keywordPattern || /\\w+/;\n    if (mode.keywords) {\n      mode.keywords = compileKeywords(mode.keywords, language.case_insensitive);\n    }\n    cmode.keywordPatternRe = langRe(keywordPattern, true);\n    if (parent) {\n      if (!mode.begin) mode.begin = /\\B|\\b/;\n      cmode.beginRe = langRe(cmode.begin);\n      if (!mode.end && !mode.endsWithParent) mode.end = /\\B|\\b/;\n      if (mode.end) cmode.endRe = langRe(cmode.end);\n      cmode.terminatorEnd = source(cmode.end) || '';\n      if (mode.endsWithParent && parent.terminatorEnd) {\n        cmode.terminatorEnd += (mode.end ? '|' : '') + parent.terminatorEnd;\n      }\n    }\n    if (mode.illegal) cmode.illegalRe = langRe(/** @type {RegExp | string} */mode.illegal);\n    if (!mode.contains) mode.contains = [];\n    mode.contains = (_ref6 = []).concat.apply(_ref6, _toConsumableArray(mode.contains.map(function (c) {\n      return expandOrCloneMode(c === 'self' ? mode : c);\n    })));\n    mode.contains.forEach(function (c) {\n      compileMode(/** @type Mode */c, cmode);\n    });\n    if (mode.starts) {\n      compileMode(mode.starts, parent);\n    }\n    cmode.matcher = buildModeRegex(cmode);\n    return cmode;\n  }\n  if (!language.compilerExtensions) language.compilerExtensions = [];\n\n  // self is not valid at the top-level\n  if (language.contains && language.contains.includes('self')) {\n    throw new Error(\"ERR: contains `self` is not supported at the top-level of a language.  See documentation.\");\n  }\n\n  // we need a null object, which inherit will guarantee\n  language.classNameAliases = inherit$1(language.classNameAliases || {});\n  return compileMode(/** @type Mode */language);\n}\n\n/**\n * Determines if a mode has a dependency on it's parent or not\n *\n * If a mode does have a parent dependency then often we need to clone it if\n * it's used in multiple places so that each copy points to the correct parent,\n * where-as modes without a parent can often safely be re-used at the bottom of\n * a mode chain.\n *\n * @param {Mode | null} mode\n * @returns {boolean} - is there a dependency on the parent?\n * */\nfunction dependencyOnParent(mode) {\n  if (!mode) return false;\n  return mode.endsWithParent || dependencyOnParent(mode.starts);\n}\n\n/**\n * Expands a mode or clones it if necessary\n *\n * This is necessary for modes with parental dependenceis (see notes on\n * `dependencyOnParent`) and for nodes that have `variants` - which must then be\n * exploded into their own individual modes at compile time.\n *\n * @param {Mode} mode\n * @returns {Mode | Mode[]}\n * */\nfunction expandOrCloneMode(mode) {\n  if (mode.variants && !mode.cachedVariants) {\n    mode.cachedVariants = mode.variants.map(function (variant) {\n      return inherit$1(mode, {\n        variants: null\n      }, variant);\n    });\n  }\n\n  // EXPAND\n  // if we have variants then essentially \"replace\" the mode with the variants\n  // this happens in compileMode, where this function is called from\n  if (mode.cachedVariants) {\n    return mode.cachedVariants;\n  }\n\n  // CLONE\n  // if we have dependencies on parents then we need a unique\n  // instance of ourselves, so we can be reused with many\n  // different parents without issue\n  if (dependencyOnParent(mode)) {\n    return inherit$1(mode, {\n      starts: mode.starts ? inherit$1(mode.starts) : null\n    });\n  }\n  if (Object.isFrozen(mode)) {\n    return inherit$1(mode);\n  }\n\n  // no special dependency issues, just return ourselves\n  return mode;\n}\nvar version = \"11.11.1\";\nvar HTMLInjectionError = /*#__PURE__*/function (_Error) {\n  \"use strict\";\n\n  function HTMLInjectionError(reason, html) {\n    var _this3;\n    _classCallCheck(this, HTMLInjectionError);\n    _this3 = _callSuper(this, HTMLInjectionError, [reason]);\n    _this3.name = \"HTMLInjectionError\";\n    _this3.html = html;\n    return _this3;\n  }\n  _inherits(HTMLInjectionError, _Error);\n  return _createClass(HTMLInjectionError);\n}(/*#__PURE__*/_wrapNativeSuper(Error));\n/*\nSyntax highlighting with language autodetection.\nhttps://highlightjs.org/\n*/\n/**\n@typedef {import('highlight.js').Mode} Mode\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n@typedef {import('highlight.js').CompiledScope} CompiledScope\n@typedef {import('highlight.js').Language} Language\n@typedef {import('highlight.js').HLJSApi} HLJSApi\n@typedef {import('highlight.js').HLJSPlugin} HLJSPlugin\n@typedef {import('highlight.js').PluginEvent} PluginEvent\n@typedef {import('highlight.js').HLJSOptions} HLJSOptions\n@typedef {import('highlight.js').LanguageFn} LanguageFn\n@typedef {import('highlight.js').HighlightedHTMLElement} HighlightedHTMLElement\n@typedef {import('highlight.js').BeforeHighlightContext} BeforeHighlightContext\n@typedef {import('highlight.js/private').MatchType} MatchType\n@typedef {import('highlight.js/private').KeywordData} KeywordData\n@typedef {import('highlight.js/private').EnhancedMatch} EnhancedMatch\n@typedef {import('highlight.js/private').AnnotatedError} AnnotatedError\n@typedef {import('highlight.js').AutoHighlightResult} AutoHighlightResult\n@typedef {import('highlight.js').HighlightOptions} HighlightOptions\n@typedef {import('highlight.js').HighlightResult} HighlightResult\n*/\nvar escape = escapeHTML;\nvar inherit = inherit$1;\nvar NO_MATCH = Symbol(\"nomatch\");\nvar MAX_KEYWORD_HITS = 7;\n\n/**\n * @param {any} hljs - object that is extended (legacy)\n * @returns {HLJSApi}\n */\nvar HLJS = function HLJS(hljs) {\n  // Global internal variables used within the highlight.js library.\n  /** @type {Record<string, Language>} */\n  var languages = Object.create(null);\n  /** @type {Record<string, string>} */\n  var aliases = Object.create(null);\n  /** @type {HLJSPlugin[]} */\n  var plugins = [];\n\n  // safe/production mode - swallows more errors, tries to keep running\n  // even if a single syntax or parse hits a fatal error\n  var SAFE_MODE = true;\n  var LANGUAGE_NOT_FOUND = \"Could not find the language '{}', did you forget to load/include a language module?\";\n  /** @type {Language} */\n  var PLAINTEXT_LANGUAGE = {\n    disableAutodetect: true,\n    name: 'Plain text',\n    contains: []\n  };\n\n  // Global options used when within external APIs. This is modified when\n  // calling the `hljs.configure` function.\n  /** @type HLJSOptions */\n  var options = {\n    ignoreUnescapedHTML: false,\n    throwUnescapedHTML: false,\n    noHighlightRe: /^(no-?highlight)$/i,\n    languageDetectRe: /\\blang(?:uage)?-([\\w-]+)\\b/i,\n    classPrefix: 'hljs-',\n    cssSelector: 'pre code',\n    languages: null,\n    // beta configuration options, subject to change, welcome to discuss\n    // https://github.com/highlightjs/highlight.js/issues/1086\n    __emitter: TokenTreeEmitter\n  };\n\n  /* Utility functions */\n\n  /**\n   * Tests a language name to see if highlighting should be skipped\n   * @param {string} languageName\n   */\n  function shouldNotHighlight(languageName) {\n    return options.noHighlightRe.test(languageName);\n  }\n\n  /**\n   * @param {HighlightedHTMLElement} block - the HTML element to determine language for\n   */\n  function blockLanguage(block) {\n    var classes = block.className + ' ';\n    classes += block.parentNode ? block.parentNode.className : '';\n\n    // language-* takes precedence over non-prefixed class names.\n    var match = options.languageDetectRe.exec(classes);\n    if (match) {\n      var language = getLanguage(match[1]);\n      if (!language) {\n        warn(LANGUAGE_NOT_FOUND.replace(\"{}\", match[1]));\n        warn(\"Falling back to no-highlight mode for this block.\", block);\n      }\n      return language ? match[1] : 'no-highlight';\n    }\n    return classes.split(/\\s+/).find(function (_class) {\n      return shouldNotHighlight(_class) || getLanguage(_class);\n    });\n  }\n\n  /**\n   * Core highlighting function.\n   *\n   * OLD API\n   * highlight(lang, code, ignoreIllegals, continuation)\n   *\n   * NEW API\n   * highlight(code, {lang, ignoreIllegals})\n   *\n   * @param {string} codeOrLanguageName - the language to use for highlighting\n   * @param {string | HighlightOptions} optionsOrCode - the code to highlight\n   * @param {boolean} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   *\n   * @returns {HighlightResult} Result - an object that represents the result\n   * @property {string} language - the language name\n   * @property {number} relevance - the relevance score\n   * @property {string} value - the highlighted HTML code\n   * @property {string} code - the original raw code\n   * @property {CompiledMode} top - top of the current mode stack\n   * @property {boolean} illegal - indicates whether any illegal matches were found\n  */\n  function highlight(codeOrLanguageName, optionsOrCode, ignoreIllegals) {\n    var code = \"\";\n    var languageName = \"\";\n    if (typeof optionsOrCode === \"object\") {\n      code = codeOrLanguageName;\n      ignoreIllegals = optionsOrCode.ignoreIllegals;\n      languageName = optionsOrCode.language;\n    } else {\n      // old API\n      deprecated(\"10.7.0\", \"highlight(lang, code, ...args) has been deprecated.\");\n      deprecated(\"10.7.0\", \"Please use highlight(code, options) instead.\\nhttps://github.com/highlightjs/highlight.js/issues/2277\");\n      languageName = codeOrLanguageName;\n      code = optionsOrCode;\n    }\n\n    // https://github.com/highlightjs/highlight.js/issues/3149\n    // eslint-disable-next-line no-undefined\n    if (ignoreIllegals === undefined) {\n      ignoreIllegals = true;\n    }\n\n    /** @type {BeforeHighlightContext} */\n    var context = {\n      code,\n      language: languageName\n    };\n    // the plugin can change the desired language or the code to be highlighted\n    // just be changing the object it was passed\n    fire(\"before:highlight\", context);\n\n    // a before plugin can usurp the result completely by providing it's own\n    // in which case we don't even need to call highlight\n    var result = context.result ? context.result : _highlight(context.language, context.code, ignoreIllegals);\n    result.code = context.code;\n    // the plugin can change anything in result to suite it\n    fire(\"after:highlight\", result);\n    return result;\n  }\n\n  /**\n   * private highlight that's used internally and does not fire callbacks\n   *\n   * @param {string} languageName - the language to use for highlighting\n   * @param {string} codeToHighlight - the code to highlight\n   * @param {boolean?} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   * @param {CompiledMode?} [continuation] - current continuation mode, if any\n   * @returns {HighlightResult} - result of the highlight operation\n  */\n  function _highlight(languageName, codeToHighlight, ignoreIllegals, continuation) {\n    var keywordHits = Object.create(null);\n\n    /**\n     * Return keyword data if a match is a keyword\n     * @param {CompiledMode} mode - current mode\n     * @param {string} matchText - the textual match\n     * @returns {KeywordData | false}\n     */\n    function keywordData(mode, matchText) {\n      return mode.keywords[matchText];\n    }\n    function processKeywords() {\n      if (!top.keywords) {\n        emitter.addText(modeBuffer);\n        return;\n      }\n      var lastIndex = 0;\n      top.keywordPatternRe.lastIndex = 0;\n      var match = top.keywordPatternRe.exec(modeBuffer);\n      var buf = \"\";\n      while (match) {\n        buf += modeBuffer.substring(lastIndex, match.index);\n        var word = language.case_insensitive ? match[0].toLowerCase() : match[0];\n        var data = keywordData(top, word);\n        if (data) {\n          var _data = _slicedToArray(data, 2),\n            kind = _data[0],\n            keywordRelevance = _data[1];\n          emitter.addText(buf);\n          buf = \"\";\n          keywordHits[word] = (keywordHits[word] || 0) + 1;\n          if (keywordHits[word] <= MAX_KEYWORD_HITS) relevance += keywordRelevance;\n          if (kind.startsWith(\"_\")) {\n            // _ implied for relevance only, do not highlight\n            // by applying a class name\n            buf += match[0];\n          } else {\n            var cssClass = language.classNameAliases[kind] || kind;\n            emitKeyword(match[0], cssClass);\n          }\n        } else {\n          buf += match[0];\n        }\n        lastIndex = top.keywordPatternRe.lastIndex;\n        match = top.keywordPatternRe.exec(modeBuffer);\n      }\n      buf += modeBuffer.substring(lastIndex);\n      emitter.addText(buf);\n    }\n    function processSubLanguage() {\n      if (modeBuffer === \"\") return;\n      /** @type HighlightResult */\n      var result = null;\n      if (typeof top.subLanguage === 'string') {\n        if (!languages[top.subLanguage]) {\n          emitter.addText(modeBuffer);\n          return;\n        }\n        result = _highlight(top.subLanguage, modeBuffer, true, continuations[top.subLanguage]);\n        continuations[top.subLanguage] = /** @type {CompiledMode} */result._top;\n      } else {\n        result = highlightAuto(modeBuffer, top.subLanguage.length ? top.subLanguage : null);\n      }\n\n      // Counting embedded language score towards the host language may be disabled\n      // with zeroing the containing mode relevance. Use case in point is Markdown that\n      // allows XML everywhere and makes every XML snippet to have a much larger Markdown\n      // score.\n      if (top.relevance > 0) {\n        relevance += result.relevance;\n      }\n      emitter.__addSublanguage(result._emitter, result.language);\n    }\n    function processBuffer() {\n      if (top.subLanguage != null) {\n        processSubLanguage();\n      } else {\n        processKeywords();\n      }\n      modeBuffer = '';\n    }\n\n    /**\n     * @param {string} text\n     * @param {string} scope\n     */\n    function emitKeyword(keyword, scope) {\n      if (keyword === \"\") return;\n      emitter.startScope(scope);\n      emitter.addText(keyword);\n      emitter.endScope();\n    }\n\n    /**\n     * @param {CompiledScope} scope\n     * @param {RegExpMatchArray} match\n     */\n    function emitMultiClass(scope, match) {\n      var i = 1;\n      var max = match.length - 1;\n      while (i <= max) {\n        if (!scope._emit[i]) {\n          i++;\n          continue;\n        }\n        var klass = language.classNameAliases[scope[i]] || scope[i];\n        var text = match[i];\n        if (klass) {\n          emitKeyword(text, klass);\n        } else {\n          modeBuffer = text;\n          processKeywords();\n          modeBuffer = \"\";\n        }\n        i++;\n      }\n    }\n\n    /**\n     * @param {CompiledMode} mode - new mode to start\n     * @param {RegExpMatchArray} match\n     */\n    function startNewMode(mode, match) {\n      if (mode.scope && typeof mode.scope === \"string\") {\n        emitter.openNode(language.classNameAliases[mode.scope] || mode.scope);\n      }\n      if (mode.beginScope) {\n        // beginScope just wraps the begin match itself in a scope\n        if (mode.beginScope._wrap) {\n          emitKeyword(modeBuffer, language.classNameAliases[mode.beginScope._wrap] || mode.beginScope._wrap);\n          modeBuffer = \"\";\n        } else if (mode.beginScope._multi) {\n          // at this point modeBuffer should just be the match\n          emitMultiClass(mode.beginScope, match);\n          modeBuffer = \"\";\n        }\n      }\n      top = Object.create(mode, {\n        parent: {\n          value: top\n        }\n      });\n      return top;\n    }\n\n    /**\n     * @param {CompiledMode } mode - the mode to potentially end\n     * @param {RegExpMatchArray} match - the latest match\n     * @param {string} matchPlusRemainder - match plus remainder of content\n     * @returns {CompiledMode | void} - the next mode, or if void continue on in current mode\n     */\n    function endOfMode(mode, match, matchPlusRemainder) {\n      var matched = startsWith(mode.endRe, matchPlusRemainder);\n      if (matched) {\n        if (mode[\"on:end\"]) {\n          var resp = new Response(mode);\n          mode[\"on:end\"](match, resp);\n          if (resp.isMatchIgnored) matched = false;\n        }\n        if (matched) {\n          while (mode.endsParent && mode.parent) {\n            mode = mode.parent;\n          }\n          return mode;\n        }\n      }\n      // even if on:end fires an `ignore` it's still possible\n      // that we might trigger the end node because of a parent mode\n      if (mode.endsWithParent) {\n        return endOfMode(mode.parent, match, matchPlusRemainder);\n      }\n    }\n\n    /**\n     * Handle matching but then ignoring a sequence of text\n     *\n     * @param {string} lexeme - string containing full match text\n     */\n    function doIgnore(lexeme) {\n      if (top.matcher.regexIndex === 0) {\n        // no more regexes to potentially match here, so we move the cursor forward one\n        // space\n        modeBuffer += lexeme[0];\n        return 1;\n      } else {\n        // no need to move the cursor, we still have additional regexes to try and\n        // match at this very spot\n        resumeScanAtSamePosition = true;\n        return 0;\n      }\n    }\n\n    /**\n     * Handle the start of a new potential mode match\n     *\n     * @param {EnhancedMatch} match - the current match\n     * @returns {number} how far to advance the parse cursor\n     */\n    function doBeginMatch(match) {\n      var lexeme = match[0];\n      var newMode = match.rule;\n      var resp = new Response(newMode);\n      // first internal before callbacks, then the public ones\n      var beforeCallbacks = [newMode.__beforeBegin, newMode[\"on:begin\"]];\n      for (var _i = 0, _beforeCallbacks = beforeCallbacks; _i < _beforeCallbacks.length; _i++) {\n        var cb = _beforeCallbacks[_i];\n        if (!cb) continue;\n        cb(match, resp);\n        if (resp.isMatchIgnored) return doIgnore(lexeme);\n      }\n      if (newMode.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (newMode.excludeBegin) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (!newMode.returnBegin && !newMode.excludeBegin) {\n          modeBuffer = lexeme;\n        }\n      }\n      startNewMode(newMode, match);\n      return newMode.returnBegin ? 0 : lexeme.length;\n    }\n\n    /**\n     * Handle the potential end of mode\n     *\n     * @param {RegExpMatchArray} match - the current match\n     */\n    function doEndMatch(match) {\n      var lexeme = match[0];\n      var matchPlusRemainder = codeToHighlight.substring(match.index);\n      var endMode = endOfMode(top, match, matchPlusRemainder);\n      if (!endMode) {\n        return NO_MATCH;\n      }\n      var origin = top;\n      if (top.endScope && top.endScope._wrap) {\n        processBuffer();\n        emitKeyword(lexeme, top.endScope._wrap);\n      } else if (top.endScope && top.endScope._multi) {\n        processBuffer();\n        emitMultiClass(top.endScope, match);\n      } else if (origin.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (!(origin.returnEnd || origin.excludeEnd)) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (origin.excludeEnd) {\n          modeBuffer = lexeme;\n        }\n      }\n      do {\n        if (top.scope) {\n          emitter.closeNode();\n        }\n        if (!top.skip && !top.subLanguage) {\n          relevance += top.relevance;\n        }\n        top = top.parent;\n      } while (top !== endMode.parent);\n      if (endMode.starts) {\n        startNewMode(endMode.starts, match);\n      }\n      return origin.returnEnd ? 0 : lexeme.length;\n    }\n    function processContinuations() {\n      var list = [];\n      for (var current = top; current !== language; current = current.parent) {\n        if (current.scope) {\n          list.unshift(current.scope);\n        }\n      }\n      list.forEach(function (item) {\n        return emitter.openNode(item);\n      });\n    }\n\n    /** @type {{type?: MatchType, index?: number, rule?: Mode}}} */\n    var lastMatch = {};\n\n    /**\n     *  Process an individual match\n     *\n     * @param {string} textBeforeMatch - text preceding the match (since the last match)\n     * @param {EnhancedMatch} [match] - the match itself\n     */\n    function processLexeme(textBeforeMatch, match) {\n      var lexeme = match && match[0];\n\n      // add non-matched text to the current mode buffer\n      modeBuffer += textBeforeMatch;\n      if (lexeme == null) {\n        processBuffer();\n        return 0;\n      }\n\n      // we've found a 0 width match and we're stuck, so we need to advance\n      // this happens when we have badly behaved rules that have optional matchers to the degree that\n      // sometimes they can end up matching nothing at all\n      // Ref: https://github.com/highlightjs/highlight.js/issues/2140\n      if (lastMatch.type === \"begin\" && match.type === \"end\" && lastMatch.index === match.index && lexeme === \"\") {\n        // spit the \"skipped\" character that our regex choked on back into the output sequence\n        modeBuffer += codeToHighlight.slice(match.index, match.index + 1);\n        if (!SAFE_MODE) {\n          /** @type {AnnotatedError} */\n          var err = new Error(`0 width match regex (${languageName})`);\n          err.languageName = languageName;\n          err.badRule = lastMatch.rule;\n          throw err;\n        }\n        return 1;\n      }\n      lastMatch = match;\n      if (match.type === \"begin\") {\n        return doBeginMatch(match);\n      } else if (match.type === \"illegal\" && !ignoreIllegals) {\n        // illegal match, we do not continue processing\n        /** @type {AnnotatedError} */\n        var _err = new Error('Illegal lexeme \"' + lexeme + '\" for mode \"' + (top.scope || '<unnamed>') + '\"');\n        _err.mode = top;\n        throw _err;\n      } else if (match.type === \"end\") {\n        var processed = doEndMatch(match);\n        if (processed !== NO_MATCH) {\n          return processed;\n        }\n      }\n\n      // edge case for when illegal matches $ (end of line) which is technically\n      // a 0 width match but not a begin/end match so it's not caught by the\n      // first handler (when ignoreIllegals is true)\n      if (match.type === \"illegal\" && lexeme === \"\") {\n        // advance so we aren't stuck in an infinite loop\n        modeBuffer += \"\\n\";\n        return 1;\n      }\n\n      // infinite loops are BAD, this is a last ditch catch all. if we have a\n      // decent number of iterations yet our index (cursor position in our\n      // parsing) still 3x behind our index then something is very wrong\n      // so we bail\n      if (iterations > 100000 && iterations > match.index * 3) {\n        var _err2 = new Error('potential infinite loop, way more iterations than matches');\n        throw _err2;\n      }\n\n      /*\n      Why might be find ourselves here?  An potential end match that was\n      triggered but could not be completed.  IE, `doEndMatch` returned NO_MATCH.\n      (this could be because a callback requests the match be ignored, etc)\n       This causes no real harm other than stopping a few times too many.\n      */\n\n      modeBuffer += lexeme;\n      return lexeme.length;\n    }\n    var language = getLanguage(languageName);\n    if (!language) {\n      error(LANGUAGE_NOT_FOUND.replace(\"{}\", languageName));\n      throw new Error('Unknown language: \"' + languageName + '\"');\n    }\n    var md = compileLanguage(language);\n    var result = '';\n    /** @type {CompiledMode} */\n    var top = continuation || md;\n    /** @type Record<string,CompiledMode> */\n    var continuations = {}; // keep continuations for sub-languages\n    var emitter = new options.__emitter(options);\n    processContinuations();\n    var modeBuffer = '';\n    var relevance = 0;\n    var index = 0;\n    var iterations = 0;\n    var resumeScanAtSamePosition = false;\n    try {\n      if (!language.__emitTokens) {\n        top.matcher.considerAll();\n        for (;;) {\n          iterations++;\n          if (resumeScanAtSamePosition) {\n            // only regexes not matched previously will now be\n            // considered for a potential match\n            resumeScanAtSamePosition = false;\n          } else {\n            top.matcher.considerAll();\n          }\n          top.matcher.lastIndex = index;\n          var match = top.matcher.exec(codeToHighlight);\n          // console.log(\"match\", match[0], match.rule && match.rule.begin)\n\n          if (!match) break;\n          var beforeMatch = codeToHighlight.substring(index, match.index);\n          var processedCount = processLexeme(beforeMatch, match);\n          index = match.index + processedCount;\n        }\n        processLexeme(codeToHighlight.substring(index));\n      } else {\n        language.__emitTokens(codeToHighlight, emitter);\n      }\n      emitter.finalize();\n      result = emitter.toHTML();\n      return {\n        language: languageName,\n        value: result,\n        relevance,\n        illegal: false,\n        _emitter: emitter,\n        _top: top\n      };\n    } catch (err) {\n      if (err.message && err.message.includes('Illegal')) {\n        return {\n          language: languageName,\n          value: escape(codeToHighlight),\n          illegal: true,\n          relevance: 0,\n          _illegalBy: {\n            message: err.message,\n            index,\n            context: codeToHighlight.slice(index - 100, index + 100),\n            mode: err.mode,\n            resultSoFar: result\n          },\n          _emitter: emitter\n        };\n      } else if (SAFE_MODE) {\n        return {\n          language: languageName,\n          value: escape(codeToHighlight),\n          illegal: false,\n          relevance: 0,\n          errorRaised: err,\n          _emitter: emitter,\n          _top: top\n        };\n      } else {\n        throw err;\n      }\n    }\n  }\n\n  /**\n   * returns a valid highlight result, without actually doing any actual work,\n   * auto highlight starts with this and it's possible for small snippets that\n   * auto-detection may not find a better match\n   * @param {string} code\n   * @returns {HighlightResult}\n   */\n  function justTextHighlightResult(code) {\n    var result = {\n      value: escape(code),\n      illegal: false,\n      relevance: 0,\n      _top: PLAINTEXT_LANGUAGE,\n      _emitter: new options.__emitter(options)\n    };\n    result._emitter.addText(code);\n    return result;\n  }\n\n  /**\n  Highlighting with language detection. Accepts a string with the code to\n  highlight. Returns an object with the following properties:\n   - language (detected language)\n  - relevance (int)\n  - value (an HTML string with highlighting markup)\n  - secondBest (object with the same structure for second-best heuristically\n    detected language, may be absent)\n     @param {string} code\n    @param {Array<string>} [languageSubset]\n    @returns {AutoHighlightResult}\n  */\n  function highlightAuto(code, languageSubset) {\n    languageSubset = languageSubset || options.languages || Object.keys(languages);\n    var plaintext = justTextHighlightResult(code);\n    var results = languageSubset.filter(getLanguage).filter(autoDetection).map(function (name) {\n      return _highlight(name, code, false);\n    });\n    results.unshift(plaintext); // plaintext is always an option\n\n    var sorted = results.sort(function (a, b) {\n      // sort base on relevance\n      if (a.relevance !== b.relevance) return b.relevance - a.relevance;\n\n      // always award the tie to the base language\n      // ie if C++ and Arduino are tied, it's more likely to be C++\n      if (a.language && b.language) {\n        if (getLanguage(a.language).supersetOf === b.language) {\n          return 1;\n        } else if (getLanguage(b.language).supersetOf === a.language) {\n          return -1;\n        }\n      }\n\n      // otherwise say they are equal, which has the effect of sorting on\n      // relevance while preserving the original ordering - which is how ties\n      // have historically been settled, ie the language that comes first always\n      // wins in the case of a tie\n      return 0;\n    });\n    var _sorted = _slicedToArray(sorted, 2),\n      best = _sorted[0],\n      secondBest = _sorted[1];\n\n    /** @type {AutoHighlightResult} */\n    var result = best;\n    result.secondBest = secondBest;\n    return result;\n  }\n\n  /**\n   * Builds new class name for block given the language name\n   *\n   * @param {HTMLElement} element\n   * @param {string} [currentLang]\n   * @param {string} [resultLang]\n   */\n  function updateClassName(element, currentLang, resultLang) {\n    var language = currentLang && aliases[currentLang] || resultLang;\n    element.classList.add(\"hljs\");\n    element.classList.add(`language-${language}`);\n  }\n\n  /**\n   * Applies highlighting to a DOM node containing code.\n   *\n   * @param {HighlightedHTMLElement} element - the HTML element to highlight\n  */\n  function highlightElement(element) {\n    /** @type HTMLElement */\n    var node = null;\n    var language = blockLanguage(element);\n    if (shouldNotHighlight(language)) return;\n    fire(\"before:highlightElement\", {\n      el: element,\n      language\n    });\n    if (element.dataset.highlighted) {\n      console.log(\"Element previously highlighted. To highlight again, first unset `dataset.highlighted`.\", element);\n      return;\n    }\n\n    // we should be all text, no child nodes (unescaped HTML) - this is possibly\n    // an HTML injection attack - it's likely too late if this is already in\n    // production (the code has likely already done its damage by the time\n    // we're seeing it)... but we yell loudly about this so that hopefully it's\n    // more likely to be caught in development before making it to production\n    if (element.children.length > 0) {\n      if (!options.ignoreUnescapedHTML) {\n        console.warn(\"One of your code blocks includes unescaped HTML. This is a potentially serious security risk.\");\n        console.warn(\"https://github.com/highlightjs/highlight.js/wiki/security\");\n        console.warn(\"The element with unescaped HTML:\");\n        console.warn(element);\n      }\n      if (options.throwUnescapedHTML) {\n        var err = new HTMLInjectionError(\"One of your code blocks includes unescaped HTML.\", element.innerHTML);\n        throw err;\n      }\n    }\n    node = element;\n    var text = node.textContent;\n    var result = language ? highlight(text, {\n      language,\n      ignoreIllegals: true\n    }) : highlightAuto(text);\n    element.innerHTML = result.value;\n    element.dataset.highlighted = \"yes\";\n    updateClassName(element, language, result.language);\n    element.result = {\n      language: result.language,\n      // TODO: remove with version 11.0\n      re: result.relevance,\n      relevance: result.relevance\n    };\n    if (result.secondBest) {\n      element.secondBest = {\n        language: result.secondBest.language,\n        relevance: result.secondBest.relevance\n      };\n    }\n    fire(\"after:highlightElement\", {\n      el: element,\n      result,\n      text\n    });\n  }\n\n  /**\n   * Updates highlight.js global options with the passed options\n   *\n   * @param {Partial<HLJSOptions>} userOptions\n   */\n  function configure(userOptions) {\n    options = inherit(options, userOptions);\n  }\n\n  // TODO: remove v12, deprecated\n  var initHighlighting = function initHighlighting() {\n    highlightAll();\n    deprecated(\"10.6.0\", \"initHighlighting() deprecated.  Use highlightAll() now.\");\n  };\n\n  // TODO: remove v12, deprecated\n  function initHighlightingOnLoad() {\n    highlightAll();\n    deprecated(\"10.6.0\", \"initHighlightingOnLoad() deprecated.  Use highlightAll() now.\");\n  }\n  var wantsHighlight = false;\n\n  /**\n   * auto-highlights all pre>code elements on the page\n   */\n  function highlightAll() {\n    function boot() {\n      // if a highlight was requested before DOM was loaded, do now\n      highlightAll();\n    }\n\n    // if we are called too early in the loading process\n    if (document.readyState === \"loading\") {\n      // make sure the event listener is only added once\n      if (!wantsHighlight) {\n        window.addEventListener('DOMContentLoaded', boot, false);\n      }\n      wantsHighlight = true;\n      return;\n    }\n    var blocks = document.querySelectorAll(options.cssSelector);\n    blocks.forEach(highlightElement);\n  }\n\n  /**\n   * Register a language grammar module\n   *\n   * @param {string} languageName\n   * @param {LanguageFn} languageDefinition\n   */\n  function registerLanguage(languageName, languageDefinition) {\n    var lang = null;\n    try {\n      lang = languageDefinition(hljs);\n    } catch (error$1) {\n      error(\"Language definition for '{}' could not be registered.\".replace(\"{}\", languageName));\n      // hard or soft error\n      if (!SAFE_MODE) {\n        throw error$1;\n      } else {\n        error(error$1);\n      }\n      // languages that have serious errors are replaced with essentially a\n      // \"plaintext\" stand-in so that the code blocks will still get normal\n      // css classes applied to them - and one bad language won't break the\n      // entire highlighter\n      lang = PLAINTEXT_LANGUAGE;\n    }\n    // give it a temporary name if it doesn't have one in the meta-data\n    if (!lang.name) lang.name = languageName;\n    languages[languageName] = lang;\n    lang.rawDefinition = languageDefinition.bind(null, hljs);\n    if (lang.aliases) {\n      registerAliases(lang.aliases, {\n        languageName\n      });\n    }\n  }\n\n  /**\n   * Remove a language grammar module\n   *\n   * @param {string} languageName\n   */\n  function unregisterLanguage(languageName) {\n    delete languages[languageName];\n    for (var _i2 = 0, _Object$keys = Object.keys(aliases); _i2 < _Object$keys.length; _i2++) {\n      var alias = _Object$keys[_i2];\n      if (aliases[alias] === languageName) {\n        delete aliases[alias];\n      }\n    }\n  }\n\n  /**\n   * @returns {string[]} List of language internal names\n   */\n  function listLanguages() {\n    return Object.keys(languages);\n  }\n\n  /**\n   * @param {string} name - name of the language to retrieve\n   * @returns {Language | undefined}\n   */\n  function getLanguage(name) {\n    name = (name || '').toLowerCase();\n    return languages[name] || languages[aliases[name]];\n  }\n\n  /**\n   *\n   * @param {string|string[]} aliasList - single alias or list of aliases\n   * @param {{languageName: string}} opts\n   */\n  function registerAliases(aliasList, _ref7) {\n    var languageName = _ref7.languageName;\n    if (typeof aliasList === 'string') {\n      aliasList = [aliasList];\n    }\n    aliasList.forEach(function (alias) {\n      aliases[alias.toLowerCase()] = languageName;\n    });\n  }\n\n  /**\n   * Determines if a given language has auto-detection enabled\n   * @param {string} name - name of the language\n   */\n  function autoDetection(name) {\n    var lang = getLanguage(name);\n    return lang && !lang.disableAutodetect;\n  }\n\n  /**\n   * Upgrades the old highlightBlock plugins to the new\n   * highlightElement API\n   * @param {HLJSPlugin} plugin\n   */\n  function upgradePluginAPI(plugin) {\n    // TODO: remove with v12\n    if (plugin[\"before:highlightBlock\"] && !plugin[\"before:highlightElement\"]) {\n      plugin[\"before:highlightElement\"] = function (data) {\n        plugin[\"before:highlightBlock\"](Object.assign({\n          block: data.el\n        }, data));\n      };\n    }\n    if (plugin[\"after:highlightBlock\"] && !plugin[\"after:highlightElement\"]) {\n      plugin[\"after:highlightElement\"] = function (data) {\n        plugin[\"after:highlightBlock\"](Object.assign({\n          block: data.el\n        }, data));\n      };\n    }\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function addPlugin(plugin) {\n    upgradePluginAPI(plugin);\n    plugins.push(plugin);\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function removePlugin(plugin) {\n    var index = plugins.indexOf(plugin);\n    if (index !== -1) {\n      plugins.splice(index, 1);\n    }\n  }\n\n  /**\n   *\n   * @param {PluginEvent} event\n   * @param {any} args\n   */\n  function fire(event, args) {\n    var cb = event;\n    plugins.forEach(function (plugin) {\n      if (plugin[cb]) {\n        plugin[cb](args);\n      }\n    });\n  }\n\n  /**\n   * DEPRECATED\n   * @param {HighlightedHTMLElement} el\n   */\n  function deprecateHighlightBlock(el) {\n    deprecated(\"10.7.0\", \"highlightBlock will be removed entirely in v12.0\");\n    deprecated(\"10.7.0\", \"Please use highlightElement now.\");\n    return highlightElement(el);\n  }\n\n  /* Interface definition */\n  Object.assign(hljs, {\n    highlight,\n    highlightAuto,\n    highlightAll,\n    highlightElement,\n    // TODO: Remove with v12 API\n    highlightBlock: deprecateHighlightBlock,\n    configure,\n    initHighlighting,\n    initHighlightingOnLoad,\n    registerLanguage,\n    unregisterLanguage,\n    listLanguages,\n    getLanguage,\n    registerAliases,\n    autoDetection,\n    inherit,\n    addPlugin,\n    removePlugin\n  });\n  hljs.debugMode = function () {\n    SAFE_MODE = false;\n  };\n  hljs.safeMode = function () {\n    SAFE_MODE = true;\n  };\n  hljs.versionString = version;\n  hljs.regex = {\n    concat: concat,\n    lookahead: lookahead,\n    either: either,\n    optional: optional,\n    anyNumberOfTimes: anyNumberOfTimes\n  };\n  for (var key in MODES) {\n    // @ts-ignore\n    if (typeof MODES[key] === \"object\") {\n      // @ts-ignore\n      deepFreeze(MODES[key]);\n    }\n  }\n\n  // merge all the modes/regexes into our main object\n  Object.assign(hljs, MODES);\n  return hljs;\n};\n\n// Other names for the variable may break build script\nvar highlight = HLJS({});\n\n// returns a new instance of the highlighter to be used for extensions\n// check https://github.com/wooorm/lowlight/issues/47\nhighlight.newInstance = function () {\n  return HLJS({});\n};\nmodule.exports = highlight;\nhighlight.HighlightJS = highlight;\nhighlight.default = highlight;", "map": {"version": 3, "names": ["deepFreeze", "obj", "Map", "clear", "delete", "set", "Error", "Set", "add", "Object", "freeze", "getOwnPropertyNames", "for<PERSON>ach", "name", "prop", "type", "isFrozen", "Response", "mode", "_classCallCheck", "data", "undefined", "isMatchIgnored", "_createClass", "key", "value", "ignoreMatch", "escapeHTML", "replace", "inherit$1", "original", "result", "create", "_len", "arguments", "length", "objects", "Array", "_key", "SPAN_CLOSE", "emitsWrappingTags", "node", "scope", "scopeToCSSClass", "_ref", "prefix", "startsWith", "includes", "pieces", "split", "shift", "concat", "_toConsumableArray", "map", "x", "i", "repeat", "join", "HTMLR<PERSON><PERSON>", "parseTree", "options", "buffer", "classPrefix", "walk", "addText", "text", "openNode", "className", "span", "closeNode", "newNode", "opts", "children", "assign", "TokenTree", "rootNode", "stack", "get", "top", "push", "pop", "closeAllNodes", "toJSON", "JSON", "stringify", "builder", "constructor", "_walk", "_this", "child", "_collapse", "every", "el", "TokenTreeEmitter", "_TokenTree", "_this2", "_callSuper", "_inherits", "startScope", "endScope", "__addSublanguage", "emitter", "root", "toHTML", "renderer", "finalize", "source", "re", "<PERSON><PERSON><PERSON>", "anyNumberOfTimes", "optional", "_len2", "args", "_key3", "joined", "stripOptionsFromArgs", "splice", "either", "_len3", "_key4", "capture", "countMatchGroups", "RegExp", "toString", "exec", "lexeme", "match", "index", "BACKREF_RE", "_rewriteBackreferences", "regexps", "_ref2", "joinWith", "numCaptures", "regex", "offset", "out", "substring", "String", "Number", "MATCH_NOTHING_RE", "IDENT_RE", "UNDERSCORE_IDENT_RE", "NUMBER_RE", "C_NUMBER_RE", "BINARY_NUMBER_RE", "RE_STARTERS_RE", "SHEBANG", "beginShebang", "binary", "begin", "end", "relevance", "onBegin", "m", "resp", "BACKSLASH_ESCAPE", "APOS_STRING_MODE", "illegal", "contains", "QUOTE_STRING_MODE", "PHRASAL_WORDS_MODE", "COMMENT", "modeOptions", "excludeBegin", "ENGLISH_WORD", "C_LINE_COMMENT_MODE", "C_BLOCK_COMMENT_MODE", "HASH_COMMENT_MODE", "NUMBER_MODE", "C_NUMBER_MODE", "BINARY_NUMBER_MODE", "REGEXP_MODE", "TITLE_MODE", "UNDERSCORE_TITLE_MODE", "METHOD_GUARD", "END_SAME_AS_BEGIN", "_beginMatch", "onEnd", "MODES", "__proto__", "skipIfHasPrecedingDot", "response", "before", "input", "scopeClassName", "_parent", "beginKeywords", "parent", "__beforeBegin", "keywords", "compileIllegal", "isArray", "apply", "compileMatch", "compileRelevance", "beforeMatchExt", "beforeMatch", "starts", "originalMode", "keys", "endsParent", "COMMON_KEYWORDS", "DEFAULT_KEYWORD_SCOPE", "compileKeywords", "rawKeywords", "caseInsensitive", "scopeName", "compiledKeywords", "compileList", "keywordList", "toLowerCase", "keyword", "pair", "scoreForKeyword", "providedScore", "commonKeyword", "seenDeprecations", "error", "message", "console", "warn", "_console", "_len4", "_key5", "log", "deprecated", "version", "MultiClassError", "remapScopeNames", "regexes", "_ref3", "scopeNames", "emit", "positions", "_emit", "_multi", "beginMultiClass", "skip", "returnBegin", "beginScope", "endMultiClass", "excludeEnd", "returnEnd", "scopeSugar", "MultiClass", "_wrap", "compileLanguage", "language", "langRe", "global", "case_insensitive", "unicodeRegex", "MultiRegex", "matchIndexes", "matchAt", "position", "addRule", "compile", "terminators", "matcherRe", "lastIndex", "s", "findIndex", "matchData", "ResumableMultiRegex", "rules", "multiRegexes", "count", "regexIndex", "getMatcher", "matcher", "slice", "_ref4", "_ref5", "_slicedToArray", "resumingScanAtSamePosition", "considerAll", "m2", "buildModeRegex", "mm", "term", "rule", "terminatorEnd", "compileMode", "_ref6", "cmode", "isCompiled", "ext", "compilerExtensions", "keywordPattern", "$pattern", "keywordPatternRe", "beginRe", "endsWithParent", "endRe", "illegalRe", "c", "expandOrCloneMode", "classNameAliases", "dependencyOnParent", "variants", "cachedVariants", "variant", "HTMLInjectionError", "_Error", "reason", "html", "_this3", "_wrapNativeSuper", "escape", "inherit", "NO_MATCH", "Symbol", "MAX_KEYWORD_HITS", "HLJS", "hljs", "languages", "aliases", "plugins", "SAFE_MODE", "LANGUAGE_NOT_FOUND", "PLAINTEXT_LANGUAGE", "disableAutodetect", "ignoreUnescapedHTML", "throwUnescapedHTML", "noHighlightRe", "languageDetectRe", "cssSelector", "__emitter", "shouldNotHighlight", "languageName", "test", "blockLanguage", "block", "classes", "parentNode", "getLanguage", "find", "_class", "highlight", "codeOrLanguageName", "optionsOrCode", "ignoreIllegals", "code", "context", "fire", "_highlight", "codeToHighlight", "continuation", "keywordHits", "keywordData", "matchText", "processKeywords", "modeBuffer", "buf", "word", "_data", "kind", "keywordRelevance", "cssClass", "emitKey<PERSON>", "processSubLanguage", "subLanguage", "continuations", "_top", "highlightAuto", "_emitter", "processBuffer", "emitMultiClass", "max", "klass", "startNewMode", "endOfMode", "matchPlusRemainder", "matched", "doIgnore", "resumeScanAtSamePosition", "doBeginMatch", "newMode", "beforeCallbacks", "_i", "_beforeCallbacks", "cb", "doEndMatch", "endMode", "origin", "processContinuations", "list", "current", "unshift", "item", "lastMatch", "processLexeme", "textBeforeMatch", "err", "badRule", "processed", "iterations", "md", "__emitTokens", "processedCount", "_illegalBy", "resultSoFar", "errorRaised", "justTextHighlightResult", "languageSubset", "plaintext", "results", "filter", "autoDetection", "sorted", "sort", "a", "b", "supersetOf", "_sorted", "best", "secondBest", "updateClassName", "element", "currentLang", "resultLang", "classList", "highlightElement", "dataset", "highlighted", "innerHTML", "textContent", "configure", "userOptions", "initHighlighting", "highlightAll", "initHighlightingOnLoad", "wantsHighlight", "boot", "document", "readyState", "window", "addEventListener", "blocks", "querySelectorAll", "registerLanguage", "languageDefinition", "lang", "error$1", "rawDefinition", "bind", "registerAliases", "unregisterLanguage", "_i2", "_Object$keys", "alias", "listLanguages", "aliasList", "_ref7", "upgradePluginAPI", "plugin", "addPlugin", "removePlugin", "indexOf", "event", "deprecateHighlightBlock", "highlightBlock", "debugMode", "safeMode", "versionString", "newInstance", "module", "exports", "HighlightJS", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/highlight.js@11.11.1/node_modules/highlight.js/lib/core.js"], "sourcesContent": ["/* eslint-disable no-multi-assign */\n\nfunction deepFreeze(obj) {\n  if (obj instanceof Map) {\n    obj.clear =\n      obj.delete =\n      obj.set =\n        function () {\n          throw new Error('map is read-only');\n        };\n  } else if (obj instanceof Set) {\n    obj.add =\n      obj.clear =\n      obj.delete =\n        function () {\n          throw new Error('set is read-only');\n        };\n  }\n\n  // Freeze self\n  Object.freeze(obj);\n\n  Object.getOwnPropertyNames(obj).forEach((name) => {\n    const prop = obj[name];\n    const type = typeof prop;\n\n    // Freeze prop if it is an object or function and also not already frozen\n    if ((type === 'object' || type === 'function') && !Object.isFrozen(prop)) {\n      deepFreeze(prop);\n    }\n  });\n\n  return obj;\n}\n\n/** @typedef {import('highlight.js').CallbackResponse} CallbackResponse */\n/** @typedef {import('highlight.js').CompiledMode} CompiledMode */\n/** @implements CallbackResponse */\n\nclass Response {\n  /**\n   * @param {CompiledMode} mode\n   */\n  constructor(mode) {\n    // eslint-disable-next-line no-undefined\n    if (mode.data === undefined) mode.data = {};\n\n    this.data = mode.data;\n    this.isMatchIgnored = false;\n  }\n\n  ignoreMatch() {\n    this.isMatchIgnored = true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {string}\n */\nfunction escapeHTML(value) {\n  return value\n    .replace(/&/g, '&amp;')\n    .replace(/</g, '&lt;')\n    .replace(/>/g, '&gt;')\n    .replace(/\"/g, '&quot;')\n    .replace(/'/g, '&#x27;');\n}\n\n/**\n * performs a shallow merge of multiple objects into one\n *\n * @template T\n * @param {T} original\n * @param {Record<string,any>[]} objects\n * @returns {T} a single new object\n */\nfunction inherit$1(original, ...objects) {\n  /** @type Record<string,any> */\n  const result = Object.create(null);\n\n  for (const key in original) {\n    result[key] = original[key];\n  }\n  objects.forEach(function(obj) {\n    for (const key in obj) {\n      result[key] = obj[key];\n    }\n  });\n  return /** @type {T} */ (result);\n}\n\n/**\n * @typedef {object} Renderer\n * @property {(text: string) => void} addText\n * @property {(node: Node) => void} openNode\n * @property {(node: Node) => void} closeNode\n * @property {() => string} value\n */\n\n/** @typedef {{scope?: string, language?: string, sublanguage?: boolean}} Node */\n/** @typedef {{walk: (r: Renderer) => void}} Tree */\n/** */\n\nconst SPAN_CLOSE = '</span>';\n\n/**\n * Determines if a node needs to be wrapped in <span>\n *\n * @param {Node} node */\nconst emitsWrappingTags = (node) => {\n  // rarely we can have a sublanguage where language is undefined\n  // TODO: track down why\n  return !!node.scope;\n};\n\n/**\n *\n * @param {string} name\n * @param {{prefix:string}} options\n */\nconst scopeToCSSClass = (name, { prefix }) => {\n  // sub-language\n  if (name.startsWith(\"language:\")) {\n    return name.replace(\"language:\", \"language-\");\n  }\n  // tiered scope: comment.line\n  if (name.includes(\".\")) {\n    const pieces = name.split(\".\");\n    return [\n      `${prefix}${pieces.shift()}`,\n      ...(pieces.map((x, i) => `${x}${\"_\".repeat(i + 1)}`))\n    ].join(\" \");\n  }\n  // simple scope\n  return `${prefix}${name}`;\n};\n\n/** @type {Renderer} */\nclass HTMLRenderer {\n  /**\n   * Creates a new HTMLRenderer\n   *\n   * @param {Tree} parseTree - the parse tree (must support `walk` API)\n   * @param {{classPrefix: string}} options\n   */\n  constructor(parseTree, options) {\n    this.buffer = \"\";\n    this.classPrefix = options.classPrefix;\n    parseTree.walk(this);\n  }\n\n  /**\n   * Adds texts to the output stream\n   *\n   * @param {string} text */\n  addText(text) {\n    this.buffer += escapeHTML(text);\n  }\n\n  /**\n   * Adds a node open to the output stream (if needed)\n   *\n   * @param {Node} node */\n  openNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    const className = scopeToCSSClass(node.scope,\n      { prefix: this.classPrefix });\n    this.span(className);\n  }\n\n  /**\n   * Adds a node close to the output stream (if needed)\n   *\n   * @param {Node} node */\n  closeNode(node) {\n    if (!emitsWrappingTags(node)) return;\n\n    this.buffer += SPAN_CLOSE;\n  }\n\n  /**\n   * returns the accumulated buffer\n  */\n  value() {\n    return this.buffer;\n  }\n\n  // helpers\n\n  /**\n   * Builds a span element\n   *\n   * @param {string} className */\n  span(className) {\n    this.buffer += `<span class=\"${className}\">`;\n  }\n}\n\n/** @typedef {{scope?: string, language?: string, children: Node[]} | string} Node */\n/** @typedef {{scope?: string, language?: string, children: Node[]} } DataNode */\n/** @typedef {import('highlight.js').Emitter} Emitter */\n/**  */\n\n/** @returns {DataNode} */\nconst newNode = (opts = {}) => {\n  /** @type DataNode */\n  const result = { children: [] };\n  Object.assign(result, opts);\n  return result;\n};\n\nclass TokenTree {\n  constructor() {\n    /** @type DataNode */\n    this.rootNode = newNode();\n    this.stack = [this.rootNode];\n  }\n\n  get top() {\n    return this.stack[this.stack.length - 1];\n  }\n\n  get root() { return this.rootNode; }\n\n  /** @param {Node} node */\n  add(node) {\n    this.top.children.push(node);\n  }\n\n  /** @param {string} scope */\n  openNode(scope) {\n    /** @type Node */\n    const node = newNode({ scope });\n    this.add(node);\n    this.stack.push(node);\n  }\n\n  closeNode() {\n    if (this.stack.length > 1) {\n      return this.stack.pop();\n    }\n    // eslint-disable-next-line no-undefined\n    return undefined;\n  }\n\n  closeAllNodes() {\n    while (this.closeNode());\n  }\n\n  toJSON() {\n    return JSON.stringify(this.rootNode, null, 4);\n  }\n\n  /**\n   * @typedef { import(\"./html_renderer\").Renderer } Renderer\n   * @param {Renderer} builder\n   */\n  walk(builder) {\n    // this does not\n    return this.constructor._walk(builder, this.rootNode);\n    // this works\n    // return TokenTree._walk(builder, this.rootNode);\n  }\n\n  /**\n   * @param {Renderer} builder\n   * @param {Node} node\n   */\n  static _walk(builder, node) {\n    if (typeof node === \"string\") {\n      builder.addText(node);\n    } else if (node.children) {\n      builder.openNode(node);\n      node.children.forEach((child) => this._walk(builder, child));\n      builder.closeNode(node);\n    }\n    return builder;\n  }\n\n  /**\n   * @param {Node} node\n   */\n  static _collapse(node) {\n    if (typeof node === \"string\") return;\n    if (!node.children) return;\n\n    if (node.children.every(el => typeof el === \"string\")) {\n      // node.text = node.children.join(\"\");\n      // delete node.children;\n      node.children = [node.children.join(\"\")];\n    } else {\n      node.children.forEach((child) => {\n        TokenTree._collapse(child);\n      });\n    }\n  }\n}\n\n/**\n  Currently this is all private API, but this is the minimal API necessary\n  that an Emitter must implement to fully support the parser.\n\n  Minimal interface:\n\n  - addText(text)\n  - __addSublanguage(emitter, subLanguageName)\n  - startScope(scope)\n  - endScope()\n  - finalize()\n  - toHTML()\n\n*/\n\n/**\n * @implements {Emitter}\n */\nclass TokenTreeEmitter extends TokenTree {\n  /**\n   * @param {*} options\n   */\n  constructor(options) {\n    super();\n    this.options = options;\n  }\n\n  /**\n   * @param {string} text\n   */\n  addText(text) {\n    if (text === \"\") { return; }\n\n    this.add(text);\n  }\n\n  /** @param {string} scope */\n  startScope(scope) {\n    this.openNode(scope);\n  }\n\n  endScope() {\n    this.closeNode();\n  }\n\n  /**\n   * @param {Emitter & {root: DataNode}} emitter\n   * @param {string} name\n   */\n  __addSublanguage(emitter, name) {\n    /** @type DataNode */\n    const node = emitter.root;\n    if (name) node.scope = `language:${name}`;\n\n    this.add(node);\n  }\n\n  toHTML() {\n    const renderer = new HTMLRenderer(this, this.options);\n    return renderer.value();\n  }\n\n  finalize() {\n    this.closeAllNodes();\n    return true;\n  }\n}\n\n/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction anyNumberOfTimes(re) {\n  return concat('(?:', re, ')*');\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction optional(re) {\n  return concat('(?:', re, ')?');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * @param { Array<string | RegExp | Object> } args\n * @returns {object}\n */\nfunction stripOptionsFromArgs(args) {\n  const opts = args[args.length - 1];\n\n  if (typeof opts === 'object' && opts.constructor === Object) {\n    args.splice(args.length - 1, 1);\n    return opts;\n  } else {\n    return {};\n  }\n}\n\n/** @typedef { {capture?: boolean} } RegexEitherOptions */\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] | [...(RegExp | string)[], RegexEitherOptions]} args\n * @returns {string}\n */\nfunction either(...args) {\n  /** @type { object & {capture?: boolean} }  */\n  const opts = stripOptionsFromArgs(args);\n  const joined = '('\n    + (opts.capture ? \"\" : \"?:\")\n    + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/**\n * @param {RegExp | string} re\n * @returns {number}\n */\nfunction countMatchGroups(re) {\n  return (new RegExp(re.toString() + '|')).exec('').length - 1;\n}\n\n/**\n * Does lexeme start with a regular expression match at the beginning\n * @param {RegExp} re\n * @param {string} lexeme\n */\nfunction startsWith(re, lexeme) {\n  const match = re && re.exec(lexeme);\n  return match && match.index === 0;\n}\n\n// BACKREF_RE matches an open parenthesis or backreference. To avoid\n// an incorrect parse, it additionally matches the following:\n// - [...] elements, where the meaning of parentheses and escapes change\n// - other escape sequences, so we do not misparse escape sequences as\n//   interesting elements\n// - non-matching or lookahead parentheses, which do not capture. These\n//   follow the '(' with a '?'.\nconst BACKREF_RE = /\\[(?:[^\\\\\\]]|\\\\.)*\\]|\\(\\??|\\\\([1-9][0-9]*)|\\\\./;\n\n// **INTERNAL** Not intended for outside usage\n// join logically computes regexps.join(separator), but fixes the\n// backreferences so they continue to match.\n// it also places each individual regular expression into it's own\n// match group, keeping track of the sequencing of those match groups\n// is currently an exercise for the caller. :-)\n/**\n * @param {(string | RegExp)[]} regexps\n * @param {{joinWith: string}} opts\n * @returns {string}\n */\nfunction _rewriteBackreferences(regexps, { joinWith }) {\n  let numCaptures = 0;\n\n  return regexps.map((regex) => {\n    numCaptures += 1;\n    const offset = numCaptures;\n    let re = source(regex);\n    let out = '';\n\n    while (re.length > 0) {\n      const match = BACKREF_RE.exec(re);\n      if (!match) {\n        out += re;\n        break;\n      }\n      out += re.substring(0, match.index);\n      re = re.substring(match.index + match[0].length);\n      if (match[0][0] === '\\\\' && match[1]) {\n        // Adjust the backreference.\n        out += '\\\\' + String(Number(match[1]) + offset);\n      } else {\n        out += match[0];\n        if (match[0] === '(') {\n          numCaptures++;\n        }\n      }\n    }\n    return out;\n  }).map(re => `(${re})`).join(joinWith);\n}\n\n/** @typedef {import('highlight.js').Mode} Mode */\n/** @typedef {import('highlight.js').ModeCallback} ModeCallback */\n\n// Common regexps\nconst MATCH_NOTHING_RE = /\\b\\B/;\nconst IDENT_RE = '[a-zA-Z]\\\\w*';\nconst UNDERSCORE_IDENT_RE = '[a-zA-Z_]\\\\w*';\nconst NUMBER_RE = '\\\\b\\\\d+(\\\\.\\\\d+)?';\nconst C_NUMBER_RE = '(-?)(\\\\b0[xX][a-fA-F0-9]+|(\\\\b\\\\d+(\\\\.\\\\d*)?|\\\\.\\\\d+)([eE][-+]?\\\\d+)?)'; // 0x..., 0..., decimal, float\nconst BINARY_NUMBER_RE = '\\\\b(0b[01]+)'; // 0b...\nconst RE_STARTERS_RE = '!|!=|!==|%|%=|&|&&|&=|\\\\*|\\\\*=|\\\\+|\\\\+=|,|-|-=|/=|/|:|;|<<|<<=|<=|<|===|==|=|>>>=|>>=|>=|>>>|>>|>|\\\\?|\\\\[|\\\\{|\\\\(|\\\\^|\\\\^=|\\\\||\\\\|=|\\\\|\\\\||~';\n\n/**\n* @param { Partial<Mode> & {binary?: string | RegExp} } opts\n*/\nconst SHEBANG = (opts = {}) => {\n  const beginShebang = /^#![ ]*\\//;\n  if (opts.binary) {\n    opts.begin = concat(\n      beginShebang,\n      /.*\\b/,\n      opts.binary,\n      /\\b.*/);\n  }\n  return inherit$1({\n    scope: 'meta',\n    begin: beginShebang,\n    end: /$/,\n    relevance: 0,\n    /** @type {ModeCallback} */\n    \"on:begin\": (m, resp) => {\n      if (m.index !== 0) resp.ignoreMatch();\n    }\n  }, opts);\n};\n\n// Common modes\nconst BACKSLASH_ESCAPE = {\n  begin: '\\\\\\\\[\\\\s\\\\S]', relevance: 0\n};\nconst APOS_STRING_MODE = {\n  scope: 'string',\n  begin: '\\'',\n  end: '\\'',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst QUOTE_STRING_MODE = {\n  scope: 'string',\n  begin: '\"',\n  end: '\"',\n  illegal: '\\\\n',\n  contains: [BACKSLASH_ESCAPE]\n};\nconst PHRASAL_WORDS_MODE = {\n  begin: /\\b(a|an|the|are|I'm|isn't|don't|doesn't|won't|but|just|should|pretty|simply|enough|gonna|going|wtf|so|such|will|you|your|they|like|more)\\b/\n};\n/**\n * Creates a comment mode\n *\n * @param {string | RegExp} begin\n * @param {string | RegExp} end\n * @param {Mode | {}} [modeOptions]\n * @returns {Partial<Mode>}\n */\nconst COMMENT = function(begin, end, modeOptions = {}) {\n  const mode = inherit$1(\n    {\n      scope: 'comment',\n      begin,\n      end,\n      contains: []\n    },\n    modeOptions\n  );\n  mode.contains.push({\n    scope: 'doctag',\n    // hack to avoid the space from being included. the space is necessary to\n    // match here to prevent the plain text rule below from gobbling up doctags\n    begin: '[ ]*(?=(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):)',\n    end: /(TODO|FIXME|NOTE|BUG|OPTIMIZE|HACK|XXX):/,\n    excludeBegin: true,\n    relevance: 0\n  });\n  const ENGLISH_WORD = either(\n    // list of common 1 and 2 letter words in English\n    \"I\",\n    \"a\",\n    \"is\",\n    \"so\",\n    \"us\",\n    \"to\",\n    \"at\",\n    \"if\",\n    \"in\",\n    \"it\",\n    \"on\",\n    // note: this is not an exhaustive list of contractions, just popular ones\n    /[A-Za-z]+['](d|ve|re|ll|t|s|n)/, // contractions - can't we'd they're let's, etc\n    /[A-Za-z]+[-][a-z]+/, // `no-way`, etc.\n    /[A-Za-z][a-z]{2,}/ // allow capitalized words at beginning of sentences\n  );\n  // looking like plain text, more likely to be a comment\n  mode.contains.push(\n    {\n      // TODO: how to include \", (, ) without breaking grammars that use these for\n      // comment delimiters?\n      // begin: /[ ]+([()\"]?([A-Za-z'-]{3,}|is|a|I|so|us|[tT][oO]|at|if|in|it|on)[.]?[()\":]?([.][ ]|[ ]|\\))){3}/\n      // ---\n\n      // this tries to find sequences of 3 english words in a row (without any\n      // \"programming\" type syntax) this gives us a strong signal that we've\n      // TRULY found a comment - vs perhaps scanning with the wrong language.\n      // It's possible to find something that LOOKS like the start of the\n      // comment - but then if there is no readable text - good chance it is a\n      // false match and not a comment.\n      //\n      // for a visual example please see:\n      // https://github.com/highlightjs/highlight.js/issues/2827\n\n      begin: concat(\n        /[ ]+/, // necessary to prevent us gobbling up doctags like /* <AUTHOR> Mcgill */\n        '(',\n        ENGLISH_WORD,\n        /[.]?[:]?([.][ ]|[ ])/,\n        '){3}') // look for 3 words in a row\n    }\n  );\n  return mode;\n};\nconst C_LINE_COMMENT_MODE = COMMENT('//', '$');\nconst C_BLOCK_COMMENT_MODE = COMMENT('/\\\\*', '\\\\*/');\nconst HASH_COMMENT_MODE = COMMENT('#', '$');\nconst NUMBER_MODE = {\n  scope: 'number',\n  begin: NUMBER_RE,\n  relevance: 0\n};\nconst C_NUMBER_MODE = {\n  scope: 'number',\n  begin: C_NUMBER_RE,\n  relevance: 0\n};\nconst BINARY_NUMBER_MODE = {\n  scope: 'number',\n  begin: BINARY_NUMBER_RE,\n  relevance: 0\n};\nconst REGEXP_MODE = {\n  scope: \"regexp\",\n  begin: /\\/(?=[^/\\n]*\\/)/,\n  end: /\\/[gimuy]*/,\n  contains: [\n    BACKSLASH_ESCAPE,\n    {\n      begin: /\\[/,\n      end: /\\]/,\n      relevance: 0,\n      contains: [BACKSLASH_ESCAPE]\n    }\n  ]\n};\nconst TITLE_MODE = {\n  scope: 'title',\n  begin: IDENT_RE,\n  relevance: 0\n};\nconst UNDERSCORE_TITLE_MODE = {\n  scope: 'title',\n  begin: UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\nconst METHOD_GUARD = {\n  // excludes method names from keyword processing\n  begin: '\\\\.\\\\s*' + UNDERSCORE_IDENT_RE,\n  relevance: 0\n};\n\n/**\n * Adds end same as begin mechanics to a mode\n *\n * Your mode must include at least a single () match group as that first match\n * group is what is used for comparison\n * @param {Partial<Mode>} mode\n */\nconst END_SAME_AS_BEGIN = function(mode) {\n  return Object.assign(mode,\n    {\n      /** @type {ModeCallback} */\n      'on:begin': (m, resp) => { resp.data._beginMatch = m[1]; },\n      /** @type {ModeCallback} */\n      'on:end': (m, resp) => { if (resp.data._beginMatch !== m[1]) resp.ignoreMatch(); }\n    });\n};\n\nvar MODES = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  APOS_STRING_MODE: APOS_STRING_MODE,\n  BACKSLASH_ESCAPE: BACKSLASH_ESCAPE,\n  BINARY_NUMBER_MODE: BINARY_NUMBER_MODE,\n  BINARY_NUMBER_RE: BINARY_NUMBER_RE,\n  COMMENT: COMMENT,\n  C_BLOCK_COMMENT_MODE: C_BLOCK_COMMENT_MODE,\n  C_LINE_COMMENT_MODE: C_LINE_COMMENT_MODE,\n  C_NUMBER_MODE: C_NUMBER_MODE,\n  C_NUMBER_RE: C_NUMBER_RE,\n  END_SAME_AS_BEGIN: END_SAME_AS_BEGIN,\n  HASH_COMMENT_MODE: HASH_COMMENT_MODE,\n  IDENT_RE: IDENT_RE,\n  MATCH_NOTHING_RE: MATCH_NOTHING_RE,\n  METHOD_GUARD: METHOD_GUARD,\n  NUMBER_MODE: NUMBER_MODE,\n  NUMBER_RE: NUMBER_RE,\n  PHRASAL_WORDS_MODE: PHRASAL_WORDS_MODE,\n  QUOTE_STRING_MODE: QUOTE_STRING_MODE,\n  REGEXP_MODE: REGEXP_MODE,\n  RE_STARTERS_RE: RE_STARTERS_RE,\n  SHEBANG: SHEBANG,\n  TITLE_MODE: TITLE_MODE,\n  UNDERSCORE_IDENT_RE: UNDERSCORE_IDENT_RE,\n  UNDERSCORE_TITLE_MODE: UNDERSCORE_TITLE_MODE\n});\n\n/**\n@typedef {import('highlight.js').CallbackResponse} CallbackResponse\n@typedef {import('highlight.js').CompilerExt} CompilerExt\n*/\n\n// Grammar extensions / plugins\n// See: https://github.com/highlightjs/highlight.js/issues/2833\n\n// Grammar extensions allow \"syntactic sugar\" to be added to the grammar modes\n// without requiring any underlying changes to the compiler internals.\n\n// `compileMatch` being the perfect small example of now allowing a grammar\n// author to write `match` when they desire to match a single expression rather\n// than being forced to use `begin`.  The extension then just moves `match` into\n// `begin` when it runs.  Ie, no features have been added, but we've just made\n// the experience of writing (and reading grammars) a little bit nicer.\n\n// ------\n\n// TODO: We need negative look-behind support to do this properly\n/**\n * Skip a match if it has a preceding dot\n *\n * This is used for `beginKeywords` to prevent matching expressions such as\n * `bob.keyword.do()`. The mode compiler automatically wires this up as a\n * special _internal_ 'on:begin' callback for modes with `beginKeywords`\n * @param {RegExpMatchArray} match\n * @param {CallbackResponse} response\n */\nfunction skipIfHasPrecedingDot(match, response) {\n  const before = match.input[match.index - 1];\n  if (before === \".\") {\n    response.ignoreMatch();\n  }\n}\n\n/**\n *\n * @type {CompilerExt}\n */\nfunction scopeClassName(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.className !== undefined) {\n    mode.scope = mode.className;\n    delete mode.className;\n  }\n}\n\n/**\n * `beginKeywords` syntactic sugar\n * @type {CompilerExt}\n */\nfunction beginKeywords(mode, parent) {\n  if (!parent) return;\n  if (!mode.beginKeywords) return;\n\n  // for languages with keywords that include non-word characters checking for\n  // a word boundary is not sufficient, so instead we check for a word boundary\n  // or whitespace - this does no harm in any case since our keyword engine\n  // doesn't allow spaces in keywords anyways and we still check for the boundary\n  // first\n  mode.begin = '\\\\b(' + mode.beginKeywords.split(' ').join('|') + ')(?!\\\\.)(?=\\\\b|\\\\s)';\n  mode.__beforeBegin = skipIfHasPrecedingDot;\n  mode.keywords = mode.keywords || mode.beginKeywords;\n  delete mode.beginKeywords;\n\n  // prevents double relevance, the keywords themselves provide\n  // relevance, the mode doesn't need to double it\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 0;\n}\n\n/**\n * Allow `illegal` to contain an array of illegal values\n * @type {CompilerExt}\n */\nfunction compileIllegal(mode, _parent) {\n  if (!Array.isArray(mode.illegal)) return;\n\n  mode.illegal = either(...mode.illegal);\n}\n\n/**\n * `match` to match a single expression for readability\n * @type {CompilerExt}\n */\nfunction compileMatch(mode, _parent) {\n  if (!mode.match) return;\n  if (mode.begin || mode.end) throw new Error(\"begin & end are not supported with match\");\n\n  mode.begin = mode.match;\n  delete mode.match;\n}\n\n/**\n * provides the default 1 relevance to all modes\n * @type {CompilerExt}\n */\nfunction compileRelevance(mode, _parent) {\n  // eslint-disable-next-line no-undefined\n  if (mode.relevance === undefined) mode.relevance = 1;\n}\n\n// allow beforeMatch to act as a \"qualifier\" for the match\n// the full match begin must be [beforeMatch][begin]\nconst beforeMatchExt = (mode, parent) => {\n  if (!mode.beforeMatch) return;\n  // starts conflicts with endsParent which we need to make sure the child\n  // rule is not matched multiple times\n  if (mode.starts) throw new Error(\"beforeMatch cannot be used with starts\");\n\n  const originalMode = Object.assign({}, mode);\n  Object.keys(mode).forEach((key) => { delete mode[key]; });\n\n  mode.keywords = originalMode.keywords;\n  mode.begin = concat(originalMode.beforeMatch, lookahead(originalMode.begin));\n  mode.starts = {\n    relevance: 0,\n    contains: [\n      Object.assign(originalMode, { endsParent: true })\n    ]\n  };\n  mode.relevance = 0;\n\n  delete originalMode.beforeMatch;\n};\n\n// keywords that should have no default relevance value\nconst COMMON_KEYWORDS = [\n  'of',\n  'and',\n  'for',\n  'in',\n  'not',\n  'or',\n  'if',\n  'then',\n  'parent', // common variable name\n  'list', // common variable name\n  'value' // common variable name\n];\n\nconst DEFAULT_KEYWORD_SCOPE = \"keyword\";\n\n/**\n * Given raw keywords from a language definition, compile them.\n *\n * @param {string | Record<string,string|string[]> | Array<string>} rawKeywords\n * @param {boolean} caseInsensitive\n */\nfunction compileKeywords(rawKeywords, caseInsensitive, scopeName = DEFAULT_KEYWORD_SCOPE) {\n  /** @type {import(\"highlight.js/private\").KeywordDict} */\n  const compiledKeywords = Object.create(null);\n\n  // input can be a string of keywords, an array of keywords, or a object with\n  // named keys representing scopeName (which can then point to a string or array)\n  if (typeof rawKeywords === 'string') {\n    compileList(scopeName, rawKeywords.split(\" \"));\n  } else if (Array.isArray(rawKeywords)) {\n    compileList(scopeName, rawKeywords);\n  } else {\n    Object.keys(rawKeywords).forEach(function(scopeName) {\n      // collapse all our objects back into the parent object\n      Object.assign(\n        compiledKeywords,\n        compileKeywords(rawKeywords[scopeName], caseInsensitive, scopeName)\n      );\n    });\n  }\n  return compiledKeywords;\n\n  // ---\n\n  /**\n   * Compiles an individual list of keywords\n   *\n   * Ex: \"for if when while|5\"\n   *\n   * @param {string} scopeName\n   * @param {Array<string>} keywordList\n   */\n  function compileList(scopeName, keywordList) {\n    if (caseInsensitive) {\n      keywordList = keywordList.map(x => x.toLowerCase());\n    }\n    keywordList.forEach(function(keyword) {\n      const pair = keyword.split('|');\n      compiledKeywords[pair[0]] = [scopeName, scoreForKeyword(pair[0], pair[1])];\n    });\n  }\n}\n\n/**\n * Returns the proper score for a given keyword\n *\n * Also takes into account comment keywords, which will be scored 0 UNLESS\n * another score has been manually assigned.\n * @param {string} keyword\n * @param {string} [providedScore]\n */\nfunction scoreForKeyword(keyword, providedScore) {\n  // manual scores always win over common keywords\n  // so you can force a score of 1 if you really insist\n  if (providedScore) {\n    return Number(providedScore);\n  }\n\n  return commonKeyword(keyword) ? 0 : 1;\n}\n\n/**\n * Determines if a given keyword is common or not\n *\n * @param {string} keyword */\nfunction commonKeyword(keyword) {\n  return COMMON_KEYWORDS.includes(keyword.toLowerCase());\n}\n\n/*\n\nFor the reasoning behind this please see:\nhttps://github.com/highlightjs/highlight.js/issues/2880#issuecomment-*********\n\n*/\n\n/**\n * @type {Record<string, boolean>}\n */\nconst seenDeprecations = {};\n\n/**\n * @param {string} message\n */\nconst error = (message) => {\n  console.error(message);\n};\n\n/**\n * @param {string} message\n * @param {any} args\n */\nconst warn = (message, ...args) => {\n  console.log(`WARN: ${message}`, ...args);\n};\n\n/**\n * @param {string} version\n * @param {string} message\n */\nconst deprecated = (version, message) => {\n  if (seenDeprecations[`${version}/${message}`]) return;\n\n  console.log(`Deprecated as of ${version}. ${message}`);\n  seenDeprecations[`${version}/${message}`] = true;\n};\n\n/* eslint-disable no-throw-literal */\n\n/**\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n*/\n\nconst MultiClassError = new Error();\n\n/**\n * Renumbers labeled scope names to account for additional inner match\n * groups that otherwise would break everything.\n *\n * Lets say we 3 match scopes:\n *\n *   { 1 => ..., 2 => ..., 3 => ... }\n *\n * So what we need is a clean match like this:\n *\n *   (a)(b)(c) => [ \"a\", \"b\", \"c\" ]\n *\n * But this falls apart with inner match groups:\n *\n * (a)(((b)))(c) => [\"a\", \"b\", \"b\", \"b\", \"c\" ]\n *\n * Our scopes are now \"out of alignment\" and we're repeating `b` 3 times.\n * What needs to happen is the numbers are remapped:\n *\n *   { 1 => ..., 2 => ..., 5 => ... }\n *\n * We also need to know that the ONLY groups that should be output\n * are 1, 2, and 5.  This function handles this behavior.\n *\n * @param {CompiledMode} mode\n * @param {Array<RegExp | string>} regexes\n * @param {{key: \"beginScope\"|\"endScope\"}} opts\n */\nfunction remapScopeNames(mode, regexes, { key }) {\n  let offset = 0;\n  const scopeNames = mode[key];\n  /** @type Record<number,boolean> */\n  const emit = {};\n  /** @type Record<number,string> */\n  const positions = {};\n\n  for (let i = 1; i <= regexes.length; i++) {\n    positions[i + offset] = scopeNames[i];\n    emit[i + offset] = true;\n    offset += countMatchGroups(regexes[i - 1]);\n  }\n  // we use _emit to keep track of which match groups are \"top-level\" to avoid double\n  // output from inside match groups\n  mode[key] = positions;\n  mode[key]._emit = emit;\n  mode[key]._multi = true;\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction beginMultiClass(mode) {\n  if (!Array.isArray(mode.begin)) return;\n\n  if (mode.skip || mode.excludeBegin || mode.returnBegin) {\n    error(\"skip, excludeBegin, returnBegin not compatible with beginScope: {}\");\n    throw MultiClassError;\n  }\n\n  if (typeof mode.beginScope !== \"object\" || mode.beginScope === null) {\n    error(\"beginScope must be object\");\n    throw MultiClassError;\n  }\n\n  remapScopeNames(mode, mode.begin, { key: \"beginScope\" });\n  mode.begin = _rewriteBackreferences(mode.begin, { joinWith: \"\" });\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction endMultiClass(mode) {\n  if (!Array.isArray(mode.end)) return;\n\n  if (mode.skip || mode.excludeEnd || mode.returnEnd) {\n    error(\"skip, excludeEnd, returnEnd not compatible with endScope: {}\");\n    throw MultiClassError;\n  }\n\n  if (typeof mode.endScope !== \"object\" || mode.endScope === null) {\n    error(\"endScope must be object\");\n    throw MultiClassError;\n  }\n\n  remapScopeNames(mode, mode.end, { key: \"endScope\" });\n  mode.end = _rewriteBackreferences(mode.end, { joinWith: \"\" });\n}\n\n/**\n * this exists only to allow `scope: {}` to be used beside `match:`\n * Otherwise `beginScope` would necessary and that would look weird\n\n  {\n    match: [ /def/, /\\w+/ ]\n    scope: { 1: \"keyword\" , 2: \"title\" }\n  }\n\n * @param {CompiledMode} mode\n */\nfunction scopeSugar(mode) {\n  if (mode.scope && typeof mode.scope === \"object\" && mode.scope !== null) {\n    mode.beginScope = mode.scope;\n    delete mode.scope;\n  }\n}\n\n/**\n * @param {CompiledMode} mode\n */\nfunction MultiClass(mode) {\n  scopeSugar(mode);\n\n  if (typeof mode.beginScope === \"string\") {\n    mode.beginScope = { _wrap: mode.beginScope };\n  }\n  if (typeof mode.endScope === \"string\") {\n    mode.endScope = { _wrap: mode.endScope };\n  }\n\n  beginMultiClass(mode);\n  endMultiClass(mode);\n}\n\n/**\n@typedef {import('highlight.js').Mode} Mode\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n@typedef {import('highlight.js').Language} Language\n@typedef {import('highlight.js').HLJSPlugin} HLJSPlugin\n@typedef {import('highlight.js').CompiledLanguage} CompiledLanguage\n*/\n\n// compilation\n\n/**\n * Compiles a language definition result\n *\n * Given the raw result of a language definition (Language), compiles this so\n * that it is ready for highlighting code.\n * @param {Language} language\n * @returns {CompiledLanguage}\n */\nfunction compileLanguage(language) {\n  /**\n   * Builds a regex with the case sensitivity of the current language\n   *\n   * @param {RegExp | string} value\n   * @param {boolean} [global]\n   */\n  function langRe(value, global) {\n    return new RegExp(\n      source(value),\n      'm'\n      + (language.case_insensitive ? 'i' : '')\n      + (language.unicodeRegex ? 'u' : '')\n      + (global ? 'g' : '')\n    );\n  }\n\n  /**\n    Stores multiple regular expressions and allows you to quickly search for\n    them all in a string simultaneously - returning the first match.  It does\n    this by creating a huge (a|b|c) regex - each individual item wrapped with ()\n    and joined by `|` - using match groups to track position.  When a match is\n    found checking which position in the array has content allows us to figure\n    out which of the original regexes / match groups triggered the match.\n\n    The match object itself (the result of `Regex.exec`) is returned but also\n    enhanced by merging in any meta-data that was registered with the regex.\n    This is how we keep track of which mode matched, and what type of rule\n    (`illegal`, `begin`, end, etc).\n  */\n  class MultiRegex {\n    constructor() {\n      this.matchIndexes = {};\n      // @ts-ignore\n      this.regexes = [];\n      this.matchAt = 1;\n      this.position = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      opts.position = this.position++;\n      // @ts-ignore\n      this.matchIndexes[this.matchAt] = opts;\n      this.regexes.push([opts, re]);\n      this.matchAt += countMatchGroups(re) + 1;\n    }\n\n    compile() {\n      if (this.regexes.length === 0) {\n        // avoids the need to check length every time exec is called\n        // @ts-ignore\n        this.exec = () => null;\n      }\n      const terminators = this.regexes.map(el => el[1]);\n      this.matcherRe = langRe(_rewriteBackreferences(terminators, { joinWith: '|' }), true);\n      this.lastIndex = 0;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      this.matcherRe.lastIndex = this.lastIndex;\n      const match = this.matcherRe.exec(s);\n      if (!match) { return null; }\n\n      // eslint-disable-next-line no-undefined\n      const i = match.findIndex((el, i) => i > 0 && el !== undefined);\n      // @ts-ignore\n      const matchData = this.matchIndexes[i];\n      // trim off any earlier non-relevant match groups (ie, the other regex\n      // match groups that make up the multi-matcher)\n      match.splice(0, i);\n\n      return Object.assign(match, matchData);\n    }\n  }\n\n  /*\n    Created to solve the key deficiently with MultiRegex - there is no way to\n    test for multiple matches at a single location.  Why would we need to do\n    that?  In the future a more dynamic engine will allow certain matches to be\n    ignored.  An example: if we matched say the 3rd regex in a large group but\n    decided to ignore it - we'd need to started testing again at the 4th\n    regex... but MultiRegex itself gives us no real way to do that.\n\n    So what this class creates MultiRegexs on the fly for whatever search\n    position they are needed.\n\n    NOTE: These additional MultiRegex objects are created dynamically.  For most\n    grammars most of the time we will never actually need anything more than the\n    first MultiRegex - so this shouldn't have too much overhead.\n\n    Say this is our search group, and we match regex3, but wish to ignore it.\n\n      regex1 | regex2 | regex3 | regex4 | regex5    ' ie, startAt = 0\n\n    What we need is a new MultiRegex that only includes the remaining\n    possibilities:\n\n      regex4 | regex5                               ' ie, startAt = 3\n\n    This class wraps all that complexity up in a simple API... `startAt` decides\n    where in the array of expressions to start doing the matching. It\n    auto-increments, so if a match is found at position 2, then startAt will be\n    set to 3.  If the end is reached startAt will return to 0.\n\n    MOST of the time the parser will be setting startAt manually to 0.\n  */\n  class ResumableMultiRegex {\n    constructor() {\n      // @ts-ignore\n      this.rules = [];\n      // @ts-ignore\n      this.multiRegexes = [];\n      this.count = 0;\n\n      this.lastIndex = 0;\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    getMatcher(index) {\n      if (this.multiRegexes[index]) return this.multiRegexes[index];\n\n      const matcher = new MultiRegex();\n      this.rules.slice(index).forEach(([re, opts]) => matcher.addRule(re, opts));\n      matcher.compile();\n      this.multiRegexes[index] = matcher;\n      return matcher;\n    }\n\n    resumingScanAtSamePosition() {\n      return this.regexIndex !== 0;\n    }\n\n    considerAll() {\n      this.regexIndex = 0;\n    }\n\n    // @ts-ignore\n    addRule(re, opts) {\n      this.rules.push([re, opts]);\n      if (opts.type === \"begin\") this.count++;\n    }\n\n    /** @param {string} s */\n    exec(s) {\n      const m = this.getMatcher(this.regexIndex);\n      m.lastIndex = this.lastIndex;\n      let result = m.exec(s);\n\n      // The following is because we have no easy way to say \"resume scanning at the\n      // existing position but also skip the current rule ONLY\". What happens is\n      // all prior rules are also skipped which can result in matching the wrong\n      // thing. Example of matching \"booger\":\n\n      // our matcher is [string, \"booger\", number]\n      //\n      // ....booger....\n\n      // if \"booger\" is ignored then we'd really need a regex to scan from the\n      // SAME position for only: [string, number] but ignoring \"booger\" (if it\n      // was the first match), a simple resume would scan ahead who knows how\n      // far looking only for \"number\", ignoring potential string matches (or\n      // future \"booger\" matches that might be valid.)\n\n      // So what we do: We execute two matchers, one resuming at the same\n      // position, but the second full matcher starting at the position after:\n\n      //     /--- resume first regex match here (for [number])\n      //     |/---- full match here for [string, \"booger\", number]\n      //     vv\n      // ....booger....\n\n      // Which ever results in a match first is then used. So this 3-4 step\n      // process essentially allows us to say \"match at this position, excluding\n      // a prior rule that was ignored\".\n      //\n      // 1. Match \"booger\" first, ignore. Also proves that [string] does non match.\n      // 2. Resume matching for [number]\n      // 3. Match at index + 1 for [string, \"booger\", number]\n      // 4. If #2 and #3 result in matches, which came first?\n      if (this.resumingScanAtSamePosition()) {\n        if (result && result.index === this.lastIndex) ; else { // use the second matcher result\n          const m2 = this.getMatcher(0);\n          m2.lastIndex = this.lastIndex + 1;\n          result = m2.exec(s);\n        }\n      }\n\n      if (result) {\n        this.regexIndex += result.position + 1;\n        if (this.regexIndex === this.count) {\n          // wrap-around to considering all matches again\n          this.considerAll();\n        }\n      }\n\n      return result;\n    }\n  }\n\n  /**\n   * Given a mode, builds a huge ResumableMultiRegex that can be used to walk\n   * the content and find matches.\n   *\n   * @param {CompiledMode} mode\n   * @returns {ResumableMultiRegex}\n   */\n  function buildModeRegex(mode) {\n    const mm = new ResumableMultiRegex();\n\n    mode.contains.forEach(term => mm.addRule(term.begin, { rule: term, type: \"begin\" }));\n\n    if (mode.terminatorEnd) {\n      mm.addRule(mode.terminatorEnd, { type: \"end\" });\n    }\n    if (mode.illegal) {\n      mm.addRule(mode.illegal, { type: \"illegal\" });\n    }\n\n    return mm;\n  }\n\n  /** skip vs abort vs ignore\n   *\n   * @skip   - The mode is still entered and exited normally (and contains rules apply),\n   *           but all content is held and added to the parent buffer rather than being\n   *           output when the mode ends.  Mostly used with `sublanguage` to build up\n   *           a single large buffer than can be parsed by sublanguage.\n   *\n   *             - The mode begin ands ends normally.\n   *             - Content matched is added to the parent mode buffer.\n   *             - The parser cursor is moved forward normally.\n   *\n   * @abort  - A hack placeholder until we have ignore.  Aborts the mode (as if it\n   *           never matched) but DOES NOT continue to match subsequent `contains`\n   *           modes.  Abort is bad/suboptimal because it can result in modes\n   *           farther down not getting applied because an earlier rule eats the\n   *           content but then aborts.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is added to the mode buffer.\n   *             - The parser cursor is moved forward accordingly.\n   *\n   * @ignore - Ignores the mode (as if it never matched) and continues to match any\n   *           subsequent `contains` modes.  Ignore isn't technically possible with\n   *           the current parser implementation.\n   *\n   *             - The mode does not begin.\n   *             - Content matched by `begin` is ignored.\n   *             - The parser cursor is not moved forward.\n   */\n\n  /**\n   * Compiles an individual mode\n   *\n   * This can raise an error if the mode contains certain detectable known logic\n   * issues.\n   * @param {Mode} mode\n   * @param {CompiledMode | null} [parent]\n   * @returns {CompiledMode | never}\n   */\n  function compileMode(mode, parent) {\n    const cmode = /** @type CompiledMode */ (mode);\n    if (mode.isCompiled) return cmode;\n\n    [\n      scopeClassName,\n      // do this early so compiler extensions generally don't have to worry about\n      // the distinction between match/begin\n      compileMatch,\n      MultiClass,\n      beforeMatchExt\n    ].forEach(ext => ext(mode, parent));\n\n    language.compilerExtensions.forEach(ext => ext(mode, parent));\n\n    // __beforeBegin is considered private API, internal use only\n    mode.__beforeBegin = null;\n\n    [\n      beginKeywords,\n      // do this later so compiler extensions that come earlier have access to the\n      // raw array if they wanted to perhaps manipulate it, etc.\n      compileIllegal,\n      // default to 1 relevance if not specified\n      compileRelevance\n    ].forEach(ext => ext(mode, parent));\n\n    mode.isCompiled = true;\n\n    let keywordPattern = null;\n    if (typeof mode.keywords === \"object\" && mode.keywords.$pattern) {\n      // we need a copy because keywords might be compiled multiple times\n      // so we can't go deleting $pattern from the original on the first\n      // pass\n      mode.keywords = Object.assign({}, mode.keywords);\n      keywordPattern = mode.keywords.$pattern;\n      delete mode.keywords.$pattern;\n    }\n    keywordPattern = keywordPattern || /\\w+/;\n\n    if (mode.keywords) {\n      mode.keywords = compileKeywords(mode.keywords, language.case_insensitive);\n    }\n\n    cmode.keywordPatternRe = langRe(keywordPattern, true);\n\n    if (parent) {\n      if (!mode.begin) mode.begin = /\\B|\\b/;\n      cmode.beginRe = langRe(cmode.begin);\n      if (!mode.end && !mode.endsWithParent) mode.end = /\\B|\\b/;\n      if (mode.end) cmode.endRe = langRe(cmode.end);\n      cmode.terminatorEnd = source(cmode.end) || '';\n      if (mode.endsWithParent && parent.terminatorEnd) {\n        cmode.terminatorEnd += (mode.end ? '|' : '') + parent.terminatorEnd;\n      }\n    }\n    if (mode.illegal) cmode.illegalRe = langRe(/** @type {RegExp | string} */ (mode.illegal));\n    if (!mode.contains) mode.contains = [];\n\n    mode.contains = [].concat(...mode.contains.map(function(c) {\n      return expandOrCloneMode(c === 'self' ? mode : c);\n    }));\n    mode.contains.forEach(function(c) { compileMode(/** @type Mode */ (c), cmode); });\n\n    if (mode.starts) {\n      compileMode(mode.starts, parent);\n    }\n\n    cmode.matcher = buildModeRegex(cmode);\n    return cmode;\n  }\n\n  if (!language.compilerExtensions) language.compilerExtensions = [];\n\n  // self is not valid at the top-level\n  if (language.contains && language.contains.includes('self')) {\n    throw new Error(\"ERR: contains `self` is not supported at the top-level of a language.  See documentation.\");\n  }\n\n  // we need a null object, which inherit will guarantee\n  language.classNameAliases = inherit$1(language.classNameAliases || {});\n\n  return compileMode(/** @type Mode */ (language));\n}\n\n/**\n * Determines if a mode has a dependency on it's parent or not\n *\n * If a mode does have a parent dependency then often we need to clone it if\n * it's used in multiple places so that each copy points to the correct parent,\n * where-as modes without a parent can often safely be re-used at the bottom of\n * a mode chain.\n *\n * @param {Mode | null} mode\n * @returns {boolean} - is there a dependency on the parent?\n * */\nfunction dependencyOnParent(mode) {\n  if (!mode) return false;\n\n  return mode.endsWithParent || dependencyOnParent(mode.starts);\n}\n\n/**\n * Expands a mode or clones it if necessary\n *\n * This is necessary for modes with parental dependenceis (see notes on\n * `dependencyOnParent`) and for nodes that have `variants` - which must then be\n * exploded into their own individual modes at compile time.\n *\n * @param {Mode} mode\n * @returns {Mode | Mode[]}\n * */\nfunction expandOrCloneMode(mode) {\n  if (mode.variants && !mode.cachedVariants) {\n    mode.cachedVariants = mode.variants.map(function(variant) {\n      return inherit$1(mode, { variants: null }, variant);\n    });\n  }\n\n  // EXPAND\n  // if we have variants then essentially \"replace\" the mode with the variants\n  // this happens in compileMode, where this function is called from\n  if (mode.cachedVariants) {\n    return mode.cachedVariants;\n  }\n\n  // CLONE\n  // if we have dependencies on parents then we need a unique\n  // instance of ourselves, so we can be reused with many\n  // different parents without issue\n  if (dependencyOnParent(mode)) {\n    return inherit$1(mode, { starts: mode.starts ? inherit$1(mode.starts) : null });\n  }\n\n  if (Object.isFrozen(mode)) {\n    return inherit$1(mode);\n  }\n\n  // no special dependency issues, just return ourselves\n  return mode;\n}\n\nvar version = \"11.11.1\";\n\nclass HTMLInjectionError extends Error {\n  constructor(reason, html) {\n    super(reason);\n    this.name = \"HTMLInjectionError\";\n    this.html = html;\n  }\n}\n\n/*\nSyntax highlighting with language autodetection.\nhttps://highlightjs.org/\n*/\n\n\n\n/**\n@typedef {import('highlight.js').Mode} Mode\n@typedef {import('highlight.js').CompiledMode} CompiledMode\n@typedef {import('highlight.js').CompiledScope} CompiledScope\n@typedef {import('highlight.js').Language} Language\n@typedef {import('highlight.js').HLJSApi} HLJSApi\n@typedef {import('highlight.js').HLJSPlugin} HLJSPlugin\n@typedef {import('highlight.js').PluginEvent} PluginEvent\n@typedef {import('highlight.js').HLJSOptions} HLJSOptions\n@typedef {import('highlight.js').LanguageFn} LanguageFn\n@typedef {import('highlight.js').HighlightedHTMLElement} HighlightedHTMLElement\n@typedef {import('highlight.js').BeforeHighlightContext} BeforeHighlightContext\n@typedef {import('highlight.js/private').MatchType} MatchType\n@typedef {import('highlight.js/private').KeywordData} KeywordData\n@typedef {import('highlight.js/private').EnhancedMatch} EnhancedMatch\n@typedef {import('highlight.js/private').AnnotatedError} AnnotatedError\n@typedef {import('highlight.js').AutoHighlightResult} AutoHighlightResult\n@typedef {import('highlight.js').HighlightOptions} HighlightOptions\n@typedef {import('highlight.js').HighlightResult} HighlightResult\n*/\n\n\nconst escape = escapeHTML;\nconst inherit = inherit$1;\nconst NO_MATCH = Symbol(\"nomatch\");\nconst MAX_KEYWORD_HITS = 7;\n\n/**\n * @param {any} hljs - object that is extended (legacy)\n * @returns {HLJSApi}\n */\nconst HLJS = function(hljs) {\n  // Global internal variables used within the highlight.js library.\n  /** @type {Record<string, Language>} */\n  const languages = Object.create(null);\n  /** @type {Record<string, string>} */\n  const aliases = Object.create(null);\n  /** @type {HLJSPlugin[]} */\n  const plugins = [];\n\n  // safe/production mode - swallows more errors, tries to keep running\n  // even if a single syntax or parse hits a fatal error\n  let SAFE_MODE = true;\n  const LANGUAGE_NOT_FOUND = \"Could not find the language '{}', did you forget to load/include a language module?\";\n  /** @type {Language} */\n  const PLAINTEXT_LANGUAGE = { disableAutodetect: true, name: 'Plain text', contains: [] };\n\n  // Global options used when within external APIs. This is modified when\n  // calling the `hljs.configure` function.\n  /** @type HLJSOptions */\n  let options = {\n    ignoreUnescapedHTML: false,\n    throwUnescapedHTML: false,\n    noHighlightRe: /^(no-?highlight)$/i,\n    languageDetectRe: /\\blang(?:uage)?-([\\w-]+)\\b/i,\n    classPrefix: 'hljs-',\n    cssSelector: 'pre code',\n    languages: null,\n    // beta configuration options, subject to change, welcome to discuss\n    // https://github.com/highlightjs/highlight.js/issues/1086\n    __emitter: TokenTreeEmitter\n  };\n\n  /* Utility functions */\n\n  /**\n   * Tests a language name to see if highlighting should be skipped\n   * @param {string} languageName\n   */\n  function shouldNotHighlight(languageName) {\n    return options.noHighlightRe.test(languageName);\n  }\n\n  /**\n   * @param {HighlightedHTMLElement} block - the HTML element to determine language for\n   */\n  function blockLanguage(block) {\n    let classes = block.className + ' ';\n\n    classes += block.parentNode ? block.parentNode.className : '';\n\n    // language-* takes precedence over non-prefixed class names.\n    const match = options.languageDetectRe.exec(classes);\n    if (match) {\n      const language = getLanguage(match[1]);\n      if (!language) {\n        warn(LANGUAGE_NOT_FOUND.replace(\"{}\", match[1]));\n        warn(\"Falling back to no-highlight mode for this block.\", block);\n      }\n      return language ? match[1] : 'no-highlight';\n    }\n\n    return classes\n      .split(/\\s+/)\n      .find((_class) => shouldNotHighlight(_class) || getLanguage(_class));\n  }\n\n  /**\n   * Core highlighting function.\n   *\n   * OLD API\n   * highlight(lang, code, ignoreIllegals, continuation)\n   *\n   * NEW API\n   * highlight(code, {lang, ignoreIllegals})\n   *\n   * @param {string} codeOrLanguageName - the language to use for highlighting\n   * @param {string | HighlightOptions} optionsOrCode - the code to highlight\n   * @param {boolean} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   *\n   * @returns {HighlightResult} Result - an object that represents the result\n   * @property {string} language - the language name\n   * @property {number} relevance - the relevance score\n   * @property {string} value - the highlighted HTML code\n   * @property {string} code - the original raw code\n   * @property {CompiledMode} top - top of the current mode stack\n   * @property {boolean} illegal - indicates whether any illegal matches were found\n  */\n  function highlight(codeOrLanguageName, optionsOrCode, ignoreIllegals) {\n    let code = \"\";\n    let languageName = \"\";\n    if (typeof optionsOrCode === \"object\") {\n      code = codeOrLanguageName;\n      ignoreIllegals = optionsOrCode.ignoreIllegals;\n      languageName = optionsOrCode.language;\n    } else {\n      // old API\n      deprecated(\"10.7.0\", \"highlight(lang, code, ...args) has been deprecated.\");\n      deprecated(\"10.7.0\", \"Please use highlight(code, options) instead.\\nhttps://github.com/highlightjs/highlight.js/issues/2277\");\n      languageName = codeOrLanguageName;\n      code = optionsOrCode;\n    }\n\n    // https://github.com/highlightjs/highlight.js/issues/3149\n    // eslint-disable-next-line no-undefined\n    if (ignoreIllegals === undefined) { ignoreIllegals = true; }\n\n    /** @type {BeforeHighlightContext} */\n    const context = {\n      code,\n      language: languageName\n    };\n    // the plugin can change the desired language or the code to be highlighted\n    // just be changing the object it was passed\n    fire(\"before:highlight\", context);\n\n    // a before plugin can usurp the result completely by providing it's own\n    // in which case we don't even need to call highlight\n    const result = context.result\n      ? context.result\n      : _highlight(context.language, context.code, ignoreIllegals);\n\n    result.code = context.code;\n    // the plugin can change anything in result to suite it\n    fire(\"after:highlight\", result);\n\n    return result;\n  }\n\n  /**\n   * private highlight that's used internally and does not fire callbacks\n   *\n   * @param {string} languageName - the language to use for highlighting\n   * @param {string} codeToHighlight - the code to highlight\n   * @param {boolean?} [ignoreIllegals] - whether to ignore illegal matches, default is to bail\n   * @param {CompiledMode?} [continuation] - current continuation mode, if any\n   * @returns {HighlightResult} - result of the highlight operation\n  */\n  function _highlight(languageName, codeToHighlight, ignoreIllegals, continuation) {\n    const keywordHits = Object.create(null);\n\n    /**\n     * Return keyword data if a match is a keyword\n     * @param {CompiledMode} mode - current mode\n     * @param {string} matchText - the textual match\n     * @returns {KeywordData | false}\n     */\n    function keywordData(mode, matchText) {\n      return mode.keywords[matchText];\n    }\n\n    function processKeywords() {\n      if (!top.keywords) {\n        emitter.addText(modeBuffer);\n        return;\n      }\n\n      let lastIndex = 0;\n      top.keywordPatternRe.lastIndex = 0;\n      let match = top.keywordPatternRe.exec(modeBuffer);\n      let buf = \"\";\n\n      while (match) {\n        buf += modeBuffer.substring(lastIndex, match.index);\n        const word = language.case_insensitive ? match[0].toLowerCase() : match[0];\n        const data = keywordData(top, word);\n        if (data) {\n          const [kind, keywordRelevance] = data;\n          emitter.addText(buf);\n          buf = \"\";\n\n          keywordHits[word] = (keywordHits[word] || 0) + 1;\n          if (keywordHits[word] <= MAX_KEYWORD_HITS) relevance += keywordRelevance;\n          if (kind.startsWith(\"_\")) {\n            // _ implied for relevance only, do not highlight\n            // by applying a class name\n            buf += match[0];\n          } else {\n            const cssClass = language.classNameAliases[kind] || kind;\n            emitKeyword(match[0], cssClass);\n          }\n        } else {\n          buf += match[0];\n        }\n        lastIndex = top.keywordPatternRe.lastIndex;\n        match = top.keywordPatternRe.exec(modeBuffer);\n      }\n      buf += modeBuffer.substring(lastIndex);\n      emitter.addText(buf);\n    }\n\n    function processSubLanguage() {\n      if (modeBuffer === \"\") return;\n      /** @type HighlightResult */\n      let result = null;\n\n      if (typeof top.subLanguage === 'string') {\n        if (!languages[top.subLanguage]) {\n          emitter.addText(modeBuffer);\n          return;\n        }\n        result = _highlight(top.subLanguage, modeBuffer, true, continuations[top.subLanguage]);\n        continuations[top.subLanguage] = /** @type {CompiledMode} */ (result._top);\n      } else {\n        result = highlightAuto(modeBuffer, top.subLanguage.length ? top.subLanguage : null);\n      }\n\n      // Counting embedded language score towards the host language may be disabled\n      // with zeroing the containing mode relevance. Use case in point is Markdown that\n      // allows XML everywhere and makes every XML snippet to have a much larger Markdown\n      // score.\n      if (top.relevance > 0) {\n        relevance += result.relevance;\n      }\n      emitter.__addSublanguage(result._emitter, result.language);\n    }\n\n    function processBuffer() {\n      if (top.subLanguage != null) {\n        processSubLanguage();\n      } else {\n        processKeywords();\n      }\n      modeBuffer = '';\n    }\n\n    /**\n     * @param {string} text\n     * @param {string} scope\n     */\n    function emitKeyword(keyword, scope) {\n      if (keyword === \"\") return;\n\n      emitter.startScope(scope);\n      emitter.addText(keyword);\n      emitter.endScope();\n    }\n\n    /**\n     * @param {CompiledScope} scope\n     * @param {RegExpMatchArray} match\n     */\n    function emitMultiClass(scope, match) {\n      let i = 1;\n      const max = match.length - 1;\n      while (i <= max) {\n        if (!scope._emit[i]) { i++; continue; }\n        const klass = language.classNameAliases[scope[i]] || scope[i];\n        const text = match[i];\n        if (klass) {\n          emitKeyword(text, klass);\n        } else {\n          modeBuffer = text;\n          processKeywords();\n          modeBuffer = \"\";\n        }\n        i++;\n      }\n    }\n\n    /**\n     * @param {CompiledMode} mode - new mode to start\n     * @param {RegExpMatchArray} match\n     */\n    function startNewMode(mode, match) {\n      if (mode.scope && typeof mode.scope === \"string\") {\n        emitter.openNode(language.classNameAliases[mode.scope] || mode.scope);\n      }\n      if (mode.beginScope) {\n        // beginScope just wraps the begin match itself in a scope\n        if (mode.beginScope._wrap) {\n          emitKeyword(modeBuffer, language.classNameAliases[mode.beginScope._wrap] || mode.beginScope._wrap);\n          modeBuffer = \"\";\n        } else if (mode.beginScope._multi) {\n          // at this point modeBuffer should just be the match\n          emitMultiClass(mode.beginScope, match);\n          modeBuffer = \"\";\n        }\n      }\n\n      top = Object.create(mode, { parent: { value: top } });\n      return top;\n    }\n\n    /**\n     * @param {CompiledMode } mode - the mode to potentially end\n     * @param {RegExpMatchArray} match - the latest match\n     * @param {string} matchPlusRemainder - match plus remainder of content\n     * @returns {CompiledMode | void} - the next mode, or if void continue on in current mode\n     */\n    function endOfMode(mode, match, matchPlusRemainder) {\n      let matched = startsWith(mode.endRe, matchPlusRemainder);\n\n      if (matched) {\n        if (mode[\"on:end\"]) {\n          const resp = new Response(mode);\n          mode[\"on:end\"](match, resp);\n          if (resp.isMatchIgnored) matched = false;\n        }\n\n        if (matched) {\n          while (mode.endsParent && mode.parent) {\n            mode = mode.parent;\n          }\n          return mode;\n        }\n      }\n      // even if on:end fires an `ignore` it's still possible\n      // that we might trigger the end node because of a parent mode\n      if (mode.endsWithParent) {\n        return endOfMode(mode.parent, match, matchPlusRemainder);\n      }\n    }\n\n    /**\n     * Handle matching but then ignoring a sequence of text\n     *\n     * @param {string} lexeme - string containing full match text\n     */\n    function doIgnore(lexeme) {\n      if (top.matcher.regexIndex === 0) {\n        // no more regexes to potentially match here, so we move the cursor forward one\n        // space\n        modeBuffer += lexeme[0];\n        return 1;\n      } else {\n        // no need to move the cursor, we still have additional regexes to try and\n        // match at this very spot\n        resumeScanAtSamePosition = true;\n        return 0;\n      }\n    }\n\n    /**\n     * Handle the start of a new potential mode match\n     *\n     * @param {EnhancedMatch} match - the current match\n     * @returns {number} how far to advance the parse cursor\n     */\n    function doBeginMatch(match) {\n      const lexeme = match[0];\n      const newMode = match.rule;\n\n      const resp = new Response(newMode);\n      // first internal before callbacks, then the public ones\n      const beforeCallbacks = [newMode.__beforeBegin, newMode[\"on:begin\"]];\n      for (const cb of beforeCallbacks) {\n        if (!cb) continue;\n        cb(match, resp);\n        if (resp.isMatchIgnored) return doIgnore(lexeme);\n      }\n\n      if (newMode.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (newMode.excludeBegin) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (!newMode.returnBegin && !newMode.excludeBegin) {\n          modeBuffer = lexeme;\n        }\n      }\n      startNewMode(newMode, match);\n      return newMode.returnBegin ? 0 : lexeme.length;\n    }\n\n    /**\n     * Handle the potential end of mode\n     *\n     * @param {RegExpMatchArray} match - the current match\n     */\n    function doEndMatch(match) {\n      const lexeme = match[0];\n      const matchPlusRemainder = codeToHighlight.substring(match.index);\n\n      const endMode = endOfMode(top, match, matchPlusRemainder);\n      if (!endMode) { return NO_MATCH; }\n\n      const origin = top;\n      if (top.endScope && top.endScope._wrap) {\n        processBuffer();\n        emitKeyword(lexeme, top.endScope._wrap);\n      } else if (top.endScope && top.endScope._multi) {\n        processBuffer();\n        emitMultiClass(top.endScope, match);\n      } else if (origin.skip) {\n        modeBuffer += lexeme;\n      } else {\n        if (!(origin.returnEnd || origin.excludeEnd)) {\n          modeBuffer += lexeme;\n        }\n        processBuffer();\n        if (origin.excludeEnd) {\n          modeBuffer = lexeme;\n        }\n      }\n      do {\n        if (top.scope) {\n          emitter.closeNode();\n        }\n        if (!top.skip && !top.subLanguage) {\n          relevance += top.relevance;\n        }\n        top = top.parent;\n      } while (top !== endMode.parent);\n      if (endMode.starts) {\n        startNewMode(endMode.starts, match);\n      }\n      return origin.returnEnd ? 0 : lexeme.length;\n    }\n\n    function processContinuations() {\n      const list = [];\n      for (let current = top; current !== language; current = current.parent) {\n        if (current.scope) {\n          list.unshift(current.scope);\n        }\n      }\n      list.forEach(item => emitter.openNode(item));\n    }\n\n    /** @type {{type?: MatchType, index?: number, rule?: Mode}}} */\n    let lastMatch = {};\n\n    /**\n     *  Process an individual match\n     *\n     * @param {string} textBeforeMatch - text preceding the match (since the last match)\n     * @param {EnhancedMatch} [match] - the match itself\n     */\n    function processLexeme(textBeforeMatch, match) {\n      const lexeme = match && match[0];\n\n      // add non-matched text to the current mode buffer\n      modeBuffer += textBeforeMatch;\n\n      if (lexeme == null) {\n        processBuffer();\n        return 0;\n      }\n\n      // we've found a 0 width match and we're stuck, so we need to advance\n      // this happens when we have badly behaved rules that have optional matchers to the degree that\n      // sometimes they can end up matching nothing at all\n      // Ref: https://github.com/highlightjs/highlight.js/issues/2140\n      if (lastMatch.type === \"begin\" && match.type === \"end\" && lastMatch.index === match.index && lexeme === \"\") {\n        // spit the \"skipped\" character that our regex choked on back into the output sequence\n        modeBuffer += codeToHighlight.slice(match.index, match.index + 1);\n        if (!SAFE_MODE) {\n          /** @type {AnnotatedError} */\n          const err = new Error(`0 width match regex (${languageName})`);\n          err.languageName = languageName;\n          err.badRule = lastMatch.rule;\n          throw err;\n        }\n        return 1;\n      }\n      lastMatch = match;\n\n      if (match.type === \"begin\") {\n        return doBeginMatch(match);\n      } else if (match.type === \"illegal\" && !ignoreIllegals) {\n        // illegal match, we do not continue processing\n        /** @type {AnnotatedError} */\n        const err = new Error('Illegal lexeme \"' + lexeme + '\" for mode \"' + (top.scope || '<unnamed>') + '\"');\n        err.mode = top;\n        throw err;\n      } else if (match.type === \"end\") {\n        const processed = doEndMatch(match);\n        if (processed !== NO_MATCH) {\n          return processed;\n        }\n      }\n\n      // edge case for when illegal matches $ (end of line) which is technically\n      // a 0 width match but not a begin/end match so it's not caught by the\n      // first handler (when ignoreIllegals is true)\n      if (match.type === \"illegal\" && lexeme === \"\") {\n        // advance so we aren't stuck in an infinite loop\n        modeBuffer += \"\\n\";\n        return 1;\n      }\n\n      // infinite loops are BAD, this is a last ditch catch all. if we have a\n      // decent number of iterations yet our index (cursor position in our\n      // parsing) still 3x behind our index then something is very wrong\n      // so we bail\n      if (iterations > 100000 && iterations > match.index * 3) {\n        const err = new Error('potential infinite loop, way more iterations than matches');\n        throw err;\n      }\n\n      /*\n      Why might be find ourselves here?  An potential end match that was\n      triggered but could not be completed.  IE, `doEndMatch` returned NO_MATCH.\n      (this could be because a callback requests the match be ignored, etc)\n\n      This causes no real harm other than stopping a few times too many.\n      */\n\n      modeBuffer += lexeme;\n      return lexeme.length;\n    }\n\n    const language = getLanguage(languageName);\n    if (!language) {\n      error(LANGUAGE_NOT_FOUND.replace(\"{}\", languageName));\n      throw new Error('Unknown language: \"' + languageName + '\"');\n    }\n\n    const md = compileLanguage(language);\n    let result = '';\n    /** @type {CompiledMode} */\n    let top = continuation || md;\n    /** @type Record<string,CompiledMode> */\n    const continuations = {}; // keep continuations for sub-languages\n    const emitter = new options.__emitter(options);\n    processContinuations();\n    let modeBuffer = '';\n    let relevance = 0;\n    let index = 0;\n    let iterations = 0;\n    let resumeScanAtSamePosition = false;\n\n    try {\n      if (!language.__emitTokens) {\n        top.matcher.considerAll();\n\n        for (;;) {\n          iterations++;\n          if (resumeScanAtSamePosition) {\n            // only regexes not matched previously will now be\n            // considered for a potential match\n            resumeScanAtSamePosition = false;\n          } else {\n            top.matcher.considerAll();\n          }\n          top.matcher.lastIndex = index;\n\n          const match = top.matcher.exec(codeToHighlight);\n          // console.log(\"match\", match[0], match.rule && match.rule.begin)\n\n          if (!match) break;\n\n          const beforeMatch = codeToHighlight.substring(index, match.index);\n          const processedCount = processLexeme(beforeMatch, match);\n          index = match.index + processedCount;\n        }\n        processLexeme(codeToHighlight.substring(index));\n      } else {\n        language.__emitTokens(codeToHighlight, emitter);\n      }\n\n      emitter.finalize();\n      result = emitter.toHTML();\n\n      return {\n        language: languageName,\n        value: result,\n        relevance,\n        illegal: false,\n        _emitter: emitter,\n        _top: top\n      };\n    } catch (err) {\n      if (err.message && err.message.includes('Illegal')) {\n        return {\n          language: languageName,\n          value: escape(codeToHighlight),\n          illegal: true,\n          relevance: 0,\n          _illegalBy: {\n            message: err.message,\n            index,\n            context: codeToHighlight.slice(index - 100, index + 100),\n            mode: err.mode,\n            resultSoFar: result\n          },\n          _emitter: emitter\n        };\n      } else if (SAFE_MODE) {\n        return {\n          language: languageName,\n          value: escape(codeToHighlight),\n          illegal: false,\n          relevance: 0,\n          errorRaised: err,\n          _emitter: emitter,\n          _top: top\n        };\n      } else {\n        throw err;\n      }\n    }\n  }\n\n  /**\n   * returns a valid highlight result, without actually doing any actual work,\n   * auto highlight starts with this and it's possible for small snippets that\n   * auto-detection may not find a better match\n   * @param {string} code\n   * @returns {HighlightResult}\n   */\n  function justTextHighlightResult(code) {\n    const result = {\n      value: escape(code),\n      illegal: false,\n      relevance: 0,\n      _top: PLAINTEXT_LANGUAGE,\n      _emitter: new options.__emitter(options)\n    };\n    result._emitter.addText(code);\n    return result;\n  }\n\n  /**\n  Highlighting with language detection. Accepts a string with the code to\n  highlight. Returns an object with the following properties:\n\n  - language (detected language)\n  - relevance (int)\n  - value (an HTML string with highlighting markup)\n  - secondBest (object with the same structure for second-best heuristically\n    detected language, may be absent)\n\n    @param {string} code\n    @param {Array<string>} [languageSubset]\n    @returns {AutoHighlightResult}\n  */\n  function highlightAuto(code, languageSubset) {\n    languageSubset = languageSubset || options.languages || Object.keys(languages);\n    const plaintext = justTextHighlightResult(code);\n\n    const results = languageSubset.filter(getLanguage).filter(autoDetection).map(name =>\n      _highlight(name, code, false)\n    );\n    results.unshift(plaintext); // plaintext is always an option\n\n    const sorted = results.sort((a, b) => {\n      // sort base on relevance\n      if (a.relevance !== b.relevance) return b.relevance - a.relevance;\n\n      // always award the tie to the base language\n      // ie if C++ and Arduino are tied, it's more likely to be C++\n      if (a.language && b.language) {\n        if (getLanguage(a.language).supersetOf === b.language) {\n          return 1;\n        } else if (getLanguage(b.language).supersetOf === a.language) {\n          return -1;\n        }\n      }\n\n      // otherwise say they are equal, which has the effect of sorting on\n      // relevance while preserving the original ordering - which is how ties\n      // have historically been settled, ie the language that comes first always\n      // wins in the case of a tie\n      return 0;\n    });\n\n    const [best, secondBest] = sorted;\n\n    /** @type {AutoHighlightResult} */\n    const result = best;\n    result.secondBest = secondBest;\n\n    return result;\n  }\n\n  /**\n   * Builds new class name for block given the language name\n   *\n   * @param {HTMLElement} element\n   * @param {string} [currentLang]\n   * @param {string} [resultLang]\n   */\n  function updateClassName(element, currentLang, resultLang) {\n    const language = (currentLang && aliases[currentLang]) || resultLang;\n\n    element.classList.add(\"hljs\");\n    element.classList.add(`language-${language}`);\n  }\n\n  /**\n   * Applies highlighting to a DOM node containing code.\n   *\n   * @param {HighlightedHTMLElement} element - the HTML element to highlight\n  */\n  function highlightElement(element) {\n    /** @type HTMLElement */\n    let node = null;\n    const language = blockLanguage(element);\n\n    if (shouldNotHighlight(language)) return;\n\n    fire(\"before:highlightElement\",\n      { el: element, language });\n\n    if (element.dataset.highlighted) {\n      console.log(\"Element previously highlighted. To highlight again, first unset `dataset.highlighted`.\", element);\n      return;\n    }\n\n    // we should be all text, no child nodes (unescaped HTML) - this is possibly\n    // an HTML injection attack - it's likely too late if this is already in\n    // production (the code has likely already done its damage by the time\n    // we're seeing it)... but we yell loudly about this so that hopefully it's\n    // more likely to be caught in development before making it to production\n    if (element.children.length > 0) {\n      if (!options.ignoreUnescapedHTML) {\n        console.warn(\"One of your code blocks includes unescaped HTML. This is a potentially serious security risk.\");\n        console.warn(\"https://github.com/highlightjs/highlight.js/wiki/security\");\n        console.warn(\"The element with unescaped HTML:\");\n        console.warn(element);\n      }\n      if (options.throwUnescapedHTML) {\n        const err = new HTMLInjectionError(\n          \"One of your code blocks includes unescaped HTML.\",\n          element.innerHTML\n        );\n        throw err;\n      }\n    }\n\n    node = element;\n    const text = node.textContent;\n    const result = language ? highlight(text, { language, ignoreIllegals: true }) : highlightAuto(text);\n\n    element.innerHTML = result.value;\n    element.dataset.highlighted = \"yes\";\n    updateClassName(element, language, result.language);\n    element.result = {\n      language: result.language,\n      // TODO: remove with version 11.0\n      re: result.relevance,\n      relevance: result.relevance\n    };\n    if (result.secondBest) {\n      element.secondBest = {\n        language: result.secondBest.language,\n        relevance: result.secondBest.relevance\n      };\n    }\n\n    fire(\"after:highlightElement\", { el: element, result, text });\n  }\n\n  /**\n   * Updates highlight.js global options with the passed options\n   *\n   * @param {Partial<HLJSOptions>} userOptions\n   */\n  function configure(userOptions) {\n    options = inherit(options, userOptions);\n  }\n\n  // TODO: remove v12, deprecated\n  const initHighlighting = () => {\n    highlightAll();\n    deprecated(\"10.6.0\", \"initHighlighting() deprecated.  Use highlightAll() now.\");\n  };\n\n  // TODO: remove v12, deprecated\n  function initHighlightingOnLoad() {\n    highlightAll();\n    deprecated(\"10.6.0\", \"initHighlightingOnLoad() deprecated.  Use highlightAll() now.\");\n  }\n\n  let wantsHighlight = false;\n\n  /**\n   * auto-highlights all pre>code elements on the page\n   */\n  function highlightAll() {\n    function boot() {\n      // if a highlight was requested before DOM was loaded, do now\n      highlightAll();\n    }\n\n    // if we are called too early in the loading process\n    if (document.readyState === \"loading\") {\n      // make sure the event listener is only added once\n      if (!wantsHighlight) {\n        window.addEventListener('DOMContentLoaded', boot, false);\n      }\n      wantsHighlight = true;\n      return;\n    }\n\n    const blocks = document.querySelectorAll(options.cssSelector);\n    blocks.forEach(highlightElement);\n  }\n\n  /**\n   * Register a language grammar module\n   *\n   * @param {string} languageName\n   * @param {LanguageFn} languageDefinition\n   */\n  function registerLanguage(languageName, languageDefinition) {\n    let lang = null;\n    try {\n      lang = languageDefinition(hljs);\n    } catch (error$1) {\n      error(\"Language definition for '{}' could not be registered.\".replace(\"{}\", languageName));\n      // hard or soft error\n      if (!SAFE_MODE) { throw error$1; } else { error(error$1); }\n      // languages that have serious errors are replaced with essentially a\n      // \"plaintext\" stand-in so that the code blocks will still get normal\n      // css classes applied to them - and one bad language won't break the\n      // entire highlighter\n      lang = PLAINTEXT_LANGUAGE;\n    }\n    // give it a temporary name if it doesn't have one in the meta-data\n    if (!lang.name) lang.name = languageName;\n    languages[languageName] = lang;\n    lang.rawDefinition = languageDefinition.bind(null, hljs);\n\n    if (lang.aliases) {\n      registerAliases(lang.aliases, { languageName });\n    }\n  }\n\n  /**\n   * Remove a language grammar module\n   *\n   * @param {string} languageName\n   */\n  function unregisterLanguage(languageName) {\n    delete languages[languageName];\n    for (const alias of Object.keys(aliases)) {\n      if (aliases[alias] === languageName) {\n        delete aliases[alias];\n      }\n    }\n  }\n\n  /**\n   * @returns {string[]} List of language internal names\n   */\n  function listLanguages() {\n    return Object.keys(languages);\n  }\n\n  /**\n   * @param {string} name - name of the language to retrieve\n   * @returns {Language | undefined}\n   */\n  function getLanguage(name) {\n    name = (name || '').toLowerCase();\n    return languages[name] || languages[aliases[name]];\n  }\n\n  /**\n   *\n   * @param {string|string[]} aliasList - single alias or list of aliases\n   * @param {{languageName: string}} opts\n   */\n  function registerAliases(aliasList, { languageName }) {\n    if (typeof aliasList === 'string') {\n      aliasList = [aliasList];\n    }\n    aliasList.forEach(alias => { aliases[alias.toLowerCase()] = languageName; });\n  }\n\n  /**\n   * Determines if a given language has auto-detection enabled\n   * @param {string} name - name of the language\n   */\n  function autoDetection(name) {\n    const lang = getLanguage(name);\n    return lang && !lang.disableAutodetect;\n  }\n\n  /**\n   * Upgrades the old highlightBlock plugins to the new\n   * highlightElement API\n   * @param {HLJSPlugin} plugin\n   */\n  function upgradePluginAPI(plugin) {\n    // TODO: remove with v12\n    if (plugin[\"before:highlightBlock\"] && !plugin[\"before:highlightElement\"]) {\n      plugin[\"before:highlightElement\"] = (data) => {\n        plugin[\"before:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n    if (plugin[\"after:highlightBlock\"] && !plugin[\"after:highlightElement\"]) {\n      plugin[\"after:highlightElement\"] = (data) => {\n        plugin[\"after:highlightBlock\"](\n          Object.assign({ block: data.el }, data)\n        );\n      };\n    }\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function addPlugin(plugin) {\n    upgradePluginAPI(plugin);\n    plugins.push(plugin);\n  }\n\n  /**\n   * @param {HLJSPlugin} plugin\n   */\n  function removePlugin(plugin) {\n    const index = plugins.indexOf(plugin);\n    if (index !== -1) {\n      plugins.splice(index, 1);\n    }\n  }\n\n  /**\n   *\n   * @param {PluginEvent} event\n   * @param {any} args\n   */\n  function fire(event, args) {\n    const cb = event;\n    plugins.forEach(function(plugin) {\n      if (plugin[cb]) {\n        plugin[cb](args);\n      }\n    });\n  }\n\n  /**\n   * DEPRECATED\n   * @param {HighlightedHTMLElement} el\n   */\n  function deprecateHighlightBlock(el) {\n    deprecated(\"10.7.0\", \"highlightBlock will be removed entirely in v12.0\");\n    deprecated(\"10.7.0\", \"Please use highlightElement now.\");\n\n    return highlightElement(el);\n  }\n\n  /* Interface definition */\n  Object.assign(hljs, {\n    highlight,\n    highlightAuto,\n    highlightAll,\n    highlightElement,\n    // TODO: Remove with v12 API\n    highlightBlock: deprecateHighlightBlock,\n    configure,\n    initHighlighting,\n    initHighlightingOnLoad,\n    registerLanguage,\n    unregisterLanguage,\n    listLanguages,\n    getLanguage,\n    registerAliases,\n    autoDetection,\n    inherit,\n    addPlugin,\n    removePlugin\n  });\n\n  hljs.debugMode = function() { SAFE_MODE = false; };\n  hljs.safeMode = function() { SAFE_MODE = true; };\n  hljs.versionString = version;\n\n  hljs.regex = {\n    concat: concat,\n    lookahead: lookahead,\n    either: either,\n    optional: optional,\n    anyNumberOfTimes: anyNumberOfTimes\n  };\n\n  for (const key in MODES) {\n    // @ts-ignore\n    if (typeof MODES[key] === \"object\") {\n      // @ts-ignore\n      deepFreeze(MODES[key]);\n    }\n  }\n\n  // merge all the modes/regexes into our main object\n  Object.assign(hljs, MODES);\n\n  return hljs;\n};\n\n// Other names for the variable may break build script\nconst highlight = HLJS({});\n\n// returns a new instance of the highlighter to be used for extensions\n// check https://github.com/wooorm/lowlight/issues/47\nhighlight.newInstance = () => HLJS({});\n\nmodule.exports = highlight;\nhighlight.HighlightJS = highlight;\nhighlight.default = highlight;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEA,SAASA,UAAUA,CAACC,GAAG,EAAE;EACvB,IAAIA,GAAG,YAAYC,GAAG,EAAE;IACtBD,GAAG,CAACE,KAAK,GACPF,GAAG,CAACG,MAAM,GACVH,GAAG,CAACI,GAAG,GACL,YAAY;MACV,MAAM,IAAIC,KAAK,CAAC,kBAAkB,CAAC;IACrC,CAAC;EACP,CAAC,MAAM,IAAIL,GAAG,YAAYM,GAAG,EAAE;IAC7BN,GAAG,CAACO,GAAG,GACLP,GAAG,CAACE,KAAK,GACTF,GAAG,CAACG,MAAM,GACR,YAAY;MACV,MAAM,IAAIE,KAAK,CAAC,kBAAkB,CAAC;IACrC,CAAC;EACP;;EAEA;EACAG,MAAM,CAACC,MAAM,CAACT,GAAG,CAAC;EAElBQ,MAAM,CAACE,mBAAmB,CAACV,GAAG,CAAC,CAACW,OAAO,CAAC,UAACC,IAAI,EAAK;IAChD,IAAMC,IAAI,GAAGb,GAAG,CAACY,IAAI,CAAC;IACtB,IAAME,IAAI,GAAG,OAAOD,IAAI;;IAExB;IACA,IAAI,CAACC,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,UAAU,KAAK,CAACN,MAAM,CAACO,QAAQ,CAACF,IAAI,CAAC,EAAE;MACxEd,UAAU,CAACc,IAAI,CAAC;IAClB;EACF,CAAC,CAAC;EAEF,OAAOb,GAAG;AACZ;;AAEA;AACA;AACA;AAAA,IAEMgB,QAAQ;EAAA;;EACZ;AACF;AACA;EACE,SAAAA,SAAYC,IAAI,EAAE;IAAAC,eAAA,OAAAF,QAAA;IAChB;IACA,IAAIC,IAAI,CAACE,IAAI,KAAKC,SAAS,EAAEH,IAAI,CAACE,IAAI,GAAG,CAAC,CAAC;IAE3C,IAAI,CAACA,IAAI,GAAGF,IAAI,CAACE,IAAI;IACrB,IAAI,CAACE,cAAc,GAAG,KAAK;EAC7B;EAAC,OAAAC,YAAA,CAAAN,QAAA;IAAAO,GAAA;IAAAC,KAAA,EAED,SAAAC,WAAWA,CAAA,EAAG;MACZ,IAAI,CAACJ,cAAc,GAAG,IAAI;IAC5B;EAAC;AAAA;AAGH;AACA;AACA;AACA;AACA,SAASK,UAAUA,CAACF,KAAK,EAAE;EACzB,OAAOA,KAAK,CACTG,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CACtBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CACrBA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,CACvBA,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,QAAQ,EAAc;EACvC;EACA,IAAMC,MAAM,GAAGtB,MAAM,CAACuB,MAAM,CAAC,IAAI,CAAC;EAElC,KAAK,IAAMR,GAAG,IAAIM,QAAQ,EAAE;IAC1BC,MAAM,CAACP,GAAG,CAAC,GAAGM,QAAQ,CAACN,GAAG,CAAC;EAC7B;EAAC,SAAAS,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAN6BC,OAAO,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAPF,OAAO,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EAOrCF,OAAO,CAACxB,OAAO,CAAC,UAASX,GAAG,EAAE;IAC5B,KAAK,IAAMuB,KAAG,IAAIvB,GAAG,EAAE;MACrB8B,MAAM,CAACP,KAAG,CAAC,GAAGvB,GAAG,CAACuB,KAAG,CAAC;IACxB;EACF,CAAC,CAAC;EACF,OAAO,gBAAkBO,MAAM;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA,IAAMQ,UAAU,GAAG,SAAS;;AAE5B;AACA;AACA;AACA;AACA,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,IAAI,EAAK;EAClC;EACA;EACA,OAAO,CAAC,CAACA,IAAI,CAACC,KAAK;AACrB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAI9B,IAAI,EAAA+B,IAAA,EAAiB;EAAA,IAAbC,MAAM,GAAAD,IAAA,CAANC,MAAM;EACrC;EACA,IAAIhC,IAAI,CAACiC,UAAU,CAAC,WAAW,CAAC,EAAE;IAChC,OAAOjC,IAAI,CAACe,OAAO,CAAC,WAAW,EAAE,WAAW,CAAC;EAC/C;EACA;EACA,IAAIf,IAAI,CAACkC,QAAQ,CAAC,GAAG,CAAC,EAAE;IACtB,IAAMC,MAAM,GAAGnC,IAAI,CAACoC,KAAK,CAAC,GAAG,CAAC;IAC9B,OAAO,CACL,GAAGJ,MAAM,GAAGG,MAAM,CAACE,KAAK,CAAC,CAAC,EAAE,EAAAC,MAAA,CAAAC,kBAAA,CACxBJ,MAAM,CAACK,GAAG,CAAC,UAACC,CAAC,EAAEC,CAAC;MAAA,OAAK,GAAGD,CAAC,GAAG,GAAG,CAACE,MAAM,CAACD,CAAC,GAAG,CAAC,CAAC,EAAE;IAAA,EAAC,GACpDE,IAAI,CAAC,GAAG,CAAC;EACb;EACA;EACA,OAAO,GAAGZ,MAAM,GAAGhC,IAAI,EAAE;AAC3B,CAAC;;AAED;AAAA,IACM6C,YAAY;EAAA;;EAChB;AACF;AACA;AACA;AACA;AACA;EACE,SAAAA,aAAYC,SAAS,EAAEC,OAAO,EAAE;IAAAzC,eAAA,OAAAuC,YAAA;IAC9B,IAAI,CAACG,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,WAAW,GAAGF,OAAO,CAACE,WAAW;IACtCH,SAAS,CAACI,IAAI,CAAC,IAAI,CAAC;EACtB;;EAEA;AACF;AACA;AACA;EAHE,OAAAxC,YAAA,CAAAmC,YAAA;IAAAlC,GAAA;IAAAC,KAAA,EAIA,SAAAuC,OAAOA,CAACC,IAAI,EAAE;MACZ,IAAI,CAACJ,MAAM,IAAIlC,UAAU,CAACsC,IAAI,CAAC;IACjC;;IAEA;AACF;AACA;AACA;EAHE;IAAAzC,GAAA;IAAAC,KAAA,EAIA,SAAAyC,QAAQA,CAACzB,IAAI,EAAE;MACb,IAAI,CAACD,iBAAiB,CAACC,IAAI,CAAC,EAAE;MAE9B,IAAM0B,SAAS,GAAGxB,eAAe,CAACF,IAAI,CAACC,KAAK,EAC1C;QAAEG,MAAM,EAAE,IAAI,CAACiB;MAAY,CAAC,CAAC;MAC/B,IAAI,CAACM,IAAI,CAACD,SAAS,CAAC;IACtB;;IAEA;AACF;AACA;AACA;EAHE;IAAA3C,GAAA;IAAAC,KAAA,EAIA,SAAA4C,SAASA,CAAC5B,IAAI,EAAE;MACd,IAAI,CAACD,iBAAiB,CAACC,IAAI,CAAC,EAAE;MAE9B,IAAI,CAACoB,MAAM,IAAItB,UAAU;IAC3B;;IAEA;AACF;AACA;EAFE;IAAAf,GAAA;IAAAC,KAAA,EAGA,SAAAA,KAAKA,CAAA,EAAG;MACN,OAAO,IAAI,CAACoC,MAAM;IACpB;;IAEA;;IAEA;AACF;AACA;AACA;EAHE;IAAArC,GAAA;IAAAC,KAAA,EAIA,SAAA2C,IAAIA,CAACD,SAAS,EAAE;MACd,IAAI,CAACN,MAAM,IAAI,gBAAgBM,SAAS,IAAI;IAC9C;EAAC;AAAA;AAGH;AACA;AACA;AACA;AAEA;AACA,IAAMG,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAkB;EAAA,IAAdC,IAAI,GAAArC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAAG,CAAC,CAAC;EACxB;EACA,IAAMH,MAAM,GAAG;IAAEyC,QAAQ,EAAE;EAAG,CAAC;EAC/B/D,MAAM,CAACgE,MAAM,CAAC1C,MAAM,EAAEwC,IAAI,CAAC;EAC3B,OAAOxC,MAAM;AACf,CAAC;AAAC,IAEI2C,SAAS;EAAA;;EACb,SAAAA,UAAA,EAAc;IAAAvD,eAAA,OAAAuD,SAAA;IACZ;IACA,IAAI,CAACC,QAAQ,GAAGL,OAAO,CAAC,CAAC;IACzB,IAAI,CAACM,KAAK,GAAG,CAAC,IAAI,CAACD,QAAQ,CAAC;EAC9B;EAAC,OAAApD,YAAA,CAAAmD,SAAA;IAAAlD,GAAA;IAAAqD,GAAA,EAED,SAAAA,IAAA,EAAU;MACR,OAAO,IAAI,CAACD,KAAK,CAAC,IAAI,CAACA,KAAK,CAACzC,MAAM,GAAG,CAAC,CAAC;IAC1C;EAAC;IAAAX,GAAA;IAAAqD,GAAA,EAED,SAAAA,IAAA,EAAW;MAAE,OAAO,IAAI,CAACF,QAAQ;IAAE;;IAEnC;EAAA;IAAAnD,GAAA;IAAAC,KAAA,EACA,SAAAjB,GAAGA,CAACiC,IAAI,EAAE;MACR,IAAI,CAACqC,GAAG,CAACN,QAAQ,CAACO,IAAI,CAACtC,IAAI,CAAC;IAC9B;;IAEA;EAAA;IAAAjB,GAAA;IAAAC,KAAA,EACA,SAAAyC,QAAQA,CAACxB,KAAK,EAAE;MACd;MACA,IAAMD,IAAI,GAAG6B,OAAO,CAAC;QAAE5B;MAAM,CAAC,CAAC;MAC/B,IAAI,CAAClC,GAAG,CAACiC,IAAI,CAAC;MACd,IAAI,CAACmC,KAAK,CAACG,IAAI,CAACtC,IAAI,CAAC;IACvB;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EAED,SAAA4C,SAASA,CAAA,EAAG;MACV,IAAI,IAAI,CAACO,KAAK,CAACzC,MAAM,GAAG,CAAC,EAAE;QACzB,OAAO,IAAI,CAACyC,KAAK,CAACI,GAAG,CAAC,CAAC;MACzB;MACA;MACA,OAAO3D,SAAS;IAClB;EAAC;IAAAG,GAAA;IAAAC,KAAA,EAED,SAAAwD,aAAaA,CAAA,EAAG;MACd,OAAO,IAAI,CAACZ,SAAS,CAAC,CAAC,CAAC;IAC1B;EAAC;IAAA7C,GAAA;IAAAC,KAAA,EAED,SAAAyD,MAAMA,CAAA,EAAG;MACP,OAAOC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACT,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC;IAC/C;;IAEA;AACF;AACA;AACA;EAHE;IAAAnD,GAAA;IAAAC,KAAA,EAIA,SAAAsC,IAAIA,CAACsB,OAAO,EAAE;MACZ;MACA,OAAO,IAAI,CAACC,WAAW,CAACC,KAAK,CAACF,OAAO,EAAE,IAAI,CAACV,QAAQ,CAAC;MACrD;MACA;IACF;;IAEA;AACF;AACA;AACA;EAHE;IAAAnD,GAAA;IAAAC,KAAA,EAIA,SAAO8D,KAAKA,CAACF,OAAO,EAAE5C,IAAI,EAAE;MAAA,IAAA+C,KAAA;MAC1B,IAAI,OAAO/C,IAAI,KAAK,QAAQ,EAAE;QAC5B4C,OAAO,CAACrB,OAAO,CAACvB,IAAI,CAAC;MACvB,CAAC,MAAM,IAAIA,IAAI,CAAC+B,QAAQ,EAAE;QACxBa,OAAO,CAACnB,QAAQ,CAACzB,IAAI,CAAC;QACtBA,IAAI,CAAC+B,QAAQ,CAAC5D,OAAO,CAAC,UAAC6E,KAAK;UAAA,OAAKD,KAAI,CAACD,KAAK,CAACF,OAAO,EAAEI,KAAK,CAAC;QAAA,EAAC;QAC5DJ,OAAO,CAAChB,SAAS,CAAC5B,IAAI,CAAC;MACzB;MACA,OAAO4C,OAAO;IAChB;;IAEA;AACF;AACA;EAFE;IAAA7D,GAAA;IAAAC,KAAA,EAGA,SAAOiE,SAASA,CAACjD,IAAI,EAAE;MACrB,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;MAC9B,IAAI,CAACA,IAAI,CAAC+B,QAAQ,EAAE;MAEpB,IAAI/B,IAAI,CAAC+B,QAAQ,CAACmB,KAAK,CAAC,UAAAC,EAAE;QAAA,OAAI,OAAOA,EAAE,KAAK,QAAQ;MAAA,EAAC,EAAE;QACrD;QACA;QACAnD,IAAI,CAAC+B,QAAQ,GAAG,CAAC/B,IAAI,CAAC+B,QAAQ,CAACf,IAAI,CAAC,EAAE,CAAC,CAAC;MAC1C,CAAC,MAAM;QACLhB,IAAI,CAAC+B,QAAQ,CAAC5D,OAAO,CAAC,UAAC6E,KAAK,EAAK;UAC/Bf,SAAS,CAACgB,SAAS,CAACD,KAAK,CAAC;QAC5B,CAAC,CAAC;MACJ;IACF;EAAC;AAAA;AAGH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAFA,IAGMI,gBAAgB,0BAAAC,UAAA;EAAA;;EACpB;AACF;AACA;EACE,SAAAD,iBAAYjC,OAAO,EAAE;IAAA,IAAAmC,MAAA;IAAA5E,eAAA,OAAA0E,gBAAA;IACnBE,MAAA,GAAAC,UAAA,OAAAH,gBAAA;IACAE,MAAA,CAAKnC,OAAO,GAAGA,OAAO;IAAC,OAAAmC,MAAA;EACzB;;EAEA;AACF;AACA;EAFEE,SAAA,CAAAJ,gBAAA,EAAAC,UAAA;EAAA,OAAAvE,YAAA,CAAAsE,gBAAA;IAAArE,GAAA;IAAAC,KAAA,EAGA,SAAAuC,OAAOA,CAACC,IAAI,EAAE;MACZ,IAAIA,IAAI,KAAK,EAAE,EAAE;QAAE;MAAQ;MAE3B,IAAI,CAACzD,GAAG,CAACyD,IAAI,CAAC;IAChB;;IAEA;EAAA;IAAAzC,GAAA;IAAAC,KAAA,EACA,SAAAyE,UAAUA,CAACxD,KAAK,EAAE;MAChB,IAAI,CAACwB,QAAQ,CAACxB,KAAK,CAAC;IACtB;EAAC;IAAAlB,GAAA;IAAAC,KAAA,EAED,SAAA0E,QAAQA,CAAA,EAAG;MACT,IAAI,CAAC9B,SAAS,CAAC,CAAC;IAClB;;IAEA;AACF;AACA;AACA;EAHE;IAAA7C,GAAA;IAAAC,KAAA,EAIA,SAAA2E,gBAAgBA,CAACC,OAAO,EAAExF,IAAI,EAAE;MAC9B;MACA,IAAM4B,IAAI,GAAG4D,OAAO,CAACC,IAAI;MACzB,IAAIzF,IAAI,EAAE4B,IAAI,CAACC,KAAK,GAAG,YAAY7B,IAAI,EAAE;MAEzC,IAAI,CAACL,GAAG,CAACiC,IAAI,CAAC;IAChB;EAAC;IAAAjB,GAAA;IAAAC,KAAA,EAED,SAAA8E,MAAMA,CAAA,EAAG;MACP,IAAMC,QAAQ,GAAG,IAAI9C,YAAY,CAAC,IAAI,EAAE,IAAI,CAACE,OAAO,CAAC;MACrD,OAAO4C,QAAQ,CAAC/E,KAAK,CAAC,CAAC;IACzB;EAAC;IAAAD,GAAA;IAAAC,KAAA,EAED,SAAAgF,QAAQA,CAAA,EAAG;MACT,IAAI,CAACxB,aAAa,CAAC,CAAC;MACpB,OAAO,IAAI;IACb;EAAC;AAAA,EA/C4BP,SAAS;AAkDxC;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA,SAASgC,MAAMA,CAACC,EAAE,EAAE;EAClB,IAAI,CAACA,EAAE,EAAE,OAAO,IAAI;EACpB,IAAI,OAAOA,EAAE,KAAK,QAAQ,EAAE,OAAOA,EAAE;EAErC,OAAOA,EAAE,CAACD,MAAM;AAClB;;AAEA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAACD,EAAE,EAAE;EACrB,OAAOxD,MAAM,CAAC,KAAK,EAAEwD,EAAE,EAAE,GAAG,CAAC;AAC/B;;AAEA;AACA;AACA;AACA;AACA,SAASE,gBAAgBA,CAACF,EAAE,EAAE;EAC5B,OAAOxD,MAAM,CAAC,KAAK,EAAEwD,EAAE,EAAE,IAAI,CAAC;AAChC;;AAEA;AACA;AACA;AACA;AACA,SAASG,QAAQA,CAACH,EAAE,EAAE;EACpB,OAAOxD,MAAM,CAAC,KAAK,EAAEwD,EAAE,EAAE,IAAI,CAAC;AAChC;;AAEA;AACA;AACA;AACA;AACA,SAASxD,MAAMA,CAAA,EAAU;EAAA,SAAA4D,KAAA,GAAA7E,SAAA,CAAAC,MAAA,EAAN6E,IAAI,OAAA3E,KAAA,CAAA0E,KAAA,GAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;IAAJD,IAAI,CAAAC,KAAA,IAAA/E,SAAA,CAAA+E,KAAA;EAAA;EACrB,IAAMC,MAAM,GAAGF,IAAI,CAAC3D,GAAG,CAAC,UAACC,CAAC;IAAA,OAAKoD,MAAM,CAACpD,CAAC,CAAC;EAAA,EAAC,CAACG,IAAI,CAAC,EAAE,CAAC;EAClD,OAAOyD,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACH,IAAI,EAAE;EAClC,IAAMzC,IAAI,GAAGyC,IAAI,CAACA,IAAI,CAAC7E,MAAM,GAAG,CAAC,CAAC;EAElC,IAAI,OAAOoC,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACe,WAAW,KAAK7E,MAAM,EAAE;IAC3DuG,IAAI,CAACI,MAAM,CAACJ,IAAI,CAAC7E,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;IAC/B,OAAOoC,IAAI;EACb,CAAC,MAAM;IACL,OAAO,CAAC,CAAC;EACX;AACF;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8C,MAAMA,CAAA,EAAU;EAAA,SAAAC,KAAA,GAAApF,SAAA,CAAAC,MAAA,EAAN6E,IAAI,OAAA3E,KAAA,CAAAiF,KAAA,GAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJP,IAAI,CAAAO,KAAA,IAAArF,SAAA,CAAAqF,KAAA;EAAA;EACrB;EACA,IAAMhD,IAAI,GAAG4C,oBAAoB,CAACH,IAAI,CAAC;EACvC,IAAME,MAAM,GAAG,GAAG,IACb3C,IAAI,CAACiD,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,GAC1BR,IAAI,CAAC3D,GAAG,CAAC,UAACC,CAAC;IAAA,OAAKoD,MAAM,CAACpD,CAAC,CAAC;EAAA,EAAC,CAACG,IAAI,CAAC,GAAG,CAAC,GAAG,GAAG;EAC9C,OAAOyD,MAAM;AACf;;AAEA;AACA;AACA;AACA;AACA,SAASO,gBAAgBA,CAACd,EAAE,EAAE;EAC5B,OAAQ,IAAIe,MAAM,CAACf,EAAE,CAACgB,QAAQ,CAAC,CAAC,GAAG,GAAG,CAAC,CAAEC,IAAI,CAAC,EAAE,CAAC,CAACzF,MAAM,GAAG,CAAC;AAC9D;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASW,UAAUA,CAAC6D,EAAE,EAAEkB,MAAM,EAAE;EAC9B,IAAMC,KAAK,GAAGnB,EAAE,IAAIA,EAAE,CAACiB,IAAI,CAACC,MAAM,CAAC;EACnC,OAAOC,KAAK,IAAIA,KAAK,CAACC,KAAK,KAAK,CAAC;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMC,UAAU,GAAG,gDAAgD;;AAEnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,sBAAsBA,CAACC,OAAO,EAAAC,KAAA,EAAgB;EAAA,IAAZC,QAAQ,GAAAD,KAAA,CAARC,QAAQ;EACjD,IAAIC,WAAW,GAAG,CAAC;EAEnB,OAAOH,OAAO,CAAC7E,GAAG,CAAC,UAACiF,KAAK,EAAK;IAC5BD,WAAW,IAAI,CAAC;IAChB,IAAME,MAAM,GAAGF,WAAW;IAC1B,IAAI1B,EAAE,GAAGD,MAAM,CAAC4B,KAAK,CAAC;IACtB,IAAIE,GAAG,GAAG,EAAE;IAEZ,OAAO7B,EAAE,CAACxE,MAAM,GAAG,CAAC,EAAE;MACpB,IAAM2F,KAAK,GAAGE,UAAU,CAACJ,IAAI,CAACjB,EAAE,CAAC;MACjC,IAAI,CAACmB,KAAK,EAAE;QACVU,GAAG,IAAI7B,EAAE;QACT;MACF;MACA6B,GAAG,IAAI7B,EAAE,CAAC8B,SAAS,CAAC,CAAC,EAAEX,KAAK,CAACC,KAAK,CAAC;MACnCpB,EAAE,GAAGA,EAAE,CAAC8B,SAAS,CAACX,KAAK,CAACC,KAAK,GAAGD,KAAK,CAAC,CAAC,CAAC,CAAC3F,MAAM,CAAC;MAChD,IAAI2F,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;QACpC;QACAU,GAAG,IAAI,IAAI,GAAGE,MAAM,CAACC,MAAM,CAACb,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGS,MAAM,CAAC;MACjD,CAAC,MAAM;QACLC,GAAG,IAAIV,KAAK,CAAC,CAAC,CAAC;QACf,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;UACpBO,WAAW,EAAE;QACf;MACF;IACF;IACA,OAAOG,GAAG;EACZ,CAAC,CAAC,CAACnF,GAAG,CAAC,UAAAsD,EAAE;IAAA,OAAI,IAAIA,EAAE,GAAG;EAAA,EAAC,CAAClD,IAAI,CAAC2E,QAAQ,CAAC;AACxC;;AAEA;AACA;;AAEA;AACA,IAAMQ,gBAAgB,GAAG,MAAM;AAC/B,IAAMC,QAAQ,GAAG,cAAc;AAC/B,IAAMC,mBAAmB,GAAG,eAAe;AAC3C,IAAMC,SAAS,GAAG,mBAAmB;AACrC,IAAMC,WAAW,GAAG,wEAAwE,CAAC,CAAC;AAC9F,IAAMC,gBAAgB,GAAG,cAAc,CAAC,CAAC;AACzC,IAAMC,cAAc,GAAG,8IAA8I;;AAErK;AACA;AACA;AACA,IAAMC,OAAO,GAAG,SAAVA,OAAOA,CAAA,EAAkB;EAAA,IAAd5E,IAAI,GAAArC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAAG,CAAC,CAAC;EACxB,IAAMkH,YAAY,GAAG,WAAW;EAChC,IAAI7E,IAAI,CAAC8E,MAAM,EAAE;IACf9E,IAAI,CAAC+E,KAAK,GAAGnG,MAAM,CACjBiG,YAAY,EACZ,MAAM,EACN7E,IAAI,CAAC8E,MAAM,EACX,MAAM,CAAC;EACX;EACA,OAAOxH,SAAS,CAAC;IACfa,KAAK,EAAE,MAAM;IACb4G,KAAK,EAAEF,YAAY;IACnBG,GAAG,EAAE,GAAG;IACRC,SAAS,EAAE,CAAC;IACZ;IACA,UAAU,EAAE,SAAZC,OAAUA,CAAGC,CAAC,EAAEC,IAAI,EAAK;MACvB,IAAID,CAAC,CAAC3B,KAAK,KAAK,CAAC,EAAE4B,IAAI,CAACjI,WAAW,CAAC,CAAC;IACvC;EACF,CAAC,EAAE6C,IAAI,CAAC;AACV,CAAC;;AAED;AACA,IAAMqF,gBAAgB,GAAG;EACvBN,KAAK,EAAE,cAAc;EAAEE,SAAS,EAAE;AACpC,CAAC;AACD,IAAMK,gBAAgB,GAAG;EACvBnH,KAAK,EAAE,QAAQ;EACf4G,KAAK,EAAE,IAAI;EACXC,GAAG,EAAE,IAAI;EACTO,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,CAACH,gBAAgB;AAC7B,CAAC;AACD,IAAMI,iBAAiB,GAAG;EACxBtH,KAAK,EAAE,QAAQ;EACf4G,KAAK,EAAE,GAAG;EACVC,GAAG,EAAE,GAAG;EACRO,OAAO,EAAE,KAAK;EACdC,QAAQ,EAAE,CAACH,gBAAgB;AAC7B,CAAC;AACD,IAAMK,kBAAkB,GAAG;EACzBX,KAAK,EAAE;AACT,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMY,OAAO,GAAG,SAAVA,OAAOA,CAAYZ,KAAK,EAAEC,GAAG,EAAoB;EAAA,IAAlBY,WAAW,GAAAjI,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAAG,CAAC,CAAC;EACnD,IAAMhB,IAAI,GAAGW,SAAS,CACpB;IACEa,KAAK,EAAE,SAAS;IAChB4G,KAAK;IACLC,GAAG;IACHQ,QAAQ,EAAE;EACZ,CAAC,EACDI,WACF,CAAC;EACDjJ,IAAI,CAAC6I,QAAQ,CAAChF,IAAI,CAAC;IACjBrC,KAAK,EAAE,QAAQ;IACf;IACA;IACA4G,KAAK,EAAE,kDAAkD;IACzDC,GAAG,EAAE,0CAA0C;IAC/Ca,YAAY,EAAE,IAAI;IAClBZ,SAAS,EAAE;EACb,CAAC,CAAC;EACF,IAAMa,YAAY,GAAGhD,MAAM;EACzB;EACA,GAAG,EACH,GAAG,EACH,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI;EACJ;EACA,gCAAgC;EAAE;EAClC,oBAAoB;EAAE;EACtB,mBAAmB,CAAC;EACtB,CAAC;EACD;EACAnG,IAAI,CAAC6I,QAAQ,CAAChF,IAAI,CAChB;IACE;IACA;IACA;IACA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEAuE,KAAK,EAAEnG,MAAM,CACX,MAAM;IAAE;IACR,GAAG,EACHkH,YAAY,EACZ,sBAAsB,EACtB,MAAM,CAAC,CAAC;EACZ,CACF,CAAC;EACD,OAAOnJ,IAAI;AACb,CAAC;AACD,IAAMoJ,mBAAmB,GAAGJ,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;AAC9C,IAAMK,oBAAoB,GAAGL,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC;AACpD,IAAMM,iBAAiB,GAAGN,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;AAC3C,IAAMO,WAAW,GAAG;EAClB/H,KAAK,EAAE,QAAQ;EACf4G,KAAK,EAAEP,SAAS;EAChBS,SAAS,EAAE;AACb,CAAC;AACD,IAAMkB,aAAa,GAAG;EACpBhI,KAAK,EAAE,QAAQ;EACf4G,KAAK,EAAEN,WAAW;EAClBQ,SAAS,EAAE;AACb,CAAC;AACD,IAAMmB,kBAAkB,GAAG;EACzBjI,KAAK,EAAE,QAAQ;EACf4G,KAAK,EAAEL,gBAAgB;EACvBO,SAAS,EAAE;AACb,CAAC;AACD,IAAMoB,WAAW,GAAG;EAClBlI,KAAK,EAAE,QAAQ;EACf4G,KAAK,EAAE,iBAAiB;EACxBC,GAAG,EAAE,YAAY;EACjBQ,QAAQ,EAAE,CACRH,gBAAgB,EAChB;IACEN,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,IAAI;IACTC,SAAS,EAAE,CAAC;IACZO,QAAQ,EAAE,CAACH,gBAAgB;EAC7B,CAAC;AAEL,CAAC;AACD,IAAMiB,UAAU,GAAG;EACjBnI,KAAK,EAAE,OAAO;EACd4G,KAAK,EAAET,QAAQ;EACfW,SAAS,EAAE;AACb,CAAC;AACD,IAAMsB,qBAAqB,GAAG;EAC5BpI,KAAK,EAAE,OAAO;EACd4G,KAAK,EAAER,mBAAmB;EAC1BU,SAAS,EAAE;AACb,CAAC;AACD,IAAMuB,YAAY,GAAG;EACnB;EACAzB,KAAK,EAAE,SAAS,GAAGR,mBAAmB;EACtCU,SAAS,EAAE;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAMwB,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAY9J,IAAI,EAAE;EACvC,OAAOT,MAAM,CAACgE,MAAM,CAACvD,IAAI,EACvB;IACE;IACA,UAAU,EAAE,SAAZuI,OAAUA,CAAGC,CAAC,EAAEC,IAAI,EAAK;MAAEA,IAAI,CAACvI,IAAI,CAAC6J,WAAW,GAAGvB,CAAC,CAAC,CAAC,CAAC;IAAE,CAAC;IAC1D;IACA,QAAQ,EAAE,SAAVwB,KAAQA,CAAGxB,CAAC,EAAEC,IAAI,EAAK;MAAE,IAAIA,IAAI,CAACvI,IAAI,CAAC6J,WAAW,KAAKvB,CAAC,CAAC,CAAC,CAAC,EAAEC,IAAI,CAACjI,WAAW,CAAC,CAAC;IAAE;EACnF,CAAC,CAAC;AACN,CAAC;AAED,IAAIyJ,KAAK,GAAG,aAAa1K,MAAM,CAACC,MAAM,CAAC;EACrC0K,SAAS,EAAE,IAAI;EACfvB,gBAAgB,EAAEA,gBAAgB;EAClCD,gBAAgB,EAAEA,gBAAgB;EAClCe,kBAAkB,EAAEA,kBAAkB;EACtC1B,gBAAgB,EAAEA,gBAAgB;EAClCiB,OAAO,EAAEA,OAAO;EAChBK,oBAAoB,EAAEA,oBAAoB;EAC1CD,mBAAmB,EAAEA,mBAAmB;EACxCI,aAAa,EAAEA,aAAa;EAC5B1B,WAAW,EAAEA,WAAW;EACxBgC,iBAAiB,EAAEA,iBAAiB;EACpCR,iBAAiB,EAAEA,iBAAiB;EACpC3B,QAAQ,EAAEA,QAAQ;EAClBD,gBAAgB,EAAEA,gBAAgB;EAClCmC,YAAY,EAAEA,YAAY;EAC1BN,WAAW,EAAEA,WAAW;EACxB1B,SAAS,EAAEA,SAAS;EACpBkB,kBAAkB,EAAEA,kBAAkB;EACtCD,iBAAiB,EAAEA,iBAAiB;EACpCY,WAAW,EAAEA,WAAW;EACxB1B,cAAc,EAAEA,cAAc;EAC9BC,OAAO,EAAEA,OAAO;EAChB0B,UAAU,EAAEA,UAAU;EACtB/B,mBAAmB,EAAEA,mBAAmB;EACxCgC,qBAAqB,EAAEA;AACzB,CAAC,CAAC;;AAEF;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,qBAAqBA,CAACvD,KAAK,EAAEwD,QAAQ,EAAE;EAC9C,IAAMC,MAAM,GAAGzD,KAAK,CAAC0D,KAAK,CAAC1D,KAAK,CAACC,KAAK,GAAG,CAAC,CAAC;EAC3C,IAAIwD,MAAM,KAAK,GAAG,EAAE;IAClBD,QAAQ,CAAC5J,WAAW,CAAC,CAAC;EACxB;AACF;;AAEA;AACA;AACA;AACA;AACA,SAAS+J,cAAcA,CAACvK,IAAI,EAAEwK,OAAO,EAAE;EACrC;EACA,IAAIxK,IAAI,CAACiD,SAAS,KAAK9C,SAAS,EAAE;IAChCH,IAAI,CAACwB,KAAK,GAAGxB,IAAI,CAACiD,SAAS;IAC3B,OAAOjD,IAAI,CAACiD,SAAS;EACvB;AACF;;AAEA;AACA;AACA;AACA;AACA,SAASwH,aAAaA,CAACzK,IAAI,EAAE0K,MAAM,EAAE;EACnC,IAAI,CAACA,MAAM,EAAE;EACb,IAAI,CAAC1K,IAAI,CAACyK,aAAa,EAAE;;EAEzB;EACA;EACA;EACA;EACA;EACAzK,IAAI,CAACoI,KAAK,GAAG,MAAM,GAAGpI,IAAI,CAACyK,aAAa,CAAC1I,KAAK,CAAC,GAAG,CAAC,CAACQ,IAAI,CAAC,GAAG,CAAC,GAAG,qBAAqB;EACrFvC,IAAI,CAAC2K,aAAa,GAAGR,qBAAqB;EAC1CnK,IAAI,CAAC4K,QAAQ,GAAG5K,IAAI,CAAC4K,QAAQ,IAAI5K,IAAI,CAACyK,aAAa;EACnD,OAAOzK,IAAI,CAACyK,aAAa;;EAEzB;EACA;EACA;EACA,IAAIzK,IAAI,CAACsI,SAAS,KAAKnI,SAAS,EAAEH,IAAI,CAACsI,SAAS,GAAG,CAAC;AACtD;;AAEA;AACA;AACA;AACA;AACA,SAASuC,cAAcA,CAAC7K,IAAI,EAAEwK,OAAO,EAAE;EACrC,IAAI,CAACrJ,KAAK,CAAC2J,OAAO,CAAC9K,IAAI,CAAC4I,OAAO,CAAC,EAAE;EAElC5I,IAAI,CAAC4I,OAAO,GAAGzC,MAAM,CAAA4E,KAAA,SAAA7I,kBAAA,CAAIlC,IAAI,CAAC4I,OAAO,EAAC;AACxC;;AAEA;AACA;AACA;AACA;AACA,SAASoC,YAAYA,CAAChL,IAAI,EAAEwK,OAAO,EAAE;EACnC,IAAI,CAACxK,IAAI,CAAC4G,KAAK,EAAE;EACjB,IAAI5G,IAAI,CAACoI,KAAK,IAAIpI,IAAI,CAACqI,GAAG,EAAE,MAAM,IAAIjJ,KAAK,CAAC,0CAA0C,CAAC;EAEvFY,IAAI,CAACoI,KAAK,GAAGpI,IAAI,CAAC4G,KAAK;EACvB,OAAO5G,IAAI,CAAC4G,KAAK;AACnB;;AAEA;AACA;AACA;AACA;AACA,SAASqE,gBAAgBA,CAACjL,IAAI,EAAEwK,OAAO,EAAE;EACvC;EACA,IAAIxK,IAAI,CAACsI,SAAS,KAAKnI,SAAS,EAAEH,IAAI,CAACsI,SAAS,GAAG,CAAC;AACtD;;AAEA;AACA;AACA,IAAM4C,cAAc,GAAG,SAAjBA,cAAcA,CAAIlL,IAAI,EAAE0K,MAAM,EAAK;EACvC,IAAI,CAAC1K,IAAI,CAACmL,WAAW,EAAE;EACvB;EACA;EACA,IAAInL,IAAI,CAACoL,MAAM,EAAE,MAAM,IAAIhM,KAAK,CAAC,wCAAwC,CAAC;EAE1E,IAAMiM,YAAY,GAAG9L,MAAM,CAACgE,MAAM,CAAC,CAAC,CAAC,EAAEvD,IAAI,CAAC;EAC5CT,MAAM,CAAC+L,IAAI,CAACtL,IAAI,CAAC,CAACN,OAAO,CAAC,UAACY,GAAG,EAAK;IAAE,OAAON,IAAI,CAACM,GAAG,CAAC;EAAE,CAAC,CAAC;EAEzDN,IAAI,CAAC4K,QAAQ,GAAGS,YAAY,CAACT,QAAQ;EACrC5K,IAAI,CAACoI,KAAK,GAAGnG,MAAM,CAACoJ,YAAY,CAACF,WAAW,EAAEzF,SAAS,CAAC2F,YAAY,CAACjD,KAAK,CAAC,CAAC;EAC5EpI,IAAI,CAACoL,MAAM,GAAG;IACZ9C,SAAS,EAAE,CAAC;IACZO,QAAQ,EAAE,CACRtJ,MAAM,CAACgE,MAAM,CAAC8H,YAAY,EAAE;MAAEE,UAAU,EAAE;IAAK,CAAC,CAAC;EAErD,CAAC;EACDvL,IAAI,CAACsI,SAAS,GAAG,CAAC;EAElB,OAAO+C,YAAY,CAACF,WAAW;AACjC,CAAC;;AAED;AACA,IAAMK,eAAe,GAAG,CACtB,IAAI,EACJ,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,QAAQ;AAAE;AACV,MAAM;AAAE;AACR,OAAO,CAAC;AAAA,CACT;AAED,IAAMC,qBAAqB,GAAG,SAAS;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACC,WAAW,EAAEC,eAAe,EAAqC;EAAA,IAAnCC,SAAS,GAAA7K,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAb,SAAA,GAAAa,SAAA,MAAGyK,qBAAqB;EACtF;EACA,IAAMK,gBAAgB,GAAGvM,MAAM,CAACuB,MAAM,CAAC,IAAI,CAAC;;EAE5C;EACA;EACA,IAAI,OAAO6K,WAAW,KAAK,QAAQ,EAAE;IACnCI,WAAW,CAACF,SAAS,EAAEF,WAAW,CAAC5J,KAAK,CAAC,GAAG,CAAC,CAAC;EAChD,CAAC,MAAM,IAAIZ,KAAK,CAAC2J,OAAO,CAACa,WAAW,CAAC,EAAE;IACrCI,WAAW,CAACF,SAAS,EAAEF,WAAW,CAAC;EACrC,CAAC,MAAM;IACLpM,MAAM,CAAC+L,IAAI,CAACK,WAAW,CAAC,CAACjM,OAAO,CAAC,UAASmM,SAAS,EAAE;MACnD;MACAtM,MAAM,CAACgE,MAAM,CACXuI,gBAAgB,EAChBJ,eAAe,CAACC,WAAW,CAACE,SAAS,CAAC,EAAED,eAAe,EAAEC,SAAS,CACpE,CAAC;IACH,CAAC,CAAC;EACJ;EACA,OAAOC,gBAAgB;;EAEvB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,WAAWA,CAACF,SAAS,EAAEG,WAAW,EAAE;IAC3C,IAAIJ,eAAe,EAAE;MACnBI,WAAW,GAAGA,WAAW,CAAC7J,GAAG,CAAC,UAAAC,CAAC;QAAA,OAAIA,CAAC,CAAC6J,WAAW,CAAC,CAAC;MAAA,EAAC;IACrD;IACAD,WAAW,CAACtM,OAAO,CAAC,UAASwM,OAAO,EAAE;MACpC,IAAMC,IAAI,GAAGD,OAAO,CAACnK,KAAK,CAAC,GAAG,CAAC;MAC/B+J,gBAAgB,CAACK,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAACN,SAAS,EAAEO,eAAe,CAACD,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5E,CAAC,CAAC;EACJ;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,eAAeA,CAACF,OAAO,EAAEG,aAAa,EAAE;EAC/C;EACA;EACA,IAAIA,aAAa,EAAE;IACjB,OAAO5E,MAAM,CAAC4E,aAAa,CAAC;EAC9B;EAEA,OAAOC,aAAa,CAACJ,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC;AACvC;;AAEA;AACA;AACA;AACA;AACA,SAASI,aAAaA,CAACJ,OAAO,EAAE;EAC9B,OAAOV,eAAe,CAAC3J,QAAQ,CAACqK,OAAO,CAACD,WAAW,CAAC,CAAC,CAAC;AACxD;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAMM,gBAAgB,GAAG,CAAC,CAAC;;AAE3B;AACA;AACA;AACA,IAAMC,KAAK,GAAG,SAARA,KAAKA,CAAIC,OAAO,EAAK;EACzBC,OAAO,CAACF,KAAK,CAACC,OAAO,CAAC;AACxB,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAME,IAAI,GAAG,SAAPA,IAAIA,CAAIF,OAAO,EAAc;EAAA,IAAAG,QAAA;EAAA,SAAAC,KAAA,GAAA7L,SAAA,CAAAC,MAAA,EAAT6E,IAAI,OAAA3E,KAAA,CAAA0L,KAAA,OAAAA,KAAA,WAAAC,KAAA,MAAAA,KAAA,GAAAD,KAAA,EAAAC,KAAA;IAAJhH,IAAI,CAAAgH,KAAA,QAAA9L,SAAA,CAAA8L,KAAA;EAAA;EAC5B,CAAAF,QAAA,GAAAF,OAAO,EAACK,GAAG,CAAAhC,KAAA,CAAA6B,QAAA,GAAC,SAASH,OAAO,EAAE,EAAAxK,MAAA,CAAK6D,IAAI,EAAC;AAC1C,CAAC;;AAED;AACA;AACA;AACA;AACA,IAAMkH,UAAU,GAAG,SAAbA,UAAUA,CAAIC,OAAO,EAAER,OAAO,EAAK;EACvC,IAAIF,gBAAgB,CAAC,GAAGU,OAAO,IAAIR,OAAO,EAAE,CAAC,EAAE;EAE/CC,OAAO,CAACK,GAAG,CAAC,oBAAoBE,OAAO,KAAKR,OAAO,EAAE,CAAC;EACtDF,gBAAgB,CAAC,GAAGU,OAAO,IAAIR,OAAO,EAAE,CAAC,GAAG,IAAI;AAClD,CAAC;;AAED;;AAEA;AACA;AACA;;AAEA,IAAMS,eAAe,GAAG,IAAI9N,KAAK,CAAC,CAAC;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+N,eAAeA,CAACnN,IAAI,EAAEoN,OAAO,EAAAC,KAAA,EAAW;EAAA,IAAP/M,GAAG,GAAA+M,KAAA,CAAH/M,GAAG;EAC3C,IAAI+G,MAAM,GAAG,CAAC;EACd,IAAMiG,UAAU,GAAGtN,IAAI,CAACM,GAAG,CAAC;EAC5B;EACA,IAAMiN,IAAI,GAAG,CAAC,CAAC;EACf;EACA,IAAMC,SAAS,GAAG,CAAC,CAAC;EAEpB,KAAK,IAAInL,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI+K,OAAO,CAACnM,MAAM,EAAEoB,CAAC,EAAE,EAAE;IACxCmL,SAAS,CAACnL,CAAC,GAAGgF,MAAM,CAAC,GAAGiG,UAAU,CAACjL,CAAC,CAAC;IACrCkL,IAAI,CAAClL,CAAC,GAAGgF,MAAM,CAAC,GAAG,IAAI;IACvBA,MAAM,IAAId,gBAAgB,CAAC6G,OAAO,CAAC/K,CAAC,GAAG,CAAC,CAAC,CAAC;EAC5C;EACA;EACA;EACArC,IAAI,CAACM,GAAG,CAAC,GAAGkN,SAAS;EACrBxN,IAAI,CAACM,GAAG,CAAC,CAACmN,KAAK,GAAGF,IAAI;EACtBvN,IAAI,CAACM,GAAG,CAAC,CAACoN,MAAM,GAAG,IAAI;AACzB;;AAEA;AACA;AACA;AACA,SAASC,eAAeA,CAAC3N,IAAI,EAAE;EAC7B,IAAI,CAACmB,KAAK,CAAC2J,OAAO,CAAC9K,IAAI,CAACoI,KAAK,CAAC,EAAE;EAEhC,IAAIpI,IAAI,CAAC4N,IAAI,IAAI5N,IAAI,CAACkJ,YAAY,IAAIlJ,IAAI,CAAC6N,WAAW,EAAE;IACtDrB,KAAK,CAAC,oEAAoE,CAAC;IAC3E,MAAMU,eAAe;EACvB;EAEA,IAAI,OAAOlN,IAAI,CAAC8N,UAAU,KAAK,QAAQ,IAAI9N,IAAI,CAAC8N,UAAU,KAAK,IAAI,EAAE;IACnEtB,KAAK,CAAC,2BAA2B,CAAC;IAClC,MAAMU,eAAe;EACvB;EAEAC,eAAe,CAACnN,IAAI,EAAEA,IAAI,CAACoI,KAAK,EAAE;IAAE9H,GAAG,EAAE;EAAa,CAAC,CAAC;EACxDN,IAAI,CAACoI,KAAK,GAAGrB,sBAAsB,CAAC/G,IAAI,CAACoI,KAAK,EAAE;IAAElB,QAAQ,EAAE;EAAG,CAAC,CAAC;AACnE;;AAEA;AACA;AACA;AACA,SAAS6G,aAAaA,CAAC/N,IAAI,EAAE;EAC3B,IAAI,CAACmB,KAAK,CAAC2J,OAAO,CAAC9K,IAAI,CAACqI,GAAG,CAAC,EAAE;EAE9B,IAAIrI,IAAI,CAAC4N,IAAI,IAAI5N,IAAI,CAACgO,UAAU,IAAIhO,IAAI,CAACiO,SAAS,EAAE;IAClDzB,KAAK,CAAC,8DAA8D,CAAC;IACrE,MAAMU,eAAe;EACvB;EAEA,IAAI,OAAOlN,IAAI,CAACiF,QAAQ,KAAK,QAAQ,IAAIjF,IAAI,CAACiF,QAAQ,KAAK,IAAI,EAAE;IAC/DuH,KAAK,CAAC,yBAAyB,CAAC;IAChC,MAAMU,eAAe;EACvB;EAEAC,eAAe,CAACnN,IAAI,EAAEA,IAAI,CAACqI,GAAG,EAAE;IAAE/H,GAAG,EAAE;EAAW,CAAC,CAAC;EACpDN,IAAI,CAACqI,GAAG,GAAGtB,sBAAsB,CAAC/G,IAAI,CAACqI,GAAG,EAAE;IAAEnB,QAAQ,EAAE;EAAG,CAAC,CAAC;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASgH,UAAUA,CAAClO,IAAI,EAAE;EACxB,IAAIA,IAAI,CAACwB,KAAK,IAAI,OAAOxB,IAAI,CAACwB,KAAK,KAAK,QAAQ,IAAIxB,IAAI,CAACwB,KAAK,KAAK,IAAI,EAAE;IACvExB,IAAI,CAAC8N,UAAU,GAAG9N,IAAI,CAACwB,KAAK;IAC5B,OAAOxB,IAAI,CAACwB,KAAK;EACnB;AACF;;AAEA;AACA;AACA;AACA,SAAS2M,UAAUA,CAACnO,IAAI,EAAE;EACxBkO,UAAU,CAAClO,IAAI,CAAC;EAEhB,IAAI,OAAOA,IAAI,CAAC8N,UAAU,KAAK,QAAQ,EAAE;IACvC9N,IAAI,CAAC8N,UAAU,GAAG;MAAEM,KAAK,EAAEpO,IAAI,CAAC8N;IAAW,CAAC;EAC9C;EACA,IAAI,OAAO9N,IAAI,CAACiF,QAAQ,KAAK,QAAQ,EAAE;IACrCjF,IAAI,CAACiF,QAAQ,GAAG;MAAEmJ,KAAK,EAAEpO,IAAI,CAACiF;IAAS,CAAC;EAC1C;EAEA0I,eAAe,CAAC3N,IAAI,CAAC;EACrB+N,aAAa,CAAC/N,IAAI,CAAC;AACrB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASqO,eAAeA,CAACC,QAAQ,EAAE;EACjC;AACF;AACA;AACA;AACA;AACA;EACE,SAASC,MAAMA,CAAChO,KAAK,EAAEiO,MAAM,EAAE;IAC7B,OAAO,IAAIhI,MAAM,CACfhB,MAAM,CAACjF,KAAK,CAAC,EACb,GAAG,IACA+N,QAAQ,CAACG,gBAAgB,GAAG,GAAG,GAAG,EAAE,CAAC,IACrCH,QAAQ,CAACI,YAAY,GAAG,GAAG,GAAG,EAAE,CAAC,IACjCF,MAAM,GAAG,GAAG,GAAG,EAAE,CACtB,CAAC;EACH;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAXE,IAaMG,UAAU;IAAA;;IACd,SAAAA,WAAA,EAAc;MAAA1O,eAAA,OAAA0O,UAAA;MACZ,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;MACtB;MACA,IAAI,CAACxB,OAAO,GAAG,EAAE;MACjB,IAAI,CAACyB,OAAO,GAAG,CAAC;MAChB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACnB;;IAEA;IAAA,OAAAzO,YAAA,CAAAsO,UAAA;MAAArO,GAAA;MAAAC,KAAA,EACA,SAAAwO,OAAOA,CAACtJ,EAAE,EAAEpC,IAAI,EAAE;QAChBA,IAAI,CAACyL,QAAQ,GAAG,IAAI,CAACA,QAAQ,EAAE;QAC/B;QACA,IAAI,CAACF,YAAY,CAAC,IAAI,CAACC,OAAO,CAAC,GAAGxL,IAAI;QACtC,IAAI,CAAC+J,OAAO,CAACvJ,IAAI,CAAC,CAACR,IAAI,EAAEoC,EAAE,CAAC,CAAC;QAC7B,IAAI,CAACoJ,OAAO,IAAItI,gBAAgB,CAACd,EAAE,CAAC,GAAG,CAAC;MAC1C;IAAC;MAAAnF,GAAA;MAAAC,KAAA,EAED,SAAAyO,OAAOA,CAAA,EAAG;QACR,IAAI,IAAI,CAAC5B,OAAO,CAACnM,MAAM,KAAK,CAAC,EAAE;UAC7B;UACA;UACA,IAAI,CAACyF,IAAI,GAAG;YAAA,OAAM,IAAI;UAAA;QACxB;QACA,IAAMuI,WAAW,GAAG,IAAI,CAAC7B,OAAO,CAACjL,GAAG,CAAC,UAAAuC,EAAE;UAAA,OAAIA,EAAE,CAAC,CAAC,CAAC;QAAA,EAAC;QACjD,IAAI,CAACwK,SAAS,GAAGX,MAAM,CAACxH,sBAAsB,CAACkI,WAAW,EAAE;UAAE/H,QAAQ,EAAE;QAAI,CAAC,CAAC,EAAE,IAAI,CAAC;QACrF,IAAI,CAACiI,SAAS,GAAG,CAAC;MACpB;;MAEA;IAAA;MAAA7O,GAAA;MAAAC,KAAA,EACA,SAAAmG,IAAIA,CAAC0I,CAAC,EAAE;QACN,IAAI,CAACF,SAAS,CAACC,SAAS,GAAG,IAAI,CAACA,SAAS;QACzC,IAAMvI,KAAK,GAAG,IAAI,CAACsI,SAAS,CAACxI,IAAI,CAAC0I,CAAC,CAAC;QACpC,IAAI,CAACxI,KAAK,EAAE;UAAE,OAAO,IAAI;QAAE;;QAE3B;QACA,IAAMvE,CAAC,GAAGuE,KAAK,CAACyI,SAAS,CAAC,UAAC3K,EAAE,EAAErC,CAAC;UAAA,OAAKA,CAAC,GAAG,CAAC,IAAIqC,EAAE,KAAKvE,SAAS;QAAA,EAAC;QAC/D;QACA,IAAMmP,SAAS,GAAG,IAAI,CAACV,YAAY,CAACvM,CAAC,CAAC;QACtC;QACA;QACAuE,KAAK,CAACV,MAAM,CAAC,CAAC,EAAE7D,CAAC,CAAC;QAElB,OAAO9C,MAAM,CAACgE,MAAM,CAACqD,KAAK,EAAE0I,SAAS,CAAC;MACxC;IAAC;EAAA;EAGH;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAtBE,IA+BMC,mBAAmB;IAAA;;IACvB,SAAAA,oBAAA,EAAc;MAAAtP,eAAA,OAAAsP,mBAAA;MACZ;MACA,IAAI,CAACC,KAAK,GAAG,EAAE;MACf;MACA,IAAI,CAACC,YAAY,GAAG,EAAE;MACtB,IAAI,CAACC,KAAK,GAAG,CAAC;MAEd,IAAI,CAACP,SAAS,GAAG,CAAC;MAClB,IAAI,CAACQ,UAAU,GAAG,CAAC;IACrB;;IAEA;IAAA,OAAAtP,YAAA,CAAAkP,mBAAA;MAAAjP,GAAA;MAAAC,KAAA,EACA,SAAAqP,UAAUA,CAAC/I,KAAK,EAAE;QAChB,IAAI,IAAI,CAAC4I,YAAY,CAAC5I,KAAK,CAAC,EAAE,OAAO,IAAI,CAAC4I,YAAY,CAAC5I,KAAK,CAAC;QAE7D,IAAMgJ,OAAO,GAAG,IAAIlB,UAAU,CAAC,CAAC;QAChC,IAAI,CAACa,KAAK,CAACM,KAAK,CAACjJ,KAAK,CAAC,CAACnH,OAAO,CAAC,UAAAqQ,KAAA;UAAA,IAAAC,KAAA,GAAAC,cAAA,CAAAF,KAAA;YAAEtK,EAAE,GAAAuK,KAAA;YAAE3M,IAAI,GAAA2M,KAAA;UAAA,OAAMH,OAAO,CAACd,OAAO,CAACtJ,EAAE,EAAEpC,IAAI,CAAC;QAAA,EAAC;QAC1EwM,OAAO,CAACb,OAAO,CAAC,CAAC;QACjB,IAAI,CAACS,YAAY,CAAC5I,KAAK,CAAC,GAAGgJ,OAAO;QAClC,OAAOA,OAAO;MAChB;IAAC;MAAAvP,GAAA;MAAAC,KAAA,EAED,SAAA2P,0BAA0BA,CAAA,EAAG;QAC3B,OAAO,IAAI,CAACP,UAAU,KAAK,CAAC;MAC9B;IAAC;MAAArP,GAAA;MAAAC,KAAA,EAED,SAAA4P,WAAWA,CAAA,EAAG;QACZ,IAAI,CAACR,UAAU,GAAG,CAAC;MACrB;;MAEA;IAAA;MAAArP,GAAA;MAAAC,KAAA,EACA,SAAAwO,OAAOA,CAACtJ,EAAE,EAAEpC,IAAI,EAAE;QAChB,IAAI,CAACmM,KAAK,CAAC3L,IAAI,CAAC,CAAC4B,EAAE,EAAEpC,IAAI,CAAC,CAAC;QAC3B,IAAIA,IAAI,CAACxD,IAAI,KAAK,OAAO,EAAE,IAAI,CAAC6P,KAAK,EAAE;MACzC;;MAEA;IAAA;MAAApP,GAAA;MAAAC,KAAA,EACA,SAAAmG,IAAIA,CAAC0I,CAAC,EAAE;QACN,IAAM5G,CAAC,GAAG,IAAI,CAACoH,UAAU,CAAC,IAAI,CAACD,UAAU,CAAC;QAC1CnH,CAAC,CAAC2G,SAAS,GAAG,IAAI,CAACA,SAAS;QAC5B,IAAItO,MAAM,GAAG2H,CAAC,CAAC9B,IAAI,CAAC0I,CAAC,CAAC;;QAEtB;QACA;QACA;QACA;;QAEA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;;QAEA;QACA;;QAEA;QACA;QACA;QACA;;QAEA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAI,IAAI,CAACc,0BAA0B,CAAC,CAAC,EAAE;UACrC,IAAIrP,MAAM,IAAIA,MAAM,CAACgG,KAAK,KAAK,IAAI,CAACsI,SAAS,EAAE,CAAC,KAAM;YAAE;YACtD,IAAMiB,EAAE,GAAG,IAAI,CAACR,UAAU,CAAC,CAAC,CAAC;YAC7BQ,EAAE,CAACjB,SAAS,GAAG,IAAI,CAACA,SAAS,GAAG,CAAC;YACjCtO,MAAM,GAAGuP,EAAE,CAAC1J,IAAI,CAAC0I,CAAC,CAAC;UACrB;QACF;QAEA,IAAIvO,MAAM,EAAE;UACV,IAAI,CAAC8O,UAAU,IAAI9O,MAAM,CAACiO,QAAQ,GAAG,CAAC;UACtC,IAAI,IAAI,CAACa,UAAU,KAAK,IAAI,CAACD,KAAK,EAAE;YAClC;YACA,IAAI,CAACS,WAAW,CAAC,CAAC;UACpB;QACF;QAEA,OAAOtP,MAAM;MACf;IAAC;EAAA;EAGH;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASwP,cAAcA,CAACrQ,IAAI,EAAE;IAC5B,IAAMsQ,EAAE,GAAG,IAAIf,mBAAmB,CAAC,CAAC;IAEpCvP,IAAI,CAAC6I,QAAQ,CAACnJ,OAAO,CAAC,UAAA6Q,IAAI;MAAA,OAAID,EAAE,CAACvB,OAAO,CAACwB,IAAI,CAACnI,KAAK,EAAE;QAAEoI,IAAI,EAAED,IAAI;QAAE1Q,IAAI,EAAE;MAAQ,CAAC,CAAC;IAAA,EAAC;IAEpF,IAAIG,IAAI,CAACyQ,aAAa,EAAE;MACtBH,EAAE,CAACvB,OAAO,CAAC/O,IAAI,CAACyQ,aAAa,EAAE;QAAE5Q,IAAI,EAAE;MAAM,CAAC,CAAC;IACjD;IACA,IAAIG,IAAI,CAAC4I,OAAO,EAAE;MAChB0H,EAAE,CAACvB,OAAO,CAAC/O,IAAI,CAAC4I,OAAO,EAAE;QAAE/I,IAAI,EAAE;MAAU,CAAC,CAAC;IAC/C;IAEA,OAAOyQ,EAAE;EACX;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;EAEE;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASI,WAAWA,CAAC1Q,IAAI,EAAE0K,MAAM,EAAE;IAAA,IAAAiG,KAAA;IACjC,IAAMC,KAAK,GAAG,yBAA2B5Q,IAAK;IAC9C,IAAIA,IAAI,CAAC6Q,UAAU,EAAE,OAAOD,KAAK;IAEjC,CACErG,cAAc;IACd;IACA;IACAS,YAAY,EACZmD,UAAU,EACVjD,cAAc,CACf,CAACxL,OAAO,CAAC,UAAAoR,GAAG;MAAA,OAAIA,GAAG,CAAC9Q,IAAI,EAAE0K,MAAM,CAAC;IAAA,EAAC;IAEnC4D,QAAQ,CAACyC,kBAAkB,CAACrR,OAAO,CAAC,UAAAoR,GAAG;MAAA,OAAIA,GAAG,CAAC9Q,IAAI,EAAE0K,MAAM,CAAC;IAAA,EAAC;;IAE7D;IACA1K,IAAI,CAAC2K,aAAa,GAAG,IAAI;IAEzB,CACEF,aAAa;IACb;IACA;IACAI,cAAc;IACd;IACAI,gBAAgB,CACjB,CAACvL,OAAO,CAAC,UAAAoR,GAAG;MAAA,OAAIA,GAAG,CAAC9Q,IAAI,EAAE0K,MAAM,CAAC;IAAA,EAAC;IAEnC1K,IAAI,CAAC6Q,UAAU,GAAG,IAAI;IAEtB,IAAIG,cAAc,GAAG,IAAI;IACzB,IAAI,OAAOhR,IAAI,CAAC4K,QAAQ,KAAK,QAAQ,IAAI5K,IAAI,CAAC4K,QAAQ,CAACqG,QAAQ,EAAE;MAC/D;MACA;MACA;MACAjR,IAAI,CAAC4K,QAAQ,GAAGrL,MAAM,CAACgE,MAAM,CAAC,CAAC,CAAC,EAAEvD,IAAI,CAAC4K,QAAQ,CAAC;MAChDoG,cAAc,GAAGhR,IAAI,CAAC4K,QAAQ,CAACqG,QAAQ;MACvC,OAAOjR,IAAI,CAAC4K,QAAQ,CAACqG,QAAQ;IAC/B;IACAD,cAAc,GAAGA,cAAc,IAAI,KAAK;IAExC,IAAIhR,IAAI,CAAC4K,QAAQ,EAAE;MACjB5K,IAAI,CAAC4K,QAAQ,GAAGc,eAAe,CAAC1L,IAAI,CAAC4K,QAAQ,EAAE0D,QAAQ,CAACG,gBAAgB,CAAC;IAC3E;IAEAmC,KAAK,CAACM,gBAAgB,GAAG3C,MAAM,CAACyC,cAAc,EAAE,IAAI,CAAC;IAErD,IAAItG,MAAM,EAAE;MACV,IAAI,CAAC1K,IAAI,CAACoI,KAAK,EAAEpI,IAAI,CAACoI,KAAK,GAAG,OAAO;MACrCwI,KAAK,CAACO,OAAO,GAAG5C,MAAM,CAACqC,KAAK,CAACxI,KAAK,CAAC;MACnC,IAAI,CAACpI,IAAI,CAACqI,GAAG,IAAI,CAACrI,IAAI,CAACoR,cAAc,EAAEpR,IAAI,CAACqI,GAAG,GAAG,OAAO;MACzD,IAAIrI,IAAI,CAACqI,GAAG,EAAEuI,KAAK,CAACS,KAAK,GAAG9C,MAAM,CAACqC,KAAK,CAACvI,GAAG,CAAC;MAC7CuI,KAAK,CAACH,aAAa,GAAGjL,MAAM,CAACoL,KAAK,CAACvI,GAAG,CAAC,IAAI,EAAE;MAC7C,IAAIrI,IAAI,CAACoR,cAAc,IAAI1G,MAAM,CAAC+F,aAAa,EAAE;QAC/CG,KAAK,CAACH,aAAa,IAAI,CAACzQ,IAAI,CAACqI,GAAG,GAAG,GAAG,GAAG,EAAE,IAAIqC,MAAM,CAAC+F,aAAa;MACrE;IACF;IACA,IAAIzQ,IAAI,CAAC4I,OAAO,EAAEgI,KAAK,CAACU,SAAS,GAAG/C,MAAM,CAAC,8BAAgCvO,IAAI,CAAC4I,OAAQ,CAAC;IACzF,IAAI,CAAC5I,IAAI,CAAC6I,QAAQ,EAAE7I,IAAI,CAAC6I,QAAQ,GAAG,EAAE;IAEtC7I,IAAI,CAAC6I,QAAQ,GAAG,CAAA8H,KAAA,KAAE,EAAC1O,MAAM,CAAA8I,KAAA,CAAA4F,KAAA,EAAAzO,kBAAA,CAAIlC,IAAI,CAAC6I,QAAQ,CAAC1G,GAAG,CAAC,UAASoP,CAAC,EAAE;MACzD,OAAOC,iBAAiB,CAACD,CAAC,KAAK,MAAM,GAAGvR,IAAI,GAAGuR,CAAC,CAAC;IACnD,CAAC,CAAC,EAAC;IACHvR,IAAI,CAAC6I,QAAQ,CAACnJ,OAAO,CAAC,UAAS6R,CAAC,EAAE;MAAEb,WAAW,CAAC,iBAAmBa,CAAC,EAAGX,KAAK,CAAC;IAAE,CAAC,CAAC;IAEjF,IAAI5Q,IAAI,CAACoL,MAAM,EAAE;MACfsF,WAAW,CAAC1Q,IAAI,CAACoL,MAAM,EAAEV,MAAM,CAAC;IAClC;IAEAkG,KAAK,CAACf,OAAO,GAAGQ,cAAc,CAACO,KAAK,CAAC;IACrC,OAAOA,KAAK;EACd;EAEA,IAAI,CAACtC,QAAQ,CAACyC,kBAAkB,EAAEzC,QAAQ,CAACyC,kBAAkB,GAAG,EAAE;;EAElE;EACA,IAAIzC,QAAQ,CAACzF,QAAQ,IAAIyF,QAAQ,CAACzF,QAAQ,CAAChH,QAAQ,CAAC,MAAM,CAAC,EAAE;IAC3D,MAAM,IAAIzC,KAAK,CAAC,2FAA2F,CAAC;EAC9G;;EAEA;EACAkP,QAAQ,CAACmD,gBAAgB,GAAG9Q,SAAS,CAAC2N,QAAQ,CAACmD,gBAAgB,IAAI,CAAC,CAAC,CAAC;EAEtE,OAAOf,WAAW,CAAC,iBAAmBpC,QAAS,CAAC;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoD,kBAAkBA,CAAC1R,IAAI,EAAE;EAChC,IAAI,CAACA,IAAI,EAAE,OAAO,KAAK;EAEvB,OAAOA,IAAI,CAACoR,cAAc,IAAIM,kBAAkB,CAAC1R,IAAI,CAACoL,MAAM,CAAC;AAC/D;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoG,iBAAiBA,CAACxR,IAAI,EAAE;EAC/B,IAAIA,IAAI,CAAC2R,QAAQ,IAAI,CAAC3R,IAAI,CAAC4R,cAAc,EAAE;IACzC5R,IAAI,CAAC4R,cAAc,GAAG5R,IAAI,CAAC2R,QAAQ,CAACxP,GAAG,CAAC,UAAS0P,OAAO,EAAE;MACxD,OAAOlR,SAAS,CAACX,IAAI,EAAE;QAAE2R,QAAQ,EAAE;MAAK,CAAC,EAAEE,OAAO,CAAC;IACrD,CAAC,CAAC;EACJ;;EAEA;EACA;EACA;EACA,IAAI7R,IAAI,CAAC4R,cAAc,EAAE;IACvB,OAAO5R,IAAI,CAAC4R,cAAc;EAC5B;;EAEA;EACA;EACA;EACA;EACA,IAAIF,kBAAkB,CAAC1R,IAAI,CAAC,EAAE;IAC5B,OAAOW,SAAS,CAACX,IAAI,EAAE;MAAEoL,MAAM,EAAEpL,IAAI,CAACoL,MAAM,GAAGzK,SAAS,CAACX,IAAI,CAACoL,MAAM,CAAC,GAAG;IAAK,CAAC,CAAC;EACjF;EAEA,IAAI7L,MAAM,CAACO,QAAQ,CAACE,IAAI,CAAC,EAAE;IACzB,OAAOW,SAAS,CAACX,IAAI,CAAC;EACxB;;EAEA;EACA,OAAOA,IAAI;AACb;AAEA,IAAIiN,OAAO,GAAG,SAAS;AAAC,IAElB6E,kBAAkB,0BAAAC,MAAA;EAAA;;EACtB,SAAAD,mBAAYE,MAAM,EAAEC,IAAI,EAAE;IAAA,IAAAC,MAAA;IAAAjS,eAAA,OAAA6R,kBAAA;IACxBI,MAAA,GAAApN,UAAA,OAAAgN,kBAAA,GAAME,MAAM;IACZE,MAAA,CAAKvS,IAAI,GAAG,oBAAoB;IAChCuS,MAAA,CAAKD,IAAI,GAAGA,IAAI;IAAC,OAAAC,MAAA;EACnB;EAACnN,SAAA,CAAA+M,kBAAA,EAAAC,MAAA;EAAA,OAAA1R,YAAA,CAAAyR,kBAAA;AAAA,eAAAK,gBAAA,CAL8B/S,KAAK;AAQtC;AACA;AACA;AACA;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA,IAAMgT,MAAM,GAAG3R,UAAU;AACzB,IAAM4R,OAAO,GAAG1R,SAAS;AACzB,IAAM2R,QAAQ,GAAGC,MAAM,CAAC,SAAS,CAAC;AAClC,IAAMC,gBAAgB,GAAG,CAAC;;AAE1B;AACA;AACA;AACA;AACA,IAAMC,IAAI,GAAG,SAAPA,IAAIA,CAAYC,IAAI,EAAE;EAC1B;EACA;EACA,IAAMC,SAAS,GAAGpT,MAAM,CAACuB,MAAM,CAAC,IAAI,CAAC;EACrC;EACA,IAAM8R,OAAO,GAAGrT,MAAM,CAACuB,MAAM,CAAC,IAAI,CAAC;EACnC;EACA,IAAM+R,OAAO,GAAG,EAAE;;EAElB;EACA;EACA,IAAIC,SAAS,GAAG,IAAI;EACpB,IAAMC,kBAAkB,GAAG,qFAAqF;EAChH;EACA,IAAMC,kBAAkB,GAAG;IAAEC,iBAAiB,EAAE,IAAI;IAAEtT,IAAI,EAAE,YAAY;IAAEkJ,QAAQ,EAAE;EAAG,CAAC;;EAExF;EACA;EACA;EACA,IAAInG,OAAO,GAAG;IACZwQ,mBAAmB,EAAE,KAAK;IAC1BC,kBAAkB,EAAE,KAAK;IACzBC,aAAa,EAAE,oBAAoB;IACnCC,gBAAgB,EAAE,6BAA6B;IAC/CzQ,WAAW,EAAE,OAAO;IACpB0Q,WAAW,EAAE,UAAU;IACvBX,SAAS,EAAE,IAAI;IACf;IACA;IACAY,SAAS,EAAE5O;EACb,CAAC;;EAED;;EAEA;AACF;AACA;AACA;EACE,SAAS6O,kBAAkBA,CAACC,YAAY,EAAE;IACxC,OAAO/Q,OAAO,CAAC0Q,aAAa,CAACM,IAAI,CAACD,YAAY,CAAC;EACjD;;EAEA;AACF;AACA;EACE,SAASE,aAAaA,CAACC,KAAK,EAAE;IAC5B,IAAIC,OAAO,GAAGD,KAAK,CAAC3Q,SAAS,GAAG,GAAG;IAEnC4Q,OAAO,IAAID,KAAK,CAACE,UAAU,GAAGF,KAAK,CAACE,UAAU,CAAC7Q,SAAS,GAAG,EAAE;;IAE7D;IACA,IAAM2D,KAAK,GAAGlE,OAAO,CAAC2Q,gBAAgB,CAAC3M,IAAI,CAACmN,OAAO,CAAC;IACpD,IAAIjN,KAAK,EAAE;MACT,IAAM0H,QAAQ,GAAGyF,WAAW,CAACnN,KAAK,CAAC,CAAC,CAAC,CAAC;MACtC,IAAI,CAAC0H,QAAQ,EAAE;QACb3B,IAAI,CAACoG,kBAAkB,CAACrS,OAAO,CAAC,IAAI,EAAEkG,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAChD+F,IAAI,CAAC,mDAAmD,EAAEiH,KAAK,CAAC;MAClE;MACA,OAAOtF,QAAQ,GAAG1H,KAAK,CAAC,CAAC,CAAC,GAAG,cAAc;IAC7C;IAEA,OAAOiN,OAAO,CACX9R,KAAK,CAAC,KAAK,CAAC,CACZiS,IAAI,CAAC,UAACC,MAAM;MAAA,OAAKT,kBAAkB,CAACS,MAAM,CAAC,IAAIF,WAAW,CAACE,MAAM,CAAC;IAAA,EAAC;EACxE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAASC,SAASA,CAACC,kBAAkB,EAAEC,aAAa,EAAEC,cAAc,EAAE;IACpE,IAAIC,IAAI,GAAG,EAAE;IACb,IAAIb,YAAY,GAAG,EAAE;IACrB,IAAI,OAAOW,aAAa,KAAK,QAAQ,EAAE;MACrCE,IAAI,GAAGH,kBAAkB;MACzBE,cAAc,GAAGD,aAAa,CAACC,cAAc;MAC7CZ,YAAY,GAAGW,aAAa,CAAC9F,QAAQ;IACvC,CAAC,MAAM;MACL;MACAtB,UAAU,CAAC,QAAQ,EAAE,qDAAqD,CAAC;MAC3EA,UAAU,CAAC,QAAQ,EAAE,uGAAuG,CAAC;MAC7HyG,YAAY,GAAGU,kBAAkB;MACjCG,IAAI,GAAGF,aAAa;IACtB;;IAEA;IACA;IACA,IAAIC,cAAc,KAAKlU,SAAS,EAAE;MAAEkU,cAAc,GAAG,IAAI;IAAE;;IAE3D;IACA,IAAME,OAAO,GAAG;MACdD,IAAI;MACJhG,QAAQ,EAAEmF;IACZ,CAAC;IACD;IACA;IACAe,IAAI,CAAC,kBAAkB,EAAED,OAAO,CAAC;;IAEjC;IACA;IACA,IAAM1T,MAAM,GAAG0T,OAAO,CAAC1T,MAAM,GACzB0T,OAAO,CAAC1T,MAAM,GACd4T,UAAU,CAACF,OAAO,CAACjG,QAAQ,EAAEiG,OAAO,CAACD,IAAI,EAAED,cAAc,CAAC;IAE9DxT,MAAM,CAACyT,IAAI,GAAGC,OAAO,CAACD,IAAI;IAC1B;IACAE,IAAI,CAAC,iBAAiB,EAAE3T,MAAM,CAAC;IAE/B,OAAOA,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,SAAS4T,UAAUA,CAAChB,YAAY,EAAEiB,eAAe,EAAEL,cAAc,EAAEM,YAAY,EAAE;IAC/E,IAAMC,WAAW,GAAGrV,MAAM,CAACuB,MAAM,CAAC,IAAI,CAAC;;IAEvC;AACJ;AACA;AACA;AACA;AACA;IACI,SAAS+T,WAAWA,CAAC7U,IAAI,EAAE8U,SAAS,EAAE;MACpC,OAAO9U,IAAI,CAAC4K,QAAQ,CAACkK,SAAS,CAAC;IACjC;IAEA,SAASC,eAAeA,CAAA,EAAG;MACzB,IAAI,CAACnR,GAAG,CAACgH,QAAQ,EAAE;QACjBzF,OAAO,CAACrC,OAAO,CAACkS,UAAU,CAAC;QAC3B;MACF;MAEA,IAAI7F,SAAS,GAAG,CAAC;MACjBvL,GAAG,CAACsN,gBAAgB,CAAC/B,SAAS,GAAG,CAAC;MAClC,IAAIvI,KAAK,GAAGhD,GAAG,CAACsN,gBAAgB,CAACxK,IAAI,CAACsO,UAAU,CAAC;MACjD,IAAIC,GAAG,GAAG,EAAE;MAEZ,OAAOrO,KAAK,EAAE;QACZqO,GAAG,IAAID,UAAU,CAACzN,SAAS,CAAC4H,SAAS,EAAEvI,KAAK,CAACC,KAAK,CAAC;QACnD,IAAMqO,IAAI,GAAG5G,QAAQ,CAACG,gBAAgB,GAAG7H,KAAK,CAAC,CAAC,CAAC,CAACqF,WAAW,CAAC,CAAC,GAAGrF,KAAK,CAAC,CAAC,CAAC;QAC1E,IAAM1G,IAAI,GAAG2U,WAAW,CAACjR,GAAG,EAAEsR,IAAI,CAAC;QACnC,IAAIhV,IAAI,EAAE;UACR,IAAAiV,KAAA,GAAAlF,cAAA,CAAiC/P,IAAI;YAA9BkV,IAAI,GAAAD,KAAA;YAAEE,gBAAgB,GAAAF,KAAA;UAC7BhQ,OAAO,CAACrC,OAAO,CAACmS,GAAG,CAAC;UACpBA,GAAG,GAAG,EAAE;UAERL,WAAW,CAACM,IAAI,CAAC,GAAG,CAACN,WAAW,CAACM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;UAChD,IAAIN,WAAW,CAACM,IAAI,CAAC,IAAI1C,gBAAgB,EAAElK,SAAS,IAAI+M,gBAAgB;UACxE,IAAID,IAAI,CAACxT,UAAU,CAAC,GAAG,CAAC,EAAE;YACxB;YACA;YACAqT,GAAG,IAAIrO,KAAK,CAAC,CAAC,CAAC;UACjB,CAAC,MAAM;YACL,IAAM0O,QAAQ,GAAGhH,QAAQ,CAACmD,gBAAgB,CAAC2D,IAAI,CAAC,IAAIA,IAAI;YACxDG,WAAW,CAAC3O,KAAK,CAAC,CAAC,CAAC,EAAE0O,QAAQ,CAAC;UACjC;QACF,CAAC,MAAM;UACLL,GAAG,IAAIrO,KAAK,CAAC,CAAC,CAAC;QACjB;QACAuI,SAAS,GAAGvL,GAAG,CAACsN,gBAAgB,CAAC/B,SAAS;QAC1CvI,KAAK,GAAGhD,GAAG,CAACsN,gBAAgB,CAACxK,IAAI,CAACsO,UAAU,CAAC;MAC/C;MACAC,GAAG,IAAID,UAAU,CAACzN,SAAS,CAAC4H,SAAS,CAAC;MACtChK,OAAO,CAACrC,OAAO,CAACmS,GAAG,CAAC;IACtB;IAEA,SAASO,kBAAkBA,CAAA,EAAG;MAC5B,IAAIR,UAAU,KAAK,EAAE,EAAE;MACvB;MACA,IAAInU,MAAM,GAAG,IAAI;MAEjB,IAAI,OAAO+C,GAAG,CAAC6R,WAAW,KAAK,QAAQ,EAAE;QACvC,IAAI,CAAC9C,SAAS,CAAC/O,GAAG,CAAC6R,WAAW,CAAC,EAAE;UAC/BtQ,OAAO,CAACrC,OAAO,CAACkS,UAAU,CAAC;UAC3B;QACF;QACAnU,MAAM,GAAG4T,UAAU,CAAC7Q,GAAG,CAAC6R,WAAW,EAAET,UAAU,EAAE,IAAI,EAAEU,aAAa,CAAC9R,GAAG,CAAC6R,WAAW,CAAC,CAAC;QACtFC,aAAa,CAAC9R,GAAG,CAAC6R,WAAW,CAAC,GAAG,2BAA6B5U,MAAM,CAAC8U,IAAK;MAC5E,CAAC,MAAM;QACL9U,MAAM,GAAG+U,aAAa,CAACZ,UAAU,EAAEpR,GAAG,CAAC6R,WAAW,CAACxU,MAAM,GAAG2C,GAAG,CAAC6R,WAAW,GAAG,IAAI,CAAC;MACrF;;MAEA;MACA;MACA;MACA;MACA,IAAI7R,GAAG,CAAC0E,SAAS,GAAG,CAAC,EAAE;QACrBA,SAAS,IAAIzH,MAAM,CAACyH,SAAS;MAC/B;MACAnD,OAAO,CAACD,gBAAgB,CAACrE,MAAM,CAACgV,QAAQ,EAAEhV,MAAM,CAACyN,QAAQ,CAAC;IAC5D;IAEA,SAASwH,aAAaA,CAAA,EAAG;MACvB,IAAIlS,GAAG,CAAC6R,WAAW,IAAI,IAAI,EAAE;QAC3BD,kBAAkB,CAAC,CAAC;MACtB,CAAC,MAAM;QACLT,eAAe,CAAC,CAAC;MACnB;MACAC,UAAU,GAAG,EAAE;IACjB;;IAEA;AACJ;AACA;AACA;IACI,SAASO,WAAWA,CAACrJ,OAAO,EAAE1K,KAAK,EAAE;MACnC,IAAI0K,OAAO,KAAK,EAAE,EAAE;MAEpB/G,OAAO,CAACH,UAAU,CAACxD,KAAK,CAAC;MACzB2D,OAAO,CAACrC,OAAO,CAACoJ,OAAO,CAAC;MACxB/G,OAAO,CAACF,QAAQ,CAAC,CAAC;IACpB;;IAEA;AACJ;AACA;AACA;IACI,SAAS8Q,cAAcA,CAACvU,KAAK,EAAEoF,KAAK,EAAE;MACpC,IAAIvE,CAAC,GAAG,CAAC;MACT,IAAM2T,GAAG,GAAGpP,KAAK,CAAC3F,MAAM,GAAG,CAAC;MAC5B,OAAOoB,CAAC,IAAI2T,GAAG,EAAE;QACf,IAAI,CAACxU,KAAK,CAACiM,KAAK,CAACpL,CAAC,CAAC,EAAE;UAAEA,CAAC,EAAE;UAAE;QAAU;QACtC,IAAM4T,KAAK,GAAG3H,QAAQ,CAACmD,gBAAgB,CAACjQ,KAAK,CAACa,CAAC,CAAC,CAAC,IAAIb,KAAK,CAACa,CAAC,CAAC;QAC7D,IAAMU,IAAI,GAAG6D,KAAK,CAACvE,CAAC,CAAC;QACrB,IAAI4T,KAAK,EAAE;UACTV,WAAW,CAACxS,IAAI,EAAEkT,KAAK,CAAC;QAC1B,CAAC,MAAM;UACLjB,UAAU,GAAGjS,IAAI;UACjBgS,eAAe,CAAC,CAAC;UACjBC,UAAU,GAAG,EAAE;QACjB;QACA3S,CAAC,EAAE;MACL;IACF;;IAEA;AACJ;AACA;AACA;IACI,SAAS6T,YAAYA,CAAClW,IAAI,EAAE4G,KAAK,EAAE;MACjC,IAAI5G,IAAI,CAACwB,KAAK,IAAI,OAAOxB,IAAI,CAACwB,KAAK,KAAK,QAAQ,EAAE;QAChD2D,OAAO,CAACnC,QAAQ,CAACsL,QAAQ,CAACmD,gBAAgB,CAACzR,IAAI,CAACwB,KAAK,CAAC,IAAIxB,IAAI,CAACwB,KAAK,CAAC;MACvE;MACA,IAAIxB,IAAI,CAAC8N,UAAU,EAAE;QACnB;QACA,IAAI9N,IAAI,CAAC8N,UAAU,CAACM,KAAK,EAAE;UACzBmH,WAAW,CAACP,UAAU,EAAE1G,QAAQ,CAACmD,gBAAgB,CAACzR,IAAI,CAAC8N,UAAU,CAACM,KAAK,CAAC,IAAIpO,IAAI,CAAC8N,UAAU,CAACM,KAAK,CAAC;UAClG4G,UAAU,GAAG,EAAE;QACjB,CAAC,MAAM,IAAIhV,IAAI,CAAC8N,UAAU,CAACJ,MAAM,EAAE;UACjC;UACAqI,cAAc,CAAC/V,IAAI,CAAC8N,UAAU,EAAElH,KAAK,CAAC;UACtCoO,UAAU,GAAG,EAAE;QACjB;MACF;MAEApR,GAAG,GAAGrE,MAAM,CAACuB,MAAM,CAACd,IAAI,EAAE;QAAE0K,MAAM,EAAE;UAAEnK,KAAK,EAAEqD;QAAI;MAAE,CAAC,CAAC;MACrD,OAAOA,GAAG;IACZ;;IAEA;AACJ;AACA;AACA;AACA;AACA;IACI,SAASuS,SAASA,CAACnW,IAAI,EAAE4G,KAAK,EAAEwP,kBAAkB,EAAE;MAClD,IAAIC,OAAO,GAAGzU,UAAU,CAAC5B,IAAI,CAACqR,KAAK,EAAE+E,kBAAkB,CAAC;MAExD,IAAIC,OAAO,EAAE;QACX,IAAIrW,IAAI,CAAC,QAAQ,CAAC,EAAE;UAClB,IAAMyI,IAAI,GAAG,IAAI1I,QAAQ,CAACC,IAAI,CAAC;UAC/BA,IAAI,CAAC,QAAQ,CAAC,CAAC4G,KAAK,EAAE6B,IAAI,CAAC;UAC3B,IAAIA,IAAI,CAACrI,cAAc,EAAEiW,OAAO,GAAG,KAAK;QAC1C;QAEA,IAAIA,OAAO,EAAE;UACX,OAAOrW,IAAI,CAACuL,UAAU,IAAIvL,IAAI,CAAC0K,MAAM,EAAE;YACrC1K,IAAI,GAAGA,IAAI,CAAC0K,MAAM;UACpB;UACA,OAAO1K,IAAI;QACb;MACF;MACA;MACA;MACA,IAAIA,IAAI,CAACoR,cAAc,EAAE;QACvB,OAAO+E,SAAS,CAACnW,IAAI,CAAC0K,MAAM,EAAE9D,KAAK,EAAEwP,kBAAkB,CAAC;MAC1D;IACF;;IAEA;AACJ;AACA;AACA;AACA;IACI,SAASE,QAAQA,CAAC3P,MAAM,EAAE;MACxB,IAAI/C,GAAG,CAACiM,OAAO,CAACF,UAAU,KAAK,CAAC,EAAE;QAChC;QACA;QACAqF,UAAU,IAAIrO,MAAM,CAAC,CAAC,CAAC;QACvB,OAAO,CAAC;MACV,CAAC,MAAM;QACL;QACA;QACA4P,wBAAwB,GAAG,IAAI;QAC/B,OAAO,CAAC;MACV;IACF;;IAEA;AACJ;AACA;AACA;AACA;AACA;IACI,SAASC,YAAYA,CAAC5P,KAAK,EAAE;MAC3B,IAAMD,MAAM,GAAGC,KAAK,CAAC,CAAC,CAAC;MACvB,IAAM6P,OAAO,GAAG7P,KAAK,CAAC4J,IAAI;MAE1B,IAAM/H,IAAI,GAAG,IAAI1I,QAAQ,CAAC0W,OAAO,CAAC;MAClC;MACA,IAAMC,eAAe,GAAG,CAACD,OAAO,CAAC9L,aAAa,EAAE8L,OAAO,CAAC,UAAU,CAAC,CAAC;MACpE,SAAAE,EAAA,MAAAC,gBAAA,GAAiBF,eAAe,EAAAC,EAAA,GAAAC,gBAAA,CAAA3V,MAAA,EAAA0V,EAAA,IAAE;QAA7B,IAAME,EAAE,GAAAD,gBAAA,CAAAD,EAAA;QACX,IAAI,CAACE,EAAE,EAAE;QACTA,EAAE,CAACjQ,KAAK,EAAE6B,IAAI,CAAC;QACf,IAAIA,IAAI,CAACrI,cAAc,EAAE,OAAOkW,QAAQ,CAAC3P,MAAM,CAAC;MAClD;MAEA,IAAI8P,OAAO,CAAC7I,IAAI,EAAE;QAChBoH,UAAU,IAAIrO,MAAM;MACtB,CAAC,MAAM;QACL,IAAI8P,OAAO,CAACvN,YAAY,EAAE;UACxB8L,UAAU,IAAIrO,MAAM;QACtB;QACAmP,aAAa,CAAC,CAAC;QACf,IAAI,CAACW,OAAO,CAAC5I,WAAW,IAAI,CAAC4I,OAAO,CAACvN,YAAY,EAAE;UACjD8L,UAAU,GAAGrO,MAAM;QACrB;MACF;MACAuP,YAAY,CAACO,OAAO,EAAE7P,KAAK,CAAC;MAC5B,OAAO6P,OAAO,CAAC5I,WAAW,GAAG,CAAC,GAAGlH,MAAM,CAAC1F,MAAM;IAChD;;IAEA;AACJ;AACA;AACA;AACA;IACI,SAAS6V,UAAUA,CAAClQ,KAAK,EAAE;MACzB,IAAMD,MAAM,GAAGC,KAAK,CAAC,CAAC,CAAC;MACvB,IAAMwP,kBAAkB,GAAG1B,eAAe,CAACnN,SAAS,CAACX,KAAK,CAACC,KAAK,CAAC;MAEjE,IAAMkQ,OAAO,GAAGZ,SAAS,CAACvS,GAAG,EAAEgD,KAAK,EAAEwP,kBAAkB,CAAC;MACzD,IAAI,CAACW,OAAO,EAAE;QAAE,OAAOzE,QAAQ;MAAE;MAEjC,IAAM0E,MAAM,GAAGpT,GAAG;MAClB,IAAIA,GAAG,CAACqB,QAAQ,IAAIrB,GAAG,CAACqB,QAAQ,CAACmJ,KAAK,EAAE;QACtC0H,aAAa,CAAC,CAAC;QACfP,WAAW,CAAC5O,MAAM,EAAE/C,GAAG,CAACqB,QAAQ,CAACmJ,KAAK,CAAC;MACzC,CAAC,MAAM,IAAIxK,GAAG,CAACqB,QAAQ,IAAIrB,GAAG,CAACqB,QAAQ,CAACyI,MAAM,EAAE;QAC9CoI,aAAa,CAAC,CAAC;QACfC,cAAc,CAACnS,GAAG,CAACqB,QAAQ,EAAE2B,KAAK,CAAC;MACrC,CAAC,MAAM,IAAIoQ,MAAM,CAACpJ,IAAI,EAAE;QACtBoH,UAAU,IAAIrO,MAAM;MACtB,CAAC,MAAM;QACL,IAAI,EAAEqQ,MAAM,CAAC/I,SAAS,IAAI+I,MAAM,CAAChJ,UAAU,CAAC,EAAE;UAC5CgH,UAAU,IAAIrO,MAAM;QACtB;QACAmP,aAAa,CAAC,CAAC;QACf,IAAIkB,MAAM,CAAChJ,UAAU,EAAE;UACrBgH,UAAU,GAAGrO,MAAM;QACrB;MACF;MACA,GAAG;QACD,IAAI/C,GAAG,CAACpC,KAAK,EAAE;UACb2D,OAAO,CAAChC,SAAS,CAAC,CAAC;QACrB;QACA,IAAI,CAACS,GAAG,CAACgK,IAAI,IAAI,CAAChK,GAAG,CAAC6R,WAAW,EAAE;UACjCnN,SAAS,IAAI1E,GAAG,CAAC0E,SAAS;QAC5B;QACA1E,GAAG,GAAGA,GAAG,CAAC8G,MAAM;MAClB,CAAC,QAAQ9G,GAAG,KAAKmT,OAAO,CAACrM,MAAM;MAC/B,IAAIqM,OAAO,CAAC3L,MAAM,EAAE;QAClB8K,YAAY,CAACa,OAAO,CAAC3L,MAAM,EAAExE,KAAK,CAAC;MACrC;MACA,OAAOoQ,MAAM,CAAC/I,SAAS,GAAG,CAAC,GAAGtH,MAAM,CAAC1F,MAAM;IAC7C;IAEA,SAASgW,oBAAoBA,CAAA,EAAG;MAC9B,IAAMC,IAAI,GAAG,EAAE;MACf,KAAK,IAAIC,OAAO,GAAGvT,GAAG,EAAEuT,OAAO,KAAK7I,QAAQ,EAAE6I,OAAO,GAAGA,OAAO,CAACzM,MAAM,EAAE;QACtE,IAAIyM,OAAO,CAAC3V,KAAK,EAAE;UACjB0V,IAAI,CAACE,OAAO,CAACD,OAAO,CAAC3V,KAAK,CAAC;QAC7B;MACF;MACA0V,IAAI,CAACxX,OAAO,CAAC,UAAA2X,IAAI;QAAA,OAAIlS,OAAO,CAACnC,QAAQ,CAACqU,IAAI,CAAC;MAAA,EAAC;IAC9C;;IAEA;IACA,IAAIC,SAAS,GAAG,CAAC,CAAC;;IAElB;AACJ;AACA;AACA;AACA;AACA;IACI,SAASC,aAAaA,CAACC,eAAe,EAAE5Q,KAAK,EAAE;MAC7C,IAAMD,MAAM,GAAGC,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC;;MAEhC;MACAoO,UAAU,IAAIwC,eAAe;MAE7B,IAAI7Q,MAAM,IAAI,IAAI,EAAE;QAClBmP,aAAa,CAAC,CAAC;QACf,OAAO,CAAC;MACV;;MAEA;MACA;MACA;MACA;MACA,IAAIwB,SAAS,CAACzX,IAAI,KAAK,OAAO,IAAI+G,KAAK,CAAC/G,IAAI,KAAK,KAAK,IAAIyX,SAAS,CAACzQ,KAAK,KAAKD,KAAK,CAACC,KAAK,IAAIF,MAAM,KAAK,EAAE,EAAE;QAC1G;QACAqO,UAAU,IAAIN,eAAe,CAAC5E,KAAK,CAAClJ,KAAK,CAACC,KAAK,EAAED,KAAK,CAACC,KAAK,GAAG,CAAC,CAAC;QACjE,IAAI,CAACiM,SAAS,EAAE;UACd;UACA,IAAM2E,GAAG,GAAG,IAAIrY,KAAK,CAAC,wBAAwBqU,YAAY,GAAG,CAAC;UAC9DgE,GAAG,CAAChE,YAAY,GAAGA,YAAY;UAC/BgE,GAAG,CAACC,OAAO,GAAGJ,SAAS,CAAC9G,IAAI;UAC5B,MAAMiH,GAAG;QACX;QACA,OAAO,CAAC;MACV;MACAH,SAAS,GAAG1Q,KAAK;MAEjB,IAAIA,KAAK,CAAC/G,IAAI,KAAK,OAAO,EAAE;QAC1B,OAAO2W,YAAY,CAAC5P,KAAK,CAAC;MAC5B,CAAC,MAAM,IAAIA,KAAK,CAAC/G,IAAI,KAAK,SAAS,IAAI,CAACwU,cAAc,EAAE;QACtD;QACA;QACA,IAAMoD,IAAG,GAAG,IAAIrY,KAAK,CAAC,kBAAkB,GAAGuH,MAAM,GAAG,cAAc,IAAI/C,GAAG,CAACpC,KAAK,IAAI,WAAW,CAAC,GAAG,GAAG,CAAC;QACtGiW,IAAG,CAACzX,IAAI,GAAG4D,GAAG;QACd,MAAM6T,IAAG;MACX,CAAC,MAAM,IAAI7Q,KAAK,CAAC/G,IAAI,KAAK,KAAK,EAAE;QAC/B,IAAM8X,SAAS,GAAGb,UAAU,CAAClQ,KAAK,CAAC;QACnC,IAAI+Q,SAAS,KAAKrF,QAAQ,EAAE;UAC1B,OAAOqF,SAAS;QAClB;MACF;;MAEA;MACA;MACA;MACA,IAAI/Q,KAAK,CAAC/G,IAAI,KAAK,SAAS,IAAI8G,MAAM,KAAK,EAAE,EAAE;QAC7C;QACAqO,UAAU,IAAI,IAAI;QAClB,OAAO,CAAC;MACV;;MAEA;MACA;MACA;MACA;MACA,IAAI4C,UAAU,GAAG,MAAM,IAAIA,UAAU,GAAGhR,KAAK,CAACC,KAAK,GAAG,CAAC,EAAE;QACvD,IAAM4Q,KAAG,GAAG,IAAIrY,KAAK,CAAC,2DAA2D,CAAC;QAClF,MAAMqY,KAAG;MACX;;MAEA;AACN;AACA;AACA;AACA;AACA;;MAGMzC,UAAU,IAAIrO,MAAM;MACpB,OAAOA,MAAM,CAAC1F,MAAM;IACtB;IAEA,IAAMqN,QAAQ,GAAGyF,WAAW,CAACN,YAAY,CAAC;IAC1C,IAAI,CAACnF,QAAQ,EAAE;MACb9B,KAAK,CAACuG,kBAAkB,CAACrS,OAAO,CAAC,IAAI,EAAE+S,YAAY,CAAC,CAAC;MACrD,MAAM,IAAIrU,KAAK,CAAC,qBAAqB,GAAGqU,YAAY,GAAG,GAAG,CAAC;IAC7D;IAEA,IAAMoE,EAAE,GAAGxJ,eAAe,CAACC,QAAQ,CAAC;IACpC,IAAIzN,MAAM,GAAG,EAAE;IACf;IACA,IAAI+C,GAAG,GAAG+Q,YAAY,IAAIkD,EAAE;IAC5B;IACA,IAAMnC,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC;IAC1B,IAAMvQ,OAAO,GAAG,IAAIzC,OAAO,CAAC6Q,SAAS,CAAC7Q,OAAO,CAAC;IAC9CuU,oBAAoB,CAAC,CAAC;IACtB,IAAIjC,UAAU,GAAG,EAAE;IACnB,IAAI1M,SAAS,GAAG,CAAC;IACjB,IAAIzB,KAAK,GAAG,CAAC;IACb,IAAI+Q,UAAU,GAAG,CAAC;IAClB,IAAIrB,wBAAwB,GAAG,KAAK;IAEpC,IAAI;MACF,IAAI,CAACjI,QAAQ,CAACwJ,YAAY,EAAE;QAC1BlU,GAAG,CAACiM,OAAO,CAACM,WAAW,CAAC,CAAC;QAEzB,SAAS;UACPyH,UAAU,EAAE;UACZ,IAAIrB,wBAAwB,EAAE;YAC5B;YACA;YACAA,wBAAwB,GAAG,KAAK;UAClC,CAAC,MAAM;YACL3S,GAAG,CAACiM,OAAO,CAACM,WAAW,CAAC,CAAC;UAC3B;UACAvM,GAAG,CAACiM,OAAO,CAACV,SAAS,GAAGtI,KAAK;UAE7B,IAAMD,KAAK,GAAGhD,GAAG,CAACiM,OAAO,CAACnJ,IAAI,CAACgO,eAAe,CAAC;UAC/C;;UAEA,IAAI,CAAC9N,KAAK,EAAE;UAEZ,IAAMuE,WAAW,GAAGuJ,eAAe,CAACnN,SAAS,CAACV,KAAK,EAAED,KAAK,CAACC,KAAK,CAAC;UACjE,IAAMkR,cAAc,GAAGR,aAAa,CAACpM,WAAW,EAAEvE,KAAK,CAAC;UACxDC,KAAK,GAAGD,KAAK,CAACC,KAAK,GAAGkR,cAAc;QACtC;QACAR,aAAa,CAAC7C,eAAe,CAACnN,SAAS,CAACV,KAAK,CAAC,CAAC;MACjD,CAAC,MAAM;QACLyH,QAAQ,CAACwJ,YAAY,CAACpD,eAAe,EAAEvP,OAAO,CAAC;MACjD;MAEAA,OAAO,CAACI,QAAQ,CAAC,CAAC;MAClB1E,MAAM,GAAGsE,OAAO,CAACE,MAAM,CAAC,CAAC;MAEzB,OAAO;QACLiJ,QAAQ,EAAEmF,YAAY;QACtBlT,KAAK,EAAEM,MAAM;QACbyH,SAAS;QACTM,OAAO,EAAE,KAAK;QACdiN,QAAQ,EAAE1Q,OAAO;QACjBwQ,IAAI,EAAE/R;MACR,CAAC;IACH,CAAC,CAAC,OAAO6T,GAAG,EAAE;MACZ,IAAIA,GAAG,CAAChL,OAAO,IAAIgL,GAAG,CAAChL,OAAO,CAAC5K,QAAQ,CAAC,SAAS,CAAC,EAAE;QAClD,OAAO;UACLyM,QAAQ,EAAEmF,YAAY;UACtBlT,KAAK,EAAE6R,MAAM,CAACsC,eAAe,CAAC;UAC9B9L,OAAO,EAAE,IAAI;UACbN,SAAS,EAAE,CAAC;UACZ0P,UAAU,EAAE;YACVvL,OAAO,EAAEgL,GAAG,CAAChL,OAAO;YACpB5F,KAAK;YACL0N,OAAO,EAAEG,eAAe,CAAC5E,KAAK,CAACjJ,KAAK,GAAG,GAAG,EAAEA,KAAK,GAAG,GAAG,CAAC;YACxD7G,IAAI,EAAEyX,GAAG,CAACzX,IAAI;YACdiY,WAAW,EAAEpX;UACf,CAAC;UACDgV,QAAQ,EAAE1Q;QACZ,CAAC;MACH,CAAC,MAAM,IAAI2N,SAAS,EAAE;QACpB,OAAO;UACLxE,QAAQ,EAAEmF,YAAY;UACtBlT,KAAK,EAAE6R,MAAM,CAACsC,eAAe,CAAC;UAC9B9L,OAAO,EAAE,KAAK;UACdN,SAAS,EAAE,CAAC;UACZ4P,WAAW,EAAET,GAAG;UAChB5B,QAAQ,EAAE1Q,OAAO;UACjBwQ,IAAI,EAAE/R;QACR,CAAC;MACH,CAAC,MAAM;QACL,MAAM6T,GAAG;MACX;IACF;EACF;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASU,uBAAuBA,CAAC7D,IAAI,EAAE;IACrC,IAAMzT,MAAM,GAAG;MACbN,KAAK,EAAE6R,MAAM,CAACkC,IAAI,CAAC;MACnB1L,OAAO,EAAE,KAAK;MACdN,SAAS,EAAE,CAAC;MACZqN,IAAI,EAAE3C,kBAAkB;MACxB6C,QAAQ,EAAE,IAAInT,OAAO,CAAC6Q,SAAS,CAAC7Q,OAAO;IACzC,CAAC;IACD7B,MAAM,CAACgV,QAAQ,CAAC/S,OAAO,CAACwR,IAAI,CAAC;IAC7B,OAAOzT,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAGE,SAAS+U,aAAaA,CAACtB,IAAI,EAAE8D,cAAc,EAAE;IAC3CA,cAAc,GAAGA,cAAc,IAAI1V,OAAO,CAACiQ,SAAS,IAAIpT,MAAM,CAAC+L,IAAI,CAACqH,SAAS,CAAC;IAC9E,IAAM0F,SAAS,GAAGF,uBAAuB,CAAC7D,IAAI,CAAC;IAE/C,IAAMgE,OAAO,GAAGF,cAAc,CAACG,MAAM,CAACxE,WAAW,CAAC,CAACwE,MAAM,CAACC,aAAa,CAAC,CAACrW,GAAG,CAAC,UAAAxC,IAAI;MAAA,OAC/E8U,UAAU,CAAC9U,IAAI,EAAE2U,IAAI,EAAE,KAAK,CAAC;IAAA,CAC/B,CAAC;IACDgE,OAAO,CAAClB,OAAO,CAACiB,SAAS,CAAC,CAAC,CAAC;;IAE5B,IAAMI,MAAM,GAAGH,OAAO,CAACI,IAAI,CAAC,UAACC,CAAC,EAAEC,CAAC,EAAK;MACpC;MACA,IAAID,CAAC,CAACrQ,SAAS,KAAKsQ,CAAC,CAACtQ,SAAS,EAAE,OAAOsQ,CAAC,CAACtQ,SAAS,GAAGqQ,CAAC,CAACrQ,SAAS;;MAEjE;MACA;MACA,IAAIqQ,CAAC,CAACrK,QAAQ,IAAIsK,CAAC,CAACtK,QAAQ,EAAE;QAC5B,IAAIyF,WAAW,CAAC4E,CAAC,CAACrK,QAAQ,CAAC,CAACuK,UAAU,KAAKD,CAAC,CAACtK,QAAQ,EAAE;UACrD,OAAO,CAAC;QACV,CAAC,MAAM,IAAIyF,WAAW,CAAC6E,CAAC,CAACtK,QAAQ,CAAC,CAACuK,UAAU,KAAKF,CAAC,CAACrK,QAAQ,EAAE;UAC5D,OAAO,CAAC,CAAC;QACX;MACF;;MAEA;MACA;MACA;MACA;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IAEF,IAAAwK,OAAA,GAAA7I,cAAA,CAA2BwI,MAAM;MAA1BM,IAAI,GAAAD,OAAA;MAAEE,UAAU,GAAAF,OAAA;;IAEvB;IACA,IAAMjY,MAAM,GAAGkY,IAAI;IACnBlY,MAAM,CAACmY,UAAU,GAAGA,UAAU;IAE9B,OAAOnY,MAAM;EACf;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASoY,eAAeA,CAACC,OAAO,EAAEC,WAAW,EAAEC,UAAU,EAAE;IACzD,IAAM9K,QAAQ,GAAI6K,WAAW,IAAIvG,OAAO,CAACuG,WAAW,CAAC,IAAKC,UAAU;IAEpEF,OAAO,CAACG,SAAS,CAAC/Z,GAAG,CAAC,MAAM,CAAC;IAC7B4Z,OAAO,CAACG,SAAS,CAAC/Z,GAAG,CAAC,YAAYgP,QAAQ,EAAE,CAAC;EAC/C;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASgL,gBAAgBA,CAACJ,OAAO,EAAE;IACjC;IACA,IAAI3X,IAAI,GAAG,IAAI;IACf,IAAM+M,QAAQ,GAAGqF,aAAa,CAACuF,OAAO,CAAC;IAEvC,IAAI1F,kBAAkB,CAAClF,QAAQ,CAAC,EAAE;IAElCkG,IAAI,CAAC,yBAAyB,EAC5B;MAAE9P,EAAE,EAAEwU,OAAO;MAAE5K;IAAS,CAAC,CAAC;IAE5B,IAAI4K,OAAO,CAACK,OAAO,CAACC,WAAW,EAAE;MAC/B9M,OAAO,CAACK,GAAG,CAAC,wFAAwF,EAAEmM,OAAO,CAAC;MAC9G;IACF;;IAEA;IACA;IACA;IACA;IACA;IACA,IAAIA,OAAO,CAAC5V,QAAQ,CAACrC,MAAM,GAAG,CAAC,EAAE;MAC/B,IAAI,CAACyB,OAAO,CAACwQ,mBAAmB,EAAE;QAChCxG,OAAO,CAACC,IAAI,CAAC,+FAA+F,CAAC;QAC7GD,OAAO,CAACC,IAAI,CAAC,2DAA2D,CAAC;QACzED,OAAO,CAACC,IAAI,CAAC,kCAAkC,CAAC;QAChDD,OAAO,CAACC,IAAI,CAACuM,OAAO,CAAC;MACvB;MACA,IAAIxW,OAAO,CAACyQ,kBAAkB,EAAE;QAC9B,IAAMsE,GAAG,GAAG,IAAI3F,kBAAkB,CAChC,kDAAkD,EAClDoH,OAAO,CAACO,SACV,CAAC;QACD,MAAMhC,GAAG;MACX;IACF;IAEAlW,IAAI,GAAG2X,OAAO;IACd,IAAMnW,IAAI,GAAGxB,IAAI,CAACmY,WAAW;IAC7B,IAAM7Y,MAAM,GAAGyN,QAAQ,GAAG4F,SAAS,CAACnR,IAAI,EAAE;MAAEuL,QAAQ;MAAE+F,cAAc,EAAE;IAAK,CAAC,CAAC,GAAGuB,aAAa,CAAC7S,IAAI,CAAC;IAEnGmW,OAAO,CAACO,SAAS,GAAG5Y,MAAM,CAACN,KAAK;IAChC2Y,OAAO,CAACK,OAAO,CAACC,WAAW,GAAG,KAAK;IACnCP,eAAe,CAACC,OAAO,EAAE5K,QAAQ,EAAEzN,MAAM,CAACyN,QAAQ,CAAC;IACnD4K,OAAO,CAACrY,MAAM,GAAG;MACfyN,QAAQ,EAAEzN,MAAM,CAACyN,QAAQ;MACzB;MACA7I,EAAE,EAAE5E,MAAM,CAACyH,SAAS;MACpBA,SAAS,EAAEzH,MAAM,CAACyH;IACpB,CAAC;IACD,IAAIzH,MAAM,CAACmY,UAAU,EAAE;MACrBE,OAAO,CAACF,UAAU,GAAG;QACnB1K,QAAQ,EAAEzN,MAAM,CAACmY,UAAU,CAAC1K,QAAQ;QACpChG,SAAS,EAAEzH,MAAM,CAACmY,UAAU,CAAC1Q;MAC/B,CAAC;IACH;IAEAkM,IAAI,CAAC,wBAAwB,EAAE;MAAE9P,EAAE,EAAEwU,OAAO;MAAErY,MAAM;MAAEkC;IAAK,CAAC,CAAC;EAC/D;;EAEA;AACF;AACA;AACA;AACA;EACE,SAAS4W,SAASA,CAACC,WAAW,EAAE;IAC9BlX,OAAO,GAAG2P,OAAO,CAAC3P,OAAO,EAAEkX,WAAW,CAAC;EACzC;;EAEA;EACA,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA,EAAS;IAC7BC,YAAY,CAAC,CAAC;IACd9M,UAAU,CAAC,QAAQ,EAAE,yDAAyD,CAAC;EACjF,CAAC;;EAED;EACA,SAAS+M,sBAAsBA,CAAA,EAAG;IAChCD,YAAY,CAAC,CAAC;IACd9M,UAAU,CAAC,QAAQ,EAAE,+DAA+D,CAAC;EACvF;EAEA,IAAIgN,cAAc,GAAG,KAAK;;EAE1B;AACF;AACA;EACE,SAASF,YAAYA,CAAA,EAAG;IACtB,SAASG,IAAIA,CAAA,EAAG;MACd;MACAH,YAAY,CAAC,CAAC;IAChB;;IAEA;IACA,IAAII,QAAQ,CAACC,UAAU,KAAK,SAAS,EAAE;MACrC;MACA,IAAI,CAACH,cAAc,EAAE;QACnBI,MAAM,CAACC,gBAAgB,CAAC,kBAAkB,EAAEJ,IAAI,EAAE,KAAK,CAAC;MAC1D;MACAD,cAAc,GAAG,IAAI;MACrB;IACF;IAEA,IAAMM,MAAM,GAAGJ,QAAQ,CAACK,gBAAgB,CAAC7X,OAAO,CAAC4Q,WAAW,CAAC;IAC7DgH,MAAM,CAAC5a,OAAO,CAAC4Z,gBAAgB,CAAC;EAClC;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,SAASkB,gBAAgBA,CAAC/G,YAAY,EAAEgH,kBAAkB,EAAE;IAC1D,IAAIC,IAAI,GAAG,IAAI;IACf,IAAI;MACFA,IAAI,GAAGD,kBAAkB,CAAC/H,IAAI,CAAC;IACjC,CAAC,CAAC,OAAOiI,OAAO,EAAE;MAChBnO,KAAK,CAAC,uDAAuD,CAAC9L,OAAO,CAAC,IAAI,EAAE+S,YAAY,CAAC,CAAC;MAC1F;MACA,IAAI,CAACX,SAAS,EAAE;QAAE,MAAM6H,OAAO;MAAE,CAAC,MAAM;QAAEnO,KAAK,CAACmO,OAAO,CAAC;MAAE;MAC1D;MACA;MACA;MACA;MACAD,IAAI,GAAG1H,kBAAkB;IAC3B;IACA;IACA,IAAI,CAAC0H,IAAI,CAAC/a,IAAI,EAAE+a,IAAI,CAAC/a,IAAI,GAAG8T,YAAY;IACxCd,SAAS,CAACc,YAAY,CAAC,GAAGiH,IAAI;IAC9BA,IAAI,CAACE,aAAa,GAAGH,kBAAkB,CAACI,IAAI,CAAC,IAAI,EAAEnI,IAAI,CAAC;IAExD,IAAIgI,IAAI,CAAC9H,OAAO,EAAE;MAChBkI,eAAe,CAACJ,IAAI,CAAC9H,OAAO,EAAE;QAAEa;MAAa,CAAC,CAAC;IACjD;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASsH,kBAAkBA,CAACtH,YAAY,EAAE;IACxC,OAAOd,SAAS,CAACc,YAAY,CAAC;IAC9B,SAAAuH,GAAA,MAAAC,YAAA,GAAoB1b,MAAM,CAAC+L,IAAI,CAACsH,OAAO,CAAC,EAAAoI,GAAA,GAAAC,YAAA,CAAAha,MAAA,EAAA+Z,GAAA,IAAE;MAArC,IAAME,KAAK,GAAAD,YAAA,CAAAD,GAAA;MACd,IAAIpI,OAAO,CAACsI,KAAK,CAAC,KAAKzH,YAAY,EAAE;QACnC,OAAOb,OAAO,CAACsI,KAAK,CAAC;MACvB;IACF;EACF;;EAEA;AACF;AACA;EACE,SAASC,aAAaA,CAAA,EAAG;IACvB,OAAO5b,MAAM,CAAC+L,IAAI,CAACqH,SAAS,CAAC;EAC/B;;EAEA;AACF;AACA;AACA;EACE,SAASoB,WAAWA,CAACpU,IAAI,EAAE;IACzBA,IAAI,GAAG,CAACA,IAAI,IAAI,EAAE,EAAEsM,WAAW,CAAC,CAAC;IACjC,OAAO0G,SAAS,CAAChT,IAAI,CAAC,IAAIgT,SAAS,CAACC,OAAO,CAACjT,IAAI,CAAC,CAAC;EACpD;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASmb,eAAeA,CAACM,SAAS,EAAAC,KAAA,EAAoB;IAAA,IAAhB5H,YAAY,GAAA4H,KAAA,CAAZ5H,YAAY;IAChD,IAAI,OAAO2H,SAAS,KAAK,QAAQ,EAAE;MACjCA,SAAS,GAAG,CAACA,SAAS,CAAC;IACzB;IACAA,SAAS,CAAC1b,OAAO,CAAC,UAAAwb,KAAK,EAAI;MAAEtI,OAAO,CAACsI,KAAK,CAACjP,WAAW,CAAC,CAAC,CAAC,GAAGwH,YAAY;IAAE,CAAC,CAAC;EAC9E;;EAEA;AACF;AACA;AACA;EACE,SAAS+E,aAAaA,CAAC7Y,IAAI,EAAE;IAC3B,IAAM+a,IAAI,GAAG3G,WAAW,CAACpU,IAAI,CAAC;IAC9B,OAAO+a,IAAI,IAAI,CAACA,IAAI,CAACzH,iBAAiB;EACxC;;EAEA;AACF;AACA;AACA;AACA;EACE,SAASqI,gBAAgBA,CAACC,MAAM,EAAE;IAChC;IACA,IAAIA,MAAM,CAAC,uBAAuB,CAAC,IAAI,CAACA,MAAM,CAAC,yBAAyB,CAAC,EAAE;MACzEA,MAAM,CAAC,yBAAyB,CAAC,GAAG,UAACrb,IAAI,EAAK;QAC5Cqb,MAAM,CAAC,uBAAuB,CAAC,CAC7Bhc,MAAM,CAACgE,MAAM,CAAC;UAAEqQ,KAAK,EAAE1T,IAAI,CAACwE;QAAG,CAAC,EAAExE,IAAI,CACxC,CAAC;MACH,CAAC;IACH;IACA,IAAIqb,MAAM,CAAC,sBAAsB,CAAC,IAAI,CAACA,MAAM,CAAC,wBAAwB,CAAC,EAAE;MACvEA,MAAM,CAAC,wBAAwB,CAAC,GAAG,UAACrb,IAAI,EAAK;QAC3Cqb,MAAM,CAAC,sBAAsB,CAAC,CAC5Bhc,MAAM,CAACgE,MAAM,CAAC;UAAEqQ,KAAK,EAAE1T,IAAI,CAACwE;QAAG,CAAC,EAAExE,IAAI,CACxC,CAAC;MACH,CAAC;IACH;EACF;;EAEA;AACF;AACA;EACE,SAASsb,SAASA,CAACD,MAAM,EAAE;IACzBD,gBAAgB,CAACC,MAAM,CAAC;IACxB1I,OAAO,CAAChP,IAAI,CAAC0X,MAAM,CAAC;EACtB;;EAEA;AACF;AACA;EACE,SAASE,YAAYA,CAACF,MAAM,EAAE;IAC5B,IAAM1U,KAAK,GAAGgM,OAAO,CAAC6I,OAAO,CAACH,MAAM,CAAC;IACrC,IAAI1U,KAAK,KAAK,CAAC,CAAC,EAAE;MAChBgM,OAAO,CAAC3M,MAAM,CAACW,KAAK,EAAE,CAAC,CAAC;IAC1B;EACF;;EAEA;AACF;AACA;AACA;AACA;EACE,SAAS2N,IAAIA,CAACmH,KAAK,EAAE7V,IAAI,EAAE;IACzB,IAAM+Q,EAAE,GAAG8E,KAAK;IAChB9I,OAAO,CAACnT,OAAO,CAAC,UAAS6b,MAAM,EAAE;MAC/B,IAAIA,MAAM,CAAC1E,EAAE,CAAC,EAAE;QACd0E,MAAM,CAAC1E,EAAE,CAAC,CAAC/Q,IAAI,CAAC;MAClB;IACF,CAAC,CAAC;EACJ;;EAEA;AACF;AACA;AACA;EACE,SAAS8V,uBAAuBA,CAAClX,EAAE,EAAE;IACnCsI,UAAU,CAAC,QAAQ,EAAE,kDAAkD,CAAC;IACxEA,UAAU,CAAC,QAAQ,EAAE,kCAAkC,CAAC;IAExD,OAAOsM,gBAAgB,CAAC5U,EAAE,CAAC;EAC7B;;EAEA;EACAnF,MAAM,CAACgE,MAAM,CAACmP,IAAI,EAAE;IAClBwB,SAAS;IACT0B,aAAa;IACbkE,YAAY;IACZR,gBAAgB;IAChB;IACAuC,cAAc,EAAED,uBAAuB;IACvCjC,SAAS;IACTE,gBAAgB;IAChBE,sBAAsB;IACtBS,gBAAgB;IAChBO,kBAAkB;IAClBI,aAAa;IACbpH,WAAW;IACX+G,eAAe;IACftC,aAAa;IACbnG,OAAO;IACPmJ,SAAS;IACTC;EACF,CAAC,CAAC;EAEF/I,IAAI,CAACoJ,SAAS,GAAG,YAAW;IAAEhJ,SAAS,GAAG,KAAK;EAAE,CAAC;EAClDJ,IAAI,CAACqJ,QAAQ,GAAG,YAAW;IAAEjJ,SAAS,GAAG,IAAI;EAAE,CAAC;EAChDJ,IAAI,CAACsJ,aAAa,GAAG/O,OAAO;EAE5ByF,IAAI,CAACtL,KAAK,GAAG;IACXnF,MAAM,EAAEA,MAAM;IACdyD,SAAS,EAAEA,SAAS;IACpBS,MAAM,EAAEA,MAAM;IACdP,QAAQ,EAAEA,QAAQ;IAClBD,gBAAgB,EAAEA;EACpB,CAAC;EAED,KAAK,IAAMrF,GAAG,IAAI2J,KAAK,EAAE;IACvB;IACA,IAAI,OAAOA,KAAK,CAAC3J,GAAG,CAAC,KAAK,QAAQ,EAAE;MAClC;MACAxB,UAAU,CAACmL,KAAK,CAAC3J,GAAG,CAAC,CAAC;IACxB;EACF;;EAEA;EACAf,MAAM,CAACgE,MAAM,CAACmP,IAAI,EAAEzI,KAAK,CAAC;EAE1B,OAAOyI,IAAI;AACb,CAAC;;AAED;AACA,IAAMwB,SAAS,GAAGzB,IAAI,CAAC,CAAC,CAAC,CAAC;;AAE1B;AACA;AACAyB,SAAS,CAAC+H,WAAW,GAAG;EAAA,OAAMxJ,IAAI,CAAC,CAAC,CAAC,CAAC;AAAA;AAEtCyJ,MAAM,CAACC,OAAO,GAAGjI,SAAS;AAC1BA,SAAS,CAACkI,WAAW,GAAGlI,SAAS;AACjCA,SAAS,CAACmI,OAAO,GAAGnI,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}