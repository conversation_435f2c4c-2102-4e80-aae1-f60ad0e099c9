{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { defineComponent, computed, openBlock, createElementBlock, mergeProps, unref, renderSlot } from 'vue';\nimport { visualHiddenProps } from './visual-hidden2.mjs';\nimport _export_sfc from '../../../_virtual/plugin-vue_export-helper.mjs';\nvar __default__ = defineComponent({\n  name: \"ElVisuallyHidden\"\n});\nvar _sfc_main = /* @__PURE__ */defineComponent(_objectSpread(_objectSpread({}, __default__), {}, {\n  props: visualHiddenProps,\n  setup(__props) {\n    var props = __props;\n    var computedStyle = computed(function () {\n      return [props.style, {\n        position: \"absolute\",\n        border: 0,\n        width: 1,\n        height: 1,\n        padding: 0,\n        margin: -1,\n        overflow: \"hidden\",\n        clip: \"rect(0, 0, 0, 0)\",\n        whiteSpace: \"nowrap\",\n        wordWrap: \"normal\"\n      }];\n    });\n    return function (_ctx, _cache) {\n      return openBlock(), createElementBlock(\"span\", mergeProps(_ctx.$attrs, {\n        style: unref(computedStyle)\n      }), [renderSlot(_ctx.$slots, \"default\")], 16);\n    };\n  }\n}));\nvar ElVisuallyHidden = /* @__PURE__ */_export_sfc(_sfc_main, [[\"__file\", \"visual-hidden.vue\"]]);\nexport { ElVisuallyHidden as default };", "map": {"version": 3, "names": ["name", "computedStyle", "computed", "props", "style", "position", "border", "width", "height", "padding", "margin", "overflow", "clip", "whiteSpace", "wordWrap"], "sources": ["../../../../../../packages/components/visual-hidden/src/visual-hidden.vue"], "sourcesContent": ["<template>\n  <span v-bind=\"$attrs\" :style=\"computedStyle\">\n    <slot />\n  </span>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { visualHiddenProps } from './visual-hidden'\nimport type { StyleValue } from 'vue'\nconst props = defineProps(visualHiddenProps)\n\ndefineOptions({\n  name: 'ElVisuallyHidden',\n})\n\nconst computedStyle = computed<StyleValue>(() => {\n  return [\n    props.style,\n    {\n      position: 'absolute',\n      border: 0,\n      width: 1,\n      height: 1,\n      padding: 0,\n      margin: -1,\n      overflow: 'hidden',\n      clip: 'rect(0, 0, 0, 0)',\n      whiteSpace: 'nowrap',\n      wordWrap: 'normal',\n    },\n  ]\n})\n</script>\n"], "mappings": ";;;;;;;;iCAYc;EACZA,IAAM;AACR;;;;;IAEM,IAAAC,aAAA,GAAgBC,QAAA,CAAqB,YAAM;MACxC,QACLC,KAAM,CAAAC,KAAA,EACN;QACEC,QAAU;QACVC,MAAQ;QACRC,KAAO;QACPC,MAAQ;QACRC,OAAS;QACTC,MAAQ;QACRC,QAAU;QACVC,IAAM;QACNC,UAAY;QACZC,QAAU;MAAA,CACZ,CACF;IAAA,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}