{"ast": null, "code": "import { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, vShow as _vShow, withDirectives as _withDirectives, Transition as _Transition, openBlock as _openBlock, createBlock as _createBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"global-system-verify-wrapper\"\n};\nvar _hoisted_2 = {\n  class: \"global-system-verify\"\n};\nvar _hoisted_3 = {\n  class: \"global-system-verify-item\"\n};\nvar _hoisted_4 = {\n  class: \"global-system-verify-content\"\n};\nvar _hoisted_5 = {\n  class: \"global-system-verify-item\"\n};\nvar _hoisted_6 = {\n  class: \"global-system-verify-content\"\n};\nvar _hoisted_7 = {\n  class: \"global-system-verify-item\"\n};\nvar _hoisted_8 = {\n  class: \"global-system-verify-content\"\n};\nvar _hoisted_9 = {\n  class: \"global-system-verify-item\"\n};\nvar _hoisted_10 = {\n  class: \"global-system-verify-content\"\n};\nvar _hoisted_11 = {\n  class: \"global-system-verify-item\"\n};\nvar _hoisted_12 = {\n  class: \"global-system-verify-content\"\n};\nvar _hoisted_13 = {\n  class: \"global-system-verify-button\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  var _component_el_config_provider = _resolveComponent(\"el-config-provider\");\n  return _openBlock(), _createBlock(_component_el_config_provider, {\n    locale: $setup.locale,\n    namespace: \"zy-el\"\n  }, {\n    default: _withCtx(function () {\n      return [_createElementVNode(\"div\", _hoisted_1, [_createVNode(_Transition, {\n        name: \"global-system-verify-fade\",\n        persisted: \"\"\n      }, {\n        default: _withCtx(function () {\n          return [_withDirectives(_createElementVNode(\"div\", _hoisted_2, [_cache[9] || (_cache[9] = _createElementVNode(\"div\", {\n            class: \"global-system-verify-title\"\n          }, \"正宇软件正版授权\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_3, [_cache[2] || (_cache[2] = _createElementVNode(\"div\", {\n            class: \"global-system-verify-label\"\n          }, \"MAC地址：\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_4, _toDisplayString($setup.details.macs), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_5, [_cache[3] || (_cache[3] = _createElementVNode(\"div\", {\n            class: \"global-system-verify-label\"\n          }, \"CPU编号：\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_6, _toDisplayString($setup.details.cpuIds), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_7, [_cache[4] || (_cache[4] = _createElementVNode(\"div\", {\n            class: \"global-system-verify-label\"\n          }, \"硬盘编号：\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_8, _toDisplayString($setup.details.hardNumbers), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_9, [_cache[5] || (_cache[5] = _createElementVNode(\"div\", {\n            class: \"global-system-verify-label\"\n          }, \"数据库签名：\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_10, _toDisplayString($setup.details.databaseSign), 1 /* TEXT */)]), _createElementVNode(\"div\", _hoisted_11, [_cache[6] || (_cache[6] = _createElementVNode(\"div\", {\n            class: \"global-system-verify-label\"\n          }, \"授权码：\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_12, [_createVNode(_component_el_input, {\n            modelValue: $setup.licence,\n            \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n              return $setup.licence = $event;\n            }),\n            type: \"textarea\",\n            rows: 9,\n            placeholder: \"请输入授权码\"\n          }, null, 8 /* PROPS */, [\"modelValue\"])])]), _createElementVNode(\"div\", _hoisted_13, [_createVNode(_component_el_button, {\n            onClick: _cache[1] || (_cache[1] = function ($event) {\n              return $setup.handleCopy($setup.copyText);\n            })\n          }, {\n            default: _withCtx(function () {\n              return _cache[7] || (_cache[7] = [_createTextVNode(\"复制授权所需信息\")]);\n            }),\n            _: 1 /* STABLE */\n          }), _createVNode(_component_el_button, {\n            type: \"primary\",\n            onClick: $setup.licenceUpdate\n          }, {\n            default: _withCtx(function () {\n              return _cache[8] || (_cache[8] = [_createTextVNode(\"更新授权码\")]);\n            }),\n            _: 1 /* STABLE */\n          })])], 512 /* NEED_PATCH */), [[_vShow, $setup.visible]])];\n        }),\n        _: 1 /* STABLE */\n      })])];\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"locale\"]);\n}", "map": {"version": 3, "names": ["class", "_createBlock", "_component_el_config_provider", "locale", "$setup", "namespace", "default", "_withCtx", "_createElementVNode", "_hoisted_1", "_createVNode", "_Transition", "name", "persisted", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_toDisplayString", "details", "macs", "_hoisted_5", "_hoisted_6", "cpuIds", "_hoisted_7", "_hoisted_8", "hardNumbers", "_hoisted_9", "_hoisted_10", "databaseSign", "_hoisted_11", "_hoisted_12", "_component_el_input", "modelValue", "licence", "_cache", "$event", "type", "rows", "placeholder", "_hoisted_13", "_component_el_button", "onClick", "handleCopy", "copyText", "_createTextVNode", "_", "licenceUpdate", "visible"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\common\\components\\global-system-verify\\global-system-verify.vue"], "sourcesContent": ["<template>\r\n  <el-config-provider :locale=\"locale\" namespace=\"zy-el\">\r\n    <div class=\"global-system-verify-wrapper\">\r\n      <transition name=\"global-system-verify-fade\">\r\n        <div class=\"global-system-verify\" v-show=\"visible\">\r\n          <div class=\"global-system-verify-title\">正宇软件正版授权</div>\r\n          <div class=\"global-system-verify-item\">\r\n            <div class=\"global-system-verify-label\">MAC地址：</div>\r\n            <div class=\"global-system-verify-content\">{{ details.macs }}</div>\r\n          </div>\r\n          <div class=\"global-system-verify-item\">\r\n            <div class=\"global-system-verify-label\">CPU编号：</div>\r\n            <div class=\"global-system-verify-content\">{{ details.cpuIds }}</div>\r\n          </div>\r\n          <div class=\"global-system-verify-item\">\r\n            <div class=\"global-system-verify-label\">硬盘编号：</div>\r\n            <div class=\"global-system-verify-content\">{{ details.hardNumbers }}</div>\r\n          </div>\r\n          <div class=\"global-system-verify-item\">\r\n            <div class=\"global-system-verify-label\">数据库签名：</div>\r\n            <div class=\"global-system-verify-content\">{{ details.databaseSign }}</div>\r\n          </div>\r\n          <div class=\"global-system-verify-item\">\r\n            <div class=\"global-system-verify-label\">授权码：</div>\r\n            <div class=\"global-system-verify-content\">\r\n              <el-input v-model=\"licence\" type=\"textarea\" :rows=\"9\" placeholder=\"请输入授权码\" />\r\n            </div>\r\n          </div>\r\n          <div class=\"global-system-verify-button\">\r\n            <el-button @click=\"handleCopy(copyText)\">复制授权所需信息</el-button>\r\n            <el-button type=\"primary\" @click=\"licenceUpdate\">更新授权码</el-button>\r\n          </div>\r\n        </div>\r\n      </transition>\r\n    </div>\r\n  </el-config-provider>\r\n</template>\r\n\r\n<script>\r\nexport default { name: 'GlobalSystemVerify' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport zhCn from 'element-plus/dist/locale/zh-cn.mjs'\r\nimport { ElMessage } from 'element-plus'\r\nconst props = defineProps({ closeCallback: { type: Function } })\r\n\r\nconst locale = zhCn\r\nconst licence = ref('')\r\nconst details = ref({})\r\nconst visible = ref(false)\r\nconst copyText = ref('')\r\n\r\nonMounted(() => {\r\n  visible.value = true\r\n  licenceInfo()\r\n})\r\n\r\nconst licenceInfo = async () => {\r\n  const { data } = await api.licenceInfo()\r\n  licence.value = data.licence\r\n  details.value = data\r\n  copyText.value = `MAC地址：${data.macs}\\nCPU编号：${data.cpuIds}\\n硬盘编号：${data.hardNumbers}\\n数据库签名： ${data.databaseSign}\\n授权码：${data.licence}`\r\n}\r\nconst handleCopy = (value) => {\r\n  if (!value) return ElMessage({ message: '无复制内容', type: 'warning' })\r\n  const textarea = document.createElement('textarea')\r\n  textarea.readOnly = 'readonly'\r\n  textarea.style.position = 'absolute'\r\n  textarea.style.left = '-9999px'\r\n  textarea.value = value\r\n  document.body.appendChild(textarea)\r\n  textarea.select()\r\n  const result = document.execCommand('Copy')\r\n  if (result) ElMessage({ message: '复制成功', type: 'success' })\r\n  document.body.removeChild(textarea)\r\n}\r\nconst licenceUpdate = async () => {\r\n  const { code } = await api.licenceUpdate({ licence: licence.value })\r\n  if (code === 200) {\r\n    ElMessage({ type: 'success', message: '更新成功' })\r\n    handleCancel()\r\n  }\r\n}\r\nconst handleCancel = () => {\r\n  visible.value = false\r\n  setTimeout(() => {\r\n    props.closeCallback()\r\n  }, 300)\r\n}\r\n</script>\r\n<style lang=\"scss\">\r\n.global-system-verify-wrapper {\r\n  position: fixed;\r\n  top: 0;\r\n  bottom: 0;\r\n  right: 0;\r\n  left: 0;\r\n  background: rgba(0, 0, 0, 0.5);\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  z-index: 1000;\r\n\r\n  .global-system-verify {\r\n    width: 880px;\r\n    padding: 40px;\r\n    background: #fff;\r\n    border-radius: var(--el-border-radius-base);\r\n    box-shadow: var(--zy-el-box-shadow);\r\n    backface-visibility: hidden;\r\n\r\n    .global-system-verify-title {\r\n      font-weight: bold;\r\n      text-align: center;\r\n      font-size: var(--zy-title-font-size);\r\n      line-height: var(--zy-line-height);\r\n      padding: var(--zy-distance-four) var(--zy-distance-one);\r\n      border-bottom: 1px solid var(--zy-el-color-primary);\r\n    }\r\n\r\n    .global-system-verify-item {\r\n      display: flex;\r\n      padding-top: 12px;\r\n      line-height: var(--zy-line-height);\r\n      font-size: var(--zy-name-font-size);\r\n\r\n      .global-system-verify-label {\r\n        width: 120px;\r\n        text-align: right;\r\n      }\r\n\r\n      .global-system-verify-content {\r\n        width: calc(100% - 120px);\r\n      }\r\n    }\r\n\r\n    .global-system-verify-button {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      padding: 20px 200px 0 200px;\r\n    }\r\n  }\r\n\r\n  .global-system-verify-fade-enter-active {\r\n    -webkit-animation: global-system-verify-fade-in 0.3s;\r\n    animation: global-system-verify-fade-in 0.3s;\r\n  }\r\n\r\n  .global-system-verify-fade-leave-active {\r\n    -webkit-animation: global-system-verify-fade-out 0.3s;\r\n    animation: global-system-verify-fade-out 0.3s;\r\n  }\r\n\r\n  @keyframes global-system-verify-fade-in {\r\n    0% {\r\n      transform: translate3d(0, -20px, 0);\r\n      opacity: 0;\r\n    }\r\n\r\n    100% {\r\n      transform: translate3d(0, 0, 0);\r\n      opacity: 1;\r\n    }\r\n  }\r\n\r\n  @keyframes global-system-verify-fade-out {\r\n    0% {\r\n      transform: translate3d(0, 0, 0);\r\n      opacity: 1;\r\n    }\r\n\r\n    100% {\r\n      transform: translate3d(0, -20px, 0);\r\n      opacity: 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAESA,KAAK,EAAC;AAA8B;;EAEhCA,KAAK,EAAC;AAAsB;;EAE1BA,KAAK,EAAC;AAA2B;;EAE/BA,KAAK,EAAC;AAA8B;;EAEtCA,KAAK,EAAC;AAA2B;;EAE/BA,KAAK,EAAC;AAA8B;;EAEtCA,KAAK,EAAC;AAA2B;;EAE/BA,KAAK,EAAC;AAA8B;;EAEtCA,KAAK,EAAC;AAA2B;;EAE/BA,KAAK,EAAC;AAA8B;;EAEtCA,KAAK,EAAC;AAA2B;;EAE/BA,KAAK,EAAC;AAA8B;;EAItCA,KAAK,EAAC;AAA6B;;;;;uBA3BhDC,YAAA,CAkCqBC,6BAAA;IAlCAC,MAAM,EAAEC,MAAA,CAAAD,MAAM;IAAEE,SAAS,EAAC;;IADjDC,OAAA,EAAAC,QAAA,CAEI;MAAA,OAgCM,CAhCNC,mBAAA,CAgCM,OAhCNC,UAgCM,GA/BJC,YAAA,CA8BaC,WAAA;QA9BDC,IAAI,EAAC,2BAA2B;QAA5CC,SA8Ba,EA9Bb;;QAHNP,OAAA,EAAAC,QAAA,CAIQ;UAAA,OA4BM,C,gBA5BNC,mBAAA,CA4BM,OA5BNM,UA4BM,G,0BA3BJN,mBAAA,CAAsD;YAAjDR,KAAK,EAAC;UAA4B,GAAC,UAAQ,sBAChDQ,mBAAA,CAGM,OAHNO,UAGM,G,0BAFJP,mBAAA,CAAoD;YAA/CR,KAAK,EAAC;UAA4B,GAAC,QAAM,sBAC9CQ,mBAAA,CAAkE,OAAlEQ,UAAkE,EAAAC,gBAAA,CAArBb,MAAA,CAAAc,OAAO,CAACC,IAAI,iB,GAE3DX,mBAAA,CAGM,OAHNY,UAGM,G,0BAFJZ,mBAAA,CAAoD;YAA/CR,KAAK,EAAC;UAA4B,GAAC,QAAM,sBAC9CQ,mBAAA,CAAoE,OAApEa,UAAoE,EAAAJ,gBAAA,CAAvBb,MAAA,CAAAc,OAAO,CAACI,MAAM,iB,GAE7Dd,mBAAA,CAGM,OAHNe,UAGM,G,0BAFJf,mBAAA,CAAmD;YAA9CR,KAAK,EAAC;UAA4B,GAAC,OAAK,sBAC7CQ,mBAAA,CAAyE,OAAzEgB,UAAyE,EAAAP,gBAAA,CAA5Bb,MAAA,CAAAc,OAAO,CAACO,WAAW,iB,GAElEjB,mBAAA,CAGM,OAHNkB,UAGM,G,0BAFJlB,mBAAA,CAAoD;YAA/CR,KAAK,EAAC;UAA4B,GAAC,QAAM,sBAC9CQ,mBAAA,CAA0E,OAA1EmB,WAA0E,EAAAV,gBAAA,CAA7Bb,MAAA,CAAAc,OAAO,CAACU,YAAY,iB,GAEnEpB,mBAAA,CAKM,OALNqB,WAKM,G,0BAJJrB,mBAAA,CAAkD;YAA7CR,KAAK,EAAC;UAA4B,GAAC,MAAI,sBAC5CQ,mBAAA,CAEM,OAFNsB,WAEM,GADJpB,YAAA,CAA6EqB,mBAAA;YAzB3FC,UAAA,EAyBiC5B,MAAA,CAAA6B,OAAO;YAzBxC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAyBiC/B,MAAA,CAAA6B,OAAO,GAAAE,MAAA;YAAA;YAAEC,IAAI,EAAC,UAAU;YAAEC,IAAI,EAAE,CAAC;YAAEC,WAAW,EAAC;uDAGtE9B,mBAAA,CAGM,OAHN+B,WAGM,GAFJ7B,YAAA,CAA6D8B,oBAAA;YAAjDC,OAAK,EAAAP,MAAA,QAAAA,MAAA,gBAAAC,MAAA;cAAA,OAAE/B,MAAA,CAAAsC,UAAU,CAACtC,MAAA,CAAAuC,QAAQ;YAAA;;YA7BlDrC,OAAA,EAAAC,QAAA,CA6BqD;cAAA,OAAQ2B,MAAA,QAAAA,MAAA,OA7B7DU,gBAAA,CA6BqD,UAAQ,E;;YA7B7DC,CAAA;cA8BYnC,YAAA,CAAkE8B,oBAAA;YAAvDJ,IAAI,EAAC,SAAS;YAAEK,OAAK,EAAErC,MAAA,CAAA0C;;YA9B9CxC,OAAA,EAAAC,QAAA,CA8B6D;cAAA,OAAK2B,MAAA,QAAAA,MAAA,OA9BlEU,gBAAA,CA8B6D,OAAK,E;;YA9BlEC,CAAA;kDAIkDzC,MAAA,CAAA2C,OAAO,E;;QAJzDF,CAAA;;;IAAAA,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}