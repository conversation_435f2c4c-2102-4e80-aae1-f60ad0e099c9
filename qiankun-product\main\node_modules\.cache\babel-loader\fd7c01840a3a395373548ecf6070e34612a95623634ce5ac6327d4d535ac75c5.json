{"ast": null, "code": "import api from '@/api';\nimport { computed } from 'vue';\nvar __default__ = {\n  name: 'PreviewPic'\n};\nexport default /*@__PURE__*/Object.assign(__default__, {\n  props: {\n    id: {\n      type: String,\n      default: ''\n    },\n    type: {\n      type: String,\n      default: ''\n    }\n  },\n  setup(__props, _ref) {\n    var __expose = _ref.expose;\n    __expose();\n    var props = __props;\n    var fileUrl = computed(function () {\n      return [`${api.fileURL(props.id + '.' + props.type)}`];\n    });\n    var __returned__ = {\n      props,\n      fileUrl,\n      get api() {\n        return api;\n      },\n      computed\n    };\n    Object.defineProperty(__returned__, '__isScriptSetup', {\n      enumerable: false,\n      value: true\n    });\n    return __returned__;\n  }\n});", "map": {"version": 3, "names": ["api", "computed", "__default__", "name", "props", "__props", "fileUrl", "fileURL", "id", "type"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/main/src/components/global-file-preview/components/preview-pic.vue"], "sourcesContent": ["<template>\r\n  <div class=\"preview-pic\">\r\n    <el-image-viewer :url-list=\"fileUrl\"></el-image-viewer>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'PreviewPic' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { computed } from 'vue'\r\nconst props = defineProps({ id: { type: String, default: '' }, type: { type: String, default: '' } })\r\nconst fileUrl = computed(() => [`${api.fileURL(props.id + '.' + props.type)}`])\r\n</script>\r\n<style lang=\"scss\">\r\n.preview-pic {\r\n  width: 100%;\r\n  height: 100%;\r\n  position: relative;\r\n\r\n  .zy-el-image-viewer__wrapper {\r\n    position: absolute;\r\n    overflow: hidden;\r\n\r\n    .zy-el-image-viewer__mask {\r\n      background-color: transparent;\r\n    }\r\n\r\n    .zy-el-image-viewer__close {\r\n      display: none;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": "AASA,OAAOA,GAAG,MAAM,OAAO;AACvB,SAASC,QAAQ,QAAQ,KAAK;AAJ9B,IAAAC,WAAA,GAAe;EAAEC,IAAI,EAAE;AAAa,CAAC;;;;;;;;;;;;;;;IAKrC,IAAMC,KAAK,GAAGC,OAAuF;IACrG,IAAMC,OAAO,GAAGL,QAAQ,CAAC;MAAA,OAAM,CAAC,GAAGD,GAAG,CAACO,OAAO,CAACH,KAAK,CAACI,EAAE,GAAG,GAAG,GAAGJ,KAAK,CAACK,IAAI,CAAC,EAAE,CAAC;IAAA,EAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}