{"ast": null, "code": "import { createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalAiChatData\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createVNode($setup[\"GlobalMarkdown\"], {\n    content: $setup.details.markdownContent\n  }, null, 8 /* PROPS */, [\"content\"])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createVNode", "$setup", "content", "details", "markdownContent"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalAiChat\\GlobalAiChatData.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalAiChatData\">\r\n    <GlobalMarkdown :content=\"details.markdownContent\" />\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalAiChatData' }\r\n</script>\r\n<script setup>\r\nimport { computed, defineAsyncComponent } from 'vue'\r\nconst GlobalMarkdown = defineAsyncComponent(() => import('common/components/global-markdown/global-markdown.vue'))\r\nconst props = defineProps({ data: { type: Object, default: () => ({}) } })\r\nconst details = computed(() => props.data)\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalAiChatData {\r\n  width: 680px;\r\n  height: calc(85vh - 52px);\r\n  padding: 20px 40px;\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAkB;;uBAA7BC,mBAAA,CAEM,OAFNC,UAEM,GADJC,YAAA,CAAqDC,MAAA;IAApCC,OAAO,EAAED,MAAA,CAAAE,OAAO,CAACC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}