{"ast": null, "code": "/*\nLanguage: C++\nCategory: common, system\nWebsite: https://isocpp.org\n*/\n\n/** @type LanguageFn */\nfunction cpp(hljs) {\n  var regex = hljs.regex;\n  // added for historic reasons because `hljs.C_LINE_COMMENT_MODE` does\n  // not include such support nor can we be sure all the grammars depending\n  // on it would desire this behavior\n  var C_LINE_COMMENT_MODE = hljs.COMMENT('//', '$', {\n    contains: [{\n      begin: /\\\\\\n/\n    }]\n  });\n  var DECLTYPE_AUTO_RE = 'decltype\\\\(auto\\\\)';\n  var NAMESPACE_RE = '[a-zA-Z_]\\\\w*::';\n  var TEMPLATE_ARGUMENT_RE = '<[^<>]+>';\n  var FUNCTION_TYPE_RE = '(?!struct)(' + DECLTYPE_AUTO_RE + '|' + regex.optional(NAMESPACE_RE) + '[a-zA-Z_]\\\\w*' + regex.optional(TEMPLATE_ARGUMENT_RE) + ')';\n  var CPP_PRIMITIVE_TYPES = {\n    className: 'type',\n    begin: '\\\\b[a-z\\\\d_]*_t\\\\b'\n  };\n\n  // https://en.cppreference.com/w/cpp/language/escape\n  // \\\\ \\x \\xFF \\u2837 \\u00323747 \\374\n  var CHARACTER_ESCAPES = '\\\\\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\\\S)';\n  var STRINGS = {\n    className: 'string',\n    variants: [{\n      begin: '(u8?|U|L)?\"',\n      end: '\"',\n      illegal: '\\\\n',\n      contains: [hljs.BACKSLASH_ESCAPE]\n    }, {\n      begin: '(u8?|U|L)?\\'(' + CHARACTER_ESCAPES + '|.)',\n      end: '\\'',\n      illegal: '.'\n    }, hljs.END_SAME_AS_BEGIN({\n      begin: /(?:u8?|U|L)?R\"([^()\\\\ ]{0,16})\\(/,\n      end: /\\)([^()\\\\ ]{0,16})\"/\n    })]\n  };\n  var NUMBERS = {\n    className: 'number',\n    variants: [\n    // Floating-point literal.\n    {\n      begin: \"[+-]?(?:\" // Leading sign.\n      // Decimal.\n      + \"(?:\" + \"[0-9](?:'?[0-9])*\\\\.(?:[0-9](?:'?[0-9])*)?\" + \"|\\\\.[0-9](?:'?[0-9])*\" + \")(?:[Ee][+-]?[0-9](?:'?[0-9])*)?\" + \"|[0-9](?:'?[0-9])*[Ee][+-]?[0-9](?:'?[0-9])*\"\n      // Hexadecimal.\n      + \"|0[Xx](?:\" + \"[0-9A-Fa-f](?:'?[0-9A-Fa-f])*(?:\\\\.(?:[0-9A-Fa-f](?:'?[0-9A-Fa-f])*)?)?\" + \"|\\\\.[0-9A-Fa-f](?:'?[0-9A-Fa-f])*\" + \")[Pp][+-]?[0-9](?:'?[0-9])*\" + \")(?:\" // Literal suffixes.\n      + \"[Ff](?:16|32|64|128)?\" + \"|(BF|bf)16\" + \"|[Ll]\" + \"|\" // Literal suffix is optional.\n      + \")\"\n    },\n    // Integer literal.\n    {\n      begin: \"[+-]?\\\\b(?:\" // Leading sign.\n      + \"0[Bb][01](?:'?[01])*\" // Binary.\n      + \"|0[Xx][0-9A-Fa-f](?:'?[0-9A-Fa-f])*\" // Hexadecimal.\n      + \"|0(?:'?[0-7])*\" // Octal or just a lone zero.\n      + \"|[1-9](?:'?[0-9])*\" // Decimal.\n      + \")(?:\" // Literal suffixes.\n      + \"[Uu](?:LL?|ll?)\" + \"|[Uu][Zz]?\" + \"|(?:LL?|ll?)[Uu]?\" + \"|[Zz][Uu]\" + \"|\" // Literal suffix is optional.\n      + \")\"\n      // Note: there are user-defined literal suffixes too, but perhaps having the custom suffix not part of the\n      // literal highlight actually makes it stand out more.\n    }],\n    relevance: 0\n  };\n  var PREPROCESSOR = {\n    className: 'meta',\n    begin: /#\\s*[a-z]+\\b/,\n    end: /$/,\n    keywords: {\n      keyword: 'if else elif endif define undef warning error line ' + 'pragma _Pragma ifdef ifndef include'\n    },\n    contains: [{\n      begin: /\\\\\\n/,\n      relevance: 0\n    }, hljs.inherit(STRINGS, {\n      className: 'string'\n    }), {\n      className: 'string',\n      begin: /<.*?>/\n    }, C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE]\n  };\n  var TITLE_MODE = {\n    className: 'title',\n    begin: regex.optional(NAMESPACE_RE) + hljs.IDENT_RE,\n    relevance: 0\n  };\n  var FUNCTION_TITLE = regex.optional(NAMESPACE_RE) + hljs.IDENT_RE + '\\\\s*\\\\(';\n\n  // https://en.cppreference.com/w/cpp/keyword\n  var RESERVED_KEYWORDS = ['alignas', 'alignof', 'and', 'and_eq', 'asm', 'atomic_cancel', 'atomic_commit', 'atomic_noexcept', 'auto', 'bitand', 'bitor', 'break', 'case', 'catch', 'class', 'co_await', 'co_return', 'co_yield', 'compl', 'concept', 'const_cast|10', 'consteval', 'constexpr', 'constinit', 'continue', 'decltype', 'default', 'delete', 'do', 'dynamic_cast|10', 'else', 'enum', 'explicit', 'export', 'extern', 'false', 'final', 'for', 'friend', 'goto', 'if', 'import', 'inline', 'module', 'mutable', 'namespace', 'new', 'noexcept', 'not', 'not_eq', 'nullptr', 'operator', 'or', 'or_eq', 'override', 'private', 'protected', 'public', 'reflexpr', 'register', 'reinterpret_cast|10', 'requires', 'return', 'sizeof', 'static_assert', 'static_cast|10', 'struct', 'switch', 'synchronized', 'template', 'this', 'thread_local', 'throw', 'transaction_safe', 'transaction_safe_dynamic', 'true', 'try', 'typedef', 'typeid', 'typename', 'union', 'using', 'virtual', 'volatile', 'while', 'xor', 'xor_eq'];\n\n  // https://en.cppreference.com/w/cpp/keyword\n  var RESERVED_TYPES = ['bool', 'char', 'char16_t', 'char32_t', 'char8_t', 'double', 'float', 'int', 'long', 'short', 'void', 'wchar_t', 'unsigned', 'signed', 'const', 'static'];\n  var TYPE_HINTS = ['any', 'auto_ptr', 'barrier', 'binary_semaphore', 'bitset', 'complex', 'condition_variable', 'condition_variable_any', 'counting_semaphore', 'deque', 'false_type', 'flat_map', 'flat_set', 'future', 'imaginary', 'initializer_list', 'istringstream', 'jthread', 'latch', 'lock_guard', 'multimap', 'multiset', 'mutex', 'optional', 'ostringstream', 'packaged_task', 'pair', 'promise', 'priority_queue', 'queue', 'recursive_mutex', 'recursive_timed_mutex', 'scoped_lock', 'set', 'shared_future', 'shared_lock', 'shared_mutex', 'shared_timed_mutex', 'shared_ptr', 'stack', 'string_view', 'stringstream', 'timed_mutex', 'thread', 'true_type', 'tuple', 'unique_lock', 'unique_ptr', 'unordered_map', 'unordered_multimap', 'unordered_multiset', 'unordered_set', 'variant', 'vector', 'weak_ptr', 'wstring', 'wstring_view'];\n  var FUNCTION_HINTS = ['abort', 'abs', 'acos', 'apply', 'as_const', 'asin', 'atan', 'atan2', 'calloc', 'ceil', 'cerr', 'cin', 'clog', 'cos', 'cosh', 'cout', 'declval', 'endl', 'exchange', 'exit', 'exp', 'fabs', 'floor', 'fmod', 'forward', 'fprintf', 'fputs', 'free', 'frexp', 'fscanf', 'future', 'invoke', 'isalnum', 'isalpha', 'iscntrl', 'isdigit', 'isgraph', 'islower', 'isprint', 'ispunct', 'isspace', 'isupper', 'isxdigit', 'labs', 'launder', 'ldexp', 'log', 'log10', 'make_pair', 'make_shared', 'make_shared_for_overwrite', 'make_tuple', 'make_unique', 'malloc', 'memchr', 'memcmp', 'memcpy', 'memset', 'modf', 'move', 'pow', 'printf', 'putchar', 'puts', 'realloc', 'scanf', 'sin', 'sinh', 'snprintf', 'sprintf', 'sqrt', 'sscanf', 'std', 'stderr', 'stdin', 'stdout', 'strcat', 'strchr', 'strcmp', 'strcpy', 'strcspn', 'strlen', 'strncat', 'strncmp', 'strncpy', 'strpbrk', 'strrchr', 'strspn', 'strstr', 'swap', 'tan', 'tanh', 'terminate', 'to_underlying', 'tolower', 'toupper', 'vfprintf', 'visit', 'vprintf', 'vsprintf'];\n  var LITERALS = ['NULL', 'false', 'nullopt', 'nullptr', 'true'];\n\n  // https://en.cppreference.com/w/cpp/keyword\n  var BUILT_IN = ['_Pragma'];\n  var CPP_KEYWORDS = {\n    type: RESERVED_TYPES,\n    keyword: RESERVED_KEYWORDS,\n    literal: LITERALS,\n    built_in: BUILT_IN,\n    _type_hints: TYPE_HINTS\n  };\n  var FUNCTION_DISPATCH = {\n    className: 'function.dispatch',\n    relevance: 0,\n    keywords: {\n      // Only for relevance, not highlighting.\n      _hint: FUNCTION_HINTS\n    },\n    begin: regex.concat(/\\b/, /(?!decltype)/, /(?!if)/, /(?!for)/, /(?!switch)/, /(?!while)/, hljs.IDENT_RE, regex.lookahead(/(<[^<>]+>|)\\s*\\(/))\n  };\n  var EXPRESSION_CONTAINS = [FUNCTION_DISPATCH, PREPROCESSOR, CPP_PRIMITIVE_TYPES, C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, NUMBERS, STRINGS];\n  var EXPRESSION_CONTEXT = {\n    // This mode covers expression context where we can't expect a function\n    // definition and shouldn't highlight anything that looks like one:\n    // `return some()`, `else if()`, `(x*sum(1, 2))`\n    variants: [{\n      begin: /=/,\n      end: /;/\n    }, {\n      begin: /\\(/,\n      end: /\\)/\n    }, {\n      beginKeywords: 'new throw return else',\n      end: /;/\n    }],\n    keywords: CPP_KEYWORDS,\n    contains: EXPRESSION_CONTAINS.concat([{\n      begin: /\\(/,\n      end: /\\)/,\n      keywords: CPP_KEYWORDS,\n      contains: EXPRESSION_CONTAINS.concat(['self']),\n      relevance: 0\n    }]),\n    relevance: 0\n  };\n  var FUNCTION_DECLARATION = {\n    className: 'function',\n    begin: '(' + FUNCTION_TYPE_RE + '[\\\\*&\\\\s]+)+' + FUNCTION_TITLE,\n    returnBegin: true,\n    end: /[{;=]/,\n    excludeEnd: true,\n    keywords: CPP_KEYWORDS,\n    illegal: /[^\\w\\s\\*&:<>.]/,\n    contains: [{\n      // to prevent it from being confused as the function title\n      begin: DECLTYPE_AUTO_RE,\n      keywords: CPP_KEYWORDS,\n      relevance: 0\n    }, {\n      begin: FUNCTION_TITLE,\n      returnBegin: true,\n      contains: [TITLE_MODE],\n      relevance: 0\n    },\n    // needed because we do not have look-behind on the below rule\n    // to prevent it from grabbing the final : in a :: pair\n    {\n      begin: /::/,\n      relevance: 0\n    },\n    // initializers\n    {\n      begin: /:/,\n      endsWithParent: true,\n      contains: [STRINGS, NUMBERS]\n    },\n    // allow for multiple declarations, e.g.:\n    // extern void f(int), g(char);\n    {\n      relevance: 0,\n      match: /,/\n    }, {\n      className: 'params',\n      begin: /\\(/,\n      end: /\\)/,\n      keywords: CPP_KEYWORDS,\n      relevance: 0,\n      contains: [C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, STRINGS, NUMBERS, CPP_PRIMITIVE_TYPES,\n      // Count matching parentheses.\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: CPP_KEYWORDS,\n        relevance: 0,\n        contains: ['self', C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, STRINGS, NUMBERS, CPP_PRIMITIVE_TYPES]\n      }]\n    }, CPP_PRIMITIVE_TYPES, C_LINE_COMMENT_MODE, hljs.C_BLOCK_COMMENT_MODE, PREPROCESSOR]\n  };\n  return {\n    name: 'C++',\n    aliases: ['cc', 'c++', 'h++', 'hpp', 'hh', 'hxx', 'cxx'],\n    keywords: CPP_KEYWORDS,\n    illegal: '</',\n    classNameAliases: {\n      'function.dispatch': 'built_in'\n    },\n    contains: [].concat(EXPRESSION_CONTEXT, FUNCTION_DECLARATION, FUNCTION_DISPATCH, EXPRESSION_CONTAINS, [PREPROCESSOR, {\n      // containers: ie, `vector <int> rooms (9);`\n      begin: '\\\\b(deque|list|queue|priority_queue|pair|stack|vector|map|set|bitset|multiset|multimap|unordered_map|unordered_set|unordered_multiset|unordered_multimap|array|tuple|optional|variant|function|flat_map|flat_set)\\\\s*<(?!<)',\n      end: '>',\n      keywords: CPP_KEYWORDS,\n      contains: ['self', CPP_PRIMITIVE_TYPES]\n    }, {\n      begin: hljs.IDENT_RE + '::',\n      keywords: CPP_KEYWORDS\n    }, {\n      match: [\n      // extra complexity to deal with `enum class` and `enum struct`\n      /\\b(?:enum(?:\\s+(?:class|struct))?|class|struct|union)/, /\\s+/, /\\w+/],\n      className: {\n        1: 'keyword',\n        3: 'title.class'\n      }\n    }])\n  };\n}\nexport { cpp as default };", "map": {"version": 3, "names": ["cpp", "hljs", "regex", "C_LINE_COMMENT_MODE", "COMMENT", "contains", "begin", "DECLTYPE_AUTO_RE", "NAMESPACE_RE", "TEMPLATE_ARGUMENT_RE", "FUNCTION_TYPE_RE", "optional", "CPP_PRIMITIVE_TYPES", "className", "CHARACTER_ESCAPES", "STRINGS", "variants", "end", "illegal", "BACKSLASH_ESCAPE", "END_SAME_AS_BEGIN", "NUMBERS", "relevance", "PREPROCESSOR", "keywords", "keyword", "inherit", "C_BLOCK_COMMENT_MODE", "TITLE_MODE", "IDENT_RE", "FUNCTION_TITLE", "RESERVED_KEYWORDS", "RESERVED_TYPES", "TYPE_HINTS", "FUNCTION_HINTS", "LITERALS", "BUILT_IN", "CPP_KEYWORDS", "type", "literal", "built_in", "_type_hints", "FUNCTION_DISPATCH", "_hint", "concat", "<PERSON><PERSON><PERSON>", "EXPRESSION_CONTAINS", "EXPRESSION_CONTEXT", "beginKeywords", "FUNCTION_DECLARATION", "returnBegin", "excludeEnd", "endsWithParent", "match", "name", "aliases", "classNameAliases", "default"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/highlight.js@11.11.1/node_modules/highlight.js/es/languages/cpp.js"], "sourcesContent": ["/*\nLanguage: C++\nCategory: common, system\nWebsite: https://isocpp.org\n*/\n\n/** @type LanguageFn */\nfunction cpp(hljs) {\n  const regex = hljs.regex;\n  // added for historic reasons because `hljs.C_LINE_COMMENT_MODE` does\n  // not include such support nor can we be sure all the grammars depending\n  // on it would desire this behavior\n  const C_LINE_COMMENT_MODE = hljs.COMMENT('//', '$', { contains: [ { begin: /\\\\\\n/ } ] });\n  const DECLTYPE_AUTO_RE = 'decltype\\\\(auto\\\\)';\n  const NAMESPACE_RE = '[a-zA-Z_]\\\\w*::';\n  const TEMPLATE_ARGUMENT_RE = '<[^<>]+>';\n  const FUNCTION_TYPE_RE = '(?!struct)('\n    + DECLTYPE_AUTO_RE + '|'\n    + regex.optional(NAMESPACE_RE)\n    + '[a-zA-Z_]\\\\w*' + regex.optional(TEMPLATE_ARGUMENT_RE)\n  + ')';\n\n  const CPP_PRIMITIVE_TYPES = {\n    className: 'type',\n    begin: '\\\\b[a-z\\\\d_]*_t\\\\b'\n  };\n\n  // https://en.cppreference.com/w/cpp/language/escape\n  // \\\\ \\x \\xFF \\u2837 \\u00323747 \\374\n  const CHARACTER_ESCAPES = '\\\\\\\\(x[0-9A-Fa-f]{2}|u[0-9A-Fa-f]{4,8}|[0-7]{3}|\\\\S)';\n  const STRINGS = {\n    className: 'string',\n    variants: [\n      {\n        begin: '(u8?|U|L)?\"',\n        end: '\"',\n        illegal: '\\\\n',\n        contains: [ hljs.BACKSLASH_ESCAPE ]\n      },\n      {\n        begin: '(u8?|U|L)?\\'(' + CHARACTER_ESCAPES + '|.)',\n        end: '\\'',\n        illegal: '.'\n      },\n      hljs.END_SAME_AS_BEGIN({\n        begin: /(?:u8?|U|L)?R\"([^()\\\\ ]{0,16})\\(/,\n        end: /\\)([^()\\\\ ]{0,16})\"/\n      })\n    ]\n  };\n\n  const NUMBERS = {\n    className: 'number',\n    variants: [\n      // Floating-point literal.\n      { begin:\n        \"[+-]?(?:\" // Leading sign.\n          // Decimal.\n          + \"(?:\"\n            +\"[0-9](?:'?[0-9])*\\\\.(?:[0-9](?:'?[0-9])*)?\"\n            + \"|\\\\.[0-9](?:'?[0-9])*\"\n          + \")(?:[Ee][+-]?[0-9](?:'?[0-9])*)?\"\n          + \"|[0-9](?:'?[0-9])*[Ee][+-]?[0-9](?:'?[0-9])*\"\n          // Hexadecimal.\n          + \"|0[Xx](?:\"\n            +\"[0-9A-Fa-f](?:'?[0-9A-Fa-f])*(?:\\\\.(?:[0-9A-Fa-f](?:'?[0-9A-Fa-f])*)?)?\"\n            + \"|\\\\.[0-9A-Fa-f](?:'?[0-9A-Fa-f])*\"\n          + \")[Pp][+-]?[0-9](?:'?[0-9])*\"\n        + \")(?:\" // Literal suffixes.\n          + \"[Ff](?:16|32|64|128)?\"\n          + \"|(BF|bf)16\"\n          + \"|[Ll]\"\n          + \"|\" // Literal suffix is optional.\n        + \")\"\n      },\n      // Integer literal.\n      { begin:\n        \"[+-]?\\\\b(?:\" // Leading sign.\n          + \"0[Bb][01](?:'?[01])*\" // Binary.\n          + \"|0[Xx][0-9A-Fa-f](?:'?[0-9A-Fa-f])*\" // Hexadecimal.\n          + \"|0(?:'?[0-7])*\" // Octal or just a lone zero.\n          + \"|[1-9](?:'?[0-9])*\" // Decimal.\n        + \")(?:\" // Literal suffixes.\n          + \"[Uu](?:LL?|ll?)\"\n          + \"|[Uu][Zz]?\"\n          + \"|(?:LL?|ll?)[Uu]?\"\n          + \"|[Zz][Uu]\"\n          + \"|\" // Literal suffix is optional.\n        + \")\"\n        // Note: there are user-defined literal suffixes too, but perhaps having the custom suffix not part of the\n        // literal highlight actually makes it stand out more.\n      }\n    ],\n    relevance: 0\n  };\n\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: /#\\s*[a-z]+\\b/,\n    end: /$/,\n    keywords: { keyword:\n        'if else elif endif define undef warning error line '\n        + 'pragma _Pragma ifdef ifndef include' },\n    contains: [\n      {\n        begin: /\\\\\\n/,\n        relevance: 0\n      },\n      hljs.inherit(STRINGS, { className: 'string' }),\n      {\n        className: 'string',\n        begin: /<.*?>/\n      },\n      C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE\n    ]\n  };\n\n  const TITLE_MODE = {\n    className: 'title',\n    begin: regex.optional(NAMESPACE_RE) + hljs.IDENT_RE,\n    relevance: 0\n  };\n\n  const FUNCTION_TITLE = regex.optional(NAMESPACE_RE) + hljs.IDENT_RE + '\\\\s*\\\\(';\n\n  // https://en.cppreference.com/w/cpp/keyword\n  const RESERVED_KEYWORDS = [\n    'alignas',\n    'alignof',\n    'and',\n    'and_eq',\n    'asm',\n    'atomic_cancel',\n    'atomic_commit',\n    'atomic_noexcept',\n    'auto',\n    'bitand',\n    'bitor',\n    'break',\n    'case',\n    'catch',\n    'class',\n    'co_await',\n    'co_return',\n    'co_yield',\n    'compl',\n    'concept',\n    'const_cast|10',\n    'consteval',\n    'constexpr',\n    'constinit',\n    'continue',\n    'decltype',\n    'default',\n    'delete',\n    'do',\n    'dynamic_cast|10',\n    'else',\n    'enum',\n    'explicit',\n    'export',\n    'extern',\n    'false',\n    'final',\n    'for',\n    'friend',\n    'goto',\n    'if',\n    'import',\n    'inline',\n    'module',\n    'mutable',\n    'namespace',\n    'new',\n    'noexcept',\n    'not',\n    'not_eq',\n    'nullptr',\n    'operator',\n    'or',\n    'or_eq',\n    'override',\n    'private',\n    'protected',\n    'public',\n    'reflexpr',\n    'register',\n    'reinterpret_cast|10',\n    'requires',\n    'return',\n    'sizeof',\n    'static_assert',\n    'static_cast|10',\n    'struct',\n    'switch',\n    'synchronized',\n    'template',\n    'this',\n    'thread_local',\n    'throw',\n    'transaction_safe',\n    'transaction_safe_dynamic',\n    'true',\n    'try',\n    'typedef',\n    'typeid',\n    'typename',\n    'union',\n    'using',\n    'virtual',\n    'volatile',\n    'while',\n    'xor',\n    'xor_eq'\n  ];\n\n  // https://en.cppreference.com/w/cpp/keyword\n  const RESERVED_TYPES = [\n    'bool',\n    'char',\n    'char16_t',\n    'char32_t',\n    'char8_t',\n    'double',\n    'float',\n    'int',\n    'long',\n    'short',\n    'void',\n    'wchar_t',\n    'unsigned',\n    'signed',\n    'const',\n    'static'\n  ];\n\n  const TYPE_HINTS = [\n    'any',\n    'auto_ptr',\n    'barrier',\n    'binary_semaphore',\n    'bitset',\n    'complex',\n    'condition_variable',\n    'condition_variable_any',\n    'counting_semaphore',\n    'deque',\n    'false_type',\n    'flat_map',\n    'flat_set',\n    'future',\n    'imaginary',\n    'initializer_list',\n    'istringstream',\n    'jthread',\n    'latch',\n    'lock_guard',\n    'multimap',\n    'multiset',\n    'mutex',\n    'optional',\n    'ostringstream',\n    'packaged_task',\n    'pair',\n    'promise',\n    'priority_queue',\n    'queue',\n    'recursive_mutex',\n    'recursive_timed_mutex',\n    'scoped_lock',\n    'set',\n    'shared_future',\n    'shared_lock',\n    'shared_mutex',\n    'shared_timed_mutex',\n    'shared_ptr',\n    'stack',\n    'string_view',\n    'stringstream',\n    'timed_mutex',\n    'thread',\n    'true_type',\n    'tuple',\n    'unique_lock',\n    'unique_ptr',\n    'unordered_map',\n    'unordered_multimap',\n    'unordered_multiset',\n    'unordered_set',\n    'variant',\n    'vector',\n    'weak_ptr',\n    'wstring',\n    'wstring_view'\n  ];\n\n  const FUNCTION_HINTS = [\n    'abort',\n    'abs',\n    'acos',\n    'apply',\n    'as_const',\n    'asin',\n    'atan',\n    'atan2',\n    'calloc',\n    'ceil',\n    'cerr',\n    'cin',\n    'clog',\n    'cos',\n    'cosh',\n    'cout',\n    'declval',\n    'endl',\n    'exchange',\n    'exit',\n    'exp',\n    'fabs',\n    'floor',\n    'fmod',\n    'forward',\n    'fprintf',\n    'fputs',\n    'free',\n    'frexp',\n    'fscanf',\n    'future',\n    'invoke',\n    'isalnum',\n    'isalpha',\n    'iscntrl',\n    'isdigit',\n    'isgraph',\n    'islower',\n    'isprint',\n    'ispunct',\n    'isspace',\n    'isupper',\n    'isxdigit',\n    'labs',\n    'launder',\n    'ldexp',\n    'log',\n    'log10',\n    'make_pair',\n    'make_shared',\n    'make_shared_for_overwrite',\n    'make_tuple',\n    'make_unique',\n    'malloc',\n    'memchr',\n    'memcmp',\n    'memcpy',\n    'memset',\n    'modf',\n    'move',\n    'pow',\n    'printf',\n    'putchar',\n    'puts',\n    'realloc',\n    'scanf',\n    'sin',\n    'sinh',\n    'snprintf',\n    'sprintf',\n    'sqrt',\n    'sscanf',\n    'std',\n    'stderr',\n    'stdin',\n    'stdout',\n    'strcat',\n    'strchr',\n    'strcmp',\n    'strcpy',\n    'strcspn',\n    'strlen',\n    'strncat',\n    'strncmp',\n    'strncpy',\n    'strpbrk',\n    'strrchr',\n    'strspn',\n    'strstr',\n    'swap',\n    'tan',\n    'tanh',\n    'terminate',\n    'to_underlying',\n    'tolower',\n    'toupper',\n    'vfprintf',\n    'visit',\n    'vprintf',\n    'vsprintf'\n  ];\n\n  const LITERALS = [\n    'NULL',\n    'false',\n    'nullopt',\n    'nullptr',\n    'true'\n  ];\n\n  // https://en.cppreference.com/w/cpp/keyword\n  const BUILT_IN = [ '_Pragma' ];\n\n  const CPP_KEYWORDS = {\n    type: RESERVED_TYPES,\n    keyword: RESERVED_KEYWORDS,\n    literal: LITERALS,\n    built_in: BUILT_IN,\n    _type_hints: TYPE_HINTS\n  };\n\n  const FUNCTION_DISPATCH = {\n    className: 'function.dispatch',\n    relevance: 0,\n    keywords: {\n      // Only for relevance, not highlighting.\n      _hint: FUNCTION_HINTS },\n    begin: regex.concat(\n      /\\b/,\n      /(?!decltype)/,\n      /(?!if)/,\n      /(?!for)/,\n      /(?!switch)/,\n      /(?!while)/,\n      hljs.IDENT_RE,\n      regex.lookahead(/(<[^<>]+>|)\\s*\\(/))\n  };\n\n  const EXPRESSION_CONTAINS = [\n    FUNCTION_DISPATCH,\n    PREPROCESSOR,\n    CPP_PRIMITIVE_TYPES,\n    C_LINE_COMMENT_MODE,\n    hljs.C_BLOCK_COMMENT_MODE,\n    NUMBERS,\n    STRINGS\n  ];\n\n  const EXPRESSION_CONTEXT = {\n    // This mode covers expression context where we can't expect a function\n    // definition and shouldn't highlight anything that looks like one:\n    // `return some()`, `else if()`, `(x*sum(1, 2))`\n    variants: [\n      {\n        begin: /=/,\n        end: /;/\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/\n      },\n      {\n        beginKeywords: 'new throw return else',\n        end: /;/\n      }\n    ],\n    keywords: CPP_KEYWORDS,\n    contains: EXPRESSION_CONTAINS.concat([\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: CPP_KEYWORDS,\n        contains: EXPRESSION_CONTAINS.concat([ 'self' ]),\n        relevance: 0\n      }\n    ]),\n    relevance: 0\n  };\n\n  const FUNCTION_DECLARATION = {\n    className: 'function',\n    begin: '(' + FUNCTION_TYPE_RE + '[\\\\*&\\\\s]+)+' + FUNCTION_TITLE,\n    returnBegin: true,\n    end: /[{;=]/,\n    excludeEnd: true,\n    keywords: CPP_KEYWORDS,\n    illegal: /[^\\w\\s\\*&:<>.]/,\n    contains: [\n      { // to prevent it from being confused as the function title\n        begin: DECLTYPE_AUTO_RE,\n        keywords: CPP_KEYWORDS,\n        relevance: 0\n      },\n      {\n        begin: FUNCTION_TITLE,\n        returnBegin: true,\n        contains: [ TITLE_MODE ],\n        relevance: 0\n      },\n      // needed because we do not have look-behind on the below rule\n      // to prevent it from grabbing the final : in a :: pair\n      {\n        begin: /::/,\n        relevance: 0\n      },\n      // initializers\n      {\n        begin: /:/,\n        endsWithParent: true,\n        contains: [\n          STRINGS,\n          NUMBERS\n        ]\n      },\n      // allow for multiple declarations, e.g.:\n      // extern void f(int), g(char);\n      {\n        relevance: 0,\n        match: /,/\n      },\n      {\n        className: 'params',\n        begin: /\\(/,\n        end: /\\)/,\n        keywords: CPP_KEYWORDS,\n        relevance: 0,\n        contains: [\n          C_LINE_COMMENT_MODE,\n          hljs.C_BLOCK_COMMENT_MODE,\n          STRINGS,\n          NUMBERS,\n          CPP_PRIMITIVE_TYPES,\n          // Count matching parentheses.\n          {\n            begin: /\\(/,\n            end: /\\)/,\n            keywords: CPP_KEYWORDS,\n            relevance: 0,\n            contains: [\n              'self',\n              C_LINE_COMMENT_MODE,\n              hljs.C_BLOCK_COMMENT_MODE,\n              STRINGS,\n              NUMBERS,\n              CPP_PRIMITIVE_TYPES\n            ]\n          }\n        ]\n      },\n      CPP_PRIMITIVE_TYPES,\n      C_LINE_COMMENT_MODE,\n      hljs.C_BLOCK_COMMENT_MODE,\n      PREPROCESSOR\n    ]\n  };\n\n  return {\n    name: 'C++',\n    aliases: [\n      'cc',\n      'c++',\n      'h++',\n      'hpp',\n      'hh',\n      'hxx',\n      'cxx'\n    ],\n    keywords: CPP_KEYWORDS,\n    illegal: '</',\n    classNameAliases: { 'function.dispatch': 'built_in' },\n    contains: [].concat(\n      EXPRESSION_CONTEXT,\n      FUNCTION_DECLARATION,\n      FUNCTION_DISPATCH,\n      EXPRESSION_CONTAINS,\n      [\n        PREPROCESSOR,\n        { // containers: ie, `vector <int> rooms (9);`\n          begin: '\\\\b(deque|list|queue|priority_queue|pair|stack|vector|map|set|bitset|multiset|multimap|unordered_map|unordered_set|unordered_multiset|unordered_multimap|array|tuple|optional|variant|function|flat_map|flat_set)\\\\s*<(?!<)',\n          end: '>',\n          keywords: CPP_KEYWORDS,\n          contains: [\n            'self',\n            CPP_PRIMITIVE_TYPES\n          ]\n        },\n        {\n          begin: hljs.IDENT_RE + '::',\n          keywords: CPP_KEYWORDS\n        },\n        {\n          match: [\n            // extra complexity to deal with `enum class` and `enum struct`\n            /\\b(?:enum(?:\\s+(?:class|struct))?|class|struct|union)/,\n            /\\s+/,\n            /\\w+/\n          ],\n          className: {\n            1: 'keyword',\n            3: 'title.class'\n          }\n        }\n      ])\n  };\n}\n\nexport { cpp as default };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA;AACA,SAASA,GAAGA,CAACC,IAAI,EAAE;EACjB,IAAMC,KAAK,GAAGD,IAAI,CAACC,KAAK;EACxB;EACA;EACA;EACA,IAAMC,mBAAmB,GAAGF,IAAI,CAACG,OAAO,CAAC,IAAI,EAAE,GAAG,EAAE;IAAEC,QAAQ,EAAE,CAAE;MAAEC,KAAK,EAAE;IAAO,CAAC;EAAG,CAAC,CAAC;EACxF,IAAMC,gBAAgB,GAAG,oBAAoB;EAC7C,IAAMC,YAAY,GAAG,iBAAiB;EACtC,IAAMC,oBAAoB,GAAG,UAAU;EACvC,IAAMC,gBAAgB,GAAG,aAAa,GAClCH,gBAAgB,GAAG,GAAG,GACtBL,KAAK,CAACS,QAAQ,CAACH,YAAY,CAAC,GAC5B,eAAe,GAAGN,KAAK,CAACS,QAAQ,CAACF,oBAAoB,CAAC,GACxD,GAAG;EAEL,IAAMG,mBAAmB,GAAG;IAC1BC,SAAS,EAAE,MAAM;IACjBP,KAAK,EAAE;EACT,CAAC;;EAED;EACA;EACA,IAAMQ,iBAAiB,GAAG,sDAAsD;EAChF,IAAMC,OAAO,GAAG;IACdF,SAAS,EAAE,QAAQ;IACnBG,QAAQ,EAAE,CACR;MACEV,KAAK,EAAE,aAAa;MACpBW,GAAG,EAAE,GAAG;MACRC,OAAO,EAAE,KAAK;MACdb,QAAQ,EAAE,CAAEJ,IAAI,CAACkB,gBAAgB;IACnC,CAAC,EACD;MACEb,KAAK,EAAE,eAAe,GAAGQ,iBAAiB,GAAG,KAAK;MAClDG,GAAG,EAAE,IAAI;MACTC,OAAO,EAAE;IACX,CAAC,EACDjB,IAAI,CAACmB,iBAAiB,CAAC;MACrBd,KAAK,EAAE,kCAAkC;MACzCW,GAAG,EAAE;IACP,CAAC,CAAC;EAEN,CAAC;EAED,IAAMI,OAAO,GAAG;IACdR,SAAS,EAAE,QAAQ;IACnBG,QAAQ,EAAE;IACR;IACA;MAAEV,KAAK,EACL,UAAU,CAAC;MACT;MAAA,EACE,KAAK,GACJ,4CAA4C,GAC3C,uBAAuB,GACzB,kCAAkC,GAClC;MACF;MAAA,EACE,WAAW,GACV,yEAAyE,GACxE,mCAAmC,GACrC,6BAA6B,GAC/B,MAAM,CAAC;MAAA,EACL,uBAAuB,GACvB,YAAY,GACZ,OAAO,GACP,GAAG,CAAC;MAAA,EACN;IACJ,CAAC;IACD;IACA;MAAEA,KAAK,EACL,aAAa,CAAC;MAAA,EACV,sBAAsB,CAAC;MAAA,EACvB,qCAAqC,CAAC;MAAA,EACtC,gBAAgB,CAAC;MAAA,EACjB,oBAAoB,CAAC;MAAA,EACvB,MAAM,CAAC;MAAA,EACL,iBAAiB,GACjB,YAAY,GACZ,mBAAmB,GACnB,WAAW,GACX,GAAG,CAAC;MAAA,EACN;MACF;MACA;IACF,CAAC,CACF;IACDgB,SAAS,EAAE;EACb,CAAC;EAED,IAAMC,YAAY,GAAG;IACnBV,SAAS,EAAE,MAAM;IACjBP,KAAK,EAAE,cAAc;IACrBW,GAAG,EAAE,GAAG;IACRO,QAAQ,EAAE;MAAEC,OAAO,EACf,qDAAqD,GACnD;IAAsC,CAAC;IAC7CpB,QAAQ,EAAE,CACR;MACEC,KAAK,EAAE,MAAM;MACbgB,SAAS,EAAE;IACb,CAAC,EACDrB,IAAI,CAACyB,OAAO,CAACX,OAAO,EAAE;MAAEF,SAAS,EAAE;IAAS,CAAC,CAAC,EAC9C;MACEA,SAAS,EAAE,QAAQ;MACnBP,KAAK,EAAE;IACT,CAAC,EACDH,mBAAmB,EACnBF,IAAI,CAAC0B,oBAAoB;EAE7B,CAAC;EAED,IAAMC,UAAU,GAAG;IACjBf,SAAS,EAAE,OAAO;IAClBP,KAAK,EAAEJ,KAAK,CAACS,QAAQ,CAACH,YAAY,CAAC,GAAGP,IAAI,CAAC4B,QAAQ;IACnDP,SAAS,EAAE;EACb,CAAC;EAED,IAAMQ,cAAc,GAAG5B,KAAK,CAACS,QAAQ,CAACH,YAAY,CAAC,GAAGP,IAAI,CAAC4B,QAAQ,GAAG,SAAS;;EAE/E;EACA,IAAME,iBAAiB,GAAG,CACxB,SAAS,EACT,SAAS,EACT,KAAK,EACL,QAAQ,EACR,KAAK,EACL,eAAe,EACf,eAAe,EACf,iBAAiB,EACjB,MAAM,EACN,QAAQ,EACR,OAAO,EACP,OAAO,EACP,MAAM,EACN,OAAO,EACP,OAAO,EACP,UAAU,EACV,WAAW,EACX,UAAU,EACV,OAAO,EACP,SAAS,EACT,eAAe,EACf,WAAW,EACX,WAAW,EACX,WAAW,EACX,UAAU,EACV,UAAU,EACV,SAAS,EACT,QAAQ,EACR,IAAI,EACJ,iBAAiB,EACjB,MAAM,EACN,MAAM,EACN,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,OAAO,EACP,KAAK,EACL,QAAQ,EACR,MAAM,EACN,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,WAAW,EACX,KAAK,EACL,UAAU,EACV,KAAK,EACL,QAAQ,EACR,SAAS,EACT,UAAU,EACV,IAAI,EACJ,OAAO,EACP,UAAU,EACV,SAAS,EACT,WAAW,EACX,QAAQ,EACR,UAAU,EACV,UAAU,EACV,qBAAqB,EACrB,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,eAAe,EACf,gBAAgB,EAChB,QAAQ,EACR,QAAQ,EACR,cAAc,EACd,UAAU,EACV,MAAM,EACN,cAAc,EACd,OAAO,EACP,kBAAkB,EAClB,0BAA0B,EAC1B,MAAM,EACN,KAAK,EACL,SAAS,EACT,QAAQ,EACR,UAAU,EACV,OAAO,EACP,OAAO,EACP,SAAS,EACT,UAAU,EACV,OAAO,EACP,KAAK,EACL,QAAQ,CACT;;EAED;EACA,IAAMC,cAAc,GAAG,CACrB,MAAM,EACN,MAAM,EACN,UAAU,EACV,UAAU,EACV,SAAS,EACT,QAAQ,EACR,OAAO,EACP,KAAK,EACL,MAAM,EACN,OAAO,EACP,MAAM,EACN,SAAS,EACT,UAAU,EACV,QAAQ,EACR,OAAO,EACP,QAAQ,CACT;EAED,IAAMC,UAAU,GAAG,CACjB,KAAK,EACL,UAAU,EACV,SAAS,EACT,kBAAkB,EAClB,QAAQ,EACR,SAAS,EACT,oBAAoB,EACpB,wBAAwB,EACxB,oBAAoB,EACpB,OAAO,EACP,YAAY,EACZ,UAAU,EACV,UAAU,EACV,QAAQ,EACR,WAAW,EACX,kBAAkB,EAClB,eAAe,EACf,SAAS,EACT,OAAO,EACP,YAAY,EACZ,UAAU,EACV,UAAU,EACV,OAAO,EACP,UAAU,EACV,eAAe,EACf,eAAe,EACf,MAAM,EACN,SAAS,EACT,gBAAgB,EAChB,OAAO,EACP,iBAAiB,EACjB,uBAAuB,EACvB,aAAa,EACb,KAAK,EACL,eAAe,EACf,aAAa,EACb,cAAc,EACd,oBAAoB,EACpB,YAAY,EACZ,OAAO,EACP,aAAa,EACb,cAAc,EACd,aAAa,EACb,QAAQ,EACR,WAAW,EACX,OAAO,EACP,aAAa,EACb,YAAY,EACZ,eAAe,EACf,oBAAoB,EACpB,oBAAoB,EACpB,eAAe,EACf,SAAS,EACT,QAAQ,EACR,UAAU,EACV,SAAS,EACT,cAAc,CACf;EAED,IAAMC,cAAc,GAAG,CACrB,OAAO,EACP,KAAK,EACL,MAAM,EACN,OAAO,EACP,UAAU,EACV,MAAM,EACN,MAAM,EACN,OAAO,EACP,QAAQ,EACR,MAAM,EACN,MAAM,EACN,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,MAAM,EACN,SAAS,EACT,MAAM,EACN,UAAU,EACV,MAAM,EACN,KAAK,EACL,MAAM,EACN,OAAO,EACP,MAAM,EACN,SAAS,EACT,SAAS,EACT,OAAO,EACP,MAAM,EACN,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,UAAU,EACV,MAAM,EACN,SAAS,EACT,OAAO,EACP,KAAK,EACL,OAAO,EACP,WAAW,EACX,aAAa,EACb,2BAA2B,EAC3B,YAAY,EACZ,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,MAAM,EACN,KAAK,EACL,QAAQ,EACR,SAAS,EACT,MAAM,EACN,SAAS,EACT,OAAO,EACP,KAAK,EACL,MAAM,EACN,UAAU,EACV,SAAS,EACT,MAAM,EACN,QAAQ,EACR,KAAK,EACL,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,KAAK,EACL,MAAM,EACN,WAAW,EACX,eAAe,EACf,SAAS,EACT,SAAS,EACT,UAAU,EACV,OAAO,EACP,SAAS,EACT,UAAU,CACX;EAED,IAAMC,QAAQ,GAAG,CACf,MAAM,EACN,OAAO,EACP,SAAS,EACT,SAAS,EACT,MAAM,CACP;;EAED;EACA,IAAMC,QAAQ,GAAG,CAAE,SAAS,CAAE;EAE9B,IAAMC,YAAY,GAAG;IACnBC,IAAI,EAAEN,cAAc;IACpBP,OAAO,EAAEM,iBAAiB;IAC1BQ,OAAO,EAAEJ,QAAQ;IACjBK,QAAQ,EAAEJ,QAAQ;IAClBK,WAAW,EAAER;EACf,CAAC;EAED,IAAMS,iBAAiB,GAAG;IACxB7B,SAAS,EAAE,mBAAmB;IAC9BS,SAAS,EAAE,CAAC;IACZE,QAAQ,EAAE;MACR;MACAmB,KAAK,EAAET;IAAe,CAAC;IACzB5B,KAAK,EAAEJ,KAAK,CAAC0C,MAAM,CACjB,IAAI,EACJ,cAAc,EACd,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,WAAW,EACX3C,IAAI,CAAC4B,QAAQ,EACb3B,KAAK,CAAC2C,SAAS,CAAC,kBAAkB,CAAC;EACvC,CAAC;EAED,IAAMC,mBAAmB,GAAG,CAC1BJ,iBAAiB,EACjBnB,YAAY,EACZX,mBAAmB,EACnBT,mBAAmB,EACnBF,IAAI,CAAC0B,oBAAoB,EACzBN,OAAO,EACPN,OAAO,CACR;EAED,IAAMgC,kBAAkB,GAAG;IACzB;IACA;IACA;IACA/B,QAAQ,EAAE,CACR;MACEV,KAAK,EAAE,GAAG;MACVW,GAAG,EAAE;IACP,CAAC,EACD;MACEX,KAAK,EAAE,IAAI;MACXW,GAAG,EAAE;IACP,CAAC,EACD;MACE+B,aAAa,EAAE,uBAAuB;MACtC/B,GAAG,EAAE;IACP,CAAC,CACF;IACDO,QAAQ,EAAEa,YAAY;IACtBhC,QAAQ,EAAEyC,mBAAmB,CAACF,MAAM,CAAC,CACnC;MACEtC,KAAK,EAAE,IAAI;MACXW,GAAG,EAAE,IAAI;MACTO,QAAQ,EAAEa,YAAY;MACtBhC,QAAQ,EAAEyC,mBAAmB,CAACF,MAAM,CAAC,CAAE,MAAM,CAAE,CAAC;MAChDtB,SAAS,EAAE;IACb,CAAC,CACF,CAAC;IACFA,SAAS,EAAE;EACb,CAAC;EAED,IAAM2B,oBAAoB,GAAG;IAC3BpC,SAAS,EAAE,UAAU;IACrBP,KAAK,EAAE,GAAG,GAAGI,gBAAgB,GAAG,cAAc,GAAGoB,cAAc;IAC/DoB,WAAW,EAAE,IAAI;IACjBjC,GAAG,EAAE,OAAO;IACZkC,UAAU,EAAE,IAAI;IAChB3B,QAAQ,EAAEa,YAAY;IACtBnB,OAAO,EAAE,gBAAgB;IACzBb,QAAQ,EAAE,CACR;MAAE;MACAC,KAAK,EAAEC,gBAAgB;MACvBiB,QAAQ,EAAEa,YAAY;MACtBf,SAAS,EAAE;IACb,CAAC,EACD;MACEhB,KAAK,EAAEwB,cAAc;MACrBoB,WAAW,EAAE,IAAI;MACjB7C,QAAQ,EAAE,CAAEuB,UAAU,CAAE;MACxBN,SAAS,EAAE;IACb,CAAC;IACD;IACA;IACA;MACEhB,KAAK,EAAE,IAAI;MACXgB,SAAS,EAAE;IACb,CAAC;IACD;IACA;MACEhB,KAAK,EAAE,GAAG;MACV8C,cAAc,EAAE,IAAI;MACpB/C,QAAQ,EAAE,CACRU,OAAO,EACPM,OAAO;IAEX,CAAC;IACD;IACA;IACA;MACEC,SAAS,EAAE,CAAC;MACZ+B,KAAK,EAAE;IACT,CAAC,EACD;MACExC,SAAS,EAAE,QAAQ;MACnBP,KAAK,EAAE,IAAI;MACXW,GAAG,EAAE,IAAI;MACTO,QAAQ,EAAEa,YAAY;MACtBf,SAAS,EAAE,CAAC;MACZjB,QAAQ,EAAE,CACRF,mBAAmB,EACnBF,IAAI,CAAC0B,oBAAoB,EACzBZ,OAAO,EACPM,OAAO,EACPT,mBAAmB;MACnB;MACA;QACEN,KAAK,EAAE,IAAI;QACXW,GAAG,EAAE,IAAI;QACTO,QAAQ,EAAEa,YAAY;QACtBf,SAAS,EAAE,CAAC;QACZjB,QAAQ,EAAE,CACR,MAAM,EACNF,mBAAmB,EACnBF,IAAI,CAAC0B,oBAAoB,EACzBZ,OAAO,EACPM,OAAO,EACPT,mBAAmB;MAEvB,CAAC;IAEL,CAAC,EACDA,mBAAmB,EACnBT,mBAAmB,EACnBF,IAAI,CAAC0B,oBAAoB,EACzBJ,YAAY;EAEhB,CAAC;EAED,OAAO;IACL+B,IAAI,EAAE,KAAK;IACXC,OAAO,EAAE,CACP,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACL,IAAI,EACJ,KAAK,EACL,KAAK,CACN;IACD/B,QAAQ,EAAEa,YAAY;IACtBnB,OAAO,EAAE,IAAI;IACbsC,gBAAgB,EAAE;MAAE,mBAAmB,EAAE;IAAW,CAAC;IACrDnD,QAAQ,EAAE,EAAE,CAACuC,MAAM,CACjBG,kBAAkB,EAClBE,oBAAoB,EACpBP,iBAAiB,EACjBI,mBAAmB,EACnB,CACEvB,YAAY,EACZ;MAAE;MACAjB,KAAK,EAAE,6NAA6N;MACpOW,GAAG,EAAE,GAAG;MACRO,QAAQ,EAAEa,YAAY;MACtBhC,QAAQ,EAAE,CACR,MAAM,EACNO,mBAAmB;IAEvB,CAAC,EACD;MACEN,KAAK,EAAEL,IAAI,CAAC4B,QAAQ,GAAG,IAAI;MAC3BL,QAAQ,EAAEa;IACZ,CAAC,EACD;MACEgB,KAAK,EAAE;MACL;MACA,uDAAuD,EACvD,KAAK,EACL,KAAK,CACN;MACDxC,SAAS,EAAE;QACT,CAAC,EAAE,SAAS;QACZ,CAAC,EAAE;MACL;IACF,CAAC,CACF;EACL,CAAC;AACH;AAEA,SAASb,GAAG,IAAIyD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}