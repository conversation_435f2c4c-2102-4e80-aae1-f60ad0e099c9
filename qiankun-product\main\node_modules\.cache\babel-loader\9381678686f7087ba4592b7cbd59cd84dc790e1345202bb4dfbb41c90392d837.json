{"ast": null, "code": "\"use strict\";\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _toConsumableArray(r) {\n  return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread();\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return _arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;\n  }\n}\nfunction _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return _arrayLikeToArray(r);\n}\nfunction _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(e, r, t) {\n  return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nvar getResolvedId = require(\"./get-resolved-id.js\");\nfunction moduleResolve(part, options) {\n  var moduleResolved;\n  for (var i = 0, l = options.modules.length; i < l; i++) {\n    var _module = options.modules[i];\n    moduleResolved = _module.resolve(part, options);\n    if (moduleResolved) {\n      return moduleResolved;\n    }\n  }\n  return false;\n}\nfunction resolve(options) {\n  var resolved = [];\n  var baseNullGetter = options.baseNullGetter;\n  var compiled = options.compiled,\n    scopeManager = options.scopeManager;\n  options.nullGetter = function (part, sm) {\n    return baseNullGetter(part, sm || scopeManager);\n  };\n  options.resolved = resolved;\n  var errors = [];\n  return Promise.all(compiled.filter(function (part) {\n    return [\"content\", \"tag\"].indexOf(part.type) === -1;\n  }).reduce(function (promises, part) {\n    var moduleResolved = moduleResolve(part, _objectSpread(_objectSpread({}, options), {}, {\n      resolvedId: getResolvedId(part, options)\n    }));\n    var result;\n    if (moduleResolved) {\n      result = moduleResolved.then(function (value) {\n        resolved.push({\n          tag: part.value,\n          lIndex: part.lIndex,\n          value: value\n        });\n      });\n    } else if (part.type === \"placeholder\") {\n      result = scopeManager.getValueAsync(part.value, {\n        part: part\n      }).then(function (value) {\n        return value == null ? options.nullGetter(part) : value;\n      }).then(function (value) {\n        resolved.push({\n          tag: part.value,\n          lIndex: part.lIndex,\n          value: value\n        });\n        return value;\n      });\n    } else {\n      return;\n    }\n    promises.push(result[\"catch\"](function (e) {\n      if (e instanceof Array) {\n        errors.push.apply(errors, _toConsumableArray(e));\n      } else {\n        errors.push(e);\n      }\n    }));\n    return promises;\n  }, [])).then(function () {\n    return {\n      errors: errors,\n      resolved: resolved\n    };\n  });\n}\nmodule.exports = resolve;", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_toConsumableArray", "r", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "TypeError", "a", "_arrayLikeToArray", "t", "toString", "call", "slice", "name", "Array", "from", "test", "isArray", "length", "e", "n", "ownKeys", "Object", "keys", "getOwnPropertySymbols", "filter", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread", "arguments", "for<PERSON>ach", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "configurable", "writable", "i", "_toPrimitive", "toPrimitive", "String", "Number", "getResolvedId", "require", "moduleResolve", "part", "options", "moduleResolved", "l", "modules", "_module", "resolve", "resolved", "baseNullGetter", "compiled", "scopeManager", "nullGetter", "sm", "errors", "Promise", "all", "indexOf", "type", "reduce", "promises", "resolvedId", "result", "then", "tag", "lIndex", "getValueAsync", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/docxtemplater@3.52.0/node_modules/docxtemplater/js/resolve.js"], "sourcesContent": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nvar getResolvedId = require(\"./get-resolved-id.js\");\nfunction moduleResolve(part, options) {\n  var moduleResolved;\n  for (var i = 0, l = options.modules.length; i < l; i++) {\n    var _module = options.modules[i];\n    moduleResolved = _module.resolve(part, options);\n    if (moduleResolved) {\n      return moduleResolved;\n    }\n  }\n  return false;\n}\nfunction resolve(options) {\n  var resolved = [];\n  var baseNullGetter = options.baseNullGetter;\n  var compiled = options.compiled,\n    scopeManager = options.scopeManager;\n  options.nullGetter = function (part, sm) {\n    return baseNullGetter(part, sm || scopeManager);\n  };\n  options.resolved = resolved;\n  var errors = [];\n  return Promise.all(compiled.filter(function (part) {\n    return [\"content\", \"tag\"].indexOf(part.type) === -1;\n  }).reduce(function (promises, part) {\n    var moduleResolved = moduleResolve(part, _objectSpread(_objectSpread({}, options), {}, {\n      resolvedId: getResolvedId(part, options)\n    }));\n    var result;\n    if (moduleResolved) {\n      result = moduleResolved.then(function (value) {\n        resolved.push({\n          tag: part.value,\n          lIndex: part.lIndex,\n          value: value\n        });\n      });\n    } else if (part.type === \"placeholder\") {\n      result = scopeManager.getValueAsync(part.value, {\n        part: part\n      }).then(function (value) {\n        return value == null ? options.nullGetter(part) : value;\n      }).then(function (value) {\n        resolved.push({\n          tag: part.value,\n          lIndex: part.lIndex,\n          value: value\n        });\n        return value;\n      });\n    } else {\n      return;\n    }\n    promises.push(result[\"catch\"](function (e) {\n      if (e instanceof Array) {\n        errors.push.apply(errors, _toConsumableArray(e));\n      } else {\n        errors.push(e);\n      }\n    }));\n    return promises;\n  }, [])).then(function () {\n    return {\n      errors: errors,\n      resolved: resolved\n    };\n  });\n}\nmodule.exports = resolve;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,kBAAkBA,CAACC,CAAC,EAAE;EAAE,OAAOC,kBAAkB,CAACD,CAAC,CAAC,IAAIE,gBAAgB,CAACF,CAAC,CAAC,IAAIG,2BAA2B,CAACH,CAAC,CAAC,IAAII,kBAAkB,CAAC,CAAC;AAAE;AAChJ,SAASA,kBAAkBA,CAAA,EAAG;EAAE,MAAM,IAAIC,SAAS,CAAC,sIAAsI,CAAC;AAAE;AAC7L,SAASF,2BAA2BA,CAACH,CAAC,EAAEM,CAAC,EAAE;EAAE,IAAIN,CAAC,EAAE;IAAE,IAAI,QAAQ,IAAI,OAAOA,CAAC,EAAE,OAAOO,iBAAiB,CAACP,CAAC,EAAEM,CAAC,CAAC;IAAE,IAAIE,CAAC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAACC,IAAI,CAACV,CAAC,CAAC,CAACW,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAAE,OAAO,QAAQ,KAAKH,CAAC,IAAIR,CAAC,CAACH,WAAW,KAAKW,CAAC,GAAGR,CAAC,CAACH,WAAW,CAACe,IAAI,CAAC,EAAE,KAAK,KAAKJ,CAAC,IAAI,KAAK,KAAKA,CAAC,GAAGK,KAAK,CAACC,IAAI,CAACd,CAAC,CAAC,GAAG,WAAW,KAAKQ,CAAC,IAAI,0CAA0C,CAACO,IAAI,CAACP,CAAC,CAAC,GAAGD,iBAAiB,CAACP,CAAC,EAAEM,CAAC,CAAC,GAAG,KAAK,CAAC;EAAE;AAAE;AACzX,SAASJ,gBAAgBA,CAACF,CAAC,EAAE;EAAE,IAAI,WAAW,IAAI,OAAOL,MAAM,IAAI,IAAI,IAAIK,CAAC,CAACL,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAII,CAAC,CAAC,YAAY,CAAC,EAAE,OAAOa,KAAK,CAACC,IAAI,CAACd,CAAC,CAAC;AAAE;AAChJ,SAASC,kBAAkBA,CAACD,CAAC,EAAE;EAAE,IAAIa,KAAK,CAACG,OAAO,CAAChB,CAAC,CAAC,EAAE,OAAOO,iBAAiB,CAACP,CAAC,CAAC;AAAE;AACpF,SAASO,iBAAiBA,CAACP,CAAC,EAAEM,CAAC,EAAE;EAAE,CAAC,IAAI,IAAIA,CAAC,IAAIA,CAAC,GAAGN,CAAC,CAACiB,MAAM,MAAMX,CAAC,GAAGN,CAAC,CAACiB,MAAM,CAAC;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGN,KAAK,CAACP,CAAC,CAAC,EAAEY,CAAC,GAAGZ,CAAC,EAAEY,CAAC,EAAE,EAAEC,CAAC,CAACD,CAAC,CAAC,GAAGlB,CAAC,CAACkB,CAAC,CAAC;EAAE,OAAOC,CAAC;AAAE;AACnJ,SAASC,OAAOA,CAACF,CAAC,EAAElB,CAAC,EAAE;EAAE,IAAIQ,CAAC,GAAGa,MAAM,CAACC,IAAI,CAACJ,CAAC,CAAC;EAAE,IAAIG,MAAM,CAACE,qBAAqB,EAAE;IAAE,IAAI7B,CAAC,GAAG2B,MAAM,CAACE,qBAAqB,CAACL,CAAC,CAAC;IAAElB,CAAC,KAAKN,CAAC,GAAGA,CAAC,CAAC8B,MAAM,CAAC,UAAUxB,CAAC,EAAE;MAAE,OAAOqB,MAAM,CAACI,wBAAwB,CAACP,CAAC,EAAElB,CAAC,CAAC,CAAC0B,UAAU;IAAE,CAAC,CAAC,CAAC,EAAElB,CAAC,CAACmB,IAAI,CAACC,KAAK,CAACpB,CAAC,EAAEd,CAAC,CAAC;EAAE;EAAE,OAAOc,CAAC;AAAE;AAC9P,SAASqB,aAAaA,CAACX,CAAC,EAAE;EAAE,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,SAAS,CAACb,MAAM,EAAEjB,CAAC,EAAE,EAAE;IAAE,IAAIQ,CAAC,GAAG,IAAI,IAAIsB,SAAS,CAAC9B,CAAC,CAAC,GAAG8B,SAAS,CAAC9B,CAAC,CAAC,GAAG,CAAC,CAAC;IAAEA,CAAC,GAAG,CAAC,GAAGoB,OAAO,CAACC,MAAM,CAACb,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACuB,OAAO,CAAC,UAAU/B,CAAC,EAAE;MAAEgC,eAAe,CAACd,CAAC,EAAElB,CAAC,EAAEQ,CAAC,CAACR,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC,GAAGqB,MAAM,CAACY,yBAAyB,GAAGZ,MAAM,CAACa,gBAAgB,CAAChB,CAAC,EAAEG,MAAM,CAACY,yBAAyB,CAACzB,CAAC,CAAC,CAAC,GAAGY,OAAO,CAACC,MAAM,CAACb,CAAC,CAAC,CAAC,CAACuB,OAAO,CAAC,UAAU/B,CAAC,EAAE;MAAEqB,MAAM,CAACc,cAAc,CAACjB,CAAC,EAAElB,CAAC,EAAEqB,MAAM,CAACI,wBAAwB,CAACjB,CAAC,EAAER,CAAC,CAAC,CAAC;IAAE,CAAC,CAAC;EAAE;EAAE,OAAOkB,CAAC;AAAE;AACtb,SAASc,eAAeA,CAACd,CAAC,EAAElB,CAAC,EAAEQ,CAAC,EAAE;EAAE,OAAO,CAACR,CAAC,GAAGoC,cAAc,CAACpC,CAAC,CAAC,KAAKkB,CAAC,GAAGG,MAAM,CAACc,cAAc,CAACjB,CAAC,EAAElB,CAAC,EAAE;IAAEqC,KAAK,EAAE7B,CAAC;IAAEkB,UAAU,EAAE,CAAC,CAAC;IAAEY,YAAY,EAAE,CAAC,CAAC;IAAEC,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,GAAGrB,CAAC,CAAClB,CAAC,CAAC,GAAGQ,CAAC,EAAEU,CAAC;AAAE;AACnL,SAASkB,cAAcA,CAAC5B,CAAC,EAAE;EAAE,IAAIgC,CAAC,GAAGC,YAAY,CAACjC,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAIf,OAAO,CAAC+C,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASC,YAAYA,CAACjC,CAAC,EAAER,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIP,OAAO,CAACe,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIU,CAAC,GAAGV,CAAC,CAACb,MAAM,CAAC+C,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKxB,CAAC,EAAE;IAAE,IAAIsB,CAAC,GAAGtB,CAAC,CAACR,IAAI,CAACF,CAAC,EAAER,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIP,OAAO,CAAC+C,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAInC,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKL,CAAC,GAAG2C,MAAM,GAAGC,MAAM,EAAEpC,CAAC,CAAC;AAAE;AAC3T,IAAIqC,aAAa,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AACnD,SAASC,aAAaA,CAACC,IAAI,EAAEC,OAAO,EAAE;EACpC,IAAIC,cAAc;EAClB,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEW,CAAC,GAAGF,OAAO,CAACG,OAAO,CAACnC,MAAM,EAAEuB,CAAC,GAAGW,CAAC,EAAEX,CAAC,EAAE,EAAE;IACtD,IAAIa,OAAO,GAAGJ,OAAO,CAACG,OAAO,CAACZ,CAAC,CAAC;IAChCU,cAAc,GAAGG,OAAO,CAACC,OAAO,CAACN,IAAI,EAAEC,OAAO,CAAC;IAC/C,IAAIC,cAAc,EAAE;MAClB,OAAOA,cAAc;IACvB;EACF;EACA,OAAO,KAAK;AACd;AACA,SAASI,OAAOA,CAACL,OAAO,EAAE;EACxB,IAAIM,QAAQ,GAAG,EAAE;EACjB,IAAIC,cAAc,GAAGP,OAAO,CAACO,cAAc;EAC3C,IAAIC,QAAQ,GAAGR,OAAO,CAACQ,QAAQ;IAC7BC,YAAY,GAAGT,OAAO,CAACS,YAAY;EACrCT,OAAO,CAACU,UAAU,GAAG,UAAUX,IAAI,EAAEY,EAAE,EAAE;IACvC,OAAOJ,cAAc,CAACR,IAAI,EAAEY,EAAE,IAAIF,YAAY,CAAC;EACjD,CAAC;EACDT,OAAO,CAACM,QAAQ,GAAGA,QAAQ;EAC3B,IAAIM,MAAM,GAAG,EAAE;EACf,OAAOC,OAAO,CAACC,GAAG,CAACN,QAAQ,CAACjC,MAAM,CAAC,UAAUwB,IAAI,EAAE;IACjD,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,CAACgB,OAAO,CAAChB,IAAI,CAACiB,IAAI,CAAC,KAAK,CAAC,CAAC;EACrD,CAAC,CAAC,CAACC,MAAM,CAAC,UAAUC,QAAQ,EAAEnB,IAAI,EAAE;IAClC,IAAIE,cAAc,GAAGH,aAAa,CAACC,IAAI,EAAEnB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEoB,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;MACrFmB,UAAU,EAAEvB,aAAa,CAACG,IAAI,EAAEC,OAAO;IACzC,CAAC,CAAC,CAAC;IACH,IAAIoB,MAAM;IACV,IAAInB,cAAc,EAAE;MAClBmB,MAAM,GAAGnB,cAAc,CAACoB,IAAI,CAAC,UAAUjC,KAAK,EAAE;QAC5CkB,QAAQ,CAAC5B,IAAI,CAAC;UACZ4C,GAAG,EAAEvB,IAAI,CAACX,KAAK;UACfmC,MAAM,EAAExB,IAAI,CAACwB,MAAM;UACnBnC,KAAK,EAAEA;QACT,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIW,IAAI,CAACiB,IAAI,KAAK,aAAa,EAAE;MACtCI,MAAM,GAAGX,YAAY,CAACe,aAAa,CAACzB,IAAI,CAACX,KAAK,EAAE;QAC9CW,IAAI,EAAEA;MACR,CAAC,CAAC,CAACsB,IAAI,CAAC,UAAUjC,KAAK,EAAE;QACvB,OAAOA,KAAK,IAAI,IAAI,GAAGY,OAAO,CAACU,UAAU,CAACX,IAAI,CAAC,GAAGX,KAAK;MACzD,CAAC,CAAC,CAACiC,IAAI,CAAC,UAAUjC,KAAK,EAAE;QACvBkB,QAAQ,CAAC5B,IAAI,CAAC;UACZ4C,GAAG,EAAEvB,IAAI,CAACX,KAAK;UACfmC,MAAM,EAAExB,IAAI,CAACwB,MAAM;UACnBnC,KAAK,EAAEA;QACT,CAAC,CAAC;QACF,OAAOA,KAAK;MACd,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;IACF;IACA8B,QAAQ,CAACxC,IAAI,CAAC0C,MAAM,CAAC,OAAO,CAAC,CAAC,UAAUnD,CAAC,EAAE;MACzC,IAAIA,CAAC,YAAYL,KAAK,EAAE;QACtBgD,MAAM,CAAClC,IAAI,CAACC,KAAK,CAACiC,MAAM,EAAE9D,kBAAkB,CAACmB,CAAC,CAAC,CAAC;MAClD,CAAC,MAAM;QACL2C,MAAM,CAAClC,IAAI,CAACT,CAAC,CAAC;MAChB;IACF,CAAC,CAAC,CAAC;IACH,OAAOiD,QAAQ;EACjB,CAAC,EAAE,EAAE,CAAC,CAAC,CAACG,IAAI,CAAC,YAAY;IACvB,OAAO;MACLT,MAAM,EAAEA,MAAM;MACdN,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC;AACJ;AACAmB,MAAM,CAACC,OAAO,GAAGrB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}