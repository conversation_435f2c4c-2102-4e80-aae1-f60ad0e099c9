{"ast": null, "code": "// Inline parser state\n\n'use strict';\n\nvar Token = require('../token');\nvar isWhiteSpace = require('../common/utils').isWhiteSpace;\nvar isPunctChar = require('../common/utils').isPunctChar;\nvar isMdAsciiPunct = require('../common/utils').isMdAsciiPunct;\nfunction StateInline(src, md, env, outTokens) {\n  this.src = src;\n  this.env = env;\n  this.md = md;\n  this.tokens = outTokens;\n  this.tokens_meta = Array(outTokens.length);\n  this.pos = 0;\n  this.posMax = this.src.length;\n  this.level = 0;\n  this.pending = '';\n  this.pendingLevel = 0;\n\n  // Stores { start: end } pairs. Useful for backtrack\n  // optimization of pairs parse (emphasis, strikes).\n  this.cache = {};\n\n  // List of emphasis-like delimiters for current tag\n  this.delimiters = [];\n\n  // Stack of delimiter lists for upper level tags\n  this._prev_delimiters = [];\n\n  // backtick length => last seen position\n  this.backticks = {};\n  this.backticksScanned = false;\n}\n\n// Flush pending text\n//\nStateInline.prototype.pushPending = function () {\n  var token = new Token('text', '', 0);\n  token.content = this.pending;\n  token.level = this.pendingLevel;\n  this.tokens.push(token);\n  this.pending = '';\n  return token;\n};\n\n// Push new token to \"stream\".\n// If pending text exists - flush it as text token\n//\nStateInline.prototype.push = function (type, tag, nesting) {\n  if (this.pending) {\n    this.pushPending();\n  }\n  var token = new Token(type, tag, nesting);\n  var token_meta = null;\n  if (nesting < 0) {\n    // closing tag\n    this.level--;\n    this.delimiters = this._prev_delimiters.pop();\n  }\n  token.level = this.level;\n  if (nesting > 0) {\n    // opening tag\n    this.level++;\n    this._prev_delimiters.push(this.delimiters);\n    this.delimiters = [];\n    token_meta = {\n      delimiters: this.delimiters\n    };\n  }\n  this.pendingLevel = this.level;\n  this.tokens.push(token);\n  this.tokens_meta.push(token_meta);\n  return token;\n};\n\n// Scan a sequence of emphasis-like markers, and determine whether\n// it can start an emphasis sequence or end an emphasis sequence.\n//\n//  - start - position to scan from (it should point at a valid marker);\n//  - canSplitWord - determine if these markers can be found inside a word\n//\nStateInline.prototype.scanDelims = function (start, canSplitWord) {\n  var pos = start,\n    lastChar,\n    nextChar,\n    count,\n    can_open,\n    can_close,\n    isLastWhiteSpace,\n    isLastPunctChar,\n    isNextWhiteSpace,\n    isNextPunctChar,\n    left_flanking = true,\n    right_flanking = true,\n    max = this.posMax,\n    marker = this.src.charCodeAt(start);\n\n  // treat beginning of the line as a whitespace\n  lastChar = start > 0 ? this.src.charCodeAt(start - 1) : 0x20;\n  while (pos < max && this.src.charCodeAt(pos) === marker) {\n    pos++;\n  }\n  count = pos - start;\n\n  // treat end of the line as a whitespace\n  nextChar = pos < max ? this.src.charCodeAt(pos) : 0x20;\n  isLastPunctChar = isMdAsciiPunct(lastChar) || isPunctChar(String.fromCharCode(lastChar));\n  isNextPunctChar = isMdAsciiPunct(nextChar) || isPunctChar(String.fromCharCode(nextChar));\n  isLastWhiteSpace = isWhiteSpace(lastChar);\n  isNextWhiteSpace = isWhiteSpace(nextChar);\n  if (isNextWhiteSpace) {\n    left_flanking = false;\n  } else if (isNextPunctChar) {\n    if (!(isLastWhiteSpace || isLastPunctChar)) {\n      left_flanking = false;\n    }\n  }\n  if (isLastWhiteSpace) {\n    right_flanking = false;\n  } else if (isLastPunctChar) {\n    if (!(isNextWhiteSpace || isNextPunctChar)) {\n      right_flanking = false;\n    }\n  }\n  if (!canSplitWord) {\n    can_open = left_flanking && (!right_flanking || isLastPunctChar);\n    can_close = right_flanking && (!left_flanking || isNextPunctChar);\n  } else {\n    can_open = left_flanking;\n    can_close = right_flanking;\n  }\n  return {\n    can_open: can_open,\n    can_close: can_close,\n    length: count\n  };\n};\n\n// re-export Token class to use in block rules\nStateInline.prototype.Token = Token;\nmodule.exports = StateInline;", "map": {"version": 3, "names": ["Token", "require", "isWhiteSpace", "isPunctChar", "isMdAsciiPunct", "StateInline", "src", "md", "env", "outTokens", "tokens", "tokens_meta", "Array", "length", "pos", "posMax", "level", "pending", "pendingLevel", "cache", "delimiters", "_prev_delimiters", "backticks", "backticksScanned", "prototype", "pushPending", "token", "content", "push", "type", "tag", "nesting", "token_meta", "pop", "scanDelims", "start", "canSplitWord", "lastChar", "nextChar", "count", "can_open", "can_close", "isLastWhiteSpace", "isLastPunctChar", "isNextWhiteSpace", "isNextPunctChar", "left_flanking", "right_flanking", "max", "marker", "charCodeAt", "String", "fromCharCode", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_inline/state_inline.js"], "sourcesContent": ["// Inline parser state\n\n'use strict';\n\n\nvar Token          = require('../token');\nvar isWhiteSpace   = require('../common/utils').isWhiteSpace;\nvar isPunctChar    = require('../common/utils').isPunctChar;\nvar isMdAsciiPunct = require('../common/utils').isMdAsciiPunct;\n\n\nfunction StateInline(src, md, env, outTokens) {\n  this.src = src;\n  this.env = env;\n  this.md = md;\n  this.tokens = outTokens;\n  this.tokens_meta = Array(outTokens.length);\n\n  this.pos = 0;\n  this.posMax = this.src.length;\n  this.level = 0;\n  this.pending = '';\n  this.pendingLevel = 0;\n\n  // Stores { start: end } pairs. Useful for backtrack\n  // optimization of pairs parse (emphasis, strikes).\n  this.cache = {};\n\n  // List of emphasis-like delimiters for current tag\n  this.delimiters = [];\n\n  // Stack of delimiter lists for upper level tags\n  this._prev_delimiters = [];\n\n  // backtick length => last seen position\n  this.backticks = {};\n  this.backticksScanned = false;\n}\n\n\n// Flush pending text\n//\nStateInline.prototype.pushPending = function () {\n  var token = new Token('text', '', 0);\n  token.content = this.pending;\n  token.level = this.pendingLevel;\n  this.tokens.push(token);\n  this.pending = '';\n  return token;\n};\n\n\n// Push new token to \"stream\".\n// If pending text exists - flush it as text token\n//\nStateInline.prototype.push = function (type, tag, nesting) {\n  if (this.pending) {\n    this.pushPending();\n  }\n\n  var token = new Token(type, tag, nesting);\n  var token_meta = null;\n\n  if (nesting < 0) {\n    // closing tag\n    this.level--;\n    this.delimiters = this._prev_delimiters.pop();\n  }\n\n  token.level = this.level;\n\n  if (nesting > 0) {\n    // opening tag\n    this.level++;\n    this._prev_delimiters.push(this.delimiters);\n    this.delimiters = [];\n    token_meta = { delimiters: this.delimiters };\n  }\n\n  this.pendingLevel = this.level;\n  this.tokens.push(token);\n  this.tokens_meta.push(token_meta);\n  return token;\n};\n\n\n// Scan a sequence of emphasis-like markers, and determine whether\n// it can start an emphasis sequence or end an emphasis sequence.\n//\n//  - start - position to scan from (it should point at a valid marker);\n//  - canSplitWord - determine if these markers can be found inside a word\n//\nStateInline.prototype.scanDelims = function (start, canSplitWord) {\n  var pos = start, lastChar, nextChar, count, can_open, can_close,\n      isLastWhiteSpace, isLastPunctChar,\n      isNextWhiteSpace, isNextPunctChar,\n      left_flanking = true,\n      right_flanking = true,\n      max = this.posMax,\n      marker = this.src.charCodeAt(start);\n\n  // treat beginning of the line as a whitespace\n  lastChar = start > 0 ? this.src.charCodeAt(start - 1) : 0x20;\n\n  while (pos < max && this.src.charCodeAt(pos) === marker) { pos++; }\n\n  count = pos - start;\n\n  // treat end of the line as a whitespace\n  nextChar = pos < max ? this.src.charCodeAt(pos) : 0x20;\n\n  isLastPunctChar = isMdAsciiPunct(lastChar) || isPunctChar(String.fromCharCode(lastChar));\n  isNextPunctChar = isMdAsciiPunct(nextChar) || isPunctChar(String.fromCharCode(nextChar));\n\n  isLastWhiteSpace = isWhiteSpace(lastChar);\n  isNextWhiteSpace = isWhiteSpace(nextChar);\n\n  if (isNextWhiteSpace) {\n    left_flanking = false;\n  } else if (isNextPunctChar) {\n    if (!(isLastWhiteSpace || isLastPunctChar)) {\n      left_flanking = false;\n    }\n  }\n\n  if (isLastWhiteSpace) {\n    right_flanking = false;\n  } else if (isLastPunctChar) {\n    if (!(isNextWhiteSpace || isNextPunctChar)) {\n      right_flanking = false;\n    }\n  }\n\n  if (!canSplitWord) {\n    can_open  = left_flanking  && (!right_flanking || isLastPunctChar);\n    can_close = right_flanking && (!left_flanking  || isNextPunctChar);\n  } else {\n    can_open  = left_flanking;\n    can_close = right_flanking;\n  }\n\n  return {\n    can_open:  can_open,\n    can_close: can_close,\n    length:    count\n  };\n};\n\n\n// re-export Token class to use in block rules\nStateInline.prototype.Token = Token;\n\n\nmodule.exports = StateInline;\n"], "mappings": "AAAA;;AAEA,YAAY;;AAGZ,IAAIA,KAAK,GAAYC,OAAO,CAAC,UAAU,CAAC;AACxC,IAAIC,YAAY,GAAKD,OAAO,CAAC,iBAAiB,CAAC,CAACC,YAAY;AAC5D,IAAIC,WAAW,GAAMF,OAAO,CAAC,iBAAiB,CAAC,CAACE,WAAW;AAC3D,IAAIC,cAAc,GAAGH,OAAO,CAAC,iBAAiB,CAAC,CAACG,cAAc;AAG9D,SAASC,WAAWA,CAACC,GAAG,EAAEC,EAAE,EAAEC,GAAG,EAAEC,SAAS,EAAE;EAC5C,IAAI,CAACH,GAAG,GAAGA,GAAG;EACd,IAAI,CAACE,GAAG,GAAGA,GAAG;EACd,IAAI,CAACD,EAAE,GAAGA,EAAE;EACZ,IAAI,CAACG,MAAM,GAAGD,SAAS;EACvB,IAAI,CAACE,WAAW,GAAGC,KAAK,CAACH,SAAS,CAACI,MAAM,CAAC;EAE1C,IAAI,CAACC,GAAG,GAAG,CAAC;EACZ,IAAI,CAACC,MAAM,GAAG,IAAI,CAACT,GAAG,CAACO,MAAM;EAC7B,IAAI,CAACG,KAAK,GAAG,CAAC;EACd,IAAI,CAACC,OAAO,GAAG,EAAE;EACjB,IAAI,CAACC,YAAY,GAAG,CAAC;;EAErB;EACA;EACA,IAAI,CAACC,KAAK,GAAG,CAAC,CAAC;;EAEf;EACA,IAAI,CAACC,UAAU,GAAG,EAAE;;EAEpB;EACA,IAAI,CAACC,gBAAgB,GAAG,EAAE;;EAE1B;EACA,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;EACnB,IAAI,CAACC,gBAAgB,GAAG,KAAK;AAC/B;;AAGA;AACA;AACAlB,WAAW,CAACmB,SAAS,CAACC,WAAW,GAAG,YAAY;EAC9C,IAAIC,KAAK,GAAG,IAAI1B,KAAK,CAAC,MAAM,EAAE,EAAE,EAAE,CAAC,CAAC;EACpC0B,KAAK,CAACC,OAAO,GAAG,IAAI,CAACV,OAAO;EAC5BS,KAAK,CAACV,KAAK,GAAG,IAAI,CAACE,YAAY;EAC/B,IAAI,CAACR,MAAM,CAACkB,IAAI,CAACF,KAAK,CAAC;EACvB,IAAI,CAACT,OAAO,GAAG,EAAE;EACjB,OAAOS,KAAK;AACd,CAAC;;AAGD;AACA;AACA;AACArB,WAAW,CAACmB,SAAS,CAACI,IAAI,GAAG,UAAUC,IAAI,EAAEC,GAAG,EAAEC,OAAO,EAAE;EACzD,IAAI,IAAI,CAACd,OAAO,EAAE;IAChB,IAAI,CAACQ,WAAW,CAAC,CAAC;EACpB;EAEA,IAAIC,KAAK,GAAG,IAAI1B,KAAK,CAAC6B,IAAI,EAAEC,GAAG,EAAEC,OAAO,CAAC;EACzC,IAAIC,UAAU,GAAG,IAAI;EAErB,IAAID,OAAO,GAAG,CAAC,EAAE;IACf;IACA,IAAI,CAACf,KAAK,EAAE;IACZ,IAAI,CAACI,UAAU,GAAG,IAAI,CAACC,gBAAgB,CAACY,GAAG,CAAC,CAAC;EAC/C;EAEAP,KAAK,CAACV,KAAK,GAAG,IAAI,CAACA,KAAK;EAExB,IAAIe,OAAO,GAAG,CAAC,EAAE;IACf;IACA,IAAI,CAACf,KAAK,EAAE;IACZ,IAAI,CAACK,gBAAgB,CAACO,IAAI,CAAC,IAAI,CAACR,UAAU,CAAC;IAC3C,IAAI,CAACA,UAAU,GAAG,EAAE;IACpBY,UAAU,GAAG;MAAEZ,UAAU,EAAE,IAAI,CAACA;IAAW,CAAC;EAC9C;EAEA,IAAI,CAACF,YAAY,GAAG,IAAI,CAACF,KAAK;EAC9B,IAAI,CAACN,MAAM,CAACkB,IAAI,CAACF,KAAK,CAAC;EACvB,IAAI,CAACf,WAAW,CAACiB,IAAI,CAACI,UAAU,CAAC;EACjC,OAAON,KAAK;AACd,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACArB,WAAW,CAACmB,SAAS,CAACU,UAAU,GAAG,UAAUC,KAAK,EAAEC,YAAY,EAAE;EAChE,IAAItB,GAAG,GAAGqB,KAAK;IAAEE,QAAQ;IAAEC,QAAQ;IAAEC,KAAK;IAAEC,QAAQ;IAAEC,SAAS;IAC3DC,gBAAgB;IAAEC,eAAe;IACjCC,gBAAgB;IAAEC,eAAe;IACjCC,aAAa,GAAG,IAAI;IACpBC,cAAc,GAAG,IAAI;IACrBC,GAAG,GAAG,IAAI,CAACjC,MAAM;IACjBkC,MAAM,GAAG,IAAI,CAAC3C,GAAG,CAAC4C,UAAU,CAACf,KAAK,CAAC;;EAEvC;EACAE,QAAQ,GAAGF,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC7B,GAAG,CAAC4C,UAAU,CAACf,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI;EAE5D,OAAOrB,GAAG,GAAGkC,GAAG,IAAI,IAAI,CAAC1C,GAAG,CAAC4C,UAAU,CAACpC,GAAG,CAAC,KAAKmC,MAAM,EAAE;IAAEnC,GAAG,EAAE;EAAE;EAElEyB,KAAK,GAAGzB,GAAG,GAAGqB,KAAK;;EAEnB;EACAG,QAAQ,GAAGxB,GAAG,GAAGkC,GAAG,GAAG,IAAI,CAAC1C,GAAG,CAAC4C,UAAU,CAACpC,GAAG,CAAC,GAAG,IAAI;EAEtD6B,eAAe,GAAGvC,cAAc,CAACiC,QAAQ,CAAC,IAAIlC,WAAW,CAACgD,MAAM,CAACC,YAAY,CAACf,QAAQ,CAAC,CAAC;EACxFQ,eAAe,GAAGzC,cAAc,CAACkC,QAAQ,CAAC,IAAInC,WAAW,CAACgD,MAAM,CAACC,YAAY,CAACd,QAAQ,CAAC,CAAC;EAExFI,gBAAgB,GAAGxC,YAAY,CAACmC,QAAQ,CAAC;EACzCO,gBAAgB,GAAG1C,YAAY,CAACoC,QAAQ,CAAC;EAEzC,IAAIM,gBAAgB,EAAE;IACpBE,aAAa,GAAG,KAAK;EACvB,CAAC,MAAM,IAAID,eAAe,EAAE;IAC1B,IAAI,EAAEH,gBAAgB,IAAIC,eAAe,CAAC,EAAE;MAC1CG,aAAa,GAAG,KAAK;IACvB;EACF;EAEA,IAAIJ,gBAAgB,EAAE;IACpBK,cAAc,GAAG,KAAK;EACxB,CAAC,MAAM,IAAIJ,eAAe,EAAE;IAC1B,IAAI,EAAEC,gBAAgB,IAAIC,eAAe,CAAC,EAAE;MAC1CE,cAAc,GAAG,KAAK;IACxB;EACF;EAEA,IAAI,CAACX,YAAY,EAAE;IACjBI,QAAQ,GAAIM,aAAa,KAAM,CAACC,cAAc,IAAIJ,eAAe,CAAC;IAClEF,SAAS,GAAGM,cAAc,KAAK,CAACD,aAAa,IAAKD,eAAe,CAAC;EACpE,CAAC,MAAM;IACLL,QAAQ,GAAIM,aAAa;IACzBL,SAAS,GAAGM,cAAc;EAC5B;EAEA,OAAO;IACLP,QAAQ,EAAGA,QAAQ;IACnBC,SAAS,EAAEA,SAAS;IACpB5B,MAAM,EAAK0B;EACb,CAAC;AACH,CAAC;;AAGD;AACAlC,WAAW,CAACmB,SAAS,CAACxB,KAAK,GAAGA,KAAK;AAGnCqD,MAAM,CAACC,OAAO,GAAGjD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}