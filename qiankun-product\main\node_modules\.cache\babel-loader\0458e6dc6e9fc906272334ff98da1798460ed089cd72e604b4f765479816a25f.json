{"ast": null, "code": "// Skip text characters for text token, place those to pending buffer\n// and increment current pos\n\n'use strict';\n\n// Rule to skip pure text\n// '{}$%@~+=:' reserved for extentions\n\n// !, \", #, $, %, &, ', (, ), *, +, ,, -, ., /, :, ;, <, =, >, ?, @, [, \\, ], ^, _, `, {, |, }, or ~\n\n// !!!! Don't confuse with \"Markdown ASCII Punctuation\" chars\n// http://spec.commonmark.org/0.15/#ascii-punctuation-character\nfunction isTerminatorChar(ch) {\n  switch (ch) {\n    case 0x0A /* \\n */:\n    case 0x21 /* ! */:\n    case 0x23 /* # */:\n    case 0x24 /* $ */:\n    case 0x25 /* % */:\n    case 0x26 /* & */:\n    case 0x2A /* * */:\n    case 0x2B /* + */:\n    case 0x2D /* - */:\n    case 0x3A /* : */:\n    case 0x3C /* < */:\n    case 0x3D /* = */:\n    case 0x3E /* > */:\n    case 0x40 /* @ */:\n    case 0x5B /* [ */:\n    case 0x5C /* \\ */:\n    case 0x5D /* ] */:\n    case 0x5E /* ^ */:\n    case 0x5F /* _ */:\n    case 0x60 /* ` */:\n    case 0x7B /* { */:\n    case 0x7D /* } */:\n    case 0x7E /* ~ */:\n      return true;\n    default:\n      return false;\n  }\n}\nmodule.exports = function text(state, silent) {\n  var pos = state.pos;\n  while (pos < state.posMax && !isTerminatorChar(state.src.charCodeAt(pos))) {\n    pos++;\n  }\n  if (pos === state.pos) {\n    return false;\n  }\n  if (!silent) {\n    state.pending += state.src.slice(state.pos, pos);\n  }\n  state.pos = pos;\n  return true;\n};\n\n// Alternative implementation, for memory.\n//\n// It costs 10% of performance, but allows extend terminators list, if place it\n// to `ParcerInline` property. Probably, will switch to it sometime, such\n// flexibility required.\n\n/*\nvar TERMINATOR_RE = /[\\n!#$%&*+\\-:<=>@[\\\\\\]^_`{}~]/;\n\nmodule.exports = function text(state, silent) {\n  var pos = state.pos,\n      idx = state.src.slice(pos).search(TERMINATOR_RE);\n\n  // first char is terminator -> empty text\n  if (idx === 0) { return false; }\n\n  // no terminator -> text till end of string\n  if (idx < 0) {\n    if (!silent) { state.pending += state.src.slice(pos); }\n    state.pos = state.src.length;\n    return true;\n  }\n\n  if (!silent) { state.pending += state.src.slice(pos, pos + idx); }\n\n  state.pos += idx;\n\n  return true;\n};*/", "map": {"version": 3, "names": ["isTerminatorChar", "ch", "module", "exports", "text", "state", "silent", "pos", "posMax", "src", "charCodeAt", "pending", "slice"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_inline/text.js"], "sourcesContent": ["// Skip text characters for text token, place those to pending buffer\n// and increment current pos\n\n'use strict';\n\n\n// Rule to skip pure text\n// '{}$%@~+=:' reserved for extentions\n\n// !, \", #, $, %, &, ', (, ), *, +, ,, -, ., /, :, ;, <, =, >, ?, @, [, \\, ], ^, _, `, {, |, }, or ~\n\n// !!!! Don't confuse with \"Markdown ASCII Punctuation\" chars\n// http://spec.commonmark.org/0.15/#ascii-punctuation-character\nfunction isTerminatorChar(ch) {\n  switch (ch) {\n    case 0x0A/* \\n */:\n    case 0x21/* ! */:\n    case 0x23/* # */:\n    case 0x24/* $ */:\n    case 0x25/* % */:\n    case 0x26/* & */:\n    case 0x2A/* * */:\n    case 0x2B/* + */:\n    case 0x2D/* - */:\n    case 0x3A/* : */:\n    case 0x3C/* < */:\n    case 0x3D/* = */:\n    case 0x3E/* > */:\n    case 0x40/* @ */:\n    case 0x5B/* [ */:\n    case 0x5C/* \\ */:\n    case 0x5D/* ] */:\n    case 0x5E/* ^ */:\n    case 0x5F/* _ */:\n    case 0x60/* ` */:\n    case 0x7B/* { */:\n    case 0x7D/* } */:\n    case 0x7E/* ~ */:\n      return true;\n    default:\n      return false;\n  }\n}\n\nmodule.exports = function text(state, silent) {\n  var pos = state.pos;\n\n  while (pos < state.posMax && !isTerminatorChar(state.src.charCodeAt(pos))) {\n    pos++;\n  }\n\n  if (pos === state.pos) { return false; }\n\n  if (!silent) { state.pending += state.src.slice(state.pos, pos); }\n\n  state.pos = pos;\n\n  return true;\n};\n\n// Alternative implementation, for memory.\n//\n// It costs 10% of performance, but allows extend terminators list, if place it\n// to `ParcerInline` property. Probably, will switch to it sometime, such\n// flexibility required.\n\n/*\nvar TERMINATOR_RE = /[\\n!#$%&*+\\-:<=>@[\\\\\\]^_`{}~]/;\n\nmodule.exports = function text(state, silent) {\n  var pos = state.pos,\n      idx = state.src.slice(pos).search(TERMINATOR_RE);\n\n  // first char is terminator -> empty text\n  if (idx === 0) { return false; }\n\n  // no terminator -> text till end of string\n  if (idx < 0) {\n    if (!silent) { state.pending += state.src.slice(pos); }\n    state.pos = state.src.length;\n    return true;\n  }\n\n  if (!silent) { state.pending += state.src.slice(pos, pos + idx); }\n\n  state.pos += idx;\n\n  return true;\n};*/\n"], "mappings": "AAAA;AACA;;AAEA,YAAY;;AAGZ;AACA;;AAEA;;AAEA;AACA;AACA,SAASA,gBAAgBA,CAACC,EAAE,EAAE;EAC5B,QAAQA,EAAE;IACR,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;IACT,KAAK,IAAI;MACP,OAAO,IAAI;IACb;MACE,OAAO,KAAK;EAChB;AACF;AAEAC,MAAM,CAACC,OAAO,GAAG,SAASC,IAAIA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC5C,IAAIC,GAAG,GAAGF,KAAK,CAACE,GAAG;EAEnB,OAAOA,GAAG,GAAGF,KAAK,CAACG,MAAM,IAAI,CAACR,gBAAgB,CAACK,KAAK,CAACI,GAAG,CAACC,UAAU,CAACH,GAAG,CAAC,CAAC,EAAE;IACzEA,GAAG,EAAE;EACP;EAEA,IAAIA,GAAG,KAAKF,KAAK,CAACE,GAAG,EAAE;IAAE,OAAO,KAAK;EAAE;EAEvC,IAAI,CAACD,MAAM,EAAE;IAAED,KAAK,CAACM,OAAO,IAAIN,KAAK,CAACI,GAAG,CAACG,KAAK,CAACP,KAAK,CAACE,GAAG,EAAEA,GAAG,CAAC;EAAE;EAEjEF,KAAK,CAACE,GAAG,GAAGA,GAAG;EAEf,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}