{"ast": null, "code": "/**\n * class Renderer\n *\n * Generates HTML from parsed token stream. Each instance has independent\n * copy of rules. Those can be rewritten with ease. Also, you can add new\n * rules if you create plugin and adds new token types.\n **/\n'use strict';\n\nvar assign = require('./common/utils').assign;\nvar unescapeAll = require('./common/utils').unescapeAll;\nvar escapeHtml = require('./common/utils').escapeHtml;\n\n////////////////////////////////////////////////////////////////////////////////\n\nvar default_rules = {};\ndefault_rules.code_inline = function (tokens, idx, options, env, slf) {\n  var token = tokens[idx];\n  return '<code' + slf.renderAttrs(token) + '>' + escapeHtml(tokens[idx].content) + '</code>';\n};\ndefault_rules.code_block = function (tokens, idx, options, env, slf) {\n  var token = tokens[idx];\n  return '<pre' + slf.renderAttrs(token) + '><code>' + escapeHtml(tokens[idx].content) + '</code></pre>\\n';\n};\ndefault_rules.fence = function (tokens, idx, options, env, slf) {\n  var token = tokens[idx],\n    info = token.info ? unescapeAll(token.info).trim() : '',\n    langName = '',\n    langAttrs = '',\n    highlighted,\n    i,\n    arr,\n    tmpAttrs,\n    tmpToken;\n  if (info) {\n    arr = info.split(/(\\s+)/g);\n    langName = arr[0];\n    langAttrs = arr.slice(2).join('');\n  }\n  if (options.highlight) {\n    highlighted = options.highlight(token.content, langName, langAttrs) || escapeHtml(token.content);\n  } else {\n    highlighted = escapeHtml(token.content);\n  }\n  if (highlighted.indexOf('<pre') === 0) {\n    return highlighted + '\\n';\n  }\n\n  // If language exists, inject class gently, without modifying original token.\n  // May be, one day we will add .deepClone() for token and simplify this part, but\n  // now we prefer to keep things local.\n  if (info) {\n    i = token.attrIndex('class');\n    tmpAttrs = token.attrs ? token.attrs.slice() : [];\n    if (i < 0) {\n      tmpAttrs.push(['class', options.langPrefix + langName]);\n    } else {\n      tmpAttrs[i] = tmpAttrs[i].slice();\n      tmpAttrs[i][1] += ' ' + options.langPrefix + langName;\n    }\n\n    // Fake token just to render attributes\n    tmpToken = {\n      attrs: tmpAttrs\n    };\n    return '<pre><code' + slf.renderAttrs(tmpToken) + '>' + highlighted + '</code></pre>\\n';\n  }\n  return '<pre><code' + slf.renderAttrs(token) + '>' + highlighted + '</code></pre>\\n';\n};\ndefault_rules.image = function (tokens, idx, options, env, slf) {\n  var token = tokens[idx];\n\n  // \"alt\" attr MUST be set, even if empty. Because it's mandatory and\n  // should be placed on proper position for tests.\n  //\n  // Replace content with actual value\n\n  token.attrs[token.attrIndex('alt')][1] = slf.renderInlineAsText(token.children, options, env);\n  return slf.renderToken(tokens, idx, options);\n};\ndefault_rules.hardbreak = function (tokens, idx, options /*, env */) {\n  return options.xhtmlOut ? '<br />\\n' : '<br>\\n';\n};\ndefault_rules.softbreak = function (tokens, idx, options /*, env */) {\n  return options.breaks ? options.xhtmlOut ? '<br />\\n' : '<br>\\n' : '\\n';\n};\ndefault_rules.text = function (tokens, idx /*, options, env */) {\n  return escapeHtml(tokens[idx].content);\n};\ndefault_rules.html_block = function (tokens, idx /*, options, env */) {\n  return tokens[idx].content;\n};\ndefault_rules.html_inline = function (tokens, idx /*, options, env */) {\n  return tokens[idx].content;\n};\n\n/**\n * new Renderer()\n *\n * Creates new [[Renderer]] instance and fill [[Renderer#rules]] with defaults.\n **/\nfunction Renderer() {\n  /**\n   * Renderer#rules -> Object\n   *\n   * Contains render rules for tokens. Can be updated and extended.\n   *\n   * ##### Example\n   *\n   * ```javascript\n   * var md = require('markdown-it')();\n   *\n   * md.renderer.rules.strong_open  = function () { return '<b>'; };\n   * md.renderer.rules.strong_close = function () { return '</b>'; };\n   *\n   * var result = md.renderInline(...);\n   * ```\n   *\n   * Each rule is called as independent static function with fixed signature:\n   *\n   * ```javascript\n   * function my_token_render(tokens, idx, options, env, renderer) {\n   *   // ...\n   *   return renderedHTML;\n   * }\n   * ```\n   *\n   * See [source code](https://github.com/markdown-it/markdown-it/blob/master/lib/renderer.js)\n   * for more details and examples.\n   **/\n  this.rules = assign({}, default_rules);\n}\n\n/**\n * Renderer.renderAttrs(token) -> String\n *\n * Render token attributes to string.\n **/\nRenderer.prototype.renderAttrs = function renderAttrs(token) {\n  var i, l, result;\n  if (!token.attrs) {\n    return '';\n  }\n  result = '';\n  for (i = 0, l = token.attrs.length; i < l; i++) {\n    result += ' ' + escapeHtml(token.attrs[i][0]) + '=\"' + escapeHtml(token.attrs[i][1]) + '\"';\n  }\n  return result;\n};\n\n/**\n * Renderer.renderToken(tokens, idx, options) -> String\n * - tokens (Array): list of tokens\n * - idx (Numbed): token index to render\n * - options (Object): params of parser instance\n *\n * Default token renderer. Can be overriden by custom function\n * in [[Renderer#rules]].\n **/\nRenderer.prototype.renderToken = function renderToken(tokens, idx, options) {\n  var nextToken,\n    result = '',\n    needLf = false,\n    token = tokens[idx];\n\n  // Tight list paragraphs\n  if (token.hidden) {\n    return '';\n  }\n\n  // Insert a newline between hidden paragraph and subsequent opening\n  // block-level tag.\n  //\n  // For example, here we should insert a newline before blockquote:\n  //  - a\n  //    >\n  //\n  if (token.block && token.nesting !== -1 && idx && tokens[idx - 1].hidden) {\n    result += '\\n';\n  }\n\n  // Add token name, e.g. `<img`\n  result += (token.nesting === -1 ? '</' : '<') + token.tag;\n\n  // Encode attributes, e.g. `<img src=\"foo\"`\n  result += this.renderAttrs(token);\n\n  // Add a slash for self-closing tags, e.g. `<img src=\"foo\" /`\n  if (token.nesting === 0 && options.xhtmlOut) {\n    result += ' /';\n  }\n\n  // Check if we need to add a newline after this tag\n  if (token.block) {\n    needLf = true;\n    if (token.nesting === 1) {\n      if (idx + 1 < tokens.length) {\n        nextToken = tokens[idx + 1];\n        if (nextToken.type === 'inline' || nextToken.hidden) {\n          // Block-level tag containing an inline tag.\n          //\n          needLf = false;\n        } else if (nextToken.nesting === -1 && nextToken.tag === token.tag) {\n          // Opening tag + closing tag of the same type. E.g. `<li></li>`.\n          //\n          needLf = false;\n        }\n      }\n    }\n  }\n  result += needLf ? '>\\n' : '>';\n  return result;\n};\n\n/**\n * Renderer.renderInline(tokens, options, env) -> String\n * - tokens (Array): list on block tokens to render\n * - options (Object): params of parser instance\n * - env (Object): additional data from parsed input (references, for example)\n *\n * The same as [[Renderer.render]], but for single token of `inline` type.\n **/\nRenderer.prototype.renderInline = function (tokens, options, env) {\n  var type,\n    result = '',\n    rules = this.rules;\n  for (var i = 0, len = tokens.length; i < len; i++) {\n    type = tokens[i].type;\n    if (typeof rules[type] !== 'undefined') {\n      result += rules[type](tokens, i, options, env, this);\n    } else {\n      result += this.renderToken(tokens, i, options);\n    }\n  }\n  return result;\n};\n\n/** internal\n * Renderer.renderInlineAsText(tokens, options, env) -> String\n * - tokens (Array): list on block tokens to render\n * - options (Object): params of parser instance\n * - env (Object): additional data from parsed input (references, for example)\n *\n * Special kludge for image `alt` attributes to conform CommonMark spec.\n * Don't try to use it! Spec requires to show `alt` content with stripped markup,\n * instead of simple escaping.\n **/\nRenderer.prototype.renderInlineAsText = function (tokens, options, env) {\n  var result = '';\n  for (var i = 0, len = tokens.length; i < len; i++) {\n    if (tokens[i].type === 'text') {\n      result += tokens[i].content;\n    } else if (tokens[i].type === 'image') {\n      result += this.renderInlineAsText(tokens[i].children, options, env);\n    } else if (tokens[i].type === 'softbreak') {\n      result += '\\n';\n    }\n  }\n  return result;\n};\n\n/**\n * Renderer.render(tokens, options, env) -> String\n * - tokens (Array): list on block tokens to render\n * - options (Object): params of parser instance\n * - env (Object): additional data from parsed input (references, for example)\n *\n * Takes token stream and generates HTML. Probably, you will never need to call\n * this method directly.\n **/\nRenderer.prototype.render = function (tokens, options, env) {\n  var i,\n    len,\n    type,\n    result = '',\n    rules = this.rules;\n  for (i = 0, len = tokens.length; i < len; i++) {\n    type = tokens[i].type;\n    if (type === 'inline') {\n      result += this.renderInline(tokens[i].children, options, env);\n    } else if (typeof rules[type] !== 'undefined') {\n      result += rules[tokens[i].type](tokens, i, options, env, this);\n    } else {\n      result += this.renderToken(tokens, i, options, env);\n    }\n  }\n  return result;\n};\nmodule.exports = Renderer;", "map": {"version": 3, "names": ["assign", "require", "unescapeAll", "escapeHtml", "default_rules", "code_inline", "tokens", "idx", "options", "env", "slf", "token", "renderAttrs", "content", "code_block", "fence", "info", "trim", "langName", "langAttrs", "highlighted", "i", "arr", "tmpAttrs", "tmpToken", "split", "slice", "join", "highlight", "indexOf", "attrIndex", "attrs", "push", "langPrefix", "image", "renderInlineAsText", "children", "renderToken", "hardbreak", "xhtmlOut", "softbreak", "breaks", "text", "html_block", "html_inline", "<PERSON><PERSON><PERSON>", "rules", "prototype", "l", "result", "length", "nextToken", "needLf", "hidden", "block", "nesting", "tag", "type", "renderInline", "len", "render", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/renderer.js"], "sourcesContent": ["/**\n * class Renderer\n *\n * Generates HTML from parsed token stream. Each instance has independent\n * copy of rules. Those can be rewritten with ease. Also, you can add new\n * rules if you create plugin and adds new token types.\n **/\n'use strict';\n\n\nvar assign          = require('./common/utils').assign;\nvar unescapeAll     = require('./common/utils').unescapeAll;\nvar escapeHtml      = require('./common/utils').escapeHtml;\n\n\n////////////////////////////////////////////////////////////////////////////////\n\nvar default_rules = {};\n\n\ndefault_rules.code_inline = function (tokens, idx, options, env, slf) {\n  var token = tokens[idx];\n\n  return  '<code' + slf.renderAttrs(token) + '>' +\n          escapeHtml(tokens[idx].content) +\n          '</code>';\n};\n\n\ndefault_rules.code_block = function (tokens, idx, options, env, slf) {\n  var token = tokens[idx];\n\n  return  '<pre' + slf.renderAttrs(token) + '><code>' +\n          escapeHtml(tokens[idx].content) +\n          '</code></pre>\\n';\n};\n\n\ndefault_rules.fence = function (tokens, idx, options, env, slf) {\n  var token = tokens[idx],\n      info = token.info ? unescapeAll(token.info).trim() : '',\n      langName = '',\n      langAttrs = '',\n      highlighted, i, arr, tmpAttrs, tmpToken;\n\n  if (info) {\n    arr = info.split(/(\\s+)/g);\n    langName = arr[0];\n    langAttrs = arr.slice(2).join('');\n  }\n\n  if (options.highlight) {\n    highlighted = options.highlight(token.content, langName, langAttrs) || escapeHtml(token.content);\n  } else {\n    highlighted = escapeHtml(token.content);\n  }\n\n  if (highlighted.indexOf('<pre') === 0) {\n    return highlighted + '\\n';\n  }\n\n  // If language exists, inject class gently, without modifying original token.\n  // May be, one day we will add .deepClone() for token and simplify this part, but\n  // now we prefer to keep things local.\n  if (info) {\n    i        = token.attrIndex('class');\n    tmpAttrs = token.attrs ? token.attrs.slice() : [];\n\n    if (i < 0) {\n      tmpAttrs.push([ 'class', options.langPrefix + langName ]);\n    } else {\n      tmpAttrs[i] = tmpAttrs[i].slice();\n      tmpAttrs[i][1] += ' ' + options.langPrefix + langName;\n    }\n\n    // Fake token just to render attributes\n    tmpToken = {\n      attrs: tmpAttrs\n    };\n\n    return  '<pre><code' + slf.renderAttrs(tmpToken) + '>'\n          + highlighted\n          + '</code></pre>\\n';\n  }\n\n\n  return  '<pre><code' + slf.renderAttrs(token) + '>'\n        + highlighted\n        + '</code></pre>\\n';\n};\n\n\ndefault_rules.image = function (tokens, idx, options, env, slf) {\n  var token = tokens[idx];\n\n  // \"alt\" attr MUST be set, even if empty. Because it's mandatory and\n  // should be placed on proper position for tests.\n  //\n  // Replace content with actual value\n\n  token.attrs[token.attrIndex('alt')][1] =\n    slf.renderInlineAsText(token.children, options, env);\n\n  return slf.renderToken(tokens, idx, options);\n};\n\n\ndefault_rules.hardbreak = function (tokens, idx, options /*, env */) {\n  return options.xhtmlOut ? '<br />\\n' : '<br>\\n';\n};\ndefault_rules.softbreak = function (tokens, idx, options /*, env */) {\n  return options.breaks ? (options.xhtmlOut ? '<br />\\n' : '<br>\\n') : '\\n';\n};\n\n\ndefault_rules.text = function (tokens, idx /*, options, env */) {\n  return escapeHtml(tokens[idx].content);\n};\n\n\ndefault_rules.html_block = function (tokens, idx /*, options, env */) {\n  return tokens[idx].content;\n};\ndefault_rules.html_inline = function (tokens, idx /*, options, env */) {\n  return tokens[idx].content;\n};\n\n\n/**\n * new Renderer()\n *\n * Creates new [[Renderer]] instance and fill [[Renderer#rules]] with defaults.\n **/\nfunction Renderer() {\n\n  /**\n   * Renderer#rules -> Object\n   *\n   * Contains render rules for tokens. Can be updated and extended.\n   *\n   * ##### Example\n   *\n   * ```javascript\n   * var md = require('markdown-it')();\n   *\n   * md.renderer.rules.strong_open  = function () { return '<b>'; };\n   * md.renderer.rules.strong_close = function () { return '</b>'; };\n   *\n   * var result = md.renderInline(...);\n   * ```\n   *\n   * Each rule is called as independent static function with fixed signature:\n   *\n   * ```javascript\n   * function my_token_render(tokens, idx, options, env, renderer) {\n   *   // ...\n   *   return renderedHTML;\n   * }\n   * ```\n   *\n   * See [source code](https://github.com/markdown-it/markdown-it/blob/master/lib/renderer.js)\n   * for more details and examples.\n   **/\n  this.rules = assign({}, default_rules);\n}\n\n\n/**\n * Renderer.renderAttrs(token) -> String\n *\n * Render token attributes to string.\n **/\nRenderer.prototype.renderAttrs = function renderAttrs(token) {\n  var i, l, result;\n\n  if (!token.attrs) { return ''; }\n\n  result = '';\n\n  for (i = 0, l = token.attrs.length; i < l; i++) {\n    result += ' ' + escapeHtml(token.attrs[i][0]) + '=\"' + escapeHtml(token.attrs[i][1]) + '\"';\n  }\n\n  return result;\n};\n\n\n/**\n * Renderer.renderToken(tokens, idx, options) -> String\n * - tokens (Array): list of tokens\n * - idx (Numbed): token index to render\n * - options (Object): params of parser instance\n *\n * Default token renderer. Can be overriden by custom function\n * in [[Renderer#rules]].\n **/\nRenderer.prototype.renderToken = function renderToken(tokens, idx, options) {\n  var nextToken,\n      result = '',\n      needLf = false,\n      token = tokens[idx];\n\n  // Tight list paragraphs\n  if (token.hidden) {\n    return '';\n  }\n\n  // Insert a newline between hidden paragraph and subsequent opening\n  // block-level tag.\n  //\n  // For example, here we should insert a newline before blockquote:\n  //  - a\n  //    >\n  //\n  if (token.block && token.nesting !== -1 && idx && tokens[idx - 1].hidden) {\n    result += '\\n';\n  }\n\n  // Add token name, e.g. `<img`\n  result += (token.nesting === -1 ? '</' : '<') + token.tag;\n\n  // Encode attributes, e.g. `<img src=\"foo\"`\n  result += this.renderAttrs(token);\n\n  // Add a slash for self-closing tags, e.g. `<img src=\"foo\" /`\n  if (token.nesting === 0 && options.xhtmlOut) {\n    result += ' /';\n  }\n\n  // Check if we need to add a newline after this tag\n  if (token.block) {\n    needLf = true;\n\n    if (token.nesting === 1) {\n      if (idx + 1 < tokens.length) {\n        nextToken = tokens[idx + 1];\n\n        if (nextToken.type === 'inline' || nextToken.hidden) {\n          // Block-level tag containing an inline tag.\n          //\n          needLf = false;\n\n        } else if (nextToken.nesting === -1 && nextToken.tag === token.tag) {\n          // Opening tag + closing tag of the same type. E.g. `<li></li>`.\n          //\n          needLf = false;\n        }\n      }\n    }\n  }\n\n  result += needLf ? '>\\n' : '>';\n\n  return result;\n};\n\n\n/**\n * Renderer.renderInline(tokens, options, env) -> String\n * - tokens (Array): list on block tokens to render\n * - options (Object): params of parser instance\n * - env (Object): additional data from parsed input (references, for example)\n *\n * The same as [[Renderer.render]], but for single token of `inline` type.\n **/\nRenderer.prototype.renderInline = function (tokens, options, env) {\n  var type,\n      result = '',\n      rules = this.rules;\n\n  for (var i = 0, len = tokens.length; i < len; i++) {\n    type = tokens[i].type;\n\n    if (typeof rules[type] !== 'undefined') {\n      result += rules[type](tokens, i, options, env, this);\n    } else {\n      result += this.renderToken(tokens, i, options);\n    }\n  }\n\n  return result;\n};\n\n\n/** internal\n * Renderer.renderInlineAsText(tokens, options, env) -> String\n * - tokens (Array): list on block tokens to render\n * - options (Object): params of parser instance\n * - env (Object): additional data from parsed input (references, for example)\n *\n * Special kludge for image `alt` attributes to conform CommonMark spec.\n * Don't try to use it! Spec requires to show `alt` content with stripped markup,\n * instead of simple escaping.\n **/\nRenderer.prototype.renderInlineAsText = function (tokens, options, env) {\n  var result = '';\n\n  for (var i = 0, len = tokens.length; i < len; i++) {\n    if (tokens[i].type === 'text') {\n      result += tokens[i].content;\n    } else if (tokens[i].type === 'image') {\n      result += this.renderInlineAsText(tokens[i].children, options, env);\n    } else if (tokens[i].type === 'softbreak') {\n      result += '\\n';\n    }\n  }\n\n  return result;\n};\n\n\n/**\n * Renderer.render(tokens, options, env) -> String\n * - tokens (Array): list on block tokens to render\n * - options (Object): params of parser instance\n * - env (Object): additional data from parsed input (references, for example)\n *\n * Takes token stream and generates HTML. Probably, you will never need to call\n * this method directly.\n **/\nRenderer.prototype.render = function (tokens, options, env) {\n  var i, len, type,\n      result = '',\n      rules = this.rules;\n\n  for (i = 0, len = tokens.length; i < len; i++) {\n    type = tokens[i].type;\n\n    if (type === 'inline') {\n      result += this.renderInline(tokens[i].children, options, env);\n    } else if (typeof rules[type] !== 'undefined') {\n      result += rules[tokens[i].type](tokens, i, options, env, this);\n    } else {\n      result += this.renderToken(tokens, i, options, env);\n    }\n  }\n\n  return result;\n};\n\nmodule.exports = Renderer;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAGZ,IAAIA,MAAM,GAAYC,OAAO,CAAC,gBAAgB,CAAC,CAACD,MAAM;AACtD,IAAIE,WAAW,GAAOD,OAAO,CAAC,gBAAgB,CAAC,CAACC,WAAW;AAC3D,IAAIC,UAAU,GAAQF,OAAO,CAAC,gBAAgB,CAAC,CAACE,UAAU;;AAG1D;;AAEA,IAAIC,aAAa,GAAG,CAAC,CAAC;AAGtBA,aAAa,CAACC,WAAW,GAAG,UAAUC,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACpE,IAAIC,KAAK,GAAGL,MAAM,CAACC,GAAG,CAAC;EAEvB,OAAQ,OAAO,GAAGG,GAAG,CAACE,WAAW,CAACD,KAAK,CAAC,GAAG,GAAG,GACtCR,UAAU,CAACG,MAAM,CAACC,GAAG,CAAC,CAACM,OAAO,CAAC,GAC/B,SAAS;AACnB,CAAC;AAGDT,aAAa,CAACU,UAAU,GAAG,UAAUR,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,GAAG,EAAEC,GAAG,EAAE;EACnE,IAAIC,KAAK,GAAGL,MAAM,CAACC,GAAG,CAAC;EAEvB,OAAQ,MAAM,GAAGG,GAAG,CAACE,WAAW,CAACD,KAAK,CAAC,GAAG,SAAS,GAC3CR,UAAU,CAACG,MAAM,CAACC,GAAG,CAAC,CAACM,OAAO,CAAC,GAC/B,iBAAiB;AAC3B,CAAC;AAGDT,aAAa,CAACW,KAAK,GAAG,UAAUT,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC9D,IAAIC,KAAK,GAAGL,MAAM,CAACC,GAAG,CAAC;IACnBS,IAAI,GAAGL,KAAK,CAACK,IAAI,GAAGd,WAAW,CAACS,KAAK,CAACK,IAAI,CAAC,CAACC,IAAI,CAAC,CAAC,GAAG,EAAE;IACvDC,QAAQ,GAAG,EAAE;IACbC,SAAS,GAAG,EAAE;IACdC,WAAW;IAAEC,CAAC;IAAEC,GAAG;IAAEC,QAAQ;IAAEC,QAAQ;EAE3C,IAAIR,IAAI,EAAE;IACRM,GAAG,GAAGN,IAAI,CAACS,KAAK,CAAC,QAAQ,CAAC;IAC1BP,QAAQ,GAAGI,GAAG,CAAC,CAAC,CAAC;IACjBH,SAAS,GAAGG,GAAG,CAACI,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EACnC;EAEA,IAAInB,OAAO,CAACoB,SAAS,EAAE;IACrBR,WAAW,GAAGZ,OAAO,CAACoB,SAAS,CAACjB,KAAK,CAACE,OAAO,EAAEK,QAAQ,EAAEC,SAAS,CAAC,IAAIhB,UAAU,CAACQ,KAAK,CAACE,OAAO,CAAC;EAClG,CAAC,MAAM;IACLO,WAAW,GAAGjB,UAAU,CAACQ,KAAK,CAACE,OAAO,CAAC;EACzC;EAEA,IAAIO,WAAW,CAACS,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;IACrC,OAAOT,WAAW,GAAG,IAAI;EAC3B;;EAEA;EACA;EACA;EACA,IAAIJ,IAAI,EAAE;IACRK,CAAC,GAAUV,KAAK,CAACmB,SAAS,CAAC,OAAO,CAAC;IACnCP,QAAQ,GAAGZ,KAAK,CAACoB,KAAK,GAAGpB,KAAK,CAACoB,KAAK,CAACL,KAAK,CAAC,CAAC,GAAG,EAAE;IAEjD,IAAIL,CAAC,GAAG,CAAC,EAAE;MACTE,QAAQ,CAACS,IAAI,CAAC,CAAE,OAAO,EAAExB,OAAO,CAACyB,UAAU,GAAGf,QAAQ,CAAE,CAAC;IAC3D,CAAC,MAAM;MACLK,QAAQ,CAACF,CAAC,CAAC,GAAGE,QAAQ,CAACF,CAAC,CAAC,CAACK,KAAK,CAAC,CAAC;MACjCH,QAAQ,CAACF,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,GAAGb,OAAO,CAACyB,UAAU,GAAGf,QAAQ;IACvD;;IAEA;IACAM,QAAQ,GAAG;MACTO,KAAK,EAAER;IACT,CAAC;IAED,OAAQ,YAAY,GAAGb,GAAG,CAACE,WAAW,CAACY,QAAQ,CAAC,GAAG,GAAG,GAC9CJ,WAAW,GACX,iBAAiB;EAC3B;EAGA,OAAQ,YAAY,GAAGV,GAAG,CAACE,WAAW,CAACD,KAAK,CAAC,GAAG,GAAG,GAC3CS,WAAW,GACX,iBAAiB;AAC3B,CAAC;AAGDhB,aAAa,CAAC8B,KAAK,GAAG,UAAU5B,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAEC,GAAG,EAAEC,GAAG,EAAE;EAC9D,IAAIC,KAAK,GAAGL,MAAM,CAACC,GAAG,CAAC;;EAEvB;EACA;EACA;EACA;;EAEAI,KAAK,CAACoB,KAAK,CAACpB,KAAK,CAACmB,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,GACpCpB,GAAG,CAACyB,kBAAkB,CAACxB,KAAK,CAACyB,QAAQ,EAAE5B,OAAO,EAAEC,GAAG,CAAC;EAEtD,OAAOC,GAAG,CAAC2B,WAAW,CAAC/B,MAAM,EAAEC,GAAG,EAAEC,OAAO,CAAC;AAC9C,CAAC;AAGDJ,aAAa,CAACkC,SAAS,GAAG,UAAUhC,MAAM,EAAEC,GAAG,EAAEC,OAAO,CAAC,YAAY;EACnE,OAAOA,OAAO,CAAC+B,QAAQ,GAAG,UAAU,GAAG,QAAQ;AACjD,CAAC;AACDnC,aAAa,CAACoC,SAAS,GAAG,UAAUlC,MAAM,EAAEC,GAAG,EAAEC,OAAO,CAAC,YAAY;EACnE,OAAOA,OAAO,CAACiC,MAAM,GAAIjC,OAAO,CAAC+B,QAAQ,GAAG,UAAU,GAAG,QAAQ,GAAI,IAAI;AAC3E,CAAC;AAGDnC,aAAa,CAACsC,IAAI,GAAG,UAAUpC,MAAM,EAAEC,GAAG,CAAC,qBAAqB;EAC9D,OAAOJ,UAAU,CAACG,MAAM,CAACC,GAAG,CAAC,CAACM,OAAO,CAAC;AACxC,CAAC;AAGDT,aAAa,CAACuC,UAAU,GAAG,UAAUrC,MAAM,EAAEC,GAAG,CAAC,qBAAqB;EACpE,OAAOD,MAAM,CAACC,GAAG,CAAC,CAACM,OAAO;AAC5B,CAAC;AACDT,aAAa,CAACwC,WAAW,GAAG,UAAUtC,MAAM,EAAEC,GAAG,CAAC,qBAAqB;EACrE,OAAOD,MAAM,CAACC,GAAG,CAAC,CAACM,OAAO;AAC5B,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA,SAASgC,QAAQA,CAAA,EAAG;EAElB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAI,CAACC,KAAK,GAAG9C,MAAM,CAAC,CAAC,CAAC,EAAEI,aAAa,CAAC;AACxC;;AAGA;AACA;AACA;AACA;AACA;AACAyC,QAAQ,CAACE,SAAS,CAACnC,WAAW,GAAG,SAASA,WAAWA,CAACD,KAAK,EAAE;EAC3D,IAAIU,CAAC,EAAE2B,CAAC,EAAEC,MAAM;EAEhB,IAAI,CAACtC,KAAK,CAACoB,KAAK,EAAE;IAAE,OAAO,EAAE;EAAE;EAE/BkB,MAAM,GAAG,EAAE;EAEX,KAAK5B,CAAC,GAAG,CAAC,EAAE2B,CAAC,GAAGrC,KAAK,CAACoB,KAAK,CAACmB,MAAM,EAAE7B,CAAC,GAAG2B,CAAC,EAAE3B,CAAC,EAAE,EAAE;IAC9C4B,MAAM,IAAI,GAAG,GAAG9C,UAAU,CAACQ,KAAK,CAACoB,KAAK,CAACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGlB,UAAU,CAACQ,KAAK,CAACoB,KAAK,CAACV,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG;EAC5F;EAEA,OAAO4B,MAAM;AACf,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAJ,QAAQ,CAACE,SAAS,CAACV,WAAW,GAAG,SAASA,WAAWA,CAAC/B,MAAM,EAAEC,GAAG,EAAEC,OAAO,EAAE;EAC1E,IAAI2C,SAAS;IACTF,MAAM,GAAG,EAAE;IACXG,MAAM,GAAG,KAAK;IACdzC,KAAK,GAAGL,MAAM,CAACC,GAAG,CAAC;;EAEvB;EACA,IAAII,KAAK,CAAC0C,MAAM,EAAE;IAChB,OAAO,EAAE;EACX;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAI1C,KAAK,CAAC2C,KAAK,IAAI3C,KAAK,CAAC4C,OAAO,KAAK,CAAC,CAAC,IAAIhD,GAAG,IAAID,MAAM,CAACC,GAAG,GAAG,CAAC,CAAC,CAAC8C,MAAM,EAAE;IACxEJ,MAAM,IAAI,IAAI;EAChB;;EAEA;EACAA,MAAM,IAAI,CAACtC,KAAK,CAAC4C,OAAO,KAAK,CAAC,CAAC,GAAG,IAAI,GAAG,GAAG,IAAI5C,KAAK,CAAC6C,GAAG;;EAEzD;EACAP,MAAM,IAAI,IAAI,CAACrC,WAAW,CAACD,KAAK,CAAC;;EAEjC;EACA,IAAIA,KAAK,CAAC4C,OAAO,KAAK,CAAC,IAAI/C,OAAO,CAAC+B,QAAQ,EAAE;IAC3CU,MAAM,IAAI,IAAI;EAChB;;EAEA;EACA,IAAItC,KAAK,CAAC2C,KAAK,EAAE;IACfF,MAAM,GAAG,IAAI;IAEb,IAAIzC,KAAK,CAAC4C,OAAO,KAAK,CAAC,EAAE;MACvB,IAAIhD,GAAG,GAAG,CAAC,GAAGD,MAAM,CAAC4C,MAAM,EAAE;QAC3BC,SAAS,GAAG7C,MAAM,CAACC,GAAG,GAAG,CAAC,CAAC;QAE3B,IAAI4C,SAAS,CAACM,IAAI,KAAK,QAAQ,IAAIN,SAAS,CAACE,MAAM,EAAE;UACnD;UACA;UACAD,MAAM,GAAG,KAAK;QAEhB,CAAC,MAAM,IAAID,SAAS,CAACI,OAAO,KAAK,CAAC,CAAC,IAAIJ,SAAS,CAACK,GAAG,KAAK7C,KAAK,CAAC6C,GAAG,EAAE;UAClE;UACA;UACAJ,MAAM,GAAG,KAAK;QAChB;MACF;IACF;EACF;EAEAH,MAAM,IAAIG,MAAM,GAAG,KAAK,GAAG,GAAG;EAE9B,OAAOH,MAAM;AACf,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAJ,QAAQ,CAACE,SAAS,CAACW,YAAY,GAAG,UAAUpD,MAAM,EAAEE,OAAO,EAAEC,GAAG,EAAE;EAChE,IAAIgD,IAAI;IACJR,MAAM,GAAG,EAAE;IACXH,KAAK,GAAG,IAAI,CAACA,KAAK;EAEtB,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEsC,GAAG,GAAGrD,MAAM,CAAC4C,MAAM,EAAE7B,CAAC,GAAGsC,GAAG,EAAEtC,CAAC,EAAE,EAAE;IACjDoC,IAAI,GAAGnD,MAAM,CAACe,CAAC,CAAC,CAACoC,IAAI;IAErB,IAAI,OAAOX,KAAK,CAACW,IAAI,CAAC,KAAK,WAAW,EAAE;MACtCR,MAAM,IAAIH,KAAK,CAACW,IAAI,CAAC,CAACnD,MAAM,EAAEe,CAAC,EAAEb,OAAO,EAAEC,GAAG,EAAE,IAAI,CAAC;IACtD,CAAC,MAAM;MACLwC,MAAM,IAAI,IAAI,CAACZ,WAAW,CAAC/B,MAAM,EAAEe,CAAC,EAAEb,OAAO,CAAC;IAChD;EACF;EAEA,OAAOyC,MAAM;AACf,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAJ,QAAQ,CAACE,SAAS,CAACZ,kBAAkB,GAAG,UAAU7B,MAAM,EAAEE,OAAO,EAAEC,GAAG,EAAE;EACtE,IAAIwC,MAAM,GAAG,EAAE;EAEf,KAAK,IAAI5B,CAAC,GAAG,CAAC,EAAEsC,GAAG,GAAGrD,MAAM,CAAC4C,MAAM,EAAE7B,CAAC,GAAGsC,GAAG,EAAEtC,CAAC,EAAE,EAAE;IACjD,IAAIf,MAAM,CAACe,CAAC,CAAC,CAACoC,IAAI,KAAK,MAAM,EAAE;MAC7BR,MAAM,IAAI3C,MAAM,CAACe,CAAC,CAAC,CAACR,OAAO;IAC7B,CAAC,MAAM,IAAIP,MAAM,CAACe,CAAC,CAAC,CAACoC,IAAI,KAAK,OAAO,EAAE;MACrCR,MAAM,IAAI,IAAI,CAACd,kBAAkB,CAAC7B,MAAM,CAACe,CAAC,CAAC,CAACe,QAAQ,EAAE5B,OAAO,EAAEC,GAAG,CAAC;IACrE,CAAC,MAAM,IAAIH,MAAM,CAACe,CAAC,CAAC,CAACoC,IAAI,KAAK,WAAW,EAAE;MACzCR,MAAM,IAAI,IAAI;IAChB;EACF;EAEA,OAAOA,MAAM;AACf,CAAC;;AAGD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAJ,QAAQ,CAACE,SAAS,CAACa,MAAM,GAAG,UAAUtD,MAAM,EAAEE,OAAO,EAAEC,GAAG,EAAE;EAC1D,IAAIY,CAAC;IAAEsC,GAAG;IAAEF,IAAI;IACZR,MAAM,GAAG,EAAE;IACXH,KAAK,GAAG,IAAI,CAACA,KAAK;EAEtB,KAAKzB,CAAC,GAAG,CAAC,EAAEsC,GAAG,GAAGrD,MAAM,CAAC4C,MAAM,EAAE7B,CAAC,GAAGsC,GAAG,EAAEtC,CAAC,EAAE,EAAE;IAC7CoC,IAAI,GAAGnD,MAAM,CAACe,CAAC,CAAC,CAACoC,IAAI;IAErB,IAAIA,IAAI,KAAK,QAAQ,EAAE;MACrBR,MAAM,IAAI,IAAI,CAACS,YAAY,CAACpD,MAAM,CAACe,CAAC,CAAC,CAACe,QAAQ,EAAE5B,OAAO,EAAEC,GAAG,CAAC;IAC/D,CAAC,MAAM,IAAI,OAAOqC,KAAK,CAACW,IAAI,CAAC,KAAK,WAAW,EAAE;MAC7CR,MAAM,IAAIH,KAAK,CAACxC,MAAM,CAACe,CAAC,CAAC,CAACoC,IAAI,CAAC,CAACnD,MAAM,EAAEe,CAAC,EAAEb,OAAO,EAAEC,GAAG,EAAE,IAAI,CAAC;IAChE,CAAC,MAAM;MACLwC,MAAM,IAAI,IAAI,CAACZ,WAAW,CAAC/B,MAAM,EAAEe,CAAC,EAAEb,OAAO,EAAEC,GAAG,CAAC;IACrD;EACF;EAEA,OAAOwC,MAAM;AACf,CAAC;AAEDY,MAAM,CAACC,OAAO,GAAGjB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}