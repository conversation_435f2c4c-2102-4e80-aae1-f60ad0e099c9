{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, withCtx as _withCtx, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, createTextVNode as _createTextVNode, createBlock as _createBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalChatViewWindowBox\"\n};\nvar _hoisted_2 = {\n  key: 0,\n  class: \"GlobalChatViewWindowBody\"\n};\nvar _hoisted_3 = {\n  class: \"GlobalChatViewWindowUser\"\n};\nvar _hoisted_4 = {\n  class: \"GlobalChatViewWindowUserName ellipsis\"\n};\nvar _hoisted_5 = {\n  class: \"GlobalChatViewWindowUserControls\"\n};\nvar _hoisted_6 = {\n  key: 1,\n  class: \"GlobalChatViewWindowBody\"\n};\nvar _hoisted_7 = {\n  class: \"GlobalChatViewWindowUserInput\"\n};\nvar _hoisted_8 = {\n  key: 0,\n  class: \"GlobalChatViewWindowUserLogo\"\n};\nvar _hoisted_9 = {\n  class: \"GlobalChatViewWindowUserName ellipsis\"\n};\nvar _hoisted_10 = {\n  class: \"GlobalChatViewWindowUserControls\"\n};\nvar _hoisted_11 = {\n  class: \"GlobalChatViewWindowUserControls\"\n};\nvar _hoisted_12 = {\n  key: 2,\n  class: \"GlobalChatViewWindowControls\"\n};\nvar _hoisted_13 = {\n  class: \"GlobalChatViewWindowGroupName ellipsis\"\n};\nvar _hoisted_14 = {\n  class: \"GlobalChatViewWindowGroupName ellipsis\"\n};\nvar _hoisted_15 = [\"innerHTML\"];\nvar _hoisted_16 = {\n  class: \"GlobalChatViewWindowGroupName ellipsis\"\n};\nvar _hoisted_17 = {\n  class: \"GlobalChatViewWindowGroupName ellipsis\"\n};\nvar _hoisted_18 = {\n  class: \"GlobalChatViewWindowControls\"\n};\nvar _hoisted_19 = {\n  class: \"GlobalChatViewWindowControlsItem\"\n};\nvar _hoisted_20 = {\n  class: \"GlobalChatViewWindowControlsItem\"\n};\nvar _hoisted_21 = {\n  class: \"GlobalChatViewWindowControlsItem\"\n};\nvar _hoisted_22 = {\n  key: 3,\n  class: \"GlobalChatViewWindowControls\"\n};\nvar _hoisted_23 = {\n  key: 4,\n  class: \"GlobalChatViewWindowControls\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_Plus = _resolveComponent(\"Plus\");\n  var _component_el_icon = _resolveComponent(\"el-icon\");\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_Minus = _resolveComponent(\"Minus\");\n  var _component_ArrowDown = _resolveComponent(\"ArrowDown\");\n  var _component_ArrowUp = _resolveComponent(\"ArrowUp\");\n  var _component_ArrowRight = _resolveComponent(\"ArrowRight\");\n  var _component_el_switch = _resolveComponent(\"el-switch\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  return _openBlock(), _createBlock(_component_el_scrollbar, {\n    class: \"GlobalChatViewWindow\"\n  }, {\n    default: _withCtx(function () {\n      var _$setup$chatInfo, _$setup$chatInfo2, _$setup$chatInfo3, _$setup$chatInfo4, _$setup$chatInfo5;\n      return [_createElementVNode(\"div\", _hoisted_1, [((_$setup$chatInfo = $setup.chatInfo) === null || _$setup$chatInfo === void 0 ? void 0 : _$setup$chatInfo.type) === 1 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_image, {\n        src: $setup.imgUrl($setup.chatInfo.chatObjectInfo.img),\n        fit: \"cover\",\n        draggable: \"false\"\n      }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_4, _toDisplayString($setup.chatInfo.chatObjectInfo.name), 1 /* TEXT */)]), _createElementVNode(\"div\", {\n        class: \"GlobalChatViewWindowUser\",\n        onClick: $setup.handleCreateGroup\n      }, [_createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_Plus)];\n        }),\n        _: 1 /* STABLE */\n      })])])])) : _createCommentVNode(\"v-if\", true), ((_$setup$chatInfo2 = $setup.chatInfo) === null || _$setup$chatInfo2 === void 0 ? void 0 : _$setup$chatInfo2.type) === 3 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [_createElementVNode(\"div\", _hoisted_7, [_createVNode(_component_el_input, {\n        modelValue: $setup.keyword,\n        \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n          return $setup.keyword = $event;\n        }),\n        \"prefix-icon\": $setup.Search,\n        placeholder: \"搜索群成员\",\n        clearable: \"\"\n      }, null, 8 /* PROPS */, [\"modelValue\", \"prefix-icon\"])]), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($setup.groupUser, function (item) {\n        return _openBlock(), _createElementBlock(\"div\", {\n          class: \"GlobalChatViewWindowUser\",\n          key: item.accountId\n        }, [item.isOwner ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, \"群主\")) : _createCommentVNode(\"v-if\", true), _createVNode(_component_el_image, {\n          src: $setup.imgUrl(item.photo || item.headImg),\n          fit: \"cover\",\n          draggable: \"false\"\n        }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_9, _toDisplayString(item.userName), 1 /* TEXT */)]);\n      }), 128 /* KEYED_FRAGMENT */)), !$setup.keyword ? (_openBlock(), _createElementBlock(\"div\", {\n        key: 0,\n        class: \"GlobalChatViewWindowUser\",\n        onClick: $setup.handleGroupAddUser\n      }, [_createElementVNode(\"div\", _hoisted_10, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_Plus)];\n        }),\n        _: 1 /* STABLE */\n      })])])) : _createCommentVNode(\"v-if\", true), $setup.isOwner && !$setup.keyword ? (_openBlock(), _createElementBlock(\"div\", {\n        key: 1,\n        class: \"GlobalChatViewWindowUser\",\n        onClick: $setup.handleGroupDelUser\n      }, [_createElementVNode(\"div\", _hoisted_11, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_Minus)];\n        }),\n        _: 1 /* STABLE */\n      })])])) : _createCommentVNode(\"v-if\", true), $setup.isUserLength && !$setup.keyword && !$setup.isAllUser ? (_openBlock(), _createElementBlock(\"div\", {\n        key: 2,\n        class: \"GlobalChatViewWindowUserButton\",\n        onClick: _cache[1] || (_cache[1] = function ($event) {\n          return $setup.isAllUser = !$setup.isAllUser;\n        })\n      }, [_cache[6] || (_cache[6] = _createTextVNode(\"展开更多 \")), _createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_ArrowDown)];\n        }),\n        _: 1 /* STABLE */\n      })])) : _createCommentVNode(\"v-if\", true), $setup.isUserLength && !$setup.keyword && $setup.isAllUser ? (_openBlock(), _createElementBlock(\"div\", {\n        key: 3,\n        class: \"GlobalChatViewWindowUserButton\",\n        onClick: _cache[2] || (_cache[2] = function ($event) {\n          return $setup.isAllUser = !$setup.isAllUser;\n        })\n      }, [_cache[7] || (_cache[7] = _createTextVNode(\"收起 \")), _createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_ArrowUp)];\n        }),\n        _: 1 /* STABLE */\n      })])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true), ((_$setup$chatInfo3 = $setup.chatInfo) === null || _$setup$chatInfo3 === void 0 ? void 0 : _$setup$chatInfo3.type) === 3 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_12, [_createElementVNode(\"div\", {\n        class: \"GlobalChatViewWindowControlsItem\",\n        onClick: $setup.handleGroupName\n      }, [_cache[8] || (_cache[8] = _createElementVNode(\"div\", null, \"群组名称\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_13, [_createTextVNode(_toDisplayString($setup.chatInfo.chatObjectInfo.name) + \" \", 1 /* TEXT */), _createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_ArrowRight)];\n        }),\n        _: 1 /* STABLE */\n      })])]), _createElementVNode(\"div\", {\n        class: \"GlobalChatViewWindowControlsItem\",\n        onClick: $setup.handleGroupQr\n      }, [_cache[9] || (_cache[9] = _createElementVNode(\"div\", null, \"群组二维码\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_14, [_createElementVNode(\"div\", {\n        innerHTML: $setup.qrCodeIcon\n      }, null, 8 /* PROPS */, _hoisted_15), _createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_ArrowRight)];\n        }),\n        _: 1 /* STABLE */\n      })])]), _createElementVNode(\"div\", {\n        class: \"GlobalChatViewWindowControlsItem\",\n        onClick: $setup.handleGroupAnnouncement\n      }, [_cache[10] || (_cache[10] = _createElementVNode(\"div\", null, \"群公告\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_16, [_createTextVNode(_toDisplayString($setup.groupInfo.callBoard) + \" \", 1 /* TEXT */), _createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_ArrowRight)];\n        }),\n        _: 1 /* STABLE */\n      })])]), $setup.isOwner ? (_openBlock(), _createElementBlock(\"div\", {\n        key: 0,\n        class: \"GlobalChatViewWindowControlsItem\",\n        onClick: $setup.handleGroupTransfer\n      }, [_cache[11] || (_cache[11] = _createElementVNode(\"div\", null, \"转让群主\", -1 /* HOISTED */)), _createElementVNode(\"div\", _hoisted_17, [_createVNode(_component_el_icon, null, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_ArrowRight)];\n        }),\n        _: 1 /* STABLE */\n      })])])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"div\", _hoisted_18, [_createElementVNode(\"div\", _hoisted_19, [_cache[12] || (_cache[12] = _createElementVNode(\"div\", null, \"消息免打扰\", -1 /* HOISTED */)), _createVNode(_component_el_switch, {\n        modelValue: $setup.notification,\n        \"onUpdate:modelValue\": _cache[3] || (_cache[3] = function ($event) {\n          return $setup.notification = $event;\n        }),\n        \"active-value\": 1,\n        \"inactive-value\": 2,\n        size: \"small\",\n        onChange: $setup.handleChange\n      }, null, 8 /* PROPS */, [\"modelValue\"])]), _createElementVNode(\"div\", _hoisted_20, [_cache[13] || (_cache[13] = _createElementVNode(\"div\", null, \"置顶聊天\", -1 /* HOISTED */)), _createVNode(_component_el_switch, {\n        modelValue: $setup.isTop,\n        \"onUpdate:modelValue\": _cache[4] || (_cache[4] = function ($event) {\n          return $setup.isTop = $event;\n        }),\n        size: \"small\",\n        onChange: $setup.handleIsTopChange\n      }, null, 8 /* PROPS */, [\"modelValue\"])]), _createElementVNode(\"div\", _hoisted_21, [_cache[14] || (_cache[14] = _createElementVNode(\"div\", null, \"是否禁言\", -1 /* HOISTED */)), _createVNode(_component_el_switch, {\n        modelValue: $setup.isSpeak,\n        \"onUpdate:modelValue\": _cache[5] || (_cache[5] = function ($event) {\n          return $setup.isSpeak = $event;\n        }),\n        size: \"small\"\n      }, null, 8 /* PROPS */, [\"modelValue\"])])]), _createElementVNode(\"div\", {\n        class: \"GlobalChatViewWindowControls\"\n      }, [_createElementVNode(\"div\", {\n        class: \"GlobalChatViewWindowClearAway\",\n        onClick: $setup.handleClearAway\n      }, \"清除聊天记录\")]), ((_$setup$chatInfo4 = $setup.chatInfo) === null || _$setup$chatInfo4 === void 0 ? void 0 : _$setup$chatInfo4.type) === 3 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, [_createElementVNode(\"div\", {\n        class: \"GlobalChatViewWindowClearAway\",\n        onClick: $setup.handleQuitGroup\n      }, \"退出群组\")])) : _createCommentVNode(\"v-if\", true), $setup.isOwner && ((_$setup$chatInfo5 = $setup.chatInfo) === null || _$setup$chatInfo5 === void 0 ? void 0 : _$setup$chatInfo5.type) === 3 ? (_openBlock(), _createElementBlock(\"div\", _hoisted_23, [_createElementVNode(\"div\", {\n        class: \"GlobalChatViewWindowClearAway\",\n        onClick: $setup.handleDissolveGroup\n      }, \"解散群组\")])) : _createCommentVNode(\"v-if\", true)])];\n    }),\n    _: 1 /* STABLE */\n  });\n}", "map": {"version": 3, "names": ["class", "key", "_createBlock", "_component_el_scrollbar", "default", "_withCtx", "_$setup$chatInfo", "_$setup$chatInfo2", "_$setup$chatInfo3", "_$setup$chatInfo4", "_$setup$chatInfo5", "_createElementVNode", "_hoisted_1", "$setup", "chatInfo", "type", "_createElementBlock", "_hoisted_2", "_hoisted_3", "_createVNode", "_component_el_image", "src", "imgUrl", "chatObjectInfo", "img", "fit", "draggable", "_hoisted_4", "_toDisplayString", "name", "onClick", "handleCreateGroup", "_hoisted_5", "_component_el_icon", "_component_Plus", "_", "_createCommentVNode", "_hoisted_6", "_hoisted_7", "_component_el_input", "modelValue", "keyword", "_cache", "$event", "Search", "placeholder", "clearable", "_Fragment", "_renderList", "groupUser", "item", "accountId", "isOwner", "_hoisted_8", "photo", "headImg", "_hoisted_9", "userName", "handleGroupAddUser", "_hoisted_10", "handleGroupDelUser", "_hoisted_11", "_component_Minus", "isUserLength", "isAllUser", "_createTextVNode", "_component_ArrowDown", "_component_ArrowUp", "_hoisted_12", "handleGroupName", "_hoisted_13", "_component_ArrowRight", "handleGroupQr", "_hoisted_14", "innerHTML", "qrCodeIcon", "_hoisted_15", "handleGroupAnnouncement", "_hoisted_16", "groupInfo", "callBoard", "handleGroupTransfer", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_component_el_switch", "notification", "size", "onChange", "handleChange", "_hoisted_20", "isTop", "handleIsTopChange", "_hoisted_21", "isSpeak", "handleClearAway", "_hoisted_22", "handleQuitGroup", "_hoisted_23", "handleDissolveGroup"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalChat\\components\\GlobalChatViewWindow\\GlobalChatViewWindow.vue"], "sourcesContent": ["<template>\r\n  <el-scrollbar class=\"GlobalChatViewWindow\">\r\n    <div class=\"GlobalChatViewWindowBox\">\r\n      <div class=\"GlobalChatViewWindowBody\" v-if=\"chatInfo?.type === 1\">\r\n        <div class=\"GlobalChatViewWindowUser\">\r\n          <el-image :src=\"imgUrl(chatInfo.chatObjectInfo.img)\" fit=\"cover\" draggable=\"false\" />\r\n          <div class=\"GlobalChatViewWindowUserName ellipsis\">{{ chatInfo.chatObjectInfo.name }}</div>\r\n        </div>\r\n        <div class=\"GlobalChatViewWindowUser\" @click=\"handleCreateGroup\">\r\n          <div class=\"GlobalChatViewWindowUserControls\">\r\n            <el-icon>\r\n              <Plus />\r\n            </el-icon>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"GlobalChatViewWindowBody\" v-if=\"chatInfo?.type === 3\">\r\n        <div class=\"GlobalChatViewWindowUserInput\">\r\n          <el-input v-model=\"keyword\" :prefix-icon=\"Search\" placeholder=\"搜索群成员\" clearable />\r\n        </div>\r\n        <div class=\"GlobalChatViewWindowUser\" v-for=\"item in groupUser\" :key=\"item.accountId\">\r\n          <div class=\"GlobalChatViewWindowUserLogo\" v-if=\"item.isOwner\">群主</div>\r\n          <el-image :src=\"imgUrl(item.photo || item.headImg)\" fit=\"cover\" draggable=\"false\" />\r\n          <div class=\"GlobalChatViewWindowUserName ellipsis\">{{ item.userName }}</div>\r\n        </div>\r\n        <div class=\"GlobalChatViewWindowUser\" @click=\"handleGroupAddUser\" v-if=\"!keyword\">\r\n          <div class=\"GlobalChatViewWindowUserControls\">\r\n            <el-icon>\r\n              <Plus />\r\n            </el-icon>\r\n          </div>\r\n        </div>\r\n        <div class=\"GlobalChatViewWindowUser\" @click=\"handleGroupDelUser\" v-if=\"isOwner && !keyword\">\r\n          <div class=\"GlobalChatViewWindowUserControls\">\r\n            <el-icon>\r\n              <Minus />\r\n            </el-icon>\r\n          </div>\r\n        </div>\r\n        <div class=\"GlobalChatViewWindowUserButton\" @click=\"isAllUser = !isAllUser\"\r\n          v-if=\"isUserLength && !keyword && !isAllUser\">展开更多\r\n          <el-icon>\r\n            <ArrowDown />\r\n          </el-icon>\r\n        </div>\r\n        <div class=\"GlobalChatViewWindowUserButton\" @click=\"isAllUser = !isAllUser\"\r\n          v-if=\"isUserLength && !keyword && isAllUser\">收起\r\n          <el-icon>\r\n            <ArrowUp />\r\n          </el-icon>\r\n        </div>\r\n      </div>\r\n      <div class=\"GlobalChatViewWindowControls\" v-if=\"chatInfo?.type === 3\">\r\n        <div class=\"GlobalChatViewWindowControlsItem\" @click=\"handleGroupName\">\r\n          <div>群组名称</div>\r\n          <div class=\"GlobalChatViewWindowGroupName ellipsis\">\r\n            {{ chatInfo.chatObjectInfo.name }}\r\n            <el-icon>\r\n              <ArrowRight />\r\n            </el-icon>\r\n          </div>\r\n        </div>\r\n        <div class=\"GlobalChatViewWindowControlsItem\" @click=\"handleGroupQr\">\r\n          <div>群组二维码</div>\r\n          <div class=\"GlobalChatViewWindowGroupName ellipsis\">\r\n            <div v-html=\"qrCodeIcon\"></div>\r\n            <el-icon>\r\n              <ArrowRight />\r\n            </el-icon>\r\n          </div>\r\n        </div>\r\n        <div class=\"GlobalChatViewWindowControlsItem\" @click=\"handleGroupAnnouncement\">\r\n          <div>群公告</div>\r\n          <div class=\"GlobalChatViewWindowGroupName ellipsis\">\r\n            {{ groupInfo.callBoard }}\r\n            <el-icon>\r\n              <ArrowRight />\r\n            </el-icon>\r\n          </div>\r\n        </div>\r\n        <div class=\"GlobalChatViewWindowControlsItem\" @click=\"handleGroupTransfer\" v-if=\"isOwner\">\r\n          <div>转让群主</div>\r\n          <div class=\"GlobalChatViewWindowGroupName ellipsis\">\r\n            <el-icon>\r\n              <ArrowRight />\r\n            </el-icon>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div class=\"GlobalChatViewWindowControls\">\r\n        <div class=\"GlobalChatViewWindowControlsItem\">\r\n          <div>消息免打扰</div>\r\n          <el-switch v-model=\"notification\" :active-value=\"1\" :inactive-value=\"2\" size=\"small\" @change=\"handleChange\" />\r\n        </div>\r\n        <div class=\"GlobalChatViewWindowControlsItem\">\r\n          <div>置顶聊天</div>\r\n          <el-switch v-model=\"isTop\" size=\"small\" @change=\"handleIsTopChange\" />\r\n        </div>\r\n        <div class=\"GlobalChatViewWindowControlsItem\">\r\n          <div>是否禁言</div>\r\n          <el-switch v-model=\"isSpeak\" size=\"small\" />\r\n        </div>\r\n      </div>\r\n      <div class=\"GlobalChatViewWindowControls\">\r\n        <div class=\"GlobalChatViewWindowClearAway\" @click=\"handleClearAway\">清除聊天记录</div>\r\n      </div>\r\n      <div class=\"GlobalChatViewWindowControls\" v-if=\"chatInfo?.type === 3\">\r\n        <div class=\"GlobalChatViewWindowClearAway\" @click=\"handleQuitGroup\">退出群组</div>\r\n      </div>\r\n      <div class=\"GlobalChatViewWindowControls\" v-if=\"isOwner && chatInfo?.type === 3\">\r\n        <div class=\"GlobalChatViewWindowClearAway\" @click=\"handleDissolveGroup\">解散群组</div>\r\n      </div>\r\n    </div>\r\n  </el-scrollbar>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalChatViewWindow' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, computed, onMounted } from 'vue'\r\nimport { useStore } from 'vuex'\r\nimport * as RongIMLib from '@rongcloud/imlib-next'\r\nimport { user, appOnlyHeader } from 'common/js/system_var.js'\r\nimport { qrCodeIcon } from '../../js/icon.js'\r\nimport { ElMessage, ElMessageBox } from 'element-plus'\r\nimport { Search } from '@element-plus/icons-vue'\r\nconst store = useStore()\r\nconst props = defineProps({ chatInfo: { type: Object, default: () => ({}) }, groupUser: { type: Array, default: () => ([]) } })\r\nconst emit = defineEmits(['refresh', 'callback'])\r\nconst rongCloudUrl = computed(() => store.getters.getRongCloudUrl)\r\nconst isPrivatization = computed(() => store.getters.getIsPrivatization)\r\nconst chatInfo = computed(() => props.chatInfo)\r\nconst keyword = ref('')\r\nconst isOwner = computed(() => {\r\n  let show = false\r\n  for (let index = 0; index < props.groupUser.length; index++) {\r\n    const item = props.groupUser[index]\r\n    if (item.isOwner && user.value?.accountId === item.accountId) show = true\r\n  }\r\n  return show\r\n})\r\nconst isUserLength = computed(() => props.groupUser.length > (isOwner.value ? 14 : 15))\r\nconst isAllUser = ref(false)\r\nconst groupUser = computed(() => {\r\n  if (props.groupUser.length > (isOwner.value ? 14 : 15) && !isAllUser.value && !keyword.value) {\r\n    return props.groupUser.slice(0, (isOwner.value ? 14 : 15))\r\n  } else {\r\n    return props.groupUser.filter(v => v.userName?.toLowerCase()?.includes(keyword.value?.toLowerCase()))\r\n  }\r\n})\r\nconst groupInfo = ref({})\r\nconst isTop = ref(true)\r\nconst notification = ref(2)\r\n\r\nconst isSpeak = ref(false)\r\n// 图片地址拼接组合\r\nconst imgUrl = url => url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg')\r\nonMounted(() => {\r\n  isTop.value = chatInfo.value.isTop\r\n  getNotificationStatus(chatInfo.value.type, chatInfo.value.id)\r\n  if (chatInfo.value.type === 3) chatGroupInfo(chatInfo.value.id)\r\n})\r\nconst chatGroupInfo = async (id) => {\r\n  const { data } = await api.chatGroupInfo({ detailId: id.slice(appOnlyHeader.value.length) })\r\n  groupInfo.value = data\r\n}\r\nconst getNotificationStatus = async (conversationType, targetId) => {\r\n  const { code, data } = await RongIMLib.getConversationNotificationStatus({ conversationType, targetId })\r\n  if (!code) notification.value = data\r\n}\r\nconst handleChange = async () => {\r\n  const { code } = await RongIMLib.setConversationNotificationStatus({ conversationType: chatInfo.value.type, targetId: chatInfo.value.id }, notification.value)\r\n  if (!code) handleRefresh()\r\n}\r\nconst handleIsTopChange = async () => {\r\n  const { code } = await RongIMLib.setConversationToTop({ conversationType: chatInfo.value.type, targetId: chatInfo.value.id }, isTop.value)\r\n  if (!code) handleRefresh()\r\n}\r\nconst handleClearAway = () => {\r\n  ElMessageBox.confirm('此操作将清除聊天记录, 是否继续?', '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(() => { clearHistoryMessages() }).catch(() => { ElMessage({ type: 'info', message: '已取消清除' }) })\r\n}\r\nconst clearHistoryMessages = async () => {\r\n  const { code } = await RongIMLib.clearHistoryMessages({ conversationType: chatInfo.value.type, targetId: chatInfo.value.id }, chatInfo.value.sentTime)\r\n  if (!code) handleRefresh()\r\n}\r\nconst handleCreateGroup = () => {\r\n  emit('callback', 'create', chatInfo.value)\r\n}\r\nconst handleGroupAddUser = () => {\r\n  emit('callback', 'add', chatInfo.value)\r\n}\r\nconst handleGroupDelUser = () => {\r\n  emit('callback', 'del', chatInfo.value)\r\n}\r\nconst handleGroupName = () => {\r\n  if (!isOwner.value) return\r\n  emit('callback', 'name', chatInfo.value)\r\n}\r\nconst handleGroupQr = () => {\r\n  emit('callback', 'qr', chatInfo.value)\r\n}\r\nconst handleGroupAnnouncement = () => {\r\n  emit('callback', 'announcement', chatInfo.value, isOwner.value)\r\n}\r\nconst handleGroupTransfer = () => {\r\n  emit('callback', 'transfer', chatInfo.value)\r\n}\r\n\r\nconst handleQuitGroup = () => {\r\n  if (isOwner.value) return ElMessage({ type: 'warning', message: '退出群组前请先转让群主！' })\r\n  ElMessageBox.confirm('此操作将退出当前群组, 是否继续?', '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(() => { chatGroupEdit() }).catch(() => { ElMessage({ type: 'info', message: '已取消退出' }) })\r\n}\r\nconst chatGroupEdit = async () => {\r\n  if (isOwner.value) return ElMessage({ type: 'warning', message: '退出群组前请先转让群主！' })\r\n  const memberUserIds = groupInfo.value.memberUserIds.filter(v => (user.value?.accountId !== v))\r\n  const { code } = await api.chatGroupEdit({\r\n    form: { id: chatInfo.value?.chatObjectInfo?.id, groupName: groupInfo.value.groupName },\r\n    ownerUserId: groupInfo.value.ownerUserId, memberUserIds: memberUserIds\r\n  })\r\n  if (code === 200) handleRongCloudGroup([user.value?.accountId])\r\n}\r\nconst handleDissolveGroup = () => {\r\n  ElMessageBox.confirm('此操作将解散当前群组, 是否继续?', '提示', {\r\n    confirmButtonText: '确定',\r\n    cancelButtonText: '取消',\r\n    type: 'warning'\r\n  }).then(() => { chatGroupDel() }).catch(() => { ElMessage({ type: 'info', message: '已取消解散' }) })\r\n}\r\nconst chatGroupDel = async () => {\r\n  const { code } = await api.chatGroupDel({ ids: [chatInfo.value?.chatObjectInfo?.id] })\r\n  if (code === 200) handleClearAway()\r\n}\r\nconst handleRongCloudGroup = async (userIds) => {\r\n  const { code } = await api.rongCloud(rongCloudUrl.value, {\r\n    type: 'quitGroup',\r\n    userIds: userIds.join(','),\r\n    groupId: chatInfo.value.id,\r\n    groupName: groupInfo.value.groupName,\r\n    environment: 1\r\n  }, isPrivatization.value)\r\n  if (code === 200) {\r\n    const sendMessageData = { name: `${user.value?.userName} 已退出群聊`, data: `${user.value?.userName}|OUI|${user.value?.accountId}|| 已退出群聊` }\r\n    emit('callback', 'quit', sendMessageData)\r\n  }\r\n}\r\nconst handleRefresh = () => { emit('refresh') }\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalChatViewWindow {\r\n  width: 320px;\r\n  height: 100%;\r\n\r\n  .GlobalChatViewWindowBox {\r\n    width: 320px;\r\n    padding: 20px;\r\n  }\r\n\r\n  .GlobalChatViewWindowBody {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n\r\n    .GlobalChatViewWindowUserInput {\r\n      width: 100%;\r\n      padding-bottom: 20px;\r\n\r\n      .zy-el-input {\r\n        width: 100%;\r\n        height: var(--zy-height-routine);\r\n      }\r\n    }\r\n\r\n    .GlobalChatViewWindowUserButton {\r\n      width: 100%;\r\n      font-size: 12px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      padding: 6px 0 12px 0;\r\n      cursor: pointer;\r\n\r\n      .zy-el-icon {\r\n        margin-left: 2px;\r\n        margin-bottom: 1px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalChatViewWindowUser {\r\n    width: 25%;\r\n    display: flex;\r\n    align-items: center;\r\n    flex-direction: column;\r\n    padding-bottom: 10px;\r\n    position: relative;\r\n\r\n    .GlobalChatViewWindowUserControls {\r\n      width: 42px;\r\n      height: 42px;\r\n      border-radius: 50%;\r\n      border: 2px solid var(--zy-el-text-color-secondary);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      cursor: pointer;\r\n\r\n      .zy-el-icon {\r\n        font-size: 18px;\r\n      }\r\n    }\r\n\r\n    .GlobalChatViewWindowUserLogo {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 50%;\r\n      transform: translate(12px, -50%);\r\n      padding: 0px 4px;\r\n      background: var(--zy-el-color-primary);\r\n      border-radius: 4px;\r\n      font-size: 12px;\r\n      color: #fff;\r\n      z-index: 2;\r\n    }\r\n\r\n    .zy-el-image {\r\n      width: 42px;\r\n      height: 42px;\r\n      border-radius: 50%;\r\n      overflow: hidden;\r\n    }\r\n\r\n    .GlobalChatViewWindowUserName {\r\n      width: 100%;\r\n      font-size: 12px;\r\n      text-align: center;\r\n      padding-top: 6px;\r\n    }\r\n  }\r\n\r\n  .GlobalChatViewWindowControls {\r\n    width: 100%;\r\n    padding-top: 6px;\r\n    background: var(--zy-el-color-info-light-9);\r\n\r\n    .GlobalChatViewWindowControlsItem {\r\n      width: 100%;\r\n      height: 38px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      font-size: 12px;\r\n      line-height: 1.6;\r\n      background: #fff;\r\n      position: relative;\r\n\r\n      &+.GlobalChatViewWindowControlsItem {\r\n        &::after {\r\n          content: \"\";\r\n          width: 100%;\r\n          height: 1px;\r\n          position: absolute;\r\n          top: 0;\r\n          left: 0;\r\n          transform: translateY(-50%);\r\n          background: var(--zy-el-border-color);\r\n        }\r\n      }\r\n\r\n      .GlobalChatViewWindowGroupName {\r\n        width: 68%;\r\n        min-height: 16px;\r\n        text-align: right;\r\n        padding-right: 16px;\r\n        position: relative;\r\n        cursor: pointer;\r\n\r\n        .zy-el-icon {\r\n          position: absolute;\r\n          top: 50%;\r\n          right: 0;\r\n          transform: translateY(-50%);\r\n          font-size: 14px;\r\n        }\r\n\r\n        &>div {\r\n          width: 100%;\r\n          height: 18px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: flex-end;\r\n\r\n          path {\r\n            fill: var(--zy-el-text-color-primary);\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .GlobalChatViewWindowClearAway {\r\n      width: 100%;\r\n      font-size: 12px;\r\n      line-height: 38px;\r\n      color: red;\r\n      cursor: pointer;\r\n      text-align: center;\r\n      background: #fff;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EAESA,KAAK,EAAC;AAAyB;;EAFxCC,GAAA;EAGWD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA0B;;EAE9BA,KAAK,EAAC;AAAuC;;EAG7CA,KAAK,EAAC;AAAkC;;EATvDC,GAAA;EAgBWD,KAAK,EAAC;;;EACJA,KAAK,EAAC;AAA+B;;EAjBlDC,GAAA;EAqBeD,KAAK,EAAC;;;EAENA,KAAK,EAAC;AAAuC;;EAG7CA,KAAK,EAAC;AAAkC;;EAOxCA,KAAK,EAAC;AAAkC;;EAjCvDC,GAAA;EAoDWD,KAAK,EAAC;;;EAGFA,KAAK,EAAC;AAAwC;;EAS9CA,KAAK,EAAC;AAAwC;kBAhE7D;;EAyEeA,KAAK,EAAC;AAAwC;;EAS9CA,KAAK,EAAC;AAAwC;;EAOlDA,KAAK,EAAC;AAA8B;;EAClCA,KAAK,EAAC;AAAkC;;EAIxCA,KAAK,EAAC;AAAkC;;EAIxCA,KAAK,EAAC;AAAkC;;EAlGrDC,GAAA;EA0GWD,KAAK,EAAC;;;EA1GjBC,GAAA;EA6GWD,KAAK,EAAC;;;;;;;;;;;;;uBA5GfE,YAAA,CAgHeC,uBAAA;IAhHDH,KAAK,EAAC;EAAsB;IAD5CI,OAAA,EAAAC,QAAA,CAEI;MAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;MAAA,OA8GM,CA9GNC,mBAAA,CA8GM,OA9GNC,UA8GM,GA7GwC,EAAAN,gBAAA,GAAAO,MAAA,CAAAC,QAAQ,cAAAR,gBAAA,uBAARA,gBAAA,CAAUS,IAAI,W,cAA1DC,mBAAA,CAYM,OAZNC,UAYM,GAXJN,mBAAA,CAGM,OAHNO,UAGM,GAFJC,YAAA,CAAqFC,mBAAA;QAA1EC,GAAG,EAAER,MAAA,CAAAS,MAAM,CAACT,MAAA,CAAAC,QAAQ,CAACS,cAAc,CAACC,GAAG;QAAGC,GAAG,EAAC,OAAO;QAACC,SAAS,EAAC;wCAC3Ef,mBAAA,CAA2F,OAA3FgB,UAA2F,EAAAC,gBAAA,CAArCf,MAAA,CAAAC,QAAQ,CAACS,cAAc,CAACM,IAAI,iB,GAEpFlB,mBAAA,CAMM;QANDX,KAAK,EAAC,0BAA0B;QAAE8B,OAAK,EAAEjB,MAAA,CAAAkB;UAC5CpB,mBAAA,CAIM,OAJNqB,UAIM,GAHJb,YAAA,CAEUc,kBAAA;QAZtB7B,OAAA,EAAAC,QAAA,CAWc;UAAA,OAAQ,CAARc,YAAA,CAAQe,eAAA,E;;QAXtBC,CAAA;kBAAAC,mBAAA,gBAgBkD,EAAA7B,iBAAA,GAAAM,MAAA,CAAAC,QAAQ,cAAAP,iBAAA,uBAARA,iBAAA,CAAUQ,IAAI,W,cAA1DC,mBAAA,CAmCM,OAnCNqB,UAmCM,GAlCJ1B,mBAAA,CAEM,OAFN2B,UAEM,GADJnB,YAAA,CAAkFoB,mBAAA;QAlB5FC,UAAA,EAkB6B3B,MAAA,CAAA4B,OAAO;QAlBpC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAkB6B9B,MAAA,CAAA4B,OAAO,GAAAE,MAAA;QAAA;QAAG,aAAW,EAAE9B,MAAA,CAAA+B,MAAM;QAAEC,WAAW,EAAC,OAAO;QAACC,SAAS,EAAT;mFAExE9B,mBAAA,CAIM+B,SAAA,QAxBdC,WAAA,CAoB6DnC,MAAA,CAAAoC,SAAS,EApBtE,UAoBqDC,IAAI;6BAAjDlC,mBAAA,CAIM;UAJDhB,KAAK,EAAC,0BAA0B;UAA4BC,GAAG,EAAEiD,IAAI,CAACC;YACzBD,IAAI,CAACE,OAAO,I,cAA5DpC,mBAAA,CAAsE,OAAtEqC,UAAsE,EAAR,IAAE,KArB1EjB,mBAAA,gBAsBUjB,YAAA,CAAoFC,mBAAA;UAAzEC,GAAG,EAAER,MAAA,CAAAS,MAAM,CAAC4B,IAAI,CAACI,KAAK,IAAIJ,IAAI,CAACK,OAAO;UAAG9B,GAAG,EAAC,OAAO;UAACC,SAAS,EAAC;0CAC1Ef,mBAAA,CAA4E,OAA5E6C,UAA4E,EAAA5B,gBAAA,CAAtBsB,IAAI,CAACO,QAAQ,iB;uCAEI5C,MAAA,CAAA4B,OAAO,I,cAAhFzB,mBAAA,CAMM;QA/Bdf,GAAA;QAyBaD,KAAK,EAAC,0BAA0B;QAAE8B,OAAK,EAAEjB,MAAA,CAAA6C;UAC5C/C,mBAAA,CAIM,OAJNgD,WAIM,GAHJxC,YAAA,CAEUc,kBAAA;QA7BtB7B,OAAA,EAAAC,QAAA,CA4Bc;UAAA,OAAQ,CAARc,YAAA,CAAQe,eAAA,E;;QA5BtBC,CAAA;gBAAAC,mBAAA,gBAgCgFvB,MAAA,CAAAuC,OAAO,KAAKvC,MAAA,CAAA4B,OAAO,I,cAA3FzB,mBAAA,CAMM;QAtCdf,GAAA;QAgCaD,KAAK,EAAC,0BAA0B;QAAE8B,OAAK,EAAEjB,MAAA,CAAA+C;UAC5CjD,mBAAA,CAIM,OAJNkD,WAIM,GAHJ1C,YAAA,CAEUc,kBAAA;QApCtB7B,OAAA,EAAAC,QAAA,CAmCc;UAAA,OAAS,CAATc,YAAA,CAAS2C,gBAAA,E;;QAnCvB3B,CAAA;gBAAAC,mBAAA,gBAwCgBvB,MAAA,CAAAkD,YAAY,KAAKlD,MAAA,CAAA4B,OAAO,KAAK5B,MAAA,CAAAmD,SAAS,I,cAD9ChD,mBAAA,CAKM;QA5Cdf,GAAA;QAuCaD,KAAK,EAAC,gCAAgC;QAAE8B,OAAK,EAAAY,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAE9B,MAAA,CAAAmD,SAAS,IAAInD,MAAA,CAAAmD,SAAS;QAAA;oCAvClFC,gBAAA,CAwCwD,OAC9C,IAAA9C,YAAA,CAEUc,kBAAA;QA3CpB7B,OAAA,EAAAC,QAAA,CA0CY;UAAA,OAAa,CAAbc,YAAA,CAAa+C,oBAAA,E;;QA1CzB/B,CAAA;cAAAC,mBAAA,gBA8CgBvB,MAAA,CAAAkD,YAAY,KAAKlD,MAAA,CAAA4B,OAAO,IAAI5B,MAAA,CAAAmD,SAAS,I,cAD7ChD,mBAAA,CAKM;QAlDdf,GAAA;QA6CaD,KAAK,EAAC,gCAAgC;QAAE8B,OAAK,EAAAY,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAAE9B,MAAA,CAAAmD,SAAS,IAAInD,MAAA,CAAAmD,SAAS;QAAA;oCA7ClFC,gBAAA,CA8CuD,KAC7C,IAAA9C,YAAA,CAEUc,kBAAA;QAjDpB7B,OAAA,EAAAC,QAAA,CAgDY;UAAA,OAAW,CAAXc,YAAA,CAAWgD,kBAAA,E;;QAhDvBhC,CAAA;cAAAC,mBAAA,e,KAAAA,mBAAA,gBAoDsD,EAAA5B,iBAAA,GAAAK,MAAA,CAAAC,QAAQ,cAAAN,iBAAA,uBAARA,iBAAA,CAAUO,IAAI,W,cAA9DC,mBAAA,CAoCM,OApCNoD,WAoCM,GAnCJzD,mBAAA,CAQM;QARDX,KAAK,EAAC,kCAAkC;QAAE8B,OAAK,EAAEjB,MAAA,CAAAwD;oCACpD1D,mBAAA,CAAe,aAAV,MAAI,sBACTA,mBAAA,CAKM,OALN2D,WAKM,GA5DhBL,gBAAA,CAAArC,gBAAA,CAwDef,MAAA,CAAAC,QAAQ,CAACS,cAAc,CAACM,IAAI,IAAG,GAClC,iBAAAV,YAAA,CAEUc,kBAAA;QA3DtB7B,OAAA,EAAAC,QAAA,CA0Dc;UAAA,OAAc,CAAdc,YAAA,CAAcoD,qBAAA,E;;QA1D5BpC,CAAA;cA8DQxB,mBAAA,CAQM;QARDX,KAAK,EAAC,kCAAkC;QAAE8B,OAAK,EAAEjB,MAAA,CAAA2D;oCACpD7D,mBAAA,CAAgB,aAAX,OAAK,sBACVA,mBAAA,CAKM,OALN8D,WAKM,GAJJ9D,mBAAA,CAA+B;QAA1B+D,SAAmB,EAAX7D,MAAA,CAAA8D;MAAU,wBAjEnCC,WAAA,GAkEYzD,YAAA,CAEUc,kBAAA;QApEtB7B,OAAA,EAAAC,QAAA,CAmEc;UAAA,OAAc,CAAdc,YAAA,CAAcoD,qBAAA,E;;QAnE5BpC,CAAA;cAuEQxB,mBAAA,CAQM;QARDX,KAAK,EAAC,kCAAkC;QAAE8B,OAAK,EAAEjB,MAAA,CAAAgE;sCACpDlE,mBAAA,CAAc,aAAT,KAAG,sBACRA,mBAAA,CAKM,OALNmE,WAKM,GA9EhBb,gBAAA,CAAArC,gBAAA,CA0Eef,MAAA,CAAAkE,SAAS,CAACC,SAAS,IAAG,GACzB,iBAAA7D,YAAA,CAEUc,kBAAA;QA7EtB7B,OAAA,EAAAC,QAAA,CA4Ec;UAAA,OAAc,CAAdc,YAAA,CAAcoD,qBAAA,E;;QA5E5BpC,CAAA;cAgFyFtB,MAAA,CAAAuC,OAAO,I,cAAxFpC,mBAAA,CAOM;QAvFdf,GAAA;QAgFaD,KAAK,EAAC,kCAAkC;QAAE8B,OAAK,EAAEjB,MAAA,CAAAoE;sCACpDtE,mBAAA,CAAe,aAAV,MAAI,sBACTA,mBAAA,CAIM,OAJNuE,WAIM,GAHJ/D,YAAA,CAEUc,kBAAA;QArFtB7B,OAAA,EAAAC,QAAA,CAoFc;UAAA,OAAc,CAAdc,YAAA,CAAcoD,qBAAA,E;;QApF5BpC,CAAA;gBAAAC,mBAAA,e,KAAAA,mBAAA,gBAyFMzB,mBAAA,CAaM,OAbNwE,WAaM,GAZJxE,mBAAA,CAGM,OAHNyE,WAGM,G,4BAFJzE,mBAAA,CAAgB,aAAX,OAAK,sBACVQ,YAAA,CAA8GkE,oBAAA;QA5FxH7C,UAAA,EA4F8B3B,MAAA,CAAAyE,YAAY;QA5F1C,uBAAA5C,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OA4F8B9B,MAAA,CAAAyE,YAAY,GAAA3C,MAAA;QAAA;QAAG,cAAY,EAAE,CAAC;QAAG,gBAAc,EAAE,CAAC;QAAE4C,IAAI,EAAC,OAAO;QAAEC,QAAM,EAAE3E,MAAA,CAAA4E;iDAEhG9E,mBAAA,CAGM,OAHN+E,WAGM,G,4BAFJ/E,mBAAA,CAAe,aAAV,MAAI,sBACTQ,YAAA,CAAsEkE,oBAAA;QAhGhF7C,UAAA,EAgG8B3B,MAAA,CAAA8E,KAAK;QAhGnC,uBAAAjD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAgG8B9B,MAAA,CAAA8E,KAAK,GAAAhD,MAAA;QAAA;QAAE4C,IAAI,EAAC,OAAO;QAAEC,QAAM,EAAE3E,MAAA,CAAA+E;iDAEnDjF,mBAAA,CAGM,OAHNkF,WAGM,G,4BAFJlF,mBAAA,CAAe,aAAV,MAAI,sBACTQ,YAAA,CAA4CkE,oBAAA;QApGtD7C,UAAA,EAoG8B3B,MAAA,CAAAiF,OAAO;QApGrC,uBAAApD,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAoG8B9B,MAAA,CAAAiF,OAAO,GAAAnD,MAAA;QAAA;QAAE4C,IAAI,EAAC;mDAGtC5E,mBAAA,CAEM;QAFDX,KAAK,EAAC;MAA8B,IACvCW,mBAAA,CAAgF;QAA3EX,KAAK,EAAC,+BAA+B;QAAE8B,OAAK,EAAEjB,MAAA,CAAAkF;SAAiB,QAAM,E,GAE5B,EAAAtF,iBAAA,GAAAI,MAAA,CAAAC,QAAQ,cAAAL,iBAAA,uBAARA,iBAAA,CAAUM,IAAI,W,cAA9DC,mBAAA,CAEM,OAFNgF,WAEM,GADJrF,mBAAA,CAA8E;QAAzEX,KAAK,EAAC,+BAA+B;QAAE8B,OAAK,EAAEjB,MAAA,CAAAoF;SAAiB,MAAI,E,KA3GhF7D,mBAAA,gBA6GsDvB,MAAA,CAAAuC,OAAO,IAAI,EAAA1C,iBAAA,GAAAG,MAAA,CAAAC,QAAQ,cAAAJ,iBAAA,uBAARA,iBAAA,CAAUK,IAAI,W,cAAzEC,mBAAA,CAEM,OAFNkF,WAEM,GADJvF,mBAAA,CAAkF;QAA7EX,KAAK,EAAC,+BAA+B;QAAE8B,OAAK,EAAEjB,MAAA,CAAAsF;SAAqB,MAAI,E,KA9GpF/D,mBAAA,e;;IAAAD,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}