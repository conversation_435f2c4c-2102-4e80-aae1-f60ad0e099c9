{"ast": null, "code": "import { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, withCtx as _withCtx, createTextVNode as _createTextVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\";\nvar _hoisted_1 = {\n  class: \"GlobalGroupTransfer\"\n};\nvar _hoisted_2 = {\n  class: \"GlobalGroupTransferInput\"\n};\nvar _hoisted_3 = {\n  class: \"GlobalGroupTransferItem\"\n};\nvar _hoisted_4 = {\n  class: \"GlobalGroupTransferName ellipsis\"\n};\nvar _hoisted_5 = {\n  class: \"GlobalGroupTransferButton\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  var _component_el_input = _resolveComponent(\"el-input\");\n  var _component_el_image = _resolveComponent(\"el-image\");\n  var _component_el_radio = _resolveComponent(\"el-radio\");\n  var _component_el_tree = _resolveComponent(\"el-tree\");\n  var _component_el_radio_group = _resolveComponent(\"el-radio-group\");\n  var _component_el_scrollbar = _resolveComponent(\"el-scrollbar\");\n  var _component_el_button = _resolveComponent(\"el-button\");\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, [_createVNode(_component_el_input, {\n    modelValue: $setup.keyword,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = function ($event) {\n      return $setup.keyword = $event;\n    }),\n    \"prefix-icon\": $setup.Search,\n    placeholder: \"搜索\",\n    onInput: $setup.handleQuery,\n    clearable: \"\"\n  }, null, 8 /* PROPS */, [\"modelValue\", \"prefix-icon\"])]), _createVNode(_component_el_scrollbar, {\n    class: \"GlobalGroupTransferScrollbar\"\n  }, {\n    default: _withCtx(function () {\n      return [_createVNode(_component_el_radio_group, {\n        modelValue: $setup.checkedUser,\n        \"onUpdate:modelValue\": _cache[1] || (_cache[1] = function ($event) {\n          return $setup.checkedUser = $event;\n        })\n      }, {\n        default: _withCtx(function () {\n          return [_createVNode(_component_el_tree, {\n            ref: \"treeRef\",\n            \"node-key\": \"accountId\",\n            data: $setup.tableData,\n            \"filter-node-method\": $setup.filterNode\n          }, {\n            default: _withCtx(function (_ref) {\n              var _$setup$disabledId;\n              var data = _ref.data;\n              return [_createVNode(_component_el_radio, {\n                value: data.accountId,\n                disabled: (_$setup$disabledId = $setup.disabledId) === null || _$setup$disabledId === void 0 ? void 0 : _$setup$disabledId.includes(data.accountId),\n                onChange: function onChange($event) {\n                  return $setup.handleChange(data);\n                }\n              }, {\n                default: _withCtx(function () {\n                  return [_createElementVNode(\"div\", _hoisted_3, [_createVNode(_component_el_image, {\n                    src: $setup.imgUrl(data.photo || data.headImg),\n                    fit: \"cover\",\n                    draggable: \"false\"\n                  }, null, 8 /* PROPS */, [\"src\"]), _createElementVNode(\"div\", _hoisted_4, _toDisplayString(data.userName), 1 /* TEXT */)])];\n                }),\n                _: 2 /* DYNAMIC */\n              }, 1032 /* PROPS, DYNAMIC_SLOTS */, [\"value\", \"disabled\", \"onChange\"])];\n            }),\n            _: 1 /* STABLE */\n          }, 8 /* PROPS */, [\"data\"])];\n        }),\n        _: 1 /* STABLE */\n      }, 8 /* PROPS */, [\"modelValue\"])];\n    }),\n    _: 1 /* STABLE */\n  }), _createElementVNode(\"div\", _hoisted_5, [_createVNode(_component_el_button, {\n    onClick: $setup.handleReset\n  }, {\n    default: _withCtx(function () {\n      return _cache[2] || (_cache[2] = [_createTextVNode(\"取消\")]);\n    }),\n    _: 1 /* STABLE */\n  }), _createVNode(_component_el_button, {\n    type: \"primary\",\n    onClick: $setup.handleSubmit,\n    disabled: !$setup.checkedUser\n  }, {\n    default: _withCtx(function () {\n      return _cache[3] || (_cache[3] = [_createTextVNode(\"完成\")]);\n    }),\n    _: 1 /* STABLE */\n  }, 8 /* PROPS */, [\"disabled\"])])]);\n}", "map": {"version": 3, "names": ["class", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_createVNode", "_component_el_input", "modelValue", "$setup", "keyword", "_cache", "$event", "Search", "placeholder", "onInput", "handleQuery", "clearable", "_component_el_scrollbar", "default", "_withCtx", "_component_el_radio_group", "checkedUser", "_component_el_tree", "ref", "data", "tableData", "filterNode", "_ref", "_$setup$disabledId", "_component_el_radio", "value", "accountId", "disabled", "disabledId", "includes", "onChange", "handleChange", "_hoisted_3", "_component_el_image", "src", "imgUrl", "photo", "headImg", "fit", "draggable", "_hoisted_4", "_toDisplayString", "userName", "_", "_hoisted_5", "_component_el_button", "onClick", "handleReset", "_createTextVNode", "type", "handleSubmit"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\GlobalChat\\components\\GlobalGroupTransfer\\GlobalGroupTransfer.vue"], "sourcesContent": ["<template>\r\n  <div class=\"GlobalGroupTransfer\">\r\n    <div class=\"GlobalGroupTransferInput\">\r\n      <el-input v-model=\"keyword\" :prefix-icon=\"Search\" placeholder=\"搜索\" @input=\"handleQuery\" clearable />\r\n    </div>\r\n    <el-scrollbar class=\"GlobalGroupTransferScrollbar\">\r\n      <el-radio-group v-model=\"checkedUser\">\r\n        <el-tree ref=\"treeRef\" node-key=\"accountId\" :data=\"tableData\" :filter-node-method=\"filterNode\">\r\n          <template #default=\"{ data }\">\r\n            <el-radio :value=\"data.accountId\" :disabled=\"disabledId?.includes(data.accountId)\"\r\n              @change=\"handleChange(data)\">\r\n              <div class=\"GlobalGroupTransferItem\">\r\n                <el-image :src=\"imgUrl(data.photo || data.headImg)\" fit=\"cover\" draggable=\"false\" />\r\n                <div class=\"GlobalGroupTransferName ellipsis\">{{ data.userName }}</div>\r\n              </div>\r\n            </el-radio>\r\n          </template>\r\n        </el-tree>\r\n      </el-radio-group>\r\n    </el-scrollbar>\r\n    <div class=\"GlobalGroupTransferButton\">\r\n      <el-button @click=\"handleReset\">取消</el-button>\r\n      <el-button type=\"primary\" @click=\"handleSubmit\" :disabled=\"(!checkedUser)\">完成</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default { name: 'GlobalGroupTransfer' }\r\n</script>\r\n<script setup>\r\nimport api from '@/api'\r\nimport { ref, onMounted } from 'vue'\r\nimport { Search } from '@element-plus/icons-vue'\r\nconst props = defineProps({ infoId: { type: String, default: '' } })\r\nconst emit = defineEmits(['callback'])\r\nconst groupInfo = ref({})\r\nconst treeRef = ref()\r\nconst keyword = ref('')\r\nconst tableData = ref([])\r\nconst disabledId = ref([])\r\nconst checkedUser = ref('')\r\nconst checkedUserData = ref({})\r\nconst imgUrl = url => url ? api.fileURL(url) : api.defaultImgURL('default_user_head.jpg')\r\n\r\nconst handleQuery = () => {\r\n  treeRef.value?.filter(keyword.value)\r\n}\r\nconst filterNode = (value, data) => {\r\n  if (!value) return true\r\n  return data.userName?.toLowerCase()?.includes(value?.toLowerCase())\r\n}\r\nconst handleChange = (item) => {\r\n  checkedUserData.value = item\r\n}\r\nconst handleSubmit = async () => {\r\n  const { code } = await api.chatGroupEdit({\r\n    form: { id: props.infoId, groupName: groupInfo.value.groupName },\r\n    ownerUserId: checkedUser.value, memberUserIds: groupInfo.value.memberUserIds\r\n  })\r\n  if (code === 200) {\r\n    const sendMessageData = {\r\n      name: `${checkedUserData.value.userName} 已成为新群主`,\r\n      data: `||${checkedUserData.value.userName}|OUI|${checkedUserData.value.accountId}|| 已成为新群主`,\r\n    }\r\n    emit('callback', true, sendMessageData)\r\n  }\r\n}\r\nconst handleReset = () => { emit('callback', false) }\r\nconst chatGroupInfo = async () => {\r\n  const { data } = await api.chatGroupInfo({ detailId: props.infoId })\r\n  groupInfo.value = data\r\n  disabledId.value = [data.ownerUserId]\r\n}\r\nconst chatGroupMemberList = async () => {\r\n  const { data } = await api.chatGroupMemberList({ pageNo: 1, pageSize: 9999, keyword: keyword.value, query: { chatGroupId: props.infoId } })\r\n  tableData.value = data\r\n}\r\nonMounted(() => {\r\n  chatGroupInfo()\r\n  chatGroupMemberList()\r\n})\r\n</script>\r\n<style lang=\"scss\">\r\n.GlobalGroupTransfer {\r\n  width: 360px;\r\n  height: 100%;\r\n  padding-bottom: 10px;\r\n\r\n  .GlobalGroupTransferInput {\r\n    width: 100%;\r\n    height: 56px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 6px 20px 0 20px;\r\n\r\n    .zy-el-input {\r\n      width: 100%;\r\n      height: var(--zy-height-routine);\r\n    }\r\n  }\r\n\r\n  .GlobalGroupTransferScrollbar {\r\n    width: 100%;\r\n    height: calc(100% - 102px);\r\n\r\n    .zy-el-tree {\r\n      width: 100%;\r\n      padding: 0 20px;\r\n\r\n      .zy-el-tree-node {\r\n        .zy-el-tree-node__content {\r\n          height: auto;\r\n          padding: 10px 0;\r\n          position: relative;\r\n          background: transparent;\r\n        }\r\n      }\r\n    }\r\n\r\n    .zy-el-radio-group {\r\n      width: 100%;\r\n    }\r\n\r\n    .zy-el-radio {\r\n      width: 100%;\r\n      height: auto;\r\n      margin: 0;\r\n      position: relative;\r\n\r\n      .zy-el-radio__input {\r\n        position: absolute;\r\n        top: 50%;\r\n        left: -5px;\r\n        transform: translate(-100%, -50%);\r\n      }\r\n\r\n      .zy-el-radio__label {\r\n        width: 100%;\r\n        padding: 0;\r\n      }\r\n    }\r\n\r\n    .GlobalGroupTransferItem {\r\n      width: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      position: relative;\r\n\r\n      &.is-active {\r\n        color: var(--zy-el-color-primary);\r\n      }\r\n\r\n      .zy-el-image {\r\n        width: 38px;\r\n        height: 38px;\r\n        border-radius: 50%;\r\n        overflow: hidden;\r\n      }\r\n\r\n      .GlobalGroupTransferName {\r\n        width: calc(100% - 54px);\r\n        font-size: 14px;\r\n\r\n        &::after {\r\n          content: \"\";\r\n          width: calc(100% - 54px);\r\n          border-bottom: 1px solid var(--zy-el-border-color-lighter);\r\n          position: absolute;\r\n          right: 0;\r\n          bottom: -10px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .GlobalGroupTransferButton {\r\n    width: 100%;\r\n    height: 46px;\r\n    display: flex;\r\n    align-items: flex-end;\r\n    justify-content: center;\r\n\r\n    .zy-el-button {\r\n      width: 120px;\r\n      height: var(--zy-height-secondary);\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAqB;;EACzBA,KAAK,EAAC;AAA0B;;EAStBA,KAAK,EAAC;AAAyB;;EAE7BA,KAAK,EAAC;AAAkC;;EAOpDA,KAAK,EAAC;AAA2B;;;;;;;;;uBAnBxCC,mBAAA,CAuBM,OAvBNC,UAuBM,GAtBJC,mBAAA,CAEM,OAFNC,UAEM,GADJC,YAAA,CAAoGC,mBAAA;IAH1GC,UAAA,EAGyBC,MAAA,CAAAC,OAAO;IAHhC,uBAAAC,MAAA,QAAAA,MAAA,gBAAAC,MAAA;MAAA,OAGyBH,MAAA,CAAAC,OAAO,GAAAE,MAAA;IAAA;IAAG,aAAW,EAAEH,MAAA,CAAAI,MAAM;IAAEC,WAAW,EAAC,IAAI;IAAEC,OAAK,EAAEN,MAAA,CAAAO,WAAW;IAAEC,SAAS,EAAT;4DAE1FX,YAAA,CAceY,uBAAA;IAdDjB,KAAK,EAAC;EAA8B;IALtDkB,OAAA,EAAAC,QAAA,CAMM;MAAA,OAYiB,CAZjBd,YAAA,CAYiBe,yBAAA;QAlBvBb,UAAA,EAM+BC,MAAA,CAAAa,WAAW;QAN1C,uBAAAX,MAAA,QAAAA,MAAA,gBAAAC,MAAA;UAAA,OAM+BH,MAAA,CAAAa,WAAW,GAAAV,MAAA;QAAA;;QAN1CO,OAAA,EAAAC,QAAA,CAOQ;UAAA,OAUU,CAVVd,YAAA,CAUUiB,kBAAA;YAVDC,GAAG,EAAC,SAAS;YAAC,UAAQ,EAAC,WAAW;YAAEC,IAAI,EAAEhB,MAAA,CAAAiB,SAAS;YAAG,oBAAkB,EAAEjB,MAAA,CAAAkB;;YACtER,OAAO,EAAAC,QAAA,CAChB,UAAAQ,IAAA;cAAA,IAAAC,kBAAA;cAAA,IADoBJ,IAAI,GAAAG,IAAA,CAAJH,IAAI;cAAA,QACxBnB,YAAA,CAMWwB,mBAAA;gBANAC,KAAK,EAAEN,IAAI,CAACO,SAAS;gBAAGC,QAAQ,GAAAJ,kBAAA,GAAEpB,MAAA,CAAAyB,UAAU,cAAAL,kBAAA,uBAAVA,kBAAA,CAAYM,QAAQ,CAACV,IAAI,CAACO,SAAS;gBAC7EI,QAAM,WAANA,QAAMA,CAAAxB,MAAA;kBAAA,OAAEH,MAAA,CAAA4B,YAAY,CAACZ,IAAI;gBAAA;;gBAVxCN,OAAA,EAAAC,QAAA,CAWc;kBAAA,OAGM,CAHNhB,mBAAA,CAGM,OAHNkC,UAGM,GAFJhC,YAAA,CAAoFiC,mBAAA;oBAAzEC,GAAG,EAAE/B,MAAA,CAAAgC,MAAM,CAAChB,IAAI,CAACiB,KAAK,IAAIjB,IAAI,CAACkB,OAAO;oBAAGC,GAAG,EAAC,OAAO;oBAACC,SAAS,EAAC;oDAC1EzC,mBAAA,CAAuE,OAAvE0C,UAAuE,EAAAC,gBAAA,CAAtBtB,IAAI,CAACuB,QAAQ,iB;;gBAb9EC,CAAA;;;YAAAA,CAAA;;;QAAAA,CAAA;;;IAAAA,CAAA;MAoBI7C,mBAAA,CAGM,OAHN8C,UAGM,GAFJ5C,YAAA,CAA8C6C,oBAAA;IAAlCC,OAAK,EAAE3C,MAAA,CAAA4C;EAAW;IArBpClC,OAAA,EAAAC,QAAA,CAqBsC;MAAA,OAAET,MAAA,QAAAA,MAAA,OArBxC2C,gBAAA,CAqBsC,IAAE,E;;IArBxCL,CAAA;MAsBM3C,YAAA,CAAyF6C,oBAAA;IAA9EI,IAAI,EAAC,SAAS;IAAEH,OAAK,EAAE3C,MAAA,CAAA+C,YAAY;IAAGvB,QAAQ,GAAIxB,MAAA,CAAAa;;IAtBnEH,OAAA,EAAAC,QAAA,CAsBiF;MAAA,OAAET,MAAA,QAAAA,MAAA,OAtBnF2C,gBAAA,CAsBiF,IAAE,E;;IAtBnFL,CAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}