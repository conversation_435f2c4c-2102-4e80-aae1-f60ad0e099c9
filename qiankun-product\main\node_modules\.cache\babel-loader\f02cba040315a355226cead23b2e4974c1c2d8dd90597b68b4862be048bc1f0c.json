{"ast": null, "code": "\"use strict\";\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nfunction _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nfunction _defineProperties(e, r) {\n  for (var t = 0; t < r.length; t++) {\n    var o = r[t];\n    o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o);\n  }\n}\nfunction _createClass(e, r, t) {\n  return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", {\n    writable: !1\n  }), e;\n}\nfunction _toPropertyKey(t) {\n  var i = _toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nfunction _toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nvar traitName = \"expandPair\";\nvar mergeSort = require(\"../merge-sort.js\");\nvar _require = require(\"../doc-utils.js\"),\n  getLeft = _require.getLeft,\n  getRight = _require.getRight;\nvar wrapper = require(\"../module-wrapper.js\");\nvar _require2 = require(\"../traits.js\"),\n  getExpandToDefault = _require2.getExpandToDefault;\nvar _require3 = require(\"../errors.js\"),\n  getUnmatchedLoopException = _require3.getUnmatchedLoopException,\n  getClosingTagNotMatchOpeningTag = _require3.getClosingTagNotMatchOpeningTag,\n  getUnbalancedLoopException = _require3.getUnbalancedLoopException;\nfunction getOpenCountChange(part) {\n  switch (part.location) {\n    case \"start\":\n      return 1;\n    case \"end\":\n      return -1;\n  }\n}\nfunction match(start, end) {\n  return start != null && end != null && (start.part.location === \"start\" && end.part.location === \"end\" && start.part.value === end.part.value || end.part.value === \"\");\n}\nfunction transformer(traits) {\n  var i = 0;\n  var errors = [];\n  while (i < traits.length) {\n    var part = traits[i].part;\n    if (part.location === \"end\") {\n      if (i === 0) {\n        traits.splice(0, 1);\n        errors.push(getUnmatchedLoopException(part));\n        return {\n          traits: traits,\n          errors: errors\n        };\n      }\n      var endIndex = i;\n      var startIndex = i - 1;\n      var offseter = 1;\n      if (match(traits[startIndex], traits[endIndex])) {\n        traits.splice(endIndex, 1);\n        traits.splice(startIndex, 1);\n        return {\n          errors: errors,\n          traits: traits\n        };\n      }\n      while (offseter < 50) {\n        var startCandidate = traits[startIndex - offseter];\n        var endCandidate = traits[endIndex + offseter];\n        if (match(startCandidate, traits[endIndex])) {\n          traits.splice(endIndex, 1);\n          traits.splice(startIndex - offseter, 1);\n          return {\n            errors: errors,\n            traits: traits\n          };\n        }\n        if (match(traits[startIndex], endCandidate)) {\n          traits.splice(endIndex + offseter, 1);\n          traits.splice(startIndex, 1);\n          return {\n            errors: errors,\n            traits: traits\n          };\n        }\n        offseter++;\n      }\n      errors.push(getClosingTagNotMatchOpeningTag({\n        tags: [traits[startIndex].part, traits[endIndex].part]\n      }));\n      traits.splice(endIndex, 1);\n      traits.splice(startIndex, 1);\n      return {\n        traits: traits,\n        errors: errors\n      };\n    }\n    i++;\n  }\n  traits.forEach(function (_ref) {\n    var part = _ref.part;\n    errors.push(getUnmatchedLoopException(part));\n  });\n  return {\n    traits: [],\n    errors: errors\n  };\n}\nfunction getPairs(traits) {\n  var levelTraits = {};\n  var errors = [];\n  var pairs = [];\n  var transformedTraits = [];\n  for (var i = 0; i < traits.length; i++) {\n    transformedTraits.push(traits[i]);\n  }\n  while (transformedTraits.length > 0) {\n    var result = transformer(transformedTraits);\n    errors = errors.concat(result.errors);\n    transformedTraits = result.traits;\n  }\n\n  // Stryker disable all : because this check makes the function return quicker\n  if (errors.length > 0) {\n    return {\n      pairs: pairs,\n      errors: errors\n    };\n  }\n  // Stryker restore all\n  var countOpen = 0;\n  for (var _i = 0; _i < traits.length; _i++) {\n    var currentTrait = traits[_i];\n    var part = currentTrait.part;\n    var change = getOpenCountChange(part);\n    countOpen += change;\n    if (change === 1) {\n      levelTraits[countOpen] = currentTrait;\n    } else {\n      var startTrait = levelTraits[countOpen + 1];\n      if (countOpen === 0) {\n        pairs = pairs.concat([[startTrait, currentTrait]]);\n      }\n    }\n    countOpen = countOpen >= 0 ? countOpen : 0;\n  }\n  return {\n    pairs: pairs,\n    errors: errors\n  };\n}\nvar ExpandPairTrait = /*#__PURE__*/function () {\n  function ExpandPairTrait() {\n    _classCallCheck(this, ExpandPairTrait);\n    this.name = \"ExpandPairTrait\";\n  }\n  return _createClass(ExpandPairTrait, [{\n    key: \"optionsTransformer\",\n    value: function optionsTransformer(options, docxtemplater) {\n      this.expandTags = docxtemplater.fileTypeConfig.expandTags.concat(docxtemplater.options.paragraphLoop ? docxtemplater.fileTypeConfig.onParagraphLoop : []);\n      return options;\n    }\n  }, {\n    key: \"postparse\",\n    value: function postparse(postparsed, _ref2) {\n      var _this = this;\n      var getTraits = _ref2.getTraits,\n        _postparse = _ref2.postparse,\n        fileType = _ref2.fileType;\n      var traits = getTraits(traitName, postparsed);\n      traits = traits.map(function (trait) {\n        return trait || [];\n      });\n      traits = mergeSort(traits);\n      var _getPairs = getPairs(traits),\n        pairs = _getPairs.pairs,\n        errors = _getPairs.errors;\n      var lastRight = 0;\n      var lastPair = null;\n      var expandedPairs = pairs.map(function (pair) {\n        var expandTo = pair[0].part.expandTo;\n        if (expandTo === \"auto\" && fileType !== \"text\") {\n          var result = getExpandToDefault(postparsed, pair, _this.expandTags);\n          if (result.error) {\n            errors.push(result.error);\n          }\n          expandTo = result.value;\n        }\n        if (!expandTo || fileType === \"text\") {\n          var _left = pair[0].offset;\n          var _right = pair[1].offset;\n          if (_left < lastRight) {\n            errors.push(getUnbalancedLoopException(pair, lastPair));\n          }\n          lastPair = pair;\n          lastRight = _right;\n          return [_left, _right];\n        }\n        var left, right;\n        try {\n          left = getLeft(postparsed, expandTo, pair[0].offset);\n        } catch (e) {\n          errors.push(e);\n        }\n        try {\n          right = getRight(postparsed, expandTo, pair[1].offset);\n        } catch (e) {\n          errors.push(e);\n        }\n        if (left < lastRight) {\n          errors.push(getUnbalancedLoopException(pair, lastPair));\n        }\n        lastRight = right;\n        lastPair = pair;\n        return [left, right];\n      });\n\n      // Stryker disable all : because this check makes the function return quicker\n      if (errors.length > 0) {\n        return {\n          postparsed: postparsed,\n          errors: errors\n        };\n      }\n      // Stryker restore all\n\n      var currentPairIndex = 0;\n      var innerParts;\n      var newParsed = postparsed.reduce(function (newParsed, part, i) {\n        var inPair = currentPairIndex < pairs.length && expandedPairs[currentPairIndex][0] <= i && i <= expandedPairs[currentPairIndex][1];\n        var pair = pairs[currentPairIndex];\n        var expandedPair = expandedPairs[currentPairIndex];\n        if (!inPair) {\n          newParsed.push(part);\n          return newParsed;\n        }\n        if (expandedPair[0] === i) {\n          innerParts = [];\n        }\n        if (pair[0].offset !== i && pair[1].offset !== i) {\n          innerParts.push(part);\n        }\n        if (expandedPair[1] === i) {\n          var basePart = postparsed[pair[0].offset];\n          basePart.subparsed = _postparse(innerParts, {\n            basePart: basePart\n          });\n          basePart.endLindex = pair[1].part.lIndex;\n          delete basePart.location;\n          delete basePart.expandTo;\n          newParsed.push(basePart);\n          currentPairIndex++;\n        }\n        return newParsed;\n      }, []);\n      return {\n        postparsed: newParsed,\n        errors: errors\n      };\n    }\n  }]);\n}();\nmodule.exports = function () {\n  return wrapper(new ExpandPairTrait());\n};", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "_classCallCheck", "a", "n", "TypeError", "_defineProperties", "e", "r", "t", "length", "enumerable", "configurable", "writable", "Object", "defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "key", "_createClass", "i", "_toPrimitive", "toPrimitive", "call", "String", "Number", "traitName", "mergeSort", "require", "_require", "getLeft", "getRight", "wrapper", "_require2", "getExpandToDefault", "_require3", "getUnmatchedLoopException", "getClosingTagNotMatchOpeningTag", "getUnbalancedLoopException", "getOpenCountChange", "part", "location", "match", "start", "end", "value", "transformer", "traits", "errors", "splice", "push", "endIndex", "startIndex", "offseter", "startCandidate", "endCandidate", "tags", "for<PERSON>ach", "_ref", "getPairs", "levelTraits", "pairs", "transformedTraits", "result", "concat", "countOpen", "_i", "currentTrait", "change", "startTrait", "ExpandPairTrait", "name", "optionsTransformer", "options", "docxtemplater", "expandTags", "fileTypeConfig", "paragraphLoop", "onParagraphLoop", "postparse", "postparsed", "_ref2", "_this", "getTraits", "_postparse", "fileType", "map", "trait", "_getPairs", "lastRight", "lastPair", "expandedPairs", "pair", "expandTo", "error", "_left", "offset", "_right", "left", "right", "currentPairIndex", "innerParts", "newParsed", "reduce", "inPair", "expandedPair", "basePart", "subparsed", "endLindex", "lIndex", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/docxtemplater@3.52.0/node_modules/docxtemplater/js/modules/expand-pair-trait.js"], "sourcesContent": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _classCallCheck(a, n) { if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\"); }\nfunction _defineProperties(e, r) { for (var t = 0; t < r.length; t++) { var o = r[t]; o.enumerable = o.enumerable || !1, o.configurable = !0, \"value\" in o && (o.writable = !0), Object.defineProperty(e, _toPropertyKey(o.key), o); } }\nfunction _createClass(e, r, t) { return r && _defineProperties(e.prototype, r), t && _defineProperties(e, t), Object.defineProperty(e, \"prototype\", { writable: !1 }), e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nvar traitName = \"expandPair\";\nvar mergeSort = require(\"../merge-sort.js\");\nvar _require = require(\"../doc-utils.js\"),\n  getLeft = _require.getLeft,\n  getRight = _require.getRight;\nvar wrapper = require(\"../module-wrapper.js\");\nvar _require2 = require(\"../traits.js\"),\n  getExpandToDefault = _require2.getExpandToDefault;\nvar _require3 = require(\"../errors.js\"),\n  getUnmatchedLoopException = _require3.getUnmatchedLoopException,\n  getClosingTagNotMatchOpeningTag = _require3.getClosingTagNotMatchOpeningTag,\n  getUnbalancedLoopException = _require3.getUnbalancedLoopException;\nfunction getOpenCountChange(part) {\n  switch (part.location) {\n    case \"start\":\n      return 1;\n    case \"end\":\n      return -1;\n  }\n}\nfunction match(start, end) {\n  return start != null && end != null && (start.part.location === \"start\" && end.part.location === \"end\" && start.part.value === end.part.value || end.part.value === \"\");\n}\nfunction transformer(traits) {\n  var i = 0;\n  var errors = [];\n  while (i < traits.length) {\n    var part = traits[i].part;\n    if (part.location === \"end\") {\n      if (i === 0) {\n        traits.splice(0, 1);\n        errors.push(getUnmatchedLoopException(part));\n        return {\n          traits: traits,\n          errors: errors\n        };\n      }\n      var endIndex = i;\n      var startIndex = i - 1;\n      var offseter = 1;\n      if (match(traits[startIndex], traits[endIndex])) {\n        traits.splice(endIndex, 1);\n        traits.splice(startIndex, 1);\n        return {\n          errors: errors,\n          traits: traits\n        };\n      }\n      while (offseter < 50) {\n        var startCandidate = traits[startIndex - offseter];\n        var endCandidate = traits[endIndex + offseter];\n        if (match(startCandidate, traits[endIndex])) {\n          traits.splice(endIndex, 1);\n          traits.splice(startIndex - offseter, 1);\n          return {\n            errors: errors,\n            traits: traits\n          };\n        }\n        if (match(traits[startIndex], endCandidate)) {\n          traits.splice(endIndex + offseter, 1);\n          traits.splice(startIndex, 1);\n          return {\n            errors: errors,\n            traits: traits\n          };\n        }\n        offseter++;\n      }\n      errors.push(getClosingTagNotMatchOpeningTag({\n        tags: [traits[startIndex].part, traits[endIndex].part]\n      }));\n      traits.splice(endIndex, 1);\n      traits.splice(startIndex, 1);\n      return {\n        traits: traits,\n        errors: errors\n      };\n    }\n    i++;\n  }\n  traits.forEach(function (_ref) {\n    var part = _ref.part;\n    errors.push(getUnmatchedLoopException(part));\n  });\n  return {\n    traits: [],\n    errors: errors\n  };\n}\nfunction getPairs(traits) {\n  var levelTraits = {};\n  var errors = [];\n  var pairs = [];\n  var transformedTraits = [];\n  for (var i = 0; i < traits.length; i++) {\n    transformedTraits.push(traits[i]);\n  }\n  while (transformedTraits.length > 0) {\n    var result = transformer(transformedTraits);\n    errors = errors.concat(result.errors);\n    transformedTraits = result.traits;\n  }\n\n  // Stryker disable all : because this check makes the function return quicker\n  if (errors.length > 0) {\n    return {\n      pairs: pairs,\n      errors: errors\n    };\n  }\n  // Stryker restore all\n  var countOpen = 0;\n  for (var _i = 0; _i < traits.length; _i++) {\n    var currentTrait = traits[_i];\n    var part = currentTrait.part;\n    var change = getOpenCountChange(part);\n    countOpen += change;\n    if (change === 1) {\n      levelTraits[countOpen] = currentTrait;\n    } else {\n      var startTrait = levelTraits[countOpen + 1];\n      if (countOpen === 0) {\n        pairs = pairs.concat([[startTrait, currentTrait]]);\n      }\n    }\n    countOpen = countOpen >= 0 ? countOpen : 0;\n  }\n  return {\n    pairs: pairs,\n    errors: errors\n  };\n}\nvar ExpandPairTrait = /*#__PURE__*/function () {\n  function ExpandPairTrait() {\n    _classCallCheck(this, ExpandPairTrait);\n    this.name = \"ExpandPairTrait\";\n  }\n  return _createClass(ExpandPairTrait, [{\n    key: \"optionsTransformer\",\n    value: function optionsTransformer(options, docxtemplater) {\n      this.expandTags = docxtemplater.fileTypeConfig.expandTags.concat(docxtemplater.options.paragraphLoop ? docxtemplater.fileTypeConfig.onParagraphLoop : []);\n      return options;\n    }\n  }, {\n    key: \"postparse\",\n    value: function postparse(postparsed, _ref2) {\n      var _this = this;\n      var getTraits = _ref2.getTraits,\n        _postparse = _ref2.postparse,\n        fileType = _ref2.fileType;\n      var traits = getTraits(traitName, postparsed);\n      traits = traits.map(function (trait) {\n        return trait || [];\n      });\n      traits = mergeSort(traits);\n      var _getPairs = getPairs(traits),\n        pairs = _getPairs.pairs,\n        errors = _getPairs.errors;\n      var lastRight = 0;\n      var lastPair = null;\n      var expandedPairs = pairs.map(function (pair) {\n        var expandTo = pair[0].part.expandTo;\n        if (expandTo === \"auto\" && fileType !== \"text\") {\n          var result = getExpandToDefault(postparsed, pair, _this.expandTags);\n          if (result.error) {\n            errors.push(result.error);\n          }\n          expandTo = result.value;\n        }\n        if (!expandTo || fileType === \"text\") {\n          var _left = pair[0].offset;\n          var _right = pair[1].offset;\n          if (_left < lastRight) {\n            errors.push(getUnbalancedLoopException(pair, lastPair));\n          }\n          lastPair = pair;\n          lastRight = _right;\n          return [_left, _right];\n        }\n        var left, right;\n        try {\n          left = getLeft(postparsed, expandTo, pair[0].offset);\n        } catch (e) {\n          errors.push(e);\n        }\n        try {\n          right = getRight(postparsed, expandTo, pair[1].offset);\n        } catch (e) {\n          errors.push(e);\n        }\n        if (left < lastRight) {\n          errors.push(getUnbalancedLoopException(pair, lastPair));\n        }\n        lastRight = right;\n        lastPair = pair;\n        return [left, right];\n      });\n\n      // Stryker disable all : because this check makes the function return quicker\n      if (errors.length > 0) {\n        return {\n          postparsed: postparsed,\n          errors: errors\n        };\n      }\n      // Stryker restore all\n\n      var currentPairIndex = 0;\n      var innerParts;\n      var newParsed = postparsed.reduce(function (newParsed, part, i) {\n        var inPair = currentPairIndex < pairs.length && expandedPairs[currentPairIndex][0] <= i && i <= expandedPairs[currentPairIndex][1];\n        var pair = pairs[currentPairIndex];\n        var expandedPair = expandedPairs[currentPairIndex];\n        if (!inPair) {\n          newParsed.push(part);\n          return newParsed;\n        }\n        if (expandedPair[0] === i) {\n          innerParts = [];\n        }\n        if (pair[0].offset !== i && pair[1].offset !== i) {\n          innerParts.push(part);\n        }\n        if (expandedPair[1] === i) {\n          var basePart = postparsed[pair[0].offset];\n          basePart.subparsed = _postparse(innerParts, {\n            basePart: basePart\n          });\n          basePart.endLindex = pair[1].part.lIndex;\n          delete basePart.location;\n          delete basePart.expandTo;\n          newParsed.push(basePart);\n          currentPairIndex++;\n        }\n        return newParsed;\n      }, []);\n      return {\n        postparsed: newParsed,\n        errors: errors\n      };\n    }\n  }]);\n}();\nmodule.exports = function () {\n  return wrapper(new ExpandPairTrait());\n};"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,SAASK,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,IAAI,EAAED,CAAC,YAAYC,CAAC,CAAC,EAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;AAAE;AAClH,SAASC,iBAAiBA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,CAAC,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;IAAE,IAAIZ,CAAC,GAAGW,CAAC,CAACC,CAAC,CAAC;IAAEZ,CAAC,CAACc,UAAU,GAAGd,CAAC,CAACc,UAAU,IAAI,CAAC,CAAC,EAAEd,CAAC,CAACe,YAAY,GAAG,CAAC,CAAC,EAAE,OAAO,IAAIf,CAAC,KAAKA,CAAC,CAACgB,QAAQ,GAAG,CAAC,CAAC,CAAC,EAAEC,MAAM,CAACC,cAAc,CAACR,CAAC,EAAES,cAAc,CAACnB,CAAC,CAACoB,GAAG,CAAC,EAAEpB,CAAC,CAAC;EAAE;AAAE;AACvO,SAASqB,YAAYA,CAACX,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;EAAE,OAAOD,CAAC,IAAIF,iBAAiB,CAACC,CAAC,CAACN,SAAS,EAAEO,CAAC,CAAC,EAAEC,CAAC,IAAIH,iBAAiB,CAACC,CAAC,EAAEE,CAAC,CAAC,EAAEK,MAAM,CAACC,cAAc,CAACR,CAAC,EAAE,WAAW,EAAE;IAAEM,QAAQ,EAAE,CAAC;EAAE,CAAC,CAAC,EAAEN,CAAC;AAAE;AAC1K,SAASS,cAAcA,CAACP,CAAC,EAAE;EAAE,IAAIU,CAAC,GAAGC,YAAY,CAACX,CAAC,EAAE,QAAQ,CAAC;EAAE,OAAO,QAAQ,IAAIb,OAAO,CAACuB,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,GAAG,EAAE;AAAE;AAC5G,SAASC,YAAYA,CAACX,CAAC,EAAED,CAAC,EAAE;EAAE,IAAI,QAAQ,IAAIZ,OAAO,CAACa,CAAC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;EAAE,IAAIF,CAAC,GAAGE,CAAC,CAACX,MAAM,CAACuB,WAAW,CAAC;EAAE,IAAI,KAAK,CAAC,KAAKd,CAAC,EAAE;IAAE,IAAIY,CAAC,GAAGZ,CAAC,CAACe,IAAI,CAACb,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;IAAE,IAAI,QAAQ,IAAIZ,OAAO,CAACuB,CAAC,CAAC,EAAE,OAAOA,CAAC;IAAE,MAAM,IAAId,SAAS,CAAC,8CAA8C,CAAC;EAAE;EAAE,OAAO,CAAC,QAAQ,KAAKG,CAAC,GAAGe,MAAM,GAAGC,MAAM,EAAEf,CAAC,CAAC;AAAE;AAC3T,IAAIgB,SAAS,GAAG,YAAY;AAC5B,IAAIC,SAAS,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAC3C,IAAIC,QAAQ,GAAGD,OAAO,CAAC,iBAAiB,CAAC;EACvCE,OAAO,GAAGD,QAAQ,CAACC,OAAO;EAC1BC,QAAQ,GAAGF,QAAQ,CAACE,QAAQ;AAC9B,IAAIC,OAAO,GAAGJ,OAAO,CAAC,sBAAsB,CAAC;AAC7C,IAAIK,SAAS,GAAGL,OAAO,CAAC,cAAc,CAAC;EACrCM,kBAAkB,GAAGD,SAAS,CAACC,kBAAkB;AACnD,IAAIC,SAAS,GAAGP,OAAO,CAAC,cAAc,CAAC;EACrCQ,yBAAyB,GAAGD,SAAS,CAACC,yBAAyB;EAC/DC,+BAA+B,GAAGF,SAAS,CAACE,+BAA+B;EAC3EC,0BAA0B,GAAGH,SAAS,CAACG,0BAA0B;AACnE,SAASC,kBAAkBA,CAACC,IAAI,EAAE;EAChC,QAAQA,IAAI,CAACC,QAAQ;IACnB,KAAK,OAAO;MACV,OAAO,CAAC;IACV,KAAK,KAAK;MACR,OAAO,CAAC,CAAC;EACb;AACF;AACA,SAASC,KAAKA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzB,OAAOD,KAAK,IAAI,IAAI,IAAIC,GAAG,IAAI,IAAI,KAAKD,KAAK,CAACH,IAAI,CAACC,QAAQ,KAAK,OAAO,IAAIG,GAAG,CAACJ,IAAI,CAACC,QAAQ,KAAK,KAAK,IAAIE,KAAK,CAACH,IAAI,CAACK,KAAK,KAAKD,GAAG,CAACJ,IAAI,CAACK,KAAK,IAAID,GAAG,CAACJ,IAAI,CAACK,KAAK,KAAK,EAAE,CAAC;AACzK;AACA,SAASC,WAAWA,CAACC,MAAM,EAAE;EAC3B,IAAI3B,CAAC,GAAG,CAAC;EACT,IAAI4B,MAAM,GAAG,EAAE;EACf,OAAO5B,CAAC,GAAG2B,MAAM,CAACpC,MAAM,EAAE;IACxB,IAAI6B,IAAI,GAAGO,MAAM,CAAC3B,CAAC,CAAC,CAACoB,IAAI;IACzB,IAAIA,IAAI,CAACC,QAAQ,KAAK,KAAK,EAAE;MAC3B,IAAIrB,CAAC,KAAK,CAAC,EAAE;QACX2B,MAAM,CAACE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;QACnBD,MAAM,CAACE,IAAI,CAACd,yBAAyB,CAACI,IAAI,CAAC,CAAC;QAC5C,OAAO;UACLO,MAAM,EAAEA,MAAM;UACdC,MAAM,EAAEA;QACV,CAAC;MACH;MACA,IAAIG,QAAQ,GAAG/B,CAAC;MAChB,IAAIgC,UAAU,GAAGhC,CAAC,GAAG,CAAC;MACtB,IAAIiC,QAAQ,GAAG,CAAC;MAChB,IAAIX,KAAK,CAACK,MAAM,CAACK,UAAU,CAAC,EAAEL,MAAM,CAACI,QAAQ,CAAC,CAAC,EAAE;QAC/CJ,MAAM,CAACE,MAAM,CAACE,QAAQ,EAAE,CAAC,CAAC;QAC1BJ,MAAM,CAACE,MAAM,CAACG,UAAU,EAAE,CAAC,CAAC;QAC5B,OAAO;UACLJ,MAAM,EAAEA,MAAM;UACdD,MAAM,EAAEA;QACV,CAAC;MACH;MACA,OAAOM,QAAQ,GAAG,EAAE,EAAE;QACpB,IAAIC,cAAc,GAAGP,MAAM,CAACK,UAAU,GAAGC,QAAQ,CAAC;QAClD,IAAIE,YAAY,GAAGR,MAAM,CAACI,QAAQ,GAAGE,QAAQ,CAAC;QAC9C,IAAIX,KAAK,CAACY,cAAc,EAAEP,MAAM,CAACI,QAAQ,CAAC,CAAC,EAAE;UAC3CJ,MAAM,CAACE,MAAM,CAACE,QAAQ,EAAE,CAAC,CAAC;UAC1BJ,MAAM,CAACE,MAAM,CAACG,UAAU,GAAGC,QAAQ,EAAE,CAAC,CAAC;UACvC,OAAO;YACLL,MAAM,EAAEA,MAAM;YACdD,MAAM,EAAEA;UACV,CAAC;QACH;QACA,IAAIL,KAAK,CAACK,MAAM,CAACK,UAAU,CAAC,EAAEG,YAAY,CAAC,EAAE;UAC3CR,MAAM,CAACE,MAAM,CAACE,QAAQ,GAAGE,QAAQ,EAAE,CAAC,CAAC;UACrCN,MAAM,CAACE,MAAM,CAACG,UAAU,EAAE,CAAC,CAAC;UAC5B,OAAO;YACLJ,MAAM,EAAEA,MAAM;YACdD,MAAM,EAAEA;UACV,CAAC;QACH;QACAM,QAAQ,EAAE;MACZ;MACAL,MAAM,CAACE,IAAI,CAACb,+BAA+B,CAAC;QAC1CmB,IAAI,EAAE,CAACT,MAAM,CAACK,UAAU,CAAC,CAACZ,IAAI,EAAEO,MAAM,CAACI,QAAQ,CAAC,CAACX,IAAI;MACvD,CAAC,CAAC,CAAC;MACHO,MAAM,CAACE,MAAM,CAACE,QAAQ,EAAE,CAAC,CAAC;MAC1BJ,MAAM,CAACE,MAAM,CAACG,UAAU,EAAE,CAAC,CAAC;MAC5B,OAAO;QACLL,MAAM,EAAEA,MAAM;QACdC,MAAM,EAAEA;MACV,CAAC;IACH;IACA5B,CAAC,EAAE;EACL;EACA2B,MAAM,CAACU,OAAO,CAAC,UAAUC,IAAI,EAAE;IAC7B,IAAIlB,IAAI,GAAGkB,IAAI,CAAClB,IAAI;IACpBQ,MAAM,CAACE,IAAI,CAACd,yBAAyB,CAACI,IAAI,CAAC,CAAC;EAC9C,CAAC,CAAC;EACF,OAAO;IACLO,MAAM,EAAE,EAAE;IACVC,MAAM,EAAEA;EACV,CAAC;AACH;AACA,SAASW,QAAQA,CAACZ,MAAM,EAAE;EACxB,IAAIa,WAAW,GAAG,CAAC,CAAC;EACpB,IAAIZ,MAAM,GAAG,EAAE;EACf,IAAIa,KAAK,GAAG,EAAE;EACd,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,KAAK,IAAI1C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2B,MAAM,CAACpC,MAAM,EAAES,CAAC,EAAE,EAAE;IACtC0C,iBAAiB,CAACZ,IAAI,CAACH,MAAM,CAAC3B,CAAC,CAAC,CAAC;EACnC;EACA,OAAO0C,iBAAiB,CAACnD,MAAM,GAAG,CAAC,EAAE;IACnC,IAAIoD,MAAM,GAAGjB,WAAW,CAACgB,iBAAiB,CAAC;IAC3Cd,MAAM,GAAGA,MAAM,CAACgB,MAAM,CAACD,MAAM,CAACf,MAAM,CAAC;IACrCc,iBAAiB,GAAGC,MAAM,CAAChB,MAAM;EACnC;;EAEA;EACA,IAAIC,MAAM,CAACrC,MAAM,GAAG,CAAC,EAAE;IACrB,OAAO;MACLkD,KAAK,EAAEA,KAAK;MACZb,MAAM,EAAEA;IACV,CAAC;EACH;EACA;EACA,IAAIiB,SAAS,GAAG,CAAC;EACjB,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGnB,MAAM,CAACpC,MAAM,EAAEuD,EAAE,EAAE,EAAE;IACzC,IAAIC,YAAY,GAAGpB,MAAM,CAACmB,EAAE,CAAC;IAC7B,IAAI1B,IAAI,GAAG2B,YAAY,CAAC3B,IAAI;IAC5B,IAAI4B,MAAM,GAAG7B,kBAAkB,CAACC,IAAI,CAAC;IACrCyB,SAAS,IAAIG,MAAM;IACnB,IAAIA,MAAM,KAAK,CAAC,EAAE;MAChBR,WAAW,CAACK,SAAS,CAAC,GAAGE,YAAY;IACvC,CAAC,MAAM;MACL,IAAIE,UAAU,GAAGT,WAAW,CAACK,SAAS,GAAG,CAAC,CAAC;MAC3C,IAAIA,SAAS,KAAK,CAAC,EAAE;QACnBJ,KAAK,GAAGA,KAAK,CAACG,MAAM,CAAC,CAAC,CAACK,UAAU,EAAEF,YAAY,CAAC,CAAC,CAAC;MACpD;IACF;IACAF,SAAS,GAAGA,SAAS,IAAI,CAAC,GAAGA,SAAS,GAAG,CAAC;EAC5C;EACA,OAAO;IACLJ,KAAK,EAAEA,KAAK;IACZb,MAAM,EAAEA;EACV,CAAC;AACH;AACA,IAAIsB,eAAe,GAAG,aAAa,YAAY;EAC7C,SAASA,eAAeA,CAAA,EAAG;IACzBnE,eAAe,CAAC,IAAI,EAAEmE,eAAe,CAAC;IACtC,IAAI,CAACC,IAAI,GAAG,iBAAiB;EAC/B;EACA,OAAOpD,YAAY,CAACmD,eAAe,EAAE,CAAC;IACpCpD,GAAG,EAAE,oBAAoB;IACzB2B,KAAK,EAAE,SAAS2B,kBAAkBA,CAACC,OAAO,EAAEC,aAAa,EAAE;MACzD,IAAI,CAACC,UAAU,GAAGD,aAAa,CAACE,cAAc,CAACD,UAAU,CAACX,MAAM,CAACU,aAAa,CAACD,OAAO,CAACI,aAAa,GAAGH,aAAa,CAACE,cAAc,CAACE,eAAe,GAAG,EAAE,CAAC;MACzJ,OAAOL,OAAO;IAChB;EACF,CAAC,EAAE;IACDvD,GAAG,EAAE,WAAW;IAChB2B,KAAK,EAAE,SAASkC,SAASA,CAACC,UAAU,EAAEC,KAAK,EAAE;MAC3C,IAAIC,KAAK,GAAG,IAAI;MAChB,IAAIC,SAAS,GAAGF,KAAK,CAACE,SAAS;QAC7BC,UAAU,GAAGH,KAAK,CAACF,SAAS;QAC5BM,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;MAC3B,IAAItC,MAAM,GAAGoC,SAAS,CAACzD,SAAS,EAAEsD,UAAU,CAAC;MAC7CjC,MAAM,GAAGA,MAAM,CAACuC,GAAG,CAAC,UAAUC,KAAK,EAAE;QACnC,OAAOA,KAAK,IAAI,EAAE;MACpB,CAAC,CAAC;MACFxC,MAAM,GAAGpB,SAAS,CAACoB,MAAM,CAAC;MAC1B,IAAIyC,SAAS,GAAG7B,QAAQ,CAACZ,MAAM,CAAC;QAC9Bc,KAAK,GAAG2B,SAAS,CAAC3B,KAAK;QACvBb,MAAM,GAAGwC,SAAS,CAACxC,MAAM;MAC3B,IAAIyC,SAAS,GAAG,CAAC;MACjB,IAAIC,QAAQ,GAAG,IAAI;MACnB,IAAIC,aAAa,GAAG9B,KAAK,CAACyB,GAAG,CAAC,UAAUM,IAAI,EAAE;QAC5C,IAAIC,QAAQ,GAAGD,IAAI,CAAC,CAAC,CAAC,CAACpD,IAAI,CAACqD,QAAQ;QACpC,IAAIA,QAAQ,KAAK,MAAM,IAAIR,QAAQ,KAAK,MAAM,EAAE;UAC9C,IAAItB,MAAM,GAAG7B,kBAAkB,CAAC8C,UAAU,EAAEY,IAAI,EAAEV,KAAK,CAACP,UAAU,CAAC;UACnE,IAAIZ,MAAM,CAAC+B,KAAK,EAAE;YAChB9C,MAAM,CAACE,IAAI,CAACa,MAAM,CAAC+B,KAAK,CAAC;UAC3B;UACAD,QAAQ,GAAG9B,MAAM,CAAClB,KAAK;QACzB;QACA,IAAI,CAACgD,QAAQ,IAAIR,QAAQ,KAAK,MAAM,EAAE;UACpC,IAAIU,KAAK,GAAGH,IAAI,CAAC,CAAC,CAAC,CAACI,MAAM;UAC1B,IAAIC,MAAM,GAAGL,IAAI,CAAC,CAAC,CAAC,CAACI,MAAM;UAC3B,IAAID,KAAK,GAAGN,SAAS,EAAE;YACrBzC,MAAM,CAACE,IAAI,CAACZ,0BAA0B,CAACsD,IAAI,EAAEF,QAAQ,CAAC,CAAC;UACzD;UACAA,QAAQ,GAAGE,IAAI;UACfH,SAAS,GAAGQ,MAAM;UAClB,OAAO,CAACF,KAAK,EAAEE,MAAM,CAAC;QACxB;QACA,IAAIC,IAAI,EAAEC,KAAK;QACf,IAAI;UACFD,IAAI,GAAGpE,OAAO,CAACkD,UAAU,EAAEa,QAAQ,EAAED,IAAI,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC;QACtD,CAAC,CAAC,OAAOxF,CAAC,EAAE;UACVwC,MAAM,CAACE,IAAI,CAAC1C,CAAC,CAAC;QAChB;QACA,IAAI;UACF2F,KAAK,GAAGpE,QAAQ,CAACiD,UAAU,EAAEa,QAAQ,EAAED,IAAI,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC;QACxD,CAAC,CAAC,OAAOxF,CAAC,EAAE;UACVwC,MAAM,CAACE,IAAI,CAAC1C,CAAC,CAAC;QAChB;QACA,IAAI0F,IAAI,GAAGT,SAAS,EAAE;UACpBzC,MAAM,CAACE,IAAI,CAACZ,0BAA0B,CAACsD,IAAI,EAAEF,QAAQ,CAAC,CAAC;QACzD;QACAD,SAAS,GAAGU,KAAK;QACjBT,QAAQ,GAAGE,IAAI;QACf,OAAO,CAACM,IAAI,EAAEC,KAAK,CAAC;MACtB,CAAC,CAAC;;MAEF;MACA,IAAInD,MAAM,CAACrC,MAAM,GAAG,CAAC,EAAE;QACrB,OAAO;UACLqE,UAAU,EAAEA,UAAU;UACtBhC,MAAM,EAAEA;QACV,CAAC;MACH;MACA;;MAEA,IAAIoD,gBAAgB,GAAG,CAAC;MACxB,IAAIC,UAAU;MACd,IAAIC,SAAS,GAAGtB,UAAU,CAACuB,MAAM,CAAC,UAAUD,SAAS,EAAE9D,IAAI,EAAEpB,CAAC,EAAE;QAC9D,IAAIoF,MAAM,GAAGJ,gBAAgB,GAAGvC,KAAK,CAAClD,MAAM,IAAIgF,aAAa,CAACS,gBAAgB,CAAC,CAAC,CAAC,CAAC,IAAIhF,CAAC,IAAIA,CAAC,IAAIuE,aAAa,CAACS,gBAAgB,CAAC,CAAC,CAAC,CAAC;QAClI,IAAIR,IAAI,GAAG/B,KAAK,CAACuC,gBAAgB,CAAC;QAClC,IAAIK,YAAY,GAAGd,aAAa,CAACS,gBAAgB,CAAC;QAClD,IAAI,CAACI,MAAM,EAAE;UACXF,SAAS,CAACpD,IAAI,CAACV,IAAI,CAAC;UACpB,OAAO8D,SAAS;QAClB;QACA,IAAIG,YAAY,CAAC,CAAC,CAAC,KAAKrF,CAAC,EAAE;UACzBiF,UAAU,GAAG,EAAE;QACjB;QACA,IAAIT,IAAI,CAAC,CAAC,CAAC,CAACI,MAAM,KAAK5E,CAAC,IAAIwE,IAAI,CAAC,CAAC,CAAC,CAACI,MAAM,KAAK5E,CAAC,EAAE;UAChDiF,UAAU,CAACnD,IAAI,CAACV,IAAI,CAAC;QACvB;QACA,IAAIiE,YAAY,CAAC,CAAC,CAAC,KAAKrF,CAAC,EAAE;UACzB,IAAIsF,QAAQ,GAAG1B,UAAU,CAACY,IAAI,CAAC,CAAC,CAAC,CAACI,MAAM,CAAC;UACzCU,QAAQ,CAACC,SAAS,GAAGvB,UAAU,CAACiB,UAAU,EAAE;YAC1CK,QAAQ,EAAEA;UACZ,CAAC,CAAC;UACFA,QAAQ,CAACE,SAAS,GAAGhB,IAAI,CAAC,CAAC,CAAC,CAACpD,IAAI,CAACqE,MAAM;UACxC,OAAOH,QAAQ,CAACjE,QAAQ;UACxB,OAAOiE,QAAQ,CAACb,QAAQ;UACxBS,SAAS,CAACpD,IAAI,CAACwD,QAAQ,CAAC;UACxBN,gBAAgB,EAAE;QACpB;QACA,OAAOE,SAAS;MAClB,CAAC,EAAE,EAAE,CAAC;MACN,OAAO;QACLtB,UAAU,EAAEsB,SAAS;QACrBtD,MAAM,EAAEA;MACV,CAAC;IACH;EACF,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;AACH8D,MAAM,CAACC,OAAO,GAAG,YAAY;EAC3B,OAAO/E,OAAO,CAAC,IAAIsC,eAAe,CAAC,CAAC,CAAC;AACvC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}