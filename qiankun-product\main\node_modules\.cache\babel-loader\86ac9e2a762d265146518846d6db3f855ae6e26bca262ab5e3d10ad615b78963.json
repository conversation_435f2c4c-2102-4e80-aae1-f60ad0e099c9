{"ast": null, "code": "import { createVNode, render } from 'vue';\nimport unauthorized from './unauthorized.vue';\n\n// 准备div容器\nvar divNode = createVNode('div', {\n  class: 'zy-confirm-container'\n});\nrender(divNode, document.body);\n// 获取 DOM 节点, 用于挂载组件或卸载组件\nvar container = divNode.el;\nexport default (function (_ref) {\n  var name = _ref.name,\n    callback = _ref.callback;\n  // 2. 点击取消按钮，触发callback同时销毁组件\n  var cancelCallback = function cancelCallback() {\n    render(null, container);\n    if (callback) {\n      callback();\n    }\n  };\n  // 1. 创建 unauthorized 组件\n  var VNode = createVNode(unauthorized, {\n    name,\n    cancelCallback\n  });\n  render(VNode, container);\n});", "map": {"version": 3, "names": ["createVNode", "render", "unauthorized", "divNode", "class", "document", "body", "container", "el", "_ref", "name", "callback", "cancelCallback", "VNode"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/common/components/unauthorized/index.js"], "sourcesContent": ["import { createVNode, render } from 'vue'\r\nimport unauthorized from './unauthorized.vue'\r\n\r\n// 准备div容器\r\nconst divNode = createVNode('div', { class: 'zy-confirm-container' })\r\nrender(divNode, document.body)\r\n// 获取 DOM 节点, 用于挂载组件或卸载组件\r\nconst container = divNode.el\r\n\r\nexport default ({ name, callback }) => {\r\n  // 2. 点击取消按钮，触发callback同时销毁组件\r\n  const cancelCallback = () => {\r\n    render(null, container)\r\n    if (callback) { callback() }\r\n  }\r\n  // 1. 创建 unauthorized 组件\r\n  const VNode = createVNode(unauthorized, { name, cancelCallback })\r\n  render(VNode, container)\r\n}\r\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,MAAM,QAAQ,KAAK;AACzC,OAAOC,YAAY,MAAM,oBAAoB;;AAE7C;AACA,IAAMC,OAAO,GAAGH,WAAW,CAAC,KAAK,EAAE;EAAEI,KAAK,EAAE;AAAuB,CAAC,CAAC;AACrEH,MAAM,CAACE,OAAO,EAAEE,QAAQ,CAACC,IAAI,CAAC;AAC9B;AACA,IAAMC,SAAS,GAAGJ,OAAO,CAACK,EAAE;AAE5B,gBAAe,UAAAC,IAAA,EAAwB;EAAA,IAArBC,IAAI,GAAAD,IAAA,CAAJC,IAAI;IAAEC,QAAQ,GAAAF,IAAA,CAARE,QAAQ;EAC9B;EACA,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAAS;IAC3BX,MAAM,CAAC,IAAI,EAAEM,SAAS,CAAC;IACvB,IAAII,QAAQ,EAAE;MAAEA,QAAQ,CAAC,CAAC;IAAC;EAC7B,CAAC;EACD;EACA,IAAME,KAAK,GAAGb,WAAW,CAACE,YAAY,EAAE;IAAEQ,IAAI;IAAEE;EAAe,CAAC,CAAC;EACjEX,MAAM,CAACY,KAAK,EAAEN,SAAS,CAAC;AAC1B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}