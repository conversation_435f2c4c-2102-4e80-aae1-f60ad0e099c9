{"ast": null, "code": "function ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == typeof i ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != typeof t || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != typeof i) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); }\nimport { ref, getCurrentInstance, computed, unref, watch } from 'vue';\nimport { getRowIdentity, walkTreeNode } from '../util.mjs';\nfunction useTree(watcherData) {\n  var expandRowKeys = ref([]);\n  var treeData = ref({});\n  var indent = ref(16);\n  var lazy = ref(false);\n  var lazyTreeNodeMap = ref({});\n  var lazyColumnIdentifier = ref(\"hasChildren\");\n  var childrenColumnName = ref(\"children\");\n  var instance = getCurrentInstance();\n  var normalizedData = computed(function () {\n    if (!watcherData.rowKey.value) return {};\n    var data = watcherData.data.value || [];\n    return normalize(data);\n  });\n  var normalizedLazyNode = computed(function () {\n    var rowKey = watcherData.rowKey.value;\n    var keys = Object.keys(lazyTreeNodeMap.value);\n    var res = {};\n    if (!keys.length) return res;\n    keys.forEach(function (key) {\n      if (lazyTreeNodeMap.value[key].length) {\n        var item = {\n          children: []\n        };\n        lazyTreeNodeMap.value[key].forEach(function (row) {\n          var currentRowKey = getRowIdentity(row, rowKey);\n          item.children.push(currentRowKey);\n          if (row[lazyColumnIdentifier.value] && !res[currentRowKey]) {\n            res[currentRowKey] = {\n              children: []\n            };\n          }\n        });\n        res[key] = item;\n      }\n    });\n    return res;\n  });\n  var normalize = function normalize(data) {\n    var rowKey = watcherData.rowKey.value;\n    var res = {};\n    walkTreeNode(data, function (parent, children, level) {\n      var parentId = getRowIdentity(parent, rowKey);\n      if (Array.isArray(children)) {\n        res[parentId] = {\n          children: children.map(function (row) {\n            return getRowIdentity(row, rowKey);\n          }),\n          level\n        };\n      } else if (lazy.value) {\n        res[parentId] = {\n          children: [],\n          lazy: true,\n          level\n        };\n      }\n    }, childrenColumnName.value, lazyColumnIdentifier.value);\n    return res;\n  };\n  var updateTreeData = function updateTreeData() {\n    var ifChangeExpandRowKeys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var ifExpandAll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : function (_a) {\n      return (_a = instance.store) == null ? void 0 : _a.states.defaultExpandAll.value;\n    }();\n    var _a2;\n    var nested = normalizedData.value;\n    var normalizedLazyNode_ = normalizedLazyNode.value;\n    var keys = Object.keys(nested);\n    var newTreeData = {};\n    if (keys.length) {\n      var oldTreeData = unref(treeData);\n      var rootLazyRowKeys = [];\n      var getExpanded = function getExpanded(oldValue, key) {\n        if (ifChangeExpandRowKeys) {\n          if (expandRowKeys.value) {\n            return ifExpandAll || expandRowKeys.value.includes(key);\n          } else {\n            return !!(ifExpandAll || (oldValue == null ? void 0 : oldValue.expanded));\n          }\n        } else {\n          var included = ifExpandAll || expandRowKeys.value && expandRowKeys.value.includes(key);\n          return !!((oldValue == null ? void 0 : oldValue.expanded) || included);\n        }\n      };\n      keys.forEach(function (key) {\n        var oldValue = oldTreeData[key];\n        var newValue = _objectSpread({}, nested[key]);\n        newValue.expanded = getExpanded(oldValue, key);\n        if (newValue.lazy) {\n          var _ref = oldValue || {},\n            _ref$loaded = _ref.loaded,\n            loaded = _ref$loaded === void 0 ? false : _ref$loaded,\n            _ref$loading = _ref.loading,\n            loading = _ref$loading === void 0 ? false : _ref$loading;\n          newValue.loaded = !!loaded;\n          newValue.loading = !!loading;\n          rootLazyRowKeys.push(key);\n        }\n        newTreeData[key] = newValue;\n      });\n      var lazyKeys = Object.keys(normalizedLazyNode_);\n      if (lazy.value && lazyKeys.length && rootLazyRowKeys.length) {\n        lazyKeys.forEach(function (key) {\n          var oldValue = oldTreeData[key];\n          var lazyNodeChildren = normalizedLazyNode_[key].children;\n          if (rootLazyRowKeys.includes(key)) {\n            if (newTreeData[key].children.length !== 0) {\n              throw new Error(\"[ElTable]children must be an empty array.\");\n            }\n            newTreeData[key].children = lazyNodeChildren;\n          } else {\n            var _ref2 = oldValue || {},\n              _ref2$loaded = _ref2.loaded,\n              loaded = _ref2$loaded === void 0 ? false : _ref2$loaded,\n              _ref2$loading = _ref2.loading,\n              loading = _ref2$loading === void 0 ? false : _ref2$loading;\n            newTreeData[key] = {\n              lazy: true,\n              loaded: !!loaded,\n              loading: !!loading,\n              expanded: getExpanded(oldValue, key),\n              children: lazyNodeChildren,\n              level: \"\"\n            };\n          }\n        });\n      }\n    }\n    treeData.value = newTreeData;\n    (_a2 = instance.store) == null ? void 0 : _a2.updateTableScrollY();\n  };\n  watch(function () {\n    return expandRowKeys.value;\n  }, function () {\n    updateTreeData(true);\n  });\n  watch(function () {\n    return normalizedData.value;\n  }, function () {\n    updateTreeData();\n  });\n  watch(function () {\n    return normalizedLazyNode.value;\n  }, function () {\n    updateTreeData();\n  });\n  var updateTreeExpandKeys = function updateTreeExpandKeys(value) {\n    expandRowKeys.value = value;\n    updateTreeData();\n  };\n  var toggleTreeExpansion = function toggleTreeExpansion(row, expanded) {\n    instance.store.assertRowKey();\n    var rowKey = watcherData.rowKey.value;\n    var id = getRowIdentity(row, rowKey);\n    var data = id && treeData.value[id];\n    if (id && data && \"expanded\" in data) {\n      var oldExpanded = data.expanded;\n      expanded = typeof expanded === \"undefined\" ? !data.expanded : expanded;\n      treeData.value[id].expanded = expanded;\n      if (oldExpanded !== expanded) {\n        instance.emit(\"expand-change\", row, expanded);\n      }\n      instance.store.updateTableScrollY();\n    }\n  };\n  var loadOrToggle = function loadOrToggle(row) {\n    instance.store.assertRowKey();\n    var rowKey = watcherData.rowKey.value;\n    var id = getRowIdentity(row, rowKey);\n    var data = treeData.value[id];\n    if (lazy.value && data && \"loaded\" in data && !data.loaded) {\n      loadData(row, id, data);\n    } else {\n      toggleTreeExpansion(row, void 0);\n    }\n  };\n  var loadData = function loadData(row, key, treeNode) {\n    var load = instance.props.load;\n    if (load && !treeData.value[key].loaded) {\n      treeData.value[key].loading = true;\n      load(row, treeNode, function (data) {\n        if (!Array.isArray(data)) {\n          throw new TypeError(\"[ElTable] data must be an array\");\n        }\n        treeData.value[key].loading = false;\n        treeData.value[key].loaded = true;\n        treeData.value[key].expanded = true;\n        if (data.length) {\n          lazyTreeNodeMap.value[key] = data;\n        }\n        instance.emit(\"expand-change\", row, true);\n      });\n    }\n  };\n  return {\n    loadData,\n    loadOrToggle,\n    toggleTreeExpansion,\n    updateTreeExpandKeys,\n    updateTreeData,\n    normalize,\n    states: {\n      expandRowKeys,\n      treeData,\n      indent,\n      lazy,\n      lazyTreeNodeMap,\n      lazyColumnIdentifier,\n      childrenColumnName\n    }\n  };\n}\nexport { useTree as default };", "map": {"version": 3, "names": ["useTree", "watcherData", "expandRowKeys", "ref", "treeData", "indent", "lazy", "lazyTreeNodeMap", "lazyColumnIdentifier", "childrenColumnName", "instance", "getCurrentInstance", "normalizedData", "computed", "<PERSON><PERSON><PERSON>", "value", "data", "normalize", "normalizedLazyNode", "keys", "Object", "res", "length", "for<PERSON>ach", "key", "item", "children", "row", "currentRowKey", "getRowIdentity", "push", "walkTreeNode", "parent", "level", "parentId", "Array", "isArray", "map", "updateTreeData", "ifChangeExpandRowKeys", "arguments", "undefined", "ifExpandAll", "_a", "store", "states", "defaultExpandAll", "_a2", "nested", "normalizedLazyNode_", "newTreeData", "oldTreeData", "unref", "rootLazyRowKeys", "getExpanded", "oldValue", "includes", "expanded", "included", "newValue", "_objectSpread", "_ref", "_ref$loaded", "loaded", "_ref$loading", "loading", "lazy<PERSON>eys", "lazy<PERSON><PERSON><PERSON><PERSON><PERSON>n", "Error", "_ref2", "_ref2$loaded", "_ref2$loading", "updateTableScrollY", "watch", "updateTreeExpandKeys", "toggleTreeExpansion", "assertRowKey", "id", "oldExpanded", "emit", "loadOrToggle", "loadData", "treeNode", "load", "props", "TypeError"], "sources": ["../../../../../../../packages/components/table/src/store/tree.ts"], "sourcesContent": ["// @ts-nocheck\nimport { computed, getCurrentInstance, ref, unref, watch } from 'vue'\nimport { getRowIdentity, walkTreeNode } from '../util'\n\nimport type { WatcherPropsData } from '.'\nimport type { Table, TableProps } from '../table/defaults'\n\nfunction useTree<T>(watcherData: WatcherPropsData<T>) {\n  const expandRowKeys = ref<string[]>([])\n  const treeData = ref<unknown>({})\n  const indent = ref(16)\n  const lazy = ref(false)\n  const lazyTreeNodeMap = ref({})\n  const lazyColumnIdentifier = ref('hasChildren')\n  const childrenColumnName = ref('children')\n  const instance = getCurrentInstance() as Table<T>\n  const normalizedData = computed(() => {\n    if (!watcherData.rowKey.value) return {}\n    const data = watcherData.data.value || []\n    return normalize(data)\n  })\n  const normalizedLazyNode = computed(() => {\n    const rowKey = watcherData.rowKey.value\n    const keys = Object.keys(lazyTreeNodeMap.value)\n    const res = {}\n    if (!keys.length) return res\n    keys.forEach((key) => {\n      if (lazyTreeNodeMap.value[key].length) {\n        const item = { children: [] }\n        lazyTreeNodeMap.value[key].forEach((row) => {\n          const currentRowKey = getRowIdentity(row, rowKey)\n          item.children.push(currentRowKey)\n          if (row[lazyColumnIdentifier.value] && !res[currentRowKey]) {\n            res[currentRowKey] = { children: [] }\n          }\n        })\n        res[key] = item\n      }\n    })\n    return res\n  })\n\n  const normalize = (data) => {\n    const rowKey = watcherData.rowKey.value\n    const res = {}\n    walkTreeNode(\n      data,\n      (parent, children, level) => {\n        const parentId = getRowIdentity(parent, rowKey)\n        if (Array.isArray(children)) {\n          res[parentId] = {\n            children: children.map((row) => getRowIdentity(row, rowKey)),\n            level,\n          }\n        } else if (lazy.value) {\n          // 当 children 不存在且 lazy 为 true，该节点即为懒加载的节点\n          res[parentId] = {\n            children: [],\n            lazy: true,\n            level,\n          }\n        }\n      },\n      childrenColumnName.value,\n      lazyColumnIdentifier.value\n    )\n    return res\n  }\n\n  const updateTreeData = (\n    ifChangeExpandRowKeys = false,\n    ifExpandAll = instance.store?.states.defaultExpandAll.value\n  ) => {\n    const nested = normalizedData.value\n    const normalizedLazyNode_ = normalizedLazyNode.value\n    const keys = Object.keys(nested)\n    const newTreeData = {}\n    if (keys.length) {\n      const oldTreeData = unref(treeData)\n      const rootLazyRowKeys = []\n      const getExpanded = (oldValue, key) => {\n        if (ifChangeExpandRowKeys) {\n          if (expandRowKeys.value) {\n            return ifExpandAll || expandRowKeys.value.includes(key)\n          } else {\n            return !!(ifExpandAll || oldValue?.expanded)\n          }\n        } else {\n          const included =\n            ifExpandAll ||\n            (expandRowKeys.value && expandRowKeys.value.includes(key))\n          return !!(oldValue?.expanded || included)\n        }\n      }\n      // 合并 expanded 与 display，确保数据刷新后，状态不变\n      keys.forEach((key) => {\n        const oldValue = oldTreeData[key]\n        const newValue = { ...nested[key] }\n        newValue.expanded = getExpanded(oldValue, key)\n        if (newValue.lazy) {\n          const { loaded = false, loading = false } = oldValue || {}\n          newValue.loaded = !!loaded\n          newValue.loading = !!loading\n          rootLazyRowKeys.push(key)\n        }\n        newTreeData[key] = newValue\n      })\n      // 根据懒加载数据更新 treeData\n      const lazyKeys = Object.keys(normalizedLazyNode_)\n      if (lazy.value && lazyKeys.length && rootLazyRowKeys.length) {\n        lazyKeys.forEach((key) => {\n          const oldValue = oldTreeData[key]\n          const lazyNodeChildren = normalizedLazyNode_[key].children\n          if (rootLazyRowKeys.includes(key)) {\n            // 懒加载的 root 节点，更新一下原有的数据，原来的 children 一定是空数组\n            if (newTreeData[key].children.length !== 0) {\n              throw new Error('[ElTable]children must be an empty array.')\n            }\n            newTreeData[key].children = lazyNodeChildren\n          } else {\n            const { loaded = false, loading = false } = oldValue || {}\n            newTreeData[key] = {\n              lazy: true,\n              loaded: !!loaded,\n              loading: !!loading,\n              expanded: getExpanded(oldValue, key),\n              children: lazyNodeChildren,\n              level: '',\n            }\n          }\n        })\n      }\n    }\n    treeData.value = newTreeData\n    instance.store?.updateTableScrollY()\n  }\n\n  watch(\n    () => expandRowKeys.value,\n    () => {\n      updateTreeData(true)\n    }\n  )\n\n  watch(\n    () => normalizedData.value,\n    () => {\n      updateTreeData()\n    }\n  )\n  watch(\n    () => normalizedLazyNode.value,\n    () => {\n      updateTreeData()\n    }\n  )\n\n  const updateTreeExpandKeys = (value: string[]) => {\n    expandRowKeys.value = value\n    updateTreeData()\n  }\n\n  const toggleTreeExpansion = (row: T, expanded?: boolean) => {\n    instance.store.assertRowKey()\n\n    const rowKey = watcherData.rowKey.value\n    const id = getRowIdentity(row, rowKey)\n    const data = id && treeData.value[id]\n    if (id && data && 'expanded' in data) {\n      const oldExpanded = data.expanded\n      expanded = typeof expanded === 'undefined' ? !data.expanded : expanded\n      treeData.value[id].expanded = expanded\n      if (oldExpanded !== expanded) {\n        instance.emit('expand-change', row, expanded)\n      }\n      instance.store.updateTableScrollY()\n    }\n  }\n\n  const loadOrToggle = (row) => {\n    instance.store.assertRowKey()\n    const rowKey = watcherData.rowKey.value\n    const id = getRowIdentity(row, rowKey)\n    const data = treeData.value[id]\n    if (lazy.value && data && 'loaded' in data && !data.loaded) {\n      loadData(row, id, data)\n    } else {\n      toggleTreeExpansion(row, undefined)\n    }\n  }\n\n  const loadData = (row: T, key: string, treeNode) => {\n    const { load } = instance.props as unknown as TableProps<T>\n    if (load && !treeData.value[key].loaded) {\n      treeData.value[key].loading = true\n      load(row, treeNode, (data) => {\n        if (!Array.isArray(data)) {\n          throw new TypeError('[ElTable] data must be an array')\n        }\n        treeData.value[key].loading = false\n        treeData.value[key].loaded = true\n        treeData.value[key].expanded = true\n        if (data.length) {\n          lazyTreeNodeMap.value[key] = data\n        }\n        instance.emit('expand-change', row, true)\n      })\n    }\n  }\n\n  return {\n    loadData,\n    loadOrToggle,\n    toggleTreeExpansion,\n    updateTreeExpandKeys,\n    updateTreeData,\n    normalize,\n    states: {\n      expandRowKeys,\n      treeData,\n      indent,\n      lazy,\n      lazyTreeNodeMap,\n      lazyColumnIdentifier,\n      childrenColumnName,\n    },\n  }\n}\n\nexport default useTree\n"], "mappings": ";;;;;;;AAEA,SAASA,OAAOA,CAACC,WAAW,EAAE;EAC5B,IAAMC,aAAa,GAAGC,GAAG,CAAC,EAAE,CAAC;EAC7B,IAAMC,QAAQ,GAAGD,GAAG,CAAC,EAAE,CAAC;EACxB,IAAME,MAAM,GAAGF,GAAG,CAAC,EAAE,CAAC;EACtB,IAAMG,IAAI,GAAGH,GAAG,CAAC,KAAK,CAAC;EACvB,IAAMI,eAAe,GAAGJ,GAAG,CAAC,EAAE,CAAC;EAC/B,IAAMK,oBAAoB,GAAGL,GAAG,CAAC,aAAa,CAAC;EAC/C,IAAMM,kBAAkB,GAAGN,GAAG,CAAC,UAAU,CAAC;EAC1C,IAAMO,QAAQ,GAAGC,kBAAkB,EAAE;EACrC,IAAMC,cAAc,GAAGC,QAAQ,CAAC,YAAM;IACpC,IAAI,CAACZ,WAAW,CAACa,MAAM,CAACC,KAAK,EAC3B,OAAO,EAAE;IACX,IAAMC,IAAI,GAAGf,WAAW,CAACe,IAAI,CAACD,KAAK,IAAI,EAAE;IACzC,OAAOE,SAAS,CAACD,IAAI,CAAC;EAC1B,CAAG,CAAC;EACF,IAAME,kBAAkB,GAAGL,QAAQ,CAAC,YAAM;IACxC,IAAMC,MAAM,GAAGb,WAAW,CAACa,MAAM,CAACC,KAAK;IACvC,IAAMI,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACZ,eAAe,CAACQ,KAAK,CAAC;IAC/C,IAAMM,GAAG,GAAG,EAAE;IACd,IAAI,CAACF,IAAI,CAACG,MAAM,EACd,OAAOD,GAAG;IACZF,IAAI,CAACI,OAAO,CAAC,UAACC,GAAG,EAAK;MACpB,IAAIjB,eAAe,CAACQ,KAAK,CAACS,GAAG,CAAC,CAACF,MAAM,EAAE;QACrC,IAAMG,IAAI,GAAG;UAAEC,QAAQ,EAAE;QAAE,CAAE;QAC7BnB,eAAe,CAACQ,KAAK,CAACS,GAAG,CAAC,CAACD,OAAO,CAAC,UAACI,GAAG,EAAK;UAC1C,IAAMC,aAAa,GAAGC,cAAc,CAACF,GAAG,EAAEb,MAAM,CAAC;UACjDW,IAAI,CAACC,QAAQ,CAACI,IAAI,CAACF,aAAa,CAAC;UACjC,IAAID,GAAG,CAACnB,oBAAoB,CAACO,KAAK,CAAC,IAAI,CAACM,GAAG,CAACO,aAAa,CAAC,EAAE;YAC1DP,GAAG,CAACO,aAAa,CAAC,GAAG;cAAEF,QAAQ,EAAE;YAAE,CAAE;UACjD;QACA,CAAS,CAAC;QACFL,GAAG,CAACG,GAAG,CAAC,GAAGC,IAAI;MACvB;IACA,CAAK,CAAC;IACF,OAAOJ,GAAG;EACd,CAAG,CAAC;EACF,IAAMJ,SAAS,GAAG,SAAZA,SAASA,CAAID,IAAI,EAAK;IAC1B,IAAMF,MAAM,GAAGb,WAAW,CAACa,MAAM,CAACC,KAAK;IACvC,IAAMM,GAAG,GAAG,EAAE;IACdU,YAAY,CAACf,IAAI,EAAE,UAACgB,MAAM,EAAEN,QAAQ,EAAEO,KAAK,EAAK;MAC9C,IAAMC,QAAQ,GAAGL,cAAc,CAACG,MAAM,EAAElB,MAAM,CAAC;MAC/C,IAAIqB,KAAK,CAACC,OAAO,CAACV,QAAQ,CAAC,EAAE;QAC3BL,GAAG,CAACa,QAAQ,CAAC,GAAG;UACdR,QAAQ,EAAEA,QAAQ,CAACW,GAAG,CAAC,UAACV,GAAG;YAAA,OAAKE,cAAc,CAACF,GAAG,EAAEb,MAAM,CAAC;UAAA,EAAC;UAC5DmB;QACV,CAAS;MACT,CAAO,MAAM,IAAI3B,IAAI,CAACS,KAAK,EAAE;QACrBM,GAAG,CAACa,QAAQ,CAAC,GAAG;UACdR,QAAQ,EAAE,EAAE;UACZpB,IAAI,EAAE,IAAI;UACV2B;QACV,CAAS;MACT;IACA,CAAK,EAAExB,kBAAkB,CAACM,KAAK,EAAEP,oBAAoB,CAACO,KAAK,CAAC;IACxD,OAAOM,GAAG;EACd,CAAG;EACD,IAAMiB,cAAc,GAAG,SAAjBA,cAAcA,CAAA,EAA2I;IAAA,IAAvIC,qBAAqB,GAAAC,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAG,KAAK;IAAA,IAAEE,WAAW,GAAAF,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAI,UAACG,EAAE;MAAA,OAAK,CAACA,EAAE,GAAGjC,QAAQ,CAACkC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,EAAE,CAACE,MAAM,CAACC,gBAAgB,CAAC/B,KAAK;IAAA,GAAG;IACxJ,IAAIgC,GAAG;IACP,IAAMC,MAAM,GAAGpC,cAAc,CAACG,KAAK;IACnC,IAAMkC,mBAAmB,GAAG/B,kBAAkB,CAACH,KAAK;IACpD,IAAMI,IAAI,GAAGC,MAAM,CAACD,IAAI,CAAC6B,MAAM,CAAC;IAChC,IAAME,WAAW,GAAG,EAAE;IACtB,IAAI/B,IAAI,CAACG,MAAM,EAAE;MACf,IAAM6B,WAAW,GAAGC,KAAK,CAAChD,QAAQ,CAAC;MACnC,IAAMiD,eAAe,GAAG,EAAE;MAC1B,IAAMC,WAAW,GAAG,SAAdA,WAAWA,CAAIC,QAAQ,EAAE/B,GAAG,EAAK;QACrC,IAAIe,qBAAqB,EAAE;UACzB,IAAIrC,aAAa,CAACa,KAAK,EAAE;YACvB,OAAO2B,WAAW,IAAIxC,aAAa,CAACa,KAAK,CAACyC,QAAQ,CAAChC,GAAG,CAAC;UACnE,CAAW,MAAM;YACL,OAAO,CAAC,EAAEkB,WAAW,KAAKa,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACE,QAAQ,CAAC,CAAC;UACrF;QACA,CAAS,MAAM;UACL,IAAMC,QAAQ,GAAGhB,WAAW,IAAIxC,aAAa,CAACa,KAAK,IAAIb,aAAa,CAACa,KAAK,CAACyC,QAAQ,CAAChC,GAAG,CAAC;UACxF,OAAO,CAAC,EAAE,CAAC+B,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACE,QAAQ,KAAKC,QAAQ,CAAC;QAChF;MACA,CAAO;MACDvC,IAAI,CAACI,OAAO,CAAC,UAACC,GAAG,EAAK;QACpB,IAAM+B,QAAQ,GAAGJ,WAAW,CAAC3B,GAAG,CAAC;QACjC,IAAMmC,QAAQ,GAAAC,aAAA,KAAQZ,MAAM,CAACxB,GAAG,CAAC,CAAE;QACnCmC,QAAQ,CAACF,QAAQ,GAAGH,WAAW,CAACC,QAAQ,EAAE/B,GAAG,CAAC;QAC9C,IAAImC,QAAQ,CAACrD,IAAI,EAAE;UACjB,IAAAuD,IAAA,GAA4CN,QAAQ,IAAI,EAAE;YAAAO,WAAA,GAAAD,IAAA,CAAlDE,MAAM;YAANA,MAAM,GAAAD,WAAA,cAAG,KAAK,GAAAA,WAAA;YAAAE,YAAA,GAAAH,IAAA,CAAEI,OAAO;YAAPA,OAAO,GAAAD,YAAA,cAAG,KAAK,GAAAA,YAAA;UACvCL,QAAQ,CAACI,MAAM,GAAG,CAAC,CAACA,MAAM;UAC1BJ,QAAQ,CAACM,OAAO,GAAG,CAAC,CAACA,OAAO;UAC5BZ,eAAe,CAACvB,IAAI,CAACN,GAAG,CAAC;QACnC;QACQ0B,WAAW,CAAC1B,GAAG,CAAC,GAAGmC,QAAQ;MACnC,CAAO,CAAC;MACF,IAAMO,QAAQ,GAAG9C,MAAM,CAACD,IAAI,CAAC8B,mBAAmB,CAAC;MACjD,IAAI3C,IAAI,CAACS,KAAK,IAAImD,QAAQ,CAAC5C,MAAM,IAAI+B,eAAe,CAAC/B,MAAM,EAAE;QAC3D4C,QAAQ,CAAC3C,OAAO,CAAC,UAACC,GAAG,EAAK;UACxB,IAAM+B,QAAQ,GAAGJ,WAAW,CAAC3B,GAAG,CAAC;UACjC,IAAM2C,gBAAgB,GAAGlB,mBAAmB,CAACzB,GAAG,CAAC,CAACE,QAAQ;UAC1D,IAAI2B,eAAe,CAACG,QAAQ,CAAChC,GAAG,CAAC,EAAE;YACjC,IAAI0B,WAAW,CAAC1B,GAAG,CAAC,CAACE,QAAQ,CAACJ,MAAM,KAAK,CAAC,EAAE;cAC1C,MAAM,IAAI8C,KAAK,CAAC,2CAA2C,CAAC;YAC1E;YACYlB,WAAW,CAAC1B,GAAG,CAAC,CAACE,QAAQ,GAAGyC,gBAAgB;UACxD,CAAW,MAAM;YACL,IAAAE,KAAA,GAA4Cd,QAAQ,IAAI,EAAE;cAAAe,YAAA,GAAAD,KAAA,CAAlDN,MAAM;cAANA,MAAM,GAAAO,YAAA,cAAG,KAAK,GAAAA,YAAA;cAAAC,aAAA,GAAAF,KAAA,CAAEJ,OAAO;cAAPA,OAAO,GAAAM,aAAA,cAAG,KAAK,GAAAA,aAAA;YACvCrB,WAAW,CAAC1B,GAAG,CAAC,GAAG;cACjBlB,IAAI,EAAE,IAAI;cACVyD,MAAM,EAAE,CAAC,CAACA,MAAM;cAChBE,OAAO,EAAE,CAAC,CAACA,OAAO;cAClBR,QAAQ,EAAEH,WAAW,CAACC,QAAQ,EAAE/B,GAAG,CAAC;cACpCE,QAAQ,EAAEyC,gBAAgB;cAC1BlC,KAAK,EAAE;YACrB,CAAa;UACb;QACA,CAAS,CAAC;MACV;IACA;IACI7B,QAAQ,CAACW,KAAK,GAAGmC,WAAW;IAC5B,CAACH,GAAG,GAAGrC,QAAQ,CAACkC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGG,GAAG,CAACyB,kBAAkB,EAAE;EACtE,CAAG;EACDC,KAAK,CAAC;IAAA,OAAMvE,aAAa,CAACa,KAAK;EAAA,GAAE,YAAM;IACrCuB,cAAc,CAAC,IAAI,CAAC;EACxB,CAAG,CAAC;EACFmC,KAAK,CAAC;IAAA,OAAM7D,cAAc,CAACG,KAAK;EAAA,GAAE,YAAM;IACtCuB,cAAc,EAAE;EACpB,CAAG,CAAC;EACFmC,KAAK,CAAC;IAAA,OAAMvD,kBAAkB,CAACH,KAAK;EAAA,GAAE,YAAM;IAC1CuB,cAAc,EAAE;EACpB,CAAG,CAAC;EACF,IAAMoC,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAI3D,KAAK,EAAK;IACtCb,aAAa,CAACa,KAAK,GAAGA,KAAK;IAC3BuB,cAAc,EAAE;EACpB,CAAG;EACD,IAAMqC,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAIhD,GAAG,EAAE8B,QAAQ,EAAK;IAC7C/C,QAAQ,CAACkC,KAAK,CAACgC,YAAY,EAAE;IAC7B,IAAM9D,MAAM,GAAGb,WAAW,CAACa,MAAM,CAACC,KAAK;IACvC,IAAM8D,EAAE,GAAGhD,cAAc,CAACF,GAAG,EAAEb,MAAM,CAAC;IACtC,IAAME,IAAI,GAAG6D,EAAE,IAAIzE,QAAQ,CAACW,KAAK,CAAC8D,EAAE,CAAC;IACrC,IAAIA,EAAE,IAAI7D,IAAI,IAAI,UAAU,IAAIA,IAAI,EAAE;MACpC,IAAM8D,WAAW,GAAG9D,IAAI,CAACyC,QAAQ;MACjCA,QAAQ,GAAG,OAAOA,QAAQ,KAAK,WAAW,GAAG,CAACzC,IAAI,CAACyC,QAAQ,GAAGA,QAAQ;MACtErD,QAAQ,CAACW,KAAK,CAAC8D,EAAE,CAAC,CAACpB,QAAQ,GAAGA,QAAQ;MACtC,IAAIqB,WAAW,KAAKrB,QAAQ,EAAE;QAC5B/C,QAAQ,CAACqE,IAAI,CAAC,eAAe,EAAEpD,GAAG,EAAE8B,QAAQ,CAAC;MACrD;MACM/C,QAAQ,CAACkC,KAAK,CAAC4B,kBAAkB,EAAE;IACzC;EACA,CAAG;EACD,IAAMQ,YAAY,GAAG,SAAfA,YAAYA,CAAIrD,GAAG,EAAK;IAC5BjB,QAAQ,CAACkC,KAAK,CAACgC,YAAY,EAAE;IAC7B,IAAM9D,MAAM,GAAGb,WAAW,CAACa,MAAM,CAACC,KAAK;IACvC,IAAM8D,EAAE,GAAGhD,cAAc,CAACF,GAAG,EAAEb,MAAM,CAAC;IACtC,IAAME,IAAI,GAAGZ,QAAQ,CAACW,KAAK,CAAC8D,EAAE,CAAC;IAC/B,IAAIvE,IAAI,CAACS,KAAK,IAAIC,IAAI,IAAI,QAAQ,IAAIA,IAAI,IAAI,CAACA,IAAI,CAAC+C,MAAM,EAAE;MAC1DkB,QAAQ,CAACtD,GAAG,EAAEkD,EAAE,EAAE7D,IAAI,CAAC;IAC7B,CAAK,MAAM;MACL2D,mBAAmB,CAAChD,GAAG,EAAE,KAAK,CAAC,CAAC;IACtC;EACA,CAAG;EACD,IAAMsD,QAAQ,GAAG,SAAXA,QAAQA,CAAItD,GAAG,EAAEH,GAAG,EAAE0D,QAAQ,EAAK;IACvC,IAAQC,IAAI,GAAKzE,QAAQ,CAAC0E,KAAK,CAAvBD,IAAI;IACZ,IAAIA,IAAI,IAAI,CAAC/E,QAAQ,CAACW,KAAK,CAACS,GAAG,CAAC,CAACuC,MAAM,EAAE;MACvC3D,QAAQ,CAACW,KAAK,CAACS,GAAG,CAAC,CAACyC,OAAO,GAAG,IAAI;MAClCkB,IAAI,CAACxD,GAAG,EAAEuD,QAAQ,EAAE,UAAClE,IAAI,EAAK;QAC5B,IAAI,CAACmB,KAAK,CAACC,OAAO,CAACpB,IAAI,CAAC,EAAE;UACxB,MAAM,IAAIqE,SAAS,CAAC,iCAAiC,CAAC;QAChE;QACQjF,QAAQ,CAACW,KAAK,CAACS,GAAG,CAAC,CAACyC,OAAO,GAAG,KAAK;QACnC7D,QAAQ,CAACW,KAAK,CAACS,GAAG,CAAC,CAACuC,MAAM,GAAG,IAAI;QACjC3D,QAAQ,CAACW,KAAK,CAACS,GAAG,CAAC,CAACiC,QAAQ,GAAG,IAAI;QACnC,IAAIzC,IAAI,CAACM,MAAM,EAAE;UACff,eAAe,CAACQ,KAAK,CAACS,GAAG,CAAC,GAAGR,IAAI;QAC3C;QACQN,QAAQ,CAACqE,IAAI,CAAC,eAAe,EAAEpD,GAAG,EAAE,IAAI,CAAC;MACjD,CAAO,CAAC;IACR;EACA,CAAG;EACD,OAAO;IACLsD,QAAQ;IACRD,YAAY;IACZL,mBAAmB;IACnBD,oBAAoB;IACpBpC,cAAc;IACdrB,SAAS;IACT4B,MAAM,EAAE;MACN3C,aAAa;MACbE,QAAQ;MACRC,MAAM;MACNC,IAAI;MACJC,eAAe;MACfC,oBAAoB;MACpBC;IACN;EACA,CAAG;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}