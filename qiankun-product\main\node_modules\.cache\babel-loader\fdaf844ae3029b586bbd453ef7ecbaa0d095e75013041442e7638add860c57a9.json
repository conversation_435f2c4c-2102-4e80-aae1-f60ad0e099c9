{"ast": null, "code": "/**!\n * Sortable 1.15.3\n * <AUTHOR>   <<EMAIL>>\n * <AUTHOR>    <<EMAIL>>\n * @license MIT\n */\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function _typeof(obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n  return _typeof(obj);\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nvar version = \"1.15.3\";\nfunction userAgent(pattern) {\n  if (typeof window !== 'undefined' && window.navigator) {\n    return !! /*@__PURE__*/navigator.userAgent.match(pattern);\n  }\n}\nvar IE11OrLess = userAgent(/(?:Trident.*rv[ :]?11\\.|msie|iemobile|Windows Phone)/i);\nvar Edge = userAgent(/Edge/i);\nvar FireFox = userAgent(/firefox/i);\nvar Safari = userAgent(/safari/i) && !userAgent(/chrome/i) && !userAgent(/android/i);\nvar IOS = userAgent(/iP(ad|od|hone)/i);\nvar ChromeForAndroid = userAgent(/chrome/i) && userAgent(/android/i);\nvar captureMode = {\n  capture: false,\n  passive: false\n};\nfunction on(el, event, fn) {\n  el.addEventListener(event, fn, !IE11OrLess && captureMode);\n}\nfunction off(el, event, fn) {\n  el.removeEventListener(event, fn, !IE11OrLess && captureMode);\n}\nfunction matches(/**HTMLElement*/el, /**String*/selector) {\n  if (!selector) return;\n  selector[0] === '>' && (selector = selector.substring(1));\n  if (el) {\n    try {\n      if (el.matches) {\n        return el.matches(selector);\n      } else if (el.msMatchesSelector) {\n        return el.msMatchesSelector(selector);\n      } else if (el.webkitMatchesSelector) {\n        return el.webkitMatchesSelector(selector);\n      }\n    } catch (_) {\n      return false;\n    }\n  }\n  return false;\n}\nfunction getParentOrHost(el) {\n  return el.host && el !== document && el.host.nodeType ? el.host : el.parentNode;\n}\nfunction closest(/**HTMLElement*/el, /**String*/selector, /**HTMLElement*/ctx, includeCTX) {\n  if (el) {\n    ctx = ctx || document;\n    do {\n      if (selector != null && (selector[0] === '>' ? el.parentNode === ctx && matches(el, selector) : matches(el, selector)) || includeCTX && el === ctx) {\n        return el;\n      }\n      if (el === ctx) break;\n      /* jshint boss:true */\n    } while (el = getParentOrHost(el));\n  }\n  return null;\n}\nvar R_SPACE = /\\s+/g;\nfunction toggleClass(el, name, state) {\n  if (el && name) {\n    if (el.classList) {\n      el.classList[state ? 'add' : 'remove'](name);\n    } else {\n      var className = (' ' + el.className + ' ').replace(R_SPACE, ' ').replace(' ' + name + ' ', ' ');\n      el.className = (className + (state ? ' ' + name : '')).replace(R_SPACE, ' ');\n    }\n  }\n}\nfunction css(el, prop, val) {\n  var style = el && el.style;\n  if (style) {\n    if (val === void 0) {\n      if (document.defaultView && document.defaultView.getComputedStyle) {\n        val = document.defaultView.getComputedStyle(el, '');\n      } else if (el.currentStyle) {\n        val = el.currentStyle;\n      }\n      return prop === void 0 ? val : val[prop];\n    } else {\n      if (!(prop in style) && prop.indexOf('webkit') === -1) {\n        prop = '-webkit-' + prop;\n      }\n      style[prop] = val + (typeof val === 'string' ? '' : 'px');\n    }\n  }\n}\nfunction matrix(el, selfOnly) {\n  var appliedTransforms = '';\n  if (typeof el === 'string') {\n    appliedTransforms = el;\n  } else {\n    do {\n      var transform = css(el, 'transform');\n      if (transform && transform !== 'none') {\n        appliedTransforms = transform + ' ' + appliedTransforms;\n      }\n      /* jshint boss:true */\n    } while (!selfOnly && (el = el.parentNode));\n  }\n  var matrixFn = window.DOMMatrix || window.WebKitCSSMatrix || window.CSSMatrix || window.MSCSSMatrix;\n  /*jshint -W056 */\n  return matrixFn && new matrixFn(appliedTransforms);\n}\nfunction find(ctx, tagName, iterator) {\n  if (ctx) {\n    var list = ctx.getElementsByTagName(tagName),\n      i = 0,\n      n = list.length;\n    if (iterator) {\n      for (; i < n; i++) {\n        iterator(list[i], i);\n      }\n    }\n    return list;\n  }\n  return [];\n}\nfunction getWindowScrollingElement() {\n  var scrollingElement = document.scrollingElement;\n  if (scrollingElement) {\n    return scrollingElement;\n  } else {\n    return document.documentElement;\n  }\n}\n\n/**\r\n * Returns the \"bounding client rect\" of given element\r\n * @param  {HTMLElement} el                       The element whose boundingClientRect is wanted\r\n * @param  {[Boolean]} relativeToContainingBlock  Whether the rect should be relative to the containing block of (including) the container\r\n * @param  {[Boolean]} relativeToNonStaticParent  Whether the rect should be relative to the relative parent of (including) the contaienr\r\n * @param  {[Boolean]} undoScale                  Whether the container's scale() should be undone\r\n * @param  {[HTMLElement]} container              The parent the element will be placed in\r\n * @return {Object}                               The boundingClientRect of el, with specified adjustments\r\n */\nfunction getRect(el, relativeToContainingBlock, relativeToNonStaticParent, undoScale, container) {\n  if (!el.getBoundingClientRect && el !== window) return;\n  var elRect, top, left, bottom, right, height, width;\n  if (el !== window && el.parentNode && el !== getWindowScrollingElement()) {\n    elRect = el.getBoundingClientRect();\n    top = elRect.top;\n    left = elRect.left;\n    bottom = elRect.bottom;\n    right = elRect.right;\n    height = elRect.height;\n    width = elRect.width;\n  } else {\n    top = 0;\n    left = 0;\n    bottom = window.innerHeight;\n    right = window.innerWidth;\n    height = window.innerHeight;\n    width = window.innerWidth;\n  }\n  if ((relativeToContainingBlock || relativeToNonStaticParent) && el !== window) {\n    // Adjust for translate()\n    container = container || el.parentNode;\n\n    // solves #1123 (see: https://stackoverflow.com/a/37953806/6088312)\n    // Not needed on <= IE11\n    if (!IE11OrLess) {\n      do {\n        if (container && container.getBoundingClientRect && (css(container, 'transform') !== 'none' || relativeToNonStaticParent && css(container, 'position') !== 'static')) {\n          var containerRect = container.getBoundingClientRect();\n\n          // Set relative to edges of padding box of container\n          top -= containerRect.top + parseInt(css(container, 'border-top-width'));\n          left -= containerRect.left + parseInt(css(container, 'border-left-width'));\n          bottom = top + elRect.height;\n          right = left + elRect.width;\n          break;\n        }\n        /* jshint boss:true */\n      } while (container = container.parentNode);\n    }\n  }\n  if (undoScale && el !== window) {\n    // Adjust for scale()\n    var elMatrix = matrix(container || el),\n      scaleX = elMatrix && elMatrix.a,\n      scaleY = elMatrix && elMatrix.d;\n    if (elMatrix) {\n      top /= scaleY;\n      left /= scaleX;\n      width /= scaleX;\n      height /= scaleY;\n      bottom = top + height;\n      right = left + width;\n    }\n  }\n  return {\n    top: top,\n    left: left,\n    bottom: bottom,\n    right: right,\n    width: width,\n    height: height\n  };\n}\n\n/**\r\n * Checks if a side of an element is scrolled past a side of its parents\r\n * @param  {HTMLElement}  el           The element who's side being scrolled out of view is in question\r\n * @param  {String}       elSide       Side of the element in question ('top', 'left', 'right', 'bottom')\r\n * @param  {String}       parentSide   Side of the parent in question ('top', 'left', 'right', 'bottom')\r\n * @return {HTMLElement}               The parent scroll element that the el's side is scrolled past, or null if there is no such element\r\n */\nfunction isScrolledPast(el, elSide, parentSide) {\n  var parent = getParentAutoScrollElement(el, true),\n    elSideVal = getRect(el)[elSide];\n\n  /* jshint boss:true */\n  while (parent) {\n    var parentSideVal = getRect(parent)[parentSide],\n      visible = void 0;\n    if (parentSide === 'top' || parentSide === 'left') {\n      visible = elSideVal >= parentSideVal;\n    } else {\n      visible = elSideVal <= parentSideVal;\n    }\n    if (!visible) return parent;\n    if (parent === getWindowScrollingElement()) break;\n    parent = getParentAutoScrollElement(parent, false);\n  }\n  return false;\n}\n\n/**\r\n * Gets nth child of el, ignoring hidden children, sortable's elements (does not ignore clone if it's visible)\r\n * and non-draggable elements\r\n * @param  {HTMLElement} el       The parent element\r\n * @param  {Number} childNum      The index of the child\r\n * @param  {Object} options       Parent Sortable's options\r\n * @return {HTMLElement}          The child at index childNum, or null if not found\r\n */\nfunction getChild(el, childNum, options, includeDragEl) {\n  var currentChild = 0,\n    i = 0,\n    children = el.children;\n  while (i < children.length) {\n    if (children[i].style.display !== 'none' && children[i] !== Sortable.ghost && (includeDragEl || children[i] !== Sortable.dragged) && closest(children[i], options.draggable, el, false)) {\n      if (currentChild === childNum) {\n        return children[i];\n      }\n      currentChild++;\n    }\n    i++;\n  }\n  return null;\n}\n\n/**\r\n * Gets the last child in the el, ignoring ghostEl or invisible elements (clones)\r\n * @param  {HTMLElement} el       Parent element\r\n * @param  {selector} selector    Any other elements that should be ignored\r\n * @return {HTMLElement}          The last child, ignoring ghostEl\r\n */\nfunction lastChild(el, selector) {\n  var last = el.lastElementChild;\n  while (last && (last === Sortable.ghost || css(last, 'display') === 'none' || selector && !matches(last, selector))) {\n    last = last.previousElementSibling;\n  }\n  return last || null;\n}\n\n/**\r\n * Returns the index of an element within its parent for a selected set of\r\n * elements\r\n * @param  {HTMLElement} el\r\n * @param  {selector} selector\r\n * @return {number}\r\n */\nfunction index(el, selector) {\n  var index = 0;\n  if (!el || !el.parentNode) {\n    return -1;\n  }\n\n  /* jshint boss:true */\n  while (el = el.previousElementSibling) {\n    if (el.nodeName.toUpperCase() !== 'TEMPLATE' && el !== Sortable.clone && (!selector || matches(el, selector))) {\n      index++;\n    }\n  }\n  return index;\n}\n\n/**\r\n * Returns the scroll offset of the given element, added with all the scroll offsets of parent elements.\r\n * The value is returned in real pixels.\r\n * @param  {HTMLElement} el\r\n * @return {Array}             Offsets in the format of [left, top]\r\n */\nfunction getRelativeScrollOffset(el) {\n  var offsetLeft = 0,\n    offsetTop = 0,\n    winScroller = getWindowScrollingElement();\n  if (el) {\n    do {\n      var elMatrix = matrix(el),\n        scaleX = elMatrix.a,\n        scaleY = elMatrix.d;\n      offsetLeft += el.scrollLeft * scaleX;\n      offsetTop += el.scrollTop * scaleY;\n    } while (el !== winScroller && (el = el.parentNode));\n  }\n  return [offsetLeft, offsetTop];\n}\n\n/**\r\n * Returns the index of the object within the given array\r\n * @param  {Array} arr   Array that may or may not hold the object\r\n * @param  {Object} obj  An object that has a key-value pair unique to and identical to a key-value pair in the object you want to find\r\n * @return {Number}      The index of the object in the array, or -1\r\n */\nfunction indexOfObject(arr, obj) {\n  for (var i in arr) {\n    if (!arr.hasOwnProperty(i)) continue;\n    for (var key in obj) {\n      if (obj.hasOwnProperty(key) && obj[key] === arr[i][key]) return Number(i);\n    }\n  }\n  return -1;\n}\nfunction getParentAutoScrollElement(el, includeSelf) {\n  // skip to window\n  if (!el || !el.getBoundingClientRect) return getWindowScrollingElement();\n  var elem = el;\n  var gotSelf = false;\n  do {\n    // we don't need to get elem css if it isn't even overflowing in the first place (performance)\n    if (elem.clientWidth < elem.scrollWidth || elem.clientHeight < elem.scrollHeight) {\n      var elemCSS = css(elem);\n      if (elem.clientWidth < elem.scrollWidth && (elemCSS.overflowX == 'auto' || elemCSS.overflowX == 'scroll') || elem.clientHeight < elem.scrollHeight && (elemCSS.overflowY == 'auto' || elemCSS.overflowY == 'scroll')) {\n        if (!elem.getBoundingClientRect || elem === document.body) return getWindowScrollingElement();\n        if (gotSelf || includeSelf) return elem;\n        gotSelf = true;\n      }\n    }\n    /* jshint boss:true */\n  } while (elem = elem.parentNode);\n  return getWindowScrollingElement();\n}\nfunction extend(dst, src) {\n  if (dst && src) {\n    for (var key in src) {\n      if (src.hasOwnProperty(key)) {\n        dst[key] = src[key];\n      }\n    }\n  }\n  return dst;\n}\nfunction isRectEqual(rect1, rect2) {\n  return Math.round(rect1.top) === Math.round(rect2.top) && Math.round(rect1.left) === Math.round(rect2.left) && Math.round(rect1.height) === Math.round(rect2.height) && Math.round(rect1.width) === Math.round(rect2.width);\n}\nvar _throttleTimeout;\nfunction throttle(callback, ms) {\n  return function () {\n    if (!_throttleTimeout) {\n      var args = arguments,\n        _this = this;\n      if (args.length === 1) {\n        callback.call(_this, args[0]);\n      } else {\n        callback.apply(_this, args);\n      }\n      _throttleTimeout = setTimeout(function () {\n        _throttleTimeout = void 0;\n      }, ms);\n    }\n  };\n}\nfunction cancelThrottle() {\n  clearTimeout(_throttleTimeout);\n  _throttleTimeout = void 0;\n}\nfunction scrollBy(el, x, y) {\n  el.scrollLeft += x;\n  el.scrollTop += y;\n}\nfunction clone(el) {\n  var Polymer = window.Polymer;\n  var $ = window.jQuery || window.Zepto;\n  if (Polymer && Polymer.dom) {\n    return Polymer.dom(el).cloneNode(true);\n  } else if ($) {\n    return $(el).clone(true)[0];\n  } else {\n    return el.cloneNode(true);\n  }\n}\nfunction setRect(el, rect) {\n  css(el, 'position', 'absolute');\n  css(el, 'top', rect.top);\n  css(el, 'left', rect.left);\n  css(el, 'width', rect.width);\n  css(el, 'height', rect.height);\n}\nfunction unsetRect(el) {\n  css(el, 'position', '');\n  css(el, 'top', '');\n  css(el, 'left', '');\n  css(el, 'width', '');\n  css(el, 'height', '');\n}\nfunction getChildContainingRectFromElement(container, options, ghostEl) {\n  var rect = {};\n  Array.from(container.children).forEach(function (child) {\n    var _rect$left, _rect$top, _rect$right, _rect$bottom;\n    if (!closest(child, options.draggable, container, false) || child.animated || child === ghostEl) return;\n    var childRect = getRect(child);\n    rect.left = Math.min((_rect$left = rect.left) !== null && _rect$left !== void 0 ? _rect$left : Infinity, childRect.left);\n    rect.top = Math.min((_rect$top = rect.top) !== null && _rect$top !== void 0 ? _rect$top : Infinity, childRect.top);\n    rect.right = Math.max((_rect$right = rect.right) !== null && _rect$right !== void 0 ? _rect$right : -Infinity, childRect.right);\n    rect.bottom = Math.max((_rect$bottom = rect.bottom) !== null && _rect$bottom !== void 0 ? _rect$bottom : -Infinity, childRect.bottom);\n  });\n  rect.width = rect.right - rect.left;\n  rect.height = rect.bottom - rect.top;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\nvar expando = 'Sortable' + new Date().getTime();\nfunction AnimationStateManager() {\n  var animationStates = [],\n    animationCallbackId;\n  return {\n    captureAnimationState: function captureAnimationState() {\n      animationStates = [];\n      if (!this.options.animation) return;\n      var children = [].slice.call(this.el.children);\n      children.forEach(function (child) {\n        if (css(child, 'display') === 'none' || child === Sortable.ghost) return;\n        animationStates.push({\n          target: child,\n          rect: getRect(child)\n        });\n        var fromRect = _objectSpread2({}, animationStates[animationStates.length - 1].rect);\n\n        // If animating: compensate for current animation\n        if (child.thisAnimationDuration) {\n          var childMatrix = matrix(child, true);\n          if (childMatrix) {\n            fromRect.top -= childMatrix.f;\n            fromRect.left -= childMatrix.e;\n          }\n        }\n        child.fromRect = fromRect;\n      });\n    },\n    addAnimationState: function addAnimationState(state) {\n      animationStates.push(state);\n    },\n    removeAnimationState: function removeAnimationState(target) {\n      animationStates.splice(indexOfObject(animationStates, {\n        target: target\n      }), 1);\n    },\n    animateAll: function animateAll(callback) {\n      var _this = this;\n      if (!this.options.animation) {\n        clearTimeout(animationCallbackId);\n        if (typeof callback === 'function') callback();\n        return;\n      }\n      var animating = false,\n        animationTime = 0;\n      animationStates.forEach(function (state) {\n        var time = 0,\n          target = state.target,\n          fromRect = target.fromRect,\n          toRect = getRect(target),\n          prevFromRect = target.prevFromRect,\n          prevToRect = target.prevToRect,\n          animatingRect = state.rect,\n          targetMatrix = matrix(target, true);\n        if (targetMatrix) {\n          // Compensate for current animation\n          toRect.top -= targetMatrix.f;\n          toRect.left -= targetMatrix.e;\n        }\n        target.toRect = toRect;\n        if (target.thisAnimationDuration) {\n          // Could also check if animatingRect is between fromRect and toRect\n          if (isRectEqual(prevFromRect, toRect) && !isRectEqual(fromRect, toRect) &&\n          // Make sure animatingRect is on line between toRect & fromRect\n          (animatingRect.top - toRect.top) / (animatingRect.left - toRect.left) === (fromRect.top - toRect.top) / (fromRect.left - toRect.left)) {\n            // If returning to same place as started from animation and on same axis\n            time = calculateRealTime(animatingRect, prevFromRect, prevToRect, _this.options);\n          }\n        }\n\n        // if fromRect != toRect: animate\n        if (!isRectEqual(toRect, fromRect)) {\n          target.prevFromRect = fromRect;\n          target.prevToRect = toRect;\n          if (!time) {\n            time = _this.options.animation;\n          }\n          _this.animate(target, animatingRect, toRect, time);\n        }\n        if (time) {\n          animating = true;\n          animationTime = Math.max(animationTime, time);\n          clearTimeout(target.animationResetTimer);\n          target.animationResetTimer = setTimeout(function () {\n            target.animationTime = 0;\n            target.prevFromRect = null;\n            target.fromRect = null;\n            target.prevToRect = null;\n            target.thisAnimationDuration = null;\n          }, time);\n          target.thisAnimationDuration = time;\n        }\n      });\n      clearTimeout(animationCallbackId);\n      if (!animating) {\n        if (typeof callback === 'function') callback();\n      } else {\n        animationCallbackId = setTimeout(function () {\n          if (typeof callback === 'function') callback();\n        }, animationTime);\n      }\n      animationStates = [];\n    },\n    animate: function animate(target, currentRect, toRect, duration) {\n      if (duration) {\n        css(target, 'transition', '');\n        css(target, 'transform', '');\n        var elMatrix = matrix(this.el),\n          scaleX = elMatrix && elMatrix.a,\n          scaleY = elMatrix && elMatrix.d,\n          translateX = (currentRect.left - toRect.left) / (scaleX || 1),\n          translateY = (currentRect.top - toRect.top) / (scaleY || 1);\n        target.animatingX = !!translateX;\n        target.animatingY = !!translateY;\n        css(target, 'transform', 'translate3d(' + translateX + 'px,' + translateY + 'px,0)');\n        this.forRepaintDummy = repaint(target); // repaint\n\n        css(target, 'transition', 'transform ' + duration + 'ms' + (this.options.easing ? ' ' + this.options.easing : ''));\n        css(target, 'transform', 'translate3d(0,0,0)');\n        typeof target.animated === 'number' && clearTimeout(target.animated);\n        target.animated = setTimeout(function () {\n          css(target, 'transition', '');\n          css(target, 'transform', '');\n          target.animated = false;\n          target.animatingX = false;\n          target.animatingY = false;\n        }, duration);\n      }\n    }\n  };\n}\nfunction repaint(target) {\n  return target.offsetWidth;\n}\nfunction calculateRealTime(animatingRect, fromRect, toRect, options) {\n  return Math.sqrt(Math.pow(fromRect.top - animatingRect.top, 2) + Math.pow(fromRect.left - animatingRect.left, 2)) / Math.sqrt(Math.pow(fromRect.top - toRect.top, 2) + Math.pow(fromRect.left - toRect.left, 2)) * options.animation;\n}\nvar plugins = [];\nvar defaults = {\n  initializeByDefault: true\n};\nvar PluginManager = {\n  mount: function mount(plugin) {\n    // Set default static properties\n    for (var option in defaults) {\n      if (defaults.hasOwnProperty(option) && !(option in plugin)) {\n        plugin[option] = defaults[option];\n      }\n    }\n    plugins.forEach(function (p) {\n      if (p.pluginName === plugin.pluginName) {\n        throw \"Sortable: Cannot mount plugin \".concat(plugin.pluginName, \" more than once\");\n      }\n    });\n    plugins.push(plugin);\n  },\n  pluginEvent: function pluginEvent(eventName, sortable, evt) {\n    var _this = this;\n    this.eventCanceled = false;\n    evt.cancel = function () {\n      _this.eventCanceled = true;\n    };\n    var eventNameGlobal = eventName + 'Global';\n    plugins.forEach(function (plugin) {\n      if (!sortable[plugin.pluginName]) return;\n      // Fire global events if it exists in this sortable\n      if (sortable[plugin.pluginName][eventNameGlobal]) {\n        sortable[plugin.pluginName][eventNameGlobal](_objectSpread2({\n          sortable: sortable\n        }, evt));\n      }\n\n      // Only fire plugin event if plugin is enabled in this sortable,\n      // and plugin has event defined\n      if (sortable.options[plugin.pluginName] && sortable[plugin.pluginName][eventName]) {\n        sortable[plugin.pluginName][eventName](_objectSpread2({\n          sortable: sortable\n        }, evt));\n      }\n    });\n  },\n  initializePlugins: function initializePlugins(sortable, el, defaults, options) {\n    plugins.forEach(function (plugin) {\n      var pluginName = plugin.pluginName;\n      if (!sortable.options[pluginName] && !plugin.initializeByDefault) return;\n      var initialized = new plugin(sortable, el, sortable.options);\n      initialized.sortable = sortable;\n      initialized.options = sortable.options;\n      sortable[pluginName] = initialized;\n\n      // Add default options from plugin\n      _extends(defaults, initialized.defaults);\n    });\n    for (var option in sortable.options) {\n      if (!sortable.options.hasOwnProperty(option)) continue;\n      var modified = this.modifyOption(sortable, option, sortable.options[option]);\n      if (typeof modified !== 'undefined') {\n        sortable.options[option] = modified;\n      }\n    }\n  },\n  getEventProperties: function getEventProperties(name, sortable) {\n    var eventProperties = {};\n    plugins.forEach(function (plugin) {\n      if (typeof plugin.eventProperties !== 'function') return;\n      _extends(eventProperties, plugin.eventProperties.call(sortable[plugin.pluginName], name));\n    });\n    return eventProperties;\n  },\n  modifyOption: function modifyOption(sortable, name, value) {\n    var modifiedValue;\n    plugins.forEach(function (plugin) {\n      // Plugin must exist on the Sortable\n      if (!sortable[plugin.pluginName]) return;\n\n      // If static option listener exists for this option, call in the context of the Sortable's instance of this plugin\n      if (plugin.optionListeners && typeof plugin.optionListeners[name] === 'function') {\n        modifiedValue = plugin.optionListeners[name].call(sortable[plugin.pluginName], value);\n      }\n    });\n    return modifiedValue;\n  }\n};\nfunction dispatchEvent(_ref) {\n  var sortable = _ref.sortable,\n    rootEl = _ref.rootEl,\n    name = _ref.name,\n    targetEl = _ref.targetEl,\n    cloneEl = _ref.cloneEl,\n    toEl = _ref.toEl,\n    fromEl = _ref.fromEl,\n    oldIndex = _ref.oldIndex,\n    newIndex = _ref.newIndex,\n    oldDraggableIndex = _ref.oldDraggableIndex,\n    newDraggableIndex = _ref.newDraggableIndex,\n    originalEvent = _ref.originalEvent,\n    putSortable = _ref.putSortable,\n    extraEventProperties = _ref.extraEventProperties;\n  sortable = sortable || rootEl && rootEl[expando];\n  if (!sortable) return;\n  var evt,\n    options = sortable.options,\n    onName = 'on' + name.charAt(0).toUpperCase() + name.substr(1);\n  // Support for new CustomEvent feature\n  if (window.CustomEvent && !IE11OrLess && !Edge) {\n    evt = new CustomEvent(name, {\n      bubbles: true,\n      cancelable: true\n    });\n  } else {\n    evt = document.createEvent('Event');\n    evt.initEvent(name, true, true);\n  }\n  evt.to = toEl || rootEl;\n  evt.from = fromEl || rootEl;\n  evt.item = targetEl || rootEl;\n  evt.clone = cloneEl;\n  evt.oldIndex = oldIndex;\n  evt.newIndex = newIndex;\n  evt.oldDraggableIndex = oldDraggableIndex;\n  evt.newDraggableIndex = newDraggableIndex;\n  evt.originalEvent = originalEvent;\n  evt.pullMode = putSortable ? putSortable.lastPutMode : undefined;\n  var allEventProperties = _objectSpread2(_objectSpread2({}, extraEventProperties), PluginManager.getEventProperties(name, sortable));\n  for (var option in allEventProperties) {\n    evt[option] = allEventProperties[option];\n  }\n  if (rootEl) {\n    rootEl.dispatchEvent(evt);\n  }\n  if (options[onName]) {\n    options[onName].call(sortable, evt);\n  }\n}\nvar _excluded = [\"evt\"];\nvar pluginEvent = function pluginEvent(eventName, sortable) {\n  var _ref = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {},\n    originalEvent = _ref.evt,\n    data = _objectWithoutProperties(_ref, _excluded);\n  PluginManager.pluginEvent.bind(Sortable)(eventName, sortable, _objectSpread2({\n    dragEl: dragEl,\n    parentEl: parentEl,\n    ghostEl: ghostEl,\n    rootEl: rootEl,\n    nextEl: nextEl,\n    lastDownEl: lastDownEl,\n    cloneEl: cloneEl,\n    cloneHidden: cloneHidden,\n    dragStarted: moved,\n    putSortable: putSortable,\n    activeSortable: Sortable.active,\n    originalEvent: originalEvent,\n    oldIndex: oldIndex,\n    oldDraggableIndex: oldDraggableIndex,\n    newIndex: newIndex,\n    newDraggableIndex: newDraggableIndex,\n    hideGhostForTarget: _hideGhostForTarget,\n    unhideGhostForTarget: _unhideGhostForTarget,\n    cloneNowHidden: function cloneNowHidden() {\n      cloneHidden = true;\n    },\n    cloneNowShown: function cloneNowShown() {\n      cloneHidden = false;\n    },\n    dispatchSortableEvent: function dispatchSortableEvent(name) {\n      _dispatchEvent({\n        sortable: sortable,\n        name: name,\n        originalEvent: originalEvent\n      });\n    }\n  }, data));\n};\nfunction _dispatchEvent(info) {\n  dispatchEvent(_objectSpread2({\n    putSortable: putSortable,\n    cloneEl: cloneEl,\n    targetEl: dragEl,\n    rootEl: rootEl,\n    oldIndex: oldIndex,\n    oldDraggableIndex: oldDraggableIndex,\n    newIndex: newIndex,\n    newDraggableIndex: newDraggableIndex\n  }, info));\n}\nvar dragEl,\n  parentEl,\n  ghostEl,\n  rootEl,\n  nextEl,\n  lastDownEl,\n  cloneEl,\n  cloneHidden,\n  oldIndex,\n  newIndex,\n  oldDraggableIndex,\n  newDraggableIndex,\n  activeGroup,\n  putSortable,\n  awaitingDragStarted = false,\n  ignoreNextClick = false,\n  sortables = [],\n  tapEvt,\n  touchEvt,\n  lastDx,\n  lastDy,\n  tapDistanceLeft,\n  tapDistanceTop,\n  moved,\n  lastTarget,\n  lastDirection,\n  pastFirstInvertThresh = false,\n  isCircumstantialInvert = false,\n  targetMoveDistance,\n  // For positioning ghost absolutely\n  ghostRelativeParent,\n  ghostRelativeParentInitialScroll = [],\n  // (left, top)\n\n  _silent = false,\n  savedInputChecked = [];\n\n/** @const */\nvar documentExists = typeof document !== 'undefined',\n  PositionGhostAbsolutely = IOS,\n  CSSFloatProperty = Edge || IE11OrLess ? 'cssFloat' : 'float',\n  // This will not pass for IE9, because IE9 DnD only works on anchors\n  supportDraggable = documentExists && !ChromeForAndroid && !IOS && 'draggable' in document.createElement('div'),\n  supportCssPointerEvents = function () {\n    if (!documentExists) return;\n    // false when <= IE11\n    if (IE11OrLess) {\n      return false;\n    }\n    var el = document.createElement('x');\n    el.style.cssText = 'pointer-events:auto';\n    return el.style.pointerEvents === 'auto';\n  }(),\n  _detectDirection = function _detectDirection(el, options) {\n    var elCSS = css(el),\n      elWidth = parseInt(elCSS.width) - parseInt(elCSS.paddingLeft) - parseInt(elCSS.paddingRight) - parseInt(elCSS.borderLeftWidth) - parseInt(elCSS.borderRightWidth),\n      child1 = getChild(el, 0, options),\n      child2 = getChild(el, 1, options),\n      firstChildCSS = child1 && css(child1),\n      secondChildCSS = child2 && css(child2),\n      firstChildWidth = firstChildCSS && parseInt(firstChildCSS.marginLeft) + parseInt(firstChildCSS.marginRight) + getRect(child1).width,\n      secondChildWidth = secondChildCSS && parseInt(secondChildCSS.marginLeft) + parseInt(secondChildCSS.marginRight) + getRect(child2).width;\n    if (elCSS.display === 'flex') {\n      return elCSS.flexDirection === 'column' || elCSS.flexDirection === 'column-reverse' ? 'vertical' : 'horizontal';\n    }\n    if (elCSS.display === 'grid') {\n      return elCSS.gridTemplateColumns.split(' ').length <= 1 ? 'vertical' : 'horizontal';\n    }\n    if (child1 && firstChildCSS[\"float\"] && firstChildCSS[\"float\"] !== 'none') {\n      var touchingSideChild2 = firstChildCSS[\"float\"] === 'left' ? 'left' : 'right';\n      return child2 && (secondChildCSS.clear === 'both' || secondChildCSS.clear === touchingSideChild2) ? 'vertical' : 'horizontal';\n    }\n    return child1 && (firstChildCSS.display === 'block' || firstChildCSS.display === 'flex' || firstChildCSS.display === 'table' || firstChildCSS.display === 'grid' || firstChildWidth >= elWidth && elCSS[CSSFloatProperty] === 'none' || child2 && elCSS[CSSFloatProperty] === 'none' && firstChildWidth + secondChildWidth > elWidth) ? 'vertical' : 'horizontal';\n  },\n  _dragElInRowColumn = function _dragElInRowColumn(dragRect, targetRect, vertical) {\n    var dragElS1Opp = vertical ? dragRect.left : dragRect.top,\n      dragElS2Opp = vertical ? dragRect.right : dragRect.bottom,\n      dragElOppLength = vertical ? dragRect.width : dragRect.height,\n      targetS1Opp = vertical ? targetRect.left : targetRect.top,\n      targetS2Opp = vertical ? targetRect.right : targetRect.bottom,\n      targetOppLength = vertical ? targetRect.width : targetRect.height;\n    return dragElS1Opp === targetS1Opp || dragElS2Opp === targetS2Opp || dragElS1Opp + dragElOppLength / 2 === targetS1Opp + targetOppLength / 2;\n  },\n  /**\r\n   * Detects first nearest empty sortable to X and Y position using emptyInsertThreshold.\r\n   * @param  {Number} x      X position\r\n   * @param  {Number} y      Y position\r\n   * @return {HTMLElement}   Element of the first found nearest Sortable\r\n   */\n  _detectNearestEmptySortable = function _detectNearestEmptySortable(x, y) {\n    var ret;\n    sortables.some(function (sortable) {\n      var threshold = sortable[expando].options.emptyInsertThreshold;\n      if (!threshold || lastChild(sortable)) return;\n      var rect = getRect(sortable),\n        insideHorizontally = x >= rect.left - threshold && x <= rect.right + threshold,\n        insideVertically = y >= rect.top - threshold && y <= rect.bottom + threshold;\n      if (insideHorizontally && insideVertically) {\n        return ret = sortable;\n      }\n    });\n    return ret;\n  },\n  _prepareGroup = function _prepareGroup(options) {\n    function toFn(value, pull) {\n      return function (to, from, dragEl, evt) {\n        var sameGroup = to.options.group.name && from.options.group.name && to.options.group.name === from.options.group.name;\n        if (value == null && (pull || sameGroup)) {\n          // Default pull value\n          // Default pull and put value if same group\n          return true;\n        } else if (value == null || value === false) {\n          return false;\n        } else if (pull && value === 'clone') {\n          return value;\n        } else if (typeof value === 'function') {\n          return toFn(value(to, from, dragEl, evt), pull)(to, from, dragEl, evt);\n        } else {\n          var otherGroup = (pull ? to : from).options.group.name;\n          return value === true || typeof value === 'string' && value === otherGroup || value.join && value.indexOf(otherGroup) > -1;\n        }\n      };\n    }\n    var group = {};\n    var originalGroup = options.group;\n    if (!originalGroup || _typeof(originalGroup) != 'object') {\n      originalGroup = {\n        name: originalGroup\n      };\n    }\n    group.name = originalGroup.name;\n    group.checkPull = toFn(originalGroup.pull, true);\n    group.checkPut = toFn(originalGroup.put);\n    group.revertClone = originalGroup.revertClone;\n    options.group = group;\n  },\n  _hideGhostForTarget = function _hideGhostForTarget() {\n    if (!supportCssPointerEvents && ghostEl) {\n      css(ghostEl, 'display', 'none');\n    }\n  },\n  _unhideGhostForTarget = function _unhideGhostForTarget() {\n    if (!supportCssPointerEvents && ghostEl) {\n      css(ghostEl, 'display', '');\n    }\n  };\n\n// #1184 fix - Prevent click event on fallback if dragged but item not changed position\nif (documentExists && !ChromeForAndroid) {\n  document.addEventListener('click', function (evt) {\n    if (ignoreNextClick) {\n      evt.preventDefault();\n      evt.stopPropagation && evt.stopPropagation();\n      evt.stopImmediatePropagation && evt.stopImmediatePropagation();\n      ignoreNextClick = false;\n      return false;\n    }\n  }, true);\n}\nvar nearestEmptyInsertDetectEvent = function nearestEmptyInsertDetectEvent(evt) {\n  if (dragEl) {\n    evt = evt.touches ? evt.touches[0] : evt;\n    var nearest = _detectNearestEmptySortable(evt.clientX, evt.clientY);\n    if (nearest) {\n      // Create imitation event\n      var event = {};\n      for (var i in evt) {\n        if (evt.hasOwnProperty(i)) {\n          event[i] = evt[i];\n        }\n      }\n      event.target = event.rootEl = nearest;\n      event.preventDefault = void 0;\n      event.stopPropagation = void 0;\n      nearest[expando]._onDragOver(event);\n    }\n  }\n};\nvar _checkOutsideTargetEl = function _checkOutsideTargetEl(evt) {\n  if (dragEl) {\n    dragEl.parentNode[expando]._isOutsideThisEl(evt.target);\n  }\n};\n\n/**\r\n * @class  Sortable\r\n * @param  {HTMLElement}  el\r\n * @param  {Object}       [options]\r\n */\nfunction Sortable(el, options) {\n  if (!(el && el.nodeType && el.nodeType === 1)) {\n    throw \"Sortable: `el` must be an HTMLElement, not \".concat({}.toString.call(el));\n  }\n  this.el = el; // root element\n  this.options = options = _extends({}, options);\n\n  // Export instance\n  el[expando] = this;\n  var defaults = {\n    group: null,\n    sort: true,\n    disabled: false,\n    store: null,\n    handle: null,\n    draggable: /^[uo]l$/i.test(el.nodeName) ? '>li' : '>*',\n    swapThreshold: 1,\n    // percentage; 0 <= x <= 1\n    invertSwap: false,\n    // invert always\n    invertedSwapThreshold: null,\n    // will be set to same as swapThreshold if default\n    removeCloneOnHide: true,\n    direction: function direction() {\n      return _detectDirection(el, this.options);\n    },\n    ghostClass: 'sortable-ghost',\n    chosenClass: 'sortable-chosen',\n    dragClass: 'sortable-drag',\n    ignore: 'a, img',\n    filter: null,\n    preventOnFilter: true,\n    animation: 0,\n    easing: null,\n    setData: function setData(dataTransfer, dragEl) {\n      dataTransfer.setData('Text', dragEl.textContent);\n    },\n    dropBubble: false,\n    dragoverBubble: false,\n    dataIdAttr: 'data-id',\n    delay: 0,\n    delayOnTouchOnly: false,\n    touchStartThreshold: (Number.parseInt ? Number : window).parseInt(window.devicePixelRatio, 10) || 1,\n    forceFallback: false,\n    fallbackClass: 'sortable-fallback',\n    fallbackOnBody: false,\n    fallbackTolerance: 0,\n    fallbackOffset: {\n      x: 0,\n      y: 0\n    },\n    supportPointer: Sortable.supportPointer !== false && 'PointerEvent' in window && !Safari,\n    emptyInsertThreshold: 5\n  };\n  PluginManager.initializePlugins(this, el, defaults);\n\n  // Set default options\n  for (var name in defaults) {\n    !(name in options) && (options[name] = defaults[name]);\n  }\n  _prepareGroup(options);\n\n  // Bind all private methods\n  for (var fn in this) {\n    if (fn.charAt(0) === '_' && typeof this[fn] === 'function') {\n      this[fn] = this[fn].bind(this);\n    }\n  }\n\n  // Setup drag mode\n  this.nativeDraggable = options.forceFallback ? false : supportDraggable;\n  if (this.nativeDraggable) {\n    // Touch start threshold cannot be greater than the native dragstart threshold\n    this.options.touchStartThreshold = 1;\n  }\n\n  // Bind events\n  if (options.supportPointer) {\n    on(el, 'pointerdown', this._onTapStart);\n  } else {\n    on(el, 'mousedown', this._onTapStart);\n    on(el, 'touchstart', this._onTapStart);\n  }\n  if (this.nativeDraggable) {\n    on(el, 'dragover', this);\n    on(el, 'dragenter', this);\n  }\n  sortables.push(this.el);\n\n  // Restore sorting\n  options.store && options.store.get && this.sort(options.store.get(this) || []);\n\n  // Add animation state manager\n  _extends(this, AnimationStateManager());\n}\nSortable.prototype = /** @lends Sortable.prototype */{\n  constructor: Sortable,\n  _isOutsideThisEl: function _isOutsideThisEl(target) {\n    if (!this.el.contains(target) && target !== this.el) {\n      lastTarget = null;\n    }\n  },\n  _getDirection: function _getDirection(evt, target) {\n    return typeof this.options.direction === 'function' ? this.options.direction.call(this, evt, target, dragEl) : this.options.direction;\n  },\n  _onTapStart: function _onTapStart(/** Event|TouchEvent */evt) {\n    if (!evt.cancelable) return;\n    var _this = this,\n      el = this.el,\n      options = this.options,\n      preventOnFilter = options.preventOnFilter,\n      type = evt.type,\n      touch = evt.touches && evt.touches[0] || evt.pointerType && evt.pointerType === 'touch' && evt,\n      target = (touch || evt).target,\n      originalTarget = evt.target.shadowRoot && (evt.path && evt.path[0] || evt.composedPath && evt.composedPath()[0]) || target,\n      filter = options.filter;\n    _saveInputCheckedState(el);\n\n    // Don't trigger start event when an element is been dragged, otherwise the evt.oldindex always wrong when set option.group.\n    if (dragEl) {\n      return;\n    }\n    if (/mousedown|pointerdown/.test(type) && evt.button !== 0 || options.disabled) {\n      return; // only left button and enabled\n    }\n\n    // cancel dnd if original target is content editable\n    if (originalTarget.isContentEditable) {\n      return;\n    }\n\n    // Safari ignores further event handling after mousedown\n    if (!this.nativeDraggable && Safari && target && target.tagName.toUpperCase() === 'SELECT') {\n      return;\n    }\n    target = closest(target, options.draggable, el, false);\n    if (target && target.animated) {\n      return;\n    }\n    if (lastDownEl === target) {\n      // Ignoring duplicate `down`\n      return;\n    }\n\n    // Get the index of the dragged element within its parent\n    oldIndex = index(target);\n    oldDraggableIndex = index(target, options.draggable);\n\n    // Check filter\n    if (typeof filter === 'function') {\n      if (filter.call(this, evt, target, this)) {\n        _dispatchEvent({\n          sortable: _this,\n          rootEl: originalTarget,\n          name: 'filter',\n          targetEl: target,\n          toEl: el,\n          fromEl: el\n        });\n        pluginEvent('filter', _this, {\n          evt: evt\n        });\n        preventOnFilter && evt.cancelable && evt.preventDefault();\n        return; // cancel dnd\n      }\n    } else if (filter) {\n      filter = filter.split(',').some(function (criteria) {\n        criteria = closest(originalTarget, criteria.trim(), el, false);\n        if (criteria) {\n          _dispatchEvent({\n            sortable: _this,\n            rootEl: criteria,\n            name: 'filter',\n            targetEl: target,\n            fromEl: el,\n            toEl: el\n          });\n          pluginEvent('filter', _this, {\n            evt: evt\n          });\n          return true;\n        }\n      });\n      if (filter) {\n        preventOnFilter && evt.cancelable && evt.preventDefault();\n        return; // cancel dnd\n      }\n    }\n    if (options.handle && !closest(originalTarget, options.handle, el, false)) {\n      return;\n    }\n\n    // Prepare `dragstart`\n    this._prepareDragStart(evt, touch, target);\n  },\n  _prepareDragStart: function _prepareDragStart(/** Event */evt, /** Touch */touch, /** HTMLElement */target) {\n    var _this = this,\n      el = _this.el,\n      options = _this.options,\n      ownerDocument = el.ownerDocument,\n      dragStartFn;\n    if (target && !dragEl && target.parentNode === el) {\n      var dragRect = getRect(target);\n      rootEl = el;\n      dragEl = target;\n      parentEl = dragEl.parentNode;\n      nextEl = dragEl.nextSibling;\n      lastDownEl = target;\n      activeGroup = options.group;\n      Sortable.dragged = dragEl;\n      tapEvt = {\n        target: dragEl,\n        clientX: (touch || evt).clientX,\n        clientY: (touch || evt).clientY\n      };\n      tapDistanceLeft = tapEvt.clientX - dragRect.left;\n      tapDistanceTop = tapEvt.clientY - dragRect.top;\n      this._lastX = (touch || evt).clientX;\n      this._lastY = (touch || evt).clientY;\n      dragEl.style['will-change'] = 'all';\n      dragStartFn = function dragStartFn() {\n        pluginEvent('delayEnded', _this, {\n          evt: evt\n        });\n        if (Sortable.eventCanceled) {\n          _this._onDrop();\n          return;\n        }\n        // Delayed drag has been triggered\n        // we can re-enable the events: touchmove/mousemove\n        _this._disableDelayedDragEvents();\n        if (!FireFox && _this.nativeDraggable) {\n          dragEl.draggable = true;\n        }\n\n        // Bind the events: dragstart/dragend\n        _this._triggerDragStart(evt, touch);\n\n        // Drag start event\n        _dispatchEvent({\n          sortable: _this,\n          name: 'choose',\n          originalEvent: evt\n        });\n\n        // Chosen item\n        toggleClass(dragEl, options.chosenClass, true);\n      };\n\n      // Disable \"draggable\"\n      options.ignore.split(',').forEach(function (criteria) {\n        find(dragEl, criteria.trim(), _disableDraggable);\n      });\n      on(ownerDocument, 'dragover', nearestEmptyInsertDetectEvent);\n      on(ownerDocument, 'mousemove', nearestEmptyInsertDetectEvent);\n      on(ownerDocument, 'touchmove', nearestEmptyInsertDetectEvent);\n      on(ownerDocument, 'mouseup', _this._onDrop);\n      on(ownerDocument, 'touchend', _this._onDrop);\n      on(ownerDocument, 'touchcancel', _this._onDrop);\n\n      // Make dragEl draggable (must be before delay for FireFox)\n      if (FireFox && this.nativeDraggable) {\n        this.options.touchStartThreshold = 4;\n        dragEl.draggable = true;\n      }\n      pluginEvent('delayStart', this, {\n        evt: evt\n      });\n\n      // Delay is impossible for native DnD in Edge or IE\n      if (options.delay && (!options.delayOnTouchOnly || touch) && (!this.nativeDraggable || !(Edge || IE11OrLess))) {\n        if (Sortable.eventCanceled) {\n          this._onDrop();\n          return;\n        }\n        // If the user moves the pointer or let go the click or touch\n        // before the delay has been reached:\n        // disable the delayed drag\n        on(ownerDocument, 'mouseup', _this._disableDelayedDrag);\n        on(ownerDocument, 'touchend', _this._disableDelayedDrag);\n        on(ownerDocument, 'touchcancel', _this._disableDelayedDrag);\n        on(ownerDocument, 'mousemove', _this._delayedDragTouchMoveHandler);\n        on(ownerDocument, 'touchmove', _this._delayedDragTouchMoveHandler);\n        options.supportPointer && on(ownerDocument, 'pointermove', _this._delayedDragTouchMoveHandler);\n        _this._dragStartTimer = setTimeout(dragStartFn, options.delay);\n      } else {\n        dragStartFn();\n      }\n    }\n  },\n  _delayedDragTouchMoveHandler: function _delayedDragTouchMoveHandler(/** TouchEvent|PointerEvent **/e) {\n    var touch = e.touches ? e.touches[0] : e;\n    if (Math.max(Math.abs(touch.clientX - this._lastX), Math.abs(touch.clientY - this._lastY)) >= Math.floor(this.options.touchStartThreshold / (this.nativeDraggable && window.devicePixelRatio || 1))) {\n      this._disableDelayedDrag();\n    }\n  },\n  _disableDelayedDrag: function _disableDelayedDrag() {\n    dragEl && _disableDraggable(dragEl);\n    clearTimeout(this._dragStartTimer);\n    this._disableDelayedDragEvents();\n  },\n  _disableDelayedDragEvents: function _disableDelayedDragEvents() {\n    var ownerDocument = this.el.ownerDocument;\n    off(ownerDocument, 'mouseup', this._disableDelayedDrag);\n    off(ownerDocument, 'touchend', this._disableDelayedDrag);\n    off(ownerDocument, 'touchcancel', this._disableDelayedDrag);\n    off(ownerDocument, 'mousemove', this._delayedDragTouchMoveHandler);\n    off(ownerDocument, 'touchmove', this._delayedDragTouchMoveHandler);\n    off(ownerDocument, 'pointermove', this._delayedDragTouchMoveHandler);\n  },\n  _triggerDragStart: function _triggerDragStart(/** Event */evt, /** Touch */touch) {\n    touch = touch || evt.pointerType == 'touch' && evt;\n    if (!this.nativeDraggable || touch) {\n      if (this.options.supportPointer) {\n        on(document, 'pointermove', this._onTouchMove);\n      } else if (touch) {\n        on(document, 'touchmove', this._onTouchMove);\n      } else {\n        on(document, 'mousemove', this._onTouchMove);\n      }\n    } else {\n      on(dragEl, 'dragend', this);\n      on(rootEl, 'dragstart', this._onDragStart);\n    }\n    try {\n      if (document.selection) {\n        // Timeout neccessary for IE9\n        _nextTick(function () {\n          document.selection.empty();\n        });\n      } else {\n        window.getSelection().removeAllRanges();\n      }\n    } catch (err) {}\n  },\n  _dragStarted: function _dragStarted(fallback, evt) {\n    awaitingDragStarted = false;\n    if (rootEl && dragEl) {\n      pluginEvent('dragStarted', this, {\n        evt: evt\n      });\n      if (this.nativeDraggable) {\n        on(document, 'dragover', _checkOutsideTargetEl);\n      }\n      var options = this.options;\n\n      // Apply effect\n      !fallback && toggleClass(dragEl, options.dragClass, false);\n      toggleClass(dragEl, options.ghostClass, true);\n      Sortable.active = this;\n      fallback && this._appendGhost();\n\n      // Drag start event\n      _dispatchEvent({\n        sortable: this,\n        name: 'start',\n        originalEvent: evt\n      });\n    } else {\n      this._nulling();\n    }\n  },\n  _emulateDragOver: function _emulateDragOver() {\n    if (touchEvt) {\n      this._lastX = touchEvt.clientX;\n      this._lastY = touchEvt.clientY;\n      _hideGhostForTarget();\n      var target = document.elementFromPoint(touchEvt.clientX, touchEvt.clientY);\n      var parent = target;\n      while (target && target.shadowRoot) {\n        target = target.shadowRoot.elementFromPoint(touchEvt.clientX, touchEvt.clientY);\n        if (target === parent) break;\n        parent = target;\n      }\n      dragEl.parentNode[expando]._isOutsideThisEl(target);\n      if (parent) {\n        do {\n          if (parent[expando]) {\n            var inserted = void 0;\n            inserted = parent[expando]._onDragOver({\n              clientX: touchEvt.clientX,\n              clientY: touchEvt.clientY,\n              target: target,\n              rootEl: parent\n            });\n            if (inserted && !this.options.dragoverBubble) {\n              break;\n            }\n          }\n          target = parent; // store last element\n        }\n        /* jshint boss:true */ while (parent = getParentOrHost(parent));\n      }\n      _unhideGhostForTarget();\n    }\n  },\n  _onTouchMove: function _onTouchMove(/**TouchEvent*/evt) {\n    if (tapEvt) {\n      var options = this.options,\n        fallbackTolerance = options.fallbackTolerance,\n        fallbackOffset = options.fallbackOffset,\n        touch = evt.touches ? evt.touches[0] : evt,\n        ghostMatrix = ghostEl && matrix(ghostEl, true),\n        scaleX = ghostEl && ghostMatrix && ghostMatrix.a,\n        scaleY = ghostEl && ghostMatrix && ghostMatrix.d,\n        relativeScrollOffset = PositionGhostAbsolutely && ghostRelativeParent && getRelativeScrollOffset(ghostRelativeParent),\n        dx = (touch.clientX - tapEvt.clientX + fallbackOffset.x) / (scaleX || 1) + (relativeScrollOffset ? relativeScrollOffset[0] - ghostRelativeParentInitialScroll[0] : 0) / (scaleX || 1),\n        dy = (touch.clientY - tapEvt.clientY + fallbackOffset.y) / (scaleY || 1) + (relativeScrollOffset ? relativeScrollOffset[1] - ghostRelativeParentInitialScroll[1] : 0) / (scaleY || 1);\n\n      // only set the status to dragging, when we are actually dragging\n      if (!Sortable.active && !awaitingDragStarted) {\n        if (fallbackTolerance && Math.max(Math.abs(touch.clientX - this._lastX), Math.abs(touch.clientY - this._lastY)) < fallbackTolerance) {\n          return;\n        }\n        this._onDragStart(evt, true);\n      }\n      if (ghostEl) {\n        if (ghostMatrix) {\n          ghostMatrix.e += dx - (lastDx || 0);\n          ghostMatrix.f += dy - (lastDy || 0);\n        } else {\n          ghostMatrix = {\n            a: 1,\n            b: 0,\n            c: 0,\n            d: 1,\n            e: dx,\n            f: dy\n          };\n        }\n        var cssMatrix = \"matrix(\".concat(ghostMatrix.a, \",\").concat(ghostMatrix.b, \",\").concat(ghostMatrix.c, \",\").concat(ghostMatrix.d, \",\").concat(ghostMatrix.e, \",\").concat(ghostMatrix.f, \")\");\n        css(ghostEl, 'webkitTransform', cssMatrix);\n        css(ghostEl, 'mozTransform', cssMatrix);\n        css(ghostEl, 'msTransform', cssMatrix);\n        css(ghostEl, 'transform', cssMatrix);\n        lastDx = dx;\n        lastDy = dy;\n        touchEvt = touch;\n      }\n      evt.cancelable && evt.preventDefault();\n    }\n  },\n  _appendGhost: function _appendGhost() {\n    // Bug if using scale(): https://stackoverflow.com/questions/2637058\n    // Not being adjusted for\n    if (!ghostEl) {\n      var container = this.options.fallbackOnBody ? document.body : rootEl,\n        rect = getRect(dragEl, true, PositionGhostAbsolutely, true, container),\n        options = this.options;\n\n      // Position absolutely\n      if (PositionGhostAbsolutely) {\n        // Get relatively positioned parent\n        ghostRelativeParent = container;\n        while (css(ghostRelativeParent, 'position') === 'static' && css(ghostRelativeParent, 'transform') === 'none' && ghostRelativeParent !== document) {\n          ghostRelativeParent = ghostRelativeParent.parentNode;\n        }\n        if (ghostRelativeParent !== document.body && ghostRelativeParent !== document.documentElement) {\n          if (ghostRelativeParent === document) ghostRelativeParent = getWindowScrollingElement();\n          rect.top += ghostRelativeParent.scrollTop;\n          rect.left += ghostRelativeParent.scrollLeft;\n        } else {\n          ghostRelativeParent = getWindowScrollingElement();\n        }\n        ghostRelativeParentInitialScroll = getRelativeScrollOffset(ghostRelativeParent);\n      }\n      ghostEl = dragEl.cloneNode(true);\n      toggleClass(ghostEl, options.ghostClass, false);\n      toggleClass(ghostEl, options.fallbackClass, true);\n      toggleClass(ghostEl, options.dragClass, true);\n      css(ghostEl, 'transition', '');\n      css(ghostEl, 'transform', '');\n      css(ghostEl, 'box-sizing', 'border-box');\n      css(ghostEl, 'margin', 0);\n      css(ghostEl, 'top', rect.top);\n      css(ghostEl, 'left', rect.left);\n      css(ghostEl, 'width', rect.width);\n      css(ghostEl, 'height', rect.height);\n      css(ghostEl, 'opacity', '0.8');\n      css(ghostEl, 'position', PositionGhostAbsolutely ? 'absolute' : 'fixed');\n      css(ghostEl, 'zIndex', '100000');\n      css(ghostEl, 'pointerEvents', 'none');\n      Sortable.ghost = ghostEl;\n      container.appendChild(ghostEl);\n\n      // Set transform-origin\n      css(ghostEl, 'transform-origin', tapDistanceLeft / parseInt(ghostEl.style.width) * 100 + '% ' + tapDistanceTop / parseInt(ghostEl.style.height) * 100 + '%');\n    }\n  },\n  _onDragStart: function _onDragStart(/**Event*/evt, /**boolean*/fallback) {\n    var _this = this;\n    var dataTransfer = evt.dataTransfer;\n    var options = _this.options;\n    pluginEvent('dragStart', this, {\n      evt: evt\n    });\n    if (Sortable.eventCanceled) {\n      this._onDrop();\n      return;\n    }\n    pluginEvent('setupClone', this);\n    if (!Sortable.eventCanceled) {\n      cloneEl = clone(dragEl);\n      cloneEl.removeAttribute(\"id\");\n      cloneEl.draggable = false;\n      cloneEl.style['will-change'] = '';\n      this._hideClone();\n      toggleClass(cloneEl, this.options.chosenClass, false);\n      Sortable.clone = cloneEl;\n    }\n\n    // #1143: IFrame support workaround\n    _this.cloneId = _nextTick(function () {\n      pluginEvent('clone', _this);\n      if (Sortable.eventCanceled) return;\n      if (!_this.options.removeCloneOnHide) {\n        rootEl.insertBefore(cloneEl, dragEl);\n      }\n      _this._hideClone();\n      _dispatchEvent({\n        sortable: _this,\n        name: 'clone'\n      });\n    });\n    !fallback && toggleClass(dragEl, options.dragClass, true);\n\n    // Set proper drop events\n    if (fallback) {\n      ignoreNextClick = true;\n      _this._loopId = setInterval(_this._emulateDragOver, 50);\n    } else {\n      // Undo what was set in _prepareDragStart before drag started\n      off(document, 'mouseup', _this._onDrop);\n      off(document, 'touchend', _this._onDrop);\n      off(document, 'touchcancel', _this._onDrop);\n      if (dataTransfer) {\n        dataTransfer.effectAllowed = 'move';\n        options.setData && options.setData.call(_this, dataTransfer, dragEl);\n      }\n      on(document, 'drop', _this);\n\n      // #1276 fix:\n      css(dragEl, 'transform', 'translateZ(0)');\n    }\n    awaitingDragStarted = true;\n    _this._dragStartId = _nextTick(_this._dragStarted.bind(_this, fallback, evt));\n    on(document, 'selectstart', _this);\n    moved = true;\n    if (Safari) {\n      css(document.body, 'user-select', 'none');\n    }\n  },\n  // Returns true - if no further action is needed (either inserted or another condition)\n  _onDragOver: function _onDragOver(/**Event*/evt) {\n    var el = this.el,\n      target = evt.target,\n      dragRect,\n      targetRect,\n      revert,\n      options = this.options,\n      group = options.group,\n      activeSortable = Sortable.active,\n      isOwner = activeGroup === group,\n      canSort = options.sort,\n      fromSortable = putSortable || activeSortable,\n      vertical,\n      _this = this,\n      completedFired = false;\n    if (_silent) return;\n    function dragOverEvent(name, extra) {\n      pluginEvent(name, _this, _objectSpread2({\n        evt: evt,\n        isOwner: isOwner,\n        axis: vertical ? 'vertical' : 'horizontal',\n        revert: revert,\n        dragRect: dragRect,\n        targetRect: targetRect,\n        canSort: canSort,\n        fromSortable: fromSortable,\n        target: target,\n        completed: completed,\n        onMove: function onMove(target, after) {\n          return _onMove(rootEl, el, dragEl, dragRect, target, getRect(target), evt, after);\n        },\n        changed: changed\n      }, extra));\n    }\n\n    // Capture animation state\n    function capture() {\n      dragOverEvent('dragOverAnimationCapture');\n      _this.captureAnimationState();\n      if (_this !== fromSortable) {\n        fromSortable.captureAnimationState();\n      }\n    }\n\n    // Return invocation when dragEl is inserted (or completed)\n    function completed(insertion) {\n      dragOverEvent('dragOverCompleted', {\n        insertion: insertion\n      });\n      if (insertion) {\n        // Clones must be hidden before folding animation to capture dragRectAbsolute properly\n        if (isOwner) {\n          activeSortable._hideClone();\n        } else {\n          activeSortable._showClone(_this);\n        }\n        if (_this !== fromSortable) {\n          // Set ghost class to new sortable's ghost class\n          toggleClass(dragEl, putSortable ? putSortable.options.ghostClass : activeSortable.options.ghostClass, false);\n          toggleClass(dragEl, options.ghostClass, true);\n        }\n        if (putSortable !== _this && _this !== Sortable.active) {\n          putSortable = _this;\n        } else if (_this === Sortable.active && putSortable) {\n          putSortable = null;\n        }\n\n        // Animation\n        if (fromSortable === _this) {\n          _this._ignoreWhileAnimating = target;\n        }\n        _this.animateAll(function () {\n          dragOverEvent('dragOverAnimationComplete');\n          _this._ignoreWhileAnimating = null;\n        });\n        if (_this !== fromSortable) {\n          fromSortable.animateAll();\n          fromSortable._ignoreWhileAnimating = null;\n        }\n      }\n\n      // Null lastTarget if it is not inside a previously swapped element\n      if (target === dragEl && !dragEl.animated || target === el && !target.animated) {\n        lastTarget = null;\n      }\n\n      // no bubbling and not fallback\n      if (!options.dragoverBubble && !evt.rootEl && target !== document) {\n        dragEl.parentNode[expando]._isOutsideThisEl(evt.target);\n\n        // Do not detect for empty insert if already inserted\n        !insertion && nearestEmptyInsertDetectEvent(evt);\n      }\n      !options.dragoverBubble && evt.stopPropagation && evt.stopPropagation();\n      return completedFired = true;\n    }\n\n    // Call when dragEl has been inserted\n    function changed() {\n      newIndex = index(dragEl);\n      newDraggableIndex = index(dragEl, options.draggable);\n      _dispatchEvent({\n        sortable: _this,\n        name: 'change',\n        toEl: el,\n        newIndex: newIndex,\n        newDraggableIndex: newDraggableIndex,\n        originalEvent: evt\n      });\n    }\n    if (evt.preventDefault !== void 0) {\n      evt.cancelable && evt.preventDefault();\n    }\n    target = closest(target, options.draggable, el, true);\n    dragOverEvent('dragOver');\n    if (Sortable.eventCanceled) return completedFired;\n    if (dragEl.contains(evt.target) || target.animated && target.animatingX && target.animatingY || _this._ignoreWhileAnimating === target) {\n      return completed(false);\n    }\n    ignoreNextClick = false;\n    if (activeSortable && !options.disabled && (isOwner ? canSort || (revert = parentEl !== rootEl) // Reverting item into the original list\n    : putSortable === this || (this.lastPutMode = activeGroup.checkPull(this, activeSortable, dragEl, evt)) && group.checkPut(this, activeSortable, dragEl, evt))) {\n      vertical = this._getDirection(evt, target) === 'vertical';\n      dragRect = getRect(dragEl);\n      dragOverEvent('dragOverValid');\n      if (Sortable.eventCanceled) return completedFired;\n      if (revert) {\n        parentEl = rootEl; // actualization\n        capture();\n        this._hideClone();\n        dragOverEvent('revert');\n        if (!Sortable.eventCanceled) {\n          if (nextEl) {\n            rootEl.insertBefore(dragEl, nextEl);\n          } else {\n            rootEl.appendChild(dragEl);\n          }\n        }\n        return completed(true);\n      }\n      var elLastChild = lastChild(el, options.draggable);\n      if (!elLastChild || _ghostIsLast(evt, vertical, this) && !elLastChild.animated) {\n        // Insert to end of list\n\n        // If already at end of list: Do not insert\n        if (elLastChild === dragEl) {\n          return completed(false);\n        }\n\n        // if there is a last element, it is the target\n        if (elLastChild && el === evt.target) {\n          target = elLastChild;\n        }\n        if (target) {\n          targetRect = getRect(target);\n        }\n        if (_onMove(rootEl, el, dragEl, dragRect, target, targetRect, evt, !!target) !== false) {\n          capture();\n          if (elLastChild && elLastChild.nextSibling) {\n            // the last draggable element is not the last node\n            el.insertBefore(dragEl, elLastChild.nextSibling);\n          } else {\n            el.appendChild(dragEl);\n          }\n          parentEl = el; // actualization\n\n          changed();\n          return completed(true);\n        }\n      } else if (elLastChild && _ghostIsFirst(evt, vertical, this)) {\n        // Insert to start of list\n        var firstChild = getChild(el, 0, options, true);\n        if (firstChild === dragEl) {\n          return completed(false);\n        }\n        target = firstChild;\n        targetRect = getRect(target);\n        if (_onMove(rootEl, el, dragEl, dragRect, target, targetRect, evt, false) !== false) {\n          capture();\n          el.insertBefore(dragEl, firstChild);\n          parentEl = el; // actualization\n\n          changed();\n          return completed(true);\n        }\n      } else if (target.parentNode === el) {\n        targetRect = getRect(target);\n        var direction = 0,\n          targetBeforeFirstSwap,\n          differentLevel = dragEl.parentNode !== el,\n          differentRowCol = !_dragElInRowColumn(dragEl.animated && dragEl.toRect || dragRect, target.animated && target.toRect || targetRect, vertical),\n          side1 = vertical ? 'top' : 'left',\n          scrolledPastTop = isScrolledPast(target, 'top', 'top') || isScrolledPast(dragEl, 'top', 'top'),\n          scrollBefore = scrolledPastTop ? scrolledPastTop.scrollTop : void 0;\n        if (lastTarget !== target) {\n          targetBeforeFirstSwap = targetRect[side1];\n          pastFirstInvertThresh = false;\n          isCircumstantialInvert = !differentRowCol && options.invertSwap || differentLevel;\n        }\n        direction = _getSwapDirection(evt, target, targetRect, vertical, differentRowCol ? 1 : options.swapThreshold, options.invertedSwapThreshold == null ? options.swapThreshold : options.invertedSwapThreshold, isCircumstantialInvert, lastTarget === target);\n        var sibling;\n        if (direction !== 0) {\n          // Check if target is beside dragEl in respective direction (ignoring hidden elements)\n          var dragIndex = index(dragEl);\n          do {\n            dragIndex -= direction;\n            sibling = parentEl.children[dragIndex];\n          } while (sibling && (css(sibling, 'display') === 'none' || sibling === ghostEl));\n        }\n        // If dragEl is already beside target: Do not insert\n        if (direction === 0 || sibling === target) {\n          return completed(false);\n        }\n        lastTarget = target;\n        lastDirection = direction;\n        var nextSibling = target.nextElementSibling,\n          after = false;\n        after = direction === 1;\n        var moveVector = _onMove(rootEl, el, dragEl, dragRect, target, targetRect, evt, after);\n        if (moveVector !== false) {\n          if (moveVector === 1 || moveVector === -1) {\n            after = moveVector === 1;\n          }\n          _silent = true;\n          setTimeout(_unsilent, 30);\n          capture();\n          if (after && !nextSibling) {\n            el.appendChild(dragEl);\n          } else {\n            target.parentNode.insertBefore(dragEl, after ? nextSibling : target);\n          }\n\n          // Undo chrome's scroll adjustment (has no effect on other browsers)\n          if (scrolledPastTop) {\n            scrollBy(scrolledPastTop, 0, scrollBefore - scrolledPastTop.scrollTop);\n          }\n          parentEl = dragEl.parentNode; // actualization\n\n          // must be done before animation\n          if (targetBeforeFirstSwap !== undefined && !isCircumstantialInvert) {\n            targetMoveDistance = Math.abs(targetBeforeFirstSwap - getRect(target)[side1]);\n          }\n          changed();\n          return completed(true);\n        }\n      }\n      if (el.contains(dragEl)) {\n        return completed(false);\n      }\n    }\n    return false;\n  },\n  _ignoreWhileAnimating: null,\n  _offMoveEvents: function _offMoveEvents() {\n    off(document, 'mousemove', this._onTouchMove);\n    off(document, 'touchmove', this._onTouchMove);\n    off(document, 'pointermove', this._onTouchMove);\n    off(document, 'dragover', nearestEmptyInsertDetectEvent);\n    off(document, 'mousemove', nearestEmptyInsertDetectEvent);\n    off(document, 'touchmove', nearestEmptyInsertDetectEvent);\n  },\n  _offUpEvents: function _offUpEvents() {\n    var ownerDocument = this.el.ownerDocument;\n    off(ownerDocument, 'mouseup', this._onDrop);\n    off(ownerDocument, 'touchend', this._onDrop);\n    off(ownerDocument, 'pointerup', this._onDrop);\n    off(ownerDocument, 'touchcancel', this._onDrop);\n    off(document, 'selectstart', this);\n  },\n  _onDrop: function _onDrop(/**Event*/evt) {\n    var el = this.el,\n      options = this.options;\n\n    // Get the index of the dragged element within its parent\n    newIndex = index(dragEl);\n    newDraggableIndex = index(dragEl, options.draggable);\n    pluginEvent('drop', this, {\n      evt: evt\n    });\n    parentEl = dragEl && dragEl.parentNode;\n\n    // Get again after plugin event\n    newIndex = index(dragEl);\n    newDraggableIndex = index(dragEl, options.draggable);\n    if (Sortable.eventCanceled) {\n      this._nulling();\n      return;\n    }\n    awaitingDragStarted = false;\n    isCircumstantialInvert = false;\n    pastFirstInvertThresh = false;\n    clearInterval(this._loopId);\n    clearTimeout(this._dragStartTimer);\n    _cancelNextTick(this.cloneId);\n    _cancelNextTick(this._dragStartId);\n\n    // Unbind events\n    if (this.nativeDraggable) {\n      off(document, 'drop', this);\n      off(el, 'dragstart', this._onDragStart);\n    }\n    this._offMoveEvents();\n    this._offUpEvents();\n    if (Safari) {\n      css(document.body, 'user-select', '');\n    }\n    css(dragEl, 'transform', '');\n    if (evt) {\n      if (moved) {\n        evt.cancelable && evt.preventDefault();\n        !options.dropBubble && evt.stopPropagation();\n      }\n      ghostEl && ghostEl.parentNode && ghostEl.parentNode.removeChild(ghostEl);\n      if (rootEl === parentEl || putSortable && putSortable.lastPutMode !== 'clone') {\n        // Remove clone(s)\n        cloneEl && cloneEl.parentNode && cloneEl.parentNode.removeChild(cloneEl);\n      }\n      if (dragEl) {\n        if (this.nativeDraggable) {\n          off(dragEl, 'dragend', this);\n        }\n        _disableDraggable(dragEl);\n        dragEl.style['will-change'] = '';\n\n        // Remove classes\n        // ghostClass is added in dragStarted\n        if (moved && !awaitingDragStarted) {\n          toggleClass(dragEl, putSortable ? putSortable.options.ghostClass : this.options.ghostClass, false);\n        }\n        toggleClass(dragEl, this.options.chosenClass, false);\n\n        // Drag stop event\n        _dispatchEvent({\n          sortable: this,\n          name: 'unchoose',\n          toEl: parentEl,\n          newIndex: null,\n          newDraggableIndex: null,\n          originalEvent: evt\n        });\n        if (rootEl !== parentEl) {\n          if (newIndex >= 0) {\n            // Add event\n            _dispatchEvent({\n              rootEl: parentEl,\n              name: 'add',\n              toEl: parentEl,\n              fromEl: rootEl,\n              originalEvent: evt\n            });\n\n            // Remove event\n            _dispatchEvent({\n              sortable: this,\n              name: 'remove',\n              toEl: parentEl,\n              originalEvent: evt\n            });\n\n            // drag from one list and drop into another\n            _dispatchEvent({\n              rootEl: parentEl,\n              name: 'sort',\n              toEl: parentEl,\n              fromEl: rootEl,\n              originalEvent: evt\n            });\n            _dispatchEvent({\n              sortable: this,\n              name: 'sort',\n              toEl: parentEl,\n              originalEvent: evt\n            });\n          }\n          putSortable && putSortable.save();\n        } else {\n          if (newIndex !== oldIndex) {\n            if (newIndex >= 0) {\n              // drag & drop within the same list\n              _dispatchEvent({\n                sortable: this,\n                name: 'update',\n                toEl: parentEl,\n                originalEvent: evt\n              });\n              _dispatchEvent({\n                sortable: this,\n                name: 'sort',\n                toEl: parentEl,\n                originalEvent: evt\n              });\n            }\n          }\n        }\n        if (Sortable.active) {\n          /* jshint eqnull:true */\n          if (newIndex == null || newIndex === -1) {\n            newIndex = oldIndex;\n            newDraggableIndex = oldDraggableIndex;\n          }\n          _dispatchEvent({\n            sortable: this,\n            name: 'end',\n            toEl: parentEl,\n            originalEvent: evt\n          });\n\n          // Save sorting\n          this.save();\n        }\n      }\n    }\n    this._nulling();\n  },\n  _nulling: function _nulling() {\n    pluginEvent('nulling', this);\n    rootEl = dragEl = parentEl = ghostEl = nextEl = cloneEl = lastDownEl = cloneHidden = tapEvt = touchEvt = moved = newIndex = newDraggableIndex = oldIndex = oldDraggableIndex = lastTarget = lastDirection = putSortable = activeGroup = Sortable.dragged = Sortable.ghost = Sortable.clone = Sortable.active = null;\n    savedInputChecked.forEach(function (el) {\n      el.checked = true;\n    });\n    savedInputChecked.length = lastDx = lastDy = 0;\n  },\n  handleEvent: function handleEvent(/**Event*/evt) {\n    switch (evt.type) {\n      case 'drop':\n      case 'dragend':\n        this._onDrop(evt);\n        break;\n      case 'dragenter':\n      case 'dragover':\n        if (dragEl) {\n          this._onDragOver(evt);\n          _globalDragOver(evt);\n        }\n        break;\n      case 'selectstart':\n        evt.preventDefault();\n        break;\n    }\n  },\n  /**\r\n   * Serializes the item into an array of string.\r\n   * @returns {String[]}\r\n   */\n  toArray: function toArray() {\n    var order = [],\n      el,\n      children = this.el.children,\n      i = 0,\n      n = children.length,\n      options = this.options;\n    for (; i < n; i++) {\n      el = children[i];\n      if (closest(el, options.draggable, this.el, false)) {\n        order.push(el.getAttribute(options.dataIdAttr) || _generateId(el));\n      }\n    }\n    return order;\n  },\n  /**\r\n   * Sorts the elements according to the array.\r\n   * @param  {String[]}  order  order of the items\r\n   */\n  sort: function sort(order, useAnimation) {\n    var items = {},\n      rootEl = this.el;\n    this.toArray().forEach(function (id, i) {\n      var el = rootEl.children[i];\n      if (closest(el, this.options.draggable, rootEl, false)) {\n        items[id] = el;\n      }\n    }, this);\n    useAnimation && this.captureAnimationState();\n    order.forEach(function (id) {\n      if (items[id]) {\n        rootEl.removeChild(items[id]);\n        rootEl.appendChild(items[id]);\n      }\n    });\n    useAnimation && this.animateAll();\n  },\n  /**\r\n   * Save the current sorting\r\n   */\n  save: function save() {\n    var store = this.options.store;\n    store && store.set && store.set(this);\n  },\n  /**\r\n   * For each element in the set, get the first element that matches the selector by testing the element itself and traversing up through its ancestors in the DOM tree.\r\n   * @param   {HTMLElement}  el\r\n   * @param   {String}       [selector]  default: `options.draggable`\r\n   * @returns {HTMLElement|null}\r\n   */\n  closest: function closest$1(el, selector) {\n    return closest(el, selector || this.options.draggable, this.el, false);\n  },\n  /**\r\n   * Set/get option\r\n   * @param   {string} name\r\n   * @param   {*}      [value]\r\n   * @returns {*}\r\n   */\n  option: function option(name, value) {\n    var options = this.options;\n    if (value === void 0) {\n      return options[name];\n    } else {\n      var modifiedValue = PluginManager.modifyOption(this, name, value);\n      if (typeof modifiedValue !== 'undefined') {\n        options[name] = modifiedValue;\n      } else {\n        options[name] = value;\n      }\n      if (name === 'group') {\n        _prepareGroup(options);\n      }\n    }\n  },\n  /**\r\n   * Destroy\r\n   */\n  destroy: function destroy() {\n    pluginEvent('destroy', this);\n    var el = this.el;\n    el[expando] = null;\n    off(el, 'mousedown', this._onTapStart);\n    off(el, 'touchstart', this._onTapStart);\n    off(el, 'pointerdown', this._onTapStart);\n    if (this.nativeDraggable) {\n      off(el, 'dragover', this);\n      off(el, 'dragenter', this);\n    }\n    // Remove draggable attributes\n    Array.prototype.forEach.call(el.querySelectorAll('[draggable]'), function (el) {\n      el.removeAttribute('draggable');\n    });\n    this._onDrop();\n    this._disableDelayedDragEvents();\n    sortables.splice(sortables.indexOf(this.el), 1);\n    this.el = el = null;\n  },\n  _hideClone: function _hideClone() {\n    if (!cloneHidden) {\n      pluginEvent('hideClone', this);\n      if (Sortable.eventCanceled) return;\n      css(cloneEl, 'display', 'none');\n      if (this.options.removeCloneOnHide && cloneEl.parentNode) {\n        cloneEl.parentNode.removeChild(cloneEl);\n      }\n      cloneHidden = true;\n    }\n  },\n  _showClone: function _showClone(putSortable) {\n    if (putSortable.lastPutMode !== 'clone') {\n      this._hideClone();\n      return;\n    }\n    if (cloneHidden) {\n      pluginEvent('showClone', this);\n      if (Sortable.eventCanceled) return;\n\n      // show clone at dragEl or original position\n      if (dragEl.parentNode == rootEl && !this.options.group.revertClone) {\n        rootEl.insertBefore(cloneEl, dragEl);\n      } else if (nextEl) {\n        rootEl.insertBefore(cloneEl, nextEl);\n      } else {\n        rootEl.appendChild(cloneEl);\n      }\n      if (this.options.group.revertClone) {\n        this.animate(dragEl, cloneEl);\n      }\n      css(cloneEl, 'display', '');\n      cloneHidden = false;\n    }\n  }\n};\nfunction _globalDragOver(/**Event*/evt) {\n  if (evt.dataTransfer) {\n    evt.dataTransfer.dropEffect = 'move';\n  }\n  evt.cancelable && evt.preventDefault();\n}\nfunction _onMove(fromEl, toEl, dragEl, dragRect, targetEl, targetRect, originalEvent, willInsertAfter) {\n  var evt,\n    sortable = fromEl[expando],\n    onMoveFn = sortable.options.onMove,\n    retVal;\n  // Support for new CustomEvent feature\n  if (window.CustomEvent && !IE11OrLess && !Edge) {\n    evt = new CustomEvent('move', {\n      bubbles: true,\n      cancelable: true\n    });\n  } else {\n    evt = document.createEvent('Event');\n    evt.initEvent('move', true, true);\n  }\n  evt.to = toEl;\n  evt.from = fromEl;\n  evt.dragged = dragEl;\n  evt.draggedRect = dragRect;\n  evt.related = targetEl || toEl;\n  evt.relatedRect = targetRect || getRect(toEl);\n  evt.willInsertAfter = willInsertAfter;\n  evt.originalEvent = originalEvent;\n  fromEl.dispatchEvent(evt);\n  if (onMoveFn) {\n    retVal = onMoveFn.call(sortable, evt, originalEvent);\n  }\n  return retVal;\n}\nfunction _disableDraggable(el) {\n  el.draggable = false;\n}\nfunction _unsilent() {\n  _silent = false;\n}\nfunction _ghostIsFirst(evt, vertical, sortable) {\n  var firstElRect = getRect(getChild(sortable.el, 0, sortable.options, true));\n  var childContainingRect = getChildContainingRectFromElement(sortable.el, sortable.options, ghostEl);\n  var spacer = 10;\n  return vertical ? evt.clientX < childContainingRect.left - spacer || evt.clientY < firstElRect.top && evt.clientX < firstElRect.right : evt.clientY < childContainingRect.top - spacer || evt.clientY < firstElRect.bottom && evt.clientX < firstElRect.left;\n}\nfunction _ghostIsLast(evt, vertical, sortable) {\n  var lastElRect = getRect(lastChild(sortable.el, sortable.options.draggable));\n  var childContainingRect = getChildContainingRectFromElement(sortable.el, sortable.options, ghostEl);\n  var spacer = 10;\n  return vertical ? evt.clientX > childContainingRect.right + spacer || evt.clientY > lastElRect.bottom && evt.clientX > lastElRect.left : evt.clientY > childContainingRect.bottom + spacer || evt.clientX > lastElRect.right && evt.clientY > lastElRect.top;\n}\nfunction _getSwapDirection(evt, target, targetRect, vertical, swapThreshold, invertedSwapThreshold, invertSwap, isLastTarget) {\n  var mouseOnAxis = vertical ? evt.clientY : evt.clientX,\n    targetLength = vertical ? targetRect.height : targetRect.width,\n    targetS1 = vertical ? targetRect.top : targetRect.left,\n    targetS2 = vertical ? targetRect.bottom : targetRect.right,\n    invert = false;\n  if (!invertSwap) {\n    // Never invert or create dragEl shadow when target movemenet causes mouse to move past the end of regular swapThreshold\n    if (isLastTarget && targetMoveDistance < targetLength * swapThreshold) {\n      // multiplied only by swapThreshold because mouse will already be inside target by (1 - threshold) * targetLength / 2\n      // check if past first invert threshold on side opposite of lastDirection\n      if (!pastFirstInvertThresh && (lastDirection === 1 ? mouseOnAxis > targetS1 + targetLength * invertedSwapThreshold / 2 : mouseOnAxis < targetS2 - targetLength * invertedSwapThreshold / 2)) {\n        // past first invert threshold, do not restrict inverted threshold to dragEl shadow\n        pastFirstInvertThresh = true;\n      }\n      if (!pastFirstInvertThresh) {\n        // dragEl shadow (target move distance shadow)\n        if (lastDirection === 1 ? mouseOnAxis < targetS1 + targetMoveDistance // over dragEl shadow\n        : mouseOnAxis > targetS2 - targetMoveDistance) {\n          return -lastDirection;\n        }\n      } else {\n        invert = true;\n      }\n    } else {\n      // Regular\n      if (mouseOnAxis > targetS1 + targetLength * (1 - swapThreshold) / 2 && mouseOnAxis < targetS2 - targetLength * (1 - swapThreshold) / 2) {\n        return _getInsertDirection(target);\n      }\n    }\n  }\n  invert = invert || invertSwap;\n  if (invert) {\n    // Invert of regular\n    if (mouseOnAxis < targetS1 + targetLength * invertedSwapThreshold / 2 || mouseOnAxis > targetS2 - targetLength * invertedSwapThreshold / 2) {\n      return mouseOnAxis > targetS1 + targetLength / 2 ? 1 : -1;\n    }\n  }\n  return 0;\n}\n\n/**\r\n * Gets the direction dragEl must be swapped relative to target in order to make it\r\n * seem that dragEl has been \"inserted\" into that element's position\r\n * @param  {HTMLElement} target       The target whose position dragEl is being inserted at\r\n * @return {Number}                   Direction dragEl must be swapped\r\n */\nfunction _getInsertDirection(target) {\n  if (index(dragEl) < index(target)) {\n    return 1;\n  } else {\n    return -1;\n  }\n}\n\n/**\r\n * Generate id\r\n * @param   {HTMLElement} el\r\n * @returns {String}\r\n * @private\r\n */\nfunction _generateId(el) {\n  var str = el.tagName + el.className + el.src + el.href + el.textContent,\n    i = str.length,\n    sum = 0;\n  while (i--) {\n    sum += str.charCodeAt(i);\n  }\n  return sum.toString(36);\n}\nfunction _saveInputCheckedState(root) {\n  savedInputChecked.length = 0;\n  var inputs = root.getElementsByTagName('input');\n  var idx = inputs.length;\n  while (idx--) {\n    var el = inputs[idx];\n    el.checked && savedInputChecked.push(el);\n  }\n}\nfunction _nextTick(fn) {\n  return setTimeout(fn, 0);\n}\nfunction _cancelNextTick(id) {\n  return clearTimeout(id);\n}\n\n// Fixed #973:\nif (documentExists) {\n  on(document, 'touchmove', function (evt) {\n    if ((Sortable.active || awaitingDragStarted) && evt.cancelable) {\n      evt.preventDefault();\n    }\n  });\n}\n\n// Export utils\nSortable.utils = {\n  on: on,\n  off: off,\n  css: css,\n  find: find,\n  is: function is(el, selector) {\n    return !!closest(el, selector, el, false);\n  },\n  extend: extend,\n  throttle: throttle,\n  closest: closest,\n  toggleClass: toggleClass,\n  clone: clone,\n  index: index,\n  nextTick: _nextTick,\n  cancelNextTick: _cancelNextTick,\n  detectDirection: _detectDirection,\n  getChild: getChild,\n  expando: expando\n};\n\n/**\r\n * Get the Sortable instance of an element\r\n * @param  {HTMLElement} element The element\r\n * @return {Sortable|undefined}         The instance of Sortable\r\n */\nSortable.get = function (element) {\n  return element[expando];\n};\n\n/**\r\n * Mount a plugin to Sortable\r\n * @param  {...SortablePlugin|SortablePlugin[]} plugins       Plugins being mounted\r\n */\nSortable.mount = function () {\n  for (var _len = arguments.length, plugins = new Array(_len), _key = 0; _key < _len; _key++) {\n    plugins[_key] = arguments[_key];\n  }\n  if (plugins[0].constructor === Array) plugins = plugins[0];\n  plugins.forEach(function (plugin) {\n    if (!plugin.prototype || !plugin.prototype.constructor) {\n      throw \"Sortable: Mounted plugin must be a constructor function, not \".concat({}.toString.call(plugin));\n    }\n    if (plugin.utils) Sortable.utils = _objectSpread2(_objectSpread2({}, Sortable.utils), plugin.utils);\n    PluginManager.mount(plugin);\n  });\n};\n\n/**\r\n * Create sortable instance\r\n * @param {HTMLElement}  el\r\n * @param {Object}      [options]\r\n */\nSortable.create = function (el, options) {\n  return new Sortable(el, options);\n};\n\n// Export\nSortable.version = version;\nvar autoScrolls = [],\n  scrollEl,\n  scrollRootEl,\n  scrolling = false,\n  lastAutoScrollX,\n  lastAutoScrollY,\n  touchEvt$1,\n  pointerElemChangedInterval;\nfunction AutoScrollPlugin() {\n  function AutoScroll() {\n    this.defaults = {\n      scroll: true,\n      forceAutoScrollFallback: false,\n      scrollSensitivity: 30,\n      scrollSpeed: 10,\n      bubbleScroll: true\n    };\n\n    // Bind all private methods\n    for (var fn in this) {\n      if (fn.charAt(0) === '_' && typeof this[fn] === 'function') {\n        this[fn] = this[fn].bind(this);\n      }\n    }\n  }\n  AutoScroll.prototype = {\n    dragStarted: function dragStarted(_ref) {\n      var originalEvent = _ref.originalEvent;\n      if (this.sortable.nativeDraggable) {\n        on(document, 'dragover', this._handleAutoScroll);\n      } else {\n        if (this.options.supportPointer) {\n          on(document, 'pointermove', this._handleFallbackAutoScroll);\n        } else if (originalEvent.touches) {\n          on(document, 'touchmove', this._handleFallbackAutoScroll);\n        } else {\n          on(document, 'mousemove', this._handleFallbackAutoScroll);\n        }\n      }\n    },\n    dragOverCompleted: function dragOverCompleted(_ref2) {\n      var originalEvent = _ref2.originalEvent;\n      // For when bubbling is canceled and using fallback (fallback 'touchmove' always reached)\n      if (!this.options.dragOverBubble && !originalEvent.rootEl) {\n        this._handleAutoScroll(originalEvent);\n      }\n    },\n    drop: function drop() {\n      if (this.sortable.nativeDraggable) {\n        off(document, 'dragover', this._handleAutoScroll);\n      } else {\n        off(document, 'pointermove', this._handleFallbackAutoScroll);\n        off(document, 'touchmove', this._handleFallbackAutoScroll);\n        off(document, 'mousemove', this._handleFallbackAutoScroll);\n      }\n      clearPointerElemChangedInterval();\n      clearAutoScrolls();\n      cancelThrottle();\n    },\n    nulling: function nulling() {\n      touchEvt$1 = scrollRootEl = scrollEl = scrolling = pointerElemChangedInterval = lastAutoScrollX = lastAutoScrollY = null;\n      autoScrolls.length = 0;\n    },\n    _handleFallbackAutoScroll: function _handleFallbackAutoScroll(evt) {\n      this._handleAutoScroll(evt, true);\n    },\n    _handleAutoScroll: function _handleAutoScroll(evt, fallback) {\n      var _this = this;\n      var x = (evt.touches ? evt.touches[0] : evt).clientX,\n        y = (evt.touches ? evt.touches[0] : evt).clientY,\n        elem = document.elementFromPoint(x, y);\n      touchEvt$1 = evt;\n\n      // IE does not seem to have native autoscroll,\n      // Edge's autoscroll seems too conditional,\n      // MACOS Safari does not have autoscroll,\n      // Firefox and Chrome are good\n      if (fallback || this.options.forceAutoScrollFallback || Edge || IE11OrLess || Safari) {\n        autoScroll(evt, this.options, elem, fallback);\n\n        // Listener for pointer element change\n        var ogElemScroller = getParentAutoScrollElement(elem, true);\n        if (scrolling && (!pointerElemChangedInterval || x !== lastAutoScrollX || y !== lastAutoScrollY)) {\n          pointerElemChangedInterval && clearPointerElemChangedInterval();\n          // Detect for pointer elem change, emulating native DnD behaviour\n          pointerElemChangedInterval = setInterval(function () {\n            var newElem = getParentAutoScrollElement(document.elementFromPoint(x, y), true);\n            if (newElem !== ogElemScroller) {\n              ogElemScroller = newElem;\n              clearAutoScrolls();\n            }\n            autoScroll(evt, _this.options, newElem, fallback);\n          }, 10);\n          lastAutoScrollX = x;\n          lastAutoScrollY = y;\n        }\n      } else {\n        // if DnD is enabled (and browser has good autoscrolling), first autoscroll will already scroll, so get parent autoscroll of first autoscroll\n        if (!this.options.bubbleScroll || getParentAutoScrollElement(elem, true) === getWindowScrollingElement()) {\n          clearAutoScrolls();\n          return;\n        }\n        autoScroll(evt, this.options, getParentAutoScrollElement(elem, false), false);\n      }\n    }\n  };\n  return _extends(AutoScroll, {\n    pluginName: 'scroll',\n    initializeByDefault: true\n  });\n}\nfunction clearAutoScrolls() {\n  autoScrolls.forEach(function (autoScroll) {\n    clearInterval(autoScroll.pid);\n  });\n  autoScrolls = [];\n}\nfunction clearPointerElemChangedInterval() {\n  clearInterval(pointerElemChangedInterval);\n}\nvar autoScroll = throttle(function (evt, options, rootEl, isFallback) {\n  // Bug: https://bugzilla.mozilla.org/show_bug.cgi?id=505521\n  if (!options.scroll) return;\n  var x = (evt.touches ? evt.touches[0] : evt).clientX,\n    y = (evt.touches ? evt.touches[0] : evt).clientY,\n    sens = options.scrollSensitivity,\n    speed = options.scrollSpeed,\n    winScroller = getWindowScrollingElement();\n  var scrollThisInstance = false,\n    scrollCustomFn;\n\n  // New scroll root, set scrollEl\n  if (scrollRootEl !== rootEl) {\n    scrollRootEl = rootEl;\n    clearAutoScrolls();\n    scrollEl = options.scroll;\n    scrollCustomFn = options.scrollFn;\n    if (scrollEl === true) {\n      scrollEl = getParentAutoScrollElement(rootEl, true);\n    }\n  }\n  var layersOut = 0;\n  var currentParent = scrollEl;\n  do {\n    var el = currentParent,\n      rect = getRect(el),\n      top = rect.top,\n      bottom = rect.bottom,\n      left = rect.left,\n      right = rect.right,\n      width = rect.width,\n      height = rect.height,\n      canScrollX = void 0,\n      canScrollY = void 0,\n      scrollWidth = el.scrollWidth,\n      scrollHeight = el.scrollHeight,\n      elCSS = css(el),\n      scrollPosX = el.scrollLeft,\n      scrollPosY = el.scrollTop;\n    if (el === winScroller) {\n      canScrollX = width < scrollWidth && (elCSS.overflowX === 'auto' || elCSS.overflowX === 'scroll' || elCSS.overflowX === 'visible');\n      canScrollY = height < scrollHeight && (elCSS.overflowY === 'auto' || elCSS.overflowY === 'scroll' || elCSS.overflowY === 'visible');\n    } else {\n      canScrollX = width < scrollWidth && (elCSS.overflowX === 'auto' || elCSS.overflowX === 'scroll');\n      canScrollY = height < scrollHeight && (elCSS.overflowY === 'auto' || elCSS.overflowY === 'scroll');\n    }\n    var vx = canScrollX && (Math.abs(right - x) <= sens && scrollPosX + width < scrollWidth) - (Math.abs(left - x) <= sens && !!scrollPosX);\n    var vy = canScrollY && (Math.abs(bottom - y) <= sens && scrollPosY + height < scrollHeight) - (Math.abs(top - y) <= sens && !!scrollPosY);\n    if (!autoScrolls[layersOut]) {\n      for (var i = 0; i <= layersOut; i++) {\n        if (!autoScrolls[i]) {\n          autoScrolls[i] = {};\n        }\n      }\n    }\n    if (autoScrolls[layersOut].vx != vx || autoScrolls[layersOut].vy != vy || autoScrolls[layersOut].el !== el) {\n      autoScrolls[layersOut].el = el;\n      autoScrolls[layersOut].vx = vx;\n      autoScrolls[layersOut].vy = vy;\n      clearInterval(autoScrolls[layersOut].pid);\n      if (vx != 0 || vy != 0) {\n        scrollThisInstance = true;\n        /* jshint loopfunc:true */\n        autoScrolls[layersOut].pid = setInterval(function () {\n          // emulate drag over during autoscroll (fallback), emulating native DnD behaviour\n          if (isFallback && this.layer === 0) {\n            Sortable.active._onTouchMove(touchEvt$1); // To move ghost if it is positioned absolutely\n          }\n          var scrollOffsetY = autoScrolls[this.layer].vy ? autoScrolls[this.layer].vy * speed : 0;\n          var scrollOffsetX = autoScrolls[this.layer].vx ? autoScrolls[this.layer].vx * speed : 0;\n          if (typeof scrollCustomFn === 'function') {\n            if (scrollCustomFn.call(Sortable.dragged.parentNode[expando], scrollOffsetX, scrollOffsetY, evt, touchEvt$1, autoScrolls[this.layer].el) !== 'continue') {\n              return;\n            }\n          }\n          scrollBy(autoScrolls[this.layer].el, scrollOffsetX, scrollOffsetY);\n        }.bind({\n          layer: layersOut\n        }), 24);\n      }\n    }\n    layersOut++;\n  } while (options.bubbleScroll && currentParent !== winScroller && (currentParent = getParentAutoScrollElement(currentParent, false)));\n  scrolling = scrollThisInstance; // in case another function catches scrolling as false in between when it is not\n}, 30);\nvar drop = function drop(_ref) {\n  var originalEvent = _ref.originalEvent,\n    putSortable = _ref.putSortable,\n    dragEl = _ref.dragEl,\n    activeSortable = _ref.activeSortable,\n    dispatchSortableEvent = _ref.dispatchSortableEvent,\n    hideGhostForTarget = _ref.hideGhostForTarget,\n    unhideGhostForTarget = _ref.unhideGhostForTarget;\n  if (!originalEvent) return;\n  var toSortable = putSortable || activeSortable;\n  hideGhostForTarget();\n  var touch = originalEvent.changedTouches && originalEvent.changedTouches.length ? originalEvent.changedTouches[0] : originalEvent;\n  var target = document.elementFromPoint(touch.clientX, touch.clientY);\n  unhideGhostForTarget();\n  if (toSortable && !toSortable.el.contains(target)) {\n    dispatchSortableEvent('spill');\n    this.onSpill({\n      dragEl: dragEl,\n      putSortable: putSortable\n    });\n  }\n};\nfunction Revert() {}\nRevert.prototype = {\n  startIndex: null,\n  dragStart: function dragStart(_ref2) {\n    var oldDraggableIndex = _ref2.oldDraggableIndex;\n    this.startIndex = oldDraggableIndex;\n  },\n  onSpill: function onSpill(_ref3) {\n    var dragEl = _ref3.dragEl,\n      putSortable = _ref3.putSortable;\n    this.sortable.captureAnimationState();\n    if (putSortable) {\n      putSortable.captureAnimationState();\n    }\n    var nextSibling = getChild(this.sortable.el, this.startIndex, this.options);\n    if (nextSibling) {\n      this.sortable.el.insertBefore(dragEl, nextSibling);\n    } else {\n      this.sortable.el.appendChild(dragEl);\n    }\n    this.sortable.animateAll();\n    if (putSortable) {\n      putSortable.animateAll();\n    }\n  },\n  drop: drop\n};\n_extends(Revert, {\n  pluginName: 'revertOnSpill'\n});\nfunction Remove() {}\nRemove.prototype = {\n  onSpill: function onSpill(_ref4) {\n    var dragEl = _ref4.dragEl,\n      putSortable = _ref4.putSortable;\n    var parentSortable = putSortable || this.sortable;\n    parentSortable.captureAnimationState();\n    dragEl.parentNode && dragEl.parentNode.removeChild(dragEl);\n    parentSortable.animateAll();\n  },\n  drop: drop\n};\n_extends(Remove, {\n  pluginName: 'removeOnSpill'\n});\nvar lastSwapEl;\nfunction SwapPlugin() {\n  function Swap() {\n    this.defaults = {\n      swapClass: 'sortable-swap-highlight'\n    };\n  }\n  Swap.prototype = {\n    dragStart: function dragStart(_ref) {\n      var dragEl = _ref.dragEl;\n      lastSwapEl = dragEl;\n    },\n    dragOverValid: function dragOverValid(_ref2) {\n      var completed = _ref2.completed,\n        target = _ref2.target,\n        onMove = _ref2.onMove,\n        activeSortable = _ref2.activeSortable,\n        changed = _ref2.changed,\n        cancel = _ref2.cancel;\n      if (!activeSortable.options.swap) return;\n      var el = this.sortable.el,\n        options = this.options;\n      if (target && target !== el) {\n        var prevSwapEl = lastSwapEl;\n        if (onMove(target) !== false) {\n          toggleClass(target, options.swapClass, true);\n          lastSwapEl = target;\n        } else {\n          lastSwapEl = null;\n        }\n        if (prevSwapEl && prevSwapEl !== lastSwapEl) {\n          toggleClass(prevSwapEl, options.swapClass, false);\n        }\n      }\n      changed();\n      completed(true);\n      cancel();\n    },\n    drop: function drop(_ref3) {\n      var activeSortable = _ref3.activeSortable,\n        putSortable = _ref3.putSortable,\n        dragEl = _ref3.dragEl;\n      var toSortable = putSortable || this.sortable;\n      var options = this.options;\n      lastSwapEl && toggleClass(lastSwapEl, options.swapClass, false);\n      if (lastSwapEl && (options.swap || putSortable && putSortable.options.swap)) {\n        if (dragEl !== lastSwapEl) {\n          toSortable.captureAnimationState();\n          if (toSortable !== activeSortable) activeSortable.captureAnimationState();\n          swapNodes(dragEl, lastSwapEl);\n          toSortable.animateAll();\n          if (toSortable !== activeSortable) activeSortable.animateAll();\n        }\n      }\n    },\n    nulling: function nulling() {\n      lastSwapEl = null;\n    }\n  };\n  return _extends(Swap, {\n    pluginName: 'swap',\n    eventProperties: function eventProperties() {\n      return {\n        swapItem: lastSwapEl\n      };\n    }\n  });\n}\nfunction swapNodes(n1, n2) {\n  var p1 = n1.parentNode,\n    p2 = n2.parentNode,\n    i1,\n    i2;\n  if (!p1 || !p2 || p1.isEqualNode(n2) || p2.isEqualNode(n1)) return;\n  i1 = index(n1);\n  i2 = index(n2);\n  if (p1.isEqualNode(p2) && i1 < i2) {\n    i2++;\n  }\n  p1.insertBefore(n2, p1.children[i1]);\n  p2.insertBefore(n1, p2.children[i2]);\n}\nvar multiDragElements = [],\n  multiDragClones = [],\n  lastMultiDragSelect,\n  // for selection with modifier key down (SHIFT)\n  multiDragSortable,\n  initialFolding = false,\n  // Initial multi-drag fold when drag started\n  folding = false,\n  // Folding any other time\n  dragStarted = false,\n  dragEl$1,\n  clonesFromRect,\n  clonesHidden;\nfunction MultiDragPlugin() {\n  function MultiDrag(sortable) {\n    // Bind all private methods\n    for (var fn in this) {\n      if (fn.charAt(0) === '_' && typeof this[fn] === 'function') {\n        this[fn] = this[fn].bind(this);\n      }\n    }\n    if (!sortable.options.avoidImplicitDeselect) {\n      if (sortable.options.supportPointer) {\n        on(document, 'pointerup', this._deselectMultiDrag);\n      } else {\n        on(document, 'mouseup', this._deselectMultiDrag);\n        on(document, 'touchend', this._deselectMultiDrag);\n      }\n    }\n    on(document, 'keydown', this._checkKeyDown);\n    on(document, 'keyup', this._checkKeyUp);\n    this.defaults = {\n      selectedClass: 'sortable-selected',\n      multiDragKey: null,\n      avoidImplicitDeselect: false,\n      setData: function setData(dataTransfer, dragEl) {\n        var data = '';\n        if (multiDragElements.length && multiDragSortable === sortable) {\n          multiDragElements.forEach(function (multiDragElement, i) {\n            data += (!i ? '' : ', ') + multiDragElement.textContent;\n          });\n        } else {\n          data = dragEl.textContent;\n        }\n        dataTransfer.setData('Text', data);\n      }\n    };\n  }\n  MultiDrag.prototype = {\n    multiDragKeyDown: false,\n    isMultiDrag: false,\n    delayStartGlobal: function delayStartGlobal(_ref) {\n      var dragged = _ref.dragEl;\n      dragEl$1 = dragged;\n    },\n    delayEnded: function delayEnded() {\n      this.isMultiDrag = ~multiDragElements.indexOf(dragEl$1);\n    },\n    setupClone: function setupClone(_ref2) {\n      var sortable = _ref2.sortable,\n        cancel = _ref2.cancel;\n      if (!this.isMultiDrag) return;\n      for (var i = 0; i < multiDragElements.length; i++) {\n        multiDragClones.push(clone(multiDragElements[i]));\n        multiDragClones[i].sortableIndex = multiDragElements[i].sortableIndex;\n        multiDragClones[i].draggable = false;\n        multiDragClones[i].style['will-change'] = '';\n        toggleClass(multiDragClones[i], this.options.selectedClass, false);\n        multiDragElements[i] === dragEl$1 && toggleClass(multiDragClones[i], this.options.chosenClass, false);\n      }\n      sortable._hideClone();\n      cancel();\n    },\n    clone: function clone(_ref3) {\n      var sortable = _ref3.sortable,\n        rootEl = _ref3.rootEl,\n        dispatchSortableEvent = _ref3.dispatchSortableEvent,\n        cancel = _ref3.cancel;\n      if (!this.isMultiDrag) return;\n      if (!this.options.removeCloneOnHide) {\n        if (multiDragElements.length && multiDragSortable === sortable) {\n          insertMultiDragClones(true, rootEl);\n          dispatchSortableEvent('clone');\n          cancel();\n        }\n      }\n    },\n    showClone: function showClone(_ref4) {\n      var cloneNowShown = _ref4.cloneNowShown,\n        rootEl = _ref4.rootEl,\n        cancel = _ref4.cancel;\n      if (!this.isMultiDrag) return;\n      insertMultiDragClones(false, rootEl);\n      multiDragClones.forEach(function (clone) {\n        css(clone, 'display', '');\n      });\n      cloneNowShown();\n      clonesHidden = false;\n      cancel();\n    },\n    hideClone: function hideClone(_ref5) {\n      var _this = this;\n      var sortable = _ref5.sortable,\n        cloneNowHidden = _ref5.cloneNowHidden,\n        cancel = _ref5.cancel;\n      if (!this.isMultiDrag) return;\n      multiDragClones.forEach(function (clone) {\n        css(clone, 'display', 'none');\n        if (_this.options.removeCloneOnHide && clone.parentNode) {\n          clone.parentNode.removeChild(clone);\n        }\n      });\n      cloneNowHidden();\n      clonesHidden = true;\n      cancel();\n    },\n    dragStartGlobal: function dragStartGlobal(_ref6) {\n      var sortable = _ref6.sortable;\n      if (!this.isMultiDrag && multiDragSortable) {\n        multiDragSortable.multiDrag._deselectMultiDrag();\n      }\n      multiDragElements.forEach(function (multiDragElement) {\n        multiDragElement.sortableIndex = index(multiDragElement);\n      });\n\n      // Sort multi-drag elements\n      multiDragElements = multiDragElements.sort(function (a, b) {\n        return a.sortableIndex - b.sortableIndex;\n      });\n      dragStarted = true;\n    },\n    dragStarted: function dragStarted(_ref7) {\n      var _this2 = this;\n      var sortable = _ref7.sortable;\n      if (!this.isMultiDrag) return;\n      if (this.options.sort) {\n        // Capture rects,\n        // hide multi drag elements (by positioning them absolute),\n        // set multi drag elements rects to dragRect,\n        // show multi drag elements,\n        // animate to rects,\n        // unset rects & remove from DOM\n\n        sortable.captureAnimationState();\n        if (this.options.animation) {\n          multiDragElements.forEach(function (multiDragElement) {\n            if (multiDragElement === dragEl$1) return;\n            css(multiDragElement, 'position', 'absolute');\n          });\n          var dragRect = getRect(dragEl$1, false, true, true);\n          multiDragElements.forEach(function (multiDragElement) {\n            if (multiDragElement === dragEl$1) return;\n            setRect(multiDragElement, dragRect);\n          });\n          folding = true;\n          initialFolding = true;\n        }\n      }\n      sortable.animateAll(function () {\n        folding = false;\n        initialFolding = false;\n        if (_this2.options.animation) {\n          multiDragElements.forEach(function (multiDragElement) {\n            unsetRect(multiDragElement);\n          });\n        }\n\n        // Remove all auxiliary multidrag items from el, if sorting enabled\n        if (_this2.options.sort) {\n          removeMultiDragElements();\n        }\n      });\n    },\n    dragOver: function dragOver(_ref8) {\n      var target = _ref8.target,\n        completed = _ref8.completed,\n        cancel = _ref8.cancel;\n      if (folding && ~multiDragElements.indexOf(target)) {\n        completed(false);\n        cancel();\n      }\n    },\n    revert: function revert(_ref9) {\n      var fromSortable = _ref9.fromSortable,\n        rootEl = _ref9.rootEl,\n        sortable = _ref9.sortable,\n        dragRect = _ref9.dragRect;\n      if (multiDragElements.length > 1) {\n        // Setup unfold animation\n        multiDragElements.forEach(function (multiDragElement) {\n          sortable.addAnimationState({\n            target: multiDragElement,\n            rect: folding ? getRect(multiDragElement) : dragRect\n          });\n          unsetRect(multiDragElement);\n          multiDragElement.fromRect = dragRect;\n          fromSortable.removeAnimationState(multiDragElement);\n        });\n        folding = false;\n        insertMultiDragElements(!this.options.removeCloneOnHide, rootEl);\n      }\n    },\n    dragOverCompleted: function dragOverCompleted(_ref10) {\n      var sortable = _ref10.sortable,\n        isOwner = _ref10.isOwner,\n        insertion = _ref10.insertion,\n        activeSortable = _ref10.activeSortable,\n        parentEl = _ref10.parentEl,\n        putSortable = _ref10.putSortable;\n      var options = this.options;\n      if (insertion) {\n        // Clones must be hidden before folding animation to capture dragRectAbsolute properly\n        if (isOwner) {\n          activeSortable._hideClone();\n        }\n        initialFolding = false;\n        // If leaving sort:false root, or already folding - Fold to new location\n        if (options.animation && multiDragElements.length > 1 && (folding || !isOwner && !activeSortable.options.sort && !putSortable)) {\n          // Fold: Set all multi drag elements's rects to dragEl's rect when multi-drag elements are invisible\n          var dragRectAbsolute = getRect(dragEl$1, false, true, true);\n          multiDragElements.forEach(function (multiDragElement) {\n            if (multiDragElement === dragEl$1) return;\n            setRect(multiDragElement, dragRectAbsolute);\n\n            // Move element(s) to end of parentEl so that it does not interfere with multi-drag clones insertion if they are inserted\n            // while folding, and so that we can capture them again because old sortable will no longer be fromSortable\n            parentEl.appendChild(multiDragElement);\n          });\n          folding = true;\n        }\n\n        // Clones must be shown (and check to remove multi drags) after folding when interfering multiDragElements are moved out\n        if (!isOwner) {\n          // Only remove if not folding (folding will remove them anyways)\n          if (!folding) {\n            removeMultiDragElements();\n          }\n          if (multiDragElements.length > 1) {\n            var clonesHiddenBefore = clonesHidden;\n            activeSortable._showClone(sortable);\n\n            // Unfold animation for clones if showing from hidden\n            if (activeSortable.options.animation && !clonesHidden && clonesHiddenBefore) {\n              multiDragClones.forEach(function (clone) {\n                activeSortable.addAnimationState({\n                  target: clone,\n                  rect: clonesFromRect\n                });\n                clone.fromRect = clonesFromRect;\n                clone.thisAnimationDuration = null;\n              });\n            }\n          } else {\n            activeSortable._showClone(sortable);\n          }\n        }\n      }\n    },\n    dragOverAnimationCapture: function dragOverAnimationCapture(_ref11) {\n      var dragRect = _ref11.dragRect,\n        isOwner = _ref11.isOwner,\n        activeSortable = _ref11.activeSortable;\n      multiDragElements.forEach(function (multiDragElement) {\n        multiDragElement.thisAnimationDuration = null;\n      });\n      if (activeSortable.options.animation && !isOwner && activeSortable.multiDrag.isMultiDrag) {\n        clonesFromRect = _extends({}, dragRect);\n        var dragMatrix = matrix(dragEl$1, true);\n        clonesFromRect.top -= dragMatrix.f;\n        clonesFromRect.left -= dragMatrix.e;\n      }\n    },\n    dragOverAnimationComplete: function dragOverAnimationComplete() {\n      if (folding) {\n        folding = false;\n        removeMultiDragElements();\n      }\n    },\n    drop: function drop(_ref12) {\n      var evt = _ref12.originalEvent,\n        rootEl = _ref12.rootEl,\n        parentEl = _ref12.parentEl,\n        sortable = _ref12.sortable,\n        dispatchSortableEvent = _ref12.dispatchSortableEvent,\n        oldIndex = _ref12.oldIndex,\n        putSortable = _ref12.putSortable;\n      var toSortable = putSortable || this.sortable;\n      if (!evt) return;\n      var options = this.options,\n        children = parentEl.children;\n\n      // Multi-drag selection\n      if (!dragStarted) {\n        if (options.multiDragKey && !this.multiDragKeyDown) {\n          this._deselectMultiDrag();\n        }\n        toggleClass(dragEl$1, options.selectedClass, !~multiDragElements.indexOf(dragEl$1));\n        if (!~multiDragElements.indexOf(dragEl$1)) {\n          multiDragElements.push(dragEl$1);\n          dispatchEvent({\n            sortable: sortable,\n            rootEl: rootEl,\n            name: 'select',\n            targetEl: dragEl$1,\n            originalEvent: evt\n          });\n\n          // Modifier activated, select from last to dragEl\n          if (evt.shiftKey && lastMultiDragSelect && sortable.el.contains(lastMultiDragSelect)) {\n            var lastIndex = index(lastMultiDragSelect),\n              currentIndex = index(dragEl$1);\n            if (~lastIndex && ~currentIndex && lastIndex !== currentIndex) {\n              // Must include lastMultiDragSelect (select it), in case modified selection from no selection\n              // (but previous selection existed)\n              var n, i;\n              if (currentIndex > lastIndex) {\n                i = lastIndex;\n                n = currentIndex;\n              } else {\n                i = currentIndex;\n                n = lastIndex + 1;\n              }\n              for (; i < n; i++) {\n                if (~multiDragElements.indexOf(children[i])) continue;\n                toggleClass(children[i], options.selectedClass, true);\n                multiDragElements.push(children[i]);\n                dispatchEvent({\n                  sortable: sortable,\n                  rootEl: rootEl,\n                  name: 'select',\n                  targetEl: children[i],\n                  originalEvent: evt\n                });\n              }\n            }\n          } else {\n            lastMultiDragSelect = dragEl$1;\n          }\n          multiDragSortable = toSortable;\n        } else {\n          multiDragElements.splice(multiDragElements.indexOf(dragEl$1), 1);\n          lastMultiDragSelect = null;\n          dispatchEvent({\n            sortable: sortable,\n            rootEl: rootEl,\n            name: 'deselect',\n            targetEl: dragEl$1,\n            originalEvent: evt\n          });\n        }\n      }\n\n      // Multi-drag drop\n      if (dragStarted && this.isMultiDrag) {\n        folding = false;\n        // Do not \"unfold\" after around dragEl if reverted\n        if ((parentEl[expando].options.sort || parentEl !== rootEl) && multiDragElements.length > 1) {\n          var dragRect = getRect(dragEl$1),\n            multiDragIndex = index(dragEl$1, ':not(.' + this.options.selectedClass + ')');\n          if (!initialFolding && options.animation) dragEl$1.thisAnimationDuration = null;\n          toSortable.captureAnimationState();\n          if (!initialFolding) {\n            if (options.animation) {\n              dragEl$1.fromRect = dragRect;\n              multiDragElements.forEach(function (multiDragElement) {\n                multiDragElement.thisAnimationDuration = null;\n                if (multiDragElement !== dragEl$1) {\n                  var rect = folding ? getRect(multiDragElement) : dragRect;\n                  multiDragElement.fromRect = rect;\n\n                  // Prepare unfold animation\n                  toSortable.addAnimationState({\n                    target: multiDragElement,\n                    rect: rect\n                  });\n                }\n              });\n            }\n\n            // Multi drag elements are not necessarily removed from the DOM on drop, so to reinsert\n            // properly they must all be removed\n            removeMultiDragElements();\n            multiDragElements.forEach(function (multiDragElement) {\n              if (children[multiDragIndex]) {\n                parentEl.insertBefore(multiDragElement, children[multiDragIndex]);\n              } else {\n                parentEl.appendChild(multiDragElement);\n              }\n              multiDragIndex++;\n            });\n\n            // If initial folding is done, the elements may have changed position because they are now\n            // unfolding around dragEl, even though dragEl may not have his index changed, so update event\n            // must be fired here as Sortable will not.\n            if (oldIndex === index(dragEl$1)) {\n              var update = false;\n              multiDragElements.forEach(function (multiDragElement) {\n                if (multiDragElement.sortableIndex !== index(multiDragElement)) {\n                  update = true;\n                  return;\n                }\n              });\n              if (update) {\n                dispatchSortableEvent('update');\n                dispatchSortableEvent('sort');\n              }\n            }\n          }\n\n          // Must be done after capturing individual rects (scroll bar)\n          multiDragElements.forEach(function (multiDragElement) {\n            unsetRect(multiDragElement);\n          });\n          toSortable.animateAll();\n        }\n        multiDragSortable = toSortable;\n      }\n\n      // Remove clones if necessary\n      if (rootEl === parentEl || putSortable && putSortable.lastPutMode !== 'clone') {\n        multiDragClones.forEach(function (clone) {\n          clone.parentNode && clone.parentNode.removeChild(clone);\n        });\n      }\n    },\n    nullingGlobal: function nullingGlobal() {\n      this.isMultiDrag = dragStarted = false;\n      multiDragClones.length = 0;\n    },\n    destroyGlobal: function destroyGlobal() {\n      this._deselectMultiDrag();\n      off(document, 'pointerup', this._deselectMultiDrag);\n      off(document, 'mouseup', this._deselectMultiDrag);\n      off(document, 'touchend', this._deselectMultiDrag);\n      off(document, 'keydown', this._checkKeyDown);\n      off(document, 'keyup', this._checkKeyUp);\n    },\n    _deselectMultiDrag: function _deselectMultiDrag(evt) {\n      if (typeof dragStarted !== \"undefined\" && dragStarted) return;\n\n      // Only deselect if selection is in this sortable\n      if (multiDragSortable !== this.sortable) return;\n\n      // Only deselect if target is not item in this sortable\n      if (evt && closest(evt.target, this.options.draggable, this.sortable.el, false)) return;\n\n      // Only deselect if left click\n      if (evt && evt.button !== 0) return;\n      while (multiDragElements.length) {\n        var el = multiDragElements[0];\n        toggleClass(el, this.options.selectedClass, false);\n        multiDragElements.shift();\n        dispatchEvent({\n          sortable: this.sortable,\n          rootEl: this.sortable.el,\n          name: 'deselect',\n          targetEl: el,\n          originalEvent: evt\n        });\n      }\n    },\n    _checkKeyDown: function _checkKeyDown(evt) {\n      if (evt.key === this.options.multiDragKey) {\n        this.multiDragKeyDown = true;\n      }\n    },\n    _checkKeyUp: function _checkKeyUp(evt) {\n      if (evt.key === this.options.multiDragKey) {\n        this.multiDragKeyDown = false;\n      }\n    }\n  };\n  return _extends(MultiDrag, {\n    // Static methods & properties\n    pluginName: 'multiDrag',\n    utils: {\n      /**\r\n       * Selects the provided multi-drag item\r\n       * @param  {HTMLElement} el    The element to be selected\r\n       */\n      select: function select(el) {\n        var sortable = el.parentNode[expando];\n        if (!sortable || !sortable.options.multiDrag || ~multiDragElements.indexOf(el)) return;\n        if (multiDragSortable && multiDragSortable !== sortable) {\n          multiDragSortable.multiDrag._deselectMultiDrag();\n          multiDragSortable = sortable;\n        }\n        toggleClass(el, sortable.options.selectedClass, true);\n        multiDragElements.push(el);\n      },\n      /**\r\n       * Deselects the provided multi-drag item\r\n       * @param  {HTMLElement} el    The element to be deselected\r\n       */\n      deselect: function deselect(el) {\n        var sortable = el.parentNode[expando],\n          index = multiDragElements.indexOf(el);\n        if (!sortable || !sortable.options.multiDrag || !~index) return;\n        toggleClass(el, sortable.options.selectedClass, false);\n        multiDragElements.splice(index, 1);\n      }\n    },\n    eventProperties: function eventProperties() {\n      var _this3 = this;\n      var oldIndicies = [],\n        newIndicies = [];\n      multiDragElements.forEach(function (multiDragElement) {\n        oldIndicies.push({\n          multiDragElement: multiDragElement,\n          index: multiDragElement.sortableIndex\n        });\n\n        // multiDragElements will already be sorted if folding\n        var newIndex;\n        if (folding && multiDragElement !== dragEl$1) {\n          newIndex = -1;\n        } else if (folding) {\n          newIndex = index(multiDragElement, ':not(.' + _this3.options.selectedClass + ')');\n        } else {\n          newIndex = index(multiDragElement);\n        }\n        newIndicies.push({\n          multiDragElement: multiDragElement,\n          index: newIndex\n        });\n      });\n      return {\n        items: _toConsumableArray(multiDragElements),\n        clones: [].concat(multiDragClones),\n        oldIndicies: oldIndicies,\n        newIndicies: newIndicies\n      };\n    },\n    optionListeners: {\n      multiDragKey: function multiDragKey(key) {\n        key = key.toLowerCase();\n        if (key === 'ctrl') {\n          key = 'Control';\n        } else if (key.length > 1) {\n          key = key.charAt(0).toUpperCase() + key.substr(1);\n        }\n        return key;\n      }\n    }\n  });\n}\nfunction insertMultiDragElements(clonesInserted, rootEl) {\n  multiDragElements.forEach(function (multiDragElement, i) {\n    var target = rootEl.children[multiDragElement.sortableIndex + (clonesInserted ? Number(i) : 0)];\n    if (target) {\n      rootEl.insertBefore(multiDragElement, target);\n    } else {\n      rootEl.appendChild(multiDragElement);\n    }\n  });\n}\n\n/**\r\n * Insert multi-drag clones\r\n * @param  {[Boolean]} elementsInserted  Whether the multi-drag elements are inserted\r\n * @param  {HTMLElement} rootEl\r\n */\nfunction insertMultiDragClones(elementsInserted, rootEl) {\n  multiDragClones.forEach(function (clone, i) {\n    var target = rootEl.children[clone.sortableIndex + (elementsInserted ? Number(i) : 0)];\n    if (target) {\n      rootEl.insertBefore(clone, target);\n    } else {\n      rootEl.appendChild(clone);\n    }\n  });\n}\nfunction removeMultiDragElements() {\n  multiDragElements.forEach(function (multiDragElement) {\n    if (multiDragElement === dragEl$1) return;\n    multiDragElement.parentNode && multiDragElement.parentNode.removeChild(multiDragElement);\n  });\n}\nSortable.mount(new AutoScrollPlugin());\nSortable.mount(Remove, Revert);\nexport default Sortable;\nexport { MultiDragPlugin as MultiDrag, Sortable, SwapPlugin as Swap };", "map": {"version": 3, "names": ["ownKeys", "object", "enumerableOnly", "keys", "Object", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "enumerable", "push", "apply", "_objectSpread2", "target", "i", "arguments", "length", "source", "for<PERSON>ach", "key", "_defineProperty", "getOwnPropertyDescriptors", "defineProperties", "defineProperty", "_typeof", "obj", "Symbol", "iterator", "constructor", "prototype", "value", "configurable", "writable", "_extends", "assign", "hasOwnProperty", "call", "_objectWithoutPropertiesLoose", "excluded", "sourceKeys", "indexOf", "_objectWithoutProperties", "sourceSymbolKeys", "propertyIsEnumerable", "_toConsumableArray", "arr", "_arrayWithoutHoles", "_iterableToArray", "_unsupportedIterableToArray", "_nonIterableSpread", "Array", "isArray", "_arrayLikeToArray", "iter", "from", "o", "minLen", "n", "toString", "slice", "name", "test", "len", "arr2", "TypeError", "version", "userAgent", "pattern", "window", "navigator", "match", "IE11OrLess", "Edge", "FireFox", "Safari", "IOS", "ChromeForAndroid", "captureMode", "capture", "passive", "on", "el", "event", "fn", "addEventListener", "off", "removeEventListener", "matches", "selector", "substring", "msMatchesSelector", "webkitMatchesSelector", "_", "getParentOrHost", "host", "document", "nodeType", "parentNode", "closest", "ctx", "includeCTX", "R_SPACE", "toggleClass", "state", "classList", "className", "replace", "css", "prop", "val", "style", "defaultView", "getComputedStyle", "currentStyle", "matrix", "selfOnly", "appliedTransforms", "transform", "matrixFn", "DOMMatrix", "WebKitCSSMatrix", "CSSMatrix", "MSCSSMatrix", "find", "tagName", "list", "getElementsByTagName", "getWindowScrollingElement", "scrollingElement", "documentElement", "getRect", "relativeToContainingBlock", "relativeToNonStaticParent", "undoScale", "container", "getBoundingClientRect", "elRect", "top", "left", "bottom", "right", "height", "width", "innerHeight", "innerWidth", "containerRect", "parseInt", "elMatrix", "scaleX", "a", "scaleY", "d", "isScrolledPast", "elSide", "parentSide", "parent", "getParentAutoScrollElement", "elSideVal", "parentSideVal", "visible", "<PERSON><PERSON><PERSON><PERSON>", "childNum", "options", "includeDragEl", "<PERSON><PERSON><PERSON><PERSON>", "children", "display", "Sortable", "ghost", "dragged", "draggable", "<PERSON><PERSON><PERSON><PERSON>", "last", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "previousElementSibling", "index", "nodeName", "toUpperCase", "clone", "getRelativeScrollOffset", "offsetLeft", "offsetTop", "winScroller", "scrollLeft", "scrollTop", "indexOfObject", "Number", "includeSelf", "elem", "gotSelf", "clientWidth", "scrollWidth", "clientHeight", "scrollHeight", "elemCSS", "overflowX", "overflowY", "body", "extend", "dst", "src", "isRectEqual", "rect1", "rect2", "Math", "round", "_throttleTimeout", "throttle", "callback", "ms", "args", "_this", "setTimeout", "cancelThrottle", "clearTimeout", "scrollBy", "x", "y", "Polymer", "$", "j<PERSON><PERSON><PERSON>", "Zepto", "dom", "cloneNode", "setRect", "rect", "unsetRect", "getChildContainingRectFromElement", "ghostEl", "child", "_rect$left", "_rect$top", "_rect$right", "_rect$bottom", "animated", "childRect", "min", "Infinity", "max", "expando", "Date", "getTime", "AnimationStateManager", "animationStates", "animationCallbackId", "captureAnimationState", "animation", "fromRect", "thisAnimationDuration", "childMatrix", "f", "e", "addAnimationState", "removeAnimationState", "splice", "animateAll", "animating", "animationTime", "time", "toRect", "prevFromRect", "prevToRect", "animatingRect", "targetMatrix", "calculateRealTime", "animate", "animationResetTimer", "currentRect", "duration", "translateX", "translateY", "animatingX", "animatingY", "forRepaintDummy", "repaint", "easing", "offsetWidth", "sqrt", "pow", "plugins", "defaults", "initializeByDefault", "Plugin<PERSON>anager", "mount", "plugin", "option", "p", "pluginName", "concat", "pluginEvent", "eventName", "sortable", "evt", "eventCanceled", "cancel", "eventNameGlobal", "initializePlugins", "initialized", "modified", "modifyOption", "getEventProperties", "eventProperties", "modifiedValue", "optionListeners", "dispatchEvent", "_ref", "rootEl", "targetEl", "cloneEl", "toEl", "fromEl", "oldIndex", "newIndex", "oldDraggableIndex", "newDraggableIndex", "originalEvent", "putSortable", "extraEventProperties", "onName", "char<PERSON>t", "substr", "CustomEvent", "bubbles", "cancelable", "createEvent", "initEvent", "to", "item", "pullMode", "lastPutMode", "undefined", "allEventProperties", "_excluded", "data", "bind", "dragEl", "parentEl", "nextEl", "lastDownEl", "cloneHidden", "dragStarted", "moved", "activeSortable", "active", "hideGhostForTarget", "_hideGhostForTarget", "unhideGhostForTarget", "_unhideGhostForTarget", "cloneNowHidden", "cloneNowShown", "dispatchSortableEvent", "_dispatchEvent", "info", "activeGroup", "awaitingDragStarted", "ignoreNextClick", "sortables", "tapEvt", "touchEvt", "lastDx", "lastDy", "tapDistanceLeft", "tapDistanceTop", "last<PERSON><PERSON><PERSON>", "lastDirection", "pastFirstInvertThresh", "isCircumstantialInvert", "targetMoveDistance", "ghostRelativeParent", "ghostRelativeParentInitialScroll", "_silent", "savedInputChecked", "documentExists", "PositionGhostAbsolutely", "CSSFloatProperty", "supportDraggable", "createElement", "supportCssPointerEvents", "cssText", "pointerEvents", "_detectDirection", "elCSS", "<PERSON><PERSON><PERSON><PERSON>", "paddingLeft", "paddingRight", "borderLeftWidth", "borderRightWidth", "child1", "child2", "firstChildCSS", "secondChildCSS", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "marginLeft", "marginRight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "flexDirection", "gridTemplateColumns", "split", "touchingSideChild2", "clear", "_dragElInRowColumn", "dragRect", "targetRect", "vertical", "dragElS1Opp", "dragElS2Opp", "dragElOppLength", "targetS1Opp", "targetS2Opp", "targetOppLength", "_detectNearestEmptySortable", "ret", "some", "threshold", "emptyInsertThreshold", "insideHorizontally", "insideVertically", "_prepareGroup", "toFn", "pull", "sameGroup", "group", "otherGroup", "join", "originalGroup", "checkPull", "checkPut", "put", "revertClone", "preventDefault", "stopPropagation", "stopImmediatePropagation", "nearestEmptyInsertDetectEvent", "touches", "nearest", "clientX", "clientY", "_onDragOver", "_checkOutsideTargetEl", "_isOutsideThisEl", "sort", "disabled", "store", "handle", "swapThreshold", "invertSwap", "invertedSwapThreshold", "removeCloneOnHide", "direction", "ghostClass", "chosenClass", "dragClass", "ignore", "preventOnFilter", "setData", "dataTransfer", "textContent", "dropBubble", "dragoverBubble", "dataIdAttr", "delay", "delayOnTouchOnly", "touchStartThreshold", "devicePixelRatio", "force<PERSON><PERSON><PERSON>", "fallbackClass", "fallbackOnBody", "fallbackTolerance", "fallbackOffset", "supportPointer", "nativeDraggable", "_onTapStart", "get", "contains", "_getDirection", "type", "touch", "pointerType", "originalTarget", "shadowRoot", "path", "<PERSON><PERSON><PERSON>", "_saveInputCheckedState", "button", "isContentEditable", "criteria", "trim", "_prepareDragStart", "ownerDocument", "dragStartFn", "nextS<PERSON>ling", "_lastX", "_lastY", "_onDrop", "_disableDelayedDragEvents", "_triggerDragStart", "_disableDraggable", "_disableDelayedDrag", "_delayedDragTouchMoveHandler", "_dragStartTimer", "abs", "floor", "_onTouchMove", "_onDragStart", "selection", "_nextTick", "empty", "getSelection", "removeAllRanges", "err", "_dragStarted", "fallback", "_appendGhost", "_nulling", "_emulateDragOver", "elementFromPoint", "inserted", "ghostMatrix", "relativeScrollOffset", "dx", "dy", "b", "c", "cssMatrix", "append<PERSON><PERSON><PERSON>", "removeAttribute", "_hideClone", "cloneId", "insertBefore", "_loopId", "setInterval", "effectAllowed", "_dragStartId", "revert", "isOwner", "canSort", "fromSortable", "completedFired", "dragOverEvent", "extra", "axis", "completed", "onMove", "after", "_onMove", "changed", "insertion", "_showClone", "_ignoreWhileAnimating", "el<PERSON>ast<PERSON><PERSON><PERSON>", "_ghostIsLast", "_ghost<PERSON>sFirst", "<PERSON><PERSON><PERSON><PERSON>", "targetBeforeFirstSwap", "differentLevel", "differentRowCol", "side1", "scrolledPastTop", "scrollBefore", "_getSwapDirection", "sibling", "dragIndex", "nextElement<PERSON><PERSON>ling", "moveVector", "_unsilent", "_offMoveEvents", "_offUpEvents", "clearInterval", "_cancelNextTick", "<PERSON><PERSON><PERSON><PERSON>", "save", "checked", "handleEvent", "_globalDragOver", "toArray", "order", "getAttribute", "_generateId", "useAnimation", "items", "id", "set", "closest$1", "destroy", "querySelectorAll", "dropEffect", "willInsertAfter", "onMoveFn", "retVal", "draggedRect", "related", "relatedRect", "firstElRect", "childContainingRect", "spacer", "lastElRect", "isLastTarget", "mouseOnAxis", "targetLength", "targetS1", "targetS2", "invert", "_getInsertDirection", "str", "href", "sum", "charCodeAt", "root", "inputs", "idx", "utils", "is", "nextTick", "cancelNextTick", "detectDirection", "element", "_len", "_key", "create", "autoScrolls", "scrollEl", "scrollRootEl", "scrolling", "lastAutoScrollX", "lastAutoScrollY", "touchEvt$1", "pointerElemChangedInterval", "AutoScrollPlugin", "AutoScroll", "scroll", "forceAutoScrollFallback", "scrollSensitivity", "scrollSpeed", "bubbleScroll", "_handleAutoScroll", "_handleFallbackAutoScroll", "dragOverCompleted", "_ref2", "dragOverBubble", "drop", "clearPointerElemChangedInterval", "clearAutoScrolls", "nulling", "autoScroll", "ogElemScroller", "newElem", "pid", "<PERSON><PERSON><PERSON><PERSON>", "sens", "speed", "scrollThisInstance", "scrollCustomFn", "scrollFn", "layersOut", "currentParent", "canScrollX", "canScrollY", "scrollPosX", "scrollPosY", "vx", "vy", "layer", "scrollOffsetY", "scrollOffsetX", "toSortable", "changedTouches", "onSpill", "<PERSON><PERSON>", "startIndex", "dragStart", "_ref3", "Remove", "_ref4", "parentSortable", "lastSwapEl", "SwapPlugin", "<PERSON><PERSON><PERSON>", "swapClass", "dragOverValid", "swap", "prevSwapEl", "swapNodes", "swapItem", "n1", "n2", "p1", "p2", "i1", "i2", "isEqualNode", "multiDragElements", "multiDragClones", "lastMultiDragSelect", "multiDragSortable", "initialFolding", "folding", "dragEl$1", "clonesFromRect", "clones<PERSON><PERSON><PERSON>", "MultiDragPlugin", "MultiDrag", "avoidImplicitDeselect", "_deselectMultiDrag", "_checkKeyDown", "_checkKeyUp", "selectedClass", "multiDragKey", "multiDragElement", "multiDragKeyDown", "isMultiDrag", "delayStartGlobal", "delayEnded", "setupClone", "sortableIndex", "insertMultiDragClones", "showClone", "<PERSON><PERSON><PERSON>", "_ref5", "dragStartGlobal", "_ref6", "multiDrag", "_ref7", "_this2", "removeMultiDragElements", "dragOver", "_ref8", "_ref9", "insertMultiDragElements", "_ref10", "dragRectAbsolute", "clonesHiddenBefore", "dragOverAnimationCapture", "_ref11", "dragMatrix", "dragOverAnimationComplete", "_ref12", "shift<PERSON>ey", "lastIndex", "currentIndex", "multiDragIndex", "update", "nullingGlobal", "destroyGlobal", "shift", "select", "deselect", "_this3", "oldIndicies", "newIndicies", "clones", "toLowerCase", "clonesInserted", "elementsInserted"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/sortablejs@1.15.3/node_modules/sortablejs/modular/sortable.esm.js"], "sourcesContent": ["/**!\n * Sortable 1.15.3\n * <AUTHOR>   <<EMAIL>>\n * <AUTHOR>    <<EMAIL>>\n * @license MIT\n */\nfunction ownKeys(object, enumerableOnly) {\n  var keys = Object.keys(object);\n  if (Object.getOwnPropertySymbols) {\n    var symbols = Object.getOwnPropertySymbols(object);\n    if (enumerableOnly) {\n      symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n    }\n    keys.push.apply(keys, symbols);\n  }\n  return keys;\n}\nfunction _objectSpread2(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i] != null ? arguments[i] : {};\n    if (i % 2) {\n      ownKeys(Object(source), true).forEach(function (key) {\n        _defineProperty(target, key, source[key]);\n      });\n    } else if (Object.getOwnPropertyDescriptors) {\n      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n    } else {\n      ownKeys(Object(source)).forEach(function (key) {\n        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n      });\n    }\n  }\n  return target;\n}\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n    _typeof = function (obj) {\n      return typeof obj;\n    };\n  } else {\n    _typeof = function (obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n    };\n  }\n  return _typeof(obj);\n}\nfunction _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nfunction _toConsumableArray(arr) {\n  return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n}\nfunction _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n}\nfunction _iterableToArray(iter) {\n  if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n}\nfunction _unsupportedIterableToArray(o, minLen) {\n  if (!o) return;\n  if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n  var n = Object.prototype.toString.call(o).slice(8, -1);\n  if (n === \"Object\" && o.constructor) n = o.constructor.name;\n  if (n === \"Map\" || n === \"Set\") return Array.from(o);\n  if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n}\nfunction _arrayLikeToArray(arr, len) {\n  if (len == null || len > arr.length) len = arr.length;\n  for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n  return arr2;\n}\nfunction _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\n\nvar version = \"1.15.3\";\n\nfunction userAgent(pattern) {\n  if (typeof window !== 'undefined' && window.navigator) {\n    return !! /*@__PURE__*/navigator.userAgent.match(pattern);\n  }\n}\nvar IE11OrLess = userAgent(/(?:Trident.*rv[ :]?11\\.|msie|iemobile|Windows Phone)/i);\nvar Edge = userAgent(/Edge/i);\nvar FireFox = userAgent(/firefox/i);\nvar Safari = userAgent(/safari/i) && !userAgent(/chrome/i) && !userAgent(/android/i);\nvar IOS = userAgent(/iP(ad|od|hone)/i);\nvar ChromeForAndroid = userAgent(/chrome/i) && userAgent(/android/i);\n\nvar captureMode = {\n  capture: false,\n  passive: false\n};\nfunction on(el, event, fn) {\n  el.addEventListener(event, fn, !IE11OrLess && captureMode);\n}\nfunction off(el, event, fn) {\n  el.removeEventListener(event, fn, !IE11OrLess && captureMode);\n}\nfunction matches( /**HTMLElement*/el, /**String*/selector) {\n  if (!selector) return;\n  selector[0] === '>' && (selector = selector.substring(1));\n  if (el) {\n    try {\n      if (el.matches) {\n        return el.matches(selector);\n      } else if (el.msMatchesSelector) {\n        return el.msMatchesSelector(selector);\n      } else if (el.webkitMatchesSelector) {\n        return el.webkitMatchesSelector(selector);\n      }\n    } catch (_) {\n      return false;\n    }\n  }\n  return false;\n}\nfunction getParentOrHost(el) {\n  return el.host && el !== document && el.host.nodeType ? el.host : el.parentNode;\n}\nfunction closest( /**HTMLElement*/el, /**String*/selector, /**HTMLElement*/ctx, includeCTX) {\n  if (el) {\n    ctx = ctx || document;\n    do {\n      if (selector != null && (selector[0] === '>' ? el.parentNode === ctx && matches(el, selector) : matches(el, selector)) || includeCTX && el === ctx) {\n        return el;\n      }\n      if (el === ctx) break;\n      /* jshint boss:true */\n    } while (el = getParentOrHost(el));\n  }\n  return null;\n}\nvar R_SPACE = /\\s+/g;\nfunction toggleClass(el, name, state) {\n  if (el && name) {\n    if (el.classList) {\n      el.classList[state ? 'add' : 'remove'](name);\n    } else {\n      var className = (' ' + el.className + ' ').replace(R_SPACE, ' ').replace(' ' + name + ' ', ' ');\n      el.className = (className + (state ? ' ' + name : '')).replace(R_SPACE, ' ');\n    }\n  }\n}\nfunction css(el, prop, val) {\n  var style = el && el.style;\n  if (style) {\n    if (val === void 0) {\n      if (document.defaultView && document.defaultView.getComputedStyle) {\n        val = document.defaultView.getComputedStyle(el, '');\n      } else if (el.currentStyle) {\n        val = el.currentStyle;\n      }\n      return prop === void 0 ? val : val[prop];\n    } else {\n      if (!(prop in style) && prop.indexOf('webkit') === -1) {\n        prop = '-webkit-' + prop;\n      }\n      style[prop] = val + (typeof val === 'string' ? '' : 'px');\n    }\n  }\n}\nfunction matrix(el, selfOnly) {\n  var appliedTransforms = '';\n  if (typeof el === 'string') {\n    appliedTransforms = el;\n  } else {\n    do {\n      var transform = css(el, 'transform');\n      if (transform && transform !== 'none') {\n        appliedTransforms = transform + ' ' + appliedTransforms;\n      }\n      /* jshint boss:true */\n    } while (!selfOnly && (el = el.parentNode));\n  }\n  var matrixFn = window.DOMMatrix || window.WebKitCSSMatrix || window.CSSMatrix || window.MSCSSMatrix;\n  /*jshint -W056 */\n  return matrixFn && new matrixFn(appliedTransforms);\n}\nfunction find(ctx, tagName, iterator) {\n  if (ctx) {\n    var list = ctx.getElementsByTagName(tagName),\n      i = 0,\n      n = list.length;\n    if (iterator) {\n      for (; i < n; i++) {\n        iterator(list[i], i);\n      }\n    }\n    return list;\n  }\n  return [];\n}\nfunction getWindowScrollingElement() {\n  var scrollingElement = document.scrollingElement;\n  if (scrollingElement) {\n    return scrollingElement;\n  } else {\n    return document.documentElement;\n  }\n}\n\n/**\r\n * Returns the \"bounding client rect\" of given element\r\n * @param  {HTMLElement} el                       The element whose boundingClientRect is wanted\r\n * @param  {[Boolean]} relativeToContainingBlock  Whether the rect should be relative to the containing block of (including) the container\r\n * @param  {[Boolean]} relativeToNonStaticParent  Whether the rect should be relative to the relative parent of (including) the contaienr\r\n * @param  {[Boolean]} undoScale                  Whether the container's scale() should be undone\r\n * @param  {[HTMLElement]} container              The parent the element will be placed in\r\n * @return {Object}                               The boundingClientRect of el, with specified adjustments\r\n */\nfunction getRect(el, relativeToContainingBlock, relativeToNonStaticParent, undoScale, container) {\n  if (!el.getBoundingClientRect && el !== window) return;\n  var elRect, top, left, bottom, right, height, width;\n  if (el !== window && el.parentNode && el !== getWindowScrollingElement()) {\n    elRect = el.getBoundingClientRect();\n    top = elRect.top;\n    left = elRect.left;\n    bottom = elRect.bottom;\n    right = elRect.right;\n    height = elRect.height;\n    width = elRect.width;\n  } else {\n    top = 0;\n    left = 0;\n    bottom = window.innerHeight;\n    right = window.innerWidth;\n    height = window.innerHeight;\n    width = window.innerWidth;\n  }\n  if ((relativeToContainingBlock || relativeToNonStaticParent) && el !== window) {\n    // Adjust for translate()\n    container = container || el.parentNode;\n\n    // solves #1123 (see: https://stackoverflow.com/a/37953806/6088312)\n    // Not needed on <= IE11\n    if (!IE11OrLess) {\n      do {\n        if (container && container.getBoundingClientRect && (css(container, 'transform') !== 'none' || relativeToNonStaticParent && css(container, 'position') !== 'static')) {\n          var containerRect = container.getBoundingClientRect();\n\n          // Set relative to edges of padding box of container\n          top -= containerRect.top + parseInt(css(container, 'border-top-width'));\n          left -= containerRect.left + parseInt(css(container, 'border-left-width'));\n          bottom = top + elRect.height;\n          right = left + elRect.width;\n          break;\n        }\n        /* jshint boss:true */\n      } while (container = container.parentNode);\n    }\n  }\n  if (undoScale && el !== window) {\n    // Adjust for scale()\n    var elMatrix = matrix(container || el),\n      scaleX = elMatrix && elMatrix.a,\n      scaleY = elMatrix && elMatrix.d;\n    if (elMatrix) {\n      top /= scaleY;\n      left /= scaleX;\n      width /= scaleX;\n      height /= scaleY;\n      bottom = top + height;\n      right = left + width;\n    }\n  }\n  return {\n    top: top,\n    left: left,\n    bottom: bottom,\n    right: right,\n    width: width,\n    height: height\n  };\n}\n\n/**\r\n * Checks if a side of an element is scrolled past a side of its parents\r\n * @param  {HTMLElement}  el           The element who's side being scrolled out of view is in question\r\n * @param  {String}       elSide       Side of the element in question ('top', 'left', 'right', 'bottom')\r\n * @param  {String}       parentSide   Side of the parent in question ('top', 'left', 'right', 'bottom')\r\n * @return {HTMLElement}               The parent scroll element that the el's side is scrolled past, or null if there is no such element\r\n */\nfunction isScrolledPast(el, elSide, parentSide) {\n  var parent = getParentAutoScrollElement(el, true),\n    elSideVal = getRect(el)[elSide];\n\n  /* jshint boss:true */\n  while (parent) {\n    var parentSideVal = getRect(parent)[parentSide],\n      visible = void 0;\n    if (parentSide === 'top' || parentSide === 'left') {\n      visible = elSideVal >= parentSideVal;\n    } else {\n      visible = elSideVal <= parentSideVal;\n    }\n    if (!visible) return parent;\n    if (parent === getWindowScrollingElement()) break;\n    parent = getParentAutoScrollElement(parent, false);\n  }\n  return false;\n}\n\n/**\r\n * Gets nth child of el, ignoring hidden children, sortable's elements (does not ignore clone if it's visible)\r\n * and non-draggable elements\r\n * @param  {HTMLElement} el       The parent element\r\n * @param  {Number} childNum      The index of the child\r\n * @param  {Object} options       Parent Sortable's options\r\n * @return {HTMLElement}          The child at index childNum, or null if not found\r\n */\nfunction getChild(el, childNum, options, includeDragEl) {\n  var currentChild = 0,\n    i = 0,\n    children = el.children;\n  while (i < children.length) {\n    if (children[i].style.display !== 'none' && children[i] !== Sortable.ghost && (includeDragEl || children[i] !== Sortable.dragged) && closest(children[i], options.draggable, el, false)) {\n      if (currentChild === childNum) {\n        return children[i];\n      }\n      currentChild++;\n    }\n    i++;\n  }\n  return null;\n}\n\n/**\r\n * Gets the last child in the el, ignoring ghostEl or invisible elements (clones)\r\n * @param  {HTMLElement} el       Parent element\r\n * @param  {selector} selector    Any other elements that should be ignored\r\n * @return {HTMLElement}          The last child, ignoring ghostEl\r\n */\nfunction lastChild(el, selector) {\n  var last = el.lastElementChild;\n  while (last && (last === Sortable.ghost || css(last, 'display') === 'none' || selector && !matches(last, selector))) {\n    last = last.previousElementSibling;\n  }\n  return last || null;\n}\n\n/**\r\n * Returns the index of an element within its parent for a selected set of\r\n * elements\r\n * @param  {HTMLElement} el\r\n * @param  {selector} selector\r\n * @return {number}\r\n */\nfunction index(el, selector) {\n  var index = 0;\n  if (!el || !el.parentNode) {\n    return -1;\n  }\n\n  /* jshint boss:true */\n  while (el = el.previousElementSibling) {\n    if (el.nodeName.toUpperCase() !== 'TEMPLATE' && el !== Sortable.clone && (!selector || matches(el, selector))) {\n      index++;\n    }\n  }\n  return index;\n}\n\n/**\r\n * Returns the scroll offset of the given element, added with all the scroll offsets of parent elements.\r\n * The value is returned in real pixels.\r\n * @param  {HTMLElement} el\r\n * @return {Array}             Offsets in the format of [left, top]\r\n */\nfunction getRelativeScrollOffset(el) {\n  var offsetLeft = 0,\n    offsetTop = 0,\n    winScroller = getWindowScrollingElement();\n  if (el) {\n    do {\n      var elMatrix = matrix(el),\n        scaleX = elMatrix.a,\n        scaleY = elMatrix.d;\n      offsetLeft += el.scrollLeft * scaleX;\n      offsetTop += el.scrollTop * scaleY;\n    } while (el !== winScroller && (el = el.parentNode));\n  }\n  return [offsetLeft, offsetTop];\n}\n\n/**\r\n * Returns the index of the object within the given array\r\n * @param  {Array} arr   Array that may or may not hold the object\r\n * @param  {Object} obj  An object that has a key-value pair unique to and identical to a key-value pair in the object you want to find\r\n * @return {Number}      The index of the object in the array, or -1\r\n */\nfunction indexOfObject(arr, obj) {\n  for (var i in arr) {\n    if (!arr.hasOwnProperty(i)) continue;\n    for (var key in obj) {\n      if (obj.hasOwnProperty(key) && obj[key] === arr[i][key]) return Number(i);\n    }\n  }\n  return -1;\n}\nfunction getParentAutoScrollElement(el, includeSelf) {\n  // skip to window\n  if (!el || !el.getBoundingClientRect) return getWindowScrollingElement();\n  var elem = el;\n  var gotSelf = false;\n  do {\n    // we don't need to get elem css if it isn't even overflowing in the first place (performance)\n    if (elem.clientWidth < elem.scrollWidth || elem.clientHeight < elem.scrollHeight) {\n      var elemCSS = css(elem);\n      if (elem.clientWidth < elem.scrollWidth && (elemCSS.overflowX == 'auto' || elemCSS.overflowX == 'scroll') || elem.clientHeight < elem.scrollHeight && (elemCSS.overflowY == 'auto' || elemCSS.overflowY == 'scroll')) {\n        if (!elem.getBoundingClientRect || elem === document.body) return getWindowScrollingElement();\n        if (gotSelf || includeSelf) return elem;\n        gotSelf = true;\n      }\n    }\n    /* jshint boss:true */\n  } while (elem = elem.parentNode);\n  return getWindowScrollingElement();\n}\nfunction extend(dst, src) {\n  if (dst && src) {\n    for (var key in src) {\n      if (src.hasOwnProperty(key)) {\n        dst[key] = src[key];\n      }\n    }\n  }\n  return dst;\n}\nfunction isRectEqual(rect1, rect2) {\n  return Math.round(rect1.top) === Math.round(rect2.top) && Math.round(rect1.left) === Math.round(rect2.left) && Math.round(rect1.height) === Math.round(rect2.height) && Math.round(rect1.width) === Math.round(rect2.width);\n}\nvar _throttleTimeout;\nfunction throttle(callback, ms) {\n  return function () {\n    if (!_throttleTimeout) {\n      var args = arguments,\n        _this = this;\n      if (args.length === 1) {\n        callback.call(_this, args[0]);\n      } else {\n        callback.apply(_this, args);\n      }\n      _throttleTimeout = setTimeout(function () {\n        _throttleTimeout = void 0;\n      }, ms);\n    }\n  };\n}\nfunction cancelThrottle() {\n  clearTimeout(_throttleTimeout);\n  _throttleTimeout = void 0;\n}\nfunction scrollBy(el, x, y) {\n  el.scrollLeft += x;\n  el.scrollTop += y;\n}\nfunction clone(el) {\n  var Polymer = window.Polymer;\n  var $ = window.jQuery || window.Zepto;\n  if (Polymer && Polymer.dom) {\n    return Polymer.dom(el).cloneNode(true);\n  } else if ($) {\n    return $(el).clone(true)[0];\n  } else {\n    return el.cloneNode(true);\n  }\n}\nfunction setRect(el, rect) {\n  css(el, 'position', 'absolute');\n  css(el, 'top', rect.top);\n  css(el, 'left', rect.left);\n  css(el, 'width', rect.width);\n  css(el, 'height', rect.height);\n}\nfunction unsetRect(el) {\n  css(el, 'position', '');\n  css(el, 'top', '');\n  css(el, 'left', '');\n  css(el, 'width', '');\n  css(el, 'height', '');\n}\nfunction getChildContainingRectFromElement(container, options, ghostEl) {\n  var rect = {};\n  Array.from(container.children).forEach(function (child) {\n    var _rect$left, _rect$top, _rect$right, _rect$bottom;\n    if (!closest(child, options.draggable, container, false) || child.animated || child === ghostEl) return;\n    var childRect = getRect(child);\n    rect.left = Math.min((_rect$left = rect.left) !== null && _rect$left !== void 0 ? _rect$left : Infinity, childRect.left);\n    rect.top = Math.min((_rect$top = rect.top) !== null && _rect$top !== void 0 ? _rect$top : Infinity, childRect.top);\n    rect.right = Math.max((_rect$right = rect.right) !== null && _rect$right !== void 0 ? _rect$right : -Infinity, childRect.right);\n    rect.bottom = Math.max((_rect$bottom = rect.bottom) !== null && _rect$bottom !== void 0 ? _rect$bottom : -Infinity, childRect.bottom);\n  });\n  rect.width = rect.right - rect.left;\n  rect.height = rect.bottom - rect.top;\n  rect.x = rect.left;\n  rect.y = rect.top;\n  return rect;\n}\nvar expando = 'Sortable' + new Date().getTime();\n\nfunction AnimationStateManager() {\n  var animationStates = [],\n    animationCallbackId;\n  return {\n    captureAnimationState: function captureAnimationState() {\n      animationStates = [];\n      if (!this.options.animation) return;\n      var children = [].slice.call(this.el.children);\n      children.forEach(function (child) {\n        if (css(child, 'display') === 'none' || child === Sortable.ghost) return;\n        animationStates.push({\n          target: child,\n          rect: getRect(child)\n        });\n        var fromRect = _objectSpread2({}, animationStates[animationStates.length - 1].rect);\n\n        // If animating: compensate for current animation\n        if (child.thisAnimationDuration) {\n          var childMatrix = matrix(child, true);\n          if (childMatrix) {\n            fromRect.top -= childMatrix.f;\n            fromRect.left -= childMatrix.e;\n          }\n        }\n        child.fromRect = fromRect;\n      });\n    },\n    addAnimationState: function addAnimationState(state) {\n      animationStates.push(state);\n    },\n    removeAnimationState: function removeAnimationState(target) {\n      animationStates.splice(indexOfObject(animationStates, {\n        target: target\n      }), 1);\n    },\n    animateAll: function animateAll(callback) {\n      var _this = this;\n      if (!this.options.animation) {\n        clearTimeout(animationCallbackId);\n        if (typeof callback === 'function') callback();\n        return;\n      }\n      var animating = false,\n        animationTime = 0;\n      animationStates.forEach(function (state) {\n        var time = 0,\n          target = state.target,\n          fromRect = target.fromRect,\n          toRect = getRect(target),\n          prevFromRect = target.prevFromRect,\n          prevToRect = target.prevToRect,\n          animatingRect = state.rect,\n          targetMatrix = matrix(target, true);\n        if (targetMatrix) {\n          // Compensate for current animation\n          toRect.top -= targetMatrix.f;\n          toRect.left -= targetMatrix.e;\n        }\n        target.toRect = toRect;\n        if (target.thisAnimationDuration) {\n          // Could also check if animatingRect is between fromRect and toRect\n          if (isRectEqual(prevFromRect, toRect) && !isRectEqual(fromRect, toRect) &&\n          // Make sure animatingRect is on line between toRect & fromRect\n          (animatingRect.top - toRect.top) / (animatingRect.left - toRect.left) === (fromRect.top - toRect.top) / (fromRect.left - toRect.left)) {\n            // If returning to same place as started from animation and on same axis\n            time = calculateRealTime(animatingRect, prevFromRect, prevToRect, _this.options);\n          }\n        }\n\n        // if fromRect != toRect: animate\n        if (!isRectEqual(toRect, fromRect)) {\n          target.prevFromRect = fromRect;\n          target.prevToRect = toRect;\n          if (!time) {\n            time = _this.options.animation;\n          }\n          _this.animate(target, animatingRect, toRect, time);\n        }\n        if (time) {\n          animating = true;\n          animationTime = Math.max(animationTime, time);\n          clearTimeout(target.animationResetTimer);\n          target.animationResetTimer = setTimeout(function () {\n            target.animationTime = 0;\n            target.prevFromRect = null;\n            target.fromRect = null;\n            target.prevToRect = null;\n            target.thisAnimationDuration = null;\n          }, time);\n          target.thisAnimationDuration = time;\n        }\n      });\n      clearTimeout(animationCallbackId);\n      if (!animating) {\n        if (typeof callback === 'function') callback();\n      } else {\n        animationCallbackId = setTimeout(function () {\n          if (typeof callback === 'function') callback();\n        }, animationTime);\n      }\n      animationStates = [];\n    },\n    animate: function animate(target, currentRect, toRect, duration) {\n      if (duration) {\n        css(target, 'transition', '');\n        css(target, 'transform', '');\n        var elMatrix = matrix(this.el),\n          scaleX = elMatrix && elMatrix.a,\n          scaleY = elMatrix && elMatrix.d,\n          translateX = (currentRect.left - toRect.left) / (scaleX || 1),\n          translateY = (currentRect.top - toRect.top) / (scaleY || 1);\n        target.animatingX = !!translateX;\n        target.animatingY = !!translateY;\n        css(target, 'transform', 'translate3d(' + translateX + 'px,' + translateY + 'px,0)');\n        this.forRepaintDummy = repaint(target); // repaint\n\n        css(target, 'transition', 'transform ' + duration + 'ms' + (this.options.easing ? ' ' + this.options.easing : ''));\n        css(target, 'transform', 'translate3d(0,0,0)');\n        typeof target.animated === 'number' && clearTimeout(target.animated);\n        target.animated = setTimeout(function () {\n          css(target, 'transition', '');\n          css(target, 'transform', '');\n          target.animated = false;\n          target.animatingX = false;\n          target.animatingY = false;\n        }, duration);\n      }\n    }\n  };\n}\nfunction repaint(target) {\n  return target.offsetWidth;\n}\nfunction calculateRealTime(animatingRect, fromRect, toRect, options) {\n  return Math.sqrt(Math.pow(fromRect.top - animatingRect.top, 2) + Math.pow(fromRect.left - animatingRect.left, 2)) / Math.sqrt(Math.pow(fromRect.top - toRect.top, 2) + Math.pow(fromRect.left - toRect.left, 2)) * options.animation;\n}\n\nvar plugins = [];\nvar defaults = {\n  initializeByDefault: true\n};\nvar PluginManager = {\n  mount: function mount(plugin) {\n    // Set default static properties\n    for (var option in defaults) {\n      if (defaults.hasOwnProperty(option) && !(option in plugin)) {\n        plugin[option] = defaults[option];\n      }\n    }\n    plugins.forEach(function (p) {\n      if (p.pluginName === plugin.pluginName) {\n        throw \"Sortable: Cannot mount plugin \".concat(plugin.pluginName, \" more than once\");\n      }\n    });\n    plugins.push(plugin);\n  },\n  pluginEvent: function pluginEvent(eventName, sortable, evt) {\n    var _this = this;\n    this.eventCanceled = false;\n    evt.cancel = function () {\n      _this.eventCanceled = true;\n    };\n    var eventNameGlobal = eventName + 'Global';\n    plugins.forEach(function (plugin) {\n      if (!sortable[plugin.pluginName]) return;\n      // Fire global events if it exists in this sortable\n      if (sortable[plugin.pluginName][eventNameGlobal]) {\n        sortable[plugin.pluginName][eventNameGlobal](_objectSpread2({\n          sortable: sortable\n        }, evt));\n      }\n\n      // Only fire plugin event if plugin is enabled in this sortable,\n      // and plugin has event defined\n      if (sortable.options[plugin.pluginName] && sortable[plugin.pluginName][eventName]) {\n        sortable[plugin.pluginName][eventName](_objectSpread2({\n          sortable: sortable\n        }, evt));\n      }\n    });\n  },\n  initializePlugins: function initializePlugins(sortable, el, defaults, options) {\n    plugins.forEach(function (plugin) {\n      var pluginName = plugin.pluginName;\n      if (!sortable.options[pluginName] && !plugin.initializeByDefault) return;\n      var initialized = new plugin(sortable, el, sortable.options);\n      initialized.sortable = sortable;\n      initialized.options = sortable.options;\n      sortable[pluginName] = initialized;\n\n      // Add default options from plugin\n      _extends(defaults, initialized.defaults);\n    });\n    for (var option in sortable.options) {\n      if (!sortable.options.hasOwnProperty(option)) continue;\n      var modified = this.modifyOption(sortable, option, sortable.options[option]);\n      if (typeof modified !== 'undefined') {\n        sortable.options[option] = modified;\n      }\n    }\n  },\n  getEventProperties: function getEventProperties(name, sortable) {\n    var eventProperties = {};\n    plugins.forEach(function (plugin) {\n      if (typeof plugin.eventProperties !== 'function') return;\n      _extends(eventProperties, plugin.eventProperties.call(sortable[plugin.pluginName], name));\n    });\n    return eventProperties;\n  },\n  modifyOption: function modifyOption(sortable, name, value) {\n    var modifiedValue;\n    plugins.forEach(function (plugin) {\n      // Plugin must exist on the Sortable\n      if (!sortable[plugin.pluginName]) return;\n\n      // If static option listener exists for this option, call in the context of the Sortable's instance of this plugin\n      if (plugin.optionListeners && typeof plugin.optionListeners[name] === 'function') {\n        modifiedValue = plugin.optionListeners[name].call(sortable[plugin.pluginName], value);\n      }\n    });\n    return modifiedValue;\n  }\n};\n\nfunction dispatchEvent(_ref) {\n  var sortable = _ref.sortable,\n    rootEl = _ref.rootEl,\n    name = _ref.name,\n    targetEl = _ref.targetEl,\n    cloneEl = _ref.cloneEl,\n    toEl = _ref.toEl,\n    fromEl = _ref.fromEl,\n    oldIndex = _ref.oldIndex,\n    newIndex = _ref.newIndex,\n    oldDraggableIndex = _ref.oldDraggableIndex,\n    newDraggableIndex = _ref.newDraggableIndex,\n    originalEvent = _ref.originalEvent,\n    putSortable = _ref.putSortable,\n    extraEventProperties = _ref.extraEventProperties;\n  sortable = sortable || rootEl && rootEl[expando];\n  if (!sortable) return;\n  var evt,\n    options = sortable.options,\n    onName = 'on' + name.charAt(0).toUpperCase() + name.substr(1);\n  // Support for new CustomEvent feature\n  if (window.CustomEvent && !IE11OrLess && !Edge) {\n    evt = new CustomEvent(name, {\n      bubbles: true,\n      cancelable: true\n    });\n  } else {\n    evt = document.createEvent('Event');\n    evt.initEvent(name, true, true);\n  }\n  evt.to = toEl || rootEl;\n  evt.from = fromEl || rootEl;\n  evt.item = targetEl || rootEl;\n  evt.clone = cloneEl;\n  evt.oldIndex = oldIndex;\n  evt.newIndex = newIndex;\n  evt.oldDraggableIndex = oldDraggableIndex;\n  evt.newDraggableIndex = newDraggableIndex;\n  evt.originalEvent = originalEvent;\n  evt.pullMode = putSortable ? putSortable.lastPutMode : undefined;\n  var allEventProperties = _objectSpread2(_objectSpread2({}, extraEventProperties), PluginManager.getEventProperties(name, sortable));\n  for (var option in allEventProperties) {\n    evt[option] = allEventProperties[option];\n  }\n  if (rootEl) {\n    rootEl.dispatchEvent(evt);\n  }\n  if (options[onName]) {\n    options[onName].call(sortable, evt);\n  }\n}\n\nvar _excluded = [\"evt\"];\nvar pluginEvent = function pluginEvent(eventName, sortable) {\n  var _ref = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {},\n    originalEvent = _ref.evt,\n    data = _objectWithoutProperties(_ref, _excluded);\n  PluginManager.pluginEvent.bind(Sortable)(eventName, sortable, _objectSpread2({\n    dragEl: dragEl,\n    parentEl: parentEl,\n    ghostEl: ghostEl,\n    rootEl: rootEl,\n    nextEl: nextEl,\n    lastDownEl: lastDownEl,\n    cloneEl: cloneEl,\n    cloneHidden: cloneHidden,\n    dragStarted: moved,\n    putSortable: putSortable,\n    activeSortable: Sortable.active,\n    originalEvent: originalEvent,\n    oldIndex: oldIndex,\n    oldDraggableIndex: oldDraggableIndex,\n    newIndex: newIndex,\n    newDraggableIndex: newDraggableIndex,\n    hideGhostForTarget: _hideGhostForTarget,\n    unhideGhostForTarget: _unhideGhostForTarget,\n    cloneNowHidden: function cloneNowHidden() {\n      cloneHidden = true;\n    },\n    cloneNowShown: function cloneNowShown() {\n      cloneHidden = false;\n    },\n    dispatchSortableEvent: function dispatchSortableEvent(name) {\n      _dispatchEvent({\n        sortable: sortable,\n        name: name,\n        originalEvent: originalEvent\n      });\n    }\n  }, data));\n};\nfunction _dispatchEvent(info) {\n  dispatchEvent(_objectSpread2({\n    putSortable: putSortable,\n    cloneEl: cloneEl,\n    targetEl: dragEl,\n    rootEl: rootEl,\n    oldIndex: oldIndex,\n    oldDraggableIndex: oldDraggableIndex,\n    newIndex: newIndex,\n    newDraggableIndex: newDraggableIndex\n  }, info));\n}\nvar dragEl,\n  parentEl,\n  ghostEl,\n  rootEl,\n  nextEl,\n  lastDownEl,\n  cloneEl,\n  cloneHidden,\n  oldIndex,\n  newIndex,\n  oldDraggableIndex,\n  newDraggableIndex,\n  activeGroup,\n  putSortable,\n  awaitingDragStarted = false,\n  ignoreNextClick = false,\n  sortables = [],\n  tapEvt,\n  touchEvt,\n  lastDx,\n  lastDy,\n  tapDistanceLeft,\n  tapDistanceTop,\n  moved,\n  lastTarget,\n  lastDirection,\n  pastFirstInvertThresh = false,\n  isCircumstantialInvert = false,\n  targetMoveDistance,\n  // For positioning ghost absolutely\n  ghostRelativeParent,\n  ghostRelativeParentInitialScroll = [],\n  // (left, top)\n\n  _silent = false,\n  savedInputChecked = [];\n\n/** @const */\nvar documentExists = typeof document !== 'undefined',\n  PositionGhostAbsolutely = IOS,\n  CSSFloatProperty = Edge || IE11OrLess ? 'cssFloat' : 'float',\n  // This will not pass for IE9, because IE9 DnD only works on anchors\n  supportDraggable = documentExists && !ChromeForAndroid && !IOS && 'draggable' in document.createElement('div'),\n  supportCssPointerEvents = function () {\n    if (!documentExists) return;\n    // false when <= IE11\n    if (IE11OrLess) {\n      return false;\n    }\n    var el = document.createElement('x');\n    el.style.cssText = 'pointer-events:auto';\n    return el.style.pointerEvents === 'auto';\n  }(),\n  _detectDirection = function _detectDirection(el, options) {\n    var elCSS = css(el),\n      elWidth = parseInt(elCSS.width) - parseInt(elCSS.paddingLeft) - parseInt(elCSS.paddingRight) - parseInt(elCSS.borderLeftWidth) - parseInt(elCSS.borderRightWidth),\n      child1 = getChild(el, 0, options),\n      child2 = getChild(el, 1, options),\n      firstChildCSS = child1 && css(child1),\n      secondChildCSS = child2 && css(child2),\n      firstChildWidth = firstChildCSS && parseInt(firstChildCSS.marginLeft) + parseInt(firstChildCSS.marginRight) + getRect(child1).width,\n      secondChildWidth = secondChildCSS && parseInt(secondChildCSS.marginLeft) + parseInt(secondChildCSS.marginRight) + getRect(child2).width;\n    if (elCSS.display === 'flex') {\n      return elCSS.flexDirection === 'column' || elCSS.flexDirection === 'column-reverse' ? 'vertical' : 'horizontal';\n    }\n    if (elCSS.display === 'grid') {\n      return elCSS.gridTemplateColumns.split(' ').length <= 1 ? 'vertical' : 'horizontal';\n    }\n    if (child1 && firstChildCSS[\"float\"] && firstChildCSS[\"float\"] !== 'none') {\n      var touchingSideChild2 = firstChildCSS[\"float\"] === 'left' ? 'left' : 'right';\n      return child2 && (secondChildCSS.clear === 'both' || secondChildCSS.clear === touchingSideChild2) ? 'vertical' : 'horizontal';\n    }\n    return child1 && (firstChildCSS.display === 'block' || firstChildCSS.display === 'flex' || firstChildCSS.display === 'table' || firstChildCSS.display === 'grid' || firstChildWidth >= elWidth && elCSS[CSSFloatProperty] === 'none' || child2 && elCSS[CSSFloatProperty] === 'none' && firstChildWidth + secondChildWidth > elWidth) ? 'vertical' : 'horizontal';\n  },\n  _dragElInRowColumn = function _dragElInRowColumn(dragRect, targetRect, vertical) {\n    var dragElS1Opp = vertical ? dragRect.left : dragRect.top,\n      dragElS2Opp = vertical ? dragRect.right : dragRect.bottom,\n      dragElOppLength = vertical ? dragRect.width : dragRect.height,\n      targetS1Opp = vertical ? targetRect.left : targetRect.top,\n      targetS2Opp = vertical ? targetRect.right : targetRect.bottom,\n      targetOppLength = vertical ? targetRect.width : targetRect.height;\n    return dragElS1Opp === targetS1Opp || dragElS2Opp === targetS2Opp || dragElS1Opp + dragElOppLength / 2 === targetS1Opp + targetOppLength / 2;\n  },\n  /**\r\n   * Detects first nearest empty sortable to X and Y position using emptyInsertThreshold.\r\n   * @param  {Number} x      X position\r\n   * @param  {Number} y      Y position\r\n   * @return {HTMLElement}   Element of the first found nearest Sortable\r\n   */\n  _detectNearestEmptySortable = function _detectNearestEmptySortable(x, y) {\n    var ret;\n    sortables.some(function (sortable) {\n      var threshold = sortable[expando].options.emptyInsertThreshold;\n      if (!threshold || lastChild(sortable)) return;\n      var rect = getRect(sortable),\n        insideHorizontally = x >= rect.left - threshold && x <= rect.right + threshold,\n        insideVertically = y >= rect.top - threshold && y <= rect.bottom + threshold;\n      if (insideHorizontally && insideVertically) {\n        return ret = sortable;\n      }\n    });\n    return ret;\n  },\n  _prepareGroup = function _prepareGroup(options) {\n    function toFn(value, pull) {\n      return function (to, from, dragEl, evt) {\n        var sameGroup = to.options.group.name && from.options.group.name && to.options.group.name === from.options.group.name;\n        if (value == null && (pull || sameGroup)) {\n          // Default pull value\n          // Default pull and put value if same group\n          return true;\n        } else if (value == null || value === false) {\n          return false;\n        } else if (pull && value === 'clone') {\n          return value;\n        } else if (typeof value === 'function') {\n          return toFn(value(to, from, dragEl, evt), pull)(to, from, dragEl, evt);\n        } else {\n          var otherGroup = (pull ? to : from).options.group.name;\n          return value === true || typeof value === 'string' && value === otherGroup || value.join && value.indexOf(otherGroup) > -1;\n        }\n      };\n    }\n    var group = {};\n    var originalGroup = options.group;\n    if (!originalGroup || _typeof(originalGroup) != 'object') {\n      originalGroup = {\n        name: originalGroup\n      };\n    }\n    group.name = originalGroup.name;\n    group.checkPull = toFn(originalGroup.pull, true);\n    group.checkPut = toFn(originalGroup.put);\n    group.revertClone = originalGroup.revertClone;\n    options.group = group;\n  },\n  _hideGhostForTarget = function _hideGhostForTarget() {\n    if (!supportCssPointerEvents && ghostEl) {\n      css(ghostEl, 'display', 'none');\n    }\n  },\n  _unhideGhostForTarget = function _unhideGhostForTarget() {\n    if (!supportCssPointerEvents && ghostEl) {\n      css(ghostEl, 'display', '');\n    }\n  };\n\n// #1184 fix - Prevent click event on fallback if dragged but item not changed position\nif (documentExists && !ChromeForAndroid) {\n  document.addEventListener('click', function (evt) {\n    if (ignoreNextClick) {\n      evt.preventDefault();\n      evt.stopPropagation && evt.stopPropagation();\n      evt.stopImmediatePropagation && evt.stopImmediatePropagation();\n      ignoreNextClick = false;\n      return false;\n    }\n  }, true);\n}\nvar nearestEmptyInsertDetectEvent = function nearestEmptyInsertDetectEvent(evt) {\n  if (dragEl) {\n    evt = evt.touches ? evt.touches[0] : evt;\n    var nearest = _detectNearestEmptySortable(evt.clientX, evt.clientY);\n    if (nearest) {\n      // Create imitation event\n      var event = {};\n      for (var i in evt) {\n        if (evt.hasOwnProperty(i)) {\n          event[i] = evt[i];\n        }\n      }\n      event.target = event.rootEl = nearest;\n      event.preventDefault = void 0;\n      event.stopPropagation = void 0;\n      nearest[expando]._onDragOver(event);\n    }\n  }\n};\nvar _checkOutsideTargetEl = function _checkOutsideTargetEl(evt) {\n  if (dragEl) {\n    dragEl.parentNode[expando]._isOutsideThisEl(evt.target);\n  }\n};\n\n/**\r\n * @class  Sortable\r\n * @param  {HTMLElement}  el\r\n * @param  {Object}       [options]\r\n */\nfunction Sortable(el, options) {\n  if (!(el && el.nodeType && el.nodeType === 1)) {\n    throw \"Sortable: `el` must be an HTMLElement, not \".concat({}.toString.call(el));\n  }\n  this.el = el; // root element\n  this.options = options = _extends({}, options);\n\n  // Export instance\n  el[expando] = this;\n  var defaults = {\n    group: null,\n    sort: true,\n    disabled: false,\n    store: null,\n    handle: null,\n    draggable: /^[uo]l$/i.test(el.nodeName) ? '>li' : '>*',\n    swapThreshold: 1,\n    // percentage; 0 <= x <= 1\n    invertSwap: false,\n    // invert always\n    invertedSwapThreshold: null,\n    // will be set to same as swapThreshold if default\n    removeCloneOnHide: true,\n    direction: function direction() {\n      return _detectDirection(el, this.options);\n    },\n    ghostClass: 'sortable-ghost',\n    chosenClass: 'sortable-chosen',\n    dragClass: 'sortable-drag',\n    ignore: 'a, img',\n    filter: null,\n    preventOnFilter: true,\n    animation: 0,\n    easing: null,\n    setData: function setData(dataTransfer, dragEl) {\n      dataTransfer.setData('Text', dragEl.textContent);\n    },\n    dropBubble: false,\n    dragoverBubble: false,\n    dataIdAttr: 'data-id',\n    delay: 0,\n    delayOnTouchOnly: false,\n    touchStartThreshold: (Number.parseInt ? Number : window).parseInt(window.devicePixelRatio, 10) || 1,\n    forceFallback: false,\n    fallbackClass: 'sortable-fallback',\n    fallbackOnBody: false,\n    fallbackTolerance: 0,\n    fallbackOffset: {\n      x: 0,\n      y: 0\n    },\n    supportPointer: Sortable.supportPointer !== false && 'PointerEvent' in window && !Safari,\n    emptyInsertThreshold: 5\n  };\n  PluginManager.initializePlugins(this, el, defaults);\n\n  // Set default options\n  for (var name in defaults) {\n    !(name in options) && (options[name] = defaults[name]);\n  }\n  _prepareGroup(options);\n\n  // Bind all private methods\n  for (var fn in this) {\n    if (fn.charAt(0) === '_' && typeof this[fn] === 'function') {\n      this[fn] = this[fn].bind(this);\n    }\n  }\n\n  // Setup drag mode\n  this.nativeDraggable = options.forceFallback ? false : supportDraggable;\n  if (this.nativeDraggable) {\n    // Touch start threshold cannot be greater than the native dragstart threshold\n    this.options.touchStartThreshold = 1;\n  }\n\n  // Bind events\n  if (options.supportPointer) {\n    on(el, 'pointerdown', this._onTapStart);\n  } else {\n    on(el, 'mousedown', this._onTapStart);\n    on(el, 'touchstart', this._onTapStart);\n  }\n  if (this.nativeDraggable) {\n    on(el, 'dragover', this);\n    on(el, 'dragenter', this);\n  }\n  sortables.push(this.el);\n\n  // Restore sorting\n  options.store && options.store.get && this.sort(options.store.get(this) || []);\n\n  // Add animation state manager\n  _extends(this, AnimationStateManager());\n}\nSortable.prototype = /** @lends Sortable.prototype */{\n  constructor: Sortable,\n  _isOutsideThisEl: function _isOutsideThisEl(target) {\n    if (!this.el.contains(target) && target !== this.el) {\n      lastTarget = null;\n    }\n  },\n  _getDirection: function _getDirection(evt, target) {\n    return typeof this.options.direction === 'function' ? this.options.direction.call(this, evt, target, dragEl) : this.options.direction;\n  },\n  _onTapStart: function _onTapStart( /** Event|TouchEvent */evt) {\n    if (!evt.cancelable) return;\n    var _this = this,\n      el = this.el,\n      options = this.options,\n      preventOnFilter = options.preventOnFilter,\n      type = evt.type,\n      touch = evt.touches && evt.touches[0] || evt.pointerType && evt.pointerType === 'touch' && evt,\n      target = (touch || evt).target,\n      originalTarget = evt.target.shadowRoot && (evt.path && evt.path[0] || evt.composedPath && evt.composedPath()[0]) || target,\n      filter = options.filter;\n    _saveInputCheckedState(el);\n\n    // Don't trigger start event when an element is been dragged, otherwise the evt.oldindex always wrong when set option.group.\n    if (dragEl) {\n      return;\n    }\n    if (/mousedown|pointerdown/.test(type) && evt.button !== 0 || options.disabled) {\n      return; // only left button and enabled\n    }\n\n    // cancel dnd if original target is content editable\n    if (originalTarget.isContentEditable) {\n      return;\n    }\n\n    // Safari ignores further event handling after mousedown\n    if (!this.nativeDraggable && Safari && target && target.tagName.toUpperCase() === 'SELECT') {\n      return;\n    }\n    target = closest(target, options.draggable, el, false);\n    if (target && target.animated) {\n      return;\n    }\n    if (lastDownEl === target) {\n      // Ignoring duplicate `down`\n      return;\n    }\n\n    // Get the index of the dragged element within its parent\n    oldIndex = index(target);\n    oldDraggableIndex = index(target, options.draggable);\n\n    // Check filter\n    if (typeof filter === 'function') {\n      if (filter.call(this, evt, target, this)) {\n        _dispatchEvent({\n          sortable: _this,\n          rootEl: originalTarget,\n          name: 'filter',\n          targetEl: target,\n          toEl: el,\n          fromEl: el\n        });\n        pluginEvent('filter', _this, {\n          evt: evt\n        });\n        preventOnFilter && evt.cancelable && evt.preventDefault();\n        return; // cancel dnd\n      }\n    } else if (filter) {\n      filter = filter.split(',').some(function (criteria) {\n        criteria = closest(originalTarget, criteria.trim(), el, false);\n        if (criteria) {\n          _dispatchEvent({\n            sortable: _this,\n            rootEl: criteria,\n            name: 'filter',\n            targetEl: target,\n            fromEl: el,\n            toEl: el\n          });\n          pluginEvent('filter', _this, {\n            evt: evt\n          });\n          return true;\n        }\n      });\n      if (filter) {\n        preventOnFilter && evt.cancelable && evt.preventDefault();\n        return; // cancel dnd\n      }\n    }\n    if (options.handle && !closest(originalTarget, options.handle, el, false)) {\n      return;\n    }\n\n    // Prepare `dragstart`\n    this._prepareDragStart(evt, touch, target);\n  },\n  _prepareDragStart: function _prepareDragStart( /** Event */evt, /** Touch */touch, /** HTMLElement */target) {\n    var _this = this,\n      el = _this.el,\n      options = _this.options,\n      ownerDocument = el.ownerDocument,\n      dragStartFn;\n    if (target && !dragEl && target.parentNode === el) {\n      var dragRect = getRect(target);\n      rootEl = el;\n      dragEl = target;\n      parentEl = dragEl.parentNode;\n      nextEl = dragEl.nextSibling;\n      lastDownEl = target;\n      activeGroup = options.group;\n      Sortable.dragged = dragEl;\n      tapEvt = {\n        target: dragEl,\n        clientX: (touch || evt).clientX,\n        clientY: (touch || evt).clientY\n      };\n      tapDistanceLeft = tapEvt.clientX - dragRect.left;\n      tapDistanceTop = tapEvt.clientY - dragRect.top;\n      this._lastX = (touch || evt).clientX;\n      this._lastY = (touch || evt).clientY;\n      dragEl.style['will-change'] = 'all';\n      dragStartFn = function dragStartFn() {\n        pluginEvent('delayEnded', _this, {\n          evt: evt\n        });\n        if (Sortable.eventCanceled) {\n          _this._onDrop();\n          return;\n        }\n        // Delayed drag has been triggered\n        // we can re-enable the events: touchmove/mousemove\n        _this._disableDelayedDragEvents();\n        if (!FireFox && _this.nativeDraggable) {\n          dragEl.draggable = true;\n        }\n\n        // Bind the events: dragstart/dragend\n        _this._triggerDragStart(evt, touch);\n\n        // Drag start event\n        _dispatchEvent({\n          sortable: _this,\n          name: 'choose',\n          originalEvent: evt\n        });\n\n        // Chosen item\n        toggleClass(dragEl, options.chosenClass, true);\n      };\n\n      // Disable \"draggable\"\n      options.ignore.split(',').forEach(function (criteria) {\n        find(dragEl, criteria.trim(), _disableDraggable);\n      });\n      on(ownerDocument, 'dragover', nearestEmptyInsertDetectEvent);\n      on(ownerDocument, 'mousemove', nearestEmptyInsertDetectEvent);\n      on(ownerDocument, 'touchmove', nearestEmptyInsertDetectEvent);\n      on(ownerDocument, 'mouseup', _this._onDrop);\n      on(ownerDocument, 'touchend', _this._onDrop);\n      on(ownerDocument, 'touchcancel', _this._onDrop);\n\n      // Make dragEl draggable (must be before delay for FireFox)\n      if (FireFox && this.nativeDraggable) {\n        this.options.touchStartThreshold = 4;\n        dragEl.draggable = true;\n      }\n      pluginEvent('delayStart', this, {\n        evt: evt\n      });\n\n      // Delay is impossible for native DnD in Edge or IE\n      if (options.delay && (!options.delayOnTouchOnly || touch) && (!this.nativeDraggable || !(Edge || IE11OrLess))) {\n        if (Sortable.eventCanceled) {\n          this._onDrop();\n          return;\n        }\n        // If the user moves the pointer or let go the click or touch\n        // before the delay has been reached:\n        // disable the delayed drag\n        on(ownerDocument, 'mouseup', _this._disableDelayedDrag);\n        on(ownerDocument, 'touchend', _this._disableDelayedDrag);\n        on(ownerDocument, 'touchcancel', _this._disableDelayedDrag);\n        on(ownerDocument, 'mousemove', _this._delayedDragTouchMoveHandler);\n        on(ownerDocument, 'touchmove', _this._delayedDragTouchMoveHandler);\n        options.supportPointer && on(ownerDocument, 'pointermove', _this._delayedDragTouchMoveHandler);\n        _this._dragStartTimer = setTimeout(dragStartFn, options.delay);\n      } else {\n        dragStartFn();\n      }\n    }\n  },\n  _delayedDragTouchMoveHandler: function _delayedDragTouchMoveHandler( /** TouchEvent|PointerEvent **/e) {\n    var touch = e.touches ? e.touches[0] : e;\n    if (Math.max(Math.abs(touch.clientX - this._lastX), Math.abs(touch.clientY - this._lastY)) >= Math.floor(this.options.touchStartThreshold / (this.nativeDraggable && window.devicePixelRatio || 1))) {\n      this._disableDelayedDrag();\n    }\n  },\n  _disableDelayedDrag: function _disableDelayedDrag() {\n    dragEl && _disableDraggable(dragEl);\n    clearTimeout(this._dragStartTimer);\n    this._disableDelayedDragEvents();\n  },\n  _disableDelayedDragEvents: function _disableDelayedDragEvents() {\n    var ownerDocument = this.el.ownerDocument;\n    off(ownerDocument, 'mouseup', this._disableDelayedDrag);\n    off(ownerDocument, 'touchend', this._disableDelayedDrag);\n    off(ownerDocument, 'touchcancel', this._disableDelayedDrag);\n    off(ownerDocument, 'mousemove', this._delayedDragTouchMoveHandler);\n    off(ownerDocument, 'touchmove', this._delayedDragTouchMoveHandler);\n    off(ownerDocument, 'pointermove', this._delayedDragTouchMoveHandler);\n  },\n  _triggerDragStart: function _triggerDragStart( /** Event */evt, /** Touch */touch) {\n    touch = touch || evt.pointerType == 'touch' && evt;\n    if (!this.nativeDraggable || touch) {\n      if (this.options.supportPointer) {\n        on(document, 'pointermove', this._onTouchMove);\n      } else if (touch) {\n        on(document, 'touchmove', this._onTouchMove);\n      } else {\n        on(document, 'mousemove', this._onTouchMove);\n      }\n    } else {\n      on(dragEl, 'dragend', this);\n      on(rootEl, 'dragstart', this._onDragStart);\n    }\n    try {\n      if (document.selection) {\n        // Timeout neccessary for IE9\n        _nextTick(function () {\n          document.selection.empty();\n        });\n      } else {\n        window.getSelection().removeAllRanges();\n      }\n    } catch (err) {}\n  },\n  _dragStarted: function _dragStarted(fallback, evt) {\n    awaitingDragStarted = false;\n    if (rootEl && dragEl) {\n      pluginEvent('dragStarted', this, {\n        evt: evt\n      });\n      if (this.nativeDraggable) {\n        on(document, 'dragover', _checkOutsideTargetEl);\n      }\n      var options = this.options;\n\n      // Apply effect\n      !fallback && toggleClass(dragEl, options.dragClass, false);\n      toggleClass(dragEl, options.ghostClass, true);\n      Sortable.active = this;\n      fallback && this._appendGhost();\n\n      // Drag start event\n      _dispatchEvent({\n        sortable: this,\n        name: 'start',\n        originalEvent: evt\n      });\n    } else {\n      this._nulling();\n    }\n  },\n  _emulateDragOver: function _emulateDragOver() {\n    if (touchEvt) {\n      this._lastX = touchEvt.clientX;\n      this._lastY = touchEvt.clientY;\n      _hideGhostForTarget();\n      var target = document.elementFromPoint(touchEvt.clientX, touchEvt.clientY);\n      var parent = target;\n      while (target && target.shadowRoot) {\n        target = target.shadowRoot.elementFromPoint(touchEvt.clientX, touchEvt.clientY);\n        if (target === parent) break;\n        parent = target;\n      }\n      dragEl.parentNode[expando]._isOutsideThisEl(target);\n      if (parent) {\n        do {\n          if (parent[expando]) {\n            var inserted = void 0;\n            inserted = parent[expando]._onDragOver({\n              clientX: touchEvt.clientX,\n              clientY: touchEvt.clientY,\n              target: target,\n              rootEl: parent\n            });\n            if (inserted && !this.options.dragoverBubble) {\n              break;\n            }\n          }\n          target = parent; // store last element\n        }\n        /* jshint boss:true */ while (parent = getParentOrHost(parent));\n      }\n      _unhideGhostForTarget();\n    }\n  },\n  _onTouchMove: function _onTouchMove( /**TouchEvent*/evt) {\n    if (tapEvt) {\n      var options = this.options,\n        fallbackTolerance = options.fallbackTolerance,\n        fallbackOffset = options.fallbackOffset,\n        touch = evt.touches ? evt.touches[0] : evt,\n        ghostMatrix = ghostEl && matrix(ghostEl, true),\n        scaleX = ghostEl && ghostMatrix && ghostMatrix.a,\n        scaleY = ghostEl && ghostMatrix && ghostMatrix.d,\n        relativeScrollOffset = PositionGhostAbsolutely && ghostRelativeParent && getRelativeScrollOffset(ghostRelativeParent),\n        dx = (touch.clientX - tapEvt.clientX + fallbackOffset.x) / (scaleX || 1) + (relativeScrollOffset ? relativeScrollOffset[0] - ghostRelativeParentInitialScroll[0] : 0) / (scaleX || 1),\n        dy = (touch.clientY - tapEvt.clientY + fallbackOffset.y) / (scaleY || 1) + (relativeScrollOffset ? relativeScrollOffset[1] - ghostRelativeParentInitialScroll[1] : 0) / (scaleY || 1);\n\n      // only set the status to dragging, when we are actually dragging\n      if (!Sortable.active && !awaitingDragStarted) {\n        if (fallbackTolerance && Math.max(Math.abs(touch.clientX - this._lastX), Math.abs(touch.clientY - this._lastY)) < fallbackTolerance) {\n          return;\n        }\n        this._onDragStart(evt, true);\n      }\n      if (ghostEl) {\n        if (ghostMatrix) {\n          ghostMatrix.e += dx - (lastDx || 0);\n          ghostMatrix.f += dy - (lastDy || 0);\n        } else {\n          ghostMatrix = {\n            a: 1,\n            b: 0,\n            c: 0,\n            d: 1,\n            e: dx,\n            f: dy\n          };\n        }\n        var cssMatrix = \"matrix(\".concat(ghostMatrix.a, \",\").concat(ghostMatrix.b, \",\").concat(ghostMatrix.c, \",\").concat(ghostMatrix.d, \",\").concat(ghostMatrix.e, \",\").concat(ghostMatrix.f, \")\");\n        css(ghostEl, 'webkitTransform', cssMatrix);\n        css(ghostEl, 'mozTransform', cssMatrix);\n        css(ghostEl, 'msTransform', cssMatrix);\n        css(ghostEl, 'transform', cssMatrix);\n        lastDx = dx;\n        lastDy = dy;\n        touchEvt = touch;\n      }\n      evt.cancelable && evt.preventDefault();\n    }\n  },\n  _appendGhost: function _appendGhost() {\n    // Bug if using scale(): https://stackoverflow.com/questions/2637058\n    // Not being adjusted for\n    if (!ghostEl) {\n      var container = this.options.fallbackOnBody ? document.body : rootEl,\n        rect = getRect(dragEl, true, PositionGhostAbsolutely, true, container),\n        options = this.options;\n\n      // Position absolutely\n      if (PositionGhostAbsolutely) {\n        // Get relatively positioned parent\n        ghostRelativeParent = container;\n        while (css(ghostRelativeParent, 'position') === 'static' && css(ghostRelativeParent, 'transform') === 'none' && ghostRelativeParent !== document) {\n          ghostRelativeParent = ghostRelativeParent.parentNode;\n        }\n        if (ghostRelativeParent !== document.body && ghostRelativeParent !== document.documentElement) {\n          if (ghostRelativeParent === document) ghostRelativeParent = getWindowScrollingElement();\n          rect.top += ghostRelativeParent.scrollTop;\n          rect.left += ghostRelativeParent.scrollLeft;\n        } else {\n          ghostRelativeParent = getWindowScrollingElement();\n        }\n        ghostRelativeParentInitialScroll = getRelativeScrollOffset(ghostRelativeParent);\n      }\n      ghostEl = dragEl.cloneNode(true);\n      toggleClass(ghostEl, options.ghostClass, false);\n      toggleClass(ghostEl, options.fallbackClass, true);\n      toggleClass(ghostEl, options.dragClass, true);\n      css(ghostEl, 'transition', '');\n      css(ghostEl, 'transform', '');\n      css(ghostEl, 'box-sizing', 'border-box');\n      css(ghostEl, 'margin', 0);\n      css(ghostEl, 'top', rect.top);\n      css(ghostEl, 'left', rect.left);\n      css(ghostEl, 'width', rect.width);\n      css(ghostEl, 'height', rect.height);\n      css(ghostEl, 'opacity', '0.8');\n      css(ghostEl, 'position', PositionGhostAbsolutely ? 'absolute' : 'fixed');\n      css(ghostEl, 'zIndex', '100000');\n      css(ghostEl, 'pointerEvents', 'none');\n      Sortable.ghost = ghostEl;\n      container.appendChild(ghostEl);\n\n      // Set transform-origin\n      css(ghostEl, 'transform-origin', tapDistanceLeft / parseInt(ghostEl.style.width) * 100 + '% ' + tapDistanceTop / parseInt(ghostEl.style.height) * 100 + '%');\n    }\n  },\n  _onDragStart: function _onDragStart( /**Event*/evt, /**boolean*/fallback) {\n    var _this = this;\n    var dataTransfer = evt.dataTransfer;\n    var options = _this.options;\n    pluginEvent('dragStart', this, {\n      evt: evt\n    });\n    if (Sortable.eventCanceled) {\n      this._onDrop();\n      return;\n    }\n    pluginEvent('setupClone', this);\n    if (!Sortable.eventCanceled) {\n      cloneEl = clone(dragEl);\n      cloneEl.removeAttribute(\"id\");\n      cloneEl.draggable = false;\n      cloneEl.style['will-change'] = '';\n      this._hideClone();\n      toggleClass(cloneEl, this.options.chosenClass, false);\n      Sortable.clone = cloneEl;\n    }\n\n    // #1143: IFrame support workaround\n    _this.cloneId = _nextTick(function () {\n      pluginEvent('clone', _this);\n      if (Sortable.eventCanceled) return;\n      if (!_this.options.removeCloneOnHide) {\n        rootEl.insertBefore(cloneEl, dragEl);\n      }\n      _this._hideClone();\n      _dispatchEvent({\n        sortable: _this,\n        name: 'clone'\n      });\n    });\n    !fallback && toggleClass(dragEl, options.dragClass, true);\n\n    // Set proper drop events\n    if (fallback) {\n      ignoreNextClick = true;\n      _this._loopId = setInterval(_this._emulateDragOver, 50);\n    } else {\n      // Undo what was set in _prepareDragStart before drag started\n      off(document, 'mouseup', _this._onDrop);\n      off(document, 'touchend', _this._onDrop);\n      off(document, 'touchcancel', _this._onDrop);\n      if (dataTransfer) {\n        dataTransfer.effectAllowed = 'move';\n        options.setData && options.setData.call(_this, dataTransfer, dragEl);\n      }\n      on(document, 'drop', _this);\n\n      // #1276 fix:\n      css(dragEl, 'transform', 'translateZ(0)');\n    }\n    awaitingDragStarted = true;\n    _this._dragStartId = _nextTick(_this._dragStarted.bind(_this, fallback, evt));\n    on(document, 'selectstart', _this);\n    moved = true;\n    if (Safari) {\n      css(document.body, 'user-select', 'none');\n    }\n  },\n  // Returns true - if no further action is needed (either inserted or another condition)\n  _onDragOver: function _onDragOver( /**Event*/evt) {\n    var el = this.el,\n      target = evt.target,\n      dragRect,\n      targetRect,\n      revert,\n      options = this.options,\n      group = options.group,\n      activeSortable = Sortable.active,\n      isOwner = activeGroup === group,\n      canSort = options.sort,\n      fromSortable = putSortable || activeSortable,\n      vertical,\n      _this = this,\n      completedFired = false;\n    if (_silent) return;\n    function dragOverEvent(name, extra) {\n      pluginEvent(name, _this, _objectSpread2({\n        evt: evt,\n        isOwner: isOwner,\n        axis: vertical ? 'vertical' : 'horizontal',\n        revert: revert,\n        dragRect: dragRect,\n        targetRect: targetRect,\n        canSort: canSort,\n        fromSortable: fromSortable,\n        target: target,\n        completed: completed,\n        onMove: function onMove(target, after) {\n          return _onMove(rootEl, el, dragEl, dragRect, target, getRect(target), evt, after);\n        },\n        changed: changed\n      }, extra));\n    }\n\n    // Capture animation state\n    function capture() {\n      dragOverEvent('dragOverAnimationCapture');\n      _this.captureAnimationState();\n      if (_this !== fromSortable) {\n        fromSortable.captureAnimationState();\n      }\n    }\n\n    // Return invocation when dragEl is inserted (or completed)\n    function completed(insertion) {\n      dragOverEvent('dragOverCompleted', {\n        insertion: insertion\n      });\n      if (insertion) {\n        // Clones must be hidden before folding animation to capture dragRectAbsolute properly\n        if (isOwner) {\n          activeSortable._hideClone();\n        } else {\n          activeSortable._showClone(_this);\n        }\n        if (_this !== fromSortable) {\n          // Set ghost class to new sortable's ghost class\n          toggleClass(dragEl, putSortable ? putSortable.options.ghostClass : activeSortable.options.ghostClass, false);\n          toggleClass(dragEl, options.ghostClass, true);\n        }\n        if (putSortable !== _this && _this !== Sortable.active) {\n          putSortable = _this;\n        } else if (_this === Sortable.active && putSortable) {\n          putSortable = null;\n        }\n\n        // Animation\n        if (fromSortable === _this) {\n          _this._ignoreWhileAnimating = target;\n        }\n        _this.animateAll(function () {\n          dragOverEvent('dragOverAnimationComplete');\n          _this._ignoreWhileAnimating = null;\n        });\n        if (_this !== fromSortable) {\n          fromSortable.animateAll();\n          fromSortable._ignoreWhileAnimating = null;\n        }\n      }\n\n      // Null lastTarget if it is not inside a previously swapped element\n      if (target === dragEl && !dragEl.animated || target === el && !target.animated) {\n        lastTarget = null;\n      }\n\n      // no bubbling and not fallback\n      if (!options.dragoverBubble && !evt.rootEl && target !== document) {\n        dragEl.parentNode[expando]._isOutsideThisEl(evt.target);\n\n        // Do not detect for empty insert if already inserted\n        !insertion && nearestEmptyInsertDetectEvent(evt);\n      }\n      !options.dragoverBubble && evt.stopPropagation && evt.stopPropagation();\n      return completedFired = true;\n    }\n\n    // Call when dragEl has been inserted\n    function changed() {\n      newIndex = index(dragEl);\n      newDraggableIndex = index(dragEl, options.draggable);\n      _dispatchEvent({\n        sortable: _this,\n        name: 'change',\n        toEl: el,\n        newIndex: newIndex,\n        newDraggableIndex: newDraggableIndex,\n        originalEvent: evt\n      });\n    }\n    if (evt.preventDefault !== void 0) {\n      evt.cancelable && evt.preventDefault();\n    }\n    target = closest(target, options.draggable, el, true);\n    dragOverEvent('dragOver');\n    if (Sortable.eventCanceled) return completedFired;\n    if (dragEl.contains(evt.target) || target.animated && target.animatingX && target.animatingY || _this._ignoreWhileAnimating === target) {\n      return completed(false);\n    }\n    ignoreNextClick = false;\n    if (activeSortable && !options.disabled && (isOwner ? canSort || (revert = parentEl !== rootEl) // Reverting item into the original list\n    : putSortable === this || (this.lastPutMode = activeGroup.checkPull(this, activeSortable, dragEl, evt)) && group.checkPut(this, activeSortable, dragEl, evt))) {\n      vertical = this._getDirection(evt, target) === 'vertical';\n      dragRect = getRect(dragEl);\n      dragOverEvent('dragOverValid');\n      if (Sortable.eventCanceled) return completedFired;\n      if (revert) {\n        parentEl = rootEl; // actualization\n        capture();\n        this._hideClone();\n        dragOverEvent('revert');\n        if (!Sortable.eventCanceled) {\n          if (nextEl) {\n            rootEl.insertBefore(dragEl, nextEl);\n          } else {\n            rootEl.appendChild(dragEl);\n          }\n        }\n        return completed(true);\n      }\n      var elLastChild = lastChild(el, options.draggable);\n      if (!elLastChild || _ghostIsLast(evt, vertical, this) && !elLastChild.animated) {\n        // Insert to end of list\n\n        // If already at end of list: Do not insert\n        if (elLastChild === dragEl) {\n          return completed(false);\n        }\n\n        // if there is a last element, it is the target\n        if (elLastChild && el === evt.target) {\n          target = elLastChild;\n        }\n        if (target) {\n          targetRect = getRect(target);\n        }\n        if (_onMove(rootEl, el, dragEl, dragRect, target, targetRect, evt, !!target) !== false) {\n          capture();\n          if (elLastChild && elLastChild.nextSibling) {\n            // the last draggable element is not the last node\n            el.insertBefore(dragEl, elLastChild.nextSibling);\n          } else {\n            el.appendChild(dragEl);\n          }\n          parentEl = el; // actualization\n\n          changed();\n          return completed(true);\n        }\n      } else if (elLastChild && _ghostIsFirst(evt, vertical, this)) {\n        // Insert to start of list\n        var firstChild = getChild(el, 0, options, true);\n        if (firstChild === dragEl) {\n          return completed(false);\n        }\n        target = firstChild;\n        targetRect = getRect(target);\n        if (_onMove(rootEl, el, dragEl, dragRect, target, targetRect, evt, false) !== false) {\n          capture();\n          el.insertBefore(dragEl, firstChild);\n          parentEl = el; // actualization\n\n          changed();\n          return completed(true);\n        }\n      } else if (target.parentNode === el) {\n        targetRect = getRect(target);\n        var direction = 0,\n          targetBeforeFirstSwap,\n          differentLevel = dragEl.parentNode !== el,\n          differentRowCol = !_dragElInRowColumn(dragEl.animated && dragEl.toRect || dragRect, target.animated && target.toRect || targetRect, vertical),\n          side1 = vertical ? 'top' : 'left',\n          scrolledPastTop = isScrolledPast(target, 'top', 'top') || isScrolledPast(dragEl, 'top', 'top'),\n          scrollBefore = scrolledPastTop ? scrolledPastTop.scrollTop : void 0;\n        if (lastTarget !== target) {\n          targetBeforeFirstSwap = targetRect[side1];\n          pastFirstInvertThresh = false;\n          isCircumstantialInvert = !differentRowCol && options.invertSwap || differentLevel;\n        }\n        direction = _getSwapDirection(evt, target, targetRect, vertical, differentRowCol ? 1 : options.swapThreshold, options.invertedSwapThreshold == null ? options.swapThreshold : options.invertedSwapThreshold, isCircumstantialInvert, lastTarget === target);\n        var sibling;\n        if (direction !== 0) {\n          // Check if target is beside dragEl in respective direction (ignoring hidden elements)\n          var dragIndex = index(dragEl);\n          do {\n            dragIndex -= direction;\n            sibling = parentEl.children[dragIndex];\n          } while (sibling && (css(sibling, 'display') === 'none' || sibling === ghostEl));\n        }\n        // If dragEl is already beside target: Do not insert\n        if (direction === 0 || sibling === target) {\n          return completed(false);\n        }\n        lastTarget = target;\n        lastDirection = direction;\n        var nextSibling = target.nextElementSibling,\n          after = false;\n        after = direction === 1;\n        var moveVector = _onMove(rootEl, el, dragEl, dragRect, target, targetRect, evt, after);\n        if (moveVector !== false) {\n          if (moveVector === 1 || moveVector === -1) {\n            after = moveVector === 1;\n          }\n          _silent = true;\n          setTimeout(_unsilent, 30);\n          capture();\n          if (after && !nextSibling) {\n            el.appendChild(dragEl);\n          } else {\n            target.parentNode.insertBefore(dragEl, after ? nextSibling : target);\n          }\n\n          // Undo chrome's scroll adjustment (has no effect on other browsers)\n          if (scrolledPastTop) {\n            scrollBy(scrolledPastTop, 0, scrollBefore - scrolledPastTop.scrollTop);\n          }\n          parentEl = dragEl.parentNode; // actualization\n\n          // must be done before animation\n          if (targetBeforeFirstSwap !== undefined && !isCircumstantialInvert) {\n            targetMoveDistance = Math.abs(targetBeforeFirstSwap - getRect(target)[side1]);\n          }\n          changed();\n          return completed(true);\n        }\n      }\n      if (el.contains(dragEl)) {\n        return completed(false);\n      }\n    }\n    return false;\n  },\n  _ignoreWhileAnimating: null,\n  _offMoveEvents: function _offMoveEvents() {\n    off(document, 'mousemove', this._onTouchMove);\n    off(document, 'touchmove', this._onTouchMove);\n    off(document, 'pointermove', this._onTouchMove);\n    off(document, 'dragover', nearestEmptyInsertDetectEvent);\n    off(document, 'mousemove', nearestEmptyInsertDetectEvent);\n    off(document, 'touchmove', nearestEmptyInsertDetectEvent);\n  },\n  _offUpEvents: function _offUpEvents() {\n    var ownerDocument = this.el.ownerDocument;\n    off(ownerDocument, 'mouseup', this._onDrop);\n    off(ownerDocument, 'touchend', this._onDrop);\n    off(ownerDocument, 'pointerup', this._onDrop);\n    off(ownerDocument, 'touchcancel', this._onDrop);\n    off(document, 'selectstart', this);\n  },\n  _onDrop: function _onDrop( /**Event*/evt) {\n    var el = this.el,\n      options = this.options;\n\n    // Get the index of the dragged element within its parent\n    newIndex = index(dragEl);\n    newDraggableIndex = index(dragEl, options.draggable);\n    pluginEvent('drop', this, {\n      evt: evt\n    });\n    parentEl = dragEl && dragEl.parentNode;\n\n    // Get again after plugin event\n    newIndex = index(dragEl);\n    newDraggableIndex = index(dragEl, options.draggable);\n    if (Sortable.eventCanceled) {\n      this._nulling();\n      return;\n    }\n    awaitingDragStarted = false;\n    isCircumstantialInvert = false;\n    pastFirstInvertThresh = false;\n    clearInterval(this._loopId);\n    clearTimeout(this._dragStartTimer);\n    _cancelNextTick(this.cloneId);\n    _cancelNextTick(this._dragStartId);\n\n    // Unbind events\n    if (this.nativeDraggable) {\n      off(document, 'drop', this);\n      off(el, 'dragstart', this._onDragStart);\n    }\n    this._offMoveEvents();\n    this._offUpEvents();\n    if (Safari) {\n      css(document.body, 'user-select', '');\n    }\n    css(dragEl, 'transform', '');\n    if (evt) {\n      if (moved) {\n        evt.cancelable && evt.preventDefault();\n        !options.dropBubble && evt.stopPropagation();\n      }\n      ghostEl && ghostEl.parentNode && ghostEl.parentNode.removeChild(ghostEl);\n      if (rootEl === parentEl || putSortable && putSortable.lastPutMode !== 'clone') {\n        // Remove clone(s)\n        cloneEl && cloneEl.parentNode && cloneEl.parentNode.removeChild(cloneEl);\n      }\n      if (dragEl) {\n        if (this.nativeDraggable) {\n          off(dragEl, 'dragend', this);\n        }\n        _disableDraggable(dragEl);\n        dragEl.style['will-change'] = '';\n\n        // Remove classes\n        // ghostClass is added in dragStarted\n        if (moved && !awaitingDragStarted) {\n          toggleClass(dragEl, putSortable ? putSortable.options.ghostClass : this.options.ghostClass, false);\n        }\n        toggleClass(dragEl, this.options.chosenClass, false);\n\n        // Drag stop event\n        _dispatchEvent({\n          sortable: this,\n          name: 'unchoose',\n          toEl: parentEl,\n          newIndex: null,\n          newDraggableIndex: null,\n          originalEvent: evt\n        });\n        if (rootEl !== parentEl) {\n          if (newIndex >= 0) {\n            // Add event\n            _dispatchEvent({\n              rootEl: parentEl,\n              name: 'add',\n              toEl: parentEl,\n              fromEl: rootEl,\n              originalEvent: evt\n            });\n\n            // Remove event\n            _dispatchEvent({\n              sortable: this,\n              name: 'remove',\n              toEl: parentEl,\n              originalEvent: evt\n            });\n\n            // drag from one list and drop into another\n            _dispatchEvent({\n              rootEl: parentEl,\n              name: 'sort',\n              toEl: parentEl,\n              fromEl: rootEl,\n              originalEvent: evt\n            });\n            _dispatchEvent({\n              sortable: this,\n              name: 'sort',\n              toEl: parentEl,\n              originalEvent: evt\n            });\n          }\n          putSortable && putSortable.save();\n        } else {\n          if (newIndex !== oldIndex) {\n            if (newIndex >= 0) {\n              // drag & drop within the same list\n              _dispatchEvent({\n                sortable: this,\n                name: 'update',\n                toEl: parentEl,\n                originalEvent: evt\n              });\n              _dispatchEvent({\n                sortable: this,\n                name: 'sort',\n                toEl: parentEl,\n                originalEvent: evt\n              });\n            }\n          }\n        }\n        if (Sortable.active) {\n          /* jshint eqnull:true */\n          if (newIndex == null || newIndex === -1) {\n            newIndex = oldIndex;\n            newDraggableIndex = oldDraggableIndex;\n          }\n          _dispatchEvent({\n            sortable: this,\n            name: 'end',\n            toEl: parentEl,\n            originalEvent: evt\n          });\n\n          // Save sorting\n          this.save();\n        }\n      }\n    }\n    this._nulling();\n  },\n  _nulling: function _nulling() {\n    pluginEvent('nulling', this);\n    rootEl = dragEl = parentEl = ghostEl = nextEl = cloneEl = lastDownEl = cloneHidden = tapEvt = touchEvt = moved = newIndex = newDraggableIndex = oldIndex = oldDraggableIndex = lastTarget = lastDirection = putSortable = activeGroup = Sortable.dragged = Sortable.ghost = Sortable.clone = Sortable.active = null;\n    savedInputChecked.forEach(function (el) {\n      el.checked = true;\n    });\n    savedInputChecked.length = lastDx = lastDy = 0;\n  },\n  handleEvent: function handleEvent( /**Event*/evt) {\n    switch (evt.type) {\n      case 'drop':\n      case 'dragend':\n        this._onDrop(evt);\n        break;\n      case 'dragenter':\n      case 'dragover':\n        if (dragEl) {\n          this._onDragOver(evt);\n          _globalDragOver(evt);\n        }\n        break;\n      case 'selectstart':\n        evt.preventDefault();\n        break;\n    }\n  },\n  /**\r\n   * Serializes the item into an array of string.\r\n   * @returns {String[]}\r\n   */\n  toArray: function toArray() {\n    var order = [],\n      el,\n      children = this.el.children,\n      i = 0,\n      n = children.length,\n      options = this.options;\n    for (; i < n; i++) {\n      el = children[i];\n      if (closest(el, options.draggable, this.el, false)) {\n        order.push(el.getAttribute(options.dataIdAttr) || _generateId(el));\n      }\n    }\n    return order;\n  },\n  /**\r\n   * Sorts the elements according to the array.\r\n   * @param  {String[]}  order  order of the items\r\n   */\n  sort: function sort(order, useAnimation) {\n    var items = {},\n      rootEl = this.el;\n    this.toArray().forEach(function (id, i) {\n      var el = rootEl.children[i];\n      if (closest(el, this.options.draggable, rootEl, false)) {\n        items[id] = el;\n      }\n    }, this);\n    useAnimation && this.captureAnimationState();\n    order.forEach(function (id) {\n      if (items[id]) {\n        rootEl.removeChild(items[id]);\n        rootEl.appendChild(items[id]);\n      }\n    });\n    useAnimation && this.animateAll();\n  },\n  /**\r\n   * Save the current sorting\r\n   */\n  save: function save() {\n    var store = this.options.store;\n    store && store.set && store.set(this);\n  },\n  /**\r\n   * For each element in the set, get the first element that matches the selector by testing the element itself and traversing up through its ancestors in the DOM tree.\r\n   * @param   {HTMLElement}  el\r\n   * @param   {String}       [selector]  default: `options.draggable`\r\n   * @returns {HTMLElement|null}\r\n   */\n  closest: function closest$1(el, selector) {\n    return closest(el, selector || this.options.draggable, this.el, false);\n  },\n  /**\r\n   * Set/get option\r\n   * @param   {string} name\r\n   * @param   {*}      [value]\r\n   * @returns {*}\r\n   */\n  option: function option(name, value) {\n    var options = this.options;\n    if (value === void 0) {\n      return options[name];\n    } else {\n      var modifiedValue = PluginManager.modifyOption(this, name, value);\n      if (typeof modifiedValue !== 'undefined') {\n        options[name] = modifiedValue;\n      } else {\n        options[name] = value;\n      }\n      if (name === 'group') {\n        _prepareGroup(options);\n      }\n    }\n  },\n  /**\r\n   * Destroy\r\n   */\n  destroy: function destroy() {\n    pluginEvent('destroy', this);\n    var el = this.el;\n    el[expando] = null;\n    off(el, 'mousedown', this._onTapStart);\n    off(el, 'touchstart', this._onTapStart);\n    off(el, 'pointerdown', this._onTapStart);\n    if (this.nativeDraggable) {\n      off(el, 'dragover', this);\n      off(el, 'dragenter', this);\n    }\n    // Remove draggable attributes\n    Array.prototype.forEach.call(el.querySelectorAll('[draggable]'), function (el) {\n      el.removeAttribute('draggable');\n    });\n    this._onDrop();\n    this._disableDelayedDragEvents();\n    sortables.splice(sortables.indexOf(this.el), 1);\n    this.el = el = null;\n  },\n  _hideClone: function _hideClone() {\n    if (!cloneHidden) {\n      pluginEvent('hideClone', this);\n      if (Sortable.eventCanceled) return;\n      css(cloneEl, 'display', 'none');\n      if (this.options.removeCloneOnHide && cloneEl.parentNode) {\n        cloneEl.parentNode.removeChild(cloneEl);\n      }\n      cloneHidden = true;\n    }\n  },\n  _showClone: function _showClone(putSortable) {\n    if (putSortable.lastPutMode !== 'clone') {\n      this._hideClone();\n      return;\n    }\n    if (cloneHidden) {\n      pluginEvent('showClone', this);\n      if (Sortable.eventCanceled) return;\n\n      // show clone at dragEl or original position\n      if (dragEl.parentNode == rootEl && !this.options.group.revertClone) {\n        rootEl.insertBefore(cloneEl, dragEl);\n      } else if (nextEl) {\n        rootEl.insertBefore(cloneEl, nextEl);\n      } else {\n        rootEl.appendChild(cloneEl);\n      }\n      if (this.options.group.revertClone) {\n        this.animate(dragEl, cloneEl);\n      }\n      css(cloneEl, 'display', '');\n      cloneHidden = false;\n    }\n  }\n};\nfunction _globalDragOver( /**Event*/evt) {\n  if (evt.dataTransfer) {\n    evt.dataTransfer.dropEffect = 'move';\n  }\n  evt.cancelable && evt.preventDefault();\n}\nfunction _onMove(fromEl, toEl, dragEl, dragRect, targetEl, targetRect, originalEvent, willInsertAfter) {\n  var evt,\n    sortable = fromEl[expando],\n    onMoveFn = sortable.options.onMove,\n    retVal;\n  // Support for new CustomEvent feature\n  if (window.CustomEvent && !IE11OrLess && !Edge) {\n    evt = new CustomEvent('move', {\n      bubbles: true,\n      cancelable: true\n    });\n  } else {\n    evt = document.createEvent('Event');\n    evt.initEvent('move', true, true);\n  }\n  evt.to = toEl;\n  evt.from = fromEl;\n  evt.dragged = dragEl;\n  evt.draggedRect = dragRect;\n  evt.related = targetEl || toEl;\n  evt.relatedRect = targetRect || getRect(toEl);\n  evt.willInsertAfter = willInsertAfter;\n  evt.originalEvent = originalEvent;\n  fromEl.dispatchEvent(evt);\n  if (onMoveFn) {\n    retVal = onMoveFn.call(sortable, evt, originalEvent);\n  }\n  return retVal;\n}\nfunction _disableDraggable(el) {\n  el.draggable = false;\n}\nfunction _unsilent() {\n  _silent = false;\n}\nfunction _ghostIsFirst(evt, vertical, sortable) {\n  var firstElRect = getRect(getChild(sortable.el, 0, sortable.options, true));\n  var childContainingRect = getChildContainingRectFromElement(sortable.el, sortable.options, ghostEl);\n  var spacer = 10;\n  return vertical ? evt.clientX < childContainingRect.left - spacer || evt.clientY < firstElRect.top && evt.clientX < firstElRect.right : evt.clientY < childContainingRect.top - spacer || evt.clientY < firstElRect.bottom && evt.clientX < firstElRect.left;\n}\nfunction _ghostIsLast(evt, vertical, sortable) {\n  var lastElRect = getRect(lastChild(sortable.el, sortable.options.draggable));\n  var childContainingRect = getChildContainingRectFromElement(sortable.el, sortable.options, ghostEl);\n  var spacer = 10;\n  return vertical ? evt.clientX > childContainingRect.right + spacer || evt.clientY > lastElRect.bottom && evt.clientX > lastElRect.left : evt.clientY > childContainingRect.bottom + spacer || evt.clientX > lastElRect.right && evt.clientY > lastElRect.top;\n}\nfunction _getSwapDirection(evt, target, targetRect, vertical, swapThreshold, invertedSwapThreshold, invertSwap, isLastTarget) {\n  var mouseOnAxis = vertical ? evt.clientY : evt.clientX,\n    targetLength = vertical ? targetRect.height : targetRect.width,\n    targetS1 = vertical ? targetRect.top : targetRect.left,\n    targetS2 = vertical ? targetRect.bottom : targetRect.right,\n    invert = false;\n  if (!invertSwap) {\n    // Never invert or create dragEl shadow when target movemenet causes mouse to move past the end of regular swapThreshold\n    if (isLastTarget && targetMoveDistance < targetLength * swapThreshold) {\n      // multiplied only by swapThreshold because mouse will already be inside target by (1 - threshold) * targetLength / 2\n      // check if past first invert threshold on side opposite of lastDirection\n      if (!pastFirstInvertThresh && (lastDirection === 1 ? mouseOnAxis > targetS1 + targetLength * invertedSwapThreshold / 2 : mouseOnAxis < targetS2 - targetLength * invertedSwapThreshold / 2)) {\n        // past first invert threshold, do not restrict inverted threshold to dragEl shadow\n        pastFirstInvertThresh = true;\n      }\n      if (!pastFirstInvertThresh) {\n        // dragEl shadow (target move distance shadow)\n        if (lastDirection === 1 ? mouseOnAxis < targetS1 + targetMoveDistance // over dragEl shadow\n        : mouseOnAxis > targetS2 - targetMoveDistance) {\n          return -lastDirection;\n        }\n      } else {\n        invert = true;\n      }\n    } else {\n      // Regular\n      if (mouseOnAxis > targetS1 + targetLength * (1 - swapThreshold) / 2 && mouseOnAxis < targetS2 - targetLength * (1 - swapThreshold) / 2) {\n        return _getInsertDirection(target);\n      }\n    }\n  }\n  invert = invert || invertSwap;\n  if (invert) {\n    // Invert of regular\n    if (mouseOnAxis < targetS1 + targetLength * invertedSwapThreshold / 2 || mouseOnAxis > targetS2 - targetLength * invertedSwapThreshold / 2) {\n      return mouseOnAxis > targetS1 + targetLength / 2 ? 1 : -1;\n    }\n  }\n  return 0;\n}\n\n/**\r\n * Gets the direction dragEl must be swapped relative to target in order to make it\r\n * seem that dragEl has been \"inserted\" into that element's position\r\n * @param  {HTMLElement} target       The target whose position dragEl is being inserted at\r\n * @return {Number}                   Direction dragEl must be swapped\r\n */\nfunction _getInsertDirection(target) {\n  if (index(dragEl) < index(target)) {\n    return 1;\n  } else {\n    return -1;\n  }\n}\n\n/**\r\n * Generate id\r\n * @param   {HTMLElement} el\r\n * @returns {String}\r\n * @private\r\n */\nfunction _generateId(el) {\n  var str = el.tagName + el.className + el.src + el.href + el.textContent,\n    i = str.length,\n    sum = 0;\n  while (i--) {\n    sum += str.charCodeAt(i);\n  }\n  return sum.toString(36);\n}\nfunction _saveInputCheckedState(root) {\n  savedInputChecked.length = 0;\n  var inputs = root.getElementsByTagName('input');\n  var idx = inputs.length;\n  while (idx--) {\n    var el = inputs[idx];\n    el.checked && savedInputChecked.push(el);\n  }\n}\nfunction _nextTick(fn) {\n  return setTimeout(fn, 0);\n}\nfunction _cancelNextTick(id) {\n  return clearTimeout(id);\n}\n\n// Fixed #973:\nif (documentExists) {\n  on(document, 'touchmove', function (evt) {\n    if ((Sortable.active || awaitingDragStarted) && evt.cancelable) {\n      evt.preventDefault();\n    }\n  });\n}\n\n// Export utils\nSortable.utils = {\n  on: on,\n  off: off,\n  css: css,\n  find: find,\n  is: function is(el, selector) {\n    return !!closest(el, selector, el, false);\n  },\n  extend: extend,\n  throttle: throttle,\n  closest: closest,\n  toggleClass: toggleClass,\n  clone: clone,\n  index: index,\n  nextTick: _nextTick,\n  cancelNextTick: _cancelNextTick,\n  detectDirection: _detectDirection,\n  getChild: getChild,\n  expando: expando\n};\n\n/**\r\n * Get the Sortable instance of an element\r\n * @param  {HTMLElement} element The element\r\n * @return {Sortable|undefined}         The instance of Sortable\r\n */\nSortable.get = function (element) {\n  return element[expando];\n};\n\n/**\r\n * Mount a plugin to Sortable\r\n * @param  {...SortablePlugin|SortablePlugin[]} plugins       Plugins being mounted\r\n */\nSortable.mount = function () {\n  for (var _len = arguments.length, plugins = new Array(_len), _key = 0; _key < _len; _key++) {\n    plugins[_key] = arguments[_key];\n  }\n  if (plugins[0].constructor === Array) plugins = plugins[0];\n  plugins.forEach(function (plugin) {\n    if (!plugin.prototype || !plugin.prototype.constructor) {\n      throw \"Sortable: Mounted plugin must be a constructor function, not \".concat({}.toString.call(plugin));\n    }\n    if (plugin.utils) Sortable.utils = _objectSpread2(_objectSpread2({}, Sortable.utils), plugin.utils);\n    PluginManager.mount(plugin);\n  });\n};\n\n/**\r\n * Create sortable instance\r\n * @param {HTMLElement}  el\r\n * @param {Object}      [options]\r\n */\nSortable.create = function (el, options) {\n  return new Sortable(el, options);\n};\n\n// Export\nSortable.version = version;\n\nvar autoScrolls = [],\n  scrollEl,\n  scrollRootEl,\n  scrolling = false,\n  lastAutoScrollX,\n  lastAutoScrollY,\n  touchEvt$1,\n  pointerElemChangedInterval;\nfunction AutoScrollPlugin() {\n  function AutoScroll() {\n    this.defaults = {\n      scroll: true,\n      forceAutoScrollFallback: false,\n      scrollSensitivity: 30,\n      scrollSpeed: 10,\n      bubbleScroll: true\n    };\n\n    // Bind all private methods\n    for (var fn in this) {\n      if (fn.charAt(0) === '_' && typeof this[fn] === 'function') {\n        this[fn] = this[fn].bind(this);\n      }\n    }\n  }\n  AutoScroll.prototype = {\n    dragStarted: function dragStarted(_ref) {\n      var originalEvent = _ref.originalEvent;\n      if (this.sortable.nativeDraggable) {\n        on(document, 'dragover', this._handleAutoScroll);\n      } else {\n        if (this.options.supportPointer) {\n          on(document, 'pointermove', this._handleFallbackAutoScroll);\n        } else if (originalEvent.touches) {\n          on(document, 'touchmove', this._handleFallbackAutoScroll);\n        } else {\n          on(document, 'mousemove', this._handleFallbackAutoScroll);\n        }\n      }\n    },\n    dragOverCompleted: function dragOverCompleted(_ref2) {\n      var originalEvent = _ref2.originalEvent;\n      // For when bubbling is canceled and using fallback (fallback 'touchmove' always reached)\n      if (!this.options.dragOverBubble && !originalEvent.rootEl) {\n        this._handleAutoScroll(originalEvent);\n      }\n    },\n    drop: function drop() {\n      if (this.sortable.nativeDraggable) {\n        off(document, 'dragover', this._handleAutoScroll);\n      } else {\n        off(document, 'pointermove', this._handleFallbackAutoScroll);\n        off(document, 'touchmove', this._handleFallbackAutoScroll);\n        off(document, 'mousemove', this._handleFallbackAutoScroll);\n      }\n      clearPointerElemChangedInterval();\n      clearAutoScrolls();\n      cancelThrottle();\n    },\n    nulling: function nulling() {\n      touchEvt$1 = scrollRootEl = scrollEl = scrolling = pointerElemChangedInterval = lastAutoScrollX = lastAutoScrollY = null;\n      autoScrolls.length = 0;\n    },\n    _handleFallbackAutoScroll: function _handleFallbackAutoScroll(evt) {\n      this._handleAutoScroll(evt, true);\n    },\n    _handleAutoScroll: function _handleAutoScroll(evt, fallback) {\n      var _this = this;\n      var x = (evt.touches ? evt.touches[0] : evt).clientX,\n        y = (evt.touches ? evt.touches[0] : evt).clientY,\n        elem = document.elementFromPoint(x, y);\n      touchEvt$1 = evt;\n\n      // IE does not seem to have native autoscroll,\n      // Edge's autoscroll seems too conditional,\n      // MACOS Safari does not have autoscroll,\n      // Firefox and Chrome are good\n      if (fallback || this.options.forceAutoScrollFallback || Edge || IE11OrLess || Safari) {\n        autoScroll(evt, this.options, elem, fallback);\n\n        // Listener for pointer element change\n        var ogElemScroller = getParentAutoScrollElement(elem, true);\n        if (scrolling && (!pointerElemChangedInterval || x !== lastAutoScrollX || y !== lastAutoScrollY)) {\n          pointerElemChangedInterval && clearPointerElemChangedInterval();\n          // Detect for pointer elem change, emulating native DnD behaviour\n          pointerElemChangedInterval = setInterval(function () {\n            var newElem = getParentAutoScrollElement(document.elementFromPoint(x, y), true);\n            if (newElem !== ogElemScroller) {\n              ogElemScroller = newElem;\n              clearAutoScrolls();\n            }\n            autoScroll(evt, _this.options, newElem, fallback);\n          }, 10);\n          lastAutoScrollX = x;\n          lastAutoScrollY = y;\n        }\n      } else {\n        // if DnD is enabled (and browser has good autoscrolling), first autoscroll will already scroll, so get parent autoscroll of first autoscroll\n        if (!this.options.bubbleScroll || getParentAutoScrollElement(elem, true) === getWindowScrollingElement()) {\n          clearAutoScrolls();\n          return;\n        }\n        autoScroll(evt, this.options, getParentAutoScrollElement(elem, false), false);\n      }\n    }\n  };\n  return _extends(AutoScroll, {\n    pluginName: 'scroll',\n    initializeByDefault: true\n  });\n}\nfunction clearAutoScrolls() {\n  autoScrolls.forEach(function (autoScroll) {\n    clearInterval(autoScroll.pid);\n  });\n  autoScrolls = [];\n}\nfunction clearPointerElemChangedInterval() {\n  clearInterval(pointerElemChangedInterval);\n}\nvar autoScroll = throttle(function (evt, options, rootEl, isFallback) {\n  // Bug: https://bugzilla.mozilla.org/show_bug.cgi?id=505521\n  if (!options.scroll) return;\n  var x = (evt.touches ? evt.touches[0] : evt).clientX,\n    y = (evt.touches ? evt.touches[0] : evt).clientY,\n    sens = options.scrollSensitivity,\n    speed = options.scrollSpeed,\n    winScroller = getWindowScrollingElement();\n  var scrollThisInstance = false,\n    scrollCustomFn;\n\n  // New scroll root, set scrollEl\n  if (scrollRootEl !== rootEl) {\n    scrollRootEl = rootEl;\n    clearAutoScrolls();\n    scrollEl = options.scroll;\n    scrollCustomFn = options.scrollFn;\n    if (scrollEl === true) {\n      scrollEl = getParentAutoScrollElement(rootEl, true);\n    }\n  }\n  var layersOut = 0;\n  var currentParent = scrollEl;\n  do {\n    var el = currentParent,\n      rect = getRect(el),\n      top = rect.top,\n      bottom = rect.bottom,\n      left = rect.left,\n      right = rect.right,\n      width = rect.width,\n      height = rect.height,\n      canScrollX = void 0,\n      canScrollY = void 0,\n      scrollWidth = el.scrollWidth,\n      scrollHeight = el.scrollHeight,\n      elCSS = css(el),\n      scrollPosX = el.scrollLeft,\n      scrollPosY = el.scrollTop;\n    if (el === winScroller) {\n      canScrollX = width < scrollWidth && (elCSS.overflowX === 'auto' || elCSS.overflowX === 'scroll' || elCSS.overflowX === 'visible');\n      canScrollY = height < scrollHeight && (elCSS.overflowY === 'auto' || elCSS.overflowY === 'scroll' || elCSS.overflowY === 'visible');\n    } else {\n      canScrollX = width < scrollWidth && (elCSS.overflowX === 'auto' || elCSS.overflowX === 'scroll');\n      canScrollY = height < scrollHeight && (elCSS.overflowY === 'auto' || elCSS.overflowY === 'scroll');\n    }\n    var vx = canScrollX && (Math.abs(right - x) <= sens && scrollPosX + width < scrollWidth) - (Math.abs(left - x) <= sens && !!scrollPosX);\n    var vy = canScrollY && (Math.abs(bottom - y) <= sens && scrollPosY + height < scrollHeight) - (Math.abs(top - y) <= sens && !!scrollPosY);\n    if (!autoScrolls[layersOut]) {\n      for (var i = 0; i <= layersOut; i++) {\n        if (!autoScrolls[i]) {\n          autoScrolls[i] = {};\n        }\n      }\n    }\n    if (autoScrolls[layersOut].vx != vx || autoScrolls[layersOut].vy != vy || autoScrolls[layersOut].el !== el) {\n      autoScrolls[layersOut].el = el;\n      autoScrolls[layersOut].vx = vx;\n      autoScrolls[layersOut].vy = vy;\n      clearInterval(autoScrolls[layersOut].pid);\n      if (vx != 0 || vy != 0) {\n        scrollThisInstance = true;\n        /* jshint loopfunc:true */\n        autoScrolls[layersOut].pid = setInterval(function () {\n          // emulate drag over during autoscroll (fallback), emulating native DnD behaviour\n          if (isFallback && this.layer === 0) {\n            Sortable.active._onTouchMove(touchEvt$1); // To move ghost if it is positioned absolutely\n          }\n          var scrollOffsetY = autoScrolls[this.layer].vy ? autoScrolls[this.layer].vy * speed : 0;\n          var scrollOffsetX = autoScrolls[this.layer].vx ? autoScrolls[this.layer].vx * speed : 0;\n          if (typeof scrollCustomFn === 'function') {\n            if (scrollCustomFn.call(Sortable.dragged.parentNode[expando], scrollOffsetX, scrollOffsetY, evt, touchEvt$1, autoScrolls[this.layer].el) !== 'continue') {\n              return;\n            }\n          }\n          scrollBy(autoScrolls[this.layer].el, scrollOffsetX, scrollOffsetY);\n        }.bind({\n          layer: layersOut\n        }), 24);\n      }\n    }\n    layersOut++;\n  } while (options.bubbleScroll && currentParent !== winScroller && (currentParent = getParentAutoScrollElement(currentParent, false)));\n  scrolling = scrollThisInstance; // in case another function catches scrolling as false in between when it is not\n}, 30);\n\nvar drop = function drop(_ref) {\n  var originalEvent = _ref.originalEvent,\n    putSortable = _ref.putSortable,\n    dragEl = _ref.dragEl,\n    activeSortable = _ref.activeSortable,\n    dispatchSortableEvent = _ref.dispatchSortableEvent,\n    hideGhostForTarget = _ref.hideGhostForTarget,\n    unhideGhostForTarget = _ref.unhideGhostForTarget;\n  if (!originalEvent) return;\n  var toSortable = putSortable || activeSortable;\n  hideGhostForTarget();\n  var touch = originalEvent.changedTouches && originalEvent.changedTouches.length ? originalEvent.changedTouches[0] : originalEvent;\n  var target = document.elementFromPoint(touch.clientX, touch.clientY);\n  unhideGhostForTarget();\n  if (toSortable && !toSortable.el.contains(target)) {\n    dispatchSortableEvent('spill');\n    this.onSpill({\n      dragEl: dragEl,\n      putSortable: putSortable\n    });\n  }\n};\nfunction Revert() {}\nRevert.prototype = {\n  startIndex: null,\n  dragStart: function dragStart(_ref2) {\n    var oldDraggableIndex = _ref2.oldDraggableIndex;\n    this.startIndex = oldDraggableIndex;\n  },\n  onSpill: function onSpill(_ref3) {\n    var dragEl = _ref3.dragEl,\n      putSortable = _ref3.putSortable;\n    this.sortable.captureAnimationState();\n    if (putSortable) {\n      putSortable.captureAnimationState();\n    }\n    var nextSibling = getChild(this.sortable.el, this.startIndex, this.options);\n    if (nextSibling) {\n      this.sortable.el.insertBefore(dragEl, nextSibling);\n    } else {\n      this.sortable.el.appendChild(dragEl);\n    }\n    this.sortable.animateAll();\n    if (putSortable) {\n      putSortable.animateAll();\n    }\n  },\n  drop: drop\n};\n_extends(Revert, {\n  pluginName: 'revertOnSpill'\n});\nfunction Remove() {}\nRemove.prototype = {\n  onSpill: function onSpill(_ref4) {\n    var dragEl = _ref4.dragEl,\n      putSortable = _ref4.putSortable;\n    var parentSortable = putSortable || this.sortable;\n    parentSortable.captureAnimationState();\n    dragEl.parentNode && dragEl.parentNode.removeChild(dragEl);\n    parentSortable.animateAll();\n  },\n  drop: drop\n};\n_extends(Remove, {\n  pluginName: 'removeOnSpill'\n});\n\nvar lastSwapEl;\nfunction SwapPlugin() {\n  function Swap() {\n    this.defaults = {\n      swapClass: 'sortable-swap-highlight'\n    };\n  }\n  Swap.prototype = {\n    dragStart: function dragStart(_ref) {\n      var dragEl = _ref.dragEl;\n      lastSwapEl = dragEl;\n    },\n    dragOverValid: function dragOverValid(_ref2) {\n      var completed = _ref2.completed,\n        target = _ref2.target,\n        onMove = _ref2.onMove,\n        activeSortable = _ref2.activeSortable,\n        changed = _ref2.changed,\n        cancel = _ref2.cancel;\n      if (!activeSortable.options.swap) return;\n      var el = this.sortable.el,\n        options = this.options;\n      if (target && target !== el) {\n        var prevSwapEl = lastSwapEl;\n        if (onMove(target) !== false) {\n          toggleClass(target, options.swapClass, true);\n          lastSwapEl = target;\n        } else {\n          lastSwapEl = null;\n        }\n        if (prevSwapEl && prevSwapEl !== lastSwapEl) {\n          toggleClass(prevSwapEl, options.swapClass, false);\n        }\n      }\n      changed();\n      completed(true);\n      cancel();\n    },\n    drop: function drop(_ref3) {\n      var activeSortable = _ref3.activeSortable,\n        putSortable = _ref3.putSortable,\n        dragEl = _ref3.dragEl;\n      var toSortable = putSortable || this.sortable;\n      var options = this.options;\n      lastSwapEl && toggleClass(lastSwapEl, options.swapClass, false);\n      if (lastSwapEl && (options.swap || putSortable && putSortable.options.swap)) {\n        if (dragEl !== lastSwapEl) {\n          toSortable.captureAnimationState();\n          if (toSortable !== activeSortable) activeSortable.captureAnimationState();\n          swapNodes(dragEl, lastSwapEl);\n          toSortable.animateAll();\n          if (toSortable !== activeSortable) activeSortable.animateAll();\n        }\n      }\n    },\n    nulling: function nulling() {\n      lastSwapEl = null;\n    }\n  };\n  return _extends(Swap, {\n    pluginName: 'swap',\n    eventProperties: function eventProperties() {\n      return {\n        swapItem: lastSwapEl\n      };\n    }\n  });\n}\nfunction swapNodes(n1, n2) {\n  var p1 = n1.parentNode,\n    p2 = n2.parentNode,\n    i1,\n    i2;\n  if (!p1 || !p2 || p1.isEqualNode(n2) || p2.isEqualNode(n1)) return;\n  i1 = index(n1);\n  i2 = index(n2);\n  if (p1.isEqualNode(p2) && i1 < i2) {\n    i2++;\n  }\n  p1.insertBefore(n2, p1.children[i1]);\n  p2.insertBefore(n1, p2.children[i2]);\n}\n\nvar multiDragElements = [],\n  multiDragClones = [],\n  lastMultiDragSelect,\n  // for selection with modifier key down (SHIFT)\n  multiDragSortable,\n  initialFolding = false,\n  // Initial multi-drag fold when drag started\n  folding = false,\n  // Folding any other time\n  dragStarted = false,\n  dragEl$1,\n  clonesFromRect,\n  clonesHidden;\nfunction MultiDragPlugin() {\n  function MultiDrag(sortable) {\n    // Bind all private methods\n    for (var fn in this) {\n      if (fn.charAt(0) === '_' && typeof this[fn] === 'function') {\n        this[fn] = this[fn].bind(this);\n      }\n    }\n    if (!sortable.options.avoidImplicitDeselect) {\n      if (sortable.options.supportPointer) {\n        on(document, 'pointerup', this._deselectMultiDrag);\n      } else {\n        on(document, 'mouseup', this._deselectMultiDrag);\n        on(document, 'touchend', this._deselectMultiDrag);\n      }\n    }\n    on(document, 'keydown', this._checkKeyDown);\n    on(document, 'keyup', this._checkKeyUp);\n    this.defaults = {\n      selectedClass: 'sortable-selected',\n      multiDragKey: null,\n      avoidImplicitDeselect: false,\n      setData: function setData(dataTransfer, dragEl) {\n        var data = '';\n        if (multiDragElements.length && multiDragSortable === sortable) {\n          multiDragElements.forEach(function (multiDragElement, i) {\n            data += (!i ? '' : ', ') + multiDragElement.textContent;\n          });\n        } else {\n          data = dragEl.textContent;\n        }\n        dataTransfer.setData('Text', data);\n      }\n    };\n  }\n  MultiDrag.prototype = {\n    multiDragKeyDown: false,\n    isMultiDrag: false,\n    delayStartGlobal: function delayStartGlobal(_ref) {\n      var dragged = _ref.dragEl;\n      dragEl$1 = dragged;\n    },\n    delayEnded: function delayEnded() {\n      this.isMultiDrag = ~multiDragElements.indexOf(dragEl$1);\n    },\n    setupClone: function setupClone(_ref2) {\n      var sortable = _ref2.sortable,\n        cancel = _ref2.cancel;\n      if (!this.isMultiDrag) return;\n      for (var i = 0; i < multiDragElements.length; i++) {\n        multiDragClones.push(clone(multiDragElements[i]));\n        multiDragClones[i].sortableIndex = multiDragElements[i].sortableIndex;\n        multiDragClones[i].draggable = false;\n        multiDragClones[i].style['will-change'] = '';\n        toggleClass(multiDragClones[i], this.options.selectedClass, false);\n        multiDragElements[i] === dragEl$1 && toggleClass(multiDragClones[i], this.options.chosenClass, false);\n      }\n      sortable._hideClone();\n      cancel();\n    },\n    clone: function clone(_ref3) {\n      var sortable = _ref3.sortable,\n        rootEl = _ref3.rootEl,\n        dispatchSortableEvent = _ref3.dispatchSortableEvent,\n        cancel = _ref3.cancel;\n      if (!this.isMultiDrag) return;\n      if (!this.options.removeCloneOnHide) {\n        if (multiDragElements.length && multiDragSortable === sortable) {\n          insertMultiDragClones(true, rootEl);\n          dispatchSortableEvent('clone');\n          cancel();\n        }\n      }\n    },\n    showClone: function showClone(_ref4) {\n      var cloneNowShown = _ref4.cloneNowShown,\n        rootEl = _ref4.rootEl,\n        cancel = _ref4.cancel;\n      if (!this.isMultiDrag) return;\n      insertMultiDragClones(false, rootEl);\n      multiDragClones.forEach(function (clone) {\n        css(clone, 'display', '');\n      });\n      cloneNowShown();\n      clonesHidden = false;\n      cancel();\n    },\n    hideClone: function hideClone(_ref5) {\n      var _this = this;\n      var sortable = _ref5.sortable,\n        cloneNowHidden = _ref5.cloneNowHidden,\n        cancel = _ref5.cancel;\n      if (!this.isMultiDrag) return;\n      multiDragClones.forEach(function (clone) {\n        css(clone, 'display', 'none');\n        if (_this.options.removeCloneOnHide && clone.parentNode) {\n          clone.parentNode.removeChild(clone);\n        }\n      });\n      cloneNowHidden();\n      clonesHidden = true;\n      cancel();\n    },\n    dragStartGlobal: function dragStartGlobal(_ref6) {\n      var sortable = _ref6.sortable;\n      if (!this.isMultiDrag && multiDragSortable) {\n        multiDragSortable.multiDrag._deselectMultiDrag();\n      }\n      multiDragElements.forEach(function (multiDragElement) {\n        multiDragElement.sortableIndex = index(multiDragElement);\n      });\n\n      // Sort multi-drag elements\n      multiDragElements = multiDragElements.sort(function (a, b) {\n        return a.sortableIndex - b.sortableIndex;\n      });\n      dragStarted = true;\n    },\n    dragStarted: function dragStarted(_ref7) {\n      var _this2 = this;\n      var sortable = _ref7.sortable;\n      if (!this.isMultiDrag) return;\n      if (this.options.sort) {\n        // Capture rects,\n        // hide multi drag elements (by positioning them absolute),\n        // set multi drag elements rects to dragRect,\n        // show multi drag elements,\n        // animate to rects,\n        // unset rects & remove from DOM\n\n        sortable.captureAnimationState();\n        if (this.options.animation) {\n          multiDragElements.forEach(function (multiDragElement) {\n            if (multiDragElement === dragEl$1) return;\n            css(multiDragElement, 'position', 'absolute');\n          });\n          var dragRect = getRect(dragEl$1, false, true, true);\n          multiDragElements.forEach(function (multiDragElement) {\n            if (multiDragElement === dragEl$1) return;\n            setRect(multiDragElement, dragRect);\n          });\n          folding = true;\n          initialFolding = true;\n        }\n      }\n      sortable.animateAll(function () {\n        folding = false;\n        initialFolding = false;\n        if (_this2.options.animation) {\n          multiDragElements.forEach(function (multiDragElement) {\n            unsetRect(multiDragElement);\n          });\n        }\n\n        // Remove all auxiliary multidrag items from el, if sorting enabled\n        if (_this2.options.sort) {\n          removeMultiDragElements();\n        }\n      });\n    },\n    dragOver: function dragOver(_ref8) {\n      var target = _ref8.target,\n        completed = _ref8.completed,\n        cancel = _ref8.cancel;\n      if (folding && ~multiDragElements.indexOf(target)) {\n        completed(false);\n        cancel();\n      }\n    },\n    revert: function revert(_ref9) {\n      var fromSortable = _ref9.fromSortable,\n        rootEl = _ref9.rootEl,\n        sortable = _ref9.sortable,\n        dragRect = _ref9.dragRect;\n      if (multiDragElements.length > 1) {\n        // Setup unfold animation\n        multiDragElements.forEach(function (multiDragElement) {\n          sortable.addAnimationState({\n            target: multiDragElement,\n            rect: folding ? getRect(multiDragElement) : dragRect\n          });\n          unsetRect(multiDragElement);\n          multiDragElement.fromRect = dragRect;\n          fromSortable.removeAnimationState(multiDragElement);\n        });\n        folding = false;\n        insertMultiDragElements(!this.options.removeCloneOnHide, rootEl);\n      }\n    },\n    dragOverCompleted: function dragOverCompleted(_ref10) {\n      var sortable = _ref10.sortable,\n        isOwner = _ref10.isOwner,\n        insertion = _ref10.insertion,\n        activeSortable = _ref10.activeSortable,\n        parentEl = _ref10.parentEl,\n        putSortable = _ref10.putSortable;\n      var options = this.options;\n      if (insertion) {\n        // Clones must be hidden before folding animation to capture dragRectAbsolute properly\n        if (isOwner) {\n          activeSortable._hideClone();\n        }\n        initialFolding = false;\n        // If leaving sort:false root, or already folding - Fold to new location\n        if (options.animation && multiDragElements.length > 1 && (folding || !isOwner && !activeSortable.options.sort && !putSortable)) {\n          // Fold: Set all multi drag elements's rects to dragEl's rect when multi-drag elements are invisible\n          var dragRectAbsolute = getRect(dragEl$1, false, true, true);\n          multiDragElements.forEach(function (multiDragElement) {\n            if (multiDragElement === dragEl$1) return;\n            setRect(multiDragElement, dragRectAbsolute);\n\n            // Move element(s) to end of parentEl so that it does not interfere with multi-drag clones insertion if they are inserted\n            // while folding, and so that we can capture them again because old sortable will no longer be fromSortable\n            parentEl.appendChild(multiDragElement);\n          });\n          folding = true;\n        }\n\n        // Clones must be shown (and check to remove multi drags) after folding when interfering multiDragElements are moved out\n        if (!isOwner) {\n          // Only remove if not folding (folding will remove them anyways)\n          if (!folding) {\n            removeMultiDragElements();\n          }\n          if (multiDragElements.length > 1) {\n            var clonesHiddenBefore = clonesHidden;\n            activeSortable._showClone(sortable);\n\n            // Unfold animation for clones if showing from hidden\n            if (activeSortable.options.animation && !clonesHidden && clonesHiddenBefore) {\n              multiDragClones.forEach(function (clone) {\n                activeSortable.addAnimationState({\n                  target: clone,\n                  rect: clonesFromRect\n                });\n                clone.fromRect = clonesFromRect;\n                clone.thisAnimationDuration = null;\n              });\n            }\n          } else {\n            activeSortable._showClone(sortable);\n          }\n        }\n      }\n    },\n    dragOverAnimationCapture: function dragOverAnimationCapture(_ref11) {\n      var dragRect = _ref11.dragRect,\n        isOwner = _ref11.isOwner,\n        activeSortable = _ref11.activeSortable;\n      multiDragElements.forEach(function (multiDragElement) {\n        multiDragElement.thisAnimationDuration = null;\n      });\n      if (activeSortable.options.animation && !isOwner && activeSortable.multiDrag.isMultiDrag) {\n        clonesFromRect = _extends({}, dragRect);\n        var dragMatrix = matrix(dragEl$1, true);\n        clonesFromRect.top -= dragMatrix.f;\n        clonesFromRect.left -= dragMatrix.e;\n      }\n    },\n    dragOverAnimationComplete: function dragOverAnimationComplete() {\n      if (folding) {\n        folding = false;\n        removeMultiDragElements();\n      }\n    },\n    drop: function drop(_ref12) {\n      var evt = _ref12.originalEvent,\n        rootEl = _ref12.rootEl,\n        parentEl = _ref12.parentEl,\n        sortable = _ref12.sortable,\n        dispatchSortableEvent = _ref12.dispatchSortableEvent,\n        oldIndex = _ref12.oldIndex,\n        putSortable = _ref12.putSortable;\n      var toSortable = putSortable || this.sortable;\n      if (!evt) return;\n      var options = this.options,\n        children = parentEl.children;\n\n      // Multi-drag selection\n      if (!dragStarted) {\n        if (options.multiDragKey && !this.multiDragKeyDown) {\n          this._deselectMultiDrag();\n        }\n        toggleClass(dragEl$1, options.selectedClass, !~multiDragElements.indexOf(dragEl$1));\n        if (!~multiDragElements.indexOf(dragEl$1)) {\n          multiDragElements.push(dragEl$1);\n          dispatchEvent({\n            sortable: sortable,\n            rootEl: rootEl,\n            name: 'select',\n            targetEl: dragEl$1,\n            originalEvent: evt\n          });\n\n          // Modifier activated, select from last to dragEl\n          if (evt.shiftKey && lastMultiDragSelect && sortable.el.contains(lastMultiDragSelect)) {\n            var lastIndex = index(lastMultiDragSelect),\n              currentIndex = index(dragEl$1);\n            if (~lastIndex && ~currentIndex && lastIndex !== currentIndex) {\n              // Must include lastMultiDragSelect (select it), in case modified selection from no selection\n              // (but previous selection existed)\n              var n, i;\n              if (currentIndex > lastIndex) {\n                i = lastIndex;\n                n = currentIndex;\n              } else {\n                i = currentIndex;\n                n = lastIndex + 1;\n              }\n              for (; i < n; i++) {\n                if (~multiDragElements.indexOf(children[i])) continue;\n                toggleClass(children[i], options.selectedClass, true);\n                multiDragElements.push(children[i]);\n                dispatchEvent({\n                  sortable: sortable,\n                  rootEl: rootEl,\n                  name: 'select',\n                  targetEl: children[i],\n                  originalEvent: evt\n                });\n              }\n            }\n          } else {\n            lastMultiDragSelect = dragEl$1;\n          }\n          multiDragSortable = toSortable;\n        } else {\n          multiDragElements.splice(multiDragElements.indexOf(dragEl$1), 1);\n          lastMultiDragSelect = null;\n          dispatchEvent({\n            sortable: sortable,\n            rootEl: rootEl,\n            name: 'deselect',\n            targetEl: dragEl$1,\n            originalEvent: evt\n          });\n        }\n      }\n\n      // Multi-drag drop\n      if (dragStarted && this.isMultiDrag) {\n        folding = false;\n        // Do not \"unfold\" after around dragEl if reverted\n        if ((parentEl[expando].options.sort || parentEl !== rootEl) && multiDragElements.length > 1) {\n          var dragRect = getRect(dragEl$1),\n            multiDragIndex = index(dragEl$1, ':not(.' + this.options.selectedClass + ')');\n          if (!initialFolding && options.animation) dragEl$1.thisAnimationDuration = null;\n          toSortable.captureAnimationState();\n          if (!initialFolding) {\n            if (options.animation) {\n              dragEl$1.fromRect = dragRect;\n              multiDragElements.forEach(function (multiDragElement) {\n                multiDragElement.thisAnimationDuration = null;\n                if (multiDragElement !== dragEl$1) {\n                  var rect = folding ? getRect(multiDragElement) : dragRect;\n                  multiDragElement.fromRect = rect;\n\n                  // Prepare unfold animation\n                  toSortable.addAnimationState({\n                    target: multiDragElement,\n                    rect: rect\n                  });\n                }\n              });\n            }\n\n            // Multi drag elements are not necessarily removed from the DOM on drop, so to reinsert\n            // properly they must all be removed\n            removeMultiDragElements();\n            multiDragElements.forEach(function (multiDragElement) {\n              if (children[multiDragIndex]) {\n                parentEl.insertBefore(multiDragElement, children[multiDragIndex]);\n              } else {\n                parentEl.appendChild(multiDragElement);\n              }\n              multiDragIndex++;\n            });\n\n            // If initial folding is done, the elements may have changed position because they are now\n            // unfolding around dragEl, even though dragEl may not have his index changed, so update event\n            // must be fired here as Sortable will not.\n            if (oldIndex === index(dragEl$1)) {\n              var update = false;\n              multiDragElements.forEach(function (multiDragElement) {\n                if (multiDragElement.sortableIndex !== index(multiDragElement)) {\n                  update = true;\n                  return;\n                }\n              });\n              if (update) {\n                dispatchSortableEvent('update');\n                dispatchSortableEvent('sort');\n              }\n            }\n          }\n\n          // Must be done after capturing individual rects (scroll bar)\n          multiDragElements.forEach(function (multiDragElement) {\n            unsetRect(multiDragElement);\n          });\n          toSortable.animateAll();\n        }\n        multiDragSortable = toSortable;\n      }\n\n      // Remove clones if necessary\n      if (rootEl === parentEl || putSortable && putSortable.lastPutMode !== 'clone') {\n        multiDragClones.forEach(function (clone) {\n          clone.parentNode && clone.parentNode.removeChild(clone);\n        });\n      }\n    },\n    nullingGlobal: function nullingGlobal() {\n      this.isMultiDrag = dragStarted = false;\n      multiDragClones.length = 0;\n    },\n    destroyGlobal: function destroyGlobal() {\n      this._deselectMultiDrag();\n      off(document, 'pointerup', this._deselectMultiDrag);\n      off(document, 'mouseup', this._deselectMultiDrag);\n      off(document, 'touchend', this._deselectMultiDrag);\n      off(document, 'keydown', this._checkKeyDown);\n      off(document, 'keyup', this._checkKeyUp);\n    },\n    _deselectMultiDrag: function _deselectMultiDrag(evt) {\n      if (typeof dragStarted !== \"undefined\" && dragStarted) return;\n\n      // Only deselect if selection is in this sortable\n      if (multiDragSortable !== this.sortable) return;\n\n      // Only deselect if target is not item in this sortable\n      if (evt && closest(evt.target, this.options.draggable, this.sortable.el, false)) return;\n\n      // Only deselect if left click\n      if (evt && evt.button !== 0) return;\n      while (multiDragElements.length) {\n        var el = multiDragElements[0];\n        toggleClass(el, this.options.selectedClass, false);\n        multiDragElements.shift();\n        dispatchEvent({\n          sortable: this.sortable,\n          rootEl: this.sortable.el,\n          name: 'deselect',\n          targetEl: el,\n          originalEvent: evt\n        });\n      }\n    },\n    _checkKeyDown: function _checkKeyDown(evt) {\n      if (evt.key === this.options.multiDragKey) {\n        this.multiDragKeyDown = true;\n      }\n    },\n    _checkKeyUp: function _checkKeyUp(evt) {\n      if (evt.key === this.options.multiDragKey) {\n        this.multiDragKeyDown = false;\n      }\n    }\n  };\n  return _extends(MultiDrag, {\n    // Static methods & properties\n    pluginName: 'multiDrag',\n    utils: {\n      /**\r\n       * Selects the provided multi-drag item\r\n       * @param  {HTMLElement} el    The element to be selected\r\n       */\n      select: function select(el) {\n        var sortable = el.parentNode[expando];\n        if (!sortable || !sortable.options.multiDrag || ~multiDragElements.indexOf(el)) return;\n        if (multiDragSortable && multiDragSortable !== sortable) {\n          multiDragSortable.multiDrag._deselectMultiDrag();\n          multiDragSortable = sortable;\n        }\n        toggleClass(el, sortable.options.selectedClass, true);\n        multiDragElements.push(el);\n      },\n      /**\r\n       * Deselects the provided multi-drag item\r\n       * @param  {HTMLElement} el    The element to be deselected\r\n       */\n      deselect: function deselect(el) {\n        var sortable = el.parentNode[expando],\n          index = multiDragElements.indexOf(el);\n        if (!sortable || !sortable.options.multiDrag || !~index) return;\n        toggleClass(el, sortable.options.selectedClass, false);\n        multiDragElements.splice(index, 1);\n      }\n    },\n    eventProperties: function eventProperties() {\n      var _this3 = this;\n      var oldIndicies = [],\n        newIndicies = [];\n      multiDragElements.forEach(function (multiDragElement) {\n        oldIndicies.push({\n          multiDragElement: multiDragElement,\n          index: multiDragElement.sortableIndex\n        });\n\n        // multiDragElements will already be sorted if folding\n        var newIndex;\n        if (folding && multiDragElement !== dragEl$1) {\n          newIndex = -1;\n        } else if (folding) {\n          newIndex = index(multiDragElement, ':not(.' + _this3.options.selectedClass + ')');\n        } else {\n          newIndex = index(multiDragElement);\n        }\n        newIndicies.push({\n          multiDragElement: multiDragElement,\n          index: newIndex\n        });\n      });\n      return {\n        items: _toConsumableArray(multiDragElements),\n        clones: [].concat(multiDragClones),\n        oldIndicies: oldIndicies,\n        newIndicies: newIndicies\n      };\n    },\n    optionListeners: {\n      multiDragKey: function multiDragKey(key) {\n        key = key.toLowerCase();\n        if (key === 'ctrl') {\n          key = 'Control';\n        } else if (key.length > 1) {\n          key = key.charAt(0).toUpperCase() + key.substr(1);\n        }\n        return key;\n      }\n    }\n  });\n}\nfunction insertMultiDragElements(clonesInserted, rootEl) {\n  multiDragElements.forEach(function (multiDragElement, i) {\n    var target = rootEl.children[multiDragElement.sortableIndex + (clonesInserted ? Number(i) : 0)];\n    if (target) {\n      rootEl.insertBefore(multiDragElement, target);\n    } else {\n      rootEl.appendChild(multiDragElement);\n    }\n  });\n}\n\n/**\r\n * Insert multi-drag clones\r\n * @param  {[Boolean]} elementsInserted  Whether the multi-drag elements are inserted\r\n * @param  {HTMLElement} rootEl\r\n */\nfunction insertMultiDragClones(elementsInserted, rootEl) {\n  multiDragClones.forEach(function (clone, i) {\n    var target = rootEl.children[clone.sortableIndex + (elementsInserted ? Number(i) : 0)];\n    if (target) {\n      rootEl.insertBefore(clone, target);\n    } else {\n      rootEl.appendChild(clone);\n    }\n  });\n}\nfunction removeMultiDragElements() {\n  multiDragElements.forEach(function (multiDragElement) {\n    if (multiDragElement === dragEl$1) return;\n    multiDragElement.parentNode && multiDragElement.parentNode.removeChild(multiDragElement);\n  });\n}\n\nSortable.mount(new AutoScrollPlugin());\nSortable.mount(Remove, Revert);\n\nexport default Sortable;\nexport { MultiDragPlugin as MultiDrag, Sortable, SwapPlugin as Swap };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,OAAOA,CAACC,MAAM,EAAEC,cAAc,EAAE;EACvC,IAAIC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAC9B,IAAIG,MAAM,CAACC,qBAAqB,EAAE;IAChC,IAAIC,OAAO,GAAGF,MAAM,CAACC,qBAAqB,CAACJ,MAAM,CAAC;IAClD,IAAIC,cAAc,EAAE;MAClBI,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QACtC,OAAOJ,MAAM,CAACK,wBAAwB,CAACR,MAAM,EAAEO,GAAG,CAAC,CAACE,UAAU;MAChE,CAAC,CAAC;IACJ;IACAP,IAAI,CAACQ,IAAI,CAACC,KAAK,CAACT,IAAI,EAAEG,OAAO,CAAC;EAChC;EACA,OAAOH,IAAI;AACb;AACA,SAASU,cAAcA,CAACC,MAAM,EAAE;EAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACzC,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC,IAAI,IAAI,GAAGC,SAAS,CAACD,CAAC,CAAC,GAAG,CAAC,CAAC;IACrD,IAAIA,CAAC,GAAG,CAAC,EAAE;MACTf,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QACnDC,eAAe,CAACP,MAAM,EAAEM,GAAG,EAAEF,MAAM,CAACE,GAAG,CAAC,CAAC;MAC3C,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIhB,MAAM,CAACkB,yBAAyB,EAAE;MAC3ClB,MAAM,CAACmB,gBAAgB,CAACT,MAAM,EAAEV,MAAM,CAACkB,yBAAyB,CAACJ,MAAM,CAAC,CAAC;IAC3E,CAAC,MAAM;MACLlB,OAAO,CAACI,MAAM,CAACc,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAC7ChB,MAAM,CAACoB,cAAc,CAACV,MAAM,EAAEM,GAAG,EAAEhB,MAAM,CAACK,wBAAwB,CAACS,MAAM,EAAEE,GAAG,CAAC,CAAC;MAClF,CAAC,CAAC;IACJ;EACF;EACA,OAAON,MAAM;AACf;AACA,SAASW,OAAOA,CAACC,GAAG,EAAE;EACpB,yBAAyB;;EAEzB,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;IACvEH,OAAO,GAAG,SAAVA,OAAOA,CAAaC,GAAG,EAAE;MACvB,OAAO,OAAOA,GAAG;IACnB,CAAC;EACH,CAAC,MAAM;IACLD,OAAO,GAAG,SAAVA,OAAOA,CAAaC,GAAG,EAAE;MACvB,OAAOA,GAAG,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;IAC9H,CAAC;EACH;EACA,OAAOD,OAAO,CAACC,GAAG,CAAC;AACrB;AACA,SAASL,eAAeA,CAACK,GAAG,EAAEN,GAAG,EAAEW,KAAK,EAAE;EACxC,IAAIX,GAAG,IAAIM,GAAG,EAAE;IACdtB,MAAM,CAACoB,cAAc,CAACE,GAAG,EAAEN,GAAG,EAAE;MAC9BW,KAAK,EAAEA,KAAK;MACZrB,UAAU,EAAE,IAAI;MAChBsB,YAAY,EAAE,IAAI;MAClBC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLP,GAAG,CAACN,GAAG,CAAC,GAAGW,KAAK;EAClB;EACA,OAAOL,GAAG;AACZ;AACA,SAASQ,QAAQA,CAAA,EAAG;EAClBA,QAAQ,GAAG9B,MAAM,CAAC+B,MAAM,IAAI,UAAUrB,MAAM,EAAE;IAC5C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACzC,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MACzB,KAAK,IAAIK,GAAG,IAAIF,MAAM,EAAE;QACtB,IAAId,MAAM,CAAC0B,SAAS,CAACM,cAAc,CAACC,IAAI,CAACnB,MAAM,EAAEE,GAAG,CAAC,EAAE;UACrDN,MAAM,CAACM,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;QAC3B;MACF;IACF;IACA,OAAON,MAAM;EACf,CAAC;EACD,OAAOoB,QAAQ,CAACtB,KAAK,CAAC,IAAI,EAAEI,SAAS,CAAC;AACxC;AACA,SAASsB,6BAA6BA,CAACpB,MAAM,EAAEqB,QAAQ,EAAE;EACvD,IAAIrB,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAC7B,IAAIJ,MAAM,GAAG,CAAC,CAAC;EACf,IAAI0B,UAAU,GAAGpC,MAAM,CAACD,IAAI,CAACe,MAAM,CAAC;EACpC,IAAIE,GAAG,EAAEL,CAAC;EACV,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,UAAU,CAACvB,MAAM,EAAEF,CAAC,EAAE,EAAE;IACtCK,GAAG,GAAGoB,UAAU,CAACzB,CAAC,CAAC;IACnB,IAAIwB,QAAQ,CAACE,OAAO,CAACrB,GAAG,CAAC,IAAI,CAAC,EAAE;IAChCN,MAAM,CAACM,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;EAC3B;EACA,OAAON,MAAM;AACf;AACA,SAAS4B,wBAAwBA,CAACxB,MAAM,EAAEqB,QAAQ,EAAE;EAClD,IAAIrB,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAC7B,IAAIJ,MAAM,GAAGwB,6BAA6B,CAACpB,MAAM,EAAEqB,QAAQ,CAAC;EAC5D,IAAInB,GAAG,EAAEL,CAAC;EACV,IAAIX,MAAM,CAACC,qBAAqB,EAAE;IAChC,IAAIsC,gBAAgB,GAAGvC,MAAM,CAACC,qBAAqB,CAACa,MAAM,CAAC;IAC3D,KAAKH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4B,gBAAgB,CAAC1B,MAAM,EAAEF,CAAC,EAAE,EAAE;MAC5CK,GAAG,GAAGuB,gBAAgB,CAAC5B,CAAC,CAAC;MACzB,IAAIwB,QAAQ,CAACE,OAAO,CAACrB,GAAG,CAAC,IAAI,CAAC,EAAE;MAChC,IAAI,CAAChB,MAAM,CAAC0B,SAAS,CAACc,oBAAoB,CAACP,IAAI,CAACnB,MAAM,EAAEE,GAAG,CAAC,EAAE;MAC9DN,MAAM,CAACM,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;IAC3B;EACF;EACA,OAAON,MAAM;AACf;AACA,SAAS+B,kBAAkBA,CAACC,GAAG,EAAE;EAC/B,OAAOC,kBAAkB,CAACD,GAAG,CAAC,IAAIE,gBAAgB,CAACF,GAAG,CAAC,IAAIG,2BAA2B,CAACH,GAAG,CAAC,IAAII,kBAAkB,CAAC,CAAC;AACrH;AACA,SAASH,kBAAkBA,CAACD,GAAG,EAAE;EAC/B,IAAIK,KAAK,CAACC,OAAO,CAACN,GAAG,CAAC,EAAE,OAAOO,iBAAiB,CAACP,GAAG,CAAC;AACvD;AACA,SAASE,gBAAgBA,CAACM,IAAI,EAAE;EAC9B,IAAI,OAAO3B,MAAM,KAAK,WAAW,IAAI2B,IAAI,CAAC3B,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAI0B,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAOH,KAAK,CAACI,IAAI,CAACD,IAAI,CAAC;AAC3H;AACA,SAASL,2BAA2BA,CAACO,CAAC,EAAEC,MAAM,EAAE;EAC9C,IAAI,CAACD,CAAC,EAAE;EACR,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOH,iBAAiB,CAACG,CAAC,EAAEC,MAAM,CAAC;EAC9D,IAAIC,CAAC,GAAGtD,MAAM,CAAC0B,SAAS,CAAC6B,QAAQ,CAACtB,IAAI,CAACmB,CAAC,CAAC,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACtD,IAAIF,CAAC,KAAK,QAAQ,IAAIF,CAAC,CAAC3B,WAAW,EAAE6B,CAAC,GAAGF,CAAC,CAAC3B,WAAW,CAACgC,IAAI;EAC3D,IAAIH,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOP,KAAK,CAACI,IAAI,CAACC,CAAC,CAAC;EACpD,IAAIE,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACI,IAAI,CAACJ,CAAC,CAAC,EAAE,OAAOL,iBAAiB,CAACG,CAAC,EAAEC,MAAM,CAAC;AAClH;AACA,SAASJ,iBAAiBA,CAACP,GAAG,EAAEiB,GAAG,EAAE;EACnC,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGjB,GAAG,CAAC7B,MAAM,EAAE8C,GAAG,GAAGjB,GAAG,CAAC7B,MAAM;EACrD,KAAK,IAAIF,CAAC,GAAG,CAAC,EAAEiD,IAAI,GAAG,IAAIb,KAAK,CAACY,GAAG,CAAC,EAAEhD,CAAC,GAAGgD,GAAG,EAAEhD,CAAC,EAAE,EAAEiD,IAAI,CAACjD,CAAC,CAAC,GAAG+B,GAAG,CAAC/B,CAAC,CAAC;EACrE,OAAOiD,IAAI;AACb;AACA,SAASd,kBAAkBA,CAAA,EAAG;EAC5B,MAAM,IAAIe,SAAS,CAAC,sIAAsI,CAAC;AAC7J;AAEA,IAAIC,OAAO,GAAG,QAAQ;AAEtB,SAASC,SAASA,CAACC,OAAO,EAAE;EAC1B,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,SAAS,EAAE;IACrD,OAAO,CAAC,EAAE,aAAaA,SAAS,CAACH,SAAS,CAACI,KAAK,CAACH,OAAO,CAAC;EAC3D;AACF;AACA,IAAII,UAAU,GAAGL,SAAS,CAAC,uDAAuD,CAAC;AACnF,IAAIM,IAAI,GAAGN,SAAS,CAAC,OAAO,CAAC;AAC7B,IAAIO,OAAO,GAAGP,SAAS,CAAC,UAAU,CAAC;AACnC,IAAIQ,MAAM,GAAGR,SAAS,CAAC,SAAS,CAAC,IAAI,CAACA,SAAS,CAAC,SAAS,CAAC,IAAI,CAACA,SAAS,CAAC,UAAU,CAAC;AACpF,IAAIS,GAAG,GAAGT,SAAS,CAAC,iBAAiB,CAAC;AACtC,IAAIU,gBAAgB,GAAGV,SAAS,CAAC,SAAS,CAAC,IAAIA,SAAS,CAAC,UAAU,CAAC;AAEpE,IAAIW,WAAW,GAAG;EAChBC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;AACX,CAAC;AACD,SAASC,EAAEA,CAACC,EAAE,EAAEC,KAAK,EAAEC,EAAE,EAAE;EACzBF,EAAE,CAACG,gBAAgB,CAACF,KAAK,EAAEC,EAAE,EAAE,CAACZ,UAAU,IAAIM,WAAW,CAAC;AAC5D;AACA,SAASQ,GAAGA,CAACJ,EAAE,EAAEC,KAAK,EAAEC,EAAE,EAAE;EAC1BF,EAAE,CAACK,mBAAmB,CAACJ,KAAK,EAAEC,EAAE,EAAE,CAACZ,UAAU,IAAIM,WAAW,CAAC;AAC/D;AACA,SAASU,OAAOA,CAAE,gBAAgBN,EAAE,EAAE,WAAWO,QAAQ,EAAE;EACzD,IAAI,CAACA,QAAQ,EAAE;EACfA,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,KAAKA,QAAQ,GAAGA,QAAQ,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC;EACzD,IAAIR,EAAE,EAAE;IACN,IAAI;MACF,IAAIA,EAAE,CAACM,OAAO,EAAE;QACd,OAAON,EAAE,CAACM,OAAO,CAACC,QAAQ,CAAC;MAC7B,CAAC,MAAM,IAAIP,EAAE,CAACS,iBAAiB,EAAE;QAC/B,OAAOT,EAAE,CAACS,iBAAiB,CAACF,QAAQ,CAAC;MACvC,CAAC,MAAM,IAAIP,EAAE,CAACU,qBAAqB,EAAE;QACnC,OAAOV,EAAE,CAACU,qBAAqB,CAACH,QAAQ,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOI,CAAC,EAAE;MACV,OAAO,KAAK;IACd;EACF;EACA,OAAO,KAAK;AACd;AACA,SAASC,eAAeA,CAACZ,EAAE,EAAE;EAC3B,OAAOA,EAAE,CAACa,IAAI,IAAIb,EAAE,KAAKc,QAAQ,IAAId,EAAE,CAACa,IAAI,CAACE,QAAQ,GAAGf,EAAE,CAACa,IAAI,GAAGb,EAAE,CAACgB,UAAU;AACjF;AACA,SAASC,OAAOA,CAAE,gBAAgBjB,EAAE,EAAE,WAAWO,QAAQ,EAAE,gBAAgBW,GAAG,EAAEC,UAAU,EAAE;EAC1F,IAAInB,EAAE,EAAE;IACNkB,GAAG,GAAGA,GAAG,IAAIJ,QAAQ;IACrB,GAAG;MACD,IAAIP,QAAQ,IAAI,IAAI,KAAKA,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGP,EAAE,CAACgB,UAAU,KAAKE,GAAG,IAAIZ,OAAO,CAACN,EAAE,EAAEO,QAAQ,CAAC,GAAGD,OAAO,CAACN,EAAE,EAAEO,QAAQ,CAAC,CAAC,IAAIY,UAAU,IAAInB,EAAE,KAAKkB,GAAG,EAAE;QAClJ,OAAOlB,EAAE;MACX;MACA,IAAIA,EAAE,KAAKkB,GAAG,EAAE;MAChB;IACF,CAAC,QAAQlB,EAAE,GAAGY,eAAe,CAACZ,EAAE,CAAC;EACnC;EACA,OAAO,IAAI;AACb;AACA,IAAIoB,OAAO,GAAG,MAAM;AACpB,SAASC,WAAWA,CAACrB,EAAE,EAAErB,IAAI,EAAE2C,KAAK,EAAE;EACpC,IAAItB,EAAE,IAAIrB,IAAI,EAAE;IACd,IAAIqB,EAAE,CAACuB,SAAS,EAAE;MAChBvB,EAAE,CAACuB,SAAS,CAACD,KAAK,GAAG,KAAK,GAAG,QAAQ,CAAC,CAAC3C,IAAI,CAAC;IAC9C,CAAC,MAAM;MACL,IAAI6C,SAAS,GAAG,CAAC,GAAG,GAAGxB,EAAE,CAACwB,SAAS,GAAG,GAAG,EAAEC,OAAO,CAACL,OAAO,EAAE,GAAG,CAAC,CAACK,OAAO,CAAC,GAAG,GAAG9C,IAAI,GAAG,GAAG,EAAE,GAAG,CAAC;MAC/FqB,EAAE,CAACwB,SAAS,GAAG,CAACA,SAAS,IAAIF,KAAK,GAAG,GAAG,GAAG3C,IAAI,GAAG,EAAE,CAAC,EAAE8C,OAAO,CAACL,OAAO,EAAE,GAAG,CAAC;IAC9E;EACF;AACF;AACA,SAASM,GAAGA,CAAC1B,EAAE,EAAE2B,IAAI,EAAEC,GAAG,EAAE;EAC1B,IAAIC,KAAK,GAAG7B,EAAE,IAAIA,EAAE,CAAC6B,KAAK;EAC1B,IAAIA,KAAK,EAAE;IACT,IAAID,GAAG,KAAK,KAAK,CAAC,EAAE;MAClB,IAAId,QAAQ,CAACgB,WAAW,IAAIhB,QAAQ,CAACgB,WAAW,CAACC,gBAAgB,EAAE;QACjEH,GAAG,GAAGd,QAAQ,CAACgB,WAAW,CAACC,gBAAgB,CAAC/B,EAAE,EAAE,EAAE,CAAC;MACrD,CAAC,MAAM,IAAIA,EAAE,CAACgC,YAAY,EAAE;QAC1BJ,GAAG,GAAG5B,EAAE,CAACgC,YAAY;MACvB;MACA,OAAOL,IAAI,KAAK,KAAK,CAAC,GAAGC,GAAG,GAAGA,GAAG,CAACD,IAAI,CAAC;IAC1C,CAAC,MAAM;MACL,IAAI,EAAEA,IAAI,IAAIE,KAAK,CAAC,IAAIF,IAAI,CAACpE,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;QACrDoE,IAAI,GAAG,UAAU,GAAGA,IAAI;MAC1B;MACAE,KAAK,CAACF,IAAI,CAAC,GAAGC,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,GAAG,EAAE,GAAG,IAAI,CAAC;IAC3D;EACF;AACF;AACA,SAASK,MAAMA,CAACjC,EAAE,EAAEkC,QAAQ,EAAE;EAC5B,IAAIC,iBAAiB,GAAG,EAAE;EAC1B,IAAI,OAAOnC,EAAE,KAAK,QAAQ,EAAE;IAC1BmC,iBAAiB,GAAGnC,EAAE;EACxB,CAAC,MAAM;IACL,GAAG;MACD,IAAIoC,SAAS,GAAGV,GAAG,CAAC1B,EAAE,EAAE,WAAW,CAAC;MACpC,IAAIoC,SAAS,IAAIA,SAAS,KAAK,MAAM,EAAE;QACrCD,iBAAiB,GAAGC,SAAS,GAAG,GAAG,GAAGD,iBAAiB;MACzD;MACA;IACF,CAAC,QAAQ,CAACD,QAAQ,KAAKlC,EAAE,GAAGA,EAAE,CAACgB,UAAU,CAAC;EAC5C;EACA,IAAIqB,QAAQ,GAAGlD,MAAM,CAACmD,SAAS,IAAInD,MAAM,CAACoD,eAAe,IAAIpD,MAAM,CAACqD,SAAS,IAAIrD,MAAM,CAACsD,WAAW;EACnG;EACA,OAAOJ,QAAQ,IAAI,IAAIA,QAAQ,CAACF,iBAAiB,CAAC;AACpD;AACA,SAASO,IAAIA,CAACxB,GAAG,EAAEyB,OAAO,EAAEjG,QAAQ,EAAE;EACpC,IAAIwE,GAAG,EAAE;IACP,IAAI0B,IAAI,GAAG1B,GAAG,CAAC2B,oBAAoB,CAACF,OAAO,CAAC;MAC1C9G,CAAC,GAAG,CAAC;MACL2C,CAAC,GAAGoE,IAAI,CAAC7G,MAAM;IACjB,IAAIW,QAAQ,EAAE;MACZ,OAAOb,CAAC,GAAG2C,CAAC,EAAE3C,CAAC,EAAE,EAAE;QACjBa,QAAQ,CAACkG,IAAI,CAAC/G,CAAC,CAAC,EAAEA,CAAC,CAAC;MACtB;IACF;IACA,OAAO+G,IAAI;EACb;EACA,OAAO,EAAE;AACX;AACA,SAASE,yBAAyBA,CAAA,EAAG;EACnC,IAAIC,gBAAgB,GAAGjC,QAAQ,CAACiC,gBAAgB;EAChD,IAAIA,gBAAgB,EAAE;IACpB,OAAOA,gBAAgB;EACzB,CAAC,MAAM;IACL,OAAOjC,QAAQ,CAACkC,eAAe;EACjC;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACjD,EAAE,EAAEkD,yBAAyB,EAAEC,yBAAyB,EAAEC,SAAS,EAAEC,SAAS,EAAE;EAC/F,IAAI,CAACrD,EAAE,CAACsD,qBAAqB,IAAItD,EAAE,KAAKb,MAAM,EAAE;EAChD,IAAIoE,MAAM,EAAEC,GAAG,EAAEC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,KAAK;EACnD,IAAI7D,EAAE,KAAKb,MAAM,IAAIa,EAAE,CAACgB,UAAU,IAAIhB,EAAE,KAAK8C,yBAAyB,CAAC,CAAC,EAAE;IACxES,MAAM,GAAGvD,EAAE,CAACsD,qBAAqB,CAAC,CAAC;IACnCE,GAAG,GAAGD,MAAM,CAACC,GAAG;IAChBC,IAAI,GAAGF,MAAM,CAACE,IAAI;IAClBC,MAAM,GAAGH,MAAM,CAACG,MAAM;IACtBC,KAAK,GAAGJ,MAAM,CAACI,KAAK;IACpBC,MAAM,GAAGL,MAAM,CAACK,MAAM;IACtBC,KAAK,GAAGN,MAAM,CAACM,KAAK;EACtB,CAAC,MAAM;IACLL,GAAG,GAAG,CAAC;IACPC,IAAI,GAAG,CAAC;IACRC,MAAM,GAAGvE,MAAM,CAAC2E,WAAW;IAC3BH,KAAK,GAAGxE,MAAM,CAAC4E,UAAU;IACzBH,MAAM,GAAGzE,MAAM,CAAC2E,WAAW;IAC3BD,KAAK,GAAG1E,MAAM,CAAC4E,UAAU;EAC3B;EACA,IAAI,CAACb,yBAAyB,IAAIC,yBAAyB,KAAKnD,EAAE,KAAKb,MAAM,EAAE;IAC7E;IACAkE,SAAS,GAAGA,SAAS,IAAIrD,EAAE,CAACgB,UAAU;;IAEtC;IACA;IACA,IAAI,CAAC1B,UAAU,EAAE;MACf,GAAG;QACD,IAAI+D,SAAS,IAAIA,SAAS,CAACC,qBAAqB,KAAK5B,GAAG,CAAC2B,SAAS,EAAE,WAAW,CAAC,KAAK,MAAM,IAAIF,yBAAyB,IAAIzB,GAAG,CAAC2B,SAAS,EAAE,UAAU,CAAC,KAAK,QAAQ,CAAC,EAAE;UACpK,IAAIW,aAAa,GAAGX,SAAS,CAACC,qBAAqB,CAAC,CAAC;;UAErD;UACAE,GAAG,IAAIQ,aAAa,CAACR,GAAG,GAAGS,QAAQ,CAACvC,GAAG,CAAC2B,SAAS,EAAE,kBAAkB,CAAC,CAAC;UACvEI,IAAI,IAAIO,aAAa,CAACP,IAAI,GAAGQ,QAAQ,CAACvC,GAAG,CAAC2B,SAAS,EAAE,mBAAmB,CAAC,CAAC;UAC1EK,MAAM,GAAGF,GAAG,GAAGD,MAAM,CAACK,MAAM;UAC5BD,KAAK,GAAGF,IAAI,GAAGF,MAAM,CAACM,KAAK;UAC3B;QACF;QACA;MACF,CAAC,QAAQR,SAAS,GAAGA,SAAS,CAACrC,UAAU;IAC3C;EACF;EACA,IAAIoC,SAAS,IAAIpD,EAAE,KAAKb,MAAM,EAAE;IAC9B;IACA,IAAI+E,QAAQ,GAAGjC,MAAM,CAACoB,SAAS,IAAIrD,EAAE,CAAC;MACpCmE,MAAM,GAAGD,QAAQ,IAAIA,QAAQ,CAACE,CAAC;MAC/BC,MAAM,GAAGH,QAAQ,IAAIA,QAAQ,CAACI,CAAC;IACjC,IAAIJ,QAAQ,EAAE;MACZV,GAAG,IAAIa,MAAM;MACbZ,IAAI,IAAIU,MAAM;MACdN,KAAK,IAAIM,MAAM;MACfP,MAAM,IAAIS,MAAM;MAChBX,MAAM,GAAGF,GAAG,GAAGI,MAAM;MACrBD,KAAK,GAAGF,IAAI,GAAGI,KAAK;IACtB;EACF;EACA,OAAO;IACLL,GAAG,EAAEA,GAAG;IACRC,IAAI,EAAEA,IAAI;IACVC,MAAM,EAAEA,MAAM;IACdC,KAAK,EAAEA,KAAK;IACZE,KAAK,EAAEA,KAAK;IACZD,MAAM,EAAEA;EACV,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASW,cAAcA,CAACvE,EAAE,EAAEwE,MAAM,EAAEC,UAAU,EAAE;EAC9C,IAAIC,MAAM,GAAGC,0BAA0B,CAAC3E,EAAE,EAAE,IAAI,CAAC;IAC/C4E,SAAS,GAAG3B,OAAO,CAACjD,EAAE,CAAC,CAACwE,MAAM,CAAC;;EAEjC;EACA,OAAOE,MAAM,EAAE;IACb,IAAIG,aAAa,GAAG5B,OAAO,CAACyB,MAAM,CAAC,CAACD,UAAU,CAAC;MAC7CK,OAAO,GAAG,KAAK,CAAC;IAClB,IAAIL,UAAU,KAAK,KAAK,IAAIA,UAAU,KAAK,MAAM,EAAE;MACjDK,OAAO,GAAGF,SAAS,IAAIC,aAAa;IACtC,CAAC,MAAM;MACLC,OAAO,GAAGF,SAAS,IAAIC,aAAa;IACtC;IACA,IAAI,CAACC,OAAO,EAAE,OAAOJ,MAAM;IAC3B,IAAIA,MAAM,KAAK5B,yBAAyB,CAAC,CAAC,EAAE;IAC5C4B,MAAM,GAAGC,0BAA0B,CAACD,MAAM,EAAE,KAAK,CAAC;EACpD;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,QAAQA,CAAC/E,EAAE,EAAEgF,QAAQ,EAAEC,OAAO,EAAEC,aAAa,EAAE;EACtD,IAAIC,YAAY,GAAG,CAAC;IAClBtJ,CAAC,GAAG,CAAC;IACLuJ,QAAQ,GAAGpF,EAAE,CAACoF,QAAQ;EACxB,OAAOvJ,CAAC,GAAGuJ,QAAQ,CAACrJ,MAAM,EAAE;IAC1B,IAAIqJ,QAAQ,CAACvJ,CAAC,CAAC,CAACgG,KAAK,CAACwD,OAAO,KAAK,MAAM,IAAID,QAAQ,CAACvJ,CAAC,CAAC,KAAKyJ,QAAQ,CAACC,KAAK,KAAKL,aAAa,IAAIE,QAAQ,CAACvJ,CAAC,CAAC,KAAKyJ,QAAQ,CAACE,OAAO,CAAC,IAAIvE,OAAO,CAACmE,QAAQ,CAACvJ,CAAC,CAAC,EAAEoJ,OAAO,CAACQ,SAAS,EAAEzF,EAAE,EAAE,KAAK,CAAC,EAAE;MACvL,IAAImF,YAAY,KAAKH,QAAQ,EAAE;QAC7B,OAAOI,QAAQ,CAACvJ,CAAC,CAAC;MACpB;MACAsJ,YAAY,EAAE;IAChB;IACAtJ,CAAC,EAAE;EACL;EACA,OAAO,IAAI;AACb;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6J,SAASA,CAAC1F,EAAE,EAAEO,QAAQ,EAAE;EAC/B,IAAIoF,IAAI,GAAG3F,EAAE,CAAC4F,gBAAgB;EAC9B,OAAOD,IAAI,KAAKA,IAAI,KAAKL,QAAQ,CAACC,KAAK,IAAI7D,GAAG,CAACiE,IAAI,EAAE,SAAS,CAAC,KAAK,MAAM,IAAIpF,QAAQ,IAAI,CAACD,OAAO,CAACqF,IAAI,EAAEpF,QAAQ,CAAC,CAAC,EAAE;IACnHoF,IAAI,GAAGA,IAAI,CAACE,sBAAsB;EACpC;EACA,OAAOF,IAAI,IAAI,IAAI;AACrB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASG,KAAKA,CAAC9F,EAAE,EAAEO,QAAQ,EAAE;EAC3B,IAAIuF,KAAK,GAAG,CAAC;EACb,IAAI,CAAC9F,EAAE,IAAI,CAACA,EAAE,CAACgB,UAAU,EAAE;IACzB,OAAO,CAAC,CAAC;EACX;;EAEA;EACA,OAAOhB,EAAE,GAAGA,EAAE,CAAC6F,sBAAsB,EAAE;IACrC,IAAI7F,EAAE,CAAC+F,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,UAAU,IAAIhG,EAAE,KAAKsF,QAAQ,CAACW,KAAK,KAAK,CAAC1F,QAAQ,IAAID,OAAO,CAACN,EAAE,EAAEO,QAAQ,CAAC,CAAC,EAAE;MAC7GuF,KAAK,EAAE;IACT;EACF;EACA,OAAOA,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,uBAAuBA,CAAClG,EAAE,EAAE;EACnC,IAAImG,UAAU,GAAG,CAAC;IAChBC,SAAS,GAAG,CAAC;IACbC,WAAW,GAAGvD,yBAAyB,CAAC,CAAC;EAC3C,IAAI9C,EAAE,EAAE;IACN,GAAG;MACD,IAAIkE,QAAQ,GAAGjC,MAAM,CAACjC,EAAE,CAAC;QACvBmE,MAAM,GAAGD,QAAQ,CAACE,CAAC;QACnBC,MAAM,GAAGH,QAAQ,CAACI,CAAC;MACrB6B,UAAU,IAAInG,EAAE,CAACsG,UAAU,GAAGnC,MAAM;MACpCiC,SAAS,IAAIpG,EAAE,CAACuG,SAAS,GAAGlC,MAAM;IACpC,CAAC,QAAQrE,EAAE,KAAKqG,WAAW,KAAKrG,EAAE,GAAGA,EAAE,CAACgB,UAAU,CAAC;EACrD;EACA,OAAO,CAACmF,UAAU,EAAEC,SAAS,CAAC;AAChC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,aAAaA,CAAC5I,GAAG,EAAEpB,GAAG,EAAE;EAC/B,KAAK,IAAIX,CAAC,IAAI+B,GAAG,EAAE;IACjB,IAAI,CAACA,GAAG,CAACV,cAAc,CAACrB,CAAC,CAAC,EAAE;IAC5B,KAAK,IAAIK,GAAG,IAAIM,GAAG,EAAE;MACnB,IAAIA,GAAG,CAACU,cAAc,CAAChB,GAAG,CAAC,IAAIM,GAAG,CAACN,GAAG,CAAC,KAAK0B,GAAG,CAAC/B,CAAC,CAAC,CAACK,GAAG,CAAC,EAAE,OAAOuK,MAAM,CAAC5K,CAAC,CAAC;IAC3E;EACF;EACA,OAAO,CAAC,CAAC;AACX;AACA,SAAS8I,0BAA0BA,CAAC3E,EAAE,EAAE0G,WAAW,EAAE;EACnD;EACA,IAAI,CAAC1G,EAAE,IAAI,CAACA,EAAE,CAACsD,qBAAqB,EAAE,OAAOR,yBAAyB,CAAC,CAAC;EACxE,IAAI6D,IAAI,GAAG3G,EAAE;EACb,IAAI4G,OAAO,GAAG,KAAK;EACnB,GAAG;IACD;IACA,IAAID,IAAI,CAACE,WAAW,GAAGF,IAAI,CAACG,WAAW,IAAIH,IAAI,CAACI,YAAY,GAAGJ,IAAI,CAACK,YAAY,EAAE;MAChF,IAAIC,OAAO,GAAGvF,GAAG,CAACiF,IAAI,CAAC;MACvB,IAAIA,IAAI,CAACE,WAAW,GAAGF,IAAI,CAACG,WAAW,KAAKG,OAAO,CAACC,SAAS,IAAI,MAAM,IAAID,OAAO,CAACC,SAAS,IAAI,QAAQ,CAAC,IAAIP,IAAI,CAACI,YAAY,GAAGJ,IAAI,CAACK,YAAY,KAAKC,OAAO,CAACE,SAAS,IAAI,MAAM,IAAIF,OAAO,CAACE,SAAS,IAAI,QAAQ,CAAC,EAAE;QACpN,IAAI,CAACR,IAAI,CAACrD,qBAAqB,IAAIqD,IAAI,KAAK7F,QAAQ,CAACsG,IAAI,EAAE,OAAOtE,yBAAyB,CAAC,CAAC;QAC7F,IAAI8D,OAAO,IAAIF,WAAW,EAAE,OAAOC,IAAI;QACvCC,OAAO,GAAG,IAAI;MAChB;IACF;IACA;EACF,CAAC,QAAQD,IAAI,GAAGA,IAAI,CAAC3F,UAAU;EAC/B,OAAO8B,yBAAyB,CAAC,CAAC;AACpC;AACA,SAASuE,MAAMA,CAACC,GAAG,EAAEC,GAAG,EAAE;EACxB,IAAID,GAAG,IAAIC,GAAG,EAAE;IACd,KAAK,IAAIrL,GAAG,IAAIqL,GAAG,EAAE;MACnB,IAAIA,GAAG,CAACrK,cAAc,CAAChB,GAAG,CAAC,EAAE;QAC3BoL,GAAG,CAACpL,GAAG,CAAC,GAAGqL,GAAG,CAACrL,GAAG,CAAC;MACrB;IACF;EACF;EACA,OAAOoL,GAAG;AACZ;AACA,SAASE,WAAWA,CAACC,KAAK,EAAEC,KAAK,EAAE;EACjC,OAAOC,IAAI,CAACC,KAAK,CAACH,KAAK,CAACjE,GAAG,CAAC,KAAKmE,IAAI,CAACC,KAAK,CAACF,KAAK,CAAClE,GAAG,CAAC,IAAImE,IAAI,CAACC,KAAK,CAACH,KAAK,CAAChE,IAAI,CAAC,KAAKkE,IAAI,CAACC,KAAK,CAACF,KAAK,CAACjE,IAAI,CAAC,IAAIkE,IAAI,CAACC,KAAK,CAACH,KAAK,CAAC7D,MAAM,CAAC,KAAK+D,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC9D,MAAM,CAAC,IAAI+D,IAAI,CAACC,KAAK,CAACH,KAAK,CAAC5D,KAAK,CAAC,KAAK8D,IAAI,CAACC,KAAK,CAACF,KAAK,CAAC7D,KAAK,CAAC;AAC7N;AACA,IAAIgE,gBAAgB;AACpB,SAASC,QAAQA,CAACC,QAAQ,EAAEC,EAAE,EAAE;EAC9B,OAAO,YAAY;IACjB,IAAI,CAACH,gBAAgB,EAAE;MACrB,IAAII,IAAI,GAAGnM,SAAS;QAClBoM,KAAK,GAAG,IAAI;MACd,IAAID,IAAI,CAAClM,MAAM,KAAK,CAAC,EAAE;QACrBgM,QAAQ,CAAC5K,IAAI,CAAC+K,KAAK,EAAED,IAAI,CAAC,CAAC,CAAC,CAAC;MAC/B,CAAC,MAAM;QACLF,QAAQ,CAACrM,KAAK,CAACwM,KAAK,EAAED,IAAI,CAAC;MAC7B;MACAJ,gBAAgB,GAAGM,UAAU,CAAC,YAAY;QACxCN,gBAAgB,GAAG,KAAK,CAAC;MAC3B,CAAC,EAAEG,EAAE,CAAC;IACR;EACF,CAAC;AACH;AACA,SAASI,cAAcA,CAAA,EAAG;EACxBC,YAAY,CAACR,gBAAgB,CAAC;EAC9BA,gBAAgB,GAAG,KAAK,CAAC;AAC3B;AACA,SAASS,QAAQA,CAACtI,EAAE,EAAEuI,CAAC,EAAEC,CAAC,EAAE;EAC1BxI,EAAE,CAACsG,UAAU,IAAIiC,CAAC;EAClBvI,EAAE,CAACuG,SAAS,IAAIiC,CAAC;AACnB;AACA,SAASvC,KAAKA,CAACjG,EAAE,EAAE;EACjB,IAAIyI,OAAO,GAAGtJ,MAAM,CAACsJ,OAAO;EAC5B,IAAIC,CAAC,GAAGvJ,MAAM,CAACwJ,MAAM,IAAIxJ,MAAM,CAACyJ,KAAK;EACrC,IAAIH,OAAO,IAAIA,OAAO,CAACI,GAAG,EAAE;IAC1B,OAAOJ,OAAO,CAACI,GAAG,CAAC7I,EAAE,CAAC,CAAC8I,SAAS,CAAC,IAAI,CAAC;EACxC,CAAC,MAAM,IAAIJ,CAAC,EAAE;IACZ,OAAOA,CAAC,CAAC1I,EAAE,CAAC,CAACiG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;EAC7B,CAAC,MAAM;IACL,OAAOjG,EAAE,CAAC8I,SAAS,CAAC,IAAI,CAAC;EAC3B;AACF;AACA,SAASC,OAAOA,CAAC/I,EAAE,EAAEgJ,IAAI,EAAE;EACzBtH,GAAG,CAAC1B,EAAE,EAAE,UAAU,EAAE,UAAU,CAAC;EAC/B0B,GAAG,CAAC1B,EAAE,EAAE,KAAK,EAAEgJ,IAAI,CAACxF,GAAG,CAAC;EACxB9B,GAAG,CAAC1B,EAAE,EAAE,MAAM,EAAEgJ,IAAI,CAACvF,IAAI,CAAC;EAC1B/B,GAAG,CAAC1B,EAAE,EAAE,OAAO,EAAEgJ,IAAI,CAACnF,KAAK,CAAC;EAC5BnC,GAAG,CAAC1B,EAAE,EAAE,QAAQ,EAAEgJ,IAAI,CAACpF,MAAM,CAAC;AAChC;AACA,SAASqF,SAASA,CAACjJ,EAAE,EAAE;EACrB0B,GAAG,CAAC1B,EAAE,EAAE,UAAU,EAAE,EAAE,CAAC;EACvB0B,GAAG,CAAC1B,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC;EAClB0B,GAAG,CAAC1B,EAAE,EAAE,MAAM,EAAE,EAAE,CAAC;EACnB0B,GAAG,CAAC1B,EAAE,EAAE,OAAO,EAAE,EAAE,CAAC;EACpB0B,GAAG,CAAC1B,EAAE,EAAE,QAAQ,EAAE,EAAE,CAAC;AACvB;AACA,SAASkJ,iCAAiCA,CAAC7F,SAAS,EAAE4B,OAAO,EAAEkE,OAAO,EAAE;EACtE,IAAIH,IAAI,GAAG,CAAC,CAAC;EACb/K,KAAK,CAACI,IAAI,CAACgF,SAAS,CAAC+B,QAAQ,CAAC,CAACnJ,OAAO,CAAC,UAAUmN,KAAK,EAAE;IACtD,IAAIC,UAAU,EAAEC,SAAS,EAAEC,WAAW,EAAEC,YAAY;IACpD,IAAI,CAACvI,OAAO,CAACmI,KAAK,EAAEnE,OAAO,CAACQ,SAAS,EAAEpC,SAAS,EAAE,KAAK,CAAC,IAAI+F,KAAK,CAACK,QAAQ,IAAIL,KAAK,KAAKD,OAAO,EAAE;IACjG,IAAIO,SAAS,GAAGzG,OAAO,CAACmG,KAAK,CAAC;IAC9BJ,IAAI,CAACvF,IAAI,GAAGkE,IAAI,CAACgC,GAAG,CAAC,CAACN,UAAU,GAAGL,IAAI,CAACvF,IAAI,MAAM,IAAI,IAAI4F,UAAU,KAAK,KAAK,CAAC,GAAGA,UAAU,GAAGO,QAAQ,EAAEF,SAAS,CAACjG,IAAI,CAAC;IACxHuF,IAAI,CAACxF,GAAG,GAAGmE,IAAI,CAACgC,GAAG,CAAC,CAACL,SAAS,GAAGN,IAAI,CAACxF,GAAG,MAAM,IAAI,IAAI8F,SAAS,KAAK,KAAK,CAAC,GAAGA,SAAS,GAAGM,QAAQ,EAAEF,SAAS,CAAClG,GAAG,CAAC;IAClHwF,IAAI,CAACrF,KAAK,GAAGgE,IAAI,CAACkC,GAAG,CAAC,CAACN,WAAW,GAAGP,IAAI,CAACrF,KAAK,MAAM,IAAI,IAAI4F,WAAW,KAAK,KAAK,CAAC,GAAGA,WAAW,GAAG,CAACK,QAAQ,EAAEF,SAAS,CAAC/F,KAAK,CAAC;IAC/HqF,IAAI,CAACtF,MAAM,GAAGiE,IAAI,CAACkC,GAAG,CAAC,CAACL,YAAY,GAAGR,IAAI,CAACtF,MAAM,MAAM,IAAI,IAAI8F,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG,CAACI,QAAQ,EAAEF,SAAS,CAAChG,MAAM,CAAC;EACvI,CAAC,CAAC;EACFsF,IAAI,CAACnF,KAAK,GAAGmF,IAAI,CAACrF,KAAK,GAAGqF,IAAI,CAACvF,IAAI;EACnCuF,IAAI,CAACpF,MAAM,GAAGoF,IAAI,CAACtF,MAAM,GAAGsF,IAAI,CAACxF,GAAG;EACpCwF,IAAI,CAACT,CAAC,GAAGS,IAAI,CAACvF,IAAI;EAClBuF,IAAI,CAACR,CAAC,GAAGQ,IAAI,CAACxF,GAAG;EACjB,OAAOwF,IAAI;AACb;AACA,IAAIc,OAAO,GAAG,UAAU,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;AAE/C,SAASC,qBAAqBA,CAAA,EAAG;EAC/B,IAAIC,eAAe,GAAG,EAAE;IACtBC,mBAAmB;EACrB,OAAO;IACLC,qBAAqB,EAAE,SAASA,qBAAqBA,CAAA,EAAG;MACtDF,eAAe,GAAG,EAAE;MACpB,IAAI,CAAC,IAAI,CAACjF,OAAO,CAACoF,SAAS,EAAE;MAC7B,IAAIjF,QAAQ,GAAG,EAAE,CAAC1G,KAAK,CAACvB,IAAI,CAAC,IAAI,CAAC6C,EAAE,CAACoF,QAAQ,CAAC;MAC9CA,QAAQ,CAACnJ,OAAO,CAAC,UAAUmN,KAAK,EAAE;QAChC,IAAI1H,GAAG,CAAC0H,KAAK,EAAE,SAAS,CAAC,KAAK,MAAM,IAAIA,KAAK,KAAK9D,QAAQ,CAACC,KAAK,EAAE;QAClE2E,eAAe,CAACzO,IAAI,CAAC;UACnBG,MAAM,EAAEwN,KAAK;UACbJ,IAAI,EAAE/F,OAAO,CAACmG,KAAK;QACrB,CAAC,CAAC;QACF,IAAIkB,QAAQ,GAAG3O,cAAc,CAAC,CAAC,CAAC,EAAEuO,eAAe,CAACA,eAAe,CAACnO,MAAM,GAAG,CAAC,CAAC,CAACiN,IAAI,CAAC;;QAEnF;QACA,IAAII,KAAK,CAACmB,qBAAqB,EAAE;UAC/B,IAAIC,WAAW,GAAGvI,MAAM,CAACmH,KAAK,EAAE,IAAI,CAAC;UACrC,IAAIoB,WAAW,EAAE;YACfF,QAAQ,CAAC9G,GAAG,IAAIgH,WAAW,CAACC,CAAC;YAC7BH,QAAQ,CAAC7G,IAAI,IAAI+G,WAAW,CAACE,CAAC;UAChC;QACF;QACAtB,KAAK,CAACkB,QAAQ,GAAGA,QAAQ;MAC3B,CAAC,CAAC;IACJ,CAAC;IACDK,iBAAiB,EAAE,SAASA,iBAAiBA,CAACrJ,KAAK,EAAE;MACnD4I,eAAe,CAACzO,IAAI,CAAC6F,KAAK,CAAC;IAC7B,CAAC;IACDsJ,oBAAoB,EAAE,SAASA,oBAAoBA,CAAChP,MAAM,EAAE;MAC1DsO,eAAe,CAACW,MAAM,CAACrE,aAAa,CAAC0D,eAAe,EAAE;QACpDtO,MAAM,EAAEA;MACV,CAAC,CAAC,EAAE,CAAC,CAAC;IACR,CAAC;IACDkP,UAAU,EAAE,SAASA,UAAUA,CAAC/C,QAAQ,EAAE;MACxC,IAAIG,KAAK,GAAG,IAAI;MAChB,IAAI,CAAC,IAAI,CAACjD,OAAO,CAACoF,SAAS,EAAE;QAC3BhC,YAAY,CAAC8B,mBAAmB,CAAC;QACjC,IAAI,OAAOpC,QAAQ,KAAK,UAAU,EAAEA,QAAQ,CAAC,CAAC;QAC9C;MACF;MACA,IAAIgD,SAAS,GAAG,KAAK;QACnBC,aAAa,GAAG,CAAC;MACnBd,eAAe,CAACjO,OAAO,CAAC,UAAUqF,KAAK,EAAE;QACvC,IAAI2J,IAAI,GAAG,CAAC;UACVrP,MAAM,GAAG0F,KAAK,CAAC1F,MAAM;UACrB0O,QAAQ,GAAG1O,MAAM,CAAC0O,QAAQ;UAC1BY,MAAM,GAAGjI,OAAO,CAACrH,MAAM,CAAC;UACxBuP,YAAY,GAAGvP,MAAM,CAACuP,YAAY;UAClCC,UAAU,GAAGxP,MAAM,CAACwP,UAAU;UAC9BC,aAAa,GAAG/J,KAAK,CAAC0H,IAAI;UAC1BsC,YAAY,GAAGrJ,MAAM,CAACrG,MAAM,EAAE,IAAI,CAAC;QACrC,IAAI0P,YAAY,EAAE;UAChB;UACAJ,MAAM,CAAC1H,GAAG,IAAI8H,YAAY,CAACb,CAAC;UAC5BS,MAAM,CAACzH,IAAI,IAAI6H,YAAY,CAACZ,CAAC;QAC/B;QACA9O,MAAM,CAACsP,MAAM,GAAGA,MAAM;QACtB,IAAItP,MAAM,CAAC2O,qBAAqB,EAAE;UAChC;UACA,IAAI/C,WAAW,CAAC2D,YAAY,EAAED,MAAM,CAAC,IAAI,CAAC1D,WAAW,CAAC8C,QAAQ,EAAEY,MAAM,CAAC;UACvE;UACA,CAACG,aAAa,CAAC7H,GAAG,GAAG0H,MAAM,CAAC1H,GAAG,KAAK6H,aAAa,CAAC5H,IAAI,GAAGyH,MAAM,CAACzH,IAAI,CAAC,KAAK,CAAC6G,QAAQ,CAAC9G,GAAG,GAAG0H,MAAM,CAAC1H,GAAG,KAAK8G,QAAQ,CAAC7G,IAAI,GAAGyH,MAAM,CAACzH,IAAI,CAAC,EAAE;YACrI;YACAwH,IAAI,GAAGM,iBAAiB,CAACF,aAAa,EAAEF,YAAY,EAAEC,UAAU,EAAElD,KAAK,CAACjD,OAAO,CAAC;UAClF;QACF;;QAEA;QACA,IAAI,CAACuC,WAAW,CAAC0D,MAAM,EAAEZ,QAAQ,CAAC,EAAE;UAClC1O,MAAM,CAACuP,YAAY,GAAGb,QAAQ;UAC9B1O,MAAM,CAACwP,UAAU,GAAGF,MAAM;UAC1B,IAAI,CAACD,IAAI,EAAE;YACTA,IAAI,GAAG/C,KAAK,CAACjD,OAAO,CAACoF,SAAS;UAChC;UACAnC,KAAK,CAACsD,OAAO,CAAC5P,MAAM,EAAEyP,aAAa,EAAEH,MAAM,EAAED,IAAI,CAAC;QACpD;QACA,IAAIA,IAAI,EAAE;UACRF,SAAS,GAAG,IAAI;UAChBC,aAAa,GAAGrD,IAAI,CAACkC,GAAG,CAACmB,aAAa,EAAEC,IAAI,CAAC;UAC7C5C,YAAY,CAACzM,MAAM,CAAC6P,mBAAmB,CAAC;UACxC7P,MAAM,CAAC6P,mBAAmB,GAAGtD,UAAU,CAAC,YAAY;YAClDvM,MAAM,CAACoP,aAAa,GAAG,CAAC;YACxBpP,MAAM,CAACuP,YAAY,GAAG,IAAI;YAC1BvP,MAAM,CAAC0O,QAAQ,GAAG,IAAI;YACtB1O,MAAM,CAACwP,UAAU,GAAG,IAAI;YACxBxP,MAAM,CAAC2O,qBAAqB,GAAG,IAAI;UACrC,CAAC,EAAEU,IAAI,CAAC;UACRrP,MAAM,CAAC2O,qBAAqB,GAAGU,IAAI;QACrC;MACF,CAAC,CAAC;MACF5C,YAAY,CAAC8B,mBAAmB,CAAC;MACjC,IAAI,CAACY,SAAS,EAAE;QACd,IAAI,OAAOhD,QAAQ,KAAK,UAAU,EAAEA,QAAQ,CAAC,CAAC;MAChD,CAAC,MAAM;QACLoC,mBAAmB,GAAGhC,UAAU,CAAC,YAAY;UAC3C,IAAI,OAAOJ,QAAQ,KAAK,UAAU,EAAEA,QAAQ,CAAC,CAAC;QAChD,CAAC,EAAEiD,aAAa,CAAC;MACnB;MACAd,eAAe,GAAG,EAAE;IACtB,CAAC;IACDsB,OAAO,EAAE,SAASA,OAAOA,CAAC5P,MAAM,EAAE8P,WAAW,EAAER,MAAM,EAAES,QAAQ,EAAE;MAC/D,IAAIA,QAAQ,EAAE;QACZjK,GAAG,CAAC9F,MAAM,EAAE,YAAY,EAAE,EAAE,CAAC;QAC7B8F,GAAG,CAAC9F,MAAM,EAAE,WAAW,EAAE,EAAE,CAAC;QAC5B,IAAIsI,QAAQ,GAAGjC,MAAM,CAAC,IAAI,CAACjC,EAAE,CAAC;UAC5BmE,MAAM,GAAGD,QAAQ,IAAIA,QAAQ,CAACE,CAAC;UAC/BC,MAAM,GAAGH,QAAQ,IAAIA,QAAQ,CAACI,CAAC;UAC/BsH,UAAU,GAAG,CAACF,WAAW,CAACjI,IAAI,GAAGyH,MAAM,CAACzH,IAAI,KAAKU,MAAM,IAAI,CAAC,CAAC;UAC7D0H,UAAU,GAAG,CAACH,WAAW,CAAClI,GAAG,GAAG0H,MAAM,CAAC1H,GAAG,KAAKa,MAAM,IAAI,CAAC,CAAC;QAC7DzI,MAAM,CAACkQ,UAAU,GAAG,CAAC,CAACF,UAAU;QAChChQ,MAAM,CAACmQ,UAAU,GAAG,CAAC,CAACF,UAAU;QAChCnK,GAAG,CAAC9F,MAAM,EAAE,WAAW,EAAE,cAAc,GAAGgQ,UAAU,GAAG,KAAK,GAAGC,UAAU,GAAG,OAAO,CAAC;QACpF,IAAI,CAACG,eAAe,GAAGC,OAAO,CAACrQ,MAAM,CAAC,CAAC,CAAC;;QAExC8F,GAAG,CAAC9F,MAAM,EAAE,YAAY,EAAE,YAAY,GAAG+P,QAAQ,GAAG,IAAI,IAAI,IAAI,CAAC1G,OAAO,CAACiH,MAAM,GAAG,GAAG,GAAG,IAAI,CAACjH,OAAO,CAACiH,MAAM,GAAG,EAAE,CAAC,CAAC;QAClHxK,GAAG,CAAC9F,MAAM,EAAE,WAAW,EAAE,oBAAoB,CAAC;QAC9C,OAAOA,MAAM,CAAC6N,QAAQ,KAAK,QAAQ,IAAIpB,YAAY,CAACzM,MAAM,CAAC6N,QAAQ,CAAC;QACpE7N,MAAM,CAAC6N,QAAQ,GAAGtB,UAAU,CAAC,YAAY;UACvCzG,GAAG,CAAC9F,MAAM,EAAE,YAAY,EAAE,EAAE,CAAC;UAC7B8F,GAAG,CAAC9F,MAAM,EAAE,WAAW,EAAE,EAAE,CAAC;UAC5BA,MAAM,CAAC6N,QAAQ,GAAG,KAAK;UACvB7N,MAAM,CAACkQ,UAAU,GAAG,KAAK;UACzBlQ,MAAM,CAACmQ,UAAU,GAAG,KAAK;QAC3B,CAAC,EAAEJ,QAAQ,CAAC;MACd;IACF;EACF,CAAC;AACH;AACA,SAASM,OAAOA,CAACrQ,MAAM,EAAE;EACvB,OAAOA,MAAM,CAACuQ,WAAW;AAC3B;AACA,SAASZ,iBAAiBA,CAACF,aAAa,EAAEf,QAAQ,EAAEY,MAAM,EAAEjG,OAAO,EAAE;EACnE,OAAO0C,IAAI,CAACyE,IAAI,CAACzE,IAAI,CAAC0E,GAAG,CAAC/B,QAAQ,CAAC9G,GAAG,GAAG6H,aAAa,CAAC7H,GAAG,EAAE,CAAC,CAAC,GAAGmE,IAAI,CAAC0E,GAAG,CAAC/B,QAAQ,CAAC7G,IAAI,GAAG4H,aAAa,CAAC5H,IAAI,EAAE,CAAC,CAAC,CAAC,GAAGkE,IAAI,CAACyE,IAAI,CAACzE,IAAI,CAAC0E,GAAG,CAAC/B,QAAQ,CAAC9G,GAAG,GAAG0H,MAAM,CAAC1H,GAAG,EAAE,CAAC,CAAC,GAAGmE,IAAI,CAAC0E,GAAG,CAAC/B,QAAQ,CAAC7G,IAAI,GAAGyH,MAAM,CAACzH,IAAI,EAAE,CAAC,CAAC,CAAC,GAAGwB,OAAO,CAACoF,SAAS;AACtO;AAEA,IAAIiC,OAAO,GAAG,EAAE;AAChB,IAAIC,QAAQ,GAAG;EACbC,mBAAmB,EAAE;AACvB,CAAC;AACD,IAAIC,aAAa,GAAG;EAClBC,KAAK,EAAE,SAASA,KAAKA,CAACC,MAAM,EAAE;IAC5B;IACA,KAAK,IAAIC,MAAM,IAAIL,QAAQ,EAAE;MAC3B,IAAIA,QAAQ,CAACrP,cAAc,CAAC0P,MAAM,CAAC,IAAI,EAAEA,MAAM,IAAID,MAAM,CAAC,EAAE;QAC1DA,MAAM,CAACC,MAAM,CAAC,GAAGL,QAAQ,CAACK,MAAM,CAAC;MACnC;IACF;IACAN,OAAO,CAACrQ,OAAO,CAAC,UAAU4Q,CAAC,EAAE;MAC3B,IAAIA,CAAC,CAACC,UAAU,KAAKH,MAAM,CAACG,UAAU,EAAE;QACtC,MAAM,gCAAgC,CAACC,MAAM,CAACJ,MAAM,CAACG,UAAU,EAAE,iBAAiB,CAAC;MACrF;IACF,CAAC,CAAC;IACFR,OAAO,CAAC7Q,IAAI,CAACkR,MAAM,CAAC;EACtB,CAAC;EACDK,WAAW,EAAE,SAASA,WAAWA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,GAAG,EAAE;IAC1D,IAAIjF,KAAK,GAAG,IAAI;IAChB,IAAI,CAACkF,aAAa,GAAG,KAAK;IAC1BD,GAAG,CAACE,MAAM,GAAG,YAAY;MACvBnF,KAAK,CAACkF,aAAa,GAAG,IAAI;IAC5B,CAAC;IACD,IAAIE,eAAe,GAAGL,SAAS,GAAG,QAAQ;IAC1CX,OAAO,CAACrQ,OAAO,CAAC,UAAU0Q,MAAM,EAAE;MAChC,IAAI,CAACO,QAAQ,CAACP,MAAM,CAACG,UAAU,CAAC,EAAE;MAClC;MACA,IAAII,QAAQ,CAACP,MAAM,CAACG,UAAU,CAAC,CAACQ,eAAe,CAAC,EAAE;QAChDJ,QAAQ,CAACP,MAAM,CAACG,UAAU,CAAC,CAACQ,eAAe,CAAC,CAAC3R,cAAc,CAAC;UAC1DuR,QAAQ,EAAEA;QACZ,CAAC,EAAEC,GAAG,CAAC,CAAC;MACV;;MAEA;MACA;MACA,IAAID,QAAQ,CAACjI,OAAO,CAAC0H,MAAM,CAACG,UAAU,CAAC,IAAII,QAAQ,CAACP,MAAM,CAACG,UAAU,CAAC,CAACG,SAAS,CAAC,EAAE;QACjFC,QAAQ,CAACP,MAAM,CAACG,UAAU,CAAC,CAACG,SAAS,CAAC,CAACtR,cAAc,CAAC;UACpDuR,QAAQ,EAAEA;QACZ,CAAC,EAAEC,GAAG,CAAC,CAAC;MACV;IACF,CAAC,CAAC;EACJ,CAAC;EACDI,iBAAiB,EAAE,SAASA,iBAAiBA,CAACL,QAAQ,EAAElN,EAAE,EAAEuM,QAAQ,EAAEtH,OAAO,EAAE;IAC7EqH,OAAO,CAACrQ,OAAO,CAAC,UAAU0Q,MAAM,EAAE;MAChC,IAAIG,UAAU,GAAGH,MAAM,CAACG,UAAU;MAClC,IAAI,CAACI,QAAQ,CAACjI,OAAO,CAAC6H,UAAU,CAAC,IAAI,CAACH,MAAM,CAACH,mBAAmB,EAAE;MAClE,IAAIgB,WAAW,GAAG,IAAIb,MAAM,CAACO,QAAQ,EAAElN,EAAE,EAAEkN,QAAQ,CAACjI,OAAO,CAAC;MAC5DuI,WAAW,CAACN,QAAQ,GAAGA,QAAQ;MAC/BM,WAAW,CAACvI,OAAO,GAAGiI,QAAQ,CAACjI,OAAO;MACtCiI,QAAQ,CAACJ,UAAU,CAAC,GAAGU,WAAW;;MAElC;MACAxQ,QAAQ,CAACuP,QAAQ,EAAEiB,WAAW,CAACjB,QAAQ,CAAC;IAC1C,CAAC,CAAC;IACF,KAAK,IAAIK,MAAM,IAAIM,QAAQ,CAACjI,OAAO,EAAE;MACnC,IAAI,CAACiI,QAAQ,CAACjI,OAAO,CAAC/H,cAAc,CAAC0P,MAAM,CAAC,EAAE;MAC9C,IAAIa,QAAQ,GAAG,IAAI,CAACC,YAAY,CAACR,QAAQ,EAAEN,MAAM,EAAEM,QAAQ,CAACjI,OAAO,CAAC2H,MAAM,CAAC,CAAC;MAC5E,IAAI,OAAOa,QAAQ,KAAK,WAAW,EAAE;QACnCP,QAAQ,CAACjI,OAAO,CAAC2H,MAAM,CAAC,GAAGa,QAAQ;MACrC;IACF;EACF,CAAC;EACDE,kBAAkB,EAAE,SAASA,kBAAkBA,CAAChP,IAAI,EAAEuO,QAAQ,EAAE;IAC9D,IAAIU,eAAe,GAAG,CAAC,CAAC;IACxBtB,OAAO,CAACrQ,OAAO,CAAC,UAAU0Q,MAAM,EAAE;MAChC,IAAI,OAAOA,MAAM,CAACiB,eAAe,KAAK,UAAU,EAAE;MAClD5Q,QAAQ,CAAC4Q,eAAe,EAAEjB,MAAM,CAACiB,eAAe,CAACzQ,IAAI,CAAC+P,QAAQ,CAACP,MAAM,CAACG,UAAU,CAAC,EAAEnO,IAAI,CAAC,CAAC;IAC3F,CAAC,CAAC;IACF,OAAOiP,eAAe;EACxB,CAAC;EACDF,YAAY,EAAE,SAASA,YAAYA,CAACR,QAAQ,EAAEvO,IAAI,EAAE9B,KAAK,EAAE;IACzD,IAAIgR,aAAa;IACjBvB,OAAO,CAACrQ,OAAO,CAAC,UAAU0Q,MAAM,EAAE;MAChC;MACA,IAAI,CAACO,QAAQ,CAACP,MAAM,CAACG,UAAU,CAAC,EAAE;;MAElC;MACA,IAAIH,MAAM,CAACmB,eAAe,IAAI,OAAOnB,MAAM,CAACmB,eAAe,CAACnP,IAAI,CAAC,KAAK,UAAU,EAAE;QAChFkP,aAAa,GAAGlB,MAAM,CAACmB,eAAe,CAACnP,IAAI,CAAC,CAACxB,IAAI,CAAC+P,QAAQ,CAACP,MAAM,CAACG,UAAU,CAAC,EAAEjQ,KAAK,CAAC;MACvF;IACF,CAAC,CAAC;IACF,OAAOgR,aAAa;EACtB;AACF,CAAC;AAED,SAASE,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAId,QAAQ,GAAGc,IAAI,CAACd,QAAQ;IAC1Be,MAAM,GAAGD,IAAI,CAACC,MAAM;IACpBtP,IAAI,GAAGqP,IAAI,CAACrP,IAAI;IAChBuP,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IACxBC,OAAO,GAAGH,IAAI,CAACG,OAAO;IACtBC,IAAI,GAAGJ,IAAI,CAACI,IAAI;IAChBC,MAAM,GAAGL,IAAI,CAACK,MAAM;IACpBC,QAAQ,GAAGN,IAAI,CAACM,QAAQ;IACxBC,QAAQ,GAAGP,IAAI,CAACO,QAAQ;IACxBC,iBAAiB,GAAGR,IAAI,CAACQ,iBAAiB;IAC1CC,iBAAiB,GAAGT,IAAI,CAACS,iBAAiB;IAC1CC,aAAa,GAAGV,IAAI,CAACU,aAAa;IAClCC,WAAW,GAAGX,IAAI,CAACW,WAAW;IAC9BC,oBAAoB,GAAGZ,IAAI,CAACY,oBAAoB;EAClD1B,QAAQ,GAAGA,QAAQ,IAAIe,MAAM,IAAIA,MAAM,CAACnE,OAAO,CAAC;EAChD,IAAI,CAACoD,QAAQ,EAAE;EACf,IAAIC,GAAG;IACLlI,OAAO,GAAGiI,QAAQ,CAACjI,OAAO;IAC1B4J,MAAM,GAAG,IAAI,GAAGlQ,IAAI,CAACmQ,MAAM,CAAC,CAAC,CAAC,CAAC9I,WAAW,CAAC,CAAC,GAAGrH,IAAI,CAACoQ,MAAM,CAAC,CAAC,CAAC;EAC/D;EACA,IAAI5P,MAAM,CAAC6P,WAAW,IAAI,CAAC1P,UAAU,IAAI,CAACC,IAAI,EAAE;IAC9C4N,GAAG,GAAG,IAAI6B,WAAW,CAACrQ,IAAI,EAAE;MAC1BsQ,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC,MAAM;IACL/B,GAAG,GAAGrM,QAAQ,CAACqO,WAAW,CAAC,OAAO,CAAC;IACnChC,GAAG,CAACiC,SAAS,CAACzQ,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACjC;EACAwO,GAAG,CAACkC,EAAE,GAAGjB,IAAI,IAAIH,MAAM;EACvBd,GAAG,CAAC9O,IAAI,GAAGgQ,MAAM,IAAIJ,MAAM;EAC3Bd,GAAG,CAACmC,IAAI,GAAGpB,QAAQ,IAAID,MAAM;EAC7Bd,GAAG,CAAClH,KAAK,GAAGkI,OAAO;EACnBhB,GAAG,CAACmB,QAAQ,GAAGA,QAAQ;EACvBnB,GAAG,CAACoB,QAAQ,GAAGA,QAAQ;EACvBpB,GAAG,CAACqB,iBAAiB,GAAGA,iBAAiB;EACzCrB,GAAG,CAACsB,iBAAiB,GAAGA,iBAAiB;EACzCtB,GAAG,CAACuB,aAAa,GAAGA,aAAa;EACjCvB,GAAG,CAACoC,QAAQ,GAAGZ,WAAW,GAAGA,WAAW,CAACa,WAAW,GAAGC,SAAS;EAChE,IAAIC,kBAAkB,GAAG/T,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEiT,oBAAoB,CAAC,EAAEnC,aAAa,CAACkB,kBAAkB,CAAChP,IAAI,EAAEuO,QAAQ,CAAC,CAAC;EACnI,KAAK,IAAIN,MAAM,IAAI8C,kBAAkB,EAAE;IACrCvC,GAAG,CAACP,MAAM,CAAC,GAAG8C,kBAAkB,CAAC9C,MAAM,CAAC;EAC1C;EACA,IAAIqB,MAAM,EAAE;IACVA,MAAM,CAACF,aAAa,CAACZ,GAAG,CAAC;EAC3B;EACA,IAAIlI,OAAO,CAAC4J,MAAM,CAAC,EAAE;IACnB5J,OAAO,CAAC4J,MAAM,CAAC,CAAC1R,IAAI,CAAC+P,QAAQ,EAAEC,GAAG,CAAC;EACrC;AACF;AAEA,IAAIwC,SAAS,GAAG,CAAC,KAAK,CAAC;AACvB,IAAI3C,WAAW,GAAG,SAASA,WAAWA,CAACC,SAAS,EAAEC,QAAQ,EAAE;EAC1D,IAAIc,IAAI,GAAGlS,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK2T,SAAS,GAAG3T,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;IAC/E4S,aAAa,GAAGV,IAAI,CAACb,GAAG;IACxByC,IAAI,GAAGpS,wBAAwB,CAACwQ,IAAI,EAAE2B,SAAS,CAAC;EAClDlD,aAAa,CAACO,WAAW,CAAC6C,IAAI,CAACvK,QAAQ,CAAC,CAAC2H,SAAS,EAAEC,QAAQ,EAAEvR,cAAc,CAAC;IAC3EmU,MAAM,EAAEA,MAAM;IACdC,QAAQ,EAAEA,QAAQ;IAClB5G,OAAO,EAAEA,OAAO;IAChB8E,MAAM,EAAEA,MAAM;IACd+B,MAAM,EAAEA,MAAM;IACdC,UAAU,EAAEA,UAAU;IACtB9B,OAAO,EAAEA,OAAO;IAChB+B,WAAW,EAAEA,WAAW;IACxBC,WAAW,EAAEC,KAAK;IAClBzB,WAAW,EAAEA,WAAW;IACxB0B,cAAc,EAAE/K,QAAQ,CAACgL,MAAM;IAC/B5B,aAAa,EAAEA,aAAa;IAC5BJ,QAAQ,EAAEA,QAAQ;IAClBE,iBAAiB,EAAEA,iBAAiB;IACpCD,QAAQ,EAAEA,QAAQ;IAClBE,iBAAiB,EAAEA,iBAAiB;IACpC8B,kBAAkB,EAAEC,mBAAmB;IACvCC,oBAAoB,EAAEC,qBAAqB;IAC3CC,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG;MACxCT,WAAW,GAAG,IAAI;IACpB,CAAC;IACDU,aAAa,EAAE,SAASA,aAAaA,CAAA,EAAG;MACtCV,WAAW,GAAG,KAAK;IACrB,CAAC;IACDW,qBAAqB,EAAE,SAASA,qBAAqBA,CAAClS,IAAI,EAAE;MAC1DmS,cAAc,CAAC;QACb5D,QAAQ,EAAEA,QAAQ;QAClBvO,IAAI,EAAEA,IAAI;QACV+P,aAAa,EAAEA;MACjB,CAAC,CAAC;IACJ;EACF,CAAC,EAAEkB,IAAI,CAAC,CAAC;AACX,CAAC;AACD,SAASkB,cAAcA,CAACC,IAAI,EAAE;EAC5BhD,aAAa,CAACpS,cAAc,CAAC;IAC3BgT,WAAW,EAAEA,WAAW;IACxBR,OAAO,EAAEA,OAAO;IAChBD,QAAQ,EAAE4B,MAAM;IAChB7B,MAAM,EAAEA,MAAM;IACdK,QAAQ,EAAEA,QAAQ;IAClBE,iBAAiB,EAAEA,iBAAiB;IACpCD,QAAQ,EAAEA,QAAQ;IAClBE,iBAAiB,EAAEA;EACrB,CAAC,EAAEsC,IAAI,CAAC,CAAC;AACX;AACA,IAAIjB,MAAM;EACRC,QAAQ;EACR5G,OAAO;EACP8E,MAAM;EACN+B,MAAM;EACNC,UAAU;EACV9B,OAAO;EACP+B,WAAW;EACX5B,QAAQ;EACRC,QAAQ;EACRC,iBAAiB;EACjBC,iBAAiB;EACjBuC,WAAW;EACXrC,WAAW;EACXsC,mBAAmB,GAAG,KAAK;EAC3BC,eAAe,GAAG,KAAK;EACvBC,SAAS,GAAG,EAAE;EACdC,MAAM;EACNC,QAAQ;EACRC,MAAM;EACNC,MAAM;EACNC,eAAe;EACfC,cAAc;EACdrB,KAAK;EACLsB,UAAU;EACVC,aAAa;EACbC,qBAAqB,GAAG,KAAK;EAC7BC,sBAAsB,GAAG,KAAK;EAC9BC,kBAAkB;EAClB;EACAC,mBAAmB;EACnBC,gCAAgC,GAAG,EAAE;EACrC;;EAEAC,OAAO,GAAG,KAAK;EACfC,iBAAiB,GAAG,EAAE;;AAExB;AACA,IAAIC,cAAc,GAAG,OAAOrR,QAAQ,KAAK,WAAW;EAClDsR,uBAAuB,GAAG1S,GAAG;EAC7B2S,gBAAgB,GAAG9S,IAAI,IAAID,UAAU,GAAG,UAAU,GAAG,OAAO;EAC5D;EACAgT,gBAAgB,GAAGH,cAAc,IAAI,CAACxS,gBAAgB,IAAI,CAACD,GAAG,IAAI,WAAW,IAAIoB,QAAQ,CAACyR,aAAa,CAAC,KAAK,CAAC;EAC9GC,uBAAuB,GAAG,YAAY;IACpC,IAAI,CAACL,cAAc,EAAE;IACrB;IACA,IAAI7S,UAAU,EAAE;MACd,OAAO,KAAK;IACd;IACA,IAAIU,EAAE,GAAGc,QAAQ,CAACyR,aAAa,CAAC,GAAG,CAAC;IACpCvS,EAAE,CAAC6B,KAAK,CAAC4Q,OAAO,GAAG,qBAAqB;IACxC,OAAOzS,EAAE,CAAC6B,KAAK,CAAC6Q,aAAa,KAAK,MAAM;EAC1C,CAAC,CAAC,CAAC;EACHC,gBAAgB,GAAG,SAASA,gBAAgBA,CAAC3S,EAAE,EAAEiF,OAAO,EAAE;IACxD,IAAI2N,KAAK,GAAGlR,GAAG,CAAC1B,EAAE,CAAC;MACjB6S,OAAO,GAAG5O,QAAQ,CAAC2O,KAAK,CAAC/O,KAAK,CAAC,GAAGI,QAAQ,CAAC2O,KAAK,CAACE,WAAW,CAAC,GAAG7O,QAAQ,CAAC2O,KAAK,CAACG,YAAY,CAAC,GAAG9O,QAAQ,CAAC2O,KAAK,CAACI,eAAe,CAAC,GAAG/O,QAAQ,CAAC2O,KAAK,CAACK,gBAAgB,CAAC;MACjKC,MAAM,GAAGnO,QAAQ,CAAC/E,EAAE,EAAE,CAAC,EAAEiF,OAAO,CAAC;MACjCkO,MAAM,GAAGpO,QAAQ,CAAC/E,EAAE,EAAE,CAAC,EAAEiF,OAAO,CAAC;MACjCmO,aAAa,GAAGF,MAAM,IAAIxR,GAAG,CAACwR,MAAM,CAAC;MACrCG,cAAc,GAAGF,MAAM,IAAIzR,GAAG,CAACyR,MAAM,CAAC;MACtCG,eAAe,GAAGF,aAAa,IAAInP,QAAQ,CAACmP,aAAa,CAACG,UAAU,CAAC,GAAGtP,QAAQ,CAACmP,aAAa,CAACI,WAAW,CAAC,GAAGvQ,OAAO,CAACiQ,MAAM,CAAC,CAACrP,KAAK;MACnI4P,gBAAgB,GAAGJ,cAAc,IAAIpP,QAAQ,CAACoP,cAAc,CAACE,UAAU,CAAC,GAAGtP,QAAQ,CAACoP,cAAc,CAACG,WAAW,CAAC,GAAGvQ,OAAO,CAACkQ,MAAM,CAAC,CAACtP,KAAK;IACzI,IAAI+O,KAAK,CAACvN,OAAO,KAAK,MAAM,EAAE;MAC5B,OAAOuN,KAAK,CAACc,aAAa,KAAK,QAAQ,IAAId,KAAK,CAACc,aAAa,KAAK,gBAAgB,GAAG,UAAU,GAAG,YAAY;IACjH;IACA,IAAId,KAAK,CAACvN,OAAO,KAAK,MAAM,EAAE;MAC5B,OAAOuN,KAAK,CAACe,mBAAmB,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC7X,MAAM,IAAI,CAAC,GAAG,UAAU,GAAG,YAAY;IACrF;IACA,IAAImX,MAAM,IAAIE,aAAa,CAAC,OAAO,CAAC,IAAIA,aAAa,CAAC,OAAO,CAAC,KAAK,MAAM,EAAE;MACzE,IAAIS,kBAAkB,GAAGT,aAAa,CAAC,OAAO,CAAC,KAAK,MAAM,GAAG,MAAM,GAAG,OAAO;MAC7E,OAAOD,MAAM,KAAKE,cAAc,CAACS,KAAK,KAAK,MAAM,IAAIT,cAAc,CAACS,KAAK,KAAKD,kBAAkB,CAAC,GAAG,UAAU,GAAG,YAAY;IAC/H;IACA,OAAOX,MAAM,KAAKE,aAAa,CAAC/N,OAAO,KAAK,OAAO,IAAI+N,aAAa,CAAC/N,OAAO,KAAK,MAAM,IAAI+N,aAAa,CAAC/N,OAAO,KAAK,OAAO,IAAI+N,aAAa,CAAC/N,OAAO,KAAK,MAAM,IAAIiO,eAAe,IAAIT,OAAO,IAAID,KAAK,CAACP,gBAAgB,CAAC,KAAK,MAAM,IAAIc,MAAM,IAAIP,KAAK,CAACP,gBAAgB,CAAC,KAAK,MAAM,IAAIiB,eAAe,GAAGG,gBAAgB,GAAGZ,OAAO,CAAC,GAAG,UAAU,GAAG,YAAY;EACnW,CAAC;EACDkB,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,QAAQ,EAAEC,UAAU,EAAEC,QAAQ,EAAE;IAC/E,IAAIC,WAAW,GAAGD,QAAQ,GAAGF,QAAQ,CAACvQ,IAAI,GAAGuQ,QAAQ,CAACxQ,GAAG;MACvD4Q,WAAW,GAAGF,QAAQ,GAAGF,QAAQ,CAACrQ,KAAK,GAAGqQ,QAAQ,CAACtQ,MAAM;MACzD2Q,eAAe,GAAGH,QAAQ,GAAGF,QAAQ,CAACnQ,KAAK,GAAGmQ,QAAQ,CAACpQ,MAAM;MAC7D0Q,WAAW,GAAGJ,QAAQ,GAAGD,UAAU,CAACxQ,IAAI,GAAGwQ,UAAU,CAACzQ,GAAG;MACzD+Q,WAAW,GAAGL,QAAQ,GAAGD,UAAU,CAACtQ,KAAK,GAAGsQ,UAAU,CAACvQ,MAAM;MAC7D8Q,eAAe,GAAGN,QAAQ,GAAGD,UAAU,CAACpQ,KAAK,GAAGoQ,UAAU,CAACrQ,MAAM;IACnE,OAAOuQ,WAAW,KAAKG,WAAW,IAAIF,WAAW,KAAKG,WAAW,IAAIJ,WAAW,GAAGE,eAAe,GAAG,CAAC,KAAKC,WAAW,GAAGE,eAAe,GAAG,CAAC;EAC9I,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACEC,2BAA2B,GAAG,SAASA,2BAA2BA,CAAClM,CAAC,EAAEC,CAAC,EAAE;IACvE,IAAIkM,GAAG;IACPvD,SAAS,CAACwD,IAAI,CAAC,UAAUzH,QAAQ,EAAE;MACjC,IAAI0H,SAAS,GAAG1H,QAAQ,CAACpD,OAAO,CAAC,CAAC7E,OAAO,CAAC4P,oBAAoB;MAC9D,IAAI,CAACD,SAAS,IAAIlP,SAAS,CAACwH,QAAQ,CAAC,EAAE;MACvC,IAAIlE,IAAI,GAAG/F,OAAO,CAACiK,QAAQ,CAAC;QAC1B4H,kBAAkB,GAAGvM,CAAC,IAAIS,IAAI,CAACvF,IAAI,GAAGmR,SAAS,IAAIrM,CAAC,IAAIS,IAAI,CAACrF,KAAK,GAAGiR,SAAS;QAC9EG,gBAAgB,GAAGvM,CAAC,IAAIQ,IAAI,CAACxF,GAAG,GAAGoR,SAAS,IAAIpM,CAAC,IAAIQ,IAAI,CAACtF,MAAM,GAAGkR,SAAS;MAC9E,IAAIE,kBAAkB,IAAIC,gBAAgB,EAAE;QAC1C,OAAOL,GAAG,GAAGxH,QAAQ;MACvB;IACF,CAAC,CAAC;IACF,OAAOwH,GAAG;EACZ,CAAC;EACDM,aAAa,GAAG,SAASA,aAAaA,CAAC/P,OAAO,EAAE;IAC9C,SAASgQ,IAAIA,CAACpY,KAAK,EAAEqY,IAAI,EAAE;MACzB,OAAO,UAAU7F,EAAE,EAAEhR,IAAI,EAAEyR,MAAM,EAAE3C,GAAG,EAAE;QACtC,IAAIgI,SAAS,GAAG9F,EAAE,CAACpK,OAAO,CAACmQ,KAAK,CAACzW,IAAI,IAAIN,IAAI,CAAC4G,OAAO,CAACmQ,KAAK,CAACzW,IAAI,IAAI0Q,EAAE,CAACpK,OAAO,CAACmQ,KAAK,CAACzW,IAAI,KAAKN,IAAI,CAAC4G,OAAO,CAACmQ,KAAK,CAACzW,IAAI;QACrH,IAAI9B,KAAK,IAAI,IAAI,KAAKqY,IAAI,IAAIC,SAAS,CAAC,EAAE;UACxC;UACA;UACA,OAAO,IAAI;QACb,CAAC,MAAM,IAAItY,KAAK,IAAI,IAAI,IAAIA,KAAK,KAAK,KAAK,EAAE;UAC3C,OAAO,KAAK;QACd,CAAC,MAAM,IAAIqY,IAAI,IAAIrY,KAAK,KAAK,OAAO,EAAE;UACpC,OAAOA,KAAK;QACd,CAAC,MAAM,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;UACtC,OAAOoY,IAAI,CAACpY,KAAK,CAACwS,EAAE,EAAEhR,IAAI,EAAEyR,MAAM,EAAE3C,GAAG,CAAC,EAAE+H,IAAI,CAAC,CAAC7F,EAAE,EAAEhR,IAAI,EAAEyR,MAAM,EAAE3C,GAAG,CAAC;QACxE,CAAC,MAAM;UACL,IAAIkI,UAAU,GAAG,CAACH,IAAI,GAAG7F,EAAE,GAAGhR,IAAI,EAAE4G,OAAO,CAACmQ,KAAK,CAACzW,IAAI;UACtD,OAAO9B,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAKwY,UAAU,IAAIxY,KAAK,CAACyY,IAAI,IAAIzY,KAAK,CAACU,OAAO,CAAC8X,UAAU,CAAC,GAAG,CAAC,CAAC;QAC5H;MACF,CAAC;IACH;IACA,IAAID,KAAK,GAAG,CAAC,CAAC;IACd,IAAIG,aAAa,GAAGtQ,OAAO,CAACmQ,KAAK;IACjC,IAAI,CAACG,aAAa,IAAIhZ,OAAO,CAACgZ,aAAa,CAAC,IAAI,QAAQ,EAAE;MACxDA,aAAa,GAAG;QACd5W,IAAI,EAAE4W;MACR,CAAC;IACH;IACAH,KAAK,CAACzW,IAAI,GAAG4W,aAAa,CAAC5W,IAAI;IAC/ByW,KAAK,CAACI,SAAS,GAAGP,IAAI,CAACM,aAAa,CAACL,IAAI,EAAE,IAAI,CAAC;IAChDE,KAAK,CAACK,QAAQ,GAAGR,IAAI,CAACM,aAAa,CAACG,GAAG,CAAC;IACxCN,KAAK,CAACO,WAAW,GAAGJ,aAAa,CAACI,WAAW;IAC7C1Q,OAAO,CAACmQ,KAAK,GAAGA,KAAK;EACvB,CAAC;EACD5E,mBAAmB,GAAG,SAASA,mBAAmBA,CAAA,EAAG;IACnD,IAAI,CAACgC,uBAAuB,IAAIrJ,OAAO,EAAE;MACvCzH,GAAG,CAACyH,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC;IACjC;EACF,CAAC;EACDuH,qBAAqB,GAAG,SAASA,qBAAqBA,CAAA,EAAG;IACvD,IAAI,CAAC8B,uBAAuB,IAAIrJ,OAAO,EAAE;MACvCzH,GAAG,CAACyH,OAAO,EAAE,SAAS,EAAE,EAAE,CAAC;IAC7B;EACF,CAAC;;AAEH;AACA,IAAIgJ,cAAc,IAAI,CAACxS,gBAAgB,EAAE;EACvCmB,QAAQ,CAACX,gBAAgB,CAAC,OAAO,EAAE,UAAUgN,GAAG,EAAE;IAChD,IAAI+D,eAAe,EAAE;MACnB/D,GAAG,CAACyI,cAAc,CAAC,CAAC;MACpBzI,GAAG,CAAC0I,eAAe,IAAI1I,GAAG,CAAC0I,eAAe,CAAC,CAAC;MAC5C1I,GAAG,CAAC2I,wBAAwB,IAAI3I,GAAG,CAAC2I,wBAAwB,CAAC,CAAC;MAC9D5E,eAAe,GAAG,KAAK;MACvB,OAAO,KAAK;IACd;EACF,CAAC,EAAE,IAAI,CAAC;AACV;AACA,IAAI6E,6BAA6B,GAAG,SAASA,6BAA6BA,CAAC5I,GAAG,EAAE;EAC9E,IAAI2C,MAAM,EAAE;IACV3C,GAAG,GAAGA,GAAG,CAAC6I,OAAO,GAAG7I,GAAG,CAAC6I,OAAO,CAAC,CAAC,CAAC,GAAG7I,GAAG;IACxC,IAAI8I,OAAO,GAAGxB,2BAA2B,CAACtH,GAAG,CAAC+I,OAAO,EAAE/I,GAAG,CAACgJ,OAAO,CAAC;IACnE,IAAIF,OAAO,EAAE;MACX;MACA,IAAIhW,KAAK,GAAG,CAAC,CAAC;MACd,KAAK,IAAIpE,CAAC,IAAIsR,GAAG,EAAE;QACjB,IAAIA,GAAG,CAACjQ,cAAc,CAACrB,CAAC,CAAC,EAAE;UACzBoE,KAAK,CAACpE,CAAC,CAAC,GAAGsR,GAAG,CAACtR,CAAC,CAAC;QACnB;MACF;MACAoE,KAAK,CAACrE,MAAM,GAAGqE,KAAK,CAACgO,MAAM,GAAGgI,OAAO;MACrChW,KAAK,CAAC2V,cAAc,GAAG,KAAK,CAAC;MAC7B3V,KAAK,CAAC4V,eAAe,GAAG,KAAK,CAAC;MAC9BI,OAAO,CAACnM,OAAO,CAAC,CAACsM,WAAW,CAACnW,KAAK,CAAC;IACrC;EACF;AACF,CAAC;AACD,IAAIoW,qBAAqB,GAAG,SAASA,qBAAqBA,CAAClJ,GAAG,EAAE;EAC9D,IAAI2C,MAAM,EAAE;IACVA,MAAM,CAAC9O,UAAU,CAAC8I,OAAO,CAAC,CAACwM,gBAAgB,CAACnJ,GAAG,CAACvR,MAAM,CAAC;EACzD;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,SAAS0J,QAAQA,CAACtF,EAAE,EAAEiF,OAAO,EAAE;EAC7B,IAAI,EAAEjF,EAAE,IAAIA,EAAE,CAACe,QAAQ,IAAIf,EAAE,CAACe,QAAQ,KAAK,CAAC,CAAC,EAAE;IAC7C,MAAM,6CAA6C,CAACgM,MAAM,CAAC,CAAC,CAAC,CAACtO,QAAQ,CAACtB,IAAI,CAAC6C,EAAE,CAAC,CAAC;EAClF;EACA,IAAI,CAACA,EAAE,GAAGA,EAAE,CAAC,CAAC;EACd,IAAI,CAACiF,OAAO,GAAGA,OAAO,GAAGjI,QAAQ,CAAC,CAAC,CAAC,EAAEiI,OAAO,CAAC;;EAE9C;EACAjF,EAAE,CAAC8J,OAAO,CAAC,GAAG,IAAI;EAClB,IAAIyC,QAAQ,GAAG;IACb6I,KAAK,EAAE,IAAI;IACXmB,IAAI,EAAE,IAAI;IACVC,QAAQ,EAAE,KAAK;IACfC,KAAK,EAAE,IAAI;IACXC,MAAM,EAAE,IAAI;IACZjR,SAAS,EAAE,UAAU,CAAC7G,IAAI,CAACoB,EAAE,CAAC+F,QAAQ,CAAC,GAAG,KAAK,GAAG,IAAI;IACtD4Q,aAAa,EAAE,CAAC;IAChB;IACAC,UAAU,EAAE,KAAK;IACjB;IACAC,qBAAqB,EAAE,IAAI;IAC3B;IACAC,iBAAiB,EAAE,IAAI;IACvBC,SAAS,EAAE,SAASA,SAASA,CAAA,EAAG;MAC9B,OAAOpE,gBAAgB,CAAC3S,EAAE,EAAE,IAAI,CAACiF,OAAO,CAAC;IAC3C,CAAC;IACD+R,UAAU,EAAE,gBAAgB;IAC5BC,WAAW,EAAE,iBAAiB;IAC9BC,SAAS,EAAE,eAAe;IAC1BC,MAAM,EAAE,QAAQ;IAChB9b,MAAM,EAAE,IAAI;IACZ+b,eAAe,EAAE,IAAI;IACrB/M,SAAS,EAAE,CAAC;IACZ6B,MAAM,EAAE,IAAI;IACZmL,OAAO,EAAE,SAASA,OAAOA,CAACC,YAAY,EAAExH,MAAM,EAAE;MAC9CwH,YAAY,CAACD,OAAO,CAAC,MAAM,EAAEvH,MAAM,CAACyH,WAAW,CAAC;IAClD,CAAC;IACDC,UAAU,EAAE,KAAK;IACjBC,cAAc,EAAE,KAAK;IACrBC,UAAU,EAAE,SAAS;IACrBC,KAAK,EAAE,CAAC;IACRC,gBAAgB,EAAE,KAAK;IACvBC,mBAAmB,EAAE,CAACpR,MAAM,CAACxC,QAAQ,GAAGwC,MAAM,GAAGtH,MAAM,EAAE8E,QAAQ,CAAC9E,MAAM,CAAC2Y,gBAAgB,EAAE,EAAE,CAAC,IAAI,CAAC;IACnGC,aAAa,EAAE,KAAK;IACpBC,aAAa,EAAE,mBAAmB;IAClCC,cAAc,EAAE,KAAK;IACrBC,iBAAiB,EAAE,CAAC;IACpBC,cAAc,EAAE;MACd5P,CAAC,EAAE,CAAC;MACJC,CAAC,EAAE;IACL,CAAC;IACD4P,cAAc,EAAE9S,QAAQ,CAAC8S,cAAc,KAAK,KAAK,IAAI,cAAc,IAAIjZ,MAAM,IAAI,CAACM,MAAM;IACxFoV,oBAAoB,EAAE;EACxB,CAAC;EACDpI,aAAa,CAACc,iBAAiB,CAAC,IAAI,EAAEvN,EAAE,EAAEuM,QAAQ,CAAC;;EAEnD;EACA,KAAK,IAAI5N,IAAI,IAAI4N,QAAQ,EAAE;IACzB,EAAE5N,IAAI,IAAIsG,OAAO,CAAC,KAAKA,OAAO,CAACtG,IAAI,CAAC,GAAG4N,QAAQ,CAAC5N,IAAI,CAAC,CAAC;EACxD;EACAqW,aAAa,CAAC/P,OAAO,CAAC;;EAEtB;EACA,KAAK,IAAI/E,EAAE,IAAI,IAAI,EAAE;IACnB,IAAIA,EAAE,CAAC4O,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,OAAO,IAAI,CAAC5O,EAAE,CAAC,KAAK,UAAU,EAAE;MAC1D,IAAI,CAACA,EAAE,CAAC,GAAG,IAAI,CAACA,EAAE,CAAC,CAAC2P,IAAI,CAAC,IAAI,CAAC;IAChC;EACF;;EAEA;EACA,IAAI,CAACwI,eAAe,GAAGpT,OAAO,CAAC8S,aAAa,GAAG,KAAK,GAAGzF,gBAAgB;EACvE,IAAI,IAAI,CAAC+F,eAAe,EAAE;IACxB;IACA,IAAI,CAACpT,OAAO,CAAC4S,mBAAmB,GAAG,CAAC;EACtC;;EAEA;EACA,IAAI5S,OAAO,CAACmT,cAAc,EAAE;IAC1BrY,EAAE,CAACC,EAAE,EAAE,aAAa,EAAE,IAAI,CAACsY,WAAW,CAAC;EACzC,CAAC,MAAM;IACLvY,EAAE,CAACC,EAAE,EAAE,WAAW,EAAE,IAAI,CAACsY,WAAW,CAAC;IACrCvY,EAAE,CAACC,EAAE,EAAE,YAAY,EAAE,IAAI,CAACsY,WAAW,CAAC;EACxC;EACA,IAAI,IAAI,CAACD,eAAe,EAAE;IACxBtY,EAAE,CAACC,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC;IACxBD,EAAE,CAACC,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC;EAC3B;EACAmR,SAAS,CAAC1V,IAAI,CAAC,IAAI,CAACuE,EAAE,CAAC;;EAEvB;EACAiF,OAAO,CAACwR,KAAK,IAAIxR,OAAO,CAACwR,KAAK,CAAC8B,GAAG,IAAI,IAAI,CAAChC,IAAI,CAACtR,OAAO,CAACwR,KAAK,CAAC8B,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;;EAE9E;EACAvb,QAAQ,CAAC,IAAI,EAAEiN,qBAAqB,CAAC,CAAC,CAAC;AACzC;AACA3E,QAAQ,CAAC1I,SAAS,GAAG,gCAAgC;EACnDD,WAAW,EAAE2I,QAAQ;EACrBgR,gBAAgB,EAAE,SAASA,gBAAgBA,CAAC1a,MAAM,EAAE;IAClD,IAAI,CAAC,IAAI,CAACoE,EAAE,CAACwY,QAAQ,CAAC5c,MAAM,CAAC,IAAIA,MAAM,KAAK,IAAI,CAACoE,EAAE,EAAE;MACnD0R,UAAU,GAAG,IAAI;IACnB;EACF,CAAC;EACD+G,aAAa,EAAE,SAASA,aAAaA,CAACtL,GAAG,EAAEvR,MAAM,EAAE;IACjD,OAAO,OAAO,IAAI,CAACqJ,OAAO,CAAC8R,SAAS,KAAK,UAAU,GAAG,IAAI,CAAC9R,OAAO,CAAC8R,SAAS,CAAC5Z,IAAI,CAAC,IAAI,EAAEgQ,GAAG,EAAEvR,MAAM,EAAEkU,MAAM,CAAC,GAAG,IAAI,CAAC7K,OAAO,CAAC8R,SAAS;EACvI,CAAC;EACDuB,WAAW,EAAE,SAASA,WAAWA,CAAE,uBAAuBnL,GAAG,EAAE;IAC7D,IAAI,CAACA,GAAG,CAAC+B,UAAU,EAAE;IACrB,IAAIhH,KAAK,GAAG,IAAI;MACdlI,EAAE,GAAG,IAAI,CAACA,EAAE;MACZiF,OAAO,GAAG,IAAI,CAACA,OAAO;MACtBmS,eAAe,GAAGnS,OAAO,CAACmS,eAAe;MACzCsB,IAAI,GAAGvL,GAAG,CAACuL,IAAI;MACfC,KAAK,GAAGxL,GAAG,CAAC6I,OAAO,IAAI7I,GAAG,CAAC6I,OAAO,CAAC,CAAC,CAAC,IAAI7I,GAAG,CAACyL,WAAW,IAAIzL,GAAG,CAACyL,WAAW,KAAK,OAAO,IAAIzL,GAAG;MAC9FvR,MAAM,GAAG,CAAC+c,KAAK,IAAIxL,GAAG,EAAEvR,MAAM;MAC9Bid,cAAc,GAAG1L,GAAG,CAACvR,MAAM,CAACkd,UAAU,KAAK3L,GAAG,CAAC4L,IAAI,IAAI5L,GAAG,CAAC4L,IAAI,CAAC,CAAC,CAAC,IAAI5L,GAAG,CAAC6L,YAAY,IAAI7L,GAAG,CAAC6L,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAIpd,MAAM;MAC1HP,MAAM,GAAG4J,OAAO,CAAC5J,MAAM;IACzB4d,sBAAsB,CAACjZ,EAAE,CAAC;;IAE1B;IACA,IAAI8P,MAAM,EAAE;MACV;IACF;IACA,IAAI,uBAAuB,CAAClR,IAAI,CAAC8Z,IAAI,CAAC,IAAIvL,GAAG,CAAC+L,MAAM,KAAK,CAAC,IAAIjU,OAAO,CAACuR,QAAQ,EAAE;MAC9E,OAAO,CAAC;IACV;;IAEA;IACA,IAAIqC,cAAc,CAACM,iBAAiB,EAAE;MACpC;IACF;;IAEA;IACA,IAAI,CAAC,IAAI,CAACd,eAAe,IAAI5Y,MAAM,IAAI7D,MAAM,IAAIA,MAAM,CAAC+G,OAAO,CAACqD,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE;MAC1F;IACF;IACApK,MAAM,GAAGqF,OAAO,CAACrF,MAAM,EAAEqJ,OAAO,CAACQ,SAAS,EAAEzF,EAAE,EAAE,KAAK,CAAC;IACtD,IAAIpE,MAAM,IAAIA,MAAM,CAAC6N,QAAQ,EAAE;MAC7B;IACF;IACA,IAAIwG,UAAU,KAAKrU,MAAM,EAAE;MACzB;MACA;IACF;;IAEA;IACA0S,QAAQ,GAAGxI,KAAK,CAAClK,MAAM,CAAC;IACxB4S,iBAAiB,GAAG1I,KAAK,CAAClK,MAAM,EAAEqJ,OAAO,CAACQ,SAAS,CAAC;;IAEpD;IACA,IAAI,OAAOpK,MAAM,KAAK,UAAU,EAAE;MAChC,IAAIA,MAAM,CAAC8B,IAAI,CAAC,IAAI,EAAEgQ,GAAG,EAAEvR,MAAM,EAAE,IAAI,CAAC,EAAE;QACxCkV,cAAc,CAAC;UACb5D,QAAQ,EAAEhF,KAAK;UACf+F,MAAM,EAAE4K,cAAc;UACtBla,IAAI,EAAE,QAAQ;UACduP,QAAQ,EAAEtS,MAAM;UAChBwS,IAAI,EAAEpO,EAAE;UACRqO,MAAM,EAAErO;QACV,CAAC,CAAC;QACFgN,WAAW,CAAC,QAAQ,EAAE9E,KAAK,EAAE;UAC3BiF,GAAG,EAAEA;QACP,CAAC,CAAC;QACFiK,eAAe,IAAIjK,GAAG,CAAC+B,UAAU,IAAI/B,GAAG,CAACyI,cAAc,CAAC,CAAC;QACzD,OAAO,CAAC;MACV;IACF,CAAC,MAAM,IAAIva,MAAM,EAAE;MACjBA,MAAM,GAAGA,MAAM,CAACuY,KAAK,CAAC,GAAG,CAAC,CAACe,IAAI,CAAC,UAAUyE,QAAQ,EAAE;QAClDA,QAAQ,GAAGnY,OAAO,CAAC4X,cAAc,EAAEO,QAAQ,CAACC,IAAI,CAAC,CAAC,EAAErZ,EAAE,EAAE,KAAK,CAAC;QAC9D,IAAIoZ,QAAQ,EAAE;UACZtI,cAAc,CAAC;YACb5D,QAAQ,EAAEhF,KAAK;YACf+F,MAAM,EAAEmL,QAAQ;YAChBza,IAAI,EAAE,QAAQ;YACduP,QAAQ,EAAEtS,MAAM;YAChByS,MAAM,EAAErO,EAAE;YACVoO,IAAI,EAAEpO;UACR,CAAC,CAAC;UACFgN,WAAW,CAAC,QAAQ,EAAE9E,KAAK,EAAE;YAC3BiF,GAAG,EAAEA;UACP,CAAC,CAAC;UACF,OAAO,IAAI;QACb;MACF,CAAC,CAAC;MACF,IAAI9R,MAAM,EAAE;QACV+b,eAAe,IAAIjK,GAAG,CAAC+B,UAAU,IAAI/B,GAAG,CAACyI,cAAc,CAAC,CAAC;QACzD,OAAO,CAAC;MACV;IACF;IACA,IAAI3Q,OAAO,CAACyR,MAAM,IAAI,CAACzV,OAAO,CAAC4X,cAAc,EAAE5T,OAAO,CAACyR,MAAM,EAAE1W,EAAE,EAAE,KAAK,CAAC,EAAE;MACzE;IACF;;IAEA;IACA,IAAI,CAACsZ,iBAAiB,CAACnM,GAAG,EAAEwL,KAAK,EAAE/c,MAAM,CAAC;EAC5C,CAAC;EACD0d,iBAAiB,EAAE,SAASA,iBAAiBA,CAAE,YAAYnM,GAAG,EAAE,YAAYwL,KAAK,EAAE,kBAAkB/c,MAAM,EAAE;IAC3G,IAAIsM,KAAK,GAAG,IAAI;MACdlI,EAAE,GAAGkI,KAAK,CAAClI,EAAE;MACbiF,OAAO,GAAGiD,KAAK,CAACjD,OAAO;MACvBsU,aAAa,GAAGvZ,EAAE,CAACuZ,aAAa;MAChCC,WAAW;IACb,IAAI5d,MAAM,IAAI,CAACkU,MAAM,IAAIlU,MAAM,CAACoF,UAAU,KAAKhB,EAAE,EAAE;MACjD,IAAIgU,QAAQ,GAAG/Q,OAAO,CAACrH,MAAM,CAAC;MAC9BqS,MAAM,GAAGjO,EAAE;MACX8P,MAAM,GAAGlU,MAAM;MACfmU,QAAQ,GAAGD,MAAM,CAAC9O,UAAU;MAC5BgP,MAAM,GAAGF,MAAM,CAAC2J,WAAW;MAC3BxJ,UAAU,GAAGrU,MAAM;MACnBoV,WAAW,GAAG/L,OAAO,CAACmQ,KAAK;MAC3B9P,QAAQ,CAACE,OAAO,GAAGsK,MAAM;MACzBsB,MAAM,GAAG;QACPxV,MAAM,EAAEkU,MAAM;QACdoG,OAAO,EAAE,CAACyC,KAAK,IAAIxL,GAAG,EAAE+I,OAAO;QAC/BC,OAAO,EAAE,CAACwC,KAAK,IAAIxL,GAAG,EAAEgJ;MAC1B,CAAC;MACD3E,eAAe,GAAGJ,MAAM,CAAC8E,OAAO,GAAGlC,QAAQ,CAACvQ,IAAI;MAChDgO,cAAc,GAAGL,MAAM,CAAC+E,OAAO,GAAGnC,QAAQ,CAACxQ,GAAG;MAC9C,IAAI,CAACkW,MAAM,GAAG,CAACf,KAAK,IAAIxL,GAAG,EAAE+I,OAAO;MACpC,IAAI,CAACyD,MAAM,GAAG,CAAChB,KAAK,IAAIxL,GAAG,EAAEgJ,OAAO;MACpCrG,MAAM,CAACjO,KAAK,CAAC,aAAa,CAAC,GAAG,KAAK;MACnC2X,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;QACnCxM,WAAW,CAAC,YAAY,EAAE9E,KAAK,EAAE;UAC/BiF,GAAG,EAAEA;QACP,CAAC,CAAC;QACF,IAAI7H,QAAQ,CAAC8H,aAAa,EAAE;UAC1BlF,KAAK,CAAC0R,OAAO,CAAC,CAAC;UACf;QACF;QACA;QACA;QACA1R,KAAK,CAAC2R,yBAAyB,CAAC,CAAC;QACjC,IAAI,CAACra,OAAO,IAAI0I,KAAK,CAACmQ,eAAe,EAAE;UACrCvI,MAAM,CAACrK,SAAS,GAAG,IAAI;QACzB;;QAEA;QACAyC,KAAK,CAAC4R,iBAAiB,CAAC3M,GAAG,EAAEwL,KAAK,CAAC;;QAEnC;QACA7H,cAAc,CAAC;UACb5D,QAAQ,EAAEhF,KAAK;UACfvJ,IAAI,EAAE,QAAQ;UACd+P,aAAa,EAAEvB;QACjB,CAAC,CAAC;;QAEF;QACA9L,WAAW,CAACyO,MAAM,EAAE7K,OAAO,CAACgS,WAAW,EAAE,IAAI,CAAC;MAChD,CAAC;;MAED;MACAhS,OAAO,CAACkS,MAAM,CAACvD,KAAK,CAAC,GAAG,CAAC,CAAC3X,OAAO,CAAC,UAAUmd,QAAQ,EAAE;QACpD1W,IAAI,CAACoN,MAAM,EAAEsJ,QAAQ,CAACC,IAAI,CAAC,CAAC,EAAEU,iBAAiB,CAAC;MAClD,CAAC,CAAC;MACFha,EAAE,CAACwZ,aAAa,EAAE,UAAU,EAAExD,6BAA6B,CAAC;MAC5DhW,EAAE,CAACwZ,aAAa,EAAE,WAAW,EAAExD,6BAA6B,CAAC;MAC7DhW,EAAE,CAACwZ,aAAa,EAAE,WAAW,EAAExD,6BAA6B,CAAC;MAC7DhW,EAAE,CAACwZ,aAAa,EAAE,SAAS,EAAErR,KAAK,CAAC0R,OAAO,CAAC;MAC3C7Z,EAAE,CAACwZ,aAAa,EAAE,UAAU,EAAErR,KAAK,CAAC0R,OAAO,CAAC;MAC5C7Z,EAAE,CAACwZ,aAAa,EAAE,aAAa,EAAErR,KAAK,CAAC0R,OAAO,CAAC;;MAE/C;MACA,IAAIpa,OAAO,IAAI,IAAI,CAAC6Y,eAAe,EAAE;QACnC,IAAI,CAACpT,OAAO,CAAC4S,mBAAmB,GAAG,CAAC;QACpC/H,MAAM,CAACrK,SAAS,GAAG,IAAI;MACzB;MACAuH,WAAW,CAAC,YAAY,EAAE,IAAI,EAAE;QAC9BG,GAAG,EAAEA;MACP,CAAC,CAAC;;MAEF;MACA,IAAIlI,OAAO,CAAC0S,KAAK,KAAK,CAAC1S,OAAO,CAAC2S,gBAAgB,IAAIe,KAAK,CAAC,KAAK,CAAC,IAAI,CAACN,eAAe,IAAI,EAAE9Y,IAAI,IAAID,UAAU,CAAC,CAAC,EAAE;QAC7G,IAAIgG,QAAQ,CAAC8H,aAAa,EAAE;UAC1B,IAAI,CAACwM,OAAO,CAAC,CAAC;UACd;QACF;QACA;QACA;QACA;QACA7Z,EAAE,CAACwZ,aAAa,EAAE,SAAS,EAAErR,KAAK,CAAC8R,mBAAmB,CAAC;QACvDja,EAAE,CAACwZ,aAAa,EAAE,UAAU,EAAErR,KAAK,CAAC8R,mBAAmB,CAAC;QACxDja,EAAE,CAACwZ,aAAa,EAAE,aAAa,EAAErR,KAAK,CAAC8R,mBAAmB,CAAC;QAC3Dja,EAAE,CAACwZ,aAAa,EAAE,WAAW,EAAErR,KAAK,CAAC+R,4BAA4B,CAAC;QAClEla,EAAE,CAACwZ,aAAa,EAAE,WAAW,EAAErR,KAAK,CAAC+R,4BAA4B,CAAC;QAClEhV,OAAO,CAACmT,cAAc,IAAIrY,EAAE,CAACwZ,aAAa,EAAE,aAAa,EAAErR,KAAK,CAAC+R,4BAA4B,CAAC;QAC9F/R,KAAK,CAACgS,eAAe,GAAG/R,UAAU,CAACqR,WAAW,EAAEvU,OAAO,CAAC0S,KAAK,CAAC;MAChE,CAAC,MAAM;QACL6B,WAAW,CAAC,CAAC;MACf;IACF;EACF,CAAC;EACDS,4BAA4B,EAAE,SAASA,4BAA4BA,CAAE,+BAA+BvP,CAAC,EAAE;IACrG,IAAIiO,KAAK,GAAGjO,CAAC,CAACsL,OAAO,GAAGtL,CAAC,CAACsL,OAAO,CAAC,CAAC,CAAC,GAAGtL,CAAC;IACxC,IAAI/C,IAAI,CAACkC,GAAG,CAAClC,IAAI,CAACwS,GAAG,CAACxB,KAAK,CAACzC,OAAO,GAAG,IAAI,CAACwD,MAAM,CAAC,EAAE/R,IAAI,CAACwS,GAAG,CAACxB,KAAK,CAACxC,OAAO,GAAG,IAAI,CAACwD,MAAM,CAAC,CAAC,IAAIhS,IAAI,CAACyS,KAAK,CAAC,IAAI,CAACnV,OAAO,CAAC4S,mBAAmB,IAAI,IAAI,CAACQ,eAAe,IAAIlZ,MAAM,CAAC2Y,gBAAgB,IAAI,CAAC,CAAC,CAAC,EAAE;MACnM,IAAI,CAACkC,mBAAmB,CAAC,CAAC;IAC5B;EACF,CAAC;EACDA,mBAAmB,EAAE,SAASA,mBAAmBA,CAAA,EAAG;IAClDlK,MAAM,IAAIiK,iBAAiB,CAACjK,MAAM,CAAC;IACnCzH,YAAY,CAAC,IAAI,CAAC6R,eAAe,CAAC;IAClC,IAAI,CAACL,yBAAyB,CAAC,CAAC;EAClC,CAAC;EACDA,yBAAyB,EAAE,SAASA,yBAAyBA,CAAA,EAAG;IAC9D,IAAIN,aAAa,GAAG,IAAI,CAACvZ,EAAE,CAACuZ,aAAa;IACzCnZ,GAAG,CAACmZ,aAAa,EAAE,SAAS,EAAE,IAAI,CAACS,mBAAmB,CAAC;IACvD5Z,GAAG,CAACmZ,aAAa,EAAE,UAAU,EAAE,IAAI,CAACS,mBAAmB,CAAC;IACxD5Z,GAAG,CAACmZ,aAAa,EAAE,aAAa,EAAE,IAAI,CAACS,mBAAmB,CAAC;IAC3D5Z,GAAG,CAACmZ,aAAa,EAAE,WAAW,EAAE,IAAI,CAACU,4BAA4B,CAAC;IAClE7Z,GAAG,CAACmZ,aAAa,EAAE,WAAW,EAAE,IAAI,CAACU,4BAA4B,CAAC;IAClE7Z,GAAG,CAACmZ,aAAa,EAAE,aAAa,EAAE,IAAI,CAACU,4BAA4B,CAAC;EACtE,CAAC;EACDH,iBAAiB,EAAE,SAASA,iBAAiBA,CAAE,YAAY3M,GAAG,EAAE,YAAYwL,KAAK,EAAE;IACjFA,KAAK,GAAGA,KAAK,IAAIxL,GAAG,CAACyL,WAAW,IAAI,OAAO,IAAIzL,GAAG;IAClD,IAAI,CAAC,IAAI,CAACkL,eAAe,IAAIM,KAAK,EAAE;MAClC,IAAI,IAAI,CAAC1T,OAAO,CAACmT,cAAc,EAAE;QAC/BrY,EAAE,CAACe,QAAQ,EAAE,aAAa,EAAE,IAAI,CAACuZ,YAAY,CAAC;MAChD,CAAC,MAAM,IAAI1B,KAAK,EAAE;QAChB5Y,EAAE,CAACe,QAAQ,EAAE,WAAW,EAAE,IAAI,CAACuZ,YAAY,CAAC;MAC9C,CAAC,MAAM;QACLta,EAAE,CAACe,QAAQ,EAAE,WAAW,EAAE,IAAI,CAACuZ,YAAY,CAAC;MAC9C;IACF,CAAC,MAAM;MACLta,EAAE,CAAC+P,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC;MAC3B/P,EAAE,CAACkO,MAAM,EAAE,WAAW,EAAE,IAAI,CAACqM,YAAY,CAAC;IAC5C;IACA,IAAI;MACF,IAAIxZ,QAAQ,CAACyZ,SAAS,EAAE;QACtB;QACAC,SAAS,CAAC,YAAY;UACpB1Z,QAAQ,CAACyZ,SAAS,CAACE,KAAK,CAAC,CAAC;QAC5B,CAAC,CAAC;MACJ,CAAC,MAAM;QACLtb,MAAM,CAACub,YAAY,CAAC,CAAC,CAACC,eAAe,CAAC,CAAC;MACzC;IACF,CAAC,CAAC,OAAOC,GAAG,EAAE,CAAC;EACjB,CAAC;EACDC,YAAY,EAAE,SAASA,YAAYA,CAACC,QAAQ,EAAE3N,GAAG,EAAE;IACjD8D,mBAAmB,GAAG,KAAK;IAC3B,IAAIhD,MAAM,IAAI6B,MAAM,EAAE;MACpB9C,WAAW,CAAC,aAAa,EAAE,IAAI,EAAE;QAC/BG,GAAG,EAAEA;MACP,CAAC,CAAC;MACF,IAAI,IAAI,CAACkL,eAAe,EAAE;QACxBtY,EAAE,CAACe,QAAQ,EAAE,UAAU,EAAEuV,qBAAqB,CAAC;MACjD;MACA,IAAIpR,OAAO,GAAG,IAAI,CAACA,OAAO;;MAE1B;MACA,CAAC6V,QAAQ,IAAIzZ,WAAW,CAACyO,MAAM,EAAE7K,OAAO,CAACiS,SAAS,EAAE,KAAK,CAAC;MAC1D7V,WAAW,CAACyO,MAAM,EAAE7K,OAAO,CAAC+R,UAAU,EAAE,IAAI,CAAC;MAC7C1R,QAAQ,CAACgL,MAAM,GAAG,IAAI;MACtBwK,QAAQ,IAAI,IAAI,CAACC,YAAY,CAAC,CAAC;;MAE/B;MACAjK,cAAc,CAAC;QACb5D,QAAQ,EAAE,IAAI;QACdvO,IAAI,EAAE,OAAO;QACb+P,aAAa,EAAEvB;MACjB,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAI,CAAC6N,QAAQ,CAAC,CAAC;IACjB;EACF,CAAC;EACDC,gBAAgB,EAAE,SAASA,gBAAgBA,CAAA,EAAG;IAC5C,IAAI5J,QAAQ,EAAE;MACZ,IAAI,CAACqI,MAAM,GAAGrI,QAAQ,CAAC6E,OAAO;MAC9B,IAAI,CAACyD,MAAM,GAAGtI,QAAQ,CAAC8E,OAAO;MAC9B3F,mBAAmB,CAAC,CAAC;MACrB,IAAI5U,MAAM,GAAGkF,QAAQ,CAACoa,gBAAgB,CAAC7J,QAAQ,CAAC6E,OAAO,EAAE7E,QAAQ,CAAC8E,OAAO,CAAC;MAC1E,IAAIzR,MAAM,GAAG9I,MAAM;MACnB,OAAOA,MAAM,IAAIA,MAAM,CAACkd,UAAU,EAAE;QAClCld,MAAM,GAAGA,MAAM,CAACkd,UAAU,CAACoC,gBAAgB,CAAC7J,QAAQ,CAAC6E,OAAO,EAAE7E,QAAQ,CAAC8E,OAAO,CAAC;QAC/E,IAAIva,MAAM,KAAK8I,MAAM,EAAE;QACvBA,MAAM,GAAG9I,MAAM;MACjB;MACAkU,MAAM,CAAC9O,UAAU,CAAC8I,OAAO,CAAC,CAACwM,gBAAgB,CAAC1a,MAAM,CAAC;MACnD,IAAI8I,MAAM,EAAE;QACV,GAAG;UACD,IAAIA,MAAM,CAACoF,OAAO,CAAC,EAAE;YACnB,IAAIqR,QAAQ,GAAG,KAAK,CAAC;YACrBA,QAAQ,GAAGzW,MAAM,CAACoF,OAAO,CAAC,CAACsM,WAAW,CAAC;cACrCF,OAAO,EAAE7E,QAAQ,CAAC6E,OAAO;cACzBC,OAAO,EAAE9E,QAAQ,CAAC8E,OAAO;cACzBva,MAAM,EAAEA,MAAM;cACdqS,MAAM,EAAEvJ;YACV,CAAC,CAAC;YACF,IAAIyW,QAAQ,IAAI,CAAC,IAAI,CAAClW,OAAO,CAACwS,cAAc,EAAE;cAC5C;YACF;UACF;UACA7b,MAAM,GAAG8I,MAAM,CAAC,CAAC;QACnB;QACA,8BAA8BA,MAAM,GAAG9D,eAAe,CAAC8D,MAAM,CAAC;MAChE;MACAgM,qBAAqB,CAAC,CAAC;IACzB;EACF,CAAC;EACD2J,YAAY,EAAE,SAASA,YAAYA,CAAE,eAAelN,GAAG,EAAE;IACvD,IAAIiE,MAAM,EAAE;MACV,IAAInM,OAAO,GAAG,IAAI,CAACA,OAAO;QACxBiT,iBAAiB,GAAGjT,OAAO,CAACiT,iBAAiB;QAC7CC,cAAc,GAAGlT,OAAO,CAACkT,cAAc;QACvCQ,KAAK,GAAGxL,GAAG,CAAC6I,OAAO,GAAG7I,GAAG,CAAC6I,OAAO,CAAC,CAAC,CAAC,GAAG7I,GAAG;QAC1CiO,WAAW,GAAGjS,OAAO,IAAIlH,MAAM,CAACkH,OAAO,EAAE,IAAI,CAAC;QAC9ChF,MAAM,GAAGgF,OAAO,IAAIiS,WAAW,IAAIA,WAAW,CAAChX,CAAC;QAChDC,MAAM,GAAG8E,OAAO,IAAIiS,WAAW,IAAIA,WAAW,CAAC9W,CAAC;QAChD+W,oBAAoB,GAAGjJ,uBAAuB,IAAIL,mBAAmB,IAAI7L,uBAAuB,CAAC6L,mBAAmB,CAAC;QACrHuJ,EAAE,GAAG,CAAC3C,KAAK,CAACzC,OAAO,GAAG9E,MAAM,CAAC8E,OAAO,GAAGiC,cAAc,CAAC5P,CAAC,KAAKpE,MAAM,IAAI,CAAC,CAAC,GAAG,CAACkX,oBAAoB,GAAGA,oBAAoB,CAAC,CAAC,CAAC,GAAGrJ,gCAAgC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK7N,MAAM,IAAI,CAAC,CAAC;QACrLoX,EAAE,GAAG,CAAC5C,KAAK,CAACxC,OAAO,GAAG/E,MAAM,CAAC+E,OAAO,GAAGgC,cAAc,CAAC3P,CAAC,KAAKnE,MAAM,IAAI,CAAC,CAAC,GAAG,CAACgX,oBAAoB,GAAGA,oBAAoB,CAAC,CAAC,CAAC,GAAGrJ,gCAAgC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK3N,MAAM,IAAI,CAAC,CAAC;;MAEvL;MACA,IAAI,CAACiB,QAAQ,CAACgL,MAAM,IAAI,CAACW,mBAAmB,EAAE;QAC5C,IAAIiH,iBAAiB,IAAIvQ,IAAI,CAACkC,GAAG,CAAClC,IAAI,CAACwS,GAAG,CAACxB,KAAK,CAACzC,OAAO,GAAG,IAAI,CAACwD,MAAM,CAAC,EAAE/R,IAAI,CAACwS,GAAG,CAACxB,KAAK,CAACxC,OAAO,GAAG,IAAI,CAACwD,MAAM,CAAC,CAAC,GAAGzB,iBAAiB,EAAE;UACnI;QACF;QACA,IAAI,CAACoC,YAAY,CAACnN,GAAG,EAAE,IAAI,CAAC;MAC9B;MACA,IAAIhE,OAAO,EAAE;QACX,IAAIiS,WAAW,EAAE;UACfA,WAAW,CAAC1Q,CAAC,IAAI4Q,EAAE,IAAIhK,MAAM,IAAI,CAAC,CAAC;UACnC8J,WAAW,CAAC3Q,CAAC,IAAI8Q,EAAE,IAAIhK,MAAM,IAAI,CAAC,CAAC;QACrC,CAAC,MAAM;UACL6J,WAAW,GAAG;YACZhX,CAAC,EAAE,CAAC;YACJoX,CAAC,EAAE,CAAC;YACJC,CAAC,EAAE,CAAC;YACJnX,CAAC,EAAE,CAAC;YACJoG,CAAC,EAAE4Q,EAAE;YACL7Q,CAAC,EAAE8Q;UACL,CAAC;QACH;QACA,IAAIG,SAAS,GAAG,SAAS,CAAC3O,MAAM,CAACqO,WAAW,CAAChX,CAAC,EAAE,GAAG,CAAC,CAAC2I,MAAM,CAACqO,WAAW,CAACI,CAAC,EAAE,GAAG,CAAC,CAACzO,MAAM,CAACqO,WAAW,CAACK,CAAC,EAAE,GAAG,CAAC,CAAC1O,MAAM,CAACqO,WAAW,CAAC9W,CAAC,EAAE,GAAG,CAAC,CAACyI,MAAM,CAACqO,WAAW,CAAC1Q,CAAC,EAAE,GAAG,CAAC,CAACqC,MAAM,CAACqO,WAAW,CAAC3Q,CAAC,EAAE,GAAG,CAAC;QAC3L/I,GAAG,CAACyH,OAAO,EAAE,iBAAiB,EAAEuS,SAAS,CAAC;QAC1Cha,GAAG,CAACyH,OAAO,EAAE,cAAc,EAAEuS,SAAS,CAAC;QACvCha,GAAG,CAACyH,OAAO,EAAE,aAAa,EAAEuS,SAAS,CAAC;QACtCha,GAAG,CAACyH,OAAO,EAAE,WAAW,EAAEuS,SAAS,CAAC;QACpCpK,MAAM,GAAGgK,EAAE;QACX/J,MAAM,GAAGgK,EAAE;QACXlK,QAAQ,GAAGsH,KAAK;MAClB;MACAxL,GAAG,CAAC+B,UAAU,IAAI/B,GAAG,CAACyI,cAAc,CAAC,CAAC;IACxC;EACF,CAAC;EACDmF,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;IACpC;IACA;IACA,IAAI,CAAC5R,OAAO,EAAE;MACZ,IAAI9F,SAAS,GAAG,IAAI,CAAC4B,OAAO,CAACgT,cAAc,GAAGnX,QAAQ,CAACsG,IAAI,GAAG6G,MAAM;QAClEjF,IAAI,GAAG/F,OAAO,CAAC6M,MAAM,EAAE,IAAI,EAAEsC,uBAAuB,EAAE,IAAI,EAAE/O,SAAS,CAAC;QACtE4B,OAAO,GAAG,IAAI,CAACA,OAAO;;MAExB;MACA,IAAImN,uBAAuB,EAAE;QAC3B;QACAL,mBAAmB,GAAG1O,SAAS;QAC/B,OAAO3B,GAAG,CAACqQ,mBAAmB,EAAE,UAAU,CAAC,KAAK,QAAQ,IAAIrQ,GAAG,CAACqQ,mBAAmB,EAAE,WAAW,CAAC,KAAK,MAAM,IAAIA,mBAAmB,KAAKjR,QAAQ,EAAE;UAChJiR,mBAAmB,GAAGA,mBAAmB,CAAC/Q,UAAU;QACtD;QACA,IAAI+Q,mBAAmB,KAAKjR,QAAQ,CAACsG,IAAI,IAAI2K,mBAAmB,KAAKjR,QAAQ,CAACkC,eAAe,EAAE;UAC7F,IAAI+O,mBAAmB,KAAKjR,QAAQ,EAAEiR,mBAAmB,GAAGjP,yBAAyB,CAAC,CAAC;UACvFkG,IAAI,CAACxF,GAAG,IAAIuO,mBAAmB,CAACxL,SAAS;UACzCyC,IAAI,CAACvF,IAAI,IAAIsO,mBAAmB,CAACzL,UAAU;QAC7C,CAAC,MAAM;UACLyL,mBAAmB,GAAGjP,yBAAyB,CAAC,CAAC;QACnD;QACAkP,gCAAgC,GAAG9L,uBAAuB,CAAC6L,mBAAmB,CAAC;MACjF;MACA5I,OAAO,GAAG2G,MAAM,CAAChH,SAAS,CAAC,IAAI,CAAC;MAChCzH,WAAW,CAAC8H,OAAO,EAAElE,OAAO,CAAC+R,UAAU,EAAE,KAAK,CAAC;MAC/C3V,WAAW,CAAC8H,OAAO,EAAElE,OAAO,CAAC+S,aAAa,EAAE,IAAI,CAAC;MACjD3W,WAAW,CAAC8H,OAAO,EAAElE,OAAO,CAACiS,SAAS,EAAE,IAAI,CAAC;MAC7CxV,GAAG,CAACyH,OAAO,EAAE,YAAY,EAAE,EAAE,CAAC;MAC9BzH,GAAG,CAACyH,OAAO,EAAE,WAAW,EAAE,EAAE,CAAC;MAC7BzH,GAAG,CAACyH,OAAO,EAAE,YAAY,EAAE,YAAY,CAAC;MACxCzH,GAAG,CAACyH,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC;MACzBzH,GAAG,CAACyH,OAAO,EAAE,KAAK,EAAEH,IAAI,CAACxF,GAAG,CAAC;MAC7B9B,GAAG,CAACyH,OAAO,EAAE,MAAM,EAAEH,IAAI,CAACvF,IAAI,CAAC;MAC/B/B,GAAG,CAACyH,OAAO,EAAE,OAAO,EAAEH,IAAI,CAACnF,KAAK,CAAC;MACjCnC,GAAG,CAACyH,OAAO,EAAE,QAAQ,EAAEH,IAAI,CAACpF,MAAM,CAAC;MACnClC,GAAG,CAACyH,OAAO,EAAE,SAAS,EAAE,KAAK,CAAC;MAC9BzH,GAAG,CAACyH,OAAO,EAAE,UAAU,EAAEiJ,uBAAuB,GAAG,UAAU,GAAG,OAAO,CAAC;MACxE1Q,GAAG,CAACyH,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC;MAChCzH,GAAG,CAACyH,OAAO,EAAE,eAAe,EAAE,MAAM,CAAC;MACrC7D,QAAQ,CAACC,KAAK,GAAG4D,OAAO;MACxB9F,SAAS,CAACsY,WAAW,CAACxS,OAAO,CAAC;;MAE9B;MACAzH,GAAG,CAACyH,OAAO,EAAE,kBAAkB,EAAEqI,eAAe,GAAGvN,QAAQ,CAACkF,OAAO,CAACtH,KAAK,CAACgC,KAAK,CAAC,GAAG,GAAG,GAAG,IAAI,GAAG4N,cAAc,GAAGxN,QAAQ,CAACkF,OAAO,CAACtH,KAAK,CAAC+B,MAAM,CAAC,GAAG,GAAG,GAAG,GAAG,CAAC;IAC9J;EACF,CAAC;EACD0W,YAAY,EAAE,SAASA,YAAYA,CAAE,UAAUnN,GAAG,EAAE,YAAY2N,QAAQ,EAAE;IACxE,IAAI5S,KAAK,GAAG,IAAI;IAChB,IAAIoP,YAAY,GAAGnK,GAAG,CAACmK,YAAY;IACnC,IAAIrS,OAAO,GAAGiD,KAAK,CAACjD,OAAO;IAC3B+H,WAAW,CAAC,WAAW,EAAE,IAAI,EAAE;MAC7BG,GAAG,EAAEA;IACP,CAAC,CAAC;IACF,IAAI7H,QAAQ,CAAC8H,aAAa,EAAE;MAC1B,IAAI,CAACwM,OAAO,CAAC,CAAC;MACd;IACF;IACA5M,WAAW,CAAC,YAAY,EAAE,IAAI,CAAC;IAC/B,IAAI,CAAC1H,QAAQ,CAAC8H,aAAa,EAAE;MAC3Be,OAAO,GAAGlI,KAAK,CAAC6J,MAAM,CAAC;MACvB3B,OAAO,CAACyN,eAAe,CAAC,IAAI,CAAC;MAC7BzN,OAAO,CAAC1I,SAAS,GAAG,KAAK;MACzB0I,OAAO,CAACtM,KAAK,CAAC,aAAa,CAAC,GAAG,EAAE;MACjC,IAAI,CAACga,UAAU,CAAC,CAAC;MACjBxa,WAAW,CAAC8M,OAAO,EAAE,IAAI,CAAClJ,OAAO,CAACgS,WAAW,EAAE,KAAK,CAAC;MACrD3R,QAAQ,CAACW,KAAK,GAAGkI,OAAO;IAC1B;;IAEA;IACAjG,KAAK,CAAC4T,OAAO,GAAGtB,SAAS,CAAC,YAAY;MACpCxN,WAAW,CAAC,OAAO,EAAE9E,KAAK,CAAC;MAC3B,IAAI5C,QAAQ,CAAC8H,aAAa,EAAE;MAC5B,IAAI,CAAClF,KAAK,CAACjD,OAAO,CAAC6R,iBAAiB,EAAE;QACpC7I,MAAM,CAAC8N,YAAY,CAAC5N,OAAO,EAAE2B,MAAM,CAAC;MACtC;MACA5H,KAAK,CAAC2T,UAAU,CAAC,CAAC;MAClB/K,cAAc,CAAC;QACb5D,QAAQ,EAAEhF,KAAK;QACfvJ,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,CAACmc,QAAQ,IAAIzZ,WAAW,CAACyO,MAAM,EAAE7K,OAAO,CAACiS,SAAS,EAAE,IAAI,CAAC;;IAEzD;IACA,IAAI4D,QAAQ,EAAE;MACZ5J,eAAe,GAAG,IAAI;MACtBhJ,KAAK,CAAC8T,OAAO,GAAGC,WAAW,CAAC/T,KAAK,CAAC+S,gBAAgB,EAAE,EAAE,CAAC;IACzD,CAAC,MAAM;MACL;MACA7a,GAAG,CAACU,QAAQ,EAAE,SAAS,EAAEoH,KAAK,CAAC0R,OAAO,CAAC;MACvCxZ,GAAG,CAACU,QAAQ,EAAE,UAAU,EAAEoH,KAAK,CAAC0R,OAAO,CAAC;MACxCxZ,GAAG,CAACU,QAAQ,EAAE,aAAa,EAAEoH,KAAK,CAAC0R,OAAO,CAAC;MAC3C,IAAItC,YAAY,EAAE;QAChBA,YAAY,CAAC4E,aAAa,GAAG,MAAM;QACnCjX,OAAO,CAACoS,OAAO,IAAIpS,OAAO,CAACoS,OAAO,CAACla,IAAI,CAAC+K,KAAK,EAAEoP,YAAY,EAAExH,MAAM,CAAC;MACtE;MACA/P,EAAE,CAACe,QAAQ,EAAE,MAAM,EAAEoH,KAAK,CAAC;;MAE3B;MACAxG,GAAG,CAACoO,MAAM,EAAE,WAAW,EAAE,eAAe,CAAC;IAC3C;IACAmB,mBAAmB,GAAG,IAAI;IAC1B/I,KAAK,CAACiU,YAAY,GAAG3B,SAAS,CAACtS,KAAK,CAAC2S,YAAY,CAAChL,IAAI,CAAC3H,KAAK,EAAE4S,QAAQ,EAAE3N,GAAG,CAAC,CAAC;IAC7EpN,EAAE,CAACe,QAAQ,EAAE,aAAa,EAAEoH,KAAK,CAAC;IAClCkI,KAAK,GAAG,IAAI;IACZ,IAAI3Q,MAAM,EAAE;MACViC,GAAG,CAACZ,QAAQ,CAACsG,IAAI,EAAE,aAAa,EAAE,MAAM,CAAC;IAC3C;EACF,CAAC;EACD;EACAgP,WAAW,EAAE,SAASA,WAAWA,CAAE,UAAUjJ,GAAG,EAAE;IAChD,IAAInN,EAAE,GAAG,IAAI,CAACA,EAAE;MACdpE,MAAM,GAAGuR,GAAG,CAACvR,MAAM;MACnBoY,QAAQ;MACRC,UAAU;MACVmI,MAAM;MACNnX,OAAO,GAAG,IAAI,CAACA,OAAO;MACtBmQ,KAAK,GAAGnQ,OAAO,CAACmQ,KAAK;MACrB/E,cAAc,GAAG/K,QAAQ,CAACgL,MAAM;MAChC+L,OAAO,GAAGrL,WAAW,KAAKoE,KAAK;MAC/BkH,OAAO,GAAGrX,OAAO,CAACsR,IAAI;MACtBgG,YAAY,GAAG5N,WAAW,IAAI0B,cAAc;MAC5C6D,QAAQ;MACRhM,KAAK,GAAG,IAAI;MACZsU,cAAc,GAAG,KAAK;IACxB,IAAIvK,OAAO,EAAE;IACb,SAASwK,aAAaA,CAAC9d,IAAI,EAAE+d,KAAK,EAAE;MAClC1P,WAAW,CAACrO,IAAI,EAAEuJ,KAAK,EAAEvM,cAAc,CAAC;QACtCwR,GAAG,EAAEA,GAAG;QACRkP,OAAO,EAAEA,OAAO;QAChBM,IAAI,EAAEzI,QAAQ,GAAG,UAAU,GAAG,YAAY;QAC1CkI,MAAM,EAAEA,MAAM;QACdpI,QAAQ,EAAEA,QAAQ;QAClBC,UAAU,EAAEA,UAAU;QACtBqI,OAAO,EAAEA,OAAO;QAChBC,YAAY,EAAEA,YAAY;QAC1B3gB,MAAM,EAAEA,MAAM;QACdghB,SAAS,EAAEA,SAAS;QACpBC,MAAM,EAAE,SAASA,MAAMA,CAACjhB,MAAM,EAAEkhB,KAAK,EAAE;UACrC,OAAOC,OAAO,CAAC9O,MAAM,EAAEjO,EAAE,EAAE8P,MAAM,EAAEkE,QAAQ,EAAEpY,MAAM,EAAEqH,OAAO,CAACrH,MAAM,CAAC,EAAEuR,GAAG,EAAE2P,KAAK,CAAC;QACnF,CAAC;QACDE,OAAO,EAAEA;MACX,CAAC,EAAEN,KAAK,CAAC,CAAC;IACZ;;IAEA;IACA,SAAS7c,OAAOA,CAAA,EAAG;MACjB4c,aAAa,CAAC,0BAA0B,CAAC;MACzCvU,KAAK,CAACkC,qBAAqB,CAAC,CAAC;MAC7B,IAAIlC,KAAK,KAAKqU,YAAY,EAAE;QAC1BA,YAAY,CAACnS,qBAAqB,CAAC,CAAC;MACtC;IACF;;IAEA;IACA,SAASwS,SAASA,CAACK,SAAS,EAAE;MAC5BR,aAAa,CAAC,mBAAmB,EAAE;QACjCQ,SAAS,EAAEA;MACb,CAAC,CAAC;MACF,IAAIA,SAAS,EAAE;QACb;QACA,IAAIZ,OAAO,EAAE;UACXhM,cAAc,CAACwL,UAAU,CAAC,CAAC;QAC7B,CAAC,MAAM;UACLxL,cAAc,CAAC6M,UAAU,CAAChV,KAAK,CAAC;QAClC;QACA,IAAIA,KAAK,KAAKqU,YAAY,EAAE;UAC1B;UACAlb,WAAW,CAACyO,MAAM,EAAEnB,WAAW,GAAGA,WAAW,CAAC1J,OAAO,CAAC+R,UAAU,GAAG3G,cAAc,CAACpL,OAAO,CAAC+R,UAAU,EAAE,KAAK,CAAC;UAC5G3V,WAAW,CAACyO,MAAM,EAAE7K,OAAO,CAAC+R,UAAU,EAAE,IAAI,CAAC;QAC/C;QACA,IAAIrI,WAAW,KAAKzG,KAAK,IAAIA,KAAK,KAAK5C,QAAQ,CAACgL,MAAM,EAAE;UACtD3B,WAAW,GAAGzG,KAAK;QACrB,CAAC,MAAM,IAAIA,KAAK,KAAK5C,QAAQ,CAACgL,MAAM,IAAI3B,WAAW,EAAE;UACnDA,WAAW,GAAG,IAAI;QACpB;;QAEA;QACA,IAAI4N,YAAY,KAAKrU,KAAK,EAAE;UAC1BA,KAAK,CAACiV,qBAAqB,GAAGvhB,MAAM;QACtC;QACAsM,KAAK,CAAC4C,UAAU,CAAC,YAAY;UAC3B2R,aAAa,CAAC,2BAA2B,CAAC;UAC1CvU,KAAK,CAACiV,qBAAqB,GAAG,IAAI;QACpC,CAAC,CAAC;QACF,IAAIjV,KAAK,KAAKqU,YAAY,EAAE;UAC1BA,YAAY,CAACzR,UAAU,CAAC,CAAC;UACzByR,YAAY,CAACY,qBAAqB,GAAG,IAAI;QAC3C;MACF;;MAEA;MACA,IAAIvhB,MAAM,KAAKkU,MAAM,IAAI,CAACA,MAAM,CAACrG,QAAQ,IAAI7N,MAAM,KAAKoE,EAAE,IAAI,CAACpE,MAAM,CAAC6N,QAAQ,EAAE;QAC9EiI,UAAU,GAAG,IAAI;MACnB;;MAEA;MACA,IAAI,CAACzM,OAAO,CAACwS,cAAc,IAAI,CAACtK,GAAG,CAACc,MAAM,IAAIrS,MAAM,KAAKkF,QAAQ,EAAE;QACjEgP,MAAM,CAAC9O,UAAU,CAAC8I,OAAO,CAAC,CAACwM,gBAAgB,CAACnJ,GAAG,CAACvR,MAAM,CAAC;;QAEvD;QACA,CAACqhB,SAAS,IAAIlH,6BAA6B,CAAC5I,GAAG,CAAC;MAClD;MACA,CAAClI,OAAO,CAACwS,cAAc,IAAItK,GAAG,CAAC0I,eAAe,IAAI1I,GAAG,CAAC0I,eAAe,CAAC,CAAC;MACvE,OAAO2G,cAAc,GAAG,IAAI;IAC9B;;IAEA;IACA,SAASQ,OAAOA,CAAA,EAAG;MACjBzO,QAAQ,GAAGzI,KAAK,CAACgK,MAAM,CAAC;MACxBrB,iBAAiB,GAAG3I,KAAK,CAACgK,MAAM,EAAE7K,OAAO,CAACQ,SAAS,CAAC;MACpDqL,cAAc,CAAC;QACb5D,QAAQ,EAAEhF,KAAK;QACfvJ,IAAI,EAAE,QAAQ;QACdyP,IAAI,EAAEpO,EAAE;QACRuO,QAAQ,EAAEA,QAAQ;QAClBE,iBAAiB,EAAEA,iBAAiB;QACpCC,aAAa,EAAEvB;MACjB,CAAC,CAAC;IACJ;IACA,IAAIA,GAAG,CAACyI,cAAc,KAAK,KAAK,CAAC,EAAE;MACjCzI,GAAG,CAAC+B,UAAU,IAAI/B,GAAG,CAACyI,cAAc,CAAC,CAAC;IACxC;IACAha,MAAM,GAAGqF,OAAO,CAACrF,MAAM,EAAEqJ,OAAO,CAACQ,SAAS,EAAEzF,EAAE,EAAE,IAAI,CAAC;IACrDyc,aAAa,CAAC,UAAU,CAAC;IACzB,IAAInX,QAAQ,CAAC8H,aAAa,EAAE,OAAOoP,cAAc;IACjD,IAAI1M,MAAM,CAAC0I,QAAQ,CAACrL,GAAG,CAACvR,MAAM,CAAC,IAAIA,MAAM,CAAC6N,QAAQ,IAAI7N,MAAM,CAACkQ,UAAU,IAAIlQ,MAAM,CAACmQ,UAAU,IAAI7D,KAAK,CAACiV,qBAAqB,KAAKvhB,MAAM,EAAE;MACtI,OAAOghB,SAAS,CAAC,KAAK,CAAC;IACzB;IACA1L,eAAe,GAAG,KAAK;IACvB,IAAIb,cAAc,IAAI,CAACpL,OAAO,CAACuR,QAAQ,KAAK6F,OAAO,GAAGC,OAAO,KAAKF,MAAM,GAAGrM,QAAQ,KAAK9B,MAAM,CAAC,CAAC;IAAA,EAC9FU,WAAW,KAAK,IAAI,IAAI,CAAC,IAAI,CAACa,WAAW,GAAGwB,WAAW,CAACwE,SAAS,CAAC,IAAI,EAAEnF,cAAc,EAAEP,MAAM,EAAE3C,GAAG,CAAC,KAAKiI,KAAK,CAACK,QAAQ,CAAC,IAAI,EAAEpF,cAAc,EAAEP,MAAM,EAAE3C,GAAG,CAAC,CAAC,EAAE;MAC7J+G,QAAQ,GAAG,IAAI,CAACuE,aAAa,CAACtL,GAAG,EAAEvR,MAAM,CAAC,KAAK,UAAU;MACzDoY,QAAQ,GAAG/Q,OAAO,CAAC6M,MAAM,CAAC;MAC1B2M,aAAa,CAAC,eAAe,CAAC;MAC9B,IAAInX,QAAQ,CAAC8H,aAAa,EAAE,OAAOoP,cAAc;MACjD,IAAIJ,MAAM,EAAE;QACVrM,QAAQ,GAAG9B,MAAM,CAAC,CAAC;QACnBpO,OAAO,CAAC,CAAC;QACT,IAAI,CAACgc,UAAU,CAAC,CAAC;QACjBY,aAAa,CAAC,QAAQ,CAAC;QACvB,IAAI,CAACnX,QAAQ,CAAC8H,aAAa,EAAE;UAC3B,IAAI4C,MAAM,EAAE;YACV/B,MAAM,CAAC8N,YAAY,CAACjM,MAAM,EAAEE,MAAM,CAAC;UACrC,CAAC,MAAM;YACL/B,MAAM,CAAC0N,WAAW,CAAC7L,MAAM,CAAC;UAC5B;QACF;QACA,OAAO8M,SAAS,CAAC,IAAI,CAAC;MACxB;MACA,IAAIQ,WAAW,GAAG1X,SAAS,CAAC1F,EAAE,EAAEiF,OAAO,CAACQ,SAAS,CAAC;MAClD,IAAI,CAAC2X,WAAW,IAAIC,YAAY,CAAClQ,GAAG,EAAE+G,QAAQ,EAAE,IAAI,CAAC,IAAI,CAACkJ,WAAW,CAAC3T,QAAQ,EAAE;QAC9E;;QAEA;QACA,IAAI2T,WAAW,KAAKtN,MAAM,EAAE;UAC1B,OAAO8M,SAAS,CAAC,KAAK,CAAC;QACzB;;QAEA;QACA,IAAIQ,WAAW,IAAIpd,EAAE,KAAKmN,GAAG,CAACvR,MAAM,EAAE;UACpCA,MAAM,GAAGwhB,WAAW;QACtB;QACA,IAAIxhB,MAAM,EAAE;UACVqY,UAAU,GAAGhR,OAAO,CAACrH,MAAM,CAAC;QAC9B;QACA,IAAImhB,OAAO,CAAC9O,MAAM,EAAEjO,EAAE,EAAE8P,MAAM,EAAEkE,QAAQ,EAAEpY,MAAM,EAAEqY,UAAU,EAAE9G,GAAG,EAAE,CAAC,CAACvR,MAAM,CAAC,KAAK,KAAK,EAAE;UACtFiE,OAAO,CAAC,CAAC;UACT,IAAIud,WAAW,IAAIA,WAAW,CAAC3D,WAAW,EAAE;YAC1C;YACAzZ,EAAE,CAAC+b,YAAY,CAACjM,MAAM,EAAEsN,WAAW,CAAC3D,WAAW,CAAC;UAClD,CAAC,MAAM;YACLzZ,EAAE,CAAC2b,WAAW,CAAC7L,MAAM,CAAC;UACxB;UACAC,QAAQ,GAAG/P,EAAE,CAAC,CAAC;;UAEfgd,OAAO,CAAC,CAAC;UACT,OAAOJ,SAAS,CAAC,IAAI,CAAC;QACxB;MACF,CAAC,MAAM,IAAIQ,WAAW,IAAIE,aAAa,CAACnQ,GAAG,EAAE+G,QAAQ,EAAE,IAAI,CAAC,EAAE;QAC5D;QACA,IAAIqJ,UAAU,GAAGxY,QAAQ,CAAC/E,EAAE,EAAE,CAAC,EAAEiF,OAAO,EAAE,IAAI,CAAC;QAC/C,IAAIsY,UAAU,KAAKzN,MAAM,EAAE;UACzB,OAAO8M,SAAS,CAAC,KAAK,CAAC;QACzB;QACAhhB,MAAM,GAAG2hB,UAAU;QACnBtJ,UAAU,GAAGhR,OAAO,CAACrH,MAAM,CAAC;QAC5B,IAAImhB,OAAO,CAAC9O,MAAM,EAAEjO,EAAE,EAAE8P,MAAM,EAAEkE,QAAQ,EAAEpY,MAAM,EAAEqY,UAAU,EAAE9G,GAAG,EAAE,KAAK,CAAC,KAAK,KAAK,EAAE;UACnFtN,OAAO,CAAC,CAAC;UACTG,EAAE,CAAC+b,YAAY,CAACjM,MAAM,EAAEyN,UAAU,CAAC;UACnCxN,QAAQ,GAAG/P,EAAE,CAAC,CAAC;;UAEfgd,OAAO,CAAC,CAAC;UACT,OAAOJ,SAAS,CAAC,IAAI,CAAC;QACxB;MACF,CAAC,MAAM,IAAIhhB,MAAM,CAACoF,UAAU,KAAKhB,EAAE,EAAE;QACnCiU,UAAU,GAAGhR,OAAO,CAACrH,MAAM,CAAC;QAC5B,IAAImb,SAAS,GAAG,CAAC;UACfyG,qBAAqB;UACrBC,cAAc,GAAG3N,MAAM,CAAC9O,UAAU,KAAKhB,EAAE;UACzC0d,eAAe,GAAG,CAAC3J,kBAAkB,CAACjE,MAAM,CAACrG,QAAQ,IAAIqG,MAAM,CAAC5E,MAAM,IAAI8I,QAAQ,EAAEpY,MAAM,CAAC6N,QAAQ,IAAI7N,MAAM,CAACsP,MAAM,IAAI+I,UAAU,EAAEC,QAAQ,CAAC;UAC7IyJ,KAAK,GAAGzJ,QAAQ,GAAG,KAAK,GAAG,MAAM;UACjC0J,eAAe,GAAGrZ,cAAc,CAAC3I,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC,IAAI2I,cAAc,CAACuL,MAAM,EAAE,KAAK,EAAE,KAAK,CAAC;UAC9F+N,YAAY,GAAGD,eAAe,GAAGA,eAAe,CAACrX,SAAS,GAAG,KAAK,CAAC;QACrE,IAAImL,UAAU,KAAK9V,MAAM,EAAE;UACzB4hB,qBAAqB,GAAGvJ,UAAU,CAAC0J,KAAK,CAAC;UACzC/L,qBAAqB,GAAG,KAAK;UAC7BC,sBAAsB,GAAG,CAAC6L,eAAe,IAAIzY,OAAO,CAAC2R,UAAU,IAAI6G,cAAc;QACnF;QACA1G,SAAS,GAAG+G,iBAAiB,CAAC3Q,GAAG,EAAEvR,MAAM,EAAEqY,UAAU,EAAEC,QAAQ,EAAEwJ,eAAe,GAAG,CAAC,GAAGzY,OAAO,CAAC0R,aAAa,EAAE1R,OAAO,CAAC4R,qBAAqB,IAAI,IAAI,GAAG5R,OAAO,CAAC0R,aAAa,GAAG1R,OAAO,CAAC4R,qBAAqB,EAAEhF,sBAAsB,EAAEH,UAAU,KAAK9V,MAAM,CAAC;QAC3P,IAAImiB,OAAO;QACX,IAAIhH,SAAS,KAAK,CAAC,EAAE;UACnB;UACA,IAAIiH,SAAS,GAAGlY,KAAK,CAACgK,MAAM,CAAC;UAC7B,GAAG;YACDkO,SAAS,IAAIjH,SAAS;YACtBgH,OAAO,GAAGhO,QAAQ,CAAC3K,QAAQ,CAAC4Y,SAAS,CAAC;UACxC,CAAC,QAAQD,OAAO,KAAKrc,GAAG,CAACqc,OAAO,EAAE,SAAS,CAAC,KAAK,MAAM,IAAIA,OAAO,KAAK5U,OAAO,CAAC;QACjF;QACA;QACA,IAAI4N,SAAS,KAAK,CAAC,IAAIgH,OAAO,KAAKniB,MAAM,EAAE;UACzC,OAAOghB,SAAS,CAAC,KAAK,CAAC;QACzB;QACAlL,UAAU,GAAG9V,MAAM;QACnB+V,aAAa,GAAGoF,SAAS;QACzB,IAAI0C,WAAW,GAAG7d,MAAM,CAACqiB,kBAAkB;UACzCnB,KAAK,GAAG,KAAK;QACfA,KAAK,GAAG/F,SAAS,KAAK,CAAC;QACvB,IAAImH,UAAU,GAAGnB,OAAO,CAAC9O,MAAM,EAAEjO,EAAE,EAAE8P,MAAM,EAAEkE,QAAQ,EAAEpY,MAAM,EAAEqY,UAAU,EAAE9G,GAAG,EAAE2P,KAAK,CAAC;QACtF,IAAIoB,UAAU,KAAK,KAAK,EAAE;UACxB,IAAIA,UAAU,KAAK,CAAC,IAAIA,UAAU,KAAK,CAAC,CAAC,EAAE;YACzCpB,KAAK,GAAGoB,UAAU,KAAK,CAAC;UAC1B;UACAjM,OAAO,GAAG,IAAI;UACd9J,UAAU,CAACgW,SAAS,EAAE,EAAE,CAAC;UACzBte,OAAO,CAAC,CAAC;UACT,IAAIid,KAAK,IAAI,CAACrD,WAAW,EAAE;YACzBzZ,EAAE,CAAC2b,WAAW,CAAC7L,MAAM,CAAC;UACxB,CAAC,MAAM;YACLlU,MAAM,CAACoF,UAAU,CAAC+a,YAAY,CAACjM,MAAM,EAAEgN,KAAK,GAAGrD,WAAW,GAAG7d,MAAM,CAAC;UACtE;;UAEA;UACA,IAAIgiB,eAAe,EAAE;YACnBtV,QAAQ,CAACsV,eAAe,EAAE,CAAC,EAAEC,YAAY,GAAGD,eAAe,CAACrX,SAAS,CAAC;UACxE;UACAwJ,QAAQ,GAAGD,MAAM,CAAC9O,UAAU,CAAC,CAAC;;UAE9B;UACA,IAAIwc,qBAAqB,KAAK/N,SAAS,IAAI,CAACoC,sBAAsB,EAAE;YAClEC,kBAAkB,GAAGnK,IAAI,CAACwS,GAAG,CAACqD,qBAAqB,GAAGva,OAAO,CAACrH,MAAM,CAAC,CAAC+hB,KAAK,CAAC,CAAC;UAC/E;UACAX,OAAO,CAAC,CAAC;UACT,OAAOJ,SAAS,CAAC,IAAI,CAAC;QACxB;MACF;MACA,IAAI5c,EAAE,CAACwY,QAAQ,CAAC1I,MAAM,CAAC,EAAE;QACvB,OAAO8M,SAAS,CAAC,KAAK,CAAC;MACzB;IACF;IACA,OAAO,KAAK;EACd,CAAC;EACDO,qBAAqB,EAAE,IAAI;EAC3BiB,cAAc,EAAE,SAASA,cAAcA,CAAA,EAAG;IACxChe,GAAG,CAACU,QAAQ,EAAE,WAAW,EAAE,IAAI,CAACuZ,YAAY,CAAC;IAC7Cja,GAAG,CAACU,QAAQ,EAAE,WAAW,EAAE,IAAI,CAACuZ,YAAY,CAAC;IAC7Cja,GAAG,CAACU,QAAQ,EAAE,aAAa,EAAE,IAAI,CAACuZ,YAAY,CAAC;IAC/Cja,GAAG,CAACU,QAAQ,EAAE,UAAU,EAAEiV,6BAA6B,CAAC;IACxD3V,GAAG,CAACU,QAAQ,EAAE,WAAW,EAAEiV,6BAA6B,CAAC;IACzD3V,GAAG,CAACU,QAAQ,EAAE,WAAW,EAAEiV,6BAA6B,CAAC;EAC3D,CAAC;EACDsI,YAAY,EAAE,SAASA,YAAYA,CAAA,EAAG;IACpC,IAAI9E,aAAa,GAAG,IAAI,CAACvZ,EAAE,CAACuZ,aAAa;IACzCnZ,GAAG,CAACmZ,aAAa,EAAE,SAAS,EAAE,IAAI,CAACK,OAAO,CAAC;IAC3CxZ,GAAG,CAACmZ,aAAa,EAAE,UAAU,EAAE,IAAI,CAACK,OAAO,CAAC;IAC5CxZ,GAAG,CAACmZ,aAAa,EAAE,WAAW,EAAE,IAAI,CAACK,OAAO,CAAC;IAC7CxZ,GAAG,CAACmZ,aAAa,EAAE,aAAa,EAAE,IAAI,CAACK,OAAO,CAAC;IAC/CxZ,GAAG,CAACU,QAAQ,EAAE,aAAa,EAAE,IAAI,CAAC;EACpC,CAAC;EACD8Y,OAAO,EAAE,SAASA,OAAOA,CAAE,UAAUzM,GAAG,EAAE;IACxC,IAAInN,EAAE,GAAG,IAAI,CAACA,EAAE;MACdiF,OAAO,GAAG,IAAI,CAACA,OAAO;;IAExB;IACAsJ,QAAQ,GAAGzI,KAAK,CAACgK,MAAM,CAAC;IACxBrB,iBAAiB,GAAG3I,KAAK,CAACgK,MAAM,EAAE7K,OAAO,CAACQ,SAAS,CAAC;IACpDuH,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE;MACxBG,GAAG,EAAEA;IACP,CAAC,CAAC;IACF4C,QAAQ,GAAGD,MAAM,IAAIA,MAAM,CAAC9O,UAAU;;IAEtC;IACAuN,QAAQ,GAAGzI,KAAK,CAACgK,MAAM,CAAC;IACxBrB,iBAAiB,GAAG3I,KAAK,CAACgK,MAAM,EAAE7K,OAAO,CAACQ,SAAS,CAAC;IACpD,IAAIH,QAAQ,CAAC8H,aAAa,EAAE;MAC1B,IAAI,CAAC4N,QAAQ,CAAC,CAAC;MACf;IACF;IACA/J,mBAAmB,GAAG,KAAK;IAC3BY,sBAAsB,GAAG,KAAK;IAC9BD,qBAAqB,GAAG,KAAK;IAC7B0M,aAAa,CAAC,IAAI,CAACtC,OAAO,CAAC;IAC3B3T,YAAY,CAAC,IAAI,CAAC6R,eAAe,CAAC;IAClCqE,eAAe,CAAC,IAAI,CAACzC,OAAO,CAAC;IAC7ByC,eAAe,CAAC,IAAI,CAACpC,YAAY,CAAC;;IAElC;IACA,IAAI,IAAI,CAAC9D,eAAe,EAAE;MACxBjY,GAAG,CAACU,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC;MAC3BV,GAAG,CAACJ,EAAE,EAAE,WAAW,EAAE,IAAI,CAACsa,YAAY,CAAC;IACzC;IACA,IAAI,CAAC8D,cAAc,CAAC,CAAC;IACrB,IAAI,CAACC,YAAY,CAAC,CAAC;IACnB,IAAI5e,MAAM,EAAE;MACViC,GAAG,CAACZ,QAAQ,CAACsG,IAAI,EAAE,aAAa,EAAE,EAAE,CAAC;IACvC;IACA1F,GAAG,CAACoO,MAAM,EAAE,WAAW,EAAE,EAAE,CAAC;IAC5B,IAAI3C,GAAG,EAAE;MACP,IAAIiD,KAAK,EAAE;QACTjD,GAAG,CAAC+B,UAAU,IAAI/B,GAAG,CAACyI,cAAc,CAAC,CAAC;QACtC,CAAC3Q,OAAO,CAACuS,UAAU,IAAIrK,GAAG,CAAC0I,eAAe,CAAC,CAAC;MAC9C;MACA1M,OAAO,IAAIA,OAAO,CAACnI,UAAU,IAAImI,OAAO,CAACnI,UAAU,CAACwd,WAAW,CAACrV,OAAO,CAAC;MACxE,IAAI8E,MAAM,KAAK8B,QAAQ,IAAIpB,WAAW,IAAIA,WAAW,CAACa,WAAW,KAAK,OAAO,EAAE;QAC7E;QACArB,OAAO,IAAIA,OAAO,CAACnN,UAAU,IAAImN,OAAO,CAACnN,UAAU,CAACwd,WAAW,CAACrQ,OAAO,CAAC;MAC1E;MACA,IAAI2B,MAAM,EAAE;QACV,IAAI,IAAI,CAACuI,eAAe,EAAE;UACxBjY,GAAG,CAAC0P,MAAM,EAAE,SAAS,EAAE,IAAI,CAAC;QAC9B;QACAiK,iBAAiB,CAACjK,MAAM,CAAC;QACzBA,MAAM,CAACjO,KAAK,CAAC,aAAa,CAAC,GAAG,EAAE;;QAEhC;QACA;QACA,IAAIuO,KAAK,IAAI,CAACa,mBAAmB,EAAE;UACjC5P,WAAW,CAACyO,MAAM,EAAEnB,WAAW,GAAGA,WAAW,CAAC1J,OAAO,CAAC+R,UAAU,GAAG,IAAI,CAAC/R,OAAO,CAAC+R,UAAU,EAAE,KAAK,CAAC;QACpG;QACA3V,WAAW,CAACyO,MAAM,EAAE,IAAI,CAAC7K,OAAO,CAACgS,WAAW,EAAE,KAAK,CAAC;;QAEpD;QACAnG,cAAc,CAAC;UACb5D,QAAQ,EAAE,IAAI;UACdvO,IAAI,EAAE,UAAU;UAChByP,IAAI,EAAE2B,QAAQ;UACdxB,QAAQ,EAAE,IAAI;UACdE,iBAAiB,EAAE,IAAI;UACvBC,aAAa,EAAEvB;QACjB,CAAC,CAAC;QACF,IAAIc,MAAM,KAAK8B,QAAQ,EAAE;UACvB,IAAIxB,QAAQ,IAAI,CAAC,EAAE;YACjB;YACAuC,cAAc,CAAC;cACb7C,MAAM,EAAE8B,QAAQ;cAChBpR,IAAI,EAAE,KAAK;cACXyP,IAAI,EAAE2B,QAAQ;cACd1B,MAAM,EAAEJ,MAAM;cACdS,aAAa,EAAEvB;YACjB,CAAC,CAAC;;YAEF;YACA2D,cAAc,CAAC;cACb5D,QAAQ,EAAE,IAAI;cACdvO,IAAI,EAAE,QAAQ;cACdyP,IAAI,EAAE2B,QAAQ;cACdrB,aAAa,EAAEvB;YACjB,CAAC,CAAC;;YAEF;YACA2D,cAAc,CAAC;cACb7C,MAAM,EAAE8B,QAAQ;cAChBpR,IAAI,EAAE,MAAM;cACZyP,IAAI,EAAE2B,QAAQ;cACd1B,MAAM,EAAEJ,MAAM;cACdS,aAAa,EAAEvB;YACjB,CAAC,CAAC;YACF2D,cAAc,CAAC;cACb5D,QAAQ,EAAE,IAAI;cACdvO,IAAI,EAAE,MAAM;cACZyP,IAAI,EAAE2B,QAAQ;cACdrB,aAAa,EAAEvB;YACjB,CAAC,CAAC;UACJ;UACAwB,WAAW,IAAIA,WAAW,CAAC8P,IAAI,CAAC,CAAC;QACnC,CAAC,MAAM;UACL,IAAIlQ,QAAQ,KAAKD,QAAQ,EAAE;YACzB,IAAIC,QAAQ,IAAI,CAAC,EAAE;cACjB;cACAuC,cAAc,CAAC;gBACb5D,QAAQ,EAAE,IAAI;gBACdvO,IAAI,EAAE,QAAQ;gBACdyP,IAAI,EAAE2B,QAAQ;gBACdrB,aAAa,EAAEvB;cACjB,CAAC,CAAC;cACF2D,cAAc,CAAC;gBACb5D,QAAQ,EAAE,IAAI;gBACdvO,IAAI,EAAE,MAAM;gBACZyP,IAAI,EAAE2B,QAAQ;gBACdrB,aAAa,EAAEvB;cACjB,CAAC,CAAC;YACJ;UACF;QACF;QACA,IAAI7H,QAAQ,CAACgL,MAAM,EAAE;UACnB;UACA,IAAI/B,QAAQ,IAAI,IAAI,IAAIA,QAAQ,KAAK,CAAC,CAAC,EAAE;YACvCA,QAAQ,GAAGD,QAAQ;YACnBG,iBAAiB,GAAGD,iBAAiB;UACvC;UACAsC,cAAc,CAAC;YACb5D,QAAQ,EAAE,IAAI;YACdvO,IAAI,EAAE,KAAK;YACXyP,IAAI,EAAE2B,QAAQ;YACdrB,aAAa,EAAEvB;UACjB,CAAC,CAAC;;UAEF;UACA,IAAI,CAACsR,IAAI,CAAC,CAAC;QACb;MACF;IACF;IACA,IAAI,CAACzD,QAAQ,CAAC,CAAC;EACjB,CAAC;EACDA,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;IAC5BhO,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC;IAC5BiB,MAAM,GAAG6B,MAAM,GAAGC,QAAQ,GAAG5G,OAAO,GAAG6G,MAAM,GAAG7B,OAAO,GAAG8B,UAAU,GAAGC,WAAW,GAAGkB,MAAM,GAAGC,QAAQ,GAAGjB,KAAK,GAAG7B,QAAQ,GAAGE,iBAAiB,GAAGH,QAAQ,GAAGE,iBAAiB,GAAGkD,UAAU,GAAGC,aAAa,GAAGhD,WAAW,GAAGqC,WAAW,GAAG1L,QAAQ,CAACE,OAAO,GAAGF,QAAQ,CAACC,KAAK,GAAGD,QAAQ,CAACW,KAAK,GAAGX,QAAQ,CAACgL,MAAM,GAAG,IAAI;IACnT4B,iBAAiB,CAACjW,OAAO,CAAC,UAAU+D,EAAE,EAAE;MACtCA,EAAE,CAAC0e,OAAO,GAAG,IAAI;IACnB,CAAC,CAAC;IACFxM,iBAAiB,CAACnW,MAAM,GAAGuV,MAAM,GAAGC,MAAM,GAAG,CAAC;EAChD,CAAC;EACDoN,WAAW,EAAE,SAASA,WAAWA,CAAE,UAAUxR,GAAG,EAAE;IAChD,QAAQA,GAAG,CAACuL,IAAI;MACd,KAAK,MAAM;MACX,KAAK,SAAS;QACZ,IAAI,CAACkB,OAAO,CAACzM,GAAG,CAAC;QACjB;MACF,KAAK,WAAW;MAChB,KAAK,UAAU;QACb,IAAI2C,MAAM,EAAE;UACV,IAAI,CAACsG,WAAW,CAACjJ,GAAG,CAAC;UACrByR,eAAe,CAACzR,GAAG,CAAC;QACtB;QACA;MACF,KAAK,aAAa;QAChBA,GAAG,CAACyI,cAAc,CAAC,CAAC;QACpB;IACJ;EACF,CAAC;EACD;AACF;AACA;AACA;EACEiJ,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;IAC1B,IAAIC,KAAK,GAAG,EAAE;MACZ9e,EAAE;MACFoF,QAAQ,GAAG,IAAI,CAACpF,EAAE,CAACoF,QAAQ;MAC3BvJ,CAAC,GAAG,CAAC;MACL2C,CAAC,GAAG4G,QAAQ,CAACrJ,MAAM;MACnBkJ,OAAO,GAAG,IAAI,CAACA,OAAO;IACxB,OAAOpJ,CAAC,GAAG2C,CAAC,EAAE3C,CAAC,EAAE,EAAE;MACjBmE,EAAE,GAAGoF,QAAQ,CAACvJ,CAAC,CAAC;MAChB,IAAIoF,OAAO,CAACjB,EAAE,EAAEiF,OAAO,CAACQ,SAAS,EAAE,IAAI,CAACzF,EAAE,EAAE,KAAK,CAAC,EAAE;QAClD8e,KAAK,CAACrjB,IAAI,CAACuE,EAAE,CAAC+e,YAAY,CAAC9Z,OAAO,CAACyS,UAAU,CAAC,IAAIsH,WAAW,CAAChf,EAAE,CAAC,CAAC;MACpE;IACF;IACA,OAAO8e,KAAK;EACd,CAAC;EACD;AACF;AACA;AACA;EACEvI,IAAI,EAAE,SAASA,IAAIA,CAACuI,KAAK,EAAEG,YAAY,EAAE;IACvC,IAAIC,KAAK,GAAG,CAAC,CAAC;MACZjR,MAAM,GAAG,IAAI,CAACjO,EAAE;IAClB,IAAI,CAAC6e,OAAO,CAAC,CAAC,CAAC5iB,OAAO,CAAC,UAAUkjB,EAAE,EAAEtjB,CAAC,EAAE;MACtC,IAAImE,EAAE,GAAGiO,MAAM,CAAC7I,QAAQ,CAACvJ,CAAC,CAAC;MAC3B,IAAIoF,OAAO,CAACjB,EAAE,EAAE,IAAI,CAACiF,OAAO,CAACQ,SAAS,EAAEwI,MAAM,EAAE,KAAK,CAAC,EAAE;QACtDiR,KAAK,CAACC,EAAE,CAAC,GAAGnf,EAAE;MAChB;IACF,CAAC,EAAE,IAAI,CAAC;IACRif,YAAY,IAAI,IAAI,CAAC7U,qBAAqB,CAAC,CAAC;IAC5C0U,KAAK,CAAC7iB,OAAO,CAAC,UAAUkjB,EAAE,EAAE;MAC1B,IAAID,KAAK,CAACC,EAAE,CAAC,EAAE;QACblR,MAAM,CAACuQ,WAAW,CAACU,KAAK,CAACC,EAAE,CAAC,CAAC;QAC7BlR,MAAM,CAAC0N,WAAW,CAACuD,KAAK,CAACC,EAAE,CAAC,CAAC;MAC/B;IACF,CAAC,CAAC;IACFF,YAAY,IAAI,IAAI,CAACnU,UAAU,CAAC,CAAC;EACnC,CAAC;EACD;AACF;AACA;EACE2T,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;IACpB,IAAIhI,KAAK,GAAG,IAAI,CAACxR,OAAO,CAACwR,KAAK;IAC9BA,KAAK,IAAIA,KAAK,CAAC2I,GAAG,IAAI3I,KAAK,CAAC2I,GAAG,CAAC,IAAI,CAAC;EACvC,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACEne,OAAO,EAAE,SAASoe,SAASA,CAACrf,EAAE,EAAEO,QAAQ,EAAE;IACxC,OAAOU,OAAO,CAACjB,EAAE,EAAEO,QAAQ,IAAI,IAAI,CAAC0E,OAAO,CAACQ,SAAS,EAAE,IAAI,CAACzF,EAAE,EAAE,KAAK,CAAC;EACxE,CAAC;EACD;AACF;AACA;AACA;AACA;AACA;EACE4M,MAAM,EAAE,SAASA,MAAMA,CAACjO,IAAI,EAAE9B,KAAK,EAAE;IACnC,IAAIoI,OAAO,GAAG,IAAI,CAACA,OAAO;IAC1B,IAAIpI,KAAK,KAAK,KAAK,CAAC,EAAE;MACpB,OAAOoI,OAAO,CAACtG,IAAI,CAAC;IACtB,CAAC,MAAM;MACL,IAAIkP,aAAa,GAAGpB,aAAa,CAACiB,YAAY,CAAC,IAAI,EAAE/O,IAAI,EAAE9B,KAAK,CAAC;MACjE,IAAI,OAAOgR,aAAa,KAAK,WAAW,EAAE;QACxC5I,OAAO,CAACtG,IAAI,CAAC,GAAGkP,aAAa;MAC/B,CAAC,MAAM;QACL5I,OAAO,CAACtG,IAAI,CAAC,GAAG9B,KAAK;MACvB;MACA,IAAI8B,IAAI,KAAK,OAAO,EAAE;QACpBqW,aAAa,CAAC/P,OAAO,CAAC;MACxB;IACF;EACF,CAAC;EACD;AACF;AACA;EACEqa,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;IAC1BtS,WAAW,CAAC,SAAS,EAAE,IAAI,CAAC;IAC5B,IAAIhN,EAAE,GAAG,IAAI,CAACA,EAAE;IAChBA,EAAE,CAAC8J,OAAO,CAAC,GAAG,IAAI;IAClB1J,GAAG,CAACJ,EAAE,EAAE,WAAW,EAAE,IAAI,CAACsY,WAAW,CAAC;IACtClY,GAAG,CAACJ,EAAE,EAAE,YAAY,EAAE,IAAI,CAACsY,WAAW,CAAC;IACvClY,GAAG,CAACJ,EAAE,EAAE,aAAa,EAAE,IAAI,CAACsY,WAAW,CAAC;IACxC,IAAI,IAAI,CAACD,eAAe,EAAE;MACxBjY,GAAG,CAACJ,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC;MACzBI,GAAG,CAACJ,EAAE,EAAE,WAAW,EAAE,IAAI,CAAC;IAC5B;IACA;IACA/B,KAAK,CAACrB,SAAS,CAACX,OAAO,CAACkB,IAAI,CAAC6C,EAAE,CAACuf,gBAAgB,CAAC,aAAa,CAAC,EAAE,UAAUvf,EAAE,EAAE;MAC7EA,EAAE,CAAC4b,eAAe,CAAC,WAAW,CAAC;IACjC,CAAC,CAAC;IACF,IAAI,CAAChC,OAAO,CAAC,CAAC;IACd,IAAI,CAACC,yBAAyB,CAAC,CAAC;IAChC1I,SAAS,CAACtG,MAAM,CAACsG,SAAS,CAAC5T,OAAO,CAAC,IAAI,CAACyC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC/C,IAAI,CAACA,EAAE,GAAGA,EAAE,GAAG,IAAI;EACrB,CAAC;EACD6b,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;IAChC,IAAI,CAAC3L,WAAW,EAAE;MAChBlD,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC;MAC9B,IAAI1H,QAAQ,CAAC8H,aAAa,EAAE;MAC5B1L,GAAG,CAACyM,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC;MAC/B,IAAI,IAAI,CAAClJ,OAAO,CAAC6R,iBAAiB,IAAI3I,OAAO,CAACnN,UAAU,EAAE;QACxDmN,OAAO,CAACnN,UAAU,CAACwd,WAAW,CAACrQ,OAAO,CAAC;MACzC;MACA+B,WAAW,GAAG,IAAI;IACpB;EACF,CAAC;EACDgN,UAAU,EAAE,SAASA,UAAUA,CAACvO,WAAW,EAAE;IAC3C,IAAIA,WAAW,CAACa,WAAW,KAAK,OAAO,EAAE;MACvC,IAAI,CAACqM,UAAU,CAAC,CAAC;MACjB;IACF;IACA,IAAI3L,WAAW,EAAE;MACflD,WAAW,CAAC,WAAW,EAAE,IAAI,CAAC;MAC9B,IAAI1H,QAAQ,CAAC8H,aAAa,EAAE;;MAE5B;MACA,IAAI0C,MAAM,CAAC9O,UAAU,IAAIiN,MAAM,IAAI,CAAC,IAAI,CAAChJ,OAAO,CAACmQ,KAAK,CAACO,WAAW,EAAE;QAClE1H,MAAM,CAAC8N,YAAY,CAAC5N,OAAO,EAAE2B,MAAM,CAAC;MACtC,CAAC,MAAM,IAAIE,MAAM,EAAE;QACjB/B,MAAM,CAAC8N,YAAY,CAAC5N,OAAO,EAAE6B,MAAM,CAAC;MACtC,CAAC,MAAM;QACL/B,MAAM,CAAC0N,WAAW,CAACxN,OAAO,CAAC;MAC7B;MACA,IAAI,IAAI,CAAClJ,OAAO,CAACmQ,KAAK,CAACO,WAAW,EAAE;QAClC,IAAI,CAACnK,OAAO,CAACsE,MAAM,EAAE3B,OAAO,CAAC;MAC/B;MACAzM,GAAG,CAACyM,OAAO,EAAE,SAAS,EAAE,EAAE,CAAC;MAC3B+B,WAAW,GAAG,KAAK;IACrB;EACF;AACF,CAAC;AACD,SAAS0O,eAAeA,CAAE,UAAUzR,GAAG,EAAE;EACvC,IAAIA,GAAG,CAACmK,YAAY,EAAE;IACpBnK,GAAG,CAACmK,YAAY,CAACkI,UAAU,GAAG,MAAM;EACtC;EACArS,GAAG,CAAC+B,UAAU,IAAI/B,GAAG,CAACyI,cAAc,CAAC,CAAC;AACxC;AACA,SAASmH,OAAOA,CAAC1O,MAAM,EAAED,IAAI,EAAE0B,MAAM,EAAEkE,QAAQ,EAAE9F,QAAQ,EAAE+F,UAAU,EAAEvF,aAAa,EAAE+Q,eAAe,EAAE;EACrG,IAAItS,GAAG;IACLD,QAAQ,GAAGmB,MAAM,CAACvE,OAAO,CAAC;IAC1B4V,QAAQ,GAAGxS,QAAQ,CAACjI,OAAO,CAAC4X,MAAM;IAClC8C,MAAM;EACR;EACA,IAAIxgB,MAAM,CAAC6P,WAAW,IAAI,CAAC1P,UAAU,IAAI,CAACC,IAAI,EAAE;IAC9C4N,GAAG,GAAG,IAAI6B,WAAW,CAAC,MAAM,EAAE;MAC5BC,OAAO,EAAE,IAAI;MACbC,UAAU,EAAE;IACd,CAAC,CAAC;EACJ,CAAC,MAAM;IACL/B,GAAG,GAAGrM,QAAQ,CAACqO,WAAW,CAAC,OAAO,CAAC;IACnChC,GAAG,CAACiC,SAAS,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC;EACnC;EACAjC,GAAG,CAACkC,EAAE,GAAGjB,IAAI;EACbjB,GAAG,CAAC9O,IAAI,GAAGgQ,MAAM;EACjBlB,GAAG,CAAC3H,OAAO,GAAGsK,MAAM;EACpB3C,GAAG,CAACyS,WAAW,GAAG5L,QAAQ;EAC1B7G,GAAG,CAAC0S,OAAO,GAAG3R,QAAQ,IAAIE,IAAI;EAC9BjB,GAAG,CAAC2S,WAAW,GAAG7L,UAAU,IAAIhR,OAAO,CAACmL,IAAI,CAAC;EAC7CjB,GAAG,CAACsS,eAAe,GAAGA,eAAe;EACrCtS,GAAG,CAACuB,aAAa,GAAGA,aAAa;EACjCL,MAAM,CAACN,aAAa,CAACZ,GAAG,CAAC;EACzB,IAAIuS,QAAQ,EAAE;IACZC,MAAM,GAAGD,QAAQ,CAACviB,IAAI,CAAC+P,QAAQ,EAAEC,GAAG,EAAEuB,aAAa,CAAC;EACtD;EACA,OAAOiR,MAAM;AACf;AACA,SAAS5F,iBAAiBA,CAAC/Z,EAAE,EAAE;EAC7BA,EAAE,CAACyF,SAAS,GAAG,KAAK;AACtB;AACA,SAAS0Y,SAASA,CAAA,EAAG;EACnBlM,OAAO,GAAG,KAAK;AACjB;AACA,SAASqL,aAAaA,CAACnQ,GAAG,EAAE+G,QAAQ,EAAEhH,QAAQ,EAAE;EAC9C,IAAI6S,WAAW,GAAG9c,OAAO,CAAC8B,QAAQ,CAACmI,QAAQ,CAAClN,EAAE,EAAE,CAAC,EAAEkN,QAAQ,CAACjI,OAAO,EAAE,IAAI,CAAC,CAAC;EAC3E,IAAI+a,mBAAmB,GAAG9W,iCAAiC,CAACgE,QAAQ,CAAClN,EAAE,EAAEkN,QAAQ,CAACjI,OAAO,EAAEkE,OAAO,CAAC;EACnG,IAAI8W,MAAM,GAAG,EAAE;EACf,OAAO/L,QAAQ,GAAG/G,GAAG,CAAC+I,OAAO,GAAG8J,mBAAmB,CAACvc,IAAI,GAAGwc,MAAM,IAAI9S,GAAG,CAACgJ,OAAO,GAAG4J,WAAW,CAACvc,GAAG,IAAI2J,GAAG,CAAC+I,OAAO,GAAG6J,WAAW,CAACpc,KAAK,GAAGwJ,GAAG,CAACgJ,OAAO,GAAG6J,mBAAmB,CAACxc,GAAG,GAAGyc,MAAM,IAAI9S,GAAG,CAACgJ,OAAO,GAAG4J,WAAW,CAACrc,MAAM,IAAIyJ,GAAG,CAAC+I,OAAO,GAAG6J,WAAW,CAACtc,IAAI;AAC9P;AACA,SAAS4Z,YAAYA,CAAClQ,GAAG,EAAE+G,QAAQ,EAAEhH,QAAQ,EAAE;EAC7C,IAAIgT,UAAU,GAAGjd,OAAO,CAACyC,SAAS,CAACwH,QAAQ,CAAClN,EAAE,EAAEkN,QAAQ,CAACjI,OAAO,CAACQ,SAAS,CAAC,CAAC;EAC5E,IAAIua,mBAAmB,GAAG9W,iCAAiC,CAACgE,QAAQ,CAAClN,EAAE,EAAEkN,QAAQ,CAACjI,OAAO,EAAEkE,OAAO,CAAC;EACnG,IAAI8W,MAAM,GAAG,EAAE;EACf,OAAO/L,QAAQ,GAAG/G,GAAG,CAAC+I,OAAO,GAAG8J,mBAAmB,CAACrc,KAAK,GAAGsc,MAAM,IAAI9S,GAAG,CAACgJ,OAAO,GAAG+J,UAAU,CAACxc,MAAM,IAAIyJ,GAAG,CAAC+I,OAAO,GAAGgK,UAAU,CAACzc,IAAI,GAAG0J,GAAG,CAACgJ,OAAO,GAAG6J,mBAAmB,CAACtc,MAAM,GAAGuc,MAAM,IAAI9S,GAAG,CAAC+I,OAAO,GAAGgK,UAAU,CAACvc,KAAK,IAAIwJ,GAAG,CAACgJ,OAAO,GAAG+J,UAAU,CAAC1c,GAAG;AAC9P;AACA,SAASsa,iBAAiBA,CAAC3Q,GAAG,EAAEvR,MAAM,EAAEqY,UAAU,EAAEC,QAAQ,EAAEyC,aAAa,EAAEE,qBAAqB,EAAED,UAAU,EAAEuJ,YAAY,EAAE;EAC5H,IAAIC,WAAW,GAAGlM,QAAQ,GAAG/G,GAAG,CAACgJ,OAAO,GAAGhJ,GAAG,CAAC+I,OAAO;IACpDmK,YAAY,GAAGnM,QAAQ,GAAGD,UAAU,CAACrQ,MAAM,GAAGqQ,UAAU,CAACpQ,KAAK;IAC9Dyc,QAAQ,GAAGpM,QAAQ,GAAGD,UAAU,CAACzQ,GAAG,GAAGyQ,UAAU,CAACxQ,IAAI;IACtD8c,QAAQ,GAAGrM,QAAQ,GAAGD,UAAU,CAACvQ,MAAM,GAAGuQ,UAAU,CAACtQ,KAAK;IAC1D6c,MAAM,GAAG,KAAK;EAChB,IAAI,CAAC5J,UAAU,EAAE;IACf;IACA,IAAIuJ,YAAY,IAAIrO,kBAAkB,GAAGuO,YAAY,GAAG1J,aAAa,EAAE;MACrE;MACA;MACA,IAAI,CAAC/E,qBAAqB,KAAKD,aAAa,KAAK,CAAC,GAAGyO,WAAW,GAAGE,QAAQ,GAAGD,YAAY,GAAGxJ,qBAAqB,GAAG,CAAC,GAAGuJ,WAAW,GAAGG,QAAQ,GAAGF,YAAY,GAAGxJ,qBAAqB,GAAG,CAAC,CAAC,EAAE;QAC3L;QACAjF,qBAAqB,GAAG,IAAI;MAC9B;MACA,IAAI,CAACA,qBAAqB,EAAE;QAC1B;QACA,IAAID,aAAa,KAAK,CAAC,GAAGyO,WAAW,GAAGE,QAAQ,GAAGxO,kBAAkB,CAAC;QAAA,EACpEsO,WAAW,GAAGG,QAAQ,GAAGzO,kBAAkB,EAAE;UAC7C,OAAO,CAACH,aAAa;QACvB;MACF,CAAC,MAAM;QACL6O,MAAM,GAAG,IAAI;MACf;IACF,CAAC,MAAM;MACL;MACA,IAAIJ,WAAW,GAAGE,QAAQ,GAAGD,YAAY,IAAI,CAAC,GAAG1J,aAAa,CAAC,GAAG,CAAC,IAAIyJ,WAAW,GAAGG,QAAQ,GAAGF,YAAY,IAAI,CAAC,GAAG1J,aAAa,CAAC,GAAG,CAAC,EAAE;QACtI,OAAO8J,mBAAmB,CAAC7kB,MAAM,CAAC;MACpC;IACF;EACF;EACA4kB,MAAM,GAAGA,MAAM,IAAI5J,UAAU;EAC7B,IAAI4J,MAAM,EAAE;IACV;IACA,IAAIJ,WAAW,GAAGE,QAAQ,GAAGD,YAAY,GAAGxJ,qBAAqB,GAAG,CAAC,IAAIuJ,WAAW,GAAGG,QAAQ,GAAGF,YAAY,GAAGxJ,qBAAqB,GAAG,CAAC,EAAE;MAC1I,OAAOuJ,WAAW,GAAGE,QAAQ,GAAGD,YAAY,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAC3D;EACF;EACA,OAAO,CAAC;AACV;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,mBAAmBA,CAAC7kB,MAAM,EAAE;EACnC,IAAIkK,KAAK,CAACgK,MAAM,CAAC,GAAGhK,KAAK,CAAClK,MAAM,CAAC,EAAE;IACjC,OAAO,CAAC;EACV,CAAC,MAAM;IACL,OAAO,CAAC,CAAC;EACX;AACF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASojB,WAAWA,CAAChf,EAAE,EAAE;EACvB,IAAI0gB,GAAG,GAAG1gB,EAAE,CAAC2C,OAAO,GAAG3C,EAAE,CAACwB,SAAS,GAAGxB,EAAE,CAACuH,GAAG,GAAGvH,EAAE,CAAC2gB,IAAI,GAAG3gB,EAAE,CAACuX,WAAW;IACrE1b,CAAC,GAAG6kB,GAAG,CAAC3kB,MAAM;IACd6kB,GAAG,GAAG,CAAC;EACT,OAAO/kB,CAAC,EAAE,EAAE;IACV+kB,GAAG,IAAIF,GAAG,CAACG,UAAU,CAAChlB,CAAC,CAAC;EAC1B;EACA,OAAO+kB,GAAG,CAACniB,QAAQ,CAAC,EAAE,CAAC;AACzB;AACA,SAASwa,sBAAsBA,CAAC6H,IAAI,EAAE;EACpC5O,iBAAiB,CAACnW,MAAM,GAAG,CAAC;EAC5B,IAAIglB,MAAM,GAAGD,IAAI,CAACje,oBAAoB,CAAC,OAAO,CAAC;EAC/C,IAAIme,GAAG,GAAGD,MAAM,CAAChlB,MAAM;EACvB,OAAOilB,GAAG,EAAE,EAAE;IACZ,IAAIhhB,EAAE,GAAG+gB,MAAM,CAACC,GAAG,CAAC;IACpBhhB,EAAE,CAAC0e,OAAO,IAAIxM,iBAAiB,CAACzW,IAAI,CAACuE,EAAE,CAAC;EAC1C;AACF;AACA,SAASwa,SAASA,CAACta,EAAE,EAAE;EACrB,OAAOiI,UAAU,CAACjI,EAAE,EAAE,CAAC,CAAC;AAC1B;AACA,SAASqe,eAAeA,CAACY,EAAE,EAAE;EAC3B,OAAO9W,YAAY,CAAC8W,EAAE,CAAC;AACzB;;AAEA;AACA,IAAIhN,cAAc,EAAE;EAClBpS,EAAE,CAACe,QAAQ,EAAE,WAAW,EAAE,UAAUqM,GAAG,EAAE;IACvC,IAAI,CAAC7H,QAAQ,CAACgL,MAAM,IAAIW,mBAAmB,KAAK9D,GAAG,CAAC+B,UAAU,EAAE;MAC9D/B,GAAG,CAACyI,cAAc,CAAC,CAAC;IACtB;EACF,CAAC,CAAC;AACJ;;AAEA;AACAtQ,QAAQ,CAAC2b,KAAK,GAAG;EACflhB,EAAE,EAAEA,EAAE;EACNK,GAAG,EAAEA,GAAG;EACRsB,GAAG,EAAEA,GAAG;EACRgB,IAAI,EAAEA,IAAI;EACVwe,EAAE,EAAE,SAASA,EAAEA,CAAClhB,EAAE,EAAEO,QAAQ,EAAE;IAC5B,OAAO,CAAC,CAACU,OAAO,CAACjB,EAAE,EAAEO,QAAQ,EAAEP,EAAE,EAAE,KAAK,CAAC;EAC3C,CAAC;EACDqH,MAAM,EAAEA,MAAM;EACdS,QAAQ,EAAEA,QAAQ;EAClB7G,OAAO,EAAEA,OAAO;EAChBI,WAAW,EAAEA,WAAW;EACxB4E,KAAK,EAAEA,KAAK;EACZH,KAAK,EAAEA,KAAK;EACZqb,QAAQ,EAAE3G,SAAS;EACnB4G,cAAc,EAAE7C,eAAe;EAC/B8C,eAAe,EAAE1O,gBAAgB;EACjC5N,QAAQ,EAAEA,QAAQ;EAClB+E,OAAO,EAAEA;AACX,CAAC;;AAED;AACA;AACA;AACA;AACA;AACAxE,QAAQ,CAACiT,GAAG,GAAG,UAAU+I,OAAO,EAAE;EAChC,OAAOA,OAAO,CAACxX,OAAO,CAAC;AACzB,CAAC;;AAED;AACA;AACA;AACA;AACAxE,QAAQ,CAACoH,KAAK,GAAG,YAAY;EAC3B,KAAK,IAAI6U,IAAI,GAAGzlB,SAAS,CAACC,MAAM,EAAEuQ,OAAO,GAAG,IAAIrO,KAAK,CAACsjB,IAAI,CAAC,EAAEC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGD,IAAI,EAAEC,IAAI,EAAE,EAAE;IAC1FlV,OAAO,CAACkV,IAAI,CAAC,GAAG1lB,SAAS,CAAC0lB,IAAI,CAAC;EACjC;EACA,IAAIlV,OAAO,CAAC,CAAC,CAAC,CAAC3P,WAAW,KAAKsB,KAAK,EAAEqO,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC;EAC1DA,OAAO,CAACrQ,OAAO,CAAC,UAAU0Q,MAAM,EAAE;IAChC,IAAI,CAACA,MAAM,CAAC/P,SAAS,IAAI,CAAC+P,MAAM,CAAC/P,SAAS,CAACD,WAAW,EAAE;MACtD,MAAM,+DAA+D,CAACoQ,MAAM,CAAC,CAAC,CAAC,CAACtO,QAAQ,CAACtB,IAAI,CAACwP,MAAM,CAAC,CAAC;IACxG;IACA,IAAIA,MAAM,CAACsU,KAAK,EAAE3b,QAAQ,CAAC2b,KAAK,GAAGtlB,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE2J,QAAQ,CAAC2b,KAAK,CAAC,EAAEtU,MAAM,CAACsU,KAAK,CAAC;IACnGxU,aAAa,CAACC,KAAK,CAACC,MAAM,CAAC;EAC7B,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACArH,QAAQ,CAACmc,MAAM,GAAG,UAAUzhB,EAAE,EAAEiF,OAAO,EAAE;EACvC,OAAO,IAAIK,QAAQ,CAACtF,EAAE,EAAEiF,OAAO,CAAC;AAClC,CAAC;;AAED;AACAK,QAAQ,CAACtG,OAAO,GAAGA,OAAO;AAE1B,IAAI0iB,WAAW,GAAG,EAAE;EAClBC,QAAQ;EACRC,YAAY;EACZC,SAAS,GAAG,KAAK;EACjBC,eAAe;EACfC,eAAe;EACfC,UAAU;EACVC,0BAA0B;AAC5B,SAASC,gBAAgBA,CAAA,EAAG;EAC1B,SAASC,UAAUA,CAAA,EAAG;IACpB,IAAI,CAAC5V,QAAQ,GAAG;MACd6V,MAAM,EAAE,IAAI;MACZC,uBAAuB,EAAE,KAAK;MAC9BC,iBAAiB,EAAE,EAAE;MACrBC,WAAW,EAAE,EAAE;MACfC,YAAY,EAAE;IAChB,CAAC;;IAED;IACA,KAAK,IAAItiB,EAAE,IAAI,IAAI,EAAE;MACnB,IAAIA,EAAE,CAAC4O,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,OAAO,IAAI,CAAC5O,EAAE,CAAC,KAAK,UAAU,EAAE;QAC1D,IAAI,CAACA,EAAE,CAAC,GAAG,IAAI,CAACA,EAAE,CAAC,CAAC2P,IAAI,CAAC,IAAI,CAAC;MAChC;IACF;EACF;EACAsS,UAAU,CAACvlB,SAAS,GAAG;IACrBuT,WAAW,EAAE,SAASA,WAAWA,CAACnC,IAAI,EAAE;MACtC,IAAIU,aAAa,GAAGV,IAAI,CAACU,aAAa;MACtC,IAAI,IAAI,CAACxB,QAAQ,CAACmL,eAAe,EAAE;QACjCtY,EAAE,CAACe,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC2hB,iBAAiB,CAAC;MAClD,CAAC,MAAM;QACL,IAAI,IAAI,CAACxd,OAAO,CAACmT,cAAc,EAAE;UAC/BrY,EAAE,CAACe,QAAQ,EAAE,aAAa,EAAE,IAAI,CAAC4hB,yBAAyB,CAAC;QAC7D,CAAC,MAAM,IAAIhU,aAAa,CAACsH,OAAO,EAAE;UAChCjW,EAAE,CAACe,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC4hB,yBAAyB,CAAC;QAC3D,CAAC,MAAM;UACL3iB,EAAE,CAACe,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC4hB,yBAAyB,CAAC;QAC3D;MACF;IACF,CAAC;IACDC,iBAAiB,EAAE,SAASA,iBAAiBA,CAACC,KAAK,EAAE;MACnD,IAAIlU,aAAa,GAAGkU,KAAK,CAAClU,aAAa;MACvC;MACA,IAAI,CAAC,IAAI,CAACzJ,OAAO,CAAC4d,cAAc,IAAI,CAACnU,aAAa,CAACT,MAAM,EAAE;QACzD,IAAI,CAACwU,iBAAiB,CAAC/T,aAAa,CAAC;MACvC;IACF,CAAC;IACDoU,IAAI,EAAE,SAASA,IAAIA,CAAA,EAAG;MACpB,IAAI,IAAI,CAAC5V,QAAQ,CAACmL,eAAe,EAAE;QACjCjY,GAAG,CAACU,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC2hB,iBAAiB,CAAC;MACnD,CAAC,MAAM;QACLriB,GAAG,CAACU,QAAQ,EAAE,aAAa,EAAE,IAAI,CAAC4hB,yBAAyB,CAAC;QAC5DtiB,GAAG,CAACU,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC4hB,yBAAyB,CAAC;QAC1DtiB,GAAG,CAACU,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC4hB,yBAAyB,CAAC;MAC5D;MACAK,+BAA+B,CAAC,CAAC;MACjCC,gBAAgB,CAAC,CAAC;MAClB5a,cAAc,CAAC,CAAC;IAClB,CAAC;IACD6a,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;MAC1BjB,UAAU,GAAGJ,YAAY,GAAGD,QAAQ,GAAGE,SAAS,GAAGI,0BAA0B,GAAGH,eAAe,GAAGC,eAAe,GAAG,IAAI;MACxHL,WAAW,CAAC3lB,MAAM,GAAG,CAAC;IACxB,CAAC;IACD2mB,yBAAyB,EAAE,SAASA,yBAAyBA,CAACvV,GAAG,EAAE;MACjE,IAAI,CAACsV,iBAAiB,CAACtV,GAAG,EAAE,IAAI,CAAC;IACnC,CAAC;IACDsV,iBAAiB,EAAE,SAASA,iBAAiBA,CAACtV,GAAG,EAAE2N,QAAQ,EAAE;MAC3D,IAAI5S,KAAK,GAAG,IAAI;MAChB,IAAIK,CAAC,GAAG,CAAC4E,GAAG,CAAC6I,OAAO,GAAG7I,GAAG,CAAC6I,OAAO,CAAC,CAAC,CAAC,GAAG7I,GAAG,EAAE+I,OAAO;QAClD1N,CAAC,GAAG,CAAC2E,GAAG,CAAC6I,OAAO,GAAG7I,GAAG,CAAC6I,OAAO,CAAC,CAAC,CAAC,GAAG7I,GAAG,EAAEgJ,OAAO;QAChDxP,IAAI,GAAG7F,QAAQ,CAACoa,gBAAgB,CAAC3S,CAAC,EAAEC,CAAC,CAAC;MACxCwZ,UAAU,GAAG7U,GAAG;;MAEhB;MACA;MACA;MACA;MACA,IAAI2N,QAAQ,IAAI,IAAI,CAAC7V,OAAO,CAACod,uBAAuB,IAAI9iB,IAAI,IAAID,UAAU,IAAIG,MAAM,EAAE;QACpFyjB,UAAU,CAAC/V,GAAG,EAAE,IAAI,CAAClI,OAAO,EAAE0B,IAAI,EAAEmU,QAAQ,CAAC;;QAE7C;QACA,IAAIqI,cAAc,GAAGxe,0BAA0B,CAACgC,IAAI,EAAE,IAAI,CAAC;QAC3D,IAAIkb,SAAS,KAAK,CAACI,0BAA0B,IAAI1Z,CAAC,KAAKuZ,eAAe,IAAItZ,CAAC,KAAKuZ,eAAe,CAAC,EAAE;UAChGE,0BAA0B,IAAIc,+BAA+B,CAAC,CAAC;UAC/D;UACAd,0BAA0B,GAAGhG,WAAW,CAAC,YAAY;YACnD,IAAImH,OAAO,GAAGze,0BAA0B,CAAC7D,QAAQ,CAACoa,gBAAgB,CAAC3S,CAAC,EAAEC,CAAC,CAAC,EAAE,IAAI,CAAC;YAC/E,IAAI4a,OAAO,KAAKD,cAAc,EAAE;cAC9BA,cAAc,GAAGC,OAAO;cACxBJ,gBAAgB,CAAC,CAAC;YACpB;YACAE,UAAU,CAAC/V,GAAG,EAAEjF,KAAK,CAACjD,OAAO,EAAEme,OAAO,EAAEtI,QAAQ,CAAC;UACnD,CAAC,EAAE,EAAE,CAAC;UACNgH,eAAe,GAAGvZ,CAAC;UACnBwZ,eAAe,GAAGvZ,CAAC;QACrB;MACF,CAAC,MAAM;QACL;QACA,IAAI,CAAC,IAAI,CAACvD,OAAO,CAACud,YAAY,IAAI7d,0BAA0B,CAACgC,IAAI,EAAE,IAAI,CAAC,KAAK7D,yBAAyB,CAAC,CAAC,EAAE;UACxGkgB,gBAAgB,CAAC,CAAC;UAClB;QACF;QACAE,UAAU,CAAC/V,GAAG,EAAE,IAAI,CAAClI,OAAO,EAAEN,0BAA0B,CAACgC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC;MAC/E;IACF;EACF,CAAC;EACD,OAAO3J,QAAQ,CAACmlB,UAAU,EAAE;IAC1BrV,UAAU,EAAE,QAAQ;IACpBN,mBAAmB,EAAE;EACvB,CAAC,CAAC;AACJ;AACA,SAASwW,gBAAgBA,CAAA,EAAG;EAC1BtB,WAAW,CAACzlB,OAAO,CAAC,UAAUinB,UAAU,EAAE;IACxC5E,aAAa,CAAC4E,UAAU,CAACG,GAAG,CAAC;EAC/B,CAAC,CAAC;EACF3B,WAAW,GAAG,EAAE;AAClB;AACA,SAASqB,+BAA+BA,CAAA,EAAG;EACzCzE,aAAa,CAAC2D,0BAA0B,CAAC;AAC3C;AACA,IAAIiB,UAAU,GAAGpb,QAAQ,CAAC,UAAUqF,GAAG,EAAElI,OAAO,EAAEgJ,MAAM,EAAEqV,UAAU,EAAE;EACpE;EACA,IAAI,CAACre,OAAO,CAACmd,MAAM,EAAE;EACrB,IAAI7Z,CAAC,GAAG,CAAC4E,GAAG,CAAC6I,OAAO,GAAG7I,GAAG,CAAC6I,OAAO,CAAC,CAAC,CAAC,GAAG7I,GAAG,EAAE+I,OAAO;IAClD1N,CAAC,GAAG,CAAC2E,GAAG,CAAC6I,OAAO,GAAG7I,GAAG,CAAC6I,OAAO,CAAC,CAAC,CAAC,GAAG7I,GAAG,EAAEgJ,OAAO;IAChDoN,IAAI,GAAGte,OAAO,CAACqd,iBAAiB;IAChCkB,KAAK,GAAGve,OAAO,CAACsd,WAAW;IAC3Blc,WAAW,GAAGvD,yBAAyB,CAAC,CAAC;EAC3C,IAAI2gB,kBAAkB,GAAG,KAAK;IAC5BC,cAAc;;EAEhB;EACA,IAAI9B,YAAY,KAAK3T,MAAM,EAAE;IAC3B2T,YAAY,GAAG3T,MAAM;IACrB+U,gBAAgB,CAAC,CAAC;IAClBrB,QAAQ,GAAG1c,OAAO,CAACmd,MAAM;IACzBsB,cAAc,GAAGze,OAAO,CAAC0e,QAAQ;IACjC,IAAIhC,QAAQ,KAAK,IAAI,EAAE;MACrBA,QAAQ,GAAGhd,0BAA0B,CAACsJ,MAAM,EAAE,IAAI,CAAC;IACrD;EACF;EACA,IAAI2V,SAAS,GAAG,CAAC;EACjB,IAAIC,aAAa,GAAGlC,QAAQ;EAC5B,GAAG;IACD,IAAI3hB,EAAE,GAAG6jB,aAAa;MACpB7a,IAAI,GAAG/F,OAAO,CAACjD,EAAE,CAAC;MAClBwD,GAAG,GAAGwF,IAAI,CAACxF,GAAG;MACdE,MAAM,GAAGsF,IAAI,CAACtF,MAAM;MACpBD,IAAI,GAAGuF,IAAI,CAACvF,IAAI;MAChBE,KAAK,GAAGqF,IAAI,CAACrF,KAAK;MAClBE,KAAK,GAAGmF,IAAI,CAACnF,KAAK;MAClBD,MAAM,GAAGoF,IAAI,CAACpF,MAAM;MACpBkgB,UAAU,GAAG,KAAK,CAAC;MACnBC,UAAU,GAAG,KAAK,CAAC;MACnBjd,WAAW,GAAG9G,EAAE,CAAC8G,WAAW;MAC5BE,YAAY,GAAGhH,EAAE,CAACgH,YAAY;MAC9B4L,KAAK,GAAGlR,GAAG,CAAC1B,EAAE,CAAC;MACfgkB,UAAU,GAAGhkB,EAAE,CAACsG,UAAU;MAC1B2d,UAAU,GAAGjkB,EAAE,CAACuG,SAAS;IAC3B,IAAIvG,EAAE,KAAKqG,WAAW,EAAE;MACtByd,UAAU,GAAGjgB,KAAK,GAAGiD,WAAW,KAAK8L,KAAK,CAAC1L,SAAS,KAAK,MAAM,IAAI0L,KAAK,CAAC1L,SAAS,KAAK,QAAQ,IAAI0L,KAAK,CAAC1L,SAAS,KAAK,SAAS,CAAC;MACjI6c,UAAU,GAAGngB,MAAM,GAAGoD,YAAY,KAAK4L,KAAK,CAACzL,SAAS,KAAK,MAAM,IAAIyL,KAAK,CAACzL,SAAS,KAAK,QAAQ,IAAIyL,KAAK,CAACzL,SAAS,KAAK,SAAS,CAAC;IACrI,CAAC,MAAM;MACL2c,UAAU,GAAGjgB,KAAK,GAAGiD,WAAW,KAAK8L,KAAK,CAAC1L,SAAS,KAAK,MAAM,IAAI0L,KAAK,CAAC1L,SAAS,KAAK,QAAQ,CAAC;MAChG6c,UAAU,GAAGngB,MAAM,GAAGoD,YAAY,KAAK4L,KAAK,CAACzL,SAAS,KAAK,MAAM,IAAIyL,KAAK,CAACzL,SAAS,KAAK,QAAQ,CAAC;IACpG;IACA,IAAI+c,EAAE,GAAGJ,UAAU,IAAI,CAACnc,IAAI,CAACwS,GAAG,CAACxW,KAAK,GAAG4E,CAAC,CAAC,IAAIgb,IAAI,IAAIS,UAAU,GAAGngB,KAAK,GAAGiD,WAAW,KAAKa,IAAI,CAACwS,GAAG,CAAC1W,IAAI,GAAG8E,CAAC,CAAC,IAAIgb,IAAI,IAAI,CAAC,CAACS,UAAU,CAAC;IACvI,IAAIG,EAAE,GAAGJ,UAAU,IAAI,CAACpc,IAAI,CAACwS,GAAG,CAACzW,MAAM,GAAG8E,CAAC,CAAC,IAAI+a,IAAI,IAAIU,UAAU,GAAGrgB,MAAM,GAAGoD,YAAY,KAAKW,IAAI,CAACwS,GAAG,CAAC3W,GAAG,GAAGgF,CAAC,CAAC,IAAI+a,IAAI,IAAI,CAAC,CAACU,UAAU,CAAC;IACzI,IAAI,CAACvC,WAAW,CAACkC,SAAS,CAAC,EAAE;MAC3B,KAAK,IAAI/nB,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI+nB,SAAS,EAAE/nB,CAAC,EAAE,EAAE;QACnC,IAAI,CAAC6lB,WAAW,CAAC7lB,CAAC,CAAC,EAAE;UACnB6lB,WAAW,CAAC7lB,CAAC,CAAC,GAAG,CAAC,CAAC;QACrB;MACF;IACF;IACA,IAAI6lB,WAAW,CAACkC,SAAS,CAAC,CAACM,EAAE,IAAIA,EAAE,IAAIxC,WAAW,CAACkC,SAAS,CAAC,CAACO,EAAE,IAAIA,EAAE,IAAIzC,WAAW,CAACkC,SAAS,CAAC,CAAC5jB,EAAE,KAAKA,EAAE,EAAE;MAC1G0hB,WAAW,CAACkC,SAAS,CAAC,CAAC5jB,EAAE,GAAGA,EAAE;MAC9B0hB,WAAW,CAACkC,SAAS,CAAC,CAACM,EAAE,GAAGA,EAAE;MAC9BxC,WAAW,CAACkC,SAAS,CAAC,CAACO,EAAE,GAAGA,EAAE;MAC9B7F,aAAa,CAACoD,WAAW,CAACkC,SAAS,CAAC,CAACP,GAAG,CAAC;MACzC,IAAIa,EAAE,IAAI,CAAC,IAAIC,EAAE,IAAI,CAAC,EAAE;QACtBV,kBAAkB,GAAG,IAAI;QACzB;QACA/B,WAAW,CAACkC,SAAS,CAAC,CAACP,GAAG,GAAGpH,WAAW,CAAC,YAAY;UACnD;UACA,IAAIqH,UAAU,IAAI,IAAI,CAACc,KAAK,KAAK,CAAC,EAAE;YAClC9e,QAAQ,CAACgL,MAAM,CAAC+J,YAAY,CAAC2H,UAAU,CAAC,CAAC,CAAC;UAC5C;UACA,IAAIqC,aAAa,GAAG3C,WAAW,CAAC,IAAI,CAAC0C,KAAK,CAAC,CAACD,EAAE,GAAGzC,WAAW,CAAC,IAAI,CAAC0C,KAAK,CAAC,CAACD,EAAE,GAAGX,KAAK,GAAG,CAAC;UACvF,IAAIc,aAAa,GAAG5C,WAAW,CAAC,IAAI,CAAC0C,KAAK,CAAC,CAACF,EAAE,GAAGxC,WAAW,CAAC,IAAI,CAAC0C,KAAK,CAAC,CAACF,EAAE,GAAGV,KAAK,GAAG,CAAC;UACvF,IAAI,OAAOE,cAAc,KAAK,UAAU,EAAE;YACxC,IAAIA,cAAc,CAACvmB,IAAI,CAACmI,QAAQ,CAACE,OAAO,CAACxE,UAAU,CAAC8I,OAAO,CAAC,EAAEwa,aAAa,EAAED,aAAa,EAAElX,GAAG,EAAE6U,UAAU,EAAEN,WAAW,CAAC,IAAI,CAAC0C,KAAK,CAAC,CAACpkB,EAAE,CAAC,KAAK,UAAU,EAAE;cACvJ;YACF;UACF;UACAsI,QAAQ,CAACoZ,WAAW,CAAC,IAAI,CAAC0C,KAAK,CAAC,CAACpkB,EAAE,EAAEskB,aAAa,EAAED,aAAa,CAAC;QACpE,CAAC,CAACxU,IAAI,CAAC;UACLuU,KAAK,EAAER;QACT,CAAC,CAAC,EAAE,EAAE,CAAC;MACT;IACF;IACAA,SAAS,EAAE;EACb,CAAC,QAAQ3e,OAAO,CAACud,YAAY,IAAIqB,aAAa,KAAKxd,WAAW,KAAKwd,aAAa,GAAGlf,0BAA0B,CAACkf,aAAa,EAAE,KAAK,CAAC,CAAC;EACpIhC,SAAS,GAAG4B,kBAAkB,CAAC,CAAC;AAClC,CAAC,EAAE,EAAE,CAAC;AAEN,IAAIX,IAAI,GAAG,SAASA,IAAIA,CAAC9U,IAAI,EAAE;EAC7B,IAAIU,aAAa,GAAGV,IAAI,CAACU,aAAa;IACpCC,WAAW,GAAGX,IAAI,CAACW,WAAW;IAC9BmB,MAAM,GAAG9B,IAAI,CAAC8B,MAAM;IACpBO,cAAc,GAAGrC,IAAI,CAACqC,cAAc;IACpCQ,qBAAqB,GAAG7C,IAAI,CAAC6C,qBAAqB;IAClDN,kBAAkB,GAAGvC,IAAI,CAACuC,kBAAkB;IAC5CE,oBAAoB,GAAGzC,IAAI,CAACyC,oBAAoB;EAClD,IAAI,CAAC/B,aAAa,EAAE;EACpB,IAAI6V,UAAU,GAAG5V,WAAW,IAAI0B,cAAc;EAC9CE,kBAAkB,CAAC,CAAC;EACpB,IAAIoI,KAAK,GAAGjK,aAAa,CAAC8V,cAAc,IAAI9V,aAAa,CAAC8V,cAAc,CAACzoB,MAAM,GAAG2S,aAAa,CAAC8V,cAAc,CAAC,CAAC,CAAC,GAAG9V,aAAa;EACjI,IAAI9S,MAAM,GAAGkF,QAAQ,CAACoa,gBAAgB,CAACvC,KAAK,CAACzC,OAAO,EAAEyC,KAAK,CAACxC,OAAO,CAAC;EACpE1F,oBAAoB,CAAC,CAAC;EACtB,IAAI8T,UAAU,IAAI,CAACA,UAAU,CAACvkB,EAAE,CAACwY,QAAQ,CAAC5c,MAAM,CAAC,EAAE;IACjDiV,qBAAqB,CAAC,OAAO,CAAC;IAC9B,IAAI,CAAC4T,OAAO,CAAC;MACX3U,MAAM,EAAEA,MAAM;MACdnB,WAAW,EAAEA;IACf,CAAC,CAAC;EACJ;AACF,CAAC;AACD,SAAS+V,MAAMA,CAAA,EAAG,CAAC;AACnBA,MAAM,CAAC9nB,SAAS,GAAG;EACjB+nB,UAAU,EAAE,IAAI;EAChBC,SAAS,EAAE,SAASA,SAASA,CAAChC,KAAK,EAAE;IACnC,IAAIpU,iBAAiB,GAAGoU,KAAK,CAACpU,iBAAiB;IAC/C,IAAI,CAACmW,UAAU,GAAGnW,iBAAiB;EACrC,CAAC;EACDiW,OAAO,EAAE,SAASA,OAAOA,CAACI,KAAK,EAAE;IAC/B,IAAI/U,MAAM,GAAG+U,KAAK,CAAC/U,MAAM;MACvBnB,WAAW,GAAGkW,KAAK,CAAClW,WAAW;IACjC,IAAI,CAACzB,QAAQ,CAAC9C,qBAAqB,CAAC,CAAC;IACrC,IAAIuE,WAAW,EAAE;MACfA,WAAW,CAACvE,qBAAqB,CAAC,CAAC;IACrC;IACA,IAAIqP,WAAW,GAAG1U,QAAQ,CAAC,IAAI,CAACmI,QAAQ,CAAClN,EAAE,EAAE,IAAI,CAAC2kB,UAAU,EAAE,IAAI,CAAC1f,OAAO,CAAC;IAC3E,IAAIwU,WAAW,EAAE;MACf,IAAI,CAACvM,QAAQ,CAAClN,EAAE,CAAC+b,YAAY,CAACjM,MAAM,EAAE2J,WAAW,CAAC;IACpD,CAAC,MAAM;MACL,IAAI,CAACvM,QAAQ,CAAClN,EAAE,CAAC2b,WAAW,CAAC7L,MAAM,CAAC;IACtC;IACA,IAAI,CAAC5C,QAAQ,CAACpC,UAAU,CAAC,CAAC;IAC1B,IAAI6D,WAAW,EAAE;MACfA,WAAW,CAAC7D,UAAU,CAAC,CAAC;IAC1B;EACF,CAAC;EACDgY,IAAI,EAAEA;AACR,CAAC;AACD9lB,QAAQ,CAAC0nB,MAAM,EAAE;EACf5X,UAAU,EAAE;AACd,CAAC,CAAC;AACF,SAASgY,MAAMA,CAAA,EAAG,CAAC;AACnBA,MAAM,CAACloB,SAAS,GAAG;EACjB6nB,OAAO,EAAE,SAASA,OAAOA,CAACM,KAAK,EAAE;IAC/B,IAAIjV,MAAM,GAAGiV,KAAK,CAACjV,MAAM;MACvBnB,WAAW,GAAGoW,KAAK,CAACpW,WAAW;IACjC,IAAIqW,cAAc,GAAGrW,WAAW,IAAI,IAAI,CAACzB,QAAQ;IACjD8X,cAAc,CAAC5a,qBAAqB,CAAC,CAAC;IACtC0F,MAAM,CAAC9O,UAAU,IAAI8O,MAAM,CAAC9O,UAAU,CAACwd,WAAW,CAAC1O,MAAM,CAAC;IAC1DkV,cAAc,CAACla,UAAU,CAAC,CAAC;EAC7B,CAAC;EACDgY,IAAI,EAAEA;AACR,CAAC;AACD9lB,QAAQ,CAAC8nB,MAAM,EAAE;EACfhY,UAAU,EAAE;AACd,CAAC,CAAC;AAEF,IAAImY,UAAU;AACd,SAASC,UAAUA,CAAA,EAAG;EACpB,SAASC,IAAIA,CAAA,EAAG;IACd,IAAI,CAAC5Y,QAAQ,GAAG;MACd6Y,SAAS,EAAE;IACb,CAAC;EACH;EACAD,IAAI,CAACvoB,SAAS,GAAG;IACfgoB,SAAS,EAAE,SAASA,SAASA,CAAC5W,IAAI,EAAE;MAClC,IAAI8B,MAAM,GAAG9B,IAAI,CAAC8B,MAAM;MACxBmV,UAAU,GAAGnV,MAAM;IACrB,CAAC;IACDuV,aAAa,EAAE,SAASA,aAAaA,CAACzC,KAAK,EAAE;MAC3C,IAAIhG,SAAS,GAAGgG,KAAK,CAAChG,SAAS;QAC7BhhB,MAAM,GAAGgnB,KAAK,CAAChnB,MAAM;QACrBihB,MAAM,GAAG+F,KAAK,CAAC/F,MAAM;QACrBxM,cAAc,GAAGuS,KAAK,CAACvS,cAAc;QACrC2M,OAAO,GAAG4F,KAAK,CAAC5F,OAAO;QACvB3P,MAAM,GAAGuV,KAAK,CAACvV,MAAM;MACvB,IAAI,CAACgD,cAAc,CAACpL,OAAO,CAACqgB,IAAI,EAAE;MAClC,IAAItlB,EAAE,GAAG,IAAI,CAACkN,QAAQ,CAAClN,EAAE;QACvBiF,OAAO,GAAG,IAAI,CAACA,OAAO;MACxB,IAAIrJ,MAAM,IAAIA,MAAM,KAAKoE,EAAE,EAAE;QAC3B,IAAIulB,UAAU,GAAGN,UAAU;QAC3B,IAAIpI,MAAM,CAACjhB,MAAM,CAAC,KAAK,KAAK,EAAE;UAC5ByF,WAAW,CAACzF,MAAM,EAAEqJ,OAAO,CAACmgB,SAAS,EAAE,IAAI,CAAC;UAC5CH,UAAU,GAAGrpB,MAAM;QACrB,CAAC,MAAM;UACLqpB,UAAU,GAAG,IAAI;QACnB;QACA,IAAIM,UAAU,IAAIA,UAAU,KAAKN,UAAU,EAAE;UAC3C5jB,WAAW,CAACkkB,UAAU,EAAEtgB,OAAO,CAACmgB,SAAS,EAAE,KAAK,CAAC;QACnD;MACF;MACApI,OAAO,CAAC,CAAC;MACTJ,SAAS,CAAC,IAAI,CAAC;MACfvP,MAAM,CAAC,CAAC;IACV,CAAC;IACDyV,IAAI,EAAE,SAASA,IAAIA,CAAC+B,KAAK,EAAE;MACzB,IAAIxU,cAAc,GAAGwU,KAAK,CAACxU,cAAc;QACvC1B,WAAW,GAAGkW,KAAK,CAAClW,WAAW;QAC/BmB,MAAM,GAAG+U,KAAK,CAAC/U,MAAM;MACvB,IAAIyU,UAAU,GAAG5V,WAAW,IAAI,IAAI,CAACzB,QAAQ;MAC7C,IAAIjI,OAAO,GAAG,IAAI,CAACA,OAAO;MAC1BggB,UAAU,IAAI5jB,WAAW,CAAC4jB,UAAU,EAAEhgB,OAAO,CAACmgB,SAAS,EAAE,KAAK,CAAC;MAC/D,IAAIH,UAAU,KAAKhgB,OAAO,CAACqgB,IAAI,IAAI3W,WAAW,IAAIA,WAAW,CAAC1J,OAAO,CAACqgB,IAAI,CAAC,EAAE;QAC3E,IAAIxV,MAAM,KAAKmV,UAAU,EAAE;UACzBV,UAAU,CAACna,qBAAqB,CAAC,CAAC;UAClC,IAAIma,UAAU,KAAKlU,cAAc,EAAEA,cAAc,CAACjG,qBAAqB,CAAC,CAAC;UACzEob,SAAS,CAAC1V,MAAM,EAAEmV,UAAU,CAAC;UAC7BV,UAAU,CAACzZ,UAAU,CAAC,CAAC;UACvB,IAAIyZ,UAAU,KAAKlU,cAAc,EAAEA,cAAc,CAACvF,UAAU,CAAC,CAAC;QAChE;MACF;IACF,CAAC;IACDmY,OAAO,EAAE,SAASA,OAAOA,CAAA,EAAG;MAC1BgC,UAAU,GAAG,IAAI;IACnB;EACF,CAAC;EACD,OAAOjoB,QAAQ,CAACmoB,IAAI,EAAE;IACpBrY,UAAU,EAAE,MAAM;IAClBc,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG;MAC1C,OAAO;QACL6X,QAAQ,EAAER;MACZ,CAAC;IACH;EACF,CAAC,CAAC;AACJ;AACA,SAASO,SAASA,CAACE,EAAE,EAAEC,EAAE,EAAE;EACzB,IAAIC,EAAE,GAAGF,EAAE,CAAC1kB,UAAU;IACpB6kB,EAAE,GAAGF,EAAE,CAAC3kB,UAAU;IAClB8kB,EAAE;IACFC,EAAE;EACJ,IAAI,CAACH,EAAE,IAAI,CAACC,EAAE,IAAID,EAAE,CAACI,WAAW,CAACL,EAAE,CAAC,IAAIE,EAAE,CAACG,WAAW,CAACN,EAAE,CAAC,EAAE;EAC5DI,EAAE,GAAGhgB,KAAK,CAAC4f,EAAE,CAAC;EACdK,EAAE,GAAGjgB,KAAK,CAAC6f,EAAE,CAAC;EACd,IAAIC,EAAE,CAACI,WAAW,CAACH,EAAE,CAAC,IAAIC,EAAE,GAAGC,EAAE,EAAE;IACjCA,EAAE,EAAE;EACN;EACAH,EAAE,CAAC7J,YAAY,CAAC4J,EAAE,EAAEC,EAAE,CAACxgB,QAAQ,CAAC0gB,EAAE,CAAC,CAAC;EACpCD,EAAE,CAAC9J,YAAY,CAAC2J,EAAE,EAAEG,EAAE,CAACzgB,QAAQ,CAAC2gB,EAAE,CAAC,CAAC;AACtC;AAEA,IAAIE,iBAAiB,GAAG,EAAE;EACxBC,eAAe,GAAG,EAAE;EACpBC,mBAAmB;EACnB;EACAC,iBAAiB;EACjBC,cAAc,GAAG,KAAK;EACtB;EACAC,OAAO,GAAG,KAAK;EACf;EACAnW,WAAW,GAAG,KAAK;EACnBoW,QAAQ;EACRC,cAAc;EACdC,YAAY;AACd,SAASC,eAAeA,CAAA,EAAG;EACzB,SAASC,SAASA,CAACzZ,QAAQ,EAAE;IAC3B;IACA,KAAK,IAAIhN,EAAE,IAAI,IAAI,EAAE;MACnB,IAAIA,EAAE,CAAC4O,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI,OAAO,IAAI,CAAC5O,EAAE,CAAC,KAAK,UAAU,EAAE;QAC1D,IAAI,CAACA,EAAE,CAAC,GAAG,IAAI,CAACA,EAAE,CAAC,CAAC2P,IAAI,CAAC,IAAI,CAAC;MAChC;IACF;IACA,IAAI,CAAC3C,QAAQ,CAACjI,OAAO,CAAC2hB,qBAAqB,EAAE;MAC3C,IAAI1Z,QAAQ,CAACjI,OAAO,CAACmT,cAAc,EAAE;QACnCrY,EAAE,CAACe,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC+lB,kBAAkB,CAAC;MACpD,CAAC,MAAM;QACL9mB,EAAE,CAACe,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC+lB,kBAAkB,CAAC;QAChD9mB,EAAE,CAACe,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC+lB,kBAAkB,CAAC;MACnD;IACF;IACA9mB,EAAE,CAACe,QAAQ,EAAE,SAAS,EAAE,IAAI,CAACgmB,aAAa,CAAC;IAC3C/mB,EAAE,CAACe,QAAQ,EAAE,OAAO,EAAE,IAAI,CAACimB,WAAW,CAAC;IACvC,IAAI,CAACxa,QAAQ,GAAG;MACdya,aAAa,EAAE,mBAAmB;MAClCC,YAAY,EAAE,IAAI;MAClBL,qBAAqB,EAAE,KAAK;MAC5BvP,OAAO,EAAE,SAASA,OAAOA,CAACC,YAAY,EAAExH,MAAM,EAAE;QAC9C,IAAIF,IAAI,GAAG,EAAE;QACb,IAAIqW,iBAAiB,CAAClqB,MAAM,IAAIqqB,iBAAiB,KAAKlZ,QAAQ,EAAE;UAC9D+Y,iBAAiB,CAAChqB,OAAO,CAAC,UAAUirB,gBAAgB,EAAErrB,CAAC,EAAE;YACvD+T,IAAI,IAAI,CAAC,CAAC/T,CAAC,GAAG,EAAE,GAAG,IAAI,IAAIqrB,gBAAgB,CAAC3P,WAAW;UACzD,CAAC,CAAC;QACJ,CAAC,MAAM;UACL3H,IAAI,GAAGE,MAAM,CAACyH,WAAW;QAC3B;QACAD,YAAY,CAACD,OAAO,CAAC,MAAM,EAAEzH,IAAI,CAAC;MACpC;IACF,CAAC;EACH;EACA+W,SAAS,CAAC/pB,SAAS,GAAG;IACpBuqB,gBAAgB,EAAE,KAAK;IACvBC,WAAW,EAAE,KAAK;IAClBC,gBAAgB,EAAE,SAASA,gBAAgBA,CAACrZ,IAAI,EAAE;MAChD,IAAIxI,OAAO,GAAGwI,IAAI,CAAC8B,MAAM;MACzByW,QAAQ,GAAG/gB,OAAO;IACpB,CAAC;IACD8hB,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;MAChC,IAAI,CAACF,WAAW,GAAG,CAACnB,iBAAiB,CAAC1oB,OAAO,CAACgpB,QAAQ,CAAC;IACzD,CAAC;IACDgB,UAAU,EAAE,SAASA,UAAUA,CAAC3E,KAAK,EAAE;MACrC,IAAI1V,QAAQ,GAAG0V,KAAK,CAAC1V,QAAQ;QAC3BG,MAAM,GAAGuV,KAAK,CAACvV,MAAM;MACvB,IAAI,CAAC,IAAI,CAAC+Z,WAAW,EAAE;MACvB,KAAK,IAAIvrB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGoqB,iBAAiB,CAAClqB,MAAM,EAAEF,CAAC,EAAE,EAAE;QACjDqqB,eAAe,CAACzqB,IAAI,CAACwK,KAAK,CAACggB,iBAAiB,CAACpqB,CAAC,CAAC,CAAC,CAAC;QACjDqqB,eAAe,CAACrqB,CAAC,CAAC,CAAC2rB,aAAa,GAAGvB,iBAAiB,CAACpqB,CAAC,CAAC,CAAC2rB,aAAa;QACrEtB,eAAe,CAACrqB,CAAC,CAAC,CAAC4J,SAAS,GAAG,KAAK;QACpCygB,eAAe,CAACrqB,CAAC,CAAC,CAACgG,KAAK,CAAC,aAAa,CAAC,GAAG,EAAE;QAC5CR,WAAW,CAAC6kB,eAAe,CAACrqB,CAAC,CAAC,EAAE,IAAI,CAACoJ,OAAO,CAAC+hB,aAAa,EAAE,KAAK,CAAC;QAClEf,iBAAiB,CAACpqB,CAAC,CAAC,KAAK0qB,QAAQ,IAAIllB,WAAW,CAAC6kB,eAAe,CAACrqB,CAAC,CAAC,EAAE,IAAI,CAACoJ,OAAO,CAACgS,WAAW,EAAE,KAAK,CAAC;MACvG;MACA/J,QAAQ,CAAC2O,UAAU,CAAC,CAAC;MACrBxO,MAAM,CAAC,CAAC;IACV,CAAC;IACDpH,KAAK,EAAE,SAASA,KAAKA,CAAC4e,KAAK,EAAE;MAC3B,IAAI3X,QAAQ,GAAG2X,KAAK,CAAC3X,QAAQ;QAC3Be,MAAM,GAAG4W,KAAK,CAAC5W,MAAM;QACrB4C,qBAAqB,GAAGgU,KAAK,CAAChU,qBAAqB;QACnDxD,MAAM,GAAGwX,KAAK,CAACxX,MAAM;MACvB,IAAI,CAAC,IAAI,CAAC+Z,WAAW,EAAE;MACvB,IAAI,CAAC,IAAI,CAACniB,OAAO,CAAC6R,iBAAiB,EAAE;QACnC,IAAImP,iBAAiB,CAAClqB,MAAM,IAAIqqB,iBAAiB,KAAKlZ,QAAQ,EAAE;UAC9Dua,qBAAqB,CAAC,IAAI,EAAExZ,MAAM,CAAC;UACnC4C,qBAAqB,CAAC,OAAO,CAAC;UAC9BxD,MAAM,CAAC,CAAC;QACV;MACF;IACF,CAAC;IACDqa,SAAS,EAAE,SAASA,SAASA,CAAC3C,KAAK,EAAE;MACnC,IAAInU,aAAa,GAAGmU,KAAK,CAACnU,aAAa;QACrC3C,MAAM,GAAG8W,KAAK,CAAC9W,MAAM;QACrBZ,MAAM,GAAG0X,KAAK,CAAC1X,MAAM;MACvB,IAAI,CAAC,IAAI,CAAC+Z,WAAW,EAAE;MACvBK,qBAAqB,CAAC,KAAK,EAAExZ,MAAM,CAAC;MACpCiY,eAAe,CAACjqB,OAAO,CAAC,UAAUgK,KAAK,EAAE;QACvCvE,GAAG,CAACuE,KAAK,EAAE,SAAS,EAAE,EAAE,CAAC;MAC3B,CAAC,CAAC;MACF2K,aAAa,CAAC,CAAC;MACf6V,YAAY,GAAG,KAAK;MACpBpZ,MAAM,CAAC,CAAC;IACV,CAAC;IACDsa,SAAS,EAAE,SAASA,SAASA,CAACC,KAAK,EAAE;MACnC,IAAI1f,KAAK,GAAG,IAAI;MAChB,IAAIgF,QAAQ,GAAG0a,KAAK,CAAC1a,QAAQ;QAC3ByD,cAAc,GAAGiX,KAAK,CAACjX,cAAc;QACrCtD,MAAM,GAAGua,KAAK,CAACva,MAAM;MACvB,IAAI,CAAC,IAAI,CAAC+Z,WAAW,EAAE;MACvBlB,eAAe,CAACjqB,OAAO,CAAC,UAAUgK,KAAK,EAAE;QACvCvE,GAAG,CAACuE,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC;QAC7B,IAAIiC,KAAK,CAACjD,OAAO,CAAC6R,iBAAiB,IAAI7Q,KAAK,CAACjF,UAAU,EAAE;UACvDiF,KAAK,CAACjF,UAAU,CAACwd,WAAW,CAACvY,KAAK,CAAC;QACrC;MACF,CAAC,CAAC;MACF0K,cAAc,CAAC,CAAC;MAChB8V,YAAY,GAAG,IAAI;MACnBpZ,MAAM,CAAC,CAAC;IACV,CAAC;IACDwa,eAAe,EAAE,SAASA,eAAeA,CAACC,KAAK,EAAE;MAC/C,IAAI5a,QAAQ,GAAG4a,KAAK,CAAC5a,QAAQ;MAC7B,IAAI,CAAC,IAAI,CAACka,WAAW,IAAIhB,iBAAiB,EAAE;QAC1CA,iBAAiB,CAAC2B,SAAS,CAAClB,kBAAkB,CAAC,CAAC;MAClD;MACAZ,iBAAiB,CAAChqB,OAAO,CAAC,UAAUirB,gBAAgB,EAAE;QACpDA,gBAAgB,CAACM,aAAa,GAAG1hB,KAAK,CAACohB,gBAAgB,CAAC;MAC1D,CAAC,CAAC;;MAEF;MACAjB,iBAAiB,GAAGA,iBAAiB,CAAC1P,IAAI,CAAC,UAAUnS,CAAC,EAAEoX,CAAC,EAAE;QACzD,OAAOpX,CAAC,CAACojB,aAAa,GAAGhM,CAAC,CAACgM,aAAa;MAC1C,CAAC,CAAC;MACFrX,WAAW,GAAG,IAAI;IACpB,CAAC;IACDA,WAAW,EAAE,SAASA,WAAWA,CAAC6X,KAAK,EAAE;MACvC,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAI/a,QAAQ,GAAG8a,KAAK,CAAC9a,QAAQ;MAC7B,IAAI,CAAC,IAAI,CAACka,WAAW,EAAE;MACvB,IAAI,IAAI,CAACniB,OAAO,CAACsR,IAAI,EAAE;QACrB;QACA;QACA;QACA;QACA;QACA;;QAEArJ,QAAQ,CAAC9C,qBAAqB,CAAC,CAAC;QAChC,IAAI,IAAI,CAACnF,OAAO,CAACoF,SAAS,EAAE;UAC1B4b,iBAAiB,CAAChqB,OAAO,CAAC,UAAUirB,gBAAgB,EAAE;YACpD,IAAIA,gBAAgB,KAAKX,QAAQ,EAAE;YACnC7kB,GAAG,CAACwlB,gBAAgB,EAAE,UAAU,EAAE,UAAU,CAAC;UAC/C,CAAC,CAAC;UACF,IAAIlT,QAAQ,GAAG/Q,OAAO,CAACsjB,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;UACnDN,iBAAiB,CAAChqB,OAAO,CAAC,UAAUirB,gBAAgB,EAAE;YACpD,IAAIA,gBAAgB,KAAKX,QAAQ,EAAE;YACnCxd,OAAO,CAACme,gBAAgB,EAAElT,QAAQ,CAAC;UACrC,CAAC,CAAC;UACFsS,OAAO,GAAG,IAAI;UACdD,cAAc,GAAG,IAAI;QACvB;MACF;MACAnZ,QAAQ,CAACpC,UAAU,CAAC,YAAY;QAC9Bwb,OAAO,GAAG,KAAK;QACfD,cAAc,GAAG,KAAK;QACtB,IAAI4B,MAAM,CAAChjB,OAAO,CAACoF,SAAS,EAAE;UAC5B4b,iBAAiB,CAAChqB,OAAO,CAAC,UAAUirB,gBAAgB,EAAE;YACpDje,SAAS,CAACie,gBAAgB,CAAC;UAC7B,CAAC,CAAC;QACJ;;QAEA;QACA,IAAIe,MAAM,CAAChjB,OAAO,CAACsR,IAAI,EAAE;UACvB2R,uBAAuB,CAAC,CAAC;QAC3B;MACF,CAAC,CAAC;IACJ,CAAC;IACDC,QAAQ,EAAE,SAASA,QAAQA,CAACC,KAAK,EAAE;MACjC,IAAIxsB,MAAM,GAAGwsB,KAAK,CAACxsB,MAAM;QACvBghB,SAAS,GAAGwL,KAAK,CAACxL,SAAS;QAC3BvP,MAAM,GAAG+a,KAAK,CAAC/a,MAAM;MACvB,IAAIiZ,OAAO,IAAI,CAACL,iBAAiB,CAAC1oB,OAAO,CAAC3B,MAAM,CAAC,EAAE;QACjDghB,SAAS,CAAC,KAAK,CAAC;QAChBvP,MAAM,CAAC,CAAC;MACV;IACF,CAAC;IACD+O,MAAM,EAAE,SAASA,MAAMA,CAACiM,KAAK,EAAE;MAC7B,IAAI9L,YAAY,GAAG8L,KAAK,CAAC9L,YAAY;QACnCtO,MAAM,GAAGoa,KAAK,CAACpa,MAAM;QACrBf,QAAQ,GAAGmb,KAAK,CAACnb,QAAQ;QACzB8G,QAAQ,GAAGqU,KAAK,CAACrU,QAAQ;MAC3B,IAAIiS,iBAAiB,CAAClqB,MAAM,GAAG,CAAC,EAAE;QAChC;QACAkqB,iBAAiB,CAAChqB,OAAO,CAAC,UAAUirB,gBAAgB,EAAE;UACpDha,QAAQ,CAACvC,iBAAiB,CAAC;YACzB/O,MAAM,EAAEsrB,gBAAgB;YACxBle,IAAI,EAAEsd,OAAO,GAAGrjB,OAAO,CAACikB,gBAAgB,CAAC,GAAGlT;UAC9C,CAAC,CAAC;UACF/K,SAAS,CAACie,gBAAgB,CAAC;UAC3BA,gBAAgB,CAAC5c,QAAQ,GAAG0J,QAAQ;UACpCuI,YAAY,CAAC3R,oBAAoB,CAACsc,gBAAgB,CAAC;QACrD,CAAC,CAAC;QACFZ,OAAO,GAAG,KAAK;QACfgC,uBAAuB,CAAC,CAAC,IAAI,CAACrjB,OAAO,CAAC6R,iBAAiB,EAAE7I,MAAM,CAAC;MAClE;IACF,CAAC;IACD0U,iBAAiB,EAAE,SAASA,iBAAiBA,CAAC4F,MAAM,EAAE;MACpD,IAAIrb,QAAQ,GAAGqb,MAAM,CAACrb,QAAQ;QAC5BmP,OAAO,GAAGkM,MAAM,CAAClM,OAAO;QACxBY,SAAS,GAAGsL,MAAM,CAACtL,SAAS;QAC5B5M,cAAc,GAAGkY,MAAM,CAAClY,cAAc;QACtCN,QAAQ,GAAGwY,MAAM,CAACxY,QAAQ;QAC1BpB,WAAW,GAAG4Z,MAAM,CAAC5Z,WAAW;MAClC,IAAI1J,OAAO,GAAG,IAAI,CAACA,OAAO;MAC1B,IAAIgY,SAAS,EAAE;QACb;QACA,IAAIZ,OAAO,EAAE;UACXhM,cAAc,CAACwL,UAAU,CAAC,CAAC;QAC7B;QACAwK,cAAc,GAAG,KAAK;QACtB;QACA,IAAIphB,OAAO,CAACoF,SAAS,IAAI4b,iBAAiB,CAAClqB,MAAM,GAAG,CAAC,KAAKuqB,OAAO,IAAI,CAACjK,OAAO,IAAI,CAAChM,cAAc,CAACpL,OAAO,CAACsR,IAAI,IAAI,CAAC5H,WAAW,CAAC,EAAE;UAC9H;UACA,IAAI6Z,gBAAgB,GAAGvlB,OAAO,CAACsjB,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;UAC3DN,iBAAiB,CAAChqB,OAAO,CAAC,UAAUirB,gBAAgB,EAAE;YACpD,IAAIA,gBAAgB,KAAKX,QAAQ,EAAE;YACnCxd,OAAO,CAACme,gBAAgB,EAAEsB,gBAAgB,CAAC;;YAE3C;YACA;YACAzY,QAAQ,CAAC4L,WAAW,CAACuL,gBAAgB,CAAC;UACxC,CAAC,CAAC;UACFZ,OAAO,GAAG,IAAI;QAChB;;QAEA;QACA,IAAI,CAACjK,OAAO,EAAE;UACZ;UACA,IAAI,CAACiK,OAAO,EAAE;YACZ4B,uBAAuB,CAAC,CAAC;UAC3B;UACA,IAAIjC,iBAAiB,CAAClqB,MAAM,GAAG,CAAC,EAAE;YAChC,IAAI0sB,kBAAkB,GAAGhC,YAAY;YACrCpW,cAAc,CAAC6M,UAAU,CAAChQ,QAAQ,CAAC;;YAEnC;YACA,IAAImD,cAAc,CAACpL,OAAO,CAACoF,SAAS,IAAI,CAACoc,YAAY,IAAIgC,kBAAkB,EAAE;cAC3EvC,eAAe,CAACjqB,OAAO,CAAC,UAAUgK,KAAK,EAAE;gBACvCoK,cAAc,CAAC1F,iBAAiB,CAAC;kBAC/B/O,MAAM,EAAEqK,KAAK;kBACb+C,IAAI,EAAEwd;gBACR,CAAC,CAAC;gBACFvgB,KAAK,CAACqE,QAAQ,GAAGkc,cAAc;gBAC/BvgB,KAAK,CAACsE,qBAAqB,GAAG,IAAI;cACpC,CAAC,CAAC;YACJ;UACF,CAAC,MAAM;YACL8F,cAAc,CAAC6M,UAAU,CAAChQ,QAAQ,CAAC;UACrC;QACF;MACF;IACF,CAAC;IACDwb,wBAAwB,EAAE,SAASA,wBAAwBA,CAACC,MAAM,EAAE;MAClE,IAAI3U,QAAQ,GAAG2U,MAAM,CAAC3U,QAAQ;QAC5BqI,OAAO,GAAGsM,MAAM,CAACtM,OAAO;QACxBhM,cAAc,GAAGsY,MAAM,CAACtY,cAAc;MACxC4V,iBAAiB,CAAChqB,OAAO,CAAC,UAAUirB,gBAAgB,EAAE;QACpDA,gBAAgB,CAAC3c,qBAAqB,GAAG,IAAI;MAC/C,CAAC,CAAC;MACF,IAAI8F,cAAc,CAACpL,OAAO,CAACoF,SAAS,IAAI,CAACgS,OAAO,IAAIhM,cAAc,CAAC0X,SAAS,CAACX,WAAW,EAAE;QACxFZ,cAAc,GAAGxpB,QAAQ,CAAC,CAAC,CAAC,EAAEgX,QAAQ,CAAC;QACvC,IAAI4U,UAAU,GAAG3mB,MAAM,CAACskB,QAAQ,EAAE,IAAI,CAAC;QACvCC,cAAc,CAAChjB,GAAG,IAAIolB,UAAU,CAACne,CAAC;QAClC+b,cAAc,CAAC/iB,IAAI,IAAImlB,UAAU,CAACle,CAAC;MACrC;IACF,CAAC;IACDme,yBAAyB,EAAE,SAASA,yBAAyBA,CAAA,EAAG;MAC9D,IAAIvC,OAAO,EAAE;QACXA,OAAO,GAAG,KAAK;QACf4B,uBAAuB,CAAC,CAAC;MAC3B;IACF,CAAC;IACDpF,IAAI,EAAE,SAASA,IAAIA,CAACgG,MAAM,EAAE;MAC1B,IAAI3b,GAAG,GAAG2b,MAAM,CAACpa,aAAa;QAC5BT,MAAM,GAAG6a,MAAM,CAAC7a,MAAM;QACtB8B,QAAQ,GAAG+Y,MAAM,CAAC/Y,QAAQ;QAC1B7C,QAAQ,GAAG4b,MAAM,CAAC5b,QAAQ;QAC1B2D,qBAAqB,GAAGiY,MAAM,CAACjY,qBAAqB;QACpDvC,QAAQ,GAAGwa,MAAM,CAACxa,QAAQ;QAC1BK,WAAW,GAAGma,MAAM,CAACna,WAAW;MAClC,IAAI4V,UAAU,GAAG5V,WAAW,IAAI,IAAI,CAACzB,QAAQ;MAC7C,IAAI,CAACC,GAAG,EAAE;MACV,IAAIlI,OAAO,GAAG,IAAI,CAACA,OAAO;QACxBG,QAAQ,GAAG2K,QAAQ,CAAC3K,QAAQ;;MAE9B;MACA,IAAI,CAAC+K,WAAW,EAAE;QAChB,IAAIlL,OAAO,CAACgiB,YAAY,IAAI,CAAC,IAAI,CAACE,gBAAgB,EAAE;UAClD,IAAI,CAACN,kBAAkB,CAAC,CAAC;QAC3B;QACAxlB,WAAW,CAACklB,QAAQ,EAAEthB,OAAO,CAAC+hB,aAAa,EAAE,CAAC,CAACf,iBAAiB,CAAC1oB,OAAO,CAACgpB,QAAQ,CAAC,CAAC;QACnF,IAAI,CAAC,CAACN,iBAAiB,CAAC1oB,OAAO,CAACgpB,QAAQ,CAAC,EAAE;UACzCN,iBAAiB,CAACxqB,IAAI,CAAC8qB,QAAQ,CAAC;UAChCxY,aAAa,CAAC;YACZb,QAAQ,EAAEA,QAAQ;YAClBe,MAAM,EAAEA,MAAM;YACdtP,IAAI,EAAE,QAAQ;YACduP,QAAQ,EAAEqY,QAAQ;YAClB7X,aAAa,EAAEvB;UACjB,CAAC,CAAC;;UAEF;UACA,IAAIA,GAAG,CAAC4b,QAAQ,IAAI5C,mBAAmB,IAAIjZ,QAAQ,CAAClN,EAAE,CAACwY,QAAQ,CAAC2N,mBAAmB,CAAC,EAAE;YACpF,IAAI6C,SAAS,GAAGljB,KAAK,CAACqgB,mBAAmB,CAAC;cACxC8C,YAAY,GAAGnjB,KAAK,CAACygB,QAAQ,CAAC;YAChC,IAAI,CAACyC,SAAS,IAAI,CAACC,YAAY,IAAID,SAAS,KAAKC,YAAY,EAAE;cAC7D;cACA;cACA,IAAIzqB,CAAC,EAAE3C,CAAC;cACR,IAAIotB,YAAY,GAAGD,SAAS,EAAE;gBAC5BntB,CAAC,GAAGmtB,SAAS;gBACbxqB,CAAC,GAAGyqB,YAAY;cAClB,CAAC,MAAM;gBACLptB,CAAC,GAAGotB,YAAY;gBAChBzqB,CAAC,GAAGwqB,SAAS,GAAG,CAAC;cACnB;cACA,OAAOntB,CAAC,GAAG2C,CAAC,EAAE3C,CAAC,EAAE,EAAE;gBACjB,IAAI,CAACoqB,iBAAiB,CAAC1oB,OAAO,CAAC6H,QAAQ,CAACvJ,CAAC,CAAC,CAAC,EAAE;gBAC7CwF,WAAW,CAAC+D,QAAQ,CAACvJ,CAAC,CAAC,EAAEoJ,OAAO,CAAC+hB,aAAa,EAAE,IAAI,CAAC;gBACrDf,iBAAiB,CAACxqB,IAAI,CAAC2J,QAAQ,CAACvJ,CAAC,CAAC,CAAC;gBACnCkS,aAAa,CAAC;kBACZb,QAAQ,EAAEA,QAAQ;kBAClBe,MAAM,EAAEA,MAAM;kBACdtP,IAAI,EAAE,QAAQ;kBACduP,QAAQ,EAAE9I,QAAQ,CAACvJ,CAAC,CAAC;kBACrB6S,aAAa,EAAEvB;gBACjB,CAAC,CAAC;cACJ;YACF;UACF,CAAC,MAAM;YACLgZ,mBAAmB,GAAGI,QAAQ;UAChC;UACAH,iBAAiB,GAAG7B,UAAU;QAChC,CAAC,MAAM;UACL0B,iBAAiB,CAACpb,MAAM,CAACob,iBAAiB,CAAC1oB,OAAO,CAACgpB,QAAQ,CAAC,EAAE,CAAC,CAAC;UAChEJ,mBAAmB,GAAG,IAAI;UAC1BpY,aAAa,CAAC;YACZb,QAAQ,EAAEA,QAAQ;YAClBe,MAAM,EAAEA,MAAM;YACdtP,IAAI,EAAE,UAAU;YAChBuP,QAAQ,EAAEqY,QAAQ;YAClB7X,aAAa,EAAEvB;UACjB,CAAC,CAAC;QACJ;MACF;;MAEA;MACA,IAAIgD,WAAW,IAAI,IAAI,CAACiX,WAAW,EAAE;QACnCd,OAAO,GAAG,KAAK;QACf;QACA,IAAI,CAACvW,QAAQ,CAACjG,OAAO,CAAC,CAAC7E,OAAO,CAACsR,IAAI,IAAIxG,QAAQ,KAAK9B,MAAM,KAAKgY,iBAAiB,CAAClqB,MAAM,GAAG,CAAC,EAAE;UAC3F,IAAIiY,QAAQ,GAAG/Q,OAAO,CAACsjB,QAAQ,CAAC;YAC9B2C,cAAc,GAAGpjB,KAAK,CAACygB,QAAQ,EAAE,QAAQ,GAAG,IAAI,CAACthB,OAAO,CAAC+hB,aAAa,GAAG,GAAG,CAAC;UAC/E,IAAI,CAACX,cAAc,IAAIphB,OAAO,CAACoF,SAAS,EAAEkc,QAAQ,CAAChc,qBAAqB,GAAG,IAAI;UAC/Ega,UAAU,CAACna,qBAAqB,CAAC,CAAC;UAClC,IAAI,CAACic,cAAc,EAAE;YACnB,IAAIphB,OAAO,CAACoF,SAAS,EAAE;cACrBkc,QAAQ,CAACjc,QAAQ,GAAG0J,QAAQ;cAC5BiS,iBAAiB,CAAChqB,OAAO,CAAC,UAAUirB,gBAAgB,EAAE;gBACpDA,gBAAgB,CAAC3c,qBAAqB,GAAG,IAAI;gBAC7C,IAAI2c,gBAAgB,KAAKX,QAAQ,EAAE;kBACjC,IAAIvd,IAAI,GAAGsd,OAAO,GAAGrjB,OAAO,CAACikB,gBAAgB,CAAC,GAAGlT,QAAQ;kBACzDkT,gBAAgB,CAAC5c,QAAQ,GAAGtB,IAAI;;kBAEhC;kBACAub,UAAU,CAAC5Z,iBAAiB,CAAC;oBAC3B/O,MAAM,EAAEsrB,gBAAgB;oBACxBle,IAAI,EAAEA;kBACR,CAAC,CAAC;gBACJ;cACF,CAAC,CAAC;YACJ;;YAEA;YACA;YACAkf,uBAAuB,CAAC,CAAC;YACzBjC,iBAAiB,CAAChqB,OAAO,CAAC,UAAUirB,gBAAgB,EAAE;cACpD,IAAI9hB,QAAQ,CAAC8jB,cAAc,CAAC,EAAE;gBAC5BnZ,QAAQ,CAACgM,YAAY,CAACmL,gBAAgB,EAAE9hB,QAAQ,CAAC8jB,cAAc,CAAC,CAAC;cACnE,CAAC,MAAM;gBACLnZ,QAAQ,CAAC4L,WAAW,CAACuL,gBAAgB,CAAC;cACxC;cACAgC,cAAc,EAAE;YAClB,CAAC,CAAC;;YAEF;YACA;YACA;YACA,IAAI5a,QAAQ,KAAKxI,KAAK,CAACygB,QAAQ,CAAC,EAAE;cAChC,IAAI4C,MAAM,GAAG,KAAK;cAClBlD,iBAAiB,CAAChqB,OAAO,CAAC,UAAUirB,gBAAgB,EAAE;gBACpD,IAAIA,gBAAgB,CAACM,aAAa,KAAK1hB,KAAK,CAACohB,gBAAgB,CAAC,EAAE;kBAC9DiC,MAAM,GAAG,IAAI;kBACb;gBACF;cACF,CAAC,CAAC;cACF,IAAIA,MAAM,EAAE;gBACVtY,qBAAqB,CAAC,QAAQ,CAAC;gBAC/BA,qBAAqB,CAAC,MAAM,CAAC;cAC/B;YACF;UACF;;UAEA;UACAoV,iBAAiB,CAAChqB,OAAO,CAAC,UAAUirB,gBAAgB,EAAE;YACpDje,SAAS,CAACie,gBAAgB,CAAC;UAC7B,CAAC,CAAC;UACF3C,UAAU,CAACzZ,UAAU,CAAC,CAAC;QACzB;QACAsb,iBAAiB,GAAG7B,UAAU;MAChC;;MAEA;MACA,IAAItW,MAAM,KAAK8B,QAAQ,IAAIpB,WAAW,IAAIA,WAAW,CAACa,WAAW,KAAK,OAAO,EAAE;QAC7E0W,eAAe,CAACjqB,OAAO,CAAC,UAAUgK,KAAK,EAAE;UACvCA,KAAK,CAACjF,UAAU,IAAIiF,KAAK,CAACjF,UAAU,CAACwd,WAAW,CAACvY,KAAK,CAAC;QACzD,CAAC,CAAC;MACJ;IACF,CAAC;IACDmjB,aAAa,EAAE,SAASA,aAAaA,CAAA,EAAG;MACtC,IAAI,CAAChC,WAAW,GAAGjX,WAAW,GAAG,KAAK;MACtC+V,eAAe,CAACnqB,MAAM,GAAG,CAAC;IAC5B,CAAC;IACDstB,aAAa,EAAE,SAASA,aAAaA,CAAA,EAAG;MACtC,IAAI,CAACxC,kBAAkB,CAAC,CAAC;MACzBzmB,GAAG,CAACU,QAAQ,EAAE,WAAW,EAAE,IAAI,CAAC+lB,kBAAkB,CAAC;MACnDzmB,GAAG,CAACU,QAAQ,EAAE,SAAS,EAAE,IAAI,CAAC+lB,kBAAkB,CAAC;MACjDzmB,GAAG,CAACU,QAAQ,EAAE,UAAU,EAAE,IAAI,CAAC+lB,kBAAkB,CAAC;MAClDzmB,GAAG,CAACU,QAAQ,EAAE,SAAS,EAAE,IAAI,CAACgmB,aAAa,CAAC;MAC5C1mB,GAAG,CAACU,QAAQ,EAAE,OAAO,EAAE,IAAI,CAACimB,WAAW,CAAC;IAC1C,CAAC;IACDF,kBAAkB,EAAE,SAASA,kBAAkBA,CAAC1Z,GAAG,EAAE;MACnD,IAAI,OAAOgD,WAAW,KAAK,WAAW,IAAIA,WAAW,EAAE;;MAEvD;MACA,IAAIiW,iBAAiB,KAAK,IAAI,CAAClZ,QAAQ,EAAE;;MAEzC;MACA,IAAIC,GAAG,IAAIlM,OAAO,CAACkM,GAAG,CAACvR,MAAM,EAAE,IAAI,CAACqJ,OAAO,CAACQ,SAAS,EAAE,IAAI,CAACyH,QAAQ,CAAClN,EAAE,EAAE,KAAK,CAAC,EAAE;;MAEjF;MACA,IAAImN,GAAG,IAAIA,GAAG,CAAC+L,MAAM,KAAK,CAAC,EAAE;MAC7B,OAAO+M,iBAAiB,CAAClqB,MAAM,EAAE;QAC/B,IAAIiE,EAAE,GAAGimB,iBAAiB,CAAC,CAAC,CAAC;QAC7B5kB,WAAW,CAACrB,EAAE,EAAE,IAAI,CAACiF,OAAO,CAAC+hB,aAAa,EAAE,KAAK,CAAC;QAClDf,iBAAiB,CAACqD,KAAK,CAAC,CAAC;QACzBvb,aAAa,CAAC;UACZb,QAAQ,EAAE,IAAI,CAACA,QAAQ;UACvBe,MAAM,EAAE,IAAI,CAACf,QAAQ,CAAClN,EAAE;UACxBrB,IAAI,EAAE,UAAU;UAChBuP,QAAQ,EAAElO,EAAE;UACZ0O,aAAa,EAAEvB;QACjB,CAAC,CAAC;MACJ;IACF,CAAC;IACD2Z,aAAa,EAAE,SAASA,aAAaA,CAAC3Z,GAAG,EAAE;MACzC,IAAIA,GAAG,CAACjR,GAAG,KAAK,IAAI,CAAC+I,OAAO,CAACgiB,YAAY,EAAE;QACzC,IAAI,CAACE,gBAAgB,GAAG,IAAI;MAC9B;IACF,CAAC;IACDJ,WAAW,EAAE,SAASA,WAAWA,CAAC5Z,GAAG,EAAE;MACrC,IAAIA,GAAG,CAACjR,GAAG,KAAK,IAAI,CAAC+I,OAAO,CAACgiB,YAAY,EAAE;QACzC,IAAI,CAACE,gBAAgB,GAAG,KAAK;MAC/B;IACF;EACF,CAAC;EACD,OAAOnqB,QAAQ,CAAC2pB,SAAS,EAAE;IACzB;IACA7Z,UAAU,EAAE,WAAW;IACvBmU,KAAK,EAAE;MACL;AACN;AACA;AACA;MACMsI,MAAM,EAAE,SAASA,MAAMA,CAACvpB,EAAE,EAAE;QAC1B,IAAIkN,QAAQ,GAAGlN,EAAE,CAACgB,UAAU,CAAC8I,OAAO,CAAC;QACrC,IAAI,CAACoD,QAAQ,IAAI,CAACA,QAAQ,CAACjI,OAAO,CAAC8iB,SAAS,IAAI,CAAC9B,iBAAiB,CAAC1oB,OAAO,CAACyC,EAAE,CAAC,EAAE;QAChF,IAAIomB,iBAAiB,IAAIA,iBAAiB,KAAKlZ,QAAQ,EAAE;UACvDkZ,iBAAiB,CAAC2B,SAAS,CAAClB,kBAAkB,CAAC,CAAC;UAChDT,iBAAiB,GAAGlZ,QAAQ;QAC9B;QACA7L,WAAW,CAACrB,EAAE,EAAEkN,QAAQ,CAACjI,OAAO,CAAC+hB,aAAa,EAAE,IAAI,CAAC;QACrDf,iBAAiB,CAACxqB,IAAI,CAACuE,EAAE,CAAC;MAC5B,CAAC;MACD;AACN;AACA;AACA;MACMwpB,QAAQ,EAAE,SAASA,QAAQA,CAACxpB,EAAE,EAAE;QAC9B,IAAIkN,QAAQ,GAAGlN,EAAE,CAACgB,UAAU,CAAC8I,OAAO,CAAC;UACnChE,KAAK,GAAGmgB,iBAAiB,CAAC1oB,OAAO,CAACyC,EAAE,CAAC;QACvC,IAAI,CAACkN,QAAQ,IAAI,CAACA,QAAQ,CAACjI,OAAO,CAAC8iB,SAAS,IAAI,CAAC,CAACjiB,KAAK,EAAE;QACzDzE,WAAW,CAACrB,EAAE,EAAEkN,QAAQ,CAACjI,OAAO,CAAC+hB,aAAa,EAAE,KAAK,CAAC;QACtDf,iBAAiB,CAACpb,MAAM,CAAC/E,KAAK,EAAE,CAAC,CAAC;MACpC;IACF,CAAC;IACD8H,eAAe,EAAE,SAASA,eAAeA,CAAA,EAAG;MAC1C,IAAI6b,MAAM,GAAG,IAAI;MACjB,IAAIC,WAAW,GAAG,EAAE;QAClBC,WAAW,GAAG,EAAE;MAClB1D,iBAAiB,CAAChqB,OAAO,CAAC,UAAUirB,gBAAgB,EAAE;QACpDwC,WAAW,CAACjuB,IAAI,CAAC;UACfyrB,gBAAgB,EAAEA,gBAAgB;UAClCphB,KAAK,EAAEohB,gBAAgB,CAACM;QAC1B,CAAC,CAAC;;QAEF;QACA,IAAIjZ,QAAQ;QACZ,IAAI+X,OAAO,IAAIY,gBAAgB,KAAKX,QAAQ,EAAE;UAC5ChY,QAAQ,GAAG,CAAC,CAAC;QACf,CAAC,MAAM,IAAI+X,OAAO,EAAE;UAClB/X,QAAQ,GAAGzI,KAAK,CAACohB,gBAAgB,EAAE,QAAQ,GAAGuC,MAAM,CAACxkB,OAAO,CAAC+hB,aAAa,GAAG,GAAG,CAAC;QACnF,CAAC,MAAM;UACLzY,QAAQ,GAAGzI,KAAK,CAACohB,gBAAgB,CAAC;QACpC;QACAyC,WAAW,CAACluB,IAAI,CAAC;UACfyrB,gBAAgB,EAAEA,gBAAgB;UAClCphB,KAAK,EAAEyI;QACT,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,OAAO;QACL2Q,KAAK,EAAEvhB,kBAAkB,CAACsoB,iBAAiB,CAAC;QAC5C2D,MAAM,EAAE,EAAE,CAAC7c,MAAM,CAACmZ,eAAe,CAAC;QAClCwD,WAAW,EAAEA,WAAW;QACxBC,WAAW,EAAEA;MACf,CAAC;IACH,CAAC;IACD7b,eAAe,EAAE;MACfmZ,YAAY,EAAE,SAASA,YAAYA,CAAC/qB,GAAG,EAAE;QACvCA,GAAG,GAAGA,GAAG,CAAC2tB,WAAW,CAAC,CAAC;QACvB,IAAI3tB,GAAG,KAAK,MAAM,EAAE;UAClBA,GAAG,GAAG,SAAS;QACjB,CAAC,MAAM,IAAIA,GAAG,CAACH,MAAM,GAAG,CAAC,EAAE;UACzBG,GAAG,GAAGA,GAAG,CAAC4S,MAAM,CAAC,CAAC,CAAC,CAAC9I,WAAW,CAAC,CAAC,GAAG9J,GAAG,CAAC6S,MAAM,CAAC,CAAC,CAAC;QACnD;QACA,OAAO7S,GAAG;MACZ;IACF;EACF,CAAC,CAAC;AACJ;AACA,SAASosB,uBAAuBA,CAACwB,cAAc,EAAE7b,MAAM,EAAE;EACvDgY,iBAAiB,CAAChqB,OAAO,CAAC,UAAUirB,gBAAgB,EAAErrB,CAAC,EAAE;IACvD,IAAID,MAAM,GAAGqS,MAAM,CAAC7I,QAAQ,CAAC8hB,gBAAgB,CAACM,aAAa,IAAIsC,cAAc,GAAGrjB,MAAM,CAAC5K,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IAC/F,IAAID,MAAM,EAAE;MACVqS,MAAM,CAAC8N,YAAY,CAACmL,gBAAgB,EAAEtrB,MAAM,CAAC;IAC/C,CAAC,MAAM;MACLqS,MAAM,CAAC0N,WAAW,CAACuL,gBAAgB,CAAC;IACtC;EACF,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASO,qBAAqBA,CAACsC,gBAAgB,EAAE9b,MAAM,EAAE;EACvDiY,eAAe,CAACjqB,OAAO,CAAC,UAAUgK,KAAK,EAAEpK,CAAC,EAAE;IAC1C,IAAID,MAAM,GAAGqS,MAAM,CAAC7I,QAAQ,CAACa,KAAK,CAACuhB,aAAa,IAAIuC,gBAAgB,GAAGtjB,MAAM,CAAC5K,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;IACtF,IAAID,MAAM,EAAE;MACVqS,MAAM,CAAC8N,YAAY,CAAC9V,KAAK,EAAErK,MAAM,CAAC;IACpC,CAAC,MAAM;MACLqS,MAAM,CAAC0N,WAAW,CAAC1V,KAAK,CAAC;IAC3B;EACF,CAAC,CAAC;AACJ;AACA,SAASiiB,uBAAuBA,CAAA,EAAG;EACjCjC,iBAAiB,CAAChqB,OAAO,CAAC,UAAUirB,gBAAgB,EAAE;IACpD,IAAIA,gBAAgB,KAAKX,QAAQ,EAAE;IACnCW,gBAAgB,CAAClmB,UAAU,IAAIkmB,gBAAgB,CAAClmB,UAAU,CAACwd,WAAW,CAAC0I,gBAAgB,CAAC;EAC1F,CAAC,CAAC;AACJ;AAEA5hB,QAAQ,CAACoH,KAAK,CAAC,IAAIwV,gBAAgB,CAAC,CAAC,CAAC;AACtC5c,QAAQ,CAACoH,KAAK,CAACoY,MAAM,EAAEJ,MAAM,CAAC;AAE9B,eAAepf,QAAQ;AACvB,SAASohB,eAAe,IAAIC,SAAS,EAAErhB,QAAQ,EAAE4f,UAAU,IAAIC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}