{"ast": null, "code": "\"use strict\";\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nvar support = require(\"./support.js\");\nvar compressions = require(\"./compressions.js\");\nvar nodeBuffer = require(\"./nodeBuffer.js\");\n/**\n * Convert a string to a \"binary string\" : a string containing only char codes between 0 and 255.\n * @param {string} str the string to transform.\n * @return {String} the binary string.\n */\nexports.string2binary = function (str) {\n  var result = \"\";\n  for (var i = 0; i < str.length; i++) {\n    result += String.fromCharCode(str.charCodeAt(i) & 0xff);\n  }\n  return result;\n};\nexports.arrayBuffer2Blob = function (buffer, mimeType) {\n  exports.checkSupport(\"blob\");\n  mimeType = mimeType || \"application/zip\";\n  try {\n    // Blob constructor\n    return new Blob([buffer], {\n      type: mimeType\n    });\n  } catch (e) {\n    try {\n      // deprecated, browser only, old way\n      var Builder = window.BlobBuilder || window.WebKitBlobBuilder || window.MozBlobBuilder || window.MSBlobBuilder;\n      var builder = new Builder();\n      builder.append(buffer);\n      return builder.getBlob(mimeType);\n    } catch (e) {\n      // well, fuck ?!\n      throw new Error(\"Bug : can't construct the Blob.\");\n    }\n  }\n};\n/**\n * The identity function.\n * @param {Object} input the input.\n * @return {Object} the same input.\n */\nfunction identity(input) {\n  return input;\n}\n\n/**\n * Fill in an array with a string.\n * @param {String} str the string to use.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to fill in (will be mutated).\n * @return {Array|ArrayBuffer|Uint8Array|Buffer} the updated array.\n */\nfunction stringToArrayLike(str, array) {\n  for (var i = 0; i < str.length; ++i) {\n    array[i] = str.charCodeAt(i) & 0xff;\n  }\n  return array;\n}\n\n/**\n * Transform an array-like object to a string.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n * @return {String} the result.\n */\nfunction arrayLikeToString(array) {\n  // Performances notes :\n  // --------------------\n  // String.fromCharCode.apply(null, array) is the fastest, see\n  // see http://jsperf.com/converting-a-uint8array-to-a-string/2\n  // but the stack is limited (and we can get huge arrays !).\n  //\n  // result += String.fromCharCode(array[i]); generate too many strings !\n  //\n  // This code is inspired by http://jsperf.com/arraybuffer-to-string-apply-performance/2\n  var chunk = 65536;\n  var result = [],\n    len = array.length,\n    type = exports.getTypeOf(array);\n  var k = 0,\n    canUseApply = true;\n  try {\n    switch (type) {\n      case \"uint8array\":\n        String.fromCharCode.apply(null, new Uint8Array(0));\n        break;\n      case \"nodebuffer\":\n        String.fromCharCode.apply(null, nodeBuffer(0));\n        break;\n    }\n  } catch (e) {\n    canUseApply = false;\n  }\n\n  // no apply : slow and painful algorithm\n  // default browser on android 4.*\n  if (!canUseApply) {\n    var resultStr = \"\";\n    for (var i = 0; i < array.length; i++) {\n      resultStr += String.fromCharCode(array[i]);\n    }\n    return resultStr;\n  }\n  while (k < len && chunk > 1) {\n    try {\n      if (type === \"array\" || type === \"nodebuffer\") {\n        result.push(String.fromCharCode.apply(null, array.slice(k, Math.min(k + chunk, len))));\n      } else {\n        result.push(String.fromCharCode.apply(null, array.subarray(k, Math.min(k + chunk, len))));\n      }\n      k += chunk;\n    } catch (e) {\n      chunk = Math.floor(chunk / 2);\n    }\n  }\n  return result.join(\"\");\n}\nexports.applyFromCharCode = arrayLikeToString;\n\n/**\n * Copy the data from an array-like to an other array-like.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} arrayFrom the origin array.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} arrayTo the destination array which will be mutated.\n * @return {Array|ArrayBuffer|Uint8Array|Buffer} the updated destination array.\n */\nfunction arrayLikeToArrayLike(arrayFrom, arrayTo) {\n  for (var i = 0; i < arrayFrom.length; i++) {\n    arrayTo[i] = arrayFrom[i];\n  }\n  return arrayTo;\n}\n\n// a matrix containing functions to transform everything into everything.\nvar transform = {};\n\n// string to ?\ntransform.string = {\n  string: identity,\n  array: function array(input) {\n    return stringToArrayLike(input, new Array(input.length));\n  },\n  arraybuffer: function arraybuffer(input) {\n    return transform.string.uint8array(input).buffer;\n  },\n  uint8array: function uint8array(input) {\n    return stringToArrayLike(input, new Uint8Array(input.length));\n  },\n  nodebuffer: function nodebuffer(input) {\n    return stringToArrayLike(input, nodeBuffer(input.length));\n  }\n};\n\n// array to ?\ntransform.array = {\n  string: arrayLikeToString,\n  array: identity,\n  arraybuffer: function arraybuffer(input) {\n    return new Uint8Array(input).buffer;\n  },\n  uint8array: function uint8array(input) {\n    return new Uint8Array(input);\n  },\n  nodebuffer: function nodebuffer(input) {\n    return nodeBuffer(input);\n  }\n};\n\n// arraybuffer to ?\ntransform.arraybuffer = {\n  string: function string(input) {\n    return arrayLikeToString(new Uint8Array(input));\n  },\n  array: function array(input) {\n    return arrayLikeToArrayLike(new Uint8Array(input), new Array(input.byteLength));\n  },\n  arraybuffer: identity,\n  uint8array: function uint8array(input) {\n    return new Uint8Array(input);\n  },\n  nodebuffer: function nodebuffer(input) {\n    return nodeBuffer(new Uint8Array(input));\n  }\n};\n\n// uint8array to ?\ntransform.uint8array = {\n  string: arrayLikeToString,\n  array: function array(input) {\n    return arrayLikeToArrayLike(input, new Array(input.length));\n  },\n  arraybuffer: function arraybuffer(input) {\n    return input.buffer;\n  },\n  uint8array: identity,\n  nodebuffer: function nodebuffer(input) {\n    return nodeBuffer(input);\n  }\n};\n\n// nodebuffer to ?\ntransform.nodebuffer = {\n  string: arrayLikeToString,\n  array: function array(input) {\n    return arrayLikeToArrayLike(input, new Array(input.length));\n  },\n  arraybuffer: function arraybuffer(input) {\n    return transform.nodebuffer.uint8array(input).buffer;\n  },\n  uint8array: function uint8array(input) {\n    return arrayLikeToArrayLike(input, new Uint8Array(input.length));\n  },\n  nodebuffer: identity\n};\n\n/**\n * Transform an input into any type.\n * The supported output type are : string, array, uint8array, arraybuffer, nodebuffer.\n * If no output type is specified, the unmodified input will be returned.\n * @param {String} outputType the output type.\n * @param {String|Array|ArrayBuffer|Uint8Array|Buffer} input the input to convert.\n * @throws {Error} an Error if the browser doesn't support the requested output type.\n */\nexports.transformTo = function (outputType, input) {\n  if (!input) {\n    // undefined, null, etc\n    // an empty string won't harm.\n    input = \"\";\n  }\n  if (!outputType) {\n    return input;\n  }\n  exports.checkSupport(outputType);\n  var inputType = exports.getTypeOf(input);\n  var result = transform[inputType][outputType](input);\n  return result;\n};\n\n/**\n * Return the type of the input.\n * The type will be in a format valid for PizZip.utils.transformTo : string, array, uint8array, arraybuffer.\n * @param {Object} input the input to identify.\n * @return {String} the (lowercase) type of the input.\n */\nexports.getTypeOf = function (input) {\n  if (input == null) {\n    return;\n  }\n  if (typeof input === \"string\") {\n    return \"string\";\n  }\n  var protoResult = Object.prototype.toString.call(input);\n  if (protoResult === \"[object Array]\") {\n    return \"array\";\n  }\n  if (support.nodebuffer && nodeBuffer.test(input)) {\n    return \"nodebuffer\";\n  }\n  if (support.uint8array && protoResult === \"[object Uint8Array]\") {\n    return \"uint8array\";\n  }\n  if (support.arraybuffer && protoResult === \"[object ArrayBuffer]\") {\n    return \"arraybuffer\";\n  }\n  if (protoResult === \"[object Promise]\") {\n    throw new Error(\"Cannot read data from a promise, you probably are running new PizZip(data) with a promise\");\n  }\n  if (_typeof(input) === \"object\" && typeof input.file === \"function\") {\n    throw new Error(\"Cannot read data from a pizzip instance, you probably are running new PizZip(zip) with a zipinstance\");\n  }\n  if (protoResult === \"[object Date]\") {\n    throw new Error(\"Cannot read data from a Date, you probably are running new PizZip(data) with a date\");\n  }\n  if (_typeof(input) === \"object\" && input.crc32 == null) {\n    throw new Error(\"Unsupported data given to new PizZip(data) (object given)\");\n  }\n};\n\n/**\n * Throw an exception if the type is not supported.\n * @param {String} type the type to check.\n * @throws {Error} an Error if the browser doesn't support the requested type.\n */\nexports.checkSupport = function (type) {\n  var supported = support[type.toLowerCase()];\n  if (!supported) {\n    throw new Error(type + \" is not supported by this browser\");\n  }\n};\nexports.MAX_VALUE_16BITS = 65535;\nexports.MAX_VALUE_32BITS = -1; // well, \"\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\" is parsed as -1\n\n/**\n * Prettify a string read as binary.\n * @param {string} str the string to prettify.\n * @return {string} a pretty string.\n */\nexports.pretty = function (str) {\n  var res = \"\",\n    code,\n    i;\n  for (i = 0; i < (str || \"\").length; i++) {\n    code = str.charCodeAt(i);\n    res += \"\\\\x\" + (code < 16 ? \"0\" : \"\") + code.toString(16).toUpperCase();\n  }\n  return res;\n};\n\n/**\n * Find a compression registered in PizZip.\n * @param {string} compressionMethod the method magic to find.\n * @return {Object|null} the PizZip compression object, null if none found.\n */\nexports.findCompression = function (compressionMethod) {\n  for (var method in compressions) {\n    if (!compressions.hasOwnProperty(method)) {\n      continue;\n    }\n    if (compressions[method].magic === compressionMethod) {\n      return compressions[method];\n    }\n  }\n  return null;\n};\n/**\n * Cross-window, cross-Node-context regular expression detection\n * @param  {Object}  object Anything\n * @return {Boolean}        true if the object is a regular expression,\n * false otherwise\n */\nexports.isRegExp = function (object) {\n  return Object.prototype.toString.call(object) === \"[object RegExp]\";\n};\n\n/**\n * Merge the objects passed as parameters into a new one.\n * @private\n * @param {...Object} var_args All objects to merge.\n * @return {Object} a new object with the data of the others.\n */\nexports.extend = function () {\n  var result = {};\n  var i, attr;\n  for (i = 0; i < arguments.length; i++) {\n    // arguments is not enumerable in some browsers\n    for (attr in arguments[i]) {\n      if (arguments[i].hasOwnProperty(attr) && typeof result[attr] === \"undefined\") {\n        result[attr] = arguments[i][attr];\n      }\n    }\n  }\n  return result;\n};", "map": {"version": 3, "names": ["_typeof", "o", "Symbol", "iterator", "constructor", "prototype", "support", "require", "compressions", "node<PERSON>uff<PERSON>", "exports", "string2binary", "str", "result", "i", "length", "String", "fromCharCode", "charCodeAt", "arrayBuffer2Blob", "buffer", "mimeType", "checkSupport", "Blob", "type", "e", "Builder", "window", "BlobBuilder", "WebKitBlobBuilder", "MozBlobBuilder", "MSBlobBuilder", "builder", "append", "getBlob", "Error", "identity", "input", "stringToArrayLike", "array", "arrayLikeToString", "chunk", "len", "getTypeOf", "k", "canUseApply", "apply", "Uint8Array", "resultStr", "push", "slice", "Math", "min", "subarray", "floor", "join", "applyFromCharCode", "arrayLikeToArrayLike", "arrayFrom", "arrayTo", "transform", "string", "Array", "arraybuffer", "uint8array", "nodebuffer", "byteLength", "transformTo", "outputType", "inputType", "protoResult", "Object", "toString", "call", "test", "file", "crc32", "supported", "toLowerCase", "MAX_VALUE_16BITS", "MAX_VALUE_32BITS", "pretty", "res", "code", "toUpperCase", "findCompression", "compressionMethod", "method", "hasOwnProperty", "magic", "isRegExp", "object", "extend", "attr", "arguments"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/pizzip@3.1.7/node_modules/pizzip/js/utils.js"], "sourcesContent": ["\"use strict\";\n\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nvar support = require(\"./support.js\");\nvar compressions = require(\"./compressions.js\");\nvar nodeBuffer = require(\"./nodeBuffer.js\");\n/**\n * Convert a string to a \"binary string\" : a string containing only char codes between 0 and 255.\n * @param {string} str the string to transform.\n * @return {String} the binary string.\n */\nexports.string2binary = function (str) {\n  var result = \"\";\n  for (var i = 0; i < str.length; i++) {\n    result += String.fromCharCode(str.charCodeAt(i) & 0xff);\n  }\n  return result;\n};\nexports.arrayBuffer2Blob = function (buffer, mimeType) {\n  exports.checkSupport(\"blob\");\n  mimeType = mimeType || \"application/zip\";\n  try {\n    // Blob constructor\n    return new Blob([buffer], {\n      type: mimeType\n    });\n  } catch (e) {\n    try {\n      // deprecated, browser only, old way\n      var Builder = window.BlobBuilder || window.WebKitBlobBuilder || window.MozBlobBuilder || window.MSBlobBuilder;\n      var builder = new Builder();\n      builder.append(buffer);\n      return builder.getBlob(mimeType);\n    } catch (e) {\n      // well, fuck ?!\n      throw new Error(\"Bug : can't construct the Blob.\");\n    }\n  }\n};\n/**\n * The identity function.\n * @param {Object} input the input.\n * @return {Object} the same input.\n */\nfunction identity(input) {\n  return input;\n}\n\n/**\n * Fill in an array with a string.\n * @param {String} str the string to use.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to fill in (will be mutated).\n * @return {Array|ArrayBuffer|Uint8Array|Buffer} the updated array.\n */\nfunction stringToArrayLike(str, array) {\n  for (var i = 0; i < str.length; ++i) {\n    array[i] = str.charCodeAt(i) & 0xff;\n  }\n  return array;\n}\n\n/**\n * Transform an array-like object to a string.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n * @return {String} the result.\n */\nfunction arrayLikeToString(array) {\n  // Performances notes :\n  // --------------------\n  // String.fromCharCode.apply(null, array) is the fastest, see\n  // see http://jsperf.com/converting-a-uint8array-to-a-string/2\n  // but the stack is limited (and we can get huge arrays !).\n  //\n  // result += String.fromCharCode(array[i]); generate too many strings !\n  //\n  // This code is inspired by http://jsperf.com/arraybuffer-to-string-apply-performance/2\n  var chunk = 65536;\n  var result = [],\n    len = array.length,\n    type = exports.getTypeOf(array);\n  var k = 0,\n    canUseApply = true;\n  try {\n    switch (type) {\n      case \"uint8array\":\n        String.fromCharCode.apply(null, new Uint8Array(0));\n        break;\n      case \"nodebuffer\":\n        String.fromCharCode.apply(null, nodeBuffer(0));\n        break;\n    }\n  } catch (e) {\n    canUseApply = false;\n  }\n\n  // no apply : slow and painful algorithm\n  // default browser on android 4.*\n  if (!canUseApply) {\n    var resultStr = \"\";\n    for (var i = 0; i < array.length; i++) {\n      resultStr += String.fromCharCode(array[i]);\n    }\n    return resultStr;\n  }\n  while (k < len && chunk > 1) {\n    try {\n      if (type === \"array\" || type === \"nodebuffer\") {\n        result.push(String.fromCharCode.apply(null, array.slice(k, Math.min(k + chunk, len))));\n      } else {\n        result.push(String.fromCharCode.apply(null, array.subarray(k, Math.min(k + chunk, len))));\n      }\n      k += chunk;\n    } catch (e) {\n      chunk = Math.floor(chunk / 2);\n    }\n  }\n  return result.join(\"\");\n}\nexports.applyFromCharCode = arrayLikeToString;\n\n/**\n * Copy the data from an array-like to an other array-like.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} arrayFrom the origin array.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} arrayTo the destination array which will be mutated.\n * @return {Array|ArrayBuffer|Uint8Array|Buffer} the updated destination array.\n */\nfunction arrayLikeToArrayLike(arrayFrom, arrayTo) {\n  for (var i = 0; i < arrayFrom.length; i++) {\n    arrayTo[i] = arrayFrom[i];\n  }\n  return arrayTo;\n}\n\n// a matrix containing functions to transform everything into everything.\nvar transform = {};\n\n// string to ?\ntransform.string = {\n  string: identity,\n  array: function array(input) {\n    return stringToArrayLike(input, new Array(input.length));\n  },\n  arraybuffer: function arraybuffer(input) {\n    return transform.string.uint8array(input).buffer;\n  },\n  uint8array: function uint8array(input) {\n    return stringToArrayLike(input, new Uint8Array(input.length));\n  },\n  nodebuffer: function nodebuffer(input) {\n    return stringToArrayLike(input, nodeBuffer(input.length));\n  }\n};\n\n// array to ?\ntransform.array = {\n  string: arrayLikeToString,\n  array: identity,\n  arraybuffer: function arraybuffer(input) {\n    return new Uint8Array(input).buffer;\n  },\n  uint8array: function uint8array(input) {\n    return new Uint8Array(input);\n  },\n  nodebuffer: function nodebuffer(input) {\n    return nodeBuffer(input);\n  }\n};\n\n// arraybuffer to ?\ntransform.arraybuffer = {\n  string: function string(input) {\n    return arrayLikeToString(new Uint8Array(input));\n  },\n  array: function array(input) {\n    return arrayLikeToArrayLike(new Uint8Array(input), new Array(input.byteLength));\n  },\n  arraybuffer: identity,\n  uint8array: function uint8array(input) {\n    return new Uint8Array(input);\n  },\n  nodebuffer: function nodebuffer(input) {\n    return nodeBuffer(new Uint8Array(input));\n  }\n};\n\n// uint8array to ?\ntransform.uint8array = {\n  string: arrayLikeToString,\n  array: function array(input) {\n    return arrayLikeToArrayLike(input, new Array(input.length));\n  },\n  arraybuffer: function arraybuffer(input) {\n    return input.buffer;\n  },\n  uint8array: identity,\n  nodebuffer: function nodebuffer(input) {\n    return nodeBuffer(input);\n  }\n};\n\n// nodebuffer to ?\ntransform.nodebuffer = {\n  string: arrayLikeToString,\n  array: function array(input) {\n    return arrayLikeToArrayLike(input, new Array(input.length));\n  },\n  arraybuffer: function arraybuffer(input) {\n    return transform.nodebuffer.uint8array(input).buffer;\n  },\n  uint8array: function uint8array(input) {\n    return arrayLikeToArrayLike(input, new Uint8Array(input.length));\n  },\n  nodebuffer: identity\n};\n\n/**\n * Transform an input into any type.\n * The supported output type are : string, array, uint8array, arraybuffer, nodebuffer.\n * If no output type is specified, the unmodified input will be returned.\n * @param {String} outputType the output type.\n * @param {String|Array|ArrayBuffer|Uint8Array|Buffer} input the input to convert.\n * @throws {Error} an Error if the browser doesn't support the requested output type.\n */\nexports.transformTo = function (outputType, input) {\n  if (!input) {\n    // undefined, null, etc\n    // an empty string won't harm.\n    input = \"\";\n  }\n  if (!outputType) {\n    return input;\n  }\n  exports.checkSupport(outputType);\n  var inputType = exports.getTypeOf(input);\n  var result = transform[inputType][outputType](input);\n  return result;\n};\n\n/**\n * Return the type of the input.\n * The type will be in a format valid for PizZip.utils.transformTo : string, array, uint8array, arraybuffer.\n * @param {Object} input the input to identify.\n * @return {String} the (lowercase) type of the input.\n */\nexports.getTypeOf = function (input) {\n  if (input == null) {\n    return;\n  }\n  if (typeof input === \"string\") {\n    return \"string\";\n  }\n  var protoResult = Object.prototype.toString.call(input);\n  if (protoResult === \"[object Array]\") {\n    return \"array\";\n  }\n  if (support.nodebuffer && nodeBuffer.test(input)) {\n    return \"nodebuffer\";\n  }\n  if (support.uint8array && protoResult === \"[object Uint8Array]\") {\n    return \"uint8array\";\n  }\n  if (support.arraybuffer && protoResult === \"[object ArrayBuffer]\") {\n    return \"arraybuffer\";\n  }\n  if (protoResult === \"[object Promise]\") {\n    throw new Error(\"Cannot read data from a promise, you probably are running new PizZip(data) with a promise\");\n  }\n  if (_typeof(input) === \"object\" && typeof input.file === \"function\") {\n    throw new Error(\"Cannot read data from a pizzip instance, you probably are running new PizZip(zip) with a zipinstance\");\n  }\n  if (protoResult === \"[object Date]\") {\n    throw new Error(\"Cannot read data from a Date, you probably are running new PizZip(data) with a date\");\n  }\n  if (_typeof(input) === \"object\" && input.crc32 == null) {\n    throw new Error(\"Unsupported data given to new PizZip(data) (object given)\");\n  }\n};\n\n/**\n * Throw an exception if the type is not supported.\n * @param {String} type the type to check.\n * @throws {Error} an Error if the browser doesn't support the requested type.\n */\nexports.checkSupport = function (type) {\n  var supported = support[type.toLowerCase()];\n  if (!supported) {\n    throw new Error(type + \" is not supported by this browser\");\n  }\n};\nexports.MAX_VALUE_16BITS = 65535;\nexports.MAX_VALUE_32BITS = -1; // well, \"\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\" is parsed as -1\n\n/**\n * Prettify a string read as binary.\n * @param {string} str the string to prettify.\n * @return {string} a pretty string.\n */\nexports.pretty = function (str) {\n  var res = \"\",\n    code,\n    i;\n  for (i = 0; i < (str || \"\").length; i++) {\n    code = str.charCodeAt(i);\n    res += \"\\\\x\" + (code < 16 ? \"0\" : \"\") + code.toString(16).toUpperCase();\n  }\n  return res;\n};\n\n/**\n * Find a compression registered in PizZip.\n * @param {string} compressionMethod the method magic to find.\n * @return {Object|null} the PizZip compression object, null if none found.\n */\nexports.findCompression = function (compressionMethod) {\n  for (var method in compressions) {\n    if (!compressions.hasOwnProperty(method)) {\n      continue;\n    }\n    if (compressions[method].magic === compressionMethod) {\n      return compressions[method];\n    }\n  }\n  return null;\n};\n/**\n * Cross-window, cross-Node-context regular expression detection\n * @param  {Object}  object Anything\n * @return {Boolean}        true if the object is a regular expression,\n * false otherwise\n */\nexports.isRegExp = function (object) {\n  return Object.prototype.toString.call(object) === \"[object RegExp]\";\n};\n\n/**\n * Merge the objects passed as parameters into a new one.\n * @private\n * @param {...Object} var_args All objects to merge.\n * @return {Object} a new object with the data of the others.\n */\nexports.extend = function () {\n  var result = {};\n  var i, attr;\n  for (i = 0; i < arguments.length; i++) {\n    // arguments is not enumerable in some browsers\n    for (attr in arguments[i]) {\n      if (arguments[i].hasOwnProperty(attr) && typeof result[attr] === \"undefined\") {\n        result[attr] = arguments[i][attr];\n      }\n    }\n  }\n  return result;\n};"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAOA,CAACC,CAAC,EAAE;EAAE,yBAAyB;;EAAE,OAAOD,OAAO,GAAG,UAAU,IAAI,OAAOE,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAUF,CAAC,EAAE;IAAE,OAAO,OAAOA,CAAC;EAAE,CAAC,GAAG,UAAUA,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAOC,MAAM,IAAID,CAAC,CAACG,WAAW,KAAKF,MAAM,IAAID,CAAC,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,CAAC;EAAE,CAAC,EAAED,OAAO,CAACC,CAAC,CAAC;AAAE;AAC7T,IAAIK,OAAO,GAAGC,OAAO,CAAC,cAAc,CAAC;AACrC,IAAIC,YAAY,GAAGD,OAAO,CAAC,mBAAmB,CAAC;AAC/C,IAAIE,UAAU,GAAGF,OAAO,CAAC,iBAAiB,CAAC;AAC3C;AACA;AACA;AACA;AACA;AACAG,OAAO,CAACC,aAAa,GAAG,UAAUC,GAAG,EAAE;EACrC,IAAIC,MAAM,GAAG,EAAE;EACf,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACnCD,MAAM,IAAIG,MAAM,CAACC,YAAY,CAACL,GAAG,CAACM,UAAU,CAACJ,CAAC,CAAC,GAAG,IAAI,CAAC;EACzD;EACA,OAAOD,MAAM;AACf,CAAC;AACDH,OAAO,CAACS,gBAAgB,GAAG,UAAUC,MAAM,EAAEC,QAAQ,EAAE;EACrDX,OAAO,CAACY,YAAY,CAAC,MAAM,CAAC;EAC5BD,QAAQ,GAAGA,QAAQ,IAAI,iBAAiB;EACxC,IAAI;IACF;IACA,OAAO,IAAIE,IAAI,CAAC,CAACH,MAAM,CAAC,EAAE;MACxBI,IAAI,EAAEH;IACR,CAAC,CAAC;EACJ,CAAC,CAAC,OAAOI,CAAC,EAAE;IACV,IAAI;MACF;MACA,IAAIC,OAAO,GAAGC,MAAM,CAACC,WAAW,IAAID,MAAM,CAACE,iBAAiB,IAAIF,MAAM,CAACG,cAAc,IAAIH,MAAM,CAACI,aAAa;MAC7G,IAAIC,OAAO,GAAG,IAAIN,OAAO,CAAC,CAAC;MAC3BM,OAAO,CAACC,MAAM,CAACb,MAAM,CAAC;MACtB,OAAOY,OAAO,CAACE,OAAO,CAACb,QAAQ,CAAC;IAClC,CAAC,CAAC,OAAOI,CAAC,EAAE;MACV;MACA,MAAM,IAAIU,KAAK,CAAC,iCAAiC,CAAC;IACpD;EACF;AACF,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,OAAOA,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAAC1B,GAAG,EAAE2B,KAAK,EAAE;EACrC,KAAK,IAAIzB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,CAACG,MAAM,EAAE,EAAED,CAAC,EAAE;IACnCyB,KAAK,CAACzB,CAAC,CAAC,GAAGF,GAAG,CAACM,UAAU,CAACJ,CAAC,CAAC,GAAG,IAAI;EACrC;EACA,OAAOyB,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAACD,KAAK,EAAE;EAChC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAIE,KAAK,GAAG,KAAK;EACjB,IAAI5B,MAAM,GAAG,EAAE;IACb6B,GAAG,GAAGH,KAAK,CAACxB,MAAM;IAClBS,IAAI,GAAGd,OAAO,CAACiC,SAAS,CAACJ,KAAK,CAAC;EACjC,IAAIK,CAAC,GAAG,CAAC;IACPC,WAAW,GAAG,IAAI;EACpB,IAAI;IACF,QAAQrB,IAAI;MACV,KAAK,YAAY;QACfR,MAAM,CAACC,YAAY,CAAC6B,KAAK,CAAC,IAAI,EAAE,IAAIC,UAAU,CAAC,CAAC,CAAC,CAAC;QAClD;MACF,KAAK,YAAY;QACf/B,MAAM,CAACC,YAAY,CAAC6B,KAAK,CAAC,IAAI,EAAErC,UAAU,CAAC,CAAC,CAAC,CAAC;QAC9C;IACJ;EACF,CAAC,CAAC,OAAOgB,CAAC,EAAE;IACVoB,WAAW,GAAG,KAAK;EACrB;;EAEA;EACA;EACA,IAAI,CAACA,WAAW,EAAE;IAChB,IAAIG,SAAS,GAAG,EAAE;IAClB,KAAK,IAAIlC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,KAAK,CAACxB,MAAM,EAAED,CAAC,EAAE,EAAE;MACrCkC,SAAS,IAAIhC,MAAM,CAACC,YAAY,CAACsB,KAAK,CAACzB,CAAC,CAAC,CAAC;IAC5C;IACA,OAAOkC,SAAS;EAClB;EACA,OAAOJ,CAAC,GAAGF,GAAG,IAAID,KAAK,GAAG,CAAC,EAAE;IAC3B,IAAI;MACF,IAAIjB,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,YAAY,EAAE;QAC7CX,MAAM,CAACoC,IAAI,CAACjC,MAAM,CAACC,YAAY,CAAC6B,KAAK,CAAC,IAAI,EAAEP,KAAK,CAACW,KAAK,CAACN,CAAC,EAAEO,IAAI,CAACC,GAAG,CAACR,CAAC,GAAGH,KAAK,EAAEC,GAAG,CAAC,CAAC,CAAC,CAAC;MACxF,CAAC,MAAM;QACL7B,MAAM,CAACoC,IAAI,CAACjC,MAAM,CAACC,YAAY,CAAC6B,KAAK,CAAC,IAAI,EAAEP,KAAK,CAACc,QAAQ,CAACT,CAAC,EAAEO,IAAI,CAACC,GAAG,CAACR,CAAC,GAAGH,KAAK,EAAEC,GAAG,CAAC,CAAC,CAAC,CAAC;MAC3F;MACAE,CAAC,IAAIH,KAAK;IACZ,CAAC,CAAC,OAAOhB,CAAC,EAAE;MACVgB,KAAK,GAAGU,IAAI,CAACG,KAAK,CAACb,KAAK,GAAG,CAAC,CAAC;IAC/B;EACF;EACA,OAAO5B,MAAM,CAAC0C,IAAI,CAAC,EAAE,CAAC;AACxB;AACA7C,OAAO,CAAC8C,iBAAiB,GAAGhB,iBAAiB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA,SAASiB,oBAAoBA,CAACC,SAAS,EAAEC,OAAO,EAAE;EAChD,KAAK,IAAI7C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG4C,SAAS,CAAC3C,MAAM,EAAED,CAAC,EAAE,EAAE;IACzC6C,OAAO,CAAC7C,CAAC,CAAC,GAAG4C,SAAS,CAAC5C,CAAC,CAAC;EAC3B;EACA,OAAO6C,OAAO;AAChB;;AAEA;AACA,IAAIC,SAAS,GAAG,CAAC,CAAC;;AAElB;AACAA,SAAS,CAACC,MAAM,GAAG;EACjBA,MAAM,EAAEzB,QAAQ;EAChBG,KAAK,EAAE,SAASA,KAAKA,CAACF,KAAK,EAAE;IAC3B,OAAOC,iBAAiB,CAACD,KAAK,EAAE,IAAIyB,KAAK,CAACzB,KAAK,CAACtB,MAAM,CAAC,CAAC;EAC1D,CAAC;EACDgD,WAAW,EAAE,SAASA,WAAWA,CAAC1B,KAAK,EAAE;IACvC,OAAOuB,SAAS,CAACC,MAAM,CAACG,UAAU,CAAC3B,KAAK,CAAC,CAACjB,MAAM;EAClD,CAAC;EACD4C,UAAU,EAAE,SAASA,UAAUA,CAAC3B,KAAK,EAAE;IACrC,OAAOC,iBAAiB,CAACD,KAAK,EAAE,IAAIU,UAAU,CAACV,KAAK,CAACtB,MAAM,CAAC,CAAC;EAC/D,CAAC;EACDkD,UAAU,EAAE,SAASA,UAAUA,CAAC5B,KAAK,EAAE;IACrC,OAAOC,iBAAiB,CAACD,KAAK,EAAE5B,UAAU,CAAC4B,KAAK,CAACtB,MAAM,CAAC,CAAC;EAC3D;AACF,CAAC;;AAED;AACA6C,SAAS,CAACrB,KAAK,GAAG;EAChBsB,MAAM,EAAErB,iBAAiB;EACzBD,KAAK,EAAEH,QAAQ;EACf2B,WAAW,EAAE,SAASA,WAAWA,CAAC1B,KAAK,EAAE;IACvC,OAAO,IAAIU,UAAU,CAACV,KAAK,CAAC,CAACjB,MAAM;EACrC,CAAC;EACD4C,UAAU,EAAE,SAASA,UAAUA,CAAC3B,KAAK,EAAE;IACrC,OAAO,IAAIU,UAAU,CAACV,KAAK,CAAC;EAC9B,CAAC;EACD4B,UAAU,EAAE,SAASA,UAAUA,CAAC5B,KAAK,EAAE;IACrC,OAAO5B,UAAU,CAAC4B,KAAK,CAAC;EAC1B;AACF,CAAC;;AAED;AACAuB,SAAS,CAACG,WAAW,GAAG;EACtBF,MAAM,EAAE,SAASA,MAAMA,CAACxB,KAAK,EAAE;IAC7B,OAAOG,iBAAiB,CAAC,IAAIO,UAAU,CAACV,KAAK,CAAC,CAAC;EACjD,CAAC;EACDE,KAAK,EAAE,SAASA,KAAKA,CAACF,KAAK,EAAE;IAC3B,OAAOoB,oBAAoB,CAAC,IAAIV,UAAU,CAACV,KAAK,CAAC,EAAE,IAAIyB,KAAK,CAACzB,KAAK,CAAC6B,UAAU,CAAC,CAAC;EACjF,CAAC;EACDH,WAAW,EAAE3B,QAAQ;EACrB4B,UAAU,EAAE,SAASA,UAAUA,CAAC3B,KAAK,EAAE;IACrC,OAAO,IAAIU,UAAU,CAACV,KAAK,CAAC;EAC9B,CAAC;EACD4B,UAAU,EAAE,SAASA,UAAUA,CAAC5B,KAAK,EAAE;IACrC,OAAO5B,UAAU,CAAC,IAAIsC,UAAU,CAACV,KAAK,CAAC,CAAC;EAC1C;AACF,CAAC;;AAED;AACAuB,SAAS,CAACI,UAAU,GAAG;EACrBH,MAAM,EAAErB,iBAAiB;EACzBD,KAAK,EAAE,SAASA,KAAKA,CAACF,KAAK,EAAE;IAC3B,OAAOoB,oBAAoB,CAACpB,KAAK,EAAE,IAAIyB,KAAK,CAACzB,KAAK,CAACtB,MAAM,CAAC,CAAC;EAC7D,CAAC;EACDgD,WAAW,EAAE,SAASA,WAAWA,CAAC1B,KAAK,EAAE;IACvC,OAAOA,KAAK,CAACjB,MAAM;EACrB,CAAC;EACD4C,UAAU,EAAE5B,QAAQ;EACpB6B,UAAU,EAAE,SAASA,UAAUA,CAAC5B,KAAK,EAAE;IACrC,OAAO5B,UAAU,CAAC4B,KAAK,CAAC;EAC1B;AACF,CAAC;;AAED;AACAuB,SAAS,CAACK,UAAU,GAAG;EACrBJ,MAAM,EAAErB,iBAAiB;EACzBD,KAAK,EAAE,SAASA,KAAKA,CAACF,KAAK,EAAE;IAC3B,OAAOoB,oBAAoB,CAACpB,KAAK,EAAE,IAAIyB,KAAK,CAACzB,KAAK,CAACtB,MAAM,CAAC,CAAC;EAC7D,CAAC;EACDgD,WAAW,EAAE,SAASA,WAAWA,CAAC1B,KAAK,EAAE;IACvC,OAAOuB,SAAS,CAACK,UAAU,CAACD,UAAU,CAAC3B,KAAK,CAAC,CAACjB,MAAM;EACtD,CAAC;EACD4C,UAAU,EAAE,SAASA,UAAUA,CAAC3B,KAAK,EAAE;IACrC,OAAOoB,oBAAoB,CAACpB,KAAK,EAAE,IAAIU,UAAU,CAACV,KAAK,CAACtB,MAAM,CAAC,CAAC;EAClE,CAAC;EACDkD,UAAU,EAAE7B;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA1B,OAAO,CAACyD,WAAW,GAAG,UAAUC,UAAU,EAAE/B,KAAK,EAAE;EACjD,IAAI,CAACA,KAAK,EAAE;IACV;IACA;IACAA,KAAK,GAAG,EAAE;EACZ;EACA,IAAI,CAAC+B,UAAU,EAAE;IACf,OAAO/B,KAAK;EACd;EACA3B,OAAO,CAACY,YAAY,CAAC8C,UAAU,CAAC;EAChC,IAAIC,SAAS,GAAG3D,OAAO,CAACiC,SAAS,CAACN,KAAK,CAAC;EACxC,IAAIxB,MAAM,GAAG+C,SAAS,CAACS,SAAS,CAAC,CAACD,UAAU,CAAC,CAAC/B,KAAK,CAAC;EACpD,OAAOxB,MAAM;AACf,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAH,OAAO,CAACiC,SAAS,GAAG,UAAUN,KAAK,EAAE;EACnC,IAAIA,KAAK,IAAI,IAAI,EAAE;IACjB;EACF;EACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,OAAO,QAAQ;EACjB;EACA,IAAIiC,WAAW,GAAGC,MAAM,CAAClE,SAAS,CAACmE,QAAQ,CAACC,IAAI,CAACpC,KAAK,CAAC;EACvD,IAAIiC,WAAW,KAAK,gBAAgB,EAAE;IACpC,OAAO,OAAO;EAChB;EACA,IAAIhE,OAAO,CAAC2D,UAAU,IAAIxD,UAAU,CAACiE,IAAI,CAACrC,KAAK,CAAC,EAAE;IAChD,OAAO,YAAY;EACrB;EACA,IAAI/B,OAAO,CAAC0D,UAAU,IAAIM,WAAW,KAAK,qBAAqB,EAAE;IAC/D,OAAO,YAAY;EACrB;EACA,IAAIhE,OAAO,CAACyD,WAAW,IAAIO,WAAW,KAAK,sBAAsB,EAAE;IACjE,OAAO,aAAa;EACtB;EACA,IAAIA,WAAW,KAAK,kBAAkB,EAAE;IACtC,MAAM,IAAInC,KAAK,CAAC,2FAA2F,CAAC;EAC9G;EACA,IAAInC,OAAO,CAACqC,KAAK,CAAC,KAAK,QAAQ,IAAI,OAAOA,KAAK,CAACsC,IAAI,KAAK,UAAU,EAAE;IACnE,MAAM,IAAIxC,KAAK,CAAC,sGAAsG,CAAC;EACzH;EACA,IAAImC,WAAW,KAAK,eAAe,EAAE;IACnC,MAAM,IAAInC,KAAK,CAAC,qFAAqF,CAAC;EACxG;EACA,IAAInC,OAAO,CAACqC,KAAK,CAAC,KAAK,QAAQ,IAAIA,KAAK,CAACuC,KAAK,IAAI,IAAI,EAAE;IACtD,MAAM,IAAIzC,KAAK,CAAC,2DAA2D,CAAC;EAC9E;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACAzB,OAAO,CAACY,YAAY,GAAG,UAAUE,IAAI,EAAE;EACrC,IAAIqD,SAAS,GAAGvE,OAAO,CAACkB,IAAI,CAACsD,WAAW,CAAC,CAAC,CAAC;EAC3C,IAAI,CAACD,SAAS,EAAE;IACd,MAAM,IAAI1C,KAAK,CAACX,IAAI,GAAG,mCAAmC,CAAC;EAC7D;AACF,CAAC;AACDd,OAAO,CAACqE,gBAAgB,GAAG,KAAK;AAChCrE,OAAO,CAACsE,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAE/B;AACA;AACA;AACA;AACA;AACAtE,OAAO,CAACuE,MAAM,GAAG,UAAUrE,GAAG,EAAE;EAC9B,IAAIsE,GAAG,GAAG,EAAE;IACVC,IAAI;IACJrE,CAAC;EACH,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAACF,GAAG,IAAI,EAAE,EAAEG,MAAM,EAAED,CAAC,EAAE,EAAE;IACvCqE,IAAI,GAAGvE,GAAG,CAACM,UAAU,CAACJ,CAAC,CAAC;IACxBoE,GAAG,IAAI,KAAK,IAAIC,IAAI,GAAG,EAAE,GAAG,GAAG,GAAG,EAAE,CAAC,GAAGA,IAAI,CAACX,QAAQ,CAAC,EAAE,CAAC,CAACY,WAAW,CAAC,CAAC;EACzE;EACA,OAAOF,GAAG;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACAxE,OAAO,CAAC2E,eAAe,GAAG,UAAUC,iBAAiB,EAAE;EACrD,KAAK,IAAIC,MAAM,IAAI/E,YAAY,EAAE;IAC/B,IAAI,CAACA,YAAY,CAACgF,cAAc,CAACD,MAAM,CAAC,EAAE;MACxC;IACF;IACA,IAAI/E,YAAY,CAAC+E,MAAM,CAAC,CAACE,KAAK,KAAKH,iBAAiB,EAAE;MACpD,OAAO9E,YAAY,CAAC+E,MAAM,CAAC;IAC7B;EACF;EACA,OAAO,IAAI;AACb,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA7E,OAAO,CAACgF,QAAQ,GAAG,UAAUC,MAAM,EAAE;EACnC,OAAOpB,MAAM,CAAClE,SAAS,CAACmE,QAAQ,CAACC,IAAI,CAACkB,MAAM,CAAC,KAAK,iBAAiB;AACrE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAjF,OAAO,CAACkF,MAAM,GAAG,YAAY;EAC3B,IAAI/E,MAAM,GAAG,CAAC,CAAC;EACf,IAAIC,CAAC,EAAE+E,IAAI;EACX,KAAK/E,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgF,SAAS,CAAC/E,MAAM,EAAED,CAAC,EAAE,EAAE;IACrC;IACA,KAAK+E,IAAI,IAAIC,SAAS,CAAChF,CAAC,CAAC,EAAE;MACzB,IAAIgF,SAAS,CAAChF,CAAC,CAAC,CAAC0E,cAAc,CAACK,IAAI,CAAC,IAAI,OAAOhF,MAAM,CAACgF,IAAI,CAAC,KAAK,WAAW,EAAE;QAC5EhF,MAAM,CAACgF,IAAI,CAAC,GAAGC,SAAS,CAAChF,CAAC,CAAC,CAAC+E,IAAI,CAAC;MACnC;IACF;EACF;EACA,OAAOhF,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}