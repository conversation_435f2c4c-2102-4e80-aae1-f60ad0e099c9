{"ast": null, "code": "export default {\n  name: 'ProposalAuxiliaryWriting'\n};", "map": {"version": 3, "names": ["name"], "sources": ["D:\\zy\\xm\\pc\\5.0\\qiankun-product\\customize\\AiToolBoxFunction\\ProposalAuxiliaryWriting\\ProposalAuxiliaryWriting.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ProposalAuxiliaryWriting\">文本识别</div>\r\n</template>\r\n<script>\r\nexport default { name: 'ProposalAuxiliaryWriting' }\r\n</script>\r\n\r\n<script setup></script>\r\n<style lang=\"scss\">\r\n.ProposalAuxiliaryWriting {\r\n  width: 100%;\r\n  height: 100%;\r\n  padding: 0 20px;\r\n  background: #f3f5f7;\r\n}\r\n</style>\r\n"], "mappings": "AAIA,eAAe;EAAEA,IAAI,EAAE;AAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}