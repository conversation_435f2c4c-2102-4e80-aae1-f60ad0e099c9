{"ast": null, "code": "import dayjs from 'dayjs';\nimport '../../../utils/index.mjs';\nimport { isDate, isArray } from '@vue/shared';\nimport { isEmpty } from '../../../utils/types.mjs';\nvar buildTimeList = function buildTimeList(value, bound) {\n  return [value > 0 ? value - 1 : void 0, value, value < bound ? value + 1 : void 0];\n};\nvar rangeArr = function rangeArr(n) {\n  return Array.from(Array.from({\n    length: n\n  }).keys());\n};\nvar extractDateFormat = function extractDateFormat(format) {\n  return format.replace(/\\W?m{1,2}|\\W?ZZ/g, \"\").replace(/\\W?h{1,2}|\\W?s{1,3}|\\W?a/gi, \"\").trim();\n};\nvar extractTimeFormat = function extractTimeFormat(format) {\n  return format.replace(/\\W?D{1,2}|\\W?Do|\\W?d{1,4}|\\W?M{1,4}|\\W?Y{2,4}/g, \"\").trim();\n};\nvar dateEquals = function dateEquals(a, b) {\n  var aIsDate = isDate(a);\n  var bIsDate = isDate(b);\n  if (aIsDate && bIsDate) {\n    return a.getTime() === b.getTime();\n  }\n  if (!aIsDate && !bIsDate) {\n    return a === b;\n  }\n  return false;\n};\nvar valueEquals = function valueEquals(a, b) {\n  var aIsArray = isArray(a);\n  var bIsArray = isArray(b);\n  if (aIsArray && bIsArray) {\n    if (a.length !== b.length) {\n      return false;\n    }\n    return a.every(function (item, index) {\n      return dateEquals(item, b[index]);\n    });\n  }\n  if (!aIsArray && !bIsArray) {\n    return dateEquals(a, b);\n  }\n  return false;\n};\nvar parseDate = function parseDate(date, format, lang) {\n  var day = isEmpty(format) || format === \"x\" ? dayjs(date).locale(lang) : dayjs(date, format).locale(lang);\n  return day.isValid() ? day : void 0;\n};\nvar formatter = function formatter(date, format, lang) {\n  if (isEmpty(format)) return date;\n  if (format === \"x\") return +date;\n  return dayjs(date).locale(lang).format(format);\n};\nvar makeList = function makeList(total, method) {\n  var _a;\n  var arr = [];\n  var disabledArr = method == null ? void 0 : method();\n  for (var i = 0; i < total; i++) {\n    arr.push((_a = disabledArr == null ? void 0 : disabledArr.includes(i)) != null ? _a : false);\n  }\n  return arr;\n};\nexport { buildTimeList, dateEquals, extractDateFormat, extractTimeFormat, formatter, makeList, parseDate, rangeArr, valueEquals };", "map": {"version": 3, "names": ["buildTimeList", "value", "bound", "rangeArr", "n", "Array", "from", "length", "keys", "extractDateFormat", "format", "replace", "trim", "extractTimeFormat", "dateEquals", "a", "b", "aIsDate", "isDate", "bIsDate", "getTime", "valueEquals", "aIsArray", "isArray", "bIsArray", "every", "item", "index", "parseDate", "date", "lang", "day", "isEmpty", "dayjs", "locale", "<PERSON><PERSON><PERSON><PERSON>", "formatter", "makeList", "total", "method", "_a", "arr", "disabledArr", "i", "push", "includes"], "sources": ["../../../../../../packages/components/time-picker/src/utils.ts"], "sourcesContent": ["import dayjs from 'dayjs'\nimport { isArray, isDate, isEmpty } from '@element-plus/utils'\n\nimport type { Dayjs } from 'dayjs'\nexport type TimeList = [number | undefined, number, undefined | number]\n\nexport const buildTimeList = (value: number, bound: number): TimeList => {\n  return [\n    value > 0 ? value - 1 : undefined,\n    value,\n    value < bound ? value + 1 : undefined,\n  ]\n}\n\nexport const rangeArr = (n: number) =>\n  Array.from(Array.from({ length: n }).keys())\n\nexport const extractDateFormat = (format: string) => {\n  return format\n    .replace(/\\W?m{1,2}|\\W?ZZ/g, '')\n    .replace(/\\W?h{1,2}|\\W?s{1,3}|\\W?a/gi, '')\n    .trim()\n}\n\nexport const extractTimeFormat = (format: string) => {\n  return format\n    .replace(/\\W?D{1,2}|\\W?Do|\\W?d{1,4}|\\W?M{1,4}|\\W?Y{2,4}/g, '')\n    .trim()\n}\n\nexport const dateEquals = function (a: Date | unknown, b: Date | unknown) {\n  const aIsDate = isDate(a)\n  const bIsDate = isDate(b)\n  if (aIsDate && bIsDate) {\n    return a.getTime() === b.getTime()\n  }\n  if (!aIsDate && !bIsDate) {\n    return a === b\n  }\n  return false\n}\n\nexport const valueEquals = function (\n  a: Array<Date> | unknown,\n  b: Array<Date> | unknown\n) {\n  const aIsArray = isArray(a)\n  const bIsArray = isArray(b)\n  if (aIsArray && bIsArray) {\n    if (a.length !== b.length) {\n      return false\n    }\n    return a.every((item, index) => dateEquals(item, b[index]))\n  }\n  if (!aIsArray && !bIsArray) {\n    return dateEquals(a, b)\n  }\n  return false\n}\n\nexport const parseDate = function (\n  date: string | number | Date,\n  format: string | undefined,\n  lang: string\n) {\n  const day =\n    isEmpty(format) || format === 'x'\n      ? dayjs(date).locale(lang)\n      : dayjs(date, format).locale(lang)\n  return day.isValid() ? day : undefined\n}\n\nexport const formatter = function (\n  date: string | number | Date | Dayjs,\n  format: string | undefined,\n  lang: string\n) {\n  if (isEmpty(format)) return date\n  if (format === 'x') return +date\n  return dayjs(date).locale(lang).format(format)\n}\n\nexport const makeList = (total: number, method?: () => number[]) => {\n  const arr: boolean[] = []\n  const disabledArr = method?.()\n  for (let i = 0; i < total; i++) {\n    arr.push(disabledArr?.includes(i) ?? false)\n  }\n  return arr\n}\n"], "mappings": ";;;;AAEY,IAACA,aAAa,GAAG,SAAhBA,aAAaA,CAAIC,KAAK,EAAEC,KAAK,EAAK;EAC7C,OAAO,CACLD,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,EAC9BA,KAAK,EACLA,KAAK,GAAGC,KAAK,GAAGD,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,CACnC;AACH;AACY,IAACE,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,CAAC;EAAA,OAAKC,KAAK,CAACC,IAAI,CAACD,KAAK,CAACC,IAAI,CAAC;IAAEC,MAAM,EAAEH;EAAC,CAAE,CAAC,CAACI,IAAI,EAAE;AAAA;AAC9D,IAACC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIC,MAAM,EAAK;EAC3C,OAAOA,MAAM,CAACC,OAAO,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAACA,OAAO,CAAC,4BAA4B,EAAE,EAAE,CAAC,CAACC,IAAI,EAAE;AAChG;AACY,IAACC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAIH,MAAM,EAAK;EAC3C,OAAOA,MAAM,CAACC,OAAO,CAAC,gDAAgD,EAAE,EAAE,CAAC,CAACC,IAAI,EAAE;AACpF;AACY,IAACE,UAAU,GAAG,SAAbA,UAAUA,CAAYC,CAAC,EAAEC,CAAC,EAAE;EACvC,IAAMC,OAAO,GAAGC,MAAM,CAACH,CAAC,CAAC;EACzB,IAAMI,OAAO,GAAGD,MAAM,CAACF,CAAC,CAAC;EACzB,IAAIC,OAAO,IAAIE,OAAO,EAAE;IACtB,OAAOJ,CAAC,CAACK,OAAO,EAAE,KAAKJ,CAAC,CAACI,OAAO,EAAE;EACtC;EACE,IAAI,CAACH,OAAO,IAAI,CAACE,OAAO,EAAE;IACxB,OAAOJ,CAAC,KAAKC,CAAC;EAClB;EACE,OAAO,KAAK;AACd;AACY,IAACK,WAAW,GAAG,SAAdA,WAAWA,CAAYN,CAAC,EAAEC,CAAC,EAAE;EACxC,IAAMM,QAAQ,GAAGC,OAAO,CAACR,CAAC,CAAC;EAC3B,IAAMS,QAAQ,GAAGD,OAAO,CAACP,CAAC,CAAC;EAC3B,IAAIM,QAAQ,IAAIE,QAAQ,EAAE;IACxB,IAAIT,CAAC,CAACR,MAAM,KAAKS,CAAC,CAACT,MAAM,EAAE;MACzB,OAAO,KAAK;IAClB;IACI,OAAOQ,CAAC,CAACU,KAAK,CAAC,UAACC,IAAI,EAAEC,KAAK;MAAA,OAAKb,UAAU,CAACY,IAAI,EAAEV,CAAC,CAACW,KAAK,CAAC,CAAC;IAAA,EAAC;EAC/D;EACE,IAAI,CAACL,QAAQ,IAAI,CAACE,QAAQ,EAAE;IAC1B,OAAOV,UAAU,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC3B;EACE,OAAO,KAAK;AACd;AACY,IAACY,SAAS,GAAG,SAAZA,SAASA,CAAYC,IAAI,EAAEnB,MAAM,EAAEoB,IAAI,EAAE;EACpD,IAAMC,GAAG,GAAGC,OAAO,CAACtB,MAAM,CAAC,IAAIA,MAAM,KAAK,GAAG,GAAGuB,KAAK,CAACJ,IAAI,CAAC,CAACK,MAAM,CAACJ,IAAI,CAAC,GAAGG,KAAK,CAACJ,IAAI,EAAEnB,MAAM,CAAC,CAACwB,MAAM,CAACJ,IAAI,CAAC;EAC3G,OAAOC,GAAG,CAACI,OAAO,EAAE,GAAGJ,GAAG,GAAG,KAAK,CAAC;AACrC;AACY,IAACK,SAAS,GAAG,SAAZA,SAASA,CAAYP,IAAI,EAAEnB,MAAM,EAAEoB,IAAI,EAAE;EACpD,IAAIE,OAAO,CAACtB,MAAM,CAAC,EACjB,OAAOmB,IAAI;EACb,IAAInB,MAAM,KAAK,GAAG,EAChB,OAAO,CAACmB,IAAI;EACd,OAAOI,KAAK,CAACJ,IAAI,CAAC,CAACK,MAAM,CAACJ,IAAI,CAAC,CAACpB,MAAM,CAACA,MAAM,CAAC;AAChD;AACY,IAAC2B,QAAQ,GAAG,SAAXA,QAAQA,CAAIC,KAAK,EAAEC,MAAM,EAAK;EACzC,IAAIC,EAAE;EACN,IAAMC,GAAG,GAAG,EAAE;EACd,IAAMC,WAAW,GAAGH,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,EAAE;EACtD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,EAAEK,CAAC,EAAE,EAAE;IAC9BF,GAAG,CAACG,IAAI,CAAC,CAACJ,EAAE,GAAGE,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACG,QAAQ,CAACF,CAAC,CAAC,KAAK,IAAI,GAAGH,EAAE,GAAG,KAAK,CAAC;EAChG;EACE,OAAOC,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}