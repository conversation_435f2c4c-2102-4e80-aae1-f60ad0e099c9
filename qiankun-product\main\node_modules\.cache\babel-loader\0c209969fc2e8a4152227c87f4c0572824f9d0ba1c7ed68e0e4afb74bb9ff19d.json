{"ast": null, "code": "'use strict';\n\nmodule.exports = function inline(state) {\n  var tokens = state.tokens,\n    tok,\n    i,\n    l;\n\n  // Parse inlines\n  for (i = 0, l = tokens.length; i < l; i++) {\n    tok = tokens[i];\n    if (tok.type === 'inline') {\n      state.md.inline.parse(tok.content, state.md, state.env, tok.children);\n    }\n  }\n};", "map": {"version": 3, "names": ["module", "exports", "inline", "state", "tokens", "tok", "i", "l", "length", "type", "md", "parse", "content", "env", "children"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/markdown-it@12.3.2/node_modules/markdown-it/lib/rules_core/inline.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = function inline(state) {\n  var tokens = state.tokens, tok, i, l;\n\n  // Parse inlines\n  for (i = 0, l = tokens.length; i < l; i++) {\n    tok = tokens[i];\n    if (tok.type === 'inline') {\n      state.md.inline.parse(tok.content, state.md, state.env, tok.children);\n    }\n  }\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG,SAASC,MAAMA,CAACC,KAAK,EAAE;EACtC,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;IAAEC,GAAG;IAAEC,CAAC;IAAEC,CAAC;;EAEpC;EACA,KAAKD,CAAC,GAAG,CAAC,EAAEC,CAAC,GAAGH,MAAM,CAACI,MAAM,EAAEF,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;IACzCD,GAAG,GAAGD,MAAM,CAACE,CAAC,CAAC;IACf,IAAID,GAAG,CAACI,IAAI,KAAK,QAAQ,EAAE;MACzBN,KAAK,CAACO,EAAE,CAACR,MAAM,CAACS,KAAK,CAACN,GAAG,CAACO,OAAO,EAAET,KAAK,CAACO,EAAE,EAAEP,KAAK,CAACU,GAAG,EAAER,GAAG,CAACS,QAAQ,CAAC;IACvE;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}