{"ast": null, "code": "\"use strict\";\n\nvar utils = require(\"./utils.js\");\nvar support = require(\"./support.js\");\nvar nodeBuffer = require(\"./nodeBuffer.js\");\n\n/**\n * The following functions come from pako, from pako/lib/utils/strings\n * released under the MIT license, see pako https://github.com/nodeca/pako/\n */\n\n// Table with utf8 lengths (calculated by first byte of sequence)\n// Note, that 5 & 6-byte values and some 4-byte values can not be represented in JS,\n// because max possible codepoint is 0x10ffff\nvar _utf8len = new Array(256);\nfor (var i = 0; i < 256; i++) {\n  _utf8len[i] = i >= 252 ? 6 : i >= 248 ? 5 : i >= 240 ? 4 : i >= 224 ? 3 : i >= 192 ? 2 : 1;\n}\n_utf8len[254] = _utf8len[254] = 1; // Invalid sequence start\n\n// convert string to array (typed, when possible)\nfunction string2buf(str) {\n  var buf,\n    c,\n    c2,\n    mPos,\n    i,\n    bufLen = 0;\n  var strLen = str.length;\n\n  // count binary size\n  for (mPos = 0; mPos < strLen; mPos++) {\n    c = str.charCodeAt(mPos);\n    if ((c & 0xfc00) === 0xd800 && mPos + 1 < strLen) {\n      c2 = str.charCodeAt(mPos + 1);\n      if ((c2 & 0xfc00) === 0xdc00) {\n        c = 0x10000 + (c - 0xd800 << 10) + (c2 - 0xdc00);\n        mPos++;\n      }\n    }\n    bufLen += c < 0x80 ? 1 : c < 0x800 ? 2 : c < 0x10000 ? 3 : 4;\n  }\n\n  // allocate buffer\n  if (support.uint8array) {\n    buf = new Uint8Array(bufLen);\n  } else {\n    buf = new Array(bufLen);\n  }\n\n  // convert\n  for (i = 0, mPos = 0; i < bufLen; mPos++) {\n    c = str.charCodeAt(mPos);\n    if ((c & 0xfc00) === 0xd800 && mPos + 1 < strLen) {\n      c2 = str.charCodeAt(mPos + 1);\n      if ((c2 & 0xfc00) === 0xdc00) {\n        c = 0x10000 + (c - 0xd800 << 10) + (c2 - 0xdc00);\n        mPos++;\n      }\n    }\n    if (c < 0x80) {\n      /* one byte */\n      buf[i++] = c;\n    } else if (c < 0x800) {\n      /* two bytes */\n      buf[i++] = 0xc0 | c >>> 6;\n      buf[i++] = 0x80 | c & 0x3f;\n    } else if (c < 0x10000) {\n      /* three bytes */\n      buf[i++] = 0xe0 | c >>> 12;\n      buf[i++] = 0x80 | c >>> 6 & 0x3f;\n      buf[i++] = 0x80 | c & 0x3f;\n    } else {\n      /* four bytes */\n      buf[i++] = 0xf0 | c >>> 18;\n      buf[i++] = 0x80 | c >>> 12 & 0x3f;\n      buf[i++] = 0x80 | c >>> 6 & 0x3f;\n      buf[i++] = 0x80 | c & 0x3f;\n    }\n  }\n  return buf;\n}\n\n// Calculate max possible position in utf8 buffer,\n// that will not break sequence. If that's not possible\n// - (very small limits) return max size as is.\n//\n// buf[] - utf8 bytes array\n// max   - length limit (mandatory);\nfunction utf8border(buf, max) {\n  var pos;\n  max = max || buf.length;\n  if (max > buf.length) {\n    max = buf.length;\n  }\n\n  // go back from last position, until start of sequence found\n  pos = max - 1;\n  while (pos >= 0 && (buf[pos] & 0xc0) === 0x80) {\n    pos--;\n  }\n\n  // Fuckup - very small and broken sequence,\n  // return max, because we should return something anyway.\n  if (pos < 0) {\n    return max;\n  }\n\n  // If we came to start of buffer - that means vuffer is too small,\n  // return max too.\n  if (pos === 0) {\n    return max;\n  }\n  return pos + _utf8len[buf[pos]] > max ? pos : max;\n}\n\n// convert array to string\nfunction buf2string(buf) {\n  var i, out, c, cLen;\n  var len = buf.length;\n\n  // Reserve max possible length (2 words per char)\n  // NB: by unknown reasons, Array is significantly faster for\n  //     String.fromCharCode.apply than Uint16Array.\n  var utf16buf = new Array(len * 2);\n  for (out = 0, i = 0; i < len;) {\n    c = buf[i++];\n    // quick process ascii\n    if (c < 0x80) {\n      utf16buf[out++] = c;\n      continue;\n    }\n    cLen = _utf8len[c];\n    // skip 5 & 6 byte codes\n    if (cLen > 4) {\n      utf16buf[out++] = 0xfffd;\n      i += cLen - 1;\n      continue;\n    }\n\n    // apply mask on first byte\n    c &= cLen === 2 ? 0x1f : cLen === 3 ? 0x0f : 0x07;\n    // join the rest\n    while (cLen > 1 && i < len) {\n      c = c << 6 | buf[i++] & 0x3f;\n      cLen--;\n    }\n\n    // terminated by end of string?\n    if (cLen > 1) {\n      utf16buf[out++] = 0xfffd;\n      continue;\n    }\n    if (c < 0x10000) {\n      utf16buf[out++] = c;\n    } else {\n      c -= 0x10000;\n      utf16buf[out++] = 0xd800 | c >> 10 & 0x3ff;\n      utf16buf[out++] = 0xdc00 | c & 0x3ff;\n    }\n  }\n\n  // shrinkBuf(utf16buf, out)\n  if (utf16buf.length !== out) {\n    if (utf16buf.subarray) {\n      utf16buf = utf16buf.subarray(0, out);\n    } else {\n      utf16buf.length = out;\n    }\n  }\n\n  // return String.fromCharCode.apply(null, utf16buf);\n  return utils.applyFromCharCode(utf16buf);\n}\n\n// That's all for the pako functions.\n\n/**\n * Transform a javascript string into an array (typed if possible) of bytes,\n * UTF-8 encoded.\n * @param {String} str the string to encode\n * @return {Array|Uint8Array|Buffer} the UTF-8 encoded string.\n */\nexports.utf8encode = function utf8encode(str) {\n  if (support.nodebuffer) {\n    return nodeBuffer(str, \"utf-8\");\n  }\n  return string2buf(str);\n};\n\n/**\n * Transform a bytes array (or a representation) representing an UTF-8 encoded\n * string into a javascript string.\n * @param {Array|Uint8Array|Buffer} buf the data de decode\n * @return {String} the decoded string.\n */\nexports.utf8decode = function utf8decode(buf) {\n  if (support.nodebuffer) {\n    return utils.transformTo(\"nodebuffer\", buf).toString(\"utf-8\");\n  }\n  buf = utils.transformTo(support.uint8array ? \"uint8array\" : \"array\", buf);\n\n  // return buf2string(buf);\n  // Chrome prefers to work with \"small\" chunks of data\n  // for the method buf2string.\n  // Firefox and Chrome has their own shortcut, IE doesn't seem to really care.\n  var result = [],\n    len = buf.length,\n    chunk = 65536;\n  var k = 0;\n  while (k < len) {\n    var nextBoundary = utf8border(buf, Math.min(k + chunk, len));\n    if (support.uint8array) {\n      result.push(buf2string(buf.subarray(k, nextBoundary)));\n    } else {\n      result.push(buf2string(buf.slice(k, nextBoundary)));\n    }\n    k = nextBoundary;\n  }\n  return result.join(\"\");\n};", "map": {"version": 3, "names": ["utils", "require", "support", "node<PERSON>uff<PERSON>", "_utf8len", "Array", "i", "string2buf", "str", "buf", "c", "c2", "mPos", "bufLen", "strLen", "length", "charCodeAt", "uint8array", "Uint8Array", "utf8border", "max", "pos", "buf2string", "out", "cLen", "len", "utf16buf", "subarray", "applyFromCharCode", "exports", "utf8encode", "nodebuffer", "utf8decode", "transformTo", "toString", "result", "chunk", "k", "nextBoundary", "Math", "min", "push", "slice", "join"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/pizzip@3.1.7/node_modules/pizzip/js/utf8.js"], "sourcesContent": ["\"use strict\";\n\nvar utils = require(\"./utils.js\");\nvar support = require(\"./support.js\");\nvar nodeBuffer = require(\"./nodeBuffer.js\");\n\n/**\n * The following functions come from pako, from pako/lib/utils/strings\n * released under the MIT license, see pako https://github.com/nodeca/pako/\n */\n\n// Table with utf8 lengths (calculated by first byte of sequence)\n// Note, that 5 & 6-byte values and some 4-byte values can not be represented in JS,\n// because max possible codepoint is 0x10ffff\nvar _utf8len = new Array(256);\nfor (var i = 0; i < 256; i++) {\n  _utf8len[i] = i >= 252 ? 6 : i >= 248 ? 5 : i >= 240 ? 4 : i >= 224 ? 3 : i >= 192 ? 2 : 1;\n}\n_utf8len[254] = _utf8len[254] = 1; // Invalid sequence start\n\n// convert string to array (typed, when possible)\nfunction string2buf(str) {\n  var buf,\n    c,\n    c2,\n    mPos,\n    i,\n    bufLen = 0;\n  var strLen = str.length;\n\n  // count binary size\n  for (mPos = 0; mPos < strLen; mPos++) {\n    c = str.charCodeAt(mPos);\n    if ((c & 0xfc00) === 0xd800 && mPos + 1 < strLen) {\n      c2 = str.charCodeAt(mPos + 1);\n      if ((c2 & 0xfc00) === 0xdc00) {\n        c = 0x10000 + (c - 0xd800 << 10) + (c2 - 0xdc00);\n        mPos++;\n      }\n    }\n    bufLen += c < 0x80 ? 1 : c < 0x800 ? 2 : c < 0x10000 ? 3 : 4;\n  }\n\n  // allocate buffer\n  if (support.uint8array) {\n    buf = new Uint8Array(bufLen);\n  } else {\n    buf = new Array(bufLen);\n  }\n\n  // convert\n  for (i = 0, mPos = 0; i < bufLen; mPos++) {\n    c = str.charCodeAt(mPos);\n    if ((c & 0xfc00) === 0xd800 && mPos + 1 < strLen) {\n      c2 = str.charCodeAt(mPos + 1);\n      if ((c2 & 0xfc00) === 0xdc00) {\n        c = 0x10000 + (c - 0xd800 << 10) + (c2 - 0xdc00);\n        mPos++;\n      }\n    }\n    if (c < 0x80) {\n      /* one byte */\n      buf[i++] = c;\n    } else if (c < 0x800) {\n      /* two bytes */\n      buf[i++] = 0xc0 | c >>> 6;\n      buf[i++] = 0x80 | c & 0x3f;\n    } else if (c < 0x10000) {\n      /* three bytes */\n      buf[i++] = 0xe0 | c >>> 12;\n      buf[i++] = 0x80 | c >>> 6 & 0x3f;\n      buf[i++] = 0x80 | c & 0x3f;\n    } else {\n      /* four bytes */\n      buf[i++] = 0xf0 | c >>> 18;\n      buf[i++] = 0x80 | c >>> 12 & 0x3f;\n      buf[i++] = 0x80 | c >>> 6 & 0x3f;\n      buf[i++] = 0x80 | c & 0x3f;\n    }\n  }\n  return buf;\n}\n\n// Calculate max possible position in utf8 buffer,\n// that will not break sequence. If that's not possible\n// - (very small limits) return max size as is.\n//\n// buf[] - utf8 bytes array\n// max   - length limit (mandatory);\nfunction utf8border(buf, max) {\n  var pos;\n  max = max || buf.length;\n  if (max > buf.length) {\n    max = buf.length;\n  }\n\n  // go back from last position, until start of sequence found\n  pos = max - 1;\n  while (pos >= 0 && (buf[pos] & 0xc0) === 0x80) {\n    pos--;\n  }\n\n  // Fuckup - very small and broken sequence,\n  // return max, because we should return something anyway.\n  if (pos < 0) {\n    return max;\n  }\n\n  // If we came to start of buffer - that means vuffer is too small,\n  // return max too.\n  if (pos === 0) {\n    return max;\n  }\n  return pos + _utf8len[buf[pos]] > max ? pos : max;\n}\n\n// convert array to string\nfunction buf2string(buf) {\n  var i, out, c, cLen;\n  var len = buf.length;\n\n  // Reserve max possible length (2 words per char)\n  // NB: by unknown reasons, Array is significantly faster for\n  //     String.fromCharCode.apply than Uint16Array.\n  var utf16buf = new Array(len * 2);\n  for (out = 0, i = 0; i < len;) {\n    c = buf[i++];\n    // quick process ascii\n    if (c < 0x80) {\n      utf16buf[out++] = c;\n      continue;\n    }\n    cLen = _utf8len[c];\n    // skip 5 & 6 byte codes\n    if (cLen > 4) {\n      utf16buf[out++] = 0xfffd;\n      i += cLen - 1;\n      continue;\n    }\n\n    // apply mask on first byte\n    c &= cLen === 2 ? 0x1f : cLen === 3 ? 0x0f : 0x07;\n    // join the rest\n    while (cLen > 1 && i < len) {\n      c = c << 6 | buf[i++] & 0x3f;\n      cLen--;\n    }\n\n    // terminated by end of string?\n    if (cLen > 1) {\n      utf16buf[out++] = 0xfffd;\n      continue;\n    }\n    if (c < 0x10000) {\n      utf16buf[out++] = c;\n    } else {\n      c -= 0x10000;\n      utf16buf[out++] = 0xd800 | c >> 10 & 0x3ff;\n      utf16buf[out++] = 0xdc00 | c & 0x3ff;\n    }\n  }\n\n  // shrinkBuf(utf16buf, out)\n  if (utf16buf.length !== out) {\n    if (utf16buf.subarray) {\n      utf16buf = utf16buf.subarray(0, out);\n    } else {\n      utf16buf.length = out;\n    }\n  }\n\n  // return String.fromCharCode.apply(null, utf16buf);\n  return utils.applyFromCharCode(utf16buf);\n}\n\n// That's all for the pako functions.\n\n/**\n * Transform a javascript string into an array (typed if possible) of bytes,\n * UTF-8 encoded.\n * @param {String} str the string to encode\n * @return {Array|Uint8Array|Buffer} the UTF-8 encoded string.\n */\nexports.utf8encode = function utf8encode(str) {\n  if (support.nodebuffer) {\n    return nodeBuffer(str, \"utf-8\");\n  }\n  return string2buf(str);\n};\n\n/**\n * Transform a bytes array (or a representation) representing an UTF-8 encoded\n * string into a javascript string.\n * @param {Array|Uint8Array|Buffer} buf the data de decode\n * @return {String} the decoded string.\n */\nexports.utf8decode = function utf8decode(buf) {\n  if (support.nodebuffer) {\n    return utils.transformTo(\"nodebuffer\", buf).toString(\"utf-8\");\n  }\n  buf = utils.transformTo(support.uint8array ? \"uint8array\" : \"array\", buf);\n\n  // return buf2string(buf);\n  // Chrome prefers to work with \"small\" chunks of data\n  // for the method buf2string.\n  // Firefox and Chrome has their own shortcut, IE doesn't seem to really care.\n  var result = [],\n    len = buf.length,\n    chunk = 65536;\n  var k = 0;\n  while (k < len) {\n    var nextBoundary = utf8border(buf, Math.min(k + chunk, len));\n    if (support.uint8array) {\n      result.push(buf2string(buf.subarray(k, nextBoundary)));\n    } else {\n      result.push(buf2string(buf.slice(k, nextBoundary)));\n    }\n    k = nextBoundary;\n  }\n  return result.join(\"\");\n};"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,YAAY,CAAC;AACjC,IAAIC,OAAO,GAAGD,OAAO,CAAC,cAAc,CAAC;AACrC,IAAIE,UAAU,GAAGF,OAAO,CAAC,iBAAiB,CAAC;;AAE3C;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,IAAIG,QAAQ,GAAG,IAAIC,KAAK,CAAC,GAAG,CAAC;AAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;EAC5BF,QAAQ,CAACE,CAAC,CAAC,GAAGA,CAAC,IAAI,GAAG,GAAG,CAAC,GAAGA,CAAC,IAAI,GAAG,GAAG,CAAC,GAAGA,CAAC,IAAI,GAAG,GAAG,CAAC,GAAGA,CAAC,IAAI,GAAG,GAAG,CAAC,GAAGA,CAAC,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC;AAC5F;AACAF,QAAQ,CAAC,GAAG,CAAC,GAAGA,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;;AAEnC;AACA,SAASG,UAAUA,CAACC,GAAG,EAAE;EACvB,IAAIC,GAAG;IACLC,CAAC;IACDC,EAAE;IACFC,IAAI;IACJN,CAAC;IACDO,MAAM,GAAG,CAAC;EACZ,IAAIC,MAAM,GAAGN,GAAG,CAACO,MAAM;;EAEvB;EACA,KAAKH,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGE,MAAM,EAAEF,IAAI,EAAE,EAAE;IACpCF,CAAC,GAAGF,GAAG,CAACQ,UAAU,CAACJ,IAAI,CAAC;IACxB,IAAI,CAACF,CAAC,GAAG,MAAM,MAAM,MAAM,IAAIE,IAAI,GAAG,CAAC,GAAGE,MAAM,EAAE;MAChDH,EAAE,GAAGH,GAAG,CAACQ,UAAU,CAACJ,IAAI,GAAG,CAAC,CAAC;MAC7B,IAAI,CAACD,EAAE,GAAG,MAAM,MAAM,MAAM,EAAE;QAC5BD,CAAC,GAAG,OAAO,IAAIA,CAAC,GAAG,MAAM,IAAI,EAAE,CAAC,IAAIC,EAAE,GAAG,MAAM,CAAC;QAChDC,IAAI,EAAE;MACR;IACF;IACAC,MAAM,IAAIH,CAAC,GAAG,IAAI,GAAG,CAAC,GAAGA,CAAC,GAAG,KAAK,GAAG,CAAC,GAAGA,CAAC,GAAG,OAAO,GAAG,CAAC,GAAG,CAAC;EAC9D;;EAEA;EACA,IAAIR,OAAO,CAACe,UAAU,EAAE;IACtBR,GAAG,GAAG,IAAIS,UAAU,CAACL,MAAM,CAAC;EAC9B,CAAC,MAAM;IACLJ,GAAG,GAAG,IAAIJ,KAAK,CAACQ,MAAM,CAAC;EACzB;;EAEA;EACA,KAAKP,CAAC,GAAG,CAAC,EAAEM,IAAI,GAAG,CAAC,EAAEN,CAAC,GAAGO,MAAM,EAAED,IAAI,EAAE,EAAE;IACxCF,CAAC,GAAGF,GAAG,CAACQ,UAAU,CAACJ,IAAI,CAAC;IACxB,IAAI,CAACF,CAAC,GAAG,MAAM,MAAM,MAAM,IAAIE,IAAI,GAAG,CAAC,GAAGE,MAAM,EAAE;MAChDH,EAAE,GAAGH,GAAG,CAACQ,UAAU,CAACJ,IAAI,GAAG,CAAC,CAAC;MAC7B,IAAI,CAACD,EAAE,GAAG,MAAM,MAAM,MAAM,EAAE;QAC5BD,CAAC,GAAG,OAAO,IAAIA,CAAC,GAAG,MAAM,IAAI,EAAE,CAAC,IAAIC,EAAE,GAAG,MAAM,CAAC;QAChDC,IAAI,EAAE;MACR;IACF;IACA,IAAIF,CAAC,GAAG,IAAI,EAAE;MACZ;MACAD,GAAG,CAACH,CAAC,EAAE,CAAC,GAAGI,CAAC;IACd,CAAC,MAAM,IAAIA,CAAC,GAAG,KAAK,EAAE;MACpB;MACAD,GAAG,CAACH,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGI,CAAC,KAAK,CAAC;MACzBD,GAAG,CAACH,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGI,CAAC,GAAG,IAAI;IAC5B,CAAC,MAAM,IAAIA,CAAC,GAAG,OAAO,EAAE;MACtB;MACAD,GAAG,CAACH,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGI,CAAC,KAAK,EAAE;MAC1BD,GAAG,CAACH,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGI,CAAC,KAAK,CAAC,GAAG,IAAI;MAChCD,GAAG,CAACH,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGI,CAAC,GAAG,IAAI;IAC5B,CAAC,MAAM;MACL;MACAD,GAAG,CAACH,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGI,CAAC,KAAK,EAAE;MAC1BD,GAAG,CAACH,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGI,CAAC,KAAK,EAAE,GAAG,IAAI;MACjCD,GAAG,CAACH,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGI,CAAC,KAAK,CAAC,GAAG,IAAI;MAChCD,GAAG,CAACH,CAAC,EAAE,CAAC,GAAG,IAAI,GAAGI,CAAC,GAAG,IAAI;IAC5B;EACF;EACA,OAAOD,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASU,UAAUA,CAACV,GAAG,EAAEW,GAAG,EAAE;EAC5B,IAAIC,GAAG;EACPD,GAAG,GAAGA,GAAG,IAAIX,GAAG,CAACM,MAAM;EACvB,IAAIK,GAAG,GAAGX,GAAG,CAACM,MAAM,EAAE;IACpBK,GAAG,GAAGX,GAAG,CAACM,MAAM;EAClB;;EAEA;EACAM,GAAG,GAAGD,GAAG,GAAG,CAAC;EACb,OAAOC,GAAG,IAAI,CAAC,IAAI,CAACZ,GAAG,CAACY,GAAG,CAAC,GAAG,IAAI,MAAM,IAAI,EAAE;IAC7CA,GAAG,EAAE;EACP;;EAEA;EACA;EACA,IAAIA,GAAG,GAAG,CAAC,EAAE;IACX,OAAOD,GAAG;EACZ;;EAEA;EACA;EACA,IAAIC,GAAG,KAAK,CAAC,EAAE;IACb,OAAOD,GAAG;EACZ;EACA,OAAOC,GAAG,GAAGjB,QAAQ,CAACK,GAAG,CAACY,GAAG,CAAC,CAAC,GAAGD,GAAG,GAAGC,GAAG,GAAGD,GAAG;AACnD;;AAEA;AACA,SAASE,UAAUA,CAACb,GAAG,EAAE;EACvB,IAAIH,CAAC,EAAEiB,GAAG,EAAEb,CAAC,EAAEc,IAAI;EACnB,IAAIC,GAAG,GAAGhB,GAAG,CAACM,MAAM;;EAEpB;EACA;EACA;EACA,IAAIW,QAAQ,GAAG,IAAIrB,KAAK,CAACoB,GAAG,GAAG,CAAC,CAAC;EACjC,KAAKF,GAAG,GAAG,CAAC,EAAEjB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmB,GAAG,GAAG;IAC7Bf,CAAC,GAAGD,GAAG,CAACH,CAAC,EAAE,CAAC;IACZ;IACA,IAAII,CAAC,GAAG,IAAI,EAAE;MACZgB,QAAQ,CAACH,GAAG,EAAE,CAAC,GAAGb,CAAC;MACnB;IACF;IACAc,IAAI,GAAGpB,QAAQ,CAACM,CAAC,CAAC;IAClB;IACA,IAAIc,IAAI,GAAG,CAAC,EAAE;MACZE,QAAQ,CAACH,GAAG,EAAE,CAAC,GAAG,MAAM;MACxBjB,CAAC,IAAIkB,IAAI,GAAG,CAAC;MACb;IACF;;IAEA;IACAd,CAAC,IAAIc,IAAI,KAAK,CAAC,GAAG,IAAI,GAAGA,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI;IACjD;IACA,OAAOA,IAAI,GAAG,CAAC,IAAIlB,CAAC,GAAGmB,GAAG,EAAE;MAC1Bf,CAAC,GAAGA,CAAC,IAAI,CAAC,GAAGD,GAAG,CAACH,CAAC,EAAE,CAAC,GAAG,IAAI;MAC5BkB,IAAI,EAAE;IACR;;IAEA;IACA,IAAIA,IAAI,GAAG,CAAC,EAAE;MACZE,QAAQ,CAACH,GAAG,EAAE,CAAC,GAAG,MAAM;MACxB;IACF;IACA,IAAIb,CAAC,GAAG,OAAO,EAAE;MACfgB,QAAQ,CAACH,GAAG,EAAE,CAAC,GAAGb,CAAC;IACrB,CAAC,MAAM;MACLA,CAAC,IAAI,OAAO;MACZgB,QAAQ,CAACH,GAAG,EAAE,CAAC,GAAG,MAAM,GAAGb,CAAC,IAAI,EAAE,GAAG,KAAK;MAC1CgB,QAAQ,CAACH,GAAG,EAAE,CAAC,GAAG,MAAM,GAAGb,CAAC,GAAG,KAAK;IACtC;EACF;;EAEA;EACA,IAAIgB,QAAQ,CAACX,MAAM,KAAKQ,GAAG,EAAE;IAC3B,IAAIG,QAAQ,CAACC,QAAQ,EAAE;MACrBD,QAAQ,GAAGA,QAAQ,CAACC,QAAQ,CAAC,CAAC,EAAEJ,GAAG,CAAC;IACtC,CAAC,MAAM;MACLG,QAAQ,CAACX,MAAM,GAAGQ,GAAG;IACvB;EACF;;EAEA;EACA,OAAOvB,KAAK,CAAC4B,iBAAiB,CAACF,QAAQ,CAAC;AAC1C;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACAG,OAAO,CAACC,UAAU,GAAG,SAASA,UAAUA,CAACtB,GAAG,EAAE;EAC5C,IAAIN,OAAO,CAAC6B,UAAU,EAAE;IACtB,OAAO5B,UAAU,CAACK,GAAG,EAAE,OAAO,CAAC;EACjC;EACA,OAAOD,UAAU,CAACC,GAAG,CAAC;AACxB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACAqB,OAAO,CAACG,UAAU,GAAG,SAASA,UAAUA,CAACvB,GAAG,EAAE;EAC5C,IAAIP,OAAO,CAAC6B,UAAU,EAAE;IACtB,OAAO/B,KAAK,CAACiC,WAAW,CAAC,YAAY,EAAExB,GAAG,CAAC,CAACyB,QAAQ,CAAC,OAAO,CAAC;EAC/D;EACAzB,GAAG,GAAGT,KAAK,CAACiC,WAAW,CAAC/B,OAAO,CAACe,UAAU,GAAG,YAAY,GAAG,OAAO,EAAER,GAAG,CAAC;;EAEzE;EACA;EACA;EACA;EACA,IAAI0B,MAAM,GAAG,EAAE;IACbV,GAAG,GAAGhB,GAAG,CAACM,MAAM;IAChBqB,KAAK,GAAG,KAAK;EACf,IAAIC,CAAC,GAAG,CAAC;EACT,OAAOA,CAAC,GAAGZ,GAAG,EAAE;IACd,IAAIa,YAAY,GAAGnB,UAAU,CAACV,GAAG,EAAE8B,IAAI,CAACC,GAAG,CAACH,CAAC,GAAGD,KAAK,EAAEX,GAAG,CAAC,CAAC;IAC5D,IAAIvB,OAAO,CAACe,UAAU,EAAE;MACtBkB,MAAM,CAACM,IAAI,CAACnB,UAAU,CAACb,GAAG,CAACkB,QAAQ,CAACU,CAAC,EAAEC,YAAY,CAAC,CAAC,CAAC;IACxD,CAAC,MAAM;MACLH,MAAM,CAACM,IAAI,CAACnB,UAAU,CAACb,GAAG,CAACiC,KAAK,CAACL,CAAC,EAAEC,YAAY,CAAC,CAAC,CAAC;IACrD;IACAD,CAAC,GAAGC,YAAY;EAClB;EACA,OAAOH,MAAM,CAACQ,IAAI,CAAC,EAAE,CAAC;AACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}