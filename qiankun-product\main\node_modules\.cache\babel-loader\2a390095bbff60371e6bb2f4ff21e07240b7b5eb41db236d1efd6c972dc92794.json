{"ast": null, "code": "\"use strict\";\n\nvar ArrayReader = require(\"./arrayReader.js\");\nfunction Uint8ArrayReader(data) {\n  if (data) {\n    this.data = data;\n    this.length = this.data.length;\n    this.index = 0;\n    this.zero = 0;\n  }\n}\nUint8ArrayReader.prototype = new ArrayReader();\n/**\n * @see DataReader.readData\n */\nUint8ArrayReader.prototype.readData = function (size) {\n  this.checkOffset(size);\n  if (size === 0) {\n    // in IE10, when using subarray(idx, idx), we get the array [0x00] instead of [].\n    return new Uint8Array(0);\n  }\n  var result = this.data.subarray(this.zero + this.index, this.zero + this.index + size);\n  this.index += size;\n  return result;\n};\nmodule.exports = Uint8ArrayReader;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "Uint8ArrayReader", "data", "length", "index", "zero", "prototype", "readData", "size", "checkOffset", "Uint8Array", "result", "subarray", "module", "exports"], "sources": ["D:/zy/xm/pc/5.0/qiankun-product/node_modules/.pnpm/pizzip@3.1.7/node_modules/pizzip/js/uint8ArrayReader.js"], "sourcesContent": ["\"use strict\";\n\nvar ArrayReader = require(\"./arrayReader.js\");\nfunction Uint8ArrayReader(data) {\n  if (data) {\n    this.data = data;\n    this.length = this.data.length;\n    this.index = 0;\n    this.zero = 0;\n  }\n}\nUint8ArrayReader.prototype = new ArrayReader();\n/**\n * @see DataReader.readData\n */\nUint8ArrayReader.prototype.readData = function (size) {\n  this.checkOffset(size);\n  if (size === 0) {\n    // in IE10, when using subarray(idx, idx), we get the array [0x00] instead of [].\n    return new Uint8Array(0);\n  }\n  var result = this.data.subarray(this.zero + this.index, this.zero + this.index + size);\n  this.index += size;\n  return result;\n};\nmodule.exports = Uint8ArrayReader;"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAC7C,SAASC,gBAAgBA,CAACC,IAAI,EAAE;EAC9B,IAAIA,IAAI,EAAE;IACR,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,MAAM,GAAG,IAAI,CAACD,IAAI,CAACC,MAAM;IAC9B,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,IAAI,GAAG,CAAC;EACf;AACF;AACAJ,gBAAgB,CAACK,SAAS,GAAG,IAAIP,WAAW,CAAC,CAAC;AAC9C;AACA;AACA;AACAE,gBAAgB,CAACK,SAAS,CAACC,QAAQ,GAAG,UAAUC,IAAI,EAAE;EACpD,IAAI,CAACC,WAAW,CAACD,IAAI,CAAC;EACtB,IAAIA,IAAI,KAAK,CAAC,EAAE;IACd;IACA,OAAO,IAAIE,UAAU,CAAC,CAAC,CAAC;EAC1B;EACA,IAAIC,MAAM,GAAG,IAAI,CAACT,IAAI,CAACU,QAAQ,CAAC,IAAI,CAACP,IAAI,GAAG,IAAI,CAACD,KAAK,EAAE,IAAI,CAACC,IAAI,GAAG,IAAI,CAACD,KAAK,GAAGI,IAAI,CAAC;EACtF,IAAI,CAACJ,KAAK,IAAII,IAAI;EAClB,OAAOG,MAAM;AACf,CAAC;AACDE,MAAM,CAACC,OAAO,GAAGb,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}